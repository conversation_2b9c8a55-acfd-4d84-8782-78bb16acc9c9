<!DOCTYPE html>
<html lang="en" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Keys - VoiceHype Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&family=Roboto+Mono&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                fontFamily: {
                    sans: ['Inter', 'sans-serif'],
                    heading: ['Poppins', 'sans-serif'],
                    mono: ['Roboto Mono', 'monospace'],
                },
                extend: {
                    colors: {
                        primary: {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                            950: '#052e16',
                        },
                        secondary: {
                            50: '#f0fdfa',
                            100: '#ccfbf1',
                            200: '#99f6e4',
                            300: '#5eead4',
                            400: '#2dd4bf',
                            500: '#14b8a6',
                            600: '#0d9488',
                            700: '#0f766e',
                            800: '#115e59',
                            900: '#134e4a',
                            950: '#042f2e',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        [x-cloak] { display: none !important; }
        body { font-family: 'Inter', sans-serif; }
        h1, h2, h3, h4, h5, h6 { font-family: 'Poppins', sans-serif; }
        .font-mono { font-family: 'Roboto Mono', monospace; }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900 text-gray-800 dark:text-gray-200 transition-colors duration-200">
    <div x-data="{ sidebarOpen: false, newKeyModal: false, showKey: false, newKey: '', darkMode: localStorage.getItem('darkMode') === 'true' }" 
         x-init="$watch('darkMode', val => { 
            localStorage.setItem('darkMode', val); 
            if (val) { document.documentElement.classList.add('dark') } 
            else { document.documentElement.classList.remove('dark') }
         }); 
         if (localStorage.getItem('darkMode') === 'true') { 
            darkMode = true;
            document.documentElement.classList.add('dark');
         }" 
         class="min-h-screen flex">
        <!-- Sidebar -->
        <div x-cloak :class="sidebarOpen ? 'block' : 'hidden'" @click="sidebarOpen = false" class="fixed inset-0 z-20 transition-opacity bg-black opacity-50 lg:hidden"></div>
        
        <div :class="sidebarOpen ? 'translate-x-0 ease-out' : '-translate-x-full ease-in'" class="fixed inset-y-0 left-0 z-30 w-64 overflow-y-auto transition duration-300 transform bg-primary-800 dark:bg-primary-950 lg:translate-x-0 lg:static lg:inset-0">
            <div class="flex items-center justify-center mt-8">
                <div class="flex items-center">
                    <span class="mx-2 text-2xl font-semibold text-white font-heading">VoiceHype</span>
                </div>
            </div>
            
            <nav class="mt-10">
                <a class="flex items-center px-6 py-2 mt-4 text-white hover:bg-primary-700 dark:hover:bg-primary-900" href="index.html">
                    <i class="fas fa-tachometer-alt mr-3"></i>
                    Dashboard
                </a>
                <a class="flex items-center px-6 py-2 mt-4 text-white bg-primary-700 dark:bg-primary-900" href="api-keys.html">
                    <i class="fas fa-key mr-3"></i>
                    API Keys
                </a>
                <a class="flex items-center px-6 py-2 mt-4 text-white hover:bg-primary-700 dark:hover:bg-primary-900" href="usage.html">
                    <i class="fas fa-chart-line mr-3"></i>
                    Usage History
                </a>
                <a class="flex items-center px-6 py-2 mt-4 text-white hover:bg-primary-700 dark:hover:bg-primary-900" href="subscription.html">
                    <i class="fas fa-credit-card mr-3"></i>
                    Subscription
                </a>
                <a class="flex items-center px-6 py-2 mt-4 text-white hover:bg-primary-700 dark:hover:bg-primary-900" href="settings.html">
                    <i class="fas fa-cog mr-3"></i>
                    Settings
                </a>
            </nav>
        </div>
        
        <!-- Content -->
        <div class="flex-1 overflow-x-hidden overflow-y-auto">
            <!-- Navbar -->
            <header class="bg-white dark:bg-gray-800 shadow">
                <div class="container px-6 py-4 mx-auto">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <button @click="sidebarOpen = true" class="text-gray-500 dark:text-gray-300 focus:outline-none lg:hidden">
                                <i class="fas fa-bars text-lg"></i>
                            </button>
                        </div>
                        
                        <div class="flex items-center space-x-4">
                            <!-- Dark mode toggle -->
                            <button @click="darkMode = !darkMode" class="text-gray-500 dark:text-gray-300 focus:outline-none">
                                <i class="fas" :class="darkMode ? 'fa-sun' : 'fa-moon'"></i>
                            </button>
                            
                            <div x-data="{ dropdownOpen: false }" class="relative">
                                <button @click="dropdownOpen = !dropdownOpen" class="relative block w-8 h-8 overflow-hidden rounded-full shadow focus:outline-none">
                                    <img class="object-cover w-full h-full" src="https://ui-avatars.com/api/?name=User&background=16a34a&color=fff" alt="Your avatar">
                                </button>

                                <div x-cloak x-show="dropdownOpen" @click="dropdownOpen = false" class="fixed inset-0 z-10"></div>

                                <div x-cloak x-show="dropdownOpen" class="absolute right-0 z-10 w-48 mt-2 overflow-hidden bg-white dark:bg-gray-800 rounded-md shadow-xl">
                                    <a href="#profile" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-primary-600 hover:text-white">Profile</a>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-primary-600 hover:text-white">Logout</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Main content -->
            <main class="container px-6 py-8 mx-auto">
                <!-- API Keys Section -->
                <section id="api-keys" class="mb-12">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-semibold text-gray-800 dark:text-gray-100 font-heading">API Keys</h2>
                        <button @click="newKeyModal = true" class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-primary-700 dark:hover:bg-primary-600">
                            <i class="fas fa-plus mr-2"></i> Create New Key
                        </button>
                    </div>
                    
                    <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                        <div class="p-6">
                            <p class="text-gray-600 dark:text-gray-400 mb-4">
                                API keys allow you to authenticate requests to the VoiceHype API. Keys are tied to your account and have specific permissions.
                            </p>
                            
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                    <thead class="bg-gray-50 dark:bg-gray-700">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Name</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Key Prefix</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Permissions</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Created</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Last Used</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900 dark:text-white">Production Key</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-500 dark:text-gray-400 font-mono">vh_prod_7f8a9...</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                                    Full Access
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                                2023-06-15
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                                Today at 10:23 AM
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                <button class="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-3">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900 dark:text-white">Development Key</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-500 dark:text-gray-400 font-mono">vh_dev_3e4d5...</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                                    Read Only
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                                2023-07-22
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                                Yesterday at 3:45 PM
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                <button class="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-3">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </section>
                
                <!-- API Usage Guidelines -->
                <section id="api-usage" class="mb-12">
                    <h2 class="text-2xl font-semibold text-gray-800 dark:text-gray-100 mb-6 font-heading">API Usage Guidelines</h2>
                    
                    <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                        <div class="p-6">
                            <div class="mb-6">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2 font-heading">Authentication</h3>
                                <p class="text-gray-600 dark:text-gray-400 mb-4">
                                    To authenticate API requests, include your API key in the request headers:
                                </p>
                                <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded-md font-mono text-sm">
                                    <code class="text-gray-800 dark:text-gray-200">
                                        X-VoiceHype-API-Key: your_api_key_here
                                    </code>
                                </div>
                            </div>
                            
                            <div class="mb-6">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2 font-heading">Rate Limits</h3>
                                <p class="text-gray-600 dark:text-gray-400 mb-4">
                                    API requests are subject to rate limiting based on your subscription plan:
                                </p>
                                <ul class="list-disc pl-5 text-gray-600 dark:text-gray-400">
                                    <li class="mb-2">Free tier: 100 requests per day</li>
                                    <li class="mb-2">Pro tier: 1,000 requests per day</li>
                                    <li class="mb-2">Enterprise tier: Custom limits</li>
                                </ul>
                            </div>
                            
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2 font-heading">Security Best Practices</h3>
                                <ul class="list-disc pl-5 text-gray-600 dark:text-gray-400">
                                    <li class="mb-2">Never expose your API keys in client-side code</li>
                                    <li class="mb-2">Rotate your API keys periodically</li>
                                    <li class="mb-2">Use environment variables to store API keys in your applications</li>
                                    <li class="mb-2">Create separate API keys for different environments (development, staging, production)</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </section>
            </main>
        </div>
        
        <!-- Create New API Key Modal -->
        <div x-cloak x-show="newKeyModal" class="fixed inset-0 z-50 overflow-y-auto">
            <div class="flex items-center justify-center min-h-screen px-4">
                <div x-cloak x-show="newKeyModal" @click.away="newKeyModal = false" 
                     x-transition:enter="transition ease-out duration-300"
                     x-transition:enter-start="opacity-0 transform scale-90"
                     x-transition:enter-end="opacity-100 transform scale-100"
                     x-transition:leave="transition ease-in duration-300"
                     x-transition:leave-start="opacity-100 transform scale-100"
                     x-transition:leave-end="opacity-0 transform scale-90"
                     class="bg-white dark:bg-gray-800 rounded-lg shadow-xl overflow-hidden max-w-md w-full">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white font-heading">Create New API Key</h3>
                    </div>
                    
                    <div class="p-6">
                        <div class="mb-4">
                            <label for="key-name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Key Name</label>
                            <input type="text" id="key-name" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Permissions</label>
                            <div class="space-y-2">
                                <div class="flex items-center">
                                    <input type="radio" id="full-access" name="permissions" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700" checked>
                                    <label for="full-access" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                        Full Access
                                    </label>
                                </div>
                                <div class="flex items-center">
                                    <input type="radio" id="read-only" name="permissions" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700">
                                    <label for="read-only" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                        Read Only
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div x-show="showKey" class="mb-4 p-4 bg-gray-100 dark:bg-gray-700 rounded-md">
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Your new API key:</p>
                            <div class="flex items-center">
                                <input type="text" x-model="newKey" readonly class="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-gray-700 dark:text-gray-300 font-mono text-sm">
                                <button @click="navigator.clipboard.writeText(newKey)" class="ml-2 p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                            <p class="text-sm text-red-600 dark:text-red-400 mt-2">
                                <i class="fas fa-exclamation-triangle mr-1"></i>
                                Save this key now. You won't be able to see it again!
                            </p>
                        </div>
                    </div>
                    
                    <div class="px-6 py-4 bg-gray-50 dark:bg-gray-700 flex justify-end">
                        <button @click="newKeyModal = false" class="px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-100 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 mr-2">
                            Cancel
                        </button>
                        <button x-show="!showKey" @click="showKey = true; newKey = 'vh_' + (Math.random().toString(36).substring(2, 10)) + '_' + (Math.random().toString(36).substring(2, 10));" class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-primary-700 dark:hover:bg-primary-600">
                            Generate Key
                        </button>
                        <button x-show="showKey" @click="newKeyModal = false; showKey = false; newKey = '';" class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-primary-700 dark:hover:bg-primary-600">
                            Done
                        </button>
                    </div>
                </div>
            </div>
            <div class="fixed inset-0 bg-black bg-opacity-50"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.x.x/dist/alpine.min.js" defer></script>
</body>
</html> 
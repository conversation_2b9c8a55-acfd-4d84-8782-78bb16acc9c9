<!DOCTYPE html>
<html lang="en" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Subscription Plans - VoiceHype Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&family=Roboto+Mono&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                fontFamily: {
                    sans: ['Inter', 'sans-serif'],
                    heading: ['Poppins', 'sans-serif'],
                    mono: ['Roboto Mono', 'monospace'],
                },
                extend: {
                    colors: {
                        primary: {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                            950: '#052e16',
                        },
                        secondary: {
                            50: '#f0fdfa',
                            100: '#ccfbf1',
                            200: '#99f6e4',
                            300: '#5eead4',
                            400: '#2dd4bf',
                            500: '#14b8a6',
                            600: '#0d9488',
                            700: '#0f766e',
                            800: '#115e59',
                            900: '#134e4a',
                            950: '#042f2e',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        [x-cloak] { display: none !important; }
        body { font-family: 'Inter', sans-serif; }
        h1, h2, h3, h4, h5, h6 { font-family: 'Poppins', sans-serif; }
        .font-mono { font-family: 'Roboto Mono', monospace; }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900 dark:text-gray-200 text-gray-800 transition-colors duration-200">
    <div x-data="{ sidebarOpen: false, billingCycle: 'monthly', darkMode: localStorage.getItem('darkMode') === 'true' }" 
         x-init="$watch('darkMode', val => { 
            localStorage.setItem('darkMode', val); 
            if (val) { document.documentElement.classList.add('dark') } 
            else { document.documentElement.classList.remove('dark') }
         }); 
         if (localStorage.getItem('darkMode') === 'true') { 
            darkMode = true;
            document.documentElement.classList.add('dark');
         }" 
         class="flex min-h-screen">
        <!-- Sidebar -->
        <div x-cloak :class="sidebarOpen ? 'block' : 'hidden'" @click="sidebarOpen = false" class="lg:hidden fixed inset-0 z-20 transition-opacity bg-black opacity-50"></div>
        
        <div :class="sidebarOpen ? 'translate-x-0 ease-out' : '-translate-x-full ease-in'" class="bg-primary-800 dark:bg-primary-950 lg:translate-x-0 lg:static lg:inset-0 fixed inset-y-0 left-0 z-30 w-64 overflow-y-auto transition duration-300 transform">
            <div class="flex items-center justify-center mt-8">
                <div class="flex items-center">
                    <span class="font-heading mx-2 text-2xl font-semibold text-white">VoiceHype</span>
                </div>
            </div>
            
            <nav class="mt-10">
                <a class="hover:bg-primary-700 dark:hover:bg-primary-900 flex items-center px-6 py-2 mt-4 text-white" href="index.html">
                    <i class="fas fa-tachometer-alt mr-3"></i>
                    Dashboard
                </a>
                <a class="hover:bg-primary-700 dark:hover:bg-primary-900 flex items-center px-6 py-2 mt-4 text-white" href="api-keys.html">
                    <i class="fas fa-key mr-3"></i>
                    API Keys
                </a>
                <a class="hover:bg-primary-700 dark:hover:bg-primary-900 flex items-center px-6 py-2 mt-4 text-white" href="usage.html">
                    <i class="fas fa-chart-line mr-3"></i>
                    Usage History
                </a>
                <a class="bg-primary-700 dark:bg-primary-900 flex items-center px-6 py-2 mt-4 text-white" href="subscription.html">
                    <i class="fas fa-credit-card mr-3"></i>
                    Subscription
                </a>
                <a class="hover:bg-primary-700 dark:hover:bg-primary-900 flex items-center px-6 py-2 mt-4 text-white" href="settings.html">
                    <i class="fas fa-cog mr-3"></i>
                    Settings
                </a>
            </nav>
        </div>
        
        <!-- Content -->
        <div class="flex-1 overflow-x-hidden overflow-y-auto">
            <!-- Navbar -->
            <header class="dark:bg-gray-800 bg-white shadow">
                <div class="container px-6 py-4 mx-auto">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <button @click="sidebarOpen = true" class="dark:text-gray-300 focus:outline-none lg:hidden text-gray-500">
                                <i class="fas fa-bars text-lg"></i>
                            </button>
                        </div>
                        
                        <div class="flex items-center space-x-4">
                            <!-- Dark mode toggle -->
                            <button @click="darkMode = !darkMode" class="dark:text-gray-300 focus:outline-none text-gray-500">
                                <i class="fas" :class="darkMode ? 'fa-sun' : 'fa-moon'"></i>
                            </button>
                            
                            <div x-data="{ dropdownOpen: false }" class="relative">
                                <button @click="dropdownOpen = !dropdownOpen" class="focus:outline-none relative block w-8 h-8 overflow-hidden rounded-full shadow">
                                    <img class="object-cover w-full h-full" src="https://ui-avatars.com/api/?name=User&background=16a34a&color=fff" alt="Your avatar">
                                </button>

                                <div x-cloak x-show="dropdownOpen" @click="dropdownOpen = false" class="fixed inset-0 z-10"></div>

                                <div x-cloak x-show="dropdownOpen" class="dark:bg-gray-800 absolute right-0 z-10 w-48 mt-2 overflow-hidden bg-white rounded-md shadow-xl">
                                    <a href="#profile" class="dark:text-gray-300 hover:bg-primary-600 hover:text-white block px-4 py-2 text-sm text-gray-700">Profile</a>
                                    <a href="#" class="dark:text-gray-300 hover:bg-primary-600 hover:text-white block px-4 py-2 text-sm text-gray-700">Logout</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Main content -->
            <main class="container px-6 py-8 mx-auto">
                <!-- Subscription Section -->
                <section id="subscription" class="mb-12">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="dark:text-gray-100 font-heading text-2xl font-semibold text-gray-800">Pricing Options</h2>
                    </div>
                    
                    <!-- Pricing Model Selection -->
                    <div class="dark:bg-gray-800 mb-8 overflow-hidden bg-white rounded-lg shadow">
                        <div class="dark:border-gray-700 px-6 py-4 border-b border-gray-200">
                            <h3 class="dark:text-white font-heading text-lg font-semibold text-gray-800">Choose Your Pricing Model</h3>
                            <p class="dark:text-gray-400 mt-1 text-gray-600">Select the pricing model that best fits your needs</p>
                        </div>
                        
                        <div class="p-6">
                            <div class="md:grid-cols-3 grid grid-cols-1 gap-6">
                                <!-- Pre-paid Credits -->
                                <div class="dark:border-gray-700 hover:border-primary-500 hover:shadow-md p-6 transition-all duration-200 border border-gray-200 rounded-lg">
                                    <div class="flex items-center justify-between mb-4">
                                        <h4 class="dark:text-white font-heading text-lg font-semibold text-gray-800">Pre-paid Credits</h4>
                                        <span class="dark:bg-green-900 dark:text-green-200 px-3 py-1 text-xs font-semibold text-green-800 bg-green-100 rounded-full">Best Value</span>
                                    </div>
                                    <p class="dark:text-gray-400 mb-4 text-gray-600">Purchase credits upfront and use them anytime. Perfect for occasional use or testing.</p>
                                    <ul class="mb-6 space-y-3">
                                        <li class="flex items-start">
                                            <i class="fas fa-check mt-1 mr-2 text-green-500"></i>
                                            <span class="dark:text-gray-300 text-gray-700">Best per-unit pricing</span>
                                        </li>
                                        <li class="flex items-start">
                                            <i class="fas fa-check mt-1 mr-2 text-green-500"></i>
                                            <span class="dark:text-gray-300 text-gray-700">No expiration date</span>
                                        </li>
                                        <li class="flex items-start">
                                            <i class="fas fa-check mt-1 mr-2 text-green-500"></i>
                                            <span class="dark:text-gray-300 text-gray-700">Volume discounts available</span>
                                        </li>
                                    </ul>
                                    <div class="mt-4">
                                        <a href="#credits-section" class="bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 block w-full px-4 py-2 text-center text-white rounded-md">
                                            Buy Credits
                                        </a>
                                    </div>
                                </div>

                                <!-- Pay As You Go -->
                                <div class="dark:border-gray-700 hover:border-primary-500 hover:shadow-md p-6 transition-all duration-200 border border-gray-200 rounded-lg">
                                    <div class="flex items-center justify-between mb-4">
                                        <h4 class="dark:text-white font-heading text-lg font-semibold text-gray-800">Pay As You Go</h4>
                                        <span class="dark:bg-blue-900 dark:text-blue-200 px-3 py-1 text-xs font-semibold text-blue-800 bg-blue-100 rounded-full">Most Flexible</span>
                                    </div>
                                    <p class="dark:text-gray-400 mb-4 text-gray-600">Pay only for what you use. Billed at the end of each month.</p>
                                    <ul class="mb-6 space-y-3">
                                        <li class="flex items-start">
                                            <i class="fas fa-check mt-1 mr-2 text-green-500"></i>
                                            <span class="dark:text-gray-300 text-gray-700">No upfront payment</span>
                                        </li>
                                        <li class="flex items-start">
                                            <i class="fas fa-check mt-1 mr-2 text-green-500"></i>
                                            <span class="dark:text-gray-300 text-gray-700">Monthly billing</span>
                                        </li>
                                        <li class="flex items-start">
                                            <i class="fas fa-check mt-1 mr-2 text-green-500"></i>
                                            <span class="dark:text-gray-300 text-gray-700">Standard rates</span>
                                        </li>
                                    </ul>
                                    <div class="mt-4">
                                        <a href="#payg-section" class="dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-primary-500 block w-full px-4 py-2 text-center text-gray-700 bg-white border border-gray-300 rounded-md">
                                            Set Up PAYG
                                        </a>
                                    </div>
                                </div>
                                
                                <!-- Subscription -->
                                <div class="dark:border-gray-700 hover:border-primary-500 hover:shadow-md p-6 transition-all duration-200 border border-gray-200 rounded-lg">
                                    <div class="flex items-center justify-between mb-4">
                                        <h4 class="dark:text-white font-heading text-lg font-semibold text-gray-800">Subscription</h4>
                                        <span class="dark:bg-purple-900 dark:text-purple-200 px-3 py-1 text-xs font-semibold text-purple-800 bg-purple-100 rounded-full">Most Popular</span>
                                    </div>
                                    <p class="dark:text-gray-400 mb-4 text-gray-600">Fixed monthly/annual plans with included quotas.</p>
                                    <ul class="mb-6 space-y-3">
                                        <li class="flex items-start">
                                            <i class="fas fa-check mt-1 mr-2 text-green-500"></i>
                                            <span class="dark:text-gray-300 text-gray-700">Predictable pricing</span>
                                        </li>
                                        <li class="flex items-start">
                                            <i class="fas fa-check mt-1 mr-2 text-green-500"></i>
                                            <span class="dark:text-gray-300 text-gray-700">Monthly/annual billing</span>
                                        </li>
                                        <li class="flex items-start">
                                            <i class="fas fa-check mt-1 mr-2 text-green-500"></i>
                                            <span class="dark:text-gray-300 text-gray-700">Priority support</span>
                                        </li>
                                    </ul>
                                    <div class="mt-4">
                                        <a href="#subscription-plans" class="bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 block w-full px-4 py-2 text-center text-white rounded-md">
                                            View Plans
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Credits Section -->
                    <div id="credits-section" class="mb-12">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="dark:text-white font-heading text-xl font-semibold text-gray-800">Pre-paid Credits</h3>
                            </div>
                            
                        <div class="dark:bg-gray-800 overflow-hidden bg-white rounded-lg shadow">
                            <div class="p-6">
                                <p class="dark:text-gray-400 mb-6 text-gray-600">
                                    Purchase credits in advance and use them whenever you need. Credits never expire and offer the best per-unit pricing.
                                </p>
                                
                                <div class="md:grid-cols-3 grid grid-cols-1 gap-6">
                                    <div class="dark:border-gray-700 hover:border-primary-500 hover:shadow-md p-4 transition-all duration-200 border border-gray-200 rounded-lg">
                                        <h4 class="dark:text-white font-heading text-lg font-semibold text-gray-800">Starter Pack</h4>
                                        <p class="dark:text-white mt-2 text-2xl font-bold text-gray-800">$10</p>
                                        <p class="dark:text-gray-400 mt-1 text-gray-600">500 credits</p>
                                        <p class="dark:text-gray-400 mt-2 text-sm text-gray-500">Perfect for testing or small projects</p>
                                        <button class="bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 w-full px-4 py-2 mt-4 text-white rounded-md">
                                            Purchase
                                        </button>
                                    </div>
                                    
                                    <div class="dark:border-gray-700 hover:border-primary-500 hover:shadow-md p-4 transition-all duration-200 border border-gray-200 rounded-lg">
                                        <h4 class="dark:text-white font-heading text-lg font-semibold text-gray-800">Value Pack</h4>
                                        <p class="dark:text-white mt-2 text-2xl font-bold text-gray-800">$25</p>
                                        <p class="dark:text-gray-400 mt-1 text-gray-600">1,500 credits <span class="dark:text-green-400 text-green-600">(20% bonus)</span></p>
                                        <p class="dark:text-gray-400 mt-2 text-sm text-gray-500">Best for medium-sized projects</p>
                                        <button class="bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 w-full px-4 py-2 mt-4 text-white rounded-md">
                                            Purchase
                                        </button>
                                    </div>
                                    
                                    <div class="dark:border-gray-700 hover:border-primary-500 hover:shadow-md p-4 transition-all duration-200 border border-gray-200 rounded-lg">
                                        <h4 class="dark:text-white font-heading text-lg font-semibold text-gray-800">Pro Pack</h4>
                                        <p class="dark:text-white mt-2 text-2xl font-bold text-gray-800">$50</p>
                                        <p class="dark:text-gray-400 mt-1 text-gray-600">3,500 credits <span class="dark:text-green-400 text-green-600">(40% bonus)</span></p>
                                        <p class="dark:text-gray-400 mt-2 text-sm text-gray-500">Ideal for larger projects</p>
                                        <button class="bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 w-full px-4 py-2 mt-4 text-white rounded-md">
                                            Purchase
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- PAYG Section -->
                    <div id="payg-section" class="mb-12">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="dark:text-white font-heading text-xl font-semibold text-gray-800">Pay As You Go</h3>
                        </div>
                        
                        <div class="dark:bg-gray-800 overflow-hidden bg-white rounded-lg shadow">
                            <div class="p-6">
                                <p class="dark:text-gray-400 mb-6 text-gray-600">
                                    Use our services without any upfront commitment. You'll be billed monthly based on your actual usage.
                                </p>

                                <div class="overflow-x-auto">
                                    <table class="dark:divide-gray-700 min-w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50 dark:bg-gray-700">
                                            <tr>
                                                <th class="dark:text-gray-300 px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Service</th>
                                                <th class="dark:text-gray-300 px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Rate</th>
                                                <th class="dark:text-gray-300 px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Unit</th>
                                                <th class="dark:text-gray-300 px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Details</th>
                                            </tr>
                                        </thead>
                                        <tbody class="dark:bg-gray-800 dark:divide-gray-700 bg-white divide-y divide-gray-200">
                                            <tr>
                                                <td class="whitespace-nowrap dark:text-gray-400 px-6 py-4 text-sm text-gray-500">Transcription</td>
                                                <td class="whitespace-nowrap dark:text-gray-400 px-6 py-4 text-sm text-gray-500">$0.02</td>
                                                <td class="whitespace-nowrap dark:text-gray-400 px-6 py-4 text-sm text-gray-500">per minute</td>
                                                <td class="dark:text-gray-400 px-6 py-4 text-sm text-gray-500">Billed per second, rounded up to nearest second</td>
                                            </tr>
                                            <tr>
                                                <td class="whitespace-nowrap dark:text-gray-400 px-6 py-4 text-sm text-gray-500">Optimization</td>
                                                <td class="whitespace-nowrap dark:text-gray-400 px-6 py-4 text-sm text-gray-500">$0.00015</td>
                                                <td class="whitespace-nowrap dark:text-gray-400 px-6 py-4 text-sm text-gray-500">per token</td>
                                                <td class="dark:text-gray-400 px-6 py-4 text-sm text-gray-500">Input and output tokens counted separately</td>
                                            </tr>
                                        </tbody>
                                    </table>
                            </div>
                            
                                <div class="dark:border-gray-700 pt-6 mt-6 border-t border-gray-200">
                                    <h4 class="dark:text-white mb-4 text-lg font-medium text-gray-800">Set Up PAYG</h4>
                                    <div class="flex items-center space-x-4">
                                        <button class="bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 px-4 py-2 text-white rounded-md">
                                            Add Payment Method
                                </button>
                                        <p class="dark:text-gray-400 text-sm text-gray-500">You'll only be charged for what you use at the end of each month</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Subscription Plans -->
                    <div id="subscription-plans">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="dark:text-white font-heading text-xl font-semibold text-gray-800">Subscription Plans</h3>
                            
                            <div class="dark:bg-gray-700 flex items-center p-1 bg-gray-100 rounded-lg">
                                <button @click="billingCycle = 'monthly'" :class="billingCycle === 'monthly' ? 'bg-white dark:bg-gray-800 shadow-sm' : ''" class="dark:text-gray-300 focus:outline-none px-4 py-2 text-sm font-medium text-gray-700 transition-colors duration-200 rounded-md">
                                    Monthly
                                </button>
                                <button @click="billingCycle = 'annual'" :class="billingCycle === 'annual' ? 'bg-white dark:bg-gray-800 shadow-sm' : ''" class="dark:text-gray-300 focus:outline-none px-4 py-2 text-sm font-medium text-gray-700 transition-colors duration-200 rounded-md">
                                    Annual <span class="dark:text-green-400 text-green-600">(Save 20%)</span>
                                </button>
                            </div>
                        </div>
                        
                        <!-- Subscription plan cards remain the same -->
                        <div class="md:grid-cols-3 grid grid-cols-1 gap-6">
                            <!-- Basic Plan -->
                            <div class="dark:bg-gray-800 overflow-hidden bg-white border-t-4 border-gray-400 rounded-lg shadow">
                                <div class="p-6">
                                    <h4 class="dark:text-white font-heading text-xl font-semibold text-gray-800">Basic</h4>
                                    <p class="dark:text-gray-400 mt-1 text-gray-600">For individuals and small projects</p>
                                    
                                    <div class="mt-4">
                                        <span class="dark:text-white text-3xl font-bold text-gray-800" x-text="billingCycle === 'monthly' ? '$7' : '$67'"></span>
                                        <span class="dark:text-gray-400 text-gray-600" x-text="billingCycle === 'monthly' ? '/month' : '/year'"></span>
                                    </div>
                                    
                                    <div class="mt-6">
                                        <ul class="space-y-3">
                                            <li class="flex items-start">
                                                <i class="fas fa-check mt-1 mr-2 text-green-500"></i>
                                                <span class="dark:text-gray-300 text-gray-700">200 mins transcription</span>
                                            </li>
                                            <li class="flex items-start">
                                                <i class="fas fa-check mt-1 mr-2 text-green-500"></i>
                                                <span class="dark:text-gray-300 text-gray-700">50,000 input tokens</span>
                                            </li>
                                            <li class="flex items-start">
                                                <i class="fas fa-check mt-1 mr-2 text-green-500"></i>
                                                <span class="dark:text-gray-300 text-gray-700">50,000 output tokens</span>
                                            </li>
                                            <li class="flex items-start">
                                                <i class="fas fa-check mt-1 mr-2 text-green-500"></i>
                                                <span class="dark:text-gray-300 text-gray-700">Email support</span>
                                            </li>
                                            <li class="flex items-start">
                                                <i class="fas fa-check mt-1 mr-2 text-green-500"></i>
                                                <span class="dark:text-gray-300 text-gray-700">API access</span>
                                            </li>
                                        </ul>
                                    </div>
                                    
                                    <div class="mt-6">
                                        <button class="dark:bg-gray-700 hover:bg-gray-700 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 w-full px-4 py-2 text-white bg-gray-800 rounded-md">
                                            Current Plan
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Pro Plan -->
                            <div class="dark:bg-gray-800 border-primary-500 z-10 overflow-hidden transform scale-105 bg-white border-t-4 rounded-lg shadow">
                                <div class="-top-4 absolute inset-x-0 flex justify-center">
                                    <span class="bg-primary-500 px-4 py-1 text-xs font-semibold tracking-wider text-white uppercase rounded-full">Most Popular</span>
                                </div>
                                <div class="p-6">
                                    <h4 class="dark:text-white font-heading text-xl font-semibold text-gray-800">Pro</h4>
                                    <p class="dark:text-gray-400 mt-1 text-gray-600">For professionals and teams</p>
                                    
                                    <div class="mt-4">
                                        <span class="dark:text-white text-3xl font-bold text-gray-800" x-text="billingCycle === 'monthly' ? '$25' : '$240'"></span>
                                        <span class="dark:text-gray-400 text-gray-600" x-text="billingCycle === 'monthly' ? '/month' : '/year'"></span>
                                    </div>
                                    
                                    <div class="mt-6">
                                        <ul class="space-y-3">
                                            <li class="flex items-start">
                                                <i class="fas fa-check mt-1 mr-2 text-green-500"></i>
                                                <span class="dark:text-gray-300 text-gray-700">1,000 mins transcription</span>
                                            </li>
                                            <li class="flex items-start">
                                                <i class="fas fa-check mt-1 mr-2 text-green-500"></i>
                                                <span class="dark:text-gray-300 text-gray-700">250,000 input tokens</span>
                                            </li>
                                            <li class="flex items-start">
                                                <i class="fas fa-check mt-1 mr-2 text-green-500"></i>
                                                <span class="dark:text-gray-300 text-gray-700">250,000 output tokens</span>
                                            </li>
                                            <li class="flex items-start">
                                                <i class="fas fa-check mt-1 mr-2 text-green-500"></i>
                                                <span class="dark:text-gray-300 text-gray-700">Priority email support</span>
                                            </li>
                                            <li class="flex items-start">
                                                <i class="fas fa-check mt-1 mr-2 text-green-500"></i>
                                                <span class="dark:text-gray-300 text-gray-700">Advanced API features</span>
                                            </li>
                                            <li class="flex items-start">
                                                <i class="fas fa-check mt-1 mr-2 text-green-500"></i>
                                                <span class="dark:text-gray-300 text-gray-700">Usage analytics</span>
                                            </li>
                                        </ul>
                                    </div>
                                    
                                    <div class="mt-6">
                                        <button class="bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 w-full px-4 py-2 text-white rounded-md">
                                            Upgrade to Pro
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Enterprise Plan -->
                            <div class="dark:bg-gray-800 border-secondary-500 overflow-hidden bg-white border-t-4 rounded-lg shadow">
                                <div class="p-6">
                                    <h4 class="dark:text-white font-heading text-xl font-semibold text-gray-800">Enterprise</h4>
                                    <p class="dark:text-gray-400 mt-1 text-gray-600">For large organizations</p>
                                    
                                    <div class="mt-4">
                                        <span class="dark:text-white text-3xl font-bold text-gray-800">Custom</span>
                                    </div>
                                    
                                    <div class="mt-6">
                                        <ul class="space-y-3">
                                            <li class="flex items-start">
                                                <i class="fas fa-check mt-1 mr-2 text-green-500"></i>
                                                <span class="dark:text-gray-300 text-gray-700">Custom transcription minutes</span>
                                            </li>
                                            <li class="flex items-start">
                                                <i class="fas fa-check mt-1 mr-2 text-green-500"></i>
                                                <span class="dark:text-gray-300 text-gray-700">Custom token allocation</span>
                                            </li>
                                            <li class="flex items-start">
                                                <i class="fas fa-check mt-1 mr-2 text-green-500"></i>
                                                <span class="dark:text-gray-300 text-gray-700">Dedicated account manager</span>
                                            </li>
                                            <li class="flex items-start">
                                                <i class="fas fa-check mt-1 mr-2 text-green-500"></i>
                                                <span class="dark:text-gray-300 text-gray-700">24/7 priority support</span>
                                            </li>
                                            <li class="flex items-start">
                                                <i class="fas fa-check mt-1 mr-2 text-green-500"></i>
                                                <span class="dark:text-gray-300 text-gray-700">Custom integrations</span>
                                            </li>
                                            <li class="flex items-start">
                                                <i class="fas fa-check mt-1 mr-2 text-green-500"></i>
                                                <span class="dark:text-gray-300 text-gray-700">SLA guarantees</span>
                                            </li>
                                        </ul>
                                    </div>
                                    
                                    <div class="mt-6">
                                        <button class="bg-secondary-600 hover:bg-secondary-700 focus:outline-none focus:ring-2 focus:ring-secondary-500 w-full px-4 py-2 text-white rounded-md">
                                            Contact Sales
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.x.x/dist/alpine.min.js" defer></script>
</body>
</html> 
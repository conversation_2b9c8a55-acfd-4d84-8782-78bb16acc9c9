<!DOCTYPE html>
<html lang="en" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - VoiceHype Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&family=Roboto+Mono&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                fontFamily: {
                    sans: ['Inter', 'sans-serif'],
                    heading: ['Poppins', 'sans-serif'],
                    mono: ['Roboto Mono', 'monospace'],
                },
                extend: {
                    colors: {
                        primary: {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                            950: '#052e16',
                        },
                        secondary: {
                            50: '#f0fdfa',
                            100: '#ccfbf1',
                            200: '#99f6e4',
                            300: '#5eead4',
                            400: '#2dd4bf',
                            500: '#14b8a6',
                            600: '#0d9488',
                            700: '#0f766e',
                            800: '#115e59',
                            900: '#134e4a',
                            950: '#042f2e',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        [x-cloak] { display: none !important; }
        body { font-family: 'Inter', sans-serif; }
        h1, h2, h3, h4, h5, h6 { font-family: 'Poppins', sans-serif; }
        .font-mono { font-family: 'Roboto Mono', monospace; }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900 text-gray-800 dark:text-gray-200 transition-colors duration-200">
    <div x-data="{ sidebarOpen: false, darkMode: localStorage.getItem('darkMode') === 'true', activeTab: 'profile' }" 
         x-init="$watch('darkMode', val => { 
            localStorage.setItem('darkMode', val); 
            if (val) { document.documentElement.classList.add('dark') } 
            else { document.documentElement.classList.remove('dark') }
         }); 
         if (localStorage.getItem('darkMode') === 'true') { 
            darkMode = true;
            document.documentElement.classList.add('dark');
         }" 
         class="min-h-screen flex">
        <!-- Sidebar -->
        <div x-cloak :class="sidebarOpen ? 'block' : 'hidden'" @click="sidebarOpen = false" class="fixed inset-0 z-20 transition-opacity bg-black opacity-50 lg:hidden"></div>
        
        <div :class="sidebarOpen ? 'translate-x-0 ease-out' : '-translate-x-full ease-in'" class="fixed inset-y-0 left-0 z-30 w-64 overflow-y-auto transition duration-300 transform bg-primary-800 dark:bg-primary-950 lg:translate-x-0 lg:static lg:inset-0">
            <div class="flex items-center justify-center mt-8">
                <div class="flex items-center">
                    <span class="mx-2 text-2xl font-semibold text-white">VoiceHype</span>
                </div>
            </div>
            
            <nav class="mt-10">
                <a class="flex items-center px-6 py-2 mt-4 text-white hover:bg-primary-800" href="index.html">
                    <i class="fas fa-tachometer-alt mr-3"></i>
                    Dashboard
                </a>
                <a class="flex items-center px-6 py-2 mt-4 text-white hover:bg-primary-800" href="api-keys.html">
                    <i class="fas fa-key mr-3"></i>
                    API Keys
                </a>
                <a class="flex items-center px-6 py-2 mt-4 text-white hover:bg-primary-800" href="usage.html">
                    <i class="fas fa-chart-line mr-3"></i>
                    Usage History
                </a>
                <a class="flex items-center px-6 py-2 mt-4 text-white hover:bg-primary-800" href="subscription.html">
                    <i class="fas fa-credit-card mr-3"></i>
                    Subscription
                </a>
                <a class="flex items-center px-6 py-2 mt-4 text-white bg-primary-800" href="settings.html">
                    <i class="fas fa-cog mr-3"></i>
                    Settings
                </a>
            </nav>
        </div>
        
        <!-- Content -->
        <div class="flex-1 overflow-x-hidden overflow-y-auto">
            <!-- Navbar -->
            <header class="bg-white dark:bg-gray-800 shadow">
                <div class="container px-6 py-4 mx-auto">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <button @click="sidebarOpen = true" class="text-gray-500 dark:text-gray-300 focus:outline-none lg:hidden">
                                <i class="fas fa-bars text-lg"></i>
                            </button>
                        </div>
                        
                        <div class="flex items-center space-x-4">
                            <!-- Dark mode toggle -->
                            <button @click="darkMode = !darkMode" class="text-gray-500 dark:text-gray-300 focus:outline-none">
                                <i class="fas" :class="darkMode ? 'fa-sun' : 'fa-moon'"></i>
                            </button>
                            
                            <div x-data="{ dropdownOpen: false }" class="relative">
                                <button @click="dropdownOpen = !dropdownOpen" class="relative block w-8 h-8 overflow-hidden rounded-full shadow focus:outline-none">
                                    <img class="object-cover w-full h-full" src="https://ui-avatars.com/api/?name=User&background=16a34a&color=fff" alt="Your avatar">
                                </button>

                                <div x-cloak x-show="dropdownOpen" @click="dropdownOpen = false" class="fixed inset-0 z-10"></div>

                                <div x-cloak x-show="dropdownOpen" class="absolute right-0 z-10 w-48 mt-2 overflow-hidden bg-white dark:bg-gray-800 rounded-md shadow-xl">
                                    <a href="#profile" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-primary-600 hover:text-white">Profile</a>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-primary-600 hover:text-white">Logout</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Main content -->
            <main class="container px-6 py-8 mx-auto">
                <!-- Settings Section -->
                <section id="settings" class="mb-12">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-semibold text-gray-800 dark:text-gray-100 font-heading">Settings</h2>
                    </div>
                    
                    <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                        <div class="flex border-b border-gray-200 dark:border-gray-700">
                            <button @click="activeTab = 'profile'" :class="activeTab === 'profile' ? 'border-b-2 border-primary-500 text-primary-600 dark:text-primary-400' : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'" class="px-6 py-4 text-sm font-medium">
                                Profile
                            </button>
                            <button @click="activeTab = 'notifications'" :class="activeTab === 'notifications' ? 'border-b-2 border-primary-500 text-primary-600 dark:text-primary-400' : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'" class="px-6 py-4 text-sm font-medium">
                                Notifications
                            </button>
                            <button @click="activeTab = 'api'" :class="activeTab === 'api' ? 'border-b-2 border-primary-500 text-primary-600 dark:text-primary-400' : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'" class="px-6 py-4 text-sm font-medium">
                                API Defaults
                            </button>
                            <button @click="activeTab = 'security'" :class="activeTab === 'security' ? 'border-b-2 border-primary-500 text-primary-600 dark:text-primary-400' : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'" class="px-6 py-4 text-sm font-medium">
                                Security
                            </button>
                        </div>
                        
                        <!-- Profile Tab -->
                        <div x-show="activeTab === 'profile'" class="p-6">
                            <div class="flex flex-col md:flex-row">
                                <div class="md:w-1/3 mb-6 md:mb-0">
                                    <div class="flex flex-col items-center">
                                        <div class="w-32 h-32 overflow-hidden rounded-full mb-4">
                                            <img class="object-cover w-full h-full" src="https://ui-avatars.com/api/?name=John+Doe&background=16a34a&color=fff&size=128" alt="Profile picture">
                                        </div>
                                        <button class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 dark:bg-primary-700 dark:hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-500">
                                            Change Photo
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="md:w-2/3 md:pl-8">
                                    <div class="mb-4">
                                        <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Full Name</label>
                                        <input type="text" id="name" value="John Doe" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                                    </div>
                                    
                                    <div class="mb-4">
                                        <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Email Address</label>
                                        <input type="email" id="email" value="<EMAIL>" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                                    </div>
                                    
                                    <div class="mb-4">
                                        <label for="company" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Company (Optional)</label>
                                        <input type="text" id="company" value="Acme Inc." class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                                    </div>
                                    
                                    <div class="mb-4">
                                        <label for="timezone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Timezone</label>
                                        <select id="timezone" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                                            <option value="utc">UTC</option>
                                            <option value="est" selected>Eastern Time (EST/EDT)</option>
                                            <option value="cst">Central Time (CST/CDT)</option>
                                            <option value="mst">Mountain Time (MST/MDT)</option>
                                            <option value="pst">Pacific Time (PST/PDT)</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mt-6">
                                        <button class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 dark:bg-primary-700 dark:hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-500">
                                            Save Changes
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Notifications Tab -->
                        <div x-show="activeTab === 'notifications'" class="p-6">
                            <h3 class="text-lg font-medium text-gray-800 dark:text-gray-100 mb-4">Email Notifications</h3>
                            
                            <div class="space-y-4">
                                <div class="flex items-start">
                                    <div class="flex items-center h-5">
                                        <input id="billing" type="checkbox" checked class="h-4 w-4 text-primary-600 dark:text-primary-400 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded">
                                    </div>
                                    <div class="ml-3 text-sm dark:text-gray-300">
                                        <label for="billing" class="font-medium text-gray-700 dark:text-gray-300">Billing Updates</label>
                                        <p class="text-gray-500 dark:text-gray-500">Receive emails for subscription renewals, payment confirmations, and receipts.</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-start">
                                    <div class="flex items-center h-5">
                                        <input id="usage" type="checkbox" checked class="h-4 w-4 text-primary-600 dark:text-primary-400 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded">
                                    </div>
                                    <div class="ml-3 text-sm dark:text-gray-300">
                                        <label for="usage" class="font-medium text-gray-700 dark:text-gray-300">Usage Alerts</label>
                                        <p class="text-gray-500 dark:text-gray-500">Get notified when you reach 80% of your plan limits or when credits are running low.</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-start">
                                    <div class="flex items-center h-5">
                                        <input id="security" type="checkbox" checked class="h-4 w-4 text-primary-600 dark:text-primary-400 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded">
                                    </div>
                                    <div class="ml-3 text-sm dark:text-gray-300">
                                        <label for="security" class="font-medium text-gray-700 dark:text-gray-300">Security Alerts</label>
                                        <p class="text-gray-500 dark:text-gray-500">Receive notifications about new logins, API key creations, or suspicious activities.</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-start">
                                    <div class="flex items-center h-5">
                                        <input id="product" type="checkbox" class="h-4 w-4 text-primary-600 dark:text-primary-400 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded">
                                    </div>
                                    <div class="ml-3 text-sm dark:text-gray-300">
                                        <label for="product" class="font-medium text-gray-700 dark:text-gray-300">Product Updates</label>
                                        <p class="text-gray-500 dark:text-gray-500">Stay informed about new features, improvements, and service updates.</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-start">
                                    <div class="flex items-center h-5">
                                        <input id="marketing" type="checkbox" class="h-4 w-4 text-primary-600 dark:text-primary-400 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded">
                                    </div>
                                    <div class="ml-3 text-sm dark:text-gray-300">
                                        <label for="marketing" class="font-medium text-gray-700 dark:text-gray-300">Marketing Communications</label>
                                        <p class="text-gray-500 dark:text-gray-500">Receive promotional offers, newsletters, and other marketing materials.</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-6">
                                <button class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 dark:bg-primary-700 dark:hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-500">
                                    Save Preferences
                                </button>
                            </div>
                        </div>
                        
                        <!-- API Defaults Tab -->
                        <div x-show="activeTab === 'api'" class="p-6">
                            <h3 class="text-lg font-medium text-gray-800 dark:text-gray-100 mb-4">API Default Settings</h3>
                            
                            <div class="mb-6">
                                <h4 class="text-md font-medium text-gray-700 dark:text-gray-300 mb-3">Transcription Defaults</h4>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label for="transcription-model" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Default Transcription Model</label>
                                        <select id="transcription-model" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                                            <option value="whisper-1" selected>whisper-1 (Recommended)</option>
                                            <option value="large-v3">large-v3</option>
                                        </select>
                                    </div>
                                    
                                    <div>
                                        <label for="transcription-language" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Default Language</label>
                                        <select id="transcription-language" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                                            <option value="auto" selected>Auto-detect</option>
                                            <option value="en">English</option>
                                            <option value="es">Spanish</option>
                                            <option value="fr">French</option>
                                            <option value="de">German</option>
                                            <option value="ja">Japanese</option>
                                            <option value="zh">Chinese</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-6">
                                <h4 class="text-md font-medium text-gray-700 dark:text-gray-300 mb-3">Optimization Defaults</h4>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label for="optimization-model" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Default Optimization Model</label>
                                        <select id="optimization-model" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                                            <option value="llama-3-8b" selected>llama-3-8b</option>
                                            <option value="gpt-3.5-turbo">gpt-3.5-turbo</option>
                                            <option value="gpt-4o">gpt-4o</option>
                                        </select>
                                    </div>
                                    
                                    <div>
                                        <label for="optimization-prompt" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Default Optimization Prompt</label>
                                        <textarea id="optimization-prompt" rows="3" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">Improve this transcription by fixing grammar, removing filler words, and making it more readable while preserving the original meaning.</textarea>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-6">
                                <h4 class="text-md font-medium text-gray-700 dark:text-gray-300 mb-3">API Response Settings</h4>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label for="response-format" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Default Response Format</label>
                                        <select id="response-format" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                                            <option value="json" selected>JSON</option>
                                            <option value="text">Plain Text</option>
                                        </select>
                                    </div>
                                    
                                    <div>
                                        <label for="timeout" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Request Timeout (seconds)</label>
                                        <input type="number" id="timeout" value="60" min="10" max="300" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-6">
                                <button class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 dark:bg-primary-700 dark:hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-500">
                                    Save API Defaults
                                </button>
                            </div>
                        </div>
                        
                        <!-- Security Tab -->
                        <div x-show="activeTab === 'security'" class="p-6">
                            <h3 class="text-lg font-medium text-gray-800 dark:text-gray-100 mb-4">Security Settings</h3>
                            
                            <div class="mb-6">
                                <h4 class="text-md font-medium text-gray-700 dark:text-gray-300 mb-3">Password</h4>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label for="current-password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Current Password</label>
                                        <input type="password" id="current-password" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                                    </div>
                                    
                                    <div class="md:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label for="new-password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">New Password</label>
                                            <input type="password" id="new-password" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                                        </div>
                                        
                                        <div>
                                            <label for="confirm-password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Confirm New Password</label>
                                            <input type="password" id="confirm-password" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mt-4">
                                    <button class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 dark:bg-primary-700 dark:hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-500">
                                        Update Password
                                    </button>
                                </div>
                            </div>
                            
                            <div class="mb-6 border-t border-gray-200 pt-6">
                                <h4 class="text-md font-medium text-gray-700 dark:text-gray-300 mb-3">Two-Factor Authentication</h4>
                                
                                <div class="flex items-start mb-4">
                                    <div class="flex items-center h-5">
                                        <input id="enable-2fa" type="checkbox" class="h-4 w-4 text-primary-600 dark:text-primary-400 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded">
                                    </div>
                                    <div class="ml-3 text-sm dark:text-gray-300">
                                        <label for="enable-2fa" class="font-medium text-gray-700 dark:text-gray-300">Enable Two-Factor Authentication</label>
                                        <p class="text-gray-500 dark:text-gray-500">Add an extra layer of security to your account by requiring a verification code in addition to your password.</p>
                                    </div>
                                </div>
                                
                                <div class="mt-4">
                                    <button class="px-4 py-2 bg-white text-gray-700 dark:bg-gray-700 dark:text-gray-300 rounded-md border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-primary-500">
                                        Set Up Two-Factor Authentication
                                    </button>
                                </div>
                            </div>
                            
                            <div class="mb-6 border-t border-gray-200 pt-6">
                                <h4 class="text-md font-medium text-gray-700 dark:text-gray-300 mb-3">API Access Restrictions</h4>
                                
                                <div class="mb-4">
                                    <label for="ip-whitelist" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">IP Whitelist (Optional)</label>
                                    <textarea id="ip-whitelist" rows="2" placeholder="Enter IP addresses separated by commas" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"></textarea>
                                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-500">Leave empty to allow access from any IP address.</p>
                                </div>
                                
                                <div class="flex items-start mb-4">
                                    <div class="flex items-center h-5">
                                        <input id="rate-limiting" type="checkbox" checked class="h-4 w-4 text-primary-600 dark:text-primary-400 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded">
                                    </div>
                                    <div class="ml-3 text-sm dark:text-gray-300">
                                        <label for="rate-limiting" class="font-medium text-gray-700 dark:text-gray-300">Enable Rate Limiting</label>
                                        <p class="text-gray-500 dark:text-gray-500">Limit the number of API requests to prevent abuse and ensure fair usage.</p>
                                    </div>
                                </div>
                                
                                <div class="mt-4">
                                    <button class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 dark:bg-primary-700 dark:hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-500">
                                        Save Security Settings
                                    </button>
                                </div>
                            </div>
                            
                            <div class="border-t border-gray-200 pt-6">
                                <h4 class="text-md font-medium text-red-600 mb-3">Danger Zone</h4>
                                
                                <div class="bg-red-50 p-4 rounded-md">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-exclamation-triangle text-red-400"></i>
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="text-sm font-medium text-red-800">Delete Account</h3>
                                            <div class="mt-2 text-sm text-red-700">
                                                <p>Once you delete your account, there is no going back. All of your data will be permanently deleted.</p>
                                            </div>
                                            <div class="mt-4">
                                                <button class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
                                                    Delete Account
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
</body>
</html> 
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Terms of Service - VoiceHype</title>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Fix for Netlify base path issues -->
    <base href="/">

    <!-- Preload critical font files -->
    <link rel="preload" href="/landing-page/fonts/clash-display/ClashDisplay-Regular.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="/landing-page/fonts/clash-display/ClashDisplay-Bold.woff2" as="font" type="font/woff2" crossorigin>

    <!-- Preload critical CSS files -->
    <link rel="preload" href="/landing-page/styles.css" as="style">
    <link rel="preload" href="/landing-page/responsive-fixes.css" as="style">

    <link href="/landing-page/fonts.css" rel="stylesheet">

    <!-- Main Landing Page CSS -->
    <link rel="stylesheet" href="/landing-page/styles.css">

    <!-- Responsive fixes for mobile -->
    <link rel="stylesheet" href="/landing-page/responsive-fixes.css">

    <!-- Policy Page Specific Styles -->
    <style>
        body {
            background-color: #080814;
            color: #ffffff;
            font-family: 'Inter', sans-serif;
        }

        .policy-page {
            width: 100%;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        main {
            flex: 1;
            padding: 3rem 0;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        .logo-text, .footer-logo-text {
            font-family: 'Clash Display', 'Inter', system-ui, sans-serif;
            font-weight: 700;
        }

        .hype-text {
            color: #14F195;
        }

        .hype-text-green {
            color: #14F195;
        }

        .signup-button {
            color: #000 !important;
            background-color: #14F195 !important;
        }

        .prose {
            color: #ffffff;
            line-height: 1.6;
            width: 100%;
            max-width: 800px;
        }

        .prose h1 {
            font-size: 2.5rem;
            margin-bottom: 2rem;
            color: #ffffff;
        }

        .prose h2 {
            color: #f0f0f0;
            margin-top: 2rem;
            font-size: 1.75rem;
        }

        .prose h3 {
            color: #f0f0f0;
            margin-top: 1.5rem;
            font-size: 1.35rem;
        }

        .prose p, .prose li, .prose strong {
            color: #ffffff;
            margin-bottom: 1rem;
        }

        .prose ul, .prose ol {
            margin-bottom: 1.5rem;
            padding-left: 2rem;
        }

        .footer-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            flex-wrap: wrap;
            gap: 3rem;
            width: 100%;
        }

        .footer-logo-section {
            flex: 1;
            min-width: 250px;
            margin-right: 2rem;
        }

        .footer-links {
            flex: 2;
        }

        .footer-links-container {
            display: flex;
            flex-wrap: wrap;
            gap: 2rem;
        }

        @media (max-width: 768px) {
            .footer-content {
                flex-direction: column;
                gap: 2.5rem;
            }

            .footer-links-container {
                flex-direction: column;
                gap: 1.5rem;
            }

            .footer-logo-section {
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    <div class="policy-page">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <div class="logo-container">
                        <svg width="40" height="30" viewBox="0 0 40 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <!-- Monitor outline with white fill -->
                            <rect x="2" y="2" width="36" height="20" rx="2" fill="white" />
                            <rect x="2" y="2" width="36" height="20" rx="2" stroke="#14F195" stroke-width="1.5" />
                            <!-- Sound wave visualization -->
                            <path d="M7 14 L10 9 L13 14 L16 9 L19 14 L22 9 L25 14 L28 9 L31 14 L34 9" stroke="#14F195"
                                stroke-width="1.5" stroke-linecap="round" />
                            <!-- Stand -->
                            <line x1="20" y1="22" x2="20" y2="26" stroke="#14F195" stroke-width="1.5" />
                            <line x1="14" y1="26" x2="26" y2="26" stroke="#14F195" stroke-width="1.5" />
                        </svg>
                    </div>
                    <h1 class="logo-text">Voice<span class="hype-text-green">Hype</span></h1>
                </div>
                <div class="nav-buttons">
                    <a href="#" class="nav-button login-button">Log In</a>
                    <a href="#" class="nav-button signup-button">Sign Up</a>
                </div>
                <button class="mobile-menu-toggle">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </button>
            </div>
        </nav>

        <main>
            <div class="container">
                <div class="prose">
                    <h1>Terms of Service</h1>
                    <p>Last Updated: May 10, 2025</p>

                    <h2>1. Introduction</h2>
                    <p>Welcome to VoiceHype. VoiceHype is operated as a partnership between Kifayat Ullah and Bilal Tariq ("we," "our," or "us"). By accessing or using our voice-to-prompt service ("Service"), you agree to be bound by these Terms of Service ("Terms"). If you disagree with any part of these Terms, you may not access our Service.</p>

                    <h2>2. Service Description</h2>
                    <p>VoiceHype provides a tool designed to supercharge workflows with Large Language Models (LLMs), particularly for developers and coders. Our Service includes:</p>
                    <ul>
                        <li>Transcription: Converting speech to text</li>
                        <li>Optimization: Processing raw speech into clean, optimized prompts for LLMs</li>
                    </ul>

                    <h2>3. Account Registration</h2>
                    <p>To use certain features of our Service, you may be required to register for an account. You agree to provide accurate, current, and complete information during the registration process and to update such information to keep it accurate, current, and complete.</p>

                    <h2>4. User Responsibilities</h2>
                    <p>When using our Service, you agree:</p>
                    <ul>
                        <li>To use the Service in compliance with all applicable laws and regulations</li>
                        <li>Not to attempt to gain unauthorized access to any portion of the Service</li>
                        <li>Not to interfere with or disrupt the integrity or performance of the Service</li>
                        <li>Not to use the Service for any illegal or harmful purposes</li>
                        <li>Not to use the Service to generate or disseminate content that promotes hatred, violence, or discrimination</li>
                    </ul>

                    <h2>5. Free Tier and Payment Terms</h2>
                    <h3>5.1 Free Tier</h3>
                    <p>We currently offer a free tier that includes:</p>
                    <ul>
                        <li>60 minutes of transcription</li>
                        <li>10,000 tokens for exploration</li>
                    </ul>

                    <h3>5.2 Payment</h3>
                    <p>For any paid services we may offer in the future:</p>
                    <ul>
                        <li>All payments will be processed securely through Paddle</li>
                        <li>Prices will be displayed clearly before purchase</li>
                        <li>You agree to pay all charges at the prices then in effect for your purchases</li>
                    </ul>

                    <h2>6. Refund Policy</h2>
                    <p>We offer refunds only in cases of accidental purchases (e.g., typing 95 instead of 9.5). To request a refund:</p>
                    <ul>
                        <li>Contact <NAME_EMAIL> within 7 days of purchase</li>
                        <li>Explain the nature of the error</li>
                        <li>Provide proof of purchase</li>
                    </ul>
                    <p>We do not offer refunds for any other reason.</p>

                    <h2>7. Intellectual Property</h2>
                    <p>All content, features, and functionality of the Service are owned by VoiceHype and are protected by international copyright, trademark, patent, trade secret, and other intellectual property laws.</p>

                    <h2>8. Limitation of Liability</h2>
                    <p>To the maximum extent permitted by law, VoiceHype shall not be liable for any indirect, incidental, special, consequential, or punitive damages, or any loss of profits or revenues, whether incurred directly or indirectly, or any loss of data, use, goodwill, or other intangible losses resulting from:</p>
                    <ul>
                        <li>Your use or inability to use the Service</li>
                        <li>Any unauthorized access to or use of our servers and/or any personal information stored therein</li>
                        <li>Any interruption or cessation of transmission to or from the Service</li>
                    </ul>

                    <h2>9. Termination</h2>
                    <p>We may terminate or suspend your account and access to the Service immediately, without prior notice or liability, for any reason whatsoever, including, without limitation, if you breach these Terms.</p>

                    <h2>10. Changes to Terms</h2>
                    <p>We reserve the right to modify or replace these Terms at any time. If a revision is material, we will provide at least 30 days' notice prior to any new terms taking effect.</p>

                    <h2>11. Company Information</h2>
                    <p>VoiceHype is operated as a partnership between:</p>
                    <ul>
                        <li>Kifayat Ullah (Co-founder)</li>
                        <li>Bilal Tariq (Co-founder)</li>
                    </ul>
                    <p>We are not currently registered as a formal business entity but operate as a partnership between the individuals named above.</p>

                    <h2>12. Governing Law</h2>
                    <p>These Terms shall be governed by and construed in accordance with the laws of the jurisdiction in which VoiceHype is established, without regard to its conflict of law provisions.</p>

                    <h2>13. Contact Information</h2>
                    <p>For any questions about these Terms, please contact us at:</p>
                    <ul>
                        <li>Email: <EMAIL></li>
                    </ul>
                </div>
            </div>
        </main>

        <footer class="footer-section">
            <div class="footer-container">
                <div class="footer-content">
                    <div class="footer-logo-section">
                        <div class="footer-logo">
                            <div class="logo-container">
                                <svg width="40" height="30" viewBox="0 0 40 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect x="2" y="2" width="36" height="20" rx="2" fill="white" />
                                    <rect x="2" y="2" width="36" height="20" rx="2" stroke="#14F195" stroke-width="1.5" />
                                    <path d="M7 14 L10 9 L13 14 L16 9 L19 14 L22 9 L25 14 L28 9 L31 14 L34 9" stroke="#14F195"
                                        stroke-width="1.5" stroke-linecap="round" />
                                    <line x1="20" y1="22" x2="20" y2="26" stroke="#14F195" stroke-width="1.5" />
                                    <line x1="14" y1="26" x2="26" y2="26" stroke="#14F195" stroke-width="1.5" />
                                </svg>
                            </div>
                            <h3 class="footer-logo-text">Voice<span class="hype-text-green">Hype</span></h3>
                        </div>
                        <p class="footer-tagline">Revolutionizing coding with voice-to-prompt technology</p>
                    </div>

                    <!-- Footer links with a more structured layout for mobile -->
                    <div class="footer-links">
                        <div class="footer-links-container">
                            <div class="footer-links-column">
                                <h4>Product</h4>
                                <ul>
                                    <li><a href="/#features">Features</a></li>
                                    <li><a href="/#pricing-section">Pricing</a></li>
                                    <li><a href="/#use-cases">Use Cases</a></li>
                                    <li><a href="/#blog-section">Blog</a></li>
                                    <li><a href="/#faq">FAQ</a></li>
                                </ul>
                            </div>
                            <div class="footer-links-column">
                                <h4>Legal</h4>
                                <ul>
                                    <li><a href="/privacy-policy.html">Privacy Policy</a></li>
                                    <li><a href="/terms-of-service.html">Terms of Service</a></li>
                                    <li><a href="/refund-policy.html">Refund Policy</a></li>
                                    <li><a href="/security.html">Security</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <div class="footer-bottom-container">
                    <p class="copyright">
                        <!-- 2025 VoiceHype. All rights reserved. -->
                    </p>
                    <div class="contact-info">
                        <a href="mailto:<EMAIL>" class="contact-email">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z">
                                </path>
                                <polyline points="22,6 12,13 2,6"></polyline>
                            </svg>
                            <EMAIL>
                        </a>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- Mobile menu functionality -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM fully loaded - setting up mobile menu');

            // Mobile menu toggle functionality
            const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
            const navButtons = document.querySelector('.nav-buttons');
            const body = document.body;

            console.log('Mobile menu elements:', {
                mobileMenuToggle: mobileMenuToggle,
                navButtons: navButtons
            });

            if (mobileMenuToggle) {
                mobileMenuToggle.addEventListener('click', function() {
                    console.log('Mobile menu toggle clicked');
                    mobileMenuToggle.classList.toggle('active');

                    // Toggle body scroll lock
                    body.classList.toggle('menu-open');

                    // Handle nav buttons visibility
                    if (navButtons) {
                        if (mobileMenuToggle.classList.contains('active')) {
                            navButtons.style.position = 'fixed';
                            navButtons.style.top = '80px';
                            navButtons.style.left = '0';
                            navButtons.style.width = '100%';
                            navButtons.style.flexDirection = 'column';
                            navButtons.style.alignItems = 'center';
                            navButtons.style.gap = '1rem';
                            navButtons.style.padding = '1.5rem 0';
                            navButtons.style.backgroundColor = 'rgba(0, 0, 0, 0.9)';
                            navButtons.style.backdropFilter = 'blur(10px)';
                            navButtons.style.zIndex = '1000';
                            console.log('Navigation buttons shown');
                        } else {
                            navButtons.style.position = '';
                            navButtons.style.top = '';
                            navButtons.style.left = '';
                            navButtons.style.width = '';
                            navButtons.style.flexDirection = '';
                            navButtons.style.alignItems = '';
                            navButtons.style.gap = '';
                            navButtons.style.padding = '';
                            navButtons.style.backgroundColor = '';
                            navButtons.style.backdropFilter = '';
                            navButtons.style.zIndex = '';
                            console.log('Navigation buttons hidden');
                        }
                    }
                });
            } else {
                console.error('Mobile menu toggle button not found!');
            }

            // Handle blog links to scroll to blog section on landing page
            document.querySelectorAll('a[href*="blog"], a[href="/#blog-section"]').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Navigate to landing page with blog section hash
                    window.location.href = '/#blog-section';

                    return false;
                });
            });
        });
    </script>
</body>
</html>
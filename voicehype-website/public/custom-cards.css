/* Custom styling for use case cards on the landing page */
.user-type-card {
  background: linear-gradient(145deg, rgba(20, 241, 149, 0.04) 0%, rgba(20, 241, 149, 0.01) 100%);
  border: none;
  border-radius: 1.25rem;
  padding: 2.5rem;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: visible;
  backdrop-filter: blur(12px);
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.2);
  isolation: isolate;
}

.user-type-card::before {
  content: '';
  position: absolute;
  inset: -2px;
  padding: 2px;
  background: linear-gradient(
    90deg,
    transparent,
    transparent,
    #14F195,
    #9ef4ff,
    #14F195,
    transparent,
    transparent
  );
  background-size: 200% 100%;
  border-radius: 1.35rem;
  -webkit-mask: 
    linear-gradient(#fff 0 0) content-box, 
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask: 
    linear-gradient(#fff 0 0) content-box, 
    linear-gradient(#fff 0 0);
  mask-composite: exclude;
  animation: border-travel 3s linear infinite;
  z-index: -1;
}

.user-type-card::after {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(145deg, rgba(20, 241, 149, 0.08) 0%, rgba(20, 241, 149, 0.02) 100%);
  border-radius: 1.25rem;
  z-index: -1;
}

@keyframes border-travel {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.user-type-card:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow: 0 20px 40px rgba(20, 241, 149, 0.2);
}

.user-type-card:hover::before {
  animation: border-travel 1.5s linear infinite;
  background: linear-gradient(
    90deg,
    transparent,
    transparent,
    #14F195,
    #9ef4ff,
    #14F195,
    transparent,
    transparent
  );
  background-size: 200% 100%;
}

.user-type-header {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
  position: relative;
  z-index: 1;
}

.user-type-header h3 {
  font-family: 'Clash Display', sans-serif;
  font-weight: 600;
  font-size: 1.75rem;
  background: linear-gradient(90deg, #14F195 0%, #9ef4ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  position: relative;
  transition: all 0.4s ease;
}

.user-type-card:hover .user-type-header h3 {
  transform: scale(1.05);
  background: linear-gradient(90deg, #14F195 20%, #9ef4ff 80%);
  -webkit-background-clip: text;
  background-clip: text;
}

.user-type-content {
  position: relative;
  z-index: 1;
  transition: transform 0.4s ease;
}

.user-type-card:hover .user-type-content {
  transform: translateY(-4px);
}

.user-icon {
  width: 4rem;
  height: 4rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(20, 241, 149, 0.08);
  margin-right: 1.5rem;
  position: relative;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.user-icon::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 1px solid rgba(20, 241, 149, 0.2);
  animation: pulse-glow 3s infinite cubic-bezier(0.4, 0, 0.2, 1);
}

.user-icon::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: radial-gradient(circle at center, rgba(20, 241, 149, 0.15), transparent 70%);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.user-icon svg {
  width: 2rem;
  height: 2rem;
  fill: #14F195;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 0 8px rgba(20, 241, 149, 0.3));
}

.user-type-card:hover .user-icon {
  background: rgba(20, 241, 149, 0.12);
  transform: scale(1.1) rotate(8deg);
}

.user-type-card:hover .user-icon::before {
  animation-duration: 1.5s;
  border-color: rgba(20, 241, 149, 0.4);
}

.user-type-card:hover .user-icon::after {
  opacity: 1;
}

.user-type-card:hover .user-icon svg {
  transform: scale(1.2);
  filter: drop-shadow(0 0 12px rgba(20, 241, 149, 0.4));
}

.user-description {
  margin-bottom: 2rem;
  transition: all 0.4s ease;
}

.user-description p {
  font-size: 1.1rem;
  line-height: 1.7;
  color: rgba(255, 255, 255, 0.85);
  margin: 0;
  transition: color 0.4s ease;
}

.user-type-card:hover .user-description p {
  color: rgba(255, 255, 255, 0.95);
}

.user-quote {
  background: rgba(20, 241, 149, 0.04);
  border-left: 3px solid #14F195;
  padding: 1.5rem;
  border-radius: 0 1rem 1rem 0;
  margin-bottom: 2rem;
  position: relative;
  transition: all 0.4s ease;
  box-shadow: inset 0 0 20px rgba(20, 241, 149, 0.02);
}

.user-quote::before {
  content: '"';
  position: absolute;
  top: -0.75rem;
  left: 0.75rem;
  font-size: 3.5rem;
  font-family: Georgia, serif;
  color: rgba(20, 241, 149, 0.2);
  line-height: 1;
  transition: all 0.4s ease;
}

.user-type-card:hover .user-quote {
  background: rgba(20, 241, 149, 0.06);
  border-left-width: 4px;
  transform: translateX(4px);
}

.user-type-card:hover .user-quote::before {
  color: rgba(20, 241, 149, 0.3);
  transform: scale(1.1) rotate(-8deg);
}

.user-quote blockquote {
  font-style: italic;
  font-size: 1.05rem;
  line-height: 1.7;
  margin: 0;
  color: rgba(255, 255, 255, 0.9);
  transition: color 0.4s ease;
}

.user-type-card:hover .user-quote blockquote {
  color: rgba(255, 255, 255, 1);
}

.common-tasks {
  margin-top: 2rem;
  transition: transform 0.4s ease;
}

.common-tasks h4 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: rgba(255, 255, 255, 0.95);
  transition: color 0.4s ease;
}

.common-tasks ul {
  padding-left: 1.75rem;
  margin-top: 0.75rem;
}

.common-tasks li {
  margin-bottom: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  position: relative;
  transition: all 0.4s ease;
  transform-origin: left;
}

.common-tasks li::marker {
  color: #14F195;
}

.user-type-card:hover .common-tasks li {
  color: rgba(255, 255, 255, 0.9);
  transform: translateX(4px);
}

.user-type-card:hover .common-tasks h4 {
  color: rgba(255, 255, 255, 1);
}

/* Animation keyframes */
@keyframes pulse-glow {
  0% {
    transform: scale(1);
    opacity: 0.4;
    border-color: rgba(20, 241, 149, 0.4);
  }
  50% {
    transform: scale(1.15);
    opacity: 0.2;
    border-color: rgba(158, 244, 255, 0.4);
  }
  100% {
    transform: scale(1);
    opacity: 0.4;
    border-color: rgba(20, 241, 149, 0.4);
  }
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .user-type-card {
    padding: 2rem;
  }
  
  .user-quote {
    padding: 1.25rem;
  }
  
  .common-tasks {
    margin-top: 1.5rem;
  }
  
  .user-type-header h3 {
    font-size: 1.5rem;
  }
}

@media (max-width: 768px) {
  .user-description p {
    font-size: 1rem;
  }
  
  .user-quote blockquote {
    font-size: 1rem;
  }
  
  .user-icon {
    width: 3.5rem;
    height: 3.5rem;
  }
  
  .user-icon svg {
    width: 1.75rem;
    height: 1.75rem;
  }
}

@media (max-width: 480px) {
  .user-type-card {
    padding: 1.5rem;
  }
  
  .user-icon {
    width: 3rem;
    height: 3rem;
  }
  
  .user-icon svg {
    width: 1.5rem;
    height: 1.5rem;
  }
  
  .user-quote {
    padding: 1rem;
  }
  
  .common-tasks h4 {
    font-size: 1.1rem;
  }
  
  .common-tasks li {
    font-size: 0.95rem;
  }
  
  .user-type-header h3 {
    font-size: 1.35rem;
  }
} 
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Refund Policy - VoiceHype</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Fix for Netlify base path issues -->
    <base href="/">

    <!-- Preload critical font files -->
    <link rel="preload" href="/landing-page/fonts/clash-display/ClashDisplay-Regular.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="/landing-page/fonts/clash-display/ClashDisplay-Bold.woff2" as="font" type="font/woff2" crossorigin>

    <!-- Preload critical CSS files -->
    <link rel="preload" href="/landing-page/styles.css" as="style">
    <link rel="preload" href="/landing-page/responsive-fixes.css" as="style">

    <link href="/landing-page/fonts.css" rel="stylesheet">
    
    <!-- Main Landing Page CSS -->
    <link rel="stylesheet" href="/landing-page/styles.css">
    
    <!-- Responsive fixes for mobile -->
    <link rel="stylesheet" href="/landing-page/responsive-fixes.css">
    
    <!-- Policy Page Specific Styles -->
    <style>
        body {
            background-color: #080814;
            color: #ffffff;
            font-family: 'Inter', sans-serif;
        }
        
        .policy-page {
            width: 100%;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        main {
            flex: 1;
            padding: 3rem 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        
        .logo-text, .footer-logo-text {
            font-family: 'Clash Display', 'Inter', system-ui, sans-serif;
            font-weight: 700;
        }
        
        .hype-text {
            color: #14F195;
        }
        
        .hype-text-green {
            color: #14F195;
        }
        
        .signup-button {
            color: #000 !important;
            background-color: #14F195 !important;
        }
        
        .prose {
            color: #ffffff;
            line-height: 1.6;
            width: 100%;
            max-width: 800px;
        }
        
        .prose h1 {
            font-size: 2.5rem;
            margin-bottom: 2rem;
            color: #ffffff;
        }
        
        .prose h2 {
            color: #f0f0f0;
            margin-top: 2rem;
            font-size: 1.75rem;
        }
        
        .prose h3 {
            color: #f0f0f0;
            margin-top: 1.5rem;
            font-size: 1.35rem;
        }
        
        .prose p, .prose li, .prose strong {
            color: #ffffff;
            margin-bottom: 1rem;
        }
        
        .prose ul, .prose ol {
            margin-bottom: 1.5rem;
            padding-left: 2rem;
        }
    </style>
</head>
<body>
    <div class="policy-page">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <div class="logo-container">
                        <svg width="40" height="30" viewBox="0 0 40 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <!-- Monitor outline with white fill -->
                            <rect x="2" y="2" width="36" height="20" rx="2" fill="white" />
                            <rect x="2" y="2" width="36" height="20" rx="2" stroke="#14F195" stroke-width="1.5" />
                            <!-- Sound wave visualization -->
                            <path d="M7 14 L10 9 L13 14 L16 9 L19 14 L22 9 L25 14 L28 9 L31 14 L34 9" stroke="#14F195"
                                stroke-width="1.5" stroke-linecap="round" />
                            <!-- Stand -->
                            <line x1="20" y1="22" x2="20" y2="26" stroke="#14F195" stroke-width="1.5" />
                            <line x1="14" y1="26" x2="26" y2="26" stroke="#14F195" stroke-width="1.5" />
                        </svg>
                    </div>
                    <h1 class="logo-text">Voice<span class="hype-text-green">Hype</span></h1>
                </div>
                <div class="nav-buttons">
                    <a href="#" class="nav-button login-button">Log In</a>
                    <a href="#" class="nav-button signup-button">Sign Up</a>
                </div>
                <button class="mobile-menu-toggle">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </button>
            </div>
        </nav>
        
        <main>
            <div class="container">
                <div class="prose">
                    <h1>Refund Policy</h1>
                    <p>Last Updated: April 9, 2025</p>
                    
                    <h2>Overview</h2>
                    <p>At VoiceHype, we strive to provide high-quality service and ensure our users' satisfaction. This Refund Policy outlines the circumstances under which refunds may be issued for purchases made on our platform.</p>
                    
                    <h2>Free Service Tier</h2>
                    <p>Currently, VoiceHype offers a free tier with the following limits:</p>
                    <ul>
                        <li>60 minutes of transcription</li>
                        <li>10,000 tokens for exploration</li>
                    </ul>
                    <p>No payment is required for the free tier, and therefore no refunds apply.</p>
                    
                    <h2>Paid Services and Credits</h2>
                    <p>For any paid services or credits purchased:</p>
                    
                    <h3>Eligibility for Refunds</h3>
                    <p>VoiceHype offers refunds only in cases of accidental purchases. Examples include:</p>
                    <ul>
                        <li>Entering an incorrect amount (e.g., typing 95 instead of 9.5)</li>
                        <li>Unintentional duplicate purchases</li>
                        <li>Technical errors during the payment process</li>
                    </ul>
                    
                    <h3>Refund Request Process</h3>
                    <p>To request a refund:</p>
                    <ol>
                        <li>Email <EMAIL> within 7 days of purchase</li>
                        <li>Include your account information and order/transaction ID</li>
                        <li>Clearly explain the reason for your refund request</li>
                        <li>Provide any supporting documentation if applicable</li>
                    </ol>
                    
                    <h3>Review Process</h3>
                    <p>All refund requests will be reviewed on a case-by-case basis. We will respond to your request within 5 business days.</p>
                    
                    <h3>Approval and Processing</h3>
                    <p>If your refund request is approved:</p>
                    <ul>
                        <li>Credits will be returned to your account, or</li>
                        <li>The purchase amount will be refunded to your original payment method</li>
                        <li>Processing time may vary depending on your payment provider (typically 5-10 business days)</li>
                    </ul>
                    
                    <h3>Non-Refundable Circumstances</h3>
                    <p>Refunds will NOT be issued in the following situations:</p>
                    <ul>
                        <li>Requests made after the 7-day window</li>
                        <li>Dissatisfaction with the service after use</li>
                        <li>Purchased credits that have been partially or fully used</li>
                        <li>Violations of our Terms of Service</li>
                        <li>Any reason other than accidental purchases as described above</li>
                    </ul>
                    
                    <h2>Future Subscription Plans</h2>
                    <p>As stated in our service information, subscription plans are in development. Once launched, specific refund terms for subscription services will be added to this policy.</p>
                    
                    <h2>Policy Updates</h2>
                    <p>We reserve the right to modify this Refund Policy at any time. Changes will be effective immediately upon posting to our website.</p>
                    
                    <h2>Contact Information</h2>
                    <p>If you have any questions about our Refund Policy, please contact us:</p>
                    <ul>
                        <li>Email: <EMAIL></li>
                    </ul>
                </div>
            </div>
        </main>
        
        <footer class="footer-section">
            <div class="footer-container">
                <div class="footer-content">
                    <div class="footer-logo-section">
                        <div class="footer-logo">
                            <div class="logo-container">
                                <svg width="40" height="30" viewBox="0 0 40 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect x="2" y="2" width="36" height="20" rx="2" fill="white" />
                                    <rect x="2" y="2" width="36" height="20" rx="2" stroke="#14F195" stroke-width="1.5" />
                                    <path d="M7 14 L10 9 L13 14 L16 9 L19 14 L22 9 L25 14 L28 9 L31 14 L34 9" stroke="#14F195"
                                        stroke-width="1.5" stroke-linecap="round" />
                                    <line x1="20" y1="22" x2="20" y2="26" stroke="#14F195" stroke-width="1.5" />
                                    <line x1="14" y1="26" x2="26" y2="26" stroke="#14F195" stroke-width="1.5" />
                                </svg>
                            </div>
                            <h3 class="footer-logo-text">Voice<span class="hype-text-green">Hype</span></h3>
                        </div>
                        <p class="footer-tagline">Revolutionizing coding with voice-to-prompt technology</p>
                    </div>

                    <!-- Footer links with a more structured layout for mobile -->
                    <div class="footer-links">
                        <div class="footer-links-container">
                            <div class="footer-links-column">
                                <h4>Product</h4>
                                <ul>
                                    <li><a href="/#features">Features</a></li>
                                    <li><a href="/#pricing-section">Pricing</a></li>
                                    <li><a href="/#use-cases">Use Cases</a></li>
                                    <li><a href="/#blog-section">Blog</a></li>
                                    <li><a href="/#faq">FAQ</a></li>
                                </ul>
                            </div>
                            <div class="footer-links-column">
                                <h4>Legal</h4>
                                <ul>
                                    <li><a href="/privacy-policy.html">Privacy Policy</a></li>
                                    <li><a href="/terms-of-service.html">Terms of Service</a></li>
                                    <li><a href="/refund-policy.html">Refund Policy</a></li>
                                    <li><a href="/security.html">Security</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <div class="footer-bottom-container">
                    <p class="copyright">
                        <!-- 2025 VoiceHype. All rights reserved. -->
                    </p>
                    <div class="contact-info">
                        <a href="mailto:<EMAIL>" class="contact-email">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z">
                                </path>
                                <polyline points="22,6 12,13 2,6"></polyline>
                            </svg>
                            <EMAIL>
                        </a>
                    </div>
                </div>
            </div>
        </footer>
    </div>
    
    <!-- Mobile menu functionality -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM fully loaded - setting up mobile menu');
            
            // Mobile menu toggle functionality
            const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
            const navButtons = document.querySelector('.nav-buttons');
            const body = document.body;
            
            console.log('Mobile menu elements:', {
                mobileMenuToggle: mobileMenuToggle,
                navButtons: navButtons
            });
            
            if (mobileMenuToggle) {
                mobileMenuToggle.addEventListener('click', function() {
                    console.log('Mobile menu toggle clicked');
                    mobileMenuToggle.classList.toggle('active');
                    
                    // Toggle body scroll lock
                    body.classList.toggle('menu-open');
                    
                    // Handle nav buttons visibility
                    if (navButtons) {
                        if (mobileMenuToggle.classList.contains('active')) {
                            navButtons.style.position = 'fixed';
                            navButtons.style.top = '80px';
                            navButtons.style.left = '0';
                            navButtons.style.width = '100%';
                            navButtons.style.flexDirection = 'column';
                            navButtons.style.alignItems = 'center';
                            navButtons.style.gap = '1rem';
                            navButtons.style.padding = '1.5rem 0';
                            navButtons.style.backgroundColor = 'rgba(0, 0, 0, 0.9)';
                            navButtons.style.backdropFilter = 'blur(10px)';
                            navButtons.style.zIndex = '1000';
                            console.log('Navigation buttons shown');
                        } else {
                            navButtons.style.position = '';
                            navButtons.style.top = '';
                            navButtons.style.left = '';
                            navButtons.style.width = '';
                            navButtons.style.flexDirection = '';
                            navButtons.style.alignItems = '';
                            navButtons.style.gap = '';
                            navButtons.style.padding = '';
                            navButtons.style.backgroundColor = '';
                            navButtons.style.backdropFilter = '';
                            navButtons.style.zIndex = '';
                            console.log('Navigation buttons hidden');
                        }
                    }
                });
            } else {
                console.error('Mobile menu toggle button not found!');
            }
        });

        // Handle blog links to scroll to blog section on landing page
        document.querySelectorAll('a[href*="blog"], a[href="/#blog-section"]').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Navigate to landing page with blog section hash
                window.location.href = '/#blog-section';
                
                return false;
            });
        });
    </script>
</body>
</html> 
/* Specific navbar fixes for the landing page */

.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: rgba(8, 8, 20, 0.8);
  backdrop-filter: blur(10px);
  padding: 0.75rem 0;
  height: auto;
}

.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.nav-logo {
  display: flex;
  align-items: center;
  height: auto;
  margin: 0;
  padding: 0;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo-text {
  font-family: var(--font-display, 'Clash Display', 'Inter', system-ui, sans-serif);
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  padding: 0;
  line-height: 1;
}

.nav-links {
  display: flex;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-links li {
  margin: 0 0.75rem;
}

.nav-link {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  position: relative;
  font-weight: 500;
  transition: color 0.3s ease;
  padding: 0;
}

.nav-buttons {
  display: flex;
  align-items: center;
}

.nav-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.35rem 0.8rem;
  text-decoration: none;
  border-radius: 4px;
  font-weight: 500;
  font-size: 0.85rem;
  transition: all 0.3s ease;
  margin-left: 0.5rem;
} 
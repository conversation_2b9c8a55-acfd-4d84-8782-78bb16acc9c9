# Font Implementation Guide - CORS Issue Fix

## Problem Solved
We've fixed the CORS issue with Clash Display font by implementing a self-hosted solution instead of loading directly from Fontshare. This eliminates the cross-origin restriction that was blocking the font in production.

## Implementation Overview

1. Created a `fonts.css` file with proper `@font-face` declarations for the Clash Display font in all needed weights.

2. Created a directory structure to store the font files:
   ```
   public/landing-page/fonts/clash-display/
   ```

3. Added font file references to `index.html` with preloading for critical weights.

4. Updated the Vue components to load the self-hosted font instead of using Fontshare:
   - Modified `LandingPageView.vue` to remove Fontshare references and add self-hosted fonts
   - Updated `ModularLandingPageView.vue` to include the fonts.css file

## Important Font License Note

When using self-hosted fonts from Fontshare (or any font provider), always ensure you comply with their licensing terms. The Clash Display font from Fontshare is free for personal and commercial use, but you should still follow their terms:

- Include attribution when required
- Don't redistribute the font files as standalone downloads
- Don't modify and redistribute the font files

More information can be found on the [Fontshare website](https://www.fontshare.com/licenses/trial-license).

## How the Fix Works

1. **Preloading**: We preload the most frequently used font weights to improve loading performance.
2. **Self-hosting**: By serving the font files from your own domain, you avoid CORS restrictions.
3. **Fallbacks**: We maintain fallback font specifications in case the primary font fails to load.

## Fonts References in CSS

The `fonts.css` file contains the necessary `@font-face` declarations that map font weights to the appropriate files.

## Maintenance 

If you need to update the fonts in the future, simply:
1. Download the new font files
2. Replace the existing WOFF2 files in the `/landing-page/fonts/clash-display/` directory
3. Update the `fonts.css` if there are any changes to font weights or styles 
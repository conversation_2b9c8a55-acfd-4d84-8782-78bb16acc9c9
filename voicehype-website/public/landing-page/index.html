<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VoiceHype - Voice AI for Coding</title>
    <meta name="description"
        content="VoiceHype transforms your spoken ideas into perfect AI prompts for coding assistants. Code faster with your voice.">
    <!-- Preload self-hosted fonts -->
    <link rel="preload" href="/landing-page/fonts/clash-display/ClashDisplay-Regular.woff2" as="font" type="font/woff2"
        crossorigin>
    <link rel="preload" href="/landing-page/fonts/clash-display/ClashDisplay-Bold.woff2" as="font" type="font/woff2"
        crossorigin>
    <link rel="preload" href="/landing-page/fonts/clash-display/ClashDisplay-Semibold.woff2" as="font" type="font/woff2"
        crossorigin>
    <!-- Self-hosted fonts CSS -->
    <link rel="stylesheet" href="fonts.css">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="animations.css">
    <link rel="stylesheet" href="responsive.css">
    <link rel="stylesheet" href="responsive-fixes.css">
    <link rel="stylesheet" href="pricing-section.css">
    <link rel="stylesheet" href="faq-section.css">
    <link rel="stylesheet" href="how-to-use-section.css">
    <link rel="icon" type="image/png" href="../assets/favicon.png">
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="VoiceHype - Code Without Lifting a Finger">
    <meta property="og:description"
        content="VoiceHype transforms your spoken ideas into perfect AI prompts for coding assistants. Code faster with your voice.">
    <meta property="og:image" content="../assets/og-image.png">
    <meta property="og:url" content="https://voicehype.ai">
    <meta property="og:type" content="website">
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="VoiceHype - Code Without Lifting a Finger">
    <meta name="twitter:description"
        content="VoiceHype transforms your spoken ideas into perfect AI prompts for coding assistants. Code faster with your voice.">
    <meta name="twitter:image" content="../assets/og-image.png">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com">
    <link
        href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Manrope:wght@400;500;600;700;800&display=swap"
        rel="stylesheet">

    <!-- Critical inline CSS to prevent FOUC -->
    <style>

    /* Fade-in on scroll effect */
    .fade-in-section {
        opacity: 0;
        transform: translateY(30px);
        transition: opacity 0.8s ease, transform 0.8s ease;
    }

    .fade-in-section.visible {
        opacity: 1;
        transform: translateY(0);
    }
        body {
            background-color: #080814;
            color: #ffffff;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            margin: 0;
            padding: 0;
            opacity: 0;
            transition: opacity 0.5s ease-in;
        }

        body.content-loaded {
            opacity: 1;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #080814;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.5s ease-out, visibility 0.5s ease-out;
        }

        .loading-overlay.hidden {
            opacity: 0;
            visibility: hidden;
        }

        .loading-spinner {
            width: 48px;
            height: 48px;
            border: 3px solid rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            border-top-color: #14F195;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        /* FAQ Quick Fixes */
        .faq-item.active .question-icon svg {
            transform: rotate(45deg);
        }

        .question-icon svg {
            transition: transform 0.3s ease;
        }
    </style>

    <!-- Preload CSS -->
    <link rel="preload" href="styles.css" as="style">
    <link rel="preload" href="pricing-section.css" as="style">

    <!-- Preload critical fonts -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
        as="style" crossorigin>

    <!-- CSS and font imports -->
    <link
        href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600;700&display=swap"
        rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Outfit:wght@300;400;500;600;700;800;900&display=swap"
        rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap"
        rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@600;700;800&display=swap" rel="stylesheet">

    <!-- Typed.js library -->
    <script src="https://cdn.jsdelivr.net/npm/typed.js@2.0.16/dist/typed.umd.js"></script>

    <!-- Preload Typed.js initialization -->
    <script>
        // Ensure Typed.js is loaded
        document.addEventListener('DOMContentLoaded', function () {
            if (typeof Typed === 'undefined') {
                console.warn('Typed.js not loaded via CDN, attempting to load from npm');
                const script = document.createElement('script');
                script.src = '/node_modules/typed.js/dist/typed.umd.js';
                script.onload = function () {
                    console.log('Typed.js loaded from npm');
                    initTyped();
                };
                script.onerror = function () {
                    console.error('Failed to load Typed.js from npm');
                };
                document.head.appendChild(script);
            } else {
                // Initialize Typed.js if it's already loaded
                initTyped();
            }

            // Function to initialize Typed.js
            function initTyped() {
                // We're not using Typed.js for the subtitle anymore
                // Instead, we're using CSS animations for the fade/shimmer effect
            }
        });
    </script>
    <style>
        /* Enhanced Metrics Styles */
        .enhanced-metrics-container {
            display: flex;
            justify-content: space-between;
            margin: 3rem 0;
            position: relative;
            padding: 1rem;
            gap: 2rem;
        }

        .enhanced-metrics-container::before {
            content: '';
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            background: rgba(20, 241, 149, 0.03);
            border-radius: 16px;
            z-index: -1;
        }

        .enhanced-metric {
            flex: 1;
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(20, 241, 149, 0.1);
            border-radius: 12px;
            padding: 2rem 1rem;
            text-align: center;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            z-index: 1;
        }

        .enhanced-metric:hover {
            transform: translateY(-5px);
            background: rgba(20, 241, 149, 0.05);
            border-color: rgba(20, 241, 149, 0.3);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .metric-value {
            font-family: var(--font-display);
            font-size: 3.5rem;
            font-weight: 700;
            color: var(--color-primary);
            line-height: 1;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, var(--color-primary), #0AD6DF);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            position: relative;
        }

        .metric-label {
            font-family: var(--font-body);
            font-size: 1.1rem;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
            letter-spacing: 0.01em;
        }

        .metric-glow {
            position: absolute;
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(20, 241, 149, 0.2), transparent 70%);
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .enhanced-metric:hover .metric-glow {
            opacity: 1;
        }

        .speed-benefits-title {
            font-family: var(--font-display);
            font-size: 2.8rem;
            font-weight: 700;
            margin-bottom: 2rem;
            text-align: center;
            background: linear-gradient(90deg, #ffffff, var(--color-primary));
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            display: inline-block;
            width: 100%;
        }

        @media (max-width: 768px) {
            .enhanced-metrics-container {
                flex-direction: column;
            }

            .metric-value {
                font-size: 3rem;
            }
        }
    </style>
</head>

<body>
    <!-- Loading Overlay -->
    <div class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Scroll Progress Indicator -->
    <div class="scroll-progress"></div>

    <div class="page-wrapper">
        <!-- Navigation bar -->
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <div class="logo-container">
                        <svg width="40" height="30" viewBox="0 0 40 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <!-- Monitor outline with white fill -->
                            <rect x="2" y="2" width="36" height="20" rx="2" fill="white" />
                            <rect x="2" y="2" width="36" height="20" rx="2" stroke="#14F195" stroke-width="1.5" />
                            <!-- Sound wave visualization -->
                            <path d="M7 14 L10 9 L13 14 L16 9 L19 14 L22 9 L25 14 L28 9 L31 14 L34 9" stroke="#14F195"
                                stroke-width="1.5" stroke-linecap="round" />
                            <!-- Stand -->
                            <line x1="20" y1="22" x2="20" y2="26" stroke="#14F195" stroke-width="1.5" />
                            <line x1="14" y1="26" x2="26" y2="26" stroke="#14F195" stroke-width="1.5" />
                        </svg>
                    </div>
                    <h1 class="logo-text">Voice<span class="hype-text-green">Hype</span></h1>
                </div>
                <div class="nav-center">
                    <ul class="nav-links">
                        <li>
                            <a href="#features"
                                class="nav-link hover:text-primary py-2 font-semibold transition-all duration-300 ease-in-out rounded-lg">Features</a>
                        </li>
                        <li>
                            <a href="#use-cases"
                                class="nav-link hover:text-primary py-2 font-semibold transition-all duration-300 ease-in-out rounded-lg">Use
                                Cases</a>
                        </li>
                        <li>
                            <a href="#pricing-section"
                                class="nav-link hover:text-primary py-2 font-semibold transition-all duration-300 ease-in-out rounded-lg">Pricing</a>
                        </li>
                        <li>
                            <a href="#faq"
                                class="nav-link hover:text-primary py-2 font-semibold transition-all duration-300 ease-in-out rounded-lg">FAQ</a>
                        </li>
                    </ul>
                </div>
                <div class="nav-buttons">
                    <a href="#" class="nav-button login-button">Log In</a>
                    <a href="#" class="nav-button signup-button">Sign Up</a>
                </div>
                <button class="mobile-menu-toggle">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </button>
            </div>
        </nav>

        <!-- Top half with video background -->
        <div class="top-section">
            <img class="hero-video" src="../assets/VoiceHype_beauty.png" alt="Hero Image">
            <div class="video-overlay"></div>

            <div class="hero-content">
                <div class="title-container hover-area">
                    <h1 class="main-title">
                        <span class="title-line">Stop typing.</span>
                        <span class="title-line"><span class="highlight">Start talking.</span></span>
                    </h1>
                    <div class="subtitle-container"
                        style="font-family: var(--font-display); font-size: 1.9rem; font-weight: 600; color: var(--color-text); width: 100%; max-width: 90%; margin: 1.5rem auto 0; letter-spacing: 0.05em; word-spacing: 0.2em; text-align: center; position: relative; opacity: 0; animation: fadeIn 1.5s ease forwards 0.5s;">
                        <span id="typed-text"
                            style="position: relative; display: inline-block; line-height: 1.3; padding: 0.1em 0.2em; overflow: hidden; border-radius: 4px;">Prompt
                            Engineering with Your Voice</span>
                        <style>
                            /* Show video only on desktop */
                            @media screen and (min-width: 768px) {
                                .hero-video {
                                    display: hidden;
                                }

                                /* Offset hero content when video is visible */
                                .hero-content {
                                    margin-left: 20%;
                                }

                                @keyframes shimmer {
                                    0% {
                                        transform: translateX(-100%);
                                    }

                                    100% {
                                        transform: translateX(100%);
                                    }
                                }

                                @keyframes gradientShift {
                                    0% {
                                        background-position: 0% 50%;
                                    }

                                    50% {
                                        background-position: 100% 50%;
                                    }

                                    100% {
                                        background-position: 0% 50%;
                                    }
                                }

                                #typed-text::before {
                                    content: '';
                                    position: absolute;
                                    top: 0;
                                    left: 0;
                                    width: 100%;
                                    height: 100%;
                                    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);
                                    z-index: 1;
                                    animation: shimmer 3s infinite linear;
                                    transform: translateX(-100%);
                                    /* Constrain to text content */
                                    pointer-events: none;
                                }

                                #typed-text::after {
                                    content: '';
                                    position: absolute;
                                    bottom: -3px;
                                    left: 0;
                                    width: 100%;
                                    height: 2px;
                                    background: linear-gradient(90deg, transparent, var(--color-primary), var(--color-secondary), transparent);
                                    opacity: 0.8;
                                    animation: gradientShift 8s infinite linear;
                                    background-size: 200% 100%;
                                }

                                /* Responsive styles */
                                @media (max-width: 768px) {
                                    .subtitle-container {
                                        font-size: 1.5rem !important;
                                        max-width: 95% !important;
                                        letter-spacing: 0.03em !important;
                                        word-spacing: 0.1em !important;
                                    }

                                    #typed-text {
                                        padding: 0 5px !important;
                                    }

                                    #typed-text::after {
                                        bottom: -2px !important;
                                        height: 2px !important;
                                        opacity: 0.8 !important;
                                    }
                                }

                                @media (max-width: 480px) {
                                    .subtitle-container {
                                        font-size: 1.2rem !important;
                                        max-width: 100% !important;
                                        letter-spacing: 0.02em !important;
                                        word-spacing: 0.05em !important;
                                    }

                                    #typed-text {
                                        padding: 0 3px !important;
                                    }

                                    #typed-text::after {
                                        bottom: -2px !important;
                                        height: 1.5px !important;
                                        opacity: 0.9 !important;
                                    }
                                }
                        </style>
                    </div>
                </div>

                <div class="cta-button hover-area">
                    <button class="primary-button">Get Started</button>
                    <div class="pricing-cta-note">No credit card required</div>
                </div>
            </div>
        </div>

        <!-- Quote Section -->
        <div class="quote-section">
            <div class="quote-container">
                <blockquote class="main-quote">
                    "When you're typing, you're fighting with the LLM. Because you get tired writing and you don't give
                    it all it needs to do the perfect job. So you write more and it doesn't get it before you go crazy."
                </blockquote>
            </div>
        </div>

        <!-- Platform Logos Section -->
        <div class="platform-logos-section">
            <div class="logos-carousel-container">
                <div class="logos-carousel">
                    <div class="logo-item" data-platform="cursor">
                        <img src="/landing-page/cursor-dark-logo.avif" alt="Cursor">
                    </div>
                    <div class="logo-item" data-platform="vscode">
                        <img src="https://code.visualstudio.com/favicon.ico" alt="VS Code">
                    </div>
                    <div class="logo-item" data-platform="codium">
                        <img src="/landing-page/vscodium-logo.svg" alt="VSCodium">
                    </div>
                    <div class="logo-item" data-platform="windsurf">
                        <img src="/landing-page/navbar_windsurf_logo.svg" alt="Windsurf">
                    </div>
                    <!-- Removing GitHub Copilot, Continue, Warp and Windsurf logos as they are not supported -->
                </div>
            </div>
        </div>

        <!-- Voice Hype Intro Section -->
        <div class="intro-section">
            <div class="intro-container">
                <div class="intro-header-row">
                    <div class="intro-header">
                        <div class="intro-title-container">
                            <h2 class="intro-title logo-style"><span class="meet-text">Meet</span> <span
                                    class="">Voice<span class="hype-text hype-text-green">Hype</span></span></h2>
                        </div>
                        <p class="intro-tagline">
                            VoiceHype transforms coding through <strong>AI-powered</strong> voice recognition, boosting
                            <strong>developer productivity</strong> by converting natural speech into <strong>perfect
                                code prompts</strong>.
                        </p>
                    </div>
                    <!-- Video section commented out as requested -->
                    <!--
                    <div class="intro-video">
                        <div class="video-wrapper">
                            <iframe width="300" height="125" src="https://www.youtube.com/embed/dQw4w9WgXcQ" title="Voice Hype Demo" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
                        </div>
                    </div>
                    -->
                </div>
            </div>
        </div>

        <!-- Speed Benefits Section -->
        <div class="speed-benefits-section">
            <div class="speed-benefits-container">
                <div class="speed-benefits-content">
                    <h2 class="speed-benefits-title">Boost Your Coding Speed</h2>
                    <div class="enhanced-metrics-container">
                        <div class="enhanced-metric">
                            <div class="metric-value">Faster</div>
                            <div class="metric-label">Prompt Creation</div>
                            <div class="metric-glow"></div>
                        </div>
                        <div class="" style="height: 1rem;"></div>
                        <div class="enhanced-metric">
                            <div class="metric-value">More</div>
                            <div class="metric-label">Code Generated</div>
                            <div class="metric-glow"></div>
                        </div>
                        <div class="" style="height: 1rem;"></div>
                        <div class="enhanced-metric">
                            <div class="metric-value">Time Saved</div>
                            <div class="metric-label">Daily</div>
                            <div class="metric-glow"></div>
                        </div>
                    </div>
                    <div class="" style="height: 1rem;"></div>
                    <p class="speed-benefits-description">
                        <span class="highlight">Stop wasting hours crafting the perfect prompts.</span> VoiceHype
                        eliminates the bottleneck between your ideas and execution. While others meticulously type out
                        detailed prompts, you'll be speaking naturally and watching as your code materializes in
                        seconds.
                    </p>
                    <div class="speed-benefits-cta">
                        <button class="speed-benefits-button">Try It Now</button>
                    </div>
                </div>
                <div class="speed-benefits-visualization">
                    <div class="comparison-chart">
                        <div class="comparison-item typing">
                            <div class="comparison-label">Typing Prompts <span class="time-indicator">⏱️</span></div>
                            <div class="comparison-bar typing-bar">
                                <span class="typing-indicator">
                                    <span class="typing-dot"></span>
                                    <span class="typing-dot"></span>
                                    <span class="typing-dot"></span>
                                </span>
                            </div>
                            <div class="comparison-time">8-12 min</div>
                        </div>
                        <div class="comparison-item voice">
                            <div class="comparison-label">VoiceHype <span class="speed-badge">FAST</span></div>
                            <div class="comparison-bar voice-bar">
                                <span class="voice-pulse"></span>
                            </div>
                            <div class="comparison-time">4-5 min</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Key Features Section -->
        <div id="features" class="features-overview-section">
            <div class="features-overview-container">
                <h3 class="features-overview-title">How <span style="color: var(--color-primary);">VoiceHype</span>
                    Works</h3>

                <div class="voice-transformation-animation">
                    <!-- Voice Recording Component -->
                    <div class="animation-component recording-component">
                        <div class="component-title">Voice Input</div>
                        <div class="recording-visualization">
                            <div class="microphone-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                    stroke-linejoin="round">
                                    <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3z" />
                                    <path d="M19 10v2a7 7 0 0 1-14 0v-2" />
                                    <line x1="12" y1="19" x2="12" y2="22" />
                                    <polyline points="8 22 12 22 16 22" />
                                </svg>
                            </div>
                            <div class="voice-wave">
                                <span class="wave-bar"></span>
                                <span class="wave-bar"></span>
                                <span class="wave-bar"></span>
                                <span class="wave-bar"></span>
                                <span class="wave-bar"></span>
                                <span class="wave-bar"></span>
                                <span class="wave-bar"></span>
                                <span class="wave-bar"></span>
                                <span class="wave-bar"></span>
                                <span class="wave-bar"></span>
                            </div>
                            <div class="scrollable-text-container">
                                "Mashallah. Mashallah. You've done some really, really great work right there. I just
                                have like two questions from you. I'm really interested, first of all in how you
                                actually calculated this balance. I mean, like, I'm really interested in the mathematics
                                that you did behind that. Subhanallah. I really want to know how you did that. Great job
                                right there. Mashallah, by the way. And the second thing is, like, you've mentioned
                                like, that you've calculated that on a 70% profit margin for the basic tier, we should
                                like 324 minutes plus 210,000 tokens. Right. But in this I want to ask you, like, why
                                did you like, select, like, what does 70% profit margin mean? Because the prices that
                                are there in the CSV table already include like an 18%. 18% profit margin. So I want to
                                see like in different profit margins, what the different number of minutes and tokens
                                would be like. You can like, first of all create it on the same original price and then
                                you can maybe use 10% and then what? How it would look on 20 and then 30 and then 40 and
                                then 50 and 60 and 70 up all the way to 100. And if I'm not wrong, if I'm not thinking
                                wrong, if we set like 100% profit margin, wouldn't that mean zero minutes and zero
                                tokens? I don't know. I might be confused, but I need your clarification on this.
                                Jazakallah, please let me know. Answer the questions that I've asked. Jazakallah, bro,
                                very good work, Mashallah."
                            </div>
                        </div>
                    </div>

                    <!-- Transcription Component -->
                    <div class="animation-component transcription-component">
                        <div class="component-title">VoiceHype Processing</div>
                        <div class="transcription-visualization">
                            <div class="processing-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                    stroke-linejoin="round">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <path d="M12 6v6l4 2"></path>
                                </svg>
                            </div>
                            <div class="transcription-text">
                                <div class="transcribing-animation">
                                    <span class="text-line"></span>
                                    <span class="text-line"></span>
                                    <span class="text-line"></span>
                                </div>
                                <div class="ai-dots">
                                    <span class="ai-dot"></span>
                                    <span class="ai-dot"></span>
                                    <span class="ai-dot"></span>
                                </div>
                            </div>
                            <div class="transformation-arrows">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round">
                                    <line x1="5" y1="12" x2="19" y2="12"></line>
                                    <polyline points="12 5 19 12 12 19"></polyline>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <!-- Optimized Output Component -->
                    <div class="animation-component optimized-component">
                        <div class="component-title">Magic</div>
                        <div class="optimized-visualization">
                            <div class="mode-badge">Polish Mode</div>
                            <div class="scrollable-text-container">
                                "I would like to commend you on your exemplary work. I have two inquiries regarding your
                                calculations. Firstly, I am particularly interested in understanding the mathematical
                                methodology you employed to calculate this balance. Your approach was impressive, and I
                                would appreciate insight into your computational process.

                                Secondly, you mentioned calculating 324 minutes plus 210,000 tokens based on a 70%
                                profit margin for the basic tier. I seek clarification on what this 70% profit margin
                                signifies, especially considering that the prices in the CSV table already incorporate
                                an 18% profit margin.

                                Could you provide a comparative analysis showing how the number of minutes and tokens
                                would vary across different profit margins? Perhaps you could start with the original
                                price, then demonstrate incremental changes at 10%, 20%, 30%, 40%, 50%, 60%, 70%, and up
                                to 100% profit margins. I am also curious whether a 100% profit margin would effectively
                                result in zero minutes and zero tokens, though I may be misunderstanding the concept.

                                I would appreciate your clarification on these matters. Thank you again for your
                                excellent work."
                            </div>
                            <div class="mode-description">
                                <span class="mode-icon">✨</span>
                                <span class="mode-text">Polish mode professionally enhances your input by incorporating
                                    structured formatting, technical specificity, and coherence. Try it out to explore
                                    more amazing modes!</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <style>
            /* Voice Transformation Animation Styles */
            .voice-transformation-animation {
                display: flex;
                justify-content: space-between;
                margin: 3rem 0;
                gap: 1.5rem;
                position: relative;
            }

            .animation-component {
                flex: 1;
                background: rgba(15, 15, 25, 0.6);
                border: 1px solid rgba(20, 241, 149, 0.15);
                border-radius: 12px;
                padding: 1.5rem;
                display: flex;
                flex-direction: column;
                position: relative;
                overflow: hidden;
                min-height: 350px;
                transition: all 0.3s ease;
            }

            .animation-component:hover {
                transform: translateY(-5px);
                box-shadow: 0 10px 30px rgba(20, 241, 149, 0.1);
                border-color: rgba(20, 241, 149, 0.3);
            }

            .component-title {
                font-family: var(--font-display);
                font-size: 1.5rem;
                font-weight: 600;
                margin-bottom: 1.5rem;
                text-align: center;
                color: var(--color-primary);
                background: linear-gradient(90deg, #14F195 0%, #0AD6DF 100%);
                -webkit-background-clip: text;
                background-clip: text;
                -webkit-text-fill-color: transparent;
            }

            /* Recording Component Styles */
            .recording-visualization {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                height: 100%;
                position: relative;
            }

            .microphone-icon {
                width: 50px;
                height: 50px;
                margin-bottom: 1.5rem;
                color: var(--color-primary);
                animation: pulse 2s infinite;
            }

            .voice-wave {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 80px;
                width: 100%;
                margin-bottom: 1.5rem;
            }

            .wave-bar {
                width: 4px;
                height: 20px;
                margin: 0 2px;
                background-color: var(--color-primary);
                border-radius: 2px;
                animation: waveAnimation 1.2s ease-in-out infinite;
            }

            .wave-bar:nth-child(1) {
                animation-delay: 0.0s;
            }

            .wave-bar:nth-child(2) {
                animation-delay: 0.1s;
            }

            .wave-bar:nth-child(3) {
                animation-delay: 0.2s;
            }

            .wave-bar:nth-child(4) {
                animation-delay: 0.3s;
            }

            .wave-bar:nth-child(5) {
                animation-delay: 0.4s;
            }

            .wave-bar:nth-child(6) {
                animation-delay: 0.5s;
            }

            .wave-bar:nth-child(7) {
                animation-delay: 0.4s;
            }

            .wave-bar:nth-child(8) {
                animation-delay: 0.3s;
            }

            .wave-bar:nth-child(9) {
                animation-delay: 0.2s;
            }

            .wave-bar:nth-child(10) {
                animation-delay: 0.1s;
            }

            .voice-sample {
                font-family: var(--font-body);
                font-size: 0.9rem;
                color: rgba(255, 255, 255, 0.85);
                text-align: center;
                line-height: 1.5;
                padding: 1rem;
                background: rgba(20, 241, 149, 0.05);
                border-radius: 8px;
                position: relative;
                max-width: 90%;
            }

            /* Transcription Component Styles */
            .transcription-visualization {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                height: 100%;
            }

            .processing-icon {
                width: 50px;
                height: 50px;
                margin-bottom: 1.5rem;
                color: var(--color-primary);
                animation: rotate 4s linear infinite;
            }

            .transcription-text {
                width: 90%;
                height: 160px;
                background: rgba(10, 10, 20, 0.3);
                border: 1px solid rgba(20, 241, 149, 0.1);
                border-radius: 8px;
                padding: 1rem;
                position: relative;
                overflow: hidden;
            }

            .transcribing-animation {
                display: flex;
                flex-direction: column;
                gap: 12px;
            }

            .text-line {
                height: 10px;
                background: linear-gradient(90deg, rgba(20, 241, 149, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%);
                border-radius: 4px;
                animation: textLine 2s infinite;
            }

            .text-line:nth-child(1) {
                width: 90%;
                animation-delay: 0s;
            }

            .text-line:nth-child(2) {
                width: 70%;
                animation-delay: 0.3s;
            }

            .text-line:nth-child(3) {
                width: 85%;
                animation-delay: 0.6s;
            }

            .ai-dots {
                display: flex;
                justify-content: center;
                margin-top: 2rem;
                gap: 8px;
            }

            .ai-dot {
                width: 10px;
                height: 10px;
                background-color: var(--color-primary);
                border-radius: 50%;
                opacity: 0.5;
                animation: dotPulse 1.5s infinite;
            }

            .ai-dot:nth-child(1) {
                animation-delay: 0s;
            }

            .ai-dot:nth-child(2) {
                animation-delay: 0.3s;
            }

            .ai-dot:nth-child(3) {
                animation-delay: 0.6s;
            }

            .transformation-arrows {
                width: 30px;
                height: 30px;
                margin-top: 1.5rem;
                color: var(--color-primary);
                animation: arrowPulse 2s infinite;
            }

            /* Optimized Output Component Styles */
            .optimized-visualization {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: space-between;
                height: 100%;
                position: relative;
            }

            .mode-badge {
                background: linear-gradient(90deg, #14F195 0%, #0AD6DF 100%);
                color: #080814;
                font-weight: 600;
                padding: 0.5rem 1rem;
                border-radius: 20px;
                margin-bottom: 1rem;
                font-size: 0.9rem;
            }

            .optimized-output {
                width: 90%;
                background: rgba(10, 10, 20, 0.3);
                border: 1px solid rgba(20, 241, 149, 0.2);
                border-radius: 8px;
                padding: 1rem;
                margin-bottom: 1.5rem;
                position: relative;
                overflow: hidden;
            }

            .output-text {
                font-family: var(--font-body);
                font-size: 0.9rem;
                color: rgba(255, 255, 255, 0.9);
                line-height: 1.5;
            }

            .beam-light {
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(20, 241, 149, 0.2), transparent);
                animation: beamLight 3s infinite;
            }

            .mode-description {
                display: flex;
                align-items: center;
                gap: 10px;
                background: rgba(20, 241, 149, 0.05);
                border-radius: 8px;
                padding: 0.8rem;
                max-width: 90%;
            }

            .mode-icon {
                font-size: 1.2rem;
            }

            .mode-text {
                font-size: 0.8rem;
                color: rgba(255, 255, 255, 0.8);
                line-height: 1.4;
            }

            /* Animations */
            @keyframes pulse {

                0%,
                100% {
                    transform: scale(1);
                    opacity: 0.8;
                }

                50% {
                    transform: scale(1.1);
                    opacity: 1;
                }
            }

            @keyframes waveAnimation {

                0%,
                100% {
                    height: 10px;
                }

                50% {
                    height: 40px;
                }
            }

            @keyframes rotate {
                0% {
                    transform: rotate(0deg);
                }

                100% {
                    transform: rotate(360deg);
                }
            }

            @keyframes textLine {

                0%,
                100% {
                    opacity: 0.3;
                }

                50% {
                    opacity: 0.8;
                }
            }

            @keyframes dotPulse {

                0%,
                100% {
                    transform: scale(1);
                    opacity: 0.5;
                }

                50% {
                    transform: scale(1.3);
                    opacity: 1;
                }
            }

            @keyframes arrowPulse {

                0%,
                100% {
                    transform: translateX(0);
                    opacity: 0.7;
                }

                50% {
                    transform: translateX(5px);
                    opacity: 1;
                }
            }

            @keyframes beamLight {
                0% {
                    left: -100%;
                }

                100% {
                    left: 100%;
                }
            }

            /* Read More Functionality */
            .read-more-container {
                margin-top: 10px;
                text-align: center;
            }

            .scrollable-text-container {
                max-height: 150px;
                overflow-y: auto;
                position: relative;
                padding-bottom: 15px;
                margin-top: 10px;
                margin-bottom: 20px;
                padding: 10px;
                background: rgba(20, 241, 149, 0.05);
                border: 1px solid rgba(20, 241, 149, 0.1);
                border-radius: 8px;
                text-align: left;
                white-space: pre-line;
                font-size: 0.85rem;
                color: rgba(255, 255, 255, 0.9);
                line-height: 1.5;
                scrollbar-width: thin;
                scrollbar-color: rgba(20, 241, 149, 0.5) rgba(20, 241, 149, 0.1);
            }

            .scrollable-text-container::-webkit-scrollbar {
                width: 6px;
            }

            .scrollable-text-container::-webkit-scrollbar-track {
                background: rgba(20, 241, 149, 0.1);
                border-radius: 10px;
            }

            .scrollable-text-container::-webkit-scrollbar-thumb {
                background-color: rgba(20, 241, 149, 0.5);
                border-radius: 10px;
            }

            .gradient-fade {
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                height: 30px;
                background: linear-gradient(to bottom, transparent, rgba(10, 10, 20, 0.9) 90%);
                pointer-events: none;
                z-index: 2;
            }

            /* Animations */
            @keyframes pulse {

                0%,
                100% {
                    transform: scale(1);
                    opacity: 0.8;
                }

                50% {
                    transform: scale(1.1);
                    opacity: 1;
                }
            }

            @media (max-width: 992px) {
                .voice-transformation-animation {
                    flex-direction: column;
                    gap: 2rem;
                }

                .animation-component {
                    min-height: 300px;
                }
            }

            @media (max-width: 768px) {
                .component-title {
                    font-size: 1.3rem;
                }

                .voice-sample,
                .output-text {
                    font-size: 0.85rem;
                }

                .mode-description {
                    max-width: 95%;
                }

                .mode-text {
                    font-size: 0.75rem;
                }
            }

            @media (max-width: 480px) {
                .component-title {
                    font-size: 1.2rem;
                }

                .voice-wave {
                    height: 60px;
                }

                .wave-bar {
                    width: 3px;
                    margin: 0 1px;
                }

                .voice-sample,
                .output-text {
                    font-size: 0.8rem;
                    padding: 0.8rem;
                }
            }
        </style>
    </div>

            <style>
        /* Basic styling for the section - Dark Mode */
        .how-to-use-section {
            padding: 60px 20px;
            background-color: #000000;
            color: #ffffff;
        }

        .how-to-use-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .how-to-use-header {
            text-align: center;
            margin-bottom: 50px;
        }

        .how-to-use-title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 16px;
            color: #ffffff;
        }

        .hype-text-green {
            color: #00ff88;
        }

        .how-to-use-subtitle {
            font-size: 1.1rem;
            color: #cccccc;
            margin: 0;
        }

        /* Main content layout */
        .how-to-use-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: start;
        }

        .how-to-use-steps {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .how-to-use-step {
            display: flex;
            align-items: flex-start;
            gap: 20px;
        }

        .step-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2rem;
            color: white;
            flex-shrink: 0;
        }

        .step-1 { background-color: #00ff88; color: #000000; }
        .step-2 { background-color: #00ff88; color: #000000; }
        .step-3 { background-color: #00ff88; color: #000000; }
        .step-4 { background-color: #00ff88; color: #000000; }

        .step-content {
            flex: 1;
        }

        .step-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 8px;
            color: #ffffff;
        }

        .step-desc {
            font-size: 1rem;
            color: #cccccc;
            line-height: 1.6;
        }

        .how-to-link {
            color: #00ff88;
            text-decoration: none;
        }

        .how-to-link:hover {
            text-decoration: underline;
            color: #00cc6a;
        }

        .kbd {
            background-color: #1a1a1a;
            border: 1px solid #333333;
            padding: 4px 8px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.9rem;
            color: #00ff88;
        }

        /* Video container styling */
        .how-to-use-video {
            display: flex;
            justify-content: center;
            align-items: flex-start;
        }

        .video-wrapper {
            position: relative;
            width: 100%;
            max-width: 280px;
            height: 200px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 255, 136, 0.2);
            border: 2px solid #00ff88;
        }

        .video-wrapper iframe {
            width: 100%;
            height: 100%;
            border: none;
        }

        /* Responsive design for mobile */
        @media (max-width: 768px) {
            .how-to-use-content {
                grid-template-columns: 1fr;
                gap: 40px;
            }

            /* Move video above steps on mobile */
            .how-to-use-video {
                order: -1;
            }

            .how-to-use-steps {
                order: 1;
            }

            .how-to-use-title {
                font-size: 2rem;
            }

            .video-wrapper {
                height: 250px;
            }

            .step-icon {
                width: 40px;
                height: 40px;
                font-size: 1rem;
            }

            .step-title {
                font-size: 1.2rem;
            }

            .step-desc {
                font-size: 0.95rem;
            }
        }

        @media (max-width: 480px) {
            .how-to-use-section {
                padding: 40px 15px;
            }

            .how-to-use-title {
                font-size: 1.8rem;
            }

            .video-wrapper {
                height: 200px;
            }
        }

        /* Animation for scroll appearance */
        .appear-on-scroll {
            opacity: 1;
            transform: translateY(0);
            transition: opacity 0.6s ease, transform 0.6s ease;
        }
    </style>

       <!-- Use Cases Section -->
    <div id="how-to-use" class="how-to-use-section appear-on-scroll">
        <div class="how-to-use-container">
            <div class="how-to-use-header">
                <h2 class="how-to-use-title">How to Use <span class="hype-text-green">VoiceHype</span></h2>
                <p class="how-to-use-subtitle">Get started in just a few simple steps, inshaAllah!</p>
            </div>
            <div class="how-to-use-content">
                <div class="how-to-use-steps">
                    <div class="how-to-use-step">
                        <div class="step-icon step-1">1</div>
                        <div class="step-content">
                            <div class="step-title">Install the Extension</div>
                            <div class="step-desc">Download <b>VoiceHype</b> from the <a href="#" class="how-to-link">VS Code Marketplace</a>.</div>
                        </div>
                    </div>
                    <div class="how-to-use-step">
                        <div class="step-icon step-2">2</div>
                        <div class="step-content">
                            <div class="step-title">Sign In with Browser</div>
                            <div class="step-desc">Click "Sign in with Browser" in VS Code and authenticate seamlessly through your web browser.</div>
                        </div>
                    </div>
                    <div class="how-to-use-step">
                        <div class="step-icon step-3">3</div>
                        <div class="step-content">
                            <div class="step-title">Choose Your Plan</div>
                            <div class="step-desc">Select a subscription plan or add prepaid credits for usage-based billing.</div>
                        </div>
                    </div>
                    <div class="how-to-use-step">
                        <div class="step-icon step-4">4</div>
                        <div class="step-content">
                            <div class="step-title">Start Voice Coding</div>
                            <div class="step-desc">Use <span class="kbd">Ctrl+Shift+8</span> and <span class="kbd">Ctrl+Shift+9</span> to activate VoiceHype, bi'idhnillah!</div>
                        </div>
                    </div>
                </div>
                <div class="how-to-use-video">
                    <div class="video-wrapper">
                        <iframe width="300" height="315" src="https://www.youtube.com/embed/G9Artw_Kgjg?si=ZwW2ONBmiHpsZS-I" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                    </div>
                </div>
            </div>
        </div>
    </div>
                    <div style = "height: 80px"></div>

        <!-- New Humorous Section About Eating While Coding -->
        <div class="eating-coding-section enhanced-food-section">
            <div class="food-backdrop"></div>

            <!-- Floating Food Icons -->
            <div class="floating-food">
                <div class="food-item pizza" data-tooltip="No more greasy keyboards!">🍕</div>
                <div class="food-item burger" data-tooltip="Speak your code, enjoy your food">🍔</div>
                <div class="food-item fries" data-tooltip="Code with your mouth full">🍟</div>
                <div class="food-item taco" data-tooltip="Taco Tuesday + Coding">🌮</div>
                <div class="food-item donut" data-tooltip="Sweet code solutions">🍩</div>
                <div class="food-item coffee" data-tooltip="Caffeine + Voice = Productivity">☕</div>
            </div>

            <div class="eating-coding-container">
                <div class="section-glow-effect"></div>

                <div class="eating-coding-header">
                    <h2 class="eating-coding-title">Keep Coding, <span class="gradient-text animated-gradient">Keep
                            Eating</span></h2>
                    <div class="title-underline"></div>
                </div>

                <div class="eating-coding-content">
                    <div class="eating-coding-image">
                        <div class="device-mockup">
                            <div class="device-screen">
                                <div class="code-snippet">
                                    <pre><code>function <span class="code-highlight">eatAndCode</span>() {
  const food = <span class="code-string">"delicious"</span>;
  const hands = <span class="code-string">"busy"</span>;
  const solution = <span class="code-highlight">VoiceHype</span>;

  <span class="code-comment">// No need to type with greasy fingers</span>
  <span class="code-keyword">return</span> solution.activate();
}</code></pre>
                                </div>
                            </div>
                            <div class="device-notch"></div>
                            <div class="device-button"></div>
                        </div>

                        <div class="food-icon-circle animated">
                            <svg xmlns="http://www.w3.org/2000/svg" height="32px" viewBox="0 0 24 24" width="32px"
                                class="svg-glow">
                                <path d="M0 0h24v24H0V0z" fill="none" />
                                <path
                                    d="M16 6v8h3v8h2V2c-2.76 0-5 2.24-5 4zm-5 3H9V2H7v7H5V2H3v7c0 2.21 1.79 4 4 4v9h2v-9c2.21 0-4-1.79-4-4z" />
                            </svg>
                        </div>
                        <div class="mic-icon-circle animated pulse">
                            <svg xmlns="http://www.w3.org/2000/svg" height="32px" viewBox="0 0 24 24" width="32px"
                                class="svg-glow">
                                <path d="M0 0h24v24H0V0z" fill="none" />
                                <path
                                    d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm-1-9c0-.55.45-1 1-1s1 .45 1 1v6c0 .55-.45 1-1 1s-1-.45-1-1V5zm6 6c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z" />
                            </svg>
                        </div>
                        <div class="keyboard-icon-circle animated">
                            <svg xmlns="http://www.w3.org/2000/svg" height="32px" viewBox="0 0 24 24" width="32px"
                                class="svg-glow">
                                <path d="M0 0h24v24H0V0zm0 0h24v24H0V0z" fill="none" />
                                <path
                                    d="M20 7v10H4V7h16m0-2H4c-1.1 0-1.99.9-1.99 2L2 17c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm-9 3h2v2h-2zm0 3h2v2h-2zM8 8h2v2H8zm0 3h2v2H8zm-3 0h2v2H5zm0-3h2v2H5zm3 6h8v2H8zm6-3h2v2h-2zm0-3h2v2h-2zm3 3h2v2h-2zm0-3h2v2h-2z" />
                            </svg>
                        </div>
                    </div>

                    <div class="eating-coding-text">
                        <h3 class="feature-heading">Don't Let Your Keyboard <span class="strike-through">Get
                                Greasy</span>
                            <span class="highlight-text">Slow You Down</span> Again
                        </h3>
                        <p class="feature-description">Enjoy your meals while staying productive with VoiceHype. No more
                            choosing between eating and coding!</p>

                       
                    </div>
                </div>
            </div>
        </div>

        <style>
            /* Enhanced Food Section Styles */
            .enhanced-food-section {
                position: relative;
                background: linear-gradient(135deg, #080814 0%, #10101e 100%);
                padding: 6rem 0;
                overflow: hidden;
                color: white;
            }

            .food-backdrop {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: radial-gradient(circle at 70% 30%, rgba(20, 241, 149, 0.1), transparent 50%),
                    radial-gradient(circle at 30% 70%, rgba(10, 214, 223, 0.1), transparent 50%);
                z-index: 0;
                opacity: 0.8;
            }

            .section-glow-effect {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 100%;
                height: 100%;
                background: radial-gradient(circle at center, rgba(20, 241, 149, 0.15), transparent 60%);
                z-index: 0;
                filter: blur(50px);
                opacity: 0.8;
                pointer-events: none;
            }

            .floating-food {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 1;
                overflow: hidden;
            }

            .food-item {
                position: absolute;
                font-size: 2.5rem;
                filter: drop-shadow(0 0 15px rgba(20, 241, 149, 0.3));
                animation: floatFood 20s infinite linear;
                opacity: 0.7;
                cursor: pointer;
                transition: all 0.3s ease;
            }

            .food-item::after {
                content: attr(data-tooltip);
                position: absolute;
                bottom: 100%;
                left: 50%;
                transform: translateX(-50%) translateY(10px);
                background: rgba(20, 241, 149, 0.2);
                backdrop-filter: blur(8px);
                color: white;
                font-size: 0.8rem;
                padding: 0.5rem 1rem;
                border-radius: 8px;
                white-space: nowrap;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
                z-index: 10;
                pointer-events: none;
            }

            .food-item:hover {
                transform: scale(1.2);
                opacity: 1;
                filter: drop-shadow(0 0 20px rgba(20, 241, 149, 0.5));
            }

            .food-item:hover::after {
                opacity: 1;
                visibility: visible;
                transform: translateX(-50%) translateY(-5px);
            }

            .pizza {
                top: 15%;
                left: 10%;
                animation-duration: 25s;
                animation-delay: 0s;
            }

            .burger {
                top: 20%;
                right: 15%;
                animation-duration: 28s;
                animation-delay: 2s;
            }

            .fries {
                bottom: 20%;
                left: 20%;
                animation-duration: 22s;
                animation-delay: 5s;
            }

            .taco {
                bottom: 25%;
                right: 25%;
                animation-duration: 26s;
                animation-delay: 3s;
            }

            .donut {
                top: 40%;
                left: 5%;
                animation-duration: 24s;
                animation-delay: 7s;
            }

            .coffee {
                top: 35%;
                right: 8%;
                animation-duration: 20s;
                animation-delay: 1s;
            }

            @keyframes floatFood {
                0% {
                    transform: translate(0, 0) rotate(0deg);
                }

                33% {
                    transform: translate(50px, -30px) rotate(10deg);
                }

                66% {
                    transform: translate(-20px, 40px) rotate(-10deg);
                }

                100% {
                    transform: translate(0, 0) rotate(0deg);
                }
            }

            .eating-coding-header {
                position: relative;
                margin-bottom: 3rem;
                text-align: center;
                z-index: 2;
            }

            .eating-coding-title {
                font-family: var(--font-display);
                font-size: 3.5rem;
                font-weight: 700;
                margin-bottom: 1rem;
                position: relative;
                display: inline-block;
            }

            .animated-gradient {
                background-size: 200% 200%;
                background-image: linear-gradient(90deg, #14F195, #0AD6DF, #6366F1, #14F195);
                animation: gradientShift 10s ease infinite;
            }

            @keyframes gradientShift {
                0% {
                    background-position: 0% 50%;
                }

                50% {
                    background-position: 100% 50%;
                }

                100% {
                    background-position: 0% 50%;
                }
            }

            .title-underline {
                width: 120px;
                height: 3px;
                background: linear-gradient(90deg, transparent, var(--color-primary), transparent);
                margin: 0 auto;
                position: relative;
            }

            .title-underline::before {
                left: 0;
            }

            .title-underline::after {
                right: 0;
                animation-delay: 1.5s;
            }

            @keyframes lineMove {

                0%,
                100% {
                    transform: translateX(0);
                    opacity: 0.6;
                }

                50% {
                    transform: translateX(50px);
                    opacity: 1;
                }
            }

            .eating-coding-container {
                position: relative;
                max-width: 1200px;
                margin: 0 auto;
                padding: 0 2rem;
                z-index: 2;
            }

            .eating-coding-content {
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: space-between;
                gap: 4rem;
                position: relative;
            }

            .eating-coding-image {
                flex: 1;
                position: relative;
                min-height: 350px;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .device-mockup {
                width: 300px;
                height: 220px;
                background: #1c1c34;
                border-radius: 16px;
                position: relative;
                box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
                overflow: hidden;
                border: 2px solid rgba(20, 241, 149, 0.2);
                transform: perspective(800px) rotateX(10deg) rotateY(-10deg);
                transition: transform 0.5s ease;
            }

            .device-mockup:hover {
                transform: perspective(800px) rotateX(5deg) rotateY(-5deg) translateY(-10px);
            }

            .device-mockup::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(135deg, rgba(20, 241, 149, 0.1) 0%, transparent 100%);
                z-index: 1;
                pointer-events: none;
            }

            .device-mockup::after {
                content: '';
                position: absolute;
                top: -50%;
                left: -50%;
                width: 200%;
                height: 200%;
                background: linear-gradient(60deg, transparent, rgba(20, 241, 149, 0.1), transparent);
                transform: rotate(45deg);
                animation: shimmer 4s infinite linear;
                z-index: 1;
                pointer-events: none;
            }

            @keyframes shimmer {
                0% {
                    transform: translateX(-100%) rotate(45deg);
                }

                100% {
                    transform: translateX(100%) rotate(45deg);
                }
            }

            .device-screen {
                position: absolute;
                top: 20px;
                left: 20px;
                right: 20px;
                bottom: 20px;
                background: #0d1117;
                border-radius: 8px;
                overflow: hidden;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .code-snippet {
                width: 100%;
                padding: 15px;
                font-family: var(--font-mono);
                font-size: 0.75rem;
                line-height: 1.4;
                color: #e6edf3;
                overflow: hidden;
            }

            .code-highlight {
                color: #14F195;
                font-weight: 600;
            }

            .code-string {
                color: #e2b86b;
            }

            .code-comment {
                color: #8b949e;
            }

            .code-keyword {
                color: #ff7b72;
            }

            .device-notch {
                position: absolute;
                top: 8px;
                left: 50%;
                transform: translateX(-50%);
                width: 80px;
                height: 4px;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 4px;
            }

            .device-button {
                position: absolute;
                bottom: 10px;
                left: 50%;
                transform: translateX(-50%);
                width: 30px;
                height: 30px;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
            }

            .food-icon-circle,
            .mic-icon-circle,
            .keyboard-icon-circle {
                position: absolute;
                width: 60px;
                height: 60px;
                background: rgba(20, 241, 149, 0.1);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 0 20px rgba(20, 241, 149, 0.3);
                z-index: 2;
                border: 1px solid rgba(20, 241, 149, 0.3);
            }

            .food-icon-circle {
                top: 20%;
                left: 10%;
                animation: float 6s ease-in-out infinite;
            }

            .mic-icon-circle {
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 80px;
                height: 80px;
                background: rgba(20, 241, 149, 0.15);
                z-index: 3;
            }

            .keyboard-icon-circle {
                bottom: 20%;
                right: 10%;
                animation: float 6s ease-in-out infinite;
                animation-delay: 3s;
            }

            .animated {
                transition: all 0.3s ease;
            }

            .animated:hover {
                transform: scale(1.2);
                box-shadow: 0 0 30px rgba(20, 241, 149, 0.5);
            }

            .pulse {
                animation: pulse 2s infinite ease-in-out;
            }

            @keyframes pulse {

                0%,
                100% {
                    transform: scale(1);
                    box-shadow: 0 0 0 0 rgba(20, 241, 149, 0.7);
                }

                50% {
                    transform: scale(1.1);
                    box-shadow: 0 0 0 10px rgba(20, 241, 149, 0);
                }
            }

            @keyframes float {

                0%,
                100% {
                    transform: translateY(0);
                }

                50% {
                    transform: translateY(-15px);
                }
            }

            .svg-glow {
                filter: drop-shadow(0 0 3px rgba(20, 241, 149, 0.7));
                fill: var(--color-primary);
            }

            .eating-coding-text {
                flex: 1;
                position: relative;
                z-index: 2;
            }

            .feature-heading {
                font-family: var(--font-display);
                font-size: 2.2rem;
                font-weight: 700;
                margin-bottom: 1.5rem;
                line-height: 1.3;
                background: linear-gradient(90deg, #ffffff, #e6e6e6);
                -webkit-background-clip: text;
                background-clip: text;
                -webkit-text-fill-color: transparent;
            }

            .strike-through {
                position: relative;
                display: inline-block;
            }

            .strike-through::after {
                content: '';
                position: absolute;
                width: 100%;
                height: 3px;
                background-color: rgba(255, 82, 82, 0.8);
                top: 50%;
                left: 0;
                transform: rotate(-5deg);
            }

            .highlight-text {
                color: var(--color-primary);
                -webkit-text-fill-color: var(--color-primary);
                font-weight: 800;
                position: relative;
            }

            .feature-description {
                font-size: 1.2rem;
                line-height: 1.6;
                color: rgba(255, 255, 255, 0.9);
                margin-bottom: 2rem;
            }

            .benefits-list {
                display: flex;
                flex-direction: column;
                gap: 1rem;
                margin-bottom: 2rem;
            }

            .benefit-item {
                display: flex;
                align-items: center;
                gap: 1rem;
                padding: 0.8rem 1.2rem;
                background: rgba(255, 255, 255, 0.05);
                border-radius: 10px;
                border-left: 3px solid var(--color-primary);
                transition: all 0.3s ease;
            }

            .benefit-item:hover {
                transform: translateX(5px);
                background: rgba(20, 241, 149, 0.1);
            }

            .benefit-icon {
                font-size: 1.5rem;
            }

            .benefit-text {
                font-size: 1.05rem;
                font-weight: 500;
            }

            .stat-container {
                display: flex;
                gap: 2rem;
                margin-top: 2.5rem;
            }

            .stat-item {
                padding: 1rem;
                background: rgba(255, 255, 255, 0.05);
                border-radius: 10px;
                text-align: center;
                flex: 1;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
                border: 1px solid rgba(20, 241, 149, 0.1);
                transition: all 0.3s ease;
            }

            .stat-item:hover {
                transform: translateY(-5px);
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
                border-color: rgba(20, 241, 149, 0.3);
            }

            .stat-value {
                font-size: 2.2rem;
                font-weight: 700;
                color: var(--color-primary);
                margin-bottom: 0.3rem;
            }

            .stat-label {
                font-size: 0.9rem;
                color: rgba(255, 255, 255, 0.8);
                line-height: 1.4;
            }

            .cta-container {
                margin-top: 3rem;
                text-align: center;
            }

            .food-cta-button {
                background: linear-gradient(135deg, var(--color-primary), #0AD6DF);
                border: none;
                color: #080814;
                padding: 1rem 2rem;
                font-size: 1.1rem;
                font-weight: 600;
                border-radius: 50px;
                cursor: pointer;
                transition: all 0.3s ease;
                display: inline-flex;
                align-items: center;
                gap: 0.8rem;
                box-shadow: 0 10px 25px rgba(20, 241, 149, 0.4);
            }

            .food-cta-button:hover {
                transform: translateY(-3px);
                box-shadow: 0 15px 30px rgba(20, 241, 149, 0.5);
            }

            .cta-icon {
                font-size: 1.3rem;
            }

            @media (max-width: 991px) {
                .eating-coding-content {
                    flex-direction: column-reverse;
                    gap: 3rem;
                }

                .eating-coding-image {
                    width: 100%;
                }

                .eating-coding-text {
                    width: 100%;
                    max-width: 650px;
                    margin: 0 auto;
                }
            }

            @media (max-width: 768px) {
                .eating-coding-title {
                    font-size: 2.8rem;
                }

                .feature-heading {
                    font-size: 1.8rem;
                }

                .device-mockup {
                    width: 270px;
                    height: 200px;
                }

                .stat-container {
                    flex-direction: column;
                    gap: 1rem;
                }
            }
            }
        </style>

        <!-- Use Cases Section -->
        <div id="use-cases" class="use-cases-section">
            <div class="user-type-container">
                <!-- For Developers Card -->
                <div class="user-type-card">
                    <div class="card-illustration">
                        <!-- New Heroicon for Developers -->
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                            stroke="currentColor" class="size-6">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M17.25 6.75 22.5 12l-5.25 5.25m-10.5 0L1.5 12l5.25-5.25m7.5-3-4.5 16.5" />
                        </svg>
                    </div>
                    <h3 class="card-title">For Developers</h3>
                    <p class="card-description">Enhance your workflow and quickly transform complex ideas into optimized
                        code with voice-to-prompt technology.</p>
                </div>

                <!-- For Vibe Coders Card -->
                <div class="user-type-card">
                    <div class="card-illustration">
                        <!-- New Heroicon for Vibe Coders -->
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                            stroke="currentColor" class="size-6">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z" />
                        </svg>
                    </div>
                    <h3 class="card-title">For Vibe Coders</h3>
                    <p class="card-description">Capture fleeting ideas instantly as you code without breaking flow. Turn
                        scattered thoughts into precise prompts even when you're deep in the zone or can't find the
                        right
                        technical terms.</p>
                </div>
            </div>
        </div>

        <!-- Pricing Section -->
        <div id="pricing-section" class="pricing-section enhanced-pricing">
            <div class="pricing-backdrop"></div>
            <div class="floating-particles">
                <div class="particle particle-1"></div>
                <div class="particle particle-2"></div>
                <div class="particle particle-3"></div>
                <div class="particle particle-4"></div>
                <div class="particle particle-5"></div>
                <div class="particle particle-6"></div>
            </div>
            <div class="pricing-container">
                <!-- New Subscription Plans Section - MOVED ABOVE TRANSPARENT PRICING -->
<div class="subscription-plans-section">
    <div class="subscription-header">
        <h2 class="subscription-title">Choose Your Plan</h2>
        <p class="subscription-subtitle">
            Get predictable pricing with included usage quotas. Upgrade anytime as your needs grow.
            You can also insert prepaid <button class="credits-link"
                onclick="scrollToCredits()">credits</button> and use them*
        </p>
    </div>

    <!-- Subscription Cards Grid -->
    <div class="subscription-cards-grid">
        <!-- Pro Plan -->
        <div class="subscription-card pro-card">
            <!-- LIMITED TIME GIFT Badge -->
            <div style="position: absolute; top: 1rem; right: 1rem; z-index: 20; background: linear-gradient(to right, #ef4444, #f97316); color: white; font-size: 0.75rem; font-weight: bold; padding: 0.25rem 0.75rem; border-radius: 9999px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); display: flex; align-items: center; gap: 0.25rem;">
                🎁 LIMITED TIME GIFT
            </div>
            
            <div class="card-glow"></div>
            <div class="card-content">
                <div class="card-header">
                    <div class="card-icon pro-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                            stroke-width="1.5" stroke="currentColor" class="size-6">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z" />
                        </svg>
                    </div>
                    <h3 class="card-title">Pro</h3>
                    <div class="price-display">
                        <span class="price">$9<span class="text-xs">.99</span></span>
                        <span class="period">/month</span>
                    </div>
                    <p class="card-description">Ideal for small teams</p>
                </div>

                <ul class="features-list">
                    <li class="feature-item">
                        <div class="feature-check"></div>
                        <span>840 minutes (14 hours) transcription</span>
                    </li>
                    <li class="feature-item">
                        <div class="feature-check" style="background: linear-gradient(135deg, #10b981, #059669);">🎁</div>
                        <span style="color: #059669; font-weight: 600;">+240 bonus minutes (4 hours) FREE!</span>
                    </li>
                    <li class="feature-item">
                        <div class="feature-check"></div>
                        <span>440K AI tokens</span>
                    </li>
                    <li class="feature-item">
                        <div class="feature-check" style="background: linear-gradient(135deg, #10b981, #059669);">🎁</div>
                        <span style="color: #059669; font-weight: 600;">+100K bonus AI tokens FREE!</span>
                    </li>
                    <li class="feature-item">
                        <div class="feature-check"></div>
                        <span>Real-time transcription (English)</span>
                    </li>
                    <li class="feature-item">
                        <div class="feature-check"></div>
                        <span>Claude, DeepSeek and Llama</span>
                    </li>
                </ul>

                <a href="https://voicehype.ai/app/payments">
                    <button class="subscription-card-button pro-button" data-plan="pro">
                        Buy Now
                    </button>
                </a>
            </div>
        </div>

        <!-- Basic Plan (Featured) -->
        <div class="subscription-card basic-card featured">
            <!-- LIMITED TIME GIFT Badge -->
            <div style="position: absolute; top: 1rem; right: 1rem; z-index: 20; background: linear-gradient(to right, #ef4444, #f97316); color: white; font-size: 0.75rem; font-weight: bold; padding: 0.25rem 0.75rem; border-radius: 9999px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); display: flex; align-items: center; gap: 0.25rem;">
                🎁 LIMITED TIME GIFT
            </div>
            
            <div class="card-glow"></div>
            <div class="card-content">
                <div class="card-header">
                    <div class="card-icon basic-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                            stroke-width="1.5" stroke="currentColor" class="size-6">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 0 0 2.25-2.25V6.75a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 6.75v10.5a2.25 2.25 0 0 0 2.25 2.25Zm.75-12h9v9h-9v-9Z" />
                        </svg>
                    </div>
                    <h3 class="card-title">Basic</h3>
                    <div class="price-display">
                        <span class="price">$4<span class="text-xs">.99</span></span>
                        <span class="period">/month</span>
                    </div>
                    <p class="card-description">Perfect for individual developers</p>
                </div>

                <ul class="features-list">
                    <li class="feature-item">
                        <div class="feature-check"></div>
                        <span>420 minutes (7 hours) transcription</span>
                    </li>
                    <li class="feature-item">
                        <div class="feature-check" style="background: linear-gradient(135deg, #10b981, #059669);">🎁</div>
                        <span style="color: #059669; font-weight: 600;">+120 bonus minutes (2 hours) FREE!</span>
                    </li>
                    <li class="feature-item">
                        <div class="feature-check"></div>
                        <span>220K AI tokens</span>
                    </li>
                    <li class="feature-item">
                        <div class="feature-check" style="background: linear-gradient(135deg, #10b981, #059669);">🎁</div>
                        <span style="color: #059669; font-weight: 600;">+50K bonus AI tokens FREE!</span>
                    </li>
                    <li class="feature-item">
                        <div class="feature-check"></div>
                        <span>Real-time transcription (English)</span>
                    </li>
                    <li class="feature-item">
                        <div class="feature-check"></div>
                        <span>Claude, DeepSeek and Llama</span>
                    </li>
                </ul>

                <a href="https://voicehype.ai/app/payments">
                    <button class="subscription-card-button basic-button" data-plan="basic">
                        Buy Now
                    </button>
                </a>
            </div>
        </div>

        <!-- Premium Plan -->
        <div class="subscription-card premium-card">
            <!-- LIMITED TIME GIFT Badge -->
            <div style="position: absolute; top: 1rem; right: 1rem; z-index: 20; background: linear-gradient(to right, #ef4444, #f97316); color: white; font-size: 0.75rem; font-weight: bold; padding: 0.25rem 0.75rem; border-radius: 9999px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); display: flex; align-items: center; gap: 0.25rem;">
                🎁 LIMITED TIME GIFT
            </div>
            
            <div class="card-glow"></div>
            <div class="card-content">
                <div class="card-header">
                    <div class="card-icon premium-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                            stroke-width="1.5" stroke="currentColor" class="size-6">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M12 18.75a6 6 0 0 0 6-6v-1.5m-6 7.5a6 6 0 0 1-6-6v-1.5m6 7.5v3.75m-3.75 0h7.5M12 15.75a3 3 0 0 1-3-3V4.5a3 3 0 1 1 6 0v8.25a3 3 0 0 1-3 3Z" />
                        </svg>
                    </div>
                    <h3 class="card-title">Premium</h3>
                    <div class="price-display">
                        <span class="price">$14<span class="text-xs">.99</span></span>
                        <span class="period">/month</span>
                    </div>
                    <p class="card-description">Enterprise-ready usage</p>
                </div>

                <ul class="features-list">
                    <li class="feature-item">
                        <div class="feature-check"></div>
                        <span>1260 minutes (21 hours) transcription</span>
                    </li>
                    <li class="feature-item">
                        <div class="feature-check" style="background: linear-gradient(135deg, #10b981, #059669);">🎁</div>
                        <span style="color: #059669; font-weight: 600;">+360 bonus minutes (6 hours) FREE!</span>
                    </li>
                    <li class="feature-item">
                        <div class="feature-check"></div>
                        <span>660K AI tokens</span>
                    </li>
                    <li class="feature-item">
                        <div class="feature-check" style="background: linear-gradient(135deg, #10b981, #059669);">🎁</div>
                        <span style="color: #059669; font-weight: 600;">+150K bonus AI tokens FREE!</span>
                    </li>
                    <li class="feature-item">
                        <div class="feature-check"></div>
                        <span>Real-time transcription (English)</span>
                    </li>
                    <li class="feature-item">
                        <div class="feature-check"></div>
                        <span>Claude, DeepSeek and Llama</span>
                    </li>
                </ul>

                <a href="https://voicehype.ai/app/payments">
                    <button class="subscription-card-button premium-button" data-plan="premium">
                        Buy Now
                    </button>
                </a>
            </div>
        </div>
    </div>

    <!-- Subscription Note -->
    <div class="subscription-note">
        <p>* VoiceHype automatically will use your credits once your quota is exhausted, so you can
            comfortably keep using it if your quota runs out before the month.</p>
    </div>
</div>

                <div class="pricing-cta">
                    <button class="pricing-cta-button">Get Started</button>
                    <div class="pricing-cta-note">No credit card required</div>
                </div>

               
            </div>
        </div>

        <style>
            /* Mobile Menu Styles */
            .mobile-menu {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 1000;
                visibility: hidden;
                opacity: 0;
                transition: visibility 0.3s ease, opacity 0.3s ease;
            }

            .mobile-menu.active {
                visibility: visible;
                opacity: 1;
            }

            .mobile-menu-backdrop {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.7);
                backdrop-filter: blur(5px);
            }

            .mobile-menu-container {
                position: absolute;
                top: 0;
                right: 0;
                width: 85%;
                max-width: 320px;
                height: 100%;
                background: #0c0c1d;
                box-shadow: -5px 0 25px rgba(0, 0, 0, 0.3);
                transform: translateX(100%);
                transition: transform 0.3s ease-out;
                display: flex;
                flex-direction: column;
                border-left: 1px solid rgba(255, 255, 255, 0.05);
            }

            .mobile-menu.active .mobile-menu-container {
                transform: translateX(0);
            }

            .mobile-menu-header {
                display: flex;
                align-items: center;
                padding: 1.5rem;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            }

            .mobile-menu-title {
                margin-left: 0.75rem;
                font-size: 1.25rem;
                font-weight: 600;
                margin-bottom: 0;
                flex-grow: 1;
            }

            .mobile-menu-close {
                background: transparent;
                border: none;
                color: rgba(255, 255, 255, 0.7);
                cursor: pointer;
                padding: 0.5rem;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .mobile-menu-close:hover {
                color: var(--color-primary);
            }

            .mobile-menu-content {
                padding: 1.5rem;
                flex-grow: 1;
                display: flex;
                flex-direction: column;
                overflow-y: auto;
            }

            .mobile-menu-links {
                list-style: none;
                padding: 0;
                margin: 0 0 2rem 0;
            }

            .mobile-menu-links li {
                margin-bottom: 1rem;
            }

            .mobile-menu-link {
                color: #ffffff;
                text-decoration: none;
                font-size: 1.1rem;
                font-weight: 500;
                padding: 0.75rem 0;
                display: block;
                border-bottom: 1px solid rgba(255, 255, 255, 0.07);
                transition: all 0.2s ease;
            }

            .mobile-menu-link:hover {
                color: var(--color-primary);
                border-bottom-color: var(--color-primary);
                transform: translateX(5px);
            }

            .mobile-menu-buttons {
                display: flex;
                flex-direction: column;
                gap: 1rem;
                margin-top: auto;
            }

            .mobile-menu-button {
                padding: 0.75rem 1.5rem;
                text-align: center;
                border-radius: 6px;
                font-weight: 600;
                text-decoration: none;
                transition: all 0.2s ease;
            }

            .mobile-menu-button.login {
                background: rgba(255, 255, 255, 0.1);
                color: #ffffff;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }

            .mobile-menu-button.login:hover {
                background: rgba(255, 255, 255, 0.15);
                border-color: rgba(255, 255, 255, 0.3);
            }

            .mobile-menu-button.signup {
                background: var(--color-primary);
                color: #080814;
                border: 1px solid transparent;
            }

            .mobile-menu-button.signup:hover {
                background: #12d685;
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(20, 241, 149, 0.25);
            }

            /* Enhanced Pricing Section Styles */
            .enhanced-pricing {
                position: relative;
                background: linear-gradient(135deg, rgba(8, 8, 20, 0.95) 0%, rgba(16, 16, 30, 0.98) 100%);
                padding: 5rem 0;
                overflow: hidden;
                color: white;
            }

            .pricing-backdrop {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: radial-gradient(circle at 70% 30%, rgba(20, 241, 149, 0.1), transparent 50%),
                    radial-gradient(circle at 30% 70%, rgba(10, 214, 223, 0.1), transparent 50%);
                z-index: 0;
                opacity: 0.8;
            }

            .floating-particles {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 1;
                overflow: hidden;
            }

            .particle {
                position: absolute;
                background: rgba(20, 241, 149, 0.3);
                border-radius: 50%;
                filter: blur(10px);
                opacity: 0.5;
                animation: float 15s infinite linear;
            }

            .particle-1 {
                width: 100px;
                height: 100px;
                top: 10%;
                left: 10%;
                animation-duration: 25s;
                animation-delay: 0s;
                background: rgba(20, 241, 149, 0.15);
            }

            .particle-2 {
                width: 150px;
                height: 150px;
                top: 60%;
                left: 80%;
                animation-duration: 30s;
                animation-delay: 2s;
                background: rgba(99, 102, 241, 0.15);
            }

            .particle-3 {
                width: 80px;
                height: 80px;
                top: 30%;
                left: 85%;
                animation-duration: 20s;
                background: rgba(20, 241, 149, 0.1);
            }

            .particle-4 {
                width: 120px;
                height: 120px;
                top: 70%;
                left: 25%;
                animation-duration: 22s;
                animation-delay: 5s;
                background: rgba(99, 102, 241, 0.1);
            }

            .particle-5 {
                width: 60px;
                height: 60px;
                top: 15%;
                left: 60%;
                animation-duration: 18s;
                animation-delay: 3s;
                background: rgba(10, 214, 223, 0.15);
            }

            .particle-6 {
                width: 90px;
                height: 90px;
                top: 50%;
                left: 40%;
                animation-duration: 28s;
                animation-delay: 7s;
                background: rgba(10, 214, 223, 0.1);
            }

            @keyframes float {
                0% {
                    transform: translate(0, 0) rotate(0deg);
                }

                33% {
                    transform: translate(50px, -30px) rotate(10deg);
                }

                66% {
                    transform: translate(-20px, 40px) rotate(-10deg);
                }

                100% {
                    transform: translate(0, 0) rotate(0deg);
                }
            }

            .pricing-container {
                position: relative;
                z-index: 2;
                max-width: 1100px;
                margin: 0 auto;
                padding: 0 2rem;
            }

            .pricing-title-wrapper {
                display: flex;
                align-items: center;
                justify-content: center;
                margin-bottom: 1rem;
                gap: 1rem;
            }

            .pricing-title {
                font-family: var(--font-display);
                font-size: 3.2rem;
                font-weight: 700;
                background: linear-gradient(135deg, #ffffff 30%, var(--color-primary) 70%);
                -webkit-background-clip: text;
                background-clip: text;
                -webkit-text-fill-color: transparent;
                text-align: center;
                margin-bottom: 0.5rem;
                position: relative;
            }

            .pricing-highlight {
                position: relative;
                color: var(--color-primary);
                -webkit-text-fill-color: var(--color-primary);
            }

            .pricing-highlight::after {
                content: '';
                position: absolute;
                bottom: -5px;
                left: 0;
                width: 100%;
                height: 2px;
                background: var(--color-primary);
                border-radius: 2px;
            }

            .pricing-subtitle {
                font-size: 1.2rem;
                text-align: center;
                max-width: 700px;
                margin: 0 auto 2rem;
                color: rgba(255, 255, 255, 0.8);
                line-height: 1.6;
            }

            .pricing-subtitle-highlight {
                color: var(--color-primary);
                font-weight: 500;
                display: block;
                margin-top: 0.5rem;
            }

            .pricing-decorative-line {
                width: 120px;
                height: 3px;
                background: linear-gradient(90deg, transparent, var(--color-primary), transparent);
                margin: 2rem auto;
                border-radius: 2px;
            }

            .pricing-tabs {
                display: flex;
                justify-content: center;
                gap: 1rem;
                margin-bottom: 2rem;
            }

            .pricing-tab {
                padding: 0.75rem 1.5rem;
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 9999px;
                color: rgba(255, 255, 255, 0.7);
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .pricing-tab.active {
                background: rgba(20, 241, 149, 0.1);
                border-color: rgba(20, 241, 149, 0.3);
                color: var(--color-primary);
                box-shadow: 0 0 15px rgba(20, 241, 149, 0.2);
            }

            .pricing-tab:hover {
                background: rgba(20, 241, 149, 0.07);
                border-color: rgba(20, 241, 149, 0.2);
                transform: translateY(-2px);
            }

            .tab-icon {
                font-size: 1.2rem;
            }

            .pricing-category-title {
                font-family: var(--font-display);
                font-size: 2rem;
                font-weight: 600;
                text-align: center;
                margin-bottom: 2rem;
                color: white;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 1rem;
            }

            .model-badge {
                font-size: 0.8rem;
                padding: 0.2rem 0.8rem;
                background: linear-gradient(135deg, rgba(20, 241, 149, 0.2), rgba(99, 102, 241, 0.2));
                border-radius: 9999px;
                color: white;
                font-weight: 500;
                text-transform: uppercase;
                letter-spacing: 0.05em;
            }

            .pricing-table {
                width: 100%;
                border-collapse: separate;
                border-spacing: 0;
                margin: 0 auto;
                backdrop-filter: blur(10px);
                background: rgba(255, 255, 255, 0.02);
                border-radius: 12px;
                overflow: hidden;
                box-shadow: 0 5px 30px rgba(0, 0, 0, 0.2), 0 1px 3px rgba(0, 0, 0, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.05);
            }

            .pricing-table thead th {
                background: rgba(0, 0, 0, 0.3);
                padding: 1.2rem 1rem;
                font-weight: 600;
                text-align: left;
                color: white;
                font-size: 1rem;
                letter-spacing: 0.03em;
                text-transform: uppercase;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            }

            .pricing-table tbody tr {
                transition: all 0.3s ease;
                border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            }

            .pricing-table tbody tr:hover {
                background: rgba(255, 255, 255, 0.05);
                transform: translateX(5px);
            }

            .pricing-table tbody tr:last-child {
                border-bottom: none;
            }

            .pricing-table td {
                padding: 1.2rem 1rem;
                color: rgba(255, 255, 255, 0.8);
            }

            .pricing-model {
                font-weight: 500;
                color: white;
            }

            .model-name {
                font-weight: 600;
                margin-bottom: 0.3rem;
            }

            .model-tag {
                display: inline-block;
                font-size: 0.7rem;
                padding: 0.1rem 0.5rem;
                border-radius: 4px;
                text-transform: uppercase;
                letter-spacing: 0.05em;
                font-weight: 600;
            }

            .premium-model .model-tag {
                background: rgba(99, 102, 241, 0.2);
                color: #a5a7fa;
            }

            .standard-model .model-tag {
                background: rgba(20, 241, 149, 0.2);
                color: #7df8c7;
            }

            .balanced-model .model-tag {
                background: rgba(10, 214, 223, 0.2);
                color: #8deff5;
            }

            .pricing-cost {
                font-family: var(--font-mono);
                font-weight: 700;
                font-size: 1.1rem;
                color: var(--color-primary);
            }

            .unit-label {
                color: rgba(255, 255, 255, 0.6);
                font-size: 0.9rem;
            }

            .pricing-note {
                display: flex;
                align-items: center;
                gap: 1rem;
                margin-top: 2rem;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
            }

            .note-icon {
                font-size: 1.2rem;
            }

            .note-text {
                font-size: 0.95rem;
                color: rgba(255, 255, 255, 0.8);
                line-height: 1.5;
            }

            .contact-link {
                color: var(--color-primary);
                text-decoration: underline;
                transition: all 0.2s ease;
            }

            .contact-link:hover {
                color: #7df8c7;
            }

            .pricing-features {
                display: flex;
                justify-content: center;
                gap: 2rem;
                margin-top: 3rem;
                flex-wrap: wrap;
            }

            .feature-item {
                display: flex;
                align-items: center;
                gap: 0.75rem;
            }

            .feature-icon {
                width: 36px;
                height: 36px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                background: rgba(20, 241, 149, 0.1);
                color: var(--color-primary);
            }

            .feature-icon svg {
                width: 20px;
                height: 20px;
            }

            .feature-text {
                font-weight: 500;
                color: white;
            }

            .pricing-cta {
                margin-top: 3rem;
                text-align: center;
            }

            .pricing-cta-button {
                padding: 1rem 2.5rem;
                font-size: 1.1rem;
                font-weight: 600;
                border-radius: 9999px;
                background: linear-gradient(135deg, var(--color-primary), #0AD6DF);
                color: #080814;
                border: none;
                cursor: pointer;
                transition: all 0.3s ease;
                box-shadow: 0 10px 25px rgba(20, 241, 149, 0.4);
            }

            .pricing-cta-button:hover {
                transform: translateY(-2px);
                box-shadow: 0 15px 30px rgba(20, 241, 149, 0.5);
            }

            .pricing-cta-note {
                margin-top: 1rem;
                font-size: 0.9rem;
                color: rgba(255, 255, 255, 0.7);
            }

            @media (max-width: 768px) {
                .pricing-title {
                    font-size: 2.5rem;
                }

                .pricing-category-title {
                    font-size: 1.6rem;
                    flex-direction: column;
                    gap: 0.5rem;
                }

                .pricing-features {
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 1rem;
                }

                .pricing-tab {
                    padding: 0.6rem 1.2rem;
                    font-size: 0.9rem;
                }

                .pricing-table {
                    font-size: 0.9rem;
                }

                .pricing-table thead th {
                    padding: 1rem 0.75rem;
                }

                .pricing-table td {
                    padding: 1rem 0.75rem;
                }
            }
        </style>

        <!-- Blog Section -->
        <div id="blog-section" class="blog-section">
            <div class="blog-container">
                <div class="blog-header section-header">
                    <h2 class="blog-title" style="text-align: center; width: 100%;"><span
                            class="gradient-text">Blog</span>
                    </h2>
                </div>

                <!-- Coming soon message -->
                <div style="text-align: center; margin: 50px 0;">
                    <h3 style="font-size: 1.8rem; color: var(--color-primary);">Coming soon, inshallah!</h3>
                    <p style="font-size: 1.2rem; max-width: 600px; margin: 20px auto;">
                        We're preparing insightful articles and tutorials to help you get the most out of VoiceHype.
                        Check back soon for updates!
                    </p>
                </div>

                <!-- Remove blog article content -->

                <!-- Remove newsletter section -->
            </div>
        </div>

        <!-- FAQ Section -->
        <div id="faq" class="faq-section">
            <div class="faq-container">
                <h2 class="faq-title">Frequently Asked <span class="gradient-text">Questions</span></h2>

                <div class="faq-grid">
                    <div class="faq-item">
                        <div class="faq-question">
                            <span class="question-text">How does VoiceHype improve my coding workflow?</span>
                            <div class="question-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24">
                                    <path d="M440-440H200v-80h240v-240h80v240h240v80H520v240h-80v-240Z"
                                        fill="currentColor" />
                                </svg>
                            </div>
                        </div>
                        <div class="faq-answer">
                            <p>VoiceHype transforms your spoken thoughts into perfectly structured code prompts. Instead
                                of
                                spending minutes typing out detailed prompts, you can simply speak naturally and let our
                                AI
                                handle the conversion. This allows you to maintain your creative flow and focus on
                                solving
                                problems rather than crafting prompts.</p>
                        </div>
                    </div>

                    <div class="faq-item">
                        <div class="faq-question">
                            <span class="question-text">Which coding platforms are compatible with VoiceHype?</span>
                            <div class="question-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24">
                                    <path d="M440-440H200v-80h240v-240h80v240h240v80H520v240h-80v-240Z"
                                        fill="currentColor" />
                                </svg>
                            </div>
                        </div>
                        <div class="faq-answer">
                            <p>VoiceHype seamlessly integrates with VS Code and its forks such as CURSOR, Windsurf, etc.
                                and
                                it can be used with extensions such as GitHub Copilot, Cline, Continue.dev, Roo Code,
                                etc.
                            </p>
                        </div>
                    </div>

                    <div class="faq-item">
                        <div class="faq-question">
                            <span class="question-text">Do I need to speak with perfect technical precision?</span>
                            <div class="question-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24">
                                    <path d="M440-440H200v-80h240v-240h80v240h240v80H520v240h-80v-240Z"
                                        fill="currentColor" />
                                </svg>
                            </div>
                        </div>
                        <div class="faq-answer">
                            <p>Not at all! VoiceHype's understands natural speech patterns, including filler
                                words, pauses, and casual descriptions. You can speak conversationally and let VoiceHype
                                do
                                the magic.</p>
                        </div>
                    </div>

                    <div class="faq-item">
                        <div class="faq-question">
                            <span class="question-text">What languages does VoiceHype support for voice input?</span>
                            <div class="question-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24">
                                    <path d="M440-440H200v-80h240v-240h80v240h240v80H520v240h-80v-240Z"
                                        fill="currentColor" />
                                </svg>
                            </div>
                        </div>
                        <div class="faq-answer">
                            <p>VoiceHype supports over 50 languages including Arabic, English, Spanish, German,
                                Chinese, Japanese, Hindi, Portuguese, Russian, Urdu, and many more.</p>
                        </div>
                    </div>

                    <div class="faq-item">
                        <div class="faq-question">
                            <span class="question-text">How does the free trial work?</span>
                            <div class="question-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24">
                                    <path d="M440-440H200v-80h240v-240h80v240h240v80H520v240h-80v-240Z"
                                        fill="currentColor" />
                                </svg>
                            </div>
                        </div>
                        <div class="faq-answer">
                            <p>We offer a one-month free trial with 60 minutes of transcription and 10K tokens of
                                optimization with LLMs. No credit card is
                                required to start.</p>
                        </div>
                    </div>

                    <div class="faq-item">
                        <div class="faq-question">
                            <span class="question-text">What about privacy and data security?</span>
                            <div class="question-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24">
                                    <path d="M440-440H200v-80h240v-240h80v240h240v80H520v240h-80v-240Z"
                                        fill="currentColor" />
                                </svg>
                            </div>
                        </div>
                        <div class="faq-answer">
                            <p>At VoiceHype, security is our priority. Neither your transcripts nor your optimizations
                                are
                                stored in our databases. Your payment information is not stored by us but instead used
                                by
                                our third-party payments provider, Paddle.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scroll to Top Button -->
        <div class="scroll-to-top">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="18 15 12 9 6 15"></polyline>
            </svg>
        </div>

        <!-- Footer Section -->
        <footer class="footer-section">
            <div class="footer-container">
                <div class="footer-content">
                    <div class="footer-logo-section">
                        <div class="footer-logo">
                            <div class="logo-container">
                                <svg width="40" height="30" viewBox="0 0 40 30" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <rect x="2" y="2" width="36" height="20" rx="2" fill="white" />
                                    <rect x="2" y="2" width="36" height="20" rx="2" stroke="#14F195"
                                        stroke-width="1.5" />
                                    <path d="M7 14 L10 9 L13 14 L16 9 L19 14 L22 9 L25 14 L28 9 L31 14 L34 9"
                                        stroke="#14F195" stroke-width="1.5" stroke-linecap="round" />
                                    <line x1="20" y1="22" x2="20" y2="26" stroke="#14F195" stroke-width="1.5" />
                                    <line x1="14" y1="26" x2="26" y2="26" stroke="#14F195" stroke-width="1.5" />
                                </svg>
                            </div>
                            <h3 class="footer-logo-text">Voice<span class="hype-text-green">Hype</span></h3>
                        </div>
                        <p class="footer-tagline">Revolutionizing coding with voice-to-prompt technology</p>
                    </div>

                    <!-- Footer links with a more structured layout for mobile -->
                    <div class="footer-links">
                        <div class="footer-links-container">
                            <div class="footer-links-column">
                                <h4>Product</h4>
                                <ul>
                                    <li><a href="#features">Features</a></li>
                                    <li><a href="#pricing-section">Pricing</a></li>
                                    <li><a href="#use-cases">Use Cases</a></li>
                                    <li><a href="#blog-section">Blog</a></li>
                                    <li><a href="#faq" class="scroll-to-faq">FAQ</a></li>
                                </ul>
                            </div>
                            <div class="footer-links-column">
                                <h4>Legal</h4>
                                <ul>
                                    <li><a href="/privacy-policy">Privacy Policy</a></li>
                                    <li><a href="/terms-of-service">Terms of Service</a></li>
                                    <li><a href="/refund-policy">Refund Policy</a></li>
                                    <li><a href="/security">Security</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <div class="footer-bottom-container">
                    <p class="copyright">
                        <!-- 2025 VoiceHype. All rights reserved. -->
                    </p>
                    <div class="contact-info">
                        <a href="mailto:<EMAIL>" class="contact-email">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z">
                                </path>
                                <polyline points="22,6 12,13 2,6"></polyline>
                            </svg>
                            <EMAIL>
                        </a>
                    </div>
                </div>
            </div>
        </footer>


        <!-- Script to handle loading state -->
        <script>
            document.addEventListener('DOMContentLoaded', function () {
                // Create a promise that resolves when fonts are loaded
                const fontsLoaded = document.fonts.ready;

                // Create a promise that resolves after a minimum loading time
                const minimumLoadTime = new Promise(resolve => setTimeout(resolve, 800));

                // Create a promise that resolves when critical resources are loaded
                const resourcesLoaded = new Promise(resolve => {
                    if (document.readyState === 'complete') {
                        resolve();
                    } else {
                        window.addEventListener('load', resolve);
                    }
                });

                // Wait for all promises to resolve
                Promise.all([fontsLoaded, minimumLoadTime, resourcesLoaded])
                    .then(() => {
                        // Add a small delay to ensure smooth transition
                        setTimeout(() => {
                            document.body.classList.add('content-loaded');
                            document.querySelector('.loading-overlay').classList.add('hidden');
                        }, 100);
                    })
                    .catch(error => {
                        console.error('Error during loading:', error);
                        // Show content anyway after a timeout in case of error
                        setTimeout(() => {
                            document.body.classList.add('content-loaded');
                            document.querySelector('.loading-overlay').classList.add('hidden');
                        }, 2000);
                    });
            });
        </script>

        <!-- Mobile menu functionality -->
        <script>
            document.addEventListener('DOMContentLoaded', function () {
                console.log('DOM fully loaded - setting up mobile menu');

                // Mobile menu elements
                const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
                const mobileMenu = document.querySelector('.mobile-menu');
                const mobileMenuClose = document.querySelector('.mobile-menu-close');
                const mobileMenuLinks = document.querySelectorAll('.mobile-menu-link');
                const mobileMenuButtons = document.querySelectorAll('.mobile-menu-button');
                const body = document.body;

                console.log('Mobile menu elements:', {
                    mobileMenuToggle: mobileMenuToggle,
                    mobileMenu: mobileMenu,
                    mobileMenuClose: mobileMenuClose,
                    mobileMenuLinks: mobileMenuLinks.length
                });

                // Open mobile menu
                if (mobileMenuToggle && mobileMenu) {
                    mobileMenuToggle.addEventListener('click', function () {
                        console.log('Mobile menu toggle clicked');
                        mobileMenuToggle.classList.add('active');
                        mobileMenu.classList.add('active');
                        body.classList.add('menu-open');
                        body.style.overflow = 'hidden'; // Prevent scrolling
                    });
                } else {
                    console.error('Mobile menu toggle button or menu not found!');
                }

                // Close mobile menu
                if (mobileMenuClose && mobileMenu) {
                    mobileMenuClose.addEventListener('click', function () {
                        console.log('Mobile menu close clicked');
                        mobileMenuToggle.classList.remove('active');
                        mobileMenu.classList.remove('active');
                        body.classList.remove('menu-open');
                        body.style.overflow = ''; // Re-enable scrolling
                    });
                }

                // Close menu when clicking on the backdrop
                if (mobileMenu) {
                    const backdrop = mobileMenu.querySelector('.mobile-menu-backdrop');
                    if (backdrop) {
                        backdrop.addEventListener('click', function () {
                            console.log('Backdrop clicked, closing menu');
                            mobileMenuToggle.classList.remove('active');
                            mobileMenu.classList.remove('active');
                            body.classList.remove('menu-open');
                            body.style.overflow = ''; // Re-enable scrolling
                        });
                    }
                }

                // Handle menu link clicks - smooth scroll and close menu
                mobileMenuLinks.forEach(link => {
                    link.addEventListener('click', function (e) {
                        e.preventDefault();
                        console.log('Menu link clicked:', this.getAttribute('href'));

                        // Get the target section
                        const targetId = this.getAttribute('href');
                        const targetElement = document.querySelector(targetId);

                        // Close the menu
                        mobileMenuToggle.classList.remove('active');
                        mobileMenu.classList.remove('active');
                        body.classList.remove('menu-open');
                        body.style.overflow = ''; // Re-enable scrolling

                        // Scroll to the target section
                        if (targetElement) {
                            setTimeout(() => {
                                targetElement.scrollIntoView({
                                    behavior: 'smooth',
                                    block: 'start'
                                });
                            }, 300); // Small delay to allow menu to close first

                            // Update URL
                            window.history.pushState(null, '', targetId);
                        }
                    });
                });

                // Handle menu button clicks - close menu
                mobileMenuButtons.forEach(button => {
                    button.addEventListener('click', function () {
                        console.log('Menu button clicked');
                        mobileMenuToggle.classList.remove('active');
                        mobileMenu.classList.remove('active');
                        body.classList.remove('menu-open');
                        body.style.overflow = ''; // Re-enable scrolling
                    });
                });
            });
        </script>

        <script>
            // JavaScript for no animations - just handle pricing tabs
            document.addEventListener('DOMContentLoaded', function () {
                // Make all appear-on-scroll elements visible immediately
                document.querySelectorAll('.appear-on-scroll').forEach(element => {
                    element.style.opacity = '1';
                    element.style.transform = 'none';
                });

                // Pricing tabs functionality without animations
                const pricingTabs = document.querySelectorAll('.pricing-tab');
                const pricingContents = document.querySelectorAll('.pricing-tab-content');

                pricingTabs.forEach(tab => {
                    tab.addEventListener('click', () => {
                        // Remove active class from all tabs
                        pricingTabs.forEach(t => t.classList.remove('active'));

                        // Add active class to clicked tab
                        tab.classList.add('active');

                        // Hide all content sections
                        pricingContents.forEach(content => {
                            content.style.display = 'none';
                        });

                        // Show the matching content section
                        const tabName = tab.getAttribute('data-tab');
                        const activeContent = document.getElementById(`${tabName}-pricing`);
                        if (activeContent) {
                            activeContent.style.display = 'block';
                        }
                    });
                });

                // Smooth scroll functionality for anchor links
                document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                    anchor.addEventListener('click', function (e) {
                        e.preventDefault();
                        const targetId = this.getAttribute('href');

                        if (targetId === '#' || !targetId) return;

                        const targetElement = document.querySelector(targetId);
                        if (targetElement) {
                            // Scroll smoothly to the target
                            targetElement.scrollIntoView({
                                behavior: 'smooth',
                                block: 'start'
                            });

                            // Push the hash to the URL for proper bookmarking
                            window.history.pushState(null, '', targetId);
                        }
                    });
                });

                // Special handling for FAQ links
                document.querySelectorAll('.scroll-to-faq').forEach(faqLink => {
                    faqLink.addEventListener('click', function (e) {
                        e.preventDefault();
                        e.stopPropagation(); // Prevent other handlers from running

                        // Find the FAQ section by ID
                        const faqSection = document.querySelector('#faq');
                        if (faqSection) {
                            // Scroll to the FAQ section
                            faqSection.scrollIntoView({
                                behavior: 'smooth',
                                block: 'start'
                            });

                            // Update URL without redirecting
                            window.history.pushState(null, '', '#faq');
                        }

                        return false; // Prevent default and stop propagation
                    });
                });

                // Special handling for Blog links
                document.querySelectorAll('a[href*="blog"], a[href="/#blog-section"], .footer-links a[href*="blog"]').forEach(link => {
                    link.addEventListener('click', function (e) {
                        // Skip if it's within the blog page itself
                        if (window.location.pathname.includes('blog')) return;

                        e.preventDefault();
                        e.stopPropagation();

                        // Find the blog section by ID
                        const blogSection = document.querySelector('#blog-section');
                        if (blogSection) {
                            // Scroll to the blog section
                            blogSection.scrollIntoView({
                                behavior: 'smooth',
                                block: 'start'
                            });

                            // Update URL without redirecting
                            window.history.pushState(null, '', '#blog-section');
                        }

                        return false; // Prevent default and stop propagation
                    });
                });
            });
        </script>


</body>

</html>
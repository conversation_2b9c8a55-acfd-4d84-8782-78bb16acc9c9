/* Pricing Section Styles */
.pricing-section {
    padding: 6rem 0;
    background-color: var(--color-background);
    position: relative;
    overflow: hidden;
}

.pricing-section::before,
.pricing-section::after {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(
        90deg, 
        rgba(255, 255, 255, 0) 0%, 
        rgba(255, 255, 255, 0.1) 20%, 
        rgba(20, 241, 149, 0.5) 50%, 
        rgba(255, 255, 255, 0.1) 80%, 
        rgba(255, 255, 255, 0) 100%
    );
}

.pricing-section::before {
    top: 0;
}

.pricing-section::after {
    bottom: 0;
    background: linear-gradient(
        90deg, 
        rgba(255, 255, 255, 0) 0%, 
        rgba(255, 255, 255, 0.1) 20%, 
        rgba(20, 241, 149, 0.3) 50%, 
        rgba(255, 255, 255, 0.1) 80%, 
        rgba(255, 255, 255, 0) 100%
    );
}

/* Container and Header Styles */
.pricing-container {
    padding: 4rem 0;
    max-width: 1200px;
    margin: 0 auto;
}

.pricing-header {
    text-align: center;
    margin-bottom: 4rem;
}

.pricing-title {
    font-size: 2.5rem;
    font-weight: 700;
    font-family: var(--font-display);
    margin-bottom: 1rem;
    background: linear-gradient(90deg, #14F195, #0A84FF);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
}

.pricing-subtitle {
    font-size: 1.1rem;
    color: var(--color-text-muted);
    max-width: 700px;
    margin: 0 auto;
}

/* Pricing Tabs Styles */
.pricing-tabs {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;
    position: relative;
}

.pricing-tab {
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    cursor: pointer;
    border: none;
    background: transparent;
    color: #64748b;
    position: relative;
    transition: all 0.3s ease;
}

.pricing-tab.active {
    color: #1e293b;
}

.tab-indicator {
    position: absolute;
    height: 3px;
    bottom: -2px;
    left: 0;
    background-color: #3b82f6;
    transition: transform 0.3s ease;
}

/* Pricing Content Styles */
.pricing-content {
    display: none;
}

.pricing-content.active {
    display: block;
}

/* Pricing Cards Layout */
.pricing-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

/* Pricing Card Styles */
.pricing-card {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
    padding: 2rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 1px solid #e2e8f0;
}

.pricing-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.pricing-card.popular {
    border-color: #3b82f6;
}

.popular-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: #3b82f6;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 999px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* Pricing Elements Styles */
.pricing-tier {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #1e293b;
}

.pricing-price {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    color: #1e293b;
}

.pricing-price .period {
    font-size: 1rem;
    color: #64748b;
    font-weight: 400;
}

/* Pricing Features Styles */
.pricing-features {
    margin: 1.5rem 0;
}

.pricing-feature {
    display: flex;
    align-items: flex-start;
    margin-bottom: 0.75rem;
    color: #475569;
}

.pricing-feature svg {
    color: #3b82f6;
    margin-right: 0.75rem;
    flex-shrink: 0;
}

/* CTA Button Styles */
.pricing-cta {
    display: block;
    width: 100%;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    text-align: center;
    transition: all 0.3s ease;
    margin-top: 1.5rem;
}

.primary-cta {
    background: #3b82f6;
    color: white;
    border: none;
}

.primary-cta:hover {
    background: #2563eb;
}

.secondary-cta {
    background: transparent;
    color: #3b82f6;
    border: 1px solid #3b82f6;
}

.secondary-cta:hover {
    background: rgba(59, 130, 246, 0.1);
}

/* Visibility Settings - No Animations */
.appear-on-scroll {
    opacity: 1;
    transform: none;
}

.delay-100, 
.delay-200, 
.delay-300, 
.delay-400 {
    transition-delay: 0ms;
}

/* Pricing Tab Content Visibility */
.pricing-tab-content {
    opacity: 1;
    transform: none;
}

.pricing-tab-content.active {
    display: block;
}

.pricing-tab-content:not(.active) {
    display: none;
}

/* Table Styles */
.pricing-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 2rem;
}

.pricing-table th {
    text-align: left;
    padding: 1rem;
    font-weight: 600;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--color-text-light);
}

.pricing-table td {
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.pricing-model {
    font-weight: 500;
    color: var(--color-text);
}

.pricing-cost {
    color: var(--color-primary);
    font-weight: 600;
}

.unit-label {
    color: var(--color-text-muted);
    font-size: 0.9rem;
}

/* Make pricing table rows visible */
.pricing-table tr {
    opacity: 1;
}

/* Pricing CTA Section */
.pricing-cta {
    text-align: center;
    margin-top: 3rem;
}

.pricing-cta-text {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    color: var(--color-text-light);
}

.pricing-button {
    display: inline-block;
    padding: 0.75rem 2rem;
    background: linear-gradient(90deg, #14F195, #0A84FF);
    color: #fff;
    font-weight: 600;
    border-radius: 8px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pricing-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(20, 241, 149, 0.3);
}

.pricing-note {
    text-align: center;
    margin-top: 2rem;
    font-size: 0.9rem;
    color: var(--color-text-muted);
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .pricing-container {
        padding: 2rem 1rem;
    }
    
    .pricing-cards {
        grid-template-columns: 1fr;
    }
    
    .pricing-tab {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
    
    .pricing-title {
        font-size: 2rem;
    }
    
    .pricing-table {
        font-size: 0.9rem;
    }
    
    .pricing-cta-text {
        font-size: 1rem;
    }
}

/* Dark Mode Specific Adjustments */
.dark .pricing-table {
    color: #f1f5f9; /* Light gray */
}

.dark .pricing-table th {
    color: #f8fafc; /* Lighter gray for headings */
    border-bottom-color: rgba(255, 255, 255, 0.1);
}

.dark .pricing-table td {
    border-bottom-color: rgba(255, 255, 255, 0.05);
}

.dark .pricing-model {
    color: #e2e8f0; /* Light gray for model names */
}

.dark .pricing-cost {
    color: #14F195; /* Primary green color */
}

.dark .unit-label {
    color: #94a3b8; /* Light muted gray */
}

.dark .pricing-tab {
    color: #94a3b8; /* Light muted gray */
}

.dark .pricing-tab.active {
    color: #f1f5f9; /* Light gray for active tab */
}

.dark .pricing-button {
    color: #ffffff; /* Ensure button text is white */
}

.dark .pricing-cta-text {
    color: #e2e8f0; /* Light gray for CTA text */
}

.dark .pricing-note {
    color: #94a3b8; /* Light muted gray for notes */
}

/* Add padding to container in mobile view for better readability */
@media (max-width: 768px) {
    .pricing-container {
        padding: 2rem 1rem;
    }
    
    .pricing-table {
        font-size: 0.85rem;
    }
    
    .pricing-table th,
    .pricing-table td {
        padding: 0.75rem 0.5rem;
    }
}

/* Pricing Icon with Dollar Sign */
.pricing-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(20, 241, 149, 0.15), rgba(99, 102, 241, 0.15));
    color: var(--color-primary);
    position: relative;
}

/* Modified animation for the pricing icon - only expands and contracts without reversing */
.pricing-icon::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(20, 241, 149, 0.2), rgba(99, 102, 241, 0.1));
    z-index: -1;
    animation: pulseOneWay 3s infinite ease-in-out;
}

/* New animation that only expands and contracts without reversing */
@keyframes pulseOneWay {
    0% {
        transform: scale(1);
        opacity: 0.7;
    }
    50% {
        transform: scale(1.15);
        opacity: 0.3;
    }
    100% {
        transform: scale(1);
        opacity: 0.7;
    }
}

.pricing-icon svg {
    width: 28px;
    height: 28px;
}

/* Hide dollar sign on tablets and mobile */
@media (max-width: 991px) {
    .pricing-icon {
        display: none;
    }
    
    .pricing-title-wrapper {
        justify-content: center;
    }
}

/* Make sure the title and wrapper are properly styled */
.pricing-title-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

/* Subscription Plans Section */
.subscription-plans-section {
    margin-top: 6rem;
    padding: 4rem 0;
    border-top: 1px solid rgba(20, 241, 149, 0.2);
}

.subscription-header {
    text-align: center;
    margin-bottom: 4rem;
}

.subscription-title {
    font-family: var(--font-display);
    font-size: 2.5rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #ffffff, var(--color-primary));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.subscription-subtitle {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.8);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.credits-link {
    background: none;
    border: none;
    color: var(--color-primary);
    text-decoration: underline;
    cursor: pointer;
    font-size: inherit;
    padding: 0;
    margin: 0;
    transition: color 0.3s ease;
}

.credits-link:hover {
    color: #0AD6DF;
}

/* Subscription Cards Grid */
.subscription-cards-grid {
    display: grid;
    grid-template-columns: 1fr 1.2fr 1fr;
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.subscription-card {
    position: relative;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    transition: all 0.3s ease;
    overflow: hidden;
}

.subscription-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border-color: rgba(20, 241, 149, 0.3);
}

.subscription-card.featured {
    transform: scale(1.05);
    border-color: rgba(20, 241, 149, 0.4);
    background: rgba(20, 241, 149, 0.03);
}

.subscription-card.featured:hover {
    transform: scale(1.05) translateY(-5px);
}

.card-glow {
    position: absolute;
    inset: 0;
    background: radial-gradient(circle at center, rgba(20, 241, 149, 0.1), transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.subscription-card:hover .card-glow {
    opacity: 1;
}

.card-content {
    position: relative;
    z-index: 2;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.card-header {
    text-align: center;
    margin-bottom: 2rem;
}

.card-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 1rem;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--color-primary), #0AD6DF);
}

.card-icon svg {
    width: 28px;
    height: 28px;
    color: white;
}

.pro-card .card-icon {
    background: linear-gradient(135deg, #9333ea, #ec4899);
}

/* Basic card - black icon */
.basic-card .card-icon {
    background: #000000;
}

.basic-card .card-icon svg {
    color: white;
}

.premium-card .card-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.card-title {
    font-family: var(--font-display);
    font-size: 1.25rem;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 1rem;
}

.price-display {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.price {
    font-family: var(--font-display);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--color-primary);
}

.basic-card.featured .price {
    font-size: 3rem;
}

.period {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.7);
    margin-left: 0.5rem;
}

.card-description {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
}

.features-list {
    list-style: none;
    padding: 0;
    margin: 0 0 2rem 0;
    flex-grow: 1;
}

.feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.5rem 0;
}

.feature-check {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--color-primary), #0AD6DF);
    margin-right: 0.75rem;
    flex-shrink: 0;
    position: relative;
}

.feature-check::after {
    content: "✓";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.pro-card .feature-check {
    background: linear-gradient(135deg, #9333ea, #ec4899);
}

/* Basic card - black check marks */
.basic-card .feature-check {
    background: #000000;
}

.premium-card .feature-check {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.feature-item span {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.4;
}

.subscription-card-button {
    width: 100%;
    padding: 1rem 2rem;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, var(--color-primary), #0AD6DF);
    color: white;
    margin-top: auto;
}

.subscription-card-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(20, 241, 149, 0.3);
}

/* Basic card - black button with white text */
.basic-button {
    background: #000000;
    color: white;
}

.basic-button:hover {
    background: #333333;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.pro-button {
    background: linear-gradient(135deg, #9333ea, #ec4899);
}

.pro-button:hover {
    box-shadow: 0 10px 20px rgba(147, 51, 234, 0.3);
}

.premium-button {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.premium-button:hover {
    box-shadow: 0 10px 20px rgba(245, 158, 11, 0.3);
}

.subscription-note {
    text-align: center;
    margin-top: 3rem;
}

.subscription-note p {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.6);
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .subscription-cards-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 0 1rem;
    }
    
    .subscription-card.featured {
        transform: none;
        order: -1;
    }
    
    .subscription-card.featured:hover {
        transform: translateY(-5px);
    }
    
    .subscription-title {
        font-size: 2rem;
    }
    
    .price {
        font-size: 2rem;
    }
    
    .basic-card.featured .price {
        font-size: 2.5rem;
    }
}

@media (max-width: 480px) {
    .subscription-card {
        padding: 1.5rem;
    }
    
    .subscription-title {
        font-size: 1.8rem;
    }
}

/* Feature Comparison Table */
.feature-comparison-section {
    margin-top: 4rem;
    padding: 3rem 0;
}

.comparison-header {
    text-align: center;
    margin-bottom: 3rem;
}

.comparison-title {
    font-family: var(--font-display);
    font-size: 2rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 1rem;
}

.comparison-subtitle {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
}

.comparison-table-wrapper {
    max-width: 800px;
    margin: 0 auto;
    overflow-x: auto;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.02);
}

.comparison-table {
    width: 100%;
    border-collapse: collapse;
    min-width: 600px;
}

.comparison-table th,
.comparison-table td {
    padding: 1rem;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.comparison-table th:first-child,
.comparison-table td:first-child {
    text-align: left;
    padding-left: 1.5rem;
}

.feature-header {
    font-weight: 600;
    color: #ffffff;
    background: rgba(255, 255, 255, 0.05);
}

.plan-header {
    font-weight: 600;
    color: #ffffff;
    background: rgba(255, 255, 255, 0.05);
}

.plan-header.featured {
    background: rgba(20, 241, 149, 0.1);
    color: var(--color-primary);
    position: relative;
}

.feature-name {
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
}

.feature-with-icon {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    justify-content: flex-start;
}

.feature-icon {
    width: 18px;
    height: 18px;
    color: #22c55e !important;
    flex-shrink: 0;
    margin-right: 0.25rem;
}

.plan-value {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.plan-value.featured {
    background: rgba(20, 241, 149, 0.05);
    color: var(--color-primary);
    font-weight: 600;
}

.checkmark-cell {
    text-align: center;
}

.checkmark-icon {
    width: 20px;
    height: 20px;
    color: #22c55e !important;
    margin: 0 auto;
    display: block;
}

.comparison-table tr:last-child th,
.comparison-table tr:last-child td {
    border-bottom: none;
}

/* Responsive Design for Comparison Table */
@media (max-width: 768px) {
    .comparison-table-wrapper {
        margin: 0 1rem;
        border-radius: 8px;
    }
    
    .comparison-table th,
    .comparison-table td {
        padding: 0.75rem 0.5rem;
        font-size: 0.9rem;
    }
    
    .comparison-table th:first-child,
    .comparison-table td:first-child {
        padding-left: 1rem;
    }
    
    .feature-with-icon {
        flex-direction: column;
        gap: 0.4rem;
        align-items: flex-start;
    }
    
    .feature-icon {
        width: 16px;
        height: 16px;
        margin-right: 0;
        margin-bottom: 0.25rem;
    }
}
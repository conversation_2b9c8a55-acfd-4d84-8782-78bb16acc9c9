/**
 * VoiceHype Pricing Module 
 * 
 * Handles pricing tab functionality and data fetching from service pricing
 */

// Updated service pricing data from CSV
const SERVICE_PRICING = {
    transcription: [
        {
            id: "59ec6542-52b6-4ef1-bb76-37f4817d0fdd",
            service: "transcription",
            model: "assembly-ai/nano",
            cost_per_unit: 0.002360000,
            unit: "minute",
            name: "Assembly AI / Nano",
            tag: "Lightweight"
        },
        {
            id: "837d2a40-63e5-4da1-b7a5-12e15f61a6ae", 
            service: "transcription",
            model: "whisper-1",
            cost_per_unit: 0.004000000,
            unit: "minute",
            name: "Whisper",
            tag: "High Quality"
        },
        {
            id: "937919fb-f39e-43bd-9148-9b66e9880bbf",
            service: "transcription", 
            model: "assembly-ai/best",
            cost_per_unit: 0.007276667,
            unit: "minute",
            name: "Assembly AI / Best",
            tag: "High Quality"
        },
        {
            id: "f2834027-681e-483d-9080-45dd5a113b7c",
            service: "transcription",
            model: "assemblyai/best-realtime", 
            cost_per_unit: 0.009200000,
            unit: "minute",
            name: "Assembly AI / Best (Realtime)",
            tag: "Streaming"
        }
    ],
    optimization: [
        // Claude Models
        {
            id: "a1b2c3d4-e5f6-4890-abcd-ef1234567890",
            service: "optimization",
            model: "anthropic/claude-3-5-sonnet-latest",
            cost_per_unit: 0.000003540,
            unit: "token",
            type: "input",
            name: "Claude 3.5 Sonnet",
            tag: "Premium"
        },
        {
            id: "b2c3d4e5-f6a7-4901-bcde-f23456789012",
            service: "optimization", 
            model: "anthropic/claude-3-5-sonnet-latest",
            cost_per_unit: 0.000017700,
            unit: "token",
            type: "output",
            name: "Claude 3.5 Sonnet",
            tag: "Premium"
        },
        {
            id: "c3d4e5f6-a7b8-4012-cdef-345678901234",
            service: "optimization",
            model: "anthropic/claude-3-7-sonnet-latest", 
            cost_per_unit: 0.000003540,
            unit: "token",
            type: "input",
            name: "Claude 3.7 Sonnet",
            tag: "Premium"
        },
        {
            id: "d4e5f6a7-b8c9-4123-defa-456789012345",
            service: "optimization",
            model: "anthropic/claude-3-7-sonnet-latest",
            cost_per_unit: 0.000017700,
            unit: "token", 
            type: "output",
            name: "Claude 3.7 Sonnet",
            tag: "Premium"
        },
        {
            id: "e5f6a7b8-c9d0-4234-efab-567890123456",
            service: "optimization",
            model: "anthropic/claude-sonnet-4-20250514",
            cost_per_unit: 0.000003540,
            unit: "token",
            type: "input", 
            name: "Claude Sonnet 4",
            tag: "Premium"
        },
        {
            id: "f6a7b8c9-d0e1-4345-fabc-678901234567",
            service: "optimization",
            model: "anthropic/claude-sonnet-4-20250514",
            cost_per_unit: 0.000017700,
            unit: "token",
            type: "output",
            name: "Claude Sonnet 4", 
            tag: "Premium"
        },
        {
            id: "a7b8c9d0-e1f2-4456-abcd-789012345678",
            service: "optimization",
            model: "anthropic/claude-3-haiku-20240307",
            cost_per_unit: 0.000000295,
            unit: "token",
            type: "input",
            name: "Claude 3 Haiku",
            tag: "Standard"
        },
        {
            id: "b8c9d0e1-f2a3-4567-bcde-890123456789",
            service: "optimization",
            model: "anthropic/claude-3-haiku-20240307", 
            cost_per_unit: 0.000001475,
            unit: "token",
            type: "output",
            name: "Claude 3 Haiku",
            tag: "Standard"
        },        // Llama Models
        {
            id: "3150b2d2-5e49-4b17-bd1b-31566123edbb",
            service: "optimization",
            model: "deepinfra/meta-llama/Meta-Llama-3.1-70B-Instruct/input",
            cost_per_unit: 0.000000271,
            unit: "token",
            type: "input",
            name: "Llama 3.1 70B",
            tag: "Balanced"
        },
        {
            id: "8de86175-941e-43cd-a6b0-7d3f6a79c909",
            service: "optimization",
            model: "deepinfra/meta-llama/Meta-Llama-3.1-70B-Instruct/output",
            cost_per_unit: 0.000000472,
            unit: "token",
            type: "output",
            name: "Llama 3.1 70B",
            tag: "Balanced"
        },
        {
            id: "e6dc9ed3-6557-4198-b1e2-5c1cbf11c87e",
            service: "optimization",
            model: "deepinfra/meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo/input",
            cost_per_unit: 0.000000024,
            unit: "token",
            type: "input",
            name: "Llama 3.1 8B Instruct Turbo",
            tag: "Lightweight"
        },
        {
            id: "aae1da71-cad0-491c-b845-9f4ce70afc45",
            service: "optimization",
            model: "deepinfra/meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo/output",
            cost_per_unit: 0.000000059,
            unit: "token",
            type: "output",
            name: "Llama 3.1 8B Instruct Turbo",
            tag: "Lightweight"
        },
        // DeepSeek Models
        {
            id: "a3b4c5d6-e7f8-4012-abcd-345678901234",
            service: "optimization",
            model: "deepinfra/deepseek-ai/DeepSeek-V3",
            cost_per_unit: 0.000001003,
            unit: "token",
            type: "input",
            name: "DeepSeek V3",
            tag: "Balanced"
        },
        {
            id: "b4c5d6e7-f8a9-4123-bcde-456789012345",
            service: "optimization", 
            model: "deepinfra/deepseek-ai/DeepSeek-V3",
            cost_per_unit: 0.000001062,
            unit: "token",
            type: "output",
            name: "DeepSeek V3",
            tag: "Balanced"
        }
    ]
};

/**
 * Initialize pricing functionality on DOM load
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialize pricing tab functionality
    initPricingTabs();
    
    // Make all pricing elements visible (no animations)
    showAllPricingElements();
    
    // Update pricing tables with service pricing data
    updatePricingTablesWithServiceData();
    
    // Initialize subscription cards functionality
    initSubscriptionCards();
});

/**
 * Initialize pricing tab functionality
 * Handles switching between optimization and transcription tabs
 */
function initPricingTabs() {
    const pricingTabs = document.querySelectorAll('.pricing-tab');
    const pricingTabContents = document.querySelectorAll('.pricing-tab-content');
    
    pricingTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Remove active class from all tabs
            pricingTabs.forEach(t => t.classList.remove('active'));
            
            // Add active class to clicked tab
            this.classList.add('active');
            
            // Hide all tab content
            pricingTabContents.forEach(content => {
                content.style.display = 'none';
                content.classList.remove('active');
            });
            
            // Show selected tab content
            const tabContentId = `${this.getAttribute('data-tab')}-pricing`;
            const selectedContent = document.getElementById(tabContentId);
            
            if (selectedContent) {
                selectedContent.style.display = 'block';
                selectedContent.classList.add('active');
            }
        });
    });
}

/**
 * Make all pricing elements visible without animations
 * Overrides CSS to ensure all elements are visible immediately
 */
function showAllPricingElements() {
    // Remove all animation classes and show content immediately
    document.querySelectorAll('.appear-on-scroll').forEach(element => {
        element.style.opacity = '1';
        element.style.transform = 'none';
    });
    
    // Make all table rows visible
    document.querySelectorAll('.pricing-table tr').forEach(row => {
        row.style.opacity = '1';
    });
    
    // Make pricing content visible
    document.querySelectorAll('.pricing-tab-content').forEach(content => {
        if (content.classList.contains('active')) {
            content.style.display = 'block';
            content.style.opacity = '1';
            content.style.transform = 'none';
        }
    });
    
    // Override any animation CSS with inline styles
    addNoAnimationStyles();
}

/**
 * Add inline CSS to override any animations
 */
function addNoAnimationStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .appear-on-scroll {
            opacity: 1 !important;
            transform: none !important;
            transition: none !important;
        }
        
        .pricing-table tr {
            opacity: 1 !important;
            transition: none !important;
        }
        
        .pricing-tab-content {
            transition: none !important;
        }
        
        .pricing-tab-content.active {
            opacity: 1 !important;
            transform: none !important;
        }
    `;
    document.head.appendChild(style);
}

/**
 * Update pricing tables with service pricing data from CSV
 */
function updatePricingTablesWithServiceData() {
    updateOptimizationPricingTable();
    updateTranscriptionPricingTable();
}

/**
 * Update optimization pricing table with grouped models
 */
function updateOptimizationPricingTable() {
    const optimizationTable = document.querySelector('#optimization-pricing table tbody');
    if (!optimizationTable) return;
    
    // Group optimization models by base model name
    const groupedModels = {};
    SERVICE_PRICING.optimization.forEach(item => {
        const baseName = item.name;
        if (!groupedModels[baseName]) {
            groupedModels[baseName] = { input: null, output: null, tag: item.tag };
        }
        if (item.type === 'input') {
            groupedModels[baseName].input = item.cost_per_unit;
        } else if (item.type === 'output') {
            groupedModels[baseName].output = item.cost_per_unit;
        }
    });
    
    // Clear existing rows
    optimizationTable.innerHTML = '';
    
    // Add rows for each model
    Object.entries(groupedModels).forEach(([modelName, data]) => {
        if (data.input !== null && data.output !== null) {
            const row = document.createElement('tr');
            const modelClass = getModelClass(data.tag);
            row.className = modelClass;
            
            row.innerHTML = `
                <td class="pricing-model">
                    <div class="model-name">${modelName}</div>
                    <div class="model-tag">${data.tag}</div>
                </td>
                <td class="pricing-cost">$${(data.input * 1000000).toFixed(2)}</td>
                <td class="pricing-cost">$${(data.output * 1000000).toFixed(2)}</td>
                <td class="unit-label">per million tokens</td>
            `;
            
            optimizationTable.appendChild(row);
        }
    });
}

/**
 * Update transcription pricing table
 */
function updateTranscriptionPricingTable() {
    const transcriptionTable = document.querySelector('#transcription-pricing table tbody');
    if (!transcriptionTable) return;
    
    // Clear existing rows
    transcriptionTable.innerHTML = '';
    
    // Add rows for transcription models
    SERVICE_PRICING.transcription.forEach(item => {
        const row = document.createElement('tr');
        const modelClass = getModelClass(item.tag);
        row.className = modelClass;
        
        row.innerHTML = `
            <td class="pricing-model">
                <div class="model-name">${item.name}</div>
                <div class="model-tag">${item.tag}</div>
            </td>
            <td class="pricing-cost">$${item.cost_per_unit.toFixed(4)}</td>
            <td class="unit-label">per ${item.unit}</td>
        `;
        
        transcriptionTable.appendChild(row);
    });
}

/**
 * Get CSS class based on model tag
 */
function getModelClass(tag) {
    switch(tag.toLowerCase()) {
        case 'premium': return 'premium-model';
        case 'standard': return 'standard-model'; 
        case 'balanced': return 'balanced-model';
        case 'lightweight': return 'balanced-model';
        case 'high quality': return 'premium-model';
        case 'streaming': return 'premium-model';
        default: return 'standard-model';
    }
}

/**
 * Initialize subscription cards functionality
 */
function initSubscriptionCards() {
    // Add event listeners to subscription card buttons
    const subscriptionButtons = document.querySelectorAll('.subscription-card-button');
    
    subscriptionButtons.forEach(button => {
        button.addEventListener('click', function() {
            const plan = this.getAttribute('data-plan');
            handleSubscriptionClick(plan);
        });
    });
}

/**
 * Handle subscription card button clicks
 */
function handleSubscriptionClick(plan) {
    // Check if user is logged in (this would need to be implemented based on your auth system)
    const isLoggedIn = checkUserLoginStatus();
    
    if (isLoggedIn) {
        // Redirect to payments page for Paddle checkout
        window.location.href = `/payments?plan=${plan}`;
    } else {
        // Redirect to login page
        window.location.href = `/login?redirect=payments&plan=${plan}`;
    }
}

/**
 * Check if user is logged in (placeholder - implement based on your auth system)
 */
function checkUserLoginStatus() {
    // This would check localStorage, cookies, or make an API call
    // For now, return false to always redirect to login
    return false;
}

/**
 * Scroll to credits section (if it exists)
 */
function scrollToCredits() {
    const creditsSection = document.querySelector('[data-credits-section]') || 
                          document.querySelector('#credits') || 
                          document.querySelector('.credits-section');
    
    if (creditsSection) {
        creditsSection.scrollIntoView({ 
            behavior: 'smooth',
            block: 'start'
        });
    } else {
        // If no credits section found, scroll to payments view
        window.location.href = '/payments#credits';
    }
}

// Make scrollToCredits available globally for onclick handlers
window.scrollToCredits = scrollToCredits;

/**
 * Update pricing tables with dynamic data from Supabase
 * 
 * @param {Array} optimizationData - Array of optimization model pricing data 
 * @param {Array} transcriptionData - Array of transcription model pricing data
 */
 
function updatePricingTables(optimizationData, transcriptionData) {
    // Currently not used but will be implemented when Supabase is connected
    if (!optimizationData || !transcriptionData) {
        console.log('No pricing data available yet');
        return;
    }
    
    /* Example implementation:
    // Update optimization pricing table
    const optimizationTable = document.querySelector('#optimization-pricing table tbody');
    optimizationTable.innerHTML = '';
    
    optimizationData.forEach(item => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="pricing-model">${item.model_name}</td>
            <td class="pricing-cost">$${item.price.toFixed(2)}</td>
            <td class="unit-label">${item.unit}</td>
        `;
        optimizationTable.appendChild(row);
    });
    
    // Update transcription pricing table
    const transcriptionTable = document.querySelector('#transcription-pricing table tbody');
    transcriptionTable.innerHTML = '';
    
    transcriptionData.forEach(item => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="pricing-model">${item.model_name}</td>
            <td class="pricing-cost">$${item.price.toFixed(4)}</td>
            <td class="unit-label">${item.unit}</td>
        `;
        transcriptionTable.appendChild(row);
    });
    */
}

// Export functions for external use
window.VoiceHypePricing = {
    updatePricingTables
};

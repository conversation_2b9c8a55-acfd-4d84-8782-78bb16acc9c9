/* Mobile Responsiveness Fixes for VoiceHype
 * This file addresses specific mobile UI issues
 */

/* Prevent scrolling when mobile menu is open */
body.menu-open {
    overflow: hidden;
}

/* Fix vertical spacing between hero elements */
.hero-content {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

/* Adjust main title for better mobile display */
.main-title {
    margin-bottom: 0.75rem;
}

/* Quote section - ensure text wraps and is not scrollable */
.quote-section {
    overflow: hidden;
}

.quote-container {
    overflow: hidden;
    width: 100%;
    max-width: 100%;
}

.main-quote {
    overflow-wrap: break-word;
    word-break: normal;
    white-space: normal;
    overflow: visible;
    max-width: 100%;
}

@media (max-width: 768px) {
    .main-title {
        font-size: 3.5rem;
        margin-bottom: 0.5rem;
    }

    /* Fix spacing between heading components */
    .title-container {
        margin-bottom: 1rem;
    }

    /* Subtitle styles removed */

    /* Adjust CTA button for better spacing */
    .cta-button {
        margin-top: 1.5rem;
        margin-bottom: 2rem;
    }

    /* Fix mobile menu toggle appearance and functionality
    .mobile-menu-toggle {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        height: 24px;
        padding: 0;
        margin-left: 1rem;
        z-index: 1001;
        background: transparent;
        border: none;
        cursor: pointer;
    }
     */
    .bar {
        display: block;
        width: 25px;
        height: 3px;
        margin: 5px auto;
        background-color: var(--color-text);
        transition: all 0.3s ease;
    }

    .mobile-menu-toggle.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .mobile-menu-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .mobile-menu-toggle.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    /* Make navbar links fullwidth on mobile */
    .nav-links {
        position: fixed;
        top: var(--nav-height);
        left: -100%;
        flex-direction: column;
        gap: 0;
        width: 100%;
        background-color: rgba(5, 5, 5, 0.95);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        padding: 1rem 0;
        text-align: center;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        height: calc(100vh - var(--nav-height));
        overflow-y: auto;
        z-index: 999;
    }

    .nav-links.active {
        left: 0;
    }

    .nav-links li {
        width: 100%;
    }

    .nav-link {
        display: block;
        padding: 1rem 0;
    }

    /* Display nav buttons in mobile menu instead of hiding them */
    .nav-buttons {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;
        padding: 1.5rem 0;
        gap: 1rem;
        position: fixed;
        top: calc(var(--nav-height) + 4rem);
        left: -100%;
        margin-left: 0;
        transition: all 0.3s ease;
        background-color: transparent;
        z-index: 1000;
    }

    /* Show nav buttons when mobile menu is active */
    .mobile-menu-toggle.active ~ .nav-container .nav-buttons,
    .nav-links.active ~ .nav-buttons {
        left: 0;
        margin-left: 0;
    }

    /* Create a darker overlay when mobile menu is active */
    body.menu-open::after {
        content: "";
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.8);
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
        z-index: 998;
        pointer-events: none;
    }

    /* Style the buttons in mobile menu */
    .nav-buttons .nav-button {
        width: 80%;
        max-width: 250px;
        text-align: center;
        padding: 0.8rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        margin-left: 0;
    }

    /* Enhance visibility of login button */
    .nav-buttons .login-button {
        background-color: rgba(20, 20, 20, 0.8);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    /* Enhance visibility of signup button */
    .nav-buttons .signup-button {
        background-color: var(--color-primary);
        color: #111 !important;
        font-weight: 700 !important;
    }

    /* Fix quote section styling */
    .quote-section {
        padding: 3rem 1.5rem;
    }

    .main-quote {
        font-size: 1.5rem;
        padding: 0 1.5rem;
        text-align: left;
        position: relative;
    }

    .main-quote::before,
    .main-quote::after {
        position: absolute;
        font-size: 3rem;
    }

    .main-quote::before {
        top: -1rem;
        left: -0.5rem;
    }

    .main-quote::after {
        bottom: -2rem;
        right: -0.5rem;
    }

    /* Voice-to-prompt animation - fix arrow direction on mobile/tablet and adjust animation */
    .arrow-transformation {
        transform: rotate(90deg);
        margin: 1rem 0;
    }


    @keyframes pulseArrowMobile {
        0%, 100% {
            transform: translateY(-5px);
            opacity: 0.7;
        }
        50% {
            transform: translateY(5px);
            opacity: 1;
        }
    }

    /* Make voice-to-prompt animation more compact on tablets */
    .voice-to-prompt-animation {
        padding: 0 0.75rem;
        gap: 1rem;
    }

    .voice-input-text,
    .optimized-prompt-text {
        padding: 0.75rem;
        font-size: 0.9rem;
    }

    /* "Meet VoiceHype" section title fix for mobile */
    .intro-title.logo-style {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 3.5rem;
        line-height: 1.2;
        gap: 0.5rem;
        text-align: center;
    }

    .intro-title.logo-style .meet-text {
        display: block;
        text-align: center;
        width: 100%;
    }

    /* New style for the combined VoiceHype word */
    .intro-title.logo-style .voice-hype-text {
        display: inline-block;
        color: inherit;
    }

    /* Keep original styling for any cases where the separate spans are still used */
    .intro-title.logo-style .voice-text,
    .intro-title.logo-style .hype-text {
        display: inline-block;
    }

    .intro-title-container {
        padding: 1rem 0;
        text-align: center;
        align-items: center;
        justify-content: center;
    }

    .intro-tagline {
        text-align: left;
    }

    /* Platform logos section - fix for mobile display */
    .platform-logos-section {
        padding: 2rem 1rem;
    }

    .logos-carousel {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
        justify-content: center;
        align-items: center;
    }

    .logo-item {
        margin: 0 auto;
        width: 100%;
        max-width: 150px;
        height: 120px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .logo-item img {
        height: 80%;
        width: 80%;
    }

    .speed-benefits-title {
        text-align: center;
    }

    .speed-benefits-title::after {
        left: 30%;
    }

    /* "Meet VoiceHype" section title fix for mobile */
    .intro-title.logo-style {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 3.5rem;
        line-height: 1.2;
        gap: 0.5rem;
        text-align: center;
    }

    .intro-title.logo-style .meet-text {
        display: block;
        text-align: center;
        width: 100%;
    }

    .intro-title.logo-style .hype-text {
        display: inline;
    }

    .intro-title-container {
        padding: 1rem 0;
        text-align: center;
    }

    .intro-tagline {
        text-align: center;
        max-width: 100%;
    }

    /* "Boost Your Coding Speed" section fix for mobile */
    .speed-benefits-section {
        padding: 3rem 1.5rem;
    }

    .speed-benefits-container {
        padding: 1.5rem;
    }

    .speed-benefits-metrics {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
        align-items: flex-start;
    }

    .speed-benefits-description {
        text-align: left;
        padding: 1.5rem 1rem;
        max-width: 100%;
        margin: 0 auto;
    }

    /* Voice vs. Typing Cards fix for mobile */
    .voice-command-container {
        padding: 1.5rem;
        margin: 0 auto;
        width: 90%;
        max-width: 600px;
    }

    .voice-input,
    .optimized-code {
        margin: 1rem 0;
    }

    /* Keep Coding, Keep Eating section fix for mobile */
    .eating-coding-section {
        padding: 3rem 1.5rem;
    }

    .eating-coding-container {
        padding: 1.5rem;
    }

    .eating-coding-content {
        gap: 2rem;
    }

    .eating-coding-text {
        padding: 0 1rem;
        max-width: 100%;
    }

    .eating-coding-text p {
        margin: 1rem 0;
        text-align: center;
    }

    .eating-benefits-list li {
        margin-bottom: 0.75rem;
    }
}

/* Specific adjustments for small screens */
@media (max-width: 480px) {
    .top-section {
        padding-top: calc(var(--nav-height) + 1rem);
    }

    .hero-content {
        gap: 0.25rem;
        align-items: center;
        text-align: center;
    }

    .title-container {
        margin-bottom: 0.5rem;
    }

    .main-title {
        font-size: 2.8rem;
        margin-bottom: 0.25rem;
        gap: 0.5rem;
    }

    .title-line .highlight {
        padding-bottom: 15px;
        margin-bottom: 5px;
    }

    /* Subtitle styles removed */

    .cta-button {
        margin-top: 1rem;
        margin-bottom: 1.5rem;
    }

    .primary-button {
        font-size: 1rem;
        padding: 0.9rem 2rem;
    }

    /* Fix quote box to ensure it stays within viewport width */
    .quote-section {
        padding: 2rem 1rem;
        overflow: hidden;
    }

    .quote-container {
        width: 100%;
        overflow: hidden;
        max-width: 100%;
    }

    .main-quote {
        font-size: 1.2rem;
        line-height: 1.4;
        padding: 0 1rem;
        word-wrap: break-word;
        white-space: normal;
        overflow: visible;
        max-width: 100%;
    }

    .main-quote::before {
        top: -1.5rem;
        left: -0.5rem;
        font-size: 2.5rem;
    }

    .main-quote::after {
        bottom: -2.5rem;
        right: -0.5rem;
        font-size: 2.5rem;
    }

    .bar {
        width: 22px;
        height: 2px;
        margin: 4px auto;
    }

    /* Stack voice-to-prompt elements vertically on mobile and fix arrow direction */
    .voice-to-prompt-animation {
        flex-direction: column;
        gap: 1rem;
        margin-top: 1rem;
        padding: 0 0.5rem;
    }

    .voice-input-side,
    .optimized-prompt-side {
        width: 100%;
        max-width: 100%;
    }

    .arrow-transformation {
        transform: rotate(90deg);
        margin: 0.25rem 0;
    }

    /* Maintain VoiceHype as single word on small screens */
    .intro-title.logo-style .voice-text,
    .intro-title.logo-style .hype-text {
        display: inline-block;
    }

    .recording-indicator {
        margin-right: 0.5rem;
    }

    /* Fix navbar links when menu is open */
    .nav-links.active {
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
    }

    .nav-link {
        padding: 0.75rem 0;
    }

    /* "Meet VoiceHype" further adjustments for smaller screens */
    .intro-title.logo-style {
        font-size: 2.8rem;
    }

    .intro-title.logo-style .voice-text,
    .intro-title.logo-style .hype-text {
        display: inline;
    }

    /* Further adjustments for Boost Your Coding Speed section on smaller screens */
    .speed-benefits-section {
        padding: 2rem 1rem;
    }

    .speed-benefits-container {
        padding: 1rem;
    }

    .speed-benefits-metrics {
        gap: 1rem;
        align-items: flex-start;
    }

    .speed-benefits-description {
        padding: 1rem 0.5rem;
        text-align: left;
    }

    /* Further adjustments for Voice vs. Typing Cards on smaller screens */
    .voice-command-container {
        padding: 1rem;
        width: 95%;
    }

    .voice-input,
    .optimized-code {
        margin: 0.75rem 0;
    }

    .input-content,
    .output-content {
        padding: 0.75rem;
    }

    /* Further adjustments for Keep Coding, Keep Eating section on smaller screens */
    .eating-coding-section {
        padding: 2rem 1rem;
    }

    .eating-coding-container {
        padding: 1rem;
    }

    .eating-coding-text {
        padding: 0;
    }

    .eating-coding-text h3 {
        font-size: 1.75rem;
        margin-bottom: 1rem;
    }

    .eating-coding-text p {
        font-size: 0.95rem;
        line-height: 1.5;
        text-align: center;
        padding: 0 0.5rem;
    }

    .eating-benefits-list li {
        font-size: 0.9rem;
        padding-left: 1.5rem;
    }
}

/* Enhanced styling for voice-to-prompt animation */
.voice-to-prompt-animation {
    max-width: 100%;
    padding: 0 1rem;
    overflow: visible;
}

.voice-input-side,
.optimized-prompt-side {
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.voice-input-text,
.optimized-prompt-text {
    padding: 1rem;
    font-size: 0.95rem;
    line-height: 1.5;
}

/* Fix for consistent section spacing */
.top-section,
.quote-section,
.platform-logos-section,
.intro-section,
.speed-benefits-section,
.mind-blown-section,
.features-overview-section,
.use-cases-section,
.blog-section,
.faq-section {
    padding-left: 1rem;
    padding-right: 1rem;
}

/* Center all content properly */
.title-container,
.cta-button,
.voice-to-prompt-animation,
.quote-container,
.intro-container,
.speed-benefits-container,
.mind-blown-container,
.features-overview-container,
.user-type-container,
.blog-container,
.faq-container {
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
}

@media (max-width: 480px) {
    /* Adjust section spacing for mobile */
    .top-section,
    .quote-section,
    .platform-logos-section,
    .intro-section,
    .speed-benefits-section,
    .mind-blown-section,
    .features-overview-section,
    .use-cases-section,
    .blog-section,
    .faq-section {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    /* Fix vertical spacing issues */
    .top-section {
        min-height: calc(100vh - var(--nav-height));
        display: flex;
        align-items: center;
        justify-content: center;
        padding-top: calc(var(--nav-height) + 1rem);
        padding-bottom: 2rem;
    }

    /* Fix content overflow issues */
    img, video, svg, canvas {
        max-width: 100%;
        height: auto;
    }

    /* Ensure proper text alignment */
    .title-container,
    .hero-content,
    .cta-button,
    .quote-container,
    .main-quote {
        text-align: center;
    }
}

/*
 * RESPONSIVE FIXES SUMMARY:
 * =========================
 *
 * 1. Navigation Bar - Fixed mobile menu to properly display login/signup buttons
 *    - Added proper positioning for nav buttons in mobile menu
 *    - Added toggle functionality in JavaScript
 *    - Fixed animation and transitions
 *
 * 2. Voice-to-Prompt Animation Arrow - Changed direction on mobile
 *    - Rotated arrow 90 degrees on mobile to match vertical stacking
 *
 * 3. Quote Section Text - Fixed formatting issues
 *    - Removed horizontal scrolling
 *    - Applied text justification
 *    - Fixed quotation mark positioning
 *
 * 4. Platform Logos Section - Improved mobile layout
 *    - Arranged logos in two rows on mobile
 *    - Added proper spacing and sizing
 *
 * 5. "Meet VoiceHype" Section - Fixed title overflow
 *    - Stacked "Meet" above "VoiceHype"
 *    - Reduced font size on mobile
 *
 * 6. "Boost Your Coding Speed" Section - Improved formatting
 *    - Added proper padding around the section
 *    - Stacked metrics vertically on mobile
 *    - Centered and justified paragraph text
 *
 * 7. Voice vs. Typing Cards - Reduced excessive margins
 *    - Decreased side margins to utilize more screen space
 *    - Improved padding and spacing
 *
 * 8. "Keep Coding, Keep Eating" Section - Fixed text margins
 *    - Reduced side margins for better text display
 *    - Improved text formatting and spacing
 *
 * 9. Footer Layout - Restructured for mobile
 *    - Stacked columns vertically on small screens
 *    - Reduced spacing between elements
 *    - Ensured all links are accessible
 */

/* Footer Layout Styles */

/* Desktop footer layout */
.footer-section {
    padding: 3rem 1.5rem 2rem;
}


.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;
}

.footer-logo-section {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 1rem;
    max-width: 300px;
}

.footer-logo {
    margin-bottom: 1rem;
    text-align: left;
}

.footer-tagline {
    text-align: left;
    margin: 0;
    max-width: 100%;
}

.footer-links {
    display: flex;
    justify-content: flex-end;
}

.footer-links-container {
    display: flex;
    gap: 4rem;
}

.footer-links-column {
    margin-bottom: 1.5rem;
    min-width: 150px;
}

.footer-links-column h4 {
    margin-bottom: 1rem;
    text-align: left;
    font-size: 1.2rem;
}

.footer-links-column ul {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    text-align: left;
}

.footer-links-column li {
    margin-bottom: 0.25rem;
}

.footer-links-column a {
    font-size: 0.95rem;
    transition: color 0.2s ease;
}

.footer-links-column a:hover {
    color: var(--color-primary);
}

.footer-bottom-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Tablet and Mobile footer styles */
@media (max-width: 768px) {
    .footer-section {
        padding: 3rem 1.5rem 2rem;
    }

    .footer-content {
        flex-direction: column;
        align-items: center;
        gap: 2rem;
    }

    .footer-logo-section {
        align-items: center;
        max-width: none;
        margin-bottom: 1.5rem;
    }

    .footer-logo {
        text-align: center;
    }

    .footer-tagline {
        text-align: center;
        margin: 0 auto;
        max-width: 80%;
    }

    .footer-links {
        margin-top: 1rem;
        width: 100%;
        justify-content: center;
    }

    .footer-links-container {
        justify-content: space-between;
        gap: 2rem;
    }

    .footer-links-column {
        flex: 1;
        min-width: unset;
    }

    .footer-bottom-container {
        flex-direction: column;
        gap: 1rem;
    }
}

/* Small screens footer styles */
@media (max-width: 480px) {
    .footer-section {
        padding: 2rem 1rem 1.5rem;
        align-items: center;
        justify-content: center;
    }

    .footer-links-container {
        flex-direction: row;
        gap: 1rem;
    }

    .footer-links-column {
        width: 48%;
        text-align: left;
    }

    .footer-links-column h4 {
        margin-bottom: 0.75rem;
        font-size: 1.1rem;
        text-align: left;
    }

    .footer-links-column ul {
        gap: 0.5rem;
        align-items: flex-start;
    }

    .footer-links-column li {
        margin-bottom: 0.25rem;
        text-align: left;
    }

    .footer-links-column a {
        font-size: 0.9rem;
    }

    .footer-tagline {
        font-size: 0.9rem;
    }

    .copyright, .contact-info {
        font-size: 0.8rem;
    }
}
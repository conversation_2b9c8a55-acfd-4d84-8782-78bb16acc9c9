// This script will help debug navigation issues by logging link clicks
document.addEventListener('DOMContentLoaded', () => {
  // Function to collect all links and their attributes
  const logAllLinks = () => {
    const links = document.querySelectorAll('a');
    console.log(`Found ${links.length} links on page:`);
    
    links.forEach((link, index) => {
      console.log(`Link ${index + 1}:
        - Text: ${link.innerText.trim() || '[No Text]'}
        - Href: ${link.getAttribute('href') || '[No href]'}
        - Classes: ${link.className || '[No classes]'}
        - ID: ${link.id || '[No ID]'}
      `);
    });
  };

  // Function to monitor all link clicks
  const monitorLinkClicks = () => {
    document.addEventListener('click', (e) => {
      const link = e.target.closest('a');
      if (link) {
        console.log(`Link clicked:
          - Text: ${link.innerText.trim() || '[No Text]'}
          - Href: ${link.getAttribute('href') || '[No href]'}
          - Classes: ${link.className || '[No classes]'}
          - ID: ${link.id || '[No ID]'}
        `);
      }
    });
  };

  // Log all links on page load
  logAllLinks();
  
  // Monitor link clicks
  monitorLinkClicks();
  
  console.log('Debug links script loaded and active');
}); 
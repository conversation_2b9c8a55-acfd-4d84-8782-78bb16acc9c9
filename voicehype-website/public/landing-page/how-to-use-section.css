/* How to Use Section Styles */
.how-to-use-section {
  background: linear-gradient(135deg, #0d1117 60%, #10101e 100%);
  padding: 5rem 0 4rem 0;
  color: var(--color-text);
  font-family: var(--font-sans);
  position: relative;
  z-index: 2;
}

.how-to-use-container {
  max-width: 800px;
  margin: 0 auto;
  background: rgba(20, 241, 149, 0.03);
  border-radius: 18px;
  box-shadow: 0 8px 32px rgba(20, 241, 149, 0.07);
  padding: 3rem 2rem 2.5rem 2rem;
  position: relative;
}

.how-to-use-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.how-to-use-title {
  font-family: var(--font-display);
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #fff;
}

.how-to-use-subtitle {
  color: var(--color-primary);
  font-size: 1.15rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.how-to-use-steps {
  display: flex;
  flex-direction: column;
  gap: 1.7rem;
  margin-bottom: 2.5rem;
}

.how-to-use-step {
  display: flex;
  align-items: flex-start;
  gap: 1.3rem;
  position: relative;
}

.step-icon {
  min-width: 44px;
  min-height: 44px;
  background: linear-gradient(135deg, var(--color-primary), #0AD6DF 80%);
  color: #080814;
  font-family: var(--font-display);
  font-size: 1.5rem;
  font-weight: 700;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 12px rgba(20, 241, 149, 0.15);
  border: 2.5px solid rgba(20, 241, 149, 0.25);
  margin-top: 2px;
  z-index: 1;
  transition: box-shadow 0.2s;
}

.how-to-use-step:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 22px;
  top: 44px;
  width: 3px;
  height: calc(100% - 44px);
  background: linear-gradient(180deg, var(--color-primary) 0%, #0AD6DF 100%);
  opacity: 0.18;
  border-radius: 2px;
  z-index: 0;
}

.step-content {
  flex: 1;
  padding-top: 2px;
}

.step-title {
  font-size: 1.18rem;
  font-weight: 600;
  color: #fff;
  font-family: var(--font-display);
  margin-bottom: 0.2rem;
}

.step-desc {
  font-size: 1.05rem;
  color: var(--color-text-muted);
  font-family: var(--font-sans);
}

.how-to-link {
  color: var(--color-primary);
  text-decoration: underline;
  transition: color 0.2s;
}
.how-to-link:hover {
  color: #0AD6DF;
}

.kbd {
  display: inline-block;
  background: #181c23;
  color: var(--color-primary);
  border-radius: 6px;
  padding: 2px 8px;
  font-size: 0.98em;
  font-family: var(--font-mono);
  margin: 0 2px;
  border: 1.5px solid rgba(20,241,149,0.18);
  box-shadow: 0 1px 4px rgba(20,241,149,0.07);
}

.how-to-use-illustration {
  margin-top: 2.5rem;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
  /* Placeholder for future illustration */
}

@media (max-width: 700px) {
  .how-to-use-container {
    padding: 2rem 0.7rem 1.5rem 0.7rem;
  }
  .how-to-use-title {
    font-size: 2rem;
  }
  .how-to-use-step {
    gap: 0.8rem;
  }
  .step-title {
    font-size: 1.05rem;
  }
  .step-desc {
    font-size: 0.97rem;
  }
}

@media (max-width: 480px) {
  .how-to-use-title {
    font-size: 1.3rem;
  }
  .how-to-use-header {
    margin-bottom: 1.2rem;
  }
  .how-to-use-steps {
    gap: 1.1rem;
  }
  .step-icon {
    min-width: 34px;
    min-height: 34px;
    font-size: 1.1rem;
  }
} 
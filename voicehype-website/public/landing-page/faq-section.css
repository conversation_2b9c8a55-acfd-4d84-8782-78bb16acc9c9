/* FAQ Section Styles */
.faq-section {
    padding: 6rem 0;
    background-color: var(--color-background);
    position: relative;
}

.faq-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 2rem;
}

.faq-title {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    font-weight: 700;
}

.faq-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
}

/* FAQ Item Styles */
.faq-item {
    background-color: rgba(255, 255, 255, 0.03);
    border-radius: 12px;
    overflow: hidden;
    transition: box-shadow 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.faq-item:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.faq-item.active {
    box-shadow: 0 4px 25px rgba(20, 241, 149, 0.15);
    border-color: rgba(20, 241, 149, 0.3);
}

/* FAQ Question Styles */
.faq-question {
    padding: 1.25rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    position: relative;
    user-select: none;
}

.question-text {
    font-weight: 600;
    font-size: 1.15rem;
    color: var(--color-text);
}

.question-icon {
    flex-shrink: 0;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 1rem;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.05);
    color: var(--color-primary);
    transition: transform 0.3s ease, background-color 0.3s ease;
}

.faq-item.active .question-icon {
    background-color: var(--color-primary);
    color: var(--color-background);
}

/* Icon for indicating open/closed state */
.question-icon svg {
    transition: transform 0.3s ease;
}

.faq-item.active .question-icon svg {
    transform: rotate(45deg);
}

/* FAQ Answer Styles */
.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease, opacity 0.3s ease;
    opacity: 0;
    padding: 0 1.5rem;
}

.faq-item.active .faq-answer {
    opacity: 1;
    padding: 0 1.5rem 1.5rem 1.5rem;
    max-height: 1000px; /* Large enough to contain any answer */
}

.faq-answer p {
    margin: 0;
    color: var(--color-text-muted);
    line-height: 1.6;
    font-size: 1rem;
}

/* Dark mode adjustments */
.dark .faq-item {
    background-color: rgba(255, 255, 255, 0.03);
    border-color: rgba(255, 255, 255, 0.08);
}

.dark .question-text {
    color: #e2e8f0;
}

.dark .faq-answer p {
    color: #94a3b8;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .faq-section {
        padding: 4rem 0;
    }
    
    .faq-title {
        font-size: 2rem;
        margin-bottom: 2rem;
    }
    
    .faq-question {
        padding: 1rem;
    }
    
    .question-text {
        font-size: 1rem;
    }
    
    .faq-item.active .faq-answer {
        padding: 0 1rem 1rem 1rem;
    }
}

/* Fix for transition */
.faq-item .faq-answer {
    padding-top: 0 !important;
    margin-top: 0 !important;
} 
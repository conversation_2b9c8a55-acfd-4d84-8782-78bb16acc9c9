hero/* Root variables */
:root {
    --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    --font-mono: 'JetBrains Mono', monospace;
    --font-instrument: 'Instrument', sans-serif;
    --font-display: 'Clash Display', sans-serif;
    --font-outfit: 'Outfit', sans-serif;
    --font-space: 'Space Grotesk', sans-serif;

    --color-background: #0d1117;
    --color-foreground: #161b22;
    --color-primary: #14F195;
    --color-primary-rgb: 20, 241, 149;
    --color-secondary: #0A84FF;
    --color-accent: #f472b6;
    --color-text: #c9d1d9;
    --color-text-muted: #8b949e;
    --color-border: #30363d;
    --color-code-bg: #21262d;
    --color-code-text: #f0f6fc;
    --color-card-bg: rgba(18, 21, 26, 0.8);
    --color-card-border: rgba(48, 54, 61, 0.5);

    --border-radius: 8px;
    --nav-height: 60px;

    /* Animation variables */
    --transition-fast: 0.2s;
    --transition-medium: 0.3s;
    --transition-slow: 0.5s;
    --cubic-smooth: cubic-bezier(0.4, 0, 0.2, 1);
    --cubic-in-out: cubic-bezier(0.65, 0.05, 0.36, 1);
}

/* Base styles */
html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-sans);
    background-color: var(--color-background);
    color: var(--color-text);
    line-height: 1.6;
    overflow-x: hidden;
    scroll-behavior: smooth;
    padding: 0;
    margin: 0;
    min-height: 100vh;
    transition: background-color 0.8s ease;
}

/* Smooth scroll animations for all elements */
.page-wrapper > * {
    animation: fadeInUp 0.6s var(--cubic-in-out) both;
    will-change: transform, opacity;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Add animation delay for staggered appearance */
.top-section {
    animation-delay: 0.1s;
}

.features-overview-section {
    animation-delay: 0.2s;
}

.use-cases-section {
    animation-delay: 0.3s;
}

/* Smooth animations for scrolling elements */
.use-case-card,
.main-title,
.subtitle,
.intro-cta-button {
    transition: transform var(--transition-medium) var(--cubic-in-out),
                opacity var(--transition-medium) var(--cubic-in-out),
                box-shadow var(--transition-medium) var(--cubic-in-out);
}

/* Navbar smooth transitions */
.navbar {
    transition: background-color var(--transition-medium) var(--cubic-smooth),
                box-shadow var(--transition-medium) var(--cubic-smooth);
}

.nav-link {
    transition: color var(--transition-fast) var(--cubic-smooth),
                opacity var(--transition-fast) var(--cubic-smooth);
}

/* Appear on scroll animations */
.appear-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.8s ease, transform 0.8s ease;
}

.appear-on-scroll.is-visible {
    opacity: 1;
    transform: translateY(0);
}

/* Additional animation variants */
.appear-on-scroll.slide-up {
    transform: translateY(50px);
}

.appear-on-scroll.slide-right {
    transform: translateX(-50px);
}

.appear-on-scroll.slide-left {
    transform: translateX(50px);
}

.appear-on-scroll.scale-in {
    transform: scale(0.8) translateY(20px);
}

.appear-on-scroll.fade-in {
    transform: translateY(0); /* Just fade, no movement */
}

/* When visible, reset all transforms */
.appear-on-scroll.is-visible {
    opacity: 1;
    transform: translateY(0) translateX(0) scale(1);
}

/* Section transitions */
.top-section,
.features-overview-section,
.speed-benefits-section,
.use-cases-section,
.platform-logos-section {
    transition: transform 0.6s ease-out, background-color 0.8s ease;
}

/* Active section highlighting */
.active-section {
    z-index: 2;
}

/* Parallax background effect */
.features-overview-section::before,
.intro-section::before,
.speed-benefits-section::before,
.use-cases-section::before {
    transition: transform 1s ease-out;
}

/* Smooth scroll behavior for the whole page */
html {
    scroll-behavior: smooth;
}

/* Active nav link styling */
.nav-link.active {
    color: var(--color-primary);
}

.nav-link.active::after {
    width: 100%;
    opacity: 1;
}

/* Scroll progress indicator */
.scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--color-primary), #7affcb);
    z-index: 1000;
    width: 0%;
    transition: width 0.2s ease-out;
}

/* Scroll to top button */
.scroll-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 45px;
    height: 45px;
    background-color: var(--color-primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.3s ease, transform 0.3s ease, background-color 0.3s ease;
    z-index: 999;
    box-shadow: 0 4px 12px rgba(20, 241, 149, 0.3);
}

.scroll-to-top.visible {
    opacity: 1;
    transform: translateY(0);
}

.scroll-to-top:hover {
    background-color: #0ad680;
}

.scroll-to-top svg {
    width: 20px;
    height: 20px;
    stroke: white;
    stroke-width: 2px;
    transition: transform 0.3s ease;
}

.scroll-to-top:hover svg {
    transform: translateY(-3px);
}

/* Section animations - staggered reveal */
.intro-feature-item,
.use-case-card,
.metric {
    transition-delay: calc(var(--index, 0) * 0.1s);
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideLeft {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideRight {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Background color transitions for full-page scrolling effect */
body {
    transition: background-color 0.8s ease;
}

.top-section.active-section ~ body {
    background-color: rgba(20, 241, 149, 0.05);
}

.intro-section.active-section ~ body {
    background-color: rgba(20, 241, 149, 0.02);
}

.features-overview-section.active-section ~ body {
    background-color: rgba(20, 241, 149, 0.03);
}

.speed-benefits-section.active-section ~ body {
    background-color: rgba(20, 241, 149, 0.04);
}

.use-cases-section.active-section ~ body {
    background-color: rgba(20, 241, 149, 0.03);
}

/* Add a subtle transition for all interactive elements */
a, button, .nav-link, .intro-feature-item, .use-case-card, .intro-cta-button {
    transition: all 0.3s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .scroll-to-top {
        width: 40px;
        height: 40px;
        bottom: 20px;
        right: 20px;
    }

    .scroll-to-top svg {
        width: 18px;
        height: 18px;
    }

    .appear-on-scroll {
        transform: translateY(20px); /* Less movement on mobile */
    }
}

@media (max-width: 480px) {
    .scroll-to-top {
        width: 35px;
        height: 35px;
        bottom: 15px;
        right: 15px;
    }

    .scroll-to-top svg {
        width: 16px;
        height: 16px;
    }
}

.page-wrapper {
    display: flex;
    flex-direction: column;
    width: 100%;
    position: relative;
    padding: 0;
    margin: 0;
    overflow-x: hidden;
}

/* Navigation bar */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: var(--nav-height);
    background-color: rgba(13, 17, 23, 0.95);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.4);
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(20, 241, 149, 0.2);
}

.navbar.scrolled {
    background-color: rgba(5, 5, 5, 0.98);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    height: var(--nav-height);
}

.logo-container {
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-container svg {
    filter: drop-shadow(0 0 3px rgba(20, 241, 149, 0.2));
}

.logo-icon {
    width: 30px;
    height: 30px;
    border-radius: 8px;
}

.logo-text {
    font-family: var(--font-display);
    font-size: 1.8rem;
    font-weight: 700;
    letter-spacing: -0.02em;
    color: #ffffff;
    display: flex;
    align-items: center;
    margin: 0;
    padding: 0;
    line-height: 1;
}

.logo-text .hype-text {
    color: var(--color-primary);
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2.5rem;
    margin: 0;
    padding: 0;
}

.nav-link {
    color: var(--color-light);
    text-decoration: none;
    position: relative;
    transition: color 0.3s ease;
}

.nav-buttons {
    display: flex;
    gap: 0.75rem;
    margin-left: 1rem;
}

.nav-button {
    padding: 0.5rem 1.5rem;
    font-size: 0.9rem;
    border-radius: 2rem;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.login-button {
    color: #14F195;
    border: 2px solid #14F195;
    background-color: transparent;
}

.login-button:hover {
    background-color: rgba(20, 241, 149, 0.1);
    transform: translateY(-2px);
}

.signup-button {
    background-color: #14F195;
    color: #000;
    border: 2px solid #14F195;
}

.signup-button:hover {
    transform: translateY(-2px);
    filter: brightness(1.1);
}

.nav-cta {
    padding: 0.35rem 0.8rem;
    font-size: 0.75rem;
    margin-left: 1rem;
    border-radius: 4px;
    font-weight: 500;
    line-height: 1;
}

.nav-cta:hover {
    transform: translateY(-2px);
}

.nav-link:hover {
    color: var(--color-text);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
    transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
}

.bar {
    display: block;
    width: 25px;
    height: 3px;
    margin: 5px auto;
    transition: all 0.3s ease;
    background-color: var(--color-text);
    border-radius: 2px;
}

/* Top Section - Code Animation */
.top-section {
    position: relative;
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding-top: var(--nav-height);
    background: #000;
    overflow: hidden;
}

#video-background {
    position: absolute;
    /* top: var(--nav-height); */
    left: 0;
    width: 100%;
    height: calc(100vh - var(--nav-height));
    object-fit: contain;
    z-index: 0;
    opacity: 1;
    transition: opacity 0.3s ease;
    filter: brightness(1.0);
}

/* Video styling */
.hero-video {
    position: absolute;
    top: 10px;
    left: 0;
    width: 100%;
    height: calc(100% - var(--navbar-height));
    object-fit: cover;
    z-index: -1;
    display: none; /* Hide by default */
}

/* Show video only on desktop */
@media screen and (min-width: 768px) {
    .hero-video {
        display: block;
    }
    
    /* Offset hero content when video is visible */
    .hero-content {
        margin-left: 20%;
    }
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right,
        rgba(5, 5, 5, 0.7) 0%,
        rgba(5, 5, 5, 0.5) 50%,
        rgba(5, 5, 5, 0.3) 100%);
    z-index: 1;
}

/* Hover areas to show animation */
.hover-area {
    position: relative;
    z-index: 10;
}

.hover-area:hover ~ #code-canvas,
.title-container:hover ~ #code-canvas,
.hero-content:hover #code-canvas {
    opacity: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    width: 90%;
    max-width: 1200px;
    padding: 2rem 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    margin: 0 auto;
}

.title-container {
    display: block;
    margin-top: 0 !important;
    position: relative;
    width: 100%;
    z-index: 10;
}

/* Enhanced voice wave animation around the title - REMOVED */
.title-container::before {
    display: none;
}

.title-container::after {
    display: none;
}

/* Voice indicator animation - REMOVED */
.title-line .highlight::before {
    display: none;
}

/* Advanced voice wave pattern below highlight - REMOVED */
.title-line .highlight::after {
    display: none;
}

/* Enhanced microphone icon - REMOVED */
.voice-icon {
    display: none;
}

/* Add circles around voice icon - REMOVED */
.voice-icon::before,
.voice-icon::after {
    display: none;
}

/* Enhance voice visualization canvas - REMOVED */
.voice-visualization {
    display: none;
}

.main-title {
    font-family: var(--font-display);
    font-size: 6rem;
    font-weight: 800;
    line-height: 0.9;
    margin: 0 0 2rem;
    text-align: center;
    text-transform: none;
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
    letter-spacing: -0.02em;
    position: relative;
    width: 100%;
    align-items: center;
    justify-content: center;
    text-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
    color: #ffffff;
}

.title-line {
    display: block;
    position: relative;
}

.title-line:first-child {
    font-weight: 700;
    color: #ffffff;
    font-size: 1.1em;
    text-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.title-line .highlight {
    color: var(--color-primary);
    -webkit-text-fill-color: var(--color-primary);
    font-weight: 800;
    font-size: 1.65em;
    position: relative;
    display: inline-block;
    background: none;
    -webkit-background-clip: initial;
    background-clip: initial;
    text-fill-color: initial;
    filter: drop-shadow(0 0 20px rgba(20, 241, 149, 0.3));
    padding-bottom: 20px;
    margin-bottom: 10px;
}

/* Voice indicator animation - enhanced */
.title-line .highlight::before {
    content: '';
    position: absolute;
    width: 115%;
    height: 10px;
    bottom: -10px;
    left: -7.5%;
    background: linear-gradient(90deg, transparent, var(--color-primary) 50%, transparent);
    opacity: 0.6;
    border-radius: 50%;
    filter: blur(5px);
    animation: voiceWave 3s ease-in-out infinite;
}

/* Add advanced voice wave pattern below highlight */
.title-line .highlight::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 20px;
    bottom: -20px;
    left: 0;
    background-image:
        linear-gradient(90deg,
            transparent 0%, var(--color-primary) 10%,
            transparent 20%, var(--color-secondary) 30%,
            transparent 40%, var(--color-primary) 50%,
            transparent 60%, var(--color-secondary) 70%,
            transparent 80%, var(--color-primary) 90%,
            transparent 100%);
    background-size: 200% 100%;
    opacity: 0.2;
    filter: blur(3px);
    animation: moveGradient 8s linear infinite;
    clip-path: polygon(
        0% 0%, 10% 30%, 20% 0%, 30% 60%, 40% 30%,
        50% 70%, 60% 20%, 70% 50%, 80% 10%, 90% 40%, 100% 0%
    );
}

/* Subtitle styles removed */

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* Hide floating prompt bubbles classes since we removed them */
.floating-prompt-bubbles,
.prompt-bubble,
.voice-recording-animation,
.voice-wave,
.bubble-content {
    display: none;
}

.cta-button {
    margin-top: 2.5rem;
    margin-bottom: 3rem;
}

.primary-button {
    background: var(--color-primary);
    color: #000;
    font-family: var(--font-display);
    font-weight: 600;
    font-size: 1.2rem;
    padding: 1.1rem 2.6rem;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2), 0 0 0 0 rgba(20, 241, 149, 0.5);
    transition: all 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
    position: relative;
    overflow: hidden;
    outline: none;
    z-index: 10;
}

.primary-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.8s ease;
    z-index: -1;
}

.primary-button:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(20, 241, 149, 0.4);
    letter-spacing: 0.05em;
}

.primary-button:hover::before {
    left: 100%;
}

.primary-button:active {
    transform: translateY(0);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2), 0 0 10px rgba(20, 241, 149, 0.3);
}

/* IDE Extensions Section - HIDDEN */
.ide-extensions {
    display: none;
}

/* Voice-to-Prompt Animation */
.voice-to-prompt-animation {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1.5rem;
    margin: 0 auto;
    width: 100%;
    max-width: 900px;
    padding: 2rem 1rem;
    position: relative;
    z-index: 3;
}

.voice-input-side, .optimized-prompt-side {
    flex: 1;
    background: rgba(15, 15, 25, 0.7);
    border-radius: 12px;
    padding: 1.5rem;
    min-height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all 0.3s ease;
}

.voice-input-side {
    border: 1px solid rgba(153, 69, 255, 0.3);
    box-shadow: 0 5px 15px rgba(153, 69, 255, 0.15);
}

.optimized-prompt-side {
    border: 1px solid rgba(20, 241, 149, 0.3);
    box-shadow: 0 5px 15px rgba(20, 241, 149, 0.15);
    opacity: 0;
    animation: fadeIn 0.5s ease-out forwards 1.5s;
}

.recording-indicator {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
}

.voice-wave-animation {
        display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    height: 30px;
}

.voice-wave-animation span {
    display: inline-block;
    width: 3px;
    height: 5px;
    background-color: var(--color-secondary);
    border-radius: 3px;
    animation: voiceWaveAnim 1.3s ease-in-out infinite;
}

.voice-wave-animation span:nth-child(1) {
    animation-delay: 0s;
}

.voice-wave-animation span:nth-child(2) {
    animation-delay: 0.2s;
}

.voice-wave-animation span:nth-child(3) {
    animation-delay: 0.4s;
}

.voice-wave-animation span:nth-child(4) {
    animation-delay: 0.2s;
}

.voice-wave-animation span:nth-child(5) {
    animation-delay: 0s;
}

.voice-input-text {
    font-family: var(--font-sans);
    font-size: 1.1rem;
    color: var(--color-text);
    text-align: center;
    line-height: 1.4;
    position: relative;
    opacity: 0;
    animation: typeIn 1s ease-out forwards 0.5s;
}

.optimized-prompt-text {
    font-family: var(--font-mono);
    font-size: 1rem;
    color: var(--color-primary);
    text-align: center;
    line-height: 1.5;
    font-weight: 500;
}

.arrow-transformation {
        display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-primary);
    animation: pulseArrow 2s ease-in-out infinite;
}

.arrow-transformation svg {
    width: 40px;
    height: 40px;
    stroke: var(--color-primary);
}

@keyframes voiceWaveAnim {
    0%, 100% {
        height: 5px;
    }
    50% {
        height: 25px;
    }
}

@keyframes pulseArrow {
    0%, 100% {
        transform: scale(1) translateX(0);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.1) translateX(5px);
        opacity: 1;
    }
}

@keyframes typeIn {
    0% {
        opacity: 0;
        transform: translateY(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Media queries for voice-to-prompt animation */
@media (max-width: 768px) {
    .voice-to-prompt-animation {
        flex-direction: column;
        gap: 1.5rem;
        margin: 2rem auto;
    }

    .arrow-transformation {
        transform: rotate(90deg);
        margin: 0.5rem 0;
    }

    .voice-input-side, .optimized-prompt-side {
        width: 100%;
        padding: 1.2rem;
    }

    .voice-input-text, .optimized-prompt-text {
        font-size: 0.95rem;
    }
}

@media (max-width: 480px) {
    .voice-to-prompt-animation {
        margin-top: 1.5rem;
    }

    .voice-input-side, .optimized-prompt-side {
        padding: 1rem;
    }

    .voice-input-text, .optimized-prompt-text {
        font-size: 0.9rem;
    }

    .arrow-transformation svg {
    width: 30px;
        height: 30px;
    }
}

/* Platform Logos Carousel */
.platform-logos-section {
    padding: 2rem 0;
    background: rgba(15, 15, 20, 0.95);
    position: relative;
    overflow: hidden;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.logos-carousel-container {
    max-width: 1000px;
    margin: 0 auto;
    position: relative;
    padding: 1rem 0;
}

.logos-carousel {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 3rem;
    flex-wrap: nowrap;
    padding: 0;
}

.logo-item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    transition: all 0.4s ease;
    filter: grayscale(0%);
    background: transparent;
    border-radius: 0;
    box-shadow: none;
    border: none;
    width: auto;
    height: auto;
    margin: 0;
}

.logo-item img {
    height: 4.2rem;
    width: auto;
    object-fit: contain;
    transition: all 0.4s ease;
    filter: brightness(0.95);
    margin: 0;
}

.logo-item:hover img {
    transform: scale(1.1);
    filter: brightness(1);
}

.continue-logo, .warp-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 4.2rem;
    width: 4.2rem;
    transition: all 0.3s ease;
}

.logo-item:hover .continue-logo,
.logo-item:hover .warp-logo {
    transform: scale(1.1);
    filter: brightness(1.2);
}

.continue-logo svg, .warp-logo svg {
    width: 100%;
    height: 100%;
}

.logo-item span {
    display: none;
}

.platform-logos-title {
    display: none;
}

@media (max-width: 768px) {
    .platform-logos-section {
        padding: 1.5rem 0;
    }

    .logos-carousel {
        gap: 1.2rem;
    }

    .logo-item img, .continue-logo, .warp-logo, .windsurf-logo {
        height: 3.5rem;
        width: 3.5rem;
    }

    .continue-logo, .warp-logo, .windsurf-logo {
        height: 3.5rem;
        width: 3.5rem;
    }
}

@media (max-width: 480px) {
    .platform-logos-section {
        padding: 1rem 0;
    }

    .logos-carousel {
        gap: 0.8rem;
    }

    .logo-item img, .continue-logo, .warp-logo, .windsurf-logo {
        height: 2.5rem;
        width: 2.5rem;
    }

    .continue-logo, .warp-logo, .windsurf-logo {
        height: 2.5rem;
        width: 2.5rem;
    }
}

/* Transformation Section */
.transformation-section {
    position: relative;
    min-height: 100vh;
    background: linear-gradient(to bottom, var(--color-background) 0%, var(--color-background-light) 100%);
    padding: 8rem 0;
        display: flex;
    flex-direction: column;
    align-items: center;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
    width: 90%;
    /* max-width: 1000px; */
}

.section-header h2 {
    font-family: var(--font-display);
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(90deg, var(--color-primary), var(--color-tertiary));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-fill-color: transparent;
}

.section-header p {
    font-size: 1.25rem;
    color: var(--color-text-dim);
    max-width: 800px;
    margin: 0 auto;
}

.transformation-container {
    width: 90%;
    max-width: 1200px;
        display: flex;
        flex-direction: column;
    gap: 3rem;
}

.transformation-stages {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    width: 100%;
}

@media (min-width: 768px) {
    .transformation-stages {
    flex-direction: row;
        align-items: stretch;
    }
}

.stage {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: rgba(10, 10, 10, 0.6);
    border-radius: 16px;
    overflow: hidden;
    border: 1px solid rgba(50, 50, 50, 0.5);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    min-height: 300px;
}

.stage:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.stage-human:hover {
    border-color: rgba(0, 194, 255, 0.4);
}

.stage-processing:hover {
    border-color: rgba(153, 69, 255, 0.4);
}

.stage-optimized:hover {
    border-color: rgba(20, 241, 149, 0.4);
}

.stage-label {
    padding: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.9rem;
    border-bottom: 1px solid rgba(50, 50, 50, 0.5);
}

.stage-human .stage-label {
    color: var(--color-tertiary);
}

.stage-processing .stage-label {
    color: var(--color-secondary);
}

.stage-optimized .stage-label {
    color: var(--color-primary);
}

.stage-content {
    flex: 1;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 220px;
}

.human-request-text {
    font-family: var(--font-sans);
    font-size: 1.2rem;
    color: var(--color-text);
    text-align: center;
        opacity: 0;
    animation: fadeIn 0.5s ease-out 0.2s forwards;
}

.wave-container {
    width: 100%;
    height: 150px;
    position: relative;
}

#transformCanvas {
    width: 100%;
    height: 100%;
    position: absolute;
        top: 0;
    left: 0;
}

.processing-indicator {
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 6px;
}

.dot {
    width: 8px;
    height: 8px;
    background-color: var(--color-secondary);
    border-radius: 50%;
    display: inline-block;
    opacity: 0;
}

.dot:nth-child(1) {
    animation: dotPulse 1.5s ease-in-out 0s infinite;
}

.dot:nth-child(2) {
    animation: dotPulse 1.5s ease-in-out 0.3s infinite;
}

.dot:nth-child(3) {
    animation: dotPulse 1.5s ease-in-out 0.6s infinite;
}

.optimized-prompt {
    font-family: var(--font-mono);
    font-size: 0.9rem;
    line-height: 1.5;
    color: var(--color-text);
    white-space: pre-wrap;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 1.2rem;
    width: 100%;
    height: 100%;
    overflow-y: auto;
        opacity: 0;
    animation: fadeIn 0.5s ease-out 3s forwards;
}

.transformation-examples {
    display: flex;
    justify-content: center;
    margin-top: 2.5rem;
    width: 100%;
}

.example-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
}

.example-button {
    background-color: rgba(10, 10, 10, 0.6);
    border: 1px solid rgba(50, 50, 50, 0.5);
    color: var(--color-text-dim);
    font-family: var(--font-sans);
    font-size: 0.9rem;
    padding: 0.6rem 1.2rem;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.example-button:hover {
    background-color: rgba(20, 20, 20, 0.8);
    color: var(--color-text);
    border-color: rgba(100, 100, 100, 0.5);
}

.example-button.active {
    background: linear-gradient(135deg, var(--color-secondary), var(--color-primary));
    color: white;
    border-color: transparent;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

@keyframes dotPulse {
    0% { opacity: 0; transform: scale(0.8); }
    50% { opacity: 1; transform: scale(1.2); }
    100% { opacity: 0; transform: scale(0.8); }
}

@keyframes typewriterTransform {
    0% { width: 0; }
    100% { width: 100%; }
}

/* Bottom Section - Sound Waves */
.bottom-section {
    position: relative;
    min-height: 100vh;
    background-color: var(--color-background-light);
    padding: 6rem 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.wave-animation-container {
    position: relative;
    width: 100%;
    height: 550px;
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
}

#wave-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}

.voice-command-container {
    position: relative;
    width: 90%;
    max-width: 900px;
    background: rgba(28, 28, 40, 0.8);
    border: 1px solid rgba(20, 241, 149, 0.2);
    border-radius: 16px;
    padding: 2.5rem;
    backdrop-filter: blur(8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    z-index: 1;
    display: flex;
    flex-direction: column;
    gap: 2.5rem;
}

.voice-input,
.voice-output {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.input-label,
.output-label {
    font-family: 'Outfit', var(--font-sans);
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--color-primary);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.input-content {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: rgba(0, 0, 0, 0.4);
    padding: 1rem;
    border-radius: 12px;
    border: 1px solid rgba(155, 53, 255, 0.2);
}

.mic-icon {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-primary);
    animation: pulse 2s infinite;
}

.input-text {
    font-family: var(--font-mono);
    font-size: 1rem;
    color: var(--color-text);
}

.typing-animation {
    border-right: 2px solid var(--color-primary);
    white-space: nowrap;
    overflow: hidden;
    animation: typeText 3s steps(50, end) forwards, blinkCursor 0.7s step-end infinite;
}

.output-content {
    background: rgba(0, 0, 0, 0.4);
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid rgba(0, 230, 118, 0.3);
}

.optimized-code {
    margin: 0;
    font-family: var(--font-mono);
    font-size: 0.9rem;
    line-height: 1.5;
    color: var(--color-text);
    white-space: pre-wrap;
}

.optimized-code code {
    opacity: 0;
    animation: fadeIn 1s ease-out 3s forwards;
}

/* Features Section */
.features-section {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 3rem;
    max-width: 1200px;
    width: 90%;
    margin: 5rem auto 3rem;
    padding: 0;
}

.feature-card {
    flex: 1;
    min-width: 250px;
    max-width: 350px;
    background: rgba(28, 28, 40, 0.8);
    border: 1px solid rgba(153, 69, 255, 0.2);
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border-color: rgba(153, 69, 255, 0.4);
}

.feature-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.feature-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--color-primary);
}

.feature-card p {
    font-size: 1rem;
    color: var(--color-text-dim);
}

/* Cursor Interface Section */
.cursor-interface-section {
    position: relative;
    min-height: 100vh;
    background: linear-gradient(to bottom, var(--color-background-light) 0%, var(--color-background) 100%);
    padding: 6rem 0;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.cursor-interface-container {
    width: 90%;
    max-width: 1200px;
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-bottom: 3rem;
}

@media (min-width: 992px) {
    .cursor-interface-container {
        flex-direction: row;
        align-items: stretch;
    }
}
/* Root variables */
:root {
    --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    --font-mono: 'JetBrains Mono', monospace;
    --font-instrument: 'Instrument', sans-serif;
    --font-display: 'Clash Display', sans-serif;
    --font-outfit: 'Outfit', sans-serif;
    --font-space: 'Space Grotesk', sans-serif;

    --color-background: #0d1117;
    --color-foreground: #161b22;
    --color-primary: #14F195;
    --color-primary-rgb: 20, 241, 149;
    --color-secondary: #0A84FF;
    --color-accent: #f472b6;
    --color-text: #c9d1d9;
    --color-text-muted: #8b949e;
    --color-border: #30363d;
    --color-code-bg: #21262d;
    --color-code-text: #f0f6fc;
    --color-card-bg: rgba(18, 21, 26, 0.8);
    --color-card-border: rgba(48, 54, 61, 0.5);

    --border-radius: 8px;
    --nav-height: 60px;

    /* Animation variables */
    --transition-fast: 0.2s;
    --transition-medium: 0.3s;
    --transition-slow: 0.5s;
    --cubic-smooth: cubic-bezier(0.4, 0, 0.2, 1);
    --cubic-in-out: cubic-bezier(0.65, 0.05, 0.36, 1);
}

/* Base styles */
html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-sans);
    background-color: var(--color-background);
    color: var(--color-text);
    line-height: 1.6;
    overflow-x: hidden;
    scroll-behavior: smooth;
    padding: 0;
    margin: 0;
    min-height: 100vh;
    transition: background-color 0.8s ease;
}

/* Smooth scroll animations for all elements */
.page-wrapper > * {
    animation: fadeInUp 0.6s var(--cubic-in-out) both;
    will-change: transform, opacity;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Add animation delay for staggered appearance */
.top-section {
    animation-delay: 0.1s;
}

.features-overview-section {
    animation-delay: 0.2s;
}

.use-cases-section {
    animation-delay: 0.3s;
}

/* Smooth animations for scrolling elements */
.intro-feature-item,
.use-case-card,
.main-title,
.subtitle,
.intro-cta-button {
    transition: transform var(--transition-medium) var(--cubic-in-out),
                opacity var(--transition-medium) var(--cubic-in-out),
                box-shadow var(--transition-medium) var(--cubic-in-out);
}

/* Navbar smooth transitions */
.navbar {
    transition: background-color var(--transition-medium) var(--cubic-smooth),
                box-shadow var(--transition-medium) var(--cubic-smooth);
}

.nav-link {
    transition: color var(--transition-fast) var(--cubic-smooth),
                opacity var(--transition-fast) var(--cubic-smooth);
}

/* Appear on scroll animations */
.appear-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.8s ease, transform 0.8s ease;
}

.appear-on-scroll.is-visible {
    opacity: 1;
    transform: translateY(0);
}

/* Additional animation variants */
.appear-on-scroll.slide-up {
    transform: translateY(50px);
}

.appear-on-scroll.slide-right {
    transform: translateX(-50px);
}

.appear-on-scroll.slide-left {
    transform: translateX(50px);
}

.appear-on-scroll.scale-in {
    transform: scale(0.8) translateY(20px);
}

.appear-on-scroll.fade-in {
    transform: translateY(0); /* Just fade, no movement */
}

/* When visible, reset all transforms */
.appear-on-scroll.is-visible {
    opacity: 1;
    transform: translateY(0) translateX(0) scale(1);
}

/* Section transitions */
.top-section,
.features-overview-section,
.intro-section,
.speed-benefits-section,
.use-cases-section,
.platform-logos-section {
    transition: transform 0.6s ease-out, background-color 0.8s ease;
}

/* Active section highlighting */
.active-section {
    z-index: 2;
}

/* Parallax background effect */
.features-overview-section::before,
.intro-section::before,
.speed-benefits-section::before,
.use-cases-section::before {
    transition: transform 1s ease-out;
}

/* Smooth scroll behavior for the whole page */
html {
    scroll-behavior: smooth;
}

/* Active nav link styling */
.nav-link.active {
    color: var(--color-primary);
}

.nav-link.active::after {
    width: 100%;
    opacity: 1;
}

/* Scroll progress indicator */
.scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--color-primary), #7affcb);
    z-index: 1000;
    width: 0%;
    transition: width 0.2s ease-out;
}

/* Scroll to top button */
.scroll-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 45px;
    height: 45px;
    background-color: var(--color-primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.3s ease, transform 0.3s ease, background-color 0.3s ease;
    z-index: 999;
    box-shadow: 0 4px 12px rgba(20, 241, 149, 0.3);
}

.scroll-to-top.visible {
    opacity: 1;
    transform: translateY(0);
}

.scroll-to-top:hover {
    background-color: #0ad680;
}

.scroll-to-top svg {
    width: 20px;
    height: 20px;
    stroke: white;
    stroke-width: 2px;
    transition: transform 0.3s ease;
}

.scroll-to-top:hover svg {
    transform: translateY(-3px);
}

/* Section animations - staggered reveal */
.intro-feature-item,
.use-case-card,
.metric {
    transition-delay: calc(var(--index, 0) * 0.1s);
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideLeft {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideRight {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Background color transitions for full-page scrolling effect */
body {
    transition: background-color 0.8s ease;
}

.top-section.active-section ~ body {
    background-color: rgba(20, 241, 149, 0.05);
}

.intro-section.active-section ~ body {
    background-color: rgba(20, 241, 149, 0.02);
}

.features-overview-section.active-section ~ body {
    background-color: rgba(20, 241, 149, 0.03);
}

.speed-benefits-section.active-section ~ body {
    background-color: rgba(20, 241, 149, 0.04);
}

.use-cases-section.active-section ~ body {
    background-color: rgba(20, 241, 149, 0.03);
}

/* Add a subtle transition for all interactive elements */
a, button, .nav-link, .intro-feature-item, .use-case-card, .intro-cta-button {
    transition: all 0.3s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .scroll-to-top {
        width: 40px;
        height: 40px;
        bottom: 20px;
        right: 20px;
    }

    .scroll-to-top svg {
        width: 18px;
        height: 18px;
    }

    .appear-on-scroll {
        transform: translateY(20px); /* Less movement on mobile */
    }
}

@media (max-width: 480px) {
    .scroll-to-top {
        width: 35px;
        height: 35px;
        bottom: 15px;
        right: 15px;
    }

    .scroll-to-top svg {
        width: 16px;
        height: 16px;
    }
}

.page-wrapper {
    display: flex;
    flex-direction: column;
    width: 100%;
    position: relative;
    padding: 0;
    margin: 0;
    overflow-x: hidden;
}

/* Navigation bar */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: var(--nav-height);
    background-color: rgba(13, 17, 23, 0.95);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.4);
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(20, 241, 149, 0.2);
}

.navbar.scrolled {
    background-color: rgba(5, 5, 5, 0.98);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    height: var(--nav-height);
}

.logo-container {
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-container svg {
    filter: drop-shadow(0 0 3px rgba(20, 241, 149, 0.2));
}

.logo-icon {
    width: 30px;
    height: 30px;
    border-radius: 8px;
}

.logo-text {
    font-family: var(--font-display);
    font-size: 1.8rem;
    font-weight: 700;
    letter-spacing: -0.02em;
    color: #ffffff;
    display: flex;
    align-items: center;
    margin: 0;
    padding: 0;
    line-height: 1;
}

.logo-text .hype-text {
    color: var(--color-primary);
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2.5rem;
    margin: 0;
    padding: 0;
}

.nav-link {
    color: var(--color-light);
    text-decoration: none;
    position: relative;
    transition: color 0.3s ease;
}

.nav-buttons {
    display: flex;
    gap: 0.75rem;
    margin-left: 1rem;
}

.nav-button {
    padding: 0.5rem 1.5rem;
    font-size: 0.9rem;
    border-radius: 2rem;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.login-button {
    color: #14F195;
    border: 2px solid #14F195;
    background-color: transparent;
}

.login-button:hover {
    background-color: rgba(20, 241, 149, 0.1);
    transform: translateY(-2px);
}

.signup-button {
    background-color: #14F195;
    color: #000;
    border: 2px solid #14F195;
}

.signup-button:hover {
    transform: translateY(-2px);
    filter: brightness(1.1);
}

.nav-cta {
    padding: 0.35rem 0.8rem;
    font-size: 0.75rem;
    margin-left: 1rem;
    border-radius: 4px;
    font-weight: 500;
    line-height: 1;
}

.nav-cta:hover {
    transform: translateY(-2px);
}

.nav-link:hover {
    color: var(--color-text);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
    transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
}

.bar {
    display: block;
    width: 25px;
    height: 3px;
    margin: 5px auto;
    transition: all 0.3s ease;
    background-color: var(--color-text);
    border-radius: 2px;
}

/* Top Section - Code Animation */
.top-section {
    position: relative;
    width: 100%;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: calc(var(--nav-height) + 4rem) 1rem 5rem;
    background: radial-gradient(circle at 50% 50%, rgba(20, 241, 149, 0.02) 0%, rgba(15, 15, 25, 0) 50%);
    overflow: visible;
}

#code-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.code-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom,
        rgba(5, 5, 5, 0.4) 0%,
        rgba(5, 5, 5, 0.6) 50%,
        rgba(5, 5, 5, 0.9) 100%);
    z-index: 1;
}

/* Hover areas to show animation */
.hover-area {
    position: relative;
    z-index: 10;
}

.hover-area:hover ~ #code-canvas,
.title-container:hover ~ #code-canvas,
.hero-content:hover #code-canvas {
    opacity: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    width: 90%;
    max-width: 1200px;
    padding: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    margin: 0 auto;
}

.title-container {
    display: block;
    margin-top: 0 !important;
    position: relative;
    width: 100%;
    z-index: 10;
}

/* Enhanced voice wave animation around the title - REMOVED */
.title-container::before {
    display: none;
}

.title-container::after {
    display: none;
}

/* Voice indicator animation - REMOVED */
.title-line .highlight::before {
    display: none;
}

/* Advanced voice wave pattern below highlight - REMOVED */
.title-line .highlight::after {
    display: none;
}

/* Enhanced microphone icon - REMOVED */
.voice-icon {
    display: none;
}

/* Add circles around voice icon - REMOVED */
.voice-icon::before,
.voice-icon::after {
    display: none;
}

/* Enhance voice visualization canvas - REMOVED */
.voice-visualization {
    display: none;
}

.main-title {
    font-family: var(--font-display);
    font-size: 6rem;
    font-weight: 800;
    line-height: 0.9;
    margin: 0 0 2rem;
    text-align: center;
    text-transform: none;
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
    letter-spacing: -0.02em;
    position: relative;
    width: 100%;
    align-items: center;
    justify-content: center;
    text-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
    color: #ffffff;
}

.title-line {
    display: block;
    position: relative;
}

.title-line:first-child {
    font-weight: 700;
    color: #ffffff;
    text-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.title-line .highlight {
    color: var(--color-primary);
    -webkit-text-fill-color: var(--color-primary);
    font-weight: 800;
    font-family: var(--font-display);
    font-size: 1.1em;
    position: relative;
    display: inline-block;
    background: none;
    -webkit-background-clip: initial;
    background-clip: initial;
    text-fill-color: initial;
    filter: drop-shadow(0 0 20px rgba(20, 241, 149, 0.3));
    padding-bottom: 20px;
    margin-bottom: 10px;
}

/* Voice indicator animation - enhanced */
.title-line .highlight::before {
    content: '';
    position: absolute;
    width: 115%;
    height: 10px;
    bottom: -10px;
    left: -7.5%;
    background: linear-gradient(90deg, transparent, var(--color-primary) 50%, transparent);
    opacity: 0.6;
    border-radius: 50%;
    filter: blur(5px);
    animation: voiceWave 3s ease-in-out infinite;
}

/* Add advanced voice wave pattern below highlight */
.title-line .highlight::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 20px;
    bottom: -20px;
    left: 0;
    background-image:
        linear-gradient(90deg,
            transparent 0%, var(--color-primary) 10%,
            transparent 20%, var(--color-secondary) 30%,
            transparent 40%, var(--color-primary) 50%,
            transparent 60%, var(--color-secondary) 70%,
            transparent 80%, var(--color-primary) 90%,
            transparent 100%);
    background-size: 200% 100%;
    opacity: 0.2;
    filter: blur(3px);
    animation: moveGradient 8s linear infinite;
    clip-path: polygon(
        0% 0%, 10% 30%, 20% 0%, 30% 60%, 40% 30%,
        50% 70%, 60% 20%, 70% 50%, 80% 10%, 90% 40%, 100% 0%
    );
}

.subtitle {
    font-family: var(--font-display);
    font-size: 2.2rem; /* Increased size */
    font-weight: 600;
    color: var(--color-text);
    max-width: 90%;
    margin: 0 auto;
    margin-top: 1.5rem;
    letter-spacing: 0.05em; /* Increased letter spacing */
    word-spacing: 0.3em; /* Added word spacing */
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    border-right: 3px solid var(--color-primary);
    width: 0;
    animation: typing 1.2s steps(40, end) forwards 0.1s,
               blinkCursor 0.75s step-end infinite;
    /* Center the text */
    margin-left: auto;
    margin-right: auto;
}

.subtitle::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--color-primary), var(--color-secondary), transparent);
    opacity: 0.6;
}

@keyframes typing {
    from { width: 0 }
    /* Limit the final width to prevent cursor extending too far */
    to { width: 28ch }
}

@keyframes blinkCursor {
    from, to { border-color: transparent }
    50% { border-color: var(--color-primary) }
}

/* Hide floating prompt bubbles classes since we removed them */
.floating-prompt-bubbles,
.prompt-bubble,
.voice-recording-animation,
.voice-wave,
.bubble-content {
    display: none;
}

.cta-button {
    margin-top: 2.5rem;
    margin-bottom: 3rem;
}

.primary-button {
    background: var(--color-primary);
    color: #000;
    font-family: var(--font-display);
    font-weight: 600;
    font-size: 1.2rem;
    padding: 1.1rem 2.6rem;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2), 0 0 0 0 rgba(20, 241, 149, 0.5);
    transition: all 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
    position: relative;
    overflow: hidden;
    outline: none;
    z-index: 10;
}

.primary-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.8s ease;
    z-index: -1;
}

.primary-button:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(20, 241, 149, 0.4);
    letter-spacing: 0.05em;
}

.primary-button:hover::before {
    left: 100%;
}

.primary-button:active {
    transform: translateY(0);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2), 0 0 10px rgba(20, 241, 149, 0.3);
}

/* IDE Extensions Section - HIDDEN */
.ide-extensions {
    display: none;
}

/* Voice-to-Prompt Animation */
.voice-to-prompt-animation {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1.5rem;
    margin-top: 2.5rem;
    width: 100%;
    max-width: 900px;
    padding: 1rem;
    position: relative;
}

.voice-input-side, .optimized-prompt-side {
    flex: 1;
    background: rgba(15, 15, 25, 0.7);
    border-radius: 12px;
    padding: 1.5rem;
    min-height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all 0.3s ease;
}

.voice-input-side {
    border: 1px solid rgba(153, 69, 255, 0.3);
    box-shadow: 0 5px 15px rgba(153, 69, 255, 0.15);
}

.optimized-prompt-side {
    border: 1px solid rgba(20, 241, 149, 0.3);
    box-shadow: 0 5px 15px rgba(20, 241, 149, 0.15);
    opacity: 0;
    animation: fadeIn 0.5s ease-out forwards 1.5s;
}

.recording-indicator {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
}

.voice-wave-animation {
        display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    height: 30px;
}

.voice-wave-animation span {
    display: inline-block;
    width: 3px;
    height: 5px;
    background-color: var(--color-secondary);
    border-radius: 3px;
    animation: voiceWaveAnim 1.3s ease-in-out infinite;
}

.voice-wave-animation span:nth-child(1) {
    animation-delay: 0s;
}

.voice-wave-animation span:nth-child(2) {
    animation-delay: 0.2s;
}

.voice-wave-animation span:nth-child(3) {
    animation-delay: 0.4s;
}

.voice-wave-animation span:nth-child(4) {
    animation-delay: 0.2s;
}

.voice-wave-animation span:nth-child(5) {
    animation-delay: 0s;
}

.voice-input-text {
    font-family: var(--font-sans);
    font-size: 1.1rem;
    color: var(--color-text);
    text-align: center;
    line-height: 1.4;
    position: relative;
    opacity: 0;
    animation: typeIn 1s ease-out forwards 0.5s;
}

.optimized-prompt-text {
    font-family: var(--font-mono);
    font-size: 1rem;
    color: var(--color-primary);
    text-align: center;
    line-height: 1.5;
    font-weight: 500;
}

.arrow-transformation {
        display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-primary);
    animation: pulseArrow 2s ease-in-out infinite;
}

.arrow-transformation svg {
    width: 40px;
    height: 40px;
    stroke: var(--color-primary);
}

@keyframes voiceWaveAnim {
    0%, 100% {
        height: 5px;
    }
    50% {
        height: 25px;
    }
}

@keyframes pulseArrow {
    0%, 100% {
        transform: scale(1) translateX(0);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.1) translateX(5px);
        opacity: 1;
    }
}

@keyframes typeIn {
    0% {
        opacity: 0;
        transform: translateY(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Media queries for voice-to-prompt animation */
@media (max-width: 768px) {
    .voice-to-prompt-animation {
        flex-direction: column;
        gap: 1rem;
        margin-top: 2rem;
    }

    .arrow-transformation {
        transform: rotate(90deg);
        margin: 0.5rem 0;
    }

    .voice-input-side, .optimized-prompt-side {
        width: 100%;
        padding: 1.2rem;
    }

    .voice-input-text, .optimized-prompt-text {
        font-size: 0.95rem;
    }
}

@media (max-width: 480px) {
    .voice-to-prompt-animation {
        margin-top: 1.5rem;
    }

    .voice-input-side, .optimized-prompt-side {
        padding: 1rem;
    }

    .voice-input-text, .optimized-prompt-text {
        font-size: 0.9rem;
    }

    .arrow-transformation svg {
    width: 30px;
        height: 30px;
    }
}

/* Platform Logos Carousel */
.platform-logos-section {
    padding: 2rem 0;
    background: rgba(15, 15, 20, 0.95);
    position: relative;
    overflow: hidden;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.logos-carousel-container {
    max-width: 1000px;
    margin: 0 auto;
    position: relative;
    padding: 1rem 0;
}

.logos-carousel {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 3rem;
    flex-wrap: nowrap;
    padding: 0;
}

.logo-item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    transition: all 0.4s ease;
    filter: grayscale(0%);
    background: transparent;
    border-radius: 0;
    box-shadow: none;
    border: none;
    width: auto;
    height: auto;
    margin: 0;
}

.logo-item img {
    height: 4.2rem;
    width: auto;
    object-fit: contain;
    transition: all 0.4s ease;
    filter: brightness(0.95);
    margin: 0;
}

.logo-item:hover img {
    transform: scale(1.1);
    filter: brightness(1);
}

.continue-logo, .warp-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 4.2rem;
    width: 4.2rem;
    transition: all 0.3s ease;
}

.logo-item:hover .continue-logo,
.logo-item:hover .warp-logo {
    transform: scale(1.1);
    filter: brightness(1.2);
}

.continue-logo svg, .warp-logo svg {
    width: 100%;
    height: 100%;
}

.logo-item span {
    display: none;
}

.platform-logos-title {
    display: none;
}

@media (max-width: 768px) {
    .platform-logos-section {
        padding: 1.5rem 0;
    }

    .logos-carousel {
        gap: 1.2rem;
    }

    .logo-item img, .continue-logo, .warp-logo, .windsurf-logo {
        height: 3.5rem;
        width: 3.5rem;
    }

    .continue-logo, .warp-logo, .windsurf-logo {
        height: 3.5rem;
        width: 3.5rem;
    }
}

@media (max-width: 480px) {
    .platform-logos-section {
        padding: 1rem 0;
    }

    .logos-carousel {
        gap: 0.8rem;
    }

    .logo-item img, .continue-logo, .warp-logo, .windsurf-logo {
        height: 2.5rem;
        width: 2.5rem;
    }

    .continue-logo, .warp-logo, .windsurf-logo {
        height: 2.5rem;
        width: 2.5rem;
    }
}

/* Transformation Section */
.transformation-section {
    position: relative;
    min-height: 100vh;
    background: linear-gradient(to bottom, var(--color-background) 0%, var(--color-background-light) 100%);
    padding: 8rem 0;
        display: flex;
    flex-direction: column;
    align-items: center;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
    width: 100%;
    /* max-width: 1000px; */
}

.section-header h2 {
    font-family: var(--font-display);
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(90deg, var(--color-primary), var(--color-tertiary));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-fill-color: transparent;
}

.section-header p {
    font-size: 1.25rem;
    color: var(--color-text-dim);
    max-width: 800px;
    margin: 0 auto;
}

.transformation-container {
    width: 90%;
    max-width: 1200px;
        display: flex;
        flex-direction: column;
    gap: 3rem;
}

.transformation-stages {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    width: 100%;
}

@media (min-width: 768px) {
    .transformation-stages {
    flex-direction: row;
        align-items: stretch;
    }
}

.stage {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: rgba(10, 10, 10, 0.6);
    border-radius: 16px;
    overflow: hidden;
    border: 1px solid rgba(50, 50, 50, 0.5);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    min-height: 300px;
}

.stage:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.stage-human:hover {
    border-color: rgba(0, 194, 255, 0.4);
}

.stage-processing:hover {
    border-color: rgba(153, 69, 255, 0.4);
}

.stage-optimized:hover {
    border-color: rgba(20, 241, 149, 0.4);
}

.stage-label {
    padding: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.9rem;
    border-bottom: 1px solid rgba(50, 50, 50, 0.5);
}

.stage-human .stage-label {
    color: var(--color-tertiary);
}

.stage-processing .stage-label {
    color: var(--color-secondary);
}

.stage-optimized .stage-label {
    color: var(--color-primary);
}

.stage-content {
    flex: 1;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 220px;
}

.human-request-text {
    font-family: var(--font-sans);
    font-size: 1.2rem;
    color: var(--color-text);
    text-align: center;
        opacity: 0;
    animation: fadeIn 0.5s ease-out 0.2s forwards;
}

.wave-container {
    width: 100%;
    height: 150px;
    position: relative;
}

#transformCanvas {
    width: 100%;
    height: 100%;
    position: absolute;
        top: 0;
    left: 0;
}

.processing-indicator {
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 6px;
}

.dot {
    width: 8px;
    height: 8px;
    background-color: var(--color-secondary);
    border-radius: 50%;
    display: inline-block;
    opacity: 0;
}

.dot:nth-child(1) {
    animation: dotPulse 1.5s ease-in-out 0s infinite;
}

.dot:nth-child(2) {
    animation: dotPulse 1.5s ease-in-out 0.3s infinite;
}

.dot:nth-child(3) {
    animation: dotPulse 1.5s ease-in-out 0.6s infinite;
}

.optimized-prompt {
    font-family: var(--font-mono);
    font-size: 0.9rem;
    line-height: 1.5;
    color: var(--color-text);
    white-space: pre-wrap;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 1.2rem;
    width: 100%;
    height: 100%;
    overflow-y: auto;
        opacity: 0;
    animation: fadeIn 0.5s ease-out 3s forwards;
}

.transformation-examples {
    display: flex;
    justify-content: center;
    margin-top: 2.5rem;
    width: 100%;
}

.example-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
}

.example-button {
    background-color: rgba(10, 10, 10, 0.6);
    border: 1px solid rgba(50, 50, 50, 0.5);
    color: var(--color-text-dim);
    font-family: var(--font-sans);
    font-size: 0.9rem;
    padding: 0.6rem 1.2rem;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.example-button:hover {
    background-color: rgba(20, 20, 20, 0.8);
    color: var(--color-text);
    border-color: rgba(100, 100, 100, 0.5);
}

.example-button.active {
    background: linear-gradient(135deg, var(--color-secondary), var(--color-primary));
    color: white;
    border-color: transparent;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

@keyframes dotPulse {
    0% { opacity: 0; transform: scale(0.8); }
    50% { opacity: 1; transform: scale(1.2); }
    100% { opacity: 0; transform: scale(0.8); }
}

@keyframes typewriterTransform {
    0% { width: 0; }
    100% { width: 100%; }
}

/* Bottom Section - Sound Waves */
.bottom-section {
    position: relative;
    min-height: 100vh;
    background-color: var(--color-background-light);
    padding: 6rem 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.wave-animation-container {
    position: relative;
    width: 100%;
    height: 550px;
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
}

#wave-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}

.voice-command-container {
    position: relative;
    width: 90%;
    max-width: 900px;
    background: rgba(28, 28, 40, 0.8);
    border: 1px solid rgba(20, 241, 149, 0.2);
    border-radius: 16px;
    padding: 2.5rem;
    backdrop-filter: blur(8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    z-index: 1;
    display: flex;
    flex-direction: column;
    gap: 2.5rem;
}

.voice-input,
.voice-output {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.input-label,
.output-label {
    font-family: 'Outfit', var(--font-sans);
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--color-primary);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.input-content {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: rgba(0, 0, 0, 0.4);
    padding: 1rem;
    border-radius: 12px;
    border: 1px solid rgba(155, 53, 255, 0.2);
}

.mic-icon {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-primary);
    animation: pulse 2s infinite;
}

.input-text {
    font-family: var(--font-mono);
    font-size: 1rem;
    color: var(--color-text);
}

.typing-animation {
    border-right: 2px solid var(--color-primary);
    white-space: nowrap;
    overflow: hidden;
    animation: typeText 3s steps(50, end) forwards, blinkCursor 0.7s step-end infinite;
}

.output-content {
    background: rgba(0, 0, 0, 0.4);
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid rgba(0, 230, 118, 0.3);
}

.optimized-code {
    margin: 0;
    font-family: var(--font-mono);
    font-size: 0.9rem;
    line-height: 1.5;
    color: var(--color-text);
    white-space: pre-wrap;
}

.optimized-code code {
    opacity: 0;
    animation: fadeIn 1s ease-out 3s forwards;
}

/* Features Section */
.features-section {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 3rem;
    max-width: 1200px;
    width: 90%;
    margin: 5rem auto 3rem;
    padding: 0;
}

.feature-card {
    flex: 1;
    min-width: 250px;
    max-width: 350px;
    background: rgba(28, 28, 40, 0.8);
    border: 1px solid rgba(153, 69, 255, 0.2);
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border-color: rgba(153, 69, 255, 0.4);
}

.feature-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.feature-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--color-primary);
}

.feature-card p {
    font-size: 1rem;
    color: var(--color-text-dim);
}

/* Cursor Interface Section */
.cursor-interface-section {
    position: relative;
    min-height: 100vh;
    background: linear-gradient(to bottom, var(--color-background-light) 0%, var(--color-background) 100%);
    padding: 6rem 0;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.cursor-interface-container {
    width: 90%;
    max-width: 1200px;
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-bottom: 3rem;
}

@media (min-width: 992px) {
    .cursor-interface-container {
        flex-direction: row;
        align-items: stretch;
    }
}

/* Cursor Editor Window */
.cursor-window {
    flex: 3;
    background-color: #1e1e1e;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
    display: flex;
        flex-direction: column;
    min-height: 500px;
    border: 1px solid rgba(100, 100, 100, 0.3);
}

.cursor-titlebar {
    height: 40px;
    background-color: #252525;
    display: flex;
    align-items: center;
    padding: 0 15px;
    border-bottom: 1px solid rgba(100, 100, 100, 0.3);
}

.titlebar-controls {
    display: flex;
    gap: 8px;
}

.control-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.control-dot.red {
    background-color: #ff5f56;
}

.control-dot.yellow {
    background-color: #ffbd2e;
}

.control-dot.green {
    background-color: #27c93f;
}

.titlebar-title {
    margin-left: 15px;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    font-family: var(--font-sans);
}

.cursor-content {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.code-editor {
    display: flex;
    width: 100%;
    height: 100%;
    overflow: auto;
}

.line-numbers {
    display: flex;
    flex-direction: column;
    padding: 1rem 0.5rem;
    background-color: #1e1e1e;
    color: #6e7681;
    font-family: var(--font-mono);
    font-size: 0.85rem;
    text-align: right;
    min-width: 40px;
    user-select: none;
}

.code-area {
    flex: 1;
    padding: 1rem;
    font-family: var(--font-mono);
    font-size: 0.85rem;
    line-height: 1.5;
    color: #abb2bf;
    overflow: auto;
}

.code-content {
    margin: 0;
    white-space: pre;
}

/* Cursor AI Panel */
.cursor-ai-panel {
    flex: 2;
    background-color: #1e1e1e;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
    display: flex;
    flex-direction: column;
    min-height: 500px;
    border: 1px solid rgba(100, 100, 100, 0.3);
    transition: all 0.3s ease;
}

.cursor-ai-panel:hover {
    border-color: rgba(153, 69, 255, 0.3);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.5);
}

.panel-header {
    height: 40px;
    background-color: #252525;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
    border-bottom: 1px solid rgba(100, 100, 100, 0.3);
}

.panel-title {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

.voice-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 8px;
    border-radius: 4px;
    background-color: rgba(153, 69, 255, 0.2);
    cursor: pointer;
    user-select: none;
    transition: all 0.2s ease;
}

.voice-toggle.active {
    background-color: rgba(153, 69, 255, 0.4);
}

.voice-toggle span {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.9);
}

.voice-toggle .mic-icon {
    width: 14px;
    height: 14px;
    color: var(--color-primary);
}

.ai-conversation {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.message-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.user-message,
.ai-message {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    max-width: 100%;
}

.ai-message {
    align-items: flex-start;
}

.message-header {
    display: flex;
    align-items: center;
    gap: 8px;
}

.user-avatar,
.ai-avatar {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 4px;
}

.user-avatar {
    background-color: rgba(153, 69, 255, 0.3);
    color: rgba(255, 255, 255, 0.9);
}

.ai-avatar {
    background-color: rgba(20, 241, 149, 0.3);
    color: rgba(255, 255, 255, 0.9);
}

.message-time {
    font-size: 0.7rem;
    color: rgba(255, 255, 255, 0.5);
}

.message-content {
    font-family: var(--font-sans);
    font-size: 0.9rem;
    line-height: 1.5;
    color: rgba(255, 255, 255, 0.9);
    background-color: rgba(40, 40, 40, 0.6);
    padding: 0.8rem 1rem;
    border-radius: 8px;
    word-break: break-word;
}

.ai-message .message-content {
    background-color: rgba(20, 241, 149, 0.15);
}

.ai-typing {
    display: flex;
    gap: 4px;
    align-items: center;
}

.ai-typing .dot {
    width: 6px;
    height: 6px;
    background-color: var(--color-primary);
    border-radius: 50%;
    animation: aiTypingDot 1.5s infinite ease-in-out;
}

.ai-typing .dot:nth-child(2) {
    animation-delay: 0.2s;
}

.ai-typing .dot:nth-child(3) {
    animation-delay: 0.4s;
}

.prompt-input {
    padding: 1rem;
    border-top: 1px solid rgba(100, 100, 100, 0.3);
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.voice-recording-indicator {
    display: flex;
    align-items: center;
    gap: 1rem;
    background-color: rgba(0, 0, 0, 0.3);
    padding: 0.8rem 1rem;
    border-radius: 8px;
    border: 1px solid rgba(153, 69, 255, 0.3);
        opacity: 0;
    height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.voice-recording-indicator.active {
        opacity: 1;
    height: 50px;
}

.recording-wave {
    display: flex;
    align-items: center;
    gap: 3px;
    height: 20px;
}

.wave-bar {
    width: 3px;
    height: 100%;
    background-color: var(--color-primary);
    border-radius: 3px;
    animation: soundWave 1.2s infinite ease-in-out;
}

.wave-bar:nth-child(2) {
    animation-delay: 0.2s;
}

.wave-bar:nth-child(3) {
    animation-delay: 0.4s;
}

.wave-bar:nth-child(4) {
    animation-delay: 0.6s;
}

.wave-bar:nth-child(5) {
    animation-delay: 0.8s;
}

.recording-text {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.8);
}

.input-buttons {
    display: flex;
    gap: 1rem;
}

.mic-button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(153, 69, 255, 0.3);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.mic-button svg {
    width: 20px;
    height: 20px;
    color: #ffffff;
}

.mic-button:hover,
.mic-button.active {
    background-color: rgba(153, 69, 255, 0.6);
    transform: scale(1.05);
}

.mic-button.active {
    animation: pulseMic 2s infinite;
}

.cancel-button,
.apply-button {
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-family: var(--font-sans);
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.cancel-button {
    background-color: transparent;
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: rgba(255, 255, 255, 0.8);
}

.cancel-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.apply-button {
    background-color: rgba(20, 241, 149, 0.3);
    border: 1px solid rgba(20, 241, 149, 0.3);
    color: rgba(255, 255, 255, 0.9);
}

.apply-button:hover {
    background-color: rgba(20, 241, 149, 0.5);
}

.interaction-guide {
    text-align: center;
    margin-bottom: 2rem;
}

.interaction-guide p {
    margin-bottom: 1rem;
    font-size: 1rem;
    color: var(--color-text-dim);
}

.voice-example-selector {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.voice-example-button {
    background-color: rgba(10, 10, 10, 0.6);
    border: 1px solid rgba(50, 50, 50, 0.5);
    color: var(--color-text-dim);
    font-family: var(--font-sans);
    font-size: 0.9rem;
    padding: 0.6rem 1.2rem;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.voice-example-button:hover {
    background-color: rgba(20, 20, 20, 0.8);
    color: var(--color-text);
    border-color: rgba(100, 100, 100, 0.5);
}

.voice-example-button.active {
    background: linear-gradient(135deg, var(--color-secondary), var(--color-primary));
    color: white;
    border-color: transparent;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

@keyframes aiTypingDot {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.6;
    }
    30% {
        transform: translateY(-5px);
        opacity: 1;
    }
}

@keyframes soundWave {
    0%, 100% {
        height: 4px;
    }
    50% {
        height: 16px;
    }
}

@keyframes pulseMic {
    0% {
        box-shadow: 0 0 0 0 rgba(153, 69, 255, 0.5);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(153, 69, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(153, 69, 255, 0);
    }
}

/* Animations */
@keyframes floatIn {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-12px);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes typeText {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blinkCursor {
    from, to {
        border-color: transparent;
    }
    50% {
        border-color: var(--color-primary);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .main-title {
        font-size: 5rem;
    }

    .subtitle {
        font-size: 1.5rem;
    }

    .section-header h2 {
        font-size: 2.5rem;
    }

    .voice-icon {
        right: -40px;
        width: 30px;
        height: 30px;
    }
}

@media (min-width: 992px) {
    .voice-icon {
        display: block; /* Show on larger screens */
    }
}

@media (max-width: 900px) {
    .mobile-menu-toggle {
        display: block;
    }

    .nav-links {
        position: fixed;
        top: var(--nav-height);
        left: -100%;
        flex-direction: column;
        gap: 0;
        width: 100%;
        background-color: rgba(5, 5, 5, 0.95);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        padding: 1rem 0;
        text-align: center;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    }

    .nav-links.active {
    left: 0;
    }

    .nav-links li {
    width: 100%;
    }

    .nav-link {
        display: block;
        padding: 1rem 0;
    }

    .nav-link::after {
        display: none;
    }

    .mobile-menu-toggle.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .mobile-menu-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .mobile-menu-toggle.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .top-section {
        min-height: 100vh;
        height: auto;
        padding: calc(var(--nav-height) + 2rem) 1rem 3rem;
        align-items: flex-start;
    }

    .hero-content {
        padding: 2rem 0;
    }

    .title-container {
        padding-top: 0;
    }

    .main-title {
        font-size: 3.5rem;
        line-height: 1.1;
    }

    /* Subtitle styles removed */

    .logo-text {
        font-size: 1.6rem;
    }

    .floating-prompt-bubbles {
        height: 300px;
    }

    .voice-command-container {
        padding: 1.5rem;
    gap: 2rem;
    }

    .features-section {
        flex-direction: column;
        align-items: center;
    }

    .feature-card {
    width: 100%;
        max-width: 100%;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .section-header p {
    font-size: 1rem;
    }

    .human-request-text {
        font-size: 1rem;
    }

    .optimized-prompt {
        font-size: 0.8rem;
    }

    .extension-logos {
    gap: 1.5rem;
    }

    .transformation-section {
    padding: 5rem 0;
    }

    .stage {
        min-height: 250px;
    }

    .cursor-interface-section {
        padding: 5rem 0 3rem;
    }

    .cursor-window, .cursor-ai-panel {
        min-height: 400px;
    }

    .code-editor, .ai-conversation {
        font-size: 0.8rem;
    }

    .interaction-guide {
        margin-top: 1rem;
    }

    .section-header {
        margin-bottom: 3rem;
    }

    .transformation-examples {
        margin-top: 2rem;
    }

    .bottom-section {
        padding: 4rem 0;
    }

    .features-section {
        margin: 3rem auto 2rem;
    gap: 2rem;
    }
}

@media (max-width: 480px) {
    .logo-icon {
        width: 24px;
        height: 24px;
    }

    .logo-text {
        font-size: 1.4rem;
    }

    .logo-container svg {
        width: 30px;
    }

    .top-section {
        padding: calc(var(--nav-height) + 1rem) 1rem 2rem;
    }

    .hero-content {
        padding: 1rem 0.5rem;
        margin-top: 0;
    }

    .title-container {
        padding-top: 0;
    }

    .main-title {
        font-size: 2.8rem;
    margin-bottom: 1.5rem;
    }

    /* Subtitle styles removed */

    .primary-button {
        padding: 0.8rem 1.8rem;
        font-size: 1rem;
    }

    .voice-command-container {
        padding: 1.2rem;
        gap: 1.5rem;
    }

    .input-content,
    .output-content {
        padding: 0.8rem;
    }

    .optimized-code {
        font-size: 0.8rem;
    }

    .transformation-stages {
        gap: 1rem;
    }

    .example-button {
        font-size: 0.8rem;
        padding: 0.5rem 1rem;
    }

    .extension-logos {
        gap: 1rem;
    }

    .extension-logo img {
        width: 25px;
        height: 25px;
    }

    .extension-logo span {
        font-size: 0.8rem;
    }

    .floating-prompt-bubbles {
        height: 380px;
    }

    .prompt-bubble {
        width: 75%;
        max-width: 200px;
        left: 12.5% !important;
        right: auto !important;
        padding: 10px 16px;
    }

    .prompt-bubble:nth-child(1) {
        top: 0%;
    }

    .prompt-bubble:nth-child(2) {
        top: 25%;
    }

    .prompt-bubble:nth-child(3) {
        top: 50%;
    }

    .prompt-bubble:nth-child(4) {
        top: 75%;
    }

    .cursor-interface-section {
        padding: 4rem 0 2rem;
    }

    .cursor-window, .cursor-ai-panel {
        min-height: 350px;
    }

    .code-content, .message-content {
        font-size: 0.75rem;
    }

    .voice-example-selector {
        gap: 0.5rem;
    }

    .voice-example-button {
        padding: 0.5rem 0.8rem;
        font-size: 0.75rem;
    }

    .section-header {
        margin-bottom: 2rem;
    }

    .transformation-examples {
        margin-top: 1.5rem;
    }

    .bottom-section {
        padding: 3rem 0;
    }

    .features-section {
        margin: 2rem auto 1.5rem;
        gap: 1.5rem;
    }
}

/* New animations for voice elements */
@keyframes pulseWave {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 230, 118, 0.6);
        transform: translateX(-50%) scale(0.8);
    }
    70% {
        box-shadow: 0 0 0 15px rgba(0, 230, 118, 0);
        transform: translateX(-50%) scale(1.1);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 230, 118, 0);
        transform: translateX(-50%) scale(0.8);
    }
}

@keyframes voiceWave {
    0%, 100% {
        width: 90%;
        left: 5%;
        opacity: 0.4;
    }
    50% {
        width: 110%;
        left: -5%;
        opacity: 0.7;
    }
}

@keyframes pulseScale {
    0%, 100% {
        transform: translateY(-50%) scale(1);
    }
    50% {
        transform: translateY(-50%) scale(1.15);
    }
}

.voice-visualization {
    position: absolute;
    z-index: 5;
    filter: drop-shadow(0 0 8px rgba(0, 230, 118, 0.5));
    border-radius: 8px;
    opacity: 0.8;
    transition: opacity 0.3s ease, transform 0.3s ease;
    transform: scale(1);
}

.title-container:hover .voice-visualization {
    opacity: 1;
    transform: scale(1.05);
}

/* New keyframes for enhanced voice animations */
@keyframes pulseRing {
    0% {
        opacity: 0.3;
        transform: translate(-50%, -50%) scale(0.5);
    }
    50% {
        opacity: 0.1;
        transform: translate(-50%, -50%) scale(1.2);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(1.8);
    }
}

@keyframes moveGradient {
    0% {
        background-position: 0% 0%;
    }
    100% {
        background-position: 200% 0%;
    }
}

/* Updated Intro Section Styles - More Minimalistic and Modern */
.intro-title-container {
    display: flex;
        align-items: center;
    gap: 1.2rem;
    margin-bottom: 1.5rem;
}

.intro-label {
    font-family: var(--font-sans);
    font-size: 1.4rem;
    font-weight: 600;
    letter-spacing: 0.2em;
    text-transform: uppercase;
    color: var(--color-primary);
    background: rgba(20, 241, 149, 0.1);
    padding: 0.6rem 1.4rem;
    border-radius: 30px;
    display: inline-block;
    position: relative;
    top: -0.5rem;
    box-shadow: 0 4px 12px rgba(20, 241, 149, 0.15);
}

.intro-title {
    font-family: 'Instrument', var(--font-sans);
    font-size: 7rem;
    font-weight: 800;
    line-height: 0.9;
    color: white;
    margin: 0;
    letter-spacing: -3px;
    position: relative;
    background: linear-gradient(to right, #ffffff, #f0f0f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 4px 25px rgba(0, 0, 0, 0.2);
}

.intro-title .highlight {
    color: var(--color-primary);
    background: linear-gradient(135deg, var(--color-primary), #9ef4ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
    display: inline-block;
}

.intro-title .highlight::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 6px;
    bottom: 5px;
    left: 0;
    background: linear-gradient(90deg, var(--color-primary), transparent);
    opacity: 0.3;
    border-radius: 5px;
    z-index: -1;
}

.intro-description {
    font-family: var(--font-sans);
    font-size: 1.8rem;
    font-weight: 400;
    line-height: 1.4;
    color: var(--color-text);
    margin-bottom: 3rem;
    max-width: 90%;
    opacity: 0.9;
}

/* Updated Feature List Styles - More Prominent and Modern */
.intro-feature-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2.5rem;
    margin-bottom: 3rem;
}

.intro-feature-item {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    padding: 1.5rem;
    border-radius: 16px;
    background: rgba(15, 15, 25, 0.3);
    backdrop-filter: blur(10px);
    border-left: 3px solid var(--color-primary);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.intro-feature-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(120deg, rgba(20, 241, 149, 0.05) 0%, transparent 50%);
/* Root variables */
:root {
    --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    --font-mono: 'JetBrains Mono', monospace;
    --font-instrument: 'Instrument', sans-serif;
    --font-display: 'Clash Display', sans-serif;
    --font-outfit: 'Outfit', sans-serif;
    --font-space: 'Space Grotesk', sans-serif;

    --color-background: #0d1117;
    --color-foreground: #161b22;
    --color-primary: #14F195;
    --color-primary-rgb: 20, 241, 149;
    --color-secondary: #0A84FF;
    --color-accent: #f472b6;
    --color-text: #c9d1d9;
    --color-text-muted: #8b949e;
    --color-border: #30363d;
    --color-code-bg: #21262d;
    --color-code-text: #f0f6fc;
    --color-card-bg: rgba(18, 21, 26, 0.8);
    --color-card-border: rgba(48, 54, 61, 0.5);

    --border-radius: 8px;
    --nav-height: 60px;

    /* Animation variables */
    --transition-fast: 0.2s;
    --transition-medium: 0.3s;
    --transition-slow: 0.5s;
    --cubic-smooth: cubic-bezier(0.4, 0, 0.2, 1);
    --cubic-in-out: cubic-bezier(0.65, 0.05, 0.36, 1);
}

/* Base styles */
html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-sans);
    background-color: var(--color-background);
    color: var(--color-text);
    line-height: 1.6;
    overflow-x: hidden;
    scroll-behavior: smooth;
    padding: 0;
    margin: 0;
    min-height: 100vh;
    transition: background-color 0.8s ease;
}

/* Smooth scroll animations for all elements */
.page-wrapper > * {
    animation: fadeInUp 0.6s var(--cubic-in-out) both;
    will-change: transform, opacity;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Add animation delay for staggered appearance */
.top-section {
    animation-delay: 0.1s;
}

.features-overview-section {
    animation-delay: 0.2s;
}

.use-cases-section {
    animation-delay: 0.3s;
}

/* Smooth animations for scrolling elements */
.intro-feature-item,
.use-case-card,
.main-title,
.subtitle,
.intro-cta-button {
    transition: transform var(--transition-medium) var(--cubic-in-out),
                opacity var(--transition-medium) var(--cubic-in-out),
                box-shadow var(--transition-medium) var(--cubic-in-out);
}

/* Navbar smooth transitions */
.navbar {
    transition: background-color var(--transition-medium) var(--cubic-smooth),
                box-shadow var(--transition-medium) var(--cubic-smooth);
}

.nav-link {
    transition: color var(--transition-fast) var(--cubic-smooth),
                opacity var(--transition-fast) var(--cubic-smooth);
}

/* Appear on scroll animations */
.appear-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.8s ease, transform 0.8s ease;
}

.appear-on-scroll.is-visible {
    opacity: 1;
    transform: translateY(0);
}

/* Additional animation variants */
.appear-on-scroll.slide-up {
    transform: translateY(50px);
}

.appear-on-scroll.slide-right {
    transform: translateX(-50px);
}

.appear-on-scroll.slide-left {
    transform: translateX(50px);
}

.appear-on-scroll.scale-in {
    transform: scale(0.8) translateY(20px);
}

.appear-on-scroll.fade-in {
    transform: translateY(0); /* Just fade, no movement */
}

/* When visible, reset all transforms */
.appear-on-scroll.is-visible {
    opacity: 1;
    transform: translateY(0) translateX(0) scale(1);
}

/* Section transitions */
.top-section,
.features-overview-section,
.intro-section,
.speed-benefits-section,
.use-cases-section,
.platform-logos-section {
    transition: transform 0.6s ease-out, background-color 0.8s ease;
}

/* Active section highlighting */
.active-section {
    z-index: 2;
}

/* Parallax background effect */
.features-overview-section::before,
.intro-section::before,
.speed-benefits-section::before,
.use-cases-section::before {
    transition: transform 1s ease-out;
}

/* Smooth scroll behavior for the whole page */
html {
    scroll-behavior: smooth;
}

/* Active nav link styling */
.nav-link.active {
    color: var(--color-primary);
}

.nav-link.active::after {
    width: 100%;
    opacity: 1;
}

/* Scroll progress indicator */
.scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--color-primary), #7affcb);
    z-index: 1000;
    width: 0%;
    transition: width 0.2s ease-out;
}

/* Scroll to top button */
.scroll-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 45px;
    height: 45px;
    background-color: var(--color-primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.3s ease, transform 0.3s ease, background-color 0.3s ease;
    z-index: 999;
    box-shadow: 0 4px 12px rgba(20, 241, 149, 0.3);
}

.scroll-to-top.visible {
    opacity: 1;
    transform: translateY(0);
}

.scroll-to-top:hover {
    background-color: #0ad680;
}

.scroll-to-top svg {
    width: 20px;
    height: 20px;
    stroke: white;
    stroke-width: 2px;
    transition: transform 0.3s ease;
}

.scroll-to-top:hover svg {
    transform: translateY(-3px);
}

/* Section animations - staggered reveal */
.intro-feature-item,
.use-case-card,
.metric {
    transition-delay: calc(var(--index, 0) * 0.1s);
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideLeft {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideRight {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Background color transitions for full-page scrolling effect */
body {
    transition: background-color 0.8s ease;
}

.top-section.active-section ~ body {
    background-color: rgba(20, 241, 149, 0.05);
}

.intro-section.active-section ~ body {
    background-color: rgba(20, 241, 149, 0.02);
}

.features-overview-section.active-section ~ body {
    background-color: rgba(20, 241, 149, 0.03);
}

.speed-benefits-section.active-section ~ body {
    background-color: rgba(20, 241, 149, 0.04);
}

.use-cases-section.active-section ~ body {
    background-color: rgba(20, 241, 149, 0.03);
}

/* Add a subtle transition for all interactive elements */
a, button, .nav-link, .intro-feature-item, .use-case-card, .intro-cta-button {
    transition: all 0.3s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .scroll-to-top {
        width: 40px;
        height: 40px;
        bottom: 20px;
        right: 20px;
    }

    .scroll-to-top svg {
        width: 18px;
        height: 18px;
    }

    .appear-on-scroll {
        transform: translateY(20px); /* Less movement on mobile */
    }
}

@media (max-width: 480px) {
    .scroll-to-top {
        width: 35px;
        height: 35px;
        bottom: 15px;
        right: 15px;
    }

    .scroll-to-top svg {
        width: 16px;
        height: 16px;
    }
}

.page-wrapper {
    display: flex;
    flex-direction: column;
    width: 100%;
    position: relative;
    padding: 0;
    margin: 0;
    overflow-x: hidden;
}

/* Navigation bar */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: var(--nav-height);
    background-color: rgba(13, 17, 23, 0.95);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.4);
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(20, 241, 149, 0.2);
}

.navbar.scrolled {
    background-color: rgba(5, 5, 5, 0.98);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    height: var(--nav-height);
}

.logo-container {
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-container svg {
    filter: drop-shadow(0 0 3px rgba(20, 241, 149, 0.2));
}

.logo-icon {
    width: 30px;
    height: 30px;
    border-radius: 8px;
}

.logo-text {
    font-family: var(--font-display);
    font-size: 1.8rem;
    font-weight: 700;
    letter-spacing: -0.02em;
    color: #ffffff;
    display: flex;
    align-items: center;
    margin: 0;
    padding: 0;
    line-height: 1;
}

.logo-text .hype-text {
    color: var(--color-primary);
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2.5rem;
    margin: 0;
    padding: 0;
}

.nav-link {
    color: var(--color-light);
    text-decoration: none;
    position: relative;
    transition: color 0.3s ease;
}

.nav-buttons {
    display: flex;
    gap: 0.75rem;
    margin-left: 1rem;
}

.nav-button {
    padding: 0.5rem 1.5rem;
    font-size: 0.9rem;
    border-radius: 2rem;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.login-button {
    color: #14F195;
    border: 2px solid #14F195;
    background-color: transparent;
}

.login-button:hover {
    background-color: rgba(20, 241, 149, 0.1);
    transform: translateY(-2px);
}

.signup-button {
    background-color: #14F195;
    color: #000;
    border: 2px solid #14F195;
}

.signup-button:hover {
    transform: translateY(-2px);
    filter: brightness(1.1);
}

.nav-cta {
    padding: 0.35rem 0.8rem;
    font-size: 0.75rem;
    margin-left: 1rem;
    border-radius: 4px;
    font-weight: 500;
    line-height: 1;
}

.nav-cta:hover {
    transform: translateY(-2px);
}

.nav-link:hover {
    color: var(--color-text);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
    transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
}

.bar {
    display: block;
    width: 25px;
    height: 3px;
    margin: 5px auto;
    transition: all 0.3s ease;
    background-color: var(--color-text);
    border-radius: 2px;
}

/* Top Section - Code Animation */
.top-section {
    position: relative;
    width: 100%;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: calc(var(--nav-height) + 4rem) 1rem 5rem;
    background: radial-gradient(circle at 50% 50%, rgba(20, 241, 149, 0.02) 0%, rgba(15, 15, 25, 0) 50%);
    overflow: visible;
}

#code-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.code-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom,
        rgba(5, 5, 5, 0.4) 0%,
        rgba(5, 5, 5, 0.6) 50%,
        rgba(5, 5, 5, 0.9) 100%);
    z-index: 1;
}

/* Hover areas to show animation */
.hover-area {
    position: relative;
    z-index: 10;
}

.hover-area:hover ~ #code-canvas,
.title-container:hover ~ #code-canvas,
.hero-content:hover #code-canvas {
    opacity: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    width: 90%;
    max-width: 1200px;
    padding: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    margin: 0 auto;
}

.title-container {
    display: block;
    margin-top: 0 !important;
    position: relative;
    width: 100%;
    z-index: 10;
}

/* Enhanced voice wave animation around the title - REMOVED */
.title-container::before {
    display: none;
}

.title-container::after {
    display: none;
}

/* Voice indicator animation - REMOVED */
.title-line .highlight::before {
    display: none;
}

/* Advanced voice wave pattern below highlight - REMOVED */
.title-line .highlight::after {
    display: none;
}

/* Enhanced microphone icon - REMOVED */
.voice-icon {
    display: none;
}

/* Add circles around voice icon - REMOVED */
.voice-icon::before,
.voice-icon::after {
    display: none;
}

/* Enhance voice visualization canvas - REMOVED */
.voice-visualization {
    display: none;
}

.main-title {
    font-family: var(--font-display);
    font-size: 6rem;
    font-weight: 800;
    line-height: 0.9;
    margin: 0 0 2rem;
    text-align: center;
    text-transform: none;
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
    letter-spacing: -0.02em;
    position: relative;
    width: 100%;
    align-items: center;
    justify-content: center;
    text-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
    color: #ffffff;
}

.title-line {
    display: block;
    position: relative;
}

.title-line:first-child {
    font-weight: 700;
    color: #ffffff;
    text-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.title-line .highlight {
    color: var(--color-primary);
    -webkit-text-fill-color: var(--color-primary);
    font-weight: 800;
    font-size: 1.05em;
    position: relative;
    display: inline-block;
    background: none;
    -webkit-background-clip: initial;
    background-clip: initial;
    text-fill-color: initial;
    filter: drop-shadow(0 0 20px rgba(20, 241, 149, 0.3));
    padding-bottom: 20px;
    margin-bottom: 10px;
}

/* Voice indicator animation - enhanced */
.title-line .highlight::before {
    content: '';
    position: absolute;
    width: 115%;
    height: 10px;
    bottom: -10px;
    left: -7.5%;
    background: linear-gradient(90deg, transparent, var(--color-primary) 50%, transparent);
    opacity: 0.6;
    border-radius: 50%;
    filter: blur(5px);
    animation: voiceWave 3s ease-in-out infinite;
}

/* Add advanced voice wave pattern below highlight */
.title-line .highlight::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 20px;
    bottom: -20px;
    left: 0;
    background-image:
        linear-gradient(90deg,
            transparent 0%, var(--color-primary) 10%,
            transparent 20%, var(--color-secondary) 30%,
            transparent 40%, var(--color-primary) 50%,
            transparent 60%, var(--color-secondary) 70%,
            transparent 80%, var(--color-primary) 90%,
            transparent 100%);
    background-size: 200% 100%;
    opacity: 0.2;
    filter: blur(3px);
    animation: moveGradient 8s linear infinite;
    clip-path: polygon(
        0% 0%, 10% 30%, 20% 0%, 30% 60%, 40% 30%,
        50% 70%, 60% 20%, 70% 50%, 80% 10%, 90% 40%, 100% 0%
    );
}

/* Subtitle styles removed */

/* Custom styling for Typed.js cursor */
.typed-cursor {
    color: var(--color-primary) !important;
    font-size: 2.2rem;
    font-weight: 500;
    opacity: 1;
    animation: blink 0.7s infinite;
}

@keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0; }
}

/* Hide floating prompt bubbles classes since we removed them */
.floating-prompt-bubbles,
.prompt-bubble,
.voice-recording-animation,
.voice-wave,
.bubble-content {
    display: none;
}

.cta-button {
    margin-top: 2.5rem;
    margin-bottom: 3rem;
}

.primary-button {
    background: var(--color-primary);
    color: #000;
    font-family: var(--font-display);
    font-weight: 600;
    font-size: 1.2rem;
    padding: 1.1rem 2.6rem;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2), 0 0 0 0 rgba(20, 241, 149, 0.5);
    transition: all 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
    position: relative;
    overflow: hidden;
    outline: none;
    z-index: 10;
}

.primary-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.8s ease;
    z-index: -1;
}

.primary-button:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(20, 241, 149, 0.4);
    letter-spacing: 0.05em;
}

.primary-button:hover::before {
    left: 100%;
}

.primary-button:active {
    transform: translateY(0);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2), 0 0 10px rgba(20, 241, 149, 0.3);
}

/* IDE Extensions Section - HIDDEN */
.ide-extensions {
    display: none;
}

/* Voice-to-Prompt Animation */
.voice-to-prompt-animation {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1.5rem;
    margin-top: 2.5rem;
    width: 100%;
    max-width: 900px;
    padding: 1rem;
    position: relative;
}

.voice-input-side, .optimized-prompt-side {
    flex: 1;
    background: rgba(15, 15, 25, 0.7);
    border-radius: 12px;
    padding: 1.5rem;
    min-height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all 0.3s ease;
}

.voice-input-side {
    border: 1px solid rgba(153, 69, 255, 0.3);
    box-shadow: 0 5px 15px rgba(153, 69, 255, 0.15);
}

.optimized-prompt-side {
    border: 1px solid rgba(20, 241, 149, 0.3);
    box-shadow: 0 5px 15px rgba(20, 241, 149, 0.15);
    opacity: 0;
    animation: fadeIn 0.5s ease-out forwards 1.5s;
}

.recording-indicator {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
}

.voice-wave-animation {
        display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    height: 30px;
}

.voice-wave-animation span {
    display: inline-block;
    width: 3px;
    height: 5px;
    background-color: var(--color-secondary);
    border-radius: 3px;
    animation: voiceWaveAnim 1.3s ease-in-out infinite;
}

.voice-wave-animation span:nth-child(1) {
    animation-delay: 0s;
}

.voice-wave-animation span:nth-child(2) {
    animation-delay: 0.2s;
}

.voice-wave-animation span:nth-child(3) {
    animation-delay: 0.4s;
}

.voice-wave-animation span:nth-child(4) {
    animation-delay: 0.2s;
}

.voice-wave-animation span:nth-child(5) {
    animation-delay: 0s;
}

.voice-input-text {
    font-family: var(--font-sans);
    font-size: 1.1rem;
    color: var(--color-text);
    text-align: center;
    line-height: 1.4;
    position: relative;
    opacity: 0;
    animation: typeIn 1s ease-out forwards 0.5s;
}

.optimized-prompt-text {
    font-family: var(--font-mono);
    font-size: 1rem;
    color: var(--color-primary);
    text-align: center;
    line-height: 1.5;
    font-weight: 500;
}

.arrow-transformation {
        display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-primary);
    animation: pulseArrow 2s ease-in-out infinite;
}

.arrow-transformation svg {
    width: 40px;
    height: 40px;
    stroke: var(--color-primary);
}

@keyframes voiceWaveAnim {
    0%, 100% {
        height: 5px;
    }
    50% {
        height: 25px;
    }
}

@keyframes pulseArrow {
    0%, 100% {
        transform: scale(1) translateX(0);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.1) translateX(5px);
        opacity: 1;
    }
}

@keyframes typeIn {
    0% {
        opacity: 0;
        transform: translateY(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Media queries for voice-to-prompt animation */
@media (max-width: 768px) {
    .voice-to-prompt-animation {
        flex-direction: column;
        gap: 1rem;
        margin-top: 2rem;
    }

    .arrow-transformation {
        transform: rotate(90deg);
        margin: 0.5rem 0;
    }

    .voice-input-side, .optimized-prompt-side {
        width: 100%;
        padding: 1.2rem;
    }

    .voice-input-text, .optimized-prompt-text {
        font-size: 0.95rem;
    }
}

@media (max-width: 480px) {
    .voice-to-prompt-animation {
        margin-top: 1.5rem;
    }

    .voice-input-side, .optimized-prompt-side {
        padding: 1rem;
    }

    .voice-input-text, .optimized-prompt-text {
        font-size: 0.9rem;
    }

    .arrow-transformation svg {
    width: 30px;
        height: 30px;
    }
}

/* Platform Logos Carousel */
.platform-logos-section {
    padding: 2rem 0;
    background: rgba(15, 15, 20, 0.95);
    position: relative;
    overflow: hidden;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.logos-carousel-container {
    max-width: 1000px;
    margin: 0 auto;
    position: relative;
    padding: 1rem 0;
}

.logos-carousel {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 3rem;
    flex-wrap: nowrap;
    padding: 0;
}

.logo-item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    transition: all 0.4s ease;
    filter: grayscale(0%);
    background: transparent;
    border-radius: 0;
    box-shadow: none;
    border: none;
    width: auto;
    height: auto;
    margin: 0;
}

.logo-item img {
    height: 4.2rem;
    width: auto;
    object-fit: contain;
    transition: all 0.4s ease;
    filter: brightness(0.95);
    margin: 0;
}

.logo-item:hover img {
    transform: scale(1.1);
    filter: brightness(1);
}

.continue-logo, .warp-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 4.2rem;
    width: 4.2rem;
    transition: all 0.3s ease;
}

.logo-item:hover .continue-logo,
.logo-item:hover .warp-logo {
    transform: scale(1.1);
    filter: brightness(1.2);
}

.continue-logo svg, .warp-logo svg {
    width: 100%;
    height: 100%;
}

.logo-item span {
    display: none;
}

.platform-logos-title {
    display: none;
}

@media (max-width: 768px) {
    .platform-logos-section {
        padding: 1.5rem 0;
    }

    .logos-carousel {
        gap: 1.2rem;
    }

    .logo-item img, .continue-logo, .warp-logo, .windsurf-logo {
        height: 3.5rem;
        width: 3.5rem;
    }

    .continue-logo, .warp-logo, .windsurf-logo {
        height: 3.5rem;
        width: 3.5rem;
    }
}

@media (max-width: 480px) {
    .platform-logos-section {
        padding: 1rem 0;
    }

    .logos-carousel {
        gap: 0.8rem;
    }

    .logo-item img, .continue-logo, .warp-logo, .windsurf-logo {
        height: 2.5rem;
        width: 2.5rem;
    }

    .continue-logo, .warp-logo, .windsurf-logo {
        height: 2.5rem;
        width: 2.5rem;
    }
}

/* Transformation Section */
.transformation-section {
    position: relative;
    min-height: 100vh;
    background: linear-gradient(to bottom, var(--color-background) 0%, var(--color-background-light) 100%);
    padding: 8rem 0;
        display: flex;
    flex-direction: column;
    align-items: center;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
    width: 90%;
    max-width: 1000px;
}

.section-header h2 {
    font-family: var(--font-display);
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(90deg, var(--color-primary), var(--color-tertiary));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-fill-color: transparent;
}

.section-header p {
    font-size: 1.25rem;
    color: var(--color-text-dim);
    max-width: 800px;
    margin: 0 auto;
}

.transformation-container {
    width: 90%;
    max-width: 1200px;
        display: flex;
        flex-direction: column;
    gap: 3rem;
}

.transformation-stages {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    width: 100%;
}

@media (min-width: 768px) {
    .transformation-stages {
    flex-direction: row;
        align-items: stretch;
    }
}

.stage {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: rgba(10, 10, 10, 0.6);
    border-radius: 16px;
    overflow: hidden;
    border: 1px solid rgba(50, 50, 50, 0.5);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    min-height: 300px;
}

.stage:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.stage-human:hover {
    border-color: rgba(0, 194, 255, 0.4);
}

.stage-processing:hover {
    border-color: rgba(153, 69, 255, 0.4);
}

.stage-optimized:hover {
    border-color: rgba(20, 241, 149, 0.4);
}

.stage-label {
    padding: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.9rem;
    border-bottom: 1px solid rgba(50, 50, 50, 0.5);
}

.stage-human .stage-label {
    color: var(--color-tertiary);
}

.stage-processing .stage-label {
    color: var(--color-secondary);
}

.stage-optimized .stage-label {
    color: var(--color-primary);
}

.stage-content {
    flex: 1;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 220px;
}

.human-request-text {
    font-family: var(--font-sans);
    font-size: 1.2rem;
    color: var(--color-text);
    text-align: center;
        opacity: 0;
    animation: fadeIn 0.5s ease-out 0.2s forwards;
}

.wave-container {
    width: 100%;
    height: 150px;
    position: relative;
}

#transformCanvas {
    width: 100%;
    height: 100%;
    position: absolute;
        top: 0;
    left: 0;
}

.processing-indicator {
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 6px;
}

.dot {
    width: 8px;
    height: 8px;
    background-color: var(--color-secondary);
    border-radius: 50%;
    display: inline-block;
    opacity: 0;
}

.dot:nth-child(1) {
    animation: dotPulse 1.5s ease-in-out 0s infinite;
}

.dot:nth-child(2) {
    animation: dotPulse 1.5s ease-in-out 0.3s infinite;
}

.dot:nth-child(3) {
    animation: dotPulse 1.5s ease-in-out 0.6s infinite;
}

.optimized-prompt {
    font-family: var(--font-mono);
    font-size: 0.9rem;
    line-height: 1.5;
    color: var(--color-text);
    white-space: pre-wrap;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 1.2rem;
    width: 100%;
    height: 100%;
    overflow-y: auto;
        opacity: 0;
    animation: fadeIn 0.5s ease-out 3s forwards;
}

.transformation-examples {
    display: flex;
    justify-content: center;
    margin-top: 2.5rem;
    width: 100%;
}

.example-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
}

.example-button {
    background-color: rgba(10, 10, 10, 0.6);
    border: 1px solid rgba(50, 50, 50, 0.5);
    color: var(--color-text-dim);
    font-family: var(--font-sans);
    font-size: 0.9rem;
    padding: 0.6rem 1.2rem;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.example-button:hover {
    background-color: rgba(20, 20, 20, 0.8);
    color: var(--color-text);
    border-color: rgba(100, 100, 100, 0.5);
}

.example-button.active {
    background: linear-gradient(135deg, var(--color-secondary), var(--color-primary));
    color: white;
    border-color: transparent;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

@keyframes dotPulse {
    0% { opacity: 0; transform: scale(0.8); }
    50% { opacity: 1; transform: scale(1.2); }
    100% { opacity: 0; transform: scale(0.8); }
}

@keyframes typewriterTransform {
    0% { width: 0; }
    100% { width: 100%; }
}

/* Bottom Section - Sound Waves */
.bottom-section {
    position: relative;
    min-height: 100vh;
    background-color: var(--color-background-light);
    padding: 6rem 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.wave-animation-container {
    position: relative;
    width: 100%;
    height: 550px;
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
}

#wave-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}

.voice-command-container {
    position: relative;
    width: 90%;
    max-width: 900px;
    background: rgba(28, 28, 40, 0.8);
    border: 1px solid rgba(20, 241, 149, 0.2);
    border-radius: 16px;
    padding: 2.5rem;
    backdrop-filter: blur(8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    z-index: 1;
    display: flex;
    flex-direction: column;
    gap: 2.5rem;
}

.voice-input,
.voice-output {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.input-label,
.output-label {
    font-family: 'Outfit', var(--font-sans);
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--color-primary);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.input-content {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: rgba(0, 0, 0, 0.4);
    padding: 1rem;
    border-radius: 12px;
    border: 1px solid rgba(155, 53, 255, 0.2);
}

.mic-icon {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-primary);
    animation: pulse 2s infinite;
}

.input-text {
    font-family: var(--font-mono);
    font-size: 1rem;
    color: var(--color-text);
}

.typing-animation {
    border-right: 2px solid var(--color-primary);
    white-space: nowrap;
    overflow: hidden;
    animation: typeText 3s steps(50, end) forwards, blinkCursor 0.7s step-end infinite;
}

.output-content {
    background: rgba(0, 0, 0, 0.4);
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid rgba(0, 230, 118, 0.3);
}

.optimized-code {
    margin: 0;
    font-family: var(--font-mono);
    font-size: 0.9rem;
    line-height: 1.5;
    color: var(--color-text);
    white-space: pre-wrap;
}

.optimized-code code {
    opacity: 0;
    animation: fadeIn 1s ease-out 3s forwards;
}

/* Features Section */
.features-section {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 3rem;
    max-width: 1200px;
    width: 90%;
    margin: 5rem auto 3rem;
    padding: 0;
}

.feature-card {
    flex: 1;
    min-width: 250px;
    max-width: 350px;
    background: rgba(28, 28, 40, 0.8);
    border: 1px solid rgba(153, 69, 255, 0.2);
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border-color: rgba(153, 69, 255, 0.4);
}

.feature-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.feature-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--color-primary);
}

.feature-card p {
    font-size: 1rem;
    color: var(--color-text-dim);
}

/* Cursor Interface Section */
.cursor-interface-section {
    position: relative;
    min-height: 100vh;
    background: linear-gradient(to bottom, var(--color-background-light) 0%, var(--color-background) 100%);
    padding: 6rem 0;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.cursor-interface-container {
    width: 90%;
    max-width: 1200px;
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-bottom: 3rem;
}

@media (min-width: 992px) {
    .cursor-interface-container {
        flex-direction: row;
        align-items: stretch;
    }
}

/* Cursor Editor Window */
.cursor-window {
    flex: 3;
    background-color: #1e1e1e;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
    display: flex;
        flex-direction: column;
    min-height: 500px;
    border: 1px solid rgba(100, 100, 100, 0.3);
}

.cursor-titlebar {
    height: 40px;
    background-color: #252525;
    display: flex;
    align-items: center;
    padding: 0 15px;
    border-bottom: 1px solid rgba(100, 100, 100, 0.3);
}

.titlebar-controls {
    display: flex;
    gap: 8px;
}

.control-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.control-dot.red {
    background-color: #ff5f56;
}

.control-dot.yellow {
    background-color: #ffbd2e;
}

.control-dot.green {
    background-color: #27c93f;
}

.titlebar-title {
    margin-left: 15px;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    font-family: var(--font-sans);
}

.cursor-content {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.code-editor {
    display: flex;
    width: 100%;
    height: 100%;
    overflow: auto;
}

.line-numbers {
    display: flex;
    flex-direction: column;
    padding: 1rem 0.5rem;
    background-color: #1e1e1e;
    color: #6e7681;
    font-family: var(--font-mono);
    font-size: 0.85rem;
    text-align: right;
    min-width: 40px;
    user-select: none;
}

.code-area {
    flex: 1;
    padding: 1rem;
    font-family: var(--font-mono);
    font-size: 0.85rem;
    line-height: 1.5;
    color: #abb2bf;
    overflow: auto;
}

.code-content {
    margin: 0;
    white-space: pre;
}

/* Cursor AI Panel */
.cursor-ai-panel {
    flex: 2;
    background-color: #1e1e1e;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
    display: flex;
    flex-direction: column;
    min-height: 500px;
    border: 1px solid rgba(100, 100, 100, 0.3);
    transition: all 0.3s ease;
}

.cursor-ai-panel:hover {
    border-color: rgba(153, 69, 255, 0.3);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.5);
}

.panel-header {
    height: 40px;
    background-color: #252525;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
    border-bottom: 1px solid rgba(100, 100, 100, 0.3);
}

.panel-title {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

.voice-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 8px;
    border-radius: 4px;
    background-color: rgba(153, 69, 255, 0.2);
    cursor: pointer;
    user-select: none;
    transition: all 0.2s ease;
}

.voice-toggle.active {
    background-color: rgba(153, 69, 255, 0.4);
}

.voice-toggle span {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.9);
}

.voice-toggle .mic-icon {
    width: 14px;
    height: 14px;
    color: var(--color-primary);
}

.ai-conversation {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.message-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.user-message,
.ai-message {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    max-width: 100%;
}

.ai-message {
    align-items: flex-start;
}

.message-header {
    display: flex;
    align-items: center;
    gap: 8px;
}

.user-avatar,
.ai-avatar {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 4px;
}

.user-avatar {
    background-color: rgba(153, 69, 255, 0.3);
    color: rgba(255, 255, 255, 0.9);
}

.ai-avatar {
    background-color: rgba(20, 241, 149, 0.3);
    color: rgba(255, 255, 255, 0.9);
}

.message-time {
    font-size: 0.7rem;
    color: rgba(255, 255, 255, 0.5);
}

.message-content {
    font-family: var(--font-sans);
    font-size: 0.9rem;
    line-height: 1.5;
    color: rgba(255, 255, 255, 0.9);
    background-color: rgba(40, 40, 40, 0.6);
    padding: 0.8rem 1rem;
    border-radius: 8px;
    word-break: break-word;
}

.ai-message .message-content {
    background-color: rgba(20, 241, 149, 0.15);
}

.ai-typing {
    display: flex;
    gap: 4px;
    align-items: center;
}

.ai-typing .dot {
    width: 6px;
    height: 6px;
    background-color: var(--color-primary);
    border-radius: 50%;
    animation: aiTypingDot 1.5s infinite ease-in-out;
}

.ai-typing .dot:nth-child(2) {
    animation-delay: 0.2s;
}

.ai-typing .dot:nth-child(3) {
    animation-delay: 0.4s;
}

.prompt-input {
    padding: 1rem;
    border-top: 1px solid rgba(100, 100, 100, 0.3);
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.voice-recording-indicator {
    display: flex;
    align-items: center;
    gap: 1rem;
    background-color: rgba(0, 0, 0, 0.3);
    padding: 0.8rem 1rem;
    border-radius: 8px;
    border: 1px solid rgba(153, 69, 255, 0.3);
        opacity: 0;
    height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.voice-recording-indicator.active {
        opacity: 1;
    height: 50px;
}

.recording-wave {
    display: flex;
    align-items: center;
    gap: 3px;
    height: 20px;
}

.wave-bar {
    width: 3px;
    height: 100%;
    background-color: var(--color-primary);
    border-radius: 3px;
    animation: soundWave 1.2s infinite ease-in-out;
}

.wave-bar:nth-child(2) {
    animation-delay: 0.2s;
}

.wave-bar:nth-child(3) {
    animation-delay: 0.4s;
}

.wave-bar:nth-child(4) {
    animation-delay: 0.6s;
}

.wave-bar:nth-child(5) {
    animation-delay: 0.8s;
}

.recording-text {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.8);
}

.input-buttons {
    display: flex;
    gap: 1rem;
}

.mic-button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(153, 69, 255, 0.3);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.mic-button svg {
    width: 20px;
    height: 20px;
    color: #ffffff;
}

.mic-button:hover,
.mic-button.active {
    background-color: rgba(153, 69, 255, 0.6);
    transform: scale(1.05);
}

.mic-button.active {
    animation: pulseMic 2s infinite;
}

.cancel-button,
.apply-button {
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-family: var(--font-sans);
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.2s ease;
}
    z-index: 2;
    animation: pulse 2s ease-in-out infinite;
.cancel-button {
    background-color: transparent;
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: rgba(255, 255, 255, 0.8);
}

    animation: float 4s ease-in-out infinite reverse;
}

.apply-button {
    background-color: rgba(20, 241, 149, 0.3);
    border: 1px solid rgba(20, 241, 149, 0.3);
    color: rgba(255, 255, 255, 0.9);
}

.apply-button:hover {
    background-color: rgba(20, 241, 149, 0.5);
}

.interaction-guide {
    text-align: center;
    margin-bottom: 2rem;
}

.interaction-guide p {
    margin-bottom: 1rem;
    font-size: 1rem;
    color: var(--color-text-dim);
}

.voice-example-selector {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.voice-example-button {
    background-color: rgba(10, 10, 10, 0.6);
    border: 1px solid rgba(50, 50, 50, 0.5);
    color: var(--color-text-dim);
    font-family: var(--font-sans);
    font-size: 0.9rem;
    padding: 0.6rem 1.2rem;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.voice-example-button:hover {
    background-color: rgba(20, 20, 20, 0.8);
    color: var(--color-text);
    border-color: rgba(100, 100, 100, 0.5);
}

.voice-example-button.active {
    background: linear-gradient(135deg, var(--color-secondary), var(--color-primary));
    color: white;
    border-color: transparent;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

@keyframes aiTypingDot {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.6;
    }
    30% {
        transform: translateY(-5px);
        opacity: 1;
    }
}

@keyframes soundWave {
    0%, 100% {
        height: 4px;
    }
    50% {
        height: 16px;
    }
}

@keyframes pulseMic {
    0% {
        box-shadow: 0 0 0 0 rgba(153, 69, 255, 0.5);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(153, 69, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(153, 69, 255, 0);
    }
}

/* Animations */
@keyframes floatIn {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-12px);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes typeText {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blinkCursor {
    from, to {
        border-color: transparent;
    }
    50% {
        border-color: var(--color-primary);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .main-title {
        font-size: 5rem;
    }

    .subtitle {
        font-size: 1.5rem;
    }

    .section-header h2 {
        font-size: 2.5rem;
    }

    .voice-icon {
        right: -40px;
        width: 30px;
        height: 30px;
    }
}

@media (min-width: 992px) {
    .voice-icon {
        display: block; /* Show on larger screens */
    }
}

@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
    }

    .nav-links {
        position: fixed;
        top: var(--nav-height);
        left: -100%;
        flex-direction: column;
        gap: 0;
        width: 100%;
        background-color: rgba(5, 5, 5, 0.95);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        padding: 1rem 0;
        text-align: center;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    }

    .nav-links.active {
    left: 0;
    }

    .nav-links li {
    width: 100%;
    }

    .nav-link {
        display: block;
        padding: 1rem 0;
    }

    .nav-link::after {
        display: none;
    }

    .mobile-menu-toggle.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .mobile-menu-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .mobile-menu-toggle.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .top-section {
        min-height: 100vh;
        height: auto;
        padding: calc(var(--nav-height) + 2rem) 1rem 3rem;
        align-items: flex-start;
    }

    .hero-content {
        padding: 2rem 0;
    }

    .title-container {
        padding-top: 0;
    }

    .main-title {
        font-size: 3.5rem;
        line-height: 1.1;
    }

    .subtitle {
        font-size: 1.3rem;
        white-space: unset;
        border-right: none;
        animation: fadeIn 1.5s ease forwards 0.5s;
    width: 100%;
        opacity: 0;
        max-width: 90%;
    }

    .logo-text {
        font-size: 1.6rem;
    }

    .floating-prompt-bubbles {
        height: 300px;
    }

    .voice-command-container {
        padding: 1.5rem;
    gap: 2rem;
    }

    .features-section {
        flex-direction: column;
        align-items: center;
    }

    .feature-card {
    width: 100%;
        max-width: 100%;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .section-header p {
    font-size: 1rem;
    }

    .human-request-text {
        font-size: 1rem;
    }

    .optimized-prompt {
        font-size: 0.8rem;
    }

    .extension-logos {
    gap: 1.5rem;
    }

    .transformation-section {
    padding: 5rem 0;
    }

    .stage {
        min-height: 250px;
    }

    .cursor-interface-section {
        padding: 5rem 0 3rem;
    }

    .cursor-window, .cursor-ai-panel {
        min-height: 400px;
    }

    .code-editor, .ai-conversation {
        font-size: 0.8rem;
    }

    .interaction-guide {
        margin-top: 1rem;
    }

    .section-header {
        margin-bottom: 3rem;
    }

    .transformation-examples {
        margin-top: 2rem;
    }

    .bottom-section {
        padding: 4rem 0;
    }

    .features-section {
        margin: 3rem auto 2rem;
    gap: 2rem;
    }
}

@media (max-width: 480px) {
    .logo-icon {
        width: 24px;
        height: 24px;
    }

    .logo-text {
        font-size: 1.4rem;
    }

    .logo-container svg {
        width: 30px;
    }

    .top-section {
        padding: calc(var(--nav-height) + 1rem) 1rem 2rem;
    }

    .hero-content {
        padding: 1rem 0.5rem;
        margin-top: 0;
    }

    .title-container {
        padding-top: 0;
    }

    .main-title {
        font-size: 2.8rem;
    margin-bottom: 1.5rem;
    }

    .subtitle {
        font-size: 1.1rem;
        margin-top: 1.5rem;
        max-width: 100%;
        padding: 0 1rem;
    }

    .primary-button {
        padding: 0.8rem 1.8rem;
        font-size: 1rem;
    }

    .voice-command-container {
        padding: 1.2rem;
        gap: 1.5rem;
    }

    .input-content,
    .output-content {
        padding: 0.8rem;
    }

    .optimized-code {
        font-size: 0.8rem;
    }

    .transformation-stages {
        gap: 1rem;
    }

    .example-button {
        font-size: 0.8rem;
        padding: 0.5rem 1rem;
    }

    .extension-logos {
        gap: 1rem;
    }

    .extension-logo img {
        width: 25px;
        height: 25px;
    }

    .extension-logo span {
        font-size: 0.8rem;
    }

    .floating-prompt-bubbles {
        height: 380px;
    }

    .prompt-bubble {
        width: 75%;
        max-width: 200px;
        left: 12.5% !important;
        right: auto !important;
        padding: 10px 16px;
    }

    .prompt-bubble:nth-child(1) {
        top: 0%;
    }

    .prompt-bubble:nth-child(2) {
        top: 25%;
    }

    .prompt-bubble:nth-child(3) {
        top: 50%;
    }

    .prompt-bubble:nth-child(4) {
        top: 75%;
    }

    .cursor-interface-section {
        padding: 4rem 0 2rem;
    }

    .cursor-window, .cursor-ai-panel {
        min-height: 350px;
    }

    .code-content, .message-content {
        font-size: 0.75rem;
    }

    .voice-example-selector {
        gap: 0.5rem;
    }

    .voice-example-button {
        padding: 0.5rem 0.8rem;
        font-size: 0.75rem;
    }

    .section-header {
        margin-bottom: 2rem;
    }

    .transformation-examples {
        margin-top: 1.5rem;
    }

    .bottom-section {
        padding: 3rem 0;
    }

    .features-section {
        margin: 2rem auto 1.5rem;
        gap: 1.5rem;
    }
}

/* New animations for voice elements */
@keyframes pulseWave {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 230, 118, 0.6);
        transform: translateX(-50%) scale(0.8);
    }
    70% {
        box-shadow: 0 0 0 15px rgba(0, 230, 118, 0);
        transform: translateX(-50%) scale(1.1);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 230, 118, 0);
        transform: translateX(-50%) scale(0.8);
    }
}

@keyframes voiceWave {
    0%, 100% {
        width: 90%;
        left: 5%;
        opacity: 0.4;
    }
    50% {
        width: 110%;
        left: -5%;
        opacity: 0.7;
    }
}

@keyframes pulseScale {
    0%, 100% {
        transform: translateY(-50%) scale(1);
    }
    50% {
        transform: translateY(-50%) scale(1.15);
    }
}

.voice-visualization {
    position: absolute;
    z-index: 5;
    filter: drop-shadow(0 0 8px rgba(0, 230, 118, 0.5));
    border-radius: 8px;
    opacity: 0.8;
    transition: opacity 0.3s ease, transform 0.3s ease;
    transform: scale(1);
}

.title-container:hover .voice-visualization {
    opacity: 1;
    transform: scale(1.05);
}

/* New keyframes for enhanced voice animations */
@keyframes pulseRing {
    0% {
        opacity: 0.3;
        transform: translate(-50%, -50%) scale(0.5);
    }
    50% {
        opacity: 0.1;
        transform: translate(-50%, -50%) scale(1.2);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(1.8);
    }
}

@keyframes moveGradient {
    0% {
        background-position: 0% 0%;
    }
    100% {
        background-position: 200% 0%;
    }
}

/* Updated Intro Section Styles - More Minimalistic and Modern */
.intro-title-container {
    display: flex;
        align-items: center;
    gap: 1.2rem;
    margin-bottom: 1.5rem;
}

.intro-label {
    font-family: var(--font-sans);
    font-size: 1.4rem;
    font-weight: 600;
    letter-spacing: 0.2em;
    text-transform: uppercase;
    color: var(--color-primary);
    background: rgba(20, 241, 149, 0.1);
    padding: 0.6rem 1.4rem;
    border-radius: 30px;
    display: inline-block;
    position: relative;
    top: -0.5rem;
    box-shadow: 0 4px 12px rgba(20, 241, 149, 0.15);
}

.intro-title {
    font-family: 'Instrument', var(--font-sans);
    font-size: 7rem;
    font-weight: 800;
    line-height: 0.9;
    color: white;
    margin: 0;
    letter-spacing: -3px;
    position: relative;
    background: linear-gradient(to right, #ffffff, #f0f0f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 4px 25px rgba(0, 0, 0, 0.2);
}

.intro-title .highlight {
    color: var(--color-primary);
    background: linear-gradient(135deg, var(--color-primary), #9ef4ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
    display: inline-block;
}

.intro-title .highlight::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 6px;
    bottom: 5px;
    left: 0;
    background: linear-gradient(90deg, var(--color-primary), transparent);
    opacity: 0.3;
    border-radius: 5px;
    z-index: -1;
}

.intro-description {
    font-family: var(--font-sans);
    font-size: 1.8rem;
    font-weight: 400;
    line-height: 1.4;
    color: var(--color-text);
    margin-bottom: 3rem;
    max-width: 90%;
    opacity: 0.9;
}

/* Updated Feature List Styles - More Prominent and Modern */
.intro-feature-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2.5rem;
    margin-bottom: 3rem;
}

.intro-feature-item {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    padding: 1.5rem;
    border-radius: 16px;
    background: rgba(15, 15, 25, 0.3);
    /* backdrop-filter: blur(10px); */
    border-left: 3px solid var(--color-primary);
    /* transition: all 0.3s ease; */
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.intro-feature-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(120deg, rgba(20, 241, 149, 0.05) 0%, transparent 50%);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.intro-feature-item:hover {
    transform: translateY(-5px);
    border-left-width: 6px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25), 0 5px 15px rgba(20, 241, 149, 0.2);
}

.intro-feature-item:hover::before {
    opacity: 1;
}

.intro-feature-item .feature-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 64px;
    height: 64px;
    border-radius: 14px;
    background: rgba(20, 241, 149, 0.1);
    box-shadow: 0 8px 25px rgba(20, 241, 149, 0.2);
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.intro-feature-item:hover .feature-icon {
    transform: scale(1.1) rotate(5deg);
    /* background: rgba(20, 241, 149, 0.15); */
    /* box-shadow: 0 12px 30px rgba(20, 241, 149, 0.3); */
}

.intro-feature-item .feature-icon svg {
    width: 32px;
    height: 32px;
    stroke: var(--color-primary);
    /* filter: drop-shadow(0 2px 8px rgba(20, 241, 149, 0.4)); */
    transition: all 0.3s ease;
}

.intro-feature-item:hover .feature-icon svg {
    transform: scale(1.15);
    /* filter: drop-shadow(0 4px 12px rgba(20, 241, 149, 0.5)); */
}

.intro-feature-item .feature-text {
    display: flex;
    flex-direction: column;
    gap: 0.4rem;
    flex: 1;
}

.intro-feature-item .feature-title {
    color: white;
    font-weight: 700;
    font-size: 1.35rem;
    font-family: 'Space Grotesk', var(--font-sans);
    letter-spacing: 0.02em;
    display: block;
    transition: all 0.3s ease;
    margin-bottom: 0.4rem;
}

.intro-feature-item:hover .feature-title {
    color: var(--color-primary);
    transform: translateX(3px);
}

.intro-feature-item p {
    font-size: 1.05rem;
    color: rgba(255, 255, 255, 0.85);
    line-height: 1.5;
    margin: 0;
    position: relative;
    z-index: 1;
    transition: color 0.3s ease;
    font-family: 'Inter', var(--font-sans);
    font-weight: 400;
}

.intro-feature-item:hover p {
    color: rgba(255, 255, 255, 0.95);
}

/* Responsive Adjustments */
@media (max-width: 992px) {
    .intro-title {
        font-size: 2.8rem;
    }

    .intro-label {
        font-size: 0.9rem;
    }

    .intro-feature-item .feature-icon {
        width: 45px;
        height: 45px;
    }

    .intro-feature-item .feature-title {
        font-size: 1.1rem;
    }

    /* Add responsive font size for tagline */
    .intro-tagline {
        font-size: 1.4rem;
        max-width: 90%;
    }
}

@media (max-width: 900px) {
  .nav-links {
    position: fixed;
    top: 60px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 60px);
    flex-direction: column;
    background-color: rgba(8, 8, 20, 0.95);
    padding: 2rem 0;
    transition: left var(--transition-base);
    justify-content: flex-start;
    align-items: center;
  }
  
  .nav-links.active {
    left: 0;
  }
  
  .nav-links li {
    margin: 1.5rem 0;
  }
  
  .nav-link {
    font-size: 1.1rem;
  }
  
  .mobile-menu-toggle {
    display: block;
  }
  
  .nav-buttons {
    display: none;
  }
  
  .nav-links.active ~ .nav-buttons {
    display: flex;
    flex-direction: column;
    position: fixed;
    top: 50%;
    left: 0;
    width: 100%;
    align-items: center;
  }
  
  .nav-links.active ~ .nav-buttons .nav-button {
    margin: 0.5rem 0;
    width: 80%;
    text-align: center;
  }
}

    .intro-feature-item .feature-icon {
        width: 35px;
        height: 35px;
    }

    .intro-feature-item .feature-icon svg {
        width: 18px;
        height: 18px;
    }

    .intro-feature-item .feature-title {
        font-size: 0.9rem;
    }

    .intro-feature-item p {
        font-size: 0.85rem;
    }

    /* Adjust tagline for mobile */
    .intro-tagline {
        font-size: 1.1rem;
        margin: 1rem 0;
        line-height: 1.6;
        max-width: 100%;
        padding-left: 0.8rem;
    }

/* Intro Section */
.intro-section {
    background: linear-gradient(180deg, var(--color-background) 0%, rgba(15, 15, 25, 0.98) 100%);
    padding: 7rem 0;
    position: relative;
    overflow: hidden;
}

.intro-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, rgba(20, 241, 149, 0) 0%, rgba(20, 241, 149, 0.7) 50%, rgba(20, 241, 149, 0) 100%);
    z-index: 2;
}

.intro-section::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: radial-gradient(circle at 30% 20%, rgba(20, 241, 149, 0.05) 0%, transparent 60%);
    z-index: 0;
    pointer-events: none;
}

.intro-container {
    display: flex;
    flex-direction: column;
    gap: 3rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    position: relative;
    z-index: 5;
}

.intro-header-row {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    gap: 3rem;
    width: 100%;
}

.intro-header {
    flex: 1;
}

.intro-video {
    flex: 1;
    margin: 0;
    max-width: 560px;
}

.intro-content {
    width: 100%;
}

/* Video styles */
.video-wrapper {
    width: 100%;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.7), 0 0 30px rgba(20, 241, 149, 0.2);
    border: 1px solid rgba(20, 241, 149, 0.2);
    position: relative;
    aspect-ratio: 16/9;
    transform: translateY(0);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.video-wrapper:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.8), 0 0 40px rgba(20, 241, 149, 0.4);
    border-color: rgba(20, 241, 149, 0.4);
}

.video-wrapper::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(20, 241, 149, 0.2) 0%, transparent 100%);
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.video-wrapper:hover::after {
    opacity: 1;
}

.video-wrapper iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #0f0f19;
}

/* CTA styles */
.intro-cta {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-top: 0.5rem;
}

.intro-cta-button {
    background: linear-gradient(135deg, var(--color-primary), #9ef4ff);
    color: #050505;
    font-family: var(--font-sans);
    font-weight: 700;
    font-size: 1rem;
    padding: 1rem 2rem;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 20px rgba(20, 241, 149, 0.25);
}

.intro-cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 25px rgba(20, 241, 149, 0.35);
}

.intro-cta-button:active {
    transform: translateY(1px);
    box-shadow: 0 5px 15px rgba(20, 241, 149, 0.2);
}

.intro-cta-link {
    color: var(--color-text);
    font-weight: 600;
    font-size: 1rem;
    text-decoration: none;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.intro-cta-link:hover {
    color: var(--color-primary);
}

.intro-cta-link .arrow {
    transition: transform 0.3s ease;
}

.intro-cta-link:hover .arrow {
    transform: translateX(3px);
}

/* Media layout queries */
@media (max-width: 992px) {
    .intro-header-row {
        flex-direction: column;
        gap: 2.5rem;
        align-items: center;
    }

    .intro-header {
    text-align: center;
    width: 100%;
    }

    .intro-video {
        width: 100%;
        max-width: 100%;
    }

    .intro-description {
        max-width: 100%;
        text-align: center;
    }
}

@media (max-width: 768px) {
    .intro-feature-list {
        grid-template-columns: 1fr;
    }

    .intro-section {
        padding: 5rem 0;
    }

    .intro-container {
        gap: 2rem;
    }
}

@media (max-width: 480px) {
    .intro-header-row {
    gap: 1.5rem;
    }
}

.intro-title.logo-style {
    font-family: var(--font-display);
    font-size: 4.5rem;
    font-weight: 700;
    letter-spacing: -0.02em;
    background: none;
    -webkit-background-clip: initial;
    -webkit-text-fill-color: white;
    color: white;
    text-shadow: none;
    line-height: 1;
    white-space: nowrap;
    display: inline-block;
}

.intro-title.logo-style .meet-text {
    color: #ffffff;
    font-family: var(--font-display);
    font-weight: 700;
    font-size: 0.75em;
    margin-right: 0.3em;
    letter-spacing: 0;
    position: relative;
    text-transform: none;
    background: none;
    -webkit-background-clip: initial;
    -webkit-text-fill-color: white;
    opacity: 1;
}

.intro-title.logo-style .hype-text {
    color: var(--color-primary);
    background: none;
    -webkit-background-clip: initial;
    -webkit-text-fill-color: var(--color-primary);
    position: relative;
    display: inline-block;
}

.intro-title.logo-style .hype-text::after {
    display: none;
}

.intro-tagline {
    font-family: 'Space Grotesk', var(--font-sans);
    font-size: 1.6rem;
    color: rgba(255, 255, 255, 0.95);
    max-width: 85%;
    margin: 1.5rem 0;
    line-height: 1.7;
    letter-spacing: 0.02em;
    font-weight: 400;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.8));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: relative;
    padding-left: 1rem;
    border-left: 3px solid var(--color-primary);
}

.intro-tagline strong {
    font-weight: 600;
    color: var(--color-primary);
    background: linear-gradient(135deg, var(--color-primary), #9ef4ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* New Features Overview Section */
.features-overview-section {
    position: relative;
    background: linear-gradient(135deg, #0d1117 0%, #161b22 100%);
    border-radius: 24px;
    margin: 4rem auto;
    padding: 3rem 2rem;
    max-width: 1200px;
    width: 90%;
    overflow: hidden;
    box-shadow: 0 20px 80px -20px rgba(20, 241, 149, 0.25);
}

.features-overview-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--color-primary), transparent);
    z-index: 1;
}

.features-overview-section::after {
    content: '';
    position: absolute;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(20, 241, 149, 0.15) 0%, rgba(20, 241, 149, 0) 70%);
    top: -100px;
    right: -100px;
    border-radius: 50%;
    z-index: 0;
    animation: pulse 8s infinite ease-in-out;
}

.features-overview-container {
    position: relative;
    z-index: 2;
}

.features-overview-title {
    font-family: var(--font-display);
    font-size: 2.8rem;
    font-weight: 700;
    color: white;
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
    display: inline-block;
}

/* Enhanced Feature Item Styling */
.intro-feature-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.intro-feature-item {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.05);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    height: 100%;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.intro-feature-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(20, 241, 149, 0.1) 0%, rgba(20, 241, 149, 0) 60%);
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.intro-feature-item:hover {
    transform: translateY(-5px);
    border-color: rgba(20, 241, 149, 0.3);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.intro-feature-item:hover::before {
    opacity: 1;
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: rgba(20, 241, 149, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    border: 1px solid rgba(20, 241, 149, 0.2);
}

.intro-feature-item:hover .feature-icon {
    background: rgba(20, 241, 149, 0.2);
    transform: scale(1.1);
    box-shadow: 0 0 20px rgba(20, 241, 149, 0.3);
}

.feature-icon svg {
    width: 30px;
    height: 30px;
    stroke: var(--color-primary);
    transition: all 0.3s ease;
}

.intro-feature-item:hover .feature-icon svg {
    stroke-width: 2;
    transform: scale(1.1);
}

.feature-text {
    flex: 1;
}

.feature-title {
    font-family: var(--font-display);
    font-size: 1.4rem;
    font-weight: 600;
    color: white;
    margin-bottom: 0.8rem;
    display: block;
    position: relative;
}

.feature-title::before {
    content: "";
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    width: 5px;
    height: 5px;
    background: var(--color-primary);
    border-radius: 50%;
    opacity: 0;
    transition: all 0.3s ease;
}

.intro-feature-item:hover .feature-title::before {
    opacity: 1;
    left: -15px;
}

.feature-text p {
    color: var(--color-text);
    line-height: 1.6;
    font-size: 0.95rem;
    margin: 0;
}

.intro-cta {
    text-align: center;
    margin-top: 3rem;
}

.intro-cta-button {
    background: var(--color-primary);
    color: #000;
    font-family: var(--font-display);
    font-weight: 600;
    font-size: 1rem;
    padding: 0.8rem 1.8rem;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 20px rgba(20, 241, 149, 0.3);
    z-index: 1;
}

.intro-cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: all 0.5s ease;
    z-index: -1;
}

.intro-cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 7px 25px rgba(20, 241, 149, 0.4);
}

.intro-cta-button:hover::before {
    left: 100%;
}

@keyframes pulse {
    0%, 100% {
        opacity: 0.5;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
    transform: scale(1.05);
    }
}

/* Small Glow Effect for Icons */
.feature-icon::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 80%;
    height: 80%;
    background: rgba(20, 241, 149, 0.1);
    border-radius: 50%;
    filter: blur(10px);
    z-index: -1;
    opacity: 0;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
}

.intro-feature-item:hover .feature-icon::after {
    opacity: 1;
}

/* Feature Index Numbers */
.intro-feature-item {
    counter-increment: feature-counter;
    position: relative;
}

.intro-feature-item::after {
    content: "0" counter(feature-counter);
    position: absolute;
    bottom: 1rem;
    right: 1rem;
    font-family: var(--font-mono);
    font-size: 2.5rem;
    color: rgba(255, 255, 255, 0.03);
    font-weight: 700;
    line-height: 1;
    transition: all 0.3s ease;
    pointer-events: none;
}

.intro-feature-item:hover::after {
    color: rgba(20, 241, 149, 0.08);
    transform: scale(1.2);
}

/* Media Queries */
@media (max-width: 768px) {
    .intro-feature-list {
        grid-template-columns: 1fr;
    }

    .features-overview-title {
        font-size: 2.2rem;
    }

    .features-overview-section {
        margin: 2rem auto;
        padding: 2rem 1.5rem;
        border-radius: 16px;
    }
}

@media (min-width: 1200px) {
    .intro-feature-list {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Speed Benefits Section */
.speed-benefits-section {
    padding: 6rem 0;
    position: relative;
    background: linear-gradient(135deg, rgba(15, 15, 25, 0.95) 0%, rgba(20, 20, 35, 0.98) 100%);
    overflow: hidden;
    z-index: 2;
}

.speed-benefits-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    position: relative;
    z-index: 1;
}

.speed-benefits-title {
    font-family: var(--font-display);
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 2rem;
    position: relative;
    display: inline-block;
}

.speed-benefits-title::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 0;
    width: 40%;
    height: 3px;
    background: linear-gradient(90deg, var(--color-primary), transparent);
}

.speed-benefits-metrics {
    display: flex;
    gap: 2rem;
    margin-bottom: 2.5rem;
}

.metric {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.metric-number {
    font-family: var(--font-space);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--color-primary);
}

.metric-label {
    font-size: 1rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.8);
}

.speed-benefits-description {
    font-size: 1.125rem;
    line-height: 1.6;
    margin-bottom: 2.5rem;
    color: rgba(255, 255, 255, 0.9);
    max-width: 90%;
}

.speed-benefits-description .highlight {
    font-weight: 600;
    color: var(--color-primary);
}

.speed-benefits-cta {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.speed-benefits-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    background-color: var(--color-primary);
    color: #111827;
    font-weight: 600;
    font-size: 1rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.speed-benefits-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(20, 241, 149, 0.3);
}

.speed-benefits-guarantee {
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.7);
}

/* Comparison Chart Styles */
.comparison-chart {
    width: 100%;
    max-width: 450px;
    margin: 0 auto;
}

.comparison-item {
    margin-bottom: 2rem;
    position: relative;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 1rem;
    padding: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.comparison-item:last-child {
    margin-bottom: 0;
}

.comparison-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.comparison-item.voice {
    border: 1px solid rgba(20, 241, 149, 0.3);
    background: rgba(20, 241, 149, 0.05);
}

.comparison-item.voice:hover {
    box-shadow: 0 10px 30px rgba(20, 241, 149, 0.1);
}

.comparison-label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.75rem;
    font-weight: 600;
    font-size: 1.1rem;
}

.time-indicator {
    font-size: 1rem;
}

.speed-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: var(--color-primary);
    color: #111827;
    font-size: 0.75rem;
    font-weight: 700;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    margin-left: 0.5rem;
}

.comparison-bar {
    height: 2.5rem;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    margin-bottom: 0.5rem;
    position: relative;
    overflow: hidden;
}

.typing-bar {
    background-color: rgba(99, 102, 241, 0.2);
    border: 1px solid rgba(99, 102, 241, 0.3);
}

.voice-bar {
    background-color: rgba(20, 241, 149, 0.1);
    border: 1px solid rgba(20, 241, 149, 0.3);
}

.typing-indicator {
    display: flex;
    align-items: center;
    position: absolute;
    top: 50%;
    left: 1rem;
    transform: translateY(-50%);
    gap: 0.25rem;
}

.typing-dot {
    width: 6px;
    height: 6px;
    background-color: rgba(99, 102, 241, 0.8);
    border-radius: 50%;
    opacity: 0.5;
    animation: typingPulse 1s infinite;
}

.typing-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typingPulse {
    0% { opacity: 0.5; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.2); }
    100% { opacity: 0.5; transform: scale(1); }
}

.voice-pulse {
    position: absolute;
    top: 50%;
    left: 1rem;
    transform: translateY(-50%);
    width: 70%;
    height: 0.75rem;
    background-image: linear-gradient(90deg,
        rgba(20, 241, 149, 0.8) 0%,
        rgba(20, 241, 149, 0.9) 50%,
        rgba(20, 241, 149, 0.4) 100%);
    border-radius: 4px;
    animation: pulsate 2s infinite;
}

@keyframes pulsate {
    0% { opacity: 0.7; transform: translateY(-50%) scaleY(0.8); }
    50% { opacity: 1; transform: translateY(-50%) scaleY(1.2); }
    100% { opacity: 0.7; transform: translateY(-50%) scaleY(0.8); }
}

.comparison-time {
    font-family: var(--font-mono);
    font-size: 1rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    text-align: right;
}

/* Use Cases Section Styles */
.use-cases-section {
    position: relative;
    width: 100%;
    padding: 4rem 1rem;
}

.use-cases-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    max-width: 1200px;
    width: 90%;
    margin: 0 auto;
}

@media (max-width: 768px) {
    .features-overview-section {
        margin: 2rem auto;
        padding: 2rem 1.5rem;
        border-radius: 16px;
    }

    .use-cases-section {
        padding: 2rem 1rem;
    }

    .use-cases-container {
        grid-template-columns: 1fr;
    }
}

.use-case-header h3 {
    font-family: var(--font-display);
    font-size: 1.2rem;
    font-weight: 600;
    color: #fff;
    white-space: nowrap;
}

.user-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    margin-right: 0.8rem;
}

.user-icon svg {
    width: 24px;
    height: 24px;
}

.user-description {
    margin-bottom: 1rem;
}

.user-quote {
    margin-bottom: 1rem;
    min-height: 80px;
    display: flex;
    align-items: center;
}

.user-quote::before {
    top: -8px;
    left: 10px;
    font-size: 2rem;
}

.use-cases-cta {
    margin-top: 3rem;
}

@media (max-width: 1200px) {
    .use-cases-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .use-case-card {
        padding: 1.2rem;
    }
}

.use-cases-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 15% 50%, rgba(20, 241, 149, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 85% 30%, rgba(20, 241, 149, 0.08) 0%, transparent 50%);
    z-index: 0;
}

.use-case-card {
    background: rgba(25, 25, 35, 0.7);
    border-radius: 16px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.08);
    padding: 1.2rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.use-case-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    justify-content: center;
}

.user-description p {
    font-size: 0.9rem;
    line-height: 1.5;
    color: rgba(255, 255, 255, 0.85);
    margin: 0;
}

.user-quote {
    padding: 1.2rem;
    margin-bottom: 0;
    min-height: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.user-quote blockquote {
    font-size: 0.95rem;
    line-height: 1.5;
    margin: 0;
    padding: 0.5rem 0;
}

.common-tasks {
    padding: 1rem;
    margin-top: auto;
}

.common-tasks h4 {
    font-size: 0.9rem;
    margin: 0 0 0.6rem 0;
}

.common-tasks ul {
    margin: 0;
    padding: 0 0 0 1rem;
}

.common-tasks li {
    font-size: 0.85rem;
    margin-bottom: 0.3rem;
    line-height: 1.4;
}

@media (max-width: 1200px) {
    .use-cases-container {
        grid-template-columns: repeat(2, 1fr);
    }

    .use-case-card {
        padding: 1rem;
        min-height: 160px;
    }
}

@media (max-width: 768px) {
    .use-cases-container {
        grid-template-columns: 1fr;
        max-width: 550px;
    }
}

.use-case-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2),
                0 0 15px rgba(20, 241, 149, 0.1);
    border-color: rgba(20, 241, 149, 0.2);
}

.use-case-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.05),
        transparent
    );
    transition: all 0.6s ease;
}

.use-case-card:hover::before {
    left: 100%;
}

.use-case-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.tool-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.tool-icon img {
    width: 30px;
    height: 30px;
    object-fit: contain;
}

.cursor-icon {
    background: rgba(42, 42, 50, 0.7);
}

.vscode-icon {
    background: rgba(0, 122, 204, 0.1);
}

.copilot-icon {
    background: rgba(39, 39, 42, 0.7);
}

.use-case-header h3 {
    font-family: var(--font-display);
    font-size: 1.5rem;
    font-weight: 600;
    color: #fff;
}

.use-case-header h3 .highlight {
    color: var(--color-primary);
}

.use-case-content {
    display: flex;
    flex-direction: column;
}

.use-case-scenario {
    background: rgba(15, 15, 20, 0.5);
    border-radius: 10px;
    padding: 1.2rem;
    margin-bottom: 1.5rem;
}

.scenario-step {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.step-number {
    background: var(--color-primary);
    color: #000;
    font-family: var(--font-display);
    font-weight: 700;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.8rem;
    flex-shrink: 0;
}

.scenario-step p {
    font-size: 0.95rem;
    line-height: 1.5;
    margin: 0;
    color: #eee;
    font-style: italic;
}

.scenario-arrow {
    text-align: center;
    margin: 1rem 0;
    color: var(--color-primary);
}

.scenario-arrow svg {
    width: 24px;
    height: 24px;
}

.scenario-result {
    background: rgba(20, 241, 149, 0.05);
    border-left: 3px solid var(--color-primary);
    padding: 1rem;
    border-radius: 0 8px 8px 0;
}

.scenario-result pre {
    margin: 0;
    white-space: pre-wrap;
}

.scenario-result code {
    font-family: var(--font-code);
    font-size: 0.85rem;
    color: #d1d5db;
    line-height: 1.5;
}

.use-case-benefits {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.benefit {
    display: flex;
    align-items: center;
}

.benefit svg {
    width: 18px;
    height: 18px;
    color: var(--color-primary);
    margin-right: 0.8rem;
    flex-shrink: 0;
}

.benefit span {
    font-size: 0.95rem;
    color: rgba(255, 255, 255, 0.8);
}

.use-cases-cta {
    text-align: center;
    margin-top: 4rem;
    position: relative;
    z-index: 1;
}

.use-cases-cta h3 {
    font-family: var(--font-display);
    font-size: 1.8rem;
    font-weight: 600;
    color: #fff;
    margin-bottom: 1.5rem;
}

.glow-button {
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
    --shine-position: 50%;
}

.glow-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 0 20px rgba(20, 241, 149, 0.4),
                0 0 40px rgba(20, 241, 149, 0.1);
    letter-spacing: 0.08em;
}

.glow-button::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
        60deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transform: rotate(30deg) translateX(calc(var(--shine-position) - 50%));
    transition: all 0.6s ease;
        opacity: 0;
    }

.glow-button:hover::after {
        opacity: 1;
}

.no-credit-card {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.5);
    margin-top: 1rem;
}

/* Responsive styles */
@media (max-width: 992px) {
    .use-cases-section {
        padding: 5rem 1.5rem;
    }

    .use-cases-section .section-header h2 {
        font-size: 2.8rem;
    }
}

@media (max-width: 768px) {
    .use-cases-container {
        grid-template-columns: 1fr;
        max-width: 550px;
    }

    .use-cases-section .section-header h2 {
        font-size: 2.4rem;
    }

    .use-cases-cta h3 {
        font-size: 1.6rem;
    }
}

@media (max-width: 480px) {
    .use-cases-section {
        padding: 4rem 1rem;
    }

    .use-cases-section .section-header h2 {
        font-size: 2rem;
    }

    .use-case-card {
        padding: 1.5rem;
    }

    .use-case-header h3 {
        font-size: 1.3rem;
    }
}

.user-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.user-icon::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, rgba(20, 241, 149, 0.3) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.5s ease;
}

.user-icon svg {
    width: 30px;
    height: 30px;
    color: var(--color-primary);
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.pro-dev-icon {
    background: rgba(20, 241, 149, 0.08);
}

.learning-dev-icon {
    background: rgba(255, 123, 0, 0.08);
}

.tech-lead-icon {
    background: rgba(94, 126, 255, 0.08);
}

.no-code-icon {
    background: rgba(255, 79, 216, 0.08);
}

.use-case-card:hover .user-icon {
    background: rgba(20, 241, 149, 0.12);
    transform: scale(1.05);
}

.use-case-card:hover .user-icon::before {
    opacity: 1;
    animation: pulse-glow 2s infinite;
}

.use-case-card:hover .user-icon svg {
    color: var(--color-primary);
    filter: drop-shadow(0 0 8px rgba(20, 241, 149, 0.4));
    transform: scale(1.1);
    animation: subtle-bounce 2s infinite;
}

.animate-icon {
    animation: pop-in 0.5s forwards;
}

@keyframes pop-in {
    0% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes pulse-glow {
    0% {
        opacity: 0.3;
    }
    50% {
        opacity: 0.7;
    }
    100% {
        opacity: 0.3;
    }
}

@keyframes subtle-bounce {
    0%, 100% {
        transform: scale(1.1) translateY(0);
    }
    50% {
        transform: scale(1.1) translateY(-2px);
    }
}

.user-description {
    margin-bottom: 1.5rem;
}

.user-description p {
    font-size: 1rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.85);
    margin: 0;
}

.user-quote {
    position: relative;
    background: rgba(15, 15, 20, 0.5);
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border-left: 3px solid var(--color-primary);
    transition: all 0.3s ease;
}

.user-quote::before {
    content: '"';
    position: absolute;
    top: -12px;
    left: 12px;
    font-family: var(--font-display);
    font-size: 3rem;
    color: var(--color-primary);
    opacity: 0.3;
    line-height: 1;
}

.user-quote blockquote {
    font-size: 0.95rem;
    line-height: 1.6;
    margin: 0;
    color: #eee;
    font-style: italic;
}

.user-quote cite {
    display: block;
    font-size: 0.85rem;
    margin-top: 0.7rem;
    text-align: right;
    color: rgba(255, 255, 255, 0.6);
    font-style: normal;
}

.common-tasks {
    background: rgba(20, 241, 149, 0.05);
    border-radius: 10px;
    padding: 1.2rem;
}

.common-tasks h4 {
    font-family: var(--font-display);
    font-size: 1rem;
    color: var(--color-primary);
    margin: 0 0 0.8rem 0;
    font-weight: 600;
}

.common-tasks ul {
    margin: 0;
    padding: 0 0 0 1.2rem;
}

.common-tasks li {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    line-height: 1.5;
}

.common-tasks li::marker {
    color: var(--color-primary);
}

@media (max-width: 992px) {
    .user-quote {
        padding: 1.2rem;
    }

    .common-tasks {
    padding: 1rem;
    }
}

@media (max-width: 768px) {
    .user-description p {
        font-size: 0.95rem;
}

    .user-quote blockquote {
    font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .user-icon {
        width: 40px;
        height: 40px;
    }

    .user-icon svg {
        width: 24px;
        height: 24px;
    }

    .user-quote {
        padding: 1rem;
    }

    .common-tasks h4 {
        font-size: 0.95rem;
    }

    .common-tasks li {
        font-size: 0.85rem;
    }
}

.use-cases-section .section-header h2 {
    font-family: var(--font-display);
    font-size: 2.6rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #fff;
}

.gradient-text {
    background: linear-gradient(90deg, #fff 0%, var(--color-primary) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
}

.use-cases-section .section-header p {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.7);
    max-width: 600px;
    margin: 0 auto;
}

/* Feature Badges and New Elements */
.feature-badge {
    background: rgba(20, 241, 149, 0.15);
    border-radius: 12px;
    padding: 0.5rem 0.8rem;
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    margin-top: 1rem;
    position: relative;
    overflow: hidden;
    align-self: flex-start;
}

.feature-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(20, 241, 149, 0.3) 0%, rgba(20, 241, 149, 0) 60%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.intro-feature-item:hover .feature-badge::before {
    opacity: 1;
}

.accuracy {
    font-family: var(--font-mono);
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--color-primary);
    line-height: 1;
}

.badge-label {
    font-size: 0.7rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    opacity: 0.8;
    color: var(--color-text);
}

/* Language Indicators */
.lang-indicators {
    display: flex;
    flex-wrap: wrap;
    gap: 0.7rem;
    margin-top: 1rem;
    justify-content: flex-start;
}

.lang-flag {
    display: inline-block;
    transition: all 0.3s ease;
    position: relative;
    cursor: help;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    overflow: hidden;
    width: 30px;
    height: 20px;
    transform-origin: center;
}

.lang-flag img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
    display: block;
}

.intro-feature-item:hover .lang-flag {
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

.lang-flag:hover {
    transform: scale(1.5) translateY(-5px);
    z-index: 10;
    box-shadow: 0 8px 16px rgba(0,0,0,0.4);
}

.lang-flag::after {
    content: attr(title);
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    font-size: 0.65rem;
    padding: 0.2rem 0.5rem;
    border-radius: 3px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    pointer-events: none;
    font-weight: 500;
    letter-spacing: 0.3px;
}

.lang-flag:hover::after {
    opacity: 1;
    visibility: visible;
    bottom: -30px;
}

/* Add flag glow effects */
.lang-flag::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 50%);
    z-index: 1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.lang-flag:hover::before {
    opacity: 1;
}

/* Flag animation on hover */
@keyframes flagPulse {
    0%, 100% {
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }
    50% {
        box-shadow: 0 4px 12px rgba(var(--color-primary-rgb), 0.5);
    }
}

.intro-feature-item:hover .lang-flag {
    animation: flagPulse 2s infinite ease-in-out;
}

/* AI Metrics */
.ai-metrics {
    display: flex;
    margin-top: 1rem;
    position: relative;
}

.ai-metric {
    background: linear-gradient(135deg, rgba(20, 241, 149, 0.15) 0%, rgba(20, 241, 149, 0.05) 100%);
    border-radius: 8px;
    padding: 0.6rem 1rem;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(20, 241, 149, 0.1);
}

.intro-feature-item:hover .ai-metric {
    transform: scale(1.05) translateY(-2px);
    border-color: rgba(20, 241, 149, 0.3);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.metric-value {
    font-family: var(--font-display);
    font-size: 1.6rem;
    font-weight: 700;
    color: var(--color-primary);
    line-height: 1;
}

.metric-label {
    font-size: 0.7rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: var(--color-text);
    opacity: 0.9;
}

/* Integration Dots */
.integration-dots {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.integration-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    position: relative;
    transition: all 0.3s ease;
}

.integration-dot:hover {
    transform: scale(1.2);
    background: var(--color-primary);
    cursor: help;
}

.integration-dot:nth-child(1) {
    background: #0078d7; /* VS Code */
}

.integration-dot:nth-child(2) {
    background: #14F195; /* Cursor */
}

.integration-dot:nth-child(3) {
    background: #fc2c60; /* JetBrains */
}

.integration-dot:nth-child(4) {
    background: #6e40c9; /* GitHub Copilot */
}

.integration-dot:nth-child(5) {
    background: linear-gradient(90deg, #0078d7, #14F195, #fc2c60, #6e40c9);
}

.integration-dot::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%) translateY(-5px);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.3rem 0.5rem;
    border-radius: 4px;
    font-size: 0.7rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    pointer-events: none;
    z-index: 10;
}

.integration-dot:hover::after {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-10px);
}

/* Adding subtle animations on hover */
@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(20, 241, 149, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(20, 241, 149, 0.8);
    }
}

.intro-feature-item:hover .feature-icon {
    animation: pulse-glow 2s infinite ease-in-out;
}

/* Smooth hover effects for cards */
.use-case-card {
    transition: transform var(--transition-medium) var(--cubic-in-out),
                box-shadow var(--transition-medium) var(--cubic-in-out),
                background-color var(--transition-medium) var(--cubic-in-out);
}

.use-case-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3),
                0 0 15px rgba(20, 241, 149, 0.15);
}

/* Smooth scale effects for feature items */
/* .intro-feature-item {
    transition: transform var(--transition-medium) var(--cubic-in-out),
                box-shadow var(--transition-medium) var(--cubic-in-out),
                background-color var(--transition-medium) var(--cubic-in-out);
} */

/* .intro-feature-item:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3),
                0 0 15px rgba(20, 241, 149, 0.2);
} */

/* Smooth button hover effects */
.primary-button,
.intro-cta-button,
.glow-button {
    transition: transform var(--transition-medium) var(--cubic-in-out),
                box-shadow var(--transition-medium) var(--cubic-in-out),
                background-color var(--transition-medium) var(--cubic-in-out),
                letter-spacing var(--transition-medium) var(--cubic-in-out);
    overflow: hidden;
}

.primary-button:hover,
.intro-cta-button:hover,
.glow-button:hover {
    transform: translateY(-3px);
    letter-spacing: 0.5px;
}

/* Scroll progress indicator */
.scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background: linear-gradient(90deg,
                rgba(20, 241, 149, 0.7),
                rgba(20, 241, 149, 1));
    z-index: 1010;
    transition: width 0.1s ease-out;
}

/* Smooth image hover effects */
img {
    transition: transform var(--transition-medium) var(--cubic-in-out),
                filter var(--transition-medium) var(--cubic-in-out);
}

a:hover img {
    transform: scale(1.05);
}

/* Smooth scroll to top button */
.scroll-to-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    background-color: rgba(20, 241, 149, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity var(--transition-medium) var(--cubic-in-out),
                transform var(--transition-medium) var(--cubic-in-out),
                background-color var(--transition-medium) var(--cubic-in-out);
    z-index: 900;
}

.scroll-to-top.visible {
    opacity: 1;
    transform: translateY(0);
}

.scroll-to-top:hover {
    background-color: rgba(20, 241, 149, 0.4);
    transform: translateY(-3px);
}

.scroll-to-top svg {
    width: 20px;
    height: 20px;
    stroke: #ffffff;
    stroke-width: 2;
    transition: transform var(--transition-medium) var(--cubic-in-out);
}

.scroll-to-top:hover svg {
    transform: translateY(-2px);
}

/* Quote Section Styling */
.quote-section {
    position: relative;
    width: 100%;
    padding: 5rem 2rem;
    background: linear-gradient(135deg, rgba(13, 17, 23, 0.95) 0%, rgba(22, 27, 34, 0.98) 100%);
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2;
}



.quote-container {
    max-width: 1200px;
    width: 90%;
    position: relative;
}

.main-quote {
    font-family: var(--font-display);
    font-size: 2.5rem;
    font-weight: 600;
    line-height: 1.3;
    color: white;
    text-align: center;
    position: relative;
    margin: 0;
    padding: 0;
    opacity: 0.9;
    word-spacing: 0.03em;
}


/* Responsive adjustments */
@media (max-width: 992px) {
    .main-quote {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .quote-section {
        padding: 4rem 1.5rem;
    }

    .main-quote {
        font-size: 1.7rem;
    }
}
   

@media (max-width: 480px) {
    .quote-section {
        padding: 3rem 1rem;
    }

    .main-quote {
        font-size: 1.4rem;
    }

    .main-quote::before {
        top: -2rem;
        left: -1rem;
        font-size: 3rem;
    }

    .main-quote::after {
        bottom: -3rem;
        right: -1rem;
        font-size: 3rem;
    }
}

/* Add Windsurf logo styles */
.windsurf-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 4.2rem;
    width: 4.2rem;
    transition: all 0.3s ease;
}

.logo-item:hover .windsurf-logo {
    transform: scale(1.1);
    filter: brightness(1.2);
}

.windsurf-logo svg {
    width: 100%;
    height: 100%;
}

@media (max-width: 768px) {
    .platform-logos-section {
        padding: 1.5rem 0;
    }

    .logos-carousel {
        gap: 1.2rem;
    }

    .logo-item img, .continue-logo, .warp-logo, .windsurf-logo {
        height: 3.5rem;
        width: 3.5rem;
    }

    .continue-logo, .warp-logo, .windsurf-logo {
        height: 3.5rem;
        width: 3.5rem;
    }
}

@media (max-width: 480px) {
    .platform-logos-section {
        padding: 1rem 0;
    }

    .logos-carousel {
        gap: 0.8rem;
    }

    .logo-item img, .continue-logo, .warp-logo, .windsurf-logo {
        height: 2.5rem;
        width: 2.5rem;
    }

    .continue-logo, .warp-logo, .windsurf-logo {
        height: 2.5rem;
        width: 2.5rem;
    }
}

/* User Types Cards Styling */
.user-type-container {
    display: flex;
    justify-content: center;
    gap: 2.5rem;
    max-width: 1200px;
    margin: 3rem auto 2rem;
    padding: 0 2rem;
}

.user-type-card {
    background: rgba(18, 21, 26, 0.7);
    border-radius: 24px;
    overflow: hidden;
    width: 45%;
    max-width: 480px;
    padding: 2.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.06);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;
}

.user-type-card:first-child {
    background: linear-gradient(140deg, rgba(20, 241, 149, 0.05) 0%, rgba(18, 21, 26, 0.7) 100%);
}

.user-type-card:last-child {
    background: linear-gradient(140deg, rgba(10, 132, 255, 0.05) 0%, rgba(18, 21, 26, 0.7) 100%);
}

.user-type-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25);
    border-color: rgba(255, 255, 255, 0.12);
}

.user-type-card:first-child:hover {
    box-shadow: 0 15px 35px rgba(20, 241, 149, 0.1);
}

.user-type-card:last-child:hover {
    box-shadow: 0 15px 35px rgba(10, 132, 255, 0.1);
}

.card-illustration {
    width: 120px;
    height: 120px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.8rem;
    position: relative;
    transition: all 0.3s ease;
}

.user-type-card:first-child .card-illustration {
    background: rgba(20, 241, 149, 0.08);
    border: 1px solid rgba(20, 241, 149, 0.15);
}

.user-type-card:last-child .card-illustration {
    background: rgba(10, 132, 255, 0.08);
    border: 1px solid rgba(10, 132, 255, 0.15);
}

.card-illustration svg {
    width: 64px;
    height: 64px;
    transition: all 0.3s ease;
}

.user-type-card:hover .card-illustration {
    transform: scale(1.05);
}

.user-type-card:hover .card-illustration svg {
    transform: scale(1.1);
}

.card-title {
    font-family: var(--font-display);
    font-weight: 600;
    font-size: 2rem;
    margin: 0 0 1.2rem;
    color: #ffffff;
    position: relative;
    display: inline-block;
}

.user-type-card:first-child .card-title {
    background: linear-gradient(135deg, #ffffff 30%, var(--color-primary) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.user-type-card:last-child .card-title {
    background: linear-gradient(135deg, #ffffff 30%, var(--color-secondary) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.card-description {
    font-size: 1.1rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    max-width: 90%;
}

@media (max-width: 992px) {
    .user-type-container {
        gap: 2rem;
        padding: 0 1.5rem;
    }

    .user-type-card {
        padding: 2rem;
    }

    .card-illustration {
        width: 100px;
        height: 100px;
        margin-bottom: 1.5rem;
    }

    .card-illustration svg {
        width: 56px;
        height: 56px;
    }

    .card-title {
        font-size: 1.8rem;
        margin-bottom: 1rem;
    }

    .card-description {
        font-size: 1rem;
    }
}

@media (max-width: 768px) {
    .user-type-container {
        flex-direction: column;
        align-items: center;
        gap: 2rem;
    }

    .user-type-card {
        width: 100%;
        max-width: 480px;
    }
}

@media (max-width: 480px) {
    .user-type-card {
        padding: 1.8rem 1.5rem;
    }

    .card-illustration {
        width: 90px;
        height: 90px;
        margin-bottom: 1.2rem;
    }

    .card-illustration svg {
        width: 48px;
        height: 48px;
    }

    .card-title {
        font-size: 1.6rem;
    }

    .card-description {
        font-size: 0.95rem;
        max-width: 100%;
    }
}

/* FAQ Section Styles */
.faq-section {
    position: relative;
    background: linear-gradient(135deg, #0a0c10 0%, #0f1219 100%);
    padding: 6rem 2rem;
    overflow: hidden;
}

.faq-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: -10%;
    width: 120%;
    height: 100%;
    background: radial-gradient(circle at 70% 30%, rgba(20, 241, 149, 0.07) 0%, transparent 50%),
                radial-gradient(circle at 30% 70%, rgba(10, 132, 255, 0.07) 0%, transparent 50%);
    z-index: 0;
}

.faq-container {
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.faq-title {
    text-align: center;
    font-family: var(--font-display);
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 3.5rem;
    color: white;
    letter-spacing: -0.03em;
}

.faq-title .gradient-text {
    background: linear-gradient(135deg, var(--color-primary) 0%, #9ef4ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    margin-bottom: 4rem;
}

.faq-item {
    background: rgba(18, 21, 26, 0.7);
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.06);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.faq-item:hover {
    transform: translateY(-3px);
    border-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.faq-question {
    padding: 1.5rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: white;
    font-family: var(--font-space);
    font-weight: 500;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.faq-question:hover {
    background: rgba(255, 255, 255, 0.03);
}

.question-text {
    flex: 1;
    padding-right: 1rem;
}

.question-icon {
    width: 24px;
    height: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    color: var(--color-primary);
    transition: all 0.3s ease;
}

.question-icon svg {
    width: 18px;
    height: 18px;
    transition: all 0.3s ease;
}

.faq-item.active .question-icon {
    transform: rotate(45deg);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s ease, padding 0.3s ease;
    background: rgba(15, 18, 25, 0.7);
}

.faq-item.active .faq-answer {
    max-height: 500px;
    padding: 0 1.5rem 1.5rem;
}

.faq-answer p {
    margin-top: 1rem;
    line-height: 1.7;
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
    font-family: var(--font-sans);
}

.hype-text {
    background: linear-gradient(90deg, #14F195, #0A84FF);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
}

.hype-text-green {
    color: #14F195;
}

.faq-cta {
    text-align: center;
    margin-top: 3rem;
}

.faq-cta p {
    font-size: 1.1rem;
    margin-bottom: 1.2rem;
    color: rgba(255, 255, 255, 0.8);
}

.faq-cta-button {
    background: linear-gradient(135deg, var(--color-primary) 0%, #2dffb8 100%);
    color: #000000;
    font-family: var(--font-space);
    font-weight: 800;
    font-size: 1rem;
    padding: 0.8rem 2rem;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(20, 241, 149, 0.2);
}

.faq-cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(20, 241, 149, 0.3);
}

@media (max-width: 992px) {
    .faq-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .faq-title {
        font-size: 2.8rem;
        margin-bottom: 2.5rem;
    }
}

@media (max-width: 768px) {
    .faq-section {
        padding: 4rem 1.5rem;
    }

    .faq-title {
        font-size: 2.5rem;
        margin-bottom: 2rem;
    }

    .faq-question {
        font-size: 1rem;
        padding: 1.2rem;
    }

    .faq-item.active .faq-answer {
        padding: 0 1.2rem 1.2rem;
    }
}

@media (max-width: 480px) {
    .faq-title {
        font-size: 2.2rem;
    }

    .faq-question {
        font-size: 0.95rem;
        padding: 1rem;
    }

    .question-icon {
        width: 20px;
        height: 20px;
    }

    .question-icon svg {
        width: 16px;
        height: 16px;
    }

    .faq-item.active .faq-answer {
        padding: 0 1rem 1rem;
    }

    .faq-answer p {
        font-size: 0.9rem;
    }

    .faq-cta p {
        font-size: 1rem;
    }

    .faq-cta-button {
        font-size: 0.9rem;
        padding: 0.7rem 1.8rem;
    }
}

/* Blog Section Styles */
.blog-section {
    position: relative;
    background: linear-gradient(135deg, #0d1117 0%, #171f2c 100%);
    padding: 6rem 2rem;
    overflow: hidden;
}

.blog-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 30% 20%, rgba(20, 241, 149, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(10, 132, 255, 0.05) 0%, transparent 50%);
    z-index: 0;
}

.blog-container {
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.blog-header {
    text-align: center;
    margin-bottom: 3.5rem;
}

.blog-title {
    font-family: var(--font-display);
    font-size: 3.5rem;
    font-weight: 700;
    color: white;
    margin: 0;
    position: relative;
    display: inline-block;
}

.blog-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--color-primary), transparent);
}

.featured-article {
    display: grid;
    grid-template-columns: 55% 45%;
    gap: 2rem;
    background: rgba(18, 21, 26, 0.6);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    transition: all 0.4s ease;
    margin-bottom: 4rem;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.featured-article:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border-color: rgba(20, 241, 149, 0.15);
}

.featured-article-image {
    position: relative;
    overflow: hidden;
    height: 100%;
    min-height: 400px;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, rgba(0, 0, 0, 0.7), transparent);
    z-index: 1;
}

.featured-article-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.7s ease;
}

.featured-article:hover .featured-article-image img {
    transform: scale(1.05);
}

.article-badge {
    position: absolute;
    top: 20px;
    left: 20px;
    background: var(--color-primary);
    color: #0a0c10;
    font-size: 0.8rem;
    font-weight: 600;
    padding: 0.4rem 0.8rem;
    border-radius: 4px;
    z-index: 2;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.featured-article-content {
    padding: 2.5rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.article-meta {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 0.85rem;
}

.article-category {
    color: var(--color-primary);
    font-weight: 600;
    margin-right: 1rem;
    padding-right: 1rem;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.article-date {
    color: rgba(255, 255, 255, 0.6);
}

.article-title {
    font-family: var(--font-display);
    font-size: 1.8rem;
    font-weight: 600;
    margin: 0 0 1.2rem;
    color: white;
    line-height: 1.3;
    transition: color 0.3s ease;
}

.featured-article:hover .article-title {
    background: linear-gradient(90deg, #fff, var(--color-primary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.article-excerpt {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: 1.5rem;
    font-size: 1rem;
}

.article-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
}

.read-more-link {
    color: var(--color-primary);
    font-weight: 600;
    text-decoration: none;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.read-more-link:hover {
    color: white;
}

.arrow-icon {
    margin-left: 6px;
    transition: transform 0.3s ease;
}

.read-more-link:hover .arrow-icon {
    transform: translateX(5px);
}

.article-stats {
    display: flex;
    align-items: center;
}

.stat {
    display: flex;
    align-items: center;
    margin-left: 1rem;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.85rem;
}

.stat svg {
    width: 16px;
    height: 16px;
    margin-right: 5px;
    stroke: var(--color-primary);
}

.blog-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    margin-bottom: 4rem;
}

.blog-card {
    background: rgba(18, 21, 26, 0.6);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    transition: all 0.4s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.blog-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25);
    border-color: rgba(20, 241, 149, 0.15);
}

.card-image {
    position: relative;
    overflow: hidden;
    height: 200px;
}

.card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.7s ease;
}

.blog-card:hover .card-image img {
    transform: scale(1.1);
}

.card-content {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    flex: 1;
}

.blog-card .article-title {
    font-size: 1.35rem;
    margin-bottom: 0.8rem;
}

.blog-card .article-excerpt {
    font-size: 0.95rem;
    margin-bottom: 1.2rem;
}

.blog-card .read-more-link {
    margin-top: auto;
}

.blog-newsletter {
    background: linear-gradient(135deg, rgba(18, 21, 26, 0.9) 0%, rgba(13, 17, 23, 0.9) 100%);
    border-radius: 16px;
    padding: 3rem;
    position: relative;
    overflow: hidden;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.newsletter-content {
    max-width: 60%;
}

.newsletter-content h3 {
    font-family: var(--font-display);
    font-size: 1.8rem;
    margin: 0 0 1rem;
    color: white;
}

.newsletter-content p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.newsletter-form {
    display: flex;
    max-width: 500px;
}

.newsletter-form input {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 0.8rem 1rem;
    border-radius: 8px 0 0 8px;
    color: white;
    font-size: 1rem;
    outline: none;
    transition: all 0.3s ease;
}

.newsletter-form input:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--color-primary);
}

.newsletter-form input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.subscribe-button {
    background: var(--color-primary);
    color: #0a0c10;
    font-weight: 600;
    padding: 0 1.5rem;
    border: none;
    border-radius: 0 8px 8px 0;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.subscribe-button:hover {
    background: #0affa7;
    transform: translateX(2px);
}

.newsletter-decoration {
    position: absolute;
    right: -100px;
    top: -100px;
    width: 400px;
    height: 400px;
    opacity: 0.6;
    pointer-events: none;
    z-index: 0;
}

@media (max-width: 1100px) {
    .featured-article {
        grid-template-columns: 1fr;
    }

    .featured-article-image {
        min-height: 300px;
    }

    .blog-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .newsletter-content {
        max-width: 100%;
    }

    .newsletter-decoration {
        display: none;
    }
}

@media (max-width: 768px) {
    .blog-section {
        padding: 4rem 1.5rem;
    }

    .blog-title {
        font-size: 2.8rem;
    }

    .featured-article-content {
        padding: 1.8rem;
    }

    .article-title {
        font-size: 1.6rem;
    }

    .blog-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .blog-newsletter {
        padding: 2rem;
    }

    .newsletter-content h3 {
        font-size: 1.5rem;
    }

    .newsletter-form {
        flex-direction: column;
    }

    .newsletter-form input {
        border-radius: 8px;
        margin-bottom: 1rem;
    }

    .subscribe-button {
        border-radius: 8px;
        padding: 0.8rem;
    }
}

@media (max-width: 480px) {
    .blog-section {
        padding: 3rem 1rem;
    }

    .blog-title {
        font-size: 2.2rem;
    }

    .featured-article-content {
        padding: 1.5rem;
    }

    .article-title {
        font-size: 1.4rem;
    }

    .blog-card .article-title {
        font-size: 1.2rem;
    }

    .article-footer {
        flex-direction: column;
        align-items: flex-start;
    }

    .article-stats {
        margin-top: 1rem;
    }

    .stat {
        margin-left: 0;
        margin-right: 1rem;
    }

    .blog-newsletter {
        padding: 1.5rem;
    }

    .newsletter-content h3 {
        font-size: 1.3rem;
    }
}

/* Blog Animation Styles */
.blog-animate {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.blog-animate.appear {
    opacity: 1;
    transform: translateY(0);
}

.featured-article.blog-animate {
    transition-delay: 0.1s;
}

.blog-card.blog-animate {
    transition-delay: 0.2s;
}

.blog-card.blog-animate:nth-child(2) {
    transition-delay: 0.3s;
}

.blog-card.blog-animate:nth-child(3) {
    transition-delay: 0.4s;
}

.blog-newsletter.blog-animate {
    transition-delay: 0.5s;
}

.blog-card.dimmed {
    opacity: 0.6;
    transform: scale(0.98);
}

.newsletter-success {
    background: rgba(20, 241, 149, 0.1);
    color: var(--color-primary);
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
    font-weight: 600;
    margin-top: 1rem;
    border: 1px solid rgba(20, 241, 149, 0.3);
    animation: fadeInUp 0.5s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Additional hover effects for blog cards */
.blog-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(20, 241, 149, 0.1) 0%, transparent 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: 0;
    pointer-events: none;
}

.blog-card:hover::after {
    opacity: 1;
}

/* Additional hover effects for article titles */
.blog-card:hover .article-title {
    background: linear-gradient(90deg, #fff, var(--color-primary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* Additional styles for the blog section */
.blog-section {
    position: relative;
}

/* Footer Styles */
.footer-section {
    background-color: var(--color-dark);
    color: var(--color-light);
    padding-top: 5rem;
    position: relative;
    overflow: hidden;
}

.footer-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 90% 10%, rgba(20, 241, 149, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.footer-content {
    display: flex;
    /* flex-direction: column; */
    align-items: flex-start;
    /* max-width: 350px; */
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.footer-logo-text {
    font-family: 'Clash Display', sans-serif;
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--color-light);
    margin: 0;
}

.footer-logo-text .hype-text {
    background: linear-gradient(to right, #14F195, #0A84FF);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
}

.footer-tagline {
    font-size: 1rem;
    color: var(--color-gray-300);
    max-width: 280px;
    margin-bottom: 2rem;
}

.social-icons {
    display: flex;
    gap: 1.2rem;
    margin-bottom: 2rem;
}

.social-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--color-light);
    transition: all 0.3s ease;
}

.social-icon:hover {
    background-color: var(--color-primary);
    color: var(--color-dark);
    transform: translateY(-3px);
}

.social-icon svg {
    width: 18px;
    height: 18px;
    stroke-width: 2px;
}

.footer-links {
    display: flex;
    gap: 4rem;
    margin-left: auto;
}

.footer-links-column h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--color-light);
}

.footer-links-column ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links-column li {
    margin-bottom: 0.8rem;
}

.footer-links-column a {
    color: var(--color-gray-300);
    text-decoration: none;
    transition: color 0.3s ease;
    font-size: 0.95rem;
}

.footer-links-column a:hover {
    color: var(--color-primary);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 1rem;
    padding: 2rem 0;
}

.footer-bottom-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.copyright {
    font-size: 0.9rem;
    color: var(--color-gray-400);
}

.contact-info {
    display: flex;
    align-items: center;
}

.contact-email {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--color-gray-300);
    text-decoration: none;
    transition: color 0.3s ease;
    font-size: 0.95rem;
}

.contact-email:hover {
    color: var(--color-primary);
}

.contact-email svg {
    stroke-width: 2px;
}

@media (max-width: 992px) {
    .footer-container {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .footer-content {
        align-items: center;
        text-align: center;
    }

    .footer-tagline {
        max-width: 80%;
    }
}

@media (max-width: 768px) {
    .footer-links {
        grid-template-columns: repeat(2, 1fr);
        gap: 2.5rem;
    }

    .footer-bottom-container {
        flex-direction: column;
        gap: 1.5rem;
    }

    .footer-section {
        padding-top: 4rem;
    }
}

@media (max-width: 480px) {
    .footer-links {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-links-column h4 {
        margin-bottom: 1rem;
    }

    .social-icons {
        justify-content: center;
    }

    .footer-tagline {
        max-width: 100%;
    }
}

/* New Eating While Coding Section */
.eating-coding-section {
    background-color: var(--color-background);
    padding: 6rem 0;
    position: relative;
    overflow: hidden;
}

.eating-coding-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 70% 30%, rgba(20, 241, 149, 0.1), transparent 50%),
                radial-gradient(circle at 30% 70%, rgba(10, 132, 255, 0.1), transparent 50%);
    z-index: 0;
}

.eating-coding-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    position: relative;
    z-index: 1;
}

.eating-coding-title {
    font-family: var(--font-display);
    font-size: 3.5rem;
    font-weight: 700;
    text-align: center;
    color: var(--color-text);
}

.eating-coding-content {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
    padding: 1.5rem;
}

.eating-coding-image {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    flex: 1;
    min-height: 250px;
    max-width: 50%;
}

.food-icon-circle {
    position: absolute;
    top: 20%;
    left: 10%;
    animation: float 4s ease-in-out infinite;
}

.mic-icon-circle {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation: pulse 5s ease-in-out infinite;
}

.keyboard-icon-circle {
    position: absolute;
    bottom: 20%;
    right: 10%;
    animation: float 4s ease-in-out infinite;
    animation-delay: 2s;
}

@media (max-width: 768px) {
    .eating-coding-content {
        flex-direction: column-reverse;
    }

    .eating-coding-text {
        max-width: 100%;
        text-align: center;
    }

    .eating-coding-image {
        max-width: 100%;
    }
}

.eating-coding-text {
    flex: 1;
    max-width: 50%;
}

.eating-coding-text h3 {
    font-family: var(--font-display);
    font-size: 2rem;
    margin-bottom: 1.5rem;
    color: var(--color-text);
}

.eating-coding-text p {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.eating-benefits-list {
    list-style-type: none;
    padding: 0;
    margin: 2rem 0;
}

.eating-benefits-list li {
    padding-left: 2rem;
    margin-bottom: 1rem;
    position: relative;
    font-size: 1.1rem;
}

.eating-benefits-list li::before {
    content: '🍕';
    position: absolute;
    left: 0;
    font-size: 1.2rem;
}

.eating-benefits-list li:nth-child(2)::before {
    content: '🍔';
}

.eating-benefits-list li:nth-child(3)::before {
    content: '🥪';
}

.eating-benefits-list li:nth-child(4)::before {
    content: '🍟';
}

.eating-coding-cta {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--color-primary);
    text-align: center;
    margin-top: 2rem;
}

@media (max-width: 992px) {
    .eating-coding-content {
        flex-direction: column;
        gap: 2rem;
    }

    .eating-coding-image {
        height: 250px;
        width: 100%;
    }

    .eating-coding-text {
        text-align: center;
    }

    .eating-benefits-list li {
        text-align: left;
    }
}

@media (max-width: 768px) {
    .eating-coding-title {
        font-size: 2.5rem;
    }

    .eating-coding-text h3 {
        font-size: 1.8rem;
    }

    .eating-coding-content {
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .eating-coding-title {
        font-size: 2rem;
    }

    .eating-coding-text h3 {
        font-size: 1.5rem;
    }

    .food-icon, .mic-icon, .keyboard-icon {
        width: 70px;
        height: 70px;
    }

    .mic-icon {
        width: 90px;
        height: 90px;
    }

    .eating-coding-text p,
    .eating-benefits-list li {
        font-size: 1rem;
    }

    .eating-coding-cta {
        font-size: 1.1rem;
    }
}

.lang-indicator-text {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--color-primary);
    margin-left: 10px;
    display: inline-block;
    vertical-align: middle;
}

.food-icon-circle svg,
.mic-icon-circle svg,
.keyboard-icon-circle svg {
    width: 64px;
    height: 64px;
    filter: drop-shadow(0 0 10px rgba(20, 241, 149, 0.3));
    transition: all 0.5s ease;
}

.food-icon-circle svg {
    fill: #ff7e5f;
}

.mic-icon-circle svg {
    fill: var(--color-primary);
    width: 72px;
    height: 72px;
}

.keyboard-icon-circle svg {
    fill: #3498db;
}

@keyframes float {
    0% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-15px);
    }
    100% {
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1);
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
    }
}



/* YouTube Video Positioning */
.video-container {
    position: relative;
    width: 100%;
    margin: 2rem 0;
}

.video-container iframe {
    width: 100%;
    height: 315px;
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Desktop Layout */
@media (min-width: 768px) {
    .video-container {
        float: right;
        width: 50%;
        margin: 0 0 2rem 2rem;
    }
}

/* Mobile Layout */
@media (max-width: 767px) {
    .video-container {
        margin: 2rem 0;
    }
}

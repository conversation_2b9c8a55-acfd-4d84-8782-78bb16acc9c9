document.addEventListener('DOMContentLoaded', () => {
    // Mobile menu toggle
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const navLinks = document.querySelector('.nav-links');
    
    if (mobileMenuToggle && navLinks) {
        mobileMenuToggle.addEventListener('click', () => {
            mobileMenuToggle.classList.toggle('active');
            navLinks.classList.toggle('active');
        });
        
        // Close mobile menu when a link is clicked
        navLinks.querySelectorAll('a').forEach(link => {
            link.addEventListener('click', () => {
                mobileMenuToggle.classList.remove('active');
                navLinks.classList.remove('active');
            });
        });
    }

    // Initialize Three.js background
    initThreeBackground();

    // Initialize hero voice wave animation
    initVoiceWaveAnimation();

    // Initialize transformation animation for new section
    initTransformAnimation();

    // Initialize example buttons
    initExampleButtons();

    // Initialize cursor interface demo
    initCursorInterfaceDemo();

    // Initialize platform logos carousel
    initPlatformLogosCarousel();
    
    // Initialize FAQ functionality
    initFAQ();
    
    // Initialize Blog functionality
    initBlog();
    
    // Support FAQ link navigation
    const faqLink = document.querySelector('a[href="#faq"]');
    if (faqLink) {
        faqLink.addEventListener('click', function(e) {
            e.preventDefault();
            const faqSection = document.getElementById('faq');
            if (faqSection) {
                const navHeight = document.querySelector('.navbar').offsetHeight;
                const targetPosition = faqSection.getBoundingClientRect().top + window.pageYOffset;
                window.scrollTo({
                    top: targetPosition - navHeight,
                    behavior: 'smooth'
                });
            }
        });
    }
    
    // Add smooth scrolling for all section links
    const allSectionLinks = document.querySelectorAll('a[href^="#"]');
    allSectionLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            if (targetId === '#') return;
            
            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                const navHeight = document.querySelector('.navbar').offsetHeight;
                const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset;
                window.scrollTo({
                    top: targetPosition - navHeight,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Navbar scroll effect
    const navbar = document.querySelector('.navbar');
    
    window.addEventListener('scroll', () => {
        if (window.scrollY > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            
            if (targetId === '#') return;
            
            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                const navHeight = document.querySelector('.navbar').offsetHeight;
                const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset;
                
                window.scrollTo({
                    top: targetPosition - navHeight,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Add active class to nav links based on scroll position
    const sections = document.querySelectorAll('section[id], div[id]:not(.nav-container)');
    
    function updateActiveNavLink() {
        const scrollPosition = window.scrollY + 100;
        
        sections.forEach(section => {
            const sectionId = section.getAttribute('id');
            const sectionTop = section.offsetTop;
            const sectionHeight = section.offsetHeight;
            const navLink = document.querySelector(`.nav-link[href="#${sectionId}"]`);
            
            if (navLink && scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
                document.querySelectorAll('.nav-link').forEach(link => {
                    link.classList.remove('active');
                });
                navLink.classList.add('active');
            }
        });
    }
    
    window.addEventListener('scroll', updateActiveNavLink);
    updateActiveNavLink(); // Call on load

    // Initialize sound wave animation if canvas exists
    if (document.getElementById('waveCanvas')) {
        initSoundWaveAnimation();
    }

    // Add interaction to microphone icon in the hero section
    const micButton = document.querySelector('.mic-button');
    if (micButton) {
        micButton.addEventListener('click', resetVoiceAnimation);
    }

    // Initialize audio typing and wave animations
    initCodeCanvas();
    
    // Initialize audio wave animation
    if (document.getElementById('wave-canvas')) {
        initAudioWaveAnimation();
    }
    
    // Initialize typing animation for voice command
    const inputText = document.querySelector('.input-text');
    if (inputText && inputText.classList.contains('typing-animation')) {
        // Force the animation to restart
        inputText.style.animation = 'none';
        void inputText.offsetWidth; // Force reflow
        inputText.style.animation = 'typeText 3s steps(50, end) forwards, blinkCursor 0.7s step-end infinite';
        
        // Show optimized code with delay
        const optimizedCode = document.querySelector('.optimized-code code');
        if (optimizedCode) {
            optimizedCode.style.opacity = '0';
            setTimeout(() => {
                optimizedCode.style.opacity = '1';
            }, 3000);
        }
        
        // Auto restart animation every 12 seconds
        setInterval(() => {
            // Reset typing animation
            inputText.style.animation = 'none';
            void inputText.offsetWidth; // Force reflow
            inputText.style.animation = 'typeText 3s steps(50, end) forwards, blinkCursor 0.7s step-end infinite';
            
            // Reset optimized code
            if (optimizedCode) {
                optimizedCode.style.opacity = '0';
                setTimeout(() => {
                    optimizedCode.style.opacity = '1';
                }, 3000);
            }
        }, 12000);
    }

    // Initialize all animations and interactions
    initMobileMenu();
    initThreeBackground();
    initFloatingElements();
    initTransformAnimation();
    initExampleButtons();
    initPromptBubbleVoiceEffects();

    // Initialize voice-to-prompt animation
    initVoiceToPromptAnimation();

    // Initialize audio typing and wave animations for the voice command showcase
    initAudioTypingWave();
    
    // Restart the typing animation for the voice command
    restartTypingAnimation();

    // Initialize new use case animations
    initUseCaseAnimations();

    // Initialize smooth scroll animations
    initSmoothScrollAnimations();
    
    // Add appear-on-scroll effect
    initAppearOnScroll();
    
    // Add smooth scrolling for anchor links
    initSmoothAnchorLinks();
    
    // Initialize scroll progress indicator
    initScrollProgress();
    
    // Initialize scroll to top button
    initScrollToTop();
    
    // Add section transitions with parallax
    initSectionTransitions();
    
    // Add background transitions
    initBackgroundTransitions();
    
    // Enhance navigation highlighting
    enhanceNavigation();

    // These functions will be called in the main DOMContentLoaded handler
    // Remove duplicate event listener at bottom of file
});

// Initialize the cursor interface demo with example functionality
document.addEventListener('DOMContentLoaded', () => {
    // Mobile menu toggle
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const navLinks = document.querySelector('.nav-links');
    
    if (mobileMenuToggle && navLinks) {
        mobileMenuToggle.addEventListener('click', () => {
            mobileMenuToggle.classList.toggle('active');
            navLinks.classList.toggle('active');
        });
        
        // Close mobile menu when a link is clicked
        navLinks.querySelectorAll('a').forEach(link => {
            link.addEventListener('click', () => {
                mobileMenuToggle.classList.remove('active');
                navLinks.classList.remove('active');
            });
        });
    }

    // Initialize Three.js background
    initThreeBackground();

    // Initialize hero voice wave animation
    initVoiceWaveAnimation();

    // Initialize transformation animation for new section
    initTransformAnimation();

    // Initialize example buttons
    initExampleButtons();

    // Initialize cursor interface demo
    initCursorInterfaceDemo();

    // Initialize platform logos carousel
    initPlatformLogosCarousel();
    
    // Initialize FAQ functionality
    initFAQ();
    
    // Initialize Blog functionality
    initBlog();

    // Navbar scroll effect
    const navbar = document.querySelector('.navbar');
    
    window.addEventListener('scroll', () => {
        if (window.scrollY > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            
            if (targetId === '#') return;
            
            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                const navHeight = document.querySelector('.navbar').offsetHeight;
                const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset;
                
                window.scrollTo({
                    top: targetPosition - navHeight,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Add active class to nav links based on scroll position
    const sections = document.querySelectorAll('section[id], div[id]:not(.nav-container)');
    
    function updateActiveNavLink() {
        const scrollPosition = window.scrollY + 100;
        
        sections.forEach(section => {
            const sectionId = section.getAttribute('id');
            const sectionTop = section.offsetTop;
            const sectionHeight = section.offsetHeight;
            const navLink = document.querySelector(`.nav-link[href="#${sectionId}"]`);
            
            if (navLink && scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
                document.querySelectorAll('.nav-link').forEach(link => {
                    link.classList.remove('active');
                });
                navLink.classList.add('active');
            }
        });
    }
    
    window.addEventListener('scroll', updateActiveNavLink);
    updateActiveNavLink(); // Call on load

    // Initialize sound wave animation if canvas exists
    if (document.getElementById('waveCanvas')) {
        initSoundWaveAnimation();
    }

    // Add interaction to microphone icon in the hero section
    const micButton = document.querySelector('.mic-button');
    if (micButton) {
        micButton.addEventListener('click', resetVoiceAnimation);
    }

    // Initialize audio typing and wave animations
    initCodeCanvas();
    
    // Initialize audio wave animation
    if (document.getElementById('wave-canvas')) {
        initAudioWaveAnimation();
    }
    
    // Initialize typing animation for voice command
    const inputText = document.querySelector('.input-text');
    if (inputText && inputText.classList.contains('typing-animation')) {
        // Force the animation to restart
        inputText.style.animation = 'none';
        void inputText.offsetWidth; // Force reflow
        inputText.style.animation = 'typeText 3s steps(50, end) forwards, blinkCursor 0.7s step-end infinite';
        
        // Show optimized code with delay
        const optimizedCode = document.querySelector('.optimized-code code');
        if (optimizedCode) {
            optimizedCode.style.opacity = '0';
            setTimeout(() => {
                optimizedCode.style.opacity = '1';
            }, 3000);
        }
        
        // Auto restart animation every 12 seconds
        setInterval(() => {
            // Reset typing animation
            inputText.style.animation = 'none';
            void inputText.offsetWidth; // Force reflow
            inputText.style.animation = 'typeText 3s steps(50, end) forwards, blinkCursor 0.7s step-end infinite';
            
            // Reset optimized code
            if (optimizedCode) {
                optimizedCode.style.opacity = '0';
                setTimeout(() => {
                    optimizedCode.style.opacity = '1';
                }, 3000);
            }
        }, 12000);
    }

    // Initialize all animations and interactions
    initMobileMenu();
    initThreeBackground();
    initFloatingElements();
    initTransformAnimation();
    initExampleButtons();
    initPromptBubbleVoiceEffects();

    // Initialize voice-to-prompt animation
    initVoiceToPromptAnimation();

    // Initialize audio typing and wave animations for the voice command showcase
    initAudioTypingWave();
    
    // Restart the typing animation for the voice command
    restartTypingAnimation();

    // Initialize new use case animations
    initUseCaseAnimations();

    // Initialize smooth scroll animations
    initSmoothScrollAnimations();
    
    // Add appear-on-scroll effect
    initAppearOnScroll();
    
    // Add smooth scrolling for anchor links
    initSmoothAnchorLinks();
    
    // Initialize scroll progress indicator
    initScrollProgress();
    
    // Initialize scroll to top button
    initScrollToTop();
    
    // Add section transitions with parallax
    initSectionTransitions();
    
    // Add background transitions
    initBackgroundTransitions();
    
    // Enhance navigation highlighting
    enhanceNavigation();

    // Add faq-link navigation support
    const addFaqLinkSupport = () => {
        const faqLink = document.querySelector('a[href="#faq"]');
        if (faqLink) {
            faqLink.addEventListener('click', function(e) {
                e.preventDefault();
                const faqSection = document.getElementById('faq');
                if (faqSection) {
                    faqSection.scrollIntoView({ behavior: 'smooth' });
                }
            });
        }
    };

    // Add smooth scrolling for all section links
    const addSmoothScrolling = () => {
        const allSectionLinks = document.querySelectorAll('a[href^="#"]');
        allSectionLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                const targetId = this.getAttribute('href');
                if (targetId !== '#') {
                    e.preventDefault();
                    const targetSection = document.querySelector(targetId);
                    if (targetSection) {
                        targetSection.scrollIntoView({ behavior: 'smooth' });
                    }
                }
            });
        });
    };

    // These functions will be called in the main DOMContentLoaded handler
    // Remove duplicate event listener at bottom of file
});

// Initialize the cursor interface demo with example functionality
function initCursorInterfaceDemo() {
    const micButton = document.querySelector('.cursor-ai-panel .mic-button');
    const voiceRecordingIndicator = document.querySelector('.voice-recording-indicator');
    const applyButton = document.querySelector('.apply-button');
    const cancelButton = document.querySelector('.cancel-button');
    const userPrompt = document.getElementById('userPrompt');
    const aiResponse = document.getElementById('aiResponse');
    const codeContent = document.getElementById('codeContent');
    const exampleButtons = document.querySelectorAll('.voice-example-button');
    
    if (!micButton || !userPrompt || !aiResponse || !codeContent) {
        return; // Exit if elements don't exist
    }
    
    // Define example scenarios for each button
    const examples = [
        {
            prompt: "Write a function that fetches data from an API and handles errors",
            aiResponse: `Here's an async function that fetches data from an API with proper error handling:`,
            code: `/**
 * Fetches data from the specified API endpoint with timeout and error handling
 * @param {string} url - The API endpoint to fetch data from
 * @param {Object} options - Fetch options (method, headers, etc.)
 * @param {number} timeout - Timeout in milliseconds
 * @returns {Promise<any>} - The parsed JSON data
 * @throws {Error} - Custom error with detailed message
 */
async function fetchDataFromAPI(url, options = {}, timeout = 5000) {
  // Create an abort controller for timeout handling
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);
  
  try {
    // Add the signal to the options
    const fetchOptions = {
      ...options,
      signal: controller.signal
    };
    
    // Make the API request
    const response = await fetch(url, fetchOptions);
    
    // Clear the timeout
    clearTimeout(timeoutId);
    
    // Check if the response is OK (status in the range 200-299)
    if (!response.ok) {
      throw new Error(\`API error: \${response.status} \${response.statusText}\`);
    }
    
    // Try to parse the JSON
    try {
      const data = await response.json();
      return data;
    } catch (parseError) {
      throw new Error(\`JSON parsing error: \${parseError.message}\`);
    }
  } catch (error) {
    // Clear the timeout to prevent memory leaks
    clearTimeout(timeoutId);
    
    // Handle different types of errors
    if (error.name === 'AbortError') {
      throw new Error(\`Request timeout after \${timeout}ms\`);
    } else if (error.name === 'TypeError' && error.message.includes('NetworkError')) {
      throw new Error('Network error: Check your internet connection');
        } else {
      throw error; // Re-throw any other errors
    }
  }
}`
        },
        {
            prompt: "Create a user authentication system using JWT tokens",
            aiResponse: `Here's a user authentication system with JWT handling:`,
            code: `/**
 * User Authentication System using JWT
 * Handles login, token verification, and user session management
 */

// Dependencies
const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');

// Configuration
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
const TOKEN_EXPIRY = '24h';
const REFRESH_TOKEN_EXPIRY = '7d';

// Store for refresh tokens (should use Redis in production)
const refreshTokenStore = new Map();

/**
 * Authenticates a user and generates JWT tokens
 * @param {Object} credentials - User login credentials
 * @returns {Object} - Authentication tokens and user info
 */
async function authenticateUser({ email, password }) {
  // Find user in database (pseudocode)
  const user = await findUserByEmail(email);
  
  // Check if user exists
  if (!user) {
    throw new Error('Invalid credentials');
  }
  
  // Verify password
  const passwordValid = await bcrypt.compare(password, user.passwordHash);
  if (!passwordValid) {
    throw new Error('Invalid credentials');
  }
  
  // Generate tokens
  return generateTokens(user);
}

/**
 * Generates access and refresh tokens
 * @param {Object} user - User object
 * @returns {Object} - Generated tokens and user info
 */
function generateTokens(user) {
  // Create payload for token
  const payload = {
    sub: user.id,
    email: user.email,
    role: user.role,
  };
  
  // Generate tokens
  const accessToken = jwt.sign(payload, JWT_SECRET, { expiresIn: TOKEN_EXPIRY });
  const refreshToken = uuidv4();
  
  // Store refresh token with expiry
  const refreshExpiry = new Date();
  refreshExpiry.setDate(refreshExpiry.getDate() + 7); // 7 days
  
  refreshTokenStore.set(refreshToken, {
    userId: user.id,
    expires: refreshExpiry
  });
  
  return {
    accessToken,
    refreshToken,
    expiresIn: TOKEN_EXPIRY,
    user: {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role
    }
  };
}`
        },
        {
            prompt: "Build a React component for a dynamic form with validation",
            aiResponse: `Here's a React component for a dynamic form with validation:`,
            code: `import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import './DynamicForm.css';

/**
 * DynamicForm - A flexible form component with built-in validation
 * @param {Object} props - Component props
 * @returns {JSX.Element} - Rendered component
 */
const DynamicForm = ({ 
  fields, 
  onSubmit, 
  submitButtonText = 'Submit', 
  initialValues = {}, 
  validationRules = {} 
}) => {
  // State for form values and errors
  const [formValues, setFormValues] = useState(initialValues);
  const [formErrors, setFormErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isFormValid, setIsFormValid] = useState(false);
  
  // Handle input changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    const inputValue = type === 'checkbox' ? checked : value;
    
    setFormValues(prev => ({
      ...prev,
      [name]: inputValue
    }));
  };
  
  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    if (isFormValid) {
      onSubmit(formValues);
    }
    
    setIsSubmitting(false);
  };
  
  return (
    <form className="dynamic-form" onSubmit={handleSubmit}>
      {/* Form fields would be rendered here */}
      <div className="form-actions">
        <button 
          type="submit" 
          className="submit-button"
          disabled={isSubmitting || !isFormValid}
        >
          {isSubmitting ? 'Processing...' : submitButtonText}
        </button>
      </div>
    </form>
  );
};

export default DynamicForm;`
        }
    ];
    
    let currentExample = 0;
    let isRecording = false;
    let isResponding = false;
    let isApplying = false;
    
    // Function to set up the example scenario
    function setupExample(index) {
        // Update button states
        exampleButtons.forEach((btn, i) => {
            btn.classList.toggle('active', i === index);
        });
        
        // Update the user prompt
        if (userPrompt) {
            userPrompt.textContent = examples[index].prompt;
            userPrompt.classList.remove('typing-animation');
            void userPrompt.offsetWidth; // Force reflow
        }
        
        // Reset AI response to typing dots
        if (aiResponse) {
            aiResponse.innerHTML = `<span class="dot"></span><span class="dot"></span><span class="dot"></span>`;
            aiResponse.classList.add('ai-typing');
        }
        
        // Reset code content
        if (codeContent) {
            codeContent.textContent = `// Here's a simple example to start
function fetchData() {
  // TODO: Implement API fetching logic
}

// More code will appear when you use voice commands`;
        }
        
        currentExample = index;
    }
    
    // Function to simulate voice recording
    function startRecording() {
        if (isRecording || isResponding) return;
        
        isRecording = true;
        
        // Show the recording indicator
        if (voiceRecordingIndicator) {
            voiceRecordingIndicator.classList.add('active');
        }
        
        // Add active class to mic button
        if (micButton) {
            micButton.classList.add('active');
        }
        
        // Reset user prompt to make it start typing
        if (userPrompt) {
            userPrompt.textContent = '';
            userPrompt.classList.add('typing-animation');
            
            // Type out the prompt character by character
            const prompt = examples[currentExample].prompt;
            let charIndex = 0;
            
            const typingInterval = setInterval(() => {
                if (charIndex < prompt.length) {
                    userPrompt.textContent += prompt.charAt(charIndex);
                    charIndex++;
            } else {
                    clearInterval(typingInterval);
                    
                    // After typing completes, stop recording and start AI response
                    setTimeout(() => {
                        stopRecording();
                    }, 500);
                }
            }, 50);
        }
    }
    
    // Function to stop recording and start AI response
    function stopRecording() {
        isRecording = false;
        isResponding = true;
        
        // Hide recording indicator
        if (voiceRecordingIndicator) {
            voiceRecordingIndicator.classList.remove('active');
        }
        
        // Remove active class from mic button
        if (micButton) {
            micButton.classList.remove('active');
        }
        
        // Start AI response with typing animation
        setTimeout(() => {
            if (aiResponse) {
                aiResponse.innerHTML = examples[currentExample].aiResponse;
                aiResponse.classList.remove('ai-typing');
                
                // Show apply button as active after response
                if (applyButton) {
                    applyButton.classList.add('active');
                }
                
                // Enable apply functionality
                isResponding = false;
            }
        }, 2000);
    }
    
    // Function to apply the code changes
    function applyChanges() {
        if (isRecording || isResponding || isApplying) return;
        
        isApplying = true;
        
        // Show applying animation on the code
        if (codeContent) {
            codeContent.textContent = examples[currentExample].code;
            
            // After a brief delay, show completion
            setTimeout(() => {
                isApplying = false;
            }, 1000);
        }
    }
    
    // Function to cancel the interaction
    function cancelInteraction() {
        isRecording = false;
        isResponding = false;
        isApplying = false;
        
        // Reset UI elements
        if (voiceRecordingIndicator) {
            voiceRecordingIndicator.classList.remove('active');
        }
        
        if (micButton) {
            micButton.classList.remove('active');
        }
        
        // Reset the example
        setupExample(currentExample);
    }
    
    // Add event listeners
    if (micButton) {
        micButton.addEventListener('click', startRecording);
    }
    
    if (applyButton) {
        applyButton.addEventListener('click', applyChanges);
    }
    
    if (cancelButton) {
        cancelButton.addEventListener('click', cancelInteraction);
    }
    
    // Add event listeners to example buttons
    exampleButtons.forEach((button, index) => {
        button.addEventListener('click', () => {
            if (index !== currentExample) {
                setupExample(index);
            }
        });
    });

    // Initialize with the first example
    setupExample(0);
    
    // Add auto-play demonstration on load
    setTimeout(() => {
        startRecording();
    }, 2000);
}

// Initialize the transformation animation with example buttons
function initExampleButtons() {
    const exampleButtons = document.querySelectorAll('.example-button');
    const examples = [
        {
            human: "Can you write a function that sorts data?",
            optimized: `/**
 * Implement an efficient sorting function that:
 * 1. Works with arrays of any data type
 * 2. Handles edge cases (empty arrays, already sorted)
 * 3. Has configurable sort order (asc/desc)
 * 4. Uses an optimal algorithm for the dataset size
 * 5. Returns a new array without modifying original
 * 
 * Compare performance against native Array.sort()
 */`
        },
        {
            human: "Make me a login page with React",
            optimized: `/**
 * Create a React login component that:
 * 1. Implements form validation for email and password
 * 2. Shows appropriate error messages for invalid inputs
 * 3. Handles form submission with loading states
 * 4. Supports both email/password and social login options
 * 5. Uses React hooks for state management
 * 6. Includes responsive styling with CSS-in-JS or styled-components
 * 7. Implements proper accessibility attributes
 *
 * Bonus: Add remember me functionality and password recovery
 */`
        },
        {
            human: "How do I connect to a database in Node?",
            optimized: `/**
 * Implement a Node.js database connection module that:
 * 1. Supports connection pooling for efficient resource usage
 * 2. Handles automatic reconnection if the connection is lost
 * 3. Implements proper error handling and logging
 * 4. Uses environment variables for configuration
 * 5. Exports functions for common CRUD operations
 * 6. Includes transaction support
 * 7. Provides both promise-based and async/await interfaces
 *
 * Examples should include initialization, query execution, and proper cleanup
 */`
        },
        {
            human: "Show me how to create a REST API",
            optimized: `/**
 * Design a RESTful API with Express.js that:
 * 1. Follows REST principles for resource naming and HTTP methods
 * 2. Implements proper status codes and error responses
 * 3. Includes middleware for authentication and request validation
 * 4. Uses route versioning for API evolution
 * 5. Implements rate limiting and security headers
 * 6. Documents endpoints with OpenAPI/Swagger
 * 7. Includes examples for CRUD operations on a resource
 *
 * Include best practices for performance, security, and scalability
 */`
        }
    ];

    const humanRequestEl = document.getElementById('humanRequest');
    const optimizedPromptEl = document.getElementById('optimizedPrompt');
    let currentExample = 0;
    let animationInterval;

    // Function to update the example content and animations
    function updateExample(index) {
        // Clear any existing animation interval
        if (animationInterval) {
            clearInterval(animationInterval);
        }

        // Update active button
        exampleButtons.forEach((btn, i) => {
            if (i === index) {
                btn.classList.add('active');
            } else {
                btn.classList.remove('active');
            }
        });

        // Update content
        const example = examples[index];
        
        // Reset animations
        humanRequestEl.style.opacity = '0';
        optimizedPromptEl.style.opacity = '0';
        
        // Force reflow
        void humanRequestEl.offsetWidth;
        void optimizedPromptEl.offsetWidth;
        
        // Update text content
        humanRequestEl.textContent = example.human;
        optimizedPromptEl.textContent = example.optimized;
        
        // Start animation sequence
        setTimeout(() => {
            // Show human request with fade in
            humanRequestEl.style.opacity = '1';
            
            // Start transform animation
            restartTransformAnimation();
            
            // Show optimized prompt after delay
            setTimeout(() => {
                optimizedPromptEl.style.opacity = '1';
            }, 3000);
        }, 200);
        
        currentExample = index;
    }

    // Add click event listeners to buttons
    exampleButtons.forEach((button, index) => {
        button.addEventListener('click', () => {
            updateExample(index);
        });
    });

    // Start with the first example
    updateExample(0);

    // Set up automatic rotation between examples every 15 seconds
    animationInterval = setInterval(() => {
        updateExample((currentExample + 1) % examples.length);
    }, 15000);
}

// Animation for the transformation section
function initTransformAnimation() {
    const canvas = document.getElementById('transformCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    let animationId;
    let particles = [];
    
    // Resize canvas to fill container
    function resizeCanvas() {
        canvas.width = canvas.parentElement.offsetWidth;
        canvas.height = canvas.parentElement.offsetHeight;
        
        // Recreate particles after resize
        createParticles();
    }
    
    // Create wave particles
    function createParticles() {
        particles = [];
        const particleCount = 100;
        
        for (let i = 0; i < particleCount; i++) {
            const x = Math.random() * canvas.width;
            const y = canvas.height / 2 + (Math.random() * 40 - 20);
            const radius = Math.random() * 3 + 1;
            const color = getParticleColor();
            const vx = (Math.random() - 0.5) * 2;
            const vy = (Math.random() - 0.5) * 1;
            
            particles.push({ x, y, radius, color, vx, vy, origY: y });
        }
    }
    
    // Get random color for particle
    function getParticleColor() {
        const colors = [
            'rgba(0, 230, 118, 0.7)',   // Green
            'rgba(155, 53, 255, 0.7)',  // Purple
            'rgba(0, 142, 255, 0.7)'    // Blue
        ];
        return colors[Math.floor(Math.random() * colors.length)];
    }
    
    // Animate the transformation
    function animate() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Draw connecting wave line
        ctx.beginPath();
        ctx.moveTo(0, canvas.height / 2);
        
        // Create wave path through points
        const controlPoints = [];
        const pointCount = 10;
        const time = Date.now() * 0.001;
        
        for (let i = 0; i <= pointCount; i++) {
            const x = (canvas.width / pointCount) * i;
            const y = canvas.height / 2 + Math.sin(time + i * 0.5) * 15;
            controlPoints.push({ x, y });
        }
        
        // Draw smooth curve through points
        for (let i = 0; i < controlPoints.length - 1; i++) {
            const xc = (controlPoints[i].x + controlPoints[i + 1].x) / 2;
            const yc = (controlPoints[i].y + controlPoints[i + 1].y) / 2;
            ctx.quadraticCurveTo(controlPoints[i].x, controlPoints[i].y, xc, yc);
        }
        
        // Complete path to right edge
        ctx.lineTo(canvas.width, canvas.height / 2);
        
        // Create gradient for line
        const gradient = ctx.createLinearGradient(0, 0, canvas.width, 0);
        gradient.addColorStop(0, 'rgba(0, 184, 230, 0.6)');    // Blue
        gradient.addColorStop(0.5, 'rgba(155, 53, 255, 0.6)'); // Purple
        gradient.addColorStop(1, 'rgba(0, 230, 118, 0.6)');    // Green
        
        ctx.strokeStyle = gradient;
        ctx.lineWidth = 2;
        ctx.stroke();
        
        // Update and draw particles
        for (let i = 0; i < particles.length; i++) {
            const p = particles[i];
            
            // Update position with slight movement
            p.x += p.vx;
            p.y = p.origY + Math.sin(time * 2 + i * 0.1) * 10;
            
            // Wraparound for x position
            if (p.x < 0) p.x = canvas.width;
            if (p.x > canvas.width) p.x = 0;
            
            // Draw particle
            ctx.beginPath();
            ctx.arc(p.x, p.y, p.radius, 0, Math.PI * 2);
            ctx.fillStyle = p.color;
            ctx.fill();
        }
        
        // Continue animation
        animationId = requestAnimationFrame(animate);
    }
    
    // Public function to restart the animation
    window.restartTransformAnimation = function() {
        // Cancel any existing animation
        if (animationId) {
            cancelAnimationFrame(animationId);
        }
        
        // Create new particles
        createParticles();
        
        // Start animation
        animate();
    };
    
    // Initialize animation
    resizeCanvas();
    animate();
    
    // Handle window resize
    window.addEventListener('resize', resizeCanvas);
}

// Reset and restart the voice command animation
function resetVoiceAnimation() {
    // Make recording indicator blink
    const recordingDot = document.querySelector('.recording-dot');
    if (recordingDot) {
        recordingDot.style.animation = 'none';
        void recordingDot.offsetWidth;
        recordingDot.style.animation = 'pulse-recording 1.5s infinite';
    }
    
    // Reset the typing animation
    const typingText = document.querySelector('.typing-text');
    if (typingText) {
        typingText.style.animation = 'none';
        void typingText.offsetWidth; // Trigger reflow
        typingText.style.animation = 'typeText 3s steps(50) forwards, blinkCursor 0.7s infinite';
    }
    
    // Hide and then fade in the optimized command
    const optimizedCommand = document.querySelector('.optimized-command');
    if (optimizedCommand) {
        optimizedCommand.style.opacity = '0';
        optimizedCommand.style.animation = 'none';
        void optimizedCommand.offsetWidth; // Trigger reflow
        
        // Delay the command appearance to match typing completion
        setTimeout(() => {
            optimizedCommand.style.animation = 'fadeInCommand 1s ease-out forwards';
            
            // Also reset the command code animation
            const commandCode = document.querySelector('.command-code code');
            if (commandCode) {
                commandCode.style.animation = 'none';
                void commandCode.offsetWidth; // Trigger reflow
                commandCode.style.animation = 'fadeInCommand 1s ease-out 0.5s forwards';
            }
        }, 3000);
    }
    
    // Pulse the mic button
    const micButton = document.querySelector('.mic-button');
    if (micButton) {
        micButton.classList.add('active');
        setTimeout(() => {
            micButton.classList.remove('active');
        }, 500);
    }
    
    // Add active state to voice container
    const voiceContainer = document.querySelector('.voice-command-container');
    if (voiceContainer) {
        voiceContainer.classList.add('active');
        setTimeout(() => {
            voiceContainer.classList.remove('active');
        }, 6000);
    }
    
    // Start a cycle to auto-restart animation every 10 seconds
    setTimeout(() => {
        resetVoiceAnimation();
    }, 10000);
}

// Three.js background animation for hero section
function initThreeBackground() {
    const canvas = document.getElementById('three-canvas');
    if (!canvas) return;
    
    // Setup Three.js scene
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({ canvas, alpha: true, antialias: true });
    
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setPixelRatio(window.devicePixelRatio);
    
    // Camera position
    camera.position.z = 30;
    
    // Create starfield of particles
    const particlesGeometry = new THREE.BufferGeometry();
    const particlesCount = 2500; // Increased particle count
    
    // Create arrays for particle positions and colors
    const posArray = new Float32Array(particlesCount * 3);
    const colorsArray = new Float32Array(particlesCount * 3);
    const sizesArray = new Float32Array(particlesCount);
    
    // Fill arrays with random values
    for (let i = 0; i < particlesCount * 3; i += 3) {
        // Position
        posArray[i] = (Math.random() - 0.5) * 120; // x - wider spread
        posArray[i + 1] = (Math.random() - 0.5) * 120; // y - wider spread
        posArray[i + 2] = (Math.random() - 0.5) * 120; // z - wider spread
        
        // Colors - primarily greens and purples with some variation
        const colorChoice = Math.random();
        if (colorChoice < 0.5) {
            // Green variations
            colorsArray[i] = 0; // R
            colorsArray[i + 1] = 0.7 + Math.random() * 0.3; // G
            colorsArray[i + 2] = 0.3 + Math.random() * 0.2; // B
        } else if (colorChoice < 0.8) {
            // Purple variations
            colorsArray[i] = 0.4 + Math.random() * 0.2; // R
            colorsArray[i + 1] = 0.1 + Math.random() * 0.2; // G
            colorsArray[i + 2] = 0.7 + Math.random() * 0.3; // B
        } else {
            // Cyan variations for contrast
            colorsArray[i] = 0.1 + Math.random() * 0.2; // R
            colorsArray[i + 1] = 0.6 + Math.random() * 0.3; // G
            colorsArray[i + 2] = 0.8 + Math.random() * 0.2; // B
        }
        
        // Vary the sizes for depth effect
        sizesArray[i/3] = Math.random() * 0.3 + 0.1;
    }
    
    // Add attributes to geometry
    particlesGeometry.setAttribute('position', new THREE.BufferAttribute(posArray, 3));
    particlesGeometry.setAttribute('color', new THREE.BufferAttribute(colorsArray, 3));
    particlesGeometry.setAttribute('size', new THREE.BufferAttribute(sizesArray, 1));
    
    // Create shader material for better-looking particles
    const particlesMaterial = new THREE.ShaderMaterial({
        uniforms: {
            time: { value: 0 },
            pixelRatio: { value: renderer.getPixelRatio() }
        },
        vertexShader: `
            attribute float size;
            attribute vec3 color;
            varying vec3 vColor;
            uniform float time;
            void main() {
                vColor = color;
                vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);
                // Size attenuation
                gl_PointSize = size * (300.0 / -mvPosition.z);
                gl_Position = projectionMatrix * mvPosition;
            }
        `,
        fragmentShader: `
            varying vec3 vColor;
            uniform float pixelRatio;
            void main() {
                // Create circular particles
                vec2 xy = gl_PointCoord.xy - vec2(0.5);
                float radius = length(xy);
                if (radius > 0.5) discard;
                
                // Add glow effect
                float alpha = 0.8 - radius * 1.6;
                gl_FragColor = vec4(vColor, alpha);
            }
        `,
        transparent: true,
        blending: THREE.AdditiveBlending,
        depthTest: false
    });
    
    // Create the particle system
    const particlesMesh = new THREE.Points(particlesGeometry, particlesMaterial);
    scene.add(particlesMesh);
    
    // Add connecting lines between some particles for network effect
    const linesMaterial = new THREE.LineBasicMaterial({
        color: 0x00e676,
        transparent: true,
        opacity: 0.3,
        blending: THREE.AdditiveBlending
    });
    
    const linesGroup = new THREE.Group();
    scene.add(linesGroup);
    
    // Create connection lines between some particles
    function createConnections() {
        // Clear previous connections
        while(linesGroup.children.length > 0) { 
            linesGroup.remove(linesGroup.children[0]); 
        }
        
        // Choose some particles to connect
        const maxConnections = 100;
        const connectionDistance = 20;
        
        for (let i = 0; i < maxConnections; i++) {
            const indexA = Math.floor(Math.random() * particlesCount);
            
            // Get position of first particle
            const posA = new THREE.Vector3(
                posArray[indexA * 3],
                posArray[indexA * 3 + 1],
                posArray[indexA * 3 + 2]
            );
            
            // Find a nearby particle
            for (let j = 0; j < particlesCount; j++) {
                if (j === indexA) continue;
                
                const posB = new THREE.Vector3(
                    posArray[j * 3],
                    posArray[j * 3 + 1],
                    posArray[j * 3 + 2]
                );
                
                // Check if they're close enough
                if (posA.distanceTo(posB) < connectionDistance) {
                    // Create a line geometry
                    const lineGeometry = new THREE.BufferGeometry().setFromPoints([posA, posB]);
                    const line = new THREE.Line(lineGeometry, linesMaterial);
                    linesGroup.add(line);
                    break;
                }
            }
        }
    }
    
    // Initial connection creation
    createConnections();
    
    // Add larger glowing orbs
    const orbsGroup = new THREE.Group();
    scene.add(orbsGroup);
    
    function createGlowingOrbs() {
        const orbCount = 5;
        
        for (let i = 0; i < orbCount; i++) {
            // Create a sphere geometry
            const radius = Math.random() * 1.5 + 0.5;
            const geometry = new THREE.SphereGeometry(radius, 16, 16);
            
            // Create a glowing material
            const material = new THREE.ShaderMaterial({
                uniforms: {
                    time: { value: 0 },
                    color: { value: new THREE.Color(i % 2 === 0 ? 0x00e676 : 0x9b35ff) }
                },
                vertexShader: `
                    varying vec3 vNormal;
                    varying vec3 vPosition;
                    void main() {
                        vNormal = normalize(normalMatrix * normal);
                        vPosition = (modelViewMatrix * vec4(position, 1.0)).xyz;
                        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                    }
                `,
                fragmentShader: `
                    uniform vec3 color;
                    uniform float time;
                    varying vec3 vNormal;
                    varying vec3 vPosition;
                    void main() {
                        float pulse = 0.5 * sin(time * 2.0) + 0.5;
                        float intensity = 0.8 - 0.8 * pow(length(vPosition)/20.0, 2.0);
                        intensity = intensity * (0.7 + 0.3 * pulse);
                        gl_FragColor = vec4(color, intensity * 0.8);
                    }
                `,
                transparent: true,
                blending: THREE.AdditiveBlending
            });
            
            const orb = new THREE.Mesh(geometry, material);
            
            // Random position
            orb.position.set(
                (Math.random() - 0.5) * 60,
                (Math.random() - 0.5) * 60,
                (Math.random() - 0.5) * 60
            );
            
            orbsGroup.add(orb);
        }
    }
    
    createGlowingOrbs();
    
    // Mouse interaction
    const mouse = {
        x: 0,
        y: 0,
        isMoving: false,
        timeout: null
    };
    
    document.addEventListener('mousemove', (event) => {
        mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
        mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
        mouse.isMoving = true;
        
        // Reset the timeout
        clearTimeout(mouse.timeout);
        mouse.timeout = setTimeout(() => {
            mouse.isMoving = false;
        }, 500);
    });
    
    // Handle window resize
    window.addEventListener('resize', () => {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
        particlesMaterial.uniforms.pixelRatio.value = renderer.getPixelRatio();
        
        // Recreate connections to adapt to new window size
        createConnections();
    });
    
    // Animation values
    let time = 0;
    let mouseIntensity = 0;
    
    // Animation loop
    function animate() {
        requestAnimationFrame(animate);
        
        time += 0.01;
        
        // Update shader uniforms
        if (particlesMaterial.uniforms) {
            particlesMaterial.uniforms.time.value = time;
        }
        
        // Update orb uniforms
        orbsGroup.children.forEach(orb => {
            if (orb.material.uniforms) {
                orb.material.uniforms.time.value = time;
            }
            
            // Slight movement
            orb.position.x += Math.sin(time + orb.position.z * 0.1) * 0.03;
            orb.position.y += Math.cos(time + orb.position.x * 0.1) * 0.03;
        });
        
        // Calculate mouse intensity for responsive animation
        if (mouse.isMoving) {
            mouseIntensity = Math.min(mouseIntensity + 0.05, 1.0);
        } else {
            mouseIntensity = Math.max(mouseIntensity - 0.01, 0.0);
        }
        
        // Rotate the particle system
        particlesMesh.rotation.x += 0.0003;
        particlesMesh.rotation.y += 0.0005;
        
        // Rotate lines group slightly differently
        linesGroup.rotation.x += 0.0002;
        linesGroup.rotation.y += 0.0004;
        
        // Respond to mouse movement with intensity factor
        if (mouse.x && mouse.y) {
            const mouseEffect = mouseIntensity * 0.0008;
            particlesMesh.rotation.x += mouse.y * mouseEffect;
            particlesMesh.rotation.y += mouse.x * mouseEffect;
            linesGroup.rotation.x += mouse.y * mouseEffect * 0.7;
            linesGroup.rotation.y += mouse.x * mouseEffect * 0.7;
            orbsGroup.rotation.x += mouse.y * mouseEffect * 0.5;
            orbsGroup.rotation.y += mouse.x * mouseEffect * 0.5;
        }
        
        renderer.render(scene, camera);
    }
    
    animate();
}

// Sound wave animation for About section
function initSoundWaveAnimation() {
    const canvas = document.getElementById('waveCanvas');
    const ctx = canvas.getContext('2d');
    
    // Set canvas to full container size
    canvas.width = canvas.parentElement.clientWidth;
    canvas.height = canvas.parentElement.clientHeight;
    
    // Variables for animation
    let particles = [];
    let isAnimating = true;
    let isRecording = false;
    
    // Create particles
    function createParticles() {
        particles = [];
        const particleCount = Math.floor(canvas.width / 2);
        
        for (let i = 0; i < particleCount; i++) {
            const x = (canvas.width / particleCount) * i;
            const baseY = canvas.height / 2;
            const amplitude = Math.random() * 20 + 10;
            const speed = 0.01 + Math.random() * 0.01;
            const phase = Math.random() * Math.PI * 2;
            
            particles.push({ x, baseY, amplitude, speed, phase });
        }
    }
    
    // Animation function
    function drawSoundWaves() {
        if (!isAnimating) return;
        
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Draw connecting curves between particles
        ctx.beginPath();
        
        // Start from the first particle
        if (particles.length > 0) {
            const firstParticle = particles[0];
            const firstY = firstParticle.baseY + Math.sin(Date.now() * firstParticle.speed + firstParticle.phase) * firstParticle.amplitude * (isRecording ? 2 : 1);
            ctx.moveTo(firstParticle.x, firstY);
            
            // Connect to the rest of the particles
            for (let i = 1; i < particles.length; i++) {
                const particle = particles[i];
                const prevParticle = particles[i - 1];
                const particleY = particle.baseY + Math.sin(Date.now() * particle.speed + particle.phase) * particle.amplitude * (isRecording ? 2 : 1);
                const prevY = prevParticle.baseY + Math.sin(Date.now() * prevParticle.speed + prevParticle.phase) * prevParticle.amplitude * (isRecording ? 2 : 1);
                
                // Use a quadratic curve to make the line smoother
                const cpX = (prevParticle.x + particle.x) / 2;
                ctx.quadraticCurveTo(prevParticle.x, prevY, cpX, (prevY + particleY) / 2);
            }
            
            // Draw the final curve to the last particle
            const lastParticle = particles[particles.length - 1];
            const lastY = lastParticle.baseY + Math.sin(Date.now() * lastParticle.speed + lastParticle.phase) * lastParticle.amplitude * (isRecording ? 2 : 1);
            ctx.lineTo(lastParticle.x, lastY);
        }
        
        // Create a gradient for the stroke
        const gradient = ctx.createLinearGradient(0, 0, canvas.width, 0);
        gradient.addColorStop(0, 'rgba(155, 53, 255, 0.7)');
        gradient.addColorStop(0.5, 'rgba(0, 230, 118, 0.7)');
        gradient.addColorStop(1, 'rgba(155, 53, 255, 0.7)');
        
        // Style and stroke the path
        ctx.strokeStyle = isRecording ? gradient : 'rgba(0, 230, 118, 0.7)';
        ctx.lineWidth = 2;
        ctx.stroke();
        
        requestAnimationFrame(drawSoundWaves);
    }
    
    // Handle window resize
    function handleResize() {
        canvas.width = canvas.parentElement.clientWidth;
        canvas.height = canvas.parentElement.clientHeight;
        createParticles();
    }
    
    window.addEventListener('resize', handleResize);
    
    // Begin the animation
    createParticles();
    drawSoundWaves();
    
    // For demo purposes - pretend recording state
    const soundWaveContainer = document.querySelector('.sound-wave-container');
    const pulseCircle = document.querySelector('.pulse-circle');
    
    pulseCircle.addEventListener('click', () => {
        isRecording = !isRecording;
        
        if (isRecording) {
            soundWaveContainer.classList.add('recording');
            pulseCircle.classList.add('active');
        } else {
            soundWaveContainer.classList.remove('recording');
            pulseCircle.classList.remove('active');
        }
    });
}

// Voice wave animation for the hero
function initVoiceWaveAnimation() {
    const canvas = document.getElementById('voiceWaveCanvas');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    const dpr = window.devicePixelRatio || 1;
    
    // Set canvas to correct size
    canvas.width = canvas.offsetWidth * dpr;
    canvas.height = canvas.offsetHeight * dpr;
    ctx.scale(dpr, dpr);
    
    // Parameters for the wave animation
    const waves = {
        count: 3,
        colors: ['rgba(0, 230, 118, 0.7)', 'rgba(155, 53, 255, 0.5)', 'rgba(0, 184, 230, 0.3)'],
        baseHeight: canvas.offsetHeight / 2,
        segments: 100,
        amplitude: [canvas.offsetHeight * 0.3, canvas.offsetHeight * 0.2, canvas.offsetHeight * 0.15],
        frequency: [0.05, 0.03, 0.02],
        speed: [0.05, 0.03, 0.02]
    };
    
    // Generate random points for each wave
    let points = [];
    for (let w = 0; w < waves.count; w++) {
        points[w] = [];
        for (let i = 0; i < waves.segments; i++) {
            points[w].push({
                x: (canvas.offsetWidth / waves.segments) * i,
                y: waves.baseHeight,
                original: waves.baseHeight,
                speed: waves.speed[w] * (Math.random() * 0.4 + 0.8),
                offset: Math.random() * Math.PI * 2,
                amplitude: waves.amplitude[w] * (Math.random() * 0.3 + 0.7),
                frequency: waves.frequency[w] * (Math.random() * 0.4 + 0.8)
            });
        }
    }
    
    let animationActive = true;
    let time = 0;
    let isRecording = true; // Start with recording state
    
    // Handle window resize
    window.addEventListener('resize', () => {
        // Adjust canvas size on resize
        canvas.width = canvas.offsetWidth * dpr;
        canvas.height = canvas.offsetHeight * dpr;
        ctx.scale(dpr, dpr);
        
        // Recalculate base height and update points
        waves.baseHeight = canvas.offsetHeight / 2;
        for (let w = 0; w < waves.count; w++) {
            for (let i = 0; i < waves.segments; i++) {
                points[w][i].x = (canvas.offsetWidth / waves.segments) * i;
                points[w][i].original = waves.baseHeight;
                points[w][i].y = waves.baseHeight;
            }
        }
    });
    
    // Animation function with recording state
    function animateWaves() {
        if (!animationActive) return;
        
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Update and draw each wave
        for (let w = 0; w < waves.count; w++) {
            // Calculate new positions
            for (let i = 0; i < points[w].length; i++) {
                const point = points[w][i];
                // Adjust amplitude based on recording state
                const amplitudeFactor = isRecording ? 1.0 : 0.5;
                point.y = point.original + 
                    Math.sin(time * point.speed + point.offset) * point.amplitude * 
                    Math.sin(point.x * point.frequency) * amplitudeFactor;
            }
            
            // Draw wave path
            ctx.beginPath();
            ctx.moveTo(0, waves.baseHeight);
            
            // Draw lines to each point
            for (let i = 0; i < points[w].length; i++) {
                if (i === 0) {
                    ctx.lineTo(points[w][i].x, points[w][i].y);
                } else {
                    // Use quadratic curves for smoother lines
                    const xc = (points[w][i].x + points[w][i-1].x) / 2;
                    const yc = (points[w][i].y + points[w][i-1].y) / 2;
                    ctx.quadraticCurveTo(points[w][i-1].x, points[w][i-1].y, xc, yc);
                }
            }
            
            // Complete the path
            ctx.lineTo(canvas.offsetWidth, waves.baseHeight);
            ctx.lineTo(0, waves.baseHeight);
            
            // Fill with gradient
            const gradient = ctx.createLinearGradient(0, 0, canvas.offsetWidth, 0);
            // Adjust colors based on recording state
            const alpha = isRecording ? '0.7' : '0.4';
            gradient.addColorStop(0, waves.colors[w].replace(/[\d\.]+\)$/, alpha + ')'));
            gradient.addColorStop(0.5, waves.colors[(w+1) % waves.count].replace(/[\d\.]+\)$/, alpha + ')'));
            gradient.addColorStop(1, waves.colors[w].replace(/[\d\.]+\)$/, alpha + ')'));
            
            ctx.fillStyle = gradient;
            ctx.fill();
        }
        
        time += 0.05;
        requestAnimationFrame(animateWaves);
    }
    
    // Toggle recording state
    function toggleRecording() {
        isRecording = !isRecording;
    }
    
    // Start the animation
    animateWaves();
    
    // Auto-toggle recording state for visual effect
    setInterval(toggleRecording, 5000);
    
    // Reset voice animation sequentially
    resetVoiceAnimation();
    
    // Add click handler for mic button
    const micButton = document.querySelector('.mic-button');
    if (micButton) {
        micButton.addEventListener('click', () => {
            isRecording = true;
            resetVoiceAnimation();
        });
    }
}

// Code typing animation for top section
function initCodeCanvas() {
    const canvas = document.getElementById('code-canvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const codeLines = [];
    
    // Resize canvas to fill container
    function resizeCanvas() {
        canvas.width = canvas.parentElement.offsetWidth;
        canvas.height = canvas.parentElement.offsetHeight;
        
        // Redraw canvas after resize
        drawCodeLines();
    }
    
    // Generate random code-like text
    function generateCodeLine() {
        const templates = [
            'function ${name}(${args}) {',
            '  const ${varName} = ${value};',
            '  if (${condition}) {',
            '  } else {',
            '  }',
            '  return ${returnVal};',
            '}',
            'const ${varName} = (${args}) => {',
            '  ${statement}',
            '};',
            'import { ${imports} } from "${package}";',
            'class ${className} {',
            '  constructor(${params}) {',
            '    this.${property} = ${value};',
            '  }',
            '  ${methodName}() {',
            '    ${statement}',
            '  }',
            '}'
        ];
        
        let line = templates[Math.floor(Math.random() * templates.length)];
        
        // Replace placeholders with random text
        line = line.replace(/\${name}/g, getRandomName())
            .replace(/\${args}/g, getRandomArgs())
            .replace(/\${varName}/g, getRandomName())
            .replace(/\${value}/g, getRandomValue())
            .replace(/\${condition}/g, getRandomCondition())
            .replace(/\${returnVal}/g, getRandomValue())
            .replace(/\${statement}/g, getRandomStatement())
            .replace(/\${imports}/g, getRandomImports())
            .replace(/\${package}/g, getRandomPackage())
            .replace(/\${className}/g, getRandomClassName())
            .replace(/\${params}/g, getRandomArgs())
            .replace(/\${property}/g, getRandomName())
            .replace(/\${methodName}/g, getRandomName());
        
        return {
            text: line,
            x: -line.length * 8, // Start off-screen
            y: Math.random() * canvas.height,
            speed: 0.5 + Math.random() * 1.5,
            color: getRandomCodeColor(),
            opacity: 0.5 + Math.random() * 0.5
        };
    }
    
    // Helper functions for random text generation
    function getRandomName() {
        const names = ['render', 'update', 'fetch', 'transform', 'process', 'handle', 'create', 'validate', 'analyze', 'convert'];
        return names[Math.floor(Math.random() * names.length)];
    }
    
    function getRandomArgs() {
        const args = ['data', 'props', 'event', 'options', 'params', 'callback', 'config', 'input', 'value', 'state'];
        const count = Math.floor(Math.random() * 3);
        const result = [];
        for (let i = 0; i < count; i++) {
            result.push(args[Math.floor(Math.random() * args.length)]);
        }
        return result.join(', ');
    }
    
    function getRandomValue() {
        const values = ['true', 'false', '0', '1', '100', '"data"', 'null', 'undefined', '[]', '{}'];
        return values[Math.floor(Math.random() * values.length)];
    }
    
    function getRandomCondition() {
        const vars = ['data', 'value', 'result', 'isValid', 'isLoading', 'hasError'];
        const ops = [' === ', ' !== ', ' > ', ' < ', ' >= ', ' <= '];
        const vals = ['true', 'false', '0', '1', '100', '"value"', 'null', 'undefined'];
        
        return vars[Math.floor(Math.random() * vars.length)] + 
               ops[Math.floor(Math.random() * ops.length)] + 
               vals[Math.floor(Math.random() * vals.length)];
    }
    
    function getRandomStatement() {
        const statements = [
            'return result;',
            'console.log(data);',
            'setLoading(true);',
            'this.update();',
            'callback(null, data);',
            'render();',
            'return Promise.resolve();'
        ];
        return statements[Math.floor(Math.random() * statements.length)];
    }
    
    function getRandomImports() {
        const modules = ['useState', 'useEffect', 'Component', 'createContext', 'useMemo', 'render', 'createApp'];
        const count = 1 + Math.floor(Math.random() * 3);
        const result = [];
        for (let i = 0; i < count; i++) {
            result.push(modules[Math.floor(Math.random() * modules.length)]);
        }
        return result.join(', ');
    }
    
    function getRandomPackage() {
        const packages = ['react', 'vue', 'lodash', '@material-ui/core', 'axios', 'express', 'next/router'];
        return packages[Math.floor(Math.random() * packages.length)];
    }
    
    function getRandomClassName() {
        const prefixes = ['App', 'User', 'Data', 'Form', 'Button', 'Modal', 'Card', 'List', 'Input', 'Auth'];
        const suffixes = ['Component', 'Container', 'Provider', 'Context', 'Handler', 'View', 'Controller'];
        return prefixes[Math.floor(Math.random() * prefixes.length)] + 
               suffixes[Math.floor(Math.random() * suffixes.length)];
    }
    
    function getRandomCodeColor() {
        const colors = [
            'rgba(155, 53, 255, 0.8)',  // Purple
            'rgba(0, 230, 118, 0.8)',   // Green
            'rgba(0, 184, 230, 0.8)',   // Blue
            'rgba(255, 255, 255, 0.8)'  // White
        ];
        return colors[Math.floor(Math.random() * colors.length)];
    }
    
    // Add initial code lines
    function initCodeLines() {
        const lineCount = 50;
        for (let i = 0; i < lineCount; i++) {
            codeLines.push(generateCodeLine());
        }
    }
    
    // Draw code lines onto canvas
    function drawCodeLines() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        for (let i = 0; i < codeLines.length; i++) {
            const line = codeLines[i];
            
            ctx.font = '14px "JetBrains Mono", monospace';
            ctx.fillStyle = line.color;
            ctx.globalAlpha = line.opacity;
            ctx.fillText(line.text, line.x, line.y);
            
            // Move line to the right
            line.x += line.speed;
            
            // Replace line if it moves off screen
            if (line.x > canvas.width) {
                codeLines[i] = generateCodeLine();
            }
        }
        
        // Continue animation
        requestAnimationFrame(drawCodeLines);
    }
    
    // Initialize and start animation
    resizeCanvas();
    initCodeLines();
    drawCodeLines();
    
    // Handle window resize
    window.addEventListener('resize', resizeCanvas);
}

// Audio waveform animation for bottom section
function initAudioWaveAnimation() {
    const canvas = document.getElementById('wave-canvas');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    let bars = [];
    const barCount = 60;
    
    // Resize canvas to fill container
    function resizeCanvas() {
        canvas.width = canvas.parentElement.offsetWidth;
        canvas.height = canvas.parentElement.offsetHeight;
        
        // Recreate bars after resize
        createBars();
    }
    
    // Create audio visualization bars
    function createBars() {
        bars = [];
        const barWidth = Math.max(2, (canvas.width / barCount) - 2);
        
        for (let i = 0; i < barCount; i++) {
            const x = i * (barWidth + 2);
            const height = Math.random() * (canvas.height * 0.4) + 10;
            const speed = 0.1 + Math.random() * 0.1;
            
            bars.push({
                x,
                height,
                speed,
                direction: Math.random() > 0.5 ? 1 : -1,
                color: getBarColor(i)
            });
        }
    }
    
    // Get color for bar based on position
    function getBarColor(index) {
        // Every 5th bar gets primary color for accent
        if (index % 5 === 0) {
            return 'rgba(0, 230, 118, 0.8)'; // Green
        } else if (index % 7 === 0) {
            return 'rgba(155, 53, 255, 0.7)'; // Purple
                } else {
            return 'rgba(255, 255, 255, 0.4)'; // White
        }
    }
    
    // Draw audio visualization
    function animate() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Calculate center Y position
        const centerY = canvas.height / 2;
        
        // Draw bars
        for (let i = 0; i < bars.length; i++) {
            const bar = bars[i];
            
            // Update height with smooth motion
            bar.height += bar.speed * bar.direction;
            if (bar.height > canvas.height * 0.4 || bar.height < 10) {
                bar.direction *= -1;
            }
            
            // Draw the bar
            ctx.fillStyle = bar.color;
            ctx.fillRect(bar.x, centerY - bar.height / 2, (canvas.width / barCount) - 2, bar.height);
            
            // Randomly adjust heights for more dynamic effect
            if (Math.random() > 0.95) {
                bar.height = Math.random() * (canvas.height * 0.4) + 10;
            }
        }
        
        // Continue animation
        requestAnimationFrame(animate);
    }
    
    // Initialize and start animation
    resizeCanvas();
    createBars();
    animate();
    
    // Handle window resize
    window.addEventListener('resize', resizeCanvas);
}

// Initialize special effects for prompt bubbles voice recording animation
function initPromptBubbleVoiceEffects() {
    const promptBubbles = document.querySelectorAll('.prompt-bubble');
    
    promptBubbles.forEach(bubble => {
        const voiceWave = bubble.querySelector('.voice-wave');
        const content = bubble.querySelector('.bubble-content');
        const delay = parseFloat(bubble.getAttribute('data-delay')) || 0;
        
        // Set custom animation delays based on data-delay attribute
        voiceWave.style.setProperty('--anim-delay', `${delay}s`);
        content.style.setProperty('--anim-delay', `${delay + 2}s`);
        
        // Reset animation on hover
        bubble.addEventListener('mouseenter', () => {
            // Only reset if animation has already played
            if (parseFloat(getComputedStyle(voiceWave).opacity) < 0.5) {
                // Reset voice wave visibility
                voiceWave.parentElement.style.animation = 'none';
                voiceWave.parentElement.style.opacity = '1';
                voiceWave.parentElement.style.height = '30px';
                voiceWave.parentElement.style.marginBottom = '8px';
                
                // Hide text content
                content.style.animation = 'none';
                content.style.opacity = '0';
                
                // Force reflow
                void bubble.offsetWidth;
                
                // Restart animations
                voiceWave.parentElement.style.animation = `fadeOutVoice 0.5s ease-out forwards 1s`;
                content.style.animation = `fadeInText 0.5s ease-out forwards 1s`;
            }
        });
    });
}

// Enhanced Platform Logos Carousel with custom colors and interactions
function initPlatformLogosCarousel() {
    const logoContainer = document.querySelector('.logos-carousel-container');
    const logoItems = document.querySelectorAll('.logo-item');
    
    if (!logoContainer || !logoItems.length) return;
    
    // Add entrance animations
    logoItems.forEach((item, index) => {
        // Set initial state for entrance animation
        item.style.opacity = '0';
        item.style.transform = 'translateY(30px)';
        
        // Animate in with delay based on index
        setTimeout(() => {
            item.style.transition = 'all 0.7s cubic-bezier(0.23, 1, 0.32, 1)';
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        }, 100 + (index * 120)); // Staggered delay
    });
    
    // Add subtle floating animation to each logo
    logoItems.forEach(item => {
        // Random subtle floating effect for each item
        const randomDelay = Math.random() * 2;
        const randomDuration = 3 + Math.random() * 2;
        
        item.style.animation = `float ${randomDuration}s ease-in-out ${randomDelay}s infinite alternate`;
    });
    
    // Add interactive hover effects with sound
    logoItems.forEach(item => {
        item.addEventListener('mouseenter', () => {
            // Play subtle hover sound if we add audio later
            item.style.transform = 'translateY(-10px)';
            
            // Make other items slightly less prominent
            logoItems.forEach(otherItem => {
                if (otherItem !== item) {
                    otherItem.style.filter = 'brightness(0.7)';
                    otherItem.style.transform = 'scale(0.95)';
                }
            });
        });
        
        item.addEventListener('mouseleave', () => {
            // Reset this item
            item.style.transform = '';
            
            // Reset other items
            logoItems.forEach(otherItem => {
                if (otherItem !== item) {
                    otherItem.style.filter = '';
                    otherItem.style.transform = '';
                }
            });
        });
    });
    
    // Add floating animation keyframes if not already in the stylesheet
    if (!document.querySelector('style#float-animation')) {
        const style = document.createElement('style');
        style.id = 'float-animation';
        style.textContent = `
            @keyframes float {
                0% {
                    transform: translateY(0);
                }
                100% {
                    transform: translateY(-8px);
                }
            }
        `;
        document.head.appendChild(style);
    }
}

// Initialize carousel when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // ... existing initialization code ...
    
    initPlatformLogosCarousel();
    
    // ... existing code ...
});

// Voice-to-Prompt Animation
function initVoiceToPromptAnimation() {
    const examplePairs = [
        {
            input: '"I need a... hmm, what do you call it... like a function that can sort data? But also maybe check for... you know... errors and edge cases? I think that would be good."',
            output: 'Implement a robust sorting function with comprehensive error handling for various data types.'
        },
        {
            input: '"So I\'m trying to build this React thing... it needs to keep track of users or something, and I want it to look nice with those sliding effects? What are they called... ah, animations, right."',
            output: 'Create a React functional component with useState hook for user data and CSS transitions for smooth animations.'
        },
        {
            input: '"I want to get some data from my database, like I need the users and their... um... the orders they made recently? Not all orders, just the new ones. Can you help with that?"',
            output: 'Write a SQL query joining Users and Orders tables with a WHERE clause to filter by recent date.'
        }
    ];
    
    const voiceInputText = document.querySelector('.voice-input-text');
    const optimizedText = document.querySelector('.optimized-prompt-text');
    const voiceWave = document.querySelector('.voice-wave-animation');
    
    let currentIndex = 0;
    
    function updateExample() {
        // Reset animations
        voiceInputText.style.opacity = '0';
        optimizedText.style.opacity = '0';
        
        // Show voice wave animation
        voiceWave.style.display = 'flex';
        
        // After a short delay, show the voice input text
    setTimeout(() => {
            voiceInputText.textContent = examplePairs[currentIndex].input;
            voiceInputText.style.opacity = '0';
            void voiceInputText.offsetWidth; // Force reflow
            voiceInputText.style.animation = 'typeIn 1s ease-out forwards';
        }, 500);
        
        // After the voice input is shown, hide the wave and show the optimized output
        setTimeout(() => {
            voiceWave.style.opacity = '0';
            optimizedText.textContent = examplePairs[currentIndex].output;
            optimizedText.style.opacity = '0';
            void optimizedText.offsetWidth; // Force reflow
            optimizedText.style.animation = 'fadeIn 0.5s ease-out forwards';
        }, 2000);
        
        // Setup for next example
        setTimeout(() => {
            voiceWave.style.opacity = '1';
            currentIndex = (currentIndex + 1) % examplePairs.length;
        }, 5000);
    }
    
    // Start the cycle
    updateExample();
    
    // Repeat the cycle every 6 seconds
    setInterval(updateExample, 6000);
}

// Audio typing and wave animations for the voice command showcase
function initAudioTypingWave() {
    const audioTypingContainer = document.querySelector('.audio-typing-container');
    const audioWaveContainer = document.querySelector('.audio-wave-container');
    
    if (!audioTypingContainer || !audioWaveContainer) return;
    
    // Add event listeners for typing and wave animations
    audioTypingContainer.addEventListener('animationend', () => {
        audioWaveContainer.classList.add('active');
    });
    
    audioWaveContainer.addEventListener('animationend', () => {
        audioTypingContainer.classList.remove('active');
    });
}

// Restart the typing animation for the voice command
function restartTypingAnimation() {
    const inputText = document.querySelector('.input-text');
    if (inputText && inputText.classList.contains('typing-animation')) {
        // Force the animation to restart
        inputText.style.animation = 'none';
        void inputText.offsetWidth; // Force reflow
        inputText.style.animation = 'typeText 3s steps(50, end) forwards, blinkCursor 0.7s step-end infinite';
    }
}

// Add animation for use case cards
function initUseCaseAnimations() {
    const useCaseCards = document.querySelectorAll('.use-case-card');
    
    if (useCaseCards.length === 0) return;
    
    // Add hover animations and tilt effect to cards
    useCaseCards.forEach(card => {
        // Add subtle entrance animation with delay
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        
        // Create staggered entrance effect
        const index = Array.from(useCaseCards).indexOf(card);
    setTimeout(() => {
            card.style.transition = 'opacity 0.8s ease, transform 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 100 + (index * 150));
        
        card.addEventListener('mousemove', (e) => {
            const rect = card.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;
            
            const deltaX = (x - centerX) / centerX;
            const deltaY = (y - centerY) / centerY;
            
            // Apply subtle tilt effect
            card.style.transform = `perspective(1000px) rotateX(${deltaY * -3}deg) rotateY(${deltaX * 3}deg) translateY(-5px)`;
            
            // Dynamic shadow based on mouse position
            const shadowX = deltaX * 10;
            const shadowY = deltaY * 10;
            card.style.boxShadow = `
                ${shadowX}px ${shadowY}px 30px rgba(0, 0, 0, 0.2),
                0 0 15px rgba(20, 241, 149, 0.15)
            `;
            
            // Highlight quote on hover
            const quote = card.querySelector('.user-quote');
            if (quote && y > quote.offsetTop && y < (quote.offsetTop + quote.offsetHeight)) {
                quote.style.borderLeftColor = 'var(--color-primary)';
                quote.style.borderLeftWidth = '4px';
                quote.style.transform = 'translateX(2px)';
                quote.style.boxShadow = '0 5px 15px rgba(20, 241, 149, 0.1)';
            } else if (quote) {
                quote.style.borderLeftWidth = '3px';
                quote.style.transform = 'translateX(0)';
                quote.style.boxShadow = 'none';
            }
        });
        
        // Reset transform on mouse leave
        card.addEventListener('mouseleave', () => {
            card.style.transform = 'translateY(-5px)';
            card.style.boxShadow = '0 15px 30px rgba(0, 0, 0, 0.2), 0 0 15px rgba(20, 241, 149, 0.1)';
            card.style.transition = 'all 0.5s ease';
            
            // Reset quote styles
            const quote = card.querySelector('.user-quote');
            if (quote) {
                quote.style.borderLeftWidth = '3px';
                quote.style.transform = 'translateX(0)';
                quote.style.boxShadow = 'none';
                quote.style.transition = 'all 0.3s ease';
            }
        });
    });
    
    // Animate icons on scroll
    const animateIconsOnScroll = () => {
        const userIcons = document.querySelectorAll('.user-icon');
        userIcons.forEach(icon => {
            const rect = icon.getBoundingClientRect();
            const isInView = rect.top < window.innerHeight && rect.bottom > 0;
            
            if (isInView) {
                icon.classList.add('animate-icon');
        setTimeout(() => {
                    icon.style.transition = 'transform 0.3s ease, background 0.3s ease';
                }, 500);
            }
        });
    };
    
    // Add animation class for CSS transitions
    document.addEventListener('scroll', animateIconsOnScroll);
    
    // Initial check for visible elements
    setTimeout(animateIconsOnScroll, 300);
    
    // Add shine effect to glow button
    const glowButton = document.querySelector('.glow-button');
    if (glowButton) {
        glowButton.addEventListener('mousemove', (e) => {
            const rect = glowButton.getBoundingClientRect();
            const x = e.clientX - rect.left;
            
            const centerX = rect.width / 2;
            const deltaX = (x - centerX) / centerX;
            
            // Move the shine effect based on mouse position
            glowButton.style.setProperty('--shine-position', `${deltaX * 50 + 50}%`);
        });
    }
}

// Function to initialize smooth scroll animations
function initSmoothScrollAnimations() {
    // Add smooth scroll to all internal links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            if (targetId === '#') return;
            
            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Function to initialize appear-on-scroll animations
function initAppearOnScroll() {
    // Add more sections and elements to animate when in view
    const animatedSections = [
        '.features-overview-section',
        '.use-cases-section',
        '.intro-section',
        '.speed-benefits-section',
        '.platform-logos-section',
        '.intro-feature-item',
        '.use-case-card',
        '.intro-cta',
        '.speed-benefits-content',
        '.speed-benefits-visualization',
        '.intro-title-container',
        '.intro-video',
        '.metric'
    ];
    
    // Add specific animation classes
    document.querySelectorAll('.intro-feature-item').forEach((el, index) => {
        el.classList.add('appear-on-scroll');
        el.style.animationDelay = `${0.1 + (index * 0.1)}s`;
    });
    
    document.querySelectorAll('.use-case-card').forEach((el, index) => {
        el.classList.add('appear-on-scroll', 'slide-up');
        el.style.animationDelay = `${0.1 + (index * 0.1)}s`;
    });
    
    document.querySelectorAll('.metric').forEach((el, index) => {
        el.classList.add('appear-on-scroll', 'scale-in');
        el.style.animationDelay = `${0.1 + (index * 0.1)}s`;
    });
    
    document.querySelectorAll('.lang-flag').forEach((el, index) => {
        el.classList.add('appear-on-scroll', 'fade-in');
        el.style.animationDelay = `${0.05 + (index * 0.05)}s`;
    });
    
    // Add the appear-on-scroll class to these elements
    animatedSections.forEach(selector => {
        document.querySelectorAll(selector).forEach(el => {
            if (!el.classList.contains('appear-on-scroll')) {
                el.classList.add('appear-on-scroll');
            }
        });
    });
    
    // Check which elements are in view
    const checkVisibility = () => {
        document.querySelectorAll('.appear-on-scroll').forEach(element => {
            const rect = element.getBoundingClientRect();
            const windowHeight = window.innerHeight || document.documentElement.clientHeight;
            
            // If element is in viewport
            if (rect.top <= windowHeight * 0.85 && rect.bottom >= 0) {
                element.classList.add('is-visible');
            }
        });
    };
    
    // Run on scroll and initially
    window.addEventListener('scroll', checkVisibility);
    window.addEventListener('resize', checkVisibility);
    
    // Initial check after a slight delay to allow for page render
    setTimeout(checkVisibility, 100);
    
    // Recheck visibility after all images are loaded
    window.addEventListener('load', checkVisibility);
}

// Function to add smooth section transitions
function initSectionTransitions() {
    // Parallax scroll effect for sections
    const parallaxSections = document.querySelectorAll('.features-overview-section, .intro-section, .speed-benefits-section');
    
    const handleParallaxScroll = () => {
        const scrollY = window.scrollY;
        
        parallaxSections.forEach(section => {
            const sectionTop = section.getBoundingClientRect().top + scrollY;
            const sectionHeight = section.offsetHeight;
            const viewportHeight = window.innerHeight;
            
            // Calculate how far the section is from the viewport center
            const distanceFromCenter = (sectionTop + sectionHeight / 2) - (scrollY + viewportHeight / 2);
            
            // Apply subtle parallax effect
            const parallaxOffset = distanceFromCenter * 0.05;
            
            section.style.transform = `translateY(${-parallaxOffset}px)`;
            
            // Add subtle scale effect when section is in view
            const percentInView = 1 - Math.min(1, Math.abs(distanceFromCenter) / (viewportHeight * 1.5));
            const scale = 0.95 + (percentInView * 0.05);
            
            // Only apply scale if we're close to the section
            if (percentInView > 0.1) {
                section.style.transform = `translateY(${-parallaxOffset}px) scale(${scale})`;
            }
        });
    };
    
    window.addEventListener('scroll', handleParallaxScroll);
    handleParallaxScroll(); // Call once to set initial positions
}

// Add background color transition on scroll
function initBackgroundTransitions() {
    const sections = document.querySelectorAll('.top-section, .features-overview-section, .intro-section, .speed-benefits-section, .use-cases-section');
    
    const handleScroll = () => {
        sections.forEach(section => {
            const rect = section.getBoundingClientRect();
            if (rect.top < window.innerHeight * 0.75 && rect.bottom > 0) {
                // Apply style for visible section
                section.classList.add('active-section');
                // Change body color based on the section
                document.body.setAttribute('data-section', section.id);
            } else {
                section.classList.remove('active-section');
            }
        });
    };
    
    window.addEventListener('scroll', handleScroll);
    setTimeout(handleScroll, 100); // Initial check
}

// Add smooth scrolling behavior to navigation
function enhanceNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    
    // Add active class to nav links based on scroll position
    const updateNavLinks = () => {
        // Find which section is currently most visible
        let currentSectionId = '';
        let maxVisibility = 0;
        
        document.querySelectorAll('section, .top-section, .features-overview-section, .use-cases-section').forEach(section => {
            const sectionId = section.id || '';
            if (!sectionId) return;
            
            const rect = section.getBoundingClientRect();
            const height = rect.height;
            const top = rect.top;
            
            // Calculate visibility as percentage of the section in viewport
            let visibility = 0;
            if (top <= 0) {
                visibility = Math.min(height, height + top) / height;
            } else if (top < window.innerHeight) {
                visibility = Math.min(window.innerHeight - top, height) / height;
            }
            
            if (visibility > maxVisibility) {
                maxVisibility = visibility;
                currentSectionId = sectionId;
            }
        });
        
        // Update active nav link
        navLinks.forEach(link => {
            const href = link.getAttribute('href').substring(1);
            if (href === currentSectionId) {
                link.classList.add('active');
            } else {
                link.classList.remove('active');
            }
        });
    };
    
    window.addEventListener('scroll', updateNavLinks);
    updateNavLinks(); // Initial update
}

// Initialize new animations for the page
document.addEventListener('DOMContentLoaded', () => {
    // ... existing code ...
    
    // Initialize smooth scroll animations
    initSmoothScrollAnimations();
    
    // Add appear-on-scroll effect
    initAppearOnScroll();
    
    // Add smooth scrolling for anchor links
    initSmoothAnchorLinks();
    
    // Initialize scroll progress indicator
    initScrollProgress();
    
    // Initialize scroll to top button
    initScrollToTop();
    
    // Add section transitions with parallax
    initSectionTransitions();
    
    // Add background transitions
    initBackgroundTransitions();
    
    // Enhance navigation highlighting
    enhanceNavigation();
}); 

// Initialize scroll to top button functionality
function initScrollToTop() {
    const scrollToTopButton = document.querySelector('.scroll-to-top');
    
    if (!scrollToTopButton) return;
    
    // Show button when page is scrolled down
    window.addEventListener('scroll', () => {
        if (window.scrollY > 300) {
            scrollToTopButton.classList.add('visible');
        } else {
            scrollToTopButton.classList.remove('visible');
        }
    });
    
    // Scroll to top when button is clicked
    scrollToTopButton.addEventListener('click', (e) => {
        e.preventDefault();
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

// Simple Platform Logos Display
function initPlatformLogosCarousel() {
    // Make logos display better
    const logoItems = document.querySelectorAll('.logo-item');
    
    if (!logoItems.length) return;
    
    // Basic fade-in for the logos
    logoItems.forEach(item => {
        item.style.opacity = '0';
        setTimeout(() => {
            item.style.transition = 'opacity 0.5s ease';
            item.style.opacity = '1';
        }, 100);
    });
} 

// Initialize FAQ functionality
function initFAQ() {
    const faqItems = document.querySelectorAll('.faq-item');
    
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        
        question.addEventListener('click', () => {
            const isActive = item.classList.contains('active');
            
            // Close all FAQ items
            faqItems.forEach(faqItem => {
                faqItem.classList.remove('active');
            });
            
            // Toggle the clicked item
            if (!isActive) {
                item.classList.add('active');
            }
        });
    });
}

// Initialize Blog animations and functionality
function initBlog() {
    // Animate blog cards and article on scroll
    const blogItems = document.querySelectorAll('.featured-article, .blog-card, .blog-newsletter');
    
    const blogObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('appear');
                blogObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.2 });
    
    blogItems.forEach(item => {
        item.classList.add('blog-animate');
        blogObserver.observe(item);
    });
    
    // Handle newsletter form submission
    const newsletterForm = document.querySelector('.newsletter-form');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const emailInput = this.querySelector('input[type="email"]');
            if (emailInput.value) {
                // Show success message
                const successMessage = document.createElement('div');
                successMessage.className = 'newsletter-success';
                successMessage.textContent = 'Thanks for subscribing!';
                
                // Replace form with success message
                this.style.display = 'none';
                this.parentNode.appendChild(successMessage);
                
                // Clear the input
                emailInput.value = '';
            }
        });
    }
    
    // Add hover effects for blog cards
    const blogCards = document.querySelectorAll('.blog-card');
    blogCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            blogCards.forEach(c => {
                if (c !== card) {
                    c.classList.add('dimmed');
                }
            });
        });
        
        card.addEventListener('mouseleave', function() {
            blogCards.forEach(c => {
                c.classList.remove('dimmed');
            });
        });
    });
}

// Add to DOM content loaded event
document.addEventListener('DOMContentLoaded', function() {
    // Initialize existing code
    
    // Initialize FAQ
    initFAQ();
    
    // Initialize Blog
    initBlog();
    
    // Handle footer links
    initFooterLinks();
    
    // Add smooth scrolling for all section links
    const allSectionLinks = document.querySelectorAll('a[href^="#"]');
    allSectionLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const targetId = this.getAttribute('href');
            if (targetId !== '#') {
                e.preventDefault();
                const targetSection = document.querySelector(targetId);
                if (targetSection) {
                    targetSection.scrollIntoView({ behavior: 'smooth' });
                }
            }
        });
    });
});

// Initialize footer links
function initFooterLinks() {
    // Get all links in the footer
    const footerLinks = document.querySelectorAll('.footer-links a');
    
    footerLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            
            // Handle links with hash (#) for on-page navigation
            if (href.includes('#') && !href.startsWith('/')) {
                e.preventDefault();
                const targetId = href.split('#')[1];
                const targetElement = document.getElementById(targetId);
                
                if (targetElement) {
                    targetElement.scrollIntoView({ behavior: 'smooth' });
                } else if (window.location.pathname !== '/') {
                    // If we're not on the landing page, redirect to the landing page with the hash
                    window.location.href = '/' + href;
                }
            }
            
            // Handle Features link
            if (href === '/#features' || href === '#features') {
                e.preventDefault();
                const featuresSection = document.querySelector('.features-overview-section');
                
                if (featuresSection) {
                    featuresSection.scrollIntoView({ behavior: 'smooth' });
                } else if (window.location.pathname !== '/') {
                    // If we're not on the landing page, redirect to the landing page with the hash
                    window.location.href = '/#features';
                }
            }
            
            // Handle Pricing link
            if (href === '/#pricing' || href === '#pricing') {
                e.preventDefault();
                // Pricing section doesn't exist yet, so scroll to a related section for now
                const relatedSection = document.querySelector('.speed-benefits-section');
                
                if (relatedSection) {
                    relatedSection.scrollIntoView({ behavior: 'smooth' });
                } else if (window.location.pathname !== '/') {
                    // If we're not on the landing page, redirect to the landing page with the hash
                    window.location.href = '/#pricing';
                }
            }
            
            // Handle Use Cases link
            if (href === '/#use-cases' || href === '#use-cases') {
                e.preventDefault();
                const useCasesSection = document.getElementById('use-cases');
                
                if (useCasesSection) {
                    useCasesSection.scrollIntoView({ behavior: 'smooth' });
                } else if (window.location.pathname !== '/') {
                    // If we're not on the landing page, redirect to the landing page with the hash
                    window.location.href = '/#use-cases';
                }
            }
            
            // Handle Blog link
            if (href === '/#blog' || href === '#blog') {
                e.preventDefault();
                const blogSection = document.getElementById('blog-section');
                
                if (blogSection) {
                    blogSection.scrollIntoView({ behavior: 'smooth' });
                } else if (window.location.pathname !== '/') {
                    // If we're not on the landing page, redirect to the landing page with the hash
                    window.location.href = '/#blog';
                }
            }
            
            // Handle FAQ link
            if (href === '/#faq' || href === '#faq') {
                e.preventDefault();
                const faqSection = document.getElementById('faq');
                
                if (faqSection) {
                    faqSection.scrollIntoView({ behavior: 'smooth' });
                } else if (window.location.pathname !== '/') {
                    // If we're not on the landing page, redirect to the landing page with the hash
                    window.location.href = '/#faq';
                }
            }
        });
    });
}

function initMobileMenu() {
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const navLinks = document.querySelector('.nav-links');
    const navButtons = document.querySelector('.nav-buttons');
    const body = document.querySelector('body');
    
    if (mobileMenuToggle) {
        mobileMenuToggle.addEventListener('click', () => {
            mobileMenuToggle.classList.toggle('active');
            
            if (navLinks) {
                navLinks.classList.toggle('active');
            }
            
            // Toggle body scroll lock
            body.classList.toggle('menu-open');
            
            // Find nav buttons even if they're not direct siblings of the nav links
            if (navButtons) {
                if (mobileMenuToggle.classList.contains('active')) {
                    navButtons.style.left = "0";
                } else {
                    navButtons.style.left = "-100%";
                }
            }
        });
        
        // Handle link clicks - close menu
        document.querySelectorAll('.nav-links a, .nav-buttons a').forEach(link => {
            link.addEventListener('click', () => {
                mobileMenuToggle.classList.remove('active');
                if (navLinks) {
                    navLinks.classList.remove('active');
                }
                body.classList.remove('menu-open');
                if (navButtons) {
                    navButtons.style.left = "-100%";
                }
            });
        });
    }
}
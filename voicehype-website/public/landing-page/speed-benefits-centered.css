/* Centered Speed Benefits Styles */
.speed-benefits-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    gap: 4rem;
}

.speed-benefits-content {
    flex: 1;
    max-width: 600px;
}

.speed-benefits-visualization {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.comparison-chart {
    width: 100%;
    max-width: 450px;
    margin: 0;
    padding: 0;
    background: transparent;
    box-shadow: none;
}

.speed-benefits-content.centered {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.speed-benefits-content.centered .speed-benefits-title {
    text-align: center;
    display: block;
}

.speed-benefits-content.centered .speed-benefits-title::after {
    left: 50%;
    transform: translateX(-50%);
}

.speed-benefits-content.centered .speed-benefits-metrics {
    justify-content: center;
}

.speed-benefits-content.centered .speed-benefits-description {
    text-align: center;
    max-width: 750px;
    margin-left: auto;
    margin-right: auto;
}

.speed-benefits-content.centered .speed-benefits-cta {
    justify-content: center;
}

@media (max-width: 768px) {
    .speed-benefits-container {
        flex-direction: column;
        padding: 0 1rem;
        gap: 3rem;
    }
    
    .speed-benefits-content.centered {
        padding: 0 1rem;
    }
} 
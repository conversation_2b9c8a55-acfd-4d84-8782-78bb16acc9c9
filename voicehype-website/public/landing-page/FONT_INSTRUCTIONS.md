# Font Installation Instructions

To fix the CORS issue with Clash Display font, we've implemented a self-hosted solution:

1. Download the Clash Display font files:
   - Go to https://fontshare.com/fonts/clash-display
   - Select all weights (or at least Regular, Medium, Semibold, Bold)
   - Click "Download Fonts"

2. Extract the font files from the download:
   - From the extracted files, locate the `woff2` format files (they're in the "fonts/ClashDisplay/")
   - Copy all the `woff2` files into: `public/landing-page/fonts/clash-display/`

3. Required font files:
   - ClashDisplay-Regular.woff2
   - ClashDisplay-Medium.woff2
   - ClashDisplay-Semibold.woff2
   - ClashDisplay-Bold.woff2
   - ClashDisplay-Extralight.woff2
   - ClashDisplay-Light.woff2

4. The fonts.css file that you need is already created with proper @font-face declarations.

5. We've already updated index.html to include the fonts.css file.

This approach eliminates the CORS issues by serving the font files directly from your domain. 
// Add event listeners to expand/collapse buttons
document.addEventListener('DOMContentLoaded', function() {
    // Sample Read More button
    const sampleReadMoreBtn = document.querySelector('.sample-read-more-btn');
    const sampleFullText = document.querySelector('.sample-full-text');
    
    if (sampleReadMoreBtn && sampleFullText) {
        sampleReadMoreBtn.addEventListener('click', function() {
            sampleFullText.classList.toggle('expanded');
            sampleReadMoreBtn.textContent = sampleFullText.classList.contains('expanded') ? 'Read less' : 'Read more';
        });
    }
    
    // Output Read More button
    const outputReadMoreBtn = document.querySelector('.output-read-more-btn');
    const outputFullText = document.querySelector('.output-full-text');
    
    if (outputReadMoreBtn && outputFullText) {
        outputReadMoreBtn.addEventListener('click', function() {
            outputFullText.classList.toggle('expanded');
            outputReadMoreBtn.textContent = outputFullText.classList.contains('expanded') ? 'Read less' : 'Read more';
        });
    }
});

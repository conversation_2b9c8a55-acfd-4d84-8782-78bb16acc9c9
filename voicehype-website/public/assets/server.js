const http = require('http');
const fs = require('fs');
const path = require('path');

const server = http.createServer((req, res) => {
    if (req.url === '/') {
        fs.readFile(path.join(__dirname, 'generate-logo.html'), (err, content) => {
            if (err) {
                res.writeHead(500);
                res.end('Error loading the page');
                return;
            }
            res.writeHead(200, { 'Content-Type': 'text/html' });
            res.end(content);
        });
    } else if (req.url.endsWith('.woff2')) {
        // Handle font file requests
        const fontPath = path.join(__dirname, '..', '..', req.url);
        fs.readFile(fontPath, (err, content) => {
            if (err) {
                res.writeHead(404);
                res.end('Font not found');
                return;
            }
            res.writeHead(200, { 'Content-Type': 'font/woff2' });
            res.end(content);
        });
    }
});

const PORT = 3000;
server.listen(PORT, () => {
    console.log(`Server running at http://localhost:${PORT}/`);
}); 
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Generate VoiceHype Logo</title>
    <style>
        @font-face {
            font-family: 'ClashDisplay-Bold';
            src: url('../../src/assets/ClashDisplay_Complete/Fonts/WEB/fonts/ClashDisplay-Bold.woff2') format('woff2');
            font-weight: 700;
            font-style: normal;
        }

        :root {
            --color-primary: #14F195;
        }

        body {
            background: transparent;
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .nav-logo {
            display: flex;
            align-items: center;
            gap: 0.4rem;
        }

        .logo-container {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo-container svg {
            filter: drop-shadow(0 0 3px rgba(20, 241, 149, 0.2));
        }

        .logo-text {
            font-family: 'ClashDisplay-Bold', 'Inter', system-ui, sans-serif;
            font-size: 1.8rem;
            font-weight: 700;
            letter-spacing: -0.02em;
            color: #ffffff;
            margin: 0;
            padding: 0;
            line-height: 1;
        }

        .hype-text-green {
            color: var(--color-primary);
        }

        #controls {
            margin-top: 20px;
        }

        button {
            padding: 8px 16px;
            margin: 0 8px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="nav-logo">
        <div class="logo-container">
            <svg width="40" height="30" viewBox="0 0 40 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                <!-- Monitor outline with white fill -->
                <rect x="2" y="2" width="36" height="20" rx="2" fill="white" />
                <rect x="2" y="2" width="36" height="20" rx="2" stroke="#14F195" stroke-width="1.5" />
                <!-- Sound wave visualization -->
                <path d="M7 14 L10 9 L13 14 L16 9 L19 14 L22 9 L25 14 L28 9 L31 14 L34 9" stroke="#14F195"
                    stroke-width="1.5" stroke-linecap="round" />
                <!-- Stand -->
                <line x1="20" y1="22" x2="20" y2="26" stroke="#14F195" stroke-width="1.5" />
                <line x1="14" y1="26" x2="26" y2="26" stroke="#14F195" stroke-width="1.5" />
            </svg>
        </div>
        <h1 class="logo-text">Voice<span class="hype-text-green">Hype</span></h1>
    </div>
    
    <div id="controls">
        <button onclick="downloadSVG()">Download SVG</button>
        <button onclick="downloadPNG()">Download PNG</button>
    </div>

    <script>
        const downloadSVG = () => {
            // Get the existing logo elements
            const logoContainer = document.querySelector('.nav-logo');
            const iconSvg = logoContainer.querySelector('svg');
            const logoText = logoContainer.querySelector('.logo-text');

            // Create a container SVG that will hold everything
            const containerSvg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
            containerSvg.setAttribute("width", "180");
            containerSvg.setAttribute("height", "30");
            containerSvg.setAttribute("viewBox", "0 0 180 30");
            containerSvg.setAttribute("fill", "none");
            containerSvg.setAttribute("xmlns", "http://www.w3.org/2000/svg");

            // Clone the icon SVG content
            const iconContent = iconSvg.cloneNode(true);
            containerSvg.appendChild(iconContent);

            // Convert the HTML text to SVG text
            const textVoice = document.createElementNS("http://www.w3.org/2000/svg", "text");
            textVoice.setAttribute("x", "50");
            textVoice.setAttribute("y", "22");
            textVoice.setAttribute("font-family", "ClashDisplay-Bold");
            textVoice.setAttribute("font-size", "24");
            textVoice.setAttribute("font-weight", "700");
            textVoice.setAttribute("letter-spacing", "-0.02em");
            textVoice.setAttribute("fill", "white");
            textVoice.textContent = "Voice";

            const textHype = document.createElementNS("http://www.w3.org/2000/svg", "text");
            textHype.setAttribute("x", "103");
            textHype.setAttribute("y", "22");
            textHype.setAttribute("font-family", "ClashDisplay-Bold");
            textHype.setAttribute("font-size", "24");
            textHype.setAttribute("font-weight", "700");
            textHype.setAttribute("letter-spacing", "-0.02em");
            textHype.setAttribute("fill", "#14F195");
            textHype.textContent = "Hype";

            containerSvg.appendChild(textVoice);
            containerSvg.appendChild(textHype);

            // Download the SVG
            const serializer = new XMLSerializer();
            const svgStr = serializer.serializeToString(containerSvg);
            const blob = new Blob([svgStr], {type: 'image/svg+xml'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'voicehype-logo.svg';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        };

        const downloadPNG = () => {
            // First create the SVG version
            const logoContainer = document.querySelector('.nav-logo');
            const iconSvg = logoContainer.querySelector('svg');
            const logoText = logoContainer.querySelector('.logo-text');

            const containerSvg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
            containerSvg.setAttribute("width", "180");
            containerSvg.setAttribute("height", "30");
            containerSvg.setAttribute("viewBox", "0 0 180 30");
            containerSvg.setAttribute("fill", "none");
            containerSvg.setAttribute("xmlns", "http://www.w3.org/2000/svg");

            const iconContent = iconSvg.cloneNode(true);
            containerSvg.appendChild(iconContent);

            const textVoice = document.createElementNS("http://www.w3.org/2000/svg", "text");
            textVoice.setAttribute("x", "50");
            textVoice.setAttribute("y", "22");
            textVoice.setAttribute("font-family", "ClashDisplay-Bold");
            textVoice.setAttribute("font-size", "24");
            textVoice.setAttribute("font-weight", "700");
            textVoice.setAttribute("letter-spacing", "-0.02em");
            textVoice.setAttribute("fill", "white");
            textVoice.textContent = "Voice";

            const textHype = document.createElementNS("http://www.w3.org/2000/svg", "text");
            textHype.setAttribute("x", "103");
            textHype.setAttribute("y", "22");
            textHype.setAttribute("font-family", "ClashDisplay-Bold");
            textHype.setAttribute("font-size", "24");
            textHype.setAttribute("font-weight", "700");
            textHype.setAttribute("letter-spacing", "-0.02em");
            textHype.setAttribute("fill", "#14F195");
            textHype.textContent = "Hype";

            containerSvg.appendChild(textVoice);
            containerSvg.appendChild(textHype);

            // Convert to PNG
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = 180;
            canvas.height = 30;

            const svgStr = new XMLSerializer().serializeToString(containerSvg);
            const blob = new Blob([svgStr], {type: 'image/svg+xml'});
            const url = URL.createObjectURL(blob);
            const img = new Image();

            img.onload = () => {
                ctx.drawImage(img, 0, 0);
                const a = document.createElement('a');
                a.href = canvas.toDataURL('image/png');
                a.download = 'voicehype-logo.png';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            };

            img.src = url;
        };
    </script>
</body>
</html> 
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security - VoiceHype</title>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Fix for Netlify base path issues -->
    <base href="/">

    <!-- Preload critical font files -->
    <link rel="preload" href="/landing-page/fonts/clash-display/ClashDisplay-Regular.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="/landing-page/fonts/clash-display/ClashDisplay-Bold.woff2" as="font" type="font/woff2" crossorigin>

    <!-- Preload critical CSS files -->
    <link rel="preload" href="/landing-page/styles.css" as="style">
    <link rel="preload" href="/landing-page/responsive-fixes.css" as="style">

    <link href="/landing-page/fonts.css" rel="stylesheet">

    <!-- Main Landing Page CSS -->
    <link rel="stylesheet" href="/landing-page/styles.css">

    <!-- Responsive fixes for mobile -->
    <link rel="stylesheet" href="/landing-page/responsive-fixes.css">

    <!-- Policy Page Specific Styles -->
    <style>
        body {
            background-color: #080814;
            color: #ffffff;
            font-family: 'Inter', sans-serif;
        }

        .policy-page {
            width: 100%;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        main {
            flex: 1;
            padding: 3rem 0;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        .logo-text, .footer-logo-text {
            font-family: 'Clash Display', 'Inter', system-ui, sans-serif;
            font-weight: 700;
        }

        .hype-text {
            color: #14F195;
        }

        .hype-text-green {
            color: #14F195;
        }

        .signup-button {
            color: #000 !important;
            background-color: #14F195 !important;
        }

        .prose {
            color: #ffffff;
            line-height: 1.6;
            width: 100%;
            max-width: 800px;
        }

        .prose h1 {
            font-size: 2.5rem;
            margin-bottom: 2rem;
            color: #ffffff;
        }

        .prose h2 {
            color: #f0f0f0;
            margin-top: 2rem;
            font-size: 1.75rem;
        }

        .prose h3 {
            color: #f0f0f0;
            margin-top: 1.5rem;
            font-size: 1.35rem;
        }

        .prose p, .prose li, .prose strong {
            color: #ffffff;
            margin-bottom: 1rem;
        }

        .prose ul, .prose ol {
            margin-bottom: 1.5rem;
            padding-left: 2rem;
        }
    </style>
</head>
<body>
    <div class="policy-page">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <div class="logo-container">
                        <svg width="40" height="30" viewBox="0 0 40 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <!-- Monitor outline with white fill -->
                            <rect x="2" y="2" width="36" height="20" rx="2" fill="white" />
                            <rect x="2" y="2" width="36" height="20" rx="2" stroke="#14F195" stroke-width="1.5" />
                            <!-- Sound wave visualization -->
                            <path d="M7 14 L10 9 L13 14 L16 9 L19 14 L22 9 L25 14 L28 9 L31 14 L34 9" stroke="#14F195"
                                stroke-width="1.5" stroke-linecap="round" />
                            <!-- Stand -->
                            <line x1="20" y1="22" x2="20" y2="26" stroke="#14F195" stroke-width="1.5" />
                            <line x1="14" y1="26" x2="26" y2="26" stroke="#14F195" stroke-width="1.5" />
                        </svg>
                    </div>
                    <h1 class="logo-text">Voice<span class="hype-text-green">Hype</span></h1>
                </div>
                <div class="nav-buttons">
                    <a href="#" class="nav-button login-button">Log In</a>
                    <a href="#" class="nav-button signup-button">Sign Up</a>
                </div>
                <button class="mobile-menu-toggle">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </button>
            </div>
        </nav>

        <main>
            <div class="container">
                <div class="prose">
                    <h1>Security</h1>
                    <p>Last Updated: May 10, 2025</p>

                    <h2>Our Security Commitment</h2>
                    <p>At VoiceHype, we prioritize the security and privacy of our users' data. Your trust is important to us, and we take all necessary measures to protect your information.</p>

                    <h2>Zero Storage Policy for Voice Data</h2>
                    <p><strong>We never store your voice recordings, transcripts, or optimized prompts.</strong> This is our firm commitment to your privacy.</p>
                    <ul>
                        <li>Voice recordings are processed in real-time and are never stored</li>
                        <li>Transcripts are generated only for immediate processing and are not retained</li>
                        <li>Optimized prompts are delivered to you directly and are not saved on our servers</li>
                    </ul>

                    <h2>Third-Party Services</h2>
                    <p>We carefully select our service providers based on their security standards. All third-party services we use adhere to strict data protection policies and do not store your voice data or transcripts.</p>

                    <h2>Security Vulnerability Reporting</h2>
                    <p>If you discover a security vulnerability, please report <NAME_EMAIL>. We will respond promptly to address any concerns, Inshaa Allah.</p>

                    <h2>Contact Information</h2>
                    <p>For questions about our security practices, please contact <NAME_EMAIL>.</p>
                </div>
            </div>
        </main>

        <footer class="footer-section">
            <div class="footer-container">
                <div class="footer-content">
                    <div class="footer-logo-section">
                        <div class="footer-logo">
                            <div class="logo-container">
                                <svg width="40" height="30" viewBox="0 0 40 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect x="2" y="2" width="36" height="20" rx="2" fill="white" />
                                    <rect x="2" y="2" width="36" height="20" rx="2" stroke="#14F195" stroke-width="1.5" />
                                    <path d="M7 14 L10 9 L13 14 L16 9 L19 14 L22 9 L25 14 L28 9 L31 14 L34 9" stroke="#14F195"
                                        stroke-width="1.5" stroke-linecap="round" />
                                    <line x1="20" y1="22" x2="20" y2="26" stroke="#14F195" stroke-width="1.5" />
                                    <line x1="14" y1="26" x2="26" y2="26" stroke="#14F195" stroke-width="1.5" />
                                </svg>
                            </div>
                            <h3 class="footer-logo-text">Voice<span class="hype-text-green">Hype</span></h3>
                        </div>
                        <p class="footer-tagline">Revolutionizing coding with voice-to-prompt technology</p>
                    </div>

                    <!-- Footer links with a more structured layout for mobile -->
                    <div class="footer-links">
                        <div class="footer-links-container">
                            <div class="footer-links-column">
                                <h4>Product</h4>
                                <ul>
                                    <li><a href="/#features">Features</a></li>
                                    <li><a href="/#pricing-section">Pricing</a></li>
                                    <li><a href="/#use-cases">Use Cases</a></li>
                                    <li><a href="/#blog-section">Blog</a></li>
                                    <li><a href="/#faq">FAQ</a></li>
                                </ul>
                            </div>
                            <div class="footer-links-column">
                                <h4>Legal</h4>
                                <ul>
                                    <li><a href="/privacy-policy.html">Privacy Policy</a></li>
                                    <li><a href="/terms-of-service.html">Terms of Service</a></li>
                                    <li><a href="/refund-policy.html">Refund Policy</a></li>
                                    <li><a href="/security.html">Security</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <div class="footer-bottom-container">
                    <p class="copyright">
                        <!-- 2025 VoiceHype. All rights reserved. -->
                    </p>
                    <div class="contact-info">
                        <a href="mailto:<EMAIL>" class="contact-email">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z">
                                </path>
                                <polyline points="22,6 12,13 2,6"></polyline>
                            </svg>
                            <EMAIL>
                        </a>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- Mobile menu functionality -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM fully loaded - setting up mobile menu');

            // Mobile menu toggle functionality
            const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
            const navButtons = document.querySelector('.nav-buttons');
            const body = document.body;

            console.log('Mobile menu elements:', {
                mobileMenuToggle: mobileMenuToggle,
                navButtons: navButtons
            });

            if (mobileMenuToggle) {
                mobileMenuToggle.addEventListener('click', function() {
                    console.log('Mobile menu toggle clicked');
                    mobileMenuToggle.classList.toggle('active');

                    // Toggle body scroll lock
                    body.classList.toggle('menu-open');

                    // Handle nav buttons visibility
                    if (navButtons) {
                        if (mobileMenuToggle.classList.contains('active')) {
                            navButtons.style.position = 'fixed';
                            navButtons.style.top = '80px';
                            navButtons.style.left = '0';
                            navButtons.style.width = '100%';
                            navButtons.style.flexDirection = 'column';
                            navButtons.style.alignItems = 'center';
                            navButtons.style.gap = '1rem';
                            navButtons.style.padding = '1.5rem 0';
                            navButtons.style.backgroundColor = 'rgba(0, 0, 0, 0.9)';
                            navButtons.style.backdropFilter = 'blur(10px)';
                            navButtons.style.zIndex = '1000';
                            console.log('Navigation buttons shown');
                        } else {
                            navButtons.style.position = '';
                            navButtons.style.top = '';
                            navButtons.style.left = '';
                            navButtons.style.width = '';
                            navButtons.style.flexDirection = '';
                            navButtons.style.alignItems = '';
                            navButtons.style.gap = '';
                            navButtons.style.padding = '';
                            navButtons.style.backgroundColor = '';
                            navButtons.style.backdropFilter = '';
                            navButtons.style.zIndex = '';
                            console.log('Navigation buttons hidden');
                        }
                    }
                });
            } else {
                console.error('Mobile menu toggle button not found!');
            }
        });

        // Handle blog links to scroll to blog section on landing page
        document.querySelectorAll('a[href*="blog"], a[href="/#blog-section"]').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();

                // Navigate to landing page with blog section hash
                window.location.href = '/#blog-section';

                return false;
            });
        });
    </script>
</body>
</html>
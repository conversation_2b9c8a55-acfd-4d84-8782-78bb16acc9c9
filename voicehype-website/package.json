{"name": "voicehype-website", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"date-fns": "^4.1.0", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@supabase/supabase-js": "^2.49.1", "@tsconfig/node22": "^22.0.0", "@types/node": "^22.13.9", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.20", "chart.js": "^4.4.8", "eslint": "^9.21.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "npm-run-all2": "^7.0.2", "postcss": "^8.5.3", "prettier": "3.5.3", "tailwindcss": "^3.4.17", "typescript": "~5.8.0", "vite": "^6.2.1", "vite-plugin-vue-devtools": "^7.7.2", "vue-chartjs": "^5.3.2", "vue-tsc": "^2.2.8"}, "format": "prettier --write src/"}
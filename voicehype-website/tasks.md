# VoiceHype Website Tasks

> **Note on checkboxes**: In markdown, checkboxes are created using `- [ ]` for unchecked and `- [x]` for checked items. When viewing in GitHub or most markdown editors, these will display as actual interactive checkboxes that you can click to toggle.

## Routing Issues
- [ ] Fix the routing problem where users are first redirected to the dashboard before landing page loads
  - Currently causes a delay in loading the landing page
  - Creates a poor first impression for visitors
  - Landing page should load immediately without dashboard redirection

## Navigation Bar
- [ ] Add a working "Use Cases" section to the website

## Examples of checked vs unchecked boxes:
- [ ] This task is not completed yet (unchecked)
- [x] This task has been completed (checked)

## Responsiveness
- [ ] Make the entire landing page responsive for all screen sizes

## Hero Section
- [ ] Fix the typing cursor animation - it currently extends too far 

## App Icons
- [ ] Replace placeholder SVG icons with real icons for supported applications (Cursor, VS Code, etc.)

## Video Section
- [ ] Remove/comment out the entire video section
  - Song in video is not appropriate
  - No tutorial video is available currently

## Statistics/Values Section
- [ ] Remove specific percentage/numeric values as they are inaccurate:
  - Replace "10 times" with "many times"
  - Replace "83%" with "faster"
  - Replace "4.5x" with "more"
  - Replace "68%" with "hundreds of hours" or "many hours of time saved daily"
- [ ] Remove "30-day satisfaction guarantee" text as no such guarantee is provided

## Features Section
- [ ] Natural Speech Recognition card:
  - Remove "99.7% accuracy" label as it's inaccurate
- [ ] Fix clunky/jittery animations throughout the features section
- [ ] Universal IDE Integration card:
  - Update text to clarify the extension only works with VS Code and its ports
  - Remove reference to JetBrains and other non-VS Code-based IDEs

## Blog Section
- [ ] Add "Coming soon, inshallah" text
- [ ] Center the blog title

## Newsletter Section
- [ ] Remove or update the newsletter section as no newsletter currently exists

## FAQ Section
- [ ] Fix expandable questions - currently they don't show answers when expanded
- [ ] Make the "Contact Support" button properly open the mail function

## Footer
- [ ] Remove "Company" and "Resources" columns entirely
- [ ] Keep only "Product" and "Legal" columns

## Legal Pages (Privacy Policy, Terms of Service, Refund Policy, Security)
- [ ] Add the original navbar to all legal pages
- [ ] Replace dummy content with actual policy content
- [ ] Remove "Back to Home" button from all legal pages
- [ ] Use the same footer as the main page on all legal pages

## New Section
- [ ] Add a humorous section highlighting how VoiceHype lets users:
  - Keep eating while coding
  - Work without touching the keyboard
  - Avoid getting food/oil on keyboard
  - Enjoy meals while remaining productive 

## Dashboard/App Tasks

### Navigation
- [ ] Fix navigation bar display issues when compiled and deployed to Netlify
  - Profile icon/circle and dark/light mode toggle get misaligned
  - Note: Works correctly in local development (npm run dev)

### API Key Management
- [ ] Correct expiration date options for API keys
  - Remove "never expire" option
  - Set maximum expiration limit to one year

### Analytics & Visualization
- [ ] Add data visualization with various charts:
  - Bar charts
  - Pie charts
  - Other chart types as needed
- [ ] Display additional usage metrics:
  - Number of minutes of transcription
  - Number of input tokens processed
  - Number of output tokens processed
  - Other relevant statistics

### Settings
- [ ] Remove the two-factor authentication section from Security tab
  - Feature not currently available 

# Extension Tasks

## Real-Time Transcription Improvements

### Sample Rate Management
- [ ] Implement automatic sample rate switching for real-time transcription:
  - Force switch to 16,000 Hz when real-time transcription is enabled with AssemblyAI
  - Restore previous sample rate when real-time transcription is disabled
  - This will improve latency by reducing audio file size during real-time processing

### Duration Calculation
- [ ] Fix calculation of duration for successfully processed chunks
  - Use the same formula as in regular transcription consistently throughout the application 

**Inshaa Allah**
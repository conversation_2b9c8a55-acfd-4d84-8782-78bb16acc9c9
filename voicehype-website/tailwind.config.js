/** @type {import('tailwindcss').Config} */
export default {
    content: [
        "./index.html",
        "./src/**/*.{vue,js,ts,jsx,tsx}",
        "./src/components/subscription/UpgradeModal.vue",
        "./public/**/*.html",
    ],
    safelist: [
        // Modal-related classes
        'fixed',
        'inset-0',
        'z-50',
        'bg-black/50',
        'modal-animation',
        'duration-300',
        'ease-out',
        'translate-y-4',
        'opacity-0',
        'sm:scale-95',
        'translate-y-0',
        'opacity-100',
        'sm:scale-100',
        // Existing safelisted classes
        'bg-primary-700',
        'dark:bg-primary-600',
        'text-white',
        'hover:bg-primary-700',
        'hover:text-white',
        'w-0',
        'w-16',
        'w-64',
        'w-auto',
        'opacity-0',
        'opacity-100',
        'translate-x-0',
        '-translate-x-full',
        'bg-opacity-50',
        'mx-auto',
        'mr-3',
        'lg:translate-x-0',
        'lg:static',
        'lg:inset-0',
        'lg:hidden',
        'lg:block',
        'hidden',
        'block',
        'fixed',
        'inset-0',
        'inset-y-0',
        'left-0',
        'z-10',
        'z-20',
        'z-30',
        'z-40',
        'z-50',
        'z-100',
        'flex',
        'items-center',
        'justify-center',
        'bg-black',
        'bg-opacity-50',
        'dark:bg-gray-900',
        'dark:bg-opacity-75',
        'backdrop-blur-sm',
        'rounded-lg',
        'p-6',
        'max-w-md',
        'w-full',
        'mx-4',
        'max-h-[90vh]',
        'overflow-y-auto',
        'border',
        'border-gray-200',
        'dark:border-[#30363d]',
        'transition-all',
        'duration-300',
        'transform',
        'font-medium',
        'bg-[#0d1117]',
        'dark:bg-[#0d1117]',
        'bg-[#161b22]',
        'dark:bg-[#161b22]',
        'bg-[#14F195]/10',
        'hover:bg-[#14F195]/5',
        'text-[#14F195]',
        'hover:text-[#14F195]',
        'text-[#c9d1d9]',
        'text-[#8b949e]',
        'border-[#30363d]',
    ],
    darkMode: 'class',
    theme: {
        extend: {
            colors: {
                primary: {
                    50: '#f0fdf4',
                    100: '#dcfce7',
                    200: '#bbf7d0',
                    300: '#86efac',
                    400: '#4ade80',
                    500: '#22c55e',
                    600: '#16a34a',
                    700: '#15803d',
                    800: '#166534',
                    900: '#14532d',
                    950: '#052e16',
                },
                secondary: {
                    50: '#f0fdfa',
                    100: '#ccfbf1',
                    200: '#99f6e4',
                    300: '#5eead4',
                    400: '#2dd4bf',
                    500: '#14b8a6',
                    600: '#0d9488',
                    700: '#0f766e',
                    800: '#115e59',
                    900: '#134e4a',
                    950: '#042f2e',
                }
            },
            fontFamily: {
                sans: ['Inter', 'sans-serif'],
                heading: ['Poppins', 'sans-serif'],
                mono: ['Roboto Mono', 'monospace'],
            }
        },
    },
    plugins: [],
}

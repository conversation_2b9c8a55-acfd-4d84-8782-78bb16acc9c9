"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Import critical CSS first to prevent FOUC
require("./assets/critical.css");
require("./assets/main.css");
require("./assets/dashboard-theme.css");
require("./shimmer-fix.css");
// Import tailwind utilities to ensure they're included in the build
require("tailwindcss/utilities.css");
var vue_1 = require("vue");
var pinia_1 = require("pinia");
var App_vue_1 = require("./App.vue");
var router_1 = require("./router");
// Initialize Paddle
require("./lib/paddle");
// Set body class immediately for best FOUC prevention
document.body.classList.add('bg-white', 'dark:bg-[#080814]');
// Initialize the app
var app = (0, vue_1.createApp)(App_vue_1.default);
// Use plugins
app.use((0, pinia_1.createPinia)());
app.use(router_1.default);
// Mount the app
app.mount('#app');

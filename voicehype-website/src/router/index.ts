import { createRouter, createWebHistory } from 'vue-router'
import { supabase } from '@/lib/supabase'
import DashboardView from '../views/DashboardView.vue'

// Define routes
const routes = [
  {
    path: '/',
    name: 'landing',
    component: () => import('../views/landing/LandingPageView.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/modular',
    name: 'modular-landing',
    component: () => import('../views/landing/ModularLandingPageView.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/app',
    component: () => import('../views/DashboardShellView.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'app',
        redirect: { name: 'dashboard' }
      },
      {
        path: 'dashboard',
        name: 'dashboard',
        component: DashboardView,
        meta: { requiresAuth: true }
      },
      {
        path: 'api-keys',
        name: 'api-keys',
        component: () => import('../views/ApiKeysView.vue'),
        meta: { requiresAuth: true }
      },
      {
        path: 'usage',
        name: 'usage',
        component: () => import('../views/UsageView.vue'),
        meta: { requiresAuth: true }
      },
      {
        path: 'payments',
        name: 'payments',
        component: () => import('../views/PaymentsView.vue'),
        meta: { requiresAuth: true }
      },
      {
        path: 'settings',
        name: 'settings',
        component: () => import('../views/SettingsView.vue'),
        meta: { requiresAuth: true }
      },
      {
        path: 'pricing',
        name: 'pricing',
        component: () => import('../views/PricingView.vue'),
        meta: { requiresAuth: true }
      },
      {
        path: 'perlin-avatar-demo',
        name: 'perlin-avatar-demo',
        component: () => import('../views/PerlinAvatarDemo.vue'),
        meta: { requiresAuth: true }
      }
    ]
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('../views/LoginView.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/register',
    name: 'register',
    component: () => import('../views/RegisterView.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/reset-password',
    name: 'reset-password',
    component: () => import('../views/ResetPasswordView.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/update-password',
    name: 'update-password',
    component: () => import('../views/UpdatePasswordView.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/auth/callback',
    name: 'auth-callback',
    component: () => import('../views/AuthCallbackView.vue')
  },
  {
    path: '/vscode-auth',
    name: 'vscode-auth',
    component: () => import('../views/VSCodeAuthView.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/web-extension-auth',
    name: 'web-extension-auth',
    component: () => import('../views/WebExtensionAuthView.vue'),
    meta: { requiresAuth: false }
  },
  // Footer link pages
  {
    path: '/privacy-policy',
    name: 'privacy-policy',
    component: () => {
      window.location.href = '/privacy-policy.html';
      return null;
    },
    meta: { requiresAuth: false }
  },
  {
    path: '/terms-of-service',
    name: 'terms-of-service',
    component: () => {
      window.location.href = '/terms-of-service.html';
      return null;
    },
    meta: { requiresAuth: false }
  },
  {
    path: '/refund-policy',
    name: 'refund-policy',
    component: () => {
      window.location.href = '/refund-policy.html';
      return null;
    },
    meta: { requiresAuth: false }
  },
  {
    path: '/security',
    name: 'security',
    component: () => {
      window.location.href = '/security.html';
      return null;
    },
    meta: { requiresAuth: false }
  },
  {
    path: '/blog',
    name: 'blog',
    component: () => import('../views/landing/pages/BlogView.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/faq',
    name: 'faq',
    component: () => import('../views/landing/pages/FaqView.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/contact',
    name: 'contact',
    component: () => import('../views/landing/pages/ContactView.vue'),
    meta: { requiresAuth: false }
  },
  // Direct routes for common paths that should redirect to /app/* (both with and without trailing slash)
  {
    path: '/dashboard',
    redirect: '/app/dashboard'
  },
  {
    path: '/dashboard/',
    redirect: '/app/dashboard'
  },
  {
    path: '/usage',
    redirect: '/app/usage'
  },
  {
    path: '/usage/',
    redirect: '/app/usage'
  },
  {
    path: '/api-keys',
    redirect: '/app/api-keys'
  },
  {
    path: '/api-keys/',
    redirect: '/app/api-keys'
  },
  {
    path: '/settings',
    redirect: '/app/settings'
  },
  {
    path: '/settings/',
    redirect: '/app/settings'
  },
  {
    path: '/payments',
    redirect: '/app/payments'
  },
  {
    path: '/payments/',
    redirect: '/app/payments'
  },
  {
    path: '/pricing',
    redirect: '/app/pricing'
  },
  {
    path: '/pricing/',
    redirect: '/app/pricing'
  },
  // 404 Not Found - must be last
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: () => import('../views/NotFoundView.vue'),
    meta: { requiresAuth: false }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior() {
    // Always scroll to top when navigating to a new route
    return { top: 0 }
  }
})

// Navigation guard for authentication
router.beforeEach(async (to, from, next) => {
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)

  // Skip auth check for public pages to improve initial loading performance
  if (!requiresAuth) {
    return next()
  }

  // Only perform auth check if the route requires authentication
  const { data } = await supabase.auth.getSession()
  const isAuthenticated = !!data.session

  if (requiresAuth && !isAuthenticated) {
    next({ name: 'login', query: { redirect: to.fullPath } })
  } else {
    next()
  }
})

export default router

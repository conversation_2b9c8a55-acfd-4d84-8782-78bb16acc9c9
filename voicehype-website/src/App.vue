<script setup lang="ts">
import { RouterView, useRoute } from 'vue-router'
import { onMounted, ref, computed, watch } from 'vue'
import { useAuthStore } from './stores/auth'
import { useSettingsStore } from './stores/settings'
import ToastContainer from './components/ui/ToastContainer.vue'

// Apply dark mode class immediately to prevent FOUC
const initialDarkMode = 
  window.localStorage.getItem('dark-mode') === 'true' || 
  (!window.localStorage.getItem('dark-mode') && 
   window.matchMedia('(prefers-color-scheme: dark)').matches)

if (initialDarkMode) {
  document.documentElement.classList.add('dark')
} else {
  document.documentElement.classList.remove('dark')
}

// Set default background color immediately
document.body.className = 'bg-white dark:bg-[#0d1117]'

const authStore = useAuthStore()
const settingsStore = useSettingsStore()
const route = useRoute()
const bannerVisible = ref(false)
const isInitialized = ref(false)

// Determine if we are on the landing page or any of the public pages
const isPublicPage = computed(() => {
  const publicRoutes = ['landing', 'privacy-policy', 'terms-of-service', 'refund-policy', 'blog', 'faq', 'contact',]
  return publicRoutes.includes(route.name as string)
})

onMounted(async () => {
  if (isPublicPage.value) {
    // For public pages (landing page), initialize immediately without waiting
    document.documentElement.classList.add('dark')
    isInitialized.value = true
    
    // Banner animation can happen in background
    setTimeout(() => {
      bannerVisible.value = true
    }, 300)
  } else {
    // For app pages, wait for auth initialization
    try {
      await authStore.initialize()
      settingsStore.initDarkMode()
      
      // Add small delay before showing banner with animation
      setTimeout(() => {
        bannerVisible.value = true
      }, 300)
    } finally {
      isInitialized.value = true
    }
  }
})

// Watch for route changes to enforce dark mode on public pages without affecting stored preference
watch(() => route.name, () => {
  if (isPublicPage.value) {
    // Force dark mode on public pages without changing stored preference
    document.documentElement.classList.add('dark')
  } else {
    // Restore user preference when viewing dashboard
    settingsStore.initDarkMode()
  }
}, { immediate: true })
</script>

<template>
  <!-- Loading state -->
  <div v-if="!isInitialized" class="fixed inset-0 bg-[#0d1117] flex items-center justify-center z-50">
    <div class="loading-spinner"></div>
  </div>

  <!-- Main content - only show when initialized -->
  <template v-else>
    <!-- Palestine support banner - only show for non-landing pages -->
    <div
      v-if="isInitialized && !isPublicPage"
      class="banner-transition border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-[#0d1117] text-dark dark:text-white py-2 px-3 text-center flex items-center justify-center shadow-sm relative left-0 right-0 w-full z-50"
      :class="[
        { 'banner-visible': bannerVisible },
        'border-b'
      ]"
    >
      <!-- Beautiful Palestine flag with enhanced styling - smaller on mobile -->
      <span class="sm:mr-2 flex items-center justify-center mr-1">
        <img src="https://flagcdn.com/w80/ps.png" alt="Palestine Flag" class="sm:h-6 inline-block h-5 rounded-md" style="filter: drop-shadow(0 0 3px rgba(0,0,0,0.2));" />
      </span>
      <span class="sm:text-sm sm:mx-2 dark:text-white mx-1 text-xs font-semibold text-gray-900">Free Palestine</span>
      <a
        href="https://alkhidmat.org/appeal/emergency-appeal-palestine-save-lives-in-gaza-today?gad_source=1&gclid=Cj0KCQjws-S-BhD2ARIsALssG0YysRLKf4LQH9OBtIjCnjt2E9BWs1ugamrJozzPr39Aq8VT5Vc48z4aApwCEALw_wcB"
        target="_blank"
        rel="noopener noreferrer"
        class="hover:bg-gray-100 dark:bg-gray-800 dark:hover:bg-gray-900 sm:text-sm dark:text-gray-200 sm:px-3 inline-flex items-center px-2 py-1 text-xs font-bold text-teal-800 transition-colors bg-white rounded-md"
      >
        <span class="sm:inline hidden mr-1">Donate via</span>
        <span class="text-xs">Alkhidmat.org</span>
        <span class="arrow inline-block ml-1 transition-transform duration-200">→</span>
      </a>
    </div>
    
    <!-- RouterView without extra padding -->
    <div class="bg-white dark:bg-[#0d1117] min-h-screen">
      <Transition name="page" mode="out-in">
        <RouterView />
      </Transition>
    </div>
    
    <!-- Toast container for notifications -->
    <ToastContainer />
  </template>
</template>

<style>
/* Remove any scoped styles and let Tailwind handle everything */

/* Add default styles to ensure CSS is generated */
html, body {
  min-height: 100vh;
  width: 100%;
}

/* Add animation styles */
a:hover .arrow {
  transform: translateX(4px);
}

.arrow {
  transition: transform 0.2s ease;
}

/* Banner fade-in transition */
.banner-transition {
  opacity: 0;
  transform: translateY(-100%);
  transition: all 0.5s ease;
}

.banner-visible {
  opacity: 1;
  transform: translateY(0);
}

/* Page transition animations */
.page-enter-active,
.page-leave-active {
  transition: opacity 0.35s ease, transform 0.35s ease;
}

.page-enter-from {
  opacity: 0;
  transform: translateX(10px);
}

.page-leave-to {
  opacity: 0;
  transform: translateX(-10px);
}

/* Loading spinner */
.loading-spinner {
  width: 48px;
  height: 48px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  border-top-color: #14F195;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>

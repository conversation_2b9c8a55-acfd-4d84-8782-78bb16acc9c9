<template>
  <div class="bg-gray-50 dark:bg-gray-900 sm:px-6 lg:px-8 flex items-center justify-center min-h-screen px-4 py-12">
    <div class="w-full max-w-md space-y-8">
      <!-- Header section -->
      <div>
        <h2 class="dark:text-white font-heading mt-6 text-3xl font-extrabold text-center text-gray-900">
          {{ isResetMode ? 'Reset your password' : 'Set new password' }}
        </h2>
        <p v-if="isResetMode" class="dark:text-gray-400 mt-2 text-sm text-center text-gray-600">
          Enter your email address and we'll send you a link to reset your password.
        </p>
      </div>
      
      <!-- Error alert -->
      <div v-if="error" class="relative px-4 py-3 text-red-700 bg-red-100 border border-red-400 rounded" role="alert">
        <span class="sm:inline block">{{ error }}</span>
      </div>
      
      <!-- Success alert -->
      <div v-if="success" class="relative px-4 py-3 text-green-700 bg-green-100 border border-green-400 rounded" role="alert">
        <span class="sm:inline block">{{ success }}</span>
        <div v-if="!isResetMode" class="mt-4">
          <router-link to="/login" class="text-primary-600 hover:text-primary-500 dark:text-primary-400 font-medium">
            Continue to login
          </router-link>
        </div>
      </div>

      <!-- Request Reset Password Form -->
      <form v-if="isResetMode && !success" class="mt-8 space-y-6" @submit.prevent="handleResetPassword">
        <div>
          <label for="email-address" class="sr-only">Email address</label>
          <input 
            id="email-address" 
            name="email" 
            type="email" 
            autocomplete="email" 
            required
            v-model="email"
            class="input rounded-md"
            placeholder="Email address" />
        </div>

        <div>
          <button 
            type="submit" 
            class="btn-primary group relative flex justify-center w-full px-4 py-2 text-sm font-medium border border-transparent rounded-md"
            :disabled="loading"
          >
            <span class="absolute inset-y-0 left-0 flex items-center pl-3">
              <EnvelopeIcon class="text-primary-500 group-hover:text-primary-400 w-5 h-5" aria-hidden="true" />
            </span>
            <span v-if="loading">Sending...</span>
            <span v-else>Send reset link</span>
          </button>
        </div>
      </form>

      <!-- Set New Password Form -->
      <form v-if="!isResetMode && !success" @submit.prevent="handleUpdatePassword" class="mt-8 space-y-6">
        <div>
          <label for="password" class="dark:text-gray-300 block text-sm font-medium text-gray-700">
            New Password
          </label>
          <input
            id="password"
            type="password"
            v-model="password"
            required
            class="input block w-full mt-1 rounded-md"
            placeholder="Enter new password"
          />
        </div>

        <div>
          <label for="confirmPassword" class="dark:text-gray-300 block text-sm font-medium text-gray-700">
            Confirm Password
          </label>
          <input
            id="confirmPassword"
            type="password"
            v-model="confirmPassword"
            required
            class="input block w-full mt-1 rounded-md"
            placeholder="Confirm new password"
          />
        </div>

        <button
          type="submit"
          :disabled="loading"
          class="btn-primary flex justify-center w-full px-4 py-2 text-sm font-medium border border-transparent rounded-md"
        >
          {{ loading ? 'Updating...' : 'Update Password' }}
        </button>
      </form>
      
      <div class="mt-4 text-center">
        <router-link to="/login" class="text-primary-600 hover:text-primary-500 dark:text-primary-400 font-medium">
          Back to login
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { EnvelopeIcon } from '@heroicons/vue/24/outline'

const router = useRouter()
const authStore = useAuthStore()

// State
const email = ref('')
const password = ref('')
const confirmPassword = ref('')
const loading = ref(false)
const error = ref('')
const success = ref('')
const isResetMode = ref(true)

// Check URL hash for tokens on mount
onMounted(() => {
  const hashParams = new URLSearchParams(window.location.hash.substring(1))
  const accessToken = hashParams.get('access_token')
  const refreshToken = hashParams.get('refresh_token')

  if (accessToken && refreshToken) {
    isResetMode.value = false
    initializePasswordUpdate(accessToken, refreshToken)
  }
})

// Initialize password update with tokens
async function initializePasswordUpdate(accessToken: string, refreshToken: string) {
  try {
    const { data, error: verifyError } = await authStore.verifyRecoveryToken(refreshToken)

    if (verifyError) {
      throw verifyError
    }

  } catch (err: any) {
    console.error('Password reset verification error:', err)
    error.value = 'Invalid or expired reset link. Please request a new one.'
    isResetMode.value = true
  }
}

// Request password reset
async function handleResetPassword() {
  if (loading.value) return
  
  loading.value = true
  error.value = ''
  success.value = ''
  
  try {
    const result = await authStore.resetPassword(email.value)
    if (result) {
      success.value = `Password reset instructions have been sent to ${email.value}. Please check your email.`
    } else {
      error.value = authStore.error || 'Failed to send reset instructions. Please try again.'
    }
  } catch (err: any) {
    error.value = 'An unexpected error occurred. Please try again.'
    console.error('Password reset error:', err)
  } finally {
    loading.value = false
  }
}

// Update password
async function handleUpdatePassword() {
  if (loading.value) return
  if (password.value !== confirmPassword.value) {
    error.value = 'Passwords do not match'
    return
  }

  loading.value = true
  error.value = ''

  try {
    const result = await authStore.updatePassword(password.value)
    
    if (!result) {
      throw new Error(authStore.error || 'Failed to update password')
    }

    success.value = 'Password updated successfully!'
    setTimeout(() => {
      router.push('/login')
    }, 2000)
  } catch (err: any) {
    error.value = err.message || 'Failed to update password'
  } finally {
    loading.value = false
  }
}
</script>
<template>
  <div class="flex items-center justify-center min-h-screen p-4 bg-black">
    <div class="w-full max-w-md">
      <!-- Loading State -->
      <div v-if="isLoading" class="rounded-2xl p-8 text-center bg-gray-900 border border-gray-700">
        <div class="animate-spin w-12 h-12 mx-auto mb-4 border-b-2 border-white rounded-full"></div>
        <h2 class="mb-2 text-xl font-semibold text-white">Processing Authorization...</h2>
        <p class="text-white">Please wait while we set up your {{ productName }} integration.</p>
      </div>

      <!-- Not Signed In -->
      <div v-else-if="!isSignedIn" class="rounded-2xl p-8 bg-gray-900 border border-gray-700">
        <div class="mb-6 text-center">
          <div class="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-gray-800 rounded-full">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
            </svg>
          </div>
          <h1 class="mb-2 text-2xl font-bold text-white">Authorize {{ productName }} Extension</h1>
          <p class="mb-2 font-medium text-white">Voice to prompt productivity tool for developers</p>
          <p v-if="productName" class="mb-2 text-sm text-gray-400">For {{ productName }}</p>
          <p class="text-white">Please sign in to your VoiceHype account to authorize the extension.</p>
        </div>

        <div class="space-y-3">
          <button
            @click="signInWithGoogle"
            :disabled="signingIn === 'google'"
            class="hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center w-full px-4 py-3 text-sm font-medium text-gray-700 transition-colors bg-white border border-transparent rounded-lg"
          >
            <svg v-if="signingIn === 'google'" class="animate-spin w-4 h-4 mr-3 -ml-1 text-gray-700" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <img v-else src="https://developers.google.com/identity/images/g-logo.png" class="w-4 h-4 mr-3" alt="Google">
            Continue with Google
          </button>

          <button
            @click="signInWithGitHub"
            :disabled="signingIn === 'github'"
            class="hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center w-full px-4 py-3 text-sm font-medium text-white transition-colors bg-gray-800 border border-gray-700 rounded-lg"
          >
            <svg v-if="signingIn === 'github'" class="animate-spin w-4 h-4 mr-3 -ml-1 text-white" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <svg v-else class="w-4 h-4 mr-3 fill-current" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z" clip-rule="evenodd" />
            </svg>
            Continue with GitHub
          </button>

          <div class="relative my-4">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-gray-700"></div>
            </div>
            <div class="relative flex justify-center text-sm">
              <span class="px-2 font-medium text-white bg-black">or</span>
            </div>
          </div>

          <button
            @click="showEmailForm = true"
            v-if="!showEmailForm"
            class="hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white flex items-center justify-center w-full px-4 py-3 text-sm font-medium text-white transition-colors bg-gray-800 border border-gray-700 rounded-lg"
          >
            <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            Continue with Email
          </button>

          <!-- Email Form -->
          <div v-if="showEmailForm" class="space-y-3">
            <input
              v-model="email"
              type="email"
              placeholder="Enter your email"
              class="focus:outline-none focus:ring-2 focus:ring-white focus:border-transparent w-full px-4 py-3 text-white placeholder-gray-400 bg-gray-800 border border-gray-700 rounded-lg"
              @keyup.enter="signInWithEmail"
            >
            <input
              v-model="password"
              type="password"
              placeholder="Enter your password"
              class="focus:outline-none focus:ring-2 focus:ring-white focus:border-transparent w-full px-4 py-3 text-white placeholder-gray-400 bg-gray-800 border border-gray-700 rounded-lg"
              @keyup.enter="signInWithEmail"
            >
            <div class="flex space-x-3">
              <button
                @click="signInWithEmail"
                :disabled="signingIn === 'email' || !email || !password"
                class="hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center flex-1 px-4 py-3 text-sm font-medium text-gray-900 transition-colors bg-white border border-transparent rounded-lg"
              >
                <svg v-if="signingIn === 'email'" class="animate-spin w-4 h-4 mr-3 -ml-1 text-gray-900" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Sign In
              </button>
              <button
                @click="showEmailForm = false; email = ''; password = ''"
                class="hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white px-4 py-3 text-sm font-medium text-white transition-colors border border-gray-600 rounded-lg"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>

        <div v-if="error" class="p-4 mt-4 bg-red-900 border border-red-700 rounded-lg">
          <p class="text-sm font-medium text-white">{{ error }}</p>
        </div>
      </div>

      <!-- Signed In - Confirmation -->
      <div v-else-if="!isAuthorizing" class="rounded-2xl p-8 bg-gray-900 border border-gray-700">
        <div class="mb-6 text-center">
          <div class="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-green-900 rounded-full">
            <svg class="w-8 h-8 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h1 class="mb-2 text-2xl font-bold text-white">Authorize {{ productName }} Extension</h1>
          <p class="mb-4 font-medium text-white">Hello {{ user?.user_metadata?.full_name || user?.email }}!</p>
          <p class="text-white">The VoiceHype {{ productName }} extension is requesting access to your account. This will create a new API key for secure access.</p>
        </div>

        <div class="p-4 mb-6 bg-yellow-900 border border-yellow-700 rounded-lg">
          <div class="flex">
            <svg class="w-5 h-5 text-yellow-400 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <div>
              <h3 class="text-sm font-medium text-yellow-300">Security Notice</h3>
              <p class="mt-1 text-sm text-white">This will create a new API key that never expires. Any existing VS Code API keys will remain active.</p>
            </div>
          </div>
        </div>

        <div class="flex space-x-4">
          <button
            @click="authorizeExtension"
            class="hover:bg-green-800 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:ring-offset-transparent flex-1 px-4 py-3 font-medium text-white transition-colors bg-green-700 rounded-lg"
          >
            Authorize Extension
          </button>
          <button
            @click="denyAuthorization"
            class="hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:ring-offset-transparent flex-1 px-4 py-3 font-medium text-white transition-colors bg-red-700 rounded-lg"
          >
            Deny Access
          </button>
        </div>
      </div>

      <!-- Authorizing State -->
      <div v-else class="rounded-2xl p-8 text-center bg-gray-900 border border-gray-700">
        <div class="animate-spin w-12 h-12 mx-auto mb-4 border-b-2 border-green-400 rounded-full"></div>
        <h2 class="mb-2 text-xl font-semibold text-white">Creating API Key...</h2>
        <p class="text-white">Please wait while we authorize your {{ productName }} extension.</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { supabase } from '@/lib/supabase'
import { useAuthStore } from '@/stores/auth'
import type { User } from '@supabase/supabase-js'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// Reactive state
const isLoading = ref(true)
const isSignedIn = ref(false)
const isAuthorizing = ref(false)
const user = ref<User | null>(null)
const error = ref('')
const signingIn = ref('')
const showEmailForm = ref(false)
const email = ref('')
const password = ref('')
const productName = ref('')  // Add productName to reactive state

// URL parameters
const state = ref('')
const redirectUri = ref('')

onMounted(async () => {
  // Get URL parameters
  state.value = route.query.state as string || ''
  redirectUri.value = route.query.redirect_uri as string || ''
  productName.value = route.query.product_name as string || 'Visual Studio Code'  // Set product name from query params
  
  // Get URI scheme from query parameters
  const uriScheme = route.query.uri_scheme as string || 'vscode'
  
  console.log('Product name:', productName.value)
  console.log('URI scheme:', uriScheme)
  console.log('Redirect URI:', redirectUri.value)

  // Validate required parameters
  if (!state.value || !redirectUri.value) {
    error.value = 'Missing required parameters. Please restart the authorization process from VS Code.'
    isLoading.value = false
    return
  }

  // Check if user is already signed in
  const { data: { session } } = await supabase.auth.getSession()
  
  if (session?.user) {
    user.value = session.user
    isSignedIn.value = true
  }
  
  isLoading.value = false
})

async function signInWithGoogle() {
  signingIn.value = 'google'
  error.value = ''

  try {
    await authStore.signInWithOAuth('google')
  } catch (err) {
    const message = err instanceof Error ? err.message : 'Failed to sign in with Google'
    error.value = message
    signingIn.value = ''
  }
}

async function signInWithGitHub() {
  signingIn.value = 'github'
  error.value = ''

  try {
    await authStore.signInWithOAuth('github')
  } catch (err) {
    const message = err instanceof Error ? err.message : 'Failed to sign in with GitHub'
    error.value = message
    signingIn.value = ''
  }
}

async function signInWithEmail() {
  if (!email.value || !password.value) {
    error.value = 'Please enter both email and password'
    return
  }
  
  signingIn.value = 'email'
  error.value = ''
  
  try {
    const { data, error: signInError } = await supabase.auth.signInWithPassword({
      email: email.value,
      password: password.value
    })
    
    if (signInError) {
      throw signInError
    }
    
    if (data.user) {
      user.value = data.user
      isSignedIn.value = true
      showEmailForm.value = false
      email.value = ''
      password.value = ''
    }
  } catch (err) {
    const message = err instanceof Error ? err.message : 'Failed to sign in'
    error.value = message
  } finally {
    signingIn.value = ''
  }
}

async function authorizeExtension() {
  isAuthorizing.value = true
  error.value = ''
  
  try {
    if (!user.value) {
      throw new Error('User not authenticated')
    }

    // Get the current Supabase session with all token information
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError) {
      throw sessionError
    }
    
    if (!session) {
      throw new Error('No active session found')
    }

    // Store the product name before using it in the template
    const currentProductName = productName.value
    
    // Generate a friendly name for the VS Code extension key
    const name = `${currentProductName} Extension (${new Date().toISOString().split('T')[0]})`
    
    // Call the create_api_key RPC function directly - create never expiring key for VS Code extension
    const { data: apiKeyData, error: apiKeyError } = await supabase.rpc('create_api_key', {
      p_name: name,
      p_expires_at: null // null means never expires
    })
    
    if (apiKeyError) {
      throw apiKeyError
    }
    
    if (!apiKeyData || !apiKeyData.id || !apiKeyData.key_secret) {
      throw new Error('Invalid API key response')
    }
    
    // Add the vhkey_ prefix to the generated key
    const fullApiKey = `vhkey_${apiKeyData.key_secret}`
    
    // Get user metadata for the redirect
    const fullName = user.value.user_metadata?.full_name
    const avatarUrl = user.value.user_metadata?.avatar_url
    
    // Get URI scheme from URL parameters or default to 'vscode'
    const uriScheme = route.query.uri_scheme as string || 'vscode'
    
    // Validate and build the redirect URL with the correct URI scheme
    let redirectUrl: URL
    try {
      // First decode the URI if it's encoded
      const decodedUri = decodeURIComponent(redirectUri.value)
      console.log('Decoded redirect URI:', decodedUri)
      
      // Check if we need to modify the URI scheme in the redirect URL
      if (decodedUri.startsWith('vscode://') && uriScheme !== 'vscode') {
        const modifiedUri = decodedUri.replace('vscode://', `${uriScheme}://`)
        console.log(`Modified redirect URI: ${modifiedUri} (original: ${decodedUri})`)
        redirectUrl = new URL(modifiedUri)
      } else {
        redirectUrl = new URL(decodedUri)
      }
      
      // Validate the redirect URL - less strict for vscode:// URLs
      if (!redirectUrl.protocol) {
        throw new Error('Invalid redirect URL format - missing protocol')
      }
      
      // For standard http/https URLs, validate host
      if ((redirectUrl.protocol === 'http:' || redirectUrl.protocol === 'https:') && !redirectUrl.host) {
        throw new Error('Invalid web redirect URL - missing host')
      }
    } catch (err) {
      console.error('Failed to construct redirect URL:', err)
      error.value = 'Invalid redirect configuration. Please contact support.'
      isAuthorizing.value = false
      return
    }
    
    // Add the parameters for the VS Code extension (existing ones)
    redirectUrl.searchParams.append('state', state.value)
    redirectUrl.searchParams.append('api_key', fullApiKey)
    redirectUrl.searchParams.append('user_id', user.value.id)
    redirectUrl.searchParams.append('email', user.value.email || '')
    
    if (fullName) {
      redirectUrl.searchParams.append('full_name', fullName)
    }
    
    if (avatarUrl) {
      redirectUrl.searchParams.append('avatar_url', avatarUrl)
    }

    // *** NEW: Add Supabase Session Data ***
    // Add the session tokens for full Supabase authentication
    redirectUrl.searchParams.append('access_token', session.access_token)
    redirectUrl.searchParams.append('refresh_token', session.refresh_token)
    redirectUrl.searchParams.append('token_type', session.token_type || 'bearer')
    redirectUrl.searchParams.append('expires_at', session.expires_at?.toString() || '')
    redirectUrl.searchParams.append('expires_in', session.expires_in?.toString() || '')
    
    // Add provider information if available
    if (session.provider_token) {
      redirectUrl.searchParams.append('provider_token', session.provider_token)
    }
    
    if (session.provider_refresh_token) {
      redirectUrl.searchParams.append('provider_refresh_token', session.provider_refresh_token)
    }

    // Create the HTML template for the success page
    const successPage = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>VoiceHype - Authorization Complete</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            margin: 0;
            background: #000000;
            color: white;
            text-align: center;
          }
          .container {
            background: #1a1a1a;
            padding: 3rem;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid #333333;
            max-width: 500px;
          }
          h1 {
            margin-bottom: 1rem;
            font-size: 2rem;
            font-weight: 700;
          }
          p {
            margin-bottom: 2rem;
            line-height: 1.6;
            font-weight: 500;
          }
          .btn {
            display: inline-block;
            background: #2d2d2d;
            color: white;
            border: 2px solid #444444;
            padding: 0.75rem 2rem;
            font-size: 1rem;
            font-weight: 600;
            border-radius: 8px;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s ease;
          }
          .btn:hover {
            background: #444444;
            transform: translateY(-2px);
          }
          .logo {
            width: 80px;
            height: 80px;
            margin-bottom: 1.5rem;
            border-radius: 50%;
            background: #2d2d2d;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin: 0 auto 1.5rem;
            border: 1px solid #444444;
          }
          .spinner {
            border: 3px solid #333333;
            border-radius: 50%;
            border-top: 3px solid white;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
          }
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }        </style>
      </head>
      <body>
        <div class="container">
          <div class="logo">🎤</div>
          <h1>Authorization Successful!</h1>
          <p>You have successfully authorized VoiceHype for ${currentProductName}. You will be redirected back to ${currentProductName} automatically, Inshaa Allah.</p>
          <div class="spinner"></div>
          <p style="font-size: 0.9rem;">If you're not redirected automatically, click the button below:</p>
          <a href="${redirectUrl.toString()}" class="btn">Open ${currentProductName}</a>
        </div>
        <${"script"}>
          // Automatically redirect after 500 milliseconds
          setTimeout(function() {
            window.location.href = "${redirectUrl.toString()}";
          }, 500);
        </${"script"}>
      </body>
      </html>
    `;

    // Debug logging
    console.log('Generated success page HTML:', successPage)
    console.log('Redirect URL:', redirectUrl.toString())
    
    try {
      // Write the HTML to the current page to show the success message and redirect
      document.open()
      document.write(successPage)
      document.close()
      console.log('Successfully wrote HTML to document')
    } catch (err) {
      console.error('Failed to write HTML to document:', err)
      error.value = 'Failed to complete authorization. Please try again.'
      isAuthorizing.value = false
    }
    
  } catch (err) {
    const message = err instanceof Error ? err.message : 'Failed to authorize extension'
    error.value = message
    isAuthorizing.value = false
  } finally {
    isAuthorizing.value = false
  }
}

function denyAuthorization() {
  // Get URI scheme from URL parameters or default to 'vscode'
  const uriScheme = route.query.uri_scheme as string || 'vscode'
  
  try {
    // Decode the redirect URI first
    const decodedUri = decodeURIComponent(redirectUri.value)
    console.log('Decoded redirect URI for deny:', decodedUri)
    
    // Create URL object
    let redirectUrl: URL
    if (decodedUri.startsWith('vscode://') && uriScheme !== 'vscode') {
      const modifiedUri = decodedUri.replace('vscode://', `${uriScheme}://`)
      console.log(`Modified redirect URI for deny: ${modifiedUri}`)
      redirectUrl = new URL(modifiedUri)
    } else {
      redirectUrl = new URL(decodedUri)
    }
    
    // Add error parameters
    redirectUrl.searchParams.append('state', state.value)
    redirectUrl.searchParams.append('error', 'access_denied')
    redirectUrl.searchParams.append('error_description', 'User denied authorization')
    
    window.location.href = redirectUrl.toString()
  } catch (err) {
    console.error('Failed to process redirect URI:', err)
    error.value = 'Failed to deny authorization. Please try again.'
  }
}
</script>

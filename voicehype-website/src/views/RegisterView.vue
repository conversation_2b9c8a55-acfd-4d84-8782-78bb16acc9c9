<template>
  <div class="bg-gray-50 dark:bg-gray-900 sm:px-6 lg:px-8 flex items-center justify-center min-h-screen px-4 py-12">
    <div class="w-full max-w-md space-y-8">
      <div>
        <h2 class="dark:text-white font-heading mt-6 text-3xl font-extrabold text-center text-gray-900">
          Create your account
        </h2>
        <p class="dark:text-gray-400 mt-2 text-sm text-center text-gray-600">
          Or
          <router-link to="/login" class="text-primary-600 hover:text-primary-500 dark:text-primary-400 font-medium">
            sign in to your existing account
          </router-link>
        </p>
      </div>

      <div v-if="error" class="relative px-4 py-3 text-red-700 bg-red-100 border border-red-400 rounded" role="alert">
        <span class="sm:inline block">{{ error }}</span>
        <div v-if="error.includes('database') || error.includes('issue creating')" class="mt-2">
          <button
            @click="handleRegister"
            class="hover:text-red-800 text-red-700 underline"
            :disabled="loading"
          >
            Try again
          </button>
        </div>
      </div>

      <div v-if="verificationSent" class="relative px-4 py-3 text-green-700 bg-green-100 border border-green-400 rounded" role="alert">
        <span class="sm:inline block">{{ verificationMessage }}</span>
        <p class="mt-2">Please check your email for a verification link to complete your registration.</p>
        <p class="mt-2 text-sm">
          If you don't see the email in your inbox, please check your spam folder.
          <br>The email will come from <strong><EMAIL></strong>
        </p>
      </div>

      <div v-if="!verificationSent" class="mt-8 space-y-6">
        <!-- OAuth Buttons -->
        <div class="space-y-3">
          <OAuthButton
            provider="google"
            :loading="googleLoading"
            @click="handleGoogleSignIn"
            buttonText="Sign up with Google"
          />
          <OAuthButton
            provider="github"
            :loading="githubLoading"
            @click="handleGitHubSignIn"
            buttonText="Sign up with GitHub"
          />

          <div class="relative my-4">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-gray-300 dark:border-gray-700"></div>
            </div>
            <div class="relative flex justify-center text-sm">
              <span class="px-2 text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-900">
                Or sign up with email
              </span>
            </div>
          </div>
        </div>

        <!-- Email/Password Form -->
        <form class="space-y-6" @submit.prevent="handleRegister">
          <div class="space-y-4">
            <div>
              <label for="full-name" class="dark:text-gray-300 block mb-1 text-sm font-medium text-gray-700">Full name</label>
              <input
                id="full-name"
                name="name"
                type="text"
                required
                v-model="fullName"
                class="input w-full rounded-md"
                placeholder="Your full name"
              >
            </div>
            <div>
              <label for="email-address" class="dark:text-gray-300 block mb-1 text-sm font-medium text-gray-700">Email address</label>
              <input
                id="email-address"
                name="email"
                type="email"
                autocomplete="email"
                required
                v-model="email"
                class="input w-full rounded-md"
                placeholder="<EMAIL>"
              >
            </div>
            <div>
              <label for="password" class="dark:text-gray-300 block mb-1 text-sm font-medium text-gray-700">Password</label>
              <input
                id="password"
                name="password"
                type="password"
                autocomplete="new-password"
                required
                v-model="password"
                class="input w-full rounded-md"
                placeholder="Create a strong password"
              >
              <p class="dark:text-gray-400 mt-1 text-xs text-gray-500">
                Password must be at least 6 characters long
              </p>
            </div>
          </div>

          <div>
            <button
              type="submit"
              class="btn-primary group relative flex justify-center w-full px-4 py-2 text-sm font-medium border border-transparent rounded-md"
              :disabled="loading"
            >
              <span class="absolute inset-y-0 left-0 flex items-center pl-3">
                <UserPlusIcon class="text-primary-500 group-hover:text-primary-400 w-5 h-5" aria-hidden="true" />
              </span>
              <span v-if="loading">
                <svg class="animate-spin inline-block w-4 h-4 mr-2 -ml-1 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Creating account...
              </span>
              <span v-else>Create account</span>
            </button>
          </div>
        </form>
      </div>

      <!-- OTP Verification Form (if needed in the future) -->
      <form v-if="showOtpForm" class="mt-8 space-y-6" @submit.prevent="handleVerifyOtp">
        <div>
          <label for="otp-code" class="dark:text-gray-300 block mb-1 text-sm font-medium text-gray-700">
            Verification Code
          </label>
          <input
            id="otp-code"
            name="otp"
            type="text"
            required
            v-model="otpCode"
            class="input w-full rounded-md"
            placeholder="Enter the code from your email"
          >
        </div>

        <div>
          <button
            type="submit"
            class="btn-primary group relative flex justify-center w-full px-4 py-2 text-sm font-medium border border-transparent rounded-md"
            :disabled="verifyLoading"
          >
            <span v-if="verifyLoading">
              <svg class="animate-spin inline-block w-4 h-4 mr-2 -ml-1 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Verifying...
            </span>
            <span v-else>Verify Email</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { UserPlusIcon } from '@heroicons/vue/24/outline'
import OAuthButton from '@/components/auth/OAuthButton.vue'

// Store
const authStore = useAuthStore()
const router = useRouter()

// State
const fullName = ref('')
const email = ref('')
const password = ref('')
const loading = ref(false)
const error = ref('')
const verificationSent = ref(false)
const verificationMessage = ref('')

// OAuth loading states
const googleLoading = ref(false)
const githubLoading = ref(false)

// OTP verification (if needed in the future)
const showOtpForm = ref(false)
const otpCode = ref('')
const verifyLoading = ref(false)

// Methods for OAuth sign-up
async function handleGoogleSignIn() {
  googleLoading.value = true
  error.value = ''

  try {
    const result = await authStore.signInWithGoogle()

    if (result && result.url) {
      // Redirect to the OAuth provider
      window.location.href = result.url
    } else {
      error.value = authStore.error || 'Failed to initiate Google sign-up. Please try again.'
    }
  } catch (err) {
    console.error('Google sign-up error:', err)
    error.value = 'An unexpected error occurred during Google sign-up.'
  } finally {
    googleLoading.value = false
  }
}

async function handleGitHubSignIn() {
  githubLoading.value = true
  error.value = ''

  try {
    const result = await authStore.signInWithGitHub()

    if (result && result.url) {
      // Redirect to the OAuth provider
      window.location.href = result.url
    } else {
      error.value = authStore.error || 'Failed to initiate GitHub sign-up. Please try again.'
    }
  } catch (err) {
    console.error('GitHub sign-up error:', err)
    error.value = 'An unexpected error occurred during GitHub sign-up.'
  } finally {
    githubLoading.value = false
  }
}

// Method for email/password registration
async function handleRegister() {
  loading.value = true
  error.value = ''

  try {
    const result = await authStore.register(email.value, password.value, fullName.value)

    if (result === false) {
      error.value = authStore.error || 'Failed to create account. Please try again.'
    } else if (typeof result === 'object' && result.success) {
      // Email verification required
      verificationSent.value = true
      verificationMessage.value = result.message || 'Your account has been created. Please check your email to verify your account.'
    } else {
      // Registration successful and user is already confirmed
      router.push('/')
    }
  } catch (err: any) {
    console.error('Registration error:', err)
    error.value = err?.message || 'An unexpected error occurred. Please try again.'
  } finally {
    loading.value = false
  }
}

async function handleVerifyOtp() {
  if (!otpCode.value) {
    error.value = 'Please enter the verification code'
    return
  }

  verifyLoading.value = true
  error.value = ''

  try {
    const success = await authStore.verifyOtp(email.value, otpCode.value)

    if (success) {
      router.push('/')
    } else {
      error.value = authStore.error || 'Failed to verify code. Please try again.'
    }
  } catch (err: any) {
    console.error('Verification error:', err)
    error.value = err?.message || 'An unexpected error occurred. Please try again.'
  } finally {
    verifyLoading.value = false
  }
}
</script>
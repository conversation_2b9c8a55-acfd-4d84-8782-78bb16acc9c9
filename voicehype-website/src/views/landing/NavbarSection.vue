<template>
  <nav class="navbar">
    <div class="nav-container lp-container lp-flex lp-justify-between lp-items-center">
      <div class="nav-logo lp-flex lp-items-center">
        <div class="logo-container">
          <svg width="40" height="30" viewBox="0 0 40 30" fill="none" xmlns="http://www.w3.org/2000/svg">
            <!-- Monitor outline with white fill -->
            <rect x="2" y="2" width="36" height="20" rx="2" fill="white"/>
            <rect x="2" y="2" width="36" height="20" rx="2" stroke="#14F195" stroke-width="1.5"/>
            <!-- Sound wave visualization -->
            <path d="M7 14 L10 9 L13 14 L16 9 L19 14 L22 9 L25 14 L28 9 L31 14 L34 9" stroke="#14F195" stroke-width="1.5" stroke-linecap="round"/>
            <!-- Stand -->
            <line x1="20" y1="22" x2="20" y2="26" stroke="#14F195" stroke-width="1.5"/>
            <line x1="14" y1="26" x2="26" y2="26" stroke="#14F195" stroke-width="1.5"/>
          </svg>
        </div>
        <h1 class="logo-text lp-font-display lp-font-bold">Voice<span class="hype-text lp-text-primary">Hype</span></h1>
      </div>
      <div class="nav-center lp-flex-1 lp-flex lp-justify-center lp-items-center">
        <ul class="nav-links lp-flex lp-gap-6" :class="{ 'active': mobileMenuOpen }">
          <li>
            <a href="#features" class="nav-link hover:lp-text-primary lp-transition-all lp-duration-300 lp-ease-in-out lp-px-4 lp-py-2 lp-rounded-lg font-semibold" @click="scrollToSection">
              Features
            </a>
          </li>
          <li>
            <a href="#use-cases" class="nav-link hover:lp-text-primary lp-transition-all lp-duration-300 lp-ease-in-out lp-px-4 lp-py-2 lp-rounded-lg font-semibold" @click="scrollToSection">
              Use Cases
            </a>
          </li>
          <li>
            <a href="#faq" class="nav-link hover:lp-text-primary lp-transition-all lp-duration-300 lp-ease-in-out lp-px-4 lp-py-2 lp-rounded-lg font-semibold" @click="scrollToSection">
              FAQ
            </a>
          </li>
          <li>
            <a href="#blog-section" class="nav-link hover:lp-text-primary lp-transition-all lp-duration-300 lp-ease-in-out lp-px-4 lp-py-2 lp-rounded-lg font-semibold" @click="scrollToSection">
              Blog
            </a>
          </li>
        </ul>
      </div>
      <div class="nav-buttons lp-flex lp-items-center">
        <a href="#" class="nav-button login-button lp-btn lp-btn-outline" @click="navigateTo('/login')">Log In</a>
        <a href="#" class="nav-button signup-button lp-btn lp-btn-primary" @click="navigateTo('/register')">Sign Up</a>
      </div>
      <button class="mobile-menu-toggle" @click="toggleMobileMenu">
        <span class="bar"></span>
        <span class="bar"></span>
        <span class="bar"></span>
      </button>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const mobileMenuOpen = ref(false);

// Force dark mode whenever this component is mounted
// without changing the user's stored preference for dashboard
onMounted(() => {
  // Only apply dark class to the DOM, don't change localStorage
  document.documentElement.classList.add('dark');
});

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value;
};

const scrollToSection = (e: MouseEvent) => {
  e.preventDefault();
  const targetId = (e.target as HTMLElement).getAttribute('href');
  
  if (targetId) {
    const targetElement = document.querySelector(targetId);
    
    if (targetElement) {
      targetElement.scrollIntoView({ behavior: 'smooth' });
      mobileMenuOpen.value = false;
    }
  }
};

const navigateTo = (route: string) => {
  router.push(route);
  window.scrollTo(0, 0);
};
</script>

<style scoped>
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: var(--z-header);
  background-color: rgba(8, 8, 20, 0.85);
  backdrop-filter: blur(10px);
  padding: 1rem 0;
  transition: all var(--transition-base);
}

.logo-text {
  color: white;
  margin-left: 0.5rem;
  font-size: 1.25rem;
}

.nav-center {
  display: none;
}

.nav-links li {
  margin: 0;
}

.nav-link {
  color: var(--color-text-muted);
  text-decoration: none;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all var(--transition-base);
}

.nav-link:hover {
  color: var(--color-primary);
  background: rgba(255, 255, 255, 0.05);
}

/* Desktop styles */
@media (min-width: 769px) {
  .nav-center {
    display: flex;
  }
  
  .mobile-menu-toggle {
    display: none;
  }
  
  .nav-links {
    position: static;
    width: auto;
    height: auto;
    flex-direction: row;
    background: transparent;
    padding: 0;
  }
  
  .nav-links li {
    margin: 0;
  }
}

.nav-buttons .nav-button {
  margin-left: 0.75rem;
}

.login-button {
  color: white !important;
  background: transparent !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.login-button:hover {
  background: rgba(255, 255, 255, 0.1) !important;
}

.signup-button {
  color: #111827 !important;
}

.mobile-menu-toggle {
  display: none;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
}

.bar {
  display: block;
  width: 25px;
  height: 3px;
  margin: 5px auto;
  background-color: white;
  transition: all var(--transition-base);
}

/* Responsive styles */
@media (max-width: 768px) {
  .nav-links {
    position: fixed;
    top: 60px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 60px);
    flex-direction: column;
    background-color: rgba(8, 8, 20, 0.95);
    padding: 2rem 0;
    transition: left var(--transition-base);
    justify-content: flex-start;
    align-items: center;
  }
  
  .nav-links.active {
    left: 0;
  }
  
  .nav-links li {
    margin: 1.5rem 0;
  }
  
  .nav-link {
    font-size: 1.1rem;
  }
  
  .mobile-menu-toggle {
    display: block;
  }
  
  .nav-buttons {
    display: none;
  }
  
  .nav-links.active ~ .nav-buttons {
    display: flex;
    flex-direction: column;
    position: fixed;
    top: 50%;
    left: 0;
    width: 100%;
    align-items: center;
  }
  
  .nav-links.active ~ .nav-buttons .nav-button {
    margin: 0.5rem 0;
    width: 80%;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .logo-text {
    font-size: 1rem;
  }
  
  .logo-container svg {
    width: 30px;
    height: 22px;
  }
}
</style>

<template>
  <section class="hero-section lp-py-xl">
    <div class="lp-container">
      <div class="hero-content lp-grid lp-grid-cols-1 lp-grid-cols-lg-2 lp-items-center">
        <div class="hero-text lp-text-left lp-text-sm-center">
          <h1 class="hero-title lp-font-display lp-font-bold lp-mb-md">
            Transform Your Application with
            <span class="lp-gradient-text">Blazing Fast</span>
            Voice Cloning
          </h1>
          <p class="hero-subtitle lp-text-muted lp-mb-lg">
            Generate high-quality speech in any voice in milliseconds.
            VoiceHype provides enterprise-grade voice cloning APIs built for scale.
          </p>
          <div class="hero-cta lp-flex lp-flex-sm-col lp-items-center lp-justify-start lp-justify-sm-center">
            <button class="lp-btn lp-btn-primary lp-mb-sm lp-mb-md-0 lp-mr-md" @click="navigateTo('/register')">
              Get Started Free
            </button>
            <a href="#demo-section" class="lp-btn lp-btn-outline" @click.prevent="scrollToDemo">
              See Demo
            </a>
          </div>
          <div class="hero-metrics lp-grid lp-grid-cols-2 lp-grid-cols-md-4 lp-mt-xl lp-text-center">
            <div class="metric-item">
              <div class="metric-number lp-font-bold lp-text-primary lp-mb-xs">10ms</div>
              <div class="metric-label lp-text-muted">Latency</div>
            </div>
            <div class="metric-item">
              <div class="metric-number lp-font-bold lp-text-primary lp-mb-xs">99.9%</div>
              <div class="metric-label lp-text-muted">Uptime</div>
            </div>
            <div class="metric-item">
              <div class="metric-number lp-font-bold lp-text-primary lp-mb-xs">100+</div>
              <div class="metric-label lp-text-muted">Languages</div>
            </div>
            <div class="metric-item">
              <div class="metric-number lp-font-bold lp-text-primary lp-mb-xs">1000+</div>
              <div class="metric-label lp-text-muted">Happy Customers</div>
            </div>
          </div>
        </div>
        
        <div class="hero-visualization lp-mt-xl lp-mt-lg-0">
          <div class="code-preview lp-card" ref="codePreviewRef">
            <div class="code-header lp-flex lp-justify-between lp-items-center lp-mb-sm">
              <div class="code-title lp-font-mono">Voice Cloning API</div>
              <div class="window-controls lp-flex">
                <div class="window-button"></div>
                <div class="window-button"></div>
                <div class="window-button"></div>
              </div>
            </div>
            <pre class="code-block lp-font-mono"><code>import voicehype

api = voicehype.VoiceCloner(api_key="...")

# Clone a voice from an audio file
voice_id = api.clone_voice(
  audio_file="sample.mp3",
  name="Custom Voice"
)

# Generate speech with the cloned voice
audio = api.text_to_speech(
  text="Hello world!",
  voice_id=voice_id
)

# Save the generated audio
audio.save("output.mp3")</code></pre>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const codePreviewRef = ref(null);
const animationFrame = null;

const navigateTo = (route: string) => {
  router.push(route);
  window.scrollTo(0, 0);
};

const scrollToDemo = () => {
  const demoSection = document.querySelector('#demo-section');
  if (demoSection) {
    demoSection.scrollIntoView({ behavior: 'smooth' });
  } else {
    // If demo section doesn't exist yet, scroll to features as fallback
    const featuresSection = document.querySelector('#features-overview-section');
    if (featuresSection) {
      featuresSection.scrollIntoView({ behavior: 'smooth' });
    }
  }
};

// Simple wave animation effect for code preview
onMounted(() => {
});


onBeforeUnmount(() => {
  if (animationFrame) {
    cancelAnimationFrame(animationFrame);
  }
  
});
</script>

<style scoped>
.hero-section {
  overflow: hidden;
  position: relative;
  padding-top: 100px; /* Extra padding for the fixed navbar */
}

.hero-title {
  font-size: clamp(2rem, 5vw, 3.5rem);
  line-height: 1.2;
}

.hero-subtitle {
  font-size: clamp(1rem, 2vw, 1.25rem);
  line-height: 1.6;
  max-width: 600px;
}

.metric-number {
  font-size: 1.5rem;
}

.metric-label {
  font-size: 0.875rem;
}

.code-preview {
  background-color: var(--color-bg-lighter);
  border-radius: var(--border-radius-lg);
  transition: all var(--transition-base);
  transform: perspective(1000px) rotateY(-5deg) rotateX(2deg);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2), 
              0 0 15px rgba(20, 241, 149, 0.15);
}

.code-header {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.code-title {
  color: var(--color-text-muted);
  font-size: 0.875rem;
}

.window-controls {
  display: flex;
  gap: 6px;
}

.window-button {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
}

.window-button:first-child {
  background-color: rgba(255, 86, 86, 0.8);
}

.window-button:nth-child(2) {
  background-color: rgba(255, 193, 7, 0.8);
}

.window-button:nth-child(3) {
  background-color: rgba(20, 241, 149, 0.8);
}

.code-block {
  padding: 1.5rem;
  margin: 0;
  white-space: pre-wrap;
  color: var(--color-text);
  font-size: 0.9rem;
  line-height: 1.5;
  overflow: auto;
}

@media (max-width: 1024px) {
  .hero-content {
    gap: 2rem;
  }
  
  .code-preview {
    transform: none;
    margin: 0 auto;
    max-width: 90%;
  }
}

@media (max-width: 768px) {
  .hero-section {
    padding-top: 80px;
  }
  
  .hero-text {
    order: 1;
  }
  
  .hero-visualization {
    order: 0;
  }
  
  .code-preview {
    max-width: 100%;
  }
}
</style> 
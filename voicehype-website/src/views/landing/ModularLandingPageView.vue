<template>
  <div class="modular-landing-page">
    <NavbarSection />
    <HeroSection />
    <QuoteSection />

    <section class="platform-logos-section">
      <h2>Platform Logos Section</h2>
      <p>This section will be replaced with the PlatformLogosSection component</p>
    </section>

    <section class="intro-section">
      <h2>Intro Section</h2>
      <p>This section will be replaced with the IntroSection component</p>
    </section>

    <section class="speed-benefits-section">
      <h2>Speed Benefits Section</h2>
      <p>This section will be replaced with the SpeedBenefitsSection component</p>
    </section>

    <section class="features-section">
      <h2>Features Section</h2>
      <p>This section will be replaced with the FeaturesSection component</p>
    </section>

    <section class="use-cases-section">
      <h2>Use Cases Section</h2>
      <p>This section will be replaced with the UseCasesSection component</p>
    </section>

    <section class="blog-section">
      <h2>Blog Section</h2>
      <p>This section will be replaced with the BlogSection component</p>
    </section>

    <FaqSection />

    <section class="footer-section">
      <h2>Footer Section</h2>
      <p>This section will be replaced with the FooterSection component</p>
    </section>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onBeforeUnmount, ref } from 'vue'
import NavbarSection from '@/views/landing/NavbarSection.vue'
import HeroSection from '@/views/landing/HeroSection.vue'
import QuoteSection from '@/views/landing/QuoteSection.vue'
import FaqSection from '@/components/FaqSection.vue'

// Define a type for the window extension
declare global {
  interface Window {
    _landingPageResources?: {
      preconnectElements: HTMLLinkElement[];
      fontStyleElements: HTMLLinkElement[];
      addedStyles?: HTMLStyleElement[];
    }
  }
}

// Preconnect to Google Fonts
const preloadedLinks: HTMLLinkElement[] = [];

// Define an array to track added scripts
const addedScripts = ref<HTMLScriptElement[]>([]);
const addedStyles = ref<HTMLStyleElement[]>([]);

onMounted(() => {
  // Force dark mode for landing page without affecting dashboard preference
  document.documentElement.classList.add('dark')
  
  // Preload CSS files
  preloadStylesheet('/src/assets/main.css');
  preloadStylesheet('/src/assets/styles/landing-page.css');
  preloadStylesheet('/landing-page/fonts.css');
  preloadStylesheet('/landing-page/pricing-section.css');
  preloadStylesheet('/landing-page/faq-section.css');
  preloadStylesheet('/landing-page/how-to-use-section.css');
  
  // Preload critical font files
  const preloadFontFile = (href: string) => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = href;
    link.as = 'font';
    link.type = 'font/woff2';
    link.crossOrigin = 'anonymous';
    document.head.appendChild(link);
    preloadedLinks.push(link);
    return link;
  };
  
  // Preload the most commonly used font weights
  preloadFontFile('/landing-page/fonts/clash-display/ClashDisplay-Regular.woff2');
  preloadFontFile('/landing-page/fonts/clash-display/ClashDisplay-Bold.woff2');
  preloadFontFile('/landing-page/fonts/clash-display/ClashDisplay-Semibold.woff2');
  
  // Add preconnect links
  const preconnectLinks = [
    { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
    { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: true }
  ]
  
  const preconnectElements: HTMLLinkElement[] = []
  
  preconnectLinks.forEach(link => {
    const linkEl = document.createElement('link')
    for (const [key, value] of Object.entries(link)) {
      linkEl.setAttribute(key, value as string)
    }
    document.head.appendChild(linkEl)
    preconnectElements.push(linkEl)
  })
  
  // Add font stylesheets
  const fontStyles = [
    'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap',
    'https://fonts.googleapis.com/css2?family=Manrope:wght@400;500;600;700&display=swap',
    'https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@400;500;600&display=swap',
    '/landing-page/fonts.css'
  ]
  
  const fontStyleElements = fontStyles.map(href => addStylesheet(href))
  
  // Add pricing-section.css stylesheet
  const pricingStylesheet = addStylesheet('/landing-page/pricing-section.css');
  fontStyleElements.push(pricingStylesheet);
  
  // Add FAQ section stylesheet
  const faqStylesheet = addStylesheet('/landing-page/faq-section.css');
  fontStyleElements.push(faqStylesheet);
  
  // Add how-to-use-section.css stylesheet
  const howToUseStylesheet = addStylesheet('/landing-page/how-to-use-section.css');
  fontStyleElements.push(howToUseStylesheet);
  
  // Set up pricing tab functionality directly
  setTimeout(() => {
    console.log('Setting up pricing tab functionality directly in ModularLandingPageView');
    const pricingTabs = document.querySelectorAll('.pricing-tab');
    const pricingTabContents = document.querySelectorAll('.pricing-tab-content');
    
    // Handle smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function(this: HTMLAnchorElement, e) {
        const href = this.getAttribute('href');
        if (href === '#' || !href) return;
        
        // Special handling for FAQ links
        if (href === "#faq" || this.classList.contains('scroll-to-faq')) {
          e.preventDefault();
          e.stopPropagation();
          
          const faqSection = document.querySelector('#faq');
          if (faqSection) {
            faqSection.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
            window.history.pushState(null, '', '#faq');
          }
          return false;
        }
        
        const targetElement = document.querySelector(href);
        if (targetElement) {
          e.preventDefault();
          targetElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
          
          // Push the hash to the URL for proper bookmarking
          window.history.pushState(null, '', href);
        }
      });
    });
    
    if (pricingTabs.length > 0) {
      console.log(`Found ${pricingTabs.length} pricing tabs`);
      
      // Make all pricing elements visible immediately
      document.querySelectorAll('.appear-on-scroll').forEach(element => {
        (element as HTMLElement).style.opacity = '1';
        (element as HTMLElement).style.transform = 'none';
      });
      
      // Make all table rows visible
      document.querySelectorAll('.pricing-table tr').forEach(row => {
        (row as HTMLElement).style.opacity = '1';
      });
      
      // Add tab click handlers
      pricingTabs.forEach(tab => {
        tab.addEventListener('click', function(this: HTMLElement) {
          console.log('Pricing tab clicked:', this.getAttribute('data-tab'));
          
          // Remove active class from all tabs
          pricingTabs.forEach(t => t.classList.remove('active'));
          
          // Add active class to clicked tab
          this.classList.add('active');
          
          // Hide all tab content
          pricingTabContents.forEach(content => {
            (content as HTMLElement).style.display = 'none';
            content.classList.remove('active');
          });
          
          // Show selected tab content
          const tabContentId = `${this.getAttribute('data-tab')}-pricing`;
          console.log('Looking for content with ID:', tabContentId);
          const selectedContent = document.getElementById(tabContentId);
          
          if (selectedContent) {
            (selectedContent as HTMLElement).style.display = 'block';
            selectedContent.classList.add('active');
            console.log('Tab content activated:', tabContentId);
          } else {
            console.error('Could not find tab content with ID:', tabContentId);
          }
        });
      });
      
      // Add override styles for animations
      const style = document.createElement('style');
      style.textContent = `
        .appear-on-scroll {
          opacity: 1 !important;
          transform: none !important;
          transition: none !important;
        }
        
        .pricing-table tr {
          opacity: 1 !important;
          transition: none !important;
        }
        
        .pricing-tab-content {
          transition: none !important;
        }
        
        .pricing-tab-content.active {
          opacity: 1 !important;
          transform: none !important;
          display: block !important;
        }
        
        .pricing-tab-content:not(.active) {
          display: none !important;
        }
      `;
      document.head.appendChild(style);
      addedStyles.value.push(style);
    } else {
      console.warn('No pricing tabs found on the page');
    }
  }, 500);
  
  // Store elements to be removed on unmount
  window._landingPageResources = {
    preconnectElements,
    fontStyleElements
  }

  // Load custom cards stylesheet
  const linkElement = document.createElement('link');
  linkElement.rel = 'stylesheet';
  linkElement.href = '/custom-cards.css';
  document.head.appendChild(linkElement);
  preloadedLinks.push(linkElement);
})

onBeforeUnmount(() => {
  // Clean up preloaded links
  preloadedLinks.forEach(link => {
    if (link.parentNode) {
      link.parentNode.removeChild(link);
    }
  });

  // Clean up added scripts
  addedScripts.value.forEach(script => {
    if (script.parentNode) {
      script.parentNode.removeChild(script);
    }
  });
  
  // Clean up added styles
  addedStyles.value.forEach(style => {
    if (style.parentNode) {
      style.parentNode.removeChild(style);
    }
  });

  if (window._landingPageResources) {
    // Remove preconnect links
    window._landingPageResources.preconnectElements.forEach((el: HTMLLinkElement) => {
      if (el && el.parentNode) {
        el.parentNode.removeChild(el)
      }
    })
    
    // Remove font stylesheets
    window._landingPageResources.fontStyleElements.forEach((el: HTMLLinkElement) => {
      if (el && el.parentNode) {
        el.parentNode.removeChild(el)
      }
    })
    
    // Clean up the resources object
    delete window._landingPageResources
  }
})

// Helper function to add stylesheet
function addStylesheet(href: string): HTMLLinkElement {
  const linkEl = document.createElement('link')
  linkEl.rel = 'stylesheet'
  linkEl.href = href
  document.head.appendChild(linkEl)
  return linkEl
}

// Function to preload stylesheet
function preloadStylesheet(href: string) {
  // Check if already preloaded
  const existingPreload = document.querySelector(`link[rel="preload"][href="${href}"]`);
  if (existingPreload) return;
  
  const link = document.createElement('link');
  link.rel = 'preload';
  link.href = href;
  link.as = 'style';
  
  document.head.appendChild(link);
  preloadedLinks.push(link);
}
</script>

<style>
@import '@/assets/styles/landing-page.css';

/* Additional page-specific styles */
.modular-landing-page section:not([class*="quote-section"]):not([class*="hero-section"]) {
  padding: 4rem 1rem;
  text-align: center;
  background-color: #111;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modular-landing-page h2 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: var(--color-primary);
  font-family: var(--font-display);
}

.modular-landing-page p {
  color: var(--color-text-muted);
  font-family: var(--font-body);
}

/* Control spacing between blog and FAQ sections */
.modular-landing-page .blog-section {
  padding-bottom: 0;
  margin-bottom: 0;
}

/* Override FaqSection margin and padding */
.modular-landing-page :deep(.faq-section) {
  padding-top: 0;
  margin-top: 0;
}
</style> 
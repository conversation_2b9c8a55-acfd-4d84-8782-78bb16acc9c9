<template>
  <section class="quote-section lp-py-xl lp-bg-lighter">
    <div class="lp-container">
      <div class="quote-content lp-grid lp-grid-cols-1 lp-items-center lp-text-center">
        <div class="quote-marks">
          <svg width="60" height="48" viewBox="0 0 60 48" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M14.0156 48C10.1719 48 7.01562 46.6875 4.54688 44.0625C1.51562 41 0 37.2188 0 32.7188C0 26.7812 2.34375 21.0625 7.03125 15.5625C11.7188 10.0625 17.9688 5.53125 25.7812 2L28.4062 7.5C24.0938 9.3125 20.4219 11.7188 17.3906 14.7188C14.3594 17.7188 12.8438 20.6875 12.8438 23.625C12.8438 24.625 13.1562 25.5469 13.7812 26.3906C14.4375 27.0469 15.5 27.5 17 27.75C20 28.25 22.375 29.3281 24.125 30.9844C25.875 32.6406 26.75 34.9375 26.75 37.875C26.75 40.875 25.8125 43.2969 23.9375 45.1406C22.0938 47.0469 19.4531 48 16.0156 48H14.0156ZM45.7656 48C41.9219 48 38.7656 46.6875 36.2969 44.0625C33.2656 41 31.75 37.2188 31.75 32.7188C31.75 26.7812 34.0938 21.0625 38.7812 15.5625C43.4688 10.0625 49.7188 5.53125 57.5312 2L60.1562 7.5C55.8438 9.3125 52.1719 11.7188 49.1406 14.7188C46.1094 17.7188 44.5938 20.6875 44.5938 23.625C44.5938 24.625 44.9062 25.5469 45.5312 26.3906C46.1875 27.0469 47.25 27.5 48.75 27.75C51.75 28.25 54.125 29.3281 55.875 30.9844C57.625 32.6406 58.5 34.9375 58.5 37.875C58.5 40.875 57.5625 43.2969 55.6875 45.1406C53.8438 47.0469 51.2031 48 47.7656 48H45.7656Z" fill="url(#paint0_linear_124_429)"/>
            <defs>
              <linearGradient id="paint0_linear_124_429" x1="0" y1="25" x2="60.1562" y2="25" gradientUnits="userSpaceOnUse">
                <stop stop-color="#14F195"/>
                <stop offset="1" stop-color="#0AD6DF"/>
              </linearGradient>
            </defs>
          </svg>
        </div>
        
        <blockquote class="quote-text lp-font-display lp-font-medium lp-my-lg">
          VoiceHype has been a game-changer for our platform. The voice cloning quality and API performance 
          far exceeded our expectations, and our users love the realistic voices we can now generate.
        </blockquote>
        
        <div class="quote-author lp-mt-md">
          <div class="author-image">
            <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="CEO Portrait" class="author-portrait">
          </div>
          <div class="author-info lp-mt-sm">
            <div class="author-name lp-font-semibold">Michael Thompson</div>
            <div class="author-role lp-text-muted">CEO at AudioStream</div>
          </div>
        </div>
        
        <div class="company-logos lp-grid lp-grid-cols-2 lp-grid-cols-md-4 lp-grid-cols-lg-5 lp-mt-xl">
          <div class="logo-item lp-flex lp-justify-center lp-items-center lp-py-md">
            <div class="company-logo">TechCorp</div>
          </div>
          <div class="logo-item lp-flex lp-justify-center lp-items-center lp-py-md">
            <div class="company-logo">AudioWave</div>
          </div>
          <div class="logo-item lp-flex lp-justify-center lp-items-center lp-py-md">
            <div class="company-logo">VoxMedia</div>
          </div>
          <div class="logo-item lp-flex lp-justify-center lp-items-center lp-py-md">
            <div class="company-logo">SpeakEasy</div>
          </div>
          <div class="logo-item lp-flex lp-justify-center lp-items-center lp-py-md">
            <div class="company-logo">EchoTech</div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
// Add any necessary functionality here
</script>

<style scoped>
.quote-section {
  position: relative;
  overflow: hidden;
}

.quote-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 15% 50%, rgba(20, 241, 149, 0.08) 0%, transparent 60%),
              radial-gradient(circle at 85% 30%, rgba(10, 214, 223, 0.08) 0%, transparent 60%);
  pointer-events: none;
}

.quote-marks {
  margin-bottom: 1rem;
}

.quote-text {
  font-size: clamp(1.5rem, 3vw, 2.25rem);
  line-height: 1.4;
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;
}

.quote-author {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.author-portrait {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  border: 3px solid var(--color-primary);
}

.author-name {
  font-size: 1.1rem;
}

.author-role {
  font-size: 0.9rem;
}

.company-logos {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 3rem;
  padding-top: 2rem;
}

.company-logo {
  color: var(--color-text-muted);
  font-family: var(--font-display);
  font-weight: 600;
  font-size: 1.1rem;
  transition: all var(--transition-base);
  opacity: 0.7;
}

.logo-item:hover .company-logo {
  color: var(--color-primary);
  opacity: 1;
}

@media (max-width: 768px) {
  .quote-text {
    font-size: 1.25rem;
  }
  
  .company-logos {
    gap: 1rem;
  }
}
</style> 
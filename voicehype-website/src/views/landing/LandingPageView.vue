<template>
  <div class="landing-page-container">
    <!-- Floating Action Button for Palestine Support -->
    <a href="https://alkhidmat.org/appeal/emergency-appeal-palestine-save-lives-in-gaza-today?gad_source=1&gclid=Cj0KCQjws-S-BhD2ARIsALssG0YysRLKf4LQH9OBtIjCnjt2E9BWs1ugamrJozzPr39Aq8VT5Vc48z4aApwCEALw_wcB"
       target="_blank"
       rel="noopener noreferrer"
       class="palestine-fab"
       title="Support Palestine - Donate via Alkhidmat.org">
      <div class="fab-content">
        <img src="https://flagcdn.com/w40/ps.png" alt="Palestine Flag" class="fab-flag">
        <span class="fab-text">Support Palestine</span>
      </div>
    </a>

    <div v-if="contentReady" id="landing-page-root" v-html="landingPageContent"></div>
    <div v-else class="landing-page-loader">
      <div class="loader-spinner"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, onBeforeUnmount } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';

const router = useRouter();
const authStore = useAuthStore();
const landingPageContent = ref('');
const addedStylesheets = ref<HTMLLinkElement[]>([]);
const addedScripts = ref<HTMLScriptElement[]>([]);
const contentReady = ref(false);

// Function to add stylesheet to the head
const addStylesheet = (href: string): HTMLLinkElement => {
  const link = document.createElement('link');
  link.rel = 'stylesheet';
  link.href = href;
  document.head.appendChild(link);
  addedStylesheets.value.push(link);
  return link;
};

// Function to preload stylesheet
const preloadStylesheet = (href: string): HTMLLinkElement | undefined => {
  // Check if already preloaded
  const existingPreload = document.querySelector(`link[rel="preload"][href="${href}"]`);
  if (existingPreload) return undefined;
  
  const link = document.createElement('link');
  link.rel = 'preload';
  link.href = href;
  link.as = 'style';
  if (href.includes('fonts.googleapis.com') || href.includes('fonts.gstatic.com')) {
    link.crossOrigin = 'anonymous';
  }
  document.head.appendChild(link);
  addedStylesheets.value.push(link);
  return link;
};

// Function to add script to the body
const addScript = (src: string): Promise<HTMLScriptElement> => {
  return new Promise((resolve) => {
    const script = document.createElement('script');
    script.src = src;
    script.onload = () => resolve(script);
    document.body.appendChild(script);
    addedScripts.value.push(script);
  });
};

// Handle navigation clicks
const handleNavigationClick = (e: MouseEvent): void => {
  const target = (e.target as HTMLElement).closest('a');
  if (target) {
    const href = target.getAttribute('href');
    if (!href) return;
    
    console.log('Intercepted click on link with href:', href);
    
    // Handle login/signup buttons in the nav
    if (target.classList.contains('login-button')) {
      e.preventDefault();
      // Check if user is already authenticated
      if (authStore.isAuthenticated) {
        // If already logged in, redirect to dashboard
        router.push('/app/dashboard');
      } else {
        // Otherwise go to login page
        router.push('/login');
      }
      window.scrollTo(0, 0);
      return;
    }
    
    if (target.classList.contains('signup-button')) {
      e.preventDefault();
      // Check if user is already authenticated
      if (authStore.isAuthenticated) {
        // If already logged in, redirect to dashboard
        router.push('/app/dashboard');
      } else {
        // Otherwise go to registration page
        router.push('/register');
      }
      window.scrollTo(0, 0);
      return;
    }
    
    // Handle specific footer links
    if (href === '#privacy' || href.includes('privacy')) {
      e.preventDefault();
      router.push('/privacy-policy');
      window.scrollTo(0, 0);
      return;
    }
    
    if (href === '#terms' || href.includes('terms')) {
      e.preventDefault();
      router.push('/terms-of-service');
      window.scrollTo(0, 0);
      return;
    }
    
    if (href === '#refund' || href.includes('refund')) {
      e.preventDefault();
      router.push('/refund-policy');
      window.scrollTo(0, 0);
      return;
    }
    
    if (href === '#security' || href.includes('security')) {
      e.preventDefault();
      router.push('/security');
      window.scrollTo(0, 0);
      return;
    }
    
    if (href === '#contact' || href.includes('contact')) {
      e.preventDefault();
      router.push('/contact');
      window.scrollTo(0, 0);
      return;
    }
    
    if (href === '#faq' || href.includes('faq')) {
      e.preventDefault();
      // Check if we're already on the landing page
      if (window.location.pathname === '/' || window.location.pathname.includes('landing')) {
        // We're on the landing page, so scroll to the FAQ section
        const faqSection = document.querySelector('#faq');
        if (faqSection) {
          faqSection.scrollIntoView({ 
            behavior: 'smooth',
            block: 'start'
          });
          window.history.pushState(null, '', '#faq');
        }
        return;
      } else {
        // We're not on the landing page, so navigate to the landing page with the FAQ hash
        router.push('/#faq');
        window.scrollTo(0, 0);
      }
      return;
    }
    
    if (href === '#blog' || href === '#blog-section' || href.includes('blog')) {
      e.preventDefault();
      // Check if we're already on the landing page
      if (window.location.pathname === '/' || window.location.pathname.includes('landing')) {
        // We're on the landing page, so scroll to the blog section
        const blogSection = document.querySelector('#blog-section');
        if (blogSection) {
          blogSection.scrollIntoView({ 
            behavior: 'smooth',
            block: 'start'
          });
          window.history.pushState(null, '', '#blog-section');
        }
        return;
      } else {
        // We're not on the landing page, so navigate to the landing page with the blog-section hash
        router.push('/#blog-section');
        window.scrollTo(0, 0);
      }
      return;
    }
    
    // Handle other general routes
    if (href.includes('login') || href === '/dashboard' || href.includes('signup')) {
      e.preventDefault();
      const route = href.includes('signup') ? '/register' : href;
      router.push(route);
      window.scrollTo(0, 0);
    } else if (href.startsWith('#')) {
      // Allow anchor navigation within the landing page
      e.preventDefault();
      const element = document.querySelector(href);
      if (element) {
        element.scrollIntoView({ 
          behavior: 'smooth',
          block: 'start'
        });
        
        // Push the hash to the URL for proper bookmarking
        window.history.pushState(null, '', href);
      }
    } else if (href.startsWith('/') && !href.startsWith('//') && !href.startsWith('/landing-page')) {
      // Handle other internal links to routes
      e.preventDefault();
      router.push(href);
      window.scrollTo(0, 0);
    }
  }
};

// Also add click handlers for buttons
const handleButtonClick = (e: MouseEvent): void => {
  const target = (e.target as HTMLElement).closest('button');
  if (target) {
    if (target.classList.contains('primary-button') || 
        target.classList.contains('speed-benefits-button') || 
        target.innerText.includes('Get Started') || 
        target.innerText.includes('Try It Now')) {
      e.preventDefault();
      // Check if user is already authenticated
      if (authStore.isAuthenticated) {
        // If already logged in, redirect to dashboard
        router.push('/app/dashboard');
      } else {
        // Otherwise go to registration page
        router.push('/register');
      }
      window.scrollTo(0, 0);
    }
  }
};

onMounted(async () => {
  try {
    // Force dark mode for landing page without affecting dashboard preference
    document.documentElement.classList.add('dark')
    
    // Load the landing page HTML content
    const response = await fetch('/landing-page/index.html');
    const html = await response.text();
    
    // Extract the body content only (ignore head content)
    const bodyMatch = html.match(/<body[^>]*>([\s\S]*)<\/body>/i);
    if (bodyMatch && bodyMatch[1]) {
      landingPageContent.value = bodyMatch[1];
    }
    
    // Add font preconnects
    const preconnectGoogle = document.createElement('link');
    preconnectGoogle.rel = 'preconnect';
    preconnectGoogle.href = 'https://fonts.googleapis.com';
    document.head.appendChild(preconnectGoogle);
    
    const preconnectGstatic = document.createElement('link');
    preconnectGstatic.rel = 'preconnect';
    preconnectGstatic.href = 'https://fonts.gstatic.com';
    preconnectGstatic.crossOrigin = 'anonymous';
    document.head.appendChild(preconnectGstatic);
    
    // Preload critical stylesheets and fonts
    preloadStylesheet('/landing-page/styles.css');
    preloadStylesheet('/landing-page/fonts.css');
    preloadStylesheet('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600;700&display=swap');
    preloadStylesheet('/landing-page/how-to-use-section.css');
    
    // Add the Google Fonts and other font resources
    addStylesheet('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600;700&display=swap');
    
    // Add self-hosted fonts CSS instead of Fontshare
    addStylesheet('/landing-page/fonts.css');
    
    addStylesheet('https://fonts.cdnfonts.com/css/instrument');
    addStylesheet('https://fonts.googleapis.com/css2?family=Outfit:wght@300;400;500;600;700;800;900&display=swap');
    addStylesheet('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap');
    
    // Add the landing page stylesheets
    addStylesheet('/landing-page/styles.css');
    addStylesheet('/landing-page/responsive-fixes.css');
    addStylesheet('/landing-page/speed-benefits-centered.css');
    addStylesheet('/landing-page/pricing-section.css');
    addStylesheet('/landing-page/how-to-use-section.css');
    addStylesheet('/custom-cards.css');
    
    // Add external libraries
    await addScript('https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js');
    await addScript('https://cdn.jsdelivr.net/npm/gsap@3.12.2/dist/gsap.min.js');
    
    
    // Ensure all resources are loaded before showing content
    setTimeout(() => {
      contentReady.value = true;
      
      // Set up event listeners after the content is rendered
      setTimeout(async () => {
        // Add fade-in scroll effect styles
        const fadeInStyles = document.createElement('style');
        fadeInStyles.textContent = `
          /* Fade-in Scroll Effect Styles */
          .fade-in-element {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.8s ease-out, transform 0.8s ease-out;
          }
          
          .fade-in-element.visible {
            opacity: 1;
            transform: translateY(0);
          }
          
          /* Hero section exclusion - keep it visible */
          .hero-section .fade-in-element,
          .hero .fade-in-element,
          section:first-of-type .fade-in-element {
            opacity: 1;
            transform: none;
            transition: none;
          }
          
          /* Palestine FAB exclusion - always visible */
          .palestine-fab,
          .palestine-fab *,
          .fab-content,
          .fab-flag,
          .fab-text {
            opacity: 1 !important;
            transform: none !important;
            transition: none !important;
          }
          
          /* Ensure quotes section fades in */
          .quotes-section,
          .testimonial-section,
          .quote-card,
          .testimonial-card,
          blockquote,
          .quote-text,
          .testimonial-text {
            /* These will be handled by the fade-in classes */
          }
        `;
        document.head.appendChild(fadeInStyles);
        
        // Add fade-in scroll effect functionality
        const initFadeInEffect = () => {
          // Get all elements that should fade in
          const elements = document.querySelectorAll('section, .feature-card, .pricing-card, .blog-card, .faq-item, .step-card, .benefit-item, .testimonial-card, .cta-section, .footer-section, .stats-section, .quotes-section, .testimonial-section, .quote-card, blockquote, .quote-text, .testimonial-text');
          
          // Add fade-in class to elements
          elements.forEach(element => {
            // Skip hero section elements and Palestine FAB
            const isHeroSection = element.closest('.hero-section') ||
                                 element.closest('.hero') ||
                                 element === document.querySelector('section');
            
            const isPalestineFAB = element.closest('.palestine-fab') ||
                                 element.classList.contains('palestine-fab') ||
                                 element.closest('.fab-content');
            
            if (!isHeroSection && !isPalestineFAB) {
              element.classList.add('fade-in-element');
            }
          });
          
          // Set up IntersectionObserver
          const observerOptions = {
            root: null,
            rootMargin: '0px 0px -50px 0px',
            threshold: 0.1
          };
          
          const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
              if (entry.isIntersecting) {
                entry.target.classList.add('visible');
              }
            });
          }, observerOptions);
          
          // Observe all fade-in elements
          document.querySelectorAll('.fade-in-element').forEach(element => {
            observer.observe(element);
          });
          
          // Also add fade-in to individual elements within sections
          const innerElements = document.querySelectorAll('h1, h2, h3, h4, h5, h6, p, img, .button, .card, .feature-item, .grid-item, .quote, .testimonial, .review');
          innerElements.forEach(element => {
            const isHeroSection = element.closest('.hero-section') ||
                                 element.closest('.hero') ||
                                 element.closest('section:first-of-type');
            
            const isPalestineFAB = element.closest('.palestine-fab') ||
                                 element.classList.contains('palestine-fab') ||
                                 element.closest('.fab-content');
            
            if (!isHeroSection && !isPalestineFAB && !element.classList.contains('fade-in-element')) {
              element.classList.add('fade-in-element');
            }
          });
          
          // Re-observe new elements
          document.querySelectorAll('.fade-in-element').forEach(element => {
            observer.observe(element);
          });
        };
        
        // Initialize fade-in effect after content loads
        setTimeout(initFadeInEffect, 100);
        
        // Remove loading script.js since we'll add mobile menu functionality directly
        // await addScript('/landing-page/script.js');
        
        // Add debug script in development mode
        if (process.env.NODE_ENV !== 'production') {
          await addScript('/landing-page/debug-links.js');
        }
        
        // Inject mobile menu HTML
        console.log('Creating enhanced mobile menu');
        const mobileMenuHTML = `
          <div class="vh-mobile-menu">
            <div class="vh-mobile-menu-backdrop"></div>
            <div class="vh-mobile-menu-container">
              <div class="vh-mobile-menu-header">
                <div class="vh-mobile-logo">
                  <svg width="32" height="24" viewBox="0 0 40 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="2" y="2" width="36" height="20" rx="2" fill="white" />
                    <rect x="2" y="2" width="36" height="20" rx="2" stroke="#14F195" stroke-width="1.5" />
                    <path d="M7 14 L10 9 L13 14 L16 9 L19 14 L22 9 L25 14 L28 9 L31 14 L34 9" stroke="#14F195"
                      stroke-width="1.5" stroke-linecap="round" />
                    <line x1="20" y1="22" x2="20" y2="26" stroke="#14F195" stroke-width="1.5" />
                    <line x1="14" y1="26" x2="26" y2="26" stroke="#14F195" stroke-width="1.5" />
                  </svg>
                  <span class="vh-mobile-logo-text">Voice<span class="hype-text-green">Hype</span></span>
                </div>
                <button class="vh-mobile-menu-close">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                  </svg>
                </button>
              </div>
              <div class="vh-mobile-menu-content">
                <ul class="vh-mobile-menu-links">
                  <li><a href="#features" class="vh-mobile-menu-link">Features</a></li>
                  <li><a href="#use-cases" class="vh-mobile-menu-link">Use Cases</a></li>
                  <li><a href="#how-to-use" class="vh-mobile-menu-link">How to Use</a></li>
                  <li><a href="#pricing-section" class="vh-mobile-menu-link">Pricing</a></li>
                  <li><a href="#blog-section" class="vh-mobile-menu-link">Blog</a></li>
                  <li><a href="#faq" class="vh-mobile-menu-link">FAQ</a></li>
                </ul>
                <div class="vh-mobile-menu-buttons">
                  <a href="#" class="vh-mobile-menu-button login">Log In</a>
                  <a href="#" class="vh-mobile-menu-button signup">Sign Up</a>
                </div>
              </div>
            </div>
          </div>
        `;
        
        // Add mobile menu HTML to the DOM
        const pageWrapper = document.querySelector('.page-wrapper');
        if (pageWrapper) {
          const tempDiv = document.createElement('div');
          tempDiv.innerHTML = mobileMenuHTML;
          pageWrapper.insertBefore(tempDiv.firstElementChild as Node, pageWrapper.firstElementChild?.nextSibling as Node);
          console.log('Mobile menu HTML injected');
        } else {
          console.error('Page wrapper not found, cannot inject mobile menu');
        }
        
        // Add mobile menu styles
        const mobileMenuStyles = document.createElement('style');
        mobileMenuStyles.textContent = `
          /* Mobile Menu Styles */
          .vh-mobile-menu {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
            visibility: hidden;
            opacity: 0;
            transition: visibility 0.3s ease, opacity 0.3s ease;
          }
          
          .vh-mobile-menu.active {
            visibility: visible;
            opacity: 1;
          }
          
          .vh-mobile-menu-backdrop {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
          }
          
          .vh-mobile-menu-container {
            position: absolute;
            top: 0;
            right: 0;
            width: 85%;
            max-width: 320px;
            height: 100%;
            background: #0c0c1d;
            box-shadow: -5px 0 25px rgba(0, 0, 0, 0.3);
            transform: translateX(100%);
            transition: transform 0.3s ease-out;
            display: flex;
            flex-direction: column;
            border-left: 1px solid rgba(255, 255, 255, 0.05);
          }
          
          .vh-mobile-menu.active .vh-mobile-menu-container {
            transform: translateX(0);
          }
          
          .vh-mobile-menu-header {
            display: flex;
            align-items: center;
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
          }
          
          .vh-mobile-logo {
            display: flex;
            align-items: center;
            flex-grow: 1;
          }
          
          .vh-mobile-logo-text {
            margin-left: 0.75rem;
            font-size: 1.25rem;
            font-weight: 600;
            font-family: var(--font-display);
          }
          
          .vh-mobile-menu-close {
            background: transparent;
            border: none;
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            padding: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          
          .vh-mobile-menu-close:hover {
            color: var(--color-primary);
          }
          
          .vh-mobile-menu-content {
            padding: 1.5rem;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
          }
          
          .vh-mobile-menu-links {
            list-style: none;
            padding: 0;
            margin: 0 0 2rem 0;
          }
          
          .vh-mobile-menu-links li {
            margin-bottom: 1rem;
          }
          
          .vh-mobile-menu-link {
            color: #ffffff;
            text-decoration: none;
            font-size: 1.1rem;
            font-weight: 500;
            padding: 0.75rem 0;
            display: block;
            border-bottom: 1px solid rgba(255, 255, 255, 0.07);
            transition: all 0.2s ease;
          }
          
          .vh-mobile-menu-link:hover {
            color: #14F195 !important; /* Hardcoded color instead of using var() */
            border-bottom-color: #14F195 !important;
            transform: translateX(5px);
            text-decoration: none !important;
          }
          
          /* More specific selector to ensure hover works */
          .vh-mobile-menu .vh-mobile-menu-links li a.vh-mobile-menu-link:hover {
            color: #14F195 !important;
            border-bottom-color: #14F195 !important;
            transform: translateX(5px);
            text-decoration: none !important;
            opacity: 1;
          }
          
          .vh-mobile-menu-buttons {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-top: auto;
          }
          
          .vh-mobile-menu-button {
            padding: 0.75rem 1.5rem;
            text-align: center;
            border-radius: 6px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.2s ease;
          }
          
          .vh-mobile-menu-button.login {
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            border: 1px solid rgba(255, 255, 255, 0.2);
          }
          
          .vh-mobile-menu-button.login:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
          }
          
          .vh-mobile-menu-button.signup {
            background: var(--color-primary);
            color: #000000 !important; /* Force black text color with !important */
            font-weight: 700; /* Slightly bolder for better contrast */
            border: 1px solid transparent;
          }
          
          .vh-mobile-menu-button.signup:hover {
            background: #12d685;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(20, 241, 149, 0.25);
          }
        `;
        document.head.appendChild(mobileMenuStyles);
        
        // Add mobile menu functionality
        console.log('Adding mobile menu functionality');
        const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
        const mobileMenu = document.querySelector('.vh-mobile-menu');
        const mobileMenuClose = document.querySelector('.vh-mobile-menu-close');
        const mobileMenuLinks = document.querySelectorAll('.vh-mobile-menu-link');
        const mobileMenuButtons = document.querySelectorAll('.vh-mobile-menu-button');
        const body = document.body;
        
        console.log('Mobile menu elements:', {
          mobileMenuToggle,
          mobileMenu,
          mobileMenuClose,
          mobileMenuLinksCount: mobileMenuLinks.length
        });
        
        // Open mobile menu
        if (mobileMenuToggle && mobileMenu) {
          mobileMenuToggle.addEventListener('click', () => {
            console.log('Mobile menu toggle clicked');
            mobileMenuToggle.classList.add('active');
            mobileMenu.classList.add('active');
            body.style.overflow = 'hidden'; // Prevent scrolling
            console.log('Mobile menu opened');
          });
        } else {
          console.error('Mobile menu toggle button or menu not found!');
        }
        
        // Close mobile menu
        const closeMenu = () => {
          console.log('Closing mobile menu');
          if (mobileMenuToggle) {
            mobileMenuToggle.classList.remove('active');
          }
          if (mobileMenu) {
            mobileMenu.classList.remove('active');
          }
          body.style.overflow = ''; // Re-enable scrolling
        };
        
        if (mobileMenuClose) {
          mobileMenuClose.addEventListener('click', closeMenu);
        }
        
        // Close menu when clicking on backdrop
        if (mobileMenu) {
          const backdrop = mobileMenu.querySelector('.vh-mobile-menu-backdrop');
          if (backdrop) {
            backdrop.addEventListener('click', closeMenu);
          }
        }
        
        // Handle section links click - smooth scroll and close menu
        mobileMenuLinks.forEach(link => {
          link.addEventListener('click', (e) => {
            e.preventDefault();
            const target = e.currentTarget as HTMLAnchorElement;
            const targetId = target.getAttribute('href');
            console.log('Mobile menu link clicked:', targetId);
            
            // Close the menu
            closeMenu();
            
            // Scroll to the section after a small delay
            if (targetId) {
              setTimeout(() => {
                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                  targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                  });
                  
                  // Update URL
                  window.history.pushState(null, '', targetId);
                  console.log('Scrolled to section:', targetId);
                } else {
                  console.error('Target section not found:', targetId);
                }
              }, 300);
            }
          });
        });
        
        // Handle login/signup buttons in mobile menu
        mobileMenuButtons.forEach(button => {
          button.addEventListener('click', (e) => {
            e.preventDefault();
            const target = e.currentTarget as HTMLElement;
            const isLogin = target.classList.contains('login');
            console.log(`Mobile menu ${isLogin ? 'login' : 'signup'} button clicked`);
            
            closeMenu();
            
            // Navigate to login or signup page
            if (isLogin) {
              if (authStore.isAuthenticated) {
                router.push('/app/dashboard');
              } else {
                router.push('/login');
              }
            } else {
              if (authStore.isAuthenticated) {
                router.push('/app/dashboard');
              } else {
                router.push('/register');
              }
            }
            window.scrollTo(0, 0);
          });
        });
        
        if (mobileMenuToggle && mobileMenu) {
          console.log('Mobile menu setup complete');
        } else {
          console.error('Mobile menu toggle button or menu not found!');
        }
        
        // Add pricing tab functionality directly
        console.log('Setting up pricing tab functionality');
        const pricingTabs = document.querySelectorAll('.pricing-tab');
        const pricingTabContents = document.querySelectorAll('.pricing-tab-content');
        
        if (pricingTabs.length > 0) {
          console.log(`Found ${pricingTabs.length} pricing tabs`);
          
          // Make all pricing elements visible immediately
          document.querySelectorAll('.appear-on-scroll').forEach(element => {
            (element as HTMLElement).style.opacity = '1';
            (element as HTMLElement).style.transform = 'none';
          });
          
          // Make all table rows visible
          document.querySelectorAll('.pricing-table tr').forEach(row => {
            (row as HTMLElement).style.opacity = '1';
          });
          
          // Make pricing content visible
          document.querySelectorAll('.pricing-tab-content').forEach(content => {
            if (content.classList.contains('active')) {
              (content as HTMLElement).style.display = 'block';
              (content as HTMLElement).style.opacity = '1';
              (content as HTMLElement).style.transform = 'none';
            }
          });
          
          // Add tab click handlers
          pricingTabs.forEach(tab => {
            tab.addEventListener('click', function(this: HTMLElement) {
              console.log('Pricing tab clicked:', this.getAttribute('data-tab'));
              
              // Remove active class from all tabs
              pricingTabs.forEach(t => t.classList.remove('active'));
              
              // Add active class to clicked tab
              this.classList.add('active');
              
              // Hide all tab content
              pricingTabContents.forEach(content => {
                (content as HTMLElement).style.display = 'none';
                content.classList.remove('active');
              });
              
              // Show selected tab content
              const tabContentId = `${this.getAttribute('data-tab')}-pricing`;
              console.log('Looking for content with ID:', tabContentId);
              const selectedContent = document.getElementById(tabContentId);
              
              if (selectedContent) {
                (selectedContent as HTMLElement).style.display = 'block';
                selectedContent.classList.add('active');
                console.log('Tab content activated:', tabContentId);
              } else {
                console.error('Could not find tab content with ID:', tabContentId);
              }
            });
          });
          
          // Ensure the active tab is visible
          const activeTab = document.querySelector('.pricing-tab.active');
          if (activeTab) {
            const tabContentId = `${activeTab.getAttribute('data-tab')}-pricing`;
            const activeContent = document.getElementById(tabContentId);
            if (activeContent) {
              (activeContent as HTMLElement).style.display = 'block';
            }
          }
          
          // Add override styles for animations
          const style = document.createElement('style');
          style.textContent = `
            .appear-on-scroll {
              opacity: 1 !important;
              transform: none !important;
              transition: none !important;
            }
            
            .pricing-table tr {
              opacity: 1 !important;
              transition: none !important;
            }
            
            .pricing-tab-content {
              transition: none !important;
            }
            
            .pricing-tab-content.active {
              opacity: 1 !important;
              transform: none !important;
            }
          `;
          document.head.appendChild(style);
        } else {
          console.warn('No pricing tabs found on the page');
        }
        
        // Initialize FAQ accordion functionality
        console.log('Setting up FAQ accordion functionality');
        const faqItems = document.querySelectorAll('.faq-item');
        
        if (faqItems.length > 0) {
          console.log(`Found ${faqItems.length} FAQ items`);
          
          faqItems.forEach((item, index) => {
            const question = item.querySelector('.faq-question');
            const answer = item.querySelector('.faq-answer');
            
            // Set initial state - first item active, others closed
            if (index === 0) {
              item.classList.add('active');
              if (answer) {
                (answer as HTMLElement).style.maxHeight = '1000px';
                (answer as HTMLElement).style.opacity = '1';
                (answer as HTMLElement).style.padding = '0 1.5rem 1.5rem 1.5rem';
              }
            } else if (answer) {
              (answer as HTMLElement).style.maxHeight = '0';
              (answer as HTMLElement).style.opacity = '0';
              (answer as HTMLElement).style.padding = '0 1.5rem';
            }
            
            if (question) {
              question.addEventListener('click', () => {
                console.log(`FAQ item ${index + 1} clicked`);
                
                // Check if this item is already active
                const isActive = item.classList.contains('active');
                
                // Close all items first
                faqItems.forEach(otherItem => {
                  const otherAnswer = otherItem.querySelector('.faq-answer');
                  
                  otherItem.classList.remove('active');
                  
                  if (otherAnswer) {
                    (otherAnswer as HTMLElement).style.maxHeight = '0';
                    (otherAnswer as HTMLElement).style.opacity = '0';
                    (otherAnswer as HTMLElement).style.padding = '0 1.5rem';
                  }
                });
                
                // If the clicked item wasn't active before, open it
                if (!isActive) {
                  item.classList.add('active');
                  
                  if (answer) {
                    (answer as HTMLElement).style.maxHeight = '1000px';
                    (answer as HTMLElement).style.opacity = '1';
                    (answer as HTMLElement).style.padding = '0 1.5rem 1.5rem 1.5rem';
                  }
                  
                  console.log(`FAQ item ${index + 1} opened`);
                } else {
                  console.log(`FAQ item ${index + 1} was already open, now closed`);
                }
              });
            }
          });
          
          // Add some basic styles for animations if not already present
          const styleElement = document.createElement('style');
          styleElement.textContent = `
            .faq-answer {
              transition: max-height 0.3s ease-out, opacity 0.3s ease-out;
            }
            
            .question-icon line {
              transition: transform 0.3s ease;
            }
            
            .faq-question {
              cursor: pointer;
            }
          `;
          document.head.appendChild(styleElement);
        } else {
          console.warn('No FAQ items found on the page');
        }
        
        // Intercept navigation clicks
        document.addEventListener('click', handleNavigationClick);
        document.addEventListener('click', handleButtonClick);
      }, 100);
    }, 500); // Give enough time for resources to load
  } catch (error) {
    console.error('Failed to load landing page:', error);
    contentReady.value = true; // Still show content on error
  }
});

// Clean up event listeners and remove added stylesheets/scripts when component is destroyed
onBeforeUnmount(() => {
  document.removeEventListener('click', handleNavigationClick);
  document.removeEventListener('click', handleButtonClick);
  
  // Remove added stylesheets
  addedStylesheets.value.forEach((link) => {
    if (link && link.parentNode) {
      link.parentNode.removeChild(link);
    }
  });
  
  // Remove added scripts
  addedScripts.value.forEach((script) => {
    if (script && script.parentNode) {
      script.parentNode.removeChild(script);
    }
  });
  
  // Remove preconnect links
  const preconnects = document.querySelectorAll('link[rel="preconnect"]');
  preconnects.forEach((element) => {
    const link = element as HTMLLinkElement;
    if (link.href.includes('fonts.googleapis.com') || 
        link.href.includes('fonts.gstatic.com')) {
      link.parentNode?.removeChild(link);
    }
  });
});
</script>

<style>
/* This ensures the landing page container takes full width/height */
.landing-page-container {
  width: 100%;
  min-height: 100vh;
  position: relative;
}

/* Loading spinner */
.landing-page-loader {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #080814;
}

.loader-spinner {
  width: 48px;
  height: 48px;
  border: 3px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  border-top-color: #14F195;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Override any Vue-specific styles that might conflict */
#landing-page-root {
  width: 100%;
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Fix signup button text color to make it more readable */
.nav-buttons .signup-button {
  color: #111827 !important; /* Dark gray, almost black */
  font-weight: 600 !important;
}

/* Center the Blog title in the blog section */
.blog-section .blog-title {
  text-align: center !important;
  width: 100% !important;
  display: block !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

.blog-section .section-header.blog-header {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

/* Font fallbacks in case Google Fonts don't load properly */
:root {
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  --font-mono: 'JetBrains Mono', Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --font-display: 'Clash Display', 'Inter', system-ui, sans-serif;
  --font-outfit: 'Outfit', var(--font-sans);
  --font-space: 'Space Grotesk', var(--font-sans);
  --font-instrument: 'Instrument', var(--font-sans);
}

/* Ensure correct font families are applied */
.logo-text,
.main-title,
.intro-title,
.section-title,
.feature-title,
.pricing-title,
.speed-benefits-title {
  font-family: var(--font-display);
}

.subtitle,
.nav-link,
.nav-button,
p,
li,
.metric-label,
.metric-number {
  font-family: var(--font-sans);
}

.code-block,
pre,
code {
  font-family: var(--font-mono);
}

/* Floating Action Button for Palestine Support */
.palestine-fab {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  background: linear-gradient(135deg, #000000, #1a1a1a);
  color: white;
  border: 2px solid #14F195;
  border-radius: 50px;
  padding: 12px 20px;
  text-decoration: none;
  box-shadow: 0 4px 20px rgba(20, 241, 149, 0.3);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  font-family: var(--font-sans);
}

.palestine-fab:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 8px 30px rgba(20, 241, 149, 0.5);
  background: linear-gradient(135deg, #14F195, #12d685);
  color: #000000;
  border-color: #000000;
}

.palestine-fab:hover .fab-text {
  color: #000000;
  font-weight: 700;
}

.palestine-fab:hover .fab-flag {
  filter: brightness(1.1);
}

.fab-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.fab-flag {
  width: 20px;
  height: 15px;
  border-radius: 2px;
  object-fit: cover;
}

.fab-text {
  white-space: nowrap;
}

/* Responsive adjustments for FAB */
@media (max-width: 768px) {
  .palestine-fab {
    bottom: 15px;
    right: 15px;
    padding: 10px 16px;
    font-size: 12px;
  }
  
  .fab-flag {
    width: 16px;
    height: 12px;
  }
  
  .fab-text {
    display: none;
  }
  
  .palestine-fab:hover .fab-text {
    display: inline;
  }
}

@media (max-width: 480px) {
  .palestine-fab {
    bottom: 10px;
    right: 10px;
    padding: 8px 12px;
  }
}
</style> 

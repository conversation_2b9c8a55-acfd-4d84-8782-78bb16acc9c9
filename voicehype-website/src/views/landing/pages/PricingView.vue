<template>
  <div class="pricing-view">
    <!-- Hero Section -->
    <section class="hero-section bg-gradient-to-br from-blue-50 via-white to-purple-50 px-4 py-16">
      <div class="max-w-4xl mx-auto text-center">
        <h1 class="md:text-5xl mb-6 text-4xl font-bold text-gray-900">
          Simple, Transparent Pricing
        </h1>
        <p class="max-w-2xl mx-auto mb-8 text-xl text-gray-600">
          Choose the perfect plan for your transcription and AI needs. 
          All plans include automatic quota reset and overage protection.
        </p>
        
        <!-- Trust indicators -->
        <div class="flex items-center justify-center space-x-8 text-sm text-gray-500">
          <div class="flex items-center">
            <CheckCircleIcon class="w-5 h-5 mr-2 text-green-500" />
            <span>Cancel anytime</span>
          </div>
          <div class="flex items-center">
            <CheckCircleIcon class="w-5 h-5 mr-2 text-green-500" />
            <span>No hidden fees</span>
          </div>
          <div class="flex items-center">
            <CheckCircleIcon class="w-5 h-5 mr-2 text-green-500" />
            <span>14-day free trial</span>
          </div>
        </div>
      </div>
    </section>

    <!-- Pricing Cards Section -->
    <section class="pricing-section px-4 py-16">
      <div class="max-w-7xl mx-auto">
        <SubscriptionPricingCards @plan-selected="handlePlanSelection" />
      </div>
    </section>

    <!-- Feature Comparison Section -->
    <section class="comparison-section bg-gray-50 px-4 py-16">
      <div class="max-w-7xl mx-auto">
        <PricingFeatureComparison />
      </div>
    </section>

    <!-- Enterprise Section -->
    <section class="enterprise-section px-4 py-16">
      <div class="max-w-4xl mx-auto text-center">
        <h2 class="mb-6 text-3xl font-bold text-gray-900">
          Need More Than Premium?
        </h2>
        <p class="mb-8 text-lg text-gray-600">
          For large organizations with custom requirements, we offer tailored Enterprise solutions.
        </p>
        
        <div class="rounded-xl p-8 bg-white border border-gray-200 shadow-lg">
          <div class="md:grid-cols-2 grid items-center gap-8">
            <div class="text-left">
              <h3 class="mb-4 text-xl font-semibold text-gray-900">Enterprise Features</h3>
              <ul class="space-y-3 text-gray-600">
                <li class="flex items-center">
                  <CheckIcon class="flex-shrink-0 w-5 h-5 mr-3 text-green-500" />
                  <span>Unlimited transcription minutes</span>
                </li>
                <li class="flex items-center">
                  <CheckIcon class="flex-shrink-0 w-5 h-5 mr-3 text-green-500" />
                  <span>Custom AI model fine-tuning</span>
                </li>
                <li class="flex items-center">
                  <CheckIcon class="flex-shrink-0 w-5 h-5 mr-3 text-green-500" />
                  <span>Dedicated support team</span>
                </li>
                <li class="flex items-center">
                  <CheckIcon class="flex-shrink-0 w-5 h-5 mr-3 text-green-500" />
                  <span>On-premise deployment options</span>
                </li>
                <li class="flex items-center">
                  <CheckIcon class="flex-shrink-0 w-5 h-5 mr-3 text-green-500" />
                  <span>Custom integrations</span>
                </li>
                <li class="flex items-center">
                  <CheckIcon class="flex-shrink-0 w-5 h-5 mr-3 text-green-500" />
                  <span>SLA guarantees</span>
                </li>
              </ul>
            </div>
            
            <div class="text-center">
              <div class="mb-6">
                <div class="mb-2 text-3xl font-bold text-gray-900">Custom Pricing</div>
                <div class="text-gray-600">Based on your specific needs</div>
              </div>
              
              <button
                @click="contactSales"
                class="hover:bg-gray-800 px-8 py-3 font-medium text-white transition-colors bg-gray-900 rounded-lg"
              >
                Contact Sales
              </button>
              
              <p class="mt-4 text-sm text-gray-500">
                Typical response time: 2 business hours
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq-section px-4 py-16">
      <div class="max-w-4xl mx-auto">
        <h2 class="mb-12 text-3xl font-bold text-center text-gray-900">
          Frequently Asked Questions
        </h2>
        
        <div class="space-y-6">
          <div
            v-for="faq in faqs"
            :key="faq.id"
            class="bg-white border border-gray-200 rounded-lg shadow-sm"
          >
            <button
              @click="toggleFaq(faq.id)"
              class="focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset w-full p-6 text-left"
            >
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">{{ faq.question }}</h3>
                <ChevronDownIcon
                  :class="[
                    'h-5 w-5 text-gray-500 transition-transform',
                    openFaq === faq.id ? 'transform rotate-180' : ''
                  ]"
                />
              </div>
            </button>
            
            <div
              v-if="openFaq === faq.id"
              class="px-6 pb-6"
            >
              <p class="text-gray-600" v-html="faq.answer"></p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section px-4 py-16 bg-blue-600">
      <div class="max-w-4xl mx-auto text-center">
        <h2 class="mb-6 text-3xl font-bold text-white">
          Ready to Get Started?
        </h2>
        <p class="mb-8 text-xl text-blue-100">
          Join thousands of developers who trust VoiceHype for their transcription needs.
        </p>
        
        <div class="sm:flex-row flex flex-col justify-center gap-4">
          <router-link
            to="/auth/signup"
            class="hover:bg-gray-50 px-8 py-3 font-medium text-blue-600 transition-colors bg-white rounded-lg"
          >
            Start Free Trial
          </router-link>
          <button
            @click="scrollToPricing"
            class="hover:bg-white hover:text-blue-600 px-8 py-3 font-medium text-white transition-colors border-2 border-white rounded-lg"
          >
            View Pricing
          </button>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { 
  CheckIcon, 
  CheckCircleIcon, 
  ChevronDownIcon 
} from '@heroicons/vue/24/solid'
import SubscriptionPricingCards from '@/components/pricing/SubscriptionPricingCards.vue'
import PricingFeatureComparison from '@/components/pricing/PricingFeatureComparison.vue'
import { useToast } from '@/services/toast'

// Router and composables
const router = useRouter()
const { success: showSuccessToast, error: showErrorToast, info: showInfoToast } = useToast()

// Reactive data
const openFaq = ref<string | null>(null)

// FAQ data
const faqs = ref([
  {
    id: 'trial',
    question: 'How does the free trial work?',
    answer: 'Every new account gets a 14-day free trial with Basic plan limits. No credit card required to start. You can upgrade to a paid plan at any time during or after the trial.'
  },
  {
    id: 'billing',
    question: 'How does the priority billing system work?',
    answer: '<strong>1. Subscription quota first:</strong> Your monthly minutes and tokens are used first.<br><strong>2. Prepaid credits:</strong> Any credits you\'ve purchased are used next.<br><strong>3. Pay-as-you-go allowance:</strong> Your $10 monthly overage allowance covers additional usage.<br><br>This ensures predictable costs while giving you flexibility for varying usage.'
  },
  {
    id: 'overage',
    question: 'What happens if I exceed my $10 overage allowance?',
    answer: 'If you exceed your $10 monthly overage allowance, you\'ll need to either purchase additional credits or wait until your next billing cycle. We\'ll always notify you before any charges and never charge without your explicit consent.'
  },
  {
    id: 'plans',
    question: 'Can I change plans anytime?',
    answer: 'Yes! You can upgrade immediately to get more quota right away. Downgrades take effect at your next billing cycle, so you keep your current plan benefits until then.'
  },
  {
    id: 'api',
    question: 'Do all plans include API access?',
    answer: 'Yes, all plans include full API access. Pro and Premium plans get priority processing, which means faster response times and higher priority in our processing queue.'
  },
  {
    id: 'rollover',
    question: 'Do unused minutes and tokens roll over?',
    answer: 'Subscription quotas reset monthly and don\'t roll over to keep pricing simple and predictable. However, any prepaid credits you purchase separately never expire and can be used anytime.'
  },
  {
    id: 'enterprise',
    question: 'What\'s included in Enterprise plans?',
    answer: 'Enterprise plans include unlimited usage, custom AI model training, dedicated support, on-premise deployment options, custom integrations, and SLA guarantees. Contact our sales team for a custom quote.'
  },
  {
    id: 'cancellation',
    question: 'How do cancellations work?',
    answer: 'You can cancel anytime from your account dashboard. Your subscription remains active until the end of your current billing period, and you keep all plan benefits until then. No cancellation fees.'
  }
])

// Methods
const toggleFaq = (faqId: string) => {
  openFaq.value = openFaq.value === faqId ? null : faqId
}

const handlePlanSelection = (selection: { plan: string, billingCycle: string }) => {
  console.log('Plan selected:', selection)
  // The pricing cards component handles the actual checkout redirect
  showInfoToast(
    'Redirecting to Checkout',
    `Setting up ${selection.plan} plan checkout...`
  )
}

const contactSales = () => {
  // Open email client or contact form
  window.location.href = 'mailto:<EMAIL>?subject=Enterprise Plan Inquiry'
}

const scrollToPricing = () => {
  const pricingSection = document.querySelector('.pricing-section')
  if (pricingSection) {
    pricingSection.scrollIntoView({ behavior: 'smooth' })
  }
}

// SEO meta tags
onMounted(() => {
  document.title = 'Pricing - VoiceHype'
  
  // Update meta description
  const metaDescription = document.querySelector('meta[name="description"]')
  if (metaDescription) {
    metaDescription.setAttribute('content', 'Simple, transparent pricing for VoiceHype transcription and AI services. Choose from Basic ($9), Pro ($18), or Premium ($27) plans with automatic quota reset and overage protection.')
  }
})
</script>

<style scoped>
/* Smooth transitions for FAQ */
.faq-section button {
  transition: all 0.2s ease-in-out;
}

.faq-section button:hover {
  background-color: #f9fafb;
}

/* Hero gradient animation */
.hero-section {
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
  animation: gradientShift 10s ease-in-out infinite alternate;
}

@keyframes gradientShift {
  0% {
    opacity: 0.3;
  }
  100% {
    opacity: 0.7;
  }
}

.hero-section > * {
  position: relative;
  z-index: 1;
}

/* CTA section styling */
.cta-section {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
}

/* Enterprise section card hover effect */
.enterprise-section .bg-white {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.enterprise-section .bg-white:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hero-section {
    padding: 3rem 1rem;
  }
  
  .hero-section h1 {
    font-size: 2.5rem;
  }
  
  .pricing-section,
  .comparison-section,
  .enterprise-section,
  .faq-section,
  .cta-section {
    padding: 3rem 1rem;
  }
}
</style>

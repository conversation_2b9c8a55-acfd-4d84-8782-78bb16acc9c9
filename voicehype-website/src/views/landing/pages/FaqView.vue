<template>
  <div class="policy-page">
    <header class="page-header">
      <div class="container px-4 py-8 mx-auto">
        <div class="flex items-center justify-between">
          <a href="/" class="flex items-center">
            <img src="/landing-page/logo.svg" alt="VoiceHype Logo" class="h-10">
          </a>
          <div class="flex space-x-4">
            <router-link to="/" class="hover:text-blue-600 text-gray-700">Home</router-link>
            <router-link to="/login" class="hover:text-blue-600 text-gray-700">Login</router-link>
            <router-link to="/app/dashboard" class="hover:text-blue-600 text-gray-700">Dashboard</router-link>
          </div>
        </div>
      </div>
    </header>
    
    <main class="container px-4 py-12 mx-auto">
      <h1 class="mb-8 text-3xl font-bold">Frequently Asked Questions</h1>
      <p class="max-w-4xl mb-8">Find answers to the most common questions about VoiceHype services.</p>
      
      <!-- Using our new FaqSection component -->
      <FaqSection />
      
      <div class="mt-12">
        <router-link to="/" class="hover:bg-blue-700 inline-block px-6 py-3 text-white transition-colors bg-blue-600 rounded-md">Back to Home</router-link>
      </div>
    </main>
    
    <footer class="py-8 mt-12 bg-gray-100">
      <div class="container px-4 mx-auto">
        <div class="md:grid-cols-3 grid grid-cols-1 gap-8">
          <div>
            <h3 class="mb-4 text-lg font-semibold">VoiceHype</h3>
            <p class="text-gray-600">Advanced voice technology for developers.</p>
          </div>
          <div>
            <h3 class="mb-4 text-lg font-semibold">Quick Links</h3>
            <ul class="space-y-2">
              <li><router-link to="/" class="hover:text-blue-600 text-gray-600">Home</router-link></li>
              <li><router-link to="/privacy-policy" class="hover:text-blue-600 text-gray-600">Privacy Policy</router-link></li>
              <li><router-link to="/terms-of-service" class="hover:text-blue-600 text-gray-600">Terms of Service</router-link></li>
              <li><router-link to="/refund-policy" class="hover:text-blue-600 text-gray-600">Refund Policy</router-link></li>
            </ul>
          </div>
          <div>
            <h3 class="mb-4 text-lg font-semibold">Contact</h3>
            <ul class="space-y-2">
              <li><router-link to="/contact" class="hover:text-blue-600 text-gray-600">Contact Us</router-link></li>
              <li><router-link to="/#faq" class="hover:text-blue-600 text-gray-600">FAQ</router-link></li>
              <li><router-link to="/blog" class="hover:text-blue-600 text-gray-600">Blog</router-link></li>
            </ul>
          </div>
        </div>
        <div class="pt-8 mt-8 text-center text-gray-600 border-t border-gray-200">
          <p>&copy; {{ new Date().getFullYear() }} VoiceHype. All rights reserved.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import FaqSection from '@/components/FaqSection.vue';
</script>

<style>
.policy-page {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

main {
  flex: 1;
}

/* Override some styles from the component to fit this page better */
:deep(.faq-section) {
  padding: 0;
  background: none;
}

:deep(.faq-section h2) {
  display: none; /* Hide the component's heading since we have our own */
}
</style> 
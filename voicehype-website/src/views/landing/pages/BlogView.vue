<template>
  <div class="policy-page">
    <nav class="navbar">
      <div class="nav-container">
        <div class="nav-logo">
          <div class="logo-container">
            <svg width="40" height="30" viewBox="0 0 40 30" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="2" y="2" width="36" height="20" rx="2" fill="white" />
              <rect x="2" y="2" width="36" height="20" rx="2" stroke="#14F195" stroke-width="1.5" />
              <path d="M7 14 L10 9 L13 14 L16 9 L19 14 L22 9 L25 14 L28 9 L31 14 L34 9" stroke="#14F195"
                stroke-width="1.5" stroke-linecap="round" />
              <line x1="20" y1="22" x2="20" y2="26" stroke="#14F195" stroke-width="1.5" />
              <line x1="14" y1="26" x2="26" y2="26" stroke="#14F195" stroke-width="1.5" />
            </svg>
          </div>
          <h1 class="logo-text">Voice<span class="hype-text">Hype</span></h1>
        </div>
        <div class="nav-buttons">
          <a href="#" class="nav-button login-button">Log In</a>
          <a href="#" class="nav-button signup-button">Sign Up</a>
        </div>
        <button class="mobile-menu-toggle">
          <span class="bar"></span>
          <span class="bar"></span>
          <span class="bar"></span>
        </button>
      </div>
    </nav>
    
    <main>
      <div class="container">
        <div class="blog-content">
          <h1>VoiceHype <span class="gradient-text">Blog</span></h1>
          <div class="coming-soon-message">
            <h3>Coming soon, inshallah!</h3>
            <p>
              We're preparing insightful articles and tutorials to help you get the most out of VoiceHype.
              Check back soon for updates!
            </p>
          </div>
        </div>
      </div>
    </main>
    
    <footer class="footer-section">
      <div class="footer-container">
        <div class="footer-content">
          <div class="footer-logo-section">
            <div class="footer-logo">
              <div class="logo-container">
                <svg width="40" height="30" viewBox="0 0 40 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect x="2" y="2" width="36" height="20" rx="2" fill="white" />
                  <rect x="2" y="2" width="36" height="20" rx="2" stroke="#14F195" stroke-width="1.5" />
                  <path d="M7 14 L10 9 L13 14 L16 9 L19 14 L22 9 L25 14 L28 9 L31 14 L34 9" stroke="#14F195"
                    stroke-width="1.5" stroke-linecap="round" />
                  <line x1="20" y1="22" x2="20" y2="26" stroke="#14F195" stroke-width="1.5" />
                  <line x1="14" y1="26" x2="26" y2="26" stroke="#14F195" stroke-width="1.5" />
                </svg>
              </div>
              <h3 class="footer-logo-text">Voice<span class="hype-text-green">Hype</span></h3>
            </div>
            <p class="footer-tagline">Revolutionizing coding with voice-to-prompt technology</p>
          </div>

          <div class="footer-links">
            <div class="footer-links-container">
              <div class="footer-links-column">
                <h4>Product</h4>
                <ul>
                  <li><a href="/#features">Features</a></li>
                  <li><a href="/#pricing-section">Pricing</a></li>
                  <li><a href="/#use-cases">Use Cases</a></li>
                  <li><a href="/#blog-section">Blog</a></li>
                  <li><a href="/#faq" class="scroll-to-faq">FAQ</a></li>
                </ul>
              </div>
              <div class="footer-links-column">
                <h4>Legal</h4>
                <ul>
                  <li><a href="/privacy-policy">Privacy Policy</a></li>
                  <li><a href="/terms-of-service">Terms of Service</a></li>
                  <li><a href="/refund-policy">Refund Policy</a></li>
                  <li><a href="/security">Security</a></li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="footer-bottom">
        <div class="footer-bottom-container">
          <p class="copyright">
            {{ new Date().getFullYear() }} VoiceHype. All rights reserved.
          </p>
          <div class="contact-info">
            <a href="mailto:<EMAIL>" class="contact-email">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z">
                </path>
                <polyline points="22,6 12,13 2,6"></polyline>
              </svg>
              <EMAIL>
            </a>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<style>
@import url('/landing-page/styles.css');
/* Replace Fontshare import with self-hosted fonts */
/* @import url('https://api.fontshare.com/v2/css?f[]=clash-display@600,700,800&display=swap'); */
@import url('/landing-page/fonts.css');

.policy-page {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: 'Inter', sans-serif;
  background-color: #080814;
  color: #ffffff;
}

main {
  flex: 1;
  padding: 5rem 0;
}

.container {
  width: 100%;
  margin: 0 auto;
  padding: 0 1rem;
}

.logo-text, .footer-logo-text {
  font-family: 'Clash Display', 'Inter', system-ui, sans-serif;
  font-weight: 700;
}

.hype-text {
  color: #14F195;
}

.hype-text-green {
  color: #14F195;
}

.signup-button {
  color: #000 !important;
  background-color: #14F195 !important;
}

.blog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 2rem 0;
  width: 100%;
}

.blog-content h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 2rem;
  font-family: 'Clash Display', 'Inter', system-ui, sans-serif;
}

.gradient-text {
  background: linear-gradient(90deg, #14F195, #9945FF);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.coming-soon-message {
  max-width: 600px;
  margin: 0 auto;
  padding: 2rem;
  border-radius: 1rem;
  background-color: rgba(255, 255, 255, 0.05);
}

.coming-soon-message h3 {
  font-size: 1.8rem;
  margin-bottom: 1rem;
  color: #14F195;
}

.coming-soon-message p {
  font-size: 1.2rem;
  line-height: 1.6;
  color: #ffffff;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 3rem;
  width: 100%;
}

.footer-logo-section {
  flex: 1;
  min-width: 250px;
  margin-right: 2rem;
}

.footer-links {
  flex: 2;
}

.footer-links-container {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
}

@media (max-width: 768px) {
  .footer-content {
    flex-direction: column;
    gap: 2.5rem;
  }
  
  .footer-links-container {
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .footer-logo-section {
    margin-right: 0;
  }
}
</style> 
<template>
  <div class="policy-page">
    <nav class="navbar">
      <div class="nav-container">
        <div class="nav-logo">
          <div class="logo-container">
            <svg width="40" height="30" viewBox="0 0 40 30" fill="none" xmlns="http://www.w3.org/2000/svg">
              <!-- Monitor outline with white fill -->
              <rect x="2" y="2" width="36" height="20" rx="2" fill="white" />
              <rect x="2" y="2" width="36" height="20" rx="2" stroke="#14F195" stroke-width="1.5" />
              <!-- Sound wave visualization -->
              <path d="M7 14 L10 9 L13 14 L16 9 L19 14 L22 9 L25 14 L28 9 L31 14 L34 9" stroke="#14F195"
                stroke-width="1.5" stroke-linecap="round" />
              <!-- Stand -->
              <line x1="20" y1="22" x2="20" y2="26" stroke="#14F195" stroke-width="1.5" />
              <line x1="14" y1="26" x2="26" y2="26" stroke="#14F195" stroke-width="1.5" />
            </svg>
          </div>
          <h1 class="logo-text">Voice<span class="hype-text">Hype</span></h1>
        </div>
        <div class="nav-buttons">
          <a href="#" class="nav-button login-button">Log In</a>
          <a href="#" class="nav-button signup-button">Sign Up</a>
        </div>
        <button class="mobile-menu-toggle">
          <span class="bar"></span>
          <span class="bar"></span>
          <span class="bar"></span>
        </button>
      </div>
    </nav>
    
    <main class="container px-4 py-12 mx-auto">
      <h1 class="mb-8 text-3xl font-bold">Refund Policy</h1>
      <div class="max-w-4xl prose">
        <p class="mb-4">Last Updated: April 9, 2025</p>
        
        <h2 class="mt-6 mb-4 text-2xl font-semibold">Overview</h2>
        <p class="mb-4">At VoiceHype, we strive to provide high-quality service and ensure our users' satisfaction. This Refund Policy outlines the circumstances under which refunds may be issued for purchases made on our platform.</p>
        
        <h2 class="mt-6 mb-4 text-2xl font-semibold">Free Service Tier</h2>
        <p class="mb-4">Currently, VoiceHype offers a free tier with the following limits:</p>
        <ul class="pl-5 mb-4 list-disc">
          <li>60 minutes of transcription</li>
          <li>10,000 tokens for exploration</li>
        </ul>
        <p class="mb-4">No payment is required for the free tier, and therefore no refunds apply.</p>
        
        <h2 class="mt-6 mb-4 text-2xl font-semibold">Paid Services and Credits</h2>
        <p class="mb-4">For any paid services or credits purchased:</p>
        
        <h3 class="mt-4 mb-2 text-xl font-semibold">Eligibility for Refunds</h3>
        <p class="mb-4">VoiceHype offers refunds only in cases of accidental purchases. Examples include:</p>
        <ul class="pl-5 mb-4 list-disc">
          <li>Entering an incorrect amount (e.g., typing 95 instead of 9.5)</li>
          <li>Unintentional duplicate purchases</li>
          <li>Technical errors during the payment process</li>
        </ul>
        
        <h3 class="mt-4 mb-2 text-xl font-semibold">Refund Request Process</h3>
        <p class="mb-4">To request a refund:</p>
        <ol class="pl-5 mb-4 list-decimal">
          <li>Email <EMAIL> within 7 days of purchase</li>
          <li>Include your account information and order/transaction ID</li>
          <li>Clearly explain the reason for your refund request</li>
          <li>Provide any supporting documentation if applicable</li>
        </ol>
        
        <h3 class="mt-4 mb-2 text-xl font-semibold">Review Process</h3>
        <p class="mb-4">All refund requests will be reviewed on a case-by-case basis. We will respond to your request within 5 business days.</p>
        
        <h3 class="mt-4 mb-2 text-xl font-semibold">Approval and Processing</h3>
        <p class="mb-4">If your refund request is approved:</p>
        <ul class="pl-5 mb-4 list-disc">
          <li>Credits will be returned to your account, or</li>
          <li>The purchase amount will be refunded to your original payment method</li>
          <li>Processing time may vary depending on your payment provider (typically 5-10 business days)</li>
        </ul>
        
        <h3 class="mt-4 mb-2 text-xl font-semibold">Non-Refundable Circumstances</h3>
        <p class="mb-4">Refunds will NOT be issued in the following situations:</p>
        <ul class="pl-5 mb-4 list-disc">
          <li>Requests made after the 7-day window</li>
          <li>Dissatisfaction with the service after use</li>
          <li>Purchased credits that have been partially or fully used</li>
          <li>Violations of our Terms of Service</li>
          <li>Any reason other than accidental purchases as described above</li>
        </ul>
        
        <h2 class="mt-6 mb-4 text-2xl font-semibold">Future Subscription Plans</h2>
        <p class="mb-4">As stated in our service information, subscription plans are in development. Once launched, specific refund terms for subscription services will be added to this policy.</p>
        
        <h2 class="mt-6 mb-4 text-2xl font-semibold">Policy Updates</h2>
        <p class="mb-4">We reserve the right to modify this Refund Policy at any time. Changes will be effective immediately upon posting to our website.</p>
        
        <h2 class="mt-6 mb-4 text-2xl font-semibold">Contact Information</h2>
        <p class="mb-4">If you have any questions about our Refund Policy, please contact us:</p>
        <ul class="pl-5 mb-4 list-disc">
          <li>Email: <EMAIL></li>
        </ul>
      </div>
    </main>
    
    <footer class="footer-section">
        <div class="footer-container">
            <div class="footer-content">
                <div class="footer-logo">
                    <div class="logo-container">
                        <svg width="40" height="30" viewBox="0 0 40 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="2" y="2" width="36" height="20" rx="2" fill="white" />
                            <rect x="2" y="2" width="36" height="20" rx="2" stroke="#14F195" stroke-width="1.5" />
                            <path d="M7 14 L10 9 L13 14 L16 9 L19 14 L22 9 L25 14 L28 9 L31 14 L34 9" stroke="#14F195"
                                stroke-width="1.5" stroke-linecap="round" />
                            <line x1="20" y1="22" x2="20" y2="26" stroke="#14F195" stroke-width="1.5" />
                            <line x1="14" y1="26" x2="26" y2="26" stroke="#14F195" stroke-width="1.5" />
                        </svg>
                    </div>
                    <h3 class="footer-logo-text">Voice<span class="hype-text">Hype</span></h3>
                </div>
                <p class="footer-tagline">Revolutionizing coding with voice-to-prompt technology</p>
            </div>

            <div class="footer-links">
                <div class="footer-links-column">
                    <h4>Product</h4>
                    <ul>
                        <li><a href="#features">Features</a></li>
                        <li><a href="#pricing">Pricing</a></li>
                        <li><a href="#use-cases">Use Cases</a></li>
                        <li><a href="#blog">Blog</a></li>
                        <li><a href="#faq">FAQ</a></li>
                    </ul>
                </div>
                <div class="footer-links-column">
                    <h4>Legal</h4>
                    <ul>
                        <li><a href="/privacy-policy">Privacy Policy</a></li>
                        <li><a href="/terms-of-service">Terms of Service</a></li>
                        <li><a href="/refund-policy">Refund Policy</a></li>
                        <li><a href="/security">Security</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="footer-bottom">
            <div class="footer-bottom-container">
                <p class="copyright">© 2025 VoiceHype. All rights reserved.</p>
                <div class="contact-info">
                    <a href="mailto:<EMAIL>" class="contact-email">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z">
                            </path>
                            <polyline points="22,6 12,13 2,6"></polyline>
                        </svg>
                        <EMAIL>
                    </a>
                </div>
            </div>
        </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
// No additional script needed for this view
</script>

<style>
/* Import styles from landing page for navbar and footer */
@import url('/landing-page/styles.css');
/* Import the custom fonts used on landing page */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Space+Grotesk:wght@500;700&display=swap');

.policy-page {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: 'Inter', sans-serif;
}

main {
  flex: 1;
}

/* Override some specific styles for policy pages */
.navbar {
  position: relative;
  background-color: #080814;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.logo-text, .footer-logo-text {
  font-family: 'Space Grotesk', sans-serif;
  font-weight: 700;
}

.hype-text {
  color: #14F195;
}

.signup-button {
  color: #000 !important;
}

.prose {
  color: #333;
  line-height: 1.6;
}

.prose h2 {
  color: #222;
  margin-top: 1.5em;
}

.prose h3 {
  color: #333;
  margin-top: 1em;
}

.prose ul, .prose ol {
  margin-bottom: 1.5em;
}

@media (prefers-color-scheme: dark) {
  .policy-page .prose {
    color: #ffffff !important;
  }
  
  .policy-page .prose h2, 
  .policy-page .prose h3 {
    color: #f0f0f0 !important;
  }

  .policy-page .prose p, 
  .policy-page .prose li, 
  .policy-page .prose strong,
  .policy-page .prose ul,
  .policy-page .prose ol {
    color: #ffffff !important;
  }
  
  /* Override any potential inline styles */
  [class*="prose"] * {
    color: #ffffff !important;
  }
}
</style> 
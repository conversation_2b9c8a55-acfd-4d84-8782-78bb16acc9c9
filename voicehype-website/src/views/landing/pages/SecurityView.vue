<template>
  <div class="policy-page">
    <nav class="navbar">
      <div class="nav-container">
        <div class="nav-logo">
          <div class="logo-container">
            <svg width="40" height="30" viewBox="0 0 40 30" fill="none" xmlns="http://www.w3.org/2000/svg">
              <!-- Monitor outline with white fill -->
              <rect x="2" y="2" width="36" height="20" rx="2" fill="white" />
              <rect x="2" y="2" width="36" height="20" rx="2" stroke="#14F195" stroke-width="1.5" />
              <!-- Sound wave visualization -->
              <path d="M7 14 L10 9 L13 14 L16 9 L19 14 L22 9 L25 14 L28 9 L31 14 L34 9" stroke="#14F195"
                stroke-width="1.5" stroke-linecap="round" />
              <!-- Stand -->
              <line x1="20" y1="22" x2="20" y2="26" stroke="#14F195" stroke-width="1.5" />
              <line x1="14" y1="26" x2="26" y2="26" stroke="#14F195" stroke-width="1.5" />
            </svg>
          </div>
          <h1 class="logo-text">Voice<span class="hype-text">Hype</span></h1>
        </div>
        <div class="nav-buttons">
          <a href="#" class="nav-button login-button">Log In</a>
          <a href="#" class="nav-button signup-button">Sign Up</a>
        </div>
        <button class="mobile-menu-toggle">
          <span class="bar"></span>
          <span class="bar"></span>
          <span class="bar"></span>
        </button>
      </div>
    </nav>

    <main class="container px-4 py-12 mx-auto">
      <h1 class="mb-8 text-3xl font-bold">Security</h1>
      <div class="max-w-4xl prose">
        <p class="mb-4">Last Updated: May 10, 2024</p>

        <h2 class="mt-6 mb-4 text-2xl font-semibold">Our Security Commitment</h2>
        <p class="mb-4">At VoiceHype, we prioritize the security and privacy of our users' data. Your trust is important to us, and we take all necessary measures to protect your information.</p>

        <h2 class="mt-6 mb-4 text-2xl font-semibold">Zero Storage Policy for Voice Data</h2>
        <p class="mb-4"><strong>We never store your voice recordings, transcripts, or optimized prompts.</strong> This is our firm commitment to your privacy.</p>
        <ul class="pl-5 mb-4 list-disc">
          <li>Voice recordings are processed in real-time and are never stored</li>
          <li>Transcripts are generated only for immediate processing and are not retained</li>
          <li>Optimized prompts are delivered to you directly and are not saved on our servers</li>
        </ul>

        <h2 class="mt-6 mb-4 text-2xl font-semibold">Data Security Measures</h2>
        <p class="mb-4">We implement comprehensive security measures to protect your account information:</p>
        <ul class="pl-5 mb-4 list-disc">
          <li>Industry-standard encryption for all data transfers</li>
          <li>Secure authentication systems</li>
          <li>Regular security updates and monitoring</li>
        </ul>

        <h2 class="mt-6 mb-4 text-2xl font-semibold">Third-Party Services</h2>
        <p class="mb-4">We carefully select our service providers based on their security standards. All third-party services we use adhere to strict data protection policies and do not store your voice data or transcripts.</p>

        <h2 class="mt-6 mb-4 text-2xl font-semibold">Security Vulnerability Reporting</h2>
        <p class="mb-4">If you discover a security vulnerability, please report <NAME_EMAIL>. We will respond promptly to address any concerns.</p>

        <h2 class="mt-6 mb-4 text-2xl font-semibold">Contact Information</h2>
        <p class="mb-4">For questions about our security practices, please contact <NAME_EMAIL>.</p>
      </div>
    </main>

    <footer class="footer-section">
        <div class="footer-container">
            <div class="footer-content">
                <div class="footer-logo">
                    <div class="logo-container">
                        <svg width="40" height="30" viewBox="0 0 40 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="2" y="2" width="36" height="20" rx="2" fill="white" />
                            <rect x="2" y="2" width="36" height="20" rx="2" stroke="#14F195" stroke-width="1.5" />
                            <path d="M7 14 L10 9 L13 14 L16 9 L19 14 L22 9 L25 14 L28 9 L31 14 L34 9" stroke="#14F195"
                                stroke-width="1.5" stroke-linecap="round" />
                            <line x1="20" y1="22" x2="20" y2="26" stroke="#14F195" stroke-width="1.5" />
                            <line x1="14" y1="26" x2="26" y2="26" stroke="#14F195" stroke-width="1.5" />
                        </svg>
                    </div>
                    <h3 class="footer-logo-text">Voice<span class="hype-text">Hype</span></h3>
                </div>
                <p class="footer-tagline">Revolutionizing coding with voice-to-prompt technology</p>
            </div>

            <div class="footer-links">
                <div class="footer-links-column">
                    <h4>Product</h4>
                    <ul>
                        <li><a href="#features">Features</a></li>
                        <li><a href="#pricing">Pricing</a></li>
                        <li><a href="#use-cases">Use Cases</a></li>
                        <li><a href="#blog">Blog</a></li>
                        <li><a href="#faq">FAQ</a></li>
                    </ul>
                </div>
                <div class="footer-links-column">
                    <h4>Legal</h4>
                    <ul>
                        <li><a href="/privacy-policy">Privacy Policy</a></li>
                        <li><a href="/terms-of-service">Terms of Service</a></li>
                        <li><a href="/refund-policy">Refund Policy</a></li>
                        <li><a href="/security">Security</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="footer-bottom">
            <div class="footer-bottom-container">
                <p class="copyright">© 2025 VoiceHype. All rights reserved.</p>
                <div class="contact-info">
                    <a href="mailto:<EMAIL>" class="contact-email">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z">
                            </path>
                            <polyline points="22,6 12,13 2,6"></polyline>
                        </svg>
                        <EMAIL>
                    </a>
                </div>
            </div>
        </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
// No additional script needed for this view
</script>

<style>
/* Import styles from landing page for navbar and footer */
@import url('/landing-page/styles.css');
/* Import the custom fonts used on landing page */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Space+Grotesk:wght@500;700&display=swap');

.policy-page {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: 'Inter', sans-serif;
}

main {
  flex: 1;
}

/* Override some specific styles for policy pages */
.navbar {
  position: relative;
  background-color: #080814;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.logo-text, .footer-logo-text {
  font-family: 'Space Grotesk', sans-serif;
  font-weight: 700;
}

.hype-text {
  color: #14F195;
}

.signup-button {
  color: #000 !important;
}

.prose {
  color: #333;
  line-height: 1.6;
}

.prose h2 {
  color: #222;
  margin-top: 1.5em;
}

.prose h3 {
  color: #333;
  margin-top: 1em;
}

.prose ul, .prose ol {
  margin-bottom: 1.5em;
}

@media (prefers-color-scheme: dark) {
  .prose {
    color: #ffffff !important;
  }

  .prose h2, .prose h3 {
    color: #f0f0f0;
  }

  .prose p, .prose li, .prose strong {
    color: #ffffff !important;
  }
}
</style>
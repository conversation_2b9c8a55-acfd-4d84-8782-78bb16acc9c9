<template>
  <!-- API Keys Section -->
  <section id="api-keys" class="mb-12">
    <div class="flex items-center justify-between mb-6">
      <h2 class="text-gray-800 dark:text-[#c9d1d9] font-heading text-2xl font-semibold">API Keys</h2>
      <button 
        @click="openCreateModal" 
        class="btn-primary"
      >
        <PlusIcon class="inline w-5 h-5 mr-2" /> Create New Key
      </button>
    </div>
    
    <!-- Loading state with shimmer effect -->
    <div v-if="loading" class="bg-white dark:bg-[#0d1117] overflow-hidden rounded-lg shadow border border-gray-200 dark:border-[#30363d]">
      <!-- Desktop shimmer -->
      <div class="md:block hidden">
        <!-- Table header shimmer -->
        <div class="bg-gray-50 dark:bg-[#21262d] p-4">
          <div class="grid grid-cols-7 gap-4">
            <div class="animate-pulse h-4 bg-gray-200 dark:bg-[#30363d] rounded"></div>
            <div class="animate-pulse h-4 bg-gray-200 dark:bg-[#30363d] rounded"></div>
            <div class="animate-pulse h-4 bg-gray-200 dark:bg-[#30363d] rounded"></div>
            <div class="animate-pulse h-4 bg-gray-200 dark:bg-[#30363d] rounded"></div>
            <div class="animate-pulse h-4 bg-gray-200 dark:bg-[#30363d] rounded"></div>
            <div class="animate-pulse h-4 bg-gray-200 dark:bg-[#30363d] rounded"></div>
            <div class="animate-pulse h-4 bg-gray-200 dark:bg-[#30363d] rounded"></div>
          </div>
        </div>
        
        <!-- Table body shimmer -->
        <div v-for="i in 3" :key="i" class="p-4 border-t border-gray-200 dark:border-[#30363d]">
          <div class="grid grid-cols-7 gap-4">
            <div class="animate-pulse h-5 bg-gray-100 dark:bg-[#30363d] rounded"></div>
            <div class="animate-pulse h-5 bg-gray-100 dark:bg-[#30363d] rounded"></div>
            <div class="animate-pulse w-16 h-5 bg-gray-100 dark:bg-[#30363d] rounded-full"></div>
            <div class="animate-pulse h-5 bg-gray-100 dark:bg-[#30363d] rounded"></div>
            <div class="animate-pulse h-5 bg-gray-100 dark:bg-[#30363d] rounded"></div>
            <div class="animate-pulse h-5 bg-gray-100 dark:bg-[#30363d] rounded"></div>
            <div class="animate-pulse h-5 bg-gray-100 dark:bg-[#30363d] rounded"></div>
          </div>
        </div>
      </div>

      <!-- Mobile shimmer -->
      <div class="md:hidden">
        <div v-for="i in 3" :key="i" class="animate-pulse p-4 border-b border-gray-200 dark:border-[#30363d]">
          <div class="flex items-center justify-between mb-3">
            <div class="w-24 h-5 bg-gray-200 dark:bg-[#30363d] rounded"></div>
            <div class="w-16 h-5 bg-gray-200 dark:bg-[#30363d] rounded-full"></div>
          </div>
          <div class="w-32 h-4 mb-3 bg-gray-100 dark:bg-[#30363d] rounded"></div>
          <div class="grid grid-cols-2 gap-2 mb-3">
            <div class="h-4 bg-gray-100 dark:bg-[#30363d] rounded"></div>
            <div class="h-4 bg-gray-100 dark:bg-[#30363d] rounded"></div>
            <div class="h-4 bg-gray-100 dark:bg-[#30363d] rounded"></div>
          </div>
          <div class="flex pt-2 mt-2 space-x-2 border-t border-gray-100 dark:border-[#30363d]">
            <div class="w-16 h-6 bg-gray-200 dark:bg-[#30363d] rounded"></div>
            <div class="w-16 h-6 bg-gray-200 dark:bg-[#30363d] rounded"></div>
            <div class="w-16 h-6 bg-gray-200 dark:bg-[#30363d] rounded"></div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Error state -->
    <div v-else-if="error" class="relative px-4 py-3 text-red-700 bg-red-100 border border-red-400 rounded" role="alert">
      <span class="sm:inline block">{{ error }}</span>
      <button 
        @click="fetchApiKeys" 
        class="mt-2 text-sm underline"
      >
        Try again
      </button>
    </div>
    
    <!-- Empty state -->
    <div v-else-if="apiKeys.length === 0" class="bg-white dark:bg-[#0d1117] p-6 text-center rounded-lg shadow border border-gray-200 dark:border-[#30363d]">
      <div class="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-[#21262d] rounded-full">
        <KeyIcon class="w-8 h-8 text-gray-500 dark:text-[#8b949e]" />
      </div>
      <h3 class="font-heading dark:text-white mb-2 text-lg font-medium text-gray-900">No API Keys</h3>
      <p class="mb-4 text-gray-600 dark:text-[#8b949e]">You haven't created any API keys yet.</p>
      <button 
        @click="openCreateModal" 
        class="btn-primary"
      >
        <PlusIcon class="inline w-5 h-5 mr-2" /> Create Your First Key
      </button>
    </div>
    
    <!-- API Keys list -->
    <div v-else class="bg-white dark:bg-[#0d1117] overflow-hidden rounded-lg shadow border border-gray-200 dark:border-[#30363d]">
      <!-- Mobile Card View - shown on small screens -->
      <div class="md:hidden">
        <div v-for="key in apiKeys" :key="key.id" class="p-4 border-b border-gray-200 dark:border-[#30363d]">
          <div class="flex items-center justify-between mb-2">
            <div class="dark:text-white text-sm font-medium text-gray-900">
              {{ key.name }}
            </div>
            <span 
              class="inline-flex px-2 text-xs font-semibold leading-5 rounded-full" 
              :class="key.is_active ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'"
            >
              {{ key.is_active ? 'Active' : 'Inactive' }}
            </span>
          </div>
          
          <div class="font-mono text-sm text-gray-500 dark:text-[#8b949e] mb-2">
            vhkey_{{ key.key_prefix }}
          </div>
          
          <div class="grid grid-cols-2 gap-2 mb-3 text-xs text-gray-500 dark:text-[#8b949e]">
            <div>
              <span class="font-medium">Created:</span> 
              <div>{{ formatDate(key.created_at) }}</div>
            </div>
            <div>
              <span class="font-medium">Last Used:</span> 
              <div>{{ key.last_used_at ? formatDate(key.last_used_at) : 'Never' }}</div>
            </div>
            <div>
              <span class="font-medium">Expires:</span> 
              <div>{{ key.expires_at ? formatDate(key.expires_at) : 'Never' }}</div>
            </div>
          </div>
          
          <div class="flex pt-2 mt-2 space-x-2 border-t border-gray-100 dark:border-[#30363d]">
            <!-- Activate/Deactivate button -->
            <button 
              v-if="key.is_active"
              @click="deactivateKey(key.id)" 
              class="bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 flex items-center px-2 py-1 text-white rounded text-xs font-medium transition-colors shadow-sm"
              title="Disable this API key"
            >
              <XCircleIcon class="w-3.5 h-3.5 mr-1" />
              Disable
            </button>
            <button 
              v-else
              @click="activateKey(key.id)" 
              class="bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-800 flex items-center px-2 py-1 text-white rounded text-xs font-medium transition-colors shadow-sm"
              title="Enable this API key"
            >
              <CheckCircleIcon class="w-3.5 h-3.5 mr-1" />
              Enable
            </button>
            
            <!-- Rename button -->
            <button 
              @click="openRenameModal(key)" 
              class="bg-primary-600 hover:bg-primary-700 dark:bg-[#14F195] dark:hover:bg-[#14F195]/80 dark:text-gray-900 flex items-center px-2 py-1 text-white rounded text-xs font-medium transition-colors shadow-sm"
              title="Rename this API key"
            >
              <PencilIcon class="w-3.5 h-3.5 mr-1" />
              Rename
            </button>
            
            <!-- Delete button -->
            <button 
              @click="deleteKey(key.id)" 
              class="bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 flex items-center px-2 py-1 text-white rounded text-xs font-medium transition-colors shadow-sm"
              title="Permanently delete this API key"
            >
              <TrashIcon class="w-3.5 h-3.5 mr-1" />
              Delete
            </button>
          </div>
        </div>
      </div>
      
      <!-- Table View - hidden on small screens, shown on medium and larger screens -->
      <div class="md:block hidden overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-[#30363d]">
          <thead class="bg-gray-50 dark:bg-[#0d1117]">
            <tr>
              <th class="text-gray-500 dark:text-[#8b949e] px-6 py-3 text-xs font-medium tracking-wider text-left uppercase">Name</th>
              <th class="text-gray-500 dark:text-[#8b949e] px-6 py-3 text-xs font-medium tracking-wider text-left uppercase">Prefix</th>
              <th class="text-gray-500 dark:text-[#8b949e] px-6 py-3 text-xs font-medium tracking-wider text-left uppercase">Status</th>
              <th class="text-gray-500 dark:text-[#8b949e] px-6 py-3 text-xs font-medium tracking-wider text-left uppercase">Created</th>
              <th class="text-gray-500 dark:text-[#8b949e] px-6 py-3 text-xs font-medium tracking-wider text-left uppercase">Last Used</th>
              <th class="text-gray-500 dark:text-[#8b949e] px-6 py-3 text-xs font-medium tracking-wider text-left uppercase">Expires</th>
              <th class="text-gray-500 dark:text-[#8b949e] px-6 py-3 text-xs font-medium tracking-wider text-right uppercase">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-[#0d1117] divide-y divide-gray-200 dark:divide-[#30363d]">
            <tr v-for="(key, index) in apiKeys" :key="key.id" :class="index % 2 === 0 ? 'bg-white dark:bg-[#0d1117]' : 'bg-gray-50 dark:bg-[#10161d]'">
              <td class="whitespace-nowrap text-gray-900 dark:text-white px-6 py-4 text-sm font-medium">
                {{ key.name }}
              </td>
              <td class="whitespace-nowrap text-gray-500 dark:text-[#8b949e] px-6 py-4 text-sm font-mono">
                vhkey_{{ key.key_prefix }}
              </td>
              <td class="whitespace-nowrap px-6 py-4">
                <span 
                  class="inline-flex px-2 text-xs font-semibold leading-5 rounded-full" 
                  :class="key.is_active ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'"
                >
                  {{ key.is_active ? 'Active' : 'Inactive' }}
                </span>
              </td>
              <td class="whitespace-nowrap text-gray-500 dark:text-[#8b949e] px-6 py-4 text-sm font-mono">
                {{ formatDate(key.created_at) }}
              </td>
              <td class="whitespace-nowrap text-gray-500 dark:text-[#8b949e] px-6 py-4 text-sm font-mono">
                {{ key.last_used_at ? formatDate(key.last_used_at) : 'Never' }}
              </td>
              <td class="whitespace-nowrap text-gray-500 dark:text-[#8b949e] px-6 py-4 text-sm font-mono">
                {{ key.expires_at ? formatDate(key.expires_at) : 'Never' }}
              </td>
              <td class="whitespace-nowrap text-sm font-medium text-right pr-6">
                <div class="flex justify-end space-x-2">
                  <!-- Activate/Deactivate button -->
                  <button 
                    v-if="key.is_active"
                    @click="deactivateKey(key.id)" 
                    class="bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 flex items-center px-3 py-1.5 text-white rounded-md transition-colors shadow-sm text-xs font-medium"
                    title="Disable this API key"
                  >
                    <XCircleIcon class="w-3.5 h-3.5 mr-1" />
                    Disable
                  </button>
                  <button 
                    v-else
                    @click="activateKey(key.id)" 
                    class="bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-800 flex items-center px-3 py-1.5 text-white rounded-md transition-colors shadow-sm text-xs font-medium"
                    title="Enable this API key"
                  >
                    <CheckCircleIcon class="w-3.5 h-3.5 mr-1" />
                    Enable
                  </button>
                  
                  <!-- Rename button -->
                  <button 
                    @click="openRenameModal(key)" 
                    class="bg-primary-600 hover:bg-primary-700 dark:bg-[#14F195] dark:hover:bg-[#14F195]/80 dark:text-gray-900 flex items-center px-3 py-1.5 text-white rounded-md transition-colors shadow-sm text-xs font-medium"
                    title="Rename this API key"
                  >
                    <PencilIcon class="w-3.5 h-3.5 mr-1" />
                    Rename
                  </button>
                  
                  <!-- Delete button -->
                  <button 
                    @click="deleteKey(key.id)" 
                    class="bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 flex items-center px-3 py-1.5 text-white rounded-md transition-colors shadow-sm text-xs font-medium"
                    title="Permanently delete this API key"
                  >
                    <TrashIcon class="w-3.5 h-3.5 mr-1" />
                    Delete
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </section>
  
  <!-- Create API Key Modal -->
  <CreateApiKeyModal 
    :is-open="showCreateModal" 
    @close="closeCreateModal" 
    @created="handleKeyCreated"
  />
  
  <!-- Rename API Key Modal -->
  <div v-if="showRenameModal" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="sm:block sm:p-0 flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center">
      <!-- Background overlay -->
      <div class="fixed inset-0 transition-opacity" @click="closeRenameModal">
        <div class="dark:bg-gray-900 dark:opacity-75 absolute inset-0 bg-gray-500 opacity-75"></div>
      </div>

      <!-- Modal panel -->
      <div class="bg-white dark:bg-[#0d1117] sm:my-8 sm:align-middle sm:max-w-lg sm:w-full inline-block overflow-hidden text-left align-bottom transition-all transform rounded-lg shadow-xl border border-gray-200 dark:border-[#30363d]">
        <div class="bg-white dark:bg-[#0d1117] sm:p-6 sm:pb-4 px-4 pt-5 pb-4">
          <div class="sm:flex sm:items-start">
            <div class="bg-primary-100 dark:bg-[#14F195]/10 sm:mx-0 sm:h-10 sm:w-10 flex items-center justify-center flex-shrink-0 w-12 h-12 mx-auto rounded-full">
              <PencilIcon class="text-primary-600 dark:text-[#14F195] w-6 h-6" />
            </div>
            <div class="sm:mt-0 sm:ml-4 sm:text-left mt-3 text-center">
              <h3 class="dark:text-white font-heading text-lg font-medium leading-6 text-gray-900">
                Rename API Key
              </h3>
              <div class="mt-2">
                <p class="text-gray-500 dark:text-[#8b949e] text-sm">
                  Enter a new name for your API key.
                </p>
              </div>
            </div>
          </div>
          
          <div class="mt-5">
            <form @submit.prevent="renameKey">
              <div class="mb-4">
                <label for="new-key-name" class="block text-sm font-medium text-gray-700 dark:text-[#c9d1d9]">
                  New Key Name
                </label>
                <input 
                  type="text" 
                  id="new-key-name" 
                  v-model="newKeyName" 
                  class="input mt-1" 
                  placeholder="e.g. Production Key"
                  required
                />
              </div>
              
              <!-- Error message -->
              <div v-if="renameError" class="dark:text-red-400 mb-4 text-sm text-red-600">
                {{ renameError }}
              </div>
              
              <!-- Buttons -->
              <div class="sm:mt-4 sm:flex sm:flex-row-reverse mt-5">
                <button 
                  type="submit" 
                  class="btn-primary sm:ml-3 sm:w-auto w-full"
                  :disabled="renameLoading"
                >
                  <span v-if="renameLoading">Saving...</span>
                  <span v-else>Save</span>
                </button>
                <button 
                  type="button" 
                  class="btn-secondary sm:mt-0 sm:w-auto w-full mt-3" 
                  @click="closeRenameModal"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Confirmation Modal for Deactivating -->
  <div v-if="showConfirmModal" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="sm:block sm:p-0 flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center">
      <!-- Background overlay -->
      <div class="fixed inset-0 transition-opacity" @click="closeConfirmModal">
        <div class="dark:bg-gray-900 dark:opacity-75 absolute inset-0 bg-gray-500 opacity-75"></div>
      </div>

      <!-- Modal panel -->
      <div class="bg-white dark:bg-[#0d1117] sm:my-8 sm:align-middle sm:max-w-lg sm:w-full inline-block overflow-hidden text-left align-bottom transition-all transform rounded-lg shadow-xl border border-gray-200 dark:border-[#30363d]">
        <div class="bg-white dark:bg-[#0d1117] sm:p-6 sm:pb-4 px-4 pt-5 pb-4">
          <div class="sm:flex sm:items-start">
            <div class="dark:bg-red-900 sm:mx-0 sm:h-10 sm:w-10 flex items-center justify-center flex-shrink-0 w-12 h-12 mx-auto bg-red-100 rounded-full">
              <ExclamationTriangleIcon class="dark:text-red-400 w-6 h-6 text-red-600" />
            </div>
            <div class="sm:mt-0 sm:ml-4 sm:text-left mt-3 text-center">
              <h3 class="dark:text-white font-heading text-lg font-medium leading-6 text-gray-900">
                Disable API Key
              </h3>
              <div class="mt-2">
                <p class="text-gray-500 dark:text-[#8b949e] text-sm">
                  Are you sure you want to disable this API key? Any applications using this key will no longer be able to access the API until you enable it again.
                </p>
              </div>
            </div>
          </div>
          
          <!-- Error message -->
          <div v-if="confirmError" class="dark:text-red-400 mt-4 text-sm text-red-600">
            {{ confirmError }}
          </div>
          
          <!-- Buttons -->
          <div class="sm:mt-4 sm:flex sm:flex-row-reverse mt-5">
            <button 
              type="button" 
              class="hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm inline-flex justify-center w-full px-4 py-2 text-base font-medium text-white bg-red-600 border border-transparent rounded-md shadow-sm"
              :disabled="confirmLoading"
              @click="confirmDeactivateKey"
            >
              <span v-if="confirmLoading">Disabling...</span>
              <span v-else>Disable</span>
            </button>
            <button 
              type="button" 
              class="btn-secondary sm:mt-0 sm:w-auto w-full mt-3" 
              @click="closeConfirmModal"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Confirmation Modal for Deleting -->
  <div v-if="showDeleteModal" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="sm:block sm:p-0 flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center">
      <!-- Background overlay -->
      <div class="fixed inset-0 transition-opacity" @click="closeDeleteModal">
        <div class="dark:bg-gray-900 dark:opacity-75 absolute inset-0 bg-gray-500 opacity-75"></div>
      </div>

      <!-- Modal panel -->
      <div class="bg-white dark:bg-[#0d1117] sm:my-8 sm:align-middle sm:max-w-lg sm:w-full inline-block overflow-hidden text-left align-bottom transition-all transform rounded-lg shadow-xl border border-gray-200 dark:border-[#30363d]">
        <div class="bg-white dark:bg-[#0d1117] sm:p-6 sm:pb-4 px-4 pt-5 pb-4">
          <div class="sm:flex sm:items-start">
            <div class="dark:bg-red-900 sm:mx-0 sm:h-10 sm:w-10 flex items-center justify-center flex-shrink-0 w-12 h-12 mx-auto bg-red-100 rounded-full">
              <ExclamationTriangleIcon class="dark:text-red-400 w-6 h-6 text-red-600" />
            </div>
            <div class="sm:mt-0 sm:ml-4 sm:text-left mt-3 text-center">
              <h3 class="dark:text-white font-heading text-lg font-medium leading-6 text-gray-900">
                Delete API Key
              </h3>
              <div class="mt-2">
                <p class="text-gray-500 dark:text-[#8b949e] text-sm">
                  Are you sure you want to permanently delete this API key? This action cannot be undone and any applications using this key will no longer be able to access the API.
                </p>
              </div>
            </div>
          </div>
          
          <!-- Error message -->
          <div v-if="deleteError" class="dark:text-red-400 mt-4 text-sm text-red-600">
            {{ deleteError }}
          </div>
          
          <!-- Buttons -->
          <div class="sm:mt-4 sm:flex sm:flex-row-reverse mt-5">
            <button 
              type="button" 
              class="hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm inline-flex justify-center w-full px-4 py-2 text-base font-medium text-white bg-red-600 border border-transparent rounded-md shadow-sm"
              :disabled="deleteLoading"
              @click="confirmDeleteKey"
            >
              <span v-if="deleteLoading">Deleting...</span>
              <span v-else>Delete</span>
            </button>
            <button 
              type="button" 
              class="btn-secondary sm:mt-0 sm:w-auto w-full mt-3" 
              @click="closeDeleteModal"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useApiKeysStore } from '@/stores/apiKeys'
import {
  PlusIcon,
  KeyIcon,
  PencilIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  CheckCircleIcon,
  TrashIcon
} from '@heroicons/vue/24/outline'
import CreateApiKeyModal from '@/components/api-keys/CreateApiKeyModal.vue'

// Stores
const apiKeysStore = useApiKeysStore()

// State
const loading = ref(true)
const error = ref('')
const showCreateModal = ref(false)
const showRenameModal = ref(false)
const showConfirmModal = ref(false)
const showDeleteModal = ref(false)
const selectedKeyId = ref('')
const newKeyName = ref('')
const renameLoading = ref(false)
const renameError = ref('')
const confirmLoading = ref(false)
const confirmError = ref('')
const deleteLoading = ref(false)
const deleteError = ref('')

// Computed
const apiKeys = computed(() => apiKeysStore.apiKeys)

// Methods
async function fetchApiKeys() {
  loading.value = true
  error.value = ''
  
  // Start tracking when we begin loading
  const loadStartTime = Date.now()
  
  try {
    await apiKeysStore.fetchApiKeys()
  } catch (err) {
    console.error('Error fetching API keys:', err)
    error.value = 'Failed to fetch API keys'
  } finally {
    // Calculate how long the loading has been shown already
    const loadingDuration = Date.now() - loadStartTime
    
    // Ensure the loading state shows for at least 600ms total for a smooth transition
    // This creates a consistent shimmer effect even if the data loads very quickly
    const minLoadingTime = 600
    
    if (loadingDuration < minLoadingTime) {
      setTimeout(() => {
        loading.value = false
      }, minLoadingTime - loadingDuration)
    } else {
      loading.value = false
    }
  }
}

function formatDate(dateString: string) {
  const date = new Date(dateString)
  return date.toLocaleDateString([], { year: 'numeric', month: 'short', day: 'numeric' })
}

function openCreateModal() {
  showCreateModal.value = true
}

function closeCreateModal() {
  showCreateModal.value = false
}

function handleKeyCreated() {
  fetchApiKeys()
}

function openRenameModal(key: { id: string, name: string }) {
  selectedKeyId.value = key.id
  newKeyName.value = key.name
  showRenameModal.value = true
}

function closeRenameModal() {
  showRenameModal.value = false
  selectedKeyId.value = ''
  newKeyName.value = ''
  renameError.value = ''
}

async function renameKey() {
  if (!selectedKeyId.value) return
  
  renameLoading.value = true
  renameError.value = ''
  
  try {
    const success = await apiKeysStore.updateKeyName(selectedKeyId.value, newKeyName.value)
    
    if (success) {
      closeRenameModal()
      // Refresh the API keys list
      await fetchApiKeys()
    } else {
      renameError.value = apiKeysStore.error || 'Failed to rename API key'
    }
  } catch (err) {
    console.error('Error renaming API key:', err)
    renameError.value = 'An unexpected error occurred'
  } finally {
    renameLoading.value = false
  }
}

function deactivateKey(keyId: string) {
  selectedKeyId.value = keyId
  showConfirmModal.value = true
}

function closeConfirmModal() {
  showConfirmModal.value = false
  selectedKeyId.value = ''
  confirmError.value = ''
}

async function confirmDeactivateKey() {
  if (!selectedKeyId.value) return
  
  confirmLoading.value = true
  confirmError.value = ''
  
  try {
    const success = await apiKeysStore.deactivateKey(selectedKeyId.value)
    
    if (success) {
      closeConfirmModal()
      // Refresh the API keys list
      await fetchApiKeys()
    } else {
      confirmError.value = apiKeysStore.error || 'Failed to disable API key'
    }
  } catch (err) {
    console.error('Error disabling API key:', err)
    confirmError.value = 'An unexpected error occurred'
  } finally {
    confirmLoading.value = false
  }
}

function activateKey(keyId: string) {
  loading.value = true
  error.value = ''
  
  apiKeysStore.activateKey(keyId)
    .then(success => {
      if (success) {
        // Refresh the API keys list
        return fetchApiKeys()
      } else {
        error.value = apiKeysStore.error || 'Failed to enable API key'
      }
    })
    .catch(err => {
      console.error('Error enabling API key:', err)
      error.value = 'An unexpected error occurred'
    })
    .finally(() => {
      loading.value = false
    })
}

function deleteKey(keyId: string) {
  selectedKeyId.value = keyId
  showDeleteModal.value = true
}

function closeDeleteModal() {
  showDeleteModal.value = false
  selectedKeyId.value = ''
  deleteError.value = ''
}

async function confirmDeleteKey() {
  if (!selectedKeyId.value) return
  
  deleteLoading.value = true
  deleteError.value = ''
  
  try {
    const success = await apiKeysStore.deleteKey(selectedKeyId.value)
    
    if (success) {
      closeDeleteModal()
      await fetchApiKeys()
    } else {
      deleteError.value = apiKeysStore.error || 'Failed to delete API key'
    }
  } catch (err) {
    console.error('Error deleting API key:', err)
    deleteError.value = 'An unexpected error occurred'
  } finally {
    deleteLoading.value = false
  }
}

// Lifecycle hooks
onMounted(() => {
  // Start with loading=true to show shimmer immediately without flashing "No API Keys"
  loading.value = true
  // Clear any existing API keys data
  apiKeysStore.$reset()
  
  // Add a delay to wait for the transition animation
  setTimeout(() => {
    fetchApiKeys()
  }, 400)
})
</script> 
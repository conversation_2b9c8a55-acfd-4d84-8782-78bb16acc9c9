<template>
    <!-- Settings Section -->
    <section id="settings" class="mb-12">
        <div class="flex items-center justify-between mb-6">
            <h2 class="font-heading text-2xl font-semibold text-gray-800 dark:text-[#c9d1d9]">Settings</h2>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="bg-white dark:bg-[#0d1117] p-6 rounded-lg shadow border border-gray-200 dark:border-[#1c2129]">
            <div class="animate-pulse space-y-6">
                <!-- Tabs Loading -->
                <div class="flex space-x-4">
                    <div class="w-20 h-8 bg-gray-200 dark:bg-[#1c2129] rounded"></div>
                    <div class="w-20 h-8 bg-gray-200 dark:bg-[#1c2129] rounded"></div>
                </div>
                
                <!-- Profile Loading -->
                <div class="md:flex-row flex flex-col">
                    <div class="md:w-1/3 md:mb-0 mb-6">
                        <div class="flex flex-col items-center">
                            <div class="w-32 h-32 bg-gray-200 dark:bg-[#1c2129] rounded-full"></div>
                        </div>
                    </div>
                    <div class="md:w-2/3 md:pl-8 space-y-4">
                        <div class="h-10 bg-gray-200 dark:bg-[#1c2129] rounded"></div>
                        <div class="h-10 bg-gray-200 dark:bg-[#1c2129] rounded"></div>
                        <div class="h-10 bg-gray-200 dark:bg-[#1c2129] rounded"></div>
                        <div class="h-10 bg-gray-200 dark:bg-[#1c2129] rounded"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Error State -->
        <div v-else-if="error" class="dark:bg-red-900/30 dark:border-red-900/50 dark:text-red-200 px-4 py-3 mb-6 text-red-700 bg-red-100 border border-red-400 rounded-lg" role="alert">
            <span class="sm:inline block">{{ error }}</span>
            <button 
                @click="fetchProfile" 
                class="hover:text-red-800 dark:text-red-300 dark:hover:text-red-100 mt-2 text-sm text-red-600"
            >
                Try again
            </button>
        </div>

        <div v-else class="bg-white dark:bg-[#0d1117] overflow-hidden rounded-lg shadow border border-gray-200 dark:border-[#1c2129]">
            <!-- Tabs -->
            <div class="flex border-b border-gray-200 dark:border-[#1c2129]">
                <button 
                    v-for="tab in tabs" 
                    :key="tab.id"
                    @click="activeTab = tab.id"
                    :class="[
                        'px-6 py-4 text-sm font-medium',
                        activeTab === tab.id 
                            ? 'border-b-2 border-primary-500 text-primary-600 dark:text-[#14F195] dark:border-[#14F195]' 
                            : 'text-gray-600 dark:text-[#8b949e] hover:text-gray-800 dark:hover:text-[#c9d1d9]'
                    ]"
                >
                    {{ tab.name }}
                </button>
            </div>

            <!-- Tab Content with Transition -->
            <div class="relative overflow-x-hidden">
                <transition name="slide" mode="out-in">
                    <div :key="activeTab">
                        <!-- Profile Tab -->
                        <div v-if="activeTab === 'profile'" class="p-6">
                            <div class="md:flex-row flex flex-col">
                                <!-- Profile Photo Section -->
                                <div class="md:w-1/3 md:mb-0 mb-6">
                                    <div class="flex flex-col items-center">
                                        <ProfileAvatar
                                            :imageUrl="userAvatarUrl"
                                            :name="userFullName"
                                            :size="128"
                                            :borderWidth="2"
                                            alt="User profile"
                                            class="mb-4"
                                        />
                                    </div>
                                </div>

                                <!-- Profile Form Section -->
                                <div class="md:w-2/3 md:pl-8">
                                    <form @submit.prevent="handleUpdateProfile" class="space-y-4">
                                        <div>
                                            <label for="full-name" class="block text-sm font-medium text-gray-700 dark:text-[#c9d1d9]">
                                                Full Name
                                            </label>
                                            <div class="relative mt-1 rounded-md shadow-sm">
                                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                    <UserIcon class="w-5 h-5 text-gray-400" />
                                                </div>
                                                <input 
                                                    id="full-name" 
                                                    type="text" 
                                                    v-model="profileData.full_name"
                                                    class="input pl-10"
                                                    placeholder="Your full name"
                                                />
                                            </div>
                                        </div>

                                        <div>
                                            <label for="email" class="block text-sm font-medium text-gray-700 dark:text-[#c9d1d9]">
                                                Email Address
                                            </label>
                                            <div class="relative mt-1 rounded-md shadow-sm">
                                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                    <EnvelopeIcon class="w-5 h-5 text-gray-400" />
                                                </div>
                                                <input 
                                                    id="email" 
                                                    type="email" 
                                                    v-model="profileData.email"
                                                    class="input pl-10"
                                                    disabled
                                                />
                                            </div>
                                        </div>

                                        <div>
                                            <label for="company" class="block text-sm font-medium text-gray-700 dark:text-[#c9d1d9]">
                                                Company (Optional)
                                            </label>
                                            <div class="relative mt-1 rounded-md shadow-sm">
                                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                    <BuildingOfficeIcon class="w-5 h-5 text-gray-400" />
                                                </div>
                                                <input 
                                                    id="company" 
                                                    type="text" 
                                                    v-model="profileData.company_name"
                                                    class="input pl-10"
                                                    placeholder="Your company name"
                                                />
                                            </div>
                                        </div>

                                        <div class="pt-4">
                                            <button 
                                                type="submit" 
                                                class="btn-primary inline-flex items-center"
                                                :disabled="updateLoading"
                                            >
                                                <CheckIcon v-if="!updateLoading" class="w-5 h-5 mr-2" />
                                                <ArrowPathIcon v-else class="animate-spin w-5 h-5 mr-2" />
                                                {{ updateLoading ? 'Saving...' : 'Save Changes' }}
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Security Tab -->
                        <div v-if="activeTab === 'security'" class="p-6">
                            <h3 class="mb-4 text-lg font-medium text-gray-800 dark:text-[#c9d1d9]">Security Settings</h3>
                            
                            <form @submit.prevent="handleChangePassword" class="space-y-4">
                                <div>
                                    <label for="current-password" class="block text-sm font-medium text-gray-700 dark:text-[#c9d1d9]">
                                        Current Password
                                    </label>
                                    <div class="relative mt-1 rounded-md shadow-sm">
                                        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                            <LockClosedIcon class="w-5 h-5 text-gray-400" />
                                        </div>
                                        <input 
                                            id="current-password" 
                                            type="password" 
                                            v-model="passwordData.currentPassword"
                                            class="input pl-10"
                                            required
                                        />
                                    </div>
                                </div>

                                <div>
                                    <label for="new-password" class="block text-sm font-medium text-gray-700 dark:text-[#c9d1d9]">
                                        New Password
                                    </label>
                                    <div class="relative mt-1 rounded-md shadow-sm">
                                        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                            <KeyIcon class="w-5 h-5 text-gray-400" />
                                        </div>
                                        <input 
                                            id="new-password" 
                                            type="password" 
                                            v-model="passwordData.newPassword"
                                            class="input pl-10"
                                            required
                                        />
                                    </div>
                                </div>

                                <div>
                                    <label for="confirm-password" class="block text-sm font-medium text-gray-700 dark:text-[#c9d1d9]">
                                        Confirm New Password
                                    </label>
                                    <div class="relative mt-1 rounded-md shadow-sm">
                                        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                            <KeyIcon class="w-5 h-5 text-gray-400" />
                                        </div>
                                        <input 
                                            id="confirm-password" 
                                            type="password" 
                                            v-model="passwordData.confirmPassword"
                                            class="input pl-10"
                                            required
                                        />
                                    </div>
                                </div>

                                <div class="pt-4">
                                    <button 
                                        type="submit" 
                                        class="btn-primary inline-flex items-center"
                                        :disabled="passwordLoading"
                                    >
                                        <ShieldCheckIcon v-if="!passwordLoading" class="w-5 h-5 mr-2" />
                                        <ArrowPathIcon v-else class="animate-spin w-5 h-5 mr-2" />
                                        {{ passwordLoading ? 'Updating...' : 'Update Password' }}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </transition>
            </div>
        </div>
    </section>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useSettingsStore } from '@/stores/settings'
import { useAuthStore } from '@/stores/auth'
import ProfileAvatar from '@/components/ProfileAvatar.vue'
import {
    UserIcon,
    EnvelopeIcon,
    BuildingOfficeIcon,
    CheckIcon,
    ArrowPathIcon,
    LockClosedIcon,
    KeyIcon,
    ShieldCheckIcon
} from '@heroicons/vue/24/outline'

// Stores
const settingsStore = useSettingsStore()
const authStore = useAuthStore()

// State
const activeTab = ref('profile')
const loading = ref(false)
const error = ref('')
const updateLoading = ref(false)
const passwordLoading = ref(false)

// Computed
const darkMode = computed(() => settingsStore.darkMode)

// User avatar and name from OAuth metadata
const userAvatarUrl = computed(() => {
    return authStore.user?.user_metadata?.avatar_url || ''
})

const userFullName = computed(() => {
    return authStore.user?.user_metadata?.full_name ||
           profileData.value.full_name ||
           authStore.user?.email?.split('@')[0] ||
           'User'
})

const tabs = [
    { id: 'profile', name: 'Profile' },
    { id: 'security', name: 'Security' }
]

const profileData = ref({
    full_name: '',
    company_name: '',
    email: '',
    default_pricing_model: 'credits'
})

const passwordData = ref({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
})

// Methods
async function fetchProfile() {
    loading.value = true
    error.value = ''

    try {
        await settingsStore.fetchProfile()
        if (settingsStore.profile) {
            profileData.value = {
                full_name: settingsStore.profile.full_name || '',
                company_name: settingsStore.profile.company_name || '',
                email: settingsStore.profile.email || '',
                default_pricing_model: settingsStore.profile.default_pricing_model || 'credits'
            }
        }
    } catch (err) {
        console.error('Error fetching profile:', err)
        error.value = 'Failed to fetch profile'
    } finally {
        loading.value = false
    }
}

async function handleUpdateProfile() {
    updateLoading.value = true
    error.value = ''

    try {
        await settingsStore.updateProfile(profileData.value)
    } catch (err) {
        console.error('Error updating profile:', err)
        error.value = 'Failed to update profile'
    } finally {
        updateLoading.value = false
    }
}

async function handleChangePassword() {
    if (passwordData.value.newPassword !== passwordData.value.confirmPassword) {
        error.value = 'New passwords do not match'
        return
    }

    passwordLoading.value = true
    error.value = ''

    try {
        await authStore.updatePassword(passwordData.value.newPassword)
        passwordData.value = {
            currentPassword: '',
            newPassword: '',
            confirmPassword: ''
        }
    } catch (err) {
        console.error('Error changing password:', err)
        error.value = 'Failed to change password'
    } finally {
        passwordLoading.value = false
    }
}

// Lifecycle hooks
onMounted(async () => {
    await fetchProfile()
})
</script>

<style scoped>
.input {
    @apply dark:bg-[#0d1117] dark:text-white dark:border-[#1c2129] dark:placeholder-gray-400 shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-[#14F195] dark:focus:border-[#14F195] block w-full sm:text-sm border-gray-300 rounded-md;
}

.btn-primary {
    @apply inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-[#14F195]/90 dark:text-[#0d1117] dark:hover:bg-[#14F195] dark:focus:ring-[#14F195];
}

.btn-secondary {
    @apply inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-[#0d1117] dark:text-gray-300 dark:border-[#1c2129] dark:hover:bg-[#1c2129] dark:focus:ring-[#14F195];
}

/* Slide transition */
.slide-enter-active,
.slide-leave-active {
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.slide-enter-from {
    opacity: 0;
    transform: translateX(40px);
}

.slide-leave-to {
    opacity: 0;
    transform: translateX(-40px);
}
</style>
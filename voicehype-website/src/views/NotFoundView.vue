<template>
  <div class="bg-white dark:bg-[#0d1117] min-h-screen flex flex-col items-center justify-center px-4 py-16">
    <div class="max-w-2xl text-center">
      <!-- Error code -->
      <div class="flex items-center justify-center mb-6">
        <div class="text-9xl font-bold font-clash bg-gradient-to-r from-primary-600 to-secondary-500 dark:from-[#14F195] dark:to-primary-600 text-transparent bg-clip-text">404</div>
      </div>
      
      <!-- Error message -->
      <h1 class="font-clash dark:text-white mb-4 text-4xl font-bold text-gray-800">Page Not Found</h1>
      <p class="text-gray-600 dark:text-[#8b949e] text-lg mb-8">
        We couldn't find the page you were looking for. It might have been moved, deleted, or never existed.
      </p>
      
      <!-- SVG illustration -->
      <div class="max-w-xs mx-auto mb-10">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" class="w-full h-auto text-primary-600 dark:text-[#14F195]" stroke-width="1">
          <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z" />
          <path stroke-linecap="round" stroke-linejoin="round" d="M9.879 16.121A3 3 0 1012.015 19.5 2.993 2.993 0 009.879 16.12z" />
        </svg>
      </div>
      
      <!-- Action buttons -->
      <div class="sm:flex-row flex flex-col justify-center gap-4">
        <router-link 
          to="/" 
          class="px-6 py-3 font-medium text-white bg-primary-600 hover:bg-primary-700 dark:bg-[#14F195]  dark:hover:bg-[#0cdb84] rounded-lg transition-colors duration-200"
        >
          <p class="dark:text-[#0d1117]">Back to Home</p>
        </router-link>
        
        <router-link 
          to="/app/dashboard" 
          v-if="authenticated"
          class="px-6 py-3 font-medium text-gray-700 dark:text-gray-300 bg-gray-100 hover:bg-gray-200 dark:bg-[#21262d] dark:hover:bg-[#30363d] border border-gray-300 dark:border-[#30363d] rounded-lg transition-colors duration-200"
        >
          Go to Dashboard
        </router-link>
        
        <a 
          href="mailto:<EMAIL>" 
          class="px-6 py-3 font-medium text-gray-700 dark:text-gray-300 bg-gray-100 hover:bg-gray-200 dark:bg-[#21262d] dark:hover:bg-[#30363d] border border-gray-300 dark:border-[#30363d] rounded-lg transition-colors duration-200"
        >
          Contact Support
        </a>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAuthStore } from '@/stores/auth'

// Check if user is authenticated to conditionally show dashboard link
const authStore = useAuthStore()
const authenticated = computed(() => authStore.isAuthenticated)
</script>

<style scoped>
/* Add custom animation for the error icon if desired */
svg {
  filter: drop-shadow(0 0 8px rgba(20, 241, 149, 0.2));
}
</style> 
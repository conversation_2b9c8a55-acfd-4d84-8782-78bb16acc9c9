<template>
  <div class="max-w-7xl sm:px-6 lg:px-8 px-4 py-8 mx-auto">
    <!-- Header -->
    <div class="mb-8">
      <h1 class="dark:text-white mb-2 text-3xl font-bold text-gray-900">Latest Prices</h1>
      <p class="dark:text-gray-400 text-gray-600">
        View pricing for all available AI models and services
      </p>
      <div class="bg-blue-50 dark:bg-blue-900/20 dark:border-blue-800 p-4 mt-4 border border-blue-200 rounded-lg">
        <p class="dark:text-blue-200 text-sm text-blue-800">
          <strong>Note:</strong> These prices are important if you're using credits.
          For subscription users, your quota is used instead - these prices do not apply.
        </p>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="pricingStore.loading" class="flex items-center justify-center py-12">
      <div class="animate-spin border-primary-600 w-12 h-12 border-b-2 rounded-full"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="pricingStore.error" class="bg-red-50 dark:bg-red-900/20 dark:border-red-800 p-4 border border-red-200 rounded-lg">
      <div class="flex">
        <ExclamationTriangleIcon class="h-5 w-5 text-red-400 mt-0.5" />
        <div class="ml-3">
          <h3 class="dark:text-red-200 text-sm font-medium text-red-800">Error Loading Pricing</h3>
          <p class="dark:text-red-300 mt-1 text-sm text-red-700">{{ pricingStore.error }}</p>
          <button
            @click="pricingStore.fetchPricingData"
            class="dark:text-red-400 hover:text-red-500 dark:hover:text-red-300 mt-2 text-sm text-red-600 underline"
          >
            Try again
          </button>
        </div>
      </div>
    </div>

    <!-- Pricing Content -->
    <div v-else>
      <!-- Optimization Models Table -->
          <div class="mb-12">
            <h2 class="dark:text-white mb-6 text-2xl font-semibold text-gray-900">Optimization Models</h2>
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200 dark:divide-[#30363d]">
                <thead class="bg-gray-50 dark:bg-[#161b22]">
                  <tr>
                    <th class="dark:text-gray-400 px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                      Model
                    </th>
                    <th class="dark:text-gray-400 px-6 py-3 text-xs font-medium tracking-wider text-right text-gray-500 uppercase">
                      Input Price
                    </th>
                    <th class="dark:text-gray-400 px-6 py-3 text-xs font-medium tracking-wider text-right text-gray-500 uppercase">
                      Output Price
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white dark:bg-[#0d1117] divide-y divide-gray-200 dark:divide-[#30363d]">
                  <tr v-for="model in pricingStore.optimizationModels" :key="model.id">
                    <td class="whitespace-nowrap px-6 py-4">
                      <div class="dark:text-white text-sm font-medium text-gray-900">
                        {{ model.friendly_name }}
                      </div>
                    </td>
                    <td class="whitespace-nowrap dark:text-white px-6 py-4 text-sm text-right text-gray-900">
                      ${{ (model.inputPrice * 1000000).toFixed(2) }}
                    </td>
                    <td class="whitespace-nowrap dark:text-white px-6 py-4 text-sm text-right text-gray-900">
                      ${{ (model.outputPrice * 1000000).toFixed(2) }}
                    </td>
                  </tr>
                </tbody>
              </table>
              <div class="dark:text-gray-400 mt-2 text-xs text-gray-500">
                Prices shown per 1 million tokens
              </div>
            </div>
          </div>

      <!-- Transcription Models Table -->
      <div class="mb-12">
        <h2 class="dark:text-white mb-6 text-2xl font-semibold text-gray-900">Transcription Models</h2>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-[#30363d]">
            <thead class="bg-gray-50 dark:bg-[#161b22]">
              <tr>
                <th class="dark:text-gray-400 px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                  Model
                </th>
                <th class="dark:text-gray-400 px-6 py-3 text-xs font-medium tracking-wider text-right text-gray-500 uppercase">
                  Price
                </th>
                <th class="dark:text-gray-400 px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                  Unit
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-[#0d1117] divide-y divide-gray-200 dark:divide-[#30363d]">
              <tr v-for="model in pricingStore.transcriptionModels" :key="model.id">
                <td class="whitespace-nowrap px-6 py-4">
                  <div class="dark:text-white text-sm font-medium text-gray-900">
                    {{ model.friendly_name }}
                  </div>
                </td>
                <td class="whitespace-nowrap dark:text-white px-6 py-4 text-sm text-right text-gray-900">
                  ${{ ((model.pricing[0]?.cost_per_unit || 0) * 60).toFixed(2) }}
                </td>
                <td class="whitespace-nowrap dark:text-gray-400 px-6 py-4 text-sm text-gray-600">
                  per hour
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { usePricingStore } from '@/stores/pricing'
import { ExclamationTriangleIcon } from '@heroicons/vue/24/outline'

const pricingStore = usePricingStore()

onMounted(async () => {
  await pricingStore.fetchPricingData()
})
</script>

<template>
  <div class="flex items-center justify-center min-h-screen p-4 bg-black">
    <div class="w-full max-w-md">
      <!-- Loading State -->
      <div v-if="isLoading" class="rounded-2xl p-8 text-center bg-gray-900 border border-gray-700">
        <div class="animate-spin w-12 h-12 mx-auto mb-4 border-b-2 border-white rounded-full"></div>
        <h2 class="mb-2 text-xl font-semibold text-white">Processing Authorization...</h2>
        <p class="text-white">Please wait while we set up your VoiceHype Web Extension integration.</p>
      </div>

      <!-- Not Signed In -->
      <div v-else-if="!isSignedIn" class="rounded-2xl p-8 bg-gray-900 border border-gray-700">
        <div class="mb-6 text-center">
          <div class="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-gray-800 rounded-full">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
            </svg>
          </div>
          <h1 class="mb-2 text-2xl font-bold text-white">Authorize VoiceHype Web Extension</h1>
          <p class="mb-2 font-medium text-white">Voice to prompt productivity tool for web browsers</p>
          <p class="mb-2 text-sm text-gray-400">For {{ browserName }} Extension</p>
          <p class="text-white">Please sign in to your VoiceHype account to authorize the web extension.</p>
        </div>

        <div class="space-y-3">
          <button
            @click="signInWithGoogle"
            :disabled="signingIn === 'google'"
            class="hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center w-full px-4 py-3 text-sm font-medium text-gray-700 transition-colors bg-white border border-transparent rounded-lg"
          >
            <svg v-if="signingIn === 'google'" class="animate-spin w-4 h-4 mr-3 -ml-1 text-gray-700" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <img v-else src="https://developers.google.com/identity/images/g-logo.png" class="w-4 h-4 mr-3" alt="Google">
            Continue with Google
          </button>

          <button
            @click="signInWithGitHub"
            :disabled="signingIn === 'github'"
            class="hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center w-full px-4 py-3 text-sm font-medium text-white transition-colors bg-gray-800 border border-gray-700 rounded-lg"
          >
            <svg v-if="signingIn === 'github'" class="animate-spin w-4 h-4 mr-3 -ml-1 text-white" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <svg v-else class="w-4 h-4 mr-3 fill-current" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z" clip-rule="evenodd" />
            </svg>
            Continue with GitHub
          </button>
        </div>

        <div class="mt-6 text-center">
          <p class="text-sm text-gray-400">
            By continuing, you agree to our 
            <a href="/terms" class="text-blue-400 hover:text-blue-300">Terms of Service</a> and 
            <a href="/privacy" class="text-blue-400 hover:text-blue-300">Privacy Policy</a>
          </p>
        </div>
      </div>

      <!-- Success State -->
      <div v-else-if="authSuccess" class="rounded-2xl p-8 bg-gray-900 border border-gray-700">
        <div class="text-center">
          <div class="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-green-600 rounded-full">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
            </svg>
          </div>
          <h2 class="mb-2 text-xl font-semibold text-white">Authorization Successful!</h2>
          <p class="mb-4 text-white">Your VoiceHype Web Extension has been authorized successfully.</p>
          
          <div class="p-4 mb-4 bg-gray-800 rounded-lg">
            <div class="flex items-center mb-2">
              <img :src="user.user_metadata?.avatar_url" :alt="user.user_metadata?.full_name" class="w-8 h-8 mr-3 rounded-full">
              <div class="text-left">
                <p class="text-sm font-medium text-white">{{ user.user_metadata?.full_name }}</p>
                <p class="text-xs text-gray-400">{{ user.email }}</p>
              </div>
            </div>
          </div>

          <div class="space-y-3">
            <div class="p-3 bg-blue-900 border border-blue-700 rounded-lg">
              <p class="text-sm text-blue-200">
                <strong>API Key:</strong> {{ maskedApiKey }}
              </p>
              <p class="text-xs text-blue-300 mt-1">
                This key has been securely stored in your extension
              </p>
            </div>

            <button
              @click="closeWindow"
              class="w-full px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              Close & Return to Extension
            </button>
          </div>
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="rounded-2xl p-8 bg-gray-900 border border-gray-700">
        <div class="text-center">
          <div class="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-red-600 rounded-full">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
          </div>
          <h2 class="mb-2 text-xl font-semibold text-white">Authorization Failed</h2>
          <p class="mb-4 text-white">{{ error }}</p>
          
          <div class="space-y-3">
            <button
              @click="retry"
              class="w-full px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Try Again
            </button>
            
            <button
              @click="closeWindow"
              class="w-full px-4 py-2 text-sm font-medium text-gray-300 bg-gray-700 border border-transparent rounded-lg hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
            >
              Close Window
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { supabase } from '@/lib/supabase'

export default {
  name: 'WebExtensionAuthView',
  setup() {
    const router = useRouter()
    const route = useRoute()
    
    // Reactive state
    const isLoading = ref(false)
    const isSignedIn = ref(false)
    const authSuccess = ref(false)
    const error = ref(null)
    const signingIn = ref(null)
    const user = ref(null)
    const apiKey = ref(null)
    
    // Get browser name from URL params
    const browserName = computed(() => {
      const browser = route.query.browser || 'Browser'
      return browser.charAt(0).toUpperCase() + browser.slice(1)
    })
    
    // Get extension ID from URL params
    const extensionId = computed(() => route.query.extension_id)
    
    // Masked API key for display
    const maskedApiKey = computed(() => {
      if (!apiKey.value) return ''
      return `${apiKey.value.substring(0, 12)}...${apiKey.value.substring(apiKey.value.length - 8)}`
    })

    // Check if user is already signed in
    onMounted(async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession()
        if (session) {
          isSignedIn.value = true
          user.value = session.user
          await generateApiKey()
        }
      } catch (err) {
        console.error('Error checking session:', err)
      }
    })

    // Sign in with Google
    const signInWithGoogle = async () => {
      try {
        signingIn.value = 'google'
        error.value = null
        
        const redirectUrl = `${window.location.origin}/web-extension-auth?browser=${route.query.browser}&extension_id=${route.query.extension_id}`
        
        const { error: authError } = await supabase.auth.signInWithOAuth({
          provider: 'google',
          options: {
            redirectTo: redirectUrl,
            queryParams: {
              access_type: 'offline',
              prompt: 'consent'
            }
          }
        })
        
        if (authError) throw authError
        
      } catch (err) {
        console.error('Google sign in error:', err)
        error.value = err.message || 'Failed to sign in with Google'
        signingIn.value = null
      }
    }

    // Sign in with GitHub
    const signInWithGitHub = async () => {
      try {
        signingIn.value = 'github'
        error.value = null
        
        const redirectUrl = `${window.location.origin}/web-extension-auth?browser=${route.query.browser}&extension_id=${route.query.extension_id}`
        
        const { error: authError } = await supabase.auth.signInWithOAuth({
          provider: 'github',
          options: {
            redirectTo: redirectUrl
          }
        })
        
        if (authError) throw authError
        
      } catch (err) {
        console.error('GitHub sign in error:', err)
        error.value = err.message || 'Failed to sign in with GitHub'
        signingIn.value = null
      }
    }

    // Generate API key for the user
    const generateApiKey = async () => {
      try {
        isLoading.value = true

        // Check if user has active session
        const { data: { session } } = await supabase.auth.getSession()
        if (!session) {
          throw new Error('No active session found')
        }

        // Generate a friendly name for the web extension key
        const browserName = route.query.browser || 'Browser'
        const name = `${browserName} Web Extension (${new Date().toISOString().split('T')[0]})`

        // Call the create_api_key RPC function directly
        const { data: apiKeyData, error: apiKeyError } = await supabase.rpc('create_api_key', {
          p_name: name,
          p_expires_at: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()
        })

        if (apiKeyError) {
          throw apiKeyError
        }

        if (!apiKeyData || !apiKeyData.id || !apiKeyData.key_secret) {
          throw new Error('Invalid API key response')
        }

        apiKey.value = apiKeyData.key_secret
        authSuccess.value = true

        // Send credentials to extension
        await sendCredentialsToExtension()

      } catch (err) {
        console.error('Error generating API key:', err)
        error.value = err.message || 'Failed to generate API key'
      } finally {
        isLoading.value = false
        signingIn.value = null
      }
    }

    // Send credentials to extension
    const sendCredentialsToExtension = async () => {
      try {
        if (!extensionId.value) {
          console.warn('No extension ID provided')
          return
        }
        
        // Construct extension callback URL
        const callbackUrl = `chrome-extension://${extensionId.value}/auth-callback.html`
        
        // Prepare auth data
        const authData = {
          success: true,
          user: user.value,
          apiKey: apiKey.value,
          session: await supabase.auth.getSession(),
          timestamp: new Date().toISOString()
        }
        
        // Encode auth data
        const encodedData = btoa(JSON.stringify(authData))
        
        // Redirect to extension callback
        window.location.href = `${callbackUrl}?data=${encodedData}`
        
      } catch (err) {
        console.error('Error sending credentials to extension:', err)
        error.value = 'Failed to communicate with extension'
      }
    }

    // Handle auth state changes
    supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === 'SIGNED_IN' && session) {
        isSignedIn.value = true
        user.value = session.user
        await generateApiKey()
      } else if (event === 'SIGNED_OUT') {
        isSignedIn.value = false
        user.value = null
        apiKey.value = null
        authSuccess.value = false
      }
    })

    // Retry authentication
    const retry = () => {
      error.value = null
      authSuccess.value = false
      signingIn.value = null
    }

    // Close window
    const closeWindow = () => {
      // Try to close the window
      if (window.opener) {
        window.close()
      } else {
        // Fallback: redirect to extension or main site
        window.location.href = 'https://voicehype.netlify.app'
      }
    }

    return {
      isLoading,
      isSignedIn,
      authSuccess,
      error,
      signingIn,
      user,
      apiKey,
      browserName,
      maskedApiKey,
      signInWithGoogle,
      signInWithGitHub,
      retry,
      closeWindow
    }
  }
}
</script>

<style scoped>
/* Component-specific styles if needed */
</style>

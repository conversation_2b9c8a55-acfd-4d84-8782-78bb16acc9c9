<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white font-heading">
          {{ pageTitle }}
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600 dark:text-gray-400">
          {{ pageSubtitle }}
        </p>
      </div>

      <div v-if="loading" class="text-center py-8">
        <ArrowPathIcon class="w-12 h-12 text-primary-600 dark:text-primary-400 animate-spin mx-auto" />
        <p class="mt-4 text-gray-600 dark:text-gray-400">{{ loadingMessage }}</p>
      </div>

      <div v-else-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
        <span class="block sm:inline">{{ error }}</span>
        <p class="mt-2">
          <router-link to="/login" class="font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400">
            Return to login
          </router-link>
        </p>
      </div>

      <div v-else-if="success" class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
        <span class="block sm:inline">{{ success }}</span>
        <p class="mt-2">
          <router-link to="/login" class="font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400" v-if="!isAuthenticated">
            Continue to login
          </router-link>
          <router-link to="/app/dashboard" class="font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400" v-else>
            Go to dashboard
          </router-link>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { supabase } from '@/lib/supabase'
import { useAuthStore } from '@/stores/auth'
import { ArrowPathIcon } from '@heroicons/vue/24/outline'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

const loading = ref(true)
const error = ref('')
const success = ref('')
const isOAuthCallback = ref(false)

// Computed properties for dynamic content
const isAuthenticated = computed(() => authStore.isAuthenticated)
const pageTitle = computed(() => isOAuthCallback.value ? 'Completing authentication' : 'Verifying your email')
const pageSubtitle = computed(() => isOAuthCallback.value ? 'Please wait while we complete your authentication...' : 'Please wait while we verify your email address...')
const loadingMessage = computed(() => isOAuthCallback.value ? 'Completing authentication...' : 'Verifying your email...')

onMounted(async () => {
  try {
    console.log('Auth callback mounted, URL:', window.location.href)

    // Check if we're on the wrong callback URL (auth/v1/callback instead of auth/callback)
    // This happens when the Supabase auth service doesn't correctly redirect to the client-side URL
    if (window.location.pathname.includes('/auth/v1/callback')) {
      console.log('Detected incorrect callback URL, redirecting to app')
      // Redirect to the app
      window.location.href = `${window.location.origin}/app`
      return
    }
    // Log hash parameters without exposing sensitive data
    const hashFragment = window.location.hash
    if (hashFragment) {
      console.log('Hash fragment present, contains:',
        hashFragment.includes('access_token') ? 'access_token' : 'no access_token',
        hashFragment.includes('refresh_token') ? 'refresh_token' : 'no refresh_token',
        hashFragment.includes('provider_token') ? 'provider_token' : 'no provider_token',
        hashFragment.includes('type') ? `type=${new URLSearchParams(hashFragment.substring(1)).get('type')}` : 'no type'
      )
    }
    // Get the hash fragment from the URL
    const hashParams = new URLSearchParams(window.location.hash.substring(1))
    const accessToken = hashParams.get('access_token')
    const refreshToken = hashParams.get('refresh_token')
    const type = hashParams.get('type')
    const providerToken = hashParams.get('provider_token')

    // Check if this is an OAuth callback
    // OAuth callbacks can come in two forms:
    // 1. With a code parameter in the URL (older flow)
    // 2. With access_token and provider_token in the hash fragment (newer flow)
    const code = route.query.code as string
    const isOAuthFlow = code || (accessToken && providerToken)

    if (isOAuthFlow) {
      // This is an OAuth callback
      isOAuthCallback.value = true
      console.log('Detected OAuth callback')

      // The Supabase client will automatically exchange the code for a session
      // We just need to check if the session was created successfully
      const { data: { session }, error: sessionError } = await supabase.auth.getSession()

      if (sessionError) {
        console.error('Session error:', sessionError)
        error.value = sessionError.message
        loading.value = false
        return
      }

      if (!session) {
        console.error('No session found after OAuth callback')
        error.value = 'Failed to authenticate. Please try again.'
        loading.value = false
        return
      }

      // Set the user in the auth store
      authStore.user = session.user
      console.log('User authenticated via OAuth:', session.user.email)

      // Check if this is a VS Code auth flow
      const isVSCodeAuth = route.query.vscode_auth === 'true'

      if (isVSCodeAuth) {
        // This is a VS Code auth flow - redirect back to vscode-auth with all parameters
        console.log('VS Code auth flow detected, redirecting back to vscode-auth')

        // Build the redirect URL with all the original parameters
        const vsCodeAuthUrl = new URL(`${window.location.origin}/vscode-auth`)

        // Preserve all the VS Code auth parameters
        const preserveParams = ['state', 'redirect_uri', 'product_name', 'uri_scheme']
        preserveParams.forEach(param => {
          const value = route.query[param] as string
          if (value) {
            vsCodeAuthUrl.searchParams.append(param, value)
          }
        })

        // Redirect immediately to the VS Code auth view
        window.location.href = vsCodeAuthUrl.toString()
        return
      }

      success.value = 'Authentication successful! You are now signed in.'

      // Redirect to dashboard after a short delay (normal auth flow)
      setTimeout(() => {
        router.push('/app/dashboard')
      }, 1500)

      return
    }

    // If not OAuth, proceed with email verification flow
    console.log('Proceeding with email verification flow')

    if (!accessToken || !refreshToken) {
      error.value = 'Invalid verification link. Please try again or request a new verification email.'
      loading.value = false
      return
    }

    // Set the session in Supabase
    const { error: sessionError } = await supabase.auth.setSession({
      access_token: accessToken,
      refresh_token: refreshToken
    })

    if (sessionError) {
      error.value = sessionError.message
      loading.value = false
      return
    }

    // Get the current session to verify it worked
    const { data: { session } } = await supabase.auth.getSession()

    if (!session) {
      error.value = 'Failed to verify your email. Please try again or request a new verification email.'
      loading.value = false
      return
    }

    // Set the user in the auth store
    authStore.user = session.user

    success.value = 'Your email has been verified successfully! You are now signed in.'

    // Redirect to dashboard after a short delay if the user is already authenticated
    if (type === 'signup' || type === 'recovery' || !type) {
      // If type is not specified, we still want to redirect to dashboard
      // This handles cases where the type parameter might be missing
      setTimeout(() => {
        router.push('/app/dashboard')
      }, 1500)
    }
  } catch (err) {
    console.error('Error in auth callback:', err)
    error.value = 'An unexpected error occurred. Please try again.'
  } finally {
    loading.value = false
  }
})
</script>
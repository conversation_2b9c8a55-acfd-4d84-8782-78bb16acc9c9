<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white font-heading">
          Set New Password
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600 dark:text-gray-400">
          Enter your new password below.
        </p>
      </div>
      
      <div v-if="loading && !error && !success" class="text-center py-8">
        <ArrowPathIcon class="w-12 h-12 text-primary-600 dark:text-primary-400 animate-spin mx-auto" />
        <p class="mt-4 text-gray-600 dark:text-gray-400">Verifying your session...</p>
      </div>
      
      <div v-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
        <span class="block sm:inline">{{ error }}</span>
        <p class="mt-2">
          <router-link to="/reset-password" class="font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400">
            Request a new password reset
          </router-link>
        </p>
      </div>
      
      <div v-if="success" class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
        <span class="block sm:inline">{{ success }}</span>
        <p class="mt-2">
          <router-link to="/login" class="font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400">
            Continue to login
          </router-link>
        </p>
      </div>
      
      <!-- Set New Password Form -->
      <form v-if="!loading && !error && !success" class="mt-8 space-y-6" @submit.prevent="handleUpdatePassword">
        <div>
          <label for="new-password" class="sr-only">New password</label>
          <input 
            id="new-password" 
            name="password" 
            type="password" 
            autocomplete="new-password" 
            required 
            v-model="password"
            class="input rounded-md"
            placeholder="New password"
          >
        </div>
        
        <div>
          <label for="confirm-password" class="sr-only">Confirm password</label>
          <input 
            id="confirm-password" 
            name="confirmPassword" 
            type="password" 
            autocomplete="new-password" 
            required 
            v-model="confirmPassword"
            class="input rounded-md"
            placeholder="Confirm password"
          >
        </div>

        <div>
          <button 
            type="submit" 
            class="btn-primary group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md"
            :disabled="formLoading"
          >
            <span class="absolute left-0 inset-y-0 flex items-center pl-3">
              <LockClosedIcon class="w-5 h-5 text-primary-500 group-hover:text-primary-400" aria-hidden="true" />
            </span>
            <span v-if="formLoading">Updating...</span>
            <span v-else>Update password</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { LockClosedIcon, ArrowPathIcon } from '@heroicons/vue/24/outline'
import { supabase } from '@/lib/supabase'

// Store and router
const authStore = useAuthStore()
const router = useRouter()

// State
const password = ref('')
const confirmPassword = ref('')
const loading = ref(true)
const formLoading = ref(false)
const error = ref('')
const success = ref('')

// Check if we have a valid session from the password reset link
onMounted(async () => {
  try {
    // Get the hash fragment from the URL
    const hashParams = new URLSearchParams(window.location.hash.substring(1))
    const accessToken = hashParams.get('access_token')
    const refreshToken = hashParams.get('refresh_token')
    const type = hashParams.get('type')
    
    if (!accessToken || !refreshToken) {
      error.value = 'Invalid password reset link. Please request a new password reset.'
      loading.value = false
      return
    }
    
    // Set the session in Supabase
    const { error: sessionError } = await supabase.auth.setSession({
      access_token: accessToken,
      refresh_token: refreshToken
    })
    
    if (sessionError) {
      error.value = sessionError.message
      loading.value = false
      return
    }
    
    // Get the current session to verify it worked
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      error.value = 'Failed to verify your session. Please request a new password reset.'
      loading.value = false
      return
    }
    
    // Session is valid, show the password update form
    loading.value = false
  } catch (err) {
    console.error('Error in update password view:', err)
    error.value = 'An unexpected error occurred. Please try again.'
    loading.value = false
  }
})

// Methods
async function handleUpdatePassword() {
  if (formLoading.value) return
  
  formLoading.value = true
  error.value = ''
  
  // Validate passwords match
  if (password.value !== confirmPassword.value) {
    error.value = 'Passwords do not match'
    formLoading.value = false
    return
  }
  
  // Validate password strength
  if (password.value.length < 8) {
    error.value = 'Password must be at least 8 characters long'
    formLoading.value = false
    return
  }
  
  try {
    const result = await authStore.updatePassword(password.value)
    
    if (result) {
      success.value = 'Your password has been updated successfully!'
      
      // Clear the form
      password.value = ''
      confirmPassword.value = ''
    } else {
      error.value = authStore.error || 'Failed to update password. Please try again.'
    }
  } catch (err) {
    console.error('Update password error:', err)
    error.value = 'An unexpected error occurred. Please try again.'
  } finally {
    formLoading.value = false
  }
}
</script> 
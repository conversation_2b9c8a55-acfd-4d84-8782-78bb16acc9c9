<template>
  <div
    class="flex min-h-screen bg-white dark:bg-[#0d1117]"
    :class="{ 'dark': darkMode }"
  >
    <!-- Sidebar backdrop -->
    <div
      v-if="sidebarOpen && !isDesktop"
      @click="sidebarOpen = false"
      class="lg:hidden fixed inset-0 z-20 transition-opacity bg-black bg-opacity-50"
    ></div>

    <!-- Sidebar -->
    <div
      :class="[
        isDesktop ? (sidebarCollapsed ? 'w-16' : 'w-64') : (sidebarOpen ? 'translate-x-0 w-64' : '-translate-x-full w-64'),
        'fixed inset-y-0 left-0 z-30 overflow-y-auto transition-all duration-300 transform bg-gray-100 dark:bg-[#0d1117] border-r border-gray-200 dark:border-[#1c2129] lg:translate-x-0 lg:static lg:inset-0'
      ]"
    >
      <!-- Conditionally render different header structures based on sidebar state -->
      <template v-if="sidebarCollapsed">
        <!-- Simple centered structure for collapsed state -->
        <div class="flex items-center justify-center px-4 py-5">
          <button
            @click="toggleSidebar"
            class="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-[#14F195] focus:outline-none lg:flex hidden justify-center items-center transition-colors duration-200"
          >
            <Bars3Icon class="flex-shrink-0 w-5 h-5" />
          </button>
        </div>
      </template>
      <template v-else>
        <!-- Original structure for expanded state -->
        <div class="flex items-center justify-between px-4 py-5">
          <div class="flex items-center overflow-hidden">
            <span class="font-heading whitespace-nowrap dark:text-white font-clash text-2xl font-semibold text-gray-800">Voice<span class="text-primary-600 dark:text-[#14F195]">Hype</span></span>
          </div>
          <button
            @click="toggleSidebar"
            class="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-[#14F195] focus:outline-none hidden lg:block transition-colors duration-200"
          >
            <XMarkIcon class="flex-shrink-0 w-5 h-5" />
          </button>
        </div>
      </template>

      <nav class="px-2 mt-6">
        <router-link
          to="/app/dashboard"
          class="flex items-center px-4 py-2.5 mt-2 text-gray-600 dark:text-gray-300 transition-colors duration-200 rounded-md"
          :class="{
            'text-primary-600 font-medium dark:text-[#14F195] dark:bg-[#14F195]/5': $route.path === '/app/dashboard',
            'hover:text-primary-600 dark:hover:text-[#14F195]': $route.path !== '/app/dashboard'
          }"
        >
          <HomeIcon class="flex-shrink-0 w-5 h-5" :class="sidebarCollapsed ? 'mx-auto' : 'mr-3'" />
          <span class="whitespace-nowrap overflow-hidden transition-all duration-300" :class="{ 'opacity-0 w-0': sidebarCollapsed, 'opacity-100 w-auto': !sidebarCollapsed }">Dashboard</span>
        </router-link>

        <router-link
          to="/app/api-keys"
          class="flex items-center px-4 py-2.5 mt-2 text-gray-600 dark:text-gray-300 transition-colors duration-200 rounded-md"
          :class="{
            'text-primary-600 font-medium dark:text-[#14F195] dark:bg-[#14F195]/5': $route.path === '/app/api-keys',
            'hover:text-primary-600 dark:hover:text-[#14F195]': $route.path !== '/app/api-keys'
          }"
        >
          <KeyIcon class="flex-shrink-0 w-5 h-5" :class="sidebarCollapsed ? 'mx-auto' : 'mr-3'" />
          <span class="whitespace-nowrap overflow-hidden transition-all duration-300" :class="{ 'opacity-0 w-0': sidebarCollapsed, 'opacity-100 w-auto': !sidebarCollapsed }">API Keys</span>
        </router-link>

        <router-link
          to="/app/usage"
          class="flex items-center px-4 py-2.5 mt-2 text-gray-600 dark:text-gray-300 transition-colors duration-200 rounded-md"
          :class="{
            'text-primary-600 font-medium dark:text-[#14F195] dark:bg-[#14F195]/5': $route.path === '/app/usage',
            'hover:text-primary-600 dark:hover:text-[#14F195]': $route.path !== '/app/usage'
          }"
        >
          <ChartBarIcon class="flex-shrink-0 w-5 h-5" :class="sidebarCollapsed ? 'mx-auto' : 'mr-3'" />
          <span class="whitespace-nowrap overflow-hidden transition-all duration-300" :class="{ 'opacity-0 w-0': sidebarCollapsed, 'opacity-100 w-auto': !sidebarCollapsed }">Usage History</span>
        </router-link>

        <router-link
          to="/app/payments"
          class="flex items-center px-4 py-2.5 mt-2 text-gray-600 dark:text-gray-300 transition-colors duration-200 rounded-md"
          :class="{
            'text-primary-600 font-medium dark:text-[#14F195] dark:bg-[#14F195]/5': $route.path === '/app/payments',
            'hover:text-primary-600 dark:hover:text-[#14F195]': $route.path !== '/app/payments'
          }"
        >
          <CreditCardIcon class="flex-shrink-0 w-5 h-5" :class="sidebarCollapsed ? 'mx-auto' : 'mr-3'" />
          <span class="whitespace-nowrap overflow-hidden transition-all duration-300" :class="{ 'opacity-0 w-0': sidebarCollapsed, 'opacity-100 w-auto': !sidebarCollapsed }">Payments</span>
        </router-link>

        <router-link
          to="/app/pricing"
          class="flex items-center px-4 py-2.5 mt-2 text-gray-600 dark:text-gray-300 transition-colors duration-200 rounded-md"
          :class="{
            'text-primary-600 font-medium dark:text-[#14F195] dark:bg-[#14F195]/5': $route.path === '/app/pricing',
            'hover:text-primary-600 dark:hover:text-[#14F195]': $route.path !== '/app/pricing'
          }"
        >
          <CurrencyDollarIcon class="flex-shrink-0 w-5 h-5" :class="sidebarCollapsed ? 'mx-auto' : 'mr-3'" />
          <span class="whitespace-nowrap overflow-hidden transition-all duration-300" :class="{ 'opacity-0 w-0': sidebarCollapsed, 'opacity-100 w-auto': !sidebarCollapsed }">Latest Prices</span>
        </router-link>

        <router-link
          to="/app/settings"
          class="flex items-center px-4 py-2.5 mt-2 text-gray-600 dark:text-gray-300 transition-colors duration-200 rounded-md"
          :class="{
            'text-primary-600 font-medium dark:text-[#14F195] dark:bg-[#14F195]/5': $route.path === '/app/settings',
            'hover:text-primary-600 dark:hover:text-[#14F195]': $route.path !== '/app/settings'
          }"
        >
          <Cog6ToothIcon class="flex-shrink-0 w-5 h-5" :class="sidebarCollapsed ? 'mx-auto' : 'mr-3'" />
          <span class="whitespace-nowrap overflow-hidden transition-all duration-300" :class="{ 'opacity-0 w-0': sidebarCollapsed, 'opacity-100 w-auto': !sidebarCollapsed }">Settings</span>
        </router-link>
      </nav>
    </div>

    <!-- Content -->
    <div class="flex-1 overflow-x-hidden overflow-y-auto">
      <!-- Navbar -->
      <header class="bg-white dark:bg-[#0d1117] shadow-sm border-b border-gray-200 dark:border-[#1c2129] mb-4">
        <div class="sm:px-6 lg:px-8 px-4 py-4 mx-auto">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <button
                @click="sidebarOpen = true"
                class="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-[#14F195] focus:outline-none lg:hidden flex items-center justify-center"
              >
                <Bars3Icon class="flex-shrink-0 w-5 h-5" />
              </button>
            </div>

            <div class="flex items-center space-x-4">
              <!-- Extension Badges -->
              <div class="sm:flex items-center hidden space-x-2">
                <a
                  href="https://marketplace.visualstudio.com/items?itemName=VoiceHype.voicehype"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <img
                    src="https://img.shields.io/badge/VS%20Code-Get-blue?style=flat-square&logo=visualstudiocode"
                    alt="VS Code Extension"
                    class="h-6"
                  />
                </a>
                <a
                  href="https://open-vsx.org/extension/VoiceHype/voicehype"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <img
                    src="https://img.shields.io/badge/OpenVSX-Get-orange?style=flat-square&logo=visualstudiocode"
                    alt="OpenVSX Extension"
                    class="h-6"
                  />
                </a>
              </div>

              <!-- Dark mode toggle -->
              <button
                @click="toggleDarkMode"
                class="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-[#14F195] focus:outline-none flex items-center justify-center transition-colors duration-200"
              >
                <SunIcon v-if="darkMode" class="flex-shrink-0 w-5 h-5" />
                <MoonIcon v-else class="flex-shrink-0 w-5 h-5" />
              </button>

              <!-- Simple Profile Button - completely rebuilt -->
              <div class="relative">
                <button
                  @click="dropdownOpen = !dropdownOpen"
                  class="focus:outline-none focus:ring-2 focus:ring-primary-500 dark:focus:ring-[#14F195] focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-[#161b22] rounded-full"
                >
                  <!-- Loading shimmer -->
                  <div v-if="profileLoading" class="animate-pulse bg-gray-200 dark:bg-[#30363d] flex items-center justify-center w-8 h-8 rounded-full">
                  </div>

                  <!-- Profile Avatar using ProfileAvatar component -->
                  <ProfileAvatar
                    v-else
                    :imageUrl="userAvatarUrl"
                    :name="userFullName"
                    :size="32"
                    :borderWidth="0"
                    alt="User profile"
                  />
                </button>

                <div
                  v-if="dropdownOpen"
                  @click="dropdownOpen = false"
                  class="fixed inset-0 z-10"
                ></div>

                <div
                  v-if="dropdownOpen"
                  class="absolute right-0 z-20 w-48 mt-2 overflow-hidden bg-white dark:bg-[#0d1117] border border-gray-200 dark:border-[#1c2129] rounded-md shadow-lg"
                >
                  <router-link
                    to="/app/settings"
                    class="text-gray-700 dark:text-gray-300 hover:bg-primary-50 hover:text-primary-600 dark:hover:bg-[#14F195]/5 dark:hover:text-[#14F195] block px-4 py-2 text-sm"
                    @click="dropdownOpen = false"
                  >
                    Profile
                  </router-link>
                  <a
                    href="#"
                    class="text-gray-700 dark:text-gray-300 hover:bg-primary-50 hover:text-primary-600 dark:hover:bg-[#14F195]/5 dark:hover:text-[#14F195] block px-4 py-2 text-sm"
                    @click="logout"
                  >
                    Logout
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <!-- Main content - Router View for nested routes -->
      <main class="container px-6 py-8 mt-4 mx-auto text-gray-800 dark:text-[#c9d1d9]">
        <Transition name="dashboard-page" mode="out-in">
          <router-view></router-view>
        </Transition>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useSettingsStore } from '@/stores/settings'
import ProfileAvatar from '@/components/ProfileAvatar.vue'
import {
  HomeIcon,
  KeyIcon,
  ChartBarIcon,
  CreditCardIcon,
  Cog6ToothIcon,
  Bars3Icon,
  XMarkIcon,
  SunIcon,
  MoonIcon,
  CurrencyDollarIcon
} from '@heroicons/vue/24/outline'

// Stores
const authStore = useAuthStore()
const settingsStore = useSettingsStore()
const router = useRouter()

// State
const sidebarOpen = ref(false)
const sidebarCollapsed = ref(false)
const dropdownOpen = ref(false)
const isDesktop = ref(window.innerWidth >= 1024)
const profileLoading = ref(true)

// Computed
const darkMode = computed(() => settingsStore.darkMode)

// User avatar and name from OAuth metadata
const userAvatarUrl = computed(() => {
  return authStore.user?.user_metadata?.avatar_url || ''
})

const userFullName = computed(() => {
  return authStore.user?.user_metadata?.full_name ||
         authStore.user?.email?.split('@')[0] ||
         'User'
})

// Methods
function toggleDarkMode() {
  settingsStore.toggleDarkMode()
}

function toggleSidebar() {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

async function logout() {
  // Just call the logout function from the auth store
  // The auth store will handle the redirection
  await authStore.logout()
}

// Handle window resize
function handleResize() {
  isDesktop.value = window.innerWidth >= 1024
  if (!isDesktop.value) {
    sidebarCollapsed.value = false
  }
}

onMounted(async () => {
  window.addEventListener('resize', handleResize)
  handleResize()

  // Create and add font stylesheets with crossOrigin attribute
  const createStylesheet = (href: string, crossOrigin = false) => {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = href;
    if (crossOrigin) {
      link.crossOrigin = 'anonymous';
    }
    document.head.appendChild(link);
    return link;
  };

  // Add preconnect for fonts
  const createPreconnect = (href: string, crossOrigin = false) => {
    const link = document.createElement('link');
    link.rel = 'preconnect';
    link.href = href;
    if (crossOrigin) {
      link.crossOrigin = 'anonymous';
    }
    document.head.appendChild(link);
    return link;
  };

  // Add preload for critical font
  const preloadFont = (href: string, crossOrigin = false) => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = href;
    link.as = 'style';
    if (crossOrigin) {
      link.crossOrigin = 'anonymous';
    }
    document.head.appendChild(link);
    return link;
  };

  // Add preconnect for Google Fonts only
  createPreconnect('https://fonts.googleapis.com');
  createPreconnect('https://fonts.gstatic.com', true);
  // Remove Fontshare preconnect
  // createPreconnect('https://api.fontshare.com', true);

  // Use self-hosted fonts instead of Fontshare
  preloadFont('/landing-page/fonts.css');

  // Explicitly preload critical font files
  const preloadFontFile = (href: string, crossOrigin: boolean = false) => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = href;
    link.as = 'font';
    link.type = 'font/woff2';
    if (crossOrigin) {
      link.crossOrigin = 'anonymous';
    }
    document.head.appendChild(link);
    return link;
  };

  // Preload the most commonly used font weights
  preloadFontFile('/landing-page/fonts/clash-display/ClashDisplay-Regular.woff2', true);
  preloadFontFile('/landing-page/fonts/clash-display/ClashDisplay-Bold.woff2', true);
  preloadFontFile('/landing-page/fonts/clash-display/ClashDisplay-Semibold.woff2', true);

  // Add self-hosted fonts CSS with higher priority
  createStylesheet('/landing-page/fonts.css');

  // Add other fonts
  createStylesheet('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@500;600;700&family=Roboto+Mono:wght@400;500&family=Space+Grotesk:wght@400;500;600;700&family=Outfit:wght@400;500;600;700&display=swap', true);
  createStylesheet('https://fonts.cdnfonts.com/css/instrument');

  // Ensure profile data is loaded when the dashboard shell is mounted
  if (!settingsStore.profile) {
    await settingsStore.fetchProfile()
  }

  profileLoading.value = false
})
</script>

<style>
/* Import self-hosted Clash Display font instead of Fontshare */
/* @import url('https://api.fontshare.com/v2/css?f[]=clash-display@600,700,800&display=swap'); */

/* Remove Fontshare @font-face declarations as they're now in fonts.css */
/* @font-face {
  font-family: 'Clash Display';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url('https://api.fontshare.com/v2/css?f[]=clash-display@600&display=swap') format('woff2');
}

@font-face {
  font-family: 'Clash Display';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url('https://api.fontshare.com/v2/css?f[]=clash-display@700&display=swap') format('woff2');
}

@font-face {
  font-family: 'Clash Display';
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url('https://api.fontshare.com/v2/css?f[]=clash-display@800&display=swap') format('woff2');
} */

/* Custom font classes */
.font-clash {
  font-family: 'Clash Display', 'Inter', system-ui, sans-serif !important;
  font-weight: 700;
}

.font-instrument {
  font-family: 'Instrument', sans-serif;
}

.font-outfit {
  font-family: 'Outfit', sans-serif;
}

.font-space {
  font-family: 'Space Grotesk', sans-serif;
}

/* Dashboard page transitions */
.dashboard-page-enter-active,
.dashboard-page-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.dashboard-page-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.dashboard-page-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}
</style>
<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white font-heading">
          Sign in to your account
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600 dark:text-gray-400">
          Or
          <router-link to="/register" class="font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400">
            create a new account
          </router-link>
        </p>
      </div>

      <div v-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
        <span class="block sm:inline">{{ error }}</span>
      </div>

      <div class="mt-8 space-y-6">
        <!-- OAuth <PERSON> -->
        <div class="space-y-3">
          <OAuthButton
            provider="google"
            :loading="googleLoading"
            @click="handleGoogleSignIn"
          />
          <OAuthButton
            provider="github"
            :loading="githubLoading"
            @click="handleGitHubSignIn"
          />

          <div class="relative my-4">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-gray-300 dark:border-gray-700"></div>
            </div>
            <div class="relative flex justify-center text-sm">
              <span class="px-2 text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-900">
                Or continue with email
              </span>
            </div>
          </div>
        </div>

        <!-- Email/Password Form -->
        <form class="space-y-6" @submit.prevent="handleLogin">
          <div class="space-y-4">
            <div>
              <label for="email-address" class="dark:text-gray-300 block mb-1 text-sm font-medium text-gray-700">Email address</label>
              <input
                id="email-address"
                name="email"
                type="email"
                autocomplete="email"
                required
                v-model="email"
                class="input w-full rounded-md"
                placeholder="<EMAIL>"
              >
            </div>
            <div>
              <label for="password" class="dark:text-gray-300 block mb-1 text-sm font-medium text-gray-700">Password</label>
              <input
                id="password"
                name="password"
                type="password"
                autocomplete="current-password"
                required
                v-model="password"
                class="input w-full rounded-md"
                placeholder="Your password"
              >
            </div>
          </div>

          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <input
                id="remember-me"
                name="remember-me"
                type="checkbox"
                v-model="rememberMe"
                class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              >
              <label for="remember-me" class="ml-2 block text-sm text-gray-900 dark:text-gray-300">
                Remember me
              </label>
            </div>

            <div class="text-sm">
              <router-link to="/reset-password" class="font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400">
                Forgot your password?
              </router-link>
            </div>
          </div>

          <div>
            <button
              type="submit"
              class="btn-primary group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md"
              :disabled="loading"
            >
              <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                <LockClosedIcon class="w-5 h-5 text-primary-500 group-hover:text-primary-400" aria-hidden="true" />
              </span>
              <span v-if="loading">Signing in...</span>
              <span v-else>Sign in</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { LockClosedIcon } from '@heroicons/vue/24/outline'
import OAuthButton from '@/components/auth/OAuthButton.vue'

// Store
const authStore = useAuthStore()
const router = useRouter()
const route = useRoute()

// State
const email = ref('')
const password = ref('')
const rememberMe = ref(false)

// Loading states
const loading = ref(false)
const googleLoading = ref(false)
const githubLoading = ref(false)
const error = ref('')

// Methods for OAuth sign-in
async function handleGoogleSignIn() {
  googleLoading.value = true
  error.value = ''

  try {
    const result = await authStore.signInWithGoogle()

    if (result && result.url) {
      // Redirect to the OAuth provider
      window.location.href = result.url
    } else {
      error.value = authStore.error || 'Failed to initiate Google sign-in. Please try again.'
    }
  } catch (err) {
    console.error('Google sign-in error:', err)
    error.value = 'An unexpected error occurred during Google sign-in.'
  } finally {
    googleLoading.value = false
  }
}

async function handleGitHubSignIn() {
  githubLoading.value = true
  error.value = ''

  try {
    const result = await authStore.signInWithGitHub()

    if (result && result.url) {
      // Redirect to the OAuth provider
      window.location.href = result.url
    } else {
      error.value = authStore.error || 'Failed to initiate GitHub sign-in. Please try again.'
    }
  } catch (err) {
    console.error('GitHub sign-in error:', err)
    error.value = 'An unexpected error occurred during GitHub sign-in.'
  } finally {
    githubLoading.value = false
  }
}

// Method for email/password sign-in
async function handleLogin() {
  loading.value = true
  error.value = ''

  try {
    const success = await authStore.login(email.value, password.value)

    if (success) {
      // Redirect to the original requested page or dashboard
      const redirectPath = route.query.redirect as string || '/app/dashboard'
      router.push(redirectPath)
    } else {
      error.value = authStore.error || 'Failed to sign in. Please check your credentials.'
    }
  } catch (err) {
    console.error('Login error:', err)
    error.value = 'An unexpected error occurred. Please try again.'
  } finally {
    loading.value = false
  }
}
</script>
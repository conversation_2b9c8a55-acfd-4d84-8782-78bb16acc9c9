<template>
  <Transition name="fade" appear>
    <div class="relative min-h-screen">
      <!-- Loading Overlay for Refresh After Upgrade -->
      <div v-if="isRefreshingAfterUpgrade" class="backdrop-blur-sm fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
        <div class="bg-white dark:bg-[#161b22] rounded-lg p-6 max-w-sm mx-4 text-center border border-gray-200 dark:border-[#30363d]">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 dark:border-[#14F195] mx-auto mb-4"></div>
          <h3 class="dark:text-white mb-2 text-lg font-medium text-gray-900">Updating Your Subscription</h3>
          <p class="dark:text-gray-400 text-sm text-gray-600">Please wait while we refresh your account data...</p>
        </div>
      </div>

      <!-- Payment Failure Banner (sticky at top) -->
      <PaymentFailureBanner 
        :subscriptionStatus="subscriptionStore.userSubscription?.status"
        :subscription="subscriptionStore.userSubscription"
        :currentPlan="subscriptionStore.currentPlan"
        :dismissible="true"
        @dismissed="handleBannerDismissed"
      />

      <div class="max-w-7xl sm:px-6 lg:px-8 px-4 mx-auto">
        <h1 class="dark:text-white mb-8 text-2xl font-bold text-gray-900">Payments</h1>
      </div>

      <!-- Subscription Section - Full Width -->
      <div class="w-full mb-12">
        <div class="max-w-7xl sm:px-6 lg:px-8 px-4 mx-auto">
          <h2 class="dark:text-white mb-6 text-2xl font-semibold text-center text-gray-900">Subscription Plans</h2>
        </div>

        <div v-if="subscriptionStore.loading" class="py-8">
          <div class="max-w-7xl sm:px-6 lg:px-8 px-4 mx-auto">
            <div class="animate-pulse flex justify-center space-x-4">
              <div class="flex-1 max-w-sm py-1 space-y-4">
                <div class="w-3/4 h-4 bg-gray-200 dark:bg-[#21262d] rounded"></div>
                <div class="w-1/2 h-4 bg-gray-200 dark:bg-[#21262d] rounded"></div>
              </div>
            </div>
          </div>
        </div>

        <div v-else-if="subscriptionStore.error" class="py-4 text-center text-red-500">
          <div class="max-w-7xl sm:px-6 lg:px-8 px-4 mx-auto">
            Failed to load subscription: {{ subscriptionStore.error }}
          </div>
        </div>

        <div v-else>
          <!-- Current Subscription Status -->
          <div v-if="subscriptionStore.hasActiveSubscription" class="max-w-7xl sm:px-6 lg:px-8 px-4 mx-auto mb-8">
            <div class="bg-white dark:bg-[#0d1117] border border-gray-200 dark:border-[#1c2129] rounded-lg shadow-sm overflow-hidden">
              <!-- Header Section -->
              <div class="p-6 border-b border-gray-200 dark:border-[#30363d]" 
                   :class="hasActiveFreeTrialPlan ? 'bg-blue-50 dark:bg-blue-900/20' : 'bg-green-50 dark:bg-green-900/20'">
                <div class="flex items-center justify-between">
                  <div>
                    <h3 class="text-xl font-semibold" 
                        :class="hasActiveFreeTrialPlan ? 'text-blue-800 dark:text-blue-300' : 'text-green-800 dark:text-green-300'">
                      {{ subscriptionStore.currentPlan?.name }} Plan
                    </h3>
                    
                    <!-- Free Trial Plan Information -->
                    <div v-if="hasActiveFreeTrialPlan">
                      <p class="dark:text-blue-400 mt-1 text-blue-600">
                        <span class="text-lg font-bold">Free</span> • 
                        Trial expires: {{ formatDate(subscriptionStore.userSubscription?.current_period_end) }}
                      </p>
                      <p class="dark:text-blue-300 mt-2 text-sm text-blue-700">
                        This gives you limited access to VoiceHype features. After your trial ends, you'll need to purchase credits to continue using VoiceHype.
                      </p>
                    </div>
                    
                    <!-- Paid Subscription Information -->
                    <div v-else>
                      <p class="dark:text-green-400 mt-1 text-green-600">
                        <span class="text-lg font-bold">${{ subscriptionStore.currentPlan?.monthly_price }}</span>/month • 
                        Next billing: {{ subscriptionStore.userSubscription?.next_billed_at ? formatDate(subscriptionStore.userSubscription.next_billed_at) : 'N/A' }}
                      </p>
                    </div>
                    
                    <!-- Show cancellation status if cancelled -->
                    <div v-if="subscriptionStore.userSubscription?.cancel_at_period_end" class="mt-2">
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300">
                        📅 Cancelling on {{ formatDate(subscriptionStore.userSubscription?.current_period_end) }}
                      </span>
                    </div>
                  </div>
                  <div class="text-right">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                          :class="hasActiveFreeTrialPlan ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'">
                      {{ hasActiveFreeTrialPlan ? 'Free Trial' : 'Active' }}
                    </span>
                    <!-- Customer Portal Button - only show for paid Paddle subscriptions -->
                    <div v-if="subscriptionStore.isPaidSubscription" class="mt-2 mb-3">
                      <CustomerPortalButton 
                        size="sm" 
                        variant="primary"
                        @success="handlePortalSuccess"
                        @error="handlePortalError"
                      />
                    </div>
                    <!-- Add Cancel/Undo Button -->
                    <div class="mt-2">
                      <!-- Show Undo button if subscription is cancelled -->
                      <UndoCancellationButton 
                        v-if="subscriptionStore.userSubscription?.cancel_at_period_end"
                        :currentPeriodEnd="formatDate(subscriptionStore.userSubscription?.current_period_end)"
                        :planName="subscriptionStore.currentPlan?.name || 'Your Plan'"
                        @success="handleUndoSuccess"
                      />
                      <!-- Show Cancel button if subscription is active and is a paid Paddle subscription -->
                      <CancelButton 
                        v-else-if="subscriptionStore.isPaidSubscription"
                        :subscription="subscriptionStore.userSubscription"
                        :planName="subscriptionStore.currentPlan?.name || 'Your Plan'"
                        :planPrice="subscriptionStore.currentPlan?.monthly_price || 0"
                        @cancelled="handleCancelSuccess"
                      />
                    </div>
                    <!-- Upgrade/Downgrade Button - only show for paid Paddle subscriptions -->
                    <div v-if="subscriptionStore.canUpgradeSubscription" class="mt-3">
                      <button 
                        @click="openUpgradeModal"
                        class="dark:text-blue-400 dark:border-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 w-full px-3 py-2 text-sm font-medium text-blue-600 transition-colors duration-200 border border-blue-600 rounded-lg"
                      >
                        Change Plan
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Quota Usage Section -->
              <div v-if="hasQuotasToShow" class="p-6">
                <h4 class="dark:text-white mb-4 text-lg font-semibold text-gray-900">Current Usage</h4>
                <div class="md:grid-cols-2 grid grid-cols-1 gap-6">
                  
                  <!-- Transcription Usage -->
                  <div v-if="transcriptionUsage.total > 0" class="bg-gray-50 dark:bg-[#161b22] p-4 rounded-lg border border-gray-200 dark:border-[#30363d]">
                    <div class="flex items-center justify-between mb-3">
                      <div class="flex items-center">
                        <div class="bg-primary-100 dark:bg-primary-900 text-primary-600 dark:text-primary-300 p-2 mr-3 rounded-full">
                          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
                          </svg>
                        </div>
                        <div>
                          <p class="dark:text-white text-sm font-medium text-gray-900">Transcription Minutes</p>
                          <p class="dark:text-gray-400 text-xs text-gray-500">This billing period</p>
                        </div>
                      </div>
                      <div class="text-right">
                        <p class="dark:text-white text-lg font-semibold text-gray-900">
                          {{ formatTranscriptionAmount(transcriptionUsage.used) }} / {{ formatTranscriptionAmount(transcriptionUsage.total) }}
                        </p>
                        <p class="dark:text-gray-400 text-sm text-gray-500">{{ Math.round(transcriptionUsage.percentage) }}% used</p>
                      </div>
                    </div>
                    <div class="w-full bg-gray-200 dark:bg-[#21262d] rounded-full h-2">
                      <div 
                        class="bg-primary-600 dark:bg-[#14F195] h-2 rounded-full transition-all duration-300"
                        :style="{ width: `${Math.min(transcriptionUsage.percentage, 100)}%` }"
                      ></div>
                    </div>
                  </div>

                  <!-- Token Usage -->
                  <div v-if="optimizationUsage.total > 0" class="bg-gray-50 dark:bg-[#161b22] p-4 rounded-lg border border-gray-200 dark:border-[#30363d]">
                    <div class="flex items-center justify-between mb-3">
                      <div class="flex items-center">
                        <div class="dark:bg-blue-900 dark:text-blue-300 p-2 mr-3 text-blue-600 bg-blue-100 rounded-full">
                          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                          </svg>
                        </div>
                        <div>
                          <p class="dark:text-white text-sm font-medium text-gray-900">AI Tokens</p>
                          <p class="dark:text-gray-400 text-xs text-gray-500">This billing period</p>
                        </div>
                      </div>
                      <div class="text-right">
                        <p class="dark:text-white text-lg font-semibold text-gray-900" v-html="formatTokensWithGreenK(optimizationUsage.used) + ' / ' + formatTokensWithGreenK(optimizationUsage.total)">
                        </p>
                        <p class="dark:text-gray-400 text-sm text-gray-500">{{ Math.round(optimizationUsage.percentage) }}% used</p>
                      </div>
                    </div>
                    <div class="w-full bg-gray-200 dark:bg-[#21262d] rounded-full h-2">
                      <div 
                        class="dark:bg-blue-400 h-2 transition-all duration-300 bg-blue-600 rounded-full"
                        :style="{ width: `${Math.min(optimizationUsage.percentage, 100)}%` }"
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Subscription Pricing Cards - Full Width -->
          <div class="w-full">
            <SubscriptionPricingCards />
          </div>
        </div>
      </div>

      <!-- Feature Comparison Section -->
      <div class="w-full mb-12">
        <div class="max-w-7xl sm:px-6 lg:px-8 px-4 mx-auto">
          <PricingFeatureComparison />
        </div>
      </div>

      <!-- Billing Management Section -->
      <div v-if="paddleCustomerStore.isPaddleCustomer" class="w-full mb-12">
        <div class="max-w-7xl sm:px-6 lg:px-8 px-4 mx-auto">
          <div class="bg-white dark:bg-[#0d1117] border border-gray-200 dark:border-[#1c2129] rounded-lg shadow-sm overflow-hidden">
            <!-- Header -->
            <div class="px-6 py-4 bg-gray-50 dark:bg-[#161b22] border-b border-gray-200 dark:border-[#30363d]">
              <div class="flex items-center justify-between">
                <div>
                  <h2 class="dark:text-white text-xl font-bold text-gray-900">Billing Management</h2>
                  <p class="dark:text-gray-400 mt-1 text-sm text-gray-600">Manage your subscription and payment methods</p>
                </div>
              </div>
            </div>

            <!-- Content -->
            <div class="p-6">
              <div class="flex justify-center">
                <CustomerPortalButton 
                  @success="handlePortalSuccess"
                  @error="handlePortalError"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Credit Balance Section -->
      <div class="w-full mb-12" data-section="credits">
        <div class="max-w-7xl sm:px-6 lg:px-8 px-4 mx-auto">
          <div class="bg-white dark:bg-[#0d1117] border border-gray-200 dark:border-[#1c2129] rounded-lg shadow-sm overflow-hidden">
            <!-- Header -->
            <div class="px-6 py-4 bg-gray-50 dark:bg-[#161b22] border-b border-gray-200 dark:border-[#30363d]">
              <div class="flex items-center justify-between">
                <div>
                  <h2 class="dark:text-white text-xl font-bold text-gray-900">Credit Balance</h2>
                  <p class="dark:text-gray-400 mt-1 text-sm text-gray-600">Available credits</p>
                </div>
                <div class="text-right">
                  <div class="flex items-baseline">
                    <span class="text-3xl font-bold text-green-600 dark:text-[#14F195]">${{ creditBalance }}</span>
                    <span class="dark:text-gray-400 ml-2 text-sm text-gray-500">available</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Content -->
            <div class="p-6">
              <div v-if="creditsStore.loading" class="py-8">
                <div class="animate-pulse flex justify-center">
                  <div class="space-y-2 text-center">
                    <div class="w-32 h-4 bg-gray-200 dark:bg-[#21262d] rounded"></div>
                    <div class="w-24 h-3 bg-gray-200 dark:bg-[#21262d] rounded mx-auto"></div>
                  </div>
                </div>
              </div>

              <div v-else-if="creditsStore.error" class="py-4">
                <div class="bg-red-50 dark:bg-red-900/20 dark:border-red-800 p-4 border border-red-200 rounded-lg">
                  <div class="flex items-center">
                    <ExclamationTriangleIcon class="dark:text-red-400 w-5 h-5 mr-3 text-red-600" />
                    <p class="dark:text-red-200 font-medium text-red-800">Failed to load credit balance</p>
                  </div>
                  <p class="dark:text-red-300 mt-1 text-sm text-red-700">{{ creditsStore.error }}</p>
                </div>
              </div>

              <div v-else>
                <!-- Credit Breakdown Table -->
                <div v-if="creditsStore.activePurchases.length > 0" class="mb-6">
                  <h3 class="dark:text-gray-300 mb-3 text-sm font-medium text-gray-700">Active Credit Purchases</h3>
                  <div class="overflow-x-auto">
                    <table class="w-full bg-white dark:bg-[#0d1117] border border-gray-200 dark:border-[#30363d] rounded-lg">
                      <thead class="bg-gray-50 dark:bg-[#161b22]">
                        <tr>
                          <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider border-r border-gray-200 dark:border-[#30363d]">
                            Amount</th>
                          <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider border-r border-gray-200 dark:border-[#30363d]">
                            Purchase Date</th>
                          <th class="dark:text-gray-400 px-4 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                            Expires</th>
                        </tr>
                      </thead>
                      <tbody class="divide-y divide-gray-200 dark:divide-[#30363d]">
                        <tr v-for="purchase in creditsStore.activePurchases" :key="purchase.id" class="hover:bg-gray-50 dark:hover:bg-[#161b22] transition-colors">
                          <td class="px-4 py-3 whitespace-nowrap text-sm font-semibold text-green-600 dark:text-[#14F195] border-r border-gray-200 dark:border-[#30363d]">
                            ${{ purchase.remaining.toFixed(2) }}
                          </td>
                          <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-300 border-r border-gray-200 dark:border-[#30363d]">
                            {{ formatDate(purchase.created_at) }}
                          </td>
                          <td class="whitespace-nowrap dark:text-gray-300 px-4 py-3 text-sm text-gray-900">
                            {{ formatDate(purchase.expires_at) }}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                <div v-else class="py-8 mb-6 text-center">
                  <div class="bg-gray-50 dark:bg-[#161b22] rounded-lg p-6 border border-gray-200 dark:border-[#30363d]">
                    <CurrencyDollarIcon class="dark:text-gray-500 w-12 h-12 mx-auto mb-4 text-gray-400" />
                    <h3 class="dark:text-white mb-2 text-lg font-medium text-gray-900">No Active Credits</h3>
                    <p class="dark:text-gray-400 text-sm text-gray-500">Purchase credits to enable pay-as-you-go usage for transcription and AI optimization.</p>
                  </div>
                </div>

                <div class="flex flex-col items-center space-y-4">
                  <!-- Negative Balance Warning -->
                  <div v-if="hasNegativeBalance" class="bg-yellow-50 dark:bg-yellow-900/20 dark:border-yellow-800 w-full max-w-md p-3 mb-4 border border-yellow-200 rounded-lg">
                    <div class="flex items-start">
                      <ExclamationTriangleIcon class="w-5 h-5 text-yellow-600 dark:text-yellow-400 mt-0.5 mr-2 flex-shrink-0" />
                      <div>
                        <p class="dark:text-yellow-200 font-medium text-yellow-800">
                          You have a negative balance of <span class="font-bold">-${{ negativeBalanceAmount.toFixed(9) }}</span>
                        </p>
                        <p class="dark:text-yellow-300 mt-1 text-sm text-yellow-700" v-if="customCreditAmount">
                          Your purchase of ${{ customCreditAmount }} will first cover this amount.
                          <span class="font-semibold">Final credit addition: ${{ adjustedPurchaseTotal.toFixed(9) }}</span>
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <!-- Credit Amount Input -->
                  <div class="flex items-center space-x-4">
                    <div class="flex items-center">
                      <span class="dark:text-gray-300 mr-2 text-gray-700">$</span>
                      <input
                        v-model="customCreditAmount"
                        type="number"
                        min="1"
                        max="100"
                        placeholder="1"
                        class="w-20 px-3 py-2 border border-gray-300 dark:border-[#30363d] dark:bg-[#21262d] dark:text-white rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 text-center"
                        @input="validateAmount"
                      />
                    </div>
                    <button
                      class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-green-600 hover:bg-green-700 dark:bg-[#14F195] dark:hover:bg-green-400 dark:text-gray-900 dark:hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 dark:focus:ring-[#14F195] transition-all duration-200 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                      @click="handleBuyCreditsClick"
                      :disabled="creditsStore.checkoutLoading || !isValidAmount"
                    >
                      <PlusIcon class="dark:text-gray-900 w-5 h-5 mr-2 text-white" />
                      <span v-if="creditsStore.checkoutLoading" class="dark:text-black text-white">Creating checkout...</span>
                      <span v-else class="dark:text-black text-white">Buy Credits</span>
                    </button>
                  </div>
                  
                  <!-- Amount validation message -->
                  <div class="text-xs text-center"
                    :class="amountError ? 'text-red-500 dark:text-red-400' : 'text-gray-500 dark:text-gray-400'">
                    {{ amountMessage }}
                  </div>

                  <!-- Error messages -->
                  <div v-if="paddleError || creditsStore.error" class="w-full max-w-md">
                    <div class="bg-red-50 dark:bg-red-900/20 dark:border-red-800 p-3 border border-red-200 rounded-lg">
                      <div class="flex items-start">
                        <ExclamationTriangleIcon class="w-4 h-4 text-red-600 dark:text-red-400 mt-0.5 mr-2 flex-shrink-0" />
                        <div>
                          <p class="dark:text-red-300 text-sm text-red-700">
                            {{ paddleError || creditsStore.error }}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Paddle Debug Section (visible only in development mode) -->
      <div v-if="isDevelopment" class="sm:px-6 lg:px-8 max-w-3xl px-4 mx-auto mb-8">
        <div class="card">
          <div class="flex items-center justify-between mb-4">
            <h2 class="dark:text-white text-xl font-semibold text-gray-900">Paddle Debug</h2>
            <button @click="showPaddleDebug = !showPaddleDebug" 
                    class="text-sm px-2 py-1 rounded border border-gray-300 dark:border-[#30363d]">
              {{ showPaddleDebug ? 'Hide' : 'Show' }} Details
            </button>
          </div>

        <div class="flex items-center justify-between mb-4">
          <div>
            <span class="font-medium text-gray-700 dark:text-[#c9d1d9]">Status: </span>
            <span v-if="paddleInitialized" class="dark:text-green-400 text-green-600">Initialized</span>
            <span v-else-if="paddleLoading" class="dark:text-yellow-400 text-yellow-600">Loading...</span>
            <span v-else class="dark:text-red-400 text-red-600">Not Initialized</span>
          </div>
          <button @click="retryPaddleInit" 
                  :disabled="paddleLoading"
                  class="hover:bg-blue-700 disabled:opacity-50 px-3 py-1 text-sm text-white bg-blue-600 rounded">
            Retry Initialization
          </button>
        </div>

        <div v-if="paddleError" class="bg-red-50 dark:bg-red-900/20 dark:border-red-800 p-3 mb-4 border border-red-200 rounded">
          <h4 class="dark:text-red-300 text-sm font-medium text-red-800">Error:</h4>
          <p class="dark:text-red-400 text-sm text-red-700 whitespace-pre-wrap">{{ paddleError }}</p>
        </div>

        <div v-if="showPaddleDebug">
          <h4 class="text-sm font-medium text-gray-700 dark:text-[#c9d1d9] mb-2">Debug Information:</h4>
          <pre class="text-xs bg-gray-50 dark:bg-[#21262d] p-3 rounded overflow-auto max-h-40">{{ JSON.stringify(debugInfo, null, 2) }}</pre>
          
          <h4 class="text-sm font-medium text-gray-700 dark:text-[#c9d1d9] mt-4 mb-2">Environment Variables:</h4>
          <div class="text-xs">
            <div class="flex justify-between p-2 border-b border-gray-200 dark:border-[#30363d]">
              <span>VITE_PADDLE_ENVIRONMENT:</span>
              <span>{{ paddleEnvironment }}</span>
            </div>
            <div class="flex justify-between p-2 border-b border-gray-200 dark:border-[#30363d]">
              <span>VITE_PADDLE_CLIENT_TOKEN:</span>
              <span>{{ hasPaddleToken ? '✓ Set' : '✗ Missing' }}</span>
            </div>
            <div class="flex justify-between p-2 border-b border-gray-200 dark:border-[#30363d]">
              <span>VITE_PADDLE_PRICE_ID_CREDITS_SANDBOX:</span>
              <span>{{ hasPaddleCreditsPriceSandbox ? '✓ Set' : '✗ Missing' }}</span>
            </div>
            <div class="flex justify-between p-2 border-b border-gray-200 dark:border-[#30363d]">
              <span>VITE_PADDLE_PRICE_ID_CREDITS_PRODUCTION:</span>
              <span>{{ hasPaddleCreditsPriceProduction ? '✓ Set' : '✗ Missing' }}</span>
            </div>
            <div class="flex justify-between p-2">
              <span>Subscription Product Variables:</span>
              <span>{{ subscriptionProductsStatus }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Add Payment Method Modal -->
      <Transition name="modal">
        <div v-if="showAddPaymentMethod"
          class="fixed inset-0 z-50 flex items-center justify-center overflow-hidden transition-all duration-300">
          <!-- Dark semi-transparent backdrop -->
          <div class="bg-opacity-70 backdrop-blur-sm absolute inset-0 bg-black"></div>
          
          <div
            class="relative w-full max-w-md mx-4 bg-white dark:bg-[#161b22] rounded-lg shadow-xl flex flex-col transform transition-transform duration-300 ease-out border border-gray-200 dark:border-[#30363d]">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-[#30363d]">
              <h2 class="dark:text-white text-xl font-bold text-gray-900">Add payment method</h2>
            </div>

            <div class="p-6 overflow-y-auto">
              <p class="mb-4 text-sm text-gray-600 dark:text-[#8b949e]">
                Add your credit card details below. This card will be saved to your account and can be removed at any
                time.
              </p>

              <div class="mb-6">
                <label class="block mb-2 text-sm font-medium text-gray-700 dark:text-[#c9d1d9]">Card information</label>
                <div class="overflow-hidden border border-gray-300 rounded-md dark:border-[#30363d]">
                  <input type="text" placeholder="Card number"
                    class="w-full px-3 py-2 border-b border-gray-300 dark:border-[#30363d] dark:bg-[#21262d] dark:text-white dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
                  <div class="flex">
                    <input type="text" placeholder="MM / YY"
                      class="w-1/2 px-3 py-2 border-r border-gray-300 dark:border-[#30363d] dark:bg-[#21262d] dark:text-white dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
                    <input type="text" placeholder="CVC"
                      class="w-1/2 px-3 py-2 dark:bg-[#21262d] dark:text-white dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
                  </div>
                </div>
              </div>

              <div class="mb-6">
                <label class="block mb-2 text-sm font-medium text-gray-700 dark:text-[#c9d1d9]">Name on card</label>
                <input type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-[#30363d] dark:bg-[#21262d] dark:text-white dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
              </div>

              <div class="mb-6">
                <label class="block mb-2 text-sm font-medium text-gray-700 dark:text-[#c9d1d9]">Billing address</label>
                <select
                  class="w-full px-3 py-2 mb-2 border border-gray-300 rounded-md dark:border-[#30363d] dark:bg-[#21262d] dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  <option>United States</option>
                  <option>Canada</option>
                  <option>United Kingdom</option>
                </select>
                <input type="text" placeholder="Address line 1"
                  class="w-full px-3 py-2 mb-2 border border-gray-300 rounded-md dark:border-[#30363d] dark:bg-[#21262d] dark:text-white dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
                <input type="text" placeholder="Address line 2"
                  class="w-full px-3 py-2 mb-2 border border-gray-300 rounded-md dark:border-[#30363d] dark:bg-[#21262d] dark:text-white dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
                <div class="grid grid-cols-2 gap-2">
                  <input type="text" placeholder="City"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-[#30363d] dark:bg-[#21262d] dark:text-white dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
                  <input type="text" placeholder="Postal code"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-[#30363d] dark:bg-[#21262d] dark:text-white dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
                </div>
              </div>
            </div>

            <div class="px-6 py-4 border-t rounded-b-lg border-gray-200 dark:border-[#30363d] dark:bg-[#161b22]">
              <div class="flex justify-end space-x-4">
                <button class="btn-secondary" @click="showAddPaymentMethod = false">Cancel</button>
                <button class="btn-primary" @click="addPaymentMethod">
                  Add payment method
                </button>
              </div>
            </div>
          </div>
        </div>
      </Transition>
      </div>
    </div>
  </Transition>
        <!-- Upgrade Modal -->
      <UpgradeModal 
        :is-open="showUpgradeModal"
        :targetPlan="selectedTargetPlan || undefined"
        @close="showUpgradeModal = false"
        @success="handleUpgradeSuccess"
        @error="handleUpgradeError"
      />
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useSubscriptionStore } from '@/stores/subscription'
import { useCreditsStore } from '@/stores/credits'
import { usePaddleCustomerStore } from '@/stores/paddleCustomer'
import { usePaddle } from '@/lib/paddle'
import { format } from 'date-fns'
import SubscriptionPricingCards from '@/components/SubscriptionPricingCards.vue'
import PricingFeatureComparison from '@/components/PricingFeatureComparison.vue'
import CancelButton from '@/components/subscription/CancelButton.vue'
import UndoCancellationButton from '@/components/subscription/UndoCancellationButton.vue'
import CustomerPortalButton from '@/components/subscription/CustomerPortalButton.vue'
import PaymentFailureBanner from '@/components/subscription/PaymentFailureBanner.vue'
import UpgradeModal from '@/components/subscription/UpgradeModal.vue'
import { useToast } from '@/services/toast'
import { PlusIcon, ExclamationTriangleIcon, CurrencyDollarIcon } from '@heroicons/vue/24/solid'

// Stores
const subscriptionStore = useSubscriptionStore()
const creditsStore = useCreditsStore()
const paddleCustomerStore = usePaddleCustomerStore()
const { isInitialized: paddleInitialized, isLoading: paddleLoading, error: paddleError, forceRetryInit, debugInfo } = usePaddle()

// Toast service
const { success: showSuccessToast, error: showErrorToast } = useToast()

// State variables
const showAddPaymentMethod = ref(false)

function handleBuyCreditsClick() {
  console.log('Buy Credits button clicked')
  // Validate amount before proceeding
  if (!isValidAmount.value) {
    console.log('Invalid amount - please enter a value between $1 and $100:', customCreditAmount.value)
    return
  }
  
  console.log(`Processing payment for $${customCreditAmount.value}`)
  processCustomPayment()
}
const customCreditAmount = ref<number | null>(null) // Start empty
const autoRecharge = ref(false)
const amountError = ref(false)
const amountMessage = ref('Enter an amount between $1 and $100')
const showPaddleDebug = ref(false)
const isRefreshingAfterUpgrade = ref(false)

// Upgrade modal state
const showUpgradeModal = ref(false)
const selectedTargetPlan = ref<'Basic' | 'Pro' | 'Premium' | null>(null)

// Computed properties
const userSubscription = computed(() => subscriptionStore.userSubscription)
const currentPlan = computed(() => subscriptionStore.currentPlan)
const hasActiveFreeTrialPlan = computed(() => Boolean(subscriptionStore.userSubscription?.status === 'active' && subscriptionStore.currentPlan?.name === 'Free Trial'))
const hasPaymentMethod = Boolean(creditsStore.paymentMethod)
const isValidAmount = computed(() => {
  const amount = customCreditAmount.value
  return amount !== null && amount !== undefined && amount !== 0 && amount >= 1 && amount <= 100
})
const creditBalance = computed(() => {
  return creditsStore.totalActiveCredits.toFixed(2) || '0.00'
})

const hasNegativeBalance = computed(() => {
  return creditsStore.totalActiveCredits < 0
})

const negativeBalanceAmount = computed(() => {
  return Math.abs(creditsStore.totalActiveCredits)
})

const adjustedPurchaseTotal = computed(() => {
  if (!customCreditAmount.value || customCreditAmount.value <= 0) return 0
  return hasNegativeBalance.value
    ? customCreditAmount.value - negativeBalanceAmount.value
    : customCreditAmount.value
})

// Quota usage computeds (similar to DashboardView)
const hasQuotasToShow = computed(() => {
  return subscriptionStore.hasSubscription && subscriptionStore.quotas.length > 0
})

const transcriptionUsage = computed(() => {
  if (subscriptionStore.hasSubscription && subscriptionStore.quotas.length > 0) {
    return subscriptionStore.getQuotaUsage('transcription')
  }
  return { used: 0, total: 0, percentage: 0 }
})

const optimizationUsage = computed(() => {
  if (subscriptionStore.hasSubscription && subscriptionStore.quotas.length > 0) {
    return subscriptionStore.getQuotaUsage('optimization')
  }
  return { used: 0, total: 0, percentage: 0 }
})

// Environment variables as computed properties to avoid template errors
const isDevelopment = computed(() => import.meta.env.DEV)
const paddleEnvironment = computed(() => import.meta.env.VITE_PADDLE_ENVIRONMENT || 'Not set')
const hasPaddleToken = computed(() => Boolean(import.meta.env.VITE_PADDLE_CLIENT_TOKEN))
const hasPaddleCreditsPriceSandbox = computed(() => Boolean(import.meta.env.VITE_PADDLE_PRICE_ID_CREDITS_SANDBOX))
const hasPaddleCreditsPriceProduction = computed(() => Boolean(import.meta.env.VITE_PADDLE_PRICE_ID_CREDITS_PRODUCTION))
const subscriptionProductsStatus = computed(() => {
  const priceIds = [
    'VITE_PADDLE_PRICE_ID_BASIC_SANDBOX',
    'VITE_PADDLE_PRICE_ID_BASIC_PRODUCTION',
    'VITE_PADDLE_PRICE_ID_PRO_SANDBOX',
    'VITE_PADDLE_PRICE_ID_PRO_PRODUCTION',
    'VITE_PADDLE_PRICE_ID_PREMIUM_SANDBOX',
    'VITE_PADDLE_PRICE_ID_PREMIUM_PRODUCTION'
  ]
  
  const configured = priceIds.filter(id => Boolean(import.meta.env[id])).length
  return `${configured}/${priceIds.length} configured`
})

// Check if current subscription is a free trial (don't show cancel button for free trial)
const isFreeTrial = computed(() => {
  return subscriptionStore.currentPlan?.name === 'Free Trial'
})

// Methods
function formatDate(dateString?: string) {
  if (!dateString) return 'N/A'
  return format(new Date(dateString), 'MMM d, yyyy')
}

// Format transcription amount (minutes and seconds)
function formatTranscriptionAmount(amount: number) {
  if (amount < 1) {
    // Convert to seconds for small values
    const seconds = amount * 60
    return `${parseFloat(seconds.toFixed(3))} secs`
  }
  
  if (Math.floor(amount) === amount) {
    return `${Math.floor(amount)} mins`
  }
  
  return `${parseFloat(amount.toFixed(1))} mins`
}

// Format tokens with green K highlighting (from DashboardView)
function formatTokensWithGreenK(amount: number) {
  const tokenCount = Math.round(amount)
  if (tokenCount >= 1000) {
    const kValue = tokenCount / 1000
    // If it's a whole number of K, don't show decimals
    if (kValue % 1 === 0) {
      return `${Math.floor(kValue)}<span class="text-green-500 dark:text-[#14F195]">K</span>`
    } else {
      return `${kValue.toFixed(1)}<span class="text-green-500 dark:text-[#14F195]">K</span>`
    }
  }
  return tokenCount.toLocaleString()
}

// Handle successful cancellation
function handleCancelSuccess() {
  // Refresh subscription data to show updated status
  subscriptionStore.fetchUserSubscription()
}

// Handle successful undo cancellation
function handleUndoSuccess() {
  // Refresh subscription data to show updated status
  subscriptionStore.fetchUserSubscription()
}

async function retryPaddleInit() {
  try {
    await forceRetryInit()
    console.log('Paddle re-initialization attempt completed')
  } catch (err) {
    console.error('Failed to re-initialize Paddle:', err)
  }
}

async function processCustomPayment() {
  // Check if Paddle is initialized
  if (!paddleInitialized.value) {
    try {
      console.log('Paddle not initialized, attempting initialization...')
      await retryPaddleInit()
    } catch (err) {
      console.error('Failed to initialize Paddle before payment:', err)
      return
    }
  }
  
  // Create Paddle checkout for credit purchase
  if (customCreditAmount.value && customCreditAmount.value > 0) {
    console.log(`Creating checkout for $${customCreditAmount.value}`)
    const success = await creditsStore.createPaddleCheckout(customCreditAmount.value)
    if (success) {
      console.log('Checkout created successfully')
    } else {
      console.error('Failed to create checkout:', creditsStore.error)
    }
  }
}

function cancelCheckout() {
  // Reset to empty
  customCreditAmount.value = null
}

function addPaymentMethod() {
  // Simulate adding a payment method
  showAddPaymentMethod.value = false
  creditsStore.addPaymentMethod()
}

function validateAmount() {
  const amount = customCreditAmount.value
  if (amount === null || amount === undefined || amount === 0) {
    amountError.value = false
    amountMessage.value = 'Enter an amount between $1 and $100'
  } else if (amount < 1) {
    amountError.value = true
    amountMessage.value = 'Minimum amount is $1'
  } else if (amount > 100) {
    amountError.value = true
    amountMessage.value = 'Maximum amount is $100'
  } else {
    amountError.value = false
    amountMessage.value = 'Enter an amount between $1 and $100'
  }
}

// Customer Portal Event Handlers
function handlePortalSuccess(environment: string) {
  console.log(`Customer portal opened successfully in ${environment} environment`)
  showSuccessToast('Billing portal opened', `Portal opened in ${environment} environment`)
}

function handlePortalError(error: string) {
  console.error('Customer portal error:', error)
  showErrorToast('Failed to open billing portal', error)
}

// Payment Failure Banner Event Handlers  
function handleBannerDismissed(status: string | null) {
  console.log(`Payment failure banner dismissed for status: ${status}`)
}

// Upgrade Modal Methods
function openUpgradeModal() {
  // Default to showing modal without pre-selected plan for manual selection
  selectedTargetPlan.value = null
  showUpgradeModal.value = true
}

async function handleUpgradeSuccess() {
  showUpgradeModal.value = false
  selectedTargetPlan.value = null
  isRefreshingAfterUpgrade.value = true
  
  // Show success message
  showSuccessToast('Plan Updated Successfully! 🎉', 'Your subscription has been updated. Refreshing to show the latest changes...')
  
  // Give the toast a moment to show, then reload the page to ensure fresh data
  setTimeout(() => {
    // Reload the page to get the freshest subscription data and ensure UI consistency
    console.log('Reloading page to refresh subscription data after successful upgrade')
    window.location.reload()
  }, 2000) // Increased to 2 seconds to give users time to read the message
}

function handleUpgradeError(error: string) {
  console.error('Upgrade error received in PaymentsView:', error)
  showErrorToast('Upgrade Failed', error || 'Failed to upgrade your subscription. Please try again or contact support.')
}

// Add a method to manually refresh subscription data
async function refreshSubscriptionData() {
  console.log('Manual refresh of subscription data requested')
  
  try {
    // Show loading indicator
    isRefreshingAfterUpgrade.value = true
    
    // Fetch the data
    await subscriptionStore.fetchUserSubscription()
    
    console.log('Manual refresh completed:', {
      hasSubscription: subscriptionStore.userSubscription !== null,
      subscriptionData: subscriptionStore.userSubscription,
      nextBilledAt: subscriptionStore.userSubscription?.next_billed_at
    })
    
    // Show success message
    showSuccessToast('Data Refreshed', 'Subscription data has been refreshed.')
  } catch (err) {
    console.error('Error during manual refresh:', err)
    showErrorToast('Refresh Failed', 'Could not refresh subscription data.')
  } finally {
    // Hide loading indicator
    isRefreshingAfterUpgrade.value = false
  }
}

// Lifecycle hooks
onMounted(async () => {
  // Check for payment success/cancel in URL params
  const urlParams = new URLSearchParams(window.location.search)
  const success = urlParams.get('success')
  const cancelled = urlParams.get('cancelled')
  const subscriptionSuccess = urlParams.get('subscription_success')
  const upgradeSuccess = urlParams.get('upgrade_success')

  if (success === 'true') {
    // Payment successful - refresh credits and show success message
    await creditsStore.fetchCredits()
    showSuccessToast('Payment Successful! 💳', 'Credits have been added to your account.')
    console.log('Payment successful! Credits have been added to your account.')

    // Clean up URL
    window.history.replaceState({}, document.title, window.location.pathname)
  } else if (cancelled === 'true') {
    // Payment cancelled - show message
    console.log('Payment was cancelled.')
    showErrorToast('Payment Cancelled', 'Your payment was cancelled. No charges were made.')

    // Clean up URL
    window.history.replaceState({}, document.title, window.location.pathname)
  } else if (subscriptionSuccess === 'true' || upgradeSuccess === 'true') {
    // Subscription or upgrade successful
    showSuccessToast('Subscription Updated! 🎉', 'Your subscription has been successfully updated.')
    console.log('Subscription update successful!')

    // Clean up URL
    window.history.replaceState({}, document.title, window.location.pathname)
  }

  // Fetch data on component mount
  console.log('Starting data fetch in PaymentsView...')
  await subscriptionStore.fetchAvailablePlans() // Fetch available plans first
  console.log('Available plans fetched:', subscriptionStore.availablePlans.length)
  
  await subscriptionStore.fetchUserSubscription()
  console.log('User subscription fetched:', {
    hasSubscription: subscriptionStore.userSubscription !== null,
    subscriptionData: subscriptionStore.userSubscription,
    nextBilledAt: subscriptionStore.userSubscription?.next_billed_at
  })
  
  await subscriptionStore.fetchQuotas() // Fetch quotas for usage display
  console.log('Quotas fetched:', subscriptionStore.quotas.length)
  
  await creditsStore.fetchCredits()
  console.log('Credits fetched')
  
  // Check if user is a paddle customer
  await paddleCustomerStore.fetchPaddleCustomer()
  console.log('Paddle customer fetched:', paddleCustomerStore.isPaddleCustomer)
})
</script>

<style scoped>
.modern-card {
  position: relative;
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
}

.modern-card::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 1rem;
  padding: 1px;
  background: linear-gradient(145deg, rgba(255,255,255,0.2), rgba(255,255,255,0.05));
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  -webkit-mask-composite: xor;
  pointer-events: none;
}

.price-display {
  line-height: 1;
}

.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
}

:deep(.dark) .card {
  background-color: #161b22;
  border-color: #30363d;
}

.btn-secondary {
  background-color: #e5e7eb;
  color: #1f2937;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  transition-property: background-color, border-color, color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.btn-secondary:hover {
  background-color: #d1d5db;
}

:deep(.dark) .btn-secondary {
  background-color: #21262d;
  color: #c9d1d9;
}

:deep(.dark) .btn-secondary:hover {
  background-color: #30363d;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  transition-property: background-color, border-color, color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.btn-primary:hover:not(:disabled) {
  background-color: #2563eb;
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Transitions */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s ease;
}

.modal-enter-from {
  opacity: 0;
}

.modal-enter-to {
  opacity: 1;
}

.modal-leave-from {
  opacity: 1;
}

.modal-leave-to {
  opacity: 0;
}

/* Custom style for number inputs */
input[type=number] {
  -moz-appearance: textfield;
  appearance: textfield;
}

input[type=number]::-webkit-outer-spin-button,
input[type=number]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
</style>

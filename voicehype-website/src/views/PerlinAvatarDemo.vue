<template>
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-8 font-heading">Perlin Noise Avatar Demo</h1>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      <!-- Basic Example -->
      <div class="card p-6">
        <h2 class="text-xl font-semibold mb-4">Basic Example</h2>
        <div class="flex flex-col items-center">
          <ProfileAvatar 
            :size="128" 
            :usePerlinNoise="true" 
          />
          <p class="mt-4 text-sm text-gray-600 dark:text-gray-400">
            Default Perlin noise avatar with 128px size
          </p>
        </div>
      </div>
      
      <!-- Auto-updating Example -->
      <div class="card p-6">
        <h2 class="text-xl font-semibold mb-4">Auto-updating (5s)</h2>
        <div class="flex flex-col items-center">
          <ProfileAvatar 
            :size="128" 
            :usePerlinNoise="true" 
            :updateInterval="5000"
          />
          <p class="mt-4 text-sm text-gray-600 dark:text-gray-400">
            Updates every 5 seconds with a new pattern
          </p>
        </div>
      </div>
      
      <!-- Different Sizes -->
      <div class="card p-6">
        <h2 class="text-xl font-semibold mb-4">Different Sizes</h2>
        <div class="flex items-center justify-around">
          <div class="flex flex-col items-center">
            <ProfileAvatar :size="48" :usePerlinNoise="true" />
            <p class="mt-2 text-xs text-gray-600 dark:text-gray-400">48px</p>
          </div>
          <div class="flex flex-col items-center">
            <ProfileAvatar :size="64" :usePerlinNoise="true" />
            <p class="mt-2 text-xs text-gray-600 dark:text-gray-400">64px</p>
          </div>
          <div class="flex flex-col items-center">
            <ProfileAvatar :size="96" :usePerlinNoise="true" />
            <p class="mt-2 text-xs text-gray-600 dark:text-gray-400">96px</p>
          </div>
        </div>
      </div>
      
      <!-- Manual Seed Control -->
      <div class="card p-6">
        <h2 class="text-xl font-semibold mb-4">Manual Seed Control</h2>
        <div class="flex flex-col items-center">
          <ProfileAvatar 
            :size="128" 
            :usePerlinNoise="true"
            :seed="manualSeed"
          />
          <div class="mt-4 w-full">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Seed: {{ manualSeed }}
            </label>
            <input 
              type="range" 
              min="1" 
              max="1000" 
              v-model.number="manualSeed"
              class="w-full"
            />
          </div>
        </div>
      </div>
      
      <!-- Fallback to Initials -->
      <div class="card p-6">
        <h2 class="text-xl font-semibold mb-4">Fallback to Initials</h2>
        <div class="flex flex-col items-center">
          <ProfileAvatar 
            :size="128" 
            :usePerlinNoise="false"
            name="John Doe"
          />
          <p class="mt-4 text-sm text-gray-600 dark:text-gray-400">
            When usePerlinNoise is false, falls back to initials
          </p>
        </div>
      </div>
      
      <!-- With Image URL -->
      <div class="card p-6">
        <h2 class="text-xl font-semibold mb-4">With Image URL</h2>
        <div class="flex flex-col items-center">
          <ProfileAvatar 
            :size="128" 
            :usePerlinNoise="true"
            imageUrl="https://i.pravatar.cc/300"
          />
          <p class="mt-4 text-sm text-gray-600 dark:text-gray-400">
            When imageUrl is provided, it takes precedence
          </p>
        </div>
      </div>
    </div>
    
    <div class="mt-12">
      <h2 class="text-2xl font-bold mb-4 font-heading">Implementation Details</h2>
      <div class="card p-6">
        <p class="mb-4">
          The Perlin noise avatar generates a unique, visually appealing pattern based on a seed value.
          By default, the seed is derived from the current timestamp, making each avatar unique.
        </p>
        <p class="mb-4">
          Features:
        </p>
        <ul class="list-disc pl-6 mb-4">
          <li>Deterministic generation based on seed value</li>
          <li>Auto-updating at configurable intervals</li>
          <li>Fallback to initials when Perlin noise is disabled</li>
          <li>Support for custom image URLs</li>
          <li>Fully responsive with customizable size</li>
        </ul>
        <p>
          The implementation uses a custom Perlin noise algorithm to generate smooth, natural-looking patterns
          with random colors based on the seed value.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ProfileAvatar from '@/components/ProfileAvatar.vue';

const manualSeed = ref(123);
</script> 
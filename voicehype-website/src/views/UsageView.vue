<template>
  <!-- Usage History Section -->
  <section id="usage-history" class="mb-12">
    <div class="flex items-center justify-between mb-6">
      <h2 class="text-gray-800 dark:text-[#c9d1d9] font-heading text-2xl font-semibold">Usage History</h2>
      <div class="flex space-x-2">
        <button @click="setCurrentMonthFilter" class="btn-secondary">
          <CalendarIcon class="inline w-5 h-5 mr-2" /> Current Month
        </button>
        <button @click="exportUsage" class="btn-secondary" :disabled="loading || exportLoading || usageHistory.length === 0">
          <span v-if="exportLoading">
            <ArrowPathIcon class="animate-spin inline w-5 h-5 mr-2" /> Exporting...
          </span>
          <span v-else>
            <ArrowDownTrayIcon class="inline w-5 h-5 mr-2" /> Export
          </span>
        </button>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-[#161b22] p-6 mb-6 rounded-lg shadow border border-gray-200 dark:border-[#30363d]">
      <div class="md:grid-cols-2 lg:grid-cols-4 grid grid-cols-1 gap-4">
        <!-- Date Range -->
        <div>
          <label class="block mb-1 text-sm font-medium text-gray-700 dark:text-[#c9d1d9]">
            Start Date
          </label>
          <input type="date" v-model="filters.startDate" class="input dark:bg-[#21262d] dark:border-[#30363d] dark:text-[#c9d1d9]" @change="applyFilters" />
        </div>
        <div>
          <label class="block mb-1 text-sm font-medium text-gray-700 dark:text-[#c9d1d9]">
            End Date
          </label>
          <input type="date" v-model="filters.endDate" class="input dark:bg-[#21262d] dark:border-[#30363d] dark:text-[#c9d1d9]" @change="applyFilters" />
        </div>

        <!-- Service Filter -->
        <div>
          <label class="block mb-1 text-sm font-medium text-gray-700 dark:text-[#c9d1d9]">
            Service
          </label>
          <select v-model="filters.service" class="input dark:bg-[#21262d] dark:border-[#30363d] dark:text-[#c9d1d9]" @change="applyFilters">
            <option value="">All Services</option>
            <option v-for="service in availableServices" :key="service" :value="service">
              {{ service }}
            </option>
          </select>
        </div>

        <!-- Model Filter -->
        <div>
          <label class="block mb-1 text-sm font-medium text-gray-700 dark:text-[#c9d1d9]">
            Model
          </label>
          <select v-model="filters.model" class="input dark:bg-[#21262d] dark:border-[#30363d] dark:text-[#c9d1d9]" @change="applyFilters">
            <option value="">All Models</option>
            <option v-for="model in availableModels" :key="model" :value="model">
              {{ formatModelName(model) }}
            </option>
          </select>
        </div>
      </div>

      <div class="flex justify-end mt-4">
        <button @click="clearFilters" class="btn-outline">
          Clear Filters
        </button>
      </div>

      <!-- Usage Summary Cards -->
      <div class="md:grid-cols-2 lg:grid-cols-4 grid gap-6 mt-6">
        <div class="bg-white dark:bg-[#0d1117] p-4 rounded-lg shadow-sm border border-gray-200 dark:border-[#1c2129]">
          <p class="text-sm text-gray-500 dark:text-[#8b949e]">Total Spend</p>
          <div v-if="totalCostLoading" class="flex items-center mt-2 space-x-2">
            <ArrowPathIcon class="animate-spin w-4 h-4 text-gray-400 dark:text-[#8b949e]" />
            <p class="text-gray-400 dark:text-[#8b949e]">Loading...</p>
          </div>
          <div v-else class="mt-2">
            <p class="dark:text-white text-2xl font-semibold text-gray-800">
              <span class="text-primary-600 dark:text-[#14F195]">$</span>{{ formatCostTwoDecimals(totalCost) }}
            </p>
            <p class="text-xs text-gray-500 dark:text-[#8b949e] mt-1">For selected time period</p>
          </div>
        </div>

        <!-- New Summary Cards -->
        <TokenSummaryCard
          title="Transcription Minutes"
          :value="totalMinutes"
          :loading="totalCostLoading"
          type="minutes"
        />

        <TokenSummaryCard
          title="Input Tokens"
          :value="totalInputTokens"
          :loading="totalCostLoading"
          type="tokens"
        />

        <TokenSummaryCard
          title="Output Tokens"
          :value="totalOutputTokens"
          :loading="totalCostLoading"
          type="tokens"
        />
      </div>

      <!-- Charts Section -->
      <div class="mt-8">
        <h3 class="text-xl font-medium text-gray-800 dark:text-[#c9d1d9] mb-4">Usage Analytics</h3>

        <div class="md:grid-cols-2 grid gap-6 mt-4">
          <!-- Minutes Chart -->
          <div class="bg-white dark:bg-[#0d1117] p-4 rounded-lg shadow-sm border border-gray-200 dark:border-[#1c2129]">
            <MinutesChart
              :minutes-data="minutesByDay"
              :loading="totalCostLoading"
            />
          </div>

          <!-- Tokens Chart -->
          <div class="bg-white dark:bg-[#0d1117] p-4 rounded-lg shadow-sm border border-gray-200 dark:border-[#1c2129]">
            <TokensChart
              :tokens-data="tokensByDay"
              :loading="totalCostLoading"
            />
          </div>
        </div>

        <!-- Bar Charts (replaced pie charts) -->
        <div class="md:grid-cols-3 grid gap-6 mt-6">
          <!-- Service Breakdown Bar Chart -->
          <div class="bg-white dark:bg-[#0d1117] p-4 rounded-lg shadow-sm border border-gray-200 dark:border-[#1c2129]">
            <UsageBarChart
              title="Service Cost Breakdown"
              :data="normalizedServiceBreakdown"
              :loading="totalCostLoading"
              color-scheme="service"
            />
          </div>

          <!-- Model Breakdown Bar Chart -->
          <div class="bg-white dark:bg-[#0d1117] p-4 rounded-lg shadow-sm border border-gray-200 dark:border-[#1c2129]">
            <UsageBarChart
              title="Model Cost Breakdown"
              :data="modelBreakdown"
              :loading="totalCostLoading"
              color-scheme="model"
            />
          </div>

          <!-- Token Type Breakdown -->
          <div class="bg-white dark:bg-[#0d1117] p-4 rounded-lg shadow-sm border border-gray-200 dark:border-[#1c2129]">
            <UsageBarChart
              title="Token Usage Breakdown"
              :data="{ 'Input Tokens': totalInputTokens, 'Output Tokens': totalOutputTokens }"
              :loading="totalCostLoading"
              color-scheme="token"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Usage Table -->
    <div class="bg-white dark:bg-[#0d1117] rounded-lg shadow">
      <!-- Loading state -->
      <div v-if="loading" class="py-8 text-center">
        <ArrowPathIcon class="animate-spin w-8 h-8 mx-auto text-primary-600 dark:text-[#14F195]" />
        <p class="mt-2 text-gray-600 dark:text-[#8b949e]">Loading usage history...</p>
      </div>

      <!-- Error state -->
      <div v-else-if="error" class="relative px-4 py-3 text-red-700 bg-red-100 border border-red-400 rounded"
        role="alert">
        <span class="sm:inline block">{{ error }}</span>
        <button @click="fetchUsageHistory" class="mt-2 text-sm underline">
          Try again
        </button>
      </div>

      <!-- Empty state -->
      <div v-else-if="usageHistory.length === 0" class="py-8 text-center">
        <div class="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-[#21262d] rounded-full">
          <ChartBarIcon class="w-8 h-8 text-gray-500 dark:text-[#8b949e]" />
        </div>
        <h3 class="font-heading dark:text-white mb-2 text-lg font-medium text-gray-900">No Usage History</h3>
        <p class="text-gray-600 dark:text-[#8b949e]">You haven't used any services yet.</p>
      </div>

      <!-- Usage table - Add overflow-x-auto -->
      <div v-else class="overflow-x-auto">
        <table class="min-w-full">
          <thead class="bg-gray-50 dark:bg-[#0d1117]">
            <tr>
              <th class="text-gray-500 dark:text-[#8b949e] px-6 py-3 text-xs font-medium tracking-wider text-left uppercase">
                Service
              </th>
              <th class="text-gray-500 dark:text-[#8b949e] px-6 py-3 text-xs font-medium tracking-wider text-left uppercase max-w-[150px]">
                Model
              </th>
              <th class="text-gray-500 dark:text-[#8b949e] px-6 py-3 text-xs font-medium tracking-wider text-left uppercase">
                Amount
              </th>
              <th class="text-gray-500 dark:text-[#8b949e] px-6 py-3 text-xs font-medium tracking-wider text-left uppercase">
                Cost
              </th>
              <th class="text-gray-500 dark:text-[#8b949e] px-6 py-3 text-xs font-medium tracking-wider text-left uppercase">
                Pricing
              </th>
              <th class="text-gray-500 dark:text-[#8b949e] px-6 py-3 text-xs font-medium tracking-wider text-left uppercase">
                Status
              </th>
              <th class="text-gray-500 dark:text-[#8b949e] px-6 py-3 text-xs font-medium tracking-wider text-left uppercase">
                Date
              </th>
              <th class="text-gray-500 dark:text-[#8b949e] md:table-cell hidden px-6 py-3 text-xs font-medium tracking-wider text-left uppercase">
                Details
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-[#0d1117]">
            <tr v-for="(record, index) in usageHistory" :key="record.id"
                :class="index % 2 === 0 ? 'bg-white dark:bg-[#0d1117]' : 'bg-gray-50 dark:bg-[#10161d]'">
              <td class="whitespace-nowrap text-gray-500 dark:text-[#8b949e] px-6 py-4 text-sm">
                {{ record.service }}
              </td>
              <td class="text-gray-500 dark:text-[#8b949e] px-6 py-4 text-sm max-w-[150px] break-words">
                {{ formatModelName(record.model) }}
              </td>
              <td class="whitespace-nowrap text-gray-500 dark:text-[#8b949e] px-6 py-4 text-sm">
                {{ formatAmount(record.amount, record.service) }}
              </td>
              <td class="whitespace-nowrap text-gray-500 dark:text-[#8b949e] px-6 py-4 text-sm">
                <template v-if="record.status === 'success'">
                  <span class="text-primary-600 dark:text-[#14F195]">$</span>{{ formatCost(record.cost) }}
                </template>
                <template v-else>
                  -
                </template>
              </td>
              <td class="whitespace-nowrap text-gray-500 dark:text-[#8b949e] px-6 py-4 text-sm">
                <template v-if="record.status === 'success'">
                  {{ formatPricingModel(record.pricing_model) }}
                </template>
                <template v-else>
                  -
                </template>
              </td>
              <td class="whitespace-nowrap px-6 py-4">
                <span class="inline-flex px-2 text-xs font-semibold leading-5 rounded-full"
                  :class="getStatusClass(record.status)">
                  {{ record.status }}
                </span>
              </td>
              <td class="whitespace-nowrap text-gray-500 dark:text-[#8b949e] px-6 py-4 text-sm">
                {{ formatDate(record.created_at) }}
              </td>
              <td class="whitespace-nowrap text-gray-500 dark:text-[#8b949e] md:table-cell hidden px-6 py-4 text-sm">
                <div v-if="record.metadata && (getInputTokens(record.metadata) || getOutputTokens(record.metadata))"
                  class="flex flex-row space-x-2">
                  <span v-if="getInputTokens(record.metadata)"
                    class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                    Input: {{ getInputTokens(record.metadata) }} tokens
                  </span>
                  <span v-if="getOutputTokens(record.metadata)"
                    class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                    Output: {{ getOutputTokens(record.metadata) }} tokens
                  </span>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Load More Button -->
      <div v-if="usageHistory.length > 0 && hasMoreRecords" class="flex justify-center py-4">
        <button @click="loadMore" class="btn-secondary" :disabled="loading">
          <span v-if="loading">
            <ArrowPathIcon class="animate-spin inline w-5 h-5 mr-2" />
            Loading...
          </span>
          <span v-else>
            Load More
          </span>
        </button>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useUsageStore } from '@/stores/usage'
import {
  ArrowDownTrayIcon,
  ArrowPathIcon,
  ChartBarIcon,
  CalendarIcon
} from '@heroicons/vue/24/outline'
import MinutesChart from '@/components/charts/MinutesChart.vue'
import TokensChart from '@/components/charts/TokensChart.vue'
import UsageBarChart from '@/components/charts/UsageBarChart.vue'
import TokenSummaryCard from '@/components/charts/TokenSummaryCard.vue'
import { formatModelName } from '@/utils/modelFormatter'
import toastService from '@/services/toast'

// Store
const usageStore = useUsageStore()

// State
const loading = ref(false)
const exportLoading = ref(false)
const error = ref('')
const filters = ref({
  startDate: '',
  endDate: '',
  service: '',
  model: '',
  status: ''
})

// Computed
const usageHistory = computed(() => usageStore.usageHistory)
const totalCost = computed(() => usageStore.totalCost)
const totalCostLoading = computed(() => usageStore.totalCostLoading)
const serviceBreakdown = computed(() => usageStore.serviceBreakdown)
const modelBreakdown = computed(() => usageStore.modelBreakdown)
const availableServices = computed(() => usageStore.availableServices)
const availableModels = computed(() => usageStore.availableModels)
const hasMoreRecords = computed(() => usageStore.hasMoreRecords)

// New statistics
const totalMinutes = computed(() => usageStore.totalMinutes)
const totalInputTokens = computed(() => usageStore.totalInputTokens)
const totalOutputTokens = computed(() => usageStore.totalOutputTokens)
const minutesByDay = computed(() => usageStore.minutesByDay)
const tokensByDay = computed(() => usageStore.tokensByDay)

// Normalize service breakdown to ensure proper capitalization
const normalizedServiceBreakdown = computed(() => {
  const result: Record<string, number> = {}

  // Convert keys to proper capitalization
  Object.entries(serviceBreakdown.value).forEach(([key, value]) => {
    // Capitalize first letter
    const normalizedKey = key.charAt(0).toUpperCase() + key.slice(1).toLowerCase()
    result[normalizedKey] = value
  })

  // Ensure both services are represented
  if (!result.hasOwnProperty('Transcription')) {
    result['Transcription'] = 0
  }

  if (!result.hasOwnProperty('Optimization')) {
    result['Optimization'] = 0
  }

  console.log('Service breakdown:', serviceBreakdown.value)
  console.log('Normalized service breakdown:', result)

  return result
})

// Methods
async function fetchUsageHistory() {
  loading.value = true
  error.value = ''

  try {
    // Apply filters to the store
    usageStore.setFilters({
      startDate: filters.value.startDate ? new Date(filters.value.startDate) : null,
      endDate: filters.value.endDate ? new Date(filters.value.endDate) : null,
      service: filters.value.service || null,
      model: filters.value.model || null,
      status: filters.value.status || null
    })

    // Fetch usage history with applied filters
    await usageStore.fetchUsageHistory(true)
  } catch (err) {
    console.error('Error fetching usage history:', err)
    error.value = 'Failed to fetch usage history'
  } finally {
    loading.value = false
  }
}

async function loadMore() {
  if (loading.value) return

  loading.value = true
  try {
    await usageStore.loadMoreUsageRecords()
  } catch (err) {
    console.error('Error loading more records:', err)
  } finally {
    loading.value = false
  }
}

function formatAmount(amount: number, service: string) {
  if (service === 'transcription') {
    if (amount < 1) {
      // Convert to seconds and display with up to 3 decimal places
      const seconds = amount * 60;
      // Remove trailing zeros
      return `${parseFloat(seconds.toFixed(3))} secs`;
    }
    // For whole numbers, don't show decimal places
    if (Math.floor(amount) === amount) {
      return `${Math.floor(amount)} mins`;
    }
    // For numbers with decimals, show up to 3 decimal places but remove trailing zeros
    return `${parseFloat(amount.toFixed(3))} mins`;
  } else if (service === 'optimization') {
    return `${Math.round(amount)} tokens`;
  }
  return amount.toString();
}

function formatCost(cost: number) {
  // Format cost with appropriate decimal places up to 7, removing trailing zeros
  if (cost === 0) return '0.00';

  // Format with up to 7 decimal places, but remove trailing zeros
  const formatted = cost.toFixed(7);
  return parseFloat(formatted).toString();
}

function formatCostTwoDecimals(cost: number) {
  // Format cost with exactly 2 decimal places for summary cards
  if (cost === 0) return '0.00';
  return cost.toFixed(2);
}

function formatPricingModel(model: string) {
  if (model === 'credits') return 'Credits'
  if (model === 'payg') return 'Pay-as-you-go'
  if (model === 'subscription') return 'Subscription'
  return model
}

function formatDate(dateString: string) {
  const date = new Date(dateString)
  return date.toLocaleDateString([], { year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' })
}

function getStatusClass(status: string) {
  switch (status) {
    case 'success':
      return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
    case 'error':
      return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
    case 'pending':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
  }
}

function applyFilters() {
  fetchUsageHistory()
}

function clearFilters() {
  filters.value = {
    startDate: '',
    endDate: '',
    service: '',
    model: '',
    status: ''
  }
  usageStore.clearFilters()
  fetchUsageHistory()
}

function exportUsage() {
  // Show loading indicator
  exportLoading.value = true
  
  try {
    // Show toast notification to inform user
    toastService.info('Preparing Export', 'Fetching usage data for export...', {
      duration: 0, // Don't auto-close this toast
      id: 'export-toast' // Use a fixed ID so we can update/close it later
    });
    
    // Prepare filters for export (same as current view)
    const exportStartDate = filters.value.startDate ? new Date(filters.value.startDate) : null
    const exportEndDate = filters.value.endDate ? new Date(filters.value.endDate) : null
    
    // Create a descriptive filename with the date range
    let filename = 'voicehype-usage'
    if (exportStartDate && exportEndDate) {
      const startStr = exportStartDate.toISOString().split('T')[0]
      const endStr = exportEndDate.toISOString().split('T')[0]
      filename += `-${startStr}-to-${endStr}`
    } else if (exportStartDate) {
      const startStr = exportStartDate.toISOString().split('T')[0]
      filename += `-from-${startStr}`
    } else if (exportEndDate) {
      const endStr = exportEndDate.toISOString().split('T')[0]
      filename += `-until-${endStr}`
    }
    filename += '.csv'
    
    // Export the data - the store.getAllUsageHistory will handle the next-day conversion
    usageStore.exportUsageHistory(
      exportStartDate,
      exportEndDate,
      filters.value.service || null,
      filters.value.model || null,
      filters.value.status || null,
      filename
    ).then(() => {
      exportLoading.value = false
      // Update the toast to success when completed
      toastService.hide('export-toast');
      toastService.success('Export Complete', 'Your usage data has been exported successfully.');
    }).catch(err => {
      console.error('Error exporting usage data:', err)
      error.value = 'Failed to export usage data'
      exportLoading.value = false
      // Update the toast to error
      toastService.hide('export-toast');
      toastService.error('Export Failed', 'There was a problem exporting your usage data.');
    })
  } catch (err) {
    console.error('Error preparing export:', err)
    error.value = 'Failed to prepare export'
    exportLoading.value = false
    // Update the toast to error
    toastService.hide('export-toast');
    toastService.error('Export Failed', 'There was a problem preparing your export.');
  }
}

// Set current month filter
function setCurrentMonthFilter() {
  const { startDate, endDate } = usageStore.getCurrentMonthDateRange()
  // For the start date, use the first day of the month
  filters.value.startDate = startDate.toISOString().split('T')[0]
  
  // For the end date in the UI, use the last day of the month (one day before the next month)
  const lastDayOfMonth = new Date(endDate);
  lastDayOfMonth.setDate(lastDayOfMonth.getDate() - 1);
  filters.value.endDate = lastDayOfMonth.toISOString().split('T')[0]
  
  // Fetch with these filters - the store will handle conversion to next-day approach
  fetchUsageHistory()
}

// Helper functions for token display
function getInputTokens(metadata: any): number | null {
  if (!metadata) return null
  return metadata.input_tokens || metadata.inputTokens || null
}

function getOutputTokens(metadata: any): number | null {
  if (!metadata) return null
  return metadata.output_tokens || metadata.outputTokens || null
}

// Helper function to adjust end date for proper filtering
function adjustEndDate(date: Date): Date {
  const nextDay = new Date(date);
  // Add one day to get the start of the next day
  nextDay.setDate(nextDay.getDate() + 1);
  // Set time to beginning of day (00:00:00)
  nextDay.setHours(0, 0, 0, 0);
  return nextDay;
}

// Lifecycle hooks
onMounted(async () => {
  // Set default filter to current month
  setCurrentMonthFilter()
})
</script>
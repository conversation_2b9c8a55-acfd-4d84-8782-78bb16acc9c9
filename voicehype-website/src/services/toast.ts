import { ref, reactive } from 'vue';

// Define the type for a toast notification
export interface ToastOptions {
  id?: string;
  title: string;
  message?: string;
  type?: 'success' | 'error' | 'info' | 'warning';
  duration?: number;
  position?: string;
  autoClose?: boolean;
}

// Create a reactive array to store all toasts
const toasts = reactive<ToastOptions[]>([]);

// Service to manage toast notifications
export const useToast = () => {
  // Add a new toast
  const show = (options: ToastOptions) => {
    const id = options.id || Math.random().toString(36).substring(2, 9);
    const toast = {
      id,
      title: options.title,
      message: options.message || '',
      type: options.type || 'info',
      duration: options.duration || 5000,
      position: options.position || 'sm:items-end justify-end', // bottom right by default
      autoClose: options.autoClose !== undefined ? options.autoClose : true,
    };
    
    // Add the toast to the array
    toasts.push(toast);
    
    // Return the ID so it can be used to dismiss the toast
    return id;
  };

  // Remove a toast by ID
  const hide = (id: string) => {
    const index = toasts.findIndex((toast) => toast.id === id);
    if (index !== -1) {
      toasts.splice(index, 1);
    }
  };

  // Helper methods for common toast types
  const success = (title: string, message?: string, options?: Partial<ToastOptions>) => {
    return show({
      title,
      message,
      type: 'success',
      ...options,
    });
  };

  const error = (title: string, message?: string, options?: Partial<ToastOptions>) => {
    return show({
      title,
      message,
      type: 'error',
      ...options,
    });
  };

  const info = (title: string, message?: string, options?: Partial<ToastOptions>) => {
    return show({
      title,
      message,
      type: 'info',
      ...options,
    });
  };

  const warning = (title: string, message?: string, options?: Partial<ToastOptions>) => {
    return show({
      title,
      message,
      type: 'warning',
      ...options,
    });
  };

  return {
    toasts,
    show,
    hide,
    success,
    error,
    info,
    warning,
  };
};

// Export a singleton instance
export default useToast();

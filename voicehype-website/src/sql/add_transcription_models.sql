-- SQL to add AssemblyA<PERSON> and Whisper models to the models table
-- These models are for transcription services

-- Insert AssemblyAI models
INSERT INTO models (id, name, description, model_type, is_active, created_at, updated_at) VALUES
(
  'assembly-ai-best',
  'AssemblyAI Best',
  'High accuracy transcription model optimized for various accents and domains',
  'transcription',
  true,
  NOW(),
  NOW()
),
(
  'assembly-ai-nano',
  'AssemblyAI Nano',
  'Faster transcription with small quality tradeoff',
  'transcription',
  true,
  NOW(),
  NOW()
),
(
  'assembly-ai-best-realtime',
  'AssemblyAI Best Realtime',
  'Real-time high accuracy transcription',
  'transcription',
  true,
  NOW(),
  NOW()
);

-- Insert Whisper model
INSERT INTO models (id, name, description, model_type, is_active, created_at, updated_at) VALUES
(
  'whisper-1',
  'OpenAI Whisper',
  'High-quality speech recognition model by OpenAI',
  'transcription',
  true,
  NOW(),
  NOW()
);

-- Verify the insertions
SELECT * FROM models WHERE model_type = 'transcription' ORDER BY name ASC;
// Paddle.js Integration for VoiceHype
// This file handles client-side Paddle integration

import { ref, readonly } from 'vue'

interface PaddleInstance {
  Environment: {
    set: (environment: 'sandbox' | 'production') => void
  }
  Initialize: (options: { token: string, checkout?: { settings?: CheckoutOptions['settings'] } }) => void
  Checkout: {
    open: (options: CheckoutOptions) => void
  }
  Update: (options: { token?: string }) => void
}

interface CheckoutOptions {
  items?: Array<{
    priceId: string
    quantity: number
  }>
  customData?: Record<string, any>
  customer?: {
    email?: string
  }
  settings?: {
    successUrl?: string
    theme?: 'light' | 'dark'
    locale?: string
    displayMode?: 'inline' | 'overlay'
    frameTarget?: string
    frameInitialHeight?: number
    frameStyle?: string
    variant?: 'multi-page' | 'one-page'
    allowLogout?: boolean
    showAddDiscounts?: boolean
    allowDiscountRemoval?: boolean
    showAddTaxId?: boolean
  }
}

declare global {
  interface Window {
    Paddle?: PaddleInstance
  }
}

class PaddleService {
  private isInitialized = false
  private isLoading = false
  private initPromise: Promise<void> | null = null
  private lastError: Error | null = null

  /**
   * Initialize Paddle.js
   */
  async init(): Promise<void> {
    if (this.isInitialized) {
      console.log('Paddle already initialized, skipping initialization')
      return
    }

    if (this.isLoading) {
      console.log('Paddle initialization already in progress, waiting...')
      return this.initPromise!
    }

    console.log('Starting Paddle initialization...')
    this.isLoading = true
    this.initPromise = this.loadPaddle()

    try {
      await this.initPromise
      this.isInitialized = true
      console.log('Paddle initialization completed successfully')
      this.lastError = null
    } catch (error) {
      this.lastError = error instanceof Error ? error : new Error(String(error))
      console.error('Paddle initialization failed:', this.lastError)
      throw this.lastError
    } finally {
      this.isLoading = false
    }
  }

  /**
   * Load Paddle.js script and initialize
   */
  private async loadPaddle(): Promise<void> {
    return new Promise((resolve, reject) => {
      // Check if Paddle is already loaded
      if (window.Paddle) {
        console.log('Paddle script already loaded, setting up...')
        try {
          this.setupPaddle()
          resolve()
        } catch (error) {
          console.error('Error during Paddle setup:', error)
          reject(error)
        }
        return
      }

      console.log('Loading Paddle script from CDN...')
      // Create script element
      const script = document.createElement('script')
      script.src = 'https://cdn.paddle.com/paddle/v2/paddle.js'
      script.async = true

      script.onload = () => {
        console.log('Paddle script loaded, waiting for initialization...')
        // Wait a bit for Paddle to be available
        setTimeout(() => {
          if (window.Paddle) {
            try {
              console.log('Paddle object available, setting up...')
              this.setupPaddle()
              resolve()
            } catch (error) {
              console.error('Error during Paddle setup:', error)
              reject(error)
            }
          } else {
            const error = new Error('Paddle failed to load - window.Paddle is undefined')
            console.error(error)
            reject(error)
          }
        }, 200) // Increased timeout to 200ms to ensure Paddle is fully loaded
      }

      script.onerror = () => {
        const error = new Error('Failed to load Paddle script')
        console.error(error)
        reject(error)
      }

      document.head.appendChild(script)
    })
  }

  /**
   * Setup Paddle with environment and token
   */
  private setupPaddle(): void {
    if (!window.Paddle) {
      const error = new Error('Paddle is not available')
      console.error(error)
      throw error
    }

    const environment = import.meta.env.VITE_PADDLE_ENVIRONMENT || 'sandbox'
    const token = import.meta.env.VITE_PADDLE_CLIENT_TOKEN

    console.log(`Setting up Paddle in ${environment} environment`)
    
    if (!token) {
      const error = new Error('Paddle client token is not configured in environment variables')
      console.error(error, 'Check your .env file for VITE_PADDLE_CLIENT_TOKEN')
      throw error
    }

    try {
      // Set environment
      console.log('Setting Paddle environment...')
      window.Paddle.Environment.set(environment as 'sandbox' | 'production')

      // Initialize with token and default checkout settings
      console.log('Initializing Paddle with token...')
      window.Paddle.Initialize({ 
        token,
        checkout: {
          settings: {
            locale: 'en',
            theme: 'light',
            allowLogout: true,
            showAddDiscounts: true,
            allowDiscountRemoval: true,
            showAddTaxId: true,
            variant: 'multi-page'
          }
        }
      })

      // Validate initialization was successful
      if (!window.Paddle.Checkout) {
        throw new Error('Paddle initialization failed - Checkout API not available')
      }

      console.log(`Paddle initialized successfully in ${environment} mode`)
    } catch (error) {
      console.error('Error during Paddle initialization:', error)
      throw error
    }
  }

  /**
   * Open Paddle overlay checkout
   * 
   * Opens a Paddle checkout in overlay mode, which appears as a modal over the current page.
   * This is the recommended checkout flow according to Paddle documentation.
   */
  async openCheckout(options: CheckoutOptions): Promise<void> {
    await this.init()

    if (!window.Paddle) {
      const error = new Error('Paddle is not initialized')
      console.error(error)
      throw error
    }

    // Set default options
    const defaultOptions: CheckoutOptions = {
      items: options.items, // Ensure items are passed correctly
      customData: options.customData || {},
      customer: options.customer || {},
      settings: {
        // Apply user settings first
        ...(options.settings || {}),
        // Then forcefully override with our required settings to ensure they take precedence
        displayMode: 'overlay', // Use standard overlay mode, not wide-overlay
        theme: document.documentElement.classList.contains('dark') ? 'dark' : 'light',
        successUrl: options.settings?.successUrl || `${window.location.origin}/payments?success=true`,
        variant: 'multi-page', // Use multi-page variant for better compatibility
        allowLogout: true,
        showAddDiscounts: true,
        allowDiscountRemoval: true,
        showAddTaxId: true,
      }
    }

    console.log('Opening Paddle checkout with options:', JSON.stringify(defaultOptions, null, 2))
    
    // Add event listeners for Paddle events
    document.addEventListener('paddle:checkout:completed', this.handleCheckoutCompleted)
    document.addEventListener('paddle:checkout:closed', this.handleCheckoutClosed)
    document.addEventListener('paddle:checkout:error', this.handleCheckoutError)
    
    window.Paddle.Checkout.open(defaultOptions)
  }
  
  /**
   * Handle checkout completed event
   */
  private handleCheckoutCompleted = (event: any): void => {
    console.log('Paddle checkout completed:', event)
    document.removeEventListener('paddle:checkout:completed', this.handleCheckoutCompleted)
    document.removeEventListener('paddle:checkout:closed', this.handleCheckoutClosed)
    document.removeEventListener('paddle:checkout:error', this.handleCheckoutError)
  }
  
  /**
   * Handle checkout closed event
   */
  private handleCheckoutClosed = (event: any): void => {
    console.log('Paddle checkout closed:', event)
    document.removeEventListener('paddle:checkout:completed', this.handleCheckoutCompleted)
    document.removeEventListener('paddle:checkout:closed', this.handleCheckoutClosed)
    document.removeEventListener('paddle:checkout:error', this.handleCheckoutError)
  }
  
  /**
   * Handle checkout error event
   */
  private handleCheckoutError = (event: any): void => {
    console.error('Paddle checkout error:', event)
    // Provide more detailed error info if available
    if (event.detail && event.detail.error) {
      console.error('Error details:', {
        code: event.detail.error.code,
        message: event.detail.error.message,
        statusCode: event.detail.error.statusCode
      })
    }
    document.removeEventListener('paddle:checkout:completed', this.handleCheckoutCompleted)
    document.removeEventListener('paddle:checkout:closed', this.handleCheckoutClosed)
    document.removeEventListener('paddle:checkout:error', this.handleCheckoutError)
  }

  /**
   * Update Paddle configuration (useful for token updates)
   */
  async updateConfig(options: { token?: string }): Promise<void> {
    await this.init()

    if (!window.Paddle) {
      const error = new Error('Paddle is not initialized')
      console.error(error)
      throw error
    }

    console.log('Updating Paddle configuration:', options)
    window.Paddle.Update(options)
  }

  /**
   * Check if Paddle is initialized
   */
  get initialized(): boolean {
    return this.isInitialized
  }

  /**
   * Get last error during initialization
   */
  get error(): Error | null {
    return this.lastError
  }

  /**
   * Get Paddle instance (for advanced usage)
   */
  get instance(): PaddleInstance | undefined {
    return window.Paddle
  }

  /**
   * Cancel a subscription at period end
   * 
   * This cancels the subscription but allows access until the end of the current billing period
   */
  async cancelSubscription(subscriptionId: string): Promise<void> {
    await this.init()

    if (!window.Paddle) {
      const error = new Error('Paddle is not initialized')
      console.error(error)
      throw error
    }

    console.log('Cancelling subscription:', subscriptionId)
    
    // Note: Paddle.js doesn't have direct subscription management APIs in the frontend
    // We'll simulate the call here and rely on the backend webhook to handle the actual cancellation
    // In a real implementation, you would call your backend API which then calls Paddle's server-side API
    
    try {
      // This would typically be a call to your backend API
      // For now, we'll just log it and let the database update handle the state
      console.log(`Subscription ${subscriptionId} marked for cancellation`)
      
      // In production, this should make an API call to your backend:
      // const response = await fetch('/api/paddle/cancel-subscription', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ subscriptionId })
      // })
      // if (!response.ok) throw new Error('Cancellation failed')
      
    } catch (error) {
      console.error('Error cancelling subscription:', error)
      throw new Error('Failed to cancel subscription')
    }
  }

  /**
   * Resume a cancelled subscription
   * 
   * This undoes a cancellation and resumes the subscription
   */
  async resumeSubscription(subscriptionId: string): Promise<void> {
    await this.init()

    if (!window.Paddle) {
      const error = new Error('Paddle is not initialized')
      console.error(error)
      throw error
    }

    console.log('Resuming subscription:', subscriptionId)
    
    try {
      // This would typically be a call to your backend API
      // For now, we'll just log it and let the database update handle the state
      console.log(`Subscription ${subscriptionId} resumed`)
      
      // In production, this should make an API call to your backend:
      // const response = await fetch('/api/paddle/resume-subscription', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ subscriptionId })
      // })
      // if (!response.ok) throw new Error('Resume failed')
      
    } catch (error) {
      console.error('Error resuming subscription:', error)
      throw new Error('Failed to resume subscription')
    }
  }
}

// Create singleton instance
export const paddleService = new PaddleService()

// Vue composable for Paddle
export function usePaddle() {
  const isInitialized = ref(false)
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const debugInfo = ref<Record<string, any>>({})

  const init = async () => {
    if (isInitialized.value) return

    isLoading.value = true
    error.value = null
    debugInfo.value = {
      startTime: new Date().toISOString(),
      environment: import.meta.env.VITE_PADDLE_ENVIRONMENT || 'sandbox',
      hasToken: Boolean(import.meta.env.VITE_PADDLE_CLIENT_TOKEN),
      tokenLength: import.meta.env.VITE_PADDLE_CLIENT_TOKEN ? import.meta.env.VITE_PADDLE_CLIENT_TOKEN.length : 0
    }

    try {
      await paddleService.init()
      isInitialized.value = true
      debugInfo.value = {
        ...debugInfo.value,
        endTime: new Date().toISOString(),
        success: true,
        paddleAvailable: Boolean(window.Paddle)
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to initialize Paddle'
      console.error('Paddle initialization error:', err)
      debugInfo.value = {
        ...debugInfo.value,
        endTime: new Date().toISOString(),
        success: false,
        errorMessage: err.message || 'Unknown error',
        errorStack: err.stack
      }
    } finally {
      isLoading.value = false
    }
  }

  const openCheckout = async (options: CheckoutOptions) => {
    try {
      await paddleService.openCheckout(options)
    } catch (err: any) {
      error.value = err.message || 'Failed to open checkout'
      console.error('Paddle checkout error:', err)
      throw err
    }
  }
  
  const forceRetryInit = async () => {
    isInitialized.value = false;
    paddleService['isInitialized'] = false;
    return init();
  }

  // Initialize Paddle when this composable is first used
  init().catch(console.error)

  return {
    isInitialized: readonly(isInitialized),
    isLoading: readonly(isLoading),
    error: readonly(error),
    debugInfo: readonly(debugInfo),
    init,
    forceRetryInit,
    openCheckout,
    paddleService
  }
}

// Auto-initialize Paddle when this module is imported
if (typeof window !== 'undefined') {
  paddleService.init().catch(console.error)
}

export default paddleService

import { createClient } from '@supabase/supabase-js'

// These will be replaced with actual values from environment variables
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || ''
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || ''

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Helper functions for API operations
export async function validateApiKey(key: string) {
  const { data, error } = await supabase.rpc('validate_api_key', { p_key: key })
  return { data, error }
}

export async function createApiKey(name: string, expiresAt?: Date) {
  const { data, error } = await supabase.rpc('create_api_key', {
    p_name: name,
    p_expires_at: expiresAt
  })

  // The data now contains both id and key_secret
  return { data, error }
}

export async function updateApiKeyName(apiKeyId: string, name: string) {
  const { data, error } = await supabase.rpc('update_api_key_name', {
    p_key_id: apiKeyId,
    p_name: name
  })
  return { data, error }
}

export async function updateApiKeyStatus(apiKeyId: string, isActive: boolean) {
  const { data, error } = await supabase.rpc('update_api_key_status', {
    p_key_id: apiKeyId,
    p_is_active: isActive
  })
  return { data, error }
}

export async function checkUsageAllowance(
  userId: string,
  service: string,
  model: string,
  amount: number,
  apiKeyId?: string
) {
  const { data, error } = await supabase.rpc('check_usage_allowance', {
    p_user_id: userId,
    p_service: service,
    p_model: model,
    p_amount: amount,
    p_api_key_id: apiKeyId
  })
  return { data, error }
}

export async function getUnpaidPaygBalances(userId: string) {
  const { data, error } = await supabase.rpc('get_unpaid_payg_balances', {
    p_user_id: userId
  })
  return { data, error }
}

export async function hasUnpaidPaygBalance(userId: string) {
  const { data, error } = await supabase.rpc('has_unpaid_payg_balance', {
    p_user_id: userId
  })
  return { data, error }
}

export async function getUsageSummaryStatistics(
  userId: string,
  startDate?: Date,
  endDate?: Date,
  service?: string,
  model?: string,
  status?: string
) {
  try {
    // For the end date, use the beginning of the next day (more reliable across timezones)
    let adjustedEndDate = endDate;
    if (endDate) {
      // Create a new date to avoid modifying the original
      const nextDay = new Date(endDate);
      // Add one day to the selected end date
      nextDay.setDate(nextDay.getDate() + 1);
      // Set time to beginning of day (00:00:00)
      nextDay.setHours(0, 0, 0, 0);
      adjustedEndDate = nextDay;
    }
    
    // Cast the userId string to UUID using Postgres type casting
    const { data, error } = await supabase.rpc('get_usage_summary_statistics', {
      p_user_id: userId,
      p_start_date: startDate?.toISOString() || null,
      p_end_date: adjustedEndDate?.toISOString() || null,
      p_service: service || null,
      p_model: model || null,
      p_status: status || null
    });

    if (error) {
      console.error('Error fetching usage summary statistics:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in getUsageSummaryStatistics:', error);
    throw error;
  }
}

export async function getAllUsageHistory(
  userId: string,
  startDate?: Date,
  endDate?: Date,
  service?: string,
  model?: string,
  status?: string
) {
  try {
    let query = supabase
      .from('usage_history')
      .select('*')
      .order('created_at', { ascending: false })

    // Apply filters if they exist
    if (userId) {
      query = query.eq('user_id', userId)
    }

    if (startDate) {
      query = query.gte('created_at', startDate.toISOString())
    }

    // For the end date, use the beginning of the next day
    if (endDate) {
      // Create a new date for the next day without modifying the original
      const nextDay = new Date(endDate);
      // Add one day to the selected end date
      nextDay.setDate(nextDay.getDate() + 1);
      // Set time to beginning of day (00:00:00)
      nextDay.setHours(0, 0, 0, 0);
      query = query.lt('created_at', nextDay.toISOString())
    }

    if (service) {
      query = query.eq('service', service)
    }

    if (model) {
      query = query.eq('model', model)
    }

    if (status) {
      query = query.eq('status', status)
    }

    const { data, error } = await query

    if (error) {
      console.error('Error fetching all usage history:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in getAllUsageHistory:', error);
    throw error;
  }
}
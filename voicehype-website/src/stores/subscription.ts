import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { supabase } from '@/lib/supabase'
import { paddleService } from '@/lib/paddle'

interface SubscriptionPlan {
  id: string
  name: string
  description: string
  monthly_price: number
  annual_price: number | null
  transcription_minutes: number
  input_tokens: number
  output_tokens: number
  tokens?: number
  is_active: boolean
  created_at: string
}

interface UserSubscription {
  id: string
  user_id: string
  plan_id: string
  status: string
  current_period_start: string
  current_period_end: string
  next_billed_at: string
  cancel_at_period_end: boolean
  created_at: string
  stripe_subscription_id: string | null
  paddle_subscription_id: string | null
}

interface Quota {
  id: string
  user_id: string
  subscription_id: string
  service: string
  used_amount: number
  total_amount: number
  reset_date: string
  created_at: string
}

export const useSubscriptionStore = defineStore('subscription', () => {
  const availablePlans = ref<SubscriptionPlan[]>([])
  const userSubscription = ref<UserSubscription | null>(null)
  const quotas = ref<Quota[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Computed property to get the current plan
  const currentPlan = computed(() => {
    if (!userSubscription.value || !availablePlans.value.length) return null

    return availablePlans.value.find(plan => plan.id === userSubscription.value?.plan_id) || null
  })

  // Computed property to check if user has an active subscription
  const hasActiveSubscription = computed(() => {
    if (!userSubscription.value) return false

    // Only check status - don't filter by date since we have 30-day grace period
    // Include both 'active' and 'past_due' status - past_due means customer still has access during dunning period
    const isActiveOrPastDue = userSubscription.value.status === 'active' || userSubscription.value.status === 'past_due'

    return isActiveOrPastDue
  })

  // Computed property to check if user has a subscription (active or expired)
  const hasSubscription = computed(() => {
    return userSubscription.value !== null
  })

  // Computed property to check if subscription is expired
  const isSubscriptionExpired = computed(() => {
    if (!userSubscription.value) return false

    // With 30-day grace period, only consider subscription expired if status indicates it
    // Don't rely on current_period_end date since grace period extends access
    return userSubscription.value.status === 'expired' || userSubscription.value.status === 'canceled'
  })

  // Computed property to check if user is on a paid Paddle subscription
  const isPaidSubscription = computed(() => {
    if (!userSubscription.value) return false
    
    // Check if it's a Paddle subscription (has paddle_subscription_id)
    // and it's not a free trial plan
    return Boolean(
      userSubscription.value.paddle_subscription_id && 
      currentPlan.value && 
      currentPlan.value.monthly_price > 0 &&
      currentPlan.value.name !== 'Free Trial'
    )
  })

  // Computed property to check if user is on a free subscription (non-Paddle)
  const isFreeSubscription = computed(() => {
    if (!userSubscription.value) return false
    
    // Check if it's a subscription without paddle_subscription_id 
    // OR it's a free trial plan
    // OR it has $0 monthly price
    return Boolean(
      !userSubscription.value.paddle_subscription_id || 
      currentPlan.value?.name === 'Free Trial' ||
      currentPlan.value?.monthly_price === 0
    )
  })

  // Computed property to check if user should see upgrade buttons
  const canUpgradeSubscription = computed(() => {
    // Only show upgrade buttons for paid Paddle subscriptions
    // Free subscription users should see "Buy" buttons instead
    return isPaidSubscription.value
  })

  // Fetch available subscription plans
  async function fetchAvailablePlans() {
    loading.value = true
    error.value = null

    try {
      const { data, error: fetchError } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('is_active', true)
        .order('monthly_price', { ascending: true })

      if (fetchError) {
        error.value = fetchError.message
        return false
      }

      availablePlans.value = data as SubscriptionPlan[]
      return true
    } catch (err) {
      console.error('Error fetching subscription plans:', err)
      error.value = 'Failed to fetch subscription plans'
      return false
    } finally {
      loading.value = false
    }
  }

  // Fetch user's current subscription
  async function fetchUserSubscription() {
    loading.value = true
    error.value = null

    try {
      console.log('🔍 Starting fetchUserSubscription...')
      
      // First try to get active or past_due subscription (grace period)
      let { data, error: fetchError } = await supabase
        .from('user_subscriptions')
        .select('*')
        .in('status', ['active', 'past_due'])
        .order('current_period_end', { ascending: false })
        .limit(1)

      console.log('🔍 Active/past_due subscription query result:', {
        hasData: data && data.length > 0,
        dataLength: data?.length || 0,
        hasError: !!fetchError,
        errorCode: fetchError?.code,
        errorMessage: fetchError?.message
      })

      // If no active/past_due subscription found, get the most recent subscription regardless of status
      if (!data || data.length === 0) {
        console.log('🔍 No active/past_due subscription found, fetching most recent...')
        
        const { data: allData, error: allError } = await supabase
          .from('user_subscriptions')
          .select('*')
          .order('current_period_end', { ascending: false })
          .limit(1)

        data = allData
        fetchError = allError
        
        console.log('🔍 Most recent subscription query result:', {
          hasData: allData && allData.length > 0,
          dataLength: allData?.length || 0,
          hasError: !!allError,
          errorCode: allError?.code,
          errorMessage: allError?.message
        })
      }

      if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 is "No rows returned" error
        console.error('❌ Error fetching subscription:', fetchError)
        error.value = fetchError.message
        return false
      }

      userSubscription.value = data && data.length > 0 ? data[0] as UserSubscription : null

      console.log('🔍 Setting userSubscription.value:', {
        hasValue: userSubscription.value !== null,
        id: userSubscription.value?.id,
        status: userSubscription.value?.status,
        nextBilledAt: userSubscription.value?.next_billed_at,
        currentPeriodEnd: userSubscription.value?.current_period_end
      })

      // If we have a subscription, fetch the quotas
      if (userSubscription.value) {
        await fetchQuotas()
      }

      return true
    } catch (err) {
      console.error('Error fetching user subscription:', err)
      error.value = 'Failed to fetch user subscription'
      return false
    } finally {
      loading.value = false
    }
  }

  // Fetch quotas for the current subscription
  async function fetchQuotas() {
    if (!userSubscription.value) return false

    loading.value = true
    error.value = null

    try {
      const { data, error: fetchError } = await supabase
        .from('quotas')
        .select('*')
        .eq('subscription_id', userSubscription.value.id)

      if (fetchError) {
        error.value = fetchError.message
        return false
      }

      quotas.value = data as Quota[]
      console.log('Fetched quotas:', quotas.value) // Debug log
      return true
    } catch (err) {
      console.error('Error fetching quotas:', err)
      error.value = 'Failed to fetch quotas'
      return false
    } finally {
      loading.value = false
    }
  }

  // Get Paddle management URLs for subscription management
  async function getSubscriptionManagementUrls() {
    if (!userSubscription.value?.paddle_subscription_id) {
      error.value = 'No subscription found'
      return null
    }

    loading.value = true
    error.value = null

    try {
      // Call our backend to get subscription details including management URLs
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        error.value = 'Not authenticated'
        return null
      }

      const response = await fetch('/functions/v1/get-subscription-management', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subscriptionId: userSubscription.value.paddle_subscription_id
        })
      })

      const result = await response.json()

      if (!response.ok || !result.success) {
        error.value = result.error || 'Failed to get management URLs'
        return null
      }

      return result.managementUrls

    } catch (err: any) {
      console.error('Get management URLs error:', err)
      error.value = err.message || 'Failed to get management URLs'
      return null
    } finally {
      loading.value = false
    }
  }

  // Cancel subscription using secure edge function
  async function cancelSubscription() {
    if (!userSubscription.value) return false

    loading.value = true
    error.value = null

    try {
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        error.value = 'Not authenticated'
        return false
      }

      const response = await supabase.functions.invoke('cancel-subscription', {
        body: { action: 'cancel' }
      })

      if (response.error) {
        error.value = response.error.message || 'Failed to cancel subscription'
        return false
      }

      // If already cancelled, that's still a success
      if (response.data.already_cancelled) {
        console.log('Subscription already scheduled for cancellation')
        return true
      }

      // Refresh subscription data to show updated cancellation status
      await fetchUserSubscription()
      return true

    } catch (err: any) {
      console.error('Cancel subscription error:', err)
      error.value = err.message || 'Failed to initiate cancellation'
      return false
    } finally {
      loading.value = false
    }
  }

  // Resume a canceled subscription (undo cancellation)
  async function resumeSubscription() {
    if (!userSubscription.value) return false

    loading.value = true
    error.value = null

    try {
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        error.value = 'Not authenticated'
        return false
      }

      const response = await supabase.functions.invoke('cancel-subscription', {
        body: { action: 'undo' }
      })

      if (response.error) {
        error.value = response.error.message || 'Failed to undo cancellation'
        return false
      }

      // If not cancelled, that's still a success
      if (response.data.not_cancelled) {
        console.log('Subscription is not scheduled for cancellation')
        return true
      }

      // Refresh subscription data to show updated status
      await fetchUserSubscription()
      return true

    } catch (err: any) {
      console.error('Resume subscription error:', err)
      error.value = err.message || 'Failed to resume subscription'
      return false
    } finally {
      loading.value = false
    }
  }

  // Get quota usage for a specific service
  function getQuotaUsage(service: string) {
    const quota = quotas.value.find(q => q.service === service)

    if (!quota) return { used: 0, total: 0, percentage: 0 }

    const used = quota.used_amount
    const total = quota.total_amount
    const percentage = total > 0 ? (used / total) * 100 : 0

    return { used, total, percentage }
  }

  // Create subscription checkout with Paddle
  async function createSubscriptionCheckout(params: { planId: string, billingCycle: 'monthly' | 'annual' } | 'Basic' | 'Pro' | 'Premium') {
    loading.value = true
    error.value = null

    try {
      // Get user session
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        throw new Error('No session')
      }

      // Get plan-specific price ID from environment variables
      const getPriceId = (planName: 'Basic' | 'Pro' | 'Premium', environment: 'production' | 'sandbox') => {
        const envVarMap: Record<'Basic' | 'Pro' | 'Premium', Record<'production' | 'sandbox', string>> = {
          'Basic': {
            production: import.meta.env.VITE_PADDLE_PRICE_ID_BASIC_PRODUCTION,
            sandbox: import.meta.env.VITE_PADDLE_PRICE_ID_BASIC_SANDBOX
          },
          'Pro': {
            production: import.meta.env.VITE_PADDLE_PRICE_ID_PRO_PRODUCTION,
            sandbox: import.meta.env.VITE_PADDLE_PRICE_ID_PRO_SANDBOX
          },
          'Premium': {
            production: import.meta.env.VITE_PADDLE_PRICE_ID_PREMIUM_PRODUCTION,
            sandbox: import.meta.env.VITE_PADDLE_PRICE_ID_PREMIUM_SANDBOX
          }
        }
        return envVarMap[planName][environment]
      }

      // Handle different parameter types
      let planName: string;
      let billingCycle: 'monthly' | 'annual' = 'monthly';

      if (typeof params === 'string') {
        // Legacy format
        planName = params;
      } else {
        // New format with planId and billingCycle
        const plan = availablePlans.value.find((p: SubscriptionPlan) => p.id === params.planId);
        if (!plan) throw new Error('Invalid plan ID');
        planName = plan.name;
        billingCycle = params.billingCycle;
      }

      const environment = import.meta.env.VITE_PADDLE_ENVIRONMENT
      const priceId = getPriceId(planName as 'Basic' | 'Pro' | 'Premium', environment as 'production' | 'sandbox')

      if (!priceId) {
        throw new Error('Paddle subscription price ID not configured')
      }

      console.log(`Creating subscription checkout for ${planName} plan (billing cycle: ${billingCycle})`)

      // Direct Paddle overlay checkout
      await paddleService.openCheckout({
        items: [{
          priceId: priceId,
          quantity: 1  // Always quantity 1 with separate price IDs
        }],
        customData: {
          subscription_plan: planName.toLowerCase(),
          user_id: session.user.id,
          billing_cycle: billingCycle
        },
        customer: {
          email: session.user.email
        },
        settings: {
          successUrl: `${window.location.origin}/app/payments?subscription_success=true`,
        }
      })

      return 'success' // Return a string value instead of boolean
    } catch (err: any) {
      console.error('Error creating subscription checkout:', err)
      error.value = err.message || 'Failed to create subscription checkout'
      return '' // Return empty string on failure
    } finally {
      loading.value = false
    }
  }

  // Get remaining quotas for upgrade preview
  function getRemainingQuotas() {
    if (!currentPlan.value || !quotas.value.length) return null

    const currentQuotas: Record<string, number> = {}
    quotas.value.forEach(quota => {
      const remaining = quota.total_amount - quota.used_amount
      currentQuotas[quota.service] = Math.max(0, remaining)
    })

    return currentQuotas
  }

  // Calculate merged quotas for upgrade preview
  function calculateMergedQuotas(targetPlan: SubscriptionPlan) {
    const remaining = getRemainingQuotas()
    if (!remaining || !targetPlan) return null

    // Get total tokens from target plan (use tokens field, or sum input + output if tokens field doesn't exist)
    const targetTotalTokens = targetPlan.tokens || (targetPlan.input_tokens + targetPlan.output_tokens)
    
    // Get remaining tokens (from optimization service, which handles all tokens in VoiceHype)
    const remainingTokens = remaining.optimization || 0

    return {
      transcription_minutes: (remaining.transcription || 0) + targetPlan.transcription_minutes,
      tokens: remainingTokens + targetTotalTokens,
      // Keep input/output for backward compatibility, but they should show same as total tokens
      input_tokens: remainingTokens + targetTotalTokens,
      output_tokens: 0 // In VoiceHype, we don't distinguish between input/output
    }
  }

  // Upgrade subscription with specified flow
  async function upgradeSubscription(
    targetPlanName: 'Basic' | 'Pro' | 'Premium',
    upgradeFlow: 'update_next_billing' | 'upgrade_now_merge_quotas'
  ) {
    loading.value = true
    error.value = null

    try {
      // Get user session
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        throw new Error('No session')
      }

      console.log(`Upgrading subscription to ${targetPlanName} plan with flow: ${upgradeFlow}`)

      // Get plan-specific price ID from environment
      const getPriceId = (planName: 'Basic' | 'Pro' | 'Premium', environment: 'production' | 'sandbox') => {
        const priceMap = {
          'Basic': {
            production: import.meta.env.VITE_PADDLE_PRICE_ID_BASIC_PRODUCTION,
            sandbox: import.meta.env.VITE_PADDLE_PRICE_ID_BASIC_SANDBOX
          },
          'Pro': {
            production: import.meta.env.VITE_PADDLE_PRICE_ID_PRO_PRODUCTION,
            sandbox: import.meta.env.VITE_PADDLE_PRICE_ID_PRO_SANDBOX
          },
          'Premium': {
            production: import.meta.env.VITE_PADDLE_PRICE_ID_PREMIUM_PRODUCTION,
            sandbox: import.meta.env.VITE_PADDLE_PRICE_ID_PREMIUM_SANDBOX
          }
        }
        return priceMap[planName][environment]
      }

      const environment = import.meta.env.VITE_PADDLE_ENVIRONMENT
      const priceId = getPriceId(targetPlanName as 'Basic' | 'Pro' | 'Premium', environment as 'production' | 'sandbox')

      if (!priceId) {
        throw new Error('Paddle subscription price ID not configured')
      }

      console.log(`Using ${import.meta.env.VITE_PADDLE_ENVIRONMENT} environment with price ID: ${priceId}`)

      // Call the backend upgrade function instead of using checkout
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/upgrade-subscription`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          targetPlan: targetPlanName,
          upgradeFlow: upgradeFlow,
          priceId: priceId
        })
      })

      // Check if response is valid before parsing JSON
      if (!response.ok) {
        const errorText = await response.text()
        console.error('Upgrade API error:', {
          status: response.status,
          statusText: response.statusText,
          body: errorText
        })
        
        // Provide more specific error messages based on status code
        let errorMessage = `Upgrade failed: ${response.status} ${response.statusText}`
        
        if (response.status === 401 || response.status === 403) {
          errorMessage = 'Authentication error: Please refresh the page and try again'
        } else if (response.status === 402) {
          errorMessage = 'Payment required: Please check your payment method'
        } else if (response.status === 429) {
          errorMessage = 'Too many requests: Please wait a moment and try again'
        } else if (response.status >= 500) {
          errorMessage = 'Server error: Please try again later or contact support'
        } else if (response.status === 400) {
          // Try to parse the error text for more specific 400 errors
          try {
            const errorData = JSON.parse(errorText)
            errorMessage = errorData.error || errorMessage
          } catch (e) {
            errorMessage = errorText || errorMessage
          }
        }
        
        throw new Error(errorMessage)
      }

      // Parse JSON response with error handling
      let result
      try {
        const responseText = await response.text()
        if (!responseText.trim()) {
          throw new Error('Empty response from upgrade API')
        }
        result = JSON.parse(responseText)
      } catch (parseError) {
        console.error('Failed to parse upgrade response:', parseError)
        throw new Error('Invalid response from upgrade API')
      }

      if (!result.success) {
        throw new Error(result.error || 'Failed to upgrade subscription')
      }

      console.log('✅ Subscription upgraded successfully:', result)
      
      // Refresh subscription data to show updated information
      await fetchUserSubscription()
      await fetchQuotas()
      
      return true
    } catch (err: any) {
      console.error('Error upgrading subscription:', err)
      error.value = err.message || 'Failed to upgrade subscription'
      return false
    } finally {
      loading.value = false
    }
  }

  // Get upgrade preview data
  function getUpgradePreview(targetPlanName: 'Basic' | 'Pro' | 'Premium') {
    const targetPlan = availablePlans.value.find(plan => 
      plan.name.toLowerCase() === targetPlanName.toLowerCase()
    )

    if (!targetPlan || !currentPlan.value) {
      return null
    }

    const remainingQuotas = getRemainingQuotas()
    const mergedQuotas = calculateMergedQuotas(targetPlan)

    return {
      currentPlan: currentPlan.value,
      targetPlan,
      remainingQuotas,
      mergedQuotas,
      isUpgrade: targetPlan.monthly_price > currentPlan.value.monthly_price,
      priceDifference: targetPlan.monthly_price - currentPlan.value.monthly_price
    }
  }

  // Alias for resumeSubscription for frontend compatibility
  async function undoCancellation() {
    return await resumeSubscription()
  }

  return {
    availablePlans,
    userSubscription,
    quotas,
    loading,
    error,
    currentPlan,
    hasActiveSubscription,
    hasSubscription,
    isSubscriptionExpired,
    isPaidSubscription,
    isFreeSubscription,
    canUpgradeSubscription,
    fetchAvailablePlans,
    fetchUserSubscription,
    fetchQuotas,
    getSubscriptionManagementUrls,
    cancelSubscription,
    resumeSubscription,
    undoCancellation,
    getQuotaUsage,
    createSubscriptionCheckout,
    upgradeSubscription,
    getUpgradePreview,
    getRemainingQuotas,
    calculateMergedQuotas
  }
})

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { supabase } from '@/lib/supabase'

interface PaddleCustomer {
  id: string
  user_id: string
  paddle_customer_id: string
  email: string
  name?: string
  country_code?: string
  created_at: string
  updated_at: string
  metadata: Record<string, any>
}

export const usePaddleCustomerStore = defineStore('paddleCustomer', () => {
  // State
  const customer = ref<PaddleCustomer | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Computed
  const hasCustomer = computed(() => Boolean(customer.value?.paddle_customer_id))
  const isPaddleCustomer = computed(() => hasCustomer.value)

  // Actions
  async function fetchPaddleCustomer() {
    loading.value = true
    error.value = null

    try {
      // Get current user session
      const { data: { session } } = await supabase.auth.getSession()
      if (!session?.user) {
        error.value = 'No authenticated user'
        return false
      }

      // Query paddle_customers table directly
      const { data, error: fetchError } = await supabase
        .from('paddle_customers')
        .select('*')
        .eq('user_id', session.user.id)
        .single()

      if (fetchError) {
        // If no record found, user is not a paddle customer
        if (fetchError.code === 'PGRST116') {
          console.log('No paddle customer record found for user')
          customer.value = null
          return false
        }
        
        // Other database errors
        console.error('Database error fetching paddle customer:', fetchError)
        error.value = fetchError.message
        customer.value = null
        return false
      }

      if (data) {
        customer.value = data
        console.log('✅ Found paddle customer:', customer.value?.paddle_customer_id)
        return true
      } else {
        customer.value = null
        return false
      }

    } catch (err: any) {
      console.warn('Failed to fetch paddle customer:', err)
      error.value = err.message || 'Failed to fetch customer data'
      customer.value = null
      return false
    } finally {
      loading.value = false
    }
  }

  // Clear customer data
  function clearCustomer() {
    customer.value = null
    error.value = null
  }

  return {
    customer,
    loading,
    error,
    hasCustomer,
    isPaddleCustomer,
    fetchPaddleCustomer,
    clearCustomer
  }
})

import { defineStore } from 'pinia'
import { ref } from 'vue'
import { supabase } from '@/lib/supabase'
import { createApi<PERSON>ey, updateApiKeyName, updateApiKeyStatus } from '@/lib/supabase'

interface ApiKey {
  id: string
  name: string
  key_prefix: string
  allow_credits: boolean
  allow_payg: boolean
  allow_subscriptions: boolean
  is_active: boolean
  created_at: string
  expires_at: string | null
  last_used_at: string | null
}

export const useApiKeysStore = defineStore('apiKeys', () => {
  const apiKeys = ref<ApiKey[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  // Clear store state
  function $reset() {
    apiKeys.value = []
    loading.value = true // Start with loading=true to show shimmer immediately
    error.value = null
  }
  
  // Fetch all API keys for the current user
  async function fetchApiKeys() {
    loading.value = true
    error.value = null
    
    try {
      const { data, error: fetchError } = await supabase
        .from('api_keys')
        .select('*')
        .order('created_at', { ascending: false })
      
      if (fetchError) {
        error.value = fetchError.message
        return false
      }
      
      apiKeys.value = data as ApiKey[]
      return true
    } catch (err) {
      console.error('Error fetching API keys:', err)
      error.value = 'Failed to fetch API keys'
      return false
    } finally {
      loading.value = false
    }
  }
  
  // Create a new API key
  async function createKey(name: string, expiresAt?: Date) {
    loading.value = true
    error.value = null
    
    try {
      const { data, error: createError } = await createApiKey(name, expiresAt)
      
      if (createError) {
        error.value = createError.message
        return null
      }
      
      // Refresh the list of API keys
      await fetchApiKeys()
      
      return data
    } catch (err) {
      console.error('Error creating API key:', err)
      error.value = 'Failed to create API key'
      return null
    } finally {
      loading.value = false
    }
  }
  
  // Update an API key's name
  async function updateKeyName(keyId: string, name: string) {
    loading.value = true
    error.value = null
    
    try {
      const { data, error: updateError } = await updateApiKeyName(keyId, name)
      
      if (updateError) {
        error.value = updateError.message
        return false
      }
      
      // Update the key in the local state
      const keyIndex = apiKeys.value.findIndex(key => key.id === keyId)
      if (keyIndex !== -1) {
        apiKeys.value[keyIndex].name = name
      }
      
      return true
    } catch (err) {
      console.error('Error updating API key name:', err)
      error.value = 'Failed to update API key name'
      return false
    } finally {
      loading.value = false
    }
  }
  
  // Deactivate an API key
  async function deactivateKey(keyId: string) {
    loading.value = true
    error.value = null
    
    try {
      const { data, error: updateError } = await updateApiKeyStatus(keyId, false)
      
      if (updateError) {
        error.value = updateError.message
        return false
      }
      
      // Update the key in the local state
      const keyIndex = apiKeys.value.findIndex(key => key.id === keyId)
      if (keyIndex !== -1) {
        apiKeys.value[keyIndex].is_active = false
      }
      
      return true
    } catch (err) {
      console.error('Error deactivating API key:', err)
      error.value = 'Failed to deactivate API key'
      return false
    } finally {
      loading.value = false
    }
  }
  
  // Activate an API key
  async function activateKey(keyId: string) {
    loading.value = true
    error.value = null
    
    try {
      const { data, error: updateError } = await updateApiKeyStatus(keyId, true)
      
      if (updateError) {
        error.value = updateError.message
        return false
      }
      
      // Update the key in the local state
      const keyIndex = apiKeys.value.findIndex(key => key.id === keyId)
      if (keyIndex !== -1) {
        apiKeys.value[keyIndex].is_active = true
      }
      
      return true
    } catch (err) {
      console.error('Error activating API key:', err)
      error.value = 'Failed to activate API key'
      return false
    } finally {
      loading.value = false
    }
  }
  
  // Delete an API key
  async function deleteKey(keyId: string) {
    loading.value = true
    error.value = null
    
    try {
      const { error: deleteError } = await supabase
        .from('api_keys')
        .delete()
        .eq('id', keyId)
      
      if (deleteError) {
        error.value = deleteError.message
        return false
      }
      
      // Remove the key from the local state
      apiKeys.value = apiKeys.value.filter(key => key.id !== keyId)
      
      return true
    } catch (err) {
      console.error('Error deleting API key:', err)
      error.value = 'Failed to delete API key'
      return false
    } finally {
      loading.value = false
    }
  }
  
  // Update API key permissions
  async function updateKeyPermissions(
    keyId: string, 
    allowCredits: boolean, 
    allowPayg: boolean, 
    allowSubscriptions: boolean
  ) {
    loading.value = true
    error.value = null
    
    try {
      const { error: updateError } = await supabase
        .from('api_keys')
        .update({
          allow_credits: allowCredits,
          allow_payg: allowPayg,
          allow_subscriptions: allowSubscriptions
        })
        .eq('id', keyId)
      
      if (updateError) {
        error.value = updateError.message
        return false
      }
      
      // Update the key in the local state
      const keyIndex = apiKeys.value.findIndex(key => key.id === keyId)
      if (keyIndex !== -1) {
        apiKeys.value[keyIndex].allow_credits = allowCredits
        apiKeys.value[keyIndex].allow_payg = allowPayg
        apiKeys.value[keyIndex].allow_subscriptions = allowSubscriptions
      }
      
      return true
    } catch (err) {
      console.error('Error updating API key permissions:', err)
      error.value = 'Failed to update API key permissions'
      return false
    } finally {
      loading.value = false
    }
  }
  
  return {
    apiKeys,
    loading,
    error,
    fetchApiKeys,
    createKey,
    updateKeyName,
    deactivateKey,
    activateKey,
    deleteKey,
    updateKeyPermissions,
    $reset
  }
}) 
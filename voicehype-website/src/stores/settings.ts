import { defineStore } from 'pinia'
import { ref } from 'vue'
import { supabase } from '@/lib/supabase'

interface Profile {
  id: string
  email: string
  full_name: string | null
  company_name: string | null
  stripe_customer_id: string | null
  default_pricing_model: string | null
  created_at: string
  updated_at: string
}

export const useSettingsStore = defineStore('settings', () => {
  const profile = ref<Profile | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const darkMode = ref(localStorage.getItem('darkMode') === 'true')
  
  // Initialize dark mode
  function initDarkMode() {
    if (darkMode.value) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }
  
  // Toggle dark mode
  function toggleDarkMode() {
    // Check if we're on a public page (landing page)
    const isPublicRoute = window.location.pathname === '/' || 
                         window.location.pathname.startsWith('/landing') ||
                         ['/blog', '/faq', '/contact', '/privacy-policy', '/terms-of-service', '/refund-policy'].includes(window.location.pathname);
    
    // If on public page, prevent turning off dark mode
    // but still allow the dashboard settings to be changed in localStorage
    if (isPublicRoute) {
      // Always ensure dark mode is enabled on public pages
      document.documentElement.classList.add('dark')
      
      // But only update the stored preference if turning dark mode ON
      if (!darkMode.value) {
        darkMode.value = true
        localStorage.setItem('darkMode', 'true')
      }
      return
    }
    
    // Normal toggle behavior for dashboard pages
    darkMode.value = !darkMode.value
    localStorage.setItem('darkMode', darkMode.value.toString())
    
    if (darkMode.value) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }
  
  // Fetch user profile
  async function fetchProfile() {
    loading.value = true
    error.value = null
    
    try {
      const { data, error: fetchError } = await supabase
        .from('profiles')
        .select('*')
        .single()
      
      if (fetchError) {
        error.value = fetchError.message
        return false
      }
      
      profile.value = data as Profile
      return true
    } catch (err) {
      console.error('Error fetching profile:', err)
      error.value = 'Failed to fetch profile'
      return false
    } finally {
      loading.value = false
    }
  }
  
  // Update user profile
  async function updateProfile(updates: Partial<Profile>) {
    if (!profile.value) return false
    
    loading.value = true
    error.value = null
    
    try {
      const { data, error: updateError } = await supabase
        .from('profiles')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', profile.value.id)
        .select()
        .single()
      
      if (updateError) {
        error.value = updateError.message
        return false
      }
      
      profile.value = data as Profile
      return true
    } catch (err) {
      console.error('Error updating profile:', err)
      error.value = 'Failed to update profile'
      return false
    } finally {
      loading.value = false
    }
  }
  
  // Update default pricing model
  async function updateDefaultPricingModel(model: string) {
    return updateProfile({ default_pricing_model: model })
  }
  
  return {
    profile,
    loading,
    error,
    darkMode,
    initDarkMode,
    toggleDarkMode,
    fetchProfile,
    updateProfile,
    updateDefaultPricingModel
  }
}) 
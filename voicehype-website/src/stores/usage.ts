import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { supabase } from '@/lib/supabase'
import { getUsageSummaryStatistics, getAllUsageHistory } from '@/lib/supabase'

interface UsageRecord {
  id: string
  user_id: string
  api_key_id: string | null
  service: string
  model: string
  amount: number
  cost: number
  pricing_model: string
  status: string
  metadata: any
  created_at: string
}

export const useUsageStore = defineStore('usage', () => {
  const usageHistory = ref<UsageRecord[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const filters = ref({
    startDate: null as Date | null,
    endDate: null as Date | null,
    service: null as string | null,
    model: null as string | null,
    status: null as string | null
  })

  // Pagination state
  const pageSize = ref(100)
  const currentPage = ref(1)
  const hasMoreRecords = ref(true)

  // Total cost state (independent of pagination)
  const totalCostValue = ref(0)
  const totalCostLoading = ref(false)

  // Service and model breakdowns (independent of pagination)
  const serviceBreakdownValue = ref<Record<string, number>>({})
  const modelBreakdownValue = ref<Record<string, number>>({})

  // Available services and models
  const availableServicesValue = ref<string[]>([])
  const availableModelsValue = ref<string[]>([])

  // New statistics values
  const totalMinutesValue = ref(0)
  const totalInputTokensValue = ref(0)
  const totalOutputTokensValue = ref(0)
  const minutesByDayValue = ref<Array<{ date: string; minutes: number }>>([])
  const tokensByDayValue = ref<Array<{ date: string; input_tokens: number; output_tokens: number }>>([])

  // Computed properties for usage statistics
  const totalCost = computed(() => {
    return totalCostValue.value
  })

  const serviceBreakdown = computed(() => {
    return serviceBreakdownValue.value
  })

  const modelBreakdown = computed(() => {
    return modelBreakdownValue.value
  })

  // New computed properties
  const totalMinutes = computed(() => {
    return totalMinutesValue.value
  })

  const totalInputTokens = computed(() => {
    return totalInputTokensValue.value
  })

  const totalOutputTokens = computed(() => {
    return totalOutputTokensValue.value
  })

  const minutesByDay = computed(() => {
    return minutesByDayValue.value
  })

  const tokensByDay = computed(() => {
    return tokensByDayValue.value
  })

  // Get current month date range
  function getCurrentMonthDateRange() {
    const now = new Date()
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
    // Set to the first day of the next month at 00:00:00
    const firstDayOfNextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1)

    return {
      startDate: startOfMonth,
      endDate: firstDayOfNextMonth
    }
  }

  // Fetch total cost and breakdowns independently
  async function fetchTotalCost() {
    totalCostLoading.value = true
    console.log('Fetching total cost with filters:', filters.value);

    try {
      const userId = (await supabase.auth.getUser()).data.user?.id || '';
      console.log('User ID:', userId);

      const data = await getUsageSummaryStatistics(
        userId,
        filters.value.startDate || undefined,
        filters.value.endDate || undefined,
        filters.value.service || undefined,
        filters.value.model || undefined,
        filters.value.status || undefined
      );

      console.log('Usage summary statistics response:', data);

      // Set the values from the database function
      totalCostValue.value = data.total_cost || 0;
      serviceBreakdownValue.value = data.service_breakdown || {};
      modelBreakdownValue.value = data.model_breakdown || {};
      availableServicesValue.value = data.available_services || [];
      availableModelsValue.value = data.available_models || [];

      // Set new statistics values
      totalMinutesValue.value = data.total_minutes || 0;
      totalInputTokensValue.value = data.total_input_tokens || 0;
      totalOutputTokensValue.value = data.total_output_tokens || 0;
      minutesByDayValue.value = data.minutes_by_day || [];
      tokensByDayValue.value = data.tokens_by_day || [];

      console.log('Updated state values:');
      console.log('- totalCostValue:', totalCostValue.value);
      console.log('- serviceBreakdownValue:', serviceBreakdownValue.value);
      console.log('- modelBreakdownValue:', modelBreakdownValue.value);
      console.log('- availableServicesValue:', availableServicesValue.value);
      console.log('- availableModelsValue:', availableModelsValue.value);
      console.log('- totalMinutesValue:', totalMinutesValue.value);
      console.log('- totalInputTokensValue:', totalInputTokensValue.value);
      console.log('- totalOutputTokensValue:', totalOutputTokensValue.value);
    } catch (err) {
      console.error('Error calculating total cost:', err)
    } finally {
      totalCostLoading.value = false
    }
  }

  // Fetch usage history with optional filters
  async function fetchUsageHistory(resetPagination = true) {
    loading.value = true
    error.value = null

    try {
      if (resetPagination) {
        currentPage.value = 1
        usageHistory.value = []
        hasMoreRecords.value = true

        // Fetch total cost independently
        await fetchTotalCost()
      }

      let query = supabase
        .from('usage_history')
        .select('*')
        .order('created_at', { ascending: false })
        .range((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value - 1)

      // Apply filters if they exist
      if (filters.value.startDate) {
        query = query.gte('created_at', filters.value.startDate.toISOString())
      }

      if (filters.value.endDate) {
        // Use the start of the next day as the end date filter
        const nextDay = new Date(filters.value.endDate);
        nextDay.setDate(nextDay.getDate() + 1);
        nextDay.setHours(0, 0, 0, 0);
        query = query.lt('created_at', nextDay.toISOString());
      }

      if (filters.value.service) {
        query = query.eq('service', filters.value.service)
      }

      if (filters.value.model) {
        query = query.eq('model', filters.value.model)
      }

      if (filters.value.status) {
        query = query.eq('status', filters.value.status)
      }

      const { data, error: fetchError } = await query

      if (fetchError) {
        error.value = fetchError.message
        return false
      }

      if (resetPagination) {
        usageHistory.value = data as UsageRecord[]
      } else {
        usageHistory.value = [...usageHistory.value, ...(data as UsageRecord[])]
      }

      // Check if there are more records to load
      hasMoreRecords.value = (data as UsageRecord[]).length === pageSize.value

      return true
    } catch (err) {
      console.error('Error fetching usage history:', err)
      error.value = 'Failed to fetch usage history'
      return false
    } finally {
      loading.value = false
    }
  }

  // Load more usage records
  async function loadMoreUsageRecords() {
    if (!hasMoreRecords.value || loading.value) return false

    currentPage.value++
    return fetchUsageHistory(false)
  }

  // Set filters for usage history
  function setFilters(newFilters: Partial<typeof filters.value>) {
    filters.value = { ...filters.value, ...newFilters }
  }

  // Set current month filter
  function setCurrentMonthFilter() {
    const { startDate, endDate } = getCurrentMonthDateRange()
    filters.value.startDate = startDate
    filters.value.endDate = endDate
  }

  // Clear all filters
  function clearFilters() {
    filters.value = {
      startDate: null,
      endDate: null,
      service: null,
      model: null,
      status: null
    }
  }

  // Get available services from usage history
  const availableServices = computed(() => {
    return availableServicesValue.value
  })

  // Get available models from usage history
  const availableModels = computed(() => {
    return availableModelsValue.value
  })

  // Export usage history to CSV
  async function exportUsageHistory(
    startDate?: Date | null,
    endDate?: Date | null,
    service?: string | null,
    model?: string | null,
    status?: string | null,
    filename: string = 'voicehype-usage.csv'
  ) {
    try {
      // Get current user ID
      const userId = (await supabase.auth.getUser()).data.user?.id || '';
      if (!userId) {
        throw new Error('User not authenticated');
      }

      // Pass the dates as is to getAllUsageHistory - it will handle the next day conversion
      const usageData = await getAllUsageHistory(
        userId,
        startDate || undefined,
        endDate || undefined, 
        service || undefined,
        model || undefined,
        status || undefined
      );

      if (!usageData || usageData.length === 0) {
        throw new Error('No usage data found for the selected filters');
      }

      // Define CSV headers
      const headers = [
        'Date', 
        'Service', 
        'Model', 
        'Amount', 
        'Status', 
        'Cost', 
        'Pricing Model',
        'Input Tokens',
        'Output Tokens'
      ];

      // Create CSV content
      let csvContent = headers.join(',') + '\n';

      // Add data rows
      usageData.forEach(record => {
        const date = new Date(record.created_at);
        const formattedDate = date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
        
        // Format amount based on service
        let formattedAmount;
        if (record.service === 'transcription') {
          formattedAmount = record.amount < 1 
            ? `${(record.amount * 60).toFixed(3)} secs` 
            : `${record.amount} mins`;
        } else {
          formattedAmount = `${Math.round(record.amount)} tokens`;
        }

        // Extract tokens from metadata
        const inputTokens = record.metadata?.input_tokens || record.metadata?.inputTokens || '';
        const outputTokens = record.metadata?.output_tokens || record.metadata?.outputTokens || '';
        
        // Format cost
        const formattedCost = record.status === 'success' 
          ? record.cost.toString()
          : '';

        // Create CSV row with proper escaping for values that might contain commas
        const row = [
          `"${formattedDate}"`,
          `"${record.service}"`,
          `"${record.model}"`,
          `"${formattedAmount}"`,
          `"${record.status}"`,
          formattedCost,
          `"${record.pricing_model}"`,
          inputTokens.toString(),
          outputTokens.toString()
        ];
        
        csvContent += row.join(',') + '\n';
      });

      // Create a blob and download link
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      return true;
    } catch (err) {
      console.error('Error exporting usage history:', err);
      throw err;
    }
  }

  return {
    usageHistory,
    loading,
    error,
    filters,
    totalCost,
    totalCostLoading,
    serviceBreakdown,
    modelBreakdown,
    fetchUsageHistory,
    loadMoreUsageRecords,
    setFilters,
    clearFilters,
    setCurrentMonthFilter,
    getCurrentMonthDateRange,
    availableServices,
    availableModels,
    hasMoreRecords,
    currentPage,
    pageSize,
    // New statistics
    totalMinutes,
    totalInputTokens,
    totalOutputTokens,
    minutesByDay,
    tokensByDay,
    exportUsageHistory
  }
})
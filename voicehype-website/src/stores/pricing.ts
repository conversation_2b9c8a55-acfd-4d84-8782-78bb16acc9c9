import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface Model {
  id: string
  friendly_name: string
  actual_url: string
  model_type: 'optimization' | 'transcription'
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface ServicePricing {
  id: string
  service: 'optimization' | 'transcription'
  model: string
  cost_per_unit: number
  unit: 'token' | 'minute'
  is_active: boolean
  created_at: string
}

export interface PricingData {
  models: Model[]
  pricing: ServicePricing[]
}

export const usePricingStore = defineStore('pricing', () => {
  const models = ref<Model[]>([])
  const pricing = ref<ServicePricing[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Get models with their pricing
  const modelsWithPricing = computed(() => {
    return models.value.map(model => {
      // Find pricing entries that match this model's actual_url
      const modelPricing = pricing.value.filter(p =>
        p.model.startsWith(model.actual_url)
      ) || []
      
      // Separate input and output pricing
      const inputPricing = modelPricing.find(p => p.model.includes('/input'))
      const outputPricing = modelPricing.find(p => p.model.includes('/output'))
      
      return {
        ...model,
        inputPrice: inputPricing?.cost_per_unit || 0,
        outputPrice: outputPricing?.cost_per_unit || 0,
        unit: inputPricing?.unit || outputPricing?.unit || 'token',
        pricing: modelPricing
      }
    })
  })

  // Group models by type
  const optimizationModels = computed(() => 
    modelsWithPricing.value.filter(m => m.model_type === 'optimization')
  )

  const transcriptionModels = computed(() => 
    modelsWithPricing.value.filter(m => m.model_type === 'transcription')
  )

  // Format price for display
  const formatPrice = (price: number, unit: string) => {
    if (unit === 'token') {
      return `$${(price * 1000).toFixed(6)}` // Price per 1K tokens
    } else if (unit === 'minute') {
      return `$${price.toFixed(4)}` // Price per minute
    }
    return `$${price.toFixed(6)}`
  }

  // Calculate estimated cost
  const calculateCost = (modelId: string, inputTokens: number, outputTokens: number = 0) => {
    const model = modelsWithPricing.value.find(m => m.id === modelId)
    if (!model) return 0
    
    const inputCost = model.inputPrice * inputTokens
    const outputCost = model.outputPrice * outputTokens
    
    return inputCost + outputCost
  }

  // Fetch models and pricing from Supabase
  const fetchPricingData = async () => {
    loading.value = true
    error.value = null
    
    try {
      const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL
      const SUPABASE_ANON_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY

      // Fetch models
      const modelsResponse = await fetch(`${SUPABASE_URL}/rest/v1/models?select=*&order=friendly_name.asc`, {
        method: 'GET',
        headers: {
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
        },
      })

      if (!modelsResponse.ok) {
        throw new Error(`Failed to fetch models: ${modelsResponse.status} ${modelsResponse.statusText}`)
      }

      const modelsData = await modelsResponse.json()
      models.value = modelsData

      // Fetch pricing
      const pricingResponse = await fetch(`${SUPABASE_URL}/rest/v1/service_pricing?select=*&order=model.asc`, {
        method: 'GET',
        headers: {
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
        },
      })

      if (!pricingResponse.ok) {
        throw new Error(`Failed to fetch pricing: ${pricingResponse.status}`)
      }

      const pricingData = await pricingResponse.json()
      pricing.value = pricingData

    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch pricing data'
      console.error('Error fetching pricing data:', err)
    } finally {
      loading.value = false
    }
  }

  return {
    models,
    pricing,
    loading,
    error,
    modelsWithPricing,
    optimizationModels,
    transcriptionModels,
    formatPrice,
    calculateCost,
    fetchPricingData
  }
})
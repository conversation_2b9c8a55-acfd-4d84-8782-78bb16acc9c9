import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { supabase } from '@/lib/supabase'
import type { User, Provider } from '@supabase/supabase-js'

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  const isAuthenticated = computed(() => !!user.value)

  // Initialize the store with the current session
  async function initialize() {
    loading.value = true
    try {
      const { data } = await supabase.auth.getSession()
      if (data.session) {
        user.value = data.session.user
        // Note: No router navigation should happen here
      }
    } catch (err) {
      console.error('Error initializing auth store:', err)
      error.value = 'Failed to initialize authentication'
    } finally {
      loading.value = false
    }
  }

  // Login with email and password
  async function login(email: string, password: string) {
    loading.value = true
    error.value = null

    try {
      const { data, error: authError } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (authError) {
        error.value = authError.message
        return false
      }

      user.value = data.user
      return true
    } catch (err) {
      console.error('Login error:', err)
      error.value = 'An unexpected error occurred during login'
      return false
    } finally {
      loading.value = false
    }
  }

  // Register a new user with email OTP verification
  async function register(email: string, password: string, fullName: string) {
    loading.value = true
    error.value = null

    try {
      // Get the site URL from environment variables or fallback to window.location.origin
      // For production, this should be https://voicehype.ai
      const siteUrl = import.meta.env.VITE_SITE_URL || window.location.origin;

      // Sign up with email verification enabled
      const { data, error: authError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName
          },
          emailRedirectTo: `${siteUrl}/auth/callback`,
        }
      })

      if (authError) {
        console.error('Auth error during registration:', authError)

        // Handle specific database error
        if (authError.message === 'Database error saving new user') {
          error.value = 'We encountered an issue with our database while creating your account. Our team has been notified and is working on a fix. Please try again in a few minutes.'
          return false
        } else if (authError.message.includes('violates not-null constraint')) {
          error.value = 'We encountered a database configuration issue. Our team has been notified and is working on a fix. Please try again later.'
          return false
        } else {
          error.value = authError.message
        }

        return false
      }

      // Check if email confirmation is required
      if (data?.user?.identities?.length === 0) {
        // User already exists but needs to confirm email
        error.value = 'An account with this email already exists. Please check your email for verification instructions.'
        return false
      }

      if (data?.user?.confirmed_at) {
        // User is already confirmed, set the user
        user.value = data.user
        return true
      } else {
        // User needs to confirm email
        console.log('Email verification required for:', email)
        return {
          success: true,
          message: 'Your account has been created successfully! Please check your email for verification instructions.'
        }
      }
    } catch (err: any) {
      console.error('Registration error:', err)

      // Check for database errors
      if (err.message && err.message.includes('database')) {
        error.value = 'We encountered a database issue while creating your account. Please try again later or contact support if the issue persists.'
      } else if (err.message && err.message.includes('violates not-null constraint')) {
        error.value = 'We encountered a database configuration issue. Our team has been notified and is working on a fix. Please try again later.'
      } else {
        error.value = err.message || 'An unexpected error occurred during registration'
      }

      return false
    } finally {
      loading.value = false
    }
  }

  // Verify OTP code for email verification
  async function verifyOtp(email: string, token: string) {
    loading.value = true
    error.value = null

    try {
      const { data, error: verifyError } = await supabase.auth.verifyOtp({
        email,
        token,
        type: 'email'
      })

      if (verifyError) {
        error.value = verifyError.message
        return false
      }

      user.value = data.user
      return true
    } catch (err) {
      console.error('OTP verification error:', err)
      error.value = 'An unexpected error occurred during verification'
      return false
    } finally {
      loading.value = false
    }
  }

  // Verify recovery token for password reset
  async function verifyRecoveryToken(token_hash: string) {
    loading.value = true
    error.value = null

    try {
      const { data, error: verifyError } = await supabase.auth.verifyOtp({
        token_hash,
        type: 'recovery'
      })

      if (verifyError) {
        error.value = verifyError.message
        return { error: verifyError }
      }

      user.value = data.user
      return { data }
    } catch (err) {
      console.error('Recovery verification error:', err)
      error.value = 'An unexpected error occurred during verification'
      return { error: new Error('An unexpected error occurred during verification') }
    } finally {
      loading.value = false
    }
  }

  // Logout the current user
  async function logout() {
    loading.value = true
    error.value = null

    try {
      // Use the scope parameter to ensure we're clearing all sessions
      const { error: authError } = await supabase.auth.signOut({ scope: 'global' })

      if (authError) {
        error.value = authError.message
        return false
      }

      // Clear user from store
      user.value = null

      // Clear any local storage items related to auth
      localStorage.removeItem('supabase.auth.token')
      localStorage.removeItem('supabase.auth.refreshToken')

      // Force a page reload to ensure all auth state is cleared
      // This is a more reliable way to ensure the user is fully logged out
      setTimeout(() => {
        window.location.href = '/'
      }, 100)

      return true
    } catch (err) {
      console.error('Logout error:', err)
      error.value = 'An unexpected error occurred during logout'
      return false
    } finally {
      loading.value = false
    }
  }

  // Reset password
  async function resetPassword(email: string) {
    loading.value = true
    error.value = null

    try {
      // Get the site URL from environment variables or fallback to window.location.origin
      // For production, this should be https://voicehype.ai
      const siteUrl = import.meta.env.VITE_SITE_URL || window.location.origin;

      const { error: authError } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${siteUrl}/update-password`
      })

      if (authError) {
        error.value = authError.message
        return false
      }

      return true
    } catch (err) {
      console.error('Password reset error:', err)
      error.value = 'An unexpected error occurred during password reset'
      return false
    } finally {
      loading.value = false
    }
  }

  // Update password
  async function updatePassword(password: string) {
    loading.value = true
    error.value = null

    try {
      const { error: authError } = await supabase.auth.updateUser({
        password
      })

      if (authError) {
        error.value = authError.message
        return false
      }

      return true
    } catch (err) {
      console.error('Update password error:', err)
      error.value = 'An unexpected error occurred while updating password'
      return false
    } finally {
      loading.value = false
    }
  }

  // Enable two-factor authentication
  async function enableTwoFactor() {
    loading.value = true
    error.value = null

    try {
      // This would be implemented with Supabase's MFA features
      // For now, we'll return a placeholder
      return {
        success: true,
        message: 'Two-factor authentication has been enabled.'
      }
    } catch (err) {
      console.error('Enable 2FA error:', err)
      error.value = 'An unexpected error occurred while enabling two-factor authentication'
      return false
    } finally {
      loading.value = false
    }
  }

  // Sign in with OAuth provider (Google, GitHub, etc.)
  async function signInWithOAuth(provider: Provider) {
    loading.value = true
    error.value = null

    try {
      // Check if we're currently on the VS Code auth page
      const isVSCodeAuth = window.location.pathname === '/vscode-auth'

      let redirectUrl: string

      if (isVSCodeAuth) {
        // For VS Code auth, redirect directly back to vscode-auth with all parameters preserved
        const currentUrl = new URL(window.location.href)
        const redirectTo = new URL(`${import.meta.env.VITE_SITE_URL || window.location.origin}/vscode-auth`)

        // Preserve all VS Code auth parameters
        const preserveParams = ['state', 'redirect_uri', 'product_name', 'uri_scheme']
        preserveParams.forEach(param => {
          const value = currentUrl.searchParams.get(param)
          if (value) {
            redirectTo.searchParams.append(param, value)
          }
        })

        redirectUrl = redirectTo.toString()
        console.log(`VS Code auth detected, using redirect URL: ${redirectUrl}`)
      } else {
        // For normal auth, redirect to app
        redirectUrl = `${import.meta.env.VITE_SITE_URL || window.location.origin}/app`
        console.log(`Normal auth, using redirect URL: ${redirectUrl}`)
      }

      // Get the client ID for the provider
      let clientId: string | undefined
      if (provider === 'google') {
        clientId = import.meta.env.VITE_OAUTH_GOOGLE_CLIENT_ID
        console.log(`Using Google OAuth with client ID: ${clientId?.substring(0, 10)}...`)
      } else if (provider === 'github') {
        clientId = import.meta.env.VITE_OAUTH_GITHUB_CLIENT_ID
        console.log(`Using GitHub OAuth with client ID: ${clientId}`)
      }

      // Options for OAuth
      const options: any = {
        // This is where the user will be redirected after Supabase processes the OAuth response
        redirectTo: redirectUrl
      }

      console.log(`Setting OAuth redirectTo: ${options.redirectTo}`)

      // Add client ID if available
      if (clientId) {
        options.queryParams = {
          client_id: clientId
        }
      }

      // For both GitHub and Google, we need to ensure we're using the correct redirect URI
      // that matches what's configured in the OAuth provider settings
      if (provider === 'github' || provider === 'google') {
        // Do NOT set redirect_uri here - let Supabase use its default from environment variables
        // This ensures the redirect_uri matches what's configured in the OAuth provider
        console.log(`Using ${provider} OAuth with Supabase server callback`)
      }

      console.log(`Initiating ${provider} OAuth sign-in with options:`, {
        redirectTo: options.redirectTo,
        hasClientId: !!options.queryParams?.client_id
      })

      const { data, error: authError } = await supabase.auth.signInWithOAuth({
        provider,
        options
      })

      console.log(`${provider} OAuth sign-in response:`, {
        success: !authError,
        hasUrl: !!data?.url,
        url: data?.url ? `${data.url.substring(0, 30)}...` : 'none'
      })

      if (authError) {
        error.value = authError.message
        return false
      }

      // This will return the URL to redirect to
      return data
    } catch (err) {
      console.error(`${provider} OAuth error:`, err)
      error.value = `An unexpected error occurred during ${provider} authentication`
      return false
    } finally {
      loading.value = false
    }
  }

  // Sign in with Google
  async function signInWithGoogle() {
    return signInWithOAuth('google')
  }

  // Sign in with GitHub
  async function signInWithGitHub() {
    return signInWithOAuth('github')
  }

  return {
    user,
    loading,
    error,
    isAuthenticated,
    initialize,
    login,
    register,
    verifyOtp,
    verifyRecoveryToken,
    logout,
    resetPassword,
    updatePassword,
    enableTwoFactor,
    signInWithOAuth,
    signInWithGoogle,
    signInWithGitHub
  }
})
/* Critical styles to prevent Flash of Unstyled Content */

/* Basic reset and colors */
body {
  margin: 0;
  padding: 0;
  background-color: #0d1117;
  color: #ffffff;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  min-height: 100vh;
}

/* Container styles */
.container, .nav-container, .hero-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Basic typography */
h1, h2, h3, h4 {
  margin: 0;
  font-weight: 700;
}

/* Basic layout helpers */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

/* Critical variables */
:root {
  --color-bg: #0d1117;
  --color-bg-light: #161b22;
  --color-text: #ffffff;
  --color-primary: #14F195;
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  --font-mono: 'JetBrains Mono', Menlo, Monaco, Consolas, monospace;
  --font-display: 'Clash Display', 'Inter', system-ui, sans-serif;
}

/* Critical components */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: rgba(8, 8, 20, 0.8);
  backdrop-filter: blur(10px);
  padding: 0.75rem 0;
  height: auto;
  display: flex;
  align-items: center;
}

.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.nav-logo {
  display: flex;
  align-items: center;
}

.logo-text {
  font-family: var(--font-display);
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  padding: 0;
  line-height: 1;
}

.hype-text {
  color: var(--color-primary);
}

/* Loading overlay and animations */
.app-loading, .landing-page-loader {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--color-bg);
  z-index: 9999;
}

.loader, .loader-spinner {
  width: 48px;
  height: 48px;
  border: 3px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Transition helpers */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
} 
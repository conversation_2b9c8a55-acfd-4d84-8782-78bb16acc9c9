/* Landing Page Global Styles */

/* CSS Variables */
:root {
  /* Colors */
  --color-bg: #080814;
  --color-bg-light: #10101e;
  --color-bg-lighter: #1c1c34;
  --color-text: #ffffff;
  --color-text-muted: rgba(255, 255, 255, 0.7);
  --color-text-subtle: rgba(255, 255, 255, 0.5);
  --color-primary: #14F195;
  --color-primary-dark: #0ad680;
  --color-primary-light: #7df8c7;
  --color-secondary: #6366F1;
  --color-accent: #0AD6DF;
  --color-warning: #FFC107;
  --color-error: #FF5252;
  --color-success: #4CAF50;
  
  /* Typography */
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  --font-mono: 'JetBrains Mono', Menlo, Monaco, Consolas, "Liberation Mono", monospace;
  --font-display: 'Clash Display', 'Inter', system-ui, sans-serif;
  --font-outfit: 'Outfit', var(--font-sans);
  --font-space: 'Space Grotesk', var(--font-sans);
  --font-instrument: 'Instrument', var(--font-sans);
  
  /* Font Sizes */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  --font-size-6xl: 3.75rem;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  --spacing-4xl: 6rem;
  
  /* Borders */
  --border-radius-sm: 0.25rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;
  --border-radius-full: 9999px;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-base: 0.3s ease;
  --transition-slow: 0.5s ease;
  
  /* Z-indices */
  --z-below: -1;
  --z-normal: 0;
  --z-above: 1;
  --z-header: 100;
  --z-overlay: 200;
  --z-modal: 300;
  --z-popover: 400;
  --z-tooltip: 500;
  
  /* Breakpoints (for reference) */
  --breakpoint-xs: 480px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}

/* Container Widths */
.lp-container {
  width: 90%;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--spacing-md);
  padding-right: var(--spacing-md);
}

/* Utility Classes */

/* Text utilities */
.lp-text-center { text-align: center; }
.lp-text-left { text-align: left; }
.lp-text-right { text-align: right; }

.lp-text-primary { color: var(--color-primary); }
.lp-text-secondary { color: var(--color-secondary); }
.lp-text-accent { color: var(--color-accent); }
.lp-text-muted { color: var(--color-text-muted); }

.lp-font-sans { font-family: var(--font-sans); }
.lp-font-mono { font-family: var(--font-mono); }
.lp-font-display { font-family: var(--font-display); }

.lp-font-thin { font-weight: 300; }
.lp-font-normal { font-weight: 400; }
.lp-font-medium { font-weight: 500; }
.lp-font-semibold { font-weight: 600; }
.lp-font-bold { font-weight: 700; }
.lp-font-extrabold { font-weight: 800; }

/* Flex utilities */
.lp-flex { display: flex; }
.lp-flex-col { flex-direction: column; }
.lp-flex-row { flex-direction: row; }
.lp-flex-wrap { flex-wrap: wrap; }

.lp-items-center { align-items: center; }
.lp-items-start { align-items: flex-start; }
.lp-items-end { align-items: flex-end; }

.lp-justify-center { justify-content: center; }
.lp-justify-start { justify-content: flex-start; }
.lp-justify-end { justify-content: flex-end; }
.lp-justify-between { justify-content: space-between; }

.lp-flex-1 { flex: 1; }
.lp-flex-auto { flex: auto; }
.lp-flex-none { flex: none; }

/* Spacing utilities */
.lp-m-0 { margin: 0; }
.lp-m-xs { margin: var(--spacing-xs); }
.lp-m-sm { margin: var(--spacing-sm); }
.lp-m-md { margin: var(--spacing-md); }
.lp-m-lg { margin: var(--spacing-lg); }
.lp-m-xl { margin: var(--spacing-xl); }

.lp-mt-0 { margin-top: 0; }
.lp-mt-xs { margin-top: var(--spacing-xs); }
.lp-mt-sm { margin-top: var(--spacing-sm); }
.lp-mt-md { margin-top: var(--spacing-md); }
.lp-mt-lg { margin-top: var(--spacing-lg); }
.lp-mt-xl { margin-top: var(--spacing-xl); }
.lp-mt-2xl { margin-top: var(--spacing-2xl); }
.lp-mt-3xl { margin-top: var(--spacing-3xl); }

.lp-mb-0 { margin-bottom: 0; }
.lp-mb-xs { margin-bottom: var(--spacing-xs); }
.lp-mb-sm { margin-bottom: var(--spacing-sm); }
.lp-mb-md { margin-bottom: var(--spacing-md); }
.lp-mb-lg { margin-bottom: var(--spacing-lg); }
.lp-mb-xl { margin-bottom: var(--spacing-xl); }
.lp-mb-2xl { margin-bottom: var(--spacing-2xl); }
.lp-mb-3xl { margin-bottom: var(--spacing-3xl); }

.lp-p-0 { padding: 0; }
.lp-p-xs { padding: var(--spacing-xs); }
.lp-p-sm { padding: var(--spacing-sm); }
.lp-p-md { padding: var(--spacing-md); }
.lp-p-lg { padding: var(--spacing-lg); }
.lp-p-xl { padding: var(--spacing-xl); }

.lp-py-0 { padding-top: 0; padding-bottom: 0; }
.lp-py-xs { padding-top: var(--spacing-xs); padding-bottom: var(--spacing-xs); }
.lp-py-sm { padding-top: var(--spacing-sm); padding-bottom: var(--spacing-sm); }
.lp-py-md { padding-top: var(--spacing-md); padding-bottom: var(--spacing-md); }
.lp-py-lg { padding-top: var(--spacing-lg); padding-bottom: var(--spacing-lg); }
.lp-py-xl { padding-top: var(--spacing-xl); padding-bottom: var(--spacing-xl); }
.lp-py-2xl { padding-top: var(--spacing-2xl); padding-bottom: var(--spacing-2xl); }

.lp-px-0 { padding-left: 0; padding-right: 0; }
.lp-px-xs { padding-left: var(--spacing-xs); padding-right: var(--spacing-xs); }
.lp-px-sm { padding-left: var(--spacing-sm); padding-right: var(--spacing-sm); }
.lp-px-md { padding-left: var(--spacing-md); padding-right: var(--spacing-md); }
.lp-px-lg { padding-left: var(--spacing-lg); padding-right: var(--spacing-lg); }
.lp-px-xl { padding-left: var(--spacing-xl); padding-right: var(--spacing-xl); }
.lp-px-2xl { padding-left: var(--spacing-2xl); padding-right: var(--spacing-2xl); }

/* Button utilities */
.lp-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--border-radius-md);
  font-family: var(--font-sans);
  font-weight: 600;
  font-size: var(--font-size-base);
  cursor: pointer;
  transition: all var(--transition-base);
  text-decoration: none;
  border: 2px solid transparent;
}

.lp-btn-primary {
  background: var(--color-primary);
  color: #111827;
}

.lp-btn-primary:hover {
  background: var(--color-primary-dark);
}

.lp-btn-secondary {
  background: var(--color-secondary);
  color: var(--color-text);
}

.lp-btn-outline {
  background: transparent;
  border: 2px solid var(--color-primary);
  color: var(--color-primary);
}

.lp-btn-outline:hover {
  background: rgba(20, 241, 149, 0.1);
}

/* Gradient text class */
.lp-gradient-text {
  background: linear-gradient(to right, var(--color-primary), var(--color-accent));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

/* Background utilities */
.lp-bg-dark { background-color: var(--color-bg); }
.lp-bg-light { background-color: var(--color-bg-light); }
.lp-bg-lighter { background-color: var(--color-bg-lighter); }
.lp-bg-primary { background-color: var(--color-primary); }
.lp-bg-primary-light { background-color: var(--color-primary-light); }

.lp-card {
  background: var(--color-bg-light);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
}

/* Grid system */
.lp-grid {
  display: grid;
  gap: var(--spacing-md);
}

.lp-grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.lp-grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.lp-grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.lp-grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

/* Responsive Grid */
@media (max-width: 1024px) {
  .lp-grid-cols-lg-3 { grid-template-columns: repeat(3, 1fr); }
  .lp-grid-cols-lg-2 { grid-template-columns: repeat(2, 1fr); }
  .lp-grid-cols-lg-1 { grid-template-columns: repeat(1, 1fr); }
}

@media (max-width: 768px) {
  .lp-grid-cols-md-2 { grid-template-columns: repeat(2, 1fr); }
  .lp-grid-cols-md-1 { grid-template-columns: repeat(1, 1fr); }
  
  .lp-container {
    width: 95%;
  }
}

@media (max-width: 640px) {
  .lp-grid-cols-sm-1 { grid-template-columns: repeat(1, 1fr); }
  
  .lp-container {
    width: 100%;
    padding-left: var(--spacing-sm);
    padding-right: var(--spacing-sm);
  }
  
  .lp-text-sm-center { text-align: center; }
  
  .lp-flex-sm-col { flex-direction: column; }
} 
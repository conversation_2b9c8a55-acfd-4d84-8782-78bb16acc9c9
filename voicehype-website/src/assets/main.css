@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@500;600;700&family=Roboto+Mono:wght@400;500&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply dark:bg-gray-900 dark:text-white font-sans antialiased text-gray-900 bg-white;
  }
  h1, h2, h3, h4, h5, h6 {
    @apply font-heading font-bold;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 px-4 py-2 font-semibold text-white transition-colors rounded-lg;
  }
  
  .btn-secondary {
    @apply hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:text-gray-300 dark:bg-gray-800 dark:hover:bg-gray-700 px-4 py-2 font-semibold text-gray-700 transition-colors bg-gray-100 rounded-lg;
  }

  .card {
    @apply dark:bg-gray-800 p-6 bg-white rounded-lg shadow-sm;
  }

  .input {
    @apply focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm;
  }
}

.app-max-width {
  @apply max-w-7xl sm:px-6 lg:px-8 px-4 mx-auto;
}

a {
  @apply text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300;
}

@media (prefers-color-scheme: dark) {
  html.dark {
    @apply bg-gray-900;
  }
}

/* Dashboard Theme Override to match Landing Page */

:root {
  /* Import color variables from landing page */
  --color-bg: #080814;
  --color-bg-light: #10101e;
  --color-bg-lighter: #1c1c34;
  --color-text: #ffffff;
  --color-text-muted: rgba(255, 255, 255, 0.7);
  --color-text-subtle: rgba(255, 255, 255, 0.5);
  --color-primary: #14F195;
  --color-primary-dark: #0ad680;
  --color-primary-light: #7df8c7;
  --color-secondary: #6366F1;
  --color-accent: #0AD6DF;
  --color-border: #30363d;
}

/* Override dark mode dashboard colors */
.dark {
  --tw-bg-opacity: 1;
  background-color: var(--color-bg) !important;
}

.dark header, 
.dark .sidebar,
.dark [class*="dark:bg-[#161b22]"],
.dark [class*="dark:bg-[#10101e]"] {
  background-color: var(--color-bg-light) !important;
}

.dark [class*="dark:border-[#30363d]"] {
  border-color: var(--color-border) !important;
}

/* Primary color overrides */
.dark [class*="dark:text-[#14F195]"],
.dark [class*="dark:hover:text-[#14F195]"],
.dark [class*="dark:focus:ring-[#14F195]"],
.dark a {
  color: var(--color-primary) !important;
}

.dark [class*="dark:bg-[#14F195]/10"] {
  background-color: rgba(20, 241, 149, 0.1) !important;
}

.dark [class*="dark:hover:bg-[#14F195]/5"] {
  background-color: rgba(20, 241, 149, 0.05) !important;
}

.dark [class*="dark:bg-[#14F195]"] {
  background-color: var(--color-primary) !important;
}

/* Text color overrides */
.dark [class*="dark:text-[#c9d1d9]"] {
  color: var(--color-text) !important;
}

.dark [class*="dark:text-gray-300"] {
  color: var(--color-text-muted) !important;
}

/* Specific element overrides */
.dark .btn-primary {
  background-color: var(--color-primary) !important;
  color: #111827 !important;
}

.dark .btn-primary:hover {
  background-color: var(--color-primary-dark) !important;
}

.dark .card {
  background-color: var(--color-bg-light) !important;
}

/* Transition animations */
.dashboard-page-enter-active,
.dashboard-page-leave-active {
  transition: opacity 0.35s ease, transform 0.35s ease;
}

.dashboard-page-enter-from {
  opacity: 0;
  transform: translateX(10px);
}

.dashboard-page-leave-to {
  opacity: 0;
  transform: translateX(-10px);
}

/* Navigation link styles */
.dark nav a.router-link-active,
.dark nav a.router-link-exact-active {
  background-color: rgba(20, 241, 149, 0.1) !important;
  color: var(--color-primary) !important;
}

.dark nav a:hover {
  background-color: rgba(20, 241, 149, 0.05) !important;
  color: var(--color-primary) !important;
} 
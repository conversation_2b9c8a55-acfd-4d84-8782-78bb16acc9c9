// Types for subscription upgrade functionality
// Used in custom proration implementation

export interface UpgradeOption {
  type: 'scheduled' | 'immediate'
  title: string
  description: string
  icon: string
  benefits: string[]
  buttonText: string
}

export interface UpgradeRequest {
  newPlanId: string
  upgradeMode: 'scheduled' | 'immediate'
  mergeQuotas?: boolean
}

export interface UpgradeResponse {
  success: boolean
  upgrade_mode: 'scheduled' | 'immediate'
  proration_mode: string
  merge_quotas: boolean
  old_plan: {
    id: string
    name: string
    monthly_price: number
  }
  new_plan: {
    id: string
    name: string
    monthly_price: number
  }
  paddle_subscription_id: string
  message: string
}

export interface QuotaPreview {
  service: 'transcription' | 'optimization'
  current_used: number
  current_total: number
  current_remaining: number
  new_plan_total: number
  final_total_after_merge: number
  unit: string // 'minutes' | 'tokens'
}

export interface UpgradePreview {
  currentPlan: {
    name: string
    price: number
  }
  newPlan: {
    name: string
    price: number
  }
  priceChange: {
    amount: number
    direction: 'increase' | 'decrease'
  }
  quotaPreviews: QuotaPreview[]
  upgradeOptions: UpgradeOption[]
}

export interface PaddleUpgradeMetadata {
  mode: 'immediate' | 'scheduled'
  merge_quotas: boolean
  old_subscription_id: string
  old_plan_id: string
  user_id: string
}

// Webhook-related types for upgrade processing
export interface UpgradeWebhookData {
  subscription_id: string
  customer_id: string
  status: string
  custom_data?: {
    upgrade_metadata?: PaddleUpgradeMetadata
  }
  current_billing_period?: {
    starts_at: string
    ends_at: string
  }
}

// Database function response types
export interface MergeQuotasResult {
  success: boolean
  user_id: string
  old_subscription_id: string
  new_subscription_id: string
  remaining_quotas: {
    transcription_minutes: number
    input_tokens: number
    output_tokens: number
  }
  new_totals: {
    transcription_minutes: number
    input_tokens: number
    output_tokens: number
  }
  merged_at: string
  error?: string
}

// UI State types
export interface UpgradeModalState {
  isOpen: boolean
  loading: boolean
  selectedOption: 'scheduled' | 'immediate' | null
  currentPlanId: string
  targetPlanId: string
  error: string | null
  success: boolean
}

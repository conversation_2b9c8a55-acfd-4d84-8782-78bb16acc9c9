<template>
  <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="sm:block sm:p-0 flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center">
      <!-- Background overlay -->
      <div class="fixed inset-0 transition-opacity" @click="close">
        <div class="dark:bg-gray-900 dark:opacity-75 absolute inset-0 bg-gray-500 opacity-75"></div>
      </div>

      <!-- Modal panel -->
      <div class="inline-block align-bottom bg-white dark:bg-[#161b22] rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full border border-gray-200 dark:border-[#30363d]">
        <div class="bg-white dark:bg-[#161b22] px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-primary-100 dark:bg-[#14F195]/10 sm:mx-0 sm:h-10 sm:w-10">
              <KeyIcon class="text-primary-600 dark:text-black w-6 h-6" />
            </div>
            <div class="sm:mt-0 sm:ml-4 sm:text-left mt-3 text-center">
              <h3 class="dark:text-white font-heading text-lg font-medium leading-6 text-gray-900">
                Create New API Key
              </h3>
              <div class="mt-2">
                <p class="text-sm text-gray-500 dark:text-[#8b949e]">
                  Create a new API key to access VoiceHype services. You'll only be able to see the key once after creation.
                </p>
              </div>
            </div>
          </div>

          <div class="mt-5">
            <form @submit.prevent="createKey">
              <!-- Key Name -->
              <div class="mb-4">
                <label for="key-name" class="block text-sm font-medium text-gray-700 dark:text-[#c9d1d9]">
                  Key Name
                </label>
                <input
                  type="text"
                  id="key-name"
                  v-model="keyName"
                  class="input mt-1"
                  placeholder="e.g. Production Key"
                  required
                />
              </div>

              <!-- Expiration -->
              <div class="mb-4">
                <label for="expiration" class="block text-sm font-medium text-gray-700 dark:text-[#c9d1d9]">
                  Expiration
                </label>
                <select
                  id="expiration"
                  v-model="expiration"
                  class="input mt-1"
                >
                  <option value="7">7 days</option>
                  <option value="30">30 days</option>
                  <option value="90">90 days</option>
                  <option value="365">1 year</option>
                  <option value="">Never expires</option>
                </select>
              </div>

              <!-- Note: Permissions section removed as all permissions are now enabled by default -->

              <!-- Error message -->
              <div v-if="error" class="dark:text-red-400 mb-4 text-sm text-red-600">
                {{ error }}
              </div>

              <!-- Buttons -->
              <div class="sm:mt-4 sm:flex sm:flex-row-reverse mt-5">
                <button
                  type="submit"
                  class="btn-primary sm:ml-3 sm:w-auto w-full"
                  :disabled="loading"
                >
                  <span v-if="loading">Creating...</span>
                  <span v-else>Create Key</span>
                </button>
                <button
                  type="button"
                  class="btn-secondary sm:mt-0 sm:w-auto w-full mt-3"
                  @click="close"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Success Modal -->
  <div v-if="showSuccessModal" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="sm:block sm:p-0 flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center">
      <!-- Background overlay -->
      <div class="fixed inset-0 transition-opacity">
        <div class="dark:bg-gray-900 dark:opacity-75 absolute inset-0 bg-gray-500 opacity-75"></div>
      </div>

      <!-- Modal panel -->
      <div class="inline-block align-bottom bg-white dark:bg-[#161b22] rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full border border-gray-200 dark:border-[#30363d]">
        <div class="bg-white dark:bg-[#161b22] px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="dark:bg-green-900 sm:mx-0 sm:h-10 sm:w-10 flex items-center justify-center flex-shrink-0 w-12 h-12 mx-auto bg-green-100 rounded-full">
              <CheckIcon class="dark:text-green-400 w-6 h-6 text-green-600" />
            </div>
            <div class="sm:mt-0 sm:ml-4 sm:text-left mt-3 text-center">
              <h3 class="dark:text-white font-heading text-lg font-medium leading-6 text-gray-900">
                API Key Created
              </h3>
              <div class="mt-2">
                <p class="text-sm text-gray-500 dark:text-[#8b949e]">
                  Your API key has been created successfully. Please copy it now as you won't be able to see it again.
                </p>
              </div>
            </div>
          </div>

          <div class="mt-5">
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 dark:text-[#c9d1d9] mb-1">
                Your API Key
              </label>
              <div class="flex">
                <input
                  type="text"
                  readonly
                  :value="formattedApiKey"
                  class="input font-mono text-sm rounded-r-none"
                />
                <button
                  type="button"
                  @click="copyApiKey"
                  class="px-4 py-2 bg-gray-200 text-gray-800 rounded-r-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-300 dark:bg-[#21262d] dark:text-[#c9d1d9] dark:hover:bg-[#30363d]"
                >
                  <DocumentDuplicateIcon class="w-5 h-5" />
                </button>
              </div>
              <p v-if="copied" class="dark:text-green-400 mt-1 text-sm text-green-600">
                Copied to clipboard!
              </p>
            </div>

            <div class="sm:mt-4 sm:flex sm:justify-end mt-5">
              <button
                type="button"
                class="btn-primary sm:w-auto w-full"
                @click="closeSuccessModal"
              >
                Done
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, computed } from 'vue'
import { useApiKeysStore } from '@/stores/apiKeys'
import {
  KeyIcon,
  CheckIcon,
  DocumentDuplicateIcon
} from '@heroicons/vue/24/outline'

defineProps<{
  isOpen: boolean
}>()

const emit = defineEmits(['close', 'created'])

// Store
const apiKeysStore = useApiKeysStore()

// State
const keyName = ref('')
const expiration = ref('')
const loading = ref(false)
const error = ref('')
const newApiKey = ref('')
const showSuccessModal = ref(false)
const copied = ref(false)

// Computed
const formattedApiKey = computed(() => {
  return newApiKey.value ? `vhkey_${newApiKey.value}` : ''
})

// Methods
function close() {
  emit('close')
  resetForm()
}

function resetForm() {
  keyName.value = ''
  expiration.value = ''
  error.value = ''
}

async function createKey() {
  loading.value = true
  error.value = ''

  try {
    // Calculate expiration date if selected, otherwise undefined for never expires
    let expiresAt: Date | undefined = undefined
    if (expiration.value && expiration.value !== '') {
      expiresAt = new Date()
      expiresAt.setDate(expiresAt.getDate() + parseInt(expiration.value))
    }

    // Create the key
    const result = await apiKeysStore.createKey(keyName.value, expiresAt)

    if (result) {
      // All permissions are now enabled by default in the backend
      // No need to call updateKeyPermissions

      // Show success modal with the key secret
      newApiKey.value = result.key_secret
      showSuccessModal.value = true

      // Emit created event
      emit('created')
    } else {
      error.value = apiKeysStore.error || 'Failed to create API key'
    }
  } catch (err) {
    console.error('Error creating API key:', err)
    error.value = 'An unexpected error occurred'
  } finally {
    loading.value = false
  }
}

function copyApiKey() {
  navigator.clipboard.writeText(formattedApiKey.value)
  copied.value = true

  // Reset copied status after 2 seconds
  setTimeout(() => {
    copied.value = false
  }, 2000)
}

function closeSuccessModal() {
  showSuccessModal.value = false
  close()
}
</script>

<style scoped>
/* Custom checkbox styling */
input[type="checkbox"] {
  appearance: none;
  -webkit-appearance: none;
  width: 1rem;
  height: 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  background-color: white;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

input[type="checkbox"]:checked {
  background-color: #0ea5e9;
  border-color: #0ea5e9;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='white'%3E%3Cpath fill-rule='evenodd' d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z' clip-rule='evenodd'/%3E%3C/svg%3E");
}

input[type="checkbox"]:focus {
  outline: 2px solid #0ea5e9;
  outline-offset: 1px;
}

/* Dark mode styles */
.dark input[type="checkbox"] {
  background-color: #1e293b;
  border-color: #475569;
}

.dark input[type="checkbox"]:checked {
  background-color: #14F195;
  border-color: #14F195;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='black'%3E%3Cpath fill-rule='evenodd' d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z' clip-rule='evenodd'/%3E%3C/svg%3E");
}

.dark input[type="checkbox"]:focus {
  outline-color: #14F195;
}
</style>
<template>
  <div class="pricing-feature-comparison">
    <div class="comparison-header mb-8 text-center">
      <h3 class="mb-4 text-2xl font-bold text-gray-900">
        Compare All Features
      </h3>
      <p class="max-w-2xl mx-auto text-gray-600">
        Choose the perfect plan for your needs. All plans include our core transcription and optimization features with different usage limits.
      </p>
    </div>

    <!-- Mobile-first responsive table -->
    <div class="comparison-table overflow-x-auto">
      <table class="w-full bg-white rounded-lg shadow-lg">
        <thead class="bg-gray-50">
          <tr>
            <th class="text-left p-4 font-semibold text-gray-900 min-w-[200px]">
              Features
            </th>
            <th class="text-center p-4 font-semibold text-gray-900 min-w-[140px]">
              Basic
              <div class="mt-1 text-sm font-normal text-gray-500">$9/month</div>
            </th>
            <th class="text-center p-4 font-semibold text-gray-900 min-w-[140px] bg-blue-50">
              Pro
              <div class="mt-1 text-sm font-normal text-gray-500">$18/month</div>
              <div class="inline-block px-2 py-1 mt-1 text-xs text-white bg-blue-500 rounded-full">
                Popular
              </div>
            </th>
            <th class="text-center p-4 font-semibold text-gray-900 min-w-[140px]">
              Premium
              <div class="mt-1 text-sm font-normal text-gray-500">$27/month</div>
            </th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
          <!-- Transcription Features -->
          <tr class="feature-category">
            <td class="bg-gray-50 p-4 font-semibold text-gray-900" colspan="4">
              🎤 Transcription
            </td>
          </tr>
          <tr>
            <td class="p-4 text-gray-700">
              Monthly transcription minutes
              <InfoTooltip text="Real-time and file transcription included" />
            </td>
            <td class="p-4 text-center">
              <span class="font-semibold text-blue-600">720 min</span>
            </td>
            <td class="bg-blue-50 p-4 text-center">
              <span class="font-semibold text-blue-600">1,440 min</span>
            </td>
            <td class="p-4 text-center">
              <span class="font-semibold text-blue-600">2,160 min</span>
            </td>
          </tr>
          <tr>
            <td class="p-4 text-gray-700">Real-time transcription</td>
            <td class="p-4 text-center">
              <CheckIcon class="w-5 h-5 mx-auto text-green-500" />
            </td>
            <td class="bg-blue-50 p-4 text-center">
              <CheckIcon class="w-5 h-5 mx-auto text-green-500" />
            </td>
            <td class="p-4 text-center">
              <CheckIcon class="w-5 h-5 mx-auto text-green-500" />
            </td>
          </tr>
          <tr>
            <td class="p-4 text-gray-700">File upload transcription</td>
            <td class="p-4 text-center">
              <CheckIcon class="w-5 h-5 mx-auto text-green-500" />
            </td>
            <td class="bg-blue-50 p-4 text-center">
              <CheckIcon class="w-5 h-5 mx-auto text-green-500" />
            </td>
            <td class="p-4 text-center">
              <CheckIcon class="w-5 h-5 mx-auto text-green-500" />
            </td>
          </tr>

          <!-- AI Processing Features -->
          <tr class="feature-category">
            <td class="bg-gray-50 p-4 font-semibold text-gray-900" colspan="4">
              🤖 AI Processing
            </td>
          </tr>
          <tr>
            <td class="p-4 text-gray-700">
              Monthly AI tokens
              <InfoTooltip text="Used for text optimization and AI features" />
            </td>
            <td class="p-4 text-center">
              <span class="font-semibold text-blue-600">400K</span>
            </td>
            <td class="bg-blue-50 p-4 text-center">
              <span class="font-semibold text-blue-600">800K</span>
            </td>
            <td class="p-4 text-center">
              <span class="font-semibold text-blue-600">1.2M</span>
            </td>
          </tr>
          <tr>
            <td class="p-4 text-gray-700">Text optimization</td>
            <td class="p-4 text-center">
              <CheckIcon class="w-5 h-5 mx-auto text-green-500" />
            </td>
            <td class="bg-blue-50 p-4 text-center">
              <CheckIcon class="w-5 h-5 mx-auto text-green-500" />
            </td>
            <td class="p-4 text-center">
              <CheckIcon class="w-5 h-5 mx-auto text-green-500" />
            </td>
          </tr>
          <tr>
            <td class="p-4 text-gray-700">Advanced AI models</td>
            <td class="p-4 text-center">
              <XMarkIcon class="w-5 h-5 mx-auto text-gray-400" />
            </td>
            <td class="bg-blue-50 p-4 text-center">
              <CheckIcon class="w-5 h-5 mx-auto text-green-500" />
            </td>
            <td class="p-4 text-center">
              <CheckIcon class="w-5 h-5 mx-auto text-green-500" />
            </td>
          </tr>

          <!-- API & Integration -->
          <tr class="feature-category">
            <td class="bg-gray-50 p-4 font-semibold text-gray-900" colspan="4">
              🔌 API & Integrations
            </td>
          </tr>
          <tr>
            <td class="p-4 text-gray-700">API access</td>
            <td class="p-4 text-center">
              <CheckIcon class="w-5 h-5 mx-auto text-green-500" />
            </td>
            <td class="bg-blue-50 p-4 text-center">
              <CheckIcon class="w-5 h-5 mx-auto text-green-500" />
            </td>
            <td class="p-4 text-center">
              <CheckIcon class="w-5 h-5 mx-auto text-green-500" />
            </td>
          </tr>
          <tr>
            <td class="p-4 text-gray-700">
              Priority API processing
              <InfoTooltip text="Faster response times and higher priority in queue" />
            </td>
            <td class="p-4 text-center">
              <XMarkIcon class="w-5 h-5 mx-auto text-gray-400" />
            </td>
            <td class="bg-blue-50 p-4 text-center">
              <CheckIcon class="w-5 h-5 mx-auto text-green-500" />
            </td>
            <td class="p-4 text-center">
              <CheckIcon class="w-5 h-5 mx-auto text-green-500" />
            </td>
          </tr>
          <tr>
            <td class="p-4 text-gray-700">Custom integrations</td>
            <td class="p-4 text-center">
              <XMarkIcon class="w-5 h-5 mx-auto text-gray-400" />
            </td>
            <td class="bg-blue-50 p-4 text-center">
              <XMarkIcon class="w-5 h-5 mx-auto text-gray-400" />
            </td>
            <td class="p-4 text-center">
              <CheckIcon class="w-5 h-5 mx-auto text-green-500" />
            </td>
          </tr>

          <!-- Billing & Support -->
          <tr class="feature-category">
            <td class="bg-gray-50 p-4 font-semibold text-gray-900" colspan="4">
              💳 Billing & Support
            </td>
          </tr>
          <tr>
            <td class="p-4 text-gray-700">
              Monthly overage allowance
              <InfoTooltip text="Automatic pay-as-you-go allowance for usage beyond quota" />
            </td>
            <td class="p-4 text-center">
              <span class="font-semibold text-green-600">$10</span>
            </td>
            <td class="bg-blue-50 p-4 text-center">
              <span class="font-semibold text-green-600">$10</span>
            </td>
            <td class="p-4 text-center">
              <span class="font-semibold text-green-600">$10</span>
            </td>
          </tr>
          <tr>
            <td class="p-4 text-gray-700">Email support</td>
            <td class="p-4 text-center">
              <span class="text-sm text-gray-500">Community</span>
            </td>
            <td class="bg-blue-50 p-4 text-center">
              <CheckIcon class="w-5 h-5 mx-auto text-green-500" />
            </td>
            <td class="p-4 text-center">
              <span class="font-semibold text-blue-600">Priority</span>
            </td>
          </tr>
          <tr>
            <td class="p-4 text-gray-700">Annual discount</td>
            <td class="p-4 text-center">
              <span class="font-semibold text-green-600">20%</span>
            </td>
            <td class="bg-blue-50 p-4 text-center">
              <span class="font-semibold text-green-600">20%</span>
            </td>
            <td class="p-4 text-center">
              <span class="font-semibold text-green-600">20%</span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Priority System Explanation -->
    <div class="priority-explanation bg-blue-50 p-6 mt-8 rounded-lg">
      <h4 class="flex items-center mb-3 font-semibold text-blue-900">
        <InformationCircleIcon class="w-5 h-5 mr-2" />
        How VoiceHype Pricing Works
      </h4>
      <div class="space-y-2 text-sm text-blue-800">
        <p>
          <strong>1. Subscription Quota First:</strong> Your monthly subscription quota is used first for all requests.
        </p>
        <p>
          <strong>2. Prepaid Credits:</strong> If subscription quota is exhausted, any prepaid credits are used next.
        </p>
        <p>
          <strong>3. Pay-as-you-go Allowance:</strong> Finally, your $10 monthly overage allowance covers additional usage.
        </p>
        <p class="font-medium text-blue-600">
          This system ensures predictable costs while providing flexibility for varying usage patterns.
        </p>
      </div>
    </div>

    <!-- FAQ Section -->
    <div class="faq-section mt-8">
      <h4 class="mb-4 font-semibold text-gray-900">Frequently Asked Questions</h4>
      <div class="space-y-4">
        <details class="bg-gray-50 p-4 rounded-lg">
          <summary class="font-medium text-gray-700 cursor-pointer">
            What happens if I exceed my monthly quota?
          </summary>
          <p class="mt-2 text-sm text-gray-600">
            No worries! You have a $10 monthly overage allowance that automatically covers additional usage at standard pay-as-you-go rates. You'll never be surprised by charges.
          </p>
        </details>
        <details class="bg-gray-50 p-4 rounded-lg">
          <summary class="font-medium text-gray-700 cursor-pointer">
            Can I upgrade or downgrade my plan anytime?
          </summary>
          <p class="mt-2 text-sm text-gray-600">
            Yes! You can change your plan at any time. Upgrades take effect immediately, while downgrades take effect at your next billing cycle.
          </p>
        </details>
        <details class="bg-gray-50 p-4 rounded-lg">
          <summary class="font-medium text-gray-700 cursor-pointer">
            Do unused minutes and tokens roll over?
          </summary>
          <p class="mt-2 text-sm text-gray-600">
            Subscription quotas reset monthly and don't roll over. However, any prepaid credits you purchase separately never expire.
          </p>
        </details>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { CheckIcon, XMarkIcon, InformationCircleIcon } from '@heroicons/vue/24/solid'
import { defineComponent } from 'vue'

// Simple tooltip component for feature explanations
const InfoTooltip = defineComponent({
  props: {
    text: String
  },
  template: `
    <div class="group relative inline-block ml-1">
      <InformationCircleIcon class="cursor-help w-4 h-4 text-gray-400" />
      <div class="bottom-full left-1/2 group-hover:opacity-100 whitespace-nowrap absolute z-10 px-2 py-1 mb-2 text-xs text-white transition-opacity transform -translate-x-1/2 bg-gray-900 rounded opacity-0 pointer-events-none">
        {{ text }}
      </div>
    </div>
  `,
  components: { InformationCircleIcon }
})
</script>

<style scoped>
/* Ensure table cells are properly aligned */
.comparison-table table {
  border-collapse: separate;
  border-spacing: 0;
}

.comparison-table th,
.comparison-table td {
  border-bottom: 1px solid #e5e7eb;
}

.comparison-table th {
  border-bottom: 2px solid #e5e7eb;
}

/* Feature category styling */
.feature-category td {
  font-weight: 600;
  background-color: #f9fafb;
  border-top: 2px solid #e5e7eb;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .comparison-table {
    font-size: 0.875rem;
  }
  
  .comparison-table th,
  .comparison-table td {
    padding: 0.75rem 0.5rem;
  }
}

/* Smooth expand/collapse for details */
details {
  transition: all 0.2s ease-in-out;
}

details[open] {
  background-color: #f3f4f6;
}

details summary {
  outline: none;
}

details summary::-webkit-details-marker {
  display: none;
}

details summary::before {
  content: '▶';
  margin-right: 0.5rem;
  transition: transform 0.2s ease-in-out;
}

details[open] summary::before {
  transform: rotate(90deg);
}
</style>

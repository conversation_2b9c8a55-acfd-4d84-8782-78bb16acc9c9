<template>
  <div class="subscription-pricing-section">
    <!-- Toggle for Monthly/Annual billing -->
    <div class="billing-toggle flex justify-center mb-8">
      <div class="flex items-center p-1 bg-gray-100 rounded-lg">
        <button
          @click="billingCycle = 'monthly'"
          :class="[
            'px-4 py-2 rounded-md text-sm font-medium transition-all',
            billingCycle === 'monthly'
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-gray-500 hover:text-gray-700'
          ]"
        >
          Monthly
        </button>
        <button
          @click="billingCycle = 'annual'"
          :class="[
            'px-4 py-2 rounded-md text-sm font-medium transition-all',
            billingCycle === 'annual'
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-gray-500 hover:text-gray-700'
          ]"
        >
          Annual
          <span class="ml-1 text-xs font-semibold text-green-600">Save 20%</span>
        </button>
      </div>
    </div>

    <!-- Pricing Cards Grid -->
    <div class="pricing-cards-grid md:grid-cols-3 grid max-w-6xl grid-cols-1 gap-8 mx-auto">
      <!-- Basic Plan -->
      <div class="pricing-card rounded-xl relative p-8 bg-white border border-gray-200 shadow-lg">
        <div class="plan-header mb-6 text-center">
          <h3 class="mb-2 text-xl font-bold text-gray-900">Basic</h3>
          <div class="price mb-4">
            <span class="text-4xl font-bold text-gray-900">
              ${{ billingCycle === 'monthly' ? '9' : '7.20' }}
            </span>
            <span class="ml-1 text-gray-500">
              /{{ billingCycle === 'monthly' ? 'month' : 'month' }}
            </span>
          </div>
          <p class="text-sm text-gray-600">Perfect for individual developers</p>
        </div>

        <div class="features mb-8">
          <ul class="space-y-3">
            <li class="flex items-center text-sm">
              <CheckIcon class="flex-shrink-0 w-5 h-5 mr-3 text-green-500" />
              <span>720 minutes transcription</span>
            </li>
            <li class="flex items-center text-sm">
              <CheckIcon class="flex-shrink-0 w-5 h-5 mr-3 text-green-500" />
              <span>400,000 AI tokens</span>
            </li>
            <li class="flex items-center text-sm">
              <CheckIcon class="flex-shrink-0 w-5 h-5 mr-3 text-green-500" />
              <span>Real-time transcription</span>
            </li>
            <li class="flex items-center text-sm">
              <CheckIcon class="flex-shrink-0 w-5 h-5 mr-3 text-green-500" />
              <span>Text optimization</span>
            </li>
            <li class="flex items-center text-sm">
              <CheckIcon class="flex-shrink-0 w-5 h-5 mr-3 text-green-500" />
              <span>API access</span>
            </li>
            <li class="flex items-center text-sm">
              <CheckIcon class="flex-shrink-0 w-5 h-5 mr-3 text-green-500" />
              <span>$10 monthly overage allowance</span>
            </li>
          </ul>
        </div>

        <button
          @click="selectPlan('basic')"
          :disabled="loading"
          class="hover:bg-blue-700 disabled:opacity-50 w-full px-4 py-3 font-medium text-white transition-colors bg-blue-600 rounded-lg"
        >
          {{ loading ? 'Processing...' : 'Get Started' }}
        </button>
      </div>

      <!-- Pro Plan (Popular) -->
      <div class="pricing-card rounded-xl relative p-8 transform scale-105 bg-white border-2 border-blue-500 shadow-xl">
        <!-- Popular Badge -->
        <div class="-top-4 left-1/2 absolute transform -translate-x-1/2">
          <span class="px-4 py-1 text-sm font-medium text-white bg-blue-500 rounded-full">
            Most Popular
          </span>
        </div>

        <div class="plan-header mb-6 text-center">
          <h3 class="mb-2 text-xl font-bold text-gray-900">Pro</h3>
          <div class="price mb-4">
            <span class="text-4xl font-bold text-gray-900">
              ${{ billingCycle === 'monthly' ? '18' : '14.40' }}
            </span>
            <span class="ml-1 text-gray-500">
              /{{ billingCycle === 'monthly' ? 'month' : 'month' }}
            </span>
          </div>
          <p class="text-sm text-gray-600">Ideal for small teams</p>
        </div>

        <div class="features mb-8">
          <ul class="space-y-3">
            <li class="flex items-center text-sm">
              <CheckIcon class="flex-shrink-0 w-5 h-5 mr-3 text-green-500" />
              <span>1,440 minutes transcription</span>
            </li>
            <li class="flex items-center text-sm">
              <CheckIcon class="flex-shrink-0 w-5 h-5 mr-3 text-green-500" />
              <span>800,000 AI tokens</span>
            </li>
            <li class="flex items-center text-sm">
              <CheckIcon class="flex-shrink-0 w-5 h-5 mr-3 text-green-500" />
              <span>Real-time transcription</span>
            </li>
            <li class="flex items-center text-sm">
              <CheckIcon class="flex-shrink-0 w-5 h-5 mr-3 text-green-500" />
              <span>Advanced text optimization</span>
            </li>
            <li class="flex items-center text-sm">
              <CheckIcon class="flex-shrink-0 w-5 h-5 mr-3 text-green-500" />
              <span>Priority API access</span>
            </li>
            <li class="flex items-center text-sm">
              <CheckIcon class="flex-shrink-0 w-5 h-5 mr-3 text-green-500" />
              <span>$10 monthly overage allowance</span>
            </li>
            <li class="flex items-center text-sm">
              <CheckIcon class="flex-shrink-0 w-5 h-5 mr-3 text-green-500" />
              <span>Email support</span>
            </li>
          </ul>
        </div>

        <button
          @click="selectPlan('pro')"
          :disabled="loading"
          class="hover:bg-blue-700 disabled:opacity-50 w-full px-4 py-3 font-medium text-white transition-colors bg-blue-600 rounded-lg"
        >
          {{ loading ? 'Processing...' : 'Get Started' }}
        </button>
      </div>

      <!-- Premium Plan -->
      <div class="pricing-card rounded-xl relative p-8 bg-white border border-gray-200 shadow-lg">
        <div class="plan-header mb-6 text-center">
          <h3 class="mb-2 text-xl font-bold text-gray-900">Premium</h3>
          <div class="price mb-4">
            <span class="text-4xl font-bold text-gray-900">
              ${{ billingCycle === 'monthly' ? '27' : '21.60' }}
            </span>
            <span class="ml-1 text-gray-500">
              /{{ billingCycle === 'monthly' ? 'month' : 'month' }}
            </span>
          </div>
          <p class="text-sm text-gray-600">Enterprise-ready usage</p>
        </div>

        <div class="features mb-8">
          <ul class="space-y-3">
            <li class="flex items-center text-sm">
              <CheckIcon class="flex-shrink-0 w-5 h-5 mr-3 text-green-500" />
              <span>2,160 minutes transcription</span>
            </li>
            <li class="flex items-center text-sm">
              <CheckIcon class="flex-shrink-0 w-5 h-5 mr-3 text-green-500" />
              <span>1,200,000 AI tokens</span>
            </li>
            <li class="flex items-center text-sm">
              <CheckIcon class="flex-shrink-0 w-5 h-5 mr-3 text-green-500" />
              <span>Real-time transcription</span>
            </li>
            <li class="flex items-center text-sm">
              <CheckIcon class="flex-shrink-0 w-5 h-5 mr-3 text-green-500" />
              <span>Advanced text optimization</span>
            </li>
            <li class="flex items-center text-sm">
              <CheckIcon class="flex-shrink-0 w-5 h-5 mr-3 text-green-500" />
              <span>Premium API access</span>
            </li>
            <li class="flex items-center text-sm">
              <CheckIcon class="flex-shrink-0 w-5 h-5 mr-3 text-green-500" />
              <span>$10 monthly overage allowance</span>
            </li>
            <li class="flex items-center text-sm">
              <CheckIcon class="flex-shrink-0 w-5 h-5 mr-3 text-green-500" />
              <span>Priority email support</span>
            </li>
            <li class="flex items-center text-sm">
              <CheckIcon class="flex-shrink-0 w-5 h-5 mr-3 text-green-500" />
              <span>Custom integrations</span>
            </li>
          </ul>
        </div>

        <button
          @click="selectPlan('premium')"
          :disabled="loading"
          class="hover:bg-blue-700 disabled:opacity-50 w-full px-4 py-3 font-medium text-white transition-colors bg-blue-600 rounded-lg"
        >
          {{ loading ? 'Processing...' : 'Get Started' }}
        </button>
      </div>
    </div>

    <!-- Additional Info -->
    <div class="additional-info mt-12 text-center">
      <p class="mb-4 text-sm text-gray-600">
        All plans include automatic monthly quota reset and pay-as-you-go overage protection.
      </p>
      <p class="text-xs text-gray-500">
        Prices shown in USD. Annual billing includes 20% discount. Cancel anytime.
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { CheckIcon } from '@heroicons/vue/24/solid'
import { useSubscriptionStore } from '@/stores/subscription'
import { useToast } from '@/services/toast'

// Props and emits
const emit = defineEmits(['plan-selected'])

// Reactive data
const billingCycle = ref<'monthly' | 'annual'>('monthly')
const loading = ref(false)

// Stores and composables
const subscriptionStore = useSubscriptionStore()
const { success: showToast, error: showError } = useToast()

// Plan selection handler
const selectPlan = async (planName: string) => {
  loading.value = true
  
  try {
    // Get the plan ID from the store
    const plan = subscriptionStore.availablePlans.find(
      p => p.name.toLowerCase() === planName.toLowerCase()
    )
    
    if (!plan) {
      throw new Error(`Plan ${planName} not found`)
    }

    // Create Paddle subscription checkout
    const checkoutUrl = await subscriptionStore.createSubscriptionCheckout({
      planId: plan.id,
      billingCycle: billingCycle.value
    })

    if (checkoutUrl) {
      // Redirect to Paddle checkout if a URL is returned
      if (checkoutUrl.startsWith('http')) {
        window.location.href = checkoutUrl
      } else {
        // Just a success indicator, no redirection needed
        showToast('Subscription process started', 'You will be redirected to the payment page shortly.')
      }
    } else {
      throw new Error('Failed to create checkout')
    }
    
  } catch (error) {
    console.error('Error selecting plan:', error)
    showError('Subscription Error', 'Failed to start subscription process. Please try again.')
  } finally {
    loading.value = false
  }
}

// Emit plan selection for parent component handling
const emitPlanSelection = (planName: string) => {
  emit('plan-selected', {
    plan: planName,
    billingCycle: billingCycle.value
  })
}
</script>

<style scoped>
.pricing-cards-grid {
  /* Ensure equal height cards */
  align-items: stretch;
}

.pricing-card {
  transition: transform 0.2s ease-in-out;
}

.pricing-card:hover {
  transform: translateY(-4px);
}

/* Popular plan scale adjustment for mobile */
@media (max-width: 768px) {
  .pricing-card.transform.scale-105 {
    transform: scale(1);
  }
}

/* Ensure proper spacing for features list */
.features ul {
  min-height: 200px;
}

/* Price number styling */
.price {
  line-height: 1;
}

/* Button hover state */
.pricing-card button:hover:not(:disabled) {
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);
}
</style>

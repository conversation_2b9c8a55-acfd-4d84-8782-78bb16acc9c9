<template>
  <div class="profile-avatar">
    <div 
      v-if="imageUrl" 
      class="avatar-container"
      :style="{ width: `${size}px`, height: `${size}px` }"
    >
      <img 
        :src="imageUrl" 
        :alt="alt" 
        class="avatar-image rounded-full object-cover"
        :style="{ width: `${size}px`, height: `${size}px`, borderWidth: `${borderWidth}px` }"
      />
    </div>
    <div 
      v-else 
      class="fallback-avatar flex items-center justify-center text-white bg-primary-600"
      :style="{ 
        width: `${size}px`, 
        height: `${size}px`, 
        fontSize: `${size * 0.4}px`,
        borderWidth: `${borderWidth}px`
      }"
    >
      <!-- Display user icon if no initials or use initials if available -->
      <span v-if="!name || name.trim() === ''">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-1/2 h-1/2">
          <path fill-rule="evenodd" d="M7.5 6a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM3.751 20.105a8.25 8.25 0 0116.498 0 .75.75 0 01-.437.695A18.683 18.683 0 0112 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 01-.437-.695z" clip-rule="evenodd" />
        </svg>
      </span>
      <span v-else>{{ initials }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps({
  imageUrl: {
    type: String,
    default: ''
  },
  name: {
    type: String,
    default: ''
  },
  size: {
    type: Number,
    default: 40
  },
  alt: {
    type: String,
    default: 'User avatar'
  },
  borderWidth: {
    type: Number,
    default: 2
  }
});

// Compute initials from name
const initials = computed(() => {
  if (!props.name || props.name.trim() === '') return '';
  
  const nameParts = props.name.split(' ').filter(part => part.length > 0);
  if (nameParts.length === 0) return '';
  
  if (nameParts.length === 1) {
    return nameParts[0].charAt(0).toUpperCase();
  }
  
  // Always ensure uppercase initials
  return (nameParts[0].charAt(0).toUpperCase() + nameParts[nameParts.length - 1].charAt(0).toUpperCase());
});
</script>

<style scoped>
.profile-avatar {
  display: inline-block;
  overflow: hidden;
}

.avatar-container {
  border-radius: 50%;
  overflow: hidden;
}

.avatar-image {
  border-style: none;
  box-shadow: none;
  object-fit: cover;
}

:root.dark .avatar-image {
  border-color: transparent;
  box-shadow: none;
}

.fallback-avatar {
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  border-style: none;
  box-shadow: none;
  overflow: hidden;
}

:root.dark .fallback-avatar {
  border-color: transparent;
  box-shadow: none;
}
</style> 
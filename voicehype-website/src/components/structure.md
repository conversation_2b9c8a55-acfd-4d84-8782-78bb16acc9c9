# VoiceHype Website Component Structure

## Core Components

### Layout Components
- ✅ `DashboardLayout.vue` - Main layout for authenticated pages
- `AuthLayout.vue` - Layout for authentication pages

### Authentication Components
- `LoginForm.vue` - Form for user login
- `RegisterForm.vue` - Form for user registration
- `ResetPasswordForm.vue` - Form for password reset

### Dashboard Components
- `UsageSummaryCard.vue` - Card displaying usage summary
- `CreditsCard.vue` - Card displaying credit balance
- `ApiKeysCard.vue` - Card displaying API keys count
- `SubscriptionCard.vue` - Card displaying subscription info
- `RecentActivityTable.vue` - Table displaying recent activity

### API Key Components
- ✅ `CreateApiKeyModal.vue` - Modal for creating new API keys
- `ApiKeysList.vue` - List of API keys
- `ApiKeyItem.vue` - Individual API key item

### Usage Components
- `UsageFilters.vue` - Filters for usage history
- `UsageTable.vue` - Table displaying usage history
- `UsageChart.vue` - Chart visualizing usage data
- `ExportUsageButton.vue` - Button for exporting usage data

### Subscription Components
- `SubscriptionPlans.vue` - Display available subscription plans
- `CurrentPlanDetails.vue` - Details of current subscription
- `BillingInformation.vue` - Form for billing information
- `PaymentHistory.vue` - Table displaying payment history
- `CreditPackages.vue` - Display available credit packages

### Settings Components
- `ProfileSettings.vue` - Form for profile settings
- `PasswordSettings.vue` - Form for changing password
- `NotificationSettings.vue` - Settings for notifications
- `PricingModelSettings.vue` - Settings for default pricing model

## Views

### Authentication Views
- ✅ `LoginView.vue` - Login page
- `RegisterView.vue` - Registration page
- `ResetPasswordView.vue` - Password reset page

### Main Views
- ✅ `DashboardView.vue` - Dashboard page
- ✅ `ApiKeysView.vue` - API keys management page
- `UsageView.vue` - Usage history page
- `SubscriptionView.vue` - Subscription management page
- `SettingsView.vue` - Settings page

## Stores

### Data Stores
- ✅ `auth.ts` - Authentication store
- ✅ `apiKeys.ts` - API keys store
- ✅ `usage.ts` - Usage history store
- ✅ `subscription.ts` - Subscription store
- ✅ `credits.ts` - Credits store
- ✅ `settings.ts` - Settings store

## Implementation Plan

1. Complete the authentication views and components
2. Implement the remaining dashboard components
3. Complete the usage view and components
4. Implement the subscription view and components
5. Complete the settings view and components
6. Add error handling and loading states
7. Implement responsive design adjustments
8. Add animations and transitions
9. Implement testing 
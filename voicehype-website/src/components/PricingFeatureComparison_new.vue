<template>
  <div class="pricing-feature-comparison">
    <div class="text-center mb-8">
      <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">Compare Plans</h3>
      <p class="text-gray-600 dark:text-gray-400">Choose the plan that best fits your usage needs</p>
    </div>

    <!-- Comparison Table -->
    <div class="overflow-x-auto">
      <table class="w-full border-collapse bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <thead>
          <tr class="border-b border-gray-200 dark:border-gray-700">
            <th class="text-left p-4 font-semibold text-gray-900 dark:text-white">Features</th>
            <th class="text-center p-4 font-semibold text-gray-900 dark:text-white">Pro</th>
            <th class="text-center p-4 font-semibold text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20">Basic</th>
            <th class="text-center p-4 font-semibold text-gray-900 dark:text-white">Premium</th>
          </tr>
        </thead>
        <tbody>
          <!-- Monthly Price -->
          <tr class="border-b border-gray-100 dark:border-gray-700">
            <td class="p-4 font-medium text-gray-900 dark:text-white">Monthly Price</td>
            <td class="p-4 text-center text-gray-700 dark:text-gray-300">$18</td>
            <td class="p-4 text-center text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 font-semibold">$9</td>
            <td class="p-4 text-center text-gray-700 dark:text-gray-300">$27</td>
          </tr>

          <!-- Transcription Minutes -->
          <tr class="border-b border-gray-100 dark:border-gray-700">
            <td class="p-4 text-gray-700 dark:text-gray-300">
              <div class="flex items-center">
                <svg class="w-5 h-5 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Transcription Minutes
              </div>
            </td>
            <td class="p-4 text-center text-gray-700 dark:text-gray-300">20 min</td>
            <td class="p-4 text-center text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 font-semibold">7 min</td>
            <td class="p-4 text-center text-gray-700 dark:text-gray-300">12 hours</td>
          </tr>

          <!-- AI Tokens -->
          <tr class="border-b border-gray-100 dark:border-gray-700">
            <td class="p-4 text-gray-700 dark:text-gray-300">
              <div class="flex items-center">
                <svg class="w-5 h-5 text-purple-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                </svg>
                AI Tokens
              </div>
            </td>
            <td class="p-4 text-center text-gray-700 dark:text-gray-300">800K</td>
            <td class="p-4 text-center text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 font-semibold">400K</td>
            <td class="p-4 text-center text-gray-700 dark:text-gray-300">1.2M</td>
          </tr>

          <!-- Real-time Transcription -->
          <tr>
            <td class="p-4 text-gray-700 dark:text-gray-300">
              <div class="flex items-center">
                <svg class="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clip-rule="evenodd"></path>
                </svg>
                Real-time Transcription
              </div>
            </td>
            <td class="p-4 text-center">
              <CheckIcon class="w-5 h-5 text-green-500 mx-auto" />
            </td>
            <td class="p-4 text-center bg-blue-50 dark:bg-blue-900/20">
              <CheckIcon class="w-5 h-5 text-green-500 mx-auto" />
            </td>
            <td class="p-4 text-center">
              <CheckIcon class="w-5 h-5 text-green-500 mx-auto" />
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup lang="ts">
// Check/X Icons as components
const CheckIcon = {
  template: `
    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
    </svg>
  `
}

const XIcon = {
  template: `
    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
      <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
    </svg>
  `
}
</script>

<style scoped>
table {
  border-spacing: 0;
}

.pricing-feature-comparison {
  max-width: 1000px;
  margin: 0 auto;
}

/* Dark mode support for table */
.dark table {
  background-color: rgb(31 41 55); /* gray-800 */
}

.dark th {
  color: white;
}

.dark td {
  color: rgb(209 213 219); /* gray-300 */
}

.dark .bg-blue-50 {
  background-color: rgb(30 58 138 / 0.2); /* blue-900/20 */
}

@media (max-width: 768px) {
  .overflow-x-auto {
    -webkit-overflow-scrolling: touch;
  }
  
  table {
    min-width: 600px;
  }
  
  .grid {
    grid-template-columns: 1fr;
  }
}
</style>

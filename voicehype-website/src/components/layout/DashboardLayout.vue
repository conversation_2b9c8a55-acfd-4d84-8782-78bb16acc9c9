<template>
  <div 
    class="flex min-h-screen bg-[#0d1117] dark:bg-[#0d1117]"
    :class="{ 'dark': darkMode }"
  >
    <!-- Sidebar backdrop -->
    <div 
      v-if="sidebarOpen && !isDesktop" 
      @click="sidebarOpen = false" 
      class="lg:hidden fixed inset-0 z-20 transition-opacity duration-300 bg-black bg-opacity-50"
    ></div>
    
    <!-- Sidebar -->
    <div 
      :class="[
        isDesktop ? (sidebarCollapsed ? 'w-16' : 'w-64') : (sidebarOpen ? 'translate-x-0 w-64' : '-translate-x-full w-64'),
        'fixed inset-y-0 left-0 z-30 overflow-y-auto transition-all duration-300 transform bg-primary-800 dark:bg-[#0d1117] lg:translate-x-0 lg:static lg:inset-0'
      ]"
    >
      <!-- Conditionally render different header structures based on sidebar state -->
      <template v-if="sidebarCollapsed">
        <!-- Simple centered structure for collapsed state -->
        <div class="flex items-center justify-center p-4">
          <button 
            @click="toggleSidebar" 
            class="hover:text-primary-200 focus:outline-none lg:flex items-center justify-center hidden text-white transition-colors duration-200"
          >
            <Bars3Icon class="w-6 h-6" />
          </button>
        </div>
      </template>
      <template v-else>
        <!-- Original structure for expanded state -->
        <div class="flex items-center justify-between p-4">
          <div class="flex items-center overflow-hidden">
            <span class="font-heading whitespace-nowrap font-clash text-2xl font-semibold text-white">Voice<span class="text-primary-400">Hype</span></span>
          </div>
          <button 
            @click="toggleSidebar" 
            class="hover:text-primary-200 focus:outline-none lg:block hidden text-white transition-colors duration-200"
          >
            <XMarkIcon class="w-6 h-6" />
          </button>
        </div>
      </template>
      
      <nav class="px-2 mt-5">
        <router-link 
          to="/" 
          class="flex items-center px-4 py-2 mt-2 text-white transition-colors duration-200 rounded-lg"
          :class="{ 
            'bg-primary-700 font-medium dark:bg-[#14F195]/5': $route.path === '/', 
            'hover:bg-primary-700 dark:hover:bg-[#14F195]/5': $route.path !== '/' 
          }"
        >
          <HomeIcon class="flex-shrink-0 w-5 h-5" :class="sidebarCollapsed ? 'mx-auto' : 'mr-3'" />
          <span class="whitespace-nowrap overflow-hidden transition-all duration-300" :class="{ 'opacity-0 w-0': sidebarCollapsed, 'opacity-100 w-auto': !sidebarCollapsed }">Dashboard</span>
        </router-link>
        
        <router-link 
          to="/api-keys" 
          class="flex items-center px-4 py-2 mt-2 text-white transition-colors duration-200 rounded-lg"
          :class="{ 
            'bg-primary-700 font-medium dark:bg-[#14F195]/5': $route.path === '/api-keys', 
            'hover:bg-primary-700 dark:hover:bg-[#14F195]/5': $route.path !== '/api-keys' 
          }"
        >
          <KeyIcon class="flex-shrink-0 w-5 h-5" :class="sidebarCollapsed ? 'mx-auto' : 'mr-3'" />
          <span class="whitespace-nowrap overflow-hidden transition-all duration-300" :class="{ 'opacity-0 w-0': sidebarCollapsed, 'opacity-100 w-auto': !sidebarCollapsed }">API Keys</span>
        </router-link>
        
        <router-link 
          to="/usage" 
          class="flex items-center px-4 py-2 mt-2 text-white transition-colors duration-200 rounded-lg"
          :class="{ 
            'bg-primary-700 font-medium dark:bg-[#14F195]/5': $route.path === '/usage', 
            'hover:bg-primary-700 dark:hover:bg-[#14F195]/5': $route.path !== '/usage' 
          }"
        >
          <ChartBarIcon class="flex-shrink-0 w-5 h-5" :class="sidebarCollapsed ? 'mx-auto' : 'mr-3'" />
          <span class="whitespace-nowrap overflow-hidden transition-all duration-300" :class="{ 'opacity-0 w-0': sidebarCollapsed, 'opacity-100 w-auto': !sidebarCollapsed }">Usage History</span>
        </router-link>
        
        <router-link 
          to="/subscription" 
          class="flex items-center px-4 py-2 mt-2 text-white transition-colors duration-200 rounded-lg"
          :class="{ 
            'bg-primary-700 font-medium dark:bg-[#14F195]/5': $route.path === '/subscription', 
            'hover:bg-primary-700 dark:hover:bg-[#14F195]/5': $route.path !== '/subscription' 
          }"
        >
          <CreditCardIcon class="flex-shrink-0 w-5 h-5" :class="sidebarCollapsed ? 'mx-auto' : 'mr-3'" />
          <span class="whitespace-nowrap overflow-hidden transition-all duration-300" :class="{ 'opacity-0 w-0': sidebarCollapsed, 'opacity-100 w-auto': !sidebarCollapsed }">Subscription</span>
        </router-link>
        
        <router-link 
          to="/settings" 
          class="flex items-center px-4 py-2 mt-2 text-white transition-colors duration-200 rounded-lg"
          :class="{ 
            'bg-primary-700 font-medium dark:bg-[#14F195]/5': $route.path === '/settings', 
            'hover:bg-primary-700 dark:hover:bg-[#14F195]/5': $route.path !== '/settings' 
          }"
        >
          <Cog6ToothIcon class="flex-shrink-0 w-5 h-5" :class="sidebarCollapsed ? 'mx-auto' : 'mr-3'" />
          <span class="whitespace-nowrap overflow-hidden transition-all duration-300" :class="{ 'opacity-0 w-0': sidebarCollapsed, 'opacity-100 w-auto': !sidebarCollapsed }">Settings</span>
        </router-link>
      </nav>
    </div>
    
    <!-- Content -->
    <div class="flex flex-col flex-1 overflow-hidden">
      <!-- Navbar -->
      <header class="bg-[#161b22] dark:bg-[#0d1117] shadow-sm">
        <div class="sm:px-6 lg:px-8 px-4 py-4 mx-auto">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <button 
                @click="sidebarOpen = true" 
                class="dark:text-gray-300 hover:text-gray-700 dark:hover:text-white focus:outline-none lg:hidden text-gray-500 transition-colors duration-200"
              >
                <Bars3Icon class="w-6 h-6" />
              </button>
            </div>
            
            <div class="flex items-center space-x-4">
              <!-- Dark mode toggle -->
              <button 
                @click="toggleDarkMode" 
                class="dark:text-gray-300 hover:text-gray-700 dark:hover:text-white focus:outline-none text-gray-500 transition-colors duration-200"
              >
                <SunIcon v-if="darkMode" class="w-5 h-5" />
                <MoonIcon v-else class="w-5 h-5" />
              </button>
              
              <div class="relative">
                <button 
                  @click="dropdownOpen = !dropdownOpen" 
                  class="focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 relative block w-8 h-8 overflow-hidden rounded-full shadow"
                >
                  <div class="bg-primary-600 dark:bg-[#14F195] flex items-center justify-center w-8 h-8 text-white dark:text-[#0d1117] rounded-full">
                    <!-- Dark mode user icon (filled) -->
                    <svg v-if="darkMode" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                      <path fill-rule="evenodd" d="M18.685 19.097A9.723 9.723 0 0 0 21.75 12c0-5.385-4.365-9.75-9.75-9.75S2.25 6.615 2.25 12a9.723 9.723 0 0 0 3.065 7.097A9.716 9.716 0 0 0 12 21.75a9.716 9.716 0 0 0 6.685-2.653Zm-12.54-1.285A7.486 7.486 0 0 1 12 15a7.486 7.486 0 0 1 5.855 2.812A8.224 8.224 0 0 1 12 20.25a8.224 8.224 0 0 1-5.855-2.438ZM15.75 9a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z" clip-rule="evenodd" />
                    </svg>
                    <!-- Light mode user icon (outline) -->
                    <svg v-else xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275M15 9.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                    </svg>
                  </div>
                </button>

                <div 
                  v-if="dropdownOpen" 
                  @click="dropdownOpen = false" 
                  class="fixed inset-0 z-10"
                ></div>

                <div 
                  v-if="dropdownOpen" 
                  class="dark:bg-[#0d1117] ring-1 ring-black ring-opacity-5 absolute right-0 z-20 w-48 mt-2 overflow-hidden bg-white rounded-md shadow-lg border dark:border-[#1c2129]"
                >
                  <router-link 
                    to="/settings" 
                    class="dark:text-gray-300 hover:bg-primary-600 hover:text-white dark:hover:bg-[#14F195]/5 dark:hover:text-[#14F195] block px-4 py-2 text-sm text-gray-700 transition-colors duration-150"
                    @click="dropdownOpen = false"
                  >
                    Profile
                  </router-link>
                  <a 
                    href="#" 
                    class="dark:text-gray-300 hover:bg-primary-600 hover:text-white dark:hover:bg-[#14F195]/5 dark:hover:text-[#14F195] block px-4 py-2 text-sm text-gray-700 transition-colors duration-150"
                    @click="logout"
                  >
                    Logout
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>
      
      <!-- Main content -->
      <main class="flex-1 overflow-y-auto bg-[#0d1117] dark:bg-[#0d1117]">
        <div class="sm:px-6 lg:px-8 px-4 py-8 mx-auto">
          <slot></slot>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useSettingsStore } from '@/stores/settings'
import {
  HomeIcon,
  KeyIcon,
  ChartBarIcon,
  CreditCardIcon,
  Cog6ToothIcon,
  Bars3Icon,
  XMarkIcon,
  SunIcon,
  MoonIcon
} from '@heroicons/vue/24/outline'

// Stores
const authStore = useAuthStore()
const settingsStore = useSettingsStore()
const router = useRouter()

// State
const sidebarOpen = ref(false)
const sidebarCollapsed = ref(false)
const dropdownOpen = ref(false)
const isDesktop = ref(window.innerWidth >= 1024)

// Computed
const darkMode = computed(() => settingsStore.darkMode)
// Methods
function toggleDarkMode() {
  settingsStore.toggleDarkMode()
}

function toggleSidebar() {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

async function logout() {
  await authStore.logout()
  router.push('/login')
}

// Handle window resize
function handleResize() {
  isDesktop.value = window.innerWidth >= 1024
  if (isDesktop.value) {
    sidebarOpen.value = false
  } else {
    sidebarCollapsed.value = false
  }
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
  handleResize()
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
})
</script> 
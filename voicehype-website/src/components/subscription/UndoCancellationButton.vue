<template>
  <div>    <button 
      @click="undoCancel"
      class="bg-green-600 hover:bg-green-700 text-white font-medium px-4 py-2 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed w-full flex items-center justify-center"
      :disabled="loading"
    >
      <svg v-if="loading" class="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <svg v-else class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
      </svg>
      {{ loading ? 'Undoing...' : 'Undo Cancellation' }}
    </button>

    <!-- Success message -->
    <div v-if="showSuccess" class="mt-2 text-xs text-green-600 dark:text-green-400">
      ✓ Cancellation undone successfully
    </div>

    <!-- Error message -->
    <div v-if="error" class="mt-2 text-xs text-red-600 dark:text-red-400">
      {{ error }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useSubscriptionStore } from '@/stores/subscription'

const subscriptionStore = useSubscriptionStore()
const loading = ref(false)
const showSuccess = ref(false)
const error = ref('')

const props = defineProps({
  currentPeriodEnd: {
    type: String,
    required: true
  },
  planName: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['success', 'error'])

async function undoCancel() {
  loading.value = true
  error.value = ''
  
  try {
    const success = await subscriptionStore.undoCancellation()
    
    if (success) {
      showSuccess.value = true
      emit('success', {
        message: `Your ${props.planName} subscription has been restored`,
        planName: props.planName
      })
      // Hide success message after 3 seconds
      setTimeout(() => {
        showSuccess.value = false
      }, 3000)
    } else {
      const errorMessage = subscriptionStore.error || 'Failed to undo cancellation'
      error.value = errorMessage
      emit('error', errorMessage)
    }
  } catch (err: any) {
    console.error('Undo cancellation failed:', err)
    const errorMessage = err.message || 'Failed to undo cancellation'
    error.value = errorMessage
    emit('error', errorMessage)
  } finally {
    loading.value = false
  }
}
</script>

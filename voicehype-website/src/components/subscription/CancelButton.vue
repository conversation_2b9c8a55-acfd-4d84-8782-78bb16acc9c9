<template>
  <div class="cancel-subscription-section">
    <!-- <PERSON><PERSON> (only show if subscription is active and not already cancelled) -->    
    <button 
      v-if="!subscription.cancel_at_period_end"
      @click="showConfirmModal = true"
      class="hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center w-full px-4 py-2 font-medium text-white transition-all duration-200 bg-red-600 rounded-lg"
      :disabled="loading"
    >
      <svg v-if="loading" class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      {{ loading ? 'Cancelling...' : 'Cancel Subscription' }}
    </button>

    <!-- Already Cancelled Status -->
    <div v-else class="cancelled-status">
      <div class="bg-yellow-50 dark:bg-yellow-900/20 dark:border-yellow-800 p-4 border border-yellow-200 rounded-lg">
        <div class="flex items-start">
          <svg class="w-5 h-5 mr-3 text-yellow-600 dark:text-yellow-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <div>
            <h4 class="dark:text-yellow-300 font-medium text-yellow-800">Subscription Cancelling</h4>
            <p class="dark:text-yellow-200 mt-1 text-sm text-yellow-700">
              Your subscription will end on {{ formatDate(subscription.current_period_end) }}.
              You'll keep full access until then.
            </p>
          </div>
        </div>
        
        <!-- Undo Cancellation Button -->
        <button 
          @click="undoCancellation"
          class="hover:bg-green-200 dark:text-green-300 dark:bg-green-900/30 dark:hover:bg-green-900/50 inline-flex items-center justify-center w-full px-4 py-2 mt-4 text-sm font-medium text-green-700 transition-colors duration-200 bg-green-100 border border-transparent rounded-lg"
          :disabled="undoLoading"
        >
          <svg v-if="undoLoading" class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          {{ undoLoading ? 'Restoring...' : 'Undo Cancellation' }}
        </button>
      </div>
    </div>

    <!-- Cancellation Modal -->
    <div v-if="showConfirmModal" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
      <!-- Background overlay -->
      <div class="dark:bg-gray-900 dark:bg-opacity-75 backdrop-blur-sm fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"></div>

      <!-- Modal container with padding -->
      <div class="flex items-center justify-center min-h-screen p-4">
        <!-- Modal panel -->
        <div class="relative w-full max-w-lg rounded-xl bg-white dark:bg-[#0d1117] shadow-xl transform transition-all">
          <!-- Modal header -->
          <div class="px-6 pt-6 pb-4 border-b border-gray-200 dark:border-[#30363d]">
            <div class="flex items-center">
              <div class="dark:bg-red-900/30 flex items-center justify-center flex-shrink-0 w-10 h-10 bg-red-100 rounded-full">
                <svg class="dark:text-red-400 w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h3 class="dark:text-white ml-3 text-lg font-semibold text-gray-900">
                Cancel Subscription
              </h3>
            </div>
          </div>

          <!-- Modal content -->
          <div class="px-6 py-4 max-h-[calc(100vh-16rem)] overflow-y-auto">
            <div class="space-y-4">
              <!-- Main message -->
              <p class="dark:text-gray-300 text-base text-gray-700">
                Are you sure you want to cancel your <strong class="dark:text-white font-medium text-gray-900">{{ planName }}</strong> subscription?
              </p>
              <p class="dark:text-gray-400 text-sm text-gray-600">
                Your subscription will be scheduled for cancellation at the end of your current billing period.
              </p>

              <!-- Current subscription details -->
              <div class="bg-gray-50 dark:bg-gray-800/50 dark:border-gray-700 p-4 mt-4 border border-gray-200 rounded-lg">
                <div class="flex items-center justify-between">
                  <div>
                    <h5 class="dark:text-white font-medium text-gray-900">{{ planName }} Plan</h5>
                    <p class="dark:text-gray-400 text-sm text-gray-600">${{ planPrice }}/month</p>
                  </div>
                  <div class="text-right">
                    <p class="dark:text-gray-400 text-sm text-gray-600">Current period ends</p>
                    <p class="dark:text-white font-medium text-gray-900">
                      {{ formatDate(subscription.current_period_end) }}
                    </p>
                  </div>
                </div>
              </div>

              <!-- What happens section -->
              <div class="mt-6">
                <h5 class="dark:text-white mb-3 font-medium text-gray-900">
                  What happens when you cancel:
                </h5>
                <ul class="space-y-3">
                  <li class="flex">
                    <div class="dark:bg-green-900/30 flex items-center justify-center flex-shrink-0 w-5 h-5 mr-3 bg-green-100 rounded-full">
                      <svg class="dark:text-green-400 w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                      </svg>
                    </div>
                    <span class="dark:text-gray-300 text-sm text-gray-700">
                      <strong>Keep full access</strong> until {{ formatDate(subscription.current_period_end) }}
                    </span>
                  </li>
                  <li class="flex">
                    <div class="dark:bg-green-900/30 flex items-center justify-center flex-shrink-0 w-5 h-5 mr-3 bg-green-100 rounded-full">
                      <svg class="dark:text-green-400 w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                      </svg>
                    </div>
                    <span class="dark:text-gray-300 text-sm text-gray-700">
                      <strong>No additional charges</strong> will be made
                    </span>
                  </li>
                  <li class="flex">
                    <div class="dark:bg-blue-900/30 flex items-center justify-center flex-shrink-0 w-5 h-5 mr-3 bg-blue-100 rounded-full">
                      <svg class="dark:text-blue-400 w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                      </svg>
                    </div>
                    <span class="dark:text-gray-300 text-sm text-gray-700">
                      <strong>You can undo</strong> this cancellation anytime before your period ends
                    </span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <!-- Modal footer -->
          <div class="px-6 py-4 bg-gray-50 dark:bg-[#161b22] border-t border-gray-200 dark:border-[#30363d] rounded-b-xl">
            <div class="flex items-center justify-end space-x-3">
              <button 
                @click="showConfirmModal = false"
                class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-[#21262d] border border-gray-300 dark:border-[#30363d] rounded-lg hover:bg-gray-50 dark:hover:bg-[#30363d] transition-colors duration-200"
              >
                Keep Subscription
              </button>
              <button 
                @click="confirmCancellation"
                class="hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed inline-flex items-center px-4 py-2 text-sm font-medium text-white transition-colors duration-200 bg-red-600 rounded-lg"
                :disabled="loading"
              >
                <svg v-if="loading" class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {{ loading ? 'Cancelling...' : 'Yes, Cancel Subscription' }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useSubscriptionStore } from '@/stores/subscription'
import { format } from 'date-fns'

const subscriptionStore = useSubscriptionStore()

const props = defineProps({
  subscription: {
    type: Object,
    required: true
  },
  planName: {
    type: String,
    required: true
  },
  planPrice: {
    type: [String, Number],
    required: true
  }
})

const emits = defineEmits(['cancelled', 'restored', 'error'])

// Reactive state
const showConfirmModal = ref(false)
const loading = ref(false)
const undoLoading = ref(false)

// Format date helper
function formatDate(dateString) {
  if (!dateString) return 'N/A'
  return format(new Date(dateString), 'MMMM d, yyyy')
}

// Cancel subscription
async function confirmCancellation() {
  loading.value = true
  try {
    const success = await subscriptionStore.cancelSubscription()
    if (success) {
      showConfirmModal.value = false
      emits('cancelled')
    }
  } catch (err) {
    console.error('Error cancelling subscription:', err)
    emits('error', err.message || 'Failed to cancel subscription')
  } finally {
    loading.value = false
  }
}

// Undo cancellation
async function undoCancellation() {
  undoLoading.value = true
  try {
    const success = await subscriptionStore.undoCancellation()
    if (success) {
      emits('restored')
    }
  } catch (err) {
    console.error('Error restoring subscription:', err)
    emits('error', err.message || 'Failed to restore subscription')
  } finally {
    undoLoading.value = false
  }
}
</script>

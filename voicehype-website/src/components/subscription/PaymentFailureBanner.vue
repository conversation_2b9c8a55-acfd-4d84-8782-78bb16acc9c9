 in <template>
  <Transition name="slide-down" appear>
    <div 
      v-if="shouldShowBanner" 
      class="payment-failure-banner"
      :class="bannerTypeClass"
    >
      <div class="max-w-7xl sm:px-6 lg:px-8 px-4 mx-auto">
        <div class="flex items-center justify-between py-3">
          <!-- Left side: Icon + Message -->
          <div class="flex items-center">
            <!-- Warning Icon for past_due -->
            <svg 
              v-if="subscriptionStatus === 'past_due'" 
              class="flex-shrink-0 w-6 h-6 mr-3" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                stroke-linecap="round" 
                stroke-linejoin="round" 
                stroke-width="2" 
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" 
              />
            </svg>

            <!-- Info Icon for other states -->
            <svg 
              v-else
              class="flex-shrink-0 w-6 h-6 mr-3" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                stroke-linecap="round" 
                stroke-linejoin="round" 
                stroke-width="2" 
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" 
              />
            </svg>

            <!-- Message Content -->
            <div class="flex-1">
              <div class="sm:flex-row sm:items-center flex flex-col">
                <p class="sm:text-base text-sm font-medium">
                  {{ bannerTitle }}
                </p>
                <p class="sm:mt-0 sm:ml-2 opacity-90 mt-1 text-sm">
                  {{ bannerMessage }}
                </p>
              </div>
            </div>
          </div>

          <!-- Right side: Action Button + Dismiss -->
          <div class="flex items-center ml-4 space-x-3">
            <!-- Primary Action Button -->
            <button 
              @click="handlePrimaryAction"
              class="action-button"
              :class="primaryActionClass"
              :disabled="actionLoading"
            >
              <svg 
                v-if="actionLoading" 
                class="animate-spin w-4 h-4 mr-2" 
                fill="none" 
                viewBox="0 0 24 24"
              >
                <circle 
                  class="opacity-25" 
                  cx="12" 
                  cy="12" 
                  r="10" 
                  stroke="currentColor" 
                  stroke-width="4"
                ></circle>
                <path 
                  class="opacity-75" 
                  fill="currentColor" 
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              {{ actionLoading ? 'Opening...' : primaryActionText }}
            </button>

            <!-- Dismiss Button -->
            <button 
              @click="dismissBanner"
              class="dismiss-button"
              :class="dismissButtonClass"
              title="Dismiss notification"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { supabase } from '@/lib/supabase'
import { useToast } from '@/services/toast'

// Props
interface Props {
  subscriptionStatus?: string | null
  subscription?: any
  currentPlan?: any
  autoHide?: boolean
  dismissible?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  subscriptionStatus: null,
  subscription: null,
  currentPlan: null,
  autoHide: false,
  dismissible: true
})

// Toast service
const { success: showSuccessToast, error: showErrorToast } = useToast()

// State
const dismissed = ref(false)
const actionLoading = ref(false)

// Computed properties
const shouldShowBanner = computed(() => {
  if (dismissed.value) return false
  
  // Show for payment failures and subscription issues
  return props.subscriptionStatus === 'past_due' || 
         props.subscriptionStatus === 'cancelled' ||
         (props.subscription?.cancel_at_period_end && props.subscriptionStatus === 'active')
})

const bannerTypeClass = computed(() => {
  switch (props.subscriptionStatus) {
    case 'past_due':
      return 'bg-yellow-50 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-200 dark:border-yellow-800'
    case 'cancelled':
      return 'bg-red-50 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-200 dark:border-red-800'
    default:
      return 'bg-blue-50 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-200 dark:border-blue-800'
  }
})

const bannerTitle = computed(() => {
  switch (props.subscriptionStatus) {
    case 'past_due':
      return '⚠️ Payment Failed'
    case 'cancelled':
      return '❌ Subscription Cancelled'
    default:
      if (props.subscription?.cancel_at_period_end) {
        return '📅 Subscription Ending'
      }
      return 'ℹ️ Subscription Notice'
  }
})

const bannerMessage = computed(() => {
  switch (props.subscriptionStatus) {
    case 'past_due':
      return 'Your payment failed, but your account remains active for 30 days while we retry. Update your payment method to avoid service interruption.'
    case 'cancelled':
      return 'Your subscription has been cancelled. Reactivate to continue using VoiceHype.'
    default:
      if (props.subscription?.cancel_at_period_end) {
        const endDate = new Date(props.subscription.current_period_end).toLocaleDateString()
        return `Your subscription will end on ${endDate}. You can reactivate anytime before then.`
      }
      return 'There may be an issue with your subscription. Please check your billing details.'
  }
})

const primaryActionText = computed(() => {
  switch (props.subscriptionStatus) {
    case 'past_due':
      return 'Update Payment Method'
    case 'cancelled':
      return 'Reactivate Subscription'
    default:
      return 'Manage Billing'
  }
})

const primaryActionClass = computed(() => {
  switch (props.subscriptionStatus) {
    case 'past_due':
      return 'bg-yellow-600 hover:bg-yellow-700 text-white dark:bg-yellow-500 dark:hover:bg-yellow-600'
    case 'cancelled':
      return 'bg-red-600 hover:bg-red-700 text-white dark:bg-red-500 dark:hover:bg-red-600'
    default:
      return 'bg-blue-600 hover:bg-blue-700 text-white dark:bg-blue-500 dark:hover:bg-blue-600'
  }
})

const dismissButtonClass = computed(() => {
  switch (props.subscriptionStatus) {
    case 'past_due':
      return 'text-yellow-600 hover:text-yellow-800 dark:text-yellow-400 dark:hover:text-yellow-200'
    case 'cancelled':
      return 'text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-200'
    default:
      return 'text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200'
  }
})

// Methods
async function handlePrimaryAction() {
  actionLoading.value = true
  
  try {
    // Open customer portal for all actions
    const { data, error } = await supabase.functions.invoke('paddle-customer-portal', {
      body: {}
    })

    if (error) {
      throw new Error(error.message)
    }

    if (!data?.portal_url) {
      throw new Error('No portal URL received')
    }

    showSuccessToast('Opening billing portal...', 'Redirecting to Paddle billing portal')
    window.location.href = data.portal_url

  } catch (error: any) {
    console.error('Failed to open customer portal:', error)
    showErrorToast('Failed to open billing portal', 'Please try again or contact support.')
  } finally {
    actionLoading.value = false
  }
}

function dismissBanner() {
  if (props.dismissible) {
    dismissed.value = true
    emit('dismissed', props.subscriptionStatus)
  }
}

// Auto-hide functionality
if (props.autoHide) {
  setTimeout(() => {
    dismissed.value = true
  }, 10000) // Hide after 10 seconds
}

// Emit events
const emit = defineEmits<{
  dismissed: [status: string | null]
  actionClicked: [action: string]
}>()

// Watch for subscription status changes and reset dismissed state
watch(() => props.subscriptionStatus, (newStatus, oldStatus) => {
  if (newStatus !== oldStatus && newStatus) {
    dismissed.value = false
  }
})
</script>

<style scoped>
.payment-failure-banner {
  @apply border-b-2 shadow-sm;
  position: sticky;
  top: 0;
  z-index: 40;
}

.action-button {
  @apply px-4 py-2 rounded-lg font-medium text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
}

.dismiss-button {
  @apply p-1 rounded-lg transition-colors duration-200 hover:bg-black/5 dark:hover:bg-white/5;
}

/* Slide down animation */
.slide-down-enter-active {
  transition: all 0.3s ease-out;
}

.slide-down-leave-active {
  transition: all 0.3s ease-in;
}

.slide-down-enter-from {
  transform: translateY(-100%);
  opacity: 0;
}

.slide-down-leave-to {
  transform: translateY(-100%);
  opacity: 0;
}

/* Animation for loading spinner */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .payment-failure-banner {
    position: relative;
  }
  
  .action-button {
    @apply px-3 py-1.5 text-xs;
  }
}
</style>

<template>
  <div 
    @click="$emit('select')"
    :class="[
      'border-2 rounded-lg p-4 cursor-pointer transition-all duration-200',
      selected 
        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-400' 
        : 'border-gray-200 dark:border-[#30363d] hover:border-gray-300 dark:hover:border-[#14F195] hover:bg-gray-50 dark:hover:bg-[#14F195]/5'
    ]"
  >
    <!-- Header with title and badge -->
    <div class="flex items-center justify-between mb-2">
      <div class="flex items-center space-x-3">
        <!-- Radio button -->
        <div :class="[
          'w-4 h-4 rounded-full border-2 flex items-center justify-center',
          selected ? 'border-blue-500 dark:border-blue-400' : 'border-gray-300 dark:border-[#30363d]'
        ]">
          <div v-if="selected" class="w-2 h-2 rounded-full bg-blue-500 dark:bg-blue-400"></div>
        </div>
        
        <!-- Title -->
        <h4 class="font-semibold text-gray-900 dark:text-white">{{ title }}</h4>
      </div>
      
      <!-- Badge -->
      <span :class="[
        'px-2 py-1 rounded-full text-xs font-medium',
        badgeColor === 'blue' 
          ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300' 
          : 'bg-green-100 text-green-700 dark:bg-green-900/50 dark:text-green-300'
      ]">
        {{ badge }}
      </span>
    </div>

    <!-- Description -->
    <p class="text-sm text-gray-600 dark:text-gray-300 mb-3 ml-7">{{ description }}</p>

    <!-- Benefits list -->
    <div class="ml-7">
      <ul class="space-y-1">
        <li 
          v-for="benefit in benefits" 
          :key="benefit"
          class="flex items-center space-x-2 text-sm"
        >
          <svg class="w-4 h-4 text-green-500 dark:text-green-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
          <span class="text-gray-700 dark:text-gray-200">{{ benefit }}</span>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  selected: boolean
  title: string
  description: string
  benefits: string[]
  badge: string
  badgeColor: 'blue' | 'green'
}

defineProps<Props>()
defineEmits<{
  select: []
}>()
</script>

<template>
  <div class="bg-gray-50 dark:bg-[#0d1117] rounded-lg p-4 border border-gray-200 dark:border-[#30363d]">
    <h4 class="text-sm font-semibold text-gray-700 dark:text-white mb-3">Quota Comparison</h4>
    
    <div class="space-y-3">
      <!-- Transcription Minutes -->
      <div class="flex justify-between items-center text-sm">
        <span class="text-gray-600 dark:text-gray-400">Transcription Minutes:</span>
        <div class="flex items-center space-x-2">
          <span class="text-gray-800 dark:text-white">{{ currentQuotas.transcription_minutes || 0 }}</span>
          <span class="text-gray-400 dark:text-gray-500">→</span>
          <span class="font-medium text-blue-600 dark:text-blue-400">{{ targetQuotas.transcription_minutes || 0 }}</span>
          <span v-if="mergedQuotas?.transcription_minutes" class="text-green-600 dark:text-green-400 text-xs">
            (Total: {{ Math.ceil((remainingQuotas?.transcription || 0) + (targetQuotas.transcription_minutes || 0)) }})
          </span>
        </div>
      </div>

      <!-- Total Tokens (combined input + output) -->
      <div class="flex justify-between items-center text-sm">
        <span class="text-gray-600 dark:text-gray-400">Total Tokens:</span>
        <div class="flex items-center space-x-2">
          <span class="text-gray-800 dark:text-white">{{ formatNumber(getTotalTokens(currentQuotas)) }}</span>
          <span class="text-gray-400 dark:text-gray-500">→</span>
          <span class="font-medium text-blue-600 dark:text-blue-400">{{ formatNumber(getTotalTokens(targetQuotas)) }}</span>
          <span v-if="mergedQuotas && getTotalRemainingTokens() > 0" class="text-green-600 dark:text-green-400 text-xs">
            (Total: {{ formatNumber(Math.ceil(getTotalRemainingTokens() + getTotalTokens(targetQuotas))) }})
          </span>
        </div>
      </div>
    </div>

    <!-- Remaining Quotas Info -->
    <div v-if="hasRemainingQuotas" class="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700/50 rounded-lg">
      <div class="flex items-start space-x-2">
        <svg class="w-4 h-4 text-blue-500 dark:text-blue-400 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
        </svg>
        <div>
          <p class="text-sm text-blue-700 dark:text-blue-300 font-medium">Unused quotas merge over!</p>
          <p class="text-xs text-blue-600 dark:text-blue-400 mt-1">
            Your remaining quotas will be merged with your new plan limits if you choose "Upgrade Now + Merge Quotas".
          </p>
        </div>
      </div>
    </div>

    <!-- No Remaining Quotas -->
    <div v-else-if="mergedQuotas" class="mt-4 p-3 bg-gray-100 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700/50 rounded-lg">
      <div class="flex items-start space-x-2">
        <svg class="w-4 h-4 text-gray-500 dark:text-gray-400 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
        </svg>
        <div>
          <p class="text-sm text-gray-600 dark:text-gray-300">No remaining quotas to carry over</p>
          <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
            You've used most of your current plan's quotas. Both upgrade options will give you the same result.
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  currentQuotas: Record<string, number>
  remainingQuotas: Record<string, number> | null
  mergedQuotas: Record<string, number> | null
  targetQuotas: Record<string, number>
}

const props = defineProps<Props>()

// Check if there are any significant remaining quotas
const hasRemainingQuotas = computed(() => {
  if (!props.remainingQuotas) return false
  
  // Check transcription minutes
  const transcriptionRemaining = props.remainingQuotas.transcription || 0
  
  // Check total tokens (sum of input and output)
  const tokensRemaining = getTotalRemainingTokens()
  
  return transcriptionRemaining > 0 || tokensRemaining > 0
})

// Get total tokens (input + output) from quotas object
function getTotalTokens(quotas: Record<string, number>): number {
  const inputTokens = quotas.input_tokens || 0
  const outputTokens = quotas.output_tokens || 0
  const tokens = quotas.tokens || 0
  
  // If 'tokens' field exists, use it, otherwise sum input and output
  return tokens > 0 ? tokens : (inputTokens + outputTokens)
}

// Get total remaining tokens
function getTotalRemainingTokens(): number {
  if (!props.remainingQuotas) return 0
  
  // Check if we have optimization service (which maps to tokens)
  const optimizationRemaining = props.remainingQuotas.optimization || 0
  
  // Also check for input_tokens and output_tokens for backward compatibility
  const inputTokensRemaining = props.remainingQuotas.input_tokens || 0
  const outputTokensRemaining = props.remainingQuotas.output_tokens || 0
  
  // Use optimization if available (this is the main token service), otherwise sum input/output
  return optimizationRemaining > 0 ? optimizationRemaining : (inputTokensRemaining + outputTokensRemaining)
}

// Format large numbers with commas
function formatNumber(num: number): string {
  return num.toLocaleString()
}
</script>

<template>
  <div class="customer-portal-section">
    <!-- Full Beautiful Layout for large/default size -->
    <div v-if="props.size === 'lg' || props.size === 'md'" class="bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 dark:border-indigo-800 rounded-xl hover:shadow-md flex items-center justify-between p-6 transition-all duration-300 border border-indigo-200 shadow-sm">
      <!-- Left Side - Information -->
      <div class="flex-1 pr-6">
        <div class="flex items-center mb-2">
          <div class="bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center flex-shrink-0 w-10 h-10 rounded-lg shadow-md">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
            </svg>
          </div>
          <div class="ml-4">
            <h3 class="dark:text-white text-lg font-semibold text-gray-900">Billing Management</h3>
            <p class="dark:text-gray-400 text-sm text-gray-600">Secure billing portal powered by Paddle</p>
          </div>
        </div>
        
        <div class="ml-14">
          <ul class="dark:text-gray-300 space-y-1 text-sm text-gray-700">
            <li class="flex items-center">
              <svg class="flex-shrink-0 w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              View and download invoices
            </li>
            <li class="flex items-center">
              <svg class="flex-shrink-0 w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              Update payment methods
            </li>
            <li class="flex items-center">
              <svg class="flex-shrink-0 w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              Manage subscription settings
            </li>
          </ul>
        </div>
      </div>

      <!-- Right Side - Button -->
      <div class="flex-shrink-0">
        <button 
          @click="openCustomerPortal"
          class="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 hover:scale-105 hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-none flex items-center px-6 py-3 font-medium text-white transition-all duration-200 transform rounded-lg"
          :disabled="loading"
        >
          <!-- Loading Spinner -->
          <svg 
            v-if="loading" 
            class="animate-spin w-4 h-4 mr-2" 
            fill="none" 
            viewBox="0 0 24 24"
          >
            <circle 
              class="opacity-25" 
              cx="12" 
              cy="12" 
              r="10" 
              stroke="currentColor" 
              stroke-width="4"
            ></circle>
            <path 
              class="opacity-75" 
              fill="currentColor" 
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>

          <!-- Arrow Icon when not loading -->
          <svg 
            v-else
            class="w-4 h-4 mr-2" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              stroke-linecap="round" 
              stroke-linejoin="round" 
              stroke-width="2" 
              d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
            />
          </svg>

          {{ loading ? 'Opening Portal...' : 'Open Portal' }}
        </button>
      </div>
    </div>

    <!-- Compact Layout for small size -->
    <button 
      v-else
      @click="openCustomerPortal"
      :class="[
        'flex items-center justify-center px-4 py-2 rounded-lg font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed w-full',
        props.variant === 'outline' 
          ? 'border border-indigo-300 text-indigo-600 hover:bg-indigo-50 focus:ring-indigo-500 dark:border-indigo-600 dark:text-indigo-400 dark:hover:bg-indigo-900/20' 
          : 'bg-indigo-600 hover:bg-indigo-700 text-white focus:ring-indigo-500 dark:bg-indigo-500 dark:hover:bg-indigo-600'
      ]"
      :disabled="loading"
    >
      <!-- Loading Spinner -->
      <svg 
        v-if="loading" 
        class="animate-spin w-4 h-4 mr-2" 
        fill="none" 
        viewBox="0 0 24 24"
      >
        <circle 
          class="opacity-25" 
          cx="12" 
          cy="12" 
          r="10" 
          stroke="currentColor" 
          stroke-width="4"
        ></circle>
        <path 
          class="opacity-75" 
          fill="currentColor" 
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        ></path>
      </svg>

      <!-- Credit Card Icon -->
      <svg 
        v-else
        class="w-4 h-4 mr-2" 
        fill="none" 
        stroke="currentColor" 
        viewBox="0 0 24 24"
      >
        <path 
          stroke-linecap="round" 
          stroke-linejoin="round" 
          stroke-width="2" 
          d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
        />
      </svg>

      {{ loading ? 'Opening Portal...' : 'Manage Billing' }}
    </button>

    <!-- Environment Badge (only show in development) -->
    <div v-if="isDevelopment && lastEnvironment" class="mt-2">
      <span 
        class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full"
        :class="environmentBadgeClass"
      >
        {{ lastEnvironment.toUpperCase() }} Environment
      </span>
    </div>

    <!-- Success Message -->
    <div v-if="showSuccessMessage" class="bg-green-50 dark:bg-green-900/20 dark:border-green-800 p-3 mt-3 border border-green-200 rounded-lg">
      <div class="flex items-center">
        <svg class="dark:text-green-400 w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <p class="dark:text-green-400 text-sm text-green-600">
          Billing portal opened successfully. You can manage your subscription, payment methods, and view invoices.
        </p>
      </div>
    </div>

    <!-- Error Message -->
    <div v-if="error" class="bg-red-50 dark:bg-red-900/20 dark:border-red-800 p-3 mt-3 border border-red-200 rounded-lg">
      <div class="flex items-center">
        <svg class="dark:text-red-400 w-5 h-5 mr-2 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <div class="text-sm">
          <p class="dark:text-red-400 font-medium text-red-600">Failed to open billing portal</p>
          <p class="dark:text-red-300 mt-1 text-red-500">{{ error }}</p>
          <button 
            @click="error = null" 
            class="dark:text-red-400 hover:no-underline mt-1 text-xs text-red-600 underline"
          >
            Dismiss
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { supabase } from '@/lib/supabase'
import { useToast } from '@/services/toast'

// Initialize toast service
const { success: showSuccessToast, error: showErrorToast } = useToast()

// Props
interface Props {
  size?: 'sm' | 'md' | 'lg'
  variant?: 'primary' | 'secondary' | 'outline'
  showSuccessMessage?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  variant: 'primary',
  showSuccessMessage: true
})

// Reactive state
const loading = ref(false)
const error = ref<string | null>(null)
const lastEnvironment = ref<string | null>(null)
const showSuccessMessage = ref(false)

// Computed properties
const isDevelopment = computed(() => {
  return import.meta.env.DEV || import.meta.env.VITE_ENVIRONMENT === 'development'
})

const environmentBadgeClass = computed(() => {
  if (lastEnvironment.value === 'sandbox') {
    return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
  } else if (lastEnvironment.value === 'production') {
    return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
  }
  return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
})

// Methods
async function openCustomerPortal() {
  loading.value = true
  error.value = null
  showSuccessMessage.value = false

  try {
    console.log('🚀 Opening customer portal...')
    
    const { data, error: apiError } = await supabase.functions.invoke('paddle-customer-portal', {
      body: {}
    })

    if (apiError) {
      throw new Error(apiError.message || 'Failed to create portal session')
    }

    if (!data?.portal_url) {
      throw new Error('No portal URL received from server')
    }

    // Store environment for debugging
    if (data.environment) {
      lastEnvironment.value = data.environment
      console.log(`🔧 Portal opened in ${data.environment} environment`)
    }

    // Show success message if enabled
    if (props.showSuccessMessage) {
      showSuccessMessage.value = true
      setTimeout(() => {
        showSuccessMessage.value = false
      }, 5000)
    }

    // Show toast notification
    showSuccessToast(`Opening billing portal...`, `Portal opened in ${data.environment || 'unknown'} environment`)

    // Redirect to Paddle's customer portal
    // Using window.location.href instead of window.open for better UX
    console.log(`🔗 Redirecting to portal: ${data.portal_url}`)
    window.location.href = data.portal_url

  } catch (err: any) {
    console.error('❌ Customer portal error:', err)
    
    // Set user-friendly error message
    if (err.message?.includes('No active subscription')) {
      error.value = 'You need an active subscription to access the billing portal. Please subscribe to a plan first.'
    } else if (err.message?.includes('Unauthorized')) {
      error.value = 'Please log in to access your billing portal.'
    } else if (err.message?.includes('configuration')) {
      error.value = 'Billing portal is temporarily unavailable. Please try again later.'
    } else {
      error.value = err.message || 'An unexpected error occurred. Please try again.'
    }

    // Show error toast
    showErrorToast('Failed to open billing portal', error.value || 'An unexpected error occurred')
  } finally {
    loading.value = false
  }
}

// Emit events for parent components
const emit = defineEmits<{
  success: [environment: string]
  error: [error: string]
}>()

// Watch for state changes and emit events
import { watch } from 'vue'

watch(lastEnvironment, (newEnv) => {
  if (newEnv) {
    emit('success', newEnv)
  }
})

watch(error, (newError) => {
  if (newError) {
    emit('error', newError)
  }
})
</script>

<style scoped>
/* Animation for loading spinner */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Fade-in animation for messages */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}
</style>

<template>
  <div v-if="isOpen" class="fixed inset-0 bg-black dark:bg-gray-900 bg-opacity-50 dark:bg-opacity-75 backdrop-blur-sm flex items-center justify-center z-50">
    <div class="bg-white dark:bg-[#161b22] rounded-lg p-6 max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto border border-gray-200 dark:border-[#30363d]">
      <!-- Header -->
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-xl font-bold text-gray-900 dark:text-white">
          {{ isUpgrade ? 'Upgrade' : 'Change' }} Subscription
        </h2>
        <button 
          @click="$emit('close')"
          class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Plan Selector (when no target plan specified) -->
      <div v-if="!targetPlan && !selectedPlan" class="mb-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Select New Plan</h3>
        <div class="space-y-3">
          <button 
            v-for="plan in availablePlans" 
            :key="plan.name"
            @click="selectedPlan = plan.name as 'Basic' | 'Pro' | 'Premium'"
            class="w-full p-3 text-left border border-gray-200 dark:border-[#30363d] rounded-lg hover:border-primary-500 dark:hover:border-[#14F195] hover:bg-primary-50 dark:hover:bg-[#14F195]/5 transition-colors"
          >
            <div class="flex justify-between items-center">
              <div>
                <span class="font-medium text-gray-900 dark:text-white">{{ plan.name }}</span>
                <span class="text-sm text-gray-600 dark:text-gray-400 block">{{ plan.description }}</span>
              </div>
              <span class="text-lg font-bold text-primary-600 dark:text-[#14F195]">${{ plan.monthly_price }}/month</span>
            </div>
          </button>
        </div>
      </div>

      <!-- Current vs Target Plan Summary -->
      <div v-if="preview" class="mb-6 p-4 bg-gray-50 dark:bg-[#0d1117] rounded-lg border border-gray-200 dark:border-[#30363d]">
        <div class="flex justify-between items-center mb-2">
          <div>
            <span class="text-sm text-gray-600 dark:text-gray-400">Current:</span>
            <span class="font-medium text-gray-900 dark:text-white">{{ preview.currentPlan.name }}</span>
            <span class="text-sm text-gray-600 dark:text-gray-400 ml-2">${{ preview.currentPlan.monthly_price }}/month</span>
          </div>
          <div class="text-right">
            <span class="text-sm text-gray-600 dark:text-gray-400">New:</span>
            <span class="font-medium text-gray-900 dark:text-white">{{ preview.targetPlan.name }}</span>
            <span class="text-sm text-gray-600 dark:text-gray-400 ml-2">${{ preview.targetPlan.monthly_price }}/month</span>
          </div>
        </div>
        
        <div v-if="preview.priceDifference !== 0" class="text-center">
          <span 
            :class="preview.priceDifference > 0 ? 'text-primary-600 dark:text-[#14F195]' : 'text-red-600 dark:text-red-400'"
            class="text-sm font-medium"
          >
            {{ preview.priceDifference > 0 ? '+' : '' }}${{ preview.priceDifference }}/month
          </span>
        </div>
      </div>

      <!-- Upgrade Options -->
      <div class="space-y-4 mb-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Choose your upgrade option:</h3>
        
        <!-- Option A: Immediate Change -->
        <UpgradeOptionCard
          :selected="selectedOption === 'upgrade_now_merge_quotas'"
          @select="selectedOption = 'upgrade_now_merge_quotas'"
          :title="isUpgrade ? 'Upgrade Now + Merge Quotas' : 'Change Plan Now'"
          :description="isUpgrade 
            ? 'Upgrade immediately and get access to new features. Your remaining quotas will be merged with the new plan limits.'
            : 'Change your plan immediately. Your billing cycle will restart today.'"
          :benefits="[
            'Immediate access to new features',
            isUpgrade ? 'Merge unused quotas with new plan' : 'Start fresh with new quotas',
            'New billing cycle starts today',
            'Prorated charges/credits applied'
          ]"
          badge="Immediate"
          badge-color="green"
          class="dark:text-gray-100"
        />

        <!-- Quota Preview - Show below merge option when selected -->
        <QuotaPreview 
          v-if="preview && selectedOption === 'upgrade_now_merge_quotas'"
          :current-quotas="getCurrentQuotaDisplay()"
          :remaining-quotas="preview.remainingQuotas"
          :merged-quotas="preview.mergedQuotas"
          :target-quotas="getTargetQuotaDisplay()"
          class="ml-4 mt-3"
        />

        <!-- Option B: Next Billing Cycle -->
        <UpgradeOptionCard
          :selected="selectedOption === 'update_next_billing'"
          @select="selectedOption = 'update_next_billing'"
          title="Update on Next Billing Cycle"
          description="Keep your current plan until your next renewal, then switch to the new plan."
          :benefits="[
            'Changes apply at next renewal',
            'Keep current quotas until then',
            'No disruption to billing cycle',
            'Predictable transition'
          ]"
          badge="Scheduled"
          badge-color="blue"
          class="dark:text-gray-100"
        />
      </div>

      <!-- Action Buttons -->
      <div class="flex space-x-3">
        <button
          @click="$emit('close')"
          class="flex-1 px-4 py-2 border border-gray-300 dark:border-[#30363d] text-gray-700 dark:text-gray-300 bg-white dark:bg-[#21262d] rounded-lg hover:bg-gray-50 dark:hover:bg-[#30363d] transition-colors"
          :disabled="loading"
        >
          Cancel
        </button>
        <button
          @click="handleUpgrade"
          :disabled="!selectedOption || !targetPlan || loading"
          class="flex-1 px-4 py-2 bg-blue-600 dark:bg-[#14F195] text-white dark:text-gray-900 rounded-lg hover:bg-blue-700 dark:hover:bg-[#14F195]/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <span v-if="loading" class="flex items-center justify-center">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Processing...
          </span>
          <span v-else>
            {{ selectedOption === 'update_next_billing' ? 'Schedule Change' : 'Upgrade Now' }}
          </span>
        </button>
      </div>

      <!-- Error Display -->
      <div v-if="error" class="mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
              Upgrade Failed
            </h3>
            <div class="mt-1 text-sm text-red-700 dark:text-red-300">
              <p>{{ error }}</p>
            </div>
            <div class="mt-3">
              <button
                @click="error = null"
                class="text-xs bg-red-100 dark:bg-red-800/30 text-red-800 dark:text-red-200 px-2 py-1 rounded-md hover:bg-red-200 dark:hover:bg-red-800/50 transition-colors"
              >
                Dismiss
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useSubscriptionStore } from '@/stores/subscription'
import QuotaPreview from './QuotaPreview.vue'
import UpgradeOptionCard from './UpgradeOptionCard.vue'

interface Props {
  isOpen: boolean
  targetPlan?: 'Basic' | 'Pro' | 'Premium'
}

const props = defineProps<Props>()
const emit = defineEmits(['close', 'success', 'error'])

// Local state
const subscriptionStore = useSubscriptionStore()
const loading = ref(false)
const error = ref<string | null>(null)
const selectedOption = ref<'update_next_billing' | 'upgrade_now_merge_quotas' | null>(null)
const selectedPlan = ref<'Basic' | 'Pro' | 'Premium' | null>(null)

// Filter out $0 plan and compute available plans
const availablePlans = computed(() => {
  return subscriptionStore.availablePlans.filter(plan => 
    plan.monthly_price > 0 && 
    plan.name !== subscriptionStore.currentPlan?.name
  )
})

// Use provided target plan or local selection
const targetPlan = computed(() => props.targetPlan || selectedPlan.value)

// Compute if this is an upgrade (vs downgrade)
const isUpgrade = computed(() => {
  if (!preview.value) return false
  return preview.value.priceDifference > 0
})

// Get upgrade preview data
const preview = computed(() => {
  if (!targetPlan.value) return null
  return subscriptionStore.getUpgradePreview(targetPlan.value)
})

// Get current quota display
function getCurrentQuotaDisplay(): Record<string, number> {
  if (!preview.value?.currentPlan) return {
    transcription_minutes: 0,
    tokens: 0,
    input_tokens: 0,
    output_tokens: 0
  }
  
  const plan = preview.value.currentPlan
  const totalTokens = plan.tokens || (plan.input_tokens + plan.output_tokens)
  
  return {
    transcription_minutes: plan.transcription_minutes || 0,
    tokens: totalTokens,
    input_tokens: totalTokens, // For backward compatibility
    output_tokens: 0 // VoiceHype doesn't distinguish between input/output
  }
}

// Get target quota display
function getTargetQuotaDisplay(): Record<string, number> {
  if (!preview.value?.targetPlan) return {
    transcription_minutes: 0,
    tokens: 0,
    input_tokens: 0,
    output_tokens: 0
  }
  
  const plan = preview.value.targetPlan
  const totalTokens = plan.tokens || (plan.input_tokens + plan.output_tokens)
  
  return {
    transcription_minutes: plan.transcription_minutes || 0,
    tokens: totalTokens,
    input_tokens: totalTokens, // For backward compatibility
    output_tokens: 0 // VoiceHype doesn't distinguish between input/output
  }
}

// Handle upgrade action
async function handleUpgrade() {
  if (!selectedOption.value || !targetPlan.value) return

  loading.value = true
  error.value = null

  try {
    console.log(`Starting ${selectedOption.value} upgrade to ${targetPlan.value} plan`)
    
    const success = await subscriptionStore.upgradeSubscription(
      targetPlan.value,
      selectedOption.value
    )

    if (success) {
      console.log('Upgrade completed successfully')
      
      // Show different success messages based on upgrade type
      if (selectedOption.value === 'upgrade_now_merge_quotas') {
        console.log('Immediate upgrade with quota merging completed')
      } else {
        console.log('Scheduled upgrade for next billing cycle completed')
      }
      
      emit('success')
      // Don't emit close here - let the parent handle it after showing success
    } else {
      const errorMsg = subscriptionStore.error || 'Failed to upgrade subscription'
      console.error('Upgrade failed:', errorMsg)
      error.value = `Upgrade failed: ${errorMsg}`
      emit('error', errorMsg)
    }
  } catch (err: any) {
    console.error('Upgrade error:', err)
    
    // Provide more user-friendly error messages
    let userError = 'An unexpected error occurred while upgrading your subscription.'
    
    if (err.message?.includes('network') || err.message?.includes('fetch')) {
      userError = 'Network error: Please check your connection and try again.'
    } else if (err.message?.includes('auth') || err.message?.includes('session')) {
      userError = 'Authentication error: Please refresh the page and try again.'
    } else if (err.message?.includes('billing') || err.message?.includes('payment')) {
      userError = 'Billing error: Please check your payment method and try again.'
    } else if (err.message) {
      userError = err.message
    }
    
    error.value = userError
    emit('error', userError)
  } finally {
    loading.value = false
  }
}

// Reset state when modal opens/closes
watch(() => props.isOpen, (isOpen) => {
  if (isOpen) {
    // If target plan is provided, preselect it
    if (props.targetPlan) {
      selectedPlan.value = props.targetPlan
    }
  } else {
    // Reset all state when modal closes
    selectedPlan.value = null
    selectedOption.value = null
    error.value = null
  }
})
</script>

<style scoped>
/* Add transitions */
.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}
</style>

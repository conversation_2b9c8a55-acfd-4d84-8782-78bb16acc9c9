<template>
  <div class="chart-container">
    <h3 class="text-lg font-medium text-gray-800 dark:text-[#c9d1d9] mb-4">{{ title }}</h3>
    <div v-if="loading" class="flex items-center justify-center h-64">
      <ArrowPathIcon class="animate-spin w-8 h-8 text-primary-600 dark:text-[#14F195]" />
    </div>
    <div v-else-if="!hasData" class="flex items-center justify-center h-64 text-gray-500 dark:text-[#8b949e]">
      No data available for the selected period
    </div>
    <div v-else class="h-64">
      <Bar
        :data="chartData"
        :options="chartOptions"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Bar } from 'vue-chartjs'
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js'
import { ArrowPathIcon } from '@heroicons/vue/24/outline'
import type { ChartOptions } from 'chart.js'

// Register Chart.js components
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend)

// Props
const props = defineProps<{
  title: string
  data: Record<string, number>
  loading: boolean
  colorScheme?: 'service' | 'model' | 'token'
}>()

// Computed
const hasData = computed(() => {
  return props.data && Object.keys(props.data).length > 0 &&
    Object.values(props.data).some(value => value > 0)
})

// Define color scheme types
type ServiceColorScheme = {
  transcription: string;
  optimization: string;
  [key: string]: string;
};

type ModelColorScheme = {
  whisper: string;
  assemblyai: string;
  lemonfox: string;
  llama: string;
  'llama-4': string;
  'llama-3': string;
  scout: string;
  maverick: string;
  claude: string;
  'claude-3.7': string;
  'claude-3.5': string;
  haiku: string;
  sonnet: string;
  deepseek: string;
  'deepseek-r1': string;
  'deepseek-v3': string;
  default: string;
  [key: string]: string;
};

type TokenColorScheme = {
  'Input Tokens': string;
  'Output Tokens': string;
  input: string;
  output: string;
  default: string;
  [key: string]: string;
};

type ColorSchemes = {
  service: ServiceColorScheme;
  model: ModelColorScheme;
  token: TokenColorScheme;
};

// Color schemes (same as in UsagePieChart to maintain consistency)
const colorSchemes: ColorSchemes = {
  service: {
    transcription: 'rgba(20, 241, 149, 0.7)',
    optimization: 'rgba(59, 130, 246, 0.7)'
  },
  model: {
    whisper: 'rgba(234, 88, 12, 0.7)',
    assemblyai: 'rgba(217, 70, 239, 0.7)',
    lemonfox: 'rgba(251, 191, 36, 0.7)',
    llama: 'rgba(16, 185, 129, 0.7)',
    'llama-4': 'rgba(5, 150, 105, 0.7)',
    'llama-3': 'rgba(16, 185, 129, 0.7)',
    scout: 'rgba(5, 150, 105, 0.7)',
    maverick: 'rgba(4, 120, 87, 0.7)',
    claude: 'rgba(6, 182, 212, 0.7)',
    'claude-3.7': 'rgba(79, 70, 229, 0.7)',
    'claude-3.5': 'rgba(124, 58, 237, 0.7)',
    haiku: 'rgba(14, 165, 233, 0.7)',
    sonnet: 'rgba(6, 182, 212, 0.7)',
    deepseek: 'rgba(220, 38, 38, 0.7)',
    'deepseek-r1': 'rgba(185, 28, 28, 0.7)',
    'deepseek-v3': 'rgba(239, 68, 68, 0.7)',
    default: 'rgba(107, 114, 128, 0.7)'
  },
  token: {
    'Input Tokens': 'rgba(59, 130, 246, 0.7)',
    'Output Tokens': 'rgba(16, 185, 129, 0.7)',
    input: 'rgba(59, 130, 246, 0.7)',
    output: 'rgba(16, 185, 129, 0.7)',
    default: 'rgba(107, 114, 128, 0.7)'
  }
}

// Generate a color based on the key and color scheme
const getColor = (key: string, index: number) => {
  const scheme = props.colorScheme || 'service'
  const colors = colorSchemes[scheme as keyof ColorSchemes]

  // First check for exact match (case-insensitive)
  const lowerKey = key.toLowerCase()

  // Try exact match first
  if (key in colors) {
    return colors[key]
  }

  // Then try case-insensitive exact match
  for (const [colorKey, color] of Object.entries(colors)) {
    if (colorKey.toLowerCase() === lowerKey) {
      return color
    }
  }

  // Then try partial match
  for (const [colorKey, color] of Object.entries(colors)) {
    // Skip the default key for partial matching
    if (colorKey === 'default') continue

    if (lowerKey.includes(colorKey.toLowerCase())) {
      return color
    }
  }

  // If no match, use default or generate a color based on index
  if ('default' in colors) {
    return colors.default
  }

  // Fallback to a generated color with better distribution
  const hue = (index * 137.5) % 360
  return `hsla(${hue}, 70%, 60%, 0.7)`
}

const chartData = computed(() => {
  const labels = Object.keys(props.data)
  const values = Object.values(props.data)

  // Generate colors based on keys and color scheme
  const backgroundColor = labels.map((key, index) => getColor(key, index))

  return {
    labels,
    datasets: [
      {
        label: '',
        data: values,
        backgroundColor,
        borderColor: backgroundColor.map(color => color.replace('0.7', '1')),
        borderWidth: 1
      }
    ]
  }
})

const chartOptions = computed<ChartOptions<'bar'>>(() => {
  // Determine if we're in dark mode
  const isDarkMode = document.documentElement.classList.contains('dark')
  const textColor = isDarkMode ? '#c9d1d9' : '#374151'
  const gridColor = isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'

  return {
    responsive: true,
    maintainAspectRatio: false,
    indexAxis: 'y',
    scales: {
      y: {
        ticks: {
          color: textColor,
          font: {
            size: 12
          }
        },
        grid: {
          display: false
        }
      },
      x: {
        beginAtZero: true,
        ticks: {
          color: textColor
        },
        grid: {
          color: gridColor
        }
      }
    },
    plugins: {
      legend: {
        display: false  // We don't need a legend for single dataset bar charts
      },
      tooltip: {
        callbacks: {
          label: (context) => {
            const label = context.label || ''
            const value = context.raw as number
            const total = (context.chart.data.datasets[0].data as number[]).reduce((a, b) => a + (b || 0), 0)
            const percentage = Math.round((value / total) * 100)

            // Format based on the title (assuming it contains keywords)
            const title = props.title.toLowerCase()
            if (title.includes('token')) {
              return `${value.toLocaleString()} tokens (${percentage}%)`
            } else if (title.includes('minute')) {
              return `${value.toFixed(2)} mins (${percentage}%)`
            } else {
              return `$${value.toFixed(2)} (${percentage}%)`
            }
          },
          title: (tooltipItems) => {
            // Display full label (not truncated) in tooltip title
            return tooltipItems[0].label;
          }
        }
      }
    }
  }
})

// Watch for dark mode changes
const updateChartOnThemeChange = () => {
  // Force chart update when theme changes
  ChartJS.defaults.color = document.documentElement.classList.contains('dark') ? '#c9d1d9' : '#374151'
}

// Set up a MutationObserver to watch for theme changes
if (typeof window !== 'undefined') {
  const observer = new MutationObserver(updateChartOnThemeChange)
  observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] })
}
</script>

<template>
  <div class="chart-container">
    <h3 class="text-lg font-medium text-gray-800 dark:text-[#c9d1d9] mb-4">Transcription Minutes Used</h3>
    <div v-if="loading" class="flex items-center justify-center h-64">
      <ArrowPathIcon class="animate-spin w-8 h-8 text-primary-600 dark:text-[#14F195]" />
    </div>
    <div v-else-if="!hasData" class="flex items-center justify-center h-64 text-gray-500 dark:text-[#8b949e]">
      No data available for the selected period
    </div>
    <div v-else class="h-64">
      <Line
        :data="chartData"
        :options="chartOptions"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { Line } from 'vue-chartjs'
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, Scale } from 'chart.js'
import { ArrowPathIcon } from '@heroicons/vue/24/outline'
import type { ChartOptions, Tick } from 'chart.js'

// Register Chart.js components
ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend)

// Props
const props = defineProps<{
  minutesData: Array<{ date: string; minutes: number }>
  loading: boolean
}>()

// Computed
const hasData = computed(() => {
  return props.minutesData && props.minutesData.length > 0 && props.minutesData.some(item => item.minutes > 0)
})

const chartData = computed(() => {
  // Format dates for display
  const labels = props.minutesData.map(item => {
    const date = new Date(item.date)
    return date.toLocaleDateString(undefined, { month: 'short', day: 'numeric' })
  })

  const minutesValues = props.minutesData.map(item => item.minutes)

  return {
    labels,
    datasets: [
      {
        label: 'Minutes',
        backgroundColor: 'rgba(20, 241, 149, 0.2)',
        borderColor: '#14F195',
        borderWidth: 2,
        data: minutesValues,
        tension: 0.4,
        fill: true
      }
    ]
  }
})

const chartOptions = computed<ChartOptions<'line'>>(() => {
  // Determine if we're in dark mode
  const isDarkMode = document.documentElement.classList.contains('dark')
  const textColor = isDarkMode ? '#c9d1d9' : '#374151'
  const gridColor = isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'

  return {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        type: 'linear',
        beginAtZero: true,
        ticks: {
          color: textColor,
          callback: function(this: Scale, tickValue: number | string) {
            const value = Number(tickValue);
            if (value % 1 === 0) return value;
            return value.toFixed(2);
          }
        },
        grid: {
          color: gridColor
        },
        title: {
          display: true,
          text: 'Minutes',
          color: textColor
        }
      },
      x: {
        ticks: {
          color: textColor
        },
        grid: {
          color: gridColor
        }
      }
    },
    plugins: {
      legend: {
        display: true,
        labels: {
          color: textColor
        }
      },
      tooltip: {
        callbacks: {
          label: (context) => {
            const value = context.raw as number;
            if (value < 1) {
              // Convert to seconds for small values
              return `${(value * 60).toFixed(1)} seconds`;
            }
            return `${value.toFixed(2)} minutes`;
          }
        }
      }
    }
  };
})

// Watch for dark mode changes
const updateChartOnThemeChange = () => {
  // Force chart update when theme changes
  ChartJS.defaults.color = document.documentElement.classList.contains('dark') ? '#c9d1d9' : '#374151'
}

// Set up a MutationObserver to watch for theme changes
if (typeof window !== 'undefined') {
  const observer = new MutationObserver(updateChartOnThemeChange)
  observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] })
}
</script>

<template>
  <div class="bg-white dark:bg-[#0d1117] p-4 rounded-lg shadow-sm border border-gray-200 dark:border-[#1c2129]">
    <p class="text-sm text-gray-500 dark:text-[#8b949e]">{{ title }}</p>
    <div v-if="loading" class="flex items-center mt-2 space-x-2">
      <ArrowPathIcon class="animate-spin w-4 h-4 text-gray-400 dark:text-[#8b949e]" />
      <p class="text-gray-400 dark:text-[#8b949e]">Loading...</p>
    </div>
    <div v-else class="mt-2">
      <p class="dark:text-white text-2xl font-semibold text-gray-800">
        <span v-if="type === 'minutes' && showHours" class="text-primary-600 dark:text-[#14F195]">{{ hoursValue }}</span>
        <span v-if="type === 'minutes' && showHours" class="text-sm ml-1 mr-2">hrs</span>
        <span v-if="type === 'tokens'" class="text-gray-800 dark:text-white">
          {{ formattedValueBase }}<span class="text-primary-500 dark:text-[#14F195]">{{ formattedValueSuffix }}</span>
        </span>
        <span v-else :class="{'text-primary-600 dark:text-[#14F195]': type === 'minutes'}">{{ formattedValue }}</span>
      </p>
      <p v-if="type === 'minutes' && showHours" class="text-xs text-gray-500 dark:text-[#8b949e] mt-1">
        {{ hoursValue }} hours {{ minutesRemainder }} minutes
      </p>
      <p v-else class="text-xs text-gray-500 dark:text-[#8b949e] mt-1">For selected time period</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ArrowPathIcon } from '@heroicons/vue/24/outline'

// Props
const props = defineProps<{
  title: string
  value: number
  loading: boolean
  type?: 'tokens' | 'minutes' | 'cost'
}>()

// Computed
const showHours = computed(() => {
  return props.type === 'minutes' && props.value >= 60
})

const hoursValue = computed(() => {
  if (props.type !== 'minutes') return 0
  return Math.floor(props.value / 60)
})

const minutesRemainder = computed(() => {
  if (props.type !== 'minutes') return 0
  return Math.round(props.value % 60)
})

// For tokens, split the value and suffix for styling
const formattedValueBase = computed(() => {
  if (props.type !== 'tokens') return ''

  if (props.value >= 1_000_000) {
    return `${(props.value / 1_000_000).toFixed(1)}`
  } else if (props.value >= 1_000) {
    return `${(props.value / 1_000).toFixed(1)}`
  }

  return props.value.toLocaleString()
})

const formattedValueSuffix = computed(() => {
  if (props.type !== 'tokens') return ''

  if (props.value >= 1_000_000) {
    return 'M'
  } else if (props.value >= 1_000) {
    return 'K'
  }

  return ''
})

const formattedValue = computed(() => {
  const type = props.type || 'tokens'

  if (type === 'tokens') {
    // Format large numbers with K or M suffix
    if (props.value >= 1_000_000) {
      return `${(props.value / 1_000_000).toFixed(1)}M`
    } else if (props.value >= 1_000) {
      return `${(props.value / 1_000).toFixed(1)}K`
    }
    return props.value.toLocaleString()
  } else if (type === 'minutes') {
    if (props.value < 1) {
      // Convert to seconds for small values
      return `${(props.value * 60).toFixed(1)} secs`
    }

    if (showHours.value) {
      // If showing hours, just show the minutes remainder
      return `${minutesRemainder.value} mins`
    }

    return `${props.value.toFixed(2)} mins`
  } else if (type === 'cost') {
    return `$${props.value.toFixed(2)}`
  }

  return props.value.toString()
})
</script>

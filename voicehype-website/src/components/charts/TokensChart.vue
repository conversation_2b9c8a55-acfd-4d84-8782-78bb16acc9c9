<template>
  <div class="chart-container">
    <h3 class="text-lg font-medium text-gray-800 dark:text-[#c9d1d9] mb-4">Token Usage</h3>
    <div v-if="loading" class="flex items-center justify-center h-64">
      <ArrowPathIcon class="animate-spin w-8 h-8 text-primary-600 dark:text-[#14F195]" />
    </div>
    <div v-else-if="!hasData" class="flex items-center justify-center h-64 text-gray-500 dark:text-[#8b949e]">
      No data available for the selected period
    </div>
    <div v-else class="h-64">
      <Bar
        :data="chartData"
        :options="chartOptions"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { Bar } from 'vue-chartjs'
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, Scale } from 'chart.js'
import { ArrowPathIcon } from '@heroicons/vue/24/outline'
import type { ChartOptions, Tick } from 'chart.js'

// Register Chart.js components
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend)

// Props
const props = defineProps<{
  tokensData: Array<{ date: string; input_tokens: number; output_tokens: number }>
  loading: boolean
}>()

// Computed
const hasData = computed(() => {
  return props.tokensData && props.tokensData.length > 0 &&
    props.tokensData.some(item => item.input_tokens > 0 || item.output_tokens > 0)
})

const chartData = computed(() => {
  // Format dates for display
  const labels = props.tokensData.map(item => {
    const date = new Date(item.date)
    return date.toLocaleDateString(undefined, { month: 'short', day: 'numeric' })
  })

  const inputTokens = props.tokensData.map(item => item.input_tokens)
  const outputTokens = props.tokensData.map(item => item.output_tokens)

  return {
    labels,
    datasets: [
      {
        label: 'Input Tokens',
        backgroundColor: 'rgba(59, 130, 246, 0.7)',
        data: inputTokens,
        borderWidth: 1,
        borderColor: 'rgba(59, 130, 246, 1)'
      },
      {
        label: 'Output Tokens',
        backgroundColor: 'rgba(16, 185, 129, 0.7)',
        data: outputTokens,
        borderWidth: 1,
        borderColor: 'rgba(16, 185, 129, 1)'
      }
    ]
  }
})

const chartOptions = computed<ChartOptions<'bar'>>(() => {
  // Determine if we're in dark mode
  const isDarkMode = document.documentElement.classList.contains('dark')
  const textColor = isDarkMode ? '#c9d1d9' : '#374151'
  const gridColor = isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'

  return {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        type: 'linear',
        beginAtZero: true,
        ticks: {
          color: textColor,
          callback: function(this: Scale, tickValue: number | string) {
            const value = Number(tickValue);
            // Format large numbers with K suffix
            if (value >= 1000) {
              return `${(value / 1000).toFixed(1)}K`;
            }
            return value;
          }
        },
        grid: {
          color: gridColor
        },
        title: {
          display: true,
          text: 'Tokens',
          color: textColor
        }
      },
      x: {
        ticks: {
          color: textColor
        },
        grid: {
          color: gridColor
        }
      }
    },
    plugins: {
      legend: {
        display: true,
        labels: {
          color: textColor
        }
      },
      tooltip: {
        callbacks: {
          label: (context) => {
            const value = context.raw as number;
            return `${context.dataset.label}: ${value.toLocaleString()}`;
          }
        }
      }
    }
  };
})

// Watch for dark mode changes
const updateChartOnThemeChange = () => {
  // Force chart update when theme changes
  ChartJS.defaults.color = document.documentElement.classList.contains('dark') ? '#c9d1d9' : '#374151'
}

// Set up a MutationObserver to watch for theme changes
if (typeof window !== 'undefined') {
  const observer = new MutationObserver(updateChartOnThemeChange)
  observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] })
}
</script>

<template>
  <div class="pricing-feature-comparison w-full">
    <div class="max-w-7xl sm:px-6 lg:px-8 px-4 mx-auto">
      <div class="mb-8 text-center">
        <h3 class="dark:text-white mb-4 text-2xl font-bold text-gray-900">Compare Plans</h3>
        <p class="dark:text-gray-400 text-gray-600">Choose the plan that best fits your usage needs</p>
      </div>

      <!-- Comparison Table Container -->
      <div class="bg-white dark:bg-[#0d1117] border border-gray-200 dark:border-[#1c2129] rounded-lg shadow-sm overflow-hidden">
        <div class="overflow-x-auto">
          <table class="w-full border-collapse">
            <thead class="bg-gray-50 dark:bg-[#161b22]">
              <tr class="border-b border-gray-200 dark:border-[#30363d]">
                <th class="dark:text-white px-6 py-4 font-semibold text-left text-gray-900">Features</th>
                <th class="dark:text-white px-6 py-4 font-semibold text-center text-gray-900">Pro</th>
                <th class="dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 px-6 py-4 font-semibold text-center text-blue-600">Basic</th>
                <th class="dark:text-white px-6 py-4 font-semibold text-center text-gray-900">Premium</th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 dark:divide-[#30363d]">
              <!-- Monthly Price -->
              <tr class="hover:bg-gray-50 dark:hover:bg-[#161b22] transition-colors">
                <td class="dark:text-white px-6 py-4 font-medium text-gray-900">Monthly Price</td>
                <td class="dark:text-gray-300 px-6 py-4 text-center text-gray-700">$9<span class="text-xs">.99</span></td>
                <td class="dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 px-6 py-4 font-semibold text-center text-blue-600">$4<span class="text-xs">.99</span></td>
                <td class="dark:text-gray-300 px-6 py-4 text-center text-gray-700">$14<span class="text-xs">.99</span></td>
              </tr>

              <!-- Transcription Minutes -->
              <tr class="hover:bg-gray-50 dark:hover:bg-[#161b22] transition-colors">
                <td class="dark:text-gray-300 px-6 py-4 text-gray-700">
                  <div class="flex items-center">
                    <ClockIcon class="dark:text-blue-400 w-5 h-5 mr-3 text-blue-500" />
                    Transcription Minutes
                  </div>
                </td>
                <td class="dark:text-gray-300 px-6 py-4 text-center text-gray-700">
                  840 min (14h) + <span class="font-semibold text-green-600">240 min FREE</span>
                  <div class="text-sm text-green-600">= 1080 min total</div>
                </td>
                <td class="dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 px-6 py-4 font-semibold text-center text-blue-600">
                  420 min (7h) + <span class="font-semibold text-green-600">120 min FREE</span>
                  <div class="text-sm text-green-600">= 540 min total</div>
                </td>
                <td class="dark:text-gray-300 px-6 py-4 text-center text-gray-700">
                  1260 min (21h) + <span class="font-semibold text-green-600">360 min FREE</span>
                  <div class="text-sm text-green-600">= 1620 min total</div>
                </td>
              </tr>

              <!-- AI Tokens -->
              <tr class="hover:bg-gray-50 dark:hover:bg-[#161b22] transition-colors">
                <td class="dark:text-gray-300 px-6 py-4 text-gray-700">
                  <div class="flex items-center">
                    <SparklesIcon class="dark:text-purple-400 w-5 h-5 mr-3 text-purple-500" />
                    AI Tokens
                  </div>
                </td>
                <td class="dark:text-gray-300 px-6 py-4 text-center text-gray-700">
                  440K + <span class="font-semibold text-green-600">100K FREE</span>
                  <div class="text-sm text-green-600">= 540K total</div>
                </td>
                <td class="dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 px-6 py-4 font-semibold text-center text-blue-600">
                  220K + <span class="font-semibold text-green-600">50K FREE</span>
                  <div class="text-sm text-green-600">= 270K total</div>
                </td>
                <td class="dark:text-gray-300 px-6 py-4 text-center text-gray-700">
                  660K + <span class="font-semibold text-green-600">150K FREE</span>
                  <div class="text-sm text-green-600">= 810K total</div>
                </td>
              </tr>

              <!-- LLM Access -->
              <tr class="hover:bg-gray-50 dark:hover:bg-[#161b22] transition-colors">
                <td class="dark:text-gray-300 px-6 py-4 text-gray-700">
                  <div class="flex items-center">
                    <CpuChipIcon class="dark:text-green-400 w-5 h-5 mr-3 text-green-500" />
                    Optimization with Claude, DeepSeek, & Llama
                  </div>
                </td>
                <td class="px-6 py-4 text-center">
                  <CheckIcon class="dark:text-green-400 w-5 h-5 mx-auto text-green-500" />
                </td>
                <td class="bg-blue-50 dark:bg-blue-900/20 px-6 py-4 text-center">
                  <CheckIcon class="dark:text-green-400 w-5 h-5 mx-auto text-green-500" />
                </td>
                <td class="px-6 py-4 text-center">
                  <CheckIcon class="dark:text-green-400 w-5 h-5 mx-auto text-green-500" />
                </td>
              </tr>

              <!-- Real-time Transcription -->
              <tr class="hover:bg-gray-50 dark:hover:bg-[#161b22] transition-colors">
                <td class="dark:text-gray-300 px-6 py-4 text-gray-700">
                  <div class="flex items-center">
                    <MicrophoneIcon class="dark:text-red-400 w-5 h-5 mr-3 text-red-500" />
                    Real-time Transcription (English)
                  </div>
                </td>
                <td class="px-6 py-4 text-center">
                  <CheckIcon class="dark:text-green-400 w-5 h-5 mx-auto text-green-500" />
                </td>
                <td class="bg-blue-50 dark:bg-blue-900/20 px-6 py-4 text-center">
                  <CheckIcon class="dark:text-green-400 w-5 h-5 mx-auto text-green-500" />
                </td>
                <td class="px-6 py-4 text-center">
                  <CheckIcon class="dark:text-green-400 w-5 h-5 mx-auto text-green-500" />
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { CheckIcon, ClockIcon, SparklesIcon, CpuChipIcon, MicrophoneIcon } from '@heroicons/vue/24/solid'
</script>

<style scoped>
/* Ensure table takes full width and spacing is consistent */
table {
  border-spacing: 0;
}

/* Smooth hover transitions */
tr:hover {
  transition: background-color 0.2s ease;
}

/* Ensure checkmark icons are properly centered */
.mx-auto {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .overflow-x-auto {
    -webkit-overflow-scrolling: touch;
  }
  
  table {
    min-width: 600px;
  }
  
  .px-6 {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .py-4 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }
}
</style>

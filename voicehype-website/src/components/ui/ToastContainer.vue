<template>
  <div class="toast-container fixed top-4 right-4 z-[9999] pointer-events-none space-y-3 max-w-md">
    <Toast
      v-for="toast in toasts"
      :key="toast.id"
      :show="true"
      :title="toast.title"
      :message="toast.message"
      :type="toast.type"
      :position="toast.position"
      :duration="toast.duration"
      :autoClose="toast.autoClose"
      @close="hideToast(toast.id!)"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import Toast from './Toast.vue';
import toastService from '@/services/toast';

const toasts = computed(() => toastService.toasts);

function hideToast(id: string) {
  toastService.hide(id);
}
</script>

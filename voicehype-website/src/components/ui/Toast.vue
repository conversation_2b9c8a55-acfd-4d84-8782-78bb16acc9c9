<template>
  <Transition
    enter-active-class="transition duration-300 ease-out"
    enter-from-class="sm:translate-y-0 sm:translate-x-2 transform translate-y-2 opacity-0"
    enter-to-class="sm:translate-x-0 transform translate-y-0 opacity-100"
    leave-active-class="transition duration-200 ease-in"
    leave-from-class="opacity-100"
    leave-to-class="opacity-0"
  >
    <div
      v-if="show"
      class="pointer-events-none"
    >
      <div
        class="max-w-md w-full min-w-[320px] bg-white dark:bg-[#0d1117] shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden"
      >
          <div class="p-5">
            <div class="flex items-start space-x-3">
              <div class="flex-shrink-0">
                <CheckCircleIcon
                  v-if="type === 'success'"
                  class="w-6 h-6 text-green-400"
                  aria-hidden="true"
                />
                <InformationCircleIcon
                  v-else-if="type === 'info'"
                  class="w-6 h-6 text-blue-400"
                  aria-hidden="true"
                />
                <ExclamationCircleIcon
                  v-else-if="type === 'error'"
                  class="w-6 h-6 text-red-400"
                  aria-hidden="true"
                />
                <ExclamationTriangleIcon
                  v-else-if="type === 'warning'"
                  class="w-6 h-6 text-yellow-400"
                  aria-hidden="true"
                />
              </div>
              <div class="ml-3 flex-1 pt-0.5 min-w-0 pr-2">
                <p class="dark:text-white text-sm font-medium text-gray-900 break-words">
                  {{ title }}
                </p>
                <p v-if="message" class="dark:text-gray-400 mt-1 text-sm text-gray-500 break-words">
                  {{ message }}
                </p>
              </div>
              <div class="flex-shrink-0">
                <button
                  type="button"
                  class="inline-flex rounded-md bg-white dark:bg-[#0d1117] text-gray-400 dark:text-gray-500 hover:text-gray-500 dark:hover:text-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                  @click="close"
                >
                  <span class="sr-only">Close</span>
                  <XMarkIcon class="w-5 h-5" aria-hidden="true" />
                </button>
              </div>
            </div>
          </div>
        </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref, onMounted, watch } from 'vue';
import {
  CheckCircleIcon,
  ExclamationCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  XMarkIcon
} from '@heroicons/vue/24/outline';

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    required: true,
  },
  message: {
    type: String,
    default: '',
  },
  type: {
    type: String,
    default: 'info',
    validator: (value: string) => ['success', 'info', 'warning', 'error'].includes(value),
  },
  position: {
    type: String,
    default: 'sm:items-end justify-end',
    validator: (value: string) => [
      'sm:items-end justify-end',      // bottom right
      'sm:items-end justify-start',    // bottom left
      'sm:items-start justify-end',    // top right
      'sm:items-start justify-start',  // top left
      'items-center justify-center'    // center
    ].includes(value),
  },
  duration: {
    type: Number,
    default: 5000, // 5 seconds
  },
  autoClose: {
    type: Boolean,
    default: true,
  }
});

const emit = defineEmits(['close']);

// Set up auto-close timer
let timer: number | undefined;

onMounted(() => {
  if (props.autoClose && props.show && props.duration > 0) {
    startTimer();
  }
});

watch(() => props.show, (newValue) => {
  if (newValue && props.autoClose && props.duration > 0) {
    startTimer();
  } else {
    clearTimer();
  }
});

function startTimer() {
  clearTimer();
  timer = window.setTimeout(() => {
    close();
  }, props.duration);
}

function clearTimer() {
  if (timer) {
    clearTimeout(timer);
    timer = undefined;
  }
}

function close() {
  clearTimer();
  emit('close');
}
</script>

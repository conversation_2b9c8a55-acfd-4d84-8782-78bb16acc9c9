<template>
  <div class="perlin-avatar" :style="{ width: size + 'px', height: size + 'px', borderWidth: borderWidth + 'px' }">
    <canvas ref="canvas" :width="size" :height="size"></canvas>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';

const props = defineProps({
  size: {
    type: Number,
    default: 40
  },
  seed: {
    type: Number,
    default: () => Math.floor(Date.now() / 1000) // Default seed based on current time (seconds)
  },
  borderWidth: {
    type: Number,
    default: 2
  }
});

const canvas = ref<HTMLCanvasElement | null>(null);

// Perlin noise implementation
function createNoise2D(random = Math.random) {
  const PERMUTATION = new Uint8Array(512);
  const p = new Uint8Array(256);
  
  for (let i = 0; i < 256; i++) p[i] = i;
  
  let n: number;
  let q: number;
  for (let i = 255; i > 0; i--) {
    n = Math.floor((i + 1) * random());
    q = p[i];
    p[i] = p[n];
    p[n] = q;
  }
  
  for (let i = 0; i < 512; i++) {
    PERMUTATION[i] = p[i & 255];
  }
  
  const fade = (t: number) => t * t * t * (t * (t * 6 - 15) + 10);
  
  const lerp = (t: number, a: number, b: number) => a + t * (b - a);
  
  const grad = (hash: number, x: number, y: number) => {
    const h = hash & 15;
    const u = h < 8 ? x : y;
    const v = h < 4 ? y : h === 12 || h === 14 ? x : 0;
    return ((h & 1) === 0 ? u : -u) + ((h & 2) === 0 ? v : -v);
  };
  
  return function noise(x: number, y: number) {
    const X = Math.floor(x) & 255;
    const Y = Math.floor(y) & 255;
    
    x -= Math.floor(x);
    y -= Math.floor(y);
    
    const u = fade(x);
    const v = fade(y);
    
    const A = PERMUTATION[X] + Y;
    const AA = PERMUTATION[A];
    const AB = PERMUTATION[A + 1];
    const B = PERMUTATION[X + 1] + Y;
    const BA = PERMUTATION[B];
    const BB = PERMUTATION[B + 1];
    
    return lerp(
      v,
      lerp(u, grad(PERMUTATION[AA], x, y), grad(PERMUTATION[BA], x - 1, y)),
      lerp(u, grad(PERMUTATION[AB], x, y - 1), grad(PERMUTATION[BB], x - 1, y - 1))
    );
  };
}

// Function to generate random color based on seed
function getRandomColor(seed: number) {
  const random = seedRandom(seed);
  return {
    r: Math.floor(random() * 255),
    g: Math.floor(random() * 255),
    b: Math.floor(random() * 255)
  };
}

// Simple seeded random function
function seedRandom(seed: number) {
  return function() {
    seed = (seed * 9301 + 49297) % 233280;
    return seed / 233280;
  };
}

// Draw the Perlin noise pattern
function drawPerlinNoise() {
  if (!canvas.value) return;
  
  const ctx = canvas.value.getContext('2d');
  if (!ctx) return;
  
  const size = props.size;
  const imageData = ctx.createImageData(size, size);
  const data = imageData.data;
  
  // Create a seeded random function
  const random = seedRandom(props.seed);
  
  // Create noise function with seeded random
  const noise2D = createNoise2D(random);
  
  // Generate base colors for the gradient
  const color1 = getRandomColor(props.seed);
  const color2 = getRandomColor(props.seed + 1000);
  const color3 = getRandomColor(props.seed + 2000);
  
  // Scale factor for noise
  const scale = 0.03;
  
  // Draw the noise pattern
  for (let y = 0; y < size; y++) {
    for (let x = 0; x < size; x++) {
      // Calculate distance from center for circular mask
      const centerX = size / 2;
      const centerY = size / 2;
      const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);
      
      // Skip pixels outside the circle
      if (distance > size / 2) {
        continue;
      }
      
      // Get noise value
      const noiseValue = (noise2D(x * scale, y * scale) + 1) / 2; // Normalize to 0-1
      
      // Interpolate between colors based on noise value
      let r, g, b;
      if (noiseValue < 0.33) {
        const t = noiseValue / 0.33;
        r = Math.floor(color1.r * (1 - t) + color2.r * t);
        g = Math.floor(color1.g * (1 - t) + color2.g * t);
        b = Math.floor(color1.b * (1 - t) + color2.b * t);
      } else if (noiseValue < 0.66) {
        const t = (noiseValue - 0.33) / 0.33;
        r = Math.floor(color2.r * (1 - t) + color3.r * t);
        g = Math.floor(color2.g * (1 - t) + color3.g * t);
        b = Math.floor(color2.b * (1 - t) + color3.b * t);
      } else {
        const t = (noiseValue - 0.66) / 0.34;
        r = Math.floor(color3.r * (1 - t) + color1.r * t);
        g = Math.floor(color3.g * (1 - t) + color1.g * t);
        b = Math.floor(color3.b * (1 - t) + color1.b * t);
      }
      
      // Set pixel color
      const idx = (y * size + x) * 4;
      data[idx] = r;
      data[idx + 1] = g;
      data[idx + 2] = b;
      data[idx + 3] = 255; // Full opacity
    }
  }
  
  ctx.putImageData(imageData, 0, 0);
  
  // Draw circle clipping
  ctx.globalCompositeOperation = 'destination-in';
  ctx.beginPath();
  ctx.arc(size / 2, size / 2, size / 2, 0, Math.PI * 2);
  ctx.fill();
}

// Draw on mount and when props change
onMounted(() => {
  drawPerlinNoise();
});

watch(() => props.seed, () => {
  drawPerlinNoise();
});

watch(() => props.size, () => {
  drawPerlinNoise();
});
</script>

<style scoped>
.perlin-avatar {
  border-radius: 50%;
  overflow: hidden;
  border-style: solid;
  border-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
}

:root.dark .perlin-avatar {
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.5);
}

canvas {
  display: block;
  width: 100%;
  height: 100%;
}
</style> 
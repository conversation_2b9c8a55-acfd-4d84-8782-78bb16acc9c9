<template>
  <div class="subscription-pricing-cards">
    <!-- Pricing Header -->
    <div class="mb-12 text-center">
      <h2 class="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text mb-4 text-3xl font-bold text-transparent">
        Choose Your Plan
      </h2>
      <p class="dark:text-gray-300 max-w-2xl mx-auto text-gray-600">
        Get predictable pricing with included usage quotas. Upgrade anytime as your needs grow. You can also insert prepaid <button @click="scrollToCredits" class="dark:text-blue-400 hover:underline text-blue-600 cursor-pointer">credits</button> and use them*
      </p>
    </div>

    <!-- Pricing Cards Grid - Basic package in the center as largest -->
    <div class="md:grid-cols-3 grid max-w-6xl gap-8 mx-auto">
      <!-- Pro Plan - MOVED TO LEFT (smaller) -->
      <div class="pricing-card group rounded-2xl border-white/10 hover:border-purple-400/50 backdrop-blur-sm relative p-6 overflow-hidden transition-all duration-300 bg-transparent border">
        <!-- LIMITED TIME GIFT Badge -->
        <div class="top-4 right-4 absolute z-20">
          <div class="bg-gradient-to-r from-red-500 to-orange-500 flex items-center gap-1 px-3 py-1 text-xs font-bold text-white rounded-full shadow-lg">
            <GiftIcon class="w-3 h-3" />
            LIMITED TIME GIFT
          </div>
        </div>
        
        <!-- Glow effect only -->
        <div class="bg-gradient-to-br from-purple-500/10 to-pink-500/10 group-hover:opacity-100 absolute inset-0 transition-opacity duration-300 opacity-0"></div>
        
        <div class="relative z-10 flex flex-col h-full">
          <div class="mb-6 text-center">
            <div class="w-14 h-14 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl inline-flex items-center justify-center mb-4 shadow-lg">
              <SparklesIcon class="w-7 h-7 text-white" />
            </div>
            <h3 class="dark:text-white mb-2 text-xl font-bold text-gray-900">Pro</h3>
            <div class="price-display bg-white/5 border-white/10 p-3 mb-4 border rounded-lg">
              <span class="dark:text-purple-400 text-4xl font-bold text-purple-600">$9<span class="text-xs">.99</span></span>
              <span class="dark:text-gray-300 text-lg text-gray-600">/month</span>
            </div>
            <p class="dark:text-gray-300 text-base text-gray-600">Ideal for small teams</p>
          </div>

          <!-- Features List -->
          <ul class="flex-grow mb-6 space-y-3">
            <li class="flex items-center">
              <div class="bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center flex-shrink-0 w-5 h-5 mr-3 rounded-full">
                <CheckIcon class="w-3 h-3 text-white" />
              </div>
              <span class="dark:text-white text-base text-gray-700">840 minutes (14 hours) transcription</span>
            </li>
            <li class="flex items-center">
              <div class="bg-gradient-to-br from-green-500 to-emerald-500 flex items-center justify-center flex-shrink-0 w-5 h-5 mr-3 rounded-full">
                <GiftIcon class="w-3 h-3 text-white" />
              </div>
              <span class="dark:text-white dark:text-green-400 text-base font-semibold text-gray-700 text-green-600">+240 bonus minutes (4 hours) FREE!</span>
            </li>
            <li class="flex items-center">
              <div class="bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center flex-shrink-0 w-5 h-5 mr-3 rounded-full">
                <CheckIcon class="w-3 h-3 text-white" />
              </div>
              <span class="dark:text-white text-base text-gray-700">440K AI tokens</span>
            </li>
            <li class="flex items-center">
              <div class="bg-gradient-to-br from-green-500 to-emerald-500 flex items-center justify-center flex-shrink-0 w-5 h-5 mr-3 rounded-full">
                <GiftIcon class="w-3 h-3 text-white" />
              </div>
              <span class="dark:text-white dark:text-green-400 text-base font-semibold text-gray-700 text-green-600">+100K bonus AI tokens FREE!</span>
            </li>
            <li class="flex items-center">
              <div class="bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center flex-shrink-0 w-5 h-5 mr-3 rounded-full">
                <CheckIcon class="w-3 h-3 text-white" />
              </div>
              <span class="dark:text-white text-base text-gray-700">Real-time transcription (English)</span>
            </li>
            <li class="flex items-center">
              <div class="bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center flex-shrink-0 w-5 h-5 mr-3 rounded-full">
                <CheckIcon class="w-3 h-3 text-white" />
              </div>
              <span class="dark:text-white text-base text-gray-700">Claude, DeepSeek & Llama</span>
            </li>
          </ul>          <!-- CTA Button at bottom -->
          <div>
            <button
              @click="handleSubscribe('Pro')"
              :disabled="loading"
              :class="getButtonConfig('Pro').class"
            >
              {{ loading ? 'Loading...' : getButtonConfig('Pro').text }}
            </button>
          </div>
        </div>
      </div>

      <!-- Basic Plan - MOVED TO MIDDLE (largest) -->
      <div class="pricing-card group rounded-2xl border-white/10 hover:border-blue-400/50 backdrop-blur-sm relative p-8 overflow-hidden transition-all duration-300 scale-110 bg-transparent border">
        <!-- LIMITED TIME GIFT Badge -->
        <div class="top-4 right-4 absolute z-20">
          <div class="bg-gradient-to-r from-red-500 to-orange-500 flex items-center gap-1 px-3 py-1 text-xs font-bold text-white rounded-full shadow-lg">
            <GiftIcon class="w-3 h-3" />
            LIMITED TIME GIFT
          </div>
        </div>
        
        <!-- Glow effect only -->
        <div class="bg-gradient-to-br from-blue-500/10 to-purple-500/10 group-hover:opacity-100 absolute inset-0 transition-opacity duration-300 opacity-0"></div>
        
        <div class="relative z-10">
          <div class="mb-8 text-center">
            <div class="rounded-2xl inline-flex items-center justify-center w-16 h-16 mb-4 bg-black shadow-lg">
              <CpuChipIcon class="w-8 h-8 text-white" />
            </div>
            <h3 class="dark:text-white mb-2 text-xl font-bold text-gray-900">Basic</h3>
            <div class="price-display bg-white/5 border-white/10 p-4 mb-4 border rounded-lg">
              <span class="dark:text-blue-400 text-4xl font-bold text-blue-600">$4<span class="text-xs">.99</span></span>
              <span class="dark:text-gray-300 text-gray-600">/month</span>
            </div>
            <p class="dark:text-gray-300 text-sm text-gray-600">Perfect for individual developers</p>
          </div>

          <!-- Features List -->
          <ul class="mb-8 space-y-4">
            <li class="flex items-center">
              <div class="flex items-center justify-center flex-shrink-0 w-6 h-6 mr-3 bg-black rounded-full">
                <CheckIcon class="w-4 h-4 text-white" />
              </div>
              <span class="dark:text-white font-medium text-gray-700">420 minutes (7 hours) transcription</span>
            </li>
            <li class="flex items-center">
              <div class="bg-gradient-to-br from-green-500 to-emerald-500 flex items-center justify-center flex-shrink-0 w-6 h-6 mr-3 rounded-full">
                <GiftIcon class="w-4 h-4 text-white" />
              </div>
              <span class="dark:text-white text-emerald-600 dark:text-emerald-400 font-medium font-semibold text-gray-700">+120 bonus minutes (2 hours) FREE!</span>
            </li>
            <li class="flex items-center">
              <div class="flex items-center justify-center flex-shrink-0 w-6 h-6 mr-3 bg-black rounded-full">
                <CheckIcon class="w-4 h-4 text-white" />
              </div>
              <span class="dark:text-white font-medium text-gray-700">220K AI tokens</span>
            </li>
            <li class="flex items-center">
              <div class="bg-gradient-to-br from-green-500 to-emerald-500 flex items-center justify-center flex-shrink-0 w-6 h-6 mr-3 rounded-full">
                <GiftIcon class="w-4 h-4 text-white" />
              </div>
              <span class="dark:text-white text-emerald-600 dark:text-emerald-400 font-medium font-semibold text-gray-700">+50K bonus AI tokens FREE!</span>
            </li>
            <li class="flex items-center">
              <div class="flex items-center justify-center flex-shrink-0 w-6 h-6 mr-3 bg-black rounded-full">
                <CheckIcon class="w-4 h-4 text-white" />
              </div>
              <span class="dark:text-white font-medium text-gray-700">Real-time transcription (English)</span>
            </li>
            <li class="flex items-center">
              <div class="flex items-center justify-center flex-shrink-0 w-6 h-6 mr-3 bg-black rounded-full">
                <CheckIcon class="w-4 h-4 text-white" />
              </div>
              <span class="dark:text-white font-medium text-gray-700">Claude, DeepSeek & Llama</span>
            </li>
          </ul>          <!-- CTA Button at bottom -->
          <div class="mt-auto">
            <button
              @click="handleSubscribe('Basic')"
              :disabled="loading"
              :class="getButtonConfig('Basic').class"
            >
              {{ loading ? 'Loading...' : getButtonConfig('Basic').text }}
            </button>
          </div>
        </div>
      </div>

      <!-- Premium Plan - MOVED TO RIGHT (smaller) -->
      <div class="pricing-card group rounded-2xl border-white/10 hover:border-amber-400/50 backdrop-blur-sm relative p-6 overflow-hidden transition-all duration-300 bg-transparent border">
        <!-- LIMITED TIME GIFT Badge -->
        <div class="top-4 right-4 absolute z-20">
          <div class="bg-gradient-to-r from-red-500 to-orange-500 flex items-center gap-1 px-3 py-1 text-xs font-bold text-white rounded-full shadow-lg">
            <GiftIcon class="w-3 h-3" />
            LIMITED TIME GIFT
          </div>
        </div>
        
        <!-- Glow effect only -->
        <div class="bg-gradient-to-br from-amber-500/10 to-orange-500/10 group-hover:opacity-100 absolute inset-0 transition-opacity duration-300 opacity-0"></div>
        
        <div class="relative z-10 flex flex-col h-full">
          <div class="mb-6 text-center">
            <div class="w-14 h-14 bg-gradient-to-br from-amber-500 to-orange-600 rounded-2xl inline-flex items-center justify-center mb-4 shadow-lg">
              <MicrophoneIcon class="w-7 h-7 text-white" />
            </div>
            <h3 class="dark:text-white mb-2 text-xl font-bold text-gray-900">Premium</h3>
            <div class="price-display bg-white/5 border-white/10 p-3 mb-4 border rounded-lg">
              <span class="dark:text-orange-400 text-4xl font-bold text-orange-600">$14<span class="text-xs">.99</span></span>
              <span class="dark:text-gray-300 text-lg text-gray-600">/month</span>
            </div>
            <p class="dark:text-gray-300 text-base text-gray-600">Enterprise-ready usage</p>
          </div>

          <!-- Features List -->
          <ul class="flex-grow mb-6 space-y-3">
            <li class="flex items-center">
              <div class="bg-gradient-to-br from-amber-500 to-orange-500 flex items-center justify-center flex-shrink-0 w-5 h-5 mr-3 rounded-full">
                <CheckIcon class="w-3 h-3 text-white" />
              </div>
              <span class="dark:text-white text-base text-gray-700">1260 minutes (21 hours) transcription</span>
            </li>
            <li class="flex items-center">
              <div class="bg-gradient-to-br from-green-500 to-emerald-500 flex items-center justify-center flex-shrink-0 w-5 h-5 mr-3 rounded-full">
                <GiftIcon class="w-3 h-3 text-white" />
              </div>
              <span class="dark:text-white dark:text-red-400 text-base font-semibold text-gray-700 text-red-600">+360 bonus minutes (6 hours) FREE!</span>
            </li>
            <li class="flex items-center">
              <div class="bg-gradient-to-br from-amber-500 to-orange-500 flex items-center justify-center flex-shrink-0 w-5 h-5 mr-3 rounded-full">
                <CheckIcon class="w-3 h-3 text-white" />
              </div>
              <span class="dark:text-white text-base text-gray-700">660K AI tokens</span>
            </li>
            <li class="flex items-center">
              <div class="bg-gradient-to-br from-green-500 to-emerald-500 flex items-center justify-center flex-shrink-0 w-5 h-5 mr-3 rounded-full">
                <GiftIcon class="w-3 h-3 text-white" />
              </div>
              <span class="dark:text-white dark:text-red-400 text-base font-semibold text-gray-700 text-red-600">+150K bonus AI tokens FREE!</span>
            </li>
            <li class="flex items-center">
              <div class="bg-gradient-to-br from-amber-500 to-orange-500 flex items-center justify-center flex-shrink-0 w-5 h-5 mr-3 rounded-full">
                <CheckIcon class="w-3 h-3 text-white" />
              </div>
              <span class="dark:text-white text-base text-gray-700">Real-time transcription (English)</span>
            </li>
            <li class="flex items-center">
              <div class="bg-gradient-to-br from-amber-500 to-orange-500 flex items-center justify-center flex-shrink-0 w-5 h-5 mr-3 rounded-full">
                <CheckIcon class="w-3 h-3 text-white" />
              </div>
              <span class="dark:text-white text-base text-gray-700">Claude, DeepSeek & Llama</span>
            </li>
          </ul>          <!-- CTA Button at bottom -->
          <div>
            <button
              @click="handleSubscribe('Premium')"
              :disabled="loading"
              :class="getButtonConfig('Premium').class"
            >
              {{ loading ? 'Loading...' : getButtonConfig('Premium').text }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Credit Usage Explanation -->
    <div class="max-w-4xl mx-auto mt-12 mb-8 text-center">
      <p class="dark:text-gray-400 text-sm text-gray-500">
        * VoiceHype automatically will use your credits once your quota is exhausted, so you can comfortably keep using it if your quota runs out before the month.
      </p>
    </div>    <!-- Additional Info -->
    <div class="dark:text-white mt-12 text-center text-gray-600">
      <p class="mb-4 text-lg font-medium">All plans include:</p>
      <div class="flex flex-wrap justify-center gap-6 text-sm">
        <div class="flex items-center">
          <div class="bg-gradient-to-r from-green-400 to-blue-500 w-2 h-2 mr-2 rounded-full"></div>
          <span>Real-time transcription (English)</span>
        </div>
        <div class="flex items-center">
          <div class="bg-gradient-to-r from-purple-400 to-pink-500 w-2 h-2 mr-2 rounded-full"></div>
          <span>Multiple transcription models (Whisper and Assembly AI)</span>
        </div>
        <div class="flex items-center">
          <div class="bg-gradient-to-r from-blue-400 to-indigo-500 w-2 h-2 mr-2 rounded-full"></div>
          <span>Secure processing</span>
        </div>
        <div class="flex items-center">
          <div class="bg-gradient-to-r from-amber-400 to-orange-500 w-2 h-2 mr-2 rounded-full"></div>
          <span>AI optimization with Claude, DeepSeek & Llama</span>
        </div>      </div>
    </div>
  </div>  <!-- Upgrade Modal -->
  <UpgradeModal 
    :is-open="showUpgradeModal"
    :target-plan="selectedTargetPlan || undefined"
    @close="showUpgradeModal = false"
    @success="handleUpgradeSuccess"
    @error="handleUpgradeError"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useSubscriptionStore } from '@/stores/subscription'
import { useRouter } from 'vue-router'
import UpgradeModal from '@/components/subscription/UpgradeModal.vue'
import { useToast } from '@/services/toast'
import { CheckIcon, SparklesIcon, MicrophoneIcon, CpuChipIcon, GiftIcon } from '@heroicons/vue/24/solid'

const subscriptionStore = useSubscriptionStore()
const router = useRouter()
const { success: showSuccessToast, error: showErrorToast } = useToast()
const loading = ref(false)

// Upgrade modal state
const showUpgradeModal = ref(false)
const selectedTargetPlan = ref<'Basic' | 'Pro' | 'Premium' | null>(null)

// Helper function to get button type for each plan
const getButtonConfig = (planName: 'Basic' | 'Pro' | 'Premium') => {
  const currentPlan = subscriptionStore.currentPlan
  
  // If no subscription at all, show "Get Started"
  if (!currentPlan) {
    return {
      text: 'Get Started',
      action: 'subscribe',
      class: getDefaultButtonClass(planName)
    }
  }

  // If user is on a free subscription (not a Paddle customer), show "Buy Now" buttons
  if (subscriptionStore.isFreeSubscription) {
    return {
      text: 'Buy Now',
      action: 'subscribe',
      class: getDefaultButtonClass(planName)
    }
  }

  // If user is on a paid subscription (Paddle customer), show upgrade/cancel/downgrade buttons
  if (subscriptionStore.isPaidSubscription) {
    const currentPrice = currentPlan.monthly_price
    const planPrices = {
      'Basic': 4.99,
      'Pro': 9.99,
      'Premium': 14.99
    }
    
    const targetPrice = planPrices[planName]
    
    if (currentPlan.name === planName) {
      // Current plan - show "Cancel"
      return {
        text: 'Cancel Plan',
        action: 'cancel',
        class: getCancelButtonClass()
      }
    } else if (targetPrice > currentPrice) {
      // Higher cost plan - show "Upgrade"
      return {
        text: 'Upgrade',
        action: 'upgrade',
        class: getUpgradeButtonClass(planName)
      }
    } else {
      // Lower cost plan - show "Downgrade"
      return {
        text: 'Downgrade',
        action: 'downgrade',
        class: getDowngradeButtonClass()
      }
    }
  }

  // Fallback - show "Get Started"
  return {
    text: 'Get Started',
    action: 'subscribe',
    class: getDefaultButtonClass(planName)
  }
}

// Button styling functions
const getDefaultButtonClass = (planName: string) => {
  const baseClass = 'w-full font-semibold py-4 px-6 rounded-xl transition-all duration-300 disabled:cursor-not-allowed disabled:opacity-50'
  
  switch (planName) {
    case 'Pro':
      return `${baseClass} bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white shadow-lg hover:shadow-xl`
    case 'Basic':
      return `${baseClass} bg-black hover:bg-gray-800 text-white shadow-lg hover:shadow-xl`
    case 'Premium':
      return `${baseClass} bg-gradient-to-r from-amber-500 to-orange-600 hover:from-amber-600 hover:to-orange-700 text-white shadow-lg hover:shadow-xl`
    default:
      return `${baseClass} bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white`
  }
}

const getCancelButtonClass = () => {
  return 'w-full font-semibold py-4 px-6 rounded-xl transition-all duration-300 bg-red-600 hover:bg-red-700 text-white border border-red-600 hover:border-red-700 shadow-lg hover:shadow-xl'
}

const getUpgradeButtonClass = (planName: string) => {
  const baseClass = 'w-full font-semibold py-4 px-6 rounded-xl transition-all duration-300 text-white shadow-lg hover:shadow-xl'
  
  switch (planName) {
    case 'Pro':
      return `${baseClass} bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600`
    case 'Basic':
      return `${baseClass} bg-black hover:bg-gray-800`
    case 'Premium':
      return `${baseClass} bg-gradient-to-r from-amber-500 to-orange-600 hover:from-amber-600 hover:to-orange-700`
    default:
      return `${baseClass} bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800`
  }
}

const getDowngradeButtonClass = () => {
  return 'w-full font-semibold py-4 px-6 rounded-xl transition-all duration-300 bg-gray-600 hover:bg-gray-700 text-white border border-gray-600 hover:border-gray-700 shadow-lg hover:shadow-xl'
}

// Handle different button actions
async function handleButtonAction(planName: 'Basic' | 'Pro' | 'Premium') {
  const config = getButtonConfig(planName)
  
  loading.value = true
  
  try {
    switch (config.action) {
      case 'subscribe':
        // New subscription - use direct checkout
        const success = await subscriptionStore.createSubscriptionCheckout(planName)
        if (!success && subscriptionStore.error) {
          console.error('Subscription checkout error:', subscriptionStore.error)
          alert(`Error: ${subscriptionStore.error}`)
        }
        break
        
      case 'upgrade':
      case 'downgrade':
        // Show upgrade modal for existing subscribers
        selectedTargetPlan.value = planName
        showUpgradeModal.value = true
        break
        
      case 'cancel':
        // Navigate to payments page where cancellation UI is available
        router.push('/app/payments')
        break
        
      default:
        console.warn('Unknown action:', config.action)
    }
  } catch (error) {
    console.error('Subscription action error:', error)
    alert('An error occurred. Please try again.')
  } finally {
    loading.value = false
  }
}

// Handle upgrade modal success
async function handleUpgradeSuccess() {
  showUpgradeModal.value = false
  selectedTargetPlan.value = null
  
  // Show success message
  showSuccessToast('Plan Updated Successfully! 🎉', 'Your subscription has been updated. Refreshing to show the latest changes...')
  
  // Give the toast a moment to show, then reload the page to ensure fresh data
  setTimeout(() => {
    // Reload the page to get the freshest subscription data and ensure UI consistency
    console.log('Reloading page to refresh subscription data after successful upgrade')
    window.location.reload()
  }, 2000) // Increased to 2 seconds to give users time to read the message
}

function handleUpgradeError(error: string) {
  console.error('Upgrade error received in SubscriptionPricingCards:', error)
  showErrorToast('Upgrade Failed', error || 'Failed to upgrade your subscription. Please try again or contact support.')
}

async function handleSubscribe(planName: 'Basic' | 'Pro' | 'Premium') {
  await handleButtonAction(planName)
}

function scrollToCredits() {
  // Scroll to the credits section on the payments page
  const creditsSection = document.querySelector('.credit-balance-section, [data-section="credits"]')
  if (creditsSection) {
    creditsSection.scrollIntoView({ behavior: 'smooth', block: 'start' })
  } else {
    // If we're not on the payments page or credits section doesn't exist,
    // scroll down to look for credit-related content
    window.scrollBy({ top: 800, behavior: 'smooth' })
  }
}
</script>

<style scoped>
.pricing-card {
  position: relative;
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.pricing-card::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 1rem;
  padding: 1px;
  background: linear-gradient(145deg, rgba(20, 241, 149, 0.1), rgba(10, 214, 223, 0.1));
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  -webkit-mask-composite: xor;
  pointer-events: none;
}

.price-display {
  line-height: 1;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Smooth animations - removed bounce/float effects */
.pricing-card {
  position: relative;
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  display: flex;
  flex-direction: column;
  min-height: 500px;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.pricing-card::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 1rem;
  padding: 1px;
  background: linear-gradient(145deg, rgba(20, 241, 149, 0.1), rgba(10, 214, 223, 0.1));
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  -webkit-mask-composite: xor;
  pointer-events: none;
}

.price-display {
  line-height: 1;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Gradient text support */
.bg-clip-text {
  -webkit-background-clip: text;
  background-clip: text;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .pricing-card {
    margin: 0 1rem;
    scale: 1 !important; /* Reset scale on mobile */
  }
  
  .text-4xl {
    font-size: 2.5rem;
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .pricing-card::before {
    background: linear-gradient(145deg, rgba(20, 241, 149, 0.05), rgba(10, 214, 223, 0.05));
  }
}

/* Enhanced hover effects */
.pricing-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(20, 241, 149, 0.3);
}

/* Card glow effect */
.pricing-card .card-glow {
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at center, rgba(20, 241, 149, 0.1), transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.pricing-card:hover .card-glow {
  opacity: 1;
}

/* Focus states for accessibility */
button:focus-visible {
  outline: 2px solid rgba(20, 241, 149, 0.5);
  outline-offset: 2px;
}

/* Card sizing - Basic (center) is largest */
.pricing-card:nth-child(2) {
  transform: scale(1.05);
  z-index: 10;
  min-height: 550px;
  border-color: rgba(20, 241, 149, 0.4);
  background: rgba(20, 241, 149, 0.03);
}

/* Pro and Premium cards are equal smaller size */
.pricing-card:nth-child(1),
.pricing-card:nth-child(3) {
  min-height: 480px;
}

/* Feature check styling */
.feature-check {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-right: 0.75rem;
  flex-shrink: 0;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-check::after {
  content: "✓";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}
</style>

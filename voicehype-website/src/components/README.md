# VoiceHype Website Components

This directory contains reusable components for the VoiceHype website.

## FaqSection Component

### Overview
`FaqSection.vue` is a reusable component for displaying FAQ (Frequently Asked Questions) sections. 
It has been extracted from the static landing page to allow for better separation of concerns and easier maintenance.

### Features
- Expandable/collapsible FAQ items with smooth animations
- Automatically calculates content height for smooth transitions
- Responsive design with proper styling
- Only one FAQ item can be open at a time
- Includes hover effects and visual indicators

### Usage
Import and use the component in any Vue file:

```vue
<template>
  <div>
    <FaqSection />
  </div>
</template>

<script setup>
import FaqSection from '@/components/FaqSection.vue';
</script>
```

### Integration
This component is currently used in:
1. `FaqView.vue` - The dedicated FAQ page
2. `ModularLandingPageView.vue` - The modular version of the landing page

### Styling
The component includes its own scoped styling. To override specific styles in the parent component, use `:deep()` selectors:

```css
:deep(.faq-section) {
  /* Your custom styles */
}
``` 
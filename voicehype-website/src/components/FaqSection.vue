<template>
  <section id="faq" class="faq-section bg-gradient-to-b from-gray-50 to-gray-100 pb-16">
    <div class="container mx-auto px-4">
      <h2 class="text-3xl font-bold text-center mb-12">Frequently Asked Questions</h2>
      
      <div class="max-w-3xl mx-auto">
        <div 
          v-for="(item, index) in faqItems" 
          :key="index" 
          class="faq-item bg-white rounded-lg shadow-md mb-4 overflow-hidden"
          :class="{ 'active': item.isActive }"
        >
          <div 
            class="faq-question p-5 cursor-pointer flex justify-between items-center"
            @click="toggleFaqItem(index)"
          >
            <h3 class="text-lg font-semibold">{{ item.question }}</h3>
            <div class="faq-icon transition-transform duration-300" :class="{ 'rotate-180': item.isActive }">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="6 9 12 15 18 9"></polyline>
              </svg>
            </div>
          </div>
          <div class="faq-answer px-5 pb-5 overflow-hidden transition-all duration-300" :style="{ maxHeight: item.isActive ? item.height + 'px' : '0px' }">
            <p>{{ item.answer }}</p>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue';

// FAQ data with reactive isActive property
const faqItems = ref([
  {
    question: "What is VoiceHype?",
    answer: "VoiceHype is a platform that provides advanced voice technology APIs for developers to integrate into their applications. Our services include real-time transcription, voice synthesis, and audio analysis.",
    isActive: false,
    height: 0
  },
  {
    question: "How do I get started with VoiceHype?",
    answer: "Getting started is easy! Simply register for an account, navigate to the dashboard, and generate your API keys. You can then use our documentation to implement our services in your application.",
    isActive: false,
    height: 0
  },
  {
    question: "What programming languages are supported?",
    answer: "Our API can be used with any programming language that can make HTTP requests. We also provide official SDKs for JavaScript, Python, and Ruby to make integration even easier.",
    isActive: false,
    height: 0
  },
  {
    question: "How is usage calculated?",
    answer: "Usage is calculated based on the duration of audio processed. Our pricing page provides detailed information on our pricing tiers and how usage is measured.",
    isActive: false,
    height: 0
  },
  {
    question: "Do you offer a free tier?",
    answer: "Yes, we offer a free tier with limited usage for developers to test our services. Check our pricing page for more details.",
    isActive: false,
    height: 0
  }
]);

// Toggle FAQ item function
const toggleFaqItem = (index) => {
  // Close all FAQ items first
  faqItems.value.forEach((item, i) => {
    if (i !== index) {
      item.isActive = false;
    }
  });
  
  // Toggle the clicked item
  faqItems.value[index].isActive = !faqItems.value[index].isActive;
};

// Calculate and store the heights of the answers after component is mounted
onMounted(async () => {
  // Wait for the DOM to update
  await nextTick();
  
  // Find all answer elements and store their heights
  const answerElements = document.querySelectorAll('.faq-answer');
  answerElements.forEach((el, index) => {
    // Temporarily make it visible to get the height
    el.style.maxHeight = 'none';
    el.style.opacity = '0';
    
    // Get the height
    faqItems.value[index].height = el.scrollHeight;
    
    // Reset styles
    el.style.maxHeight = faqItems.value[index].isActive ? faqItems.value[index].height + 'px' : '0px';
    el.style.opacity = '1';
  });
});
</script>

<style scoped>
.faq-item {
  transition: all 0.3s ease;
}

.faq-item.active {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.faq-question {
  transition: background-color 0.3s ease;
}

.faq-item.active .faq-question {
  background-color: rgba(0, 230, 118, 0.05);
  border-bottom: 1px solid rgba(0, 230, 118, 0.2);
}

.faq-answer {
  transition: max-height 0.3s ease, opacity 0.3s ease;
}

/* Hover effects */
.faq-question:hover {
  background-color: rgba(0, 230, 118, 0.02);
}

/* Icon transition */
.faq-icon {
  color: #00e676;
}
</style> 
/* Fix for fonts in production - using self-hosted fonts instead of Fontshare */
/* @import url('https://api.fontshare.com/v2/css?f[]=clash-display@600,700,800&display=swap'); */
@import url('/landing-page/fonts.css');

/* Remove Fontshare @font-face declaration as it's now in fonts.css */
/* @font-face {
  font-family: 'Clash Display';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url('https://api.fontshare.com/v2/css?f[]=clash-display@700&display=swap') format('woff2');
} */

/* Fix for shimmer animation issues in production build */

/* Apply hardware acceleration to all animation elements */
.shimmer,
.animate-pulse,
.loading-animation,
.loading-shimmer,
[class*="shimmer-"],
[class*="pulse-"],
.skeleton-loader,
.skeleton {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
  will-change: opacity, transform;
}

/* Fix for background-based shimmers */
[class*="bg-shimmer"],
[class*="bg-pulse"],
.loading-bg,
.shimmer-bg {
  background-position: 0 0 !important;
  background-attachment: scroll !important;
  transform: translateZ(0);
}

/* Reduce animation complexity in production */
@media screen and (prefers-reduced-motion: no-preference) {
  .shimmer,
  .animate-pulse,
  .loading-animation {
    animation-duration: 1.5s !important;
  }
}

/* Fix for Tailwind's animate-pulse */
@keyframes custom-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Override Tailwind's animate-pulse with our fixed version */
.animate-pulse {
  animation: custom-pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite !important;
}

/* Specific fixes for dashboard components */
.bg-gray-200.animate-pulse,
.bg-gray-100.animate-pulse,
.dark\:bg-\[\#30363d\].animate-pulse,
.bg-gray-50.dark\:bg-\[\#21262d\] .animate-pulse {
  animation: custom-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite !important;
  transform: translateZ(0);
}

/* Fix for spinning animation */
.animate-spin {
  animation-timing-function: linear !important;
  transform-origin: center !important;
  transform: translateZ(0) rotate(0deg) !important;
  animation: spin 1s linear infinite !important;
}

@keyframes spin {
  to {
    transform: translateZ(0) rotate(360deg) !important;
  }
}

/* Additional fixes for specific components */
#dashboard .animate-pulse,
#api-keys .animate-pulse,
#usage .animate-pulse {
  animation: custom-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite !important;
}

/* Add specific override for nested elements inside animate-pulse containers */
.animate-pulse > div,
.animate-pulse > span {
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Specific optimization for shimmer cards */
.shimmer-card {
  contain: content;
  content-visibility: auto;
}

/* Direct optimization for shimmer elements */
.shimmer-element {
  transform: translateZ(0);
  backface-visibility: hidden;
  animation: shimmer-element-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite !important;
}

@keyframes shimmer-element-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

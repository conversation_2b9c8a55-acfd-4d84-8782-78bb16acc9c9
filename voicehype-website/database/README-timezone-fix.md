# Timezone Bug Fix for Usage History

This directory contains files related to fixing a timezone bug in the usage history feature. The bug causes entries after 12:14 AM on May 31st to not be displayed when filtering by end date, due to timezone conversion issues.

## Fix Overview

The fix addresses the timezone issue in two places:

1. **Database Function**: The `get_usage_summary_statistics` function has been updated to properly handle end dates by ensuring they include the full day (23:59:59.999) when the original input is at midnight (00:00:00).

2. **Client-side Code**: The client code in both `supabase.ts` and `UsageView.vue` has been updated to handle end dates consistently by setting them to the end of day (23:59:59.999) when appropriate.

## Files in this Directory

- `fix_usage_summary_timezone.sql`: The updated SQL function with proper timezone handling
- `deploy_timezone_fix.sh`: A bash script to deploy the fix to your production database
- `test_timezone_fix.js`: A Node.js script to verify the fix works properly

## How to Apply the Fix

1. **Review the SQL Function**: Check `fix_usage_summary_timezone.sql` to understand the changes

2. **Deploy the Fix**: 
   - Edit `deploy_timezone_fix.sh` to add your Supabase URL and service key
   - Make the script executable: `chmod +x deploy_timezone_fix.sh`
   - Run the script: `./deploy_timezone_fix.sh`

3. **Test the Fix**:
   - Edit `test_timezone_fix.js` to add your test credentials
   - Run the test script: `node test_timezone_fix.js`
   - Verify that entries from May 31st are now showing up correctly

## Key Changes Made

1. **In the SQL function**:
   - Added detection for midnight timestamps (00:00:00)
   - Adjusted such timestamps to end-of-day (23:59:59.999)
   - Added debug information to track timezone conversions
   - Used extract() to handle timezone differences properly

2. **In the client code**:
   - Added consistent end date adjustments in filter functions
   - Implemented helpers to detect and fix midnight timestamps
   - Ensured CSV export uses the same date handling logic

## Troubleshooting

If you encounter issues after deploying:

1. Check the debug information in the SQL function's response
2. Verify your database timezone settings
3. Test with a specific date (like May 31st) that was known to be problematic
4. Compare direct database queries with the function results

## Additional Notes

- This fix preserves backward compatibility with existing code
- The debug information in the SQL function can help troubleshoot any remaining timezone issues
- Consider implementing timezone-aware date handling in future features

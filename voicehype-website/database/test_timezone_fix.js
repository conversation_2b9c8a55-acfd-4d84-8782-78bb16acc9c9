// Test script to verify the timezone fix
// This is a Node.js script that tests the fix for the May 31st date filter issue

import { createClient } from '@supabase/supabase-js';

// Replace with your actual Supabase URL and anon key
const supabaseUrl = process.env.SUPABASE_URL || '';
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY || '';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Function to test the fix
async function testEndDateTimezoneHandling() {
  try {
    // Authenticate with your test user first
    // Note: Replace with your test credentials
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'your-test-password',
    });

    if (authError) {
      console.error('Authentication error:', authError);
      return;
    }

    const userId = authData.user.id;
    console.log('Authenticated as user:', userId);

    // Test with the May 31st date that was problematic
    const testEndDate = new Date('2023-05-31T00:00:00');
    console.log('Testing with end date:', testEndDate.toISOString());

    // 1. Test the database function with debug info
    console.log('\n--- Testing get_usage_summary_statistics ---');
    const { data: statsData, error: statsError } = await supabase.rpc('get_usage_summary_statistics', {
      p_user_id: userId,
      p_start_date: new Date('2023-05-01T00:00:00').toISOString(),
      p_end_date: testEndDate.toISOString(),
      p_service: null,
      p_model: null,
      p_status: null
    });

    if (statsError) {
      console.error('Error calling get_usage_summary_statistics:', statsError);
    } else {
      console.log('Stats function returned successfully!');
      console.log('Debug info:', statsData.debug_info);
      console.log('Total records found:', statsData.total_cost > 0 ? 'Yes' : 'No');
      console.log('Total cost:', statsData.total_cost);
    }

    // 2. Test direct query to see entries on May 31st
    console.log('\n--- Testing direct query for May 31st entries ---');
    
    // Set the end date to end of day May 31st
    const endOfDay = new Date('2023-05-31T23:59:59.999');
    
    const { data: directQueryData, error: directQueryError } = await supabase
      .from('usage_history')
      .select('id, service, model, amount, cost, created_at')
      .eq('user_id', userId)
      .gte('created_at', '2023-05-31T00:00:00Z')
      .lte('created_at', endOfDay.toISOString())
      .order('created_at', { ascending: false });

    if (directQueryError) {
      console.error('Error with direct query:', directQueryError);
    } else {
      console.log(`Found ${directQueryData.length} entries on May 31st`);
      if (directQueryData.length > 0) {
        console.log('Sample entries:');
        directQueryData.slice(0, 3).forEach(entry => {
          console.log(`  - ${entry.created_at} | ${entry.service} | ${entry.model} | $${entry.cost}`);
        });
      }
    }

    console.log('\nTest completed!');
  } catch (error) {
    console.error('Unexpected error during test:', error);
  }
}

// Run the test
testEndDateTimezoneHandling();

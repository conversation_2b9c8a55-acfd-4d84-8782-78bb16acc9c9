CREATE OR REPLACE FUNCTION public.get_usage_summary_statistics(p_user_id text, p_start_date timestamp with time zone DEFAULT NULL::timestamp with time zone, p_end_date timestamp with time zone DEFAULT NULL::timestamp with time zone, p_service text DEFAULT NULL::text, p_model text DEFAULT NULL::text, p_status text DEFAULT NULL::text)
 RETURNS json
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  v_total_cost NUMERIC;
  v_service_breakdown JSON;
  v_model_breakdown JSON;
  v_available_services TEXT[];
  v_available_models TEXT[];
  v_total_minutes NUMERIC;
  v_total_input_tokens NUMERIC;
  v_total_output_tokens NUMERIC;
  v_minutes_by_day JSON;
  v_tokens_by_day JSON;
  v_result JSON;
  v_query TEXT;
  v_adjusted_end_date TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Apply end of day adjustment to the end date if it's not null
  -- This ensures we include all records for the end date
  IF p_end_date IS NOT NULL THEN
    -- If the time component is exactly midnight, adjust to end of day
    IF EXTRACT(HOUR FROM p_end_date) = 0 AND 
       EXTRACT(MINUTE FROM p_end_date) = 0 AND 
       EXTRACT(SECOND FROM p_end_date) < 1 THEN
      v_adjusted_end_date := p_end_date + INTERVAL '23 hours 59 minutes 59.999 seconds';
    ELSE
      v_adjusted_end_date := p_end_date;
    END IF;
  ELSE
    v_adjusted_end_date := NULL;
  END IF;

  -- First, calculate the total cost and service breakdown directly from the usage_history table
  -- This ensures we're getting accurate sums without any potential calculation issues
  EXECUTE 
    'WITH service_costs AS (
      SELECT 
        service,
        SUM(cost) as service_cost
      FROM 
        usage_history
      WHERE 
        user_id = $1::uuid
        AND status = ''success''
        ' || CASE WHEN p_start_date IS NOT NULL THEN 'AND created_at >= $2' ELSE '' END || '
        ' || CASE WHEN v_adjusted_end_date IS NOT NULL THEN 'AND created_at <= $3' ELSE '' END || '
        ' || CASE WHEN p_service IS NOT NULL THEN 'AND service = $4' ELSE '' END || '
        ' || CASE WHEN p_model IS NOT NULL THEN 'AND model = $5' ELSE '' END || '
        ' || CASE WHEN p_status IS NOT NULL THEN 'AND status = $6' ELSE '' END || '
      GROUP BY 
        service
    )
    SELECT 
      COALESCE(SUM(service_cost), 0) as total_cost,
      COALESCE(jsonb_object_agg(service, service_cost), ''{}''::jsonb) as service_breakdown
    FROM 
      service_costs'
  INTO 
    v_total_cost, v_service_breakdown
  USING 
    p_user_id,
    p_start_date,
    v_adjusted_end_date,
    p_service,
    p_model,
    p_status;

  -- Now build the query for the rest of the statistics
  v_query := 'WITH usage_summary AS (
    SELECT 
      service,
      model,
      SUM(cost) as total_cost_per_group,
      SUM(CASE WHEN service = ''transcription'' THEN amount ELSE 0 END) as total_minutes,
      SUM(CASE 
          WHEN metadata->>''token_based'' = ''true'' OR service = ''optimization'' 
          THEN COALESCE((metadata->>''input_tokens'')::numeric, 
                        (metadata->>''inputTokens'')::numeric, 0) 
          ELSE 0 
      END) as total_input_tokens,
      SUM(CASE 
          WHEN metadata->>''token_based'' = ''true'' OR service = ''optimization'' 
          THEN COALESCE((metadata->>''output_tokens'')::numeric, 
                        (metadata->>''outputTokens'')::numeric, 0) 
          ELSE 0 
      END) as total_output_tokens
    FROM 
      usage_history
    WHERE 
      user_id = $1::uuid
      AND status = ''success''';
  
  -- Add filters with fixed parameter positions
  IF p_start_date IS NOT NULL THEN
    v_query := v_query || ' AND created_at >= $2';
  END IF;
  
  IF v_adjusted_end_date IS NOT NULL THEN
    v_query := v_query || ' AND created_at <= $3';
  END IF;
  
  IF p_service IS NOT NULL THEN
    v_query := v_query || ' AND service = $4';
  END IF;
  
  IF p_model IS NOT NULL THEN
    v_query := v_query || ' AND model = $5';
  END IF;
  
  IF p_status IS NOT NULL THEN
    v_query := v_query || ' AND status = $6';
  END IF;
  
  -- Complete the query with proper grouping and aggregation
  v_query := v_query || ' GROUP BY service, model),
  
  -- Daily aggregation for charts
  daily_usage AS (
    SELECT 
      DATE_TRUNC(''day'', created_at) as usage_date,
      SUM(CASE WHEN service = ''transcription'' THEN amount ELSE 0 END) as minutes_used,
      SUM(CASE 
          WHEN metadata->>''token_based'' = ''true'' OR service = ''optimization'' 
          THEN COALESCE((metadata->>''input_tokens'')::numeric, 
                        (metadata->>''inputTokens'')::numeric, 0) 
          ELSE 0 
      END) as input_tokens_used,
      SUM(CASE 
          WHEN metadata->>''token_based'' = ''true'' OR service = ''optimization'' 
          THEN COALESCE((metadata->>''output_tokens'')::numeric, 
                        (metadata->>''outputTokens'')::numeric, 0) 
          ELSE 0 
      END) as output_tokens_used
    FROM 
      usage_history
    WHERE 
      user_id = $1::uuid
      AND status = ''success''';
  
  -- Add the same filters to daily aggregation
  IF p_start_date IS NOT NULL THEN
    v_query := v_query || ' AND created_at >= $2';
  END IF;
  
  IF v_adjusted_end_date IS NOT NULL THEN
    v_query := v_query || ' AND created_at <= $3';
  END IF;
  
  IF p_service IS NOT NULL THEN
    v_query := v_query || ' AND service = $4';
  END IF;
  
  IF p_model IS NOT NULL THEN
    v_query := v_query || ' AND model = $5';
  END IF;
  
  IF p_status IS NOT NULL THEN
    v_query := v_query || ' AND status = $6';
  END IF;
  
  v_query := v_query || ' GROUP BY usage_date
    ORDER BY usage_date
  )
  
  SELECT 
    COALESCE(
      jsonb_object_agg(model, total_cost_per_group) FILTER (WHERE model IS NOT NULL),
      ''{}''::jsonb
    ) AS model_breakdown,
    ARRAY_AGG(DISTINCT service) FILTER (WHERE service IS NOT NULL) AS available_services,
    ARRAY_AGG(DISTINCT model) FILTER (WHERE model IS NOT NULL) AS available_models,
    COALESCE(SUM(total_minutes), 0) AS total_minutes,
    COALESCE(SUM(total_input_tokens), 0) AS total_input_tokens,
    COALESCE(SUM(total_output_tokens), 0) AS total_output_tokens,
    (SELECT 
      COALESCE(
        jsonb_agg(
          jsonb_build_object(
            ''date'', usage_date,
            ''minutes'', minutes_used
          )
        ),
        ''[]''::jsonb
      ) 
    FROM daily_usage) AS minutes_by_day,
    (SELECT 
      COALESCE(
        jsonb_agg(
          jsonb_build_object(
            ''date'', usage_date,
            ''input_tokens'', input_tokens_used,
            ''output_tokens'', output_tokens_used
          )
        ),
        ''[]''::jsonb
      ) 
    FROM daily_usage) AS tokens_by_day
  FROM usage_summary';
  
  -- Execute the query with parameters to get the remaining statistics
  EXECUTE v_query 
  INTO v_model_breakdown, v_available_services, v_available_models, 
       v_total_minutes, v_total_input_tokens, v_total_output_tokens, v_minutes_by_day, v_tokens_by_day
  USING 
    p_user_id,
    p_start_date,
    v_adjusted_end_date,
    p_service,
    p_model,
    p_status;
  
  -- Add debug information about the date parameters to the result
  v_result := json_build_object(
    'total_cost', v_total_cost,
    'service_breakdown', v_service_breakdown,
    'model_breakdown', v_model_breakdown,
    'available_services', v_available_services,
    'available_models', v_available_models,
    'total_minutes', v_total_minutes,
    'total_input_tokens', v_total_input_tokens,
    'total_output_tokens', v_total_output_tokens,
    'minutes_by_day', v_minutes_by_day,
    'tokens_by_day', v_tokens_by_day,
    'debug_info', json_build_object(
      'original_end_date', p_end_date,
      'adjusted_end_date', v_adjusted_end_date
    )
  );
  
  RETURN v_result;
END;
$function$;

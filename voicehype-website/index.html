<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <link rel="icon" href="/tv_logo.png">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="VoiceHype - Unleash the power of your voice. Create prompts. Get super fast & super productive.">
    <meta name="author" content="VoiceHype">
    
    <!-- Base URL for all relative URLs -->
    <base href="/">
    
    <!-- Critical inline CSS to prevent FOUC -->
    <style>
      /* Critical styles to prevent Flash of Unstyled Content */
      body {
        margin: 0;
        padding: 0;
        background-color: #0d1117;
        color: #ffffff;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        min-height: 100vh;
      }
      
      /* Hide main content until fully loaded */
      #app {
        opacity: 0;
        transition: opacity 0.3s ease-in;
      }
      #app.content-loaded {
        opacity: 1;
      }
      
      /* Loading overlay and animations */
      .app-loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: #0d1117;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        transition: opacity 0.5s ease-out;
      }
      .app-loading.loaded {
        opacity: 0;
        pointer-events: none;
      }
      .loader {
        width: 48px;
        height: 48px;
        border: 3px solid rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        border-top-color: #14F195;
        animation: spin 1s ease-in-out infinite;
      }
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
      
      /* Navbar fixes */
      .navbar {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 100;
        padding: 0.75rem 0;
        height: auto;
        display: flex;
        align-items: center;
      }
      
      .nav-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
      }
      
      .nav-logo {
        display: flex;
        align-items: center;
        height: auto;
        margin: 0;
        padding: 0;
      }
      
      .logo-text {
        margin: 0;
        padding: 0;
        line-height: 1;
      }
      
      /* Critical variables */
      :root {
        --color-bg: #0d1117;
        --color-bg-light: #161b22;
        --color-text: #ffffff;
        --color-primary: #14F195;
        --font-sans: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      }
    </style>
    
    <!-- Preconnect to font providers -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Preload critical font files -->
    <link rel="preload" href="/landing-page/fonts/clash-display/ClashDisplay-Regular.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="/landing-page/fonts/clash-display/ClashDisplay-Bold.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="/landing-page/fonts/clash-display/ClashDisplay-Semibold.woff2" as="font" type="font/woff2" crossorigin>
    
    <title>VoiceHype</title>
  </head>
  <body>
    <!-- Loading overlay -->
    <div class="app-loading">
      <div class="loader"></div>
    </div>
    
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
    
    <!-- Script to handle loading state -->
    <script>
      // Wait for styles and document to fully load
      window.addEventListener('load', function() {
        // Check if all stylesheets are loaded
        const checkStylesheetsLoaded = function() {
          const styleSheets = document.styleSheets;
          let allLoaded = true;
          
          for (let i = 0; i < styleSheets.length; i++) {
            try {
              // Try to access rules - will throw if sheet not loaded
              const rules = styleSheets[i].cssRules;
            } catch (e) {
              // If we can't access rules, stylesheet might not be loaded
              if (e.name !== 'SecurityError') { // Ignore CORS security errors
                allLoaded = false;
                break;
              }
            }
          }
          
          return allLoaded;
        };
        
        const showContent = function() {
          // Add loaded class to app container
          document.getElementById('app').classList.add('content-loaded');
          // Hide the loader
          document.querySelector('.app-loading').classList.add('loaded');
        };
        
        // Try to show content only when stylesheets are loaded
        if (checkStylesheetsLoaded()) {
          showContent();
        } else {
          // If not loaded immediately, wait a bit longer
          setTimeout(function() {
            showContent();
          }, 800); // Increased delay to ensure styles are applied
        }
      });
    </script>
  </body>
</html>

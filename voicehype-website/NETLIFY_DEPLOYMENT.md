# Netlify Deployment Guide

## Email Confirmation Issue

When deploying to Netlify, you may encounter an issue where email confirmation links redirect users to `localhost` instead of your Netlify domain. This happens because the application needs to know the production URL to generate correct email confirmation links.

## Solution

### 1. Set Environment Variables in Netlify

1. Go to your Netlify dashboard
2. Select your site
3. Go to **Site settings** > **Build & deploy** > **Environment variables**
4. Add the following environment variable:
   - Key: `VITE_SITE_URL`
   - Value: `https://your-netlify-domain.netlify.app` (replace with your actual Netlify domain)

### 2. Update Supabase Site URL

You also need to update the Site URL in your Supabase project settings:

1. Go to your Supabase dashboard
2. Select your project
3. Go to **Authentication** > **URL Configuration**
4. Update the **Site URL** to match your Netlify domain
5. Click **Save**

This ensures that all authentication-related redirects (email confirmation, password reset, etc.) will correctly point to your Netlify domain instead of localhost.

## Local Development

For local development, the application will automatically fall back to using `window.location.origin` if the `VITE_SITE_URL` environment variable is not set.

## Testing Email Flows

To test email confirmation and password reset flows:

1. Make sure your Supabase Site URL is correctly set
2. Register a new user or request a password reset
3. Check that the email links point to your Netlify domain
4. Verify that clicking the links redirects you to the correct page on your Netlify site

## Troubleshooting

If you're still experiencing issues with email redirects:

1. Check that the `VITE_SITE_URL` environment variable is correctly set in Netlify
2. Verify that the Supabase Site URL is correctly set in the Supabase dashboard
3. Clear your browser cache and try again
4. Check the browser console for any errors

For more information, refer to the [Supabase Authentication documentation](https://supabase.com/docs/guides/auth). 
# VoiceHype Website Development Plan

## Project Overview
VoiceHype is a service that provides voice transcription and optimization capabilities through an API. The website will serve as a dashboard for users to manage their API keys, monitor usage, handle subscriptions, and adjust settings.

## Tech Stack
- **Frontend**: Vue 3 with TypeScript
- **State Management**: Pinia
- **Routing**: Vue Router
- **Styling**: TailwindCSS
- **Backend**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth

## Database Structure
Based on the schema, the main tables include:
- `profiles`: User profiles
- `api_keys`: API keys for accessing VoiceHype services
- `credits`: User credit balances
- `payg_usage`: Pay-as-you-go usage tracking
- `pricing_models`: Different pricing models (PAYG, credits, subscription)
- `quotas`: Usage quotas for subscription plans
- `service_pricing`: Pricing for different services and models
- `subscription_plans`: Available subscription plans
- `usage_history`: History of service usage

## Key Features

### 1. Authentication
- User registration and login
- Password reset functionality
- Session management

### 2. Dashboard
- Overview of usage statistics
- Credit balance display
- Active API keys count
- Current subscription plan
- Recent activity log

### 3. API Key Management
- Create new API keys
- View existing keys
- Rename keys
- Deactivate/delete keys
- Set key permissions (allow credits, PAYG, subscriptions)
- Set expiration dates

### 4. Usage Tracking
- Historical usage data
- Filtering by date range, service, and model
- Usage visualization (charts)
- Cost breakdown
- Export functionality

### 5. Subscription Management
- View available plans
- Upgrade/downgrade subscription
- Manage billing information
- View billing history
- Cancel subscription

### 6. Credits System
- Purchase credits
- View credit balance
- Credit usage history
- Volume discounts

### 7. Settings
- Account information management
- Default pricing model selection
- Notification preferences
- Dark/light mode toggle

## Pages to Implement

1. **Dashboard** (`/`)
   - Usage summary cards
   - Credits balance
   - API keys overview
   - Subscription status
   - Recent activity table

2. **API Keys** (`/api-keys`)
   - List of API keys
   - Create new key modal
   - Key details and permissions
   - Revoke/delete functionality

3. **Usage History** (`/usage`)
   - Filterable usage table
   - Usage charts
   - Cost breakdown
   - Export options

4. **Subscription** (`/subscription`)
   - Current plan details
   - Available plans comparison
   - Upgrade/downgrade options
   - Billing information
   - Payment history

5. **Settings** (`/settings`)
   - Profile information
   - Password change
   - Notification settings
   - Default pricing preferences
   - Theme settings

## Implementation Plan

### Phase 1: Setup and Authentication
1. Set up Vue project with TypeScript
2. Configure TailwindCSS
3. Implement Supabase client
4. Create authentication components
5. Set up protected routes

### Phase 2: Core Dashboard
1. Implement dashboard layout
2. Create sidebar navigation
3. Build usage summary cards
4. Implement recent activity table
5. Add dark/light mode toggle

### Phase 3: API Key Management
1. Create API key listing component
2. Implement create key functionality
3. Build key details view
4. Add rename and revoke capabilities

### Phase 4: Usage History
1. Build usage history table
2. Implement filtering functionality
3. Create usage visualization charts
4. Add export functionality

### Phase 5: Subscription Management
1. Create subscription plans display
2. Implement plan selection
3. Build billing information form
4. Add payment processing
5. Create payment history view

### Phase 6: Settings
1. Build profile settings form
2. Implement password change
3. Create notification preferences
4. Add default pricing model selection

### Phase 7: Testing and Optimization
1. Unit testing
2. Integration testing
3. Performance optimization
4. Accessibility improvements
5. Cross-browser testing

## API Integration

The website will need to integrate with the following Supabase functions:
- `validate_api_key`: For validating API keys
- `create_api_key`: For creating new API keys
- `update_api_key_name`: For renaming API keys
- `check_usage_allowance`: For checking if a user can use a service
- `finalize_usage`: For recording usage
- `get_unpaid_payg_balances`: For checking unpaid balances
- `has_unpaid_payg_balance`: For checking if a user has unpaid balances

## Responsive Design
The website will be fully responsive, with optimized layouts for:
- Desktop (1200px+)
- Tablet (768px - 1199px)
- Mobile (< 768px)

## Security Considerations
- Implement proper authentication guards
- Secure API key handling
- CSRF protection
- XSS prevention
- Rate limiting
- Input validation

## Performance Optimization
- Lazy loading of components
- Code splitting
- Image optimization
- Caching strategies
- Minimizing bundle size

## Accessibility
- Semantic HTML
- ARIA attributes
- Keyboard navigation
- Screen reader compatibility
- Color contrast compliance

## Next Steps
1. Set up project structure
2. Implement authentication
3. Create basic layout with navigation
4. Build dashboard page
5. Proceed with remaining features in order of priority 
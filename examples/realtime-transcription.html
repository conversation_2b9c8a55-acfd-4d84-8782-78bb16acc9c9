<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>VoiceHype Real-time Transcription Demo</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    h1 {
      color: #333;
    }
    .container {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }
    .controls {
      display: flex;
      gap: 10px;
      align-items: center;
    }
    button {
      background-color: #4CAF50;
      border: none;
      color: white;
      padding: 10px 20px;
      text-align: center;
      text-decoration: none;
      display: inline-block;
      font-size: 16px;
      margin: 4px 2px;
      cursor: pointer;
      border-radius: 4px;
    }
    button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }
    button.stop {
      background-color: #f44336;
    }
    .transcript {
      border: 1px solid #ddd;
      padding: 20px;
      border-radius: 4px;
      min-height: 200px;
      max-height: 400px;
      overflow-y: auto;
    }
    .partial {
      color: #666;
    }
    .final {
      color: #000;
      font-weight: bold;
    }
    .status {
      color: #666;
      font-style: italic;
    }
    .error {
      color: #f44336;
    }
    input[type="text"] {
      padding: 10px;
      width: 300px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
  </style>
</head>
<body>
  <h1>VoiceHype Real-time Transcription Demo</h1>
  
  <div class="container">
    <div>
      <label for="apiKey">API Key:</label>
      <input type="text" id="apiKey" placeholder="Enter your VoiceHype API key">
    </div>
    
    <div class="controls">
      <button id="startButton">Start Recording</button>
      <button id="stopButton" class="stop" disabled>Stop Recording</button>
    </div>
    
    <div class="status" id="status">Ready to start recording...</div>
    
    <div class="transcript" id="transcript"></div>
  </div>

  <script>
    // DOM elements
    const apiKeyInput = document.getElementById('apiKey');
    const startButton = document.getElementById('startButton');
    const stopButton = document.getElementById('stopButton');
    const statusElement = document.getElementById('status');
    const transcriptElement = document.getElementById('transcript');
    
    // Variables
    let mediaRecorder;
    let audioContext;
    let socket;
    let isRecording = false;
    let processor;
    let input;
    
    // Update status
    function updateStatus(message, isError = false) {
      statusElement.textContent = message;
      if (isError) {
        statusElement.classList.add('error');
      } else {
        statusElement.classList.remove('error');
      }
    }
    
    // Add transcript
    function addTranscript(text, isFinal) {
      const p = document.createElement('p');
      p.textContent = text;
      p.className = isFinal ? 'final' : 'partial';
      
      // If it's a partial transcript, replace the last partial or add new
      if (!isFinal) {
        const lastPartial = transcriptElement.querySelector('.partial:last-child');
        if (lastPartial) {
          lastPartial.textContent = text;
          return;
        }
      }
      
      transcriptElement.appendChild(p);
      transcriptElement.scrollTop = transcriptElement.scrollHeight;
    }
    
    // Start recording
    async function startRecording() {
      const apiKey = apiKeyInput.value.trim();
      if (!apiKey) {
        updateStatus('Please enter your VoiceHype API key', true);
        return;
      }
      
      try {
        // Request microphone access
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        
        // Create audio context
        audioContext = new (window.AudioContext || window.webkitAudioContext)({
          sampleRate: 16000 // 16kHz sample rate for AssemblyAI
        });
        
        // Create WebSocket connection
        const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${wsProtocol}//${window.location.host}/transcribe/realtime?apiKey=${encodeURIComponent(apiKey)}`;
        
        socket = new WebSocket(wsUrl);
        
        socket.onopen = () => {
          updateStatus('WebSocket connection established. Waiting for transcription service...');
        };
        
        socket.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            
            if (data.type === 'connected') {
              updateStatus('Connected to transcription service. Start speaking...');
            } else if (data.type === 'error') {
              updateStatus(`Error: ${data.message}`, true);
              stopRecording();
            } else if (data.type === 'service_disconnected') {
              updateStatus('Transcription service disconnected', true);
              stopRecording();
            } else if (data.message_type === 'PartialTranscript') {
              addTranscript(data.text, false);
            } else if (data.message_type === 'FinalTranscript') {
              addTranscript(data.text, true);
            }
          } catch (error) {
            console.error('Error parsing message:', error);
          }
        };
        
        socket.onerror = (error) => {
          updateStatus('WebSocket error', true);
          console.error('WebSocket error:', error);
          stopRecording();
        };
        
        socket.onclose = () => {
          updateStatus('WebSocket connection closed');
          stopRecording();
        };
        
        // Create audio processing pipeline
        input = audioContext.createMediaStreamSource(stream);
        processor = audioContext.createScriptProcessor(4096, 1, 1);
        
        processor.onaudioprocess = (e) => {
          if (socket && socket.readyState === WebSocket.OPEN) {
            // Get audio data
            const inputData = e.inputBuffer.getChannelData(0);
            
            // Convert to 16-bit PCM
            const pcmData = new Int16Array(inputData.length);
            for (let i = 0; i < inputData.length; i++) {
              pcmData[i] = Math.max(-1, Math.min(1, inputData[i])) * 0x7FFF;
            }
            
            // Send audio data to WebSocket
            socket.send(pcmData.buffer);
          }
        };
        
        // Connect audio nodes
        input.connect(processor);
        processor.connect(audioContext.destination);
        
        // Update UI
        isRecording = true;
        startButton.disabled = true;
        stopButton.disabled = false;
        updateStatus('Connecting to transcription service...');
        
      } catch (error) {
        updateStatus(`Error starting recording: ${error.message}`, true);
        console.error('Error starting recording:', error);
      }
    }
    
    // Stop recording
    function stopRecording() {
      if (isRecording) {
        // Disconnect audio processing
        if (input && processor) {
          input.disconnect();
          processor.disconnect();
        }
        
        // Close WebSocket
        if (socket && socket.readyState === WebSocket.OPEN) {
          socket.send(JSON.stringify({ type: 'close' }));
          socket.close();
        }
        
        // Close audio context
        if (audioContext && audioContext.state !== 'closed') {
          audioContext.close();
        }
        
        // Update UI
        isRecording = false;
        startButton.disabled = false;
        stopButton.disabled = true;
        updateStatus('Recording stopped');
      }
    }
    
    // Event listeners
    startButton.addEventListener('click', startRecording);
    stopButton.addEventListener('click', stopRecording);
    
    // Handle page unload
    window.addEventListener('beforeunload', () => {
      if (isRecording) {
        stopRecording();
      }
    });
  </script>
</body>
</html> 
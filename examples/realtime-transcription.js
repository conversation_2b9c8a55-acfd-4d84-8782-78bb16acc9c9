const WebSocket = require('ws');
const fs = require('fs');
const path = require('path');
const { Readable } = require('stream');
const { spawn } = require('child_process');

// Configuration
const API_KEY = process.env.VOICEHYPE_API_KEY || 'your-api-key';
const ENDPOINT = process.env.VOICEHYPE_ENDPOINT || 'wss://your-project.functions.supabase.co/transcribe/realtime';

// Connect to the WebSocket server
const wsUrl = `${ENDPOINT}?apiKey=${encodeURIComponent(API_KEY)}`;
const ws = new WebSocket(wsUrl);

// Track connection state
let isConnected = false;

// Handle WebSocket events
ws.on('open', () => {
  console.log('WebSocket connection established. Waiting for transcription service...');
});

ws.on('message', (data) => {
  try {
    const message = JSON.parse(data);
    
    if (message.type === 'connected') {
      console.log(`Connected to transcription service. Session ID: ${message.sessionId}`);
      isConnected = true;
      
      // Start recording once connected
      startRecording();
    } else if (message.type === 'error') {
      console.error(`Error: ${message.message}`);
      stopRecording();
    } else if (message.type === 'service_disconnected') {
      console.log(`Transcription service disconnected: ${message.reason} (${message.code})`);
      stopRecording();
    } else if (message.message_type === 'PartialTranscript') {
      console.log(`Partial: ${message.text}`);
    } else if (message.message_type === 'FinalTranscript') {
      console.log(`Final: ${message.text}`);
    }
  } catch (error) {
    console.error('Error parsing message:', error);
  }
});

ws.on('error', (error) => {
  console.error('WebSocket error:', error);
  stopRecording();
});

ws.on('close', () => {
  console.log('WebSocket connection closed');
  stopRecording();
  process.exit(0);
});

// Recording process
let recordingProcess = null;

function startRecording() {
  if (!isConnected) {
    console.log('Cannot start recording: Not connected to transcription service');
    return;
  }
  
  console.log('Starting recording...');
  
  // Use SoX to record audio from the microphone
  // Make sure SoX is installed on your system: http://sox.sourceforge.net/
  recordingProcess = spawn('sox', [
    '-q',                    // Quiet mode
    '-d',                    // Input from default audio device (microphone)
    '-t', 'raw',             // Output format: raw audio
    '-r', '16000',           // Sample rate: 16kHz
    '-b', '16',              // Bit depth: 16 bits
    '-c', '1',               // Channels: 1 (mono)
    '-e', 'signed-integer',  // Encoding: signed integer
    '-'                      // Output to stdout
  ]);
  
  // Handle recording process events
  recordingProcess.stdout.on('data', (audioChunk) => {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(audioChunk);
    }
  });
  
  recordingProcess.stderr.on('data', (data) => {
    console.error(`Recording error: ${data}`);
  });
  
  recordingProcess.on('close', (code) => {
    console.log(`Recording process exited with code ${code}`);
  });
  
  // Handle Ctrl+C to stop recording
  process.on('SIGINT', () => {
    console.log('\nStopping recording...');
    stopRecording();
    
    // Close WebSocket connection
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify({ type: 'close' }));
      ws.close();
    }
    
    // Wait a bit before exiting to allow for cleanup
    setTimeout(() => {
      process.exit(0);
    }, 1000);
  });
}

function stopRecording() {
  if (recordingProcess && !recordingProcess.killed) {
    recordingProcess.kill();
    recordingProcess = null;
    console.log('Recording stopped');
  }
}

console.log('Connecting to real-time transcription service...');
console.log('Press Ctrl+C to stop recording and exit'); 
/*! \page acknowledge Acknowledgements

Many thanks to the following people for providing bug fixes and improvements:
<UL>
<LI><PERSON> (major code and repository support!)</LI>
<LI><PERSON></LI>
<LI>bejuryu</LI>
<LI><PERSON> B&e<PERSON>ute;nony</LI>
<LI><PERSON></LI>
<LI><PERSON></LI>
<LI><PERSON></LI>
<LI><PERSON> C<PERSON>lando</LI>
<LI><PERSON><PERSON></LI>
<LI><PERSON></LI>
<LI><PERSON> (Windows DS and ASIO)</LI>
<LI><PERSON><PERSON></LI>
<LI><PERSON></LI>
<LI><PERSON></LI>
<LI><PERSON></LI>
<LI><PERSON></LI>
<LI><PERSON></LI>
<LI><PERSON></LI>
<LI><PERSON></LI>
<LI><PERSON></LI>
<LI><PERSON></LI>
<LI><PERSON></LI>
<LI><PERSON></LI>
<LI><PERSON> (PulseAudio)</LI>
<LI><PERSON><PERSON><PERSON></LI>
<LI>rehans</LI>
<LI>Sebastian Reimers</LI>
<LI>Ryan Schmidt</LI>
<LI>Benjamin Schroeder</LI>
<LI>sonoro1234</LI>
<LI>terminator356</LI>
<LI>Marcus Tomlinson (WASAPI)</LI>
<LI>Ryan Williams (Windows non-MS compiler ASIO support)</LI>
<LI>Ed Wildgoose (Linux ALSA and Jack)</LI>
<LI>Serge Zaitsev</LI>
<LI>Iohannes Zm&ouml;lnig</LI>

</UL>

The RtAudio API incorporates many of the concepts developed in the <A href="http://www.portaudio.com/">PortAudio</A> project by Phil Burk and Ross Bencina.  Early development also incorporated ideas from Bill Schottstaedt's <A href="http://www-ccrma.stanford.edu/software/snd/sndlib/">sndlib</A>.  The CCRMA <A href="http://www-ccrma.stanford.edu/groups/soundwire/">SoundWire group</A> provided valuable feedback during the API proposal stages.

The early 2.0 version of RtAudio was slowly developed over the course of many months while in residence at the <A href="http://www.iua.upf.es/">Institut Universitari de L'Audiovisual (IUA)</A> in Barcelona, Spain and the <A href="http://www.acoustics.hut.fi/">Laboratory of Acoustics and Audio Signal Processing</A> at the Helsinki University of Technology, Finland.  Much subsequent development happened while working at the <A href="http://www-ccrma.stanford.edu/">Center for Computer Research in Music and Acoustics (CCRMA)</A> at <A href="http://www.stanford.edu/">Stanford University</A>.  The early stages of this work were supported in part by the United States Air Force Office of Scientific Research (grant \#F49620-99-1-0293). All recent versions of RtAudio have been completed while working as a professor of <a href="http://www.music.mcgill.ca/musictech/">Music Technology</a> at <a href="http://www.mcgill.ca/">McGill University</a>.  

*/

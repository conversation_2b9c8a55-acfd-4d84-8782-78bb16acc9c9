RtAudio - a set of C++ classes that provide a common API for realtime audio input/output across Linux (native ALSA, JACK, PulseAudio, and OSS), Macintosh OS X (CoreAudio and JACK), and Windows (DirectSound, ASIO and WASAPI) operating systems.

By <PERSON>, 2001-2023.

v.6.0.1: (1 August 2023)
- soversion bump to 7

v.6.0.0: (21 July 2023)
- see git history for complete list of changes
- major API changes concerning device selection / specification
- major API changes concerning error handling ... C++ exceptions no longer used
- rewrite of device querying code

v.5.2.0: (15 November 2021)
- see git history for complete list of changes
- update to audioprobe.cpp to list devices for all APIs
- miscellaneous build system updates
- PulseAudio device detection fixes
- some Wasapi additions / fixes

v.5.1.0: (17 April 2019)
- see git history for complete list of changes
- many WASAPI updates (thanks to <PERSON>)
- miscellaneous build system updates
- bug fix for stream ticking in OS-X if using two devices for duplex
- ALSA stream handle bug fixes
- new C API wrapper
- new static functions to get API names

v5.0.0: (30 August 2017)
- see git history for complete list of changes
- WASAPI updates (thanks to <PERSON>)
- minor exception semantic changes
- miscellaneous build system updates

v4.1.2: (22 February 2016)
- added more complete automake support (thanks to <PERSON>)
- miscellaneous small fixes and updates, see github repo commit history for details

v4.1.1: (26 April 2014)
- updates to WASAPI API for MinGW compiling
- WASAPI bug fixes for audio INPUT mode
- DirectSound bug fix for INPUT mode
- Bug fixes in Core, Jack, ASIO and DS for internal draining in INPUT mode
- updates to test programs for default device specifiers
- CMake buildfile update for WASAPI
- new setStreamTime function

v4.1.0: (10 April 2014)
- RtError class renamed RtAudioError and embedded in RtAudio.h (RtError.h deleted)
- new support for the Windows WASAPI API (thanks to Marcus Tomlinson)
- CMake support (thanks to Berkus Decker)
- pulse audio update to support bufferFrames argument with audio input (thanks to Jonatan Wallmander)
- fixes for ALSA API to avoid high CPU usage during stops and to clear stale data before input (thanks to Pluto Hades)
- miscellaneous efficiency updates suggested by Martin Koegler
- bug fix for OS-X xrun reporting problem
- bug fix related to error when opening a stream after closing a previously open stream

v4.0.12: (16 April 2013)
- new functionality to allow error reporting via a client-supplied function (thanks to Pavel Mogilevskiy)
- new function to return the version number
- updated RtAudio.cpp and ASIO files for UNICODE support (thanks to Renaud Schoonbroodt)
- updates to PulseAudio API support (thanks to Peter Meerwald and Tristan Matthews)
- updates for pkg-config support in configure script
- 24-bit format changed to true 24-bit format, not sub-bytes of 32-bits (thanks to Marc Britton)
- bug fixes to make sure stream status is closed if error during probeDeviceOpen
- updates / fixes to SCHED_RR code in ALSA (thanks to Marc Lindahl)
- various changes to avoid global variables (thanks to Martin Koegler)

v4.0.11: (14 June 2012)
- fixes for memory leaks in ALSA (thanks to Martin Koegler)
- PulseAudio API support added (thanks to Peter Meerwald and Tristan Matthews)
- bitwise format flag fixes in OS-X (Benjamin Schroeder and Stefan Arisona)
- changes to stopStream / drain flag to avoid hung state in ASIO, DS, OS-X, and Jack APIs (Rasmus Ekman and Carlos Luna)

v4.0.10: (30 August 2011)
- fix for compile bug in Windows DS (counting devices)
- update to configure and library Makefile

v4.0.9: (14 August 2011)
- fix for ASIO problem enumerating devices after opening duplex stream (Oliver Larkin)
- fix for OS-X problems setting sample rate and bits-per-sample
- updates for OS-X "Lion"
- updates for wide character support in Windows DS (UNICODE)
- fix for possible ALSA callback thread hang (thanks to Tristan Matthews)
- fix for DS getDeviceCount bug (vector erase problem)

v4.0.8: (12 April 2011)
- fix for MinGW4 problem enumerating and setting sample rates (iasiothiscallresolver, Dmitry Kostjuchenko)
- fix for OS-X problem handling device names in some languages (CFString conversion, Vincent Bénony)
- small change to OS-X mutex lock location to avoid lockups
- correction to documentation regarding 24-bit data (should be lower 3 bytes, not upper 3 bytes)
- bug fix for error handling of warnings (Antoine Lefebvre)
- added option to use the ALSA "default" device (Tristan Matthews)
- removed use of mutexes in Windows
- fix for ASIO4ALL behavior when stopping/closing streams (Antoine Lefebvre)
- included python binding in "contrib" directory (beta, Antoine Lefebvre)

v4.0.7: (4 February 2010)
- revised Windows DS code and device enumeration to speed up device queries
- OS-X 10.6 updates for deprecated functions
- updates to Jack shutdown code to avoid lockup

v4.0.6: (3 June 2009)
- bug fix in ALSA code to set period size to power of two (thanks to Joakim Karrstrom)
- bug fix in OS-X for OS < 10.5 ... need preprocessor definition around new variable type (thanks to Tristan Matthews)

v4.0.5: (2 February 2009)
- added support in CoreAudio for arbitrary stream channel configurations
- added getStreamSampleRate() function because the actual sample rate can sometimes vary slightly from the specified one (thanks to Theo Veenker)
- added new StreamOptions flag "RTAUDIO_SCHEDULE_REALTIME" and attribute "priority" to StreamOptions (thanks to Theo Veenker)
- replaced usleep(50000) in callbackEvent() by a wait on condition variable which gets signaled in startStream() (thanks to Theo Veenker)
- fix for Jack API when user callback function signals stop or abort calls
- fix to way stream state is changed to avoid infinite loop problem
- fix to int<->float conversion in convertBuffer() (thanks to Theo Veenker)
- bug fix in byteSwapBuffer() (thanks to Stefan Muller Arisona and Theo Veenker)
- fixed a few gcc 4.4 errors in OS-X
- fixed bug in rtaudio-config script
- revised configure script and Makefile structures
- 64-bit fixes in ALSA API (thanks to Stefan Muller Arisona)
- fixed ASIO sample rate selection bug (thanks to Sasha Zheligovsky)

v4.0.4: (24 January 2008)
- added functionality to allow getDeviceInfo() to work in ALSA for an open device (like ASIO)
- fixes in configure script
- fixed clearing of error message stream in error()
- fixed RtAudio::DeviceInfo description in "probing" documentation
- memory leak fixes in ALSA and OSS
- Jack in/out port flag fix
- Windows changes for thread priority and GLOBALFOCUS

v4.0.3: (7 December 2007)
- added support for MinGW compiler to configure script
- a few MinGW-related changes to RtAudio.cpp
- renamed test program probe.cpp to audioprobe.cpp
- moved various header files into single "include" directory and updated VC++ project files

v4.0.2: (21 August 2007)
- fix to RtError::WARNING typo in RtAudio.h (RtApiDummy)
- removed "+1"s in RtApiCore c++ append when getting device name

v4.0.1: (13 August 2007)
- fix to RtError::WARNING typo in RtAudio.cpp

v4.0.0: (7 August 2007)
- new support for non-interleaved user data
- additional input/output parameter specifications, including channel offset
- new support for dynamic connection of devices
- new support for stream time
- revised callback arguments, including separate input and output buffer arguments
- revised C++ exception handling
- revised OSS support for version 4.0
- discontinued support of blocking functionality
- discontinued support of SGI
- Windows DirectSound API bug fix
- NetBSD support (using OSS API) by Emmanuel Dreyfus
- changed default pthread scheduling priority to SCHED_RR when defined in the system
- new getCompiledApi() static function
- new getCurrentApi(), getStreamTime(), getStreamLatency(), and isStreamRunning() functions
- modified RtAudioDeviceInfo structure to distinguish default input and output devices

v3.0.3: (18 November 2005)
- UNICODE fix for Windows DirectSound API
- MinGW compiler fix for ASIO API

v3.0.2: (14 October 2005)
- modification of ALSA read/write order to fix duplex under/overruns
- added synchronization of input/output devices for ALSA duplex operation
- cleaned up and improved error reporting throughout
- bug fix in Windows DirectSound support for 8-bit audio
- bug fix in Windows DirectSound support during device capture query
- added ASIOOutputReady() call near end of callbackEvent to fix some driver behavior
- added #include <stdio.h> to RtAudio.cpp
- fixed bug in RtApiCore for duplex operation with different I/O devices
- improvements to DirectX pointer chasing (by Robin Davies)
- backdoor RtDsStatistics hook provides DirectX performance information (by Robin Davies)
- bug fix for non-power-of-two Asio granularity used by Edirol PCR-A30 (by Robin Davies)
- auto-call CoInitialize for DSOUND and ASIO platforms (by Robin Davies)

v3.0.1: (22 March 2004)
- bug fix in Windows DirectSound support for cards with output only

v3.0: (11 March 2004)
- added Linux Jack audio server support
- new multi-api support by subclassing all apis and making rtaudio a controller class
- added over/underload check to Mac OS X support
- new scheme for blocking functionality in callback-based apis (CoreAudio, ASIO, and JACK)
- removed multiple stream support (all stream identifier arguments removed)
- various style and name changes to conform with standard C++ practice

v2.1.1: (24 October 2002)
- bug fix in duplex for Mac OS X and Windows ASIO code
- duplex example change in tutorial 

v2.1: (7 October 2002)
- added Mac OS X CoreAudio support
- added Windows ASIO support
- API change to getDeviceInfo(): device argument must be an integer between 1 - getDeviceCount().
- "configure" support added for unix systems
- adopted MIT-like license
- various internal structural changes and bug fixes

v2.01: (27 April 2002)
- Windows destructor bug fix when no devices available
- RtAudioError class renamed to RtError
- Preprocessor definitions changed slightly (i.e. __LINUX_OSS_ to __LINUX_OSS__) to conform with new Synthesis ToolKit distribution

v2.0: (22 January 2002)
- first release of new independent class


RtAudio - a set of C++ classes which provide a common API for realtime audio input/output across Linux (native ALSA, JACK, PulseAudio, and OSS), Macintosh OS X (CoreAudio and JACK), and Windows (DirectSound, ASIO and WASAPI) operating systems.

By <PERSON>, 2001-2023.

To configure and compile (on Unix systems and MinGW):

1. Unpack the RtAudio distribution (tar -xzf rtaudio-x.x.tar.gz).

2. From within the directory containing this file, run configure:

   ./configure

   If you checked out the code from git, just run "autogen.sh".

3. Typing "make" will compile static and shared libraries, as well as the example programs in the "tests/" directory.

A few options can be passed to configure (or the autogen.sh script), including:

  --enable-debug = enable various debug output
  --with-alsa = choose native ALSA API support (linux only)
  --with-pulse = choose native PulseAudio API support (linux only)
  --with-oss = choose OSS API support (unixes)
  --with-jack = choose JACK server support (linux or Macintosh OS-X)
  --with-core = choose CoreAudio API support (Macintosh OS-X only)
  --with-asio = choose ASIO API support (windows only)
  --with-wasapi = choose Windows Audio System API support (windows only)
  --with-ds = choose DirectSound API support (windows only)

Typing "./configure --help" will display all the available options.  Note that you can provide more than one "--with-" flag to the configure script to enable multiple API support.

If you wish to use a different compiler than that selected by configure, specify that compiler in the command line (e.g. to use CC):

  ./configure CXX=CC


CMAKE USAGE:

CMake support is provided via the CMakeLists.txt files.  Assuming you have CMake installed on your system, a typical usage would involve the following steps (from within the parent distribution directory):

mkdir _build_
cd _build_
cmake <path to CMakeLists.txt usually two dots> <options> e.g. cmake .. -DRTAUDIO_WINDOWS_WASAPI=ON


VCPKG USAGE:

You can build and install rtaudio using [vcpkg](https://github.com/Microsoft/vcpkg/) dependency manager:

git clone https://github.com/Microsoft/vcpkg.git
cd vcpkg
./bootstrap-vcpkg.sh  # ./bootstrap-vcpkg.bat for Windows
./vcpkg integrate install
./vcpkg install rtaudio

The rtaudio port in vcpkg is kept up to date by Microsoft team members and community contributors. If the version is out of date, please [create an issue or pull request](https://github.com/Microsoft/vcpkg) on the vcpkg repository.


WINDOWS:

All Windows audio APIs in RtAudio compile with either the MinGW compiler (tested with latest tdm64-gcc-4.8.1) or MS Visual Studio.

Visual C++ 6.0 project files (very old) are included for the test programs in the /tests/Windows/ directory.  These projects compile API support for ASIO, WASAPI and DirectSound.


LINUX OSS:

The OSS API support in RtAudio has not been tested for many years.  I'm not even sure there are OSS drivers supporting recent linux kernels.  In all likelihood, the OSS API code in RtAudio will disappear within the next year or two (if you don't want this to happen, let me know).

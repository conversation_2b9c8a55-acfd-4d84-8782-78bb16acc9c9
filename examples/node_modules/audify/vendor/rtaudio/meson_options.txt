# Audio Backends
option('jack', type : 'feature', value : 'auto', description: 'Build with JACK Backend')
option('alsa', type : 'feature', value : 'auto', description: 'Build with ALSA Backend')
option('pulse', type : 'feature', value : 'auto', description: 'Build with Pulseaudio Backend')
option('oss', type : 'boolean', value : 'false', description: 'Build with OSS Backend')
option('core', type : 'feature', value : 'auto', description: 'Build with CoreAudio Backend')
option('dsound', type : 'feature', value : 'auto', description: 'Build with DirectSound Backend')
option('asio', type : 'feature', value : 'auto', description: 'Build with ASIO Backend')
option('wasapi', type : 'feature', value : 'auto', description: 'Build with WASAPI Backend')

#
option('docs', type : 'boolean', value : 'false', description: 'Generate API documentation')
option('install_docs', type : 'boolean', value : 'false', description: 'Install API documentation')

set(OPUS_VERSION @PROJECT_VERSION@)
set(OPUS_VERSION_STRING @PROJECT_VERSION@)
set(OPUS_VERSION_MAJOR @PROJECT_VERSION_MAJOR@)
set(OPUS_VERSION_MINOR @PROJECT_VERSION_MINOR@)
set(OPUS_VERSION_PATCH @PROJECT_VERSION_PATCH@)

@PACKAGE_INIT@

set_and_check(OPUS_INCLUDE_DIR "@PACKAGE_INCLUDE_INSTALL_DIR@")
set(OPUS_INCLUDE_DIR ${OPUS_INCLUDE_DIR};${OPUS_INCLUDE_DIR}/opus)
set(OPUS_INCLUDE_DIRS "@PACKAGE_INCLUDE_INSTALL_DIR@;@PACKAGE_INCLUDE_INSTALL_DIR@/opus")

include(${CMAKE_CURRENT_LIST_DIR}/OpusTargets.cmake)

set(OPUS_LIBRARY Opus::opus)
set(OPUS_LIBRARIES Opus::opus)

check_required_components(Opus)

set(OPUS_FOUND 1)

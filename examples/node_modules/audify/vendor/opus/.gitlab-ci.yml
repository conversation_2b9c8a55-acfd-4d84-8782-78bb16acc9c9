include:
  - template: 'Workflows/Branch-Pipelines.gitlab-ci.yml'

# https://docs.gitlab.com/ee/ci/yaml/workflow.html#switch-between-branch-pipelines-and-merge-request-pipelines
workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH && $CI_OPEN_MERGE_REQUESTS && $CI_PIPELINE_SOURCE == "push"
      when: never
    - if: $CI_COMMIT_BRANCH
    - if: $CI_COMMIT_TAG

default:
  tags:
    - docker
  image: 'debian:bookworm-slim'

# https://docs.gitlab.com/ee/ci/yaml/yaml_optimization.html#reference-tags
.snippets:
  git_prep:
    # Make sure we have a recent annotated tag, otherwise meson/get-version.py
    # might fail later (e.g. shallow clone without enough history) or return
    # a bogus version based on a much older tag. This can happen in merge request
    # pipelines from a personal fork, as the fork might not have the latest
    # upstream tags if it has been forked a long time ago. Also affects the
    # git version picked up by autotools and cmake, not just meson.
    - git fetch https://gitlab.xiph.org/xiph/opus.git refs/tags/v1.4:refs/tags/v1.4
    - git describe

whitespace:
  stage: test
  before_script:
    - apt-get update &&
      apt-get install -y git
  script:
    - git diff-tree --check origin/main HEAD

# Make sure commits are GPG signed
ci-fairy:
  stage: test
  script:
    - apt update
    - apt install -y python3-pip git
    - pip3 install --break-system-packages git+https://gitlab.freedesktop.org/freedesktop/ci-templates@7811ba9814a3bad379377241c6c6b62d78b20eac
    - echo Checking commits $CI_FAIRY_BASE_COMMIT..HEAD
    - ci-fairy check-commits --gpg-signed-commit $CI_FAIRY_BASE_COMMIT..HEAD
  tags:
    - 'docker'
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      variables:
        CI_FAIRY_BASE_COMMIT: $CI_MERGE_REQUEST_DIFF_BASE_SHA
    - if: $CI_PIPELINE_SOURCE != "merge_request_event"
      variables:
        CI_FAIRY_BASE_COMMIT: 'HEAD^1'

autoconf:
  stage: build
  tags:
    - avx2
  before_script:
    - apt-get update &&
      apt-get install -y zip doxygen git automake libtool make wget
    - !reference [.snippets, git_prep]
  script:
    - ./autogen.sh
    - CFLAGS="-mavx -mfma -mavx2 -O2 -ffast-math" ./configure --enable-float-approx --enable-dred --enable-osce
    - make -j16
    - DISTCHECK_CONFIGURE_FLAGS="--enable-float-approx --enable-dred --enable-osce CFLAGS='-mavx -mfma -mavx2 -O2'" make distcheck -j16
  cache:
    paths:
      - "src/*.o"
      - "src/.libs/*.o"
      - "silk/*.o"
      - "silk/.libs/*.o"
      - "celt/*.o"
      - "celt/.libs/*.o"

cmake:
  stage: build
  tags:
    - avx2
  before_script:
    - apt-get update &&
      apt-get install -y cmake ninja-build git automake libtool wget
    - !reference [.snippets, git_prep]
  script:
    - ./autogen.sh
    - mkdir build
    - cmake -S . -B build -G "Ninja" -DCMAKE_BUILD_TYPE=Release -DOPUS_BUILD_PROGRAMS=ON -DBUILD_TESTING=ON -DOPUS_FAST_MATH=ON -DOPUS_FLOAT_APPROX=ON -DOPUS_DRED=ON -DOPUS_OSCE=ON -DOPUS_X86_PRESUME_AVX2=ON
    - cmake --build build
    - cd build && ctest --output-on-failure -j 16

.meson:
  image: 'debian:bookworm-slim'
  stage: build
  before_script:
    - apt-get update &&
      apt-get install -y ninja-build doxygen meson git automake libtool wget
    - !reference [.snippets, git_prep]
  script:
    - ./autogen.sh
    - mkdir builddir
    - meson setup -Ddeep-plc=enabled -Dosce=enabled -Ddred=enabled -Dtests=enabled -Ddocs=enabled -Dbuildtype=release builddir
    - meson compile -C builddir
    - meson test -C builddir
    #- meson dist --no-tests -C builddir

meson x86_64:
  extends: '.meson'
  tags:
    - avx2
  variables:
    MESON_EXTRA_ARGS: '--werror'

meson arm64:
  extends: '.meson'
  tags:
    - 'gstreamer-arm64-linux-docker'
  variables:
    # arm64 build has a compiler warning still, so let's not use --werror for now
    MESON_EXTRA_ARGS: '-Dwerror=false'

/* The standard CSS for doxygen */

body, table, div, p, dl {
        font-family: Lucida Grande, Verdana, Geneva, Arial, sans-serif;
        font-size: 13px;
        line-height: 1.3;
}

/* @group Heading Levels */

h1 {
        font-size: 150%;
}

.title {
        font-size: 150%;
        font-weight: bold;
        margin: 10px 2px;
}

h2 {
        font-size: 120%;
}

h3 {
        font-size: 100%;
}

dt {
        font-weight: bold;
}

div.multicol {
        -moz-column-gap: 1em;
        -webkit-column-gap: 1em;
        -moz-column-count: 3;
        -webkit-column-count: 3;
}

p.startli, p.startdd, p.starttd {
        margin-top: 2px;
}

p.endli {
        margin-bottom: 0px;
}

p.enddd {
        margin-bottom: 4px;
}

p.endtd {
        margin-bottom: 2px;
}

/* @end */

caption {
        font-weight: bold;
}

span.legend {
        font-size: 70%;
        text-align: center;
}

h3.version {
        font-size: 90%;
        text-align: center;
}

div.qindex, div.navtab{
        background-color: #F1F1F1;
        border: 1px solid #BDBDBD;
        text-align: center;
}

div.qindex, div.navpath {
        width: 100%;
        line-height: 140%;
}

div.navtab {
        margin-right: 15px;
}

/* @group Link Styling */

a {
        color: #646464;
        font-weight: normal;
        text-decoration: none;
}

.contents a:visited {
        color: #747474;
}

a:hover {
        text-decoration: underline;
}

a.qindex {
        font-weight: bold;
}

a.qindexHL {
        font-weight: bold;
        background-color: #B8B8B8;
        color: #ffffff;
        border: 1px double #A8A8A8;
}

.contents a.qindexHL:visited {
        color: #ffffff;
}

a.el {
        font-weight: bold;
}

a.elRef {
}

a.code, a.code:visited {
        color: #4665A2;
}

a.codeRef, a.codeRef:visited {
        color: #4665A2;
}

/* @end */

dl.el {
        margin-left: -1cm;
}

.fragment {
        font-family: monospace, fixed;
        font-size: 105%;
}

pre.fragment {
        border: 1px solid #D5D5D5;
        background-color: #FCFCFC;
        padding: 4px 6px;
        margin: 4px 8px 4px 2px;
        overflow: auto;
        word-wrap: break-word;
        font-size:  9pt;
        line-height: 125%;
}

div.ah {
        background-color: black;
        font-weight: bold;
        color: #ffffff;
        margin-bottom: 3px;
        margin-top: 3px;
        padding: 0.2em;
        border: solid thin #333;
        border-radius: 0.5em;
        -webkit-border-radius: .5em;
        -moz-border-radius: .5em;
        box-shadow: 2px 2px 3px #999;
        -webkit-box-shadow: 2px 2px 3px #999;
        -moz-box-shadow: rgba(0, 0, 0, 0.15) 2px 2px 2px;
        background-image: -webkit-gradient(linear, left top, left bottom, from(#eee), to(#000),color-stop(0.3, #444));
        background-image: -moz-linear-gradient(center top, #eee 0%, #444 40%, #000);
}

div.groupHeader {
        margin-left: 16px;
        margin-top: 12px;
        font-weight: bold;
}

div.groupText {
        margin-left: 16px;
        font-style: italic;
}

body {
        background-color: white;
        color: black;
        margin: 0;
}

div.contents {
        margin-top: 10px;
        margin-left: 8px;
        margin-right: 8px;
}

td.indexkey {
        background-color: #F1F1F1;
        font-weight: bold;
        border: 1px solid #D5D5D5;
        margin: 2px 0px 2px 0;
        padding: 2px 10px;
        white-space: nowrap;
        vertical-align: top;
}

td.indexvalue {
        background-color: #F1F1F1;
        border: 1px solid #D5D5D5;
        padding: 2px 10px;
        margin: 2px 0px;
}

tr.memlist {
        background-color: #F2F2F2;
}

p.formulaDsp {
        text-align: center;
}

img.formulaDsp {

}

img.formulaInl {
        vertical-align: middle;
}

div.center {
        text-align: center;
        margin-top: 0px;
        margin-bottom: 0px;
        padding: 0px;
}

div.center img {
        border: 0px;
}

address.footer {
        text-align: right;
        padding-right: 12px;
}

img.footer {
        border: 0px;
        vertical-align: middle;
}

/* @group Code Colorization */

span.keyword {
        color: #008000
}

span.keywordtype {
        color: #604020
}

span.keywordflow {
        color: #e08000
}

span.comment {
        color: #800000
}

span.preprocessor {
        color: #806020
}

span.stringliteral {
        color: #002080
}

span.charliteral {
        color: #008080
}

span.vhdldigit {
        color: #ff00ff
}

span.vhdlchar {
        color: #000000
}

span.vhdlkeyword {
        color: #700070
}

span.vhdllogic {
        color: #ff0000
}

blockquote {
        background-color: #F9F9F9;
        border-left: 2px solid #B8B8B8;
        margin: 0 24px 0 4px;
        padding: 0 12px 0 16px;
}

/* @end */

/*
.search {
        color: #003399;
        font-weight: bold;
}

form.search {
        margin-bottom: 0px;
        margin-top: 0px;
}

input.search {
        font-size: 75%;
        color: #000080;
        font-weight: normal;
        background-color: #e8eef2;
}
*/

td.tiny {
        font-size: 75%;
}

.dirtab {
        padding: 4px;
        border-collapse: collapse;
        border: 1px solid #BDBDBD;
}

th.dirtab {
        background: #F1F1F1;
        font-weight: bold;
}

hr {
        height: 0px;
        border: none;
        border-top: 1px solid #7A7A7A;
}

hr.footer {
        height: 1px;
}

/* @group Member Descriptions */

table.memberdecls {
        border-spacing: 0px;
        padding: 0px;
}

.mdescLeft, .mdescRight,
.memItemLeft, .memItemRight,
.memTemplItemLeft, .memTemplItemRight, .memTemplParams {
        background-color: #FAFAFA;
        border: none;
        margin: 4px;
        padding: 1px 0 0 8px;
}

.mdescLeft, .mdescRight {
        padding: 0px 8px 4px 8px;
        color: #555;
}

.memItemLeft, .memItemRight, .memTemplParams {
        border-top: 1px solid #D5D5D5;
}

.memItemLeft, .memTemplItemLeft {
        white-space: nowrap;
}

.memItemRight {
        width: 100%;
}

.memTemplParams {
        color: #747474;
        white-space: nowrap;
}

/* @end */

/* @group Member Details */

/* Styles for detailed member documentation */

.memtemplate {
        font-size: 80%;
        color: #747474;
        font-weight: normal;
        margin-left: 9px;
}

.memnav {
        background-color: #F1F1F1;
        border: 1px solid #BDBDBD;
        text-align: center;
        margin: 2px;
        margin-right: 15px;
        padding: 2px;
}

.mempage {
        width: 100%;
}

.memitem {
        padding: 0;
        margin-bottom: 10px;
        margin-right: 5px;
}

.memname {
        white-space: nowrap;
        font-weight: bold;
        margin-left: 6px;
}

.memproto, dl.reflist dt {
        border-top: 1px solid #C0C0C0;
        border-left: 1px solid #C0C0C0;
        border-right: 1px solid #C0C0C0;
        padding: 6px 0px 6px 0px;
        color: #3D3D3D;
        font-weight: bold;
        text-shadow: 0px 1px 1px rgba(255, 255, 255, 0.9);
        /* opera specific markup */
        box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.15);
        border-top-right-radius: 8px;
        border-top-left-radius: 8px;
        /* firefox specific markup */
        -moz-box-shadow: rgba(0, 0, 0, 0.15) 5px 5px 5px;
        -moz-border-radius-topright: 8px;
        -moz-border-radius-topleft: 8px;
        /* webkit specific markup */
        -webkit-box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.15);
        -webkit-border-top-right-radius: 8px;
        -webkit-border-top-left-radius: 8px;
        background-image:url('nav_f.png');
        background-repeat:repeat-x;
        background-color: #EAEAEA;

}

.memdoc, dl.reflist dd {
        border-bottom: 1px solid #C0C0C0;
        border-left: 1px solid #C0C0C0;
        border-right: 1px solid #C0C0C0;
        padding: 2px 5px;
        background-color: #FCFCFC;
        border-top-width: 0;
        /* opera specific markup */
        border-bottom-left-radius: 8px;
        border-bottom-right-radius: 8px;
        box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.15);
        /* firefox specific markup */
        -moz-border-radius-bottomleft: 8px;
        -moz-border-radius-bottomright: 8px;
        -moz-box-shadow: rgba(0, 0, 0, 0.15) 5px 5px 5px;
        background-image: -moz-linear-gradient(center top, #FFFFFF 0%, #FFFFFF 60%, #F9F9F9 95%, #F2F2F2);
        /* webkit specific markup */
        -webkit-border-bottom-left-radius: 8px;
        -webkit-border-bottom-right-radius: 8px;
        -webkit-box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.15);
        background-image: -webkit-gradient(linear,center top,center bottom,from(#FFFFFF), color-stop(0.6,#FFFFFF), color-stop(0.60,#FFFFFF), color-stop(0.95,#F9F9F9), to(#F2F2F2));
}

dl.reflist dt {
        padding: 5px;
}

dl.reflist dd {
        margin: 0px 0px 10px 0px;
        padding: 5px;
}

.paramkey {
        text-align: right;
}

.paramtype {
        white-space: nowrap;
}

.paramname {
        color: #602020;
        white-space: nowrap;
}
.paramname em {
        font-style: normal;
}

.params, .retval, .exception, .tparams {
        border-spacing: 6px 2px;
}

.params .paramname, .retval .paramname {
        font-weight: bold;
        vertical-align: top;
}

.params .paramtype {
        font-style: italic;
        vertical-align: top;
}

.params .paramdir {
        font-family: "courier new",courier,monospace;
        vertical-align: top;
}




/* @end */

/* @group Directory (tree) */

/* for the tree view */

.ftvtree {
        font-family: sans-serif;
        margin: 0px;
}

/* these are for tree view when used as main index */

.directory {
        font-size: 9pt;
        font-weight: bold;
        margin: 5px;
}

.directory h3 {
        margin: 0px;
        margin-top: 1em;
        font-size: 11pt;
}

/*
The following two styles can be used to replace the root node title
with an image of your choice.  Simply uncomment the next two styles,
specify the name of your image and be sure to set 'height' to the
proper pixel height of your image.
*/

/*
.directory h3.swap {
        height: 61px;
        background-repeat: no-repeat;
        background-image: url("yourimage.gif");
}
.directory h3.swap span {
        display: none;
}
*/

.directory > h3 {
        margin-top: 0;
}

.directory p {
        margin: 0px;
        white-space: nowrap;
}

.directory div {
        display: none;
        margin: 0px;
}

.directory img {
        vertical-align: -30%;
}

/* these are for tree view when not used as main index */

.directory-alt {
        font-size: 100%;
        font-weight: bold;
}

.directory-alt h3 {
        margin: 0px;
        margin-top: 1em;
        font-size: 11pt;
}

.directory-alt > h3 {
        margin-top: 0;
}

.directory-alt p {
        margin: 0px;
        white-space: nowrap;
}

.directory-alt div {
        display: none;
        margin: 0px;
}

.directory-alt img {
        vertical-align: -30%;
}

/* @end */

div.dynheader {
        margin-top: 8px;
}

address {
        font-style: normal;
        color: #464646;
}

table.doxtable {
        border-collapse:collapse;
        margin-top: 4px;
        margin-bottom: 4px;
}

table.doxtable td, table.doxtable th {
        border: 1px solid #4A4A4A;
        padding: 3px 7px 2px;
}

table.doxtable th {
        background-color: #5B5B5B;
        color: #FFFFFF;
        font-size: 110%;
        padding-bottom: 4px;
        padding-top: 5px;
}

table.fieldtable {
        width: 100%;
        margin-bottom: 10px;
        border: 1px solid #C0C0C0;
        border-spacing: 0px;
        -moz-border-radius: 4px;
        -webkit-border-radius: 4px;
        border-radius: 4px;
        -moz-box-shadow: rgba(0, 0, 0, 0.15) 2px 2px 2px;
        -webkit-box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.15);
        box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.15);
}

.fieldtable td, .fieldtable th {
        padding: 3px 7px 2px;
}

.fieldtable td.fieldtype, .fieldtable td.fieldname {
        white-space: nowrap;
        border-right: 1px solid #C0C0C0;
        border-bottom: 1px solid #C0C0C0;
        vertical-align: top;
}

.fieldtable td.fielddoc {
        border-bottom: 1px solid #C0C0C0;
        width: 100%;
}

.fieldtable tr:last-child td {
        border-bottom: none;
}

.fieldtable th {
        background-image:url('nav_f.png');
        background-repeat:repeat-x;
        background-color: #EAEAEA;
        font-size: 90%;
        color: #3D3D3D;
        padding-bottom: 4px;
        padding-top: 5px;
        text-align:left;
        -moz-border-radius-topleft: 4px;
        -moz-border-radius-topright: 4px;
        -webkit-border-top-left-radius: 4px;
        -webkit-border-top-right-radius: 4px;
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        border-bottom: 1px solid #C0C0C0;
}


.tabsearch {
        top: 0px;
        left: 10px;
        height: 36px;
        background-image: url('tab_b.png');
        z-index: 101;
        overflow: hidden;
        font-size: 13px;
}

.navpath ul
{
        font-size: 11px;
        background-image:url('tab_b.png');
        background-repeat:repeat-x;
        height:30px;
        line-height:30px;
        color:#ABABAB;
        border:solid 1px #D3D3D3;
        overflow:hidden;
        margin:0px;
        padding:0px;
}

.navpath li
{
        list-style-type:none;
        float:left;
        padding-left:10px;
        padding-right:15px;
        background-image:url('bc_s.png');
        background-repeat:no-repeat;
        background-position:right;
        color:#595959;
}

.navpath li.navelem a
{
        height:32px;
        display:block;
        text-decoration: none;
        outline: none;
}

.navpath li.navelem a:hover
{
        color:#929292;
}

.navpath li.footer
{
        list-style-type:none;
        float:right;
        padding-left:10px;
        padding-right:15px;
        background-image:none;
        background-repeat:no-repeat;
        background-position:right;
        color:#595959;
        font-size: 8pt;
}


div.summary
{
        float: right;
        font-size: 8pt;
        padding-right: 5px;
        width: 50%;
        text-align: right;
}

div.summary a
{
        white-space: nowrap;
}

div.ingroups
{
        margin-left: 5px;
        font-size: 8pt;
        padding-left: 5px;
        width: 50%;
        text-align: left;
}

div.ingroups a
{
        white-space: nowrap;
}

div.header
{
        background-image:url('nav_h.png');
        background-repeat:repeat-x;
        background-color: #FAFAFA;
        margin:  0px;
        border-bottom: 1px solid #D5D5D5;
}

div.headertitle
{
        padding: 5px 5px 5px 7px;
}

dl
{
        padding: 0 0 0 10px;
}

/* dl.note, dl.warning, dl.attention, dl.pre, dl.post, dl.invariant, dl.deprecated, dl.todo, dl.test, dl.bug */
dl.section
{
        border-left:4px solid;
        padding: 0 0 0 6px;
}

dl.note
{
        border-color: #D0C000;
}

dl.warning, dl.attention
{
        border-color: #FF0000;
}

dl.pre, dl.post, dl.invariant
{
        border-color: #00D000;
}

dl.deprecated
{
        border-color: #505050;
}

dl.todo
{
        border-color: #00C0E0;
}

dl.test
{
        border-color: #3030E0;
}

dl.bug
{
        border-color: #C08050;
}

dl.section dd {
        margin-bottom: 6px;
}


#projectlogo
{
        text-align: center;
        vertical-align: bottom;
        border-collapse: separate;
}

#projectlogo img
{
        border: 0px none;
}

#projectname
{
        font: 300% Tahoma, Arial,sans-serif;
        margin: 0px;
        padding: 2px 0px;
}

#projectbrief
{
        font: 120% Tahoma, Arial,sans-serif;
        margin: 0px;
        padding: 0px;
}

#projectnumber
{
        font: 100% Tahoma, Arial,sans-serif;
        margin: 0px;
        padding: 0px;
}

#titlearea
{
        padding: 0px;
        margin: 0px;
        width: 100%;
        border-bottom: 1px solid #848484;
}

.image
{
        text-align: center;
}

.dotgraph
{
        text-align: center;
}

.mscgraph
{
        text-align: center;
}

.caption
{
        font-weight: bold;
}

div.zoom
{
        border: 1px solid #AFAFAF;
}

dl.citelist {
        margin-bottom:50px;
}

dl.citelist dt {
        color:#545454;
        float:left;
        font-weight:bold;
        margin-right:10px;
        padding:5px;
}

dl.citelist dd {
        margin:2px 0;
        padding:5px 0;
}

div.toc {
        padding: 14px 25px;
        background-color: #F7F7F7;
        border: 1px solid #E3E3E3;
        border-radius: 7px 7px 7px 7px;
        float: right;
        height: auto;
        margin: 0 20px 10px 10px;
        width: 200px;
}

div.toc li {
        background: url("bdwn.png") no-repeat scroll 0 5px transparent;
        font: 10px/1.2 Verdana,DejaVu Sans,Geneva,sans-serif;
        margin-top: 5px;
        padding-left: 10px;
        padding-top: 2px;
}

div.toc h3 {
        font: bold 12px/1.2 Arial,FreeSans,sans-serif;
        color: #747474;
        border-bottom: 0 none;
        margin: 0;
}

div.toc ul {
        list-style: none outside none;
        border: medium none;
        padding: 0px;
}

div.toc li.level1 {
        margin-left: 0px;
}

div.toc li.level2 {
        margin-left: 15px;
}

div.toc li.level3 {
        margin-left: 30px;
}

div.toc li.level4 {
        margin-left: 45px;
}


@media print
{
  #top { display: none; }
  #side-nav { display: none; }
  #nav-path { display: none; }
  body { overflow:visible; }
  h1, h2, h3, h4, h5, h6 { page-break-after: avoid; }
  .summary { display: none; }
  .memitem { page-break-inside: avoid; }
  #doc-content
  {
    margin-left:0 !important;
    height:auto !important;
    width:auto !important;
    overflow:inherit;
    display:inline;
  }
  pre.fragment
  {
    overflow: visible;
    text-wrap: unrestricted;
    white-space: -moz-pre-wrap; /* Moz */
    white-space: -pre-wrap; /* Opera 4-6 */
    white-space: -o-pre-wrap; /* Opera 7 */
    white-space: pre-wrap; /* CSS3  */
    word-wrap: break-word; /* IE 5.5+ */
  }
}

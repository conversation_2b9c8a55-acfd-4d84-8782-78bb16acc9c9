/* Normal links */
.normal_link a:link
{
    color : yellow;
}
.normal_link a:visited
{
    color : green;
}

/* Boxes */
.pre
{
	white-space: pre;           /* CSS 2.0 */
	white-space: pre-wrap;      /* CSS 2.1 */
	white-space: -pre-wrap;     /* Opera 4-6 */
	white-space: -o-pre-wrap;   /* Opera 7 */
	white-space: -moz-pre-wrap; /* Mozilla */
	white-space: -hp-pre-wrap;  /* HP Printers */
	word-wrap  : break-word;    /* IE 5+ */
}

.title_box
{
    width            : 470px;
    height           : 70px;
    margin           : 2px 50px 2px 2px;
    padding          : 10px;
    border           : 1px solid black;
    background-color : #666666;
    white-space      : pre;
    float            : left;
    text-align       : center;
    color            : #C0C0C0;
    font-size        : 50pt;
    font-style       : italic;
}

.subindex_box
{
    margin           : 5px;
    padding          : 14px 22px;
    border           : 1px solid black;
    background-color : #778877;
    float            : left;
    text-align       : center;
    color            : #115555;
    font-size        : 32pt;
}

.frame_box
{
    margin           : 10px;
    padding          : 10px;
    border           : 0px;
    background-color : #084040;
    text-align       : left;
    color            : #C0C0C0;
    font-family      : monospace;
}

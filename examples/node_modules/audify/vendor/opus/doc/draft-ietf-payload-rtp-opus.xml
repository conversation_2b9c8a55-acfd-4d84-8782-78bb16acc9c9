<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE rfc SYSTEM "rfc2629.dtd" [
<!ENTITY rfc2119 PUBLIC '' 'http://xml.resource.org/public/rfc/bibxml/reference.RFC.2119.xml'>
<!ENTITY rfc3389 PUBLIC '' 'http://xml.resource.org/public/rfc/bibxml/reference.RFC.3389.xml'>
<!ENTITY rfc3550 PUBLIC '' 'http://xml.resource.org/public/rfc/bibxml/reference.RFC.3550.xml'>
<!ENTITY rfc3711 PUBLIC '' 'http://xml.resource.org/public/rfc/bibxml/reference.RFC.3711.xml'>
<!ENTITY rfc3551 PUBLIC '' 'http://xml.resource.org/public/rfc/bibxml/reference.RFC.3551.xml'>
<!ENTITY rfc6838 PUBLIC '' 'http://xml.resource.org/public/rfc/bibxml/reference.RFC.6838.xml'>
<!ENTITY rfc4855 PUBLIC '' 'http://xml.resource.org/public/rfc/bibxml/reference.RFC.4855.xml'>
<!ENTITY rfc4566 PUBLIC '' 'http://xml.resource.org/public/rfc/bibxml/reference.RFC.4566.xml'>
<!ENTITY rfc4585 PUBLIC '' 'http://xml.resource.org/public/rfc/bibxml/reference.RFC.4585.xml'>
<!ENTITY rfc3264 PUBLIC '' 'http://xml.resource.org/public/rfc/bibxml/reference.RFC.3264.xml'>
<!ENTITY rfc2974 PUBLIC '' 'http://xml.resource.org/public/rfc/bibxml/reference.RFC.2974.xml'>
<!ENTITY rfc2326 PUBLIC '' 'http://xml.resource.org/public/rfc/bibxml/reference.RFC.2326.xml'>
<!ENTITY rfc3555 PUBLIC '' 'http://xml.resource.org/public/rfc/bibxml/reference.RFC.3555.xml'>
<!ENTITY rfc5124 PUBLIC '' 'http://xml.resource.org/public/rfc/bibxml/reference.RFC.5124.xml'>
<!ENTITY rfc5405 PUBLIC '' 'http://xml.resource.org/public/rfc/bibxml/reference.RFC.5405.xml'>
<!ENTITY rfc5576 PUBLIC '' 'http://xml.resource.org/public/rfc/bibxml/reference.RFC.5576.xml'>
<!ENTITY rfc6562 PUBLIC '' 'http://xml.resource.org/public/rfc/bibxml/reference.RFC.6562.xml'>
<!ENTITY rfc6716 PUBLIC '' 'http://xml.resource.org/public/rfc/bibxml/reference.RFC.6716.xml'>
<!ENTITY rfc7202 PUBLIC '' 'http://xml.resource.org/public/rfc/bibxml/reference.RFC.7202.xml'>
<!ENTITY nbsp "&#160;">
  ]>

  <rfc category="std" ipr="trust200902" docName="draft-ietf-payload-rtp-opus-11">
<?xml-stylesheet type='text/xsl' href='rfc2629.xslt' ?>

<?rfc strict="yes" ?>
<?rfc toc="yes" ?>
<?rfc tocdepth="3" ?>
<?rfc tocappendix='no' ?>
<?rfc tocindent='yes' ?>
<?rfc symrefs="yes" ?>
<?rfc sortrefs="yes" ?>
<?rfc compact="no" ?>
<?rfc subcompact="yes" ?>
<?rfc iprnotified="yes" ?>

  <front>
    <title abbrev="RTP Payload Format for Opus">
      RTP Payload Format for the Opus Speech and Audio Codec
    </title>

    <author fullname="Julian Spittka" initials="J." surname="Spittka">
      <address>
        <email><EMAIL></email>
      </address>
    </author>

    <author initials='K.' surname='Vos' fullname='Koen Vos'>
      <organization>vocTone</organization>
      <address>
        <postal>
          <street></street>
          <code></code>
          <city></city>
          <region></region>
          <country></country>
        </postal>
        <email><EMAIL></email>
      </address>
    </author>

    <author initials="JM" surname="Valin" fullname="Jean-Marc Valin">
      <organization>Mozilla</organization>
      <address>
        <postal>
          <street>331 E. Evelyn Avenue</street>
          <city>Mountain View</city>
          <region>CA</region>
          <code>94041</code>
          <country>USA</country>
        </postal>
        <email><EMAIL></email>
      </address>
    </author>

    <date day='14' month='April' year='2015' />

    <abstract>
      <t>
        This document defines the Real-time Transport Protocol (RTP) payload
        format for packetization of Opus encoded
        speech and audio data necessary to integrate the codec in the
        most compatible way. It also provides an applicability statement
        for the use of Opus over RTP. Further, it describes media type registrations
        for the RTP payload format.
      </t>
    </abstract>
  </front>

  <middle>
    <section title='Introduction'>
      <t>
        Opus <xref target="RFC6716"/> is a speech and audio codec developed within the
        IETF Internet Wideband Audio Codec working group. The codec
        has a very low algorithmic delay and it
        is highly scalable in terms of audio bandwidth, bitrate, and
        complexity. Further, it provides different modes to efficiently encode speech signals
        as well as music signals, thus making it the codec of choice for
        various applications using the Internet or similar networks.
      </t>
      <t>
        This document defines the Real-time Transport Protocol (RTP)
        <xref target="RFC3550"/> payload format for packetization
        of Opus encoded speech and audio data necessary to
        integrate Opus in the
        most compatible way. It also provides an applicability statement
        for the use of Opus over RTP.
        Further, it describes media type registrations for
        the RTP payload format.
      </t>
    </section>

    <section title='Conventions, Definitions and Acronyms used in this document'>
      <t>The key words "MUST", "MUST NOT", "REQUIRED", "SHALL", "SHALL NOT",
      "SHOULD", "SHOULD NOT", "RECOMMENDED", "MAY", and "OPTIONAL" in this
      document are to be interpreted as described in <xref target="RFC2119"/>.</t>
      <t>
      <list style='hanging'>
          <t hangText="audio bandwidth:"> The range of audio frequecies being coded</t>
          <t hangText="CBR:"> Constant bitrate</t>
          <t hangText="CPU:"> Central Processing Unit</t>
          <t hangText="DTX:"> Discontinuous transmission</t>
          <t hangText="FEC:"> Forward error correction</t>
          <t hangText="IP:"> Internet Protocol</t>
          <t hangText="samples:"> Speech or audio samples (per channel)</t>
          <t hangText="SDP:"> Session Description Protocol</t>
          <t hangText="VBR:"> Variable bitrate</t>
      </list>
      </t>
        <t>
          Throughout this document, we refer to the following definitions:
        </t>
          <texttable anchor='bandwidth_definitions'>
            <ttcol align='center'>Abbreviation</ttcol>
            <ttcol align='center'>Name</ttcol>
            <ttcol align='center'>Audio Bandwidth (Hz)</ttcol>
            <ttcol align='center'>Sampling Rate (Hz)</ttcol>
            <c>NB</c>
            <c>Narrowband</c>
            <c>0 - 4000</c>
            <c>8000</c>

            <c>MB</c>
            <c>Mediumband</c>
            <c>0 - 6000</c>
            <c>12000</c>

            <c>WB</c>
            <c>Wideband</c>
            <c>0 - 8000</c>
            <c>16000</c>

            <c>SWB</c>
            <c>Super-wideband</c>
            <c>0 - 12000</c>
            <c>24000</c>

            <c>FB</c>
            <c>Fullband</c>
            <c>0 - 20000</c>
            <c>48000</c>

            <postamble>
              Audio bandwidth naming
            </postamble>
          </texttable>
    </section>

    <section title='Opus Codec'>
      <t>
        Opus encodes speech
        signals as well as general audio signals. Two different modes can be
        chosen, a voice mode or an audio mode, to allow the most efficient coding
        depending on the type of the input signal, the sampling frequency of the
        input signal, and the intended application.
      </t>

      <t>
        The voice mode allows efficient encoding of voice signals at lower bit
        rates while the audio mode is optimized for general audio signals at medium and
        higher bitrates.
      </t>

      <t>
        Opus is highly scalable in terms of audio
        bandwidth, bitrate, and complexity. Further, Opus allows
        transmitting stereo signals with in-band signaling in the bit-stream.
      </t>

      <section title='Network Bandwidth'>
          <t>
            Opus supports bitrates from 6&nbsp;kb/s to 510&nbsp;kb/s.
            The bitrate can be changed dynamically within that range.
            All
            other parameters being
            equal, higher bitrates result in higher audio quality.
          </t>
          <section title='Recommended Bitrate' anchor='bitrate_by_bandwidth'>
          <t>
            For a frame size of
            20&nbsp;ms, these
            are the bitrate "sweet spots" for Opus in various configurations:

          <list style="symbols">
            <t>8-12 kb/s for NB speech,</t>
            <t>16-20 kb/s for WB speech,</t>
            <t>28-40 kb/s for FB speech,</t>
            <t>48-64 kb/s for FB mono music, and</t>
            <t>64-128 kb/s for FB stereo music.</t>
          </list>
        </t>
      </section>
        <section title='Variable versus Constant Bitrate'  anchor='variable-vs-constant-bitrate'>
          <t>
            For the same average bitrate, variable bitrate (VBR) can achieve higher audio quality
            than constant bitrate (CBR). For the majority of voice transmission applications, VBR
            is the best choice. One reason for choosing CBR is the potential
            information leak that <spanx style='emph'>might</spanx> occur when encrypting the
            compressed stream. See <xref target="RFC6562"/> for guidelines on when VBR is
            appropriate for encrypted audio communications. In the case where an existing
            VBR stream needs to be converted to CBR for security reasons, then the Opus padding
            mechanism described in <xref target="RFC6716"/> is the RECOMMENDED way to achieve padding
            because the RTP padding bit is unencrypted.</t>

          <t>
            The bitrate can be adjusted at any point in time. To avoid congestion,
            the average bitrate SHOULD NOT exceed the available
            network bandwidth. If no target bitrate is specified, the bitrates specified in
            <xref target='bitrate_by_bandwidth'/> are RECOMMENDED.
          </t>

        </section>

        <section title='Discontinuous Transmission (DTX)'>

          <t>
            Opus can, as described in <xref target='variable-vs-constant-bitrate'/>,
            be operated with a variable bitrate. In that case, the encoder will
            automatically reduce the bitrate for certain input signals, like periods
            of silence. When using continuous transmission, it will reduce the
            bitrate when the characteristics of the input signal permit, but
            will never interrupt the transmission to the receiver. Therefore, the
            received signal will maintain the same high level of audio quality over the
            full duration of a transmission while minimizing the average bit
            rate over time.
          </t>

          <t>
            In cases where the bitrate of Opus needs to be reduced even
            further or in cases where only constant bitrate is available,
            the Opus encoder can use discontinuous
            transmission (DTX), where parts of the encoded signal that
            correspond to periods of silence in the input speech or audio signal
            are not transmitted to the receiver. A receiver can distinguish
            between DTX and packet loss by looking for gaps in the sequence
            number, as described by Section 4.1
            of&nbsp;<xref target="RFC3551"/>.
          </t>

          <t>
            On the receiving side, the non-transmitted parts will be handled by a
            frame loss concealment unit in the Opus decoder which generates a
            comfort noise signal to replace the non transmitted parts of the
            speech or audio signal. Use of <xref target="RFC3389"/> Comfort
            Noise (CN) with Opus is discouraged.
            The transmitter MUST drop whole frames only,
            based on the size of the last transmitted frame,
            to ensure successive RTP timestamps differ by a multiple of 120 and
            to allow the receiver to use whole frames for concealment.
          </t>

          <t>
            DTX can be used with both variable and constant bitrate.
            It will have a slightly lower speech or audio
            quality than continuous transmission. Therefore, using continuous
            transmission is RECOMMENDED unless constraints on available network bandwidth
            are severe.
          </t>

        </section>

        </section>

      <section title='Complexity'>

        <t>
          Complexity of the encoder can be scaled to optimize for CPU resources in real-time, mostly as
          a trade-off between audio quality and bitrate. Also, different modes of Opus have different complexity.
        </t>

      </section>

      <section title="Forward Error Correction (FEC)">

        <t>
          The voice mode of Opus allows for embedding "in-band" forward error correction (FEC)
          data into the Opus bit stream. This FEC scheme adds
          redundant information about the previous packet (N-1) to the current
          output packet N. For
          each frame, the encoder decides whether to use FEC based on (1) an
          externally-provided estimate of the channel's packet loss rate; (2) an
          externally-provided estimate of the channel's capacity; (3) the
          sensitivity of the audio or speech signal to packet loss; (4) whether
          the receiving decoder has indicated it can take advantage of "in-band"
          FEC information. The decision to send "in-band" FEC information is
          entirely controlled by the encoder and therefore no special precautions
          for the payload have to be taken.
        </t>

        <t>
          On the receiving side, the decoder can take advantage of this
          additional information when it loses a packet and the next packet
          is available.  In order to use the FEC data, the jitter buffer needs
          to provide access to payloads with the FEC data.  
          Instead of performing loss concealment for a missing packet, the
          receiver can then configure its decoder to decode the FEC data from the next packet.
        </t>

        <t>
          Any compliant Opus decoder is capable of ignoring
          FEC information when it is not needed, so encoding with FEC cannot cause
          interoperability problems.
          However, if FEC cannot be used on the receiving side, then FEC
          SHOULD NOT be used, as it leads to an inefficient usage of network
          resources. Decoder support for FEC SHOULD be indicated at the time a
          session is set up.
        </t>

      </section>

      <section title='Stereo Operation'>

        <t>
          Opus allows for transmission of stereo audio signals. This operation
          is signaled in-band in the Opus bit-stream and no special arrangement
          is needed in the payload format. An
          Opus decoder is capable of handling a stereo encoding, but an
          application might only be capable of consuming a single audio
          channel.
        </t>
        <t>
          If a decoder cannot take advantage of the benefits of a stereo signal
          this SHOULD be indicated at the time a session is set up. In that case
          the sending side SHOULD NOT send stereo signals as it leads to an
          inefficient usage of network resources.
        </t>

      </section>

    </section>

    <section title='Opus RTP Payload Format' anchor='opus-rtp-payload-format'>
      <t>The payload format for Opus consists of the RTP header and Opus payload
      data.</t>
      <section title='RTP Header Usage'>
        <t>The format of the RTP header is specified in <xref target="RFC3550"/>.
        The use of the fields of the RTP header by the Opus payload format is
        consistent with that specification.</t>

        <t>The payload length of Opus is an integer number of octets and
        therefore no padding is necessary. The payload MAY be padded by an
        integer number of octets according to <xref target="RFC3550"/>,
        although the Opus internal padding is preferred.</t>

        <t>The timestamp, sequence number, and marker bit (M) of the RTP header
        are used in accordance with Section 4.1
        of&nbsp;<xref target="RFC3551"/>.</t>

        <t>The RTP payload type for Opus is to be assigned dynamically.</t>

        <t>The receiving side MUST be prepared to receive duplicate RTP
        packets. The receiver MUST provide at most one of those payloads to the
        Opus decoder for decoding, and MUST discard the others.</t>

        <t>Opus supports 5 different audio bandwidths, which can be adjusted during
        a stream.
        The RTP timestamp is incremented with a 48000 Hz clock rate
        for all modes of Opus and all sampling rates.
        The unit
        for the timestamp is samples per single (mono) channel. The RTP timestamp corresponds to the
        sample time of the first encoded sample in the encoded frame.
        For data encoded with sampling rates other than 48000 Hz,
	the sampling rate has to be adjusted to 48000 Hz.</t>

      </section>

      <section title='Payload Structure'>
        <t>
          The Opus encoder can output encoded frames representing 2.5, 5, 10, 20,
          40, or 60&nbsp;ms of speech or audio data. Further, an arbitrary number of frames can be
          combined into a packet, up to a maximum packet duration representing
          120&nbsp;ms of speech or audio data. The grouping of one or more Opus
          frames into a single Opus packet is defined in Section&nbsp;3 of
          <xref target="RFC6716"/>. An RTP payload MUST contain exactly one
          Opus packet as defined by that document.
        </t>

        <t><xref target='payload-structure'/> shows the structure combined with the RTP header.</t>

        <figure anchor="payload-structure"
                title="Packet structure with RTP header">
          <artwork align="center">
            <![CDATA[
+----------+--------------+
|RTP Header| Opus Payload |
+----------+--------------+
           ]]>
          </artwork>
        </figure>

        <t>
          <xref target='opus-packetization'/> shows supported frame sizes in
          milliseconds of encoded speech or audio data for the speech and audio modes
          (Mode) and sampling rates (fs) of Opus and shows how the timestamp is
          incremented for packetization (ts incr). If the Opus encoder
          outputs multiple encoded frames into a single packet, the timestamp
          increment is the sum of the increments for the individual frames.
        </t>

        <texttable anchor='opus-packetization' title="Supported Opus frame
         sizes and timestamp increments marked with an o. Unsupported marked with an x.">
            <ttcol align='center'>Mode</ttcol>
            <ttcol align='center'>fs</ttcol>
            <ttcol align='center'>2.5</ttcol>
            <ttcol align='center'>5</ttcol>
            <ttcol align='center'>10</ttcol>
            <ttcol align='center'>20</ttcol>
            <ttcol align='center'>40</ttcol>
            <ttcol align='center'>60</ttcol>
            <c>ts incr</c>
            <c>all</c>
            <c>120</c>
            <c>240</c>
            <c>480</c>
            <c>960</c>
            <c>1920</c>
            <c>2880</c>
            <c>voice</c>
            <c>NB/MB/WB/SWB/FB</c>
            <c>x</c>
            <c>x</c>
            <c>o</c>
            <c>o</c>
            <c>o</c>
            <c>o</c>
            <c>audio</c>
            <c>NB/WB/SWB/FB</c>
            <c>o</c>
            <c>o</c>
            <c>o</c>
            <c>o</c>
            <c>x</c>
            <c>x</c>
          </texttable>

      </section>

    </section>

    <section title='Congestion Control'>

      <t>The target bitrate of Opus can be adjusted at any point in time, thus
      allowing efficient congestion control. Furthermore, the amount
      of encoded speech or audio data encoded in a
      single packet can be used for congestion control, since the transmission
      rate is inversely proportional to the packet duration. A lower packet
      transmission rate reduces the amount of header overhead, but at the same
      time increases latency and loss sensitivity, so it ought to be used with
      care.</t>

      <t>Since UDP does not provide congestion control, applications that use
      RTP over UDP SHOULD implement their own congestion control above the
      UDP layer <xref target="RFC5405"/>. Work in the rmcat working group
      <xref target="rmcat"/> describes the
      interactions and conceptual interfaces necessary between the application
      components that relate to congestion control, including the RTP layer,
      the higher-level media codec control layer, and the lower-level
      transport interface, as well as components dedicated to congestion
      control functions.</t>
    </section>

    <section title='IANA Considerations'>
      <t>One media subtype (audio/opus) has been defined and registered as
      described in the following section.</t>

      <section title='Opus Media Type Registration'>
        <t>Media type registration is done according to <xref
        target="RFC6838"/> and <xref target="RFC4855"/>.<vspace
        blankLines='1'/></t>

          <t>Type name: audio<vspace blankLines='1'/></t>
          <t>Subtype name: opus<vspace blankLines='1'/></t>

          <t>Required parameters:</t>
          <t><list style="hanging">
            <t hangText="rate:"> the RTP timestamp is incremented with a
            48000 Hz clock rate for all modes of Opus and all sampling
            rates. For data encoded with sampling rates other than 48000 Hz,
            the sampling rate has to be adjusted to 48000 Hz.
          </t>
          </list></t>

          <t>Optional parameters:</t>

          <t><list style="hanging">
            <t hangText="maxplaybackrate:">
              a hint about the maximum output sampling rate that the receiver is
              capable of rendering in Hz.
              The decoder MUST be capable of decoding
              any audio bandwidth but due to hardware limitations only signals
              up to the specified sampling rate can be played back. Sending signals
              with higher audio bandwidth results in higher than necessary network
              usage and encoding complexity, so an encoder SHOULD NOT encode
              frequencies above the audio bandwidth specified by maxplaybackrate.
              This parameter can take any value between 8000 and 48000, although
              commonly the value will match one of the Opus bandwidths
              (<xref target="bandwidth_definitions"/>).
              By default, the receiver is assumed to have no limitations, i.e. 48000.
              <vspace blankLines='1'/>
            </t>

            <t hangText="sprop-maxcapturerate:">
              a hint about the maximum input sampling rate that the sender is likely to produce.
              This is not a guarantee that the sender will never send any higher bandwidth
              (e.g. it could send a pre-recorded prompt that uses a higher bandwidth), but it
              indicates to the receiver that frequencies above this maximum can safely be discarded.
              This parameter is useful to avoid wasting receiver resources by operating the audio
              processing pipeline (e.g. echo cancellation) at a higher rate than necessary.
              This parameter can take any value between 8000 and 48000, although
              commonly the value will match one of the Opus bandwidths
              (<xref target="bandwidth_definitions"/>).
              By default, the sender is assumed to have no limitations, i.e. 48000.
              <vspace blankLines='1'/>
            </t>

            <t hangText="maxptime:"> the maximum duration of media represented
            by a packet (according to Section&nbsp;6 of
            <xref target="RFC4566"/>) that a decoder wants to receive, in
            milliseconds rounded up to the next full integer value.
            Possible values are 3, 5, 10, 20, 40, 60, or an arbitrary
            multiple of an Opus frame size rounded up to the next full integer
            value, up to a maximum value of 120, as
            defined in <xref target='opus-rtp-payload-format'/>. If no value is
              specified, the default is 120.
              <vspace blankLines='1'/></t>

            <t hangText="ptime:"> the preferred duration of media represented
            by a packet (according to Section&nbsp;6 of
            <xref target="RFC4566"/>) that a decoder wants to receive, in
            milliseconds rounded up to the next full integer value.
            Possible values are 3, 5, 10, 20, 40, 60, or an arbitrary
            multiple of an Opus frame size rounded up to the next full integer
            value, up to a maximum value of 120, as defined in <xref
            target='opus-rtp-payload-format'/>. If no value is
              specified, the default is 20. 
              <vspace blankLines='1'/></t>

            <t hangText="maxaveragebitrate:"> specifies the maximum average
            receive bitrate of a session in bits per second (b/s). The actual
            value of the bitrate can vary, as it is dependent on the
            characteristics of the media in a packet. Note that the maximum
            average bitrate MAY be modified dynamically during a session. Any
            positive integer is allowed, but values outside the range
            6000 to 510000 SHOULD be ignored. If no value is specified, the
            maximum value specified in <xref target='bitrate_by_bandwidth'/>
            for the corresponding mode of Opus and corresponding maxplaybackrate
            is the default.<vspace blankLines='1'/></t>

            <t hangText="stereo:">
              specifies whether the decoder prefers receiving stereo or mono signals.
              Possible values are 1 and 0 where 1 specifies that stereo signals are preferred,
              and 0 specifies that only mono signals are preferred.
              Independent of the stereo parameter every receiver MUST be able to receive and
              decode stereo signals but sending stereo signals to a receiver that signaled a
              preference for mono signals may result in higher than necessary network
              utilization and encoding complexity. If no value is specified,
              the default is 0 (mono).<vspace blankLines='1'/>
            </t>

            <t hangText="sprop-stereo:">
              specifies whether the sender is likely to produce stereo audio.
              Possible values are 1 and 0, where 1 specifies that stereo signals are likely to
              be sent, and 0 specifies that the sender will likely only send mono.
              This is not a guarantee that the sender will never send stereo audio
              (e.g. it could send a pre-recorded prompt that uses stereo), but it
              indicates to the receiver that the received signal can be safely downmixed to mono.
              This parameter is useful to avoid wasting receiver resources by operating the audio
              processing pipeline (e.g. echo cancellation) in stereo when not necessary.
              If no value is specified, the default is 0
              (mono).<vspace blankLines='1'/>
            </t>

            <t hangText="cbr:">
              specifies if the decoder prefers the use of a constant bitrate versus
              variable bitrate. Possible values are 1 and 0, where 1 specifies constant
              bitrate and 0 specifies variable bitrate. If no value is specified,
              the default is 0 (vbr). When cbr is 1, the maximum average bitrate can still
              change, e.g. to adapt to changing network conditions.<vspace blankLines='1'/>
            </t>

            <t hangText="useinbandfec:"> specifies that the decoder has the capability to
            take advantage of the Opus in-band FEC. Possible values are 1 and 0.
            Providing 0 when FEC cannot be used on the receiving side is
            RECOMMENDED. If no
            value is specified, useinbandfec is assumed to be 0.
            This parameter is only a preference and the receiver MUST be able to process
            packets that include FEC information, even if it means the FEC part is discarded.
            <vspace blankLines='1'/></t>

            <t hangText="usedtx:"> specifies if the decoder prefers the use of
            DTX. Possible values are 1 and 0. If no value is specified, the
            default is 0.<vspace blankLines='1'/></t>
          </list></t>

          <t>Encoding considerations:<vspace blankLines='1'/></t>
          <t><list style="hanging">
            <t>The Opus media type is framed and consists of binary data according
            to Section&nbsp;4.8 in <xref target="RFC6838"/>.</t>
          </list></t>

          <t>Security considerations: </t>
          <t><list style="hanging">
            <t>See <xref target='security-considerations'/> of this document.</t>
          </list></t>

          <t>Interoperability considerations: none<vspace blankLines='1'/></t>
	  <t>Published specification: RFC [XXXX]</t>
	  <t>Note to the RFC Editor: Replace [XXXX] with the number of the published
          RFC.<vspace blankLines='1'/></t>

          <t>Applications that use this media type: </t>
          <t><list style="hanging">
            <t>Any application that requires the transport of
            speech or audio data can use this media type. Some examples are,
            but not limited to, audio and video conferencing, Voice over IP,
            media streaming.</t>
          </list></t>

          <t>Fragment identifier considerations: N/A<vspace blankLines='1'/></t>

          <t>Person &amp; email address to contact for further information:</t>
          <t><list style="hanging">
            <t><NAME_EMAIL></t>
            <t>Jean-<NAME_EMAIL></t>
          </list></t>

          <t>Intended usage: COMMON<vspace blankLines='1'/></t>

          <t>Restrictions on usage:<vspace blankLines='1'/></t>

          <t><list style="hanging">
            <t>For transfer over RTP, the RTP payload format (<xref
            target='opus-rtp-payload-format'/> of this document) SHALL be
            used.</t>
          </list></t>

          <t>Author:</t>
          <t><list style="hanging">
            <t><NAME_EMAIL><vspace blankLines='1'/></t>
            <t><NAME_EMAIL><vspace blankLines='1'/></t>
            <t>Jean-<NAME_EMAIL><vspace blankLines='1'/></t>
          </list></t>

          <t> Change controller: IETF Payload Working Group delegated from the IESG</t>
      </section>
    </section>
    
    <section title='SDP Considerations'>
        <t>The information described in the media type specification has a
        specific mapping to fields in the Session Description Protocol (SDP)
        <xref target="RFC4566"/>, which is commonly used to describe RTP
        sessions. When SDP is used to specify sessions employing Opus,
        the mapping is as follows:</t>

        <t>
          <list style="symbols">
            <t>The media type ("audio") goes in SDP "m=" as the media name.</t>

            <t>The media subtype ("opus") goes in SDP "a=rtpmap" as the encoding
            name. The RTP clock rate in "a=rtpmap" MUST be 48000 and the number of
            channels MUST be 2.</t>

            <t>The OPTIONAL media type parameters "ptime" and "maxptime" are
            mapped to "a=ptime" and "a=maxptime" attributes, respectively, in the
            SDP.</t>

            <t>The OPTIONAL media type parameters "maxaveragebitrate",
            "maxplaybackrate", "stereo", "cbr", "useinbandfec", and
            "usedtx", when present, MUST be included in the "a=fmtp" attribute
            in the SDP, expressed as a media type string in the form of a
            semicolon-separated list of parameter=value pairs (e.g.,
            maxplaybackrate=48000). They MUST NOT be specified in an
            SSRC-specific "fmtp" source-level attribute (as defined in
            Section&nbsp;6.3 of&nbsp;<xref target="RFC5576"/>).</t>

            <t>The OPTIONAL media type parameters "sprop-maxcapturerate",
            and "sprop-stereo" MAY be mapped to the "a=fmtp" SDP attribute by
            copying them directly from the media type parameter string as part
            of the semicolon-separated list of parameter=value pairs (e.g.,
            sprop-stereo=1). These same OPTIONAL media type parameters MAY also
            be specified using an SSRC-specific "fmtp" source-level attribute
            as described in Section&nbsp;6.3 of&nbsp;<xref target="RFC5576"/>.
            They MAY be specified in both places, in which case the parameter
            in the source-level attribute overrides the one found on the
            "a=fmtp" line. The value of any parameter which is not specified in
            a source-level source attribute MUST be taken from the "a=fmtp"
            line, if it is present there.</t>

          </list>
        </t>

        <t>Below are some examples of SDP session descriptions for Opus:</t>

        <t>Example 1: Standard mono session with 48000 Hz clock rate</t>
          <figure>
            <artwork>
              <![CDATA[
    m=audio 54312 RTP/AVP 101
    a=rtpmap:101 opus/48000/2
              ]]>
            </artwork>
          </figure>


        <t>Example 2: 16000 Hz clock rate, maximum packet size of 40 ms,
        recommended packet size of 40 ms, maximum average bitrate of 20000 bps,
        prefers to receive stereo but only plans to send mono, FEC is desired,
        DTX is not desired</t>

        <figure>
          <artwork>
            <![CDATA[
    m=audio 54312 RTP/AVP 101
    a=rtpmap:101 opus/48000/2
    a=fmtp:101 maxplaybackrate=16000; sprop-maxcapturerate=16000;
    maxaveragebitrate=20000; stereo=1; useinbandfec=1; usedtx=0
    a=ptime:40
    a=maxptime:40
            ]]>
          </artwork>
        </figure>

        <t>Example 3: Two-way full-band stereo preferred</t>

        <figure>
          <artwork>
            <![CDATA[
    m=audio 54312 RTP/AVP 101
    a=rtpmap:101 opus/48000/2
    a=fmtp:101 stereo=1; sprop-stereo=1
            ]]>
          </artwork>
        </figure>


      <section title='SDP Offer/Answer Considerations'>

          <t>When using the offer-answer procedure described in <xref
          target="RFC3264"/> to negotiate the use of Opus, the following
          considerations apply:</t>

          <t><list style="symbols">

            <t>Opus supports several clock rates. For signaling purposes only
            the highest, i.e. 48000, is used. The actual clock rate of the
            corresponding media is signaled inside the payload and is not
            restricted by this payload format description. The decoder MUST be
            capable of decoding every received clock rate. An example
            is shown below:

            <figure>
              <artwork>
                <![CDATA[
    m=audio 54312 RTP/AVP 100
    a=rtpmap:100 opus/48000/2
                ]]>
              </artwork>
            </figure>
            </t>

            <t>The "ptime" and "maxptime" parameters are unidirectional
            receive-only parameters and typically will not compromise
            interoperability; however, some values might cause application
            performance to suffer. <xref
            target="RFC3264"/> defines the SDP offer-answer handling of the
            "ptime" parameter. The "maxptime" parameter MUST be handled in the
            same way.</t>

            <t>
              The "maxplaybackrate" parameter is a unidirectional receive-only
              parameter that reflects limitations of the local receiver. When
              sending to a single destination, a sender MUST NOT use an audio
              bandwidth higher than necessary to make full use of audio sampled at
              a sampling rate of "maxplaybackrate". Gateways or senders that
              are sending the same encoded audio to multiple destinations
              SHOULD NOT use an audio bandwidth higher than necessary to
              represent audio sampled at "maxplaybackrate", as this would lead
              to inefficient use of network resources.
              The "maxplaybackrate" parameter does not
              affect interoperability. Also, this parameter SHOULD NOT be used
              to adjust the audio bandwidth as a function of the bitrate, as this
              is the responsibility of the Opus encoder implementation.
            </t>

            <t>The "maxaveragebitrate" parameter is a unidirectional receive-only
            parameter that reflects limitations of the local receiver. The sender
            of the other side MUST NOT send with an average bitrate higher than
            "maxaveragebitrate" as it might overload the network and/or
            receiver. The "maxaveragebitrate" parameter typically will not
            compromise interoperability; however, some values might cause
            application performance to suffer, and ought to be set with
            care.</t>

            <t>The "sprop-maxcapturerate" and "sprop-stereo" parameters are
            unidirectional sender-only parameters that reflect limitations of
            the sender side.
            They allow the receiver to set up a reduced-complexity audio
            processing pipeline if the  sender is not planning to use the full
            range of Opus's capabilities.
            Neither "sprop-maxcapturerate" nor "sprop-stereo" affect
            interoperability and the receiver MUST be capable of receiving any signal.
            </t>

            <t>
              The "stereo" parameter is a unidirectional receive-only
              parameter. When sending to a single destination, a sender MUST
              NOT use stereo when "stereo" is 0. Gateways or senders that are
              sending the same encoded audio to multiple destinations SHOULD
              NOT use stereo when "stereo" is 0, as this would lead to
              inefficient use of network resources. The "stereo" parameter does
              not affect interoperability.
            </t>

            <t>
              The "cbr" parameter is a unidirectional receive-only
              parameter.
            </t>

            <t>The "useinbandfec" parameter is a unidirectional receive-only
            parameter.</t>

            <t>The "usedtx" parameter is a unidirectional receive-only
            parameter.</t>

            <t>Any unknown parameter in an offer MUST be ignored by the receiver
            and MUST be removed from the answer.</t>

          </list></t>
      
        <t>
	  The Opus parameters in an SDP Offer/Answer exchange are completely
          orthogonal, and there is no relationship between the SDP Offer and
          the Answer.
        </t>
      </section>

      <section title='Declarative SDP Considerations for Opus'>

        <t>For declarative use of SDP such as in Session Announcement Protocol
        (SAP), <xref target="RFC2974"/>, and RTSP, <xref target="RFC2326"/>, for
        Opus, the following needs to be considered:</t>

        <t><list style="symbols">

          <t>The values for "maxptime", "ptime", "maxplaybackrate", and
          "maxaveragebitrate" ought to be selected carefully to ensure that a
          reasonable performance can be achieved for the participants of a session.</t>

          <t>
            The values for "maxptime", "ptime", and of the payload
            format configuration are recommendations by the decoding side to ensure
            the best performance for the decoder.
          </t>

          <t>All other parameters of the payload format configuration are declarative
          and a participant MUST use the configurations that are provided for
          the session. More than one configuration can be provided if necessary
          by declaring multiple RTP payload types; however, the number of types
          ought to be kept small.</t>
        </list></t>
      </section>
  </section>

    <section title='Security Considerations' anchor='security-considerations'>

      <t>Use of variable bitrate (VBR) is subject to the security considerations in
      <xref target="RFC6562"/>.</t>

      <t>RTP packets using the payload format defined in this specification
      are subject to the security considerations discussed in the RTP
      specification <xref target="RFC3550"/>, and in any applicable RTP profile such as
      RTP/AVP <xref target="RFC3551"/>, RTP/AVPF <xref target="RFC4585"/>,
      RTP/SAVP <xref target="RFC3711"/> or RTP/SAVPF <xref target="RFC5124"/>.
      However, as "Securing the RTP Protocol Framework:
      Why RTP Does Not Mandate a Single Media Security Solution"
      <xref target="RFC7202"/> discusses, it is not an RTP payload
      format's responsibility to discuss or mandate what solutions are used
      to meet the basic security goals like confidentiality, integrity and
      source authenticity for RTP in general.  This responsibility lays on
      anyone using RTP in an application.  They can find guidance on
      available security mechanisms and important considerations in Options
      for Securing RTP Sessions [I-D.ietf-avtcore-rtp-security-options].
      Applications SHOULD use one or more appropriate strong security
      mechanisms.</t>

      <t>This payload format and the Opus encoding do not exhibit any
      significant non-uniformity in the receiver-end computational load and thus
      are unlikely to pose a denial-of-service threat due to the receipt of
      pathological datagrams.</t>
    </section>

    <section title='Acknowledgements'>
    <t>Many people have made useful comments and suggestions contributing to this document. 
      In particular, we would like to thank
      Tina le Grand, Cullen Jennings, Jonathan Lennox, Gregory Maxwell, Colin Perkins, Jan Skoglund,
      Timothy B. Terriberry, Martin Thompson, Justin Uberti, Magnus Westerlund, and Mo Zanaty.</t>
    </section>
  </middle>

  <back>
    <references title="Normative References">
      &rfc2119;
      &rfc3389;
      &rfc3550;
      &rfc3711;
      &rfc3551;
      &rfc6838;
      &rfc4855;
      &rfc4566;
      &rfc3264;
      &rfc2326;
      &rfc5576;
      &rfc6562;
      &rfc6716;
    </references>

    <references title="Informative References">
      &rfc2974;
      &rfc4585;
      &rfc5124;
      &rfc5405;
      &rfc7202;
      
      <reference anchor='rmcat' target='https://datatracker.ietf.org/wg/rmcat/documents/'>
        <front>
          <title>rmcat documents</title>
          <author/>
          <date/>
          <abstract>
            <t></t>
          </abstract></front>
      </reference>


    </references>

  </back>
</rfc>

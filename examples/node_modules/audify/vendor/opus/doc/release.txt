= Release checklist =

== Source release ==

- Check for uncommitted changes to master.
- Update OPUS_LT_* API versioning in configure.ac.
- Tag the release commit with 'git tag -s vN.M'.
 - Include release notes in the tag annotation.
- Verify 'make distcheck' produces a tarball with
  the desired name.
- Push tag to public repo.
- Upload source package 'opus-${version}.tar.gz'
 - Add to https://svn.xiph.org/releases/opus/
 - Update checksum files
 - svn commit
 - Copy to archive.mozilla.org/pub/opus/
 - Update checksum files there as well.
- Add release notes to https://gitlab.xiph.org/xiph/opus-website.git
- Update links and checksums on the downloads page.
- Add a copy of the documentation to <https://www.opus-codec.org/docs/>
  and update the links.
- Update /topic in #opus IRC channel.

Releases are committed to https://svn.xiph.org/releases/opus/
which propagates to downloads.xiph.org, and copied manually
to https://archive.mozilla.org/pub/opus/

Website updates are committed to https://gitlab.xiph.org/xiph/opus-website.git
which propagates to https://opus-codec.org/

== Binary release ==

We usually build opus-tools binaries for MacOS and Windows.

Binary releases are copied manually to
https://archive.mozilla.org/pub/opus/win32/

For Mac, submit a pull request to homebrew.

== Website updates ==

For major releases, recreate the files on https://opus-codec.org/examples/
with the next encoder.

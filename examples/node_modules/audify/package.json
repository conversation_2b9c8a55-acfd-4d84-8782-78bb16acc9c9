{"name": "audify", "version": "1.9.0", "description": "Play/Stream/Record PCM audio data & Encode/Decode Opus to PCM audio data", "main": "index.js", "types": "index.d.ts", "scripts": {"docs": "typedoc index.d.ts --hideGenerator --name Audify.js", "rebuild": "cmake-js compile", "install": "prebuild-install -r napi || npm run rebuild", "deploy": "np --yolo", "build-binaries": "run-script-os", "build-binaries:win32": "node --security-revert=CVE-2024-27980 ./node_modules/prebuild/bin.js --backend cmake-js --include-regex \"^.*\\.(node|dylib|dll|so(\\.[0-9])?)$\" -r napi --all --verbose -u %GITHUB_TOKEN%", "build-binaries:darwin:linux": "prebuild --backend cmake-js --include-regex \"^.*\\.(node|dylib|dll|so(\\.[0-9])?)$\" -r napi --all --verbose -u $GITHUB_TOKEN"}, "repository": {"type": "git", "url": "git+https://github.com/almoghamdani/audify.git"}, "keywords": ["play", "stream", "pcm", "encode", "decode", "opus", "rtaudio", "alsa", "jack", "pulseaudio", "oss", "<PERSON><PERSON><PERSON>", "asio", "<PERSON><PERSON>i", "record", "input", "output", "speaker", "sound", "audio", "music", "voip", "headphones"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/almoghamdani/audify/issues"}, "homepage": "https://github.com/almoghamdani/audify#readme", "dependencies": {"bindings": "^1.5.0", "cmake-js": "^7.3.0", "node-abi": "^3.62.0", "node-addon-api": "^8.0.0", "prebuild-install": "^7.1.2"}, "devDependencies": {"@types/node": "^20.12.7", "np": "^10.0.5", "prebuild": "^13.0.0", "run-script-os": "^1.1.6", "typedoc": "^0.25.13", "typescript": "^5.4.5"}, "binary": {"napi_versions": [5, 6, 7, 8, 9]}}
# node-api-headers Changelog

## [1.5.0](https://github.com/nodejs/node-api-headers/compare/v1.4.0...v1.5.0) (2025-01-09)


### Features

* update headers from nodejs/node tag v23.6.0 ([#57](https://github.com/nodejs/node-api-headers/issues/57)) ([64747b1](https://github.com/nodejs/node-api-headers/commit/64747b17a73e8356ed606d1b4dccb626804777f2))

## [1.4.0](https://github.com/nodejs/node-api-headers/compare/v1.3.0...v1.4.0) (2024-10-30)


### Features

* **no-release:** add conditional support in `#if` ([#52](https://github.com/nodejs/node-api-headers/issues/52)) ([e1b8feb](https://github.com/nodejs/node-api-headers/commit/e1b8feb060cf85522538ec1b8a1d6eb782620022))
* update headers from nodejs/node tag v23.1.0 ([#56](https://github.com/nodejs/node-api-headers/issues/56)) ([3336912](https://github.com/nodejs/node-api-headers/commit/33369124c7f8a670422a0c5a27ad149da83ed8d6))

## [1.3.0](https://github.com/nodejs/node-api-headers/compare/v1.2.0...v1.3.0) (2024-09-04)


### Features

* update headers from nodejs/node tag v22.7.0 ([#48](https://github.com/nodejs/node-api-headers/issues/48)) ([6c73c34](https://github.com/nodejs/node-api-headers/commit/6c73c34b72e836531530f863eac315bd42e4569e))

## [1.2.0](https://github.com/nodejs/node-api-headers/compare/node-api-headers-v1.1.0...node-api-headers-v1.2.0) (2024-07-09)


### Features

* update headers from nodejs/node tag v22.1.0 ([d5cfe19](https://github.com/nodejs/node-api-headers/commit/d5cfe19da8b974ca35764dd1c73b91d57cd3c4ce))

## 2023-08-05 Version 1.1.0, @NickNaso

### Notable changes

- Update headers from nodejs/node tag v20.3.0.

### Commits

- \[[`1f5a85dbb0`](https://github.com/nodejs/node-api-headers/commit/1f5a85dbb0)] - Update headers from nodejs/node tag v20.3.0 (#30) (github-actions\[bot])

## 2023-05-18 Version 1.0.1, @NickNaso

### Notable changes

- Update headers from nodejs/node tag v20.2.0.

### Commits

- \[[`d61451e6a3`](https://github.com/nodejs/node-api-headers/commit/d61451e6a3)] - Update headers from nodejs/node tag v20.2.0 (#27) (github-actions\[bot])

## 2023-04-20 Version 1.0.0, @NickNaso

### Notable changes

- Explain package version rationale.
- Update headers from nodejs/node tag v19.9.0.

### Commits

- \[[`130338da33`](https://github.com/nodejs/node-api-headers/commit/130338da33)] - **doc**: explain package version rationale (#26) (Chengzhong Wu)
- \[[`1a328031da`](https://github.com/nodejs/node-api-headers/commit/1a328031da)] - Update headers from nodejs/node tag v19.9.0 (#24) (github-actions\[bot])

## 2023-04-06 Version 0.0.5, @NickNaso

### Notable changes

- Provide def file for windows import lib.

### Commits

- \[[`15477c5898`](https://github.com/nodejs/node-api-headers/commit/15477c5898)] - Update headers from nodejs/node tag v19.8.1 (#22) (github-actions\[bot])
- \[[`d7fa23b60e`](https://github.com/nodejs/node-api-headers/commit/d7fa23b60e)] - Use git status instead of git diff for change calculation (#21) (Kevin Eady)
- \[[`ea0dc01425`](https://github.com/nodejs/node-api-headers/commit/ea0dc01425)] - **fix**: moved def files on a proper folder. (#19) (Nicola Del Gobbo)
- \[[`069c3eb6f8`](https://github.com/nodejs/node-api-headers/commit/069c3eb6f8)] - **doc**: how to create a new release. (#18) (Nicola Del Gobbo)
- \[[`d23c2879c8`](https://github.com/nodejs/node-api-headers/commit/d23c2879c8)] - Provide def file for windows import lib (#17) (Leonid Pospelov)

## 2023-03-17 Version 0.0.4, @NickNaso

### Notable changes

- Update headers from nodejs/node tag v19.8.0.

### Commits

- \[[`a1f0d41240`](https://github.com/nodejs/node-api-headers/commit/a1f0d41240)] - Merge pull request #14 from nodejs/update-headers/v19.8.0 (Nicola Del Gobbo)
- \[[`7548267285`](https://github.com/nodejs/node-api-headers/commit/7548267285)] - Update headers from nodejs/node tag v19.8.0 (github-actions\[bot])

## 2023-03-08 Version 0.0.3, @NickNaso

### Notable changes

- Add helper scripts for updating headers and symbols.js.

### Commits

- \[[`4fdbdd1710`](https://github.com/nodejs/node-api-headers/commit/4fdbdd1710)] - Update headers from nodejs/node tag v19.6.0 (#9) (github-actions\[bot])
- \[[`ecefbdd00f`](https://github.com/nodejs/node-api-headers/commit/ecefbdd00f)] - Add helper scripts for updating headers and symbols.js (#7) (Kevin Eady)

## 2022-12-29 Version 0.0.2, @NickNaso

### Notable changes

- Fixed wrong symblos in v6.

### Commits

- \[[`9c0b4ecaa5`](https://github.com/nodejs/node-api-headers/commit/9c0b4ecaa5)] - **fix**: wrong symblos in v6 (#6) (Nicola Del Gobbo)

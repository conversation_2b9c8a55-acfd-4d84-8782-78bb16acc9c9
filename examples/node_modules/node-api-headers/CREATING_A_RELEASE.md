# Creating a release

Only collaborators in npm for **node-api-headers** can create releases. If you
want to be able to do releases ask one of the existing collaborators to add
you. If necessary you can ask the build Working Group who manages the Node.js
npm user to add you if there are no other active collaborators.

Generally, the release is handled by the
[release-please](https://github.com/nodejs/node-api-headers/blob/main/.github/workflows/release-please.yml)
GitHub action. It will bump the version in `package.json` and publish
node-api-headers to npm.

In cases that the release-please action is not working, please follow the steps
below to publish node-api-headers manually.

## Publish new release manually

### Prerequisites

Before to start creating a new release check if you have installed the following
tools:

* [Changelog maker](https://www.npmjs.com/package/changelog-maker)

If not please follow the instruction reported in the tool's documentation to
install it.

### Steps

These are the steps to follow to create a new release:

* Open an issue in the **node-api-headers** repo documenting the intent to
create a new release. Give people some time to comment or suggest PRs that
should land first.

* Update the version in **package.json** appropriately.

* Update the [README.md][] to show the latest stable version of Node-API.

* Generate the changelog for the new version using **changelog maker** tool.
From the root folder of the repo launch the following command:

```bash
> changelog-maker --format=markdown
```

* Use the output generated by **changelog maker** to update the
[CHANGELOG.md][] following the style used in publishing the previous release.

* Add any new contributors to the "contributors" section in the
**package.json**.

* Do a clean checkout of `node-api-headers`.

* Login and then run `npm publish`.

* Create a release in Github (look at existing releases for an example).

* Validate that you can run `npm install node-api-headers` successfully
and that the correct version is installed.

* Comment on the issue opened in the first step that the release has been created
and close the issue.

* Tweet that the release has been created.

[README.md]: ./README.md
[CHANGELOG.md]: ./CHANGELOG.md
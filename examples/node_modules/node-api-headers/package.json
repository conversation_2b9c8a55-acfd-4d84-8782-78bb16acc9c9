{"bugs": {"url": "https://github.com/nodejs/node-api-headers/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>Eady"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/legendecas"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Pospelove"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}], "description": "Node-API headers", "devDependencies": {"acorn": "^8.12.1"}, "directories": {}, "gypfile": false, "homepage": "https://github.com/nodejs/node-api-headers", "keywords": [], "license": "MIT", "main": "index.js", "name": "node-api-headers", "readme": "README.md", "repository": {"type": "git", "url": "git://github.com/nodejs/node-api-headers.git"}, "scripts": {"update-headers": "node --no-warnings scripts/update-headers.js", "write-symbols": "node --no-warnings scripts/write-symbols.js", "write-win32-def": "node --no-warnings scripts/write-win32-def.js", "test": "node test/parse-utils.js "}, "version": "1.5.0", "support": true}
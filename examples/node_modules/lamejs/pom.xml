<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>jump3r</groupId>
  <artifactId>jump3r</artifactId>
  <version>1.0.3</version>
  <build>
  	<resources>
      <resource>
      	<directory>src/main/resources</directory>
        <excludes>
          <exclude>LameUI.xml</exclude>
          <exclude>picture.png</exclude>
          <exclude>run.bat</exclude>
          <exclude>run.sh</exclude>
        </excludes>
      </resource>
    </resources>
  	<plugins>
  		<plugin>
			<groupId>org.apache.maven.plugins</groupId>
			<artifactId>maven-jar-plugin</artifactId>
			<configuration>
				<archive>
					<manifest>
						<addClasspath>true</addClasspath>
						<mainClass>ui.LameUI</mainClass>
					</manifest>
				</archive>
			</configuration>
		</plugin>
  		<plugin>
			<artifactId>maven-assembly-plugin</artifactId>
			<configuration>
				<descriptors>
					<descriptor>src/main/assembly/bundle.xml</descriptor>
				</descriptors>
				<finalName>jump3r-1.0.3</finalName>
			</configuration>
			<executions>
				<execution>
					<id>make-assembly</id>
					<phase>package</phase>
					<goals>
						<goal>single</goal>
					</goals>
				</execution>
			</executions>
		</plugin>
  	</plugins>
  </build>
  <dependencies>
  	<dependency>
  		<groupId>org.jma</groupId>
  		<artifactId>jma-api</artifactId>
  		<version>1.0.1</version>
  		<scope>compile</scope>
  	</dependency>
  	<dependency>
  		<groupId>org.swixml</groupId>
  		<artifactId>swixml</artifactId>
  		<version>1.5.144</version>
  	</dependency>
      <dependency>
          <groupId>junit</groupId>
          <artifactId>junit</artifactId>
          <version>4.11</version>
      </dependency>
  </dependencies>
</project>
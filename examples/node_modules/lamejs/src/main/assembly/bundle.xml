<assembly xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0" 
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0 http://maven.apache.org/xsd/assembly-1.1.0.xsd">
  <id>bundle</id>
  <formats>
    <format>dir</format>
    <format>zip</format>
  </formats>
  <dependencySets>
  	<dependencySet>
  		<outputDirectory>/lib</outputDirectory>
  	</dependencySet>
  </dependencySets> 
  <files>
    <file>
      <source>src/main/resources/run.bat</source>
      <outputDirectory>/bin</outputDirectory>
      <filtered>true</filtered>
    </file>
    <file>
      <source>src/main/resources/run.sh</source>
      <outputDirectory>/bin</outputDirectory>
      <filtered>true</filtered>
      <lineEnding>unix</lineEnding>
    </file>
    <file>
      <source>src/main/resources/LameUI.xml</source>
      <outputDirectory>/conf</outputDirectory>
      <filtered>true</filtered>
    </file>
    <file>
	    <source>src/main/resources/picture.png</source>
	    <outputDirectory>/images</outputDirectory>
	    <filtered>false</filtered>
    </file>
  </files>
</assembly>
Can I use LAME in my commercial program?  

Yes, you can, under the restrictions of the LGPL.  The easiest
way to do this is to:

1. Link to LAME as separate jar (jump3r.jar)

2. <PERSON>y acknowledge that you are using LAME, and give a link
   to our web site, www.mp3dev.org

3. If you make modifications to LAME, you *must* release these
   these modifications back to the LAME project, under the LGPL.

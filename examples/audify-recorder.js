/**
 * Audio recorder using Audify with PCM to WAV conversion
 * 
 * This example demonstrates:
 * 1. Recording audio with Audify
 * 2. Converting the PCM data to WAV format
 * 3. Saving the WAV file
 * 
 * Installation: npm install audify
 */

const fs = require('fs');
const { RtAudio, RtAudioFormat } = require('audify');

// Buffer to store recorded PCM data
let recordedChunks = [];
let isRecording = false;
let recordingDuration = 0;
let recordingStartTime = 0;
const MAX_RECORDING_SECONDS = 10; // Maximum recording length in seconds

/**
 * Converts raw PCM data to a WAV file
 * @param {Buffer} pcmData - Raw PCM audio data
 * @param {Object} format - Audio format parameters
 * @param {number} format.sampleRate - Sample rate in Hz (e.g., 44100, 48000)
 * @param {number} format.channels - Number of audio channels (1 for mono, 2 for stereo)
 * @param {number} format.bitDepth - Bit depth (8, 16, 24, or 32)
 * @param {string} outputPath - Path for the output WAV file
 */
function pcmToWav(pcmData, format, outputPath) {
  const { sampleRate = 44100, channels = 1, bitDepth = 16 } = format;
  
  // Calculate byte rate and block align
  const byteDepth = bitDepth / 8;
  const blockAlign = channels * byteDepth;
  const byteRate = sampleRate * blockAlign;
  
  // Create WAV file header
  const headerBuffer = Buffer.alloc(44);
  
  // RIFF chunk descriptor
  headerBuffer.write('RIFF', 0);                                // ChunkID
  headerBuffer.writeUInt32LE(36 + pcmData.length, 4);           // ChunkSize: 36 + SubChunk2Size
  headerBuffer.write('WAVE', 8);                                // Format
  
  // "fmt " sub-chunk
  headerBuffer.write('fmt ', 12);                               // Subchunk1ID
  headerBuffer.writeUInt32LE(16, 16);                          // Subchunk1Size (16 for PCM)
  headerBuffer.writeUInt16LE(1, 20);                           // AudioFormat (1 for PCM)
  headerBuffer.writeUInt16LE(channels, 22);                    // NumChannels
  headerBuffer.writeUInt32LE(sampleRate, 24);                  // SampleRate
  headerBuffer.writeUInt32LE(byteRate, 28);                    // ByteRate
  headerBuffer.writeUInt16LE(blockAlign, 32);                  // BlockAlign
  headerBuffer.writeUInt16LE(bitDepth, 34);                    // BitsPerSample
  
  // "data" sub-chunk
  headerBuffer.write('data', 36);                              // Subchunk2ID
  headerBuffer.writeUInt32LE(pcmData.length, 40);              // Subchunk2Size
  
  // Combine header and PCM data
  const wavBuffer = Buffer.concat([headerBuffer, pcmData]);
  
  // Write WAV file to disk
  fs.writeFileSync(outputPath, wavBuffer);
  
  console.log(`WAV file created at: ${outputPath}`);
  return wavBuffer;
}

/**
 * Start recording audio with Audify
 * @param {number} sampleRate - Sample rate in Hz
 * @param {number} frameSize - Frame size (buffer size)
 * @param {number} channels - Number of channels (1 for mono, 2 for stereo)
 * @param {number} seconds - Recording duration in seconds (0 for unlimited)
 * @returns {Promise} - Resolves with recorded PCM data when complete
 */
function recordAudio(sampleRate = 48000, frameSize = 1920, channels = 1, seconds = MAX_RECORDING_SECONDS) {
  return new Promise((resolve, reject) => {
    try {
      // Clear previous recording data
      recordedChunks = [];
      isRecording = true;
      recordingStartTime = Date.now();
      
      // Create RtAudio instance using default sound API
      const rtAudio = new RtAudio();
      
      console.log('Available audio devices:');
      const devices = rtAudio.getDevices();
      devices.forEach((device, index) => {
        console.log(`[${index}] ${device.name} (Inputs: ${device.inputChannels}, Outputs: ${device.outputChannels})`);
      });
      
      // Use PulseAudio instead of default device (PulseAudio is device index 1)
      const pulseAudioDeviceId = 1; // Index of PulseAudio from the devices list
      
      console.log(`\nUsing input device: ${devices[pulseAudioDeviceId].name}`);
      console.log(`Sample rate: ${sampleRate}Hz, Frame size: ${frameSize}, Channels: ${channels}`);
      console.log(`Recording for ${seconds} seconds...`);
      
      // Open the input stream
      rtAudio.openStream(
        null, // No output
        {
          // deviceId: pulseAudioDeviceId,
          nChannels: channels,
          firstChannel: 0,
        },
        RtAudioFormat.RTAUDIO_SINT16, // PCM Format - Signed 16-bit integer
        sampleRate, // Sampling rate
        frameSize, // Frame size
        "RecordingStream", // Stream name
        (pcmData) => {
          // Callback function to process audio data
          if (isRecording) {
            // Store the PCM data
            recordedChunks.push(Buffer.from(pcmData));
            
            // Update recording duration
            recordingDuration = (Date.now() - recordingStartTime) / 1000;
            
            // Stop if we've reached the specified duration
            if (seconds > 0 && recordingDuration >= seconds) {
              stopRecording();
              
              // Combine all chunks into a single buffer
              const combinedBuffer = Buffer.concat(recordedChunks);
              
              // Close the stream and resolve the promise
              rtAudio.closeStream();
              resolve(combinedBuffer);
            }
          }
        }
      );
      
      // Function to stop recording
      function stopRecording() {
        isRecording = false;
        console.log(`Recording stopped after ${recordingDuration.toFixed(1)} seconds`);
      }
      
      // Start the stream
      rtAudio.start();
      
      // Setup keyboard interrupt handler
      console.log('Press Ctrl+C to stop recording early...');
      process.on('SIGINT', () => {
        if (isRecording) {
          stopRecording();
          
          // Combine all chunks into a single buffer
          const combinedBuffer = Buffer.concat(recordedChunks);
          
          // Close the stream and resolve the promise
          rtAudio.closeStream();
          resolve(combinedBuffer);
        }
      });
      
    } catch (error) {
      reject(error);
    }
  });
}

// Main execution function
async function main() {
  try {
    // Record audio with specific parameters
    const sampleRate = 48000;
    const channels = 1;
    const bitDepth = 16;
    const recordingSeconds = 5; // Record for 5 seconds
    
    console.log('Starting audio recording...');
    const pcmData = await recordAudio(sampleRate, 1920, channels, recordingSeconds);
    
    // Convert PCM to WAV
    const wavFilePath = './recording.wav';
    pcmToWav(pcmData, { sampleRate, channels, bitDepth }, wavFilePath);
    
    console.log(`\nRecording complete! ${pcmData.length} bytes of PCM data recorded.`);
    console.log(`WAV file saved to ${wavFilePath}`);
    
    // Exit the process
    process.exit(0);
    
  } catch (error) {
    console.error('Error during recording:', error);
    process.exit(1);
  }
}

// Run the main function
main();

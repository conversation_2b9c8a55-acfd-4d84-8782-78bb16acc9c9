/**
 * Simple PCM to WAV file converter
 * This demonstrates how to create a WAV file from PCM data without specialized audio libraries
 */

const fs = require('fs');

/**
 * Converts raw PCM data to a WAV file
 * @param {Buffer} pcmData - Raw PCM audio data
 * @param {Object} format - Audio format parameters
 * @param {number} format.sampleRate - Sample rate in Hz (e.g., 44100, 48000)
 * @param {number} format.channels - Number of audio channels (1 for mono, 2 for stereo)
 * @param {number} format.bitDepth - Bit depth (8, 16, 24, or 32)
 * @param {string} outputPath - Path for the output WAV file
 */
function pcmToWav(pcmData, format, outputPath) {
  const { sampleRate = 44100, channels = 1, bitDepth = 16 } = format;
  
  // Calculate byte rate and block align
  const byteDepth = bitDepth / 8;
  const blockAlign = channels * byteDepth;
  const byteRate = sampleRate * blockAlign;
  
  // Create WAV file header
  const headerBuffer = Buffer.alloc(44);
  
  // RIFF chunk descriptor
  headerBuffer.write('RIFF', 0);                                // ChunkID
  headerBuffer.writeUInt32LE(36 + pcmData.length, 4);           // ChunkSize: 36 + SubChunk2Size
  headerBuffer.write('WAVE', 8);                                // Format
  
  // "fmt " sub-chunk
  headerBuffer.write('fmt ', 12);                               // Subchunk1ID
  headerBuffer.writeUInt32LE(16, 16);                          // Subchunk1Size (16 for PCM)
  headerBuffer.writeUInt16LE(1, 20);                           // AudioFormat (1 for PCM)
  headerBuffer.writeUInt16LE(channels, 22);                    // NumChannels
  headerBuffer.writeUInt32LE(sampleRate, 24);                  // SampleRate
  headerBuffer.writeUInt32LE(byteRate, 28);                    // ByteRate
  headerBuffer.writeUInt16LE(blockAlign, 32);                  // BlockAlign
  headerBuffer.writeUInt16LE(bitDepth, 34);                    // BitsPerSample
  
  // "data" sub-chunk
  headerBuffer.write('data', 36);                              // Subchunk2ID
  headerBuffer.writeUInt32LE(pcmData.length, 40);              // Subchunk2Size
  
  // Combine header and PCM data
  const wavBuffer = Buffer.concat([headerBuffer, pcmData]);
  
  // Write WAV file to disk
  fs.writeFileSync(outputPath, wavBuffer);
  
  console.log(`WAV file created at: ${outputPath}`);
  return wavBuffer;
}

// Example usage (simulating PCM data for demo)
function createDemoWavFile() {
  // Generate 1 second of a sine wave at 440Hz (A4 note) as PCM data
  const sampleRate = 44100;
  const duration = 1; // seconds
  const frequency = 440; // Hz (A4 note)
  const amplitude = 0.8; // 0.0 to 1.0
  const numSamples = sampleRate * duration;
  
  // Create a buffer for 16-bit PCM data (2 bytes per sample)
  const pcmData = Buffer.alloc(numSamples * 2);
  
  // Generate a sine wave
  for (let i = 0; i < numSamples; i++) {
    const sampleValue = Math.sin((i / sampleRate) * frequency * 2 * Math.PI) * amplitude;
    // Convert to 16-bit PCM value (-32768 to 32767)
    const pcmValue = Math.floor(sampleValue * 32767);
    // Write the 16-bit value to the buffer (little-endian)
    pcmData.writeInt16LE(pcmValue, i * 2);
  }
  
  // Convert PCM to WAV
  const format = {
    sampleRate: 44100,
    channels: 1,
    bitDepth: 16
  };
  
  pcmToWav(pcmData, format, './demo-tone.wav');
}

// Run the demo
createDemoWavFile();

/**
 * If you're receiving PCM data from an audio device, you would use this function
 * after collecting the PCM buffer from your recording library, for example:
 * 
 * // Example with an audio recording library (pseudocode)
 * audioRecorder.on('data', (pcmData) => {
 *   // Convert to WAV on the fly or store for later
 *   pcmToWav(pcmData, {
 *     sampleRate: audioRecorder.sampleRate,
 *     channels: audioRecorder.channels,
 *     bitDepth: audioRecorder.bitDepth
 *   }, './recording.wav');
 * });
 */

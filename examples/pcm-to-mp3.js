/**
 * PCM to MP3 conversion example using lamejs
 * Installation: npm install lamejs
 */

const fs = require('fs');
const lamejs = require('lamejs');

/**
 * Converts raw PCM data to an MP3 file
 * @param {Buffer} pcmData - Raw PCM audio data (should be 16-bit)
 * @param {Object} format - Audio format parameters
 * @param {number} format.sampleRate - Sample rate in Hz (e.g., 44100, 48000)
 * @param {number} format.channels - Number of audio channels (1 for mono, 2 for stereo)
 * @param {number} format.bitRate - MP3 bit rate in kbps (e.g., 128, 192, 320)
 * @param {string} outputPath - Path for the output MP3 file
 */
function pcmToMp3(pcmData, format, outputPath) {
  const { sampleRate = 44100, channels = 1, bitRate = 128 } = format;

  // Create MP3 encoder
  const mp3encoder = new lamejs.Mp3Encoder(channels, sampleRate, bitRate);
  
  // Process the PCM data
  // Note: lamejs expects samples as Int16Array
  const samples = new Int16Array(pcmData.buffer, pcmData.byteOffset, pcmData.length / 2);
  
  // Encode MP3 - for simplicity we'll encode in one go
  // For larger files, you'd want to process in smaller chunks
  let mp3Data;
  
  if (channels === 1) {
    mp3Data = mp3encoder.encodeBuffer(samples); // mono
  } else {
    // For stereo, we need to separate channels
    // This is simplified - actual implementation would depend on how your PCM data is organized
    const leftChannel = new Int16Array(samples.length / 2);
    const rightChannel = new Int16Array(samples.length / 2);
    
    for (let i = 0; i < samples.length / 2; i++) {
      leftChannel[i] = samples[i * 2];
      rightChannel[i] = samples[i * 2 + 1];
    }
    
    mp3Data = mp3encoder.encodeBuffer(leftChannel, rightChannel);
  }
  
  // Get the last frames and end the stream
  const mp3End = mp3encoder.flush();
  
  // Write MP3 file to disk
  const mp3Buffer = Buffer.from(new Uint8Array(mp3Data.buffer));
  const mp3EndBuffer = Buffer.from(new Uint8Array(mp3End.buffer));
  const finalMp3 = Buffer.concat([mp3Buffer, mp3EndBuffer]);
  
  fs.writeFileSync(outputPath, finalMp3);
  
  console.log(`MP3 file created at: ${outputPath}`);
  return finalMp3;
}

// Example usage with the same sine wave generator
function createDemoMp3File() {
  // Generate 1 second of a sine wave at 440Hz (A4 note) as PCM data
  const sampleRate = 44100;
  const duration = 1; // seconds
  const frequency = 440; // Hz (A4 note)
  const amplitude = 0.8; // 0.0 to 1.0
  const numSamples = sampleRate * duration;
  
  // Create a buffer for 16-bit PCM data (2 bytes per sample)
  const pcmData = Buffer.alloc(numSamples * 2);
  
  // Generate a sine wave
  for (let i = 0; i < numSamples; i++) {
    const sampleValue = Math.sin((i / sampleRate) * frequency * 2 * Math.PI) * amplitude;
    // Convert to 16-bit PCM value (-32768 to 32767)
    const pcmValue = Math.floor(sampleValue * 32767);
    // Write the 16-bit value to the buffer (little-endian)
    pcmData.writeInt16LE(pcmValue, i * 2);
  }
  
  // Convert PCM to MP3
  const format = {
    sampleRate: 44100,
    channels: 1,
    bitRate: 128
  };
  
  pcmToMp3(pcmData, format, './demo-tone.mp3');
}

// Run the demo if lamejs is installed
try {
  createDemoMp3File();
} catch (error) {
  console.error('Error: Make sure to install lamejs first with npm install lamejs');
  console.error(error);
}

/**
 * Note on installation and usage:
 * 
 * 1. Install lamejs: npm install lamejs
 * 2. This is a simplified example - production code would need more error handling
 * 3. For large files, process PCM data in chunks rather than all at once
 */

{"name": "voicehype", "version": "1.0.0", "description": "Voice transcription and audio processing utilities", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.js", "record": "node audify-recorder.js", "convert:wav": "node pcm-to-wav.js", "convert:mp3": "node pcm-to-mp3.js", "test:audify": "node audify-test.js"}, "keywords": ["audio", "voice", "transcription", "pcm", "wav", "mp3"], "author": "", "license": "MIT", "dependencies": {"audify": "^1.8.0"}, "optionalDependencies": {"lamejs": "^1.2.1"}, "engines": {"node": ">=14.0.0"}}
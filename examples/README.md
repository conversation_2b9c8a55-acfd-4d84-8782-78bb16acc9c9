# VoiceHype Real-time Transcription

This directory contains examples for using VoiceHype's real-time transcription API.

## Real-time Transcription with WebSockets

VoiceHype now supports real-time transcription using WebSockets. This allows you to stream audio from a microphone and receive transcription results in real-time.

### How It Works

1. The client establishes a WebSocket connection to the `/transcribe/realtime` endpoint
2. The client sends audio data as binary messages
3. The server forwards the audio to AssemblyAI's real-time transcription service
4. Transcription results are sent back to the client as they become available

### Authentication

Authentication is done using your VoiceHype API key, which should be passed as a query parameter:

```
wss://your-project.functions.supabase.co/transcribe/realtime?apiKey=your-api-key
```

### Audio Format Requirements

- Sample rate: 16kHz (16000 Hz)
- Encoding: 16-bit signed PCM (Linear PCM)
- Channels: Mono (single channel)

### WebSocket Messages

#### From Client to Server

- **Binary Audio Data**: Send raw audio data as binary WebSocket messages
- **Control Messages**: Send JSON messages for control operations:
  ```json
  { "type": "close" }
  ```

#### From Server to Client

- **Connection Status**:
  ```json
  { "type": "connected", "sessionId": "unique-session-id" }
  ```

- **Partial Transcripts**:
  ```json
  {
    "message_type": "PartialTranscript",
    "text": "This is a partial transcript",
    "audio_start": 0,
    "audio_end": 1.5,
    "confidence": 0.95,
    "words": [...]
  }
  ```

- **Final Transcripts**:
  ```json
  {
    "message_type": "FinalTranscript",
    "text": "This is a final transcript.",
    "audio_start": 0,
    "audio_end": 2.5,
    "confidence": 0.98,
    "words": [...]
  }
  ```

- **Errors**:
  ```json
  { "type": "error", "message": "Error message" }
  ```

- **Service Disconnection**:
  ```json
  { "type": "service_disconnected", "code": 1000, "reason": "Normal closure" }
  ```

### Billing

Real-time transcription is billed based on the duration of the WebSocket connection. The pricing is per minute of audio processed.

## Examples

### Browser Example

See the `realtime-transcription.html` file in this directory for a complete example of how to use the real-time transcription API from a web browser.

To use the example:
1. Open the HTML file in a web browser
2. Enter your VoiceHype API key
3. Click "Start Recording" and allow microphone access
4. Speak into your microphone
5. View the transcription results in real-time
6. Click "Stop Recording" when finished

### Node.js Example

The `realtime-transcription.js` file demonstrates how to use the real-time transcription API from a Node.js application.

To use the example:
1. Install the required dependencies:
   ```
   npm install ws
   ```
2. Make sure you have SoX installed on your system:
   - macOS: `brew install sox`
   - Linux: `apt-get install sox`
   - Windows: Download from http://sox.sourceforge.net/
3. Set your API key as an environment variable:
   ```
   export VOICEHYPE_API_KEY=your-api-key
   export VOICEHYPE_ENDPOINT=wss://your-project.functions.supabase.co/transcribe/realtime
   ```
4. Run the example:
   ```
   node realtime-transcription.js
   ```
5. Speak into your microphone
6. Press Ctrl+C to stop recording and exit

### Limitations

- Currently, only English language is supported for real-time transcription
- Maximum connection time is limited to 15 minutes per session
- Audio data must be in the correct format (16kHz, 16-bit PCM, mono)

## Support

If you encounter any issues or have questions, please contact our support team. 
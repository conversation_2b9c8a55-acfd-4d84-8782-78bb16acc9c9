/**
 * Simple Audify test script
 * Tests basic recording functionality with detailed logging
 */

const fs = require('fs');
const path = require('path');
const { RtAudio, RtAudioFormat } = require('audify');

// Test configuration
const TEST_DURATION_SECONDS = 5;
const OUTPUT_FILE = path.join(__dirname, 'audify-test-recording.wav');
const DEVICE_ID = 0; // Hardcoded device ID as requested
const SAMPLE_RATE = 48000;
const CHANNELS = 1;
const FRAME_SIZE = 1920; // Using the exact frame size from the example

// Global variables
let recordedChunks = [];
let isRecording = false;
let callbackCount = 0;
let totalDataSize = 0;

console.log('=== Audify Simple Test ===');
console.log(`Device ID: ${DEVICE_ID}`);
console.log(`Sample Rate: ${SAMPLE_RATE}Hz`);
console.log(`Channels: ${CHANNELS}`);
console.log(`Frame Size: ${FRAME_SIZE}`);
console.log(`Output File: ${OUTPUT_FILE}`);
console.log(`Test Duration: ${TEST_DURATION_SECONDS} seconds`);

function pcmToWav(pcmData, format) {
  const { sampleRate = 44100, channels = 1, bitDepth = 16 } = format;
  
  // Calculate byte rate and block align
  const byteDepth = bitDepth / 8;
  const blockAlign = channels * byteDepth;
  const byteRate = sampleRate * blockAlign;
  
  // Create WAV file header
  const headerBuffer = Buffer.alloc(44);
  
  // RIFF chunk descriptor
  headerBuffer.write('RIFF', 0);                                // ChunkID
  headerBuffer.writeUInt32LE(36 + pcmData.length, 4);           // ChunkSize
  headerBuffer.write('WAVE', 8);                                // Format
  
  // "fmt " sub-chunk
  headerBuffer.write('fmt ', 12);                               // Subchunk1ID
  headerBuffer.writeUInt32LE(16, 16);                          // Subchunk1Size
  headerBuffer.writeUInt16LE(1, 20);                           // AudioFormat
  headerBuffer.writeUInt16LE(channels, 22);                    // NumChannels
  headerBuffer.writeUInt32LE(sampleRate, 24);                  // SampleRate
  headerBuffer.writeUInt32LE(byteRate, 28);                    // ByteRate
  headerBuffer.writeUInt16LE(blockAlign, 32);                  // BlockAlign
  headerBuffer.writeUInt16LE(bitDepth, 34);                    // BitsPerSample
  
  // "data" sub-chunk
  headerBuffer.write('data', 36);                              // Subchunk2ID
  headerBuffer.writeUInt32LE(pcmData.length, 40);              // Subchunk2Size
  
  // Combine header and PCM data
  return Buffer.concat([headerBuffer, pcmData]);
}

async function testRecording() {
  try {
    console.log('\nCreating RtAudio instance...');
    const rtAudio = new RtAudio();
    
    // List available devices
    console.log('\nAvailable audio devices:');
    const devices = rtAudio.getDevices();
    devices.forEach((device, index) => {
      console.log(`[${index}] ${device.name} (Inputs: ${device.inputChannels}, Outputs: ${device.outputChannels})`);
    });
    
    // Check if the device is valid
    if (!devices[DEVICE_ID] || devices[DEVICE_ID].inputChannels === 0) {
      console.error(`\nERROR: Selected device ID ${DEVICE_ID} is not valid for input.`);
      console.error('Please select a valid input device from the list above.');
      return;
    }
    
    console.log(`\nUsing input device: [${DEVICE_ID}] ${devices[DEVICE_ID].name}`);
    
    // Log default input device
    const defaultInputDevice = rtAudio.getDefaultInputDevice();
    console.log(`Default input device: [${defaultInputDevice}]`);

    // Start recording
    console.log('\nOpening audio stream...');
    recordedChunks = [];
    isRecording = true;
    callbackCount = 0;
    totalDataSize = 0;
    
    // Open stream with error handling
    try {
      rtAudio.openStream(
        null, // No output
        {
          nChannels: CHANNELS,
          firstChannel: 0
        },
        RtAudioFormat.RTAUDIO_SINT16,
        SAMPLE_RATE,
        FRAME_SIZE,
        "TestRecording",
        (pcmData) => {
          if (isRecording && pcmData) {
            callbackCount++;
            const buffer = Buffer.from(pcmData);
            totalDataSize += buffer.length;
            recordedChunks.push(buffer);
            
            // Log every 10th callback to avoid console flood
            if (callbackCount % 10 === 0) {
              console.log(`Callback #${callbackCount}, Data size: ${buffer.length} bytes, Total: ${totalDataSize} bytes`);
            }
          }
        }
      );
      
      console.log('Stream opened successfully!');
    } catch (error) {
      console.error('\nERROR: Failed to open audio stream:', error.message);
      console.error('Stream parameters:', {
        deviceId: rtAudio.getDefaultInputDevice(),
        channels: CHANNELS,
        sampleRate: SAMPLE_RATE,
        frameSize: FRAME_SIZE
      });
      return;
    }
    
    // Start the stream
    console.log('\nStarting audio stream...');
    try {
      rtAudio.start();
      console.log('Stream started successfully!');
    } catch (error) {
      console.error('\nERROR: Failed to start audio stream:', error.message);
      return;
    }
    
    console.log(`\nRecording for ${TEST_DURATION_SECONDS} seconds...`);
    
    // Wait for the specified duration
    await new Promise(resolve => setTimeout(resolve, TEST_DURATION_SECONDS * 1000));
    
    // Stop recording
    console.log('\nStopping recording...');
    isRecording = false;
    
    // Stop and close the stream
    try {
      if (rtAudio.isStreamRunning()) {
        rtAudio.stop();
      }
      if (rtAudio.isStreamOpen()) {
        rtAudio.closeStream();
      }
      console.log('Stream stopped and closed successfully');
    } catch (error) {
      console.error('\nERROR: Error stopping stream:', error.message);
    }
    
    // Check if we got any data
    console.log(`\nRecording summary:`);
    console.log(`- Total callbacks: ${callbackCount}`);
    console.log(`- Total data size: ${totalDataSize} bytes`);
    console.log(`- Chunks collected: ${recordedChunks.length}`);
    
    if (recordedChunks.length === 0) {
      console.error('\nERROR: No audio data was recorded!');
      return;
    }
    
    // Combine chunks and save to file
    console.log('\nProcessing recorded audio...');
    const combinedBuffer = Buffer.concat(recordedChunks);
    console.log(`Combined PCM data size: ${combinedBuffer.length} bytes`);
    
    // Convert to WAV and save
    const wavBuffer = pcmToWav(combinedBuffer, {
      sampleRate: SAMPLE_RATE,
      channels: CHANNELS,
      bitDepth: 16
    });
    
    fs.writeFileSync(OUTPUT_FILE, wavBuffer);
    console.log(`\nWAV file saved to: ${OUTPUT_FILE}`);
    
    // Check file size
    const stats = fs.statSync(OUTPUT_FILE);
    console.log(`File size: ${stats.size} bytes`);
    
    console.log('\nTest completed successfully!');
    
  } catch (error) {
    console.error('\nTest failed with error:', error.message);
    console.error(error.stack);
  }
}

// Run the test
testRecording().then(() => {
  console.log('Test finished');
}).catch(err => {
  console.error('Unhandled error:', err);
});

# VoiceHype API Development Plan
**Inshaa Allah** - *api.voicehype.ai*

## Overview
Building a robust Express.js API for VoiceHype with three main endpoints: transcription, optimization, and real-time WebSocket communication. The API will feature modular architecture with shared security utilities and clean separation of concerns.

## Project Structure

```
voicehype-api/
├── src/
│   ├── routes/
│   │   ├── transcribe/
│   │   │   ├── index.js
│   │   │   ├── providers/
│   │   │   │   ├── lemonfox.js
│   │   │   │   ├── assemblyai.js
│   │   │   │   └── combined.js
│   │   │   └── validation.js
│   │   ├── optimize/
│   │   │   ├── index.js
│   │   │   ├── llm-provider.js
│   │   │   └── validation.js
│   │   └── realtime/
│   │       ├── index.js
│   │       ├── websocket-handler.js
│   │       └── message-processor.js
│   ├── middleware/
│   │   ├── auth.js (shared security utility)
│   │   ├── validation.js
│   │   ├── cors.js
│   │   └── error-handler.js
│   ├── services/
│   │   ├── supabase.js
│   │   ├── database.js
│   │   └── models.js
│   ├── utils/
│   │   ├── response-formatter.js
│   │   ├── secret-generator.js
│   │   └── logger.js
│   └── config/
│       └── database.js
├── app.js (main Express app)
├── server.js (server startup)
├── package.json
└── .env
```

## API Endpoints

### 1. Transcription Endpoint (`/api/transcribe`)
- **Purpose**: Convert audio to text using multiple providers
- **Providers**: LemonFox, AssemblyAI, Combined approach
- **Response**: Raw API response from selected provider
- **Structure**: Modular provider-based architecture

### 2. Optimization Endpoint (`/api/optimize`)
- **Purpose**: LLM-based content optimization
- **Features**: Database-driven model selection from models table
- **Input**: Messages array
- **Response**: Raw LLM response
- **Models**: User-subscribed LLMs from database

### 3. Real-time Endpoint (`/api/realtime`)
- **Purpose**: WebSocket-based real-time communication
- **Features**: Live transcription, optimization streaming
- **Architecture**: Separated concerns for WebSocket management

## Security Architecture

### Authentication Flow
1. **API Secret Verification** - Validates request origin from VoiceHype app
2. **User Authentication** - Verifies authId + accessToken combination  
3. **Token Expiry Check** - Ensures access token validity
4. **User Existence Validation** - Confirms user exists in database

### Shared Security Utility
```javascript
// src/middleware/auth.js
const verifyApiSecret = (req, res, next) => {
  // Check API secret header
};

const verifyUser = async (req, res, next) => {
  // Verify authId + accessToken
  // Check token expiry  
  // Confirm user exists in database
};
```

### Method
```javascript
// src/middleware/auth.js
import { createClient } from '@supabase/supabase-js';

async function verifyUser(authId, accessToken) {
    try {
        // Create Supabase client with the user's access token
        const supabase = createClient(
            process.env.SUPABASE_URL,
            process.env.SUPABASE_ANON_KEY,
            {
                global: {
                    headers: {
                        Authorization: `Bearer ${accessToken}`
                    }
                }
            }
        );

        // Get the current user using their access token
        const { data: { user }, error } = await supabase.auth.getUser(accessToken);

        if (error || !user) {
            console.error('❌ Invalid access token:', error);
            return null;
        }

        // Verify the auth ID matches
        if (user.id !== authId) {
            console.error('❌ Auth ID mismatch:', { provided: authId, actual: user.id });
            return null;
        }

        // Check if token is expired (optional, Supabase handles this automatically)
        const now = Math.floor(Date.now() / 1000);
        if (user.exp && user.exp < now) {
            console.error('❌ Access token expired');
            return null;
        }

        return {
            userId: user.id,
            email: user.email,
            isVerified: user.email_confirmed_at !== null
        };

    } catch (error) {
        console.error('❌ Error verifying user:', error);
        return null;
    }
}
```

## Environment Configuration

### Environment Variables (.env)
```bash
# Supabase
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# API Security
API_SECRET=your_api_secret_key
JWT_SECRET=your_jwt_secret

# External Services
LEMONFOX_API_KEY=your_lemonfox_key
ASSEMBLYAI_API_KEY=your_assemblyai_key

# Server
PORT=3000
NODE_ENV=development
```

## Main Application Structure

### Express App Setup (app.js)
```javascript
const express = require('express');
const cors = require('cors');
const { createServer } = require('http');
const { Server } = require('socket.io');

// Import middleware
const authMiddleware = require('./src/middleware/auth');
const errorHandler = require('./src/middleware/error-handler');

// Import routes
const transcribeRoutes = require('./src/routes/transcribe');
const optimizeRoutes = require('./src/routes/optimize');
const realtimeRoutes = require('./src/routes/realtime');

const app = express();
const server = createServer(app);
const io = new Server(server);

// Middleware
app.use(cors());
app.use(express.json());
app.use(authMiddleware); // Shared security

// Routes
app.use('/api/transcribe', transcribeRoutes);
app.use('/api/optimize', optimizeRoutes);
app.use('/api/realtime', realtimeRoutes);

// Error handling
app.use(errorHandler);

module.exports = { app, server, io };
```

## Key Features

### Modular Architecture
- **Separated Concerns**: Each endpoint has its own directory with specific functionality
- **Provider Abstraction**: Transcription providers cleanly separated
- **Shared Utilities**: Common functionality (auth, validation, formatting) reused across endpoints

### Security First
- **Multi-layer Authentication**: API secret + user token verification
- **Database Integration**: User validation against Supabase
- **Token Management**: Proper expiry checking and refresh handling

### Scalable Design
- **Database-driven Configuration**: Models and settings stored in database
- **Raw Response Strategy**: Return unmodified provider responses for flexibility
- **WebSocket Support**: Real-time capabilities for live features

## Technical Decisions Pending

### Development Choices
1. **WebSocket Library**: Socket.IO vs native WebSockets for real-time functionality
2. **Database ORM**: Raw Supabase client vs ORM like Prisma for data operations
3. **Validation Framework**: Joi, Zod, or express-validator for request validation
4. **File Upload Strategy**: multipart/form-data vs base64 encoding for audio files

### Implementation Priorities
1. **Phase 1**: Core security middleware and basic endpoint structure
2. **Phase 2**: Transcription endpoint with provider abstraction
3. **Phase 3**: Optimization endpoint with LLM integration
4. **Phase 4**: Real-time WebSocket implementation
5. **Phase 5**: Testing, optimization, and deployment preparation

## Domain Setup & Deployment

### DNS Configuration
Add a CNAME record in your DNS provider for voicehype.ai:
```
Type: CNAME
Name: api
Value: your-server-domain.com (or server IP)
TTL: 300
```

**Example**: `api.voicehype.ai` → `server.voicehype.ai` or `**************`

### Nginx Configuration

#### Option 1: Separate Server Block (Recommended)
Create `/etc/nginx/sites-available/api.voicehype.ai`:

```nginx
server {
    listen 80;
    listen [::]:80;
    server_name api.voicehype.ai;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name api.voicehype.ai;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/api.voicehype.ai/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/api.voicehype.ai/privkey.pem;
    
    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # API Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Proxy to Express.js API
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # WebSocket Support for Real-time
    location /api/realtime {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400; # 24 hour timeout for WebSockets
    }

    # File Upload Configuration
    client_max_body_size 50M;
    
    # Logging
    access_log /var/log/nginx/api.voicehype.ai.access.log;
    error_log /var/log/nginx/api.voicehype.ai.error.log;
}
```

#### Option 2: Add to Existing voicehype.ai Config
```nginx
server {
    listen 443 ssl http2;
    server_name api.voicehype.ai;
    
    ssl_certificate /etc/letsencrypt/live/voicehype.ai/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/voicehype.ai/privkey.pem;
    
    location / {
        proxy_pass http://localhost:3000;
        # ... proxy settings
    }
}
```

### SSL Certificate Setup
```bash
# Get SSL certificate for API subdomain
sudo certbot --nginx -d api.voicehype.ai

# Or add to existing wildcard certificate
sudo certbot --nginx -d voicehype.ai -d *.voicehype.ai
```

### Process Management with PM2
```bash
# Install PM2 globally
npm install -g pm2

# Start API with PM2
pm2 start server.js --name "voicehype-api" --watch

# Save PM2 process list
pm2 save

# Setup PM2 startup on boot
pm2 startup
```

### Deployment Steps

#### 1. DNS Configuration
```bash
# Test DNS propagation
nslookup api.voicehype.ai
```

#### 2. Nginx Setup
```bash
# Create nginx config
sudo nano /etc/nginx/sites-available/api.voicehype.ai

# Enable the site
sudo ln -s /etc/nginx/sites-available/api.voicehype.ai /etc/nginx/sites-enabled/

# Test nginx configuration
sudo nginx -t

# Reload nginx
sudo systemctl reload nginx
```

#### 3. SSL & Testing
```bash
# Get SSL certificate
sudo certbot --nginx -d api.voicehype.ai

# Test API response
curl -I https://api.voicehype.ai

# Test API endpoints
curl -X POST https://api.voicehype.ai/api/transcribe \
  -H "Content-Type: application/json" \
  -d '{"test": "data"}'
```

## Next Steps
1. Review existing Deno edge functions and Express.js real-time implementation
2. Set up DNS CNAME record for api.voicehype.ai
3. Configure nginx with SSL for the API subdomain
4. Set up initial Express.js project structure
5. Implement shared security middleware
6. Build transcription endpoint with provider separation
7. Integrate database models and LLM optimization
8. Implement WebSocket real-time functionality
9. Deploy with PM2 process management
10. Test all endpoints and WebSocket functionality

---
*Bismillah - Let's build something beautiful! 🚀*
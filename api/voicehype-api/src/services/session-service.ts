import { RealtimeSession } from '../types/realtime';
import supabase from './supabase';

export class SessionService {
  private sessions: Map<string, RealtimeSession> = new Map();

  createSession(
    userId: string,
    apiKeyId: string,
    provider: 'assemblyai', // Only AssemblyAI for real-time
    model: string,
    language: string,
    maxDurationMs: number,
    pricingModel: string
  ): RealtimeSession {
    const sessionId = this.generateSessionId();
    
    const session: RealtimeSession = {
      id: sessionId,
      userId,
      apiKeyId,
      provider,
      model,
      language,
      startTime: Date.now(),
      maxDurationMs,
      isConnected: false,
      transcriptionReceived: false,
      finalTranscript: '',
      lastPartialTranscript: '',
      audioBytesSent: 0,
      audioDurationSeconds: 0,
      sessionInformationReceived: false,
      pricingModel
    };

    this.sessions.set(sessionId, session);
    return session;
  }

  getSession(sessionId: string): RealtimeSession | undefined {
    return this.sessions.get(sessionId);
  }

  removeSession(sessionId: string): boolean {
    return this.sessions.delete(sessionId);
  }

  async recordPendingUsage(session: RealtimeSession): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('usage_history')
        .insert({
          user_id: session.userId,
          api_key_id: session.apiKeyId,
          service: 'transcription',
          model: `${session.provider}/${session.model}-realtime`,
          amount: 0,
          cost: 0,
          pricing_model: session.pricingModel,
          status: 'pending',
          metadata: {
            sessionId: session.id,
            startTime: session.startTime,
            maxDurationMs: session.maxDurationMs
          }
        });

      if (error) {
        console.error('Error recording pending usage:', error);
        return false;
      }

      console.log(`Recorded pending usage for session ${session.id}`);
      return true;
    } catch (error) {
      console.error('Error in recordPendingUsage:', error);
      return false;
    }
  }

  async finalizeUsage(session: RealtimeSession): Promise<void> {
    try {
      // Check if we already finalized this session
      const { data: existingUsage } = await supabase
        .from('usage_history')
        .select('status')
        .eq('user_id', session.userId)
        .eq('service', 'transcription')
        .eq('model', `${session.provider}/${session.model}-realtime`)
        .eq('metadata->>sessionId', session.id)
        .single();

      if (existingUsage && existingUsage.status === 'success') {
        console.log(`Session ${session.id} already finalized, skipping duplicate finalization`);
        return;
      }

      // Calculate duration in minutes
      const durationMinutes = session.audioDurationSeconds > 0 
        ? session.audioDurationSeconds / 60 
        : (Date.now() - session.startTime) / 60000;

      console.log(`Finalizing usage for session ${session.id}: ${durationMinutes} minutes`);

      // Get service pricing
      const { data: servicePricing } = await supabase
        .from('service_pricing')
        .select('cost_per_unit')
        .eq('service', 'transcription')
        .eq('model', `${session.provider}/${session.model}-realtime`)
        .eq('is_active', true)
        .single();

      const costPerUnit = servicePricing?.cost_per_unit || 0;
      const totalCost = costPerUnit * durationMinutes;

      // Finalize usage
      await supabase.rpc('finalize_usage', {
        p_user_id: session.userId,
        p_api_key_id: session.apiKeyId,
        p_service: 'transcription',
        p_model: `${session.provider}/${session.model}-realtime`,
        p_amount: durationMinutes,
        p_cost: totalCost,
        p_pricing_model: session.pricingModel,
        p_metadata: {
          sessionId: session.id,
          startTime: session.startTime,
          endTime: Date.now(),
          audioBytesSent: session.audioBytesSent,
          hasTranscription: session.transcriptionReceived,
          audioDurationSeconds: session.audioDurationSeconds
        }
      });

      console.log(`Successfully finalized session ${session.id}`);
    } catch (error) {
      console.error(`Error finalizing usage for session ${session.id}:`, error);
    }
  }

  private generateSessionId(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      const r = Math.random() * 16 | 0;
      const v = c == 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
}
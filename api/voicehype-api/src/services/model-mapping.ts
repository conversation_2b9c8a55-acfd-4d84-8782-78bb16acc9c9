import { createClient, SupabaseClient, RealtimeChannel } from '@supabase/supabase-js';
import { ModelMapping } from '../types/optimize';

export class ModelMappingService {
  private supabase: SupabaseClient;
  private modelMappings: Map<string, ModelMapping> = new Map();
  private friendlyToUrl: Map<string, string> = new Map();
  private urlToFriendly: Map<string, string> = new Map();
  private channel: RealtimeChannel | null = null;

  constructor(supabaseUrl: string, supabaseServiceKey: string) {
    this.supabase = createClient(supabaseUrl, supabaseServiceKey);
  }

  async initialize(): Promise<void> {
    // Fetch initial mappings
    await this.fetchModelMappings();

    // Set up Realtime subscription
    this.channel = this.supabase
      .channel('models-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'models',
          filter: `model_type=eq.optimization`
        },
        (payload) => {
          console.log('Model change received:', payload);
          this.handleModelChange(payload);
        }
      )
      .subscribe((status) => {
        console.log('Realtime subscription status:', status);
      });
  }

  private async fetchModelMappings(): Promise<void> {
    const { data, error } = await this.supabase
      .from('models')
      .select('*')
      .eq('model_type', 'optimization')
      .eq('is_active', true);

    if (error) {
      console.error('Error fetching model mappings:', error);
      return;
    }

    // Reset mappings
    this.friendlyToUrl.clear();
    this.urlToFriendly.clear();
    this.modelMappings.clear();

    // Populate mappings
    data?.forEach((model) => {
      const friendlyName = model.friendly_name.toLowerCase();
      const actualUrl = model.actual_url.toLowerCase();

      this.modelMappings.set(model.id, model);
      this.friendlyToUrl.set(friendlyName, model.actual_url);
      this.urlToFriendly.set(actualUrl, model.friendly_name);
    });

    console.log('Model mappings updated:', {
      count: this.modelMappings.size,
      models: Array.from(this.modelMappings.values())
    });
  }

  private handleModelChange(payload: any): void {
    const { eventType, new: newRecord, old: oldRecord } = payload;

    switch (eventType) {
      case 'INSERT':
        if (newRecord.model_type === 'optimization' && newRecord.is_active) {
          this.addModelMapping(newRecord);
        }
        break;
      case 'UPDATE':
        if (oldRecord.model_type === 'optimization') {
          this.removeModelMapping(oldRecord);
          if (newRecord.is_active) {
            this.addModelMapping(newRecord);
          }
        }
        break;
      case 'DELETE':
        if (oldRecord.model_type === 'optimization') {
          this.removeModelMapping(oldRecord);
        }
        break;
    }
  }

  private addModelMapping(model: ModelMapping): void {
    const friendlyName = model.friendly_name.toLowerCase();
    const actualUrl = model.actual_url.toLowerCase();

    this.modelMappings.set(model.id, model);
    this.friendlyToUrl.set(friendlyName, model.actual_url);
    this.urlToFriendly.set(actualUrl, model.friendly_name);
  }

  private removeModelMapping(model: ModelMapping): void {
    const friendlyName = model.friendly_name.toLowerCase();
    const actualUrl = model.actual_url.toLowerCase();

    this.modelMappings.delete(model.id);
    this.friendlyToUrl.delete(friendlyName);
    this.urlToFriendly.delete(actualUrl);
  }

  resolveModelName(modelInput: string): string {
    const lowerModel = modelInput.toLowerCase();

    // Check if it's a friendly name
    if (this.friendlyToUrl.has(lowerModel)) {
      return this.friendlyToUrl.get(lowerModel)!;
    }

    // Check if it's already a URL
    if (this.urlToFriendly.has(lowerModel)) {
      return modelInput; // Already a URL
    }

    // Try to match hyphenated format with space-separated format
    const hyphenToSpace = lowerModel.replace(/-/g, ' ');
    if (this.friendlyToUrl.has(hyphenToSpace)) {
      return this.friendlyToUrl.get(hyphenToSpace)!;
    }

    // Try to match space-separated format with hyphenated format
    const spaceToHyphen = lowerModel.replace(/\s+/g, '-');
    if (this.friendlyToUrl.has(spaceToHyphen)) {
      return this.friendlyToUrl.get(spaceToHyphen)!;
    }

    // Return as-is if not found
    return modelInput;
  }

  close(): void {
    if (this.channel) {
      this.supabase.removeChannel(this.channel);
    }
  }
}
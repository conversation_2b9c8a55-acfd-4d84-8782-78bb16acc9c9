import { Request } from 'express';

export interface User {
  userId: string;
  email: string;
  isVerified: boolean;
}

export interface TranscriptionRequest {
  audioUrl: string;
  language?: string;
  model?: string;
}

export interface TranscriptionResponse {
  success: boolean;
  data: {
    transcription: string;
    model: string;
    language?: string;
  };
}

export interface TranscriptionResult {
  text: string;
}

// Define a custom request interface that extends Express Request and includes our user property
export interface AuthenticatedRequest extends Request {
  user?: User;
}

export interface CustomError extends Error {
  statusCode?: number;
}

// Define Supabase user type with exp property
export interface SupabaseUser {
  id: string;
  email?: string;
  email_confirmed_at?: string | null;
  exp?: number;
}
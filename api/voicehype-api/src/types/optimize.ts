export interface OptimizationRequest {
  messages: Array<{
    role: 'system' | 'user' | 'assistant';
    content: string;
  }>;
  model: string;
  maxOutputTokens?: number;
}

export interface OptimizationResponse {
  success: boolean;
  data: any; // Raw response from LLM provider
}

export interface ModelMapping {
  id: string;
  friendly_name: string;
  actual_url: string;
  model_type: string;
  is_active: boolean;
}

export interface ResolvedModel {
  provider: 'requesty';
  model: string;
}
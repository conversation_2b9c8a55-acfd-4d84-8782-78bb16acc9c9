import { RealtimeTranscriber } from 'assemblyai';

export interface RealtimeSession {
  id: string;
  userId: string;
  apiKeyId: string;
  provider: 'assemblyai'; // Only AssemblyAI for real-time
  model: string;
  language: string;
  startTime: number;
  maxDurationMs: number;
  isConnected: boolean;
  transcriptionReceived: boolean;
  finalTranscript: string;
  lastPartialTranscript: string;
  audioBytesSent: number;
  audioDurationSeconds: number;
  sessionInformationReceived: boolean;
  pricingModel: string;
  transcriber?: RealtimeTranscriber;
}

export interface RealtimeRequest {
  apiSecret?: string;
  authId?: string;
  accessToken?: string;
  provider?: 'assemblyai'; // Only AssemblyAI for real-time
  model?: string;
  language?: string;
  sampleRate?: number;
}

export interface RealtimeMessage {
  type: string;
  [key: string]: any;
}

export interface RealtimeError {
  error: boolean;
  statusCode: number;
  message: string;
  errorCode: string;
  timestamp: string;
}

export enum ErrorCode {
  UNAUTHORIZED = 'UNAUTHORIZED',
  INVALID_REQUEST = 'INVALID_REQUEST',
  UNSUPPORTED_MODEL = 'UNSUPPORTED_MODEL',
  SERVICE_ERROR = 'SERVICE_ERROR',
  INSUFFICIENT_CREDITS = 'INSUFFICIENT_CREDITS',
  UNPAID_BALANCE = 'UNPAID_BALANCE'
}
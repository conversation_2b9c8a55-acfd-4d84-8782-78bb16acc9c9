import app from './app';
import { createServer } from 'http';
// import { setupRealtimeServer } from './routes/realtime';
import dotenv from 'dotenv';

dotenv.config();

const PORT: number = parseInt(process.env.PORT || '3001', 10);
const server = createServer(app);

// Setup realtime WebSocket server
// setupRealtimeServer(server);

server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  // console.log(`🌐 WebSocket endpoint: ws://localhost:${PORT}/api/realtime`);
});

export default server;
// app.ts
import express, { Application } from 'express';
import corsMiddleware from './middleware/cors';
import { errorHandler } from './middleware/error-handler';
import { verifyApiSecret, verifyUser } from './middleware/auth';
import transcribeRoutes from './routes/transcribe';
import optimizeRoutes from './routes/optimize';
import transcribeOptimizeRoutes from './routes/transcribe-optimize'; // Add this
import { ModelMappingService } from './services/model-mapping';

const app: Application = express();

// Initialize model mapping service
const modelMappingService = new ModelMappingService(
  process.env.SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || ''
);

// Initialize the model mapping service
modelMappingService.initialize().catch(console.error);

// Make model mapping service available to routes
app.set('modelMappingService', modelMappingService);

// Middleware
app.use(corsMiddleware);
app.use(express.json({ limit: '100mb' }));

// Apply authentication middleware to all routes
app.use(verifyApiSecret);
app.use(verifyUser);

// Routes
app.use('/api/transcribe', transcribeRoutes);
app.use('/api/optimize', optimizeRoutes);
app.use('/api/transcribe-optimize', transcribeOptimizeRoutes); // Add this

// Error handling
app.use(errorHandler);

export default app;
import cors from 'cors';

const corsOptions: cors.CorsOptions = {
  origin: (origin, callback) => {
    // Allow requests from any origin during development
    if (process.env.NODE_ENV === 'development') {
      callback(null, true);
    } else {
      // In production, specify allowed origins
      const allowedOrigins = ['https://voicehype.ai', 'https://app.voicehype.ai'];
      if (allowedOrigins.includes(origin!) || !origin) {
        callback(null, true);
      } else {
        callback(new Error('Not allowed by CORS'));
      }
    }
  },
  credentials: true
};

export default cors(corsOptions);
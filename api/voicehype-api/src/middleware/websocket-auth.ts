import { IncomingMessage } from 'http';
import { URL } from 'url';
import { verifyApiSecret, verifyUser } from './auth';
import { AuthenticatedRequest } from '../types';

export async function authenticateWebSocket(req: IncomingMessage): Promise<{
  authenticated: boolean;
  user?: any;
  error?: any;
}> {
  try {
    // Extract authentication data from query parameters (since WebSocket doesn't support custom headers in browsers)
    const url = new URL(req.url!, `http://${req.headers.host}`);
    const apiSecret = url.searchParams.get('apiSecret') || req.headers['x-api-secret'] as string;
    const authId = url.searchParams.get('authId') || req.headers['x-auth-id'] as string;
    const accessToken = url.searchParams.get('accessToken') || req.headers['x-access-token'] as string;

    if (!apiSecret || !authId || !accessToken) {
      return {
        authenticated: false,
        error: {
          error: true,
          statusCode: 401,
          message: 'Missing authentication parameters',
          errorCode: 'UNAUTHORIZED',
          timestamp: new Date().toISOString()
        }
      };
    }

    // Create a proper mock request object that extends Express Request
    const mockReq: Partial<AuthenticatedRequest> = {
      headers: {
        'x-api-secret': apiSecret,
        'x-auth-id': authId,
        'x-access-token': accessToken
      } as any,
      // Add minimal required methods
      get: function(name: string) {
        return this.headers?.[name.toLowerCase()];
      },
      header: function(name: string) {
        return this.headers?.[name.toLowerCase()];
      }
    } as AuthenticatedRequest;

    // Mock response object
    let responseSent = false;
    let responseStatus = 200;
    let responseJson: any = null;

    const mockRes = {
      status: (code: number) => {
        responseStatus = code;
        return {
          json: (data: any) => {
            responseJson = data;
            responseSent = true;
            return mockRes;
          }
        };
      }
    };

    // Verify API secret
    try {
      await new Promise<void>((resolve, reject) => {
        verifyApiSecret(mockReq as AuthenticatedRequest, mockRes as any, (err?: any) => {
          if (err) {
            reject(err);
          } else {
            resolve();
          }
        });
      });

      if (responseSent) {
        return {
          authenticated: false,
          error: {
            error: true,
            statusCode: responseStatus,
            message: responseJson?.error || 'Invalid API secret',
            errorCode: 'UNAUTHORIZED',
            timestamp: new Date().toISOString()
          }
        };
      }
    } catch (error) {
      return {
        authenticated: false,
        error: {
          error: true,
          statusCode: 401,
          message: 'API secret verification failed',
          errorCode: 'UNAUTHORIZED',
          timestamp: new Date().toISOString()
        }
      };
    }

    // Verify user
    try {
      await new Promise<void>((resolve, reject) => {
        verifyUser(mockReq as AuthenticatedRequest, mockRes as any, (err?: any) => {
          if (err) {
            reject(err);
          } else {
            resolve();
          }
        });
      });

      if (responseSent) {
        return {
          authenticated: false,
          error: {
            error: true,
            statusCode: responseStatus,
            message: responseJson?.error || 'User verification failed',
            errorCode: 'UNAUTHORIZED',
            timestamp: new Date().toISOString()
          }
        };
      }

      if (!mockReq.user) {
        return {
          authenticated: false,
          error: {
            error: true,
            statusCode: 401,
            message: 'User not authenticated',
            errorCode: 'UNAUTHORIZED',
            timestamp: new Date().toISOString()
          }
        };
      }

      return {
        authenticated: true,
        user: mockReq.user
      };
    } catch (error) {
      return {
        authenticated: false,
        error: {
          error: true,
          statusCode: 401,
          message: 'User verification failed',
          errorCode: 'UNAUTHORIZED',
          timestamp: new Date().toISOString()
        }
      };
    }
  } catch (error) {
    return {
      authenticated: false,
      error: {
        error: true,
        statusCode: 500,
        message: 'Authentication error',
        errorCode: 'SERVICE_ERROR',
        timestamp: new Date().toISOString()
      }
    };
  }
}
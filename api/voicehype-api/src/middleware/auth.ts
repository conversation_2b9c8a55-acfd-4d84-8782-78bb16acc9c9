import { Request, Response, NextFunction } from 'express';
import supabase from '../services/supabase';
import { User, AuthenticatedRequest, SupabaseUser } from '../types';

// Verify API secret from the request headers
export const verifyApiSecret = (req: Request, res: Response, next: NextFunction): void => {
  const apiSecret = req.headers['x-api-secret'];
  if (!apiSecret || apiSecret !== process.env.API_SECRET) {
    res.status(401).json({ error: 'Invalid API secret' });
    return;
  }
  next();
};

// Verify user using the access token and authId
export const verifyUser = (req: Request, res: Response, next: NextFunction): void => {
  // Create an async function and handle errors properly
  (async () => {
    try {
      const authId = req.headers['x-auth-id'] as string;
      const accessToken = req.headers['x-access-token'] as string;

      if (!authId || !accessToken) {
        res.status(401).json({ error: 'Missing authentication headers' });
        return;
      }

      // Create a Supabase client with the user's access token
      const { data: { user }, error } = await supabase.auth.getUser(accessToken);

      if (error || !user) {
        res.status(401).json({ error: 'Invalid access token' });
        return;
      }

      // Cast user to our SupabaseUser interface to access exp property
      const supabaseUser = user as SupabaseUser;

      // Verify the auth ID matches
      if (supabaseUser.id !== authId) {
        res.status(401).json({ error: 'Auth ID mismatch' });
        return;
      }

      // Check if token is expired (optional, Supabase handles this automatically)
      const now = Math.floor(Date.now() / 1000);
      if (supabaseUser.exp && supabaseUser.exp < now) {
        res.status(401).json({ error: 'Access token expired' });
        return;
      }

      // Attach user info to the request
      // Use a type assertion to 'unknown' first, then to 'AuthenticatedRequest'
      const authReq = req as unknown as AuthenticatedRequest;
      authReq.user = {
        userId: supabaseUser.id,
        email: supabaseUser.email || '',
        isVerified: supabaseUser.email_confirmed_at !== null
      };

      next();
    } catch (error) {
      console.error('Authentication error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  })().catch(next); // Catch any unhandled promise rejections
};
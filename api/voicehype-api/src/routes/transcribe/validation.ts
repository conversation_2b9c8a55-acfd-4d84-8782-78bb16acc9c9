import { body } from 'express-validator';

export const validateTranscriptionRequest = [
  body('audioUrl')
    .notEmpty()
    .withMessage('audioUrl is required')
    .isString()
    .withMessage('audioUrl must be a string'),
  body('language')
    .optional()
    .isString()
    .withMessage('language must be a string'),
  body('model')
    .optional()
    .isString()
    .withMessage('model must be a string')
    .isIn(['whisper-1'])
    .withMessage('model must be one of: whisper-1'),
];
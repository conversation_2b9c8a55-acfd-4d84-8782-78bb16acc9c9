import { Router, Request, Response, NextFunction } from 'express';
import { transcribeWith<PERSON>emonFox } from './providers/lemonfox';
import { validateTranscriptionRequest } from './validation';
import { base64ToBuffer } from '../../utils/audio-converter';
import { handleValidationErrors } from '../../middleware/validation';
import { TranscriptionRequest, TranscriptionResponse, AuthenticatedRequest } from '../../types';
import supabase from '../../services/supabase';

const router = Router();
const MAX_FILE_SIZE = 100 * 1024 * 1024;

// POST /api/transcribe
router.post('/', 
  validateTranscriptionRequest,
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { audioUrl, model = 'whisper-1', language = 'en' } = req.body as TranscriptionRequest;
      
      // BAN HEBREW LANGUAGE - Add this validation check
      if (language === 'he' || language.toLowerCase() === 'hebrew') {
        return res.status(403).json({ 
          error: 'Hebrew language is not supported',
          errorCode: 'LANGUAGE_NOT_SUPPORTED'
        });
      }
      
      // Check if user is authenticated
      if (!req.user) {
        return res.status(401).json({ error: 'User not authenticated' });
      }
      
      // Convert base64 to Buffer
      const audioBuffer = base64ToBuffer(audioUrl);
      
      // Check file size
      if (audioBuffer.length > MAX_FILE_SIZE) {
        return res.status(413).json({ 
          error: `File too large. Maximum size is ${MAX_FILE_SIZE / (1024 * 1024)}MB` 
        });
      }
      
      // Get audio file size for duration calculation
      const audioSize = audioBuffer.length;
      
      // Calculate audio duration from the audio data
      let audioDurationSeconds: number;
      try {
        // Validate WAV header
        if (audioBuffer.length < 44) {
          throw new Error('Audio data too small to contain WAV header');
        }
        
        // Extract WAV header information
        const header = new DataView(audioBuffer.buffer, 0, 44);
        
        // Check RIFF header
        const riffHeader = String.fromCharCode(...new Uint8Array(audioBuffer.buffer, 0, 4));
        if (riffHeader !== 'RIFF') {
          throw new Error('Invalid WAV file: Missing RIFF header');
        }
        
        // Extract audio parameters
        const numChannels = header.getUint16(22, true);
        const sampleRate = header.getUint32(24, true);
        const bitsPerSample = header.getUint16(34, true);
        
        // Calculate duration
        const bytesPerSample = bitsPerSample / 8;
        const dataSize = audioBuffer.length - 44;
        audioDurationSeconds = dataSize / (sampleRate * numChannels * bytesPerSample);
        
        console.log('Calculated audio duration:', {
          numChannels,
          sampleRate,
          bitsPerSample,
          dataSize,
          duration: audioDurationSeconds
        });
      } catch (error) {
        console.error('Error calculating audio duration:', error);
        return res.status(400).json({ error: 'Failed to calculate audio duration' });
      }
      // Convert to minutes for billing purposes
      const audioDurationMinutes = Number((audioDurationSeconds / 60).toFixed(9));
      
      // Get the LemonFox API key from environment
      const apiKey = process.env.LEMONFOX_API_KEY;
      if (!apiKey) {
        throw new Error('LemonFox API key not configured');
      }
      
      // Check usage allowance
      const modelName = `${model}`;
      const { data: pricingCheck, error: pricingError } = await supabase
        .rpc('check_usage_allowance', {
          p_user_id: req.user.userId,
          p_service: 'transcription',
          p_model: modelName,
          p_amount: audioDurationMinutes,
        });
        
      if (pricingError || !pricingCheck || !pricingCheck[0] || !pricingCheck[0].can_use) {
        console.error('Insufficient usage allowance:', {
          pricingCheck,
          audioDurationMinutes
        });
        
        const errorMessage = pricingCheck?.[0]?.error_code || 'Insufficient credits or quota for this operation';
        
        if (errorMessage.includes('transcription_')) {
          const detailedMessage = errorMessage.replace(/^transcription_/, '').replace(/_/g, ' ');
          return res.status(402).json({ 
            error: detailedMessage,
            errorCode: 'INSUFFICIENT_CREDITS'
          });
        }
        
        return res.status(402).json({ 
          error: errorMessage,
          errorCode: 'INSUFFICIENT_CREDITS'
        });
      }
      
      // Call LemonFox API
      const { text } = await transcribeWithLemonFox(audioBuffer, apiKey, model, language);
      
      // Return the transcription immediately
      const response: TranscriptionResponse = {
        success: true,
        data: {
          transcription: text,
          model,
          language
        }
      };
      
      res.json(response);
      
      // Finalize usage asynchronously (don't await)
      finalizeUsageAsync(
        req.user.userId,
        'transcription',
        modelName,
        audioDurationMinutes,
        audioDurationSeconds,
        text?.length || 0,
        audioSize,
        pricingCheck[0].pricing_model
      ).catch(error => {
        console.error('Failed to finalize usage:', error);
      });
      
    } catch (error) {
      // Return error response immediately
      next(error);
      
      // Record failed usage asynchronously (don't await)
      if (req.user) {
        const { audioUrl, model = 'whisper-1' } = req.body as TranscriptionRequest;
        const audioBuffer = base64ToBuffer(audioUrl);
        const audioSize = audioBuffer.length;
        
        // Calculate duration (simplified)
        const audioDurationSeconds = audioSize / 16000; // Rough estimate
        const audioDurationMinutes = audioDurationSeconds / 60;
        const modelName = `${model}`;
        
        recordFailedUsageAsync(
          req.user.userId,
          'transcription',
          modelName,
          audioDurationMinutes,
          audioSize,
          error instanceof Error ? error.message : 'Unknown error'
        ).catch(recordError => {
          console.error('Failed to record failed usage:', recordError);
        });
      }
    }
  }
);

// Helper function to finalize usage asynchronously
async function finalizeUsageAsync(
  userId: string,
  service: string,
  model: string,
  amount: number,
  audioDurationSeconds: number,
  transcriptionLength: number,
  audioSize: number,
  pricingModel: string
) {
  try {
    await supabase
      .rpc('finalize_usage', {
        p_user_id: userId,
        p_service: service,
        p_model: model,
        p_amount: amount,
        p_cost: 0, // Cost is calculated in the database function
        p_pricing_model: pricingModel,
        p_metadata: {
          audioSize,
          transcriptionLength,
          audioDurationSeconds
        }
      });
  } catch (error) {
    console.error('Error in finalizeUsageAsync:', error);
    throw error;
  }
}

// Helper function to record failed usage asynchronously
async function recordFailedUsageAsync(
  userId: string,
  service: string,
  model: string,
  amount: number,
  audioSize: number,
  errorMessage: string
) {
  try {
    await supabase
      .from('usage_history')
      .insert({
        user_id: userId,
        service: service,
        model: model,
        amount: amount,
        cost: 0,
        pricing_model: 'credits',
        status: 'failed',
        metadata: {
          audioSize,
          error: errorMessage
        }
      });
  } catch (error) {
    console.error('Error in recordFailedUsageAsync:', error);
    throw error;
  }
}

export default router;
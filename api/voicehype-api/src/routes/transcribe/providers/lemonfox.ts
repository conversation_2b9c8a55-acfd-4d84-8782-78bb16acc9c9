import fetch from 'node-fetch';
import FormData from 'form-data';
import { TranscriptionResult } from '../../../types';

/**
 * Transcribe audio data using LemonFox Whisper API
 * @param audioBuffer - The audio data as a Buffer
 * @param apiKey - LemonFox API key
 * @param model - The model to use (default: whisper-1)
 * @param language - Optional language code
 * @returns Promise<TranscriptionResult>
 */
export async function transcribeWithLemonFox(
  audioBuffer: Buffer,
  apiKey: string,
  model: string = 'whisper-1',
  language?: string
): Promise<TranscriptionResult> {
  const endpoint = 'https://api.lemonfox.ai/v1/audio/transcriptions';

  // Create FormData with audio file
  const form = new FormData();
  form.append('file', audioBuffer, {
    filename: 'audio.wav',
    contentType: 'audio/wav'
  });
  form.append('model', model);

  if (language) {
    form.append('language', language);
  }

  const response = await fetch(endpoint, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      ...form.getHeaders()
    },
    body: form
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`LemonFox API error: ${response.status} - ${errorText}`);
  }

  const result = await response.json() as { text: string };

  if (!result.text) {
    throw new Error('Invalid response from LemonFox API: missing text field');
  }

  return { text: result.text };
}
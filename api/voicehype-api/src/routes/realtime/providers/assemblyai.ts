import { RealtimeTranscriber } from 'assemblyai';
import { RealtimeSession } from '../../../types/realtime';

export class AssemblyAIProvider {
  private transcriber: RealtimeTranscriber;
  private session: RealtimeSession;
  private onTranscript: (transcript: any) => void;
  private onSessionInformation: (sessionInfo: any) => void;
  private onError: (error: any) => void;
  private onClose: (code: number, reason: string) => void;

  constructor(
    session: RealtimeSession,
    apiKey: string,
    sampleRate: number = 16000,
    onTranscript: (transcript: any) => void,
    onSessionInformation: (sessionInfo: any) => void,
    onError: (error: any) => void,
    onClose: (code: number, reason: string) => void
  ) {
    this.session = session;
    this.onTranscript = onTranscript;
    this.onSessionInformation = onSessionInformation;
    this.onError = onError;
    this.onClose = onClose;

    // Create the transcriber
    this.transcriber = new RealtimeTranscriber({
      api<PERSON>ey,
      sampleRate,
      encoding: 'pcm_s16le',
    });

    // Set up event handlers
    this.transcriber.on('open', () => {
      console.log(`✅ Connected to AssemblyAI for session ${session.id}`);
      session.isConnected = true;
    });

    this.transcriber.on('transcript', (transcript) => {
      this.onTranscript(transcript);
    });

    this.transcriber.on('session_information', (sessionInfo) => {
      this.onSessionInformation(sessionInfo);
    });

    this.transcriber.on('error', (error) => {
      this.onError(error);
    });

    this.transcriber.on('close', (code: number, reason: string) => {
      session.isConnected = false;
      this.onClose(code, reason);
    });
  }

  async connect(): Promise<void> {
    await this.transcriber.connect();
  }

  sendAudio(audioData: Buffer): void {
    // Convert Buffer to ArrayBuffer for AssemblyAI SDK
    const arrayBuffer = audioData.buffer.slice(
      audioData.byteOffset,
      audioData.byteOffset + audioData.byteLength
    );
    this.transcriber.sendAudio(arrayBuffer);
  }

  close(): void {
    this.transcriber.close();
  }
}
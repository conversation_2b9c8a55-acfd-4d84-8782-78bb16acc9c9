import { WebSocketServer } from 'ws';
import { Server } from 'http';
import { authenticateWebSocket } from '../../middleware/websocket-auth';
import { SessionManager } from './session-manager';

export function setupRealtimeServer(server: Server): void {
  const wss = new WebSocketServer({
    server,
    path: '/api/realtime'
  });

  const sessionManager = new SessionManager();

  wss.on('connection', async (ws, req) => {
    console.log('New WebSocket connection:', {
      origin: req.headers.origin
    });

    // Authenticate the connection using our existing middleware
    const auth = await authenticateWebSocket(req);
    
    if (!auth.authenticated || !auth.user) {
      if (auth.error) {
        ws.send(JSON.stringify(auth.error));
      }
      ws.close();
      return;
    }

    // Handle the connection
    try {
      await sessionManager.handleConnection(ws, req, auth.user);
    } catch (error) {
      console.error('Error handling WebSocket connection:', error);
      ws.close();
    }
  });

  console.log('📡 Realtime transcription server initialized');
}
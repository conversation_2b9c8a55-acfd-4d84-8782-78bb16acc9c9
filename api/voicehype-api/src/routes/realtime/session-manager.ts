import { WebSocket } from 'ws';
import { RealtimeSession, RealtimeMessage, ErrorCode } from '../../types/realtime';
import { SessionService } from '../../services/session-service';
import { AssemblyAIProvider } from './providers/assemblyai';
import supabase from '../../services/supabase';

export class SessionManager {
  private sessionService: SessionService;
  private assemblyaiApiKey: string;

  constructor() {
    this.sessionService = new SessionService();
    this.assemblyaiApiKey = process.env.ASSEMBLYAI_API_KEY || '';
  }

  async handleConnection(ws: WebSocket, req: any, user: any): Promise<void> {
    const { userId, email } = user;
    // We don't have apiKeyId in this auth method, so we'll use a placeholder
    const apiKeyId = 'websocket';

    const url = new URL(req.url, `http://${req.headers.host}`);
    const model = url.searchParams.get('model') || 'best';
    const language = url.searchParams.get('language') || 'en';
    const sampleRate = parseInt(url.searchParams.get('sampleRate') || '16000');

    // Check usage allowance
    const modelForDb = `assemblyai/${model}-realtime`;
    const { data: usageCheck, error: usageError } = await supabase
      .rpc('check_usage_allowance', {
        p_user_id: userId,
        p_service: 'transcription',
        p_model: modelForDb,
        p_amount: 1,
        p_api_key_id: apiKeyId,
        p_is_input_only: false
      });

    if (usageError || !usageCheck || !usageCheck[0] || !usageCheck[0].can_use) {
      const errorMessage = usageCheck?.[0]?.error_code || 'Insufficient credits';
      this.sendError(ws, 402, errorMessage, ErrorCode.INSUFFICIENT_CREDITS);
      ws.close();
      return;
    }

    const maxDurationMinutes = usageCheck[0].max_output || 20;
    const maxDurationMs = maxDurationMinutes * 60 * 1000;
    const pricingModel = usageCheck[0].pricing_model || 'credits';

    // Create session
    const session = this.sessionService.createSession(
      userId,
      apiKeyId,
      'assemblyai', // Only AssemblyAI for real-time
      model,
      language,
      maxDurationMs,
      pricingModel
    );

    // Record pending usage
    await this.sessionService.recordPendingUsage(session);

    // Set up provider
    let providerInstance: AssemblyAIProvider;

    if (!this.assemblyaiApiKey) {
      this.sendError(ws, 500, 'AssemblyAI API key not configured', ErrorCode.SERVICE_ERROR);
      ws.close();
      return;
    }

    providerInstance = new AssemblyAIProvider(
      session,
      this.assemblyaiApiKey,
      sampleRate,
      (transcript) => this.handleTranscript(ws, session, transcript),
      (sessionInfo) => this.handleSessionInformation(ws, session, sessionInfo),
      (error) => this.handleError(ws, session, error),
      (code, reason) => this.handleProviderClose(ws, session, code, reason)
    );

    // Store the provider instance in the session
    session.transcriber = providerInstance as any;

    // Set up WebSocket message handlers
    ws.on('message', (data) => this.handleMessage(ws, session, data));
    ws.on('close', () => this.handleClose(ws, session));
    ws.on('error', (error) => this.handleError(ws, session, error));

    // Connect to provider
    try {
      await providerInstance.connect();

      // Send connection established event
      this.sendMessage(ws, {
        type: 'connected',
        sessionId: session.id,
        maxDurationMs: session.maxDurationMs
      });

      // Send transcription-ready event
      this.sendMessage(ws, {
        type: 'transcription_ready',
        message: 'Transcription service is ready to process audio',
        sessionId: session.id,
        timestamp: Date.now()
      });
    } catch (error) {
      this.sendError(ws, 500, 'Failed to connect to transcription service', ErrorCode.SERVICE_ERROR);
      ws.close();
    }
  }

  private handleMessage(ws: WebSocket, session: RealtimeSession, data: any): void {
    try {
      // Check if it's binary audio data
      if (data instanceof Buffer) {
        if (session.isConnected && session.transcriber) {
          session.audioBytesSent += data.length;
          // Convert Buffer to ArrayBuffer for AssemblyAI SDK
          const arrayBuffer = data.buffer.slice(data.byteOffset, data.byteOffset + data.byteLength);
          session.transcriber.sendAudio(arrayBuffer);
        }
      } else {
        // Handle JSON control messages
        const message = JSON.parse(data.toString());
        
        if (message.type === 'close') {
          console.log(`Client requested close for session ${session.id}`);
          if (session.transcriber) {
            session.transcriber.close();
          }
        } else if (message.type === 'ping') {
          this.sendMessage(ws, {
            type: 'pong',
            timestamp: Date.now()
          });
        } else if (message.type === 'connected' && message.sampleRate) {
          // Update sample rate if provided
          console.log(`Using sample rate: ${message.sampleRate}Hz`);
        }
      }
    } catch (error) {
      console.error(`Error handling client message for session ${session.id}:`, error);
      this.sendError(ws, 400, 'Invalid message format', ErrorCode.INVALID_REQUEST);
    }
  }

  private handleTranscript(ws: WebSocket, session: RealtimeSession, transcript: any): void {
    try {
      if (transcript.message_type === 'FinalTranscript') {
        session.transcriptionReceived = true;
        if (transcript.text && transcript.text.trim().length > 0) {
          session.finalTranscript += transcript.text + " ";
        }
      } else if (transcript.message_type === 'PartialTranscript') {
        session.transcriptionReceived = true;
        if (transcript.text && transcript.text.trim().length > 0) {
          session.lastPartialTranscript = transcript.text;
        }
      }

      // Forward to client
      ws.send(JSON.stringify(transcript));
    } catch (error) {
      console.error(`Error handling transcript for session ${session.id}:`, error);
    }
  }

  private handleSessionInformation(ws: WebSocket, session: RealtimeSession, sessionInfo: any): void {
    try {
      // Extract audio duration from the message
      const audioDuration = sessionInfo.audio_duration_seconds || 0;
      session.audioDurationSeconds = audioDuration;
      session.sessionInformationReceived = true;

      console.log(`Audio duration for session ${session.id}: ${audioDuration}s`);

      // Forward to client
      ws.send(JSON.stringify({
        type: 'SessionInformation',
        audio_duration_seconds: audioDuration,
        sessionId: session.id
      }));

      // Finalize usage and cleanup
      this.finalizeAndCleanup(ws, session);
    } catch (error) {
      console.error(`Error handling session information for session ${session.id}:`, error);
    }
  }

  private handleError(ws: WebSocket, session: RealtimeSession, error: any): void {
    console.error(`Provider error for session ${session.id}:`, error);
    this.sendError(ws, 500, 'Transcription service error', ErrorCode.SERVICE_ERROR);
  }

  private handleProviderClose(ws: WebSocket, session: RealtimeSession, code: number, reason: string): void {
    console.log(`Provider connection closed for session ${session.id}`, { code, reason });
    
    if (!reason.includes('clean')) {
      this.sendMessage(ws, {
        type: 'service_disconnected',
        code: code,
        reason: reason
      });
    }

    // Wait for SessionInformation message before cleanup
    console.log(`Waiting for SessionInformation message for session ${session.id} before cleanup...`);

    // Create a timeout that will run cleanup after 10 seconds if SessionInformation hasn't arrived
    setTimeout(() => {
      if (!session.sessionInformationReceived) {
        console.log(`SessionInformation message not received for session ${session.id} after timeout, finalizing with local calculation`);
        this.finalizeAndCleanup(ws, session);
      }
    }, 10000);
  }

  private handleClose(ws: WebSocket, session: RealtimeSession): void {
    console.log(`Client disconnected for session ${session.id}`);
    
    // Just close the provider connection - let SessionInformation handler do the rest
    if (session.transcriber && session.isConnected) {
      console.log(`Client disconnected, closing provider connection for session ${session.id}`);
      session.transcriber.close();
    } else {
      // If no active connection, just do cleanup without finalizing
      this.cleanup(ws, session);
    }
  }

  private finalizeAndCleanup(ws: WebSocket, session: RealtimeSession): void {
    // Finalize usage
    this.sessionService.finalizeUsage(session);
    
    // Then cleanup
    this.cleanup(ws, session);
  }

  private cleanup(ws: WebSocket, session: RealtimeSession): void {
    // Send final transcript if available
    const completeTranscript = session.finalTranscript.trim() || session.lastPartialTranscript.trim();
    if (completeTranscript && ws.readyState === 1) {
      ws.send(JSON.stringify({
        message_type: 'CompleteTranscript',
        text: completeTranscript,
        sessionId: session.id
      }));
    }

    if (ws.readyState === 1) {
      ws.send(JSON.stringify({
        type: 'finalized',
        message: 'Session finalized successfully',
        sessionId: session.id
      }));
    }

    // Remove session
    this.sessionService.removeSession(session.id);
    
    // Close WebSocket if still open
    if (ws.readyState === 1) {
      ws.close();
    }
  }

  private sendMessage(ws: WebSocket, message: RealtimeMessage): void {
    if (ws.readyState === 1) {
      ws.send(JSON.stringify(message));
    }
  }

  private sendError(ws: WebSocket, statusCode: number, message: string, errorCode: ErrorCode): void {
    const error = {
      error: true,
      statusCode,
      message,
      errorCode,
      timestamp: new Date().toISOString()
    };

    if (ws.readyState === 1) {
      ws.send(JSON.stringify(error));
    }
  }
}
import fetch from 'node-fetch';

export interface RequestyResponse {
  // Raw response structure - will be returned as-is
  [key: string]: any;
}

export async function optimizeWithRequesty(
  messages: Array<{ role: string; content: string }>,
  model: string,
  maxOutputTokens?: number,
  apiKey?: string
): Promise<RequestyResponse> {
  if (!apiKey) {
    throw new Error('Requesty API key is required');
  }

  const endpoint = 'https://router.requesty.ai/v1/chat/completions';

  const requestBody = {
    model,
    messages,
    max_tokens: maxOutputTokens || 4096,
    temperature: 0.7,
  };

  const response = await fetch(endpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`
    },
    body: JSON.stringify(requestBody)
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Requesty API error: ${response.status} - ${errorText}`);
  }

  // Type assertion to handle the unknown response
  const data = await response.json() as RequestyResponse;
  return data;
}
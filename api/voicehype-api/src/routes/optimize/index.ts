// routes/optimize/index.ts
import { Router, Response, NextFunction } from 'express';
import { optimizeWithRequesty } from './providers/requesty';
import { validateOptimizationRequest } from './validation';
import { handleValidationErrors } from '../../middleware/validation';
import { AuthenticatedRequest } from '../../types';
import { OptimizationRequest, OptimizationResponse } from "../../types/optimize";

const router = Router();

// POST /api/optimize
router.post('/', 
  validateOptimizationRequest,
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { messages, model, maxOutputTokens } = req.body as OptimizationRequest;
      
      // Check if user is authenticated
      if (!req.user) {
        return res.status(401).json({ error: 'User not authenticated' });
      }
      
      // Resolve model name (handle friendly names)
      const modelMappingService = req.app.get('modelMappingService');
      const resolvedModel = modelMappingService ? modelMappingService.resolveModelName(model) : model;
      
      // Default max tokens if not provided
      const resolvedMaxOutputTokens = maxOutputTokens || 4096;
      
      // Get the Requesty API key from environment
      const apiKey = process.env.REQUESTY_API_KEY;
      if (!apiKey) {
        throw new Error('Requesty API key not configured');
      }
      
      // Call Requesty API
      const result = await optimizeWithRequesty(messages, resolvedModel, resolvedMaxOutputTokens, apiKey);
      
      // Return the response immediately
      const response: OptimizationResponse = {
        success: true,
        data: result
      };
      
      res.json(response);
      
    } catch (error) {
      // Return error response immediately
      next(error);
    }
  }
);

export default router;
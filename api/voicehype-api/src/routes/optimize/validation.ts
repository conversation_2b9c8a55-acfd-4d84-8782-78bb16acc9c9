import { body } from 'express-validator';

export const validateOptimizationRequest = [
  body('messages')
    .isArray({ min: 1 })
    .withMessage('messages must be an array with at least one message'),
  body('messages.*.role')
    .isIn(['system', 'user', 'assistant'])
    .withMessage('Each message must have a valid role'),
  body('messages.*.content')
    .isString()
    .withMessage('Each message must have a content string'),
  body('model')
    .isString()
    .withMessage('model must be a string')
    .notEmpty()
    .withMessage('model is required'),
  body('maxOutputTokens')
    .optional()
    .isInt({ min: 1 })
    .withMessage('maxOutputTokens must be a positive integer'),
];
// src/routes/transcribe-optimize/validation.ts
import { body } from 'express-validator';

export const validateTranscribeOptimizeRequest = [
  body('audioUrl')
    .notEmpty()
    .withMessage('Audio URL is required')
    .isString()
    .withMessage('Audio URL must be a string'),
  body('model')
    .optional()
    .isString()
    .withMessage('Model must be a string'),
  body('language')
    .optional()
    .isString()
    .withMessage('Language must be a string')
    .custom((value) => {
      if (value === 'he' || value.toLowerCase() === 'hebrew') {
        throw new Error('Hebrew language is not supported');
      }
      return true;
    }),
  body('messages')
    .isArray({ min: 1 })
    .withMessage('Messages must be an array with at least one message')
    .custom((messages) => {
      if (!Array.isArray(messages)) return false;
      
      for (const message of messages) {
        if (typeof message !== 'object' || message === null) return false;
        if (typeof message.role !== 'string') return false;
        if (typeof message.content !== 'string') return false;
      }
      
      return true;
    })
    .withMessage('Messages must be an array of objects with role and content'),
  body('optimizationModel')
    .optional()
    .isString()
    .withMessage('Optimization model must be a string'),
  body('maxOutputTokens')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Max output tokens must be a positive integer'),
];
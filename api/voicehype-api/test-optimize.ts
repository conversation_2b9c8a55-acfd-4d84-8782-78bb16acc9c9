import { createClient } from '@supabase/supabase-js';
import fetch from 'node-fetch';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Supabase configuration
const supabaseUrl = 'https://supabase.voicehype.ai';
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY || '';

// VoiceHype API configuration
const apiUrl = 'https://api.voicehype.ai';
const apiSecret = process.env.API_SECRET || '';

// Test user credentials (replace with your actual credentials)
const testUser = {
  email: '<EMAIL>',
  password: 'Kq@12497890'
};

// Test messages for optimization
const testMessages = [
  {
    role: 'system',
    content: 'You are a helpful assistant that optimizes text for clarity and conciseness.'
  },
  {
    role: 'user',
    content: 'The following text is wordy and could be improved: "I am writing this email to inform you that we have decided to postpone the meeting that was scheduled for tomorrow to a later date that has not yet been determined."'
  }
];

async function testOptimization() {
  try {
    console.log('🔄 Signing in to Supabase...');
    
    // Create Supabase client
    const supabase = createClient(supabaseUrl, supabaseAnonKey);
    
    // Sign in
    const { data, error } = await supabase.auth.signInWithPassword({
      email: testUser.email,
      password: testUser.password
    });
    
    if (error) {
      console.error('❌ Sign in error:', error.message);
      return;
    }
    
    if (!data.session) {
      console.error('❌ No session returned');
      return;
    }
    
    const { access_token, user } = data.session;
    console.log('✅ Sign in successful!');
    console.log('📧 User:', user.email);
    console.log('🆔 User ID:', user.id);
    console.log('🔑 Access Token:', access_token.substring(0, 20) + '...');
    
    // Test the optimization endpoint with different models
    const models = [
      'Claude Haiku',
      'DeepSeek V3',
      'Claude 4 Sonnet',
      'LLaMa 3.3 70B Turbo'
    ];
    
    for (const model of models) {
      console.log(`\n🔄 Testing optimization with ${model}...`);
      await testOptimizationWithModel(user.id, access_token, model);
    }
    
  } catch (error) {
    console.error('❌ Test error:', error);
  }
}

async function testOptimizationWithModel(authId: string, accessToken: string, model: string) {
  try {
    // Prepare the request body
    const requestBody = {
      messages: testMessages,
      model,
      maxOutputTokens: 500
    };
    
    // Make the API request
    const response = await fetch(`${apiUrl}/api/optimize`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-secret': apiSecret,
        'x-auth-id': authId,
        'x-access-token': accessToken
      },
      body: JSON.stringify(requestBody)
    });
    
    console.log('📡 Response status:', response.status);
    
    // Parse the response
    const responseData = await response.json() as any;
    
    if (response.ok && responseData.success) {
      console.log('✅ Optimization successful!');
      console.log('📝 Raw response preview:', JSON.stringify(responseData.data).substring(0, 200) + '...');
    } else {
      console.error('❌ Optimization failed:', responseData.error);
    }
    
  } catch (error) {
    console.error('❌ Optimization test error:', error);
  }
}

// Run the test
testOptimization();
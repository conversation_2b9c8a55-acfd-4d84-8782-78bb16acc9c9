import { createClient } from '@supabase/supabase-js';
import fetch from 'node-fetch';
import dotenv from 'dotenv';
import { readFileSync, existsSync } from 'fs';
import { join } from 'path';

// Load environment variables
dotenv.config();

// Supabase configuration
const supabaseUrl = 'https://supabase.voicehype.ai';
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY || '';

// VoiceHype API configuration
const apiUrl = 'https://api.voicehype.ai';
const apiSecret = process.env.API_SECRET || '';

// Test user credentials (replace with your actual credentials)
const testUser = {
  email: '<EMAIL>',
  password: ''
};

// Path to audio file (optional)
const audioFilePath = join(__dirname, 'sample.wav');

// Sample base64 audio data (fallback)
const sampleAudioBase64 = 'UklGRigAAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YQQAAAAA';

// Type definitions
interface TranscriptionSuccessResponse {
  success: true;
  data: {
    transcription: string;
    model: string;
    language?: string;
  };
}

interface TranscriptionErrorResponse {
  success: false;
  error: string;
}

type TranscriptionResponse = TranscriptionSuccessResponse | TranscriptionErrorResponse;

// Type guard functions
function isErrorResponse(response: TranscriptionResponse): response is TranscriptionErrorResponse {
  return response.success === false;
}

function isSuccessResponse(response: TranscriptionResponse): response is TranscriptionSuccessResponse {
  return response.success === true;
}

async function getAudioBase64(): Promise<string> {
  if (existsSync(audioFilePath)) {
    console.log('📁 Using audio file:', audioFilePath);
    const audioBuffer = readFileSync(audioFilePath);
    return audioBuffer.toString('base64');
  } else {
    console.log('📁 Audio file not found, using sample data');
    return sampleAudioBase64;
  }
}

async function testSignIn() {
  try {
    console.log('🔄 Signing in to Supabase...');
    
    // Create Supabase client
    const supabase = createClient(supabaseUrl, supabaseAnonKey);
    
    // Sign in
    const { data, error } = await supabase.auth.signInWithPassword({
      email: testUser.email,
      password: testUser.password
    });
    
    if (error) {
      console.error('❌ Sign in error:', error.message);
      return;
    }
    
    if (!data.session) {
      console.error('❌ No session returned');
      return;
    }
    
    const { access_token, user } = data.session;
    console.log('✅ Sign in successful!');
    console.log('📧 User:', user.email);
    console.log('🆔 User ID:', user.id);
    console.log('🔑 Access Token:', access_token.substring(0, 20) + '...');
    
    // Test the transcription endpoint
    await testTranscription(user.id, access_token);
    
  } catch (error) {
    console.error('❌ Test error:', error);
  }
}

async function testTranscription(authId: string, accessToken: string) {
  try {
    console.log('\n🔄 Testing transcription endpoint...');
    
    // Get audio base64 data
    const audioBase64 = await getAudioBase64();
    
    // Prepare the request body
    const requestBody = {
      audioUrl: audioBase64,
      model: 'whisper-1',
      language: 'en'
    };
    
    // Make the API request
    const response = await fetch(`${apiUrl}/api/transcribe`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-secret': apiSecret,
        'x-auth-id': authId,
        'x-access-token': accessToken
      },
      body: JSON.stringify(requestBody)
    });
    
    console.log('📡 Response status:', response.status);
    
    // Parse the response with proper typing
    const responseData = await response.json() as TranscriptionResponse;
    
    // Use type guards to properly handle the union type
    if (response.ok && isSuccessResponse(responseData)) {
      console.log('✅ Transcription successful!');
      console.log('📝 Result:', responseData.data.transcription);
    } else if (isErrorResponse(responseData)) {
      console.error('❌ Transcription failed:', responseData.error);
    } else {
      console.error('❌ Unknown response format');
    }
    
  } catch (error) {
    console.error('❌ Transcription test error:', error);
  }
}

// Run the test
testSignIn();
import { WebSocket } from 'ws';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Configuration
const WS_URL = process.env.WS_URL || 'ws://localhost:3000/api/realtime';
const SUPABASE_URL = process.env.SUPABASE_URL || 'http://localhost:8000';
const SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY || '';

// Test user credentials (replace with your actual credentials)
const testUser = {
  email: '<EMAIL>',
  password: ''
};

async function testRealtimeConnection() {
  try {
    console.log('🔄 Signing in to Supabase...');
    
    // Create Supabase client
    const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
    
    // Sign in
    const { data, error } = await supabase.auth.signInWithPassword({
      email: testUser.email,
      password: testUser.password
    });
    
    if (error) {
      console.error('❌ Sign in error:', error.message);
      return;
    }
    
    if (!data.session) {
      console.error('❌ No session returned');
      return;
    }
    
    const { access_token, user } = data.session;
    console.log('✅ Sign in successful!');
    console.log('📧 User:', user.email);
    console.log('🆔 User ID:', user.id);
    console.log('🔑 Access Token:', access_token.substring(0, 20) + '...');
    
    // Test WebSocket connection
    await testWebSocketConnection(user.id, access_token);
    
  } catch (error) {
    console.error('❌ Test error:', error);
  }
}

async function testWebSocketConnection(userId: string, accessToken: string) {
  try {
    console.log('\n🔄 Testing WebSocket connection...');
    
    // Construct WebSocket URL with authentication parameters
    const wsUrl = new URL(WS_URL);
    wsUrl.searchParams.append('apiSecret', process.env.API_SECRET || '');
    wsUrl.searchParams.append('authId', userId);
    wsUrl.searchParams.append('accessToken', accessToken);
    wsUrl.searchParams.append('provider', 'assemblyai');
    wsUrl.searchParams.append('model', 'best');
    wsUrl.searchParams.append('language', 'en');
    
    console.log('🔗 Connecting to:', wsUrl.toString());
    
    // Create WebSocket connection
    const ws = new WebSocket(wsUrl.toString());
    
    // Set up event handlers
    ws.on('open', () => {
      console.log('✅ WebSocket connected successfully!');
      
      // Send a connected message with sample rate
      ws.send(JSON.stringify({
        type: 'connected',
        sampleRate: 16000
      }));
      
      // Send a ping message
      ws.send(JSON.stringify({
        type: 'ping'
      }));
      
      // After 2 seconds, close the connection
      setTimeout(() => {
        console.log('📤 Sending close message...');
        ws.send(JSON.stringify({
          type: 'close'
        }));
      }, 2000);
    });
    
    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data.toString());
        console.log('📨 Received message:', message);
        
        // Handle different message types
        if (message.type === 'connected') {
          console.log('✅ Session connected:', message.sessionId);
          console.log('⏱️ Max duration:', message.maxDurationMs, 'ms');
        } else if (message.type === 'transcription_ready') {
          console.log('✅ Transcription service ready');
        } else if (message.type === 'pong') {
          console.log('🏓 Pong received');
        } else if (message.type === 'finalized') {
          console.log('✅ Session finalized');
        } else if (message.error) {
          console.error('❌ Error received:', message);
        }
      } catch (error) {
        console.error('❌ Error parsing message:', error);
        console.log('Raw message:', data.toString());
      }
    });
    
    ws.on('close', (code, reason) => {
      console.log('🔌 WebSocket closed:', { code, reason: reason.toString() });
      console.log('✅ WebSocket test completed successfully!');
    });
    
    ws.on('error', (error) => {
      console.error('❌ WebSocket error:', error);
    });
    
    // Set a timeout to close the connection if it doesn't close automatically
    setTimeout(() => {
      if (ws.readyState === WebSocket.OPEN) {
        console.log('⏰ Closing connection due to timeout...');
        ws.close();
      }
    }, 10000);
    
  } catch (error) {
    console.error('❌ WebSocket test error:', error);
  }
}

// Run the test
testRealtimeConnection();
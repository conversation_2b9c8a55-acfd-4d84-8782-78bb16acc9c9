{"name": "voicehype-api", "version": "1.0.0", "description": "VoiceHype API for transcription and optimization", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "nodemon --exec ts-node src/server.ts"}, "dependencies": {"@supabase/supabase-js": "^2.57.2", "assemblyai": "^4.16.0", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^4.21.2", "express-validator": "^7.2.1", "form-data": "^4.0.4", "multer": "^1.4.5-lts.1", "node-fetch": "^3.3.2", "ws": "^8.18.3"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^4.17.23", "@types/multer": "^1.4.13", "@types/node": "^20.19.13", "@types/node-fetch": "^2.6.13", "@types/ws": "^8.18.1", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.9.2"}, "keywords": [], "author": "", "license": "ISC"}
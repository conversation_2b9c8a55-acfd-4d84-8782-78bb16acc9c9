{"inputs": [{"type": "promptString", "id": "supabase-service-role-key", "description": "Supabase Service Role Key for voicehype.ai", "password": true}], "servers": {"voicehype-supabase": {"command": "node", "args": ["/home/<USER>/Documents/cursor_extensions/voicehype/mcp-supabase-server/dist/index.js"], "env": {"SUPABASE_URL": "https://supabase.voicehype.ai", "SUPABASE_SERVICE_ROLE_KEY": "${input:supabase-service-role-key}", "MCP_SERVER_NAME": "voicehype-supabase", "MCP_SERVER_VERSION": "1.0.0"}}}}
# VoiceHype Web Extension
*<PERSON>ism<PERSON><PERSON> rahmanir raheem*

Transform your voice into optimized prompts for LLMs directly in your web browser. Perfect for developers working with AI tools across any website.

## 🎯 Features

### 🎤 **Voice Recording**
- **Universal Text Field Support** - Works on any text input across the web
- **Smart Widget Injection** - Floating microphone button appears near text fields
- **Keyboard Shortcuts** - Quick recording with Ctrl+Shift+V
- **Real-time Transcription** - Live transcription with Assembly AI WebSocket
- **Multiple Services** - Whisper, Assembly AI Best, Assembly AI Nano support

### 🤖 **AI Optimization**
- **LLM Prompt Enhancement** - Optimize transcripts for better AI responses
- **Multiple Models** - Claude, GPT, Llama, and more
- **Context Awareness** - Understands developer workflows and terminology
- **Token Efficiency** - Optimized prompts reduce token usage

### 🔐 **Secure Authentication**
- **OAuth Integration** - Sign in with Google or GitHub
- **API Key Management** - Secure key generation and storage
- **Session Handling** - Automatic token refresh and validation
- **Privacy First** - No transcripts or prompts stored locally

### 🌐 **Cross-Browser Support**
- **Chrome/Chromium** - Full Manifest V3 support
- **Firefox** - Manifest V2 compatibility
- **Microsoft Edge** - Chromium-based features
- **Universal API** - Browser-agnostic architecture

### 💎 **Premium Features**
- **Usage Tracking** - Monitor transcription minutes and tokens
- **Subscription Management** - Basic and Professional tiers
- **Credit System** - Flexible pay-as-you-go options
- **Analytics Dashboard** - Detailed usage statistics and trends

## 🚀 Quick Start

### Installation

#### Chrome/Edge
1. Download the latest release
2. Open `chrome://extensions/` (or `edge://extensions/`)
3. Enable "Developer mode"
4. Click "Load unpacked" and select the extension folder

#### Firefox
1. Download the Firefox build
2. Open `about:debugging`
3. Click "Load Temporary Add-on"
4. Select any file in the extension folder

### First Use
1. **Install Extension** - Follow installation steps above
2. **Sign In** - Click extension icon and sign in with Google/GitHub
3. **Grant Permissions** - Allow microphone access when prompted
4. **Start Recording** - Click widget on any text field or use Ctrl+Shift+V
5. **Get Transcripts** - Speak naturally and get optimized text

## 🛠️ Development

### Prerequisites
- Node.js 16+
- Modern web browser
- Microphone access
- VoiceHype account

### Build from Source
```bash
# Clone repository
git clone https://github.com/voicehype/web-extension.git
cd web-extension

# Build for all browsers
node build.js

# Builds will be in build/ directory
ls build/
# chrome/  firefox/  edge/  voicehype-*.zip
```

### Project Structure
```
web-extension/
├── background/           # Service worker and background scripts
│   ├── adapters/        # Browser compatibility layer
│   ├── services/        # Core business logic
│   └── index.js         # Main background script
├── content-scripts/     # Injected page scripts
│   ├── text-field-detector.js
│   ├── content-script-manager.js
│   └── widget-styles.css
├── frontend/            # Vue.js popup interface
│   ├── components/      # Vue components
│   ├── popup.html       # Extension popup
│   └── auth-callback.html
├── browser-specific/    # Browser-specific manifests
│   ├── chrome/
│   ├── firefox/
│   └── edge/
├── icons/              # Extension icons
├── build.js            # Build script
└── TESTING.md          # Testing guide
```

### Architecture

#### **Three-Layer Design**
1. **Background Script** - Core logic, API communication, state management
2. **Content Scripts** - Text field detection, widget injection, user interaction
3. **Frontend (Vue.js)** - Settings, profile, usage dashboard

#### **Browser Compatibility**
- **Universal API Adapter** - Abstracts Chrome vs Firefox differences
- **Manifest Versions** - V3 for Chrome/Edge, V2 for Firefox
- **Cross-Browser Testing** - Automated builds for all platforms

#### **Security Model**
- **API Key Encryption** - AES-GCM encryption for sensitive data
- **Secure Storage** - Browser extension storage with integrity checks
- **HTTPS Only** - All communications over secure connections
- **No Data Persistence** - Transcripts never stored locally

## 🧪 Testing

See [TESTING.md](./TESTING.md) for comprehensive testing guide.

### Quick Test
1. **Install Extension** - Load unpacked in developer mode
2. **Open Test Page** - Visit any website with text fields
3. **Check Widget** - Verify microphone button appears
4. **Test Recording** - Click widget or use Ctrl+Shift+V
5. **Verify Transcript** - Confirm text appears in field

### Browser Compatibility
- ✅ **Chrome 88+** - Full support
- ✅ **Firefox 78+** - Full support  
- ✅ **Edge 88+** - Full support
- ❌ **Safari** - Not supported (different extension API)

## 📚 API Reference

### Background Script Messages
```javascript
// Start authentication
chrome.runtime.sendMessage({
  type: 'START_AUTHENTICATION',
  provider: 'google' // or 'github'
});

// Start field recording
chrome.runtime.sendMessage({
  type: 'START_FIELD_RECORDING',
  fieldId: 'unique-field-id',
  fieldInfo: { type: 'textarea', placeholder: 'Enter text...' }
});

// Get user data
chrome.runtime.sendMessage({
  type: 'GET_USER_DATA'
});
```

### Content Script Events
```javascript
// Listen for transcript updates
window.addEventListener('voicehype-transcript', (event) => {
  console.log('New transcript:', event.detail.transcript);
});

// Widget state changes
window.addEventListener('voicehype-widget-state', (event) => {
  console.log('Widget state:', event.detail.state); // idle, recording, processing
});
```

## 🤝 Contributing

### Development Setup
1. Fork the repository
2. Create feature branch: `git checkout -b feature/amazing-feature`
3. Make changes and test thoroughly
4. Commit: `git commit -m 'Add amazing feature'`
5. Push: `git push origin feature/amazing-feature`
6. Open Pull Request

### Code Style
- **ES6+ JavaScript** - Modern syntax and features
- **Vue.js 3** - Composition API for frontend
- **Async/Await** - Promise-based asynchronous code
- **JSDoc Comments** - Document all public methods
- **Error Handling** - Comprehensive try/catch blocks

### Testing Requirements
- Test on all supported browsers
- Verify cross-browser compatibility
- Check accessibility compliance
- Validate security measures
- Performance testing under load

## 📄 License

This project is proprietary software owned by VoiceHype. All rights reserved.

## 🆘 Support

- **Documentation** - https://docs.voicehype.ai
- **Website** - https://voicehype.netlify.app
- **Issues** - Report bugs via GitHub Issues
- **Email** - <EMAIL>

## 🙏 Acknowledgments

- **Assembly AI** - Real-time transcription services
- **Supabase** - Authentication and database
- **Vue.js** - Frontend framework
- **Browser Extension APIs** - Cross-platform compatibility

---

*Built with ❤️ for developers who love AI*

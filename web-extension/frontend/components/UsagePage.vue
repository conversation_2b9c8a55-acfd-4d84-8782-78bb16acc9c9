<!--
  Usage Page Component
  B<PERSON><PERSON><PERSON> rah<PERSON>r raheem
  
  Display usage statistics, charts, and history for transcription and optimization
  Following the same patterns as VoiceHype website dashboard
-->

<template>
  <div class="usage-page">
    <!-- Usage Overview -->
    <div class="usage-overview">
      <h3 class="section-title">
        <i class="fas fa-chart-bar"></i>
        Usage Overview
      </h3>
      
      <div class="usage-cards">
        <!-- Transcription Usage -->
        <div class="usage-card">
          <div class="card-header">
            <div class="card-icon transcription">
              <i class="fas fa-microphone"></i>
            </div>
            <div class="card-info">
              <h4>Transcription</h4>
              <p class="card-subtitle">This month</p>
            </div>
          </div>
          
          <div class="card-content">
            <div class="usage-amount">
              <span class="amount-number">{{ formatMinutes(usageData.transcriptionMinutes) }}</span>
              <span class="amount-unit">minutes</span>
            </div>
            
            <div class="usage-limit" v-if="subscription?.transcription_minutes">
              <div class="limit-bar">
                <div 
                  class="limit-progress" 
                  :style="{ width: getUsagePercentage('transcription') + '%' }"
                ></div>
              </div>
              <span class="limit-text">
                {{ formatMinutes(usageData.transcriptionMinutes) }} / {{ formatMinutes(subscription.transcription_minutes) }}
              </span>
            </div>
          </div>
        </div>

        <!-- Optimization Usage -->
        <div class="usage-card">
          <div class="card-header">
            <div class="card-icon optimization">
              <i class="fas fa-magic"></i>
            </div>
            <div class="card-info">
              <h4>Optimization</h4>
              <p class="card-subtitle">This month</p>
            </div>
          </div>
          
          <div class="card-content">
            <div class="usage-amount">
              <span class="amount-number">{{ formatTokens(usageData.optimizationTokens) }}</span>
              <span class="amount-unit">tokens</span>
            </div>
            
            <div class="usage-limit" v-if="subscription?.optimization_tokens">
              <div class="limit-bar">
                <div 
                  class="limit-progress optimization" 
                  :style="{ width: getUsagePercentage('optimization') + '%' }"
                ></div>
              </div>
              <span class="limit-text">
                {{ formatTokens(usageData.optimizationTokens) }} / {{ formatTokens(subscription.optimization_tokens) }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Usage Charts -->
    <div class="usage-charts">
      <h3 class="section-title">
        <i class="fas fa-chart-line"></i>
        Usage Trends
      </h3>
      
      <!-- Time Period Selector -->
      <div class="period-selector">
        <button 
          v-for="period in periods" 
          :key="period.value"
          :class="['period-btn', { active: selectedPeriod === period.value }]"
          @click="selectedPeriod = period.value"
        >
          {{ period.label }}
        </button>
      </div>
      
      <!-- Chart Container -->
      <div class="chart-container">
        <div v-if="!hasUsageData" class="no-data">
          <i class="fas fa-chart-line"></i>
          <h4>No Usage Data</h4>
          <p>Start using VoiceHype to see your usage trends here</p>
        </div>
        
        <div v-else class="chart-content">
          <!-- Simple bar chart representation -->
          <div class="chart-bars">
            <div 
              v-for="(day, index) in getChartData()" 
              :key="index"
              class="chart-bar-group"
            >
              <div class="chart-bars-container">
                <div 
                  class="chart-bar transcription"
                  :style="{ height: getBarHeight(day.transcription, 'transcription') + '%' }"
                  :title="`${day.date}: ${day.transcription} minutes transcription`"
                ></div>
                <div 
                  class="chart-bar optimization"
                  :style="{ height: getBarHeight(day.optimization, 'optimization') + '%' }"
                  :title="`${day.date}: ${formatTokens(day.optimization)} tokens optimization`"
                ></div>
              </div>
              <div class="chart-label">{{ formatChartDate(day.date) }}</div>
            </div>
          </div>
          
          <!-- Chart Legend -->
          <div class="chart-legend">
            <div class="legend-item">
              <div class="legend-color transcription"></div>
              <span>Transcription (minutes)</span>
            </div>
            <div class="legend-item">
              <div class="legend-color optimization"></div>
              <span>Optimization (tokens)</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="recent-activity">
      <h3 class="section-title">
        <i class="fas fa-history"></i>
        Recent Activity
      </h3>
      
      <div v-if="!hasRecentActivity" class="no-activity">
        <i class="fas fa-clock"></i>
        <p>No recent activity to display</p>
      </div>
      
      <div v-else class="activity-list">
        <div 
          v-for="activity in recentActivities" 
          :key="activity.id"
          class="activity-item"
        >
          <div class="activity-icon" :class="activity.type">
            <i :class="getActivityIcon(activity.type)"></i>
          </div>
          
          <div class="activity-content">
            <div class="activity-description">{{ activity.description }}</div>
            <div class="activity-details">
              <span class="activity-amount">{{ formatActivityAmount(activity) }}</span>
              <span class="activity-time">{{ formatRelativeTime(activity.timestamp) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Export Options -->
    <div class="export-section">
      <h3 class="section-title">
        <i class="fas fa-download"></i>
        Export Usage Data
      </h3>
      
      <div class="export-options">
        <button class="btn btn-outline" @click="exportUsageData('csv')">
          <i class="fas fa-file-csv"></i>
          Export as CSV
        </button>
        
        <button class="btn btn-outline" @click="exportUsageData('json')">
          <i class="fas fa-file-code"></i>
          Export as JSON
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'

export default {
  name: 'UsagePage',
  
  props: {
    usageData: {
      type: Object,
      default: () => ({
        transcriptionMinutes: 0,
        optimizationTokens: 0,
        dailyUsage: [],
        monthlyUsage: []
      })
    },
    subscription: {
      type: Object,
      default: null
    }
  },
  
  setup(props) {
    // ==================== REACTIVE STATE ====================
    
    const selectedPeriod = ref('7d')
    
    const periods = [
      { value: '7d', label: '7 Days' },
      { value: '30d', label: '30 Days' },
      { value: '90d', label: '90 Days' }
    ]
    
    // Mock recent activities (in real app, this would come from props)
    const recentActivities = ref([
      {
        id: 1,
        type: 'transcription',
        description: 'Voice recording transcribed',
        amount: 2.5,
        unit: 'minutes',
        timestamp: new Date(Date.now() - 1000 * 60 * 30) // 30 minutes ago
      },
      {
        id: 2,
        type: 'optimization',
        description: 'Transcript optimized with Claude',
        amount: 1250,
        unit: 'tokens',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2) // 2 hours ago
      }
    ])

    // ==================== COMPUTED PROPERTIES ====================
    
    const hasUsageData = computed(() => {
      return props.usageData.dailyUsage?.length > 0 || 
             props.usageData.transcriptionMinutes > 0 || 
             props.usageData.optimizationTokens > 0
    })
    
    const hasRecentActivity = computed(() => {
      return recentActivities.value.length > 0
    })

    // ==================== METHODS ====================
    
    const formatMinutes = (minutes) => {
      if (minutes >= 60) {
        const hours = Math.floor(minutes / 60)
        const remainingMinutes = minutes % 60
        return `${hours}h ${remainingMinutes}m`
      }
      return `${minutes}m`
    }
    
    const formatTokens = (tokens) => {
      if (tokens >= 1000000) {
        return `${(tokens / 1000000).toFixed(1)}M`
      }
      if (tokens >= 1000) {
        return `${(tokens / 1000).toFixed(1)}K`
      }
      return tokens.toString()
    }
    
    const getUsagePercentage = (type) => {
      if (type === 'transcription' && props.subscription?.transcription_minutes) {
        return Math.min((props.usageData.transcriptionMinutes / props.subscription.transcription_minutes) * 100, 100)
      }
      if (type === 'optimization' && props.subscription?.optimization_tokens) {
        return Math.min((props.usageData.optimizationTokens / props.subscription.optimization_tokens) * 100, 100)
      }
      return 0
    }
    
    const getChartData = () => {
      // Generate mock chart data based on selected period
      const days = selectedPeriod.value === '7d' ? 7 : selectedPeriod.value === '30d' ? 30 : 90
      const data = []
      
      for (let i = days - 1; i >= 0; i--) {
        const date = new Date()
        date.setDate(date.getDate() - i)
        
        data.push({
          date: date.toISOString().split('T')[0],
          transcription: Math.floor(Math.random() * 10),
          optimization: Math.floor(Math.random() * 5000)
        })
      }
      
      return data
    }
    
    const getBarHeight = (value, type) => {
      const chartData = getChartData()
      const maxValue = Math.max(...chartData.map(d => d[type]))
      return maxValue > 0 ? (value / maxValue) * 100 : 0
    }
    
    const formatChartDate = (dateString) => {
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
    }
    
    const getActivityIcon = (type) => {
      switch (type) {
        case 'transcription': return 'fas fa-microphone'
        case 'optimization': return 'fas fa-magic'
        default: return 'fas fa-circle'
      }
    }
    
    const formatActivityAmount = (activity) => {
      if (activity.type === 'transcription') {
        return formatMinutes(activity.amount)
      }
      if (activity.type === 'optimization') {
        return formatTokens(activity.amount)
      }
      return activity.amount.toString()
    }
    
    const formatRelativeTime = (timestamp) => {
      const now = new Date()
      const diff = now - timestamp
      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      
      if (days > 0) return `${days}d ago`
      if (hours > 0) return `${hours}h ago`
      if (minutes > 0) return `${minutes}m ago`
      return 'Just now'
    }
    
    const exportUsageData = async (format) => {
      try {
        // Send message to background script to export usage data
        const response = await chrome.runtime.sendMessage({
          type: 'EXPORT_USAGE_DATA',
          format: format
        })
        
        if (response.success) {
          // Create and download file
          const mimeType = format === 'csv' ? 'text/csv' : 'application/json'
          const blob = new Blob([response.data], { type: mimeType })
          const url = URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = `voicehype-usage-${new Date().toISOString().split('T')[0]}.${format}`
          a.click()
          URL.revokeObjectURL(url)
        }
        
      } catch (error) {
        console.error('Error exporting usage data:', error)
      }
    }

    // ==================== RETURN ====================
    
    return {
      selectedPeriod,
      periods,
      recentActivities,
      hasUsageData,
      hasRecentActivity,
      
      formatMinutes,
      formatTokens,
      getUsagePercentage,
      getChartData,
      getBarHeight,
      formatChartDate,
      getActivityIcon,
      formatActivityAmount,
      formatRelativeTime,
      exportUsageData
    }
  }
}
</script>

<style scoped>
/* Component styles will be added in next edit */
</style>

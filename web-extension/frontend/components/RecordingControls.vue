<!--
  Recording Controls Component
  B<PERSON><PERSON><PERSON> rah<PERSON>r raheem
  
  Main recording interface with microphone controls, mode selection,
  and transcript display following VS Code extension patterns
-->

<template>
  <div class="recording-controls">
    <!-- Mode Selection -->
    <div class="mode-selector">
      <h3 class="section-title">Recording Mode</h3>
      <div class="mode-buttons">
        <button 
          :class="['mode-btn', { active: recordingMode === 'regular' }]"
          @click="$emit('toggle-mode')"
          :disabled="isRecording"
        >
          <i class="fas fa-microphone"></i>
          <span>Regular</span>
          <small>Record & transcribe</small>
        </button>
        
        <button 
          :class="['mode-btn', { active: recordingMode === 'realtime' }]"
          @click="$emit('toggle-mode')"
          :disabled="isRecording"
        >
          <i class="fas fa-broadcast-tower"></i>
          <span>Real-time</span>
          <small>Live transcription</small>
        </button>
      </div>
    </div>

    <!-- Recording Controls -->
    <div class="recording-section">
      <h3 class="section-title">Voice Recording</h3>
      
      <!-- Main Record Button -->
      <div class="record-button-container">
        <button 
          :class="['record-btn', { 
            recording: isRecording, 
            processing: isProcessing,
            disabled: isProcessing 
          }]"
          @click="toggleRecording"
          :disabled="isProcessing"
        >
          <div class="record-btn-content">
            <i :class="getRecordIcon()"></i>
            <span class="record-text">{{ getRecordText() }}</span>
          </div>
          
          <!-- Recording animation -->
          <div v-if="isRecording" class="recording-pulse"></div>
        </button>
      </div>

      <!-- Recording Status -->
      <div class="recording-status" v-if="isRecording || isProcessing">
        <div class="status-indicator">
          <i :class="getStatusIcon()"></i>
          <span>{{ getStatusText() }}</span>
        </div>
        
        <!-- Recording timer -->
        <div v-if="isRecording" class="recording-timer">
          {{ formatTime(recordingTime) }}
        </div>
      </div>
    </div>

    <!-- Transcript Display -->
    <div class="transcript-section" v-if="transcript || isRecording">
      <div class="transcript-header">
        <h3 class="section-title">Transcript</h3>
        <button 
          v-if="transcript && !isRecording"
          class="clear-btn"
          @click="$emit('clear-transcript')"
          title="Clear transcript"
        >
          <i class="fas fa-trash"></i>
        </button>
      </div>
      
      <div class="transcript-container">
        <div 
          :class="['transcript-content', { 
            empty: !transcript,
            partial: isRecording && recordingMode === 'realtime'
          }]"
        >
          <template v-if="transcript">
            {{ transcript }}
          </template>
          <template v-else-if="isRecording">
            <span class="placeholder">
              {{ recordingMode === 'realtime' ? 'Listening...' : 'Recording in progress...' }}
            </span>
          </template>
          <template v-else>
            <span class="placeholder">No transcript yet</span>
          </template>
        </div>
        
        <!-- Copy button -->
        <button 
          v-if="transcript"
          class="copy-btn"
          @click="copyTranscript"
          :title="copySuccess ? 'Copied!' : 'Copy transcript'"
        >
          <i :class="copySuccess ? 'fas fa-check' : 'fas fa-copy'"></i>
        </button>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions" v-if="transcript && !isRecording">
      <h3 class="section-title">Quick Actions</h3>
      <div class="action-buttons">
        <button class="action-btn optimize" @click="optimizeTranscript">
          <i class="fas fa-magic"></i>
          <span>Optimize</span>
        </button>
        
        <button class="action-btn insert" @click="insertTranscript">
          <i class="fas fa-paste"></i>
          <span>Insert</span>
        </button>
        
        <button class="action-btn save" @click="saveTranscript">
          <i class="fas fa-save"></i>
          <span>Save</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'

export default {
  name: 'RecordingControls',
  
  props: {
    isRecording: {
      type: Boolean,
      default: false
    },
    isProcessing: {
      type: Boolean,
      default: false
    },
    recordingMode: {
      type: String,
      default: 'regular',
      validator: (value) => ['regular', 'realtime'].includes(value)
    },
    transcript: {
      type: String,
      default: ''
    }
  },
  
  emits: [
    'start-recording',
    'stop-recording', 
    'toggle-mode',
    'clear-transcript'
  ],
  
  setup(props, { emit }) {
    // ==================== REACTIVE STATE ====================
    
    const recordingTime = ref(0)
    const copySuccess = ref(false)
    let recordingTimer = null

    // ==================== COMPUTED PROPERTIES ====================
    
    const getRecordIcon = () => {
      if (props.isProcessing) return 'fas fa-spinner fa-spin'
      if (props.isRecording) return 'fas fa-stop'
      return 'fas fa-microphone'
    }
    
    const getRecordText = () => {
      if (props.isProcessing) return 'Processing...'
      if (props.isRecording) return 'Stop Recording'
      return 'Start Recording'
    }
    
    const getStatusIcon = () => {
      if (props.isProcessing) return 'fas fa-cog fa-spin'
      if (props.isRecording && props.recordingMode === 'realtime') return 'fas fa-broadcast-tower'
      if (props.isRecording) return 'fas fa-microphone'
      return 'fas fa-check'
    }
    
    const getStatusText = () => {
      if (props.isProcessing) return 'Processing audio...'
      if (props.isRecording && props.recordingMode === 'realtime') return 'Live transcription active'
      if (props.isRecording) return 'Recording audio...'
      return 'Complete'
    }

    // ==================== METHODS ====================
    
    const toggleRecording = () => {
      if (props.isRecording) {
        emit('stop-recording')
        stopTimer()
      } else {
        emit('start-recording')
        startTimer()
      }
    }
    
    const startTimer = () => {
      recordingTime.value = 0
      recordingTimer = setInterval(() => {
        recordingTime.value++
      }, 1000)
    }
    
    const stopTimer = () => {
      if (recordingTimer) {
        clearInterval(recordingTimer)
        recordingTimer = null
      }
    }
    
    const formatTime = (seconds) => {
      const mins = Math.floor(seconds / 60)
      const secs = seconds % 60
      return `${mins}:${secs.toString().padStart(2, '0')}`
    }
    
    const copyTranscript = async () => {
      try {
        await navigator.clipboard.writeText(props.transcript)
        copySuccess.value = true
        setTimeout(() => {
          copySuccess.value = false
        }, 2000)
      } catch (error) {
        console.error('Failed to copy transcript:', error)
      }
    }
    
    const optimizeTranscript = async () => {
      try {
        // Send message to background script to optimize transcript
        const response = await chrome.runtime.sendMessage({
          type: 'OPTIMIZE_TRANSCRIPT',
          transcript: props.transcript
        })
        
        if (response.success) {
          // Handle optimized transcript
          console.log('Transcript optimized:', response.optimizedText)
        }
      } catch (error) {
        console.error('Error optimizing transcript:', error)
      }
    }
    
    const insertTranscript = async () => {
      try {
        // Send message to content script to insert transcript
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
        
        await chrome.tabs.sendMessage(tab.id, {
          type: 'INSERT_TEXT',
          text: props.transcript
        })
        
        // Close popup after insertion
        window.close()
      } catch (error) {
        console.error('Error inserting transcript:', error)
      }
    }
    
    const saveTranscript = async () => {
      try {
        // Save transcript to local storage or send to background
        await chrome.runtime.sendMessage({
          type: 'SAVE_TRANSCRIPT',
          transcript: props.transcript,
          timestamp: Date.now()
        })
        
        console.log('Transcript saved')
      } catch (error) {
        console.error('Error saving transcript:', error)
      }
    }

    // ==================== LIFECYCLE ====================
    
    onMounted(() => {
      // Start timer if already recording
      if (props.isRecording) {
        startTimer()
      }
    })
    
    onUnmounted(() => {
      stopTimer()
    })

    // ==================== RETURN ====================
    
    return {
      recordingTime,
      copySuccess,
      getRecordIcon,
      getRecordText,
      getStatusIcon,
      getStatusText,
      toggleRecording,
      formatTime,
      copyTranscript,
      optimizeTranscript,
      insertTranscript,
      saveTranscript
    }
  }
}
</script>

<style scoped>
/* Component styles will be added in next edit */
</style>

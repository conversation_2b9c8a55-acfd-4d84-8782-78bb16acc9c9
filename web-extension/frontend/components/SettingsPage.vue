<!--
  Settings Page Component
  Bism<PERSON><PERSON> rah<PERSON>r raheem
  
  Configuration panel for transcription services, optimization models,
  and extension preferences following VS Code extension patterns
-->

<template>
  <div class="settings-page">
    <!-- Transcription Settings -->
    <div class="settings-section">
      <h3 class="section-title">
        <i class="fas fa-microphone"></i>
        Transcription Settings
      </h3>
      
      <div class="setting-group">
        <div class="setting-item">
          <label class="setting-label">
            <span class="label-text">Transcription Service</span>
            <span class="label-description">Choose your preferred transcription provider</span>
          </label>
          
          <select 
            v-model="localSettings.transcriptionService" 
            class="setting-select"
            @change="updateSettings"
          >
            <option value="whisper">Whisper (OpenAI)</option>
            <option value="assemblyai">Assembly AI</option>
            <option value="lemonfox">Lemon Fox</option>
          </select>
        </div>
        
        <div class="setting-item">
          <label class="setting-label">
            <span class="label-text">Audio Quality</span>
            <span class="label-description">Higher quality uses more bandwidth</span>
          </label>
          
          <select 
            v-model="localSettings.audioQuality" 
            class="setting-select"
            @change="updateSettings"
          >
            <option value="standard">Standard (16kHz)</option>
            <option value="high">High (44.1kHz)</option>
          </select>
        </div>
        
        <div class="setting-item">
          <label class="setting-checkbox">
            <input 
              type="checkbox" 
              v-model="localSettings.showPartialTranscripts"
              @change="updateSettings"
            />
            <span class="checkbox-custom"></span>
            <div class="checkbox-content">
              <span class="label-text">Show Partial Transcripts</span>
              <span class="label-description">Display live transcription during real-time mode</span>
            </div>
          </label>
        </div>
        
        <div class="setting-item">
          <label class="setting-checkbox">
            <input 
              type="checkbox" 
              v-model="localSettings.autoSaveTranscripts"
              @change="updateSettings"
            />
            <span class="checkbox-custom"></span>
            <div class="checkbox-content">
              <span class="label-text">Auto-save Transcripts</span>
              <span class="label-description">Automatically save transcripts to local storage</span>
            </div>
          </label>
        </div>
      </div>
    </div>

    <!-- Optimization Settings -->
    <div class="settings-section">
      <h3 class="section-title">
        <i class="fas fa-magic"></i>
        Optimization Settings
      </h3>
      
      <div class="setting-group">
        <div class="setting-item">
          <label class="setting-label">
            <span class="label-text">Optimization Model</span>
            <span class="label-description">AI model for prompt optimization</span>
          </label>
          
          <select 
            v-model="localSettings.optimizationModel" 
            class="setting-select"
            @change="updateSettings"
          >
            <option value="claude-4-sonnet">Claude 4 Sonnet (Recommended)</option>
            <option value="claude-haiku">Claude Haiku (Fast)</option>
            <option value="llama-3.1-70b">Llama 3.1 70B</option>
            <option value="llama-3.1-8b">Llama 3.1 8B (Budget)</option>
            <option value="deepseek-v3">DeepSeek V3</option>
          </select>
        </div>
        
        <div class="setting-item">
          <label class="setting-checkbox">
            <input 
              type="checkbox" 
              v-model="localSettings.autoOptimize"
              @change="updateSettings"
            />
            <span class="checkbox-custom"></span>
            <div class="checkbox-content">
              <span class="label-text">Auto-optimize Transcripts</span>
              <span class="label-description">Automatically optimize transcripts after recording</span>
            </div>
          </label>
        </div>
        
        <div class="setting-item">
          <label class="setting-checkbox">
            <input 
              type="checkbox" 
              v-model="localSettings.copyOptimizedToClipboard"
              @change="updateSettings"
            />
            <span class="checkbox-custom"></span>
            <div class="checkbox-content">
              <span class="label-text">Auto-copy Optimized Text</span>
              <span class="label-description">Automatically copy optimized prompts to clipboard</span>
            </div>
          </label>
        </div>
      </div>
    </div>

    <!-- Extension Settings -->
    <div class="settings-section">
      <h3 class="section-title">
        <i class="fas fa-puzzle-piece"></i>
        Extension Settings
      </h3>
      
      <div class="setting-group">
        <div class="setting-item">
          <label class="setting-label">
            <span class="label-text">Widget Position</span>
            <span class="label-description">Where to show the VoiceHype widget on web pages</span>
          </label>
          
          <select 
            v-model="localSettings.widgetPosition" 
            class="setting-select"
            @change="updateSettings"
          >
            <option value="bottom-right">Bottom Right</option>
            <option value="bottom-left">Bottom Left</option>
            <option value="top-right">Top Right</option>
            <option value="top-left">Top Left</option>
          </select>
        </div>
        
        <div class="setting-item">
          <label class="setting-checkbox">
            <input 
              type="checkbox" 
              v-model="localSettings.enableKeyboardShortcuts"
              @change="updateSettings"
            />
            <span class="checkbox-custom"></span>
            <div class="checkbox-content">
              <span class="label-text">Enable Keyboard Shortcuts</span>
              <span class="label-description">Use Ctrl+Shift+V to start/stop recording</span>
            </div>
          </label>
        </div>
        
        <div class="setting-item">
          <label class="setting-checkbox">
            <input 
              type="checkbox" 
              v-model="localSettings.showNotifications"
              @change="updateSettings"
            />
            <span class="checkbox-custom"></span>
            <div class="checkbox-content">
              <span class="label-text">Show Notifications</span>
              <span class="label-description">Display browser notifications for recording status</span>
            </div>
          </label>
        </div>
        
        <div class="setting-item">
          <label class="setting-checkbox">
            <input 
              type="checkbox" 
              v-model="localSettings.enableOnAllSites"
              @change="updateSettings"
            />
            <span class="checkbox-custom"></span>
            <div class="checkbox-content">
              <span class="label-text">Enable on All Sites</span>
              <span class="label-description">Show VoiceHype widget on all websites</span>
            </div>
          </label>
        </div>
      </div>
    </div>

    <!-- Privacy Settings -->
    <div class="settings-section">
      <h3 class="section-title">
        <i class="fas fa-shield-alt"></i>
        Privacy & Security
      </h3>
      
      <div class="setting-group">
        <div class="setting-item">
          <label class="setting-checkbox">
            <input 
              type="checkbox" 
              v-model="localSettings.clearDataOnSignOut"
              @change="updateSettings"
            />
            <span class="checkbox-custom"></span>
            <div class="checkbox-content">
              <span class="label-text">Clear Data on Sign Out</span>
              <span class="label-description">Remove all local data when signing out</span>
            </div>
          </label>
        </div>
        
        <div class="setting-item">
          <label class="setting-label">
            <span class="label-text">Data Retention</span>
            <span class="label-description">How long to keep transcript history locally</span>
          </label>
          
          <select 
            v-model="localSettings.dataRetentionDays" 
            class="setting-select"
            @change="updateSettings"
          >
            <option value="7">7 days</option>
            <option value="30">30 days</option>
            <option value="90">90 days</option>
            <option value="365">1 year</option>
            <option value="0">Never delete</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Advanced Settings -->
    <div class="settings-section">
      <h3 class="section-title">
        <i class="fas fa-cogs"></i>
        Advanced
      </h3>
      
      <div class="setting-group">
        <div class="setting-item">
          <label class="setting-checkbox">
            <input 
              type="checkbox" 
              v-model="localSettings.enableDebugMode"
              @change="updateSettings"
            />
            <span class="checkbox-custom"></span>
            <div class="checkbox-content">
              <span class="label-text">Debug Mode</span>
              <span class="label-description">Enable detailed logging for troubleshooting</span>
            </div>
          </label>
        </div>
        
        <div class="setting-item">
          <button class="btn btn-outline" @click="clearAllData">
            <i class="fas fa-trash"></i>
            Clear All Local Data
          </button>
        </div>
        
        <div class="setting-item">
          <button class="btn btn-outline" @click="resetToDefaults">
            <i class="fas fa-undo"></i>
            Reset to Defaults
          </button>
        </div>
      </div>
    </div>

    <!-- Settings Actions -->
    <div class="settings-actions">
      <div class="save-status" v-if="saveStatus">
        <i :class="saveStatus.icon"></i>
        {{ saveStatus.message }}
      </div>
    </div>

    <!-- Confirmation Modal -->
    <div v-if="showConfirmModal" class="modal-overlay" @click="cancelConfirm">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h4>{{ confirmModal.title }}</h4>
          <button class="modal-close" @click="cancelConfirm">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <div class="modal-body">
          <p>{{ confirmModal.message }}</p>
        </div>
        
        <div class="modal-footer">
          <button class="btn btn-outline" @click="cancelConfirm">Cancel</button>
          <button class="btn btn-danger" @click="confirmAction">
            {{ confirmModal.confirmText }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, watch } from 'vue'

export default {
  name: 'SettingsPage',
  
  props: {
    settings: {
      type: Object,
      default: () => ({})
    }
  },
  
  emits: ['update-settings'],
  
  setup(props, { emit }) {
    // ==================== REACTIVE STATE ====================
    
    const localSettings = reactive({
      // Transcription Settings
      transcriptionService: 'whisper',
      audioQuality: 'standard',
      showPartialTranscripts: true,
      autoSaveTranscripts: true,
      
      // Optimization Settings
      optimizationModel: 'claude-4-sonnet',
      autoOptimize: true,
      copyOptimizedToClipboard: true,
      
      // Extension Settings
      widgetPosition: 'bottom-right',
      enableKeyboardShortcuts: true,
      showNotifications: true,
      enableOnAllSites: true,
      
      // Privacy Settings
      clearDataOnSignOut: false,
      dataRetentionDays: 30,
      
      // Advanced Settings
      enableDebugMode: false,
      
      // Apply props
      ...props.settings
    })
    
    const saveStatus = ref(null)
    const showConfirmModal = ref(false)
    const confirmModal = ref({
      title: '',
      message: '',
      confirmText: '',
      action: null
    })

    // ==================== METHODS ====================
    
    const updateSettings = () => {
      emit('update-settings', { ...localSettings })
      showSaveStatus('Settings saved successfully', 'success')
    }
    
    const showSaveStatus = (message, type = 'success') => {
      saveStatus.value = {
        message,
        icon: type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-circle'
      }
      
      setTimeout(() => {
        saveStatus.value = null
      }, 3000)
    }
    
    const clearAllData = () => {
      showConfirmModal.value = true
      confirmModal.value = {
        title: 'Clear All Data',
        message: 'This will permanently delete all local transcripts, settings, and cached data. This action cannot be undone.',
        confirmText: 'Clear All Data',
        action: async () => {
          try {
            await chrome.runtime.sendMessage({ type: 'CLEAR_ALL_DATA' })
            showSaveStatus('All local data cleared', 'success')
          } catch (error) {
            console.error('Error clearing data:', error)
            showSaveStatus('Failed to clear data', 'error')
          }
        }
      }
    }
    
    const resetToDefaults = () => {
      showConfirmModal.value = true
      confirmModal.value = {
        title: 'Reset Settings',
        message: 'This will reset all settings to their default values. Your transcripts and account data will not be affected.',
        confirmText: 'Reset Settings',
        action: () => {
          // Reset to default values
          Object.assign(localSettings, {
            transcriptionService: 'whisper',
            audioQuality: 'standard',
            showPartialTranscripts: true,
            autoSaveTranscripts: true,
            optimizationModel: 'claude-4-sonnet',
            autoOptimize: true,
            copyOptimizedToClipboard: true,
            widgetPosition: 'bottom-right',
            enableKeyboardShortcuts: true,
            showNotifications: true,
            enableOnAllSites: true,
            clearDataOnSignOut: false,
            dataRetentionDays: 30,
            enableDebugMode: false
          })
          
          updateSettings()
          showSaveStatus('Settings reset to defaults', 'success')
        }
      }
    }
    
    const confirmAction = () => {
      if (confirmModal.value.action) {
        confirmModal.value.action()
      }
      showConfirmModal.value = false
    }
    
    const cancelConfirm = () => {
      showConfirmModal.value = false
    }

    // ==================== WATCHERS ====================
    
    // Watch for prop changes and update local settings
    watch(() => props.settings, (newSettings) => {
      Object.assign(localSettings, newSettings)
    }, { deep: true })

    // ==================== RETURN ====================
    
    return {
      localSettings,
      saveStatus,
      showConfirmModal,
      confirmModal,
      
      updateSettings,
      clearAllData,
      resetToDefaults,
      confirmAction,
      cancelConfirm
    }
  }
}
</script>

<style scoped>
/* Component styles will be added in next edit */
</style>

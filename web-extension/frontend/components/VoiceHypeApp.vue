<!--
  VoiceHype Web Extension Main App Component
  <PERSON><PERSON><PERSON><PERSON> rahmanir raheem
  
  Main application component that handles routing between different pages
  and manages global state for the web extension UI
-->

<template>
  <div class="voicehype-app" :class="{ 'dark-mode': isDarkMode }">
    <!-- Header with navigation -->
    <header class="app-header">
      <div class="header-content">
        <div class="logo-section">
          <img src="/icons/icon-48.png" alt="VoiceHype" class="logo" />
          <h1 class="app-title">VoiceHype</h1>
        </div>
        
        <!-- Navigation tabs -->
        <nav class="nav-tabs">
          <button 
            v-for="tab in tabs" 
            :key="tab.id"
            :class="['nav-tab', { active: currentTab === tab.id }]"
            @click="switchTab(tab.id)"
          >
            <i :class="tab.icon"></i>
            {{ tab.label }}
          </button>
        </nav>

        <!-- Theme toggle and user menu -->
        <div class="header-actions">
          <button class="theme-toggle" @click="toggleTheme" :title="isDarkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'">
            <i :class="isDarkMode ? 'fas fa-sun' : 'fas fa-moon'"></i>
          </button>
          
          <div class="user-menu" v-if="user">
            <img :src="user.avatar_url || '/icons/default-avatar.png'" :alt="user.email" class="user-avatar" />
            <span class="user-name">{{ user.email?.split('@')[0] || 'User' }}</span>
          </div>
        </div>
      </div>
    </header>

    <!-- Main content area -->
    <main class="app-main">
      <!-- Authentication Required -->
      <div v-if="!user && !isLoading" class="auth-required">
        <div class="auth-card">
          <div class="auth-icon">
            <i class="fas fa-lock"></i>
          </div>
          <h2>Authentication Required</h2>
          <p>Please sign in to your VoiceHype account to use the extension.</p>
          <div class="auth-buttons">
            <button class="auth-btn google" @click="signInWithGoogle">
              <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" />
              Continue with Google
            </button>
            <button class="auth-btn github" @click="signInWithGitHub">
              <i class="fab fa-github"></i>
              Continue with GitHub
            </button>
          </div>
        </div>
      </div>

      <!-- Recording Controls (when authenticated) -->
      <RecordingControls
        v-if="currentTab === 'record' && user"
        :is-recording="isRecording"
        :is-processing="isProcessing"
        :recording-mode="recordingMode"
        :transcript="currentTranscript"
        @start-recording="startRecording"
        @stop-recording="stopRecording"
        @toggle-mode="toggleRecordingMode"
        @clear-transcript="clearTranscript"
      />

      <!-- Profile Page -->
      <ProfilePage
        v-if="currentTab === 'profile' && user"
        :user="user"
        :subscription="subscription"
        :credits="credits"
        @sign-out="handleSignOut"
      />

      <!-- Usage Page -->
      <UsagePage
        v-if="currentTab === 'usage' && user"
        :usage-data="usageData"
        :subscription="subscription"
      />

      <!-- Settings Page -->
      <SettingsPage
        v-if="currentTab === 'settings' && user"
        :settings="settings"
        @update-settings="updateSettings"
      />
    </main>

    <!-- Status bar -->
    <footer class="app-footer">
      <div class="status-info">
        <span class="connection-status" :class="connectionStatus">
          <i :class="getConnectionIcon()"></i>
          {{ getConnectionText() }}
        </span>
        
        <span class="credits-display" v-if="credits !== null">
          <i class="fas fa-coins"></i>
          {{ formatCredits(credits) }} credits
        </span>
      </div>
    </footer>

    <!-- Loading overlay -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
        <p>{{ loadingMessage }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import RecordingControls from './RecordingControls.vue'
import ProfilePage from './ProfilePage.vue'
import UsagePage from './UsagePage.vue'
import SettingsPage from './SettingsPage.vue'

export default {
  name: 'VoiceHypeApp',
  components: {
    RecordingControls,
    ProfilePage,
    UsagePage,
    SettingsPage
  },
  
  setup() {
    // ==================== REACTIVE STATE ====================
    
    // UI State
    const currentTab = ref('record')
    const isDarkMode = ref(false)
    const isLoading = ref(true)
    const loadingMessage = ref('Loading VoiceHype...')
    
    // User & Auth State
    const user = ref(null)
    const subscription = ref(null)
    const credits = ref(null)
    
    // Recording State
    const isRecording = ref(false)
    const isProcessing = ref(false)
    const recordingMode = ref('regular') // 'regular' or 'realtime'
    const currentTranscript = ref('')
    
    // Connection State
    const connectionStatus = ref('disconnected') // 'connected', 'connecting', 'disconnected', 'error'
    
    // Settings
    const settings = reactive({
      transcriptionService: 'whisper',
      optimizationModel: 'claude-4-sonnet',
      autoOptimize: true,
      showPartialTranscripts: true,
      audioQuality: 'high'
    })
    
    // Usage Data
    const usageData = ref({
      transcriptionMinutes: 0,
      optimizationTokens: 0,
      dailyUsage: [],
      monthlyUsage: []
    })

    // ==================== COMPUTED PROPERTIES ====================
    
    const tabs = computed(() => [
      { id: 'record', label: 'Record', icon: 'fas fa-microphone' },
      { id: 'profile', label: 'Profile', icon: 'fas fa-user' },
      { id: 'usage', label: 'Usage', icon: 'fas fa-chart-bar' },
      { id: 'settings', label: 'Settings', icon: 'fas fa-cog' }
    ])

    // ==================== METHODS ====================
    
    const switchTab = (tabId) => {
      currentTab.value = tabId
    }
    
    const toggleTheme = () => {
      isDarkMode.value = !isDarkMode.value
      // Save theme preference
      chrome.storage.local.set({ darkMode: isDarkMode.value })
    }
    
    const getConnectionIcon = () => {
      switch (connectionStatus.value) {
        case 'connected': return 'fas fa-wifi text-success'
        case 'connecting': return 'fas fa-spinner fa-spin text-warning'
        case 'error': return 'fas fa-exclamation-triangle text-danger'
        default: return 'fas fa-wifi-slash text-muted'
      }
    }
    
    const getConnectionText = () => {
      switch (connectionStatus.value) {
        case 'connected': return 'Connected'
        case 'connecting': return 'Connecting...'
        case 'error': return 'Connection Error'
        default: return 'Disconnected'
      }
    }
    
    const formatCredits = (credits) => {
      if (credits >= 1000) {
        return `${(credits / 1000).toFixed(1)}K`
      }
      return credits.toString()
    }

    // ==================== RECORDING METHODS ====================
    
    const startRecording = async () => {
      try {
        isRecording.value = true
        currentTranscript.value = ''
        
        // Send message to background script to start recording
        const response = await chrome.runtime.sendMessage({
          type: 'START_RECORDING',
          mode: recordingMode.value
        })
        
        if (!response.success) {
          throw new Error(response.error || 'Failed to start recording')
        }
        
      } catch (error) {
        console.error('Error starting recording:', error)
        isRecording.value = false
        // Show error notification
      }
    }
    
    const stopRecording = async () => {
      try {
        isProcessing.value = true
        
        // Send message to background script to stop recording
        const response = await chrome.runtime.sendMessage({
          type: 'STOP_RECORDING'
        })
        
        if (response.success && response.transcript) {
          currentTranscript.value = response.transcript
        }
        
      } catch (error) {
        console.error('Error stopping recording:', error)
      } finally {
        isRecording.value = false
        isProcessing.value = false
      }
    }
    
    const toggleRecordingMode = () => {
      recordingMode.value = recordingMode.value === 'regular' ? 'realtime' : 'regular'
    }
    
    const clearTranscript = () => {
      currentTranscript.value = ''
    }

    // ==================== AUTH METHODS ====================

    const signInWithGoogle = async () => {
      try {
        isLoading.value = true
        loadingMessage.value = 'Signing in with Google...'

        // Send message to background script to start auth
        const response = await chrome.runtime.sendMessage({
          type: 'START_AUTHENTICATION',
          provider: 'google'
        })

        if (response.success) {
          // Auth will complete via message handler
          console.log('VoiceHype: Google authentication started')
        } else {
          throw new Error(response.error || 'Failed to start authentication')
        }

      } catch (error) {
        console.error('Error signing in with Google:', error)
        isLoading.value = false
      }
    }

    const signInWithGitHub = async () => {
      try {
        isLoading.value = true
        loadingMessage.value = 'Signing in with GitHub...'

        // Send message to background script to start auth
        const response = await chrome.runtime.sendMessage({
          type: 'START_AUTHENTICATION',
          provider: 'github'
        })

        if (response.success) {
          // Auth will complete via message handler
          console.log('VoiceHype: GitHub authentication started')
        } else {
          throw new Error(response.error || 'Failed to start authentication')
        }

      } catch (error) {
        console.error('Error signing in with GitHub:', error)
        isLoading.value = false
      }
    }

    const handleSignOut = async () => {
      try {
        isLoading.value = true
        loadingMessage.value = 'Signing out...'

        // Send message to background script to sign out
        await chrome.runtime.sendMessage({ type: 'SIGN_OUT' })

        // Reset state
        user.value = null
        subscription.value = null
        credits.value = null

        // Redirect to auth
        window.location.reload()

      } catch (error) {
        console.error('Error signing out:', error)
      } finally {
        isLoading.value = false
      }
    }

    // ==================== SETTINGS METHODS ====================
    
    const updateSettings = async (newSettings) => {
      try {
        Object.assign(settings, newSettings)
        
        // Save settings to background script
        await chrome.runtime.sendMessage({
          type: 'UPDATE_SETTINGS',
          settings: settings
        })
        
      } catch (error) {
        console.error('Error updating settings:', error)
      }
    }

    // ==================== LIFECYCLE ====================
    
    onMounted(async () => {
      try {
        // Load theme preference
        const { darkMode } = await chrome.storage.local.get('darkMode')
        isDarkMode.value = darkMode || false
        
        // Load user data from background script
        const response = await chrome.runtime.sendMessage({ type: 'GET_USER_DATA' })
        
        if (response.success) {
          user.value = response.user
          subscription.value = response.subscription
          credits.value = response.credits
          usageData.value = response.usage || usageData.value
          Object.assign(settings, response.settings || settings)
        }
        
        // Set up message listeners for real-time updates
        chrome.runtime.onMessage.addListener((message) => {
          switch (message.type) {
            case 'TRANSCRIPT_UPDATE':
              currentTranscript.value = message.transcript
              break
            case 'RECORDING_STATUS':
              isRecording.value = message.isRecording
              isProcessing.value = message.isProcessing
              break
            case 'CONNECTION_STATUS':
              connectionStatus.value = message.status
              break
            case 'AUTH_COMPLETED':
              // Authentication completed successfully
              user.value = message.user
              isLoading.value = false
              loadingMessage.value = 'Loading VoiceHype...'
              console.log('VoiceHype: Authentication completed for:', message.user.email)
              break
            case 'AUTH_FAILED':
              // Authentication failed
              isLoading.value = false
              console.error('VoiceHype: Authentication failed:', message.error)
              break
              break
            case 'CREDITS_UPDATE':
              credits.value = message.credits
              break
          }
        })
        
      } catch (error) {
        console.error('Error initializing app:', error)
      } finally {
        isLoading.value = false
      }
    })

    // ==================== RETURN ====================
    
    return {
      // State
      currentTab,
      isDarkMode,
      isLoading,
      loadingMessage,
      user,
      subscription,
      credits,
      isRecording,
      isProcessing,
      recordingMode,
      currentTranscript,
      connectionStatus,
      settings,
      usageData,
      
      // Computed
      tabs,
      
      // Methods
      switchTab,
      toggleTheme,
      getConnectionIcon,
      getConnectionText,
      formatCredits,
      startRecording,
      stopRecording,
      toggleRecordingMode,
      clearTranscript,
      signInWithGoogle,
      signInWithGitHub,
      handleSignOut,
      updateSettings
    }
  }
}
</script>

<style scoped>
/* ==================== MAIN APP STYLES ==================== */

.voicehype-app {
  width: 400px;
  height: 600px;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* ==================== AUTHENTICATION STYLES ==================== */

.auth-required {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.auth-card {
  background: var(--bg-secondary);
  border-radius: 12px;
  padding: 32px 24px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color);
  max-width: 320px;
  width: 100%;
}

.auth-icon {
  width: 48px;
  height: 48px;
  background: var(--accent-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
  color: white;
  font-size: 20px;
}

.auth-card h2 {
  margin: 0 0 8px;
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
}

.auth-card p {
  margin: 0 0 24px;
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.4;
}

.auth-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.auth-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.auth-btn:hover {
  background: var(--bg-hover);
  border-color: var(--accent-color);
}

.auth-btn.google {
  border-color: #4285f4;
}

.auth-btn.google:hover {
  background: #4285f4;
  color: white;
}

.auth-btn.github {
  border-color: #333;
}

.auth-btn.github:hover {
  background: #333;
  color: white;
}

.auth-btn img {
  width: 16px;
  height: 16px;
}

.auth-btn i {
  font-size: 16px;
}

/* ==================== CSS VARIABLES ==================== */

.voicehype-app {
  /* Light mode colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #e9ecef;
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --text-muted: #adb5bd;
  --border-color: #dee2e6;
  --accent-primary: #007bff;
  --accent-secondary: #6c757d;
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.voicehype-app.dark-mode {
  /* Dark mode colors */
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --bg-tertiary: #404040;
  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
  --text-muted: #808080;
  --border-color: #404040;
  --accent-primary: #0d6efd;
  --accent-secondary: #6c757d;
  --success-color: #198754;
  --warning-color: #fd7e14;
  --danger-color: #dc3545;
  --shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* ==================== HEADER STYLES ==================== */

.app-header {
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: 12px 16px;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo {
  width: 24px;
  height: 24px;
}

.app-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
}

.nav-tabs {
  display: flex;
  gap: 4px;
}

.nav-tab {
  background: transparent;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  color: var(--text-secondary);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.nav-tab:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.nav-tab.active {
  background: var(--accent-primary);
  color: white;
}

.nav-tab i {
  font-size: 10px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.theme-toggle {
  background: transparent;
  border: none;
  padding: 6px;
  border-radius: 4px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.theme-toggle:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 6px;
}

.user-avatar {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid var(--border-color);
}

.user-name {
  font-size: 12px;
  color: var(--text-secondary);
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* ==================== MAIN CONTENT STYLES ==================== */

.app-main {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

/* ==================== FOOTER STYLES ==================== */

.app-footer {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  padding: 8px 16px;
  flex-shrink: 0;
}

.status-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 11px;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 4px;
}

.connection-status i {
  font-size: 10px;
}

.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }
.text-muted { color: var(--text-muted); }

.credits-display {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--success-color);
  font-weight: 500;
}

.credits-display i {
  font-size: 10px;
}

/* ==================== LOADING OVERLAY ==================== */

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  background: var(--bg-primary);
  padding: 24px;
  border-radius: 8px;
  text-align: center;
  box-shadow: var(--shadow);
}

.loading-spinner i {
  font-size: 24px;
  color: var(--accent-primary);
  margin-bottom: 12px;
}

.loading-spinner p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 14px;
}

/* ==================== RESPONSIVE ADJUSTMENTS ==================== */

@media (max-width: 380px) {
  .voicehype-app {
    width: 100%;
  }

  .nav-tab {
    padding: 4px 8px;
    font-size: 11px;
  }

  .nav-tab i {
    display: none;
  }
}
</style>

<!--
  Profile Page Component
  B<PERSON><PERSON><PERSON> rahmanir raheem
  
  User profile display with subscription info, credits, and account management
  Following the same patterns as VoiceHype website
-->

<template>
  <div class="profile-page">
    <!-- User Info Section -->
    <div class="profile-section">
      <div class="profile-header">
        <div class="user-avatar-large">
          <img 
            :src="user?.avatar_url || '/icons/default-avatar.png'"
            :alt="user?.email || 'User'"
            class="avatar-image"
          />
        </div>
        
        <div class="user-details">
          <h2 class="user-name">{{ getUserDisplayName() }}</h2>
          <p class="user-email">{{ user?.email || 'No email' }}</p>
          <div class="user-provider" v-if="user?.app_metadata?.provider">
            <i :class="getProviderIcon()"></i>
            <span>{{ getProviderName() }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Subscription Section -->
    <div class="subscription-section">
      <h3 class="section-title">
        <i class="fas fa-crown"></i>
        Subscription
      </h3>
      
      <div v-if="subscription" class="subscription-card">
        <div class="subscription-header">
          <div class="plan-info">
            <h4 class="plan-name">{{ subscription.plan_name || 'Free Plan' }}</h4>
            <span class="plan-status" :class="subscription.status">
              {{ formatStatus(subscription.status) }}
            </span>
          </div>
          
          <div class="plan-price" v-if="subscription.amount">
            ${{ subscription.amount }}<span class="period">/{{ subscription.interval || 'month' }}</span>
          </div>
        </div>
        
        <div class="subscription-details">
          <div class="detail-item" v-if="subscription.current_period_end">
            <i class="fas fa-calendar"></i>
            <span>Next billing: {{ formatDate(subscription.current_period_end) }}</span>
          </div>
          
          <div class="detail-item" v-if="subscription.transcription_minutes">
            <i class="fas fa-microphone"></i>
            <span>{{ subscription.transcription_minutes }} minutes/month</span>
          </div>
          
          <div class="detail-item" v-if="subscription.optimization_tokens">
            <i class="fas fa-magic"></i>
            <span>{{ formatTokens(subscription.optimization_tokens) }} tokens/month</span>
          </div>
        </div>
        
        <div class="subscription-actions">
          <button class="btn btn-outline" @click="manageSubscription">
            <i class="fas fa-cog"></i>
            Manage Subscription
          </button>
        </div>
      </div>
      
      <div v-else class="no-subscription">
        <div class="no-subscription-content">
          <i class="fas fa-star"></i>
          <h4>No Active Subscription</h4>
          <p>Upgrade to unlock more features and higher limits</p>
          <button class="btn btn-primary" @click="upgradeSubscription">
            <i class="fas fa-arrow-up"></i>
            Upgrade Now
          </button>
        </div>
      </div>
    </div>

    <!-- Credits Section -->
    <div class="credits-section">
      <h3 class="section-title">
        <i class="fas fa-coins"></i>
        Credits
      </h3>
      
      <div class="credits-card">
        <div class="credits-display">
          <div class="credits-amount">
            <span class="credits-number">{{ formatCredits(credits) }}</span>
            <span class="credits-label">Available Credits</span>
          </div>
          
          <div class="credits-actions">
            <button class="btn btn-outline btn-sm" @click="buyCredits">
              <i class="fas fa-plus"></i>
              Buy Credits
            </button>
          </div>
        </div>
        
        <div class="credits-info">
          <div class="info-item">
            <i class="fas fa-info-circle"></i>
            <span>Credits are used for pay-as-you-go transcription and optimization</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Account Actions -->
    <div class="account-section">
      <h3 class="section-title">
        <i class="fas fa-user-cog"></i>
        Account
      </h3>
      
      <div class="account-actions">
        <button class="btn btn-outline" @click="refreshData">
          <i class="fas fa-sync-alt" :class="{ 'fa-spin': isRefreshing }"></i>
          Refresh Data
        </button>
        
        <button class="btn btn-outline" @click="exportData">
          <i class="fas fa-download"></i>
          Export Data
        </button>
        
        <button class="btn btn-danger" @click="confirmSignOut">
          <i class="fas fa-sign-out-alt"></i>
          Sign Out
        </button>
      </div>
    </div>

    <!-- Sign Out Confirmation Modal -->
    <div v-if="showSignOutModal" class="modal-overlay" @click="cancelSignOut">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h4>Confirm Sign Out</h4>
          <button class="modal-close" @click="cancelSignOut">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <div class="modal-body">
          <p>Are you sure you want to sign out? You'll need to sign in again to use VoiceHype.</p>
        </div>
        
        <div class="modal-footer">
          <button class="btn btn-outline" @click="cancelSignOut">Cancel</button>
          <button class="btn btn-danger" @click="handleSignOut">
            <i class="fas fa-sign-out-alt"></i>
            Sign Out
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'

export default {
  name: 'ProfilePage',
  
  props: {
    user: {
      type: Object,
      default: null
    },
    subscription: {
      type: Object,
      default: null
    },
    credits: {
      type: Number,
      default: 0
    }
  },
  
  emits: ['sign-out'],
  
  setup(props, { emit }) {
    // ==================== REACTIVE STATE ====================
    
    const isRefreshing = ref(false)
    const showSignOutModal = ref(false)

    // ==================== COMPUTED PROPERTIES ====================
    
    const getUserDisplayName = () => {
      if (props.user?.user_metadata?.full_name) {
        return props.user.user_metadata.full_name
      }
      if (props.user?.email) {
        return props.user.email.split('@')[0]
      }
      return 'User'
    }
    
    const getProviderIcon = () => {
      const provider = props.user?.app_metadata?.provider
      switch (provider) {
        case 'google': return 'fab fa-google'
        case 'github': return 'fab fa-github'
        default: return 'fas fa-user'
      }
    }
    
    const getProviderName = () => {
      const provider = props.user?.app_metadata?.provider
      switch (provider) {
        case 'google': return 'Google'
        case 'github': return 'GitHub'
        default: return 'Email'
      }
    }

    // ==================== METHODS ====================
    
    const formatStatus = (status) => {
      switch (status) {
        case 'active': return 'Active'
        case 'canceled': return 'Canceled'
        case 'past_due': return 'Past Due'
        case 'unpaid': return 'Unpaid'
        default: return 'Unknown'
      }
    }
    
    const formatDate = (dateString) => {
      try {
        return new Date(dateString).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        })
      } catch {
        return 'Unknown'
      }
    }
    
    const formatTokens = (tokens) => {
      if (tokens >= 1000000) {
        return `${(tokens / 1000000).toFixed(1)}M`
      }
      if (tokens >= 1000) {
        return `${(tokens / 1000).toFixed(1)}K`
      }
      return tokens.toString()
    }
    
    const formatCredits = (credits) => {
      if (credits >= 1000) {
        return `${(credits / 1000).toFixed(1)}K`
      }
      return credits?.toString() || '0'
    }
    
    const manageSubscription = () => {
      // Open subscription management in new tab
      chrome.tabs.create({ 
        url: 'https://voicehype.netlify.app/app/subscription' 
      })
    }
    
    const upgradeSubscription = () => {
      // Open pricing page in new tab
      chrome.tabs.create({ 
        url: 'https://voicehype.netlify.app/pricing' 
      })
    }
    
    const buyCredits = () => {
      // Open credits purchase page in new tab
      chrome.tabs.create({ 
        url: 'https://voicehype.netlify.app/app/credits' 
      })
    }
    
    const refreshData = async () => {
      try {
        isRefreshing.value = true
        
        // Send message to background script to refresh user data
        await chrome.runtime.sendMessage({ type: 'REFRESH_USER_DATA' })
        
      } catch (error) {
        console.error('Error refreshing data:', error)
      } finally {
        isRefreshing.value = false
      }
    }
    
    const exportData = async () => {
      try {
        // Send message to background script to export user data
        const response = await chrome.runtime.sendMessage({ type: 'EXPORT_USER_DATA' })
        
        if (response.success) {
          // Create and download file
          const blob = new Blob([JSON.stringify(response.data, null, 2)], {
            type: 'application/json'
          })
          const url = URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = `voicehype-data-${new Date().toISOString().split('T')[0]}.json`
          a.click()
          URL.revokeObjectURL(url)
        }
        
      } catch (error) {
        console.error('Error exporting data:', error)
      }
    }
    
    const confirmSignOut = () => {
      showSignOutModal.value = true
    }
    
    const cancelSignOut = () => {
      showSignOutModal.value = false
    }
    
    const handleSignOut = () => {
      showSignOutModal.value = false
      emit('sign-out')
    }

    // ==================== RETURN ====================
    
    return {
      isRefreshing,
      showSignOutModal,
      
      getUserDisplayName,
      getProviderIcon,
      getProviderName,
      formatStatus,
      formatDate,
      formatTokens,
      formatCredits,
      manageSubscription,
      upgradeSubscription,
      buyCredits,
      refreshData,
      exportData,
      confirmSignOut,
      cancelSignOut,
      handleSignOut
    }
  }
}
</script>

<style scoped>
/* Component styles will be added in next edit */
</style>

/**
 * VoiceHype Web Extension Frontend Entry Point
 * Bismillahir rahmanir raheem
 * 
 * Main Vue.js application initialization for the web extension popup/sidebar
 * Following the same patterns as VoiceHype website frontend
 */

import { createApp } from 'vue'
import VoiceHypeApp from './components/VoiceHypeApp.vue'

// ==================== GLOBAL STYLES ====================

// Import Font Awesome for icons
const fontAwesome = document.createElement('link')
fontAwesome.rel = 'stylesheet'
fontAwesome.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'
document.head.appendChild(fontAwesome)

// Import global CSS variables and base styles
const globalStyles = document.createElement('style')
globalStyles.textContent = `
  /* ==================== GLOBAL CSS RESET ==================== */
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  /* ==================== GLOBAL CSS VARIABLES ==================== */
  
  :root {
    /* Light mode colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --text-muted: #adb5bd;
    --border-color: #dee2e6;
    --accent-primary: #007bff;
    --accent-secondary: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --shadow: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-lg: 0 4px 8px rgba(0,0,0,0.15);
    --border-radius: 6px;
    --border-radius-lg: 8px;
    --transition: all 0.2s ease;
  }
  
  [data-theme="dark"] {
    /* Dark mode colors */
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #404040;
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --text-muted: #808080;
    --border-color: #404040;
    --accent-primary: #0d6efd;
    --accent-secondary: #6c757d;
    --success-color: #198754;
    --warning-color: #fd7e14;
    --danger-color: #dc3545;
    --shadow: 0 2px 4px rgba(0,0,0,0.3);
    --shadow-lg: 0 4px 8px rgba(0,0,0,0.4);
  }
  
  /* ==================== GLOBAL UTILITY CLASSES ==================== */
  
  .btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    background: var(--accent-primary);
    color: white;
  }
  
  .btn:hover {
    opacity: 0.9;
    transform: translateY(-1px);
  }
  
  .btn:active {
    transform: translateY(0);
  }
  
  .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
  
  .btn-outline {
    background: transparent;
    color: var(--text-primary);
    border: 1px solid var(--border-color);
  }
  
  .btn-outline:hover {
    background: var(--bg-secondary);
  }
  
  .btn-danger {
    background: var(--danger-color);
  }
  
  .btn-success {
    background: var(--success-color);
  }
  
  .btn-warning {
    background: var(--warning-color);
    color: var(--text-primary);
  }
  
  .btn-sm {
    padding: 4px 8px;
    font-size: 12px;
  }
  
  .btn-lg {
    padding: 12px 24px;
    font-size: 16px;
  }
  
  /* ==================== FORM ELEMENTS ==================== */
  
  .setting-select,
  .form-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 14px;
    transition: var(--transition);
  }
  
  .setting-select:focus,
  .form-select:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
  }
  
  .form-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 14px;
    transition: var(--transition);
  }
  
  .form-input:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
  }
  
  /* ==================== SECTION STYLES ==================== */
  
  .section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 16px;
  }
  
  .section-title i {
    color: var(--accent-primary);
  }
  
  /* ==================== MODAL STYLES ==================== */
  
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  
  .modal-content {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 400px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
  }
  
  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
  }
  
  .modal-header h4 {
    margin: 0;
    color: var(--text-primary);
  }
  
  .modal-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: var(--transition);
  }
  
  .modal-close:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
  }
  
  .modal-body {
    padding: 20px;
  }
  
  .modal-body p {
    margin: 0;
    color: var(--text-secondary);
    line-height: 1.5;
  }
  
  .modal-footer {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    padding: 16px 20px;
    border-top: 1px solid var(--border-color);
  }
  
  /* ==================== SCROLLBAR STYLES ==================== */
  
  ::-webkit-scrollbar {
    width: 6px;
  }
  
  ::-webkit-scrollbar-track {
    background: var(--bg-secondary);
  }
  
  ::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
  }
  
  /* ==================== ANIMATION CLASSES ==================== */
  
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s ease;
  }
  
  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }
  
  .slide-enter-active,
  .slide-leave-active {
    transition: transform 0.3s ease;
  }
  
  .slide-enter-from {
    transform: translateX(-100%);
  }
  
  .slide-leave-to {
    transform: translateX(100%);
  }
  
  /* ==================== RESPONSIVE UTILITIES ==================== */
  
  @media (max-width: 480px) {
    .btn {
      padding: 6px 12px;
      font-size: 13px;
    }
    
    .modal-content {
      width: 95%;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
      padding: 12px 16px;
    }
  }
`
document.head.appendChild(globalStyles)

// ==================== ERROR HANDLING ====================

// Global error handler for Vue app
const handleError = (error, instance, info) => {
  console.error('VoiceHype Extension Error:', error)
  console.error('Component:', instance)
  console.error('Info:', info)
  
  // Send error to background script for logging
  if (chrome?.runtime?.sendMessage) {
    chrome.runtime.sendMessage({
      type: 'LOG_ERROR',
      error: {
        message: error.message,
        stack: error.stack,
        component: instance?.$options?.name || 'Unknown',
        info: info
      }
    }).catch(() => {
      // Ignore errors when sending error logs
    })
  }
}

// ==================== APP INITIALIZATION ====================

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', () => {
  // Create Vue app
  const app = createApp(VoiceHypeApp)
  
  // Configure error handling
  app.config.errorHandler = handleError
  
  // Global properties
  app.config.globalProperties.$chrome = chrome
  
  // Mount the app
  app.mount('#app')
  
  console.log('VoiceHype Extension Frontend initialized')
})

// ==================== CHROME EXTENSION SPECIFIC ====================

// Handle extension context invalidation
if (chrome?.runtime?.onConnect) {
  chrome.runtime.onConnect.addListener((port) => {
    port.onDisconnect.addListener(() => {
      if (chrome.runtime.lastError) {
        console.warn('Extension context invalidated, reloading...')
        window.location.reload()
      }
    })
  })
}

// Handle extension updates
if (chrome?.runtime?.onUpdateAvailable) {
  chrome.runtime.onUpdateAvailable.addListener(() => {
    console.log('Extension update available')
    // Could show a notification to user about update
  })
}

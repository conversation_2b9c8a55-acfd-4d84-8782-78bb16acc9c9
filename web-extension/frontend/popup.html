<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>VoiceHype - Voice to Prompt Extension</title>
  
  <!-- Preload critical resources -->
  <link rel="preconnect" href="https://cdnjs.cloudflare.com">
  
  <!-- Content Security Policy for extension -->
  <meta http-equiv="Content-Security-Policy" content="
    default-src 'self';
    script-src 'self' 'unsafe-inline' https://unpkg.com;
    style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com;
    font-src 'self' https://cdnjs.cloudflare.com;
    img-src 'self' data: https: chrome-extension:;
    connect-src 'self' https: wss: chrome-extension:;
  ">
  
  <!-- Favicon -->
  <link rel="icon" type="image/png" href="/icons/icon-48.png">
  
  <!-- Base styles for popup -->
  <style>
    /* ==================== POPUP SPECIFIC STYLES ==================== */
    
    html, body {
      width: 400px;
      height: 600px;
      margin: 0;
      padding: 0;
      overflow: hidden;
      background: #ffffff;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    
    #app {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
    }
    
    /* Loading state */
    .loading-container {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      background: #ffffff;
    }
    
    .loading-spinner {
      text-align: center;
      color: #6c757d;
    }
    
    .loading-spinner i {
      font-size: 24px;
      margin-bottom: 12px;
      color: #007bff;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }
    
    /* Error state */
    .error-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      padding: 20px;
      text-align: center;
      background: #ffffff;
    }
    
    .error-icon {
      font-size: 48px;
      color: #dc3545;
      margin-bottom: 16px;
    }
    
    .error-title {
      font-size: 18px;
      font-weight: 600;
      color: #212529;
      margin-bottom: 8px;
    }
    
    .error-message {
      font-size: 14px;
      color: #6c757d;
      margin-bottom: 20px;
      line-height: 1.5;
    }
    
    .error-actions {
      display: flex;
      gap: 8px;
    }
    
    .error-btn {
      padding: 8px 16px;
      border: 1px solid #007bff;
      border-radius: 6px;
      background: transparent;
      color: #007bff;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.2s ease;
    }
    
    .error-btn:hover {
      background: #007bff;
      color: white;
    }
    
    .error-btn.primary {
      background: #007bff;
      color: white;
    }
    
    .error-btn.primary:hover {
      background: #0056b3;
    }
    
    /* Authentication required state */
    .auth-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      padding: 20px;
      text-align: center;
      background: #ffffff;
    }
    
    .auth-logo {
      width: 64px;
      height: 64px;
      margin-bottom: 20px;
    }
    
    .auth-title {
      font-size: 20px;
      font-weight: 600;
      color: #212529;
      margin-bottom: 8px;
    }
    
    .auth-subtitle {
      font-size: 14px;
      color: #6c757d;
      margin-bottom: 24px;
      line-height: 1.5;
    }
    
    .auth-buttons {
      display: flex;
      flex-direction: column;
      gap: 12px;
      width: 100%;
      max-width: 280px;
    }
    
    .auth-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      padding: 12px 20px;
      border: 1px solid #dee2e6;
      border-radius: 6px;
      background: white;
      color: #212529;
      font-size: 14px;
      font-weight: 500;
      text-decoration: none;
      cursor: pointer;
      transition: all 0.2s ease;
    }
    
    .auth-btn:hover {
      background: #f8f9fa;
      border-color: #007bff;
    }
    
    .auth-btn.google {
      border-color: #db4437;
      color: #db4437;
    }
    
    .auth-btn.google:hover {
      background: #db4437;
      color: white;
    }
    
    .auth-btn.github {
      border-color: #333;
      color: #333;
    }
    
    .auth-btn.github:hover {
      background: #333;
      color: white;
    }
    
    /* Responsive adjustments for smaller screens */
    @media (max-width: 380px) {
      html, body {
        width: 100vw;
        height: 100vh;
      }
    }
  </style>
</head>

<body>
  <!-- Main app container -->
  <div id="app">
    <!-- Loading state (shown while Vue app initializes) -->
    <div class="loading-container" id="loading-state">
      <div class="loading-spinner">
        <i class="fas fa-spinner"></i>
        <p>Loading VoiceHype...</p>
      </div>
    </div>
    
    <!-- Error state (shown if app fails to load) -->
    <div class="error-container" id="error-state" style="display: none;">
      <div class="error-icon">
        <i class="fas fa-exclamation-triangle"></i>
      </div>
      <h3 class="error-title">Failed to Load</h3>
      <p class="error-message">
        VoiceHype extension failed to initialize. Please try refreshing or check your internet connection.
      </p>
      <div class="error-actions">
        <button class="error-btn" onclick="window.location.reload()">
          <i class="fas fa-redo"></i>
          Retry
        </button>
        <button class="error-btn primary" onclick="chrome.tabs.create({url: 'https://voicehype.netlify.app/support'})">
          <i class="fas fa-life-ring"></i>
          Get Help
        </button>
      </div>
    </div>
    
    <!-- Authentication required state -->
    <div class="auth-container" id="auth-state" style="display: none;">
      <img src="/icons/icon-128.png" alt="VoiceHype" class="auth-logo">
      <h2 class="auth-title">Welcome to VoiceHype</h2>
      <p class="auth-subtitle">
        Sign in to start converting your voice to optimized prompts for AI assistants.
      </p>
      <div class="auth-buttons">
        <button class="auth-btn google" onclick="signInWithGoogle()">
          <i class="fab fa-google"></i>
          Continue with Google
        </button>
        <button class="auth-btn github" onclick="signInWithGitHub()">
          <i class="fab fa-github"></i>
          Continue with GitHub
        </button>
      </div>
    </div>
  </div>

  <!-- Vue.js 3 from CDN -->
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
  
  <!-- Font Awesome for icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  
  <!-- Authentication helper functions -->
  <script>
    // ==================== AUTHENTICATION HELPERS ====================
    
    async function signInWithGoogle() {
      try {
        const response = await chrome.runtime.sendMessage({
          type: 'AUTHENTICATE',
          provider: 'google'
        })
        
        if (response.success) {
          // Hide auth state and show loading
          document.getElementById('auth-state').style.display = 'none'
          document.getElementById('loading-state').style.display = 'flex'
          
          // Reload to initialize authenticated app
          setTimeout(() => window.location.reload(), 1000)
        } else {
          throw new Error(response.error || 'Authentication failed')
        }
      } catch (error) {
        console.error('Google sign-in error:', error)
        showError('Failed to sign in with Google. Please try again.')
      }
    }
    
    async function signInWithGitHub() {
      try {
        const response = await chrome.runtime.sendMessage({
          type: 'AUTHENTICATE',
          provider: 'github'
        })
        
        if (response.success) {
          // Hide auth state and show loading
          document.getElementById('auth-state').style.display = 'none'
          document.getElementById('loading-state').style.display = 'flex'
          
          // Reload to initialize authenticated app
          setTimeout(() => window.location.reload(), 1000)
        } else {
          throw new Error(response.error || 'Authentication failed')
        }
      } catch (error) {
        console.error('GitHub sign-in error:', error)
        showError('Failed to sign in with GitHub. Please try again.')
      }
    }
    
    function showError(message) {
      document.getElementById('loading-state').style.display = 'none'
      document.getElementById('auth-state').style.display = 'none'
      document.getElementById('error-state').style.display = 'flex'
      document.querySelector('.error-message').textContent = message
    }
    
    // ==================== INITIALIZATION ====================
    
    // Check authentication status on load
    document.addEventListener('DOMContentLoaded', async () => {
      try {
        // Check if user is authenticated
        const response = await chrome.runtime.sendMessage({ type: 'CHECK_AUTH' })
        
        if (response.authenticated) {
          // User is authenticated, load main app
          document.getElementById('loading-state').style.display = 'flex'
          
          // Load main Vue app
          const script = document.createElement('script')
          script.src = './main.js'
          script.type = 'module'
          document.body.appendChild(script)
          
        } else {
          // User needs to authenticate
          document.getElementById('loading-state').style.display = 'none'
          document.getElementById('auth-state').style.display = 'flex'
        }
        
      } catch (error) {
        console.error('Initialization error:', error)
        showError('Failed to initialize VoiceHype extension.')
      }
    })
    
    // Handle extension context invalidation
    if (chrome?.runtime?.onConnect) {
      chrome.runtime.onConnect.addListener((port) => {
        port.onDisconnect.addListener(() => {
          if (chrome.runtime.lastError) {
            console.warn('Extension context invalidated, reloading...')
            window.location.reload()
          }
        })
      })
    }
  </script>
</body>
</html>

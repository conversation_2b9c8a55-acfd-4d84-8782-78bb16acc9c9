<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>VoiceHype - Authentication Callback</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .container {
      background: white;
      border-radius: 12px;
      padding: 32px;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      text-align: center;
      max-width: 400px;
      width: 100%;
    }
    
    .logo {
      width: 64px;
      height: 64px;
      background: #007bff;
      border-radius: 50%;
      margin: 0 auto 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 24px;
      font-weight: bold;
    }
    
    .spinner {
      width: 32px;
      height: 32px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #007bff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 20px;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .success {
      color: #28a745;
    }
    
    .error {
      color: #dc3545;
    }
    
    .message {
      margin: 20px 0;
      font-size: 16px;
      line-height: 1.5;
    }
    
    .details {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 16px;
      margin: 20px 0;
      font-size: 14px;
      text-align: left;
    }
    
    .button {
      background: #007bff;
      color: white;
      border: none;
      border-radius: 6px;
      padding: 12px 24px;
      font-size: 14px;
      cursor: pointer;
      transition: background-color 0.2s;
    }
    
    .button:hover {
      background: #0056b3;
    }
    
    .button:disabled {
      background: #6c757d;
      cursor: not-allowed;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="logo">VH</div>
    
    <div id="loading" class="state">
      <div class="spinner"></div>
      <h2>Processing Authentication...</h2>
      <p class="message">Please wait while we complete your VoiceHype authentication.</p>
    </div>
    
    <div id="success" class="state" style="display: none;">
      <h2 class="success">✓ Authentication Successful!</h2>
      <p class="message">Your VoiceHype extension has been authorized successfully.</p>
      <div class="details">
        <div><strong>User:</strong> <span id="user-name"></span></div>
        <div><strong>Email:</strong> <span id="user-email"></span></div>
        <div><strong>API Key:</strong> <span id="api-key-masked"></span></div>
      </div>
      <button class="button" onclick="closeWindow()">Close Window</button>
    </div>
    
    <div id="error" class="state" style="display: none;">
      <h2 class="error">✗ Authentication Failed</h2>
      <p class="message" id="error-message">An error occurred during authentication.</p>
      <div class="details" id="error-details" style="display: none;"></div>
      <button class="button" onclick="retry()">Try Again</button>
      <button class="button" onclick="closeWindow()" style="background: #6c757d; margin-left: 10px;">Close</button>
    </div>
  </div>

  <script>
    // Bismillahir rahmanir raheem
    
    class AuthCallbackHandler {
      constructor() {
        this.init();
      }
      
      async init() {
        try {
          console.log('VoiceHype: Auth callback handler initializing...');
          
          // Get auth data from URL
          const authData = this.extractAuthData();
          
          if (!authData) {
            throw new Error('No authentication data received');
          }
          
          // Validate auth data
          if (!authData.success) {
            throw new Error(authData.error || 'Authentication was not successful');
          }
          
          // Store credentials securely
          await this.storeCredentials(authData);
          
          // Show success state
          this.showSuccess(authData);
          
          // Notify background script
          await this.notifyBackgroundScript(authData);
          
          console.log('VoiceHype: Authentication completed successfully');
          
        } catch (error) {
          console.error('VoiceHype: Auth callback error:', error);
          this.showError(error.message);
        }
      }
      
      extractAuthData() {
        try {
          // Get data from URL parameters
          const urlParams = new URLSearchParams(window.location.search);
          const encodedData = urlParams.get('data');
          
          if (!encodedData) {
            return null;
          }
          
          // Decode and parse auth data
          const decodedData = atob(encodedData);
          return JSON.parse(decodedData);
          
        } catch (error) {
          console.error('Error extracting auth data:', error);
          return null;
        }
      }
      
      async storeCredentials(authData) {
        try {
          // Send credentials to background script for secure storage
          const response = await chrome.runtime.sendMessage({
            type: 'STORE_AUTH_CREDENTIALS',
            data: {
              user: authData.user,
              apiKey: authData.apiKey,
              session: authData.session,
              timestamp: authData.timestamp
            }
          });
          
          if (!response.success) {
            throw new Error(response.error || 'Failed to store credentials');
          }
          
          console.log('VoiceHype: Credentials stored successfully');
          
        } catch (error) {
          console.error('Error storing credentials:', error);
          throw error;
        }
      }
      
      async notifyBackgroundScript(authData) {
        try {
          // Notify background script that auth is complete
          await chrome.runtime.sendMessage({
            type: 'AUTH_COMPLETE',
            data: {
              user: authData.user,
              timestamp: authData.timestamp
            }
          });
          
        } catch (error) {
          console.warn('Could not notify background script:', error);
          // Non-critical error, don't throw
        }
      }
      
      showSuccess(authData) {
        // Hide loading state
        document.getElementById('loading').style.display = 'none';
        
        // Show success state
        const successDiv = document.getElementById('success');
        successDiv.style.display = 'block';
        
        // Populate user details
        const user = authData.user;
        document.getElementById('user-name').textContent = 
          user.user_metadata?.full_name || user.email;
        document.getElementById('user-email').textContent = user.email;
        
        // Show masked API key
        const apiKey = authData.apiKey;
        const maskedKey = `${apiKey.substring(0, 12)}...${apiKey.substring(apiKey.length - 8)}`;
        document.getElementById('api-key-masked').textContent = maskedKey;
      }
      
      showError(message, details = null) {
        // Hide loading state
        document.getElementById('loading').style.display = 'none';
        
        // Show error state
        const errorDiv = document.getElementById('error');
        errorDiv.style.display = 'block';
        
        // Set error message
        document.getElementById('error-message').textContent = message;
        
        // Show details if provided
        if (details) {
          const detailsDiv = document.getElementById('error-details');
          detailsDiv.textContent = details;
          detailsDiv.style.display = 'block';
        }
      }
    }
    
    // Global functions for buttons
    function closeWindow() {
      try {
        // Try to close the window
        window.close();
        
        // Fallback: notify extension to close
        setTimeout(() => {
          chrome.runtime.sendMessage({
            type: 'CLOSE_AUTH_WINDOW'
          });
        }, 100);
        
      } catch (error) {
        console.error('Error closing window:', error);
      }
    }
    
    function retry() {
      // Redirect back to auth page
      const authUrl = 'https://voicehype.netlify.app/web-extension-auth?browser=chrome&extension_id=' + 
                     chrome.runtime.id;
      window.location.href = authUrl;
    }
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        new AuthCallbackHandler();
      });
    } else {
      new AuthCallbackHandler();
    }
    
    // Handle extension context invalidation
    chrome.runtime.onConnect.addListener((port) => {
      port.onDisconnect.addListener(() => {
        if (chrome.runtime.lastError) {
          console.warn('VoiceHype: Extension context invalidated');
          document.getElementById('loading').style.display = 'none';
          document.getElementById('error').style.display = 'block';
          document.getElementById('error-message').textContent = 
            'Extension context was invalidated. Please try again.';
        }
      });
    });
  </script>
</body>
</html>

# VoiceHype Web Extension - Build and Test Instructions
*<PERSON><PERSON><PERSON><PERSON> rahmanir raheem*

## 🚀 **How to Build and Test the VoiceHype Web Extension**

### **Prerequisites**
- **Node.js 16+** installed on your system
- **Modern web browsers**: Chrome 88+, Firefox 78+, Edge 88+
- **Microphone access** for testing voice features
- **VoiceHype account** (create at https://voicehype.netlify.app)

---

## 📦 **Building the Extension**

### **1. Navigate to Extension Directory**
```bash
cd web-extension
```

### **2. Run the Build Script**
```bash
node build.js
```

**Expected Output:**
```
🚀 VoiceHype Web Extension Builder
Bismillahir rahmanir raheem

🧹 Cleaning build directory...
📦 Building for CHROME...
  ✅ Copied chrome manifest
  ✅ CHROME build complete
📦 Building for FIREFOX...
  ✅ Copied firefox manifest
  ✅ FIREFOX build complete
📦 Building for EDGE...
  ✅ Copied edge manifest
  ✅ EDGE build complete

🔍 Validating builds...
  ✅ CHROME: VoiceHype - Voice to Prompt for Developers v1.0.0
  ✅ FIREFOX: VoiceHype - Voice to Prompt for Developers v1.0.0
  ✅ EDGE: VoiceHype - Voice to Prompt for Developers v1.0.0

📦 Creating ZIP packages...
  ✅ Created voicehype-chrome.zip
  ✅ Created voicehype-firefox.zip
  ✅ Created voicehype-edge.zip

🎉 Build complete! Check the build/ directory for packages.
```

### **3. Verify Build Output**
```bash
ls build/
```

**You should see:**
```
chrome/                    # Chrome/Chromium build
firefox/                   # Firefox build
edge/                      # Microsoft Edge build
voicehype-chrome.zip       # Chrome distribution package
voicehype-firefox.zip      # Firefox distribution package
voicehype-edge.zip         # Edge distribution package
```

---

## 🧪 **Testing the Extension**

### **Chrome/Chromium Testing**

#### **1. Load Extension in Developer Mode**
1. Open Chrome and navigate to `chrome://extensions/`
2. Enable **"Developer mode"** (toggle in top-right corner)
3. Click **"Load unpacked"**
4. Select the `build/chrome/` directory
5. Extension should appear in your toolbar

#### **2. Verify Installation**
- Extension icon appears in browser toolbar
- Click icon to open popup interface
- No console errors in `chrome://extensions/`

### **Firefox Testing**

#### **1. Load as Temporary Add-on**
1. Open Firefox and navigate to `about:debugging`
2. Click **"This Firefox"** in left sidebar
3. Click **"Load Temporary Add-on..."**
4. Navigate to `build/firefox/` and select **any file**
5. Extension loads (temporary until browser restart)

#### **2. For Permanent Installation**
1. Zip the `build/firefox/` directory manually
2. Go to `about:addons`
3. Click gear icon → **"Install Add-on From File..."**
4. Select your ZIP file

### **Microsoft Edge Testing**

#### **1. Load Extension**
1. Open Edge and navigate to `edge://extensions/`
2. Enable **"Developer mode"** (toggle in left sidebar)
3. Click **"Load unpacked"**
4. Select the `build/edge/` directory
5. Extension should appear in toolbar

---

## ✅ **Comprehensive Testing Checklist**

### **🔐 Authentication Testing**
- [ ] Click extension icon opens popup
- [ ] "Sign in with Google" button works
- [ ] "Sign in with GitHub" button works
- [ ] OAuth popup opens VoiceHype website correctly
- [ ] Authentication completes and returns to extension
- [ ] User profile data displays in popup
- [ ] Sign out clears all data

### **🎤 Voice Recording Testing**

#### **Basic Recording**
- [ ] Visit any website with text fields (e.g., Gmail, GitHub)
- [ ] Microphone widget appears near text inputs
- [ ] Click widget requests microphone permission
- [ ] Recording starts (widget turns red)
- [ ] Speaking produces audio visualization
- [ ] Click widget again stops recording
- [ ] Transcript appears in text field

#### **Keyboard Shortcuts**
- [ ] Press `Ctrl+Shift+V` (or `Cmd+Shift+V` on Mac)
- [ ] Recording starts without clicking widget
- [ ] Press shortcut again to stop recording
- [ ] Works across different websites

#### **Real-time Transcription**
- [ ] Enable real-time mode in settings
- [ ] Start recording and speak
- [ ] Partial transcripts appear during recording
- [ ] Final transcript replaces partial text
- [ ] Real-time connection status shows in UI

### **🌐 Cross-Browser Compatibility**
- [ ] **Chrome**: All features work correctly
- [ ] **Firefox**: All features work correctly
- [ ] **Edge**: All features work correctly
- [ ] Manifest differences handled properly
- [ ] API compatibility layer works

### **🎯 Text Field Compatibility**
Test on various input types:
- [ ] `<input type="text">` - Basic text inputs
- [ ] `<textarea>` - Multi-line text areas
- [ ] `contenteditable` divs - Rich text editors
- [ ] Code editors (CodeMirror, Monaco)
- [ ] Popular websites:
  - [ ] Gmail compose
  - [ ] GitHub issue/PR forms
  - [ ] Google Docs
  - [ ] Slack message input
  - [ ] Discord chat
  - [ ] Twitter/X compose

### **⚙️ Settings & Configuration**
- [ ] Settings tab opens in popup
- [ ] Transcription service selection works
- [ ] Model selection updates correctly
- [ ] Real-time toggle functions
- [ ] Widget position settings apply
- [ ] Keyboard shortcut customization
- [ ] Settings persist across browser sessions

### **📊 Usage & Profile Data**
- [ ] Profile tab shows user information
- [ ] Usage tab displays statistics
- [ ] Subscription status visible
- [ ] Credit balance updates
- [ ] Usage history charts render

### **🚨 Error Handling**
- [ ] Microphone permission denied handled gracefully
- [ ] Network errors show user-friendly messages
- [ ] Invalid API key scenarios handled
- [ ] Quota exceeded notifications
- [ ] Extension context invalidation recovery

---

## 🐛 **Common Issues & Solutions**

### **Extension Won't Load**
- Check browser console for errors
- Verify manifest.json syntax
- Ensure all file paths are correct
- Try reloading extension

### **Authentication Fails**
- Check popup blockers
- Verify VoiceHype website accessibility
- Clear extension storage and retry
- Check browser console for OAuth errors

### **Recording Not Working**
- Verify microphone permissions granted
- Test on HTTPS websites (required for getUserMedia)
- Check browser console for audio errors
- Try different websites

### **Widgets Not Appearing**
- Check content script injection
- Verify CSS loading correctly
- Test on simpler websites first
- Check for conflicting page styles

### **Cross-Browser Issues**
- Firefox: Ensure manifest V2 compatibility
- Edge: Verify Chromium-based features work
- Check browser-specific API differences

---

## 📈 **Performance Testing**

### **Memory Usage**
- Monitor extension memory in browser task manager
- Test with multiple tabs open
- Check for memory leaks during long sessions

### **CPU Usage**
- Monitor during voice recording
- Check real-time transcription impact
- Test background processing efficiency

### **Network Usage**
- Monitor API calls to VoiceHype services
- Check WebSocket connection stability
- Test with slow network conditions

---

## 🔒 **Security Validation**

### **Data Privacy**
- Verify no transcripts stored locally
- Check API key encryption
- Validate secure storage implementation
- Ensure HTTPS-only connections

### **Permissions**
- Confirm minimal required permissions
- No unnecessary host permissions
- Proper content script isolation

---

## 📝 **Test Report Template**

```markdown
## VoiceHype Web Extension Test Report

**Date:** [Date]
**Tester:** [Name]
**Browser:** [Chrome/Firefox/Edge] [Version]
**OS:** [Operating System]

### ✅ Passed Tests
- Authentication flow
- Voice recording
- Text field detection
- Cross-browser compatibility

### ❌ Failed Tests
- [Issue description]
- [Steps to reproduce]
- [Expected vs actual behavior]

### 🐛 Bugs Found
1. **Issue:** Widget positioning incorrect on Gmail
   **Severity:** Medium
   **Browser:** Chrome 120.0
   **Steps:** Open Gmail compose, click message body
   **Expected:** Widget near cursor
   **Actual:** Widget at top-left

### 📊 Performance Notes
- Memory usage: [X]MB average
- CPU usage: [X]% during recording
- Network: [X] API calls per session

### 🔒 Security Notes
- All connections HTTPS ✅
- No local data storage ✅
- API keys encrypted ✅
```

---

## 🎉 **Success Criteria**

The extension is ready for deployment when:

- ✅ **All browsers load extension without errors**
- ✅ **Authentication flow completes successfully**
- ✅ **Voice recording works on major websites**
- ✅ **Real-time transcription functions properly**
- ✅ **Cross-browser compatibility verified**
- ✅ **Error handling graceful and user-friendly**
- ✅ **Performance within acceptable limits**
- ✅ **Security measures validated**
- ✅ **No critical bugs in core functionality**

---

## 📞 **Support & Troubleshooting**

If you encounter issues during testing:

1. **Check browser console** for error messages
2. **Enable extension debug mode** in settings
3. **Test in incognito/private mode** to isolate issues
4. **Compare behavior across different browsers**
5. **Report bugs** with detailed reproduction steps

**Happy testing! 🚀**

*May Allah bless this work and make it beneficial for developers worldwide.*

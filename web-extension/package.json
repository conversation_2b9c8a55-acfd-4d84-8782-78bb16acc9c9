{"name": "voicehype-web-extension", "version": "1.0.0", "description": "VoiceHype voice-to-prompt web extension for Chrome, Firefox, and Edge", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:chrome": "npm run build && npm run copy:chrome", "build:firefox": "npm run build && npm run copy:firefox", "build:edge": "npm run build && npm run copy:edge", "copy:chrome": "cp -r browser-specific/chrome/* dist/", "copy:firefox": "cp -r browser-specific/firefox/* dist/", "copy:edge": "cp -r browser-specific/edge/* dist/", "lint": "eslint . --ext .js,.vue", "lint:fix": "eslint . --ext .js,.vue --fix", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"@supabase/supabase-js": "^2.38.0", "vue": "^3.4.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.0", "@vue/eslint-config-prettier": "^9.0.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.20.0", "prettier": "^3.2.0", "terser": "^5.43.1", "vite": "^5.0.0", "vitest": "^1.2.0"}, "keywords": ["voice", "transcription", "ai", "productivity", "browser-extension", "vue", "supabase"], "author": "VoiceHype Team", "license": "Proprietary", "repository": {"type": "git", "url": "https://github.com/voicehype/voicehype-web-extension"}, "bugs": {"url": "https://github.com/voicehype/voicehype-web-extension/issues"}, "homepage": "https://voicehype.ai"}
/**
 * Storage Adapter - Specialized storage operations for VoiceHype
 * Bismillahir rahmanir raheem
 */

export class StorageAdapter {
  constructor(browserAdapter) {
    this.browser = browserAdapter;
    this.STORAGE_KEYS = {
      API_KEY: 'vh_api_key',
      API_KEY_HASH: 'vh_api_key_hash',
      USER_SETTINGS: 'vh_user_settings',
      RECORDING_HISTORY: 'vh_recording_history',
      USAGE_STATS: 'vh_usage_stats',
      AUTH_STATE: 'vh_auth_state',
      LAST_SYNC: 'vh_last_sync',
      WIDGET_POSITIONS: 'vh_widget_positions',
      SHORTCUTS: 'vh_shortcuts',
      THEME: 'vh_theme'
    };
  }

  // ==================== API KEY STORAGE ====================

  /**
   * Store API key securely with hash verification
   */
  async storeApiKey(apiKey) {
    try {
      if (!apiKey || !apiKey.startsWith('vhkey_')) {
        throw new Error('Invalid VoiceHype API key format');
      }

      // Generate hash for integrity verification
      const hash = await this.hashApiKey(apiKey);
      
      // Store both key and hash
      await Promise.all([
        this.browser.setStorage(this.STORAGE_KEYS.API_KEY, apiKey),
        this.browser.setStorage(this.STORAGE_KEYS.API_KEY_HASH, hash)
      ]);

      console.log('VoiceHype: API key stored securely');
      return true;
    } catch (error) {
      console.error('VoiceHype: Failed to store API key:', error);
      throw error;
    }
  }

  /**
   * Retrieve API key with integrity verification
   */
  async getApiKey() {
    try {
      const [apiKey, storedHash] = await Promise.all([
        this.browser.getStorage(this.STORAGE_KEYS.API_KEY),
        this.browser.getStorage(this.STORAGE_KEYS.API_KEY_HASH)
      ]);

      if (!apiKey || !storedHash) {
        return null;
      }

      // Verify integrity
      const currentHash = await this.hashApiKey(apiKey);
      if (currentHash !== storedHash) {
        console.warn('VoiceHype: API key integrity check failed');
        await this.clearApiKey();
        return null;
      }

      return apiKey;
    } catch (error) {
      console.error('VoiceHype: Failed to retrieve API key:', error);
      return null;
    }
  }

  /**
   * Clear API key and hash
   */
  async clearApiKey() {
    try {
      await Promise.all([
        this.browser.removeStorage(this.STORAGE_KEYS.API_KEY),
        this.browser.removeStorage(this.STORAGE_KEYS.API_KEY_HASH)
      ]);
      console.log('VoiceHype: API key cleared');
    } catch (error) {
      console.error('VoiceHype: Failed to clear API key:', error);
      throw error;
    }
  }

  /**
   * Hash API key for integrity verification
   */
  async hashApiKey(apiKey) {
    const encoder = new TextEncoder();
    const data = encoder.encode(apiKey + 'voicehype_salt');
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    return Array.from(new Uint8Array(hashBuffer))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
  }

  // ==================== USER SETTINGS ====================

  /**
   * Get user settings with defaults
   */
  async getUserSettings() {
    try {
      const settings = await this.browser.getStorage(this.STORAGE_KEYS.USER_SETTINGS);
      return {
        // Default settings
        transcriptionService: 'lemonfox',
        transcriptionModel: 'whisper-1',
        optimizationModel: 'claude-3-5-sonnet-20241022',
        language: 'en',
        autoOptimize: false,
        realTimeTranscription: false,
        voiceCommandsEnabled: true,
        theme: 'auto',
        shortcuts: {
          startRecording: 'Ctrl+Shift+8',
          startWithOptimization: 'Ctrl+Shift+9'
        },
        widgetPosition: 'bottom-right',
        showNotifications: true,
        autoSave: true,
        maxHistoryItems: 100,
        // Override with stored settings
        ...settings
      };
    } catch (error) {
      console.error('VoiceHype: Failed to get user settings:', error);
      return {};
    }
  }

  /**
   * Save user settings
   */
  async saveUserSettings(settings) {
    try {
      const currentSettings = await this.getUserSettings();
      const updatedSettings = { ...currentSettings, ...settings };
      
      await this.browser.setStorage(this.STORAGE_KEYS.USER_SETTINGS, updatedSettings);
      console.log('VoiceHype: User settings saved');
      return updatedSettings;
    } catch (error) {
      console.error('VoiceHype: Failed to save user settings:', error);
      throw error;
    }
  }

  // ==================== RECORDING HISTORY ====================

  /**
   * Add recording to history
   */
  async addToHistory(recording) {
    try {
      const history = await this.getHistory();
      const newEntry = {
        id: `vh_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date().toISOString(),
        duration: recording.duration,
        transcription: recording.transcription,
        optimizedText: recording.optimizedText,
        service: recording.service,
        model: recording.model,
        language: recording.language,
        tokensUsed: recording.tokensUsed || 0,
        cost: recording.cost || 0
      };

      history.unshift(newEntry);

      // Limit history size
      const settings = await this.getUserSettings();
      const maxItems = settings.maxHistoryItems || 100;
      if (history.length > maxItems) {
        history.splice(maxItems);
      }

      await this.browser.setLocalStorage(this.STORAGE_KEYS.RECORDING_HISTORY, history);
      return newEntry;
    } catch (error) {
      console.error('VoiceHype: Failed to add to history:', error);
      throw error;
    }
  }

  /**
   * Get recording history
   */
  async getHistory(limit = null) {
    try {
      const history = await this.browser.getLocalStorage(this.STORAGE_KEYS.RECORDING_HISTORY) || [];
      return limit ? history.slice(0, limit) : history;
    } catch (error) {
      console.error('VoiceHype: Failed to get history:', error);
      return [];
    }
  }

  /**
   * Clear recording history
   */
  async clearHistory() {
    try {
      await this.browser.setLocalStorage(this.STORAGE_KEYS.RECORDING_HISTORY, []);
      console.log('VoiceHype: History cleared');
    } catch (error) {
      console.error('VoiceHype: Failed to clear history:', error);
      throw error;
    }
  }

  // ==================== USAGE STATISTICS ====================

  /**
   * Update usage statistics
   */
  async updateUsageStats(stats) {
    try {
      const currentStats = await this.getUsageStats();
      const updatedStats = {
        totalRecordings: (currentStats.totalRecordings || 0) + (stats.recordings || 0),
        totalMinutes: (currentStats.totalMinutes || 0) + (stats.minutes || 0),
        totalTokens: (currentStats.totalTokens || 0) + (stats.tokens || 0),
        totalCost: (currentStats.totalCost || 0) + (stats.cost || 0),
        lastUpdated: new Date().toISOString(),
        dailyUsage: this.updateDailyUsage(currentStats.dailyUsage || {}, stats)
      };

      await this.browser.setLocalStorage(this.STORAGE_KEYS.USAGE_STATS, updatedStats);
      return updatedStats;
    } catch (error) {
      console.error('VoiceHype: Failed to update usage stats:', error);
      throw error;
    }
  }

  /**
   * Get usage statistics
   */
  async getUsageStats() {
    try {
      return await this.browser.getLocalStorage(this.STORAGE_KEYS.USAGE_STATS) || {
        totalRecordings: 0,
        totalMinutes: 0,
        totalTokens: 0,
        totalCost: 0,
        dailyUsage: {},
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      console.error('VoiceHype: Failed to get usage stats:', error);
      return {};
    }
  }

  /**
   * Update daily usage tracking
   */
  updateDailyUsage(dailyUsage, stats) {
    const today = new Date().toISOString().split('T')[0];
    const todayStats = dailyUsage[today] || { recordings: 0, minutes: 0, tokens: 0, cost: 0 };
    
    return {
      ...dailyUsage,
      [today]: {
        recordings: todayStats.recordings + (stats.recordings || 0),
        minutes: todayStats.minutes + (stats.minutes || 0),
        tokens: todayStats.tokens + (stats.tokens || 0),
        cost: todayStats.cost + (stats.cost || 0)
      }
    };
  }

  // ==================== AUTHENTICATION STATE ====================

  /**
   * Store authentication state
   */
  async storeAuthState(authState) {
    try {
      await this.browser.setStorage(this.STORAGE_KEYS.AUTH_STATE, {
        ...authState,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('VoiceHype: Failed to store auth state:', error);
      throw error;
    }
  }

  /**
   * Get authentication state
   */
  async getAuthState() {
    try {
      return await this.browser.getStorage(this.STORAGE_KEYS.AUTH_STATE);
    } catch (error) {
      console.error('VoiceHype: Failed to get auth state:', error);
      return null;
    }
  }

  /**
   * Clear authentication state
   */
  async clearAuthState() {
    try {
      await this.browser.removeStorage(this.STORAGE_KEYS.AUTH_STATE);
    } catch (error) {
      console.error('VoiceHype: Failed to clear auth state:', error);
      throw error;
    }
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Clear all VoiceHype data
   */
  async clearAllData() {
    try {
      const clearPromises = Object.values(this.STORAGE_KEYS).map(key => 
        this.browser.removeStorage(key)
      );
      await Promise.all(clearPromises);
      console.log('VoiceHype: All data cleared');
    } catch (error) {
      console.error('VoiceHype: Failed to clear all data:', error);
      throw error;
    }
  }

  /**
   * Export user data
   */
  async exportData() {
    try {
      const [settings, history, stats, authState] = await Promise.all([
        this.getUserSettings(),
        this.getHistory(),
        this.getUsageStats(),
        this.getAuthState()
      ]);

      return {
        exportDate: new Date().toISOString(),
        version: '1.0.0',
        settings,
        history,
        stats,
        authState: authState ? { ...authState, apiKey: '[REDACTED]' } : null
      };
    } catch (error) {
      console.error('VoiceHype: Failed to export data:', error);
      throw error;
    }
  }
}

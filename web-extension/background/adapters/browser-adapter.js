/**
 * Browser Adapter - Universal API abstraction for Chrome/Firefox differences
 * Bismillahir rahmanir raheem
 */

export class BrowserAdapter {
  constructor() {
    // Detect browser environment
    this.isChrome = typeof chrome !== 'undefined' && chrome.runtime;
    this.isFirefox = typeof browser !== 'undefined' && browser.runtime;
    this.isEdge = this.isChrome && navigator.userAgent.includes('Edg');
    
    // Use appropriate API
    this.api = this.isChrome ? chrome : browser;
    
    // Browser info for debugging
    this.browserInfo = {
      name: this.isFirefox ? 'firefox' : this.isEdge ? 'edge' : 'chrome',
      manifestVersion: this.isFirefox ? 2 : 3,
      supportsServiceWorker: !this.isFirefox,
      version: this.getBrowserVersion(),
      userAgent: navigator.userAgent
    };
    
    console.log('VoiceHype: Browser detected:', this.browserInfo);
  }

  // ==================== BROWSER VERSION DETECTION ====================

  getBrowserVersion() {
    const userAgent = navigator.userAgent;

    if (this.isFirefox) {
      const match = userAgent.match(/Firefox\/(\d+\.\d+)/);
      return match ? match[1] : 'unknown';
    } else if (this.isEdge) {
      const match = userAgent.match(/Edg\/(\d+\.\d+)/);
      return match ? match[1] : 'unknown';
    } else {
      const match = userAgent.match(/Chrome\/(\d+\.\d+)/);
      return match ? match[1] : 'unknown';
    }
  }

  // ==================== STORAGE METHODS ====================
  
  /**
   * Get value from sync storage
   */
  async getStorage(key) {
    return new Promise((resolve, reject) => {
      try {
        this.api.storage.sync.get(key, (result) => {
          if (this.api.runtime.lastError) {
            reject(new Error(this.api.runtime.lastError.message));
          } else {
            resolve(result[key]);
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Set value in sync storage
   */
  async setStorage(key, value) {
    return new Promise((resolve, reject) => {
      try {
        this.api.storage.sync.set({ [key]: value }, () => {
          if (this.api.runtime.lastError) {
            reject(new Error(this.api.runtime.lastError.message));
          } else {
            resolve();
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Get value from local storage
   */
  async getLocalStorage(key) {
    return new Promise((resolve, reject) => {
      try {
        this.api.storage.local.get(key, (result) => {
          if (this.api.runtime.lastError) {
            reject(new Error(this.api.runtime.lastError.message));
          } else {
            resolve(result[key]);
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Set value in local storage
   */
  async setLocalStorage(key, value) {
    return new Promise((resolve, reject) => {
      try {
        this.api.storage.local.set({ [key]: value }, () => {
          if (this.api.runtime.lastError) {
            reject(new Error(this.api.runtime.lastError.message));
          } else {
            resolve();
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Remove key from storage
   */
  async removeStorage(key) {
    return new Promise((resolve, reject) => {
      try {
        this.api.storage.sync.remove(key, () => {
          if (this.api.runtime.lastError) {
            reject(new Error(this.api.runtime.lastError.message));
          } else {
            resolve();
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Clear all storage
   */
  async clearAllStorage() {
    return new Promise((resolve, reject) => {
      try {
        Promise.all([
          new Promise(res => this.api.storage.sync.clear(res)),
          new Promise(res => this.api.storage.local.clear(res))
        ]).then(() => {
          if (this.api.runtime.lastError) {
            reject(new Error(this.api.runtime.lastError.message));
          } else {
            resolve();
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  // ==================== MESSAGING METHODS ====================

  /**
   * Listen for messages from content scripts or popup
   */
  onMessage(callback) {
    this.api.runtime.onMessage.addListener((message, sender, sendResponse) => {
      // Handle async responses properly
      const result = callback(message, sender, sendResponse);
      if (result instanceof Promise) {
        result.then(sendResponse).catch(error => {
          console.error('VoiceHype: Message handler error:', error);
          sendResponse({ error: error.message });
        });
        return true; // Keep message channel open for async response
      }
      return false;
    });
  }

  /**
   * Send message to specific tab
   */
  async sendMessageToTab(tabId, message) {
    return new Promise((resolve, reject) => {
      try {
        this.api.tabs.sendMessage(tabId, message, (response) => {
          if (this.api.runtime.lastError) {
            // Tab might be closed or not ready, don't reject
            console.warn('VoiceHype: Failed to send message to tab:', this.api.runtime.lastError.message);
            resolve(null);
          } else {
            resolve(response);
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Send message to all tabs
   */
  async sendMessageToAllTabs(message) {
    try {
      const tabs = await this.getAllTabs();
      const promises = tabs.map(tab => this.sendMessageToTab(tab.id, message));
      return await Promise.allSettled(promises);
    } catch (error) {
      console.error('VoiceHype: Failed to send message to all tabs:', error);
      return [];
    }
  }

  /**
   * Send message to runtime (background to popup/options)
   */
  async sendMessage(message) {
    return new Promise((resolve, reject) => {
      try {
        this.api.runtime.sendMessage(message, (response) => {
          if (this.api.runtime.lastError) {
            reject(new Error(this.api.runtime.lastError.message));
          } else {
            resolve(response);
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  // ==================== TAB METHODS ====================

  /**
   * Get all tabs
   */
  async getAllTabs() {
    return new Promise((resolve, reject) => {
      try {
        this.api.tabs.query({}, (tabs) => {
          if (this.api.runtime.lastError) {
            reject(new Error(this.api.runtime.lastError.message));
          } else {
            resolve(tabs);
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Get active tab
   */
  async getActiveTab() {
    return new Promise((resolve, reject) => {
      try {
        this.api.tabs.query({ active: true, currentWindow: true }, (tabs) => {
          if (this.api.runtime.lastError) {
            reject(new Error(this.api.runtime.lastError.message));
          } else {
            resolve(tabs[0]);
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Create new tab
   */
  async createTab(url) {
    return new Promise((resolve, reject) => {
      try {
        this.api.tabs.create({ url }, (tab) => {
          if (this.api.runtime.lastError) {
            reject(new Error(this.api.runtime.lastError.message));
          } else {
            resolve(tab);
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Get tab by ID
   */
  async getTab(tabId) {
    return new Promise((resolve, reject) => {
      try {
        this.api.tabs.get(tabId, (tab) => {
          if (this.api.runtime.lastError) {
            resolve(null); // Tab doesn't exist
          } else {
            resolve(tab);
          }
        });
      } catch (error) {
        resolve(null);
      }
    });
  }

  /**
   * Close tab
   */
  async closeTab(tabId) {
    return new Promise((resolve, reject) => {
      try {
        this.api.tabs.remove(tabId, () => {
          if (this.api.runtime.lastError) {
            reject(new Error(this.api.runtime.lastError.message));
          } else {
            resolve();
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  // ==================== PERMISSION METHODS ====================

  /**
   * Request permission
   */
  async requestPermission(permission) {
    return new Promise((resolve, reject) => {
      try {
        this.api.permissions.request({ permissions: [permission] }, (granted) => {
          if (this.api.runtime.lastError) {
            reject(new Error(this.api.runtime.lastError.message));
          } else {
            resolve(granted);
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Check if permission is granted
   */
  async hasPermission(permission) {
    return new Promise((resolve, reject) => {
      try {
        this.api.permissions.contains({ permissions: [permission] }, (hasPermission) => {
          if (this.api.runtime.lastError) {
            reject(new Error(this.api.runtime.lastError.message));
          } else {
            resolve(hasPermission);
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Get extension URL
   */
  getExtensionURL(path) {
    return this.api.runtime.getURL(path);
  }

  /**
   * Get extension ID
   */
  getExtensionId() {
    return this.api.runtime.id;
  }

  /**
   * Open options page
   */
  async openOptionsPage() {
    if (this.api.runtime.openOptionsPage) {
      this.api.runtime.openOptionsPage();
    } else {
      // Fallback for browsers that don't support openOptionsPage
      const url = this.getExtensionURL('frontend/options/options.html');
      await this.createTab(url);
    }
  }

  // ==================== COMMAND METHODS ====================

  /**
   * Listen for keyboard commands
   */
  onCommand(callback) {
    if (this.api.commands && this.api.commands.onCommand) {
      this.api.commands.onCommand.addListener(callback);
    }
  }

  // ==================== CONTEXT MENU METHODS ====================

  /**
   * Create context menu item
   */
  createContextMenu(options) {
    if (this.api.contextMenus) {
      return this.api.contextMenus.create(options);
    }
  }

  /**
   * Listen for context menu clicks
   */
  onContextMenuClick(callback) {
    if (this.api.contextMenus && this.api.contextMenus.onClicked) {
      this.api.contextMenus.onClicked.addListener(callback);
    }
  }

  // ==================== NOTIFICATION METHODS ====================

  /**
   * Show notification
   */
  async showNotification(options) {
    return new Promise((resolve, reject) => {
      try {
        const notificationId = `vh_${Date.now()}`;
        this.api.notifications.create(notificationId, {
          type: 'basic',
          iconUrl: this.getExtensionURL('assets/icons/icon-48.png'),
          title: 'VoiceHype',
          ...options
        }, (id) => {
          if (this.api.runtime.lastError) {
            reject(new Error(this.api.runtime.lastError.message));
          } else {
            resolve(id);
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }
}

/**
 * Messaging Adapter - Structured communication between extension components
 * Bismillahir rahmanir raheem
 */

export class MessagingAdapter {
  constructor(browserAdapter) {
    this.browser = browserAdapter;
    this.messageHandlers = new Map();
    this.setupMessageListener();
  }

  // ==================== MESSAGE TYPES ====================
  
  static MESSAGE_TYPES = {
    // Authentication
    AUTH_LOGIN: 'auth_login',
    AUTH_LOGOUT: 'auth_logout',
    AUTH_CHECK: 'auth_check',
    AUTH_GET_USER: 'auth_get_user',
    
    // Recording
    RECORDING_START: 'recording_start',
    RECORDING_STOP: 'recording_stop',
    RECORDING_PAUSE: 'recording_pause',
    RECORDING_RESUME: 'recording_resume',
    RECORDING_STATUS: 'recording_status',
    RECORDING_GET_CURRENT: 'recording_get_current',
    RECORDING_CLEAR_CURRENT: 'recording_clear_current',
    RECORDING_CHECK_MIC: 'recording_check_mic',
    
    // Transcription
    TRANSCRIBE_AUDIO: 'transcribe_audio',
    TRANSCRIBE_REALTIME: 'transcribe_realtime',
    TRANSCRIPTION_RESULT: 'transcription_result',
    TRANSCRIPTION_ERROR: 'transcription_error',
    
    // Optimization
    OPTIMIZE_TEXT: 'optimize_text',
    OPTIMIZATION_RESULT: 'optimization_result',
    OPTIMIZATION_ERROR: 'optimization_error',
    
    // Settings
    SETTINGS_GET: 'settings_get',
    SETTINGS_UPDATE: 'settings_update',
    SETTINGS_RESET: 'settings_reset',
    
    // History
    HISTORY_GET: 'history_get',
    HISTORY_ADD: 'history_add',
    HISTORY_CLEAR: 'history_clear',
    
    // Usage
    USAGE_GET: 'usage_get',
    USAGE_UPDATE: 'usage_update',
    
    // Widget
    WIDGET_SHOW: 'widget_show',
    WIDGET_HIDE: 'widget_hide',
    WIDGET_UPDATE: 'widget_update',
    WIDGET_POSITION: 'widget_position',
    
    // UI
    UI_NOTIFICATION: 'ui_notification',
    UI_UPDATE_BADGE: 'ui_update_badge',
    UI_OPEN_OPTIONS: 'ui_open_options',
    
    // System
    SYSTEM_STATUS: 'system_status',
    SYSTEM_ERROR: 'system_error',
    SYSTEM_PING: 'system_ping'
  };

  // ==================== MESSAGE HANDLING ====================

  /**
   * Setup main message listener
   */
  setupMessageListener() {
    this.browser.onMessage(async (message, sender, sendResponse) => {
      try {
        const { type, data, requestId } = message;
        
        if (!type) {
          throw new Error('Message type is required');
        }

        // Get handler for message type
        const handler = this.messageHandlers.get(type);
        if (!handler) {
          throw new Error(`No handler registered for message type: ${type}`);
        }

        // Execute handler
        const result = await handler(data, sender);
        
        // Send response
        return {
          success: true,
          data: result,
          requestId,
          timestamp: new Date().toISOString()
        };
        
      } catch (error) {
        console.error('VoiceHype: Message handling error:', error);
        return {
          success: false,
          error: error.message,
          requestId: message.requestId,
          timestamp: new Date().toISOString()
        };
      }
    });
  }

  /**
   * Register message handler
   */
  registerHandler(messageType, handler) {
    if (typeof handler !== 'function') {
      throw new Error('Handler must be a function');
    }
    
    this.messageHandlers.set(messageType, handler);
    console.log(`VoiceHype: Registered handler for ${messageType}`);
  }

  /**
   * Unregister message handler
   */
  unregisterHandler(messageType) {
    this.messageHandlers.delete(messageType);
    console.log(`VoiceHype: Unregistered handler for ${messageType}`);
  }

  // ==================== MESSAGE SENDING ====================

  /**
   * Send message with automatic response handling
   */
  async sendMessage(type, data = null, options = {}) {
    const message = {
      type,
      data,
      requestId: this.generateRequestId(),
      timestamp: new Date().toISOString(),
      ...options
    };

    try {
      if (options.tabId) {
        // Send to specific tab
        return await this.browser.sendMessageToTab(options.tabId, message);
      } else if (options.broadcast) {
        // Send to all tabs
        return await this.browser.sendMessageToAllTabs(message);
      } else {
        // Send to runtime (popup/options)
        return await this.browser.sendMessage(message);
      }
    } catch (error) {
      console.error('VoiceHype: Failed to send message:', error);
      throw error;
    }
  }

  /**
   * Send message to content script
   */
  async sendToContent(type, data = null, tabId = null) {
    if (!tabId) {
      const activeTab = await this.browser.getActiveTab();
      tabId = activeTab?.id;
    }

    if (!tabId) {
      throw new Error('No active tab found');
    }

    return await this.sendMessage(type, data, { tabId });
  }

  /**
   * Broadcast message to all tabs
   */
  async broadcast(type, data = null) {
    return await this.sendMessage(type, data, { broadcast: true });
  }

  /**
   * Send notification message
   */
  async sendNotification(message, type = 'info', options = {}) {
    return await this.broadcast(MessagingAdapter.MESSAGE_TYPES.UI_NOTIFICATION, {
      message,
      type,
      timestamp: new Date().toISOString(),
      ...options
    });
  }

  // ==================== SPECIALIZED MESSAGE METHODS ====================

  /**
   * Send recording status update
   */
  async sendRecordingStatus(status, data = {}) {
    return await this.broadcast(MessagingAdapter.MESSAGE_TYPES.RECORDING_STATUS, {
      status, // 'idle', 'recording', 'processing', 'error'
      ...data
    });
  }

  /**
   * Send transcription result
   */
  async sendTranscriptionResult(result, tabId = null) {
    return await this.sendToContent(MessagingAdapter.MESSAGE_TYPES.TRANSCRIPTION_RESULT, result, tabId);
  }

  /**
   * Send transcription error
   */
  async sendTranscriptionError(error, tabId = null) {
    return await this.sendToContent(MessagingAdapter.MESSAGE_TYPES.TRANSCRIPTION_ERROR, {
      error: error.message || error,
      timestamp: new Date().toISOString()
    }, tabId);
  }

  /**
   * Send optimization result
   */
  async sendOptimizationResult(result, tabId = null) {
    return await this.sendToContent(MessagingAdapter.MESSAGE_TYPES.OPTIMIZATION_RESULT, result, tabId);
  }

  /**
   * Update extension badge
   */
  async updateBadge(text, color = '#4CAF50') {
    return await this.sendMessage(MessagingAdapter.MESSAGE_TYPES.UI_UPDATE_BADGE, {
      text,
      color
    });
  }

  /**
   * Show widget in content script
   */
  async showWidget(position, data = {}, tabId = null) {
    return await this.sendToContent(MessagingAdapter.MESSAGE_TYPES.WIDGET_SHOW, {
      position,
      ...data
    }, tabId);
  }

  /**
   * Hide widget in content script
   */
  async hideWidget(tabId = null) {
    return await this.sendToContent(MessagingAdapter.MESSAGE_TYPES.WIDGET_HIDE, {}, tabId);
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Generate unique request ID
   */
  generateRequestId() {
    return `vh_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Create standardized error response
   */
  createErrorResponse(error, requestId = null) {
    return {
      success: false,
      error: error.message || error,
      requestId,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Create standardized success response
   */
  createSuccessResponse(data, requestId = null) {
    return {
      success: true,
      data,
      requestId,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Validate message structure
   */
  validateMessage(message) {
    if (!message || typeof message !== 'object') {
      throw new Error('Message must be an object');
    }

    if (!message.type) {
      throw new Error('Message type is required');
    }

    if (!Object.values(MessagingAdapter.MESSAGE_TYPES).includes(message.type)) {
      console.warn(`VoiceHype: Unknown message type: ${message.type}`);
    }

    return true;
  }

  /**
   * Get all registered handlers
   */
  getRegisteredHandlers() {
    return Array.from(this.messageHandlers.keys());
  }

  /**
   * Clear all handlers
   */
  clearAllHandlers() {
    this.messageHandlers.clear();
    console.log('VoiceHype: All message handlers cleared');
  }
}

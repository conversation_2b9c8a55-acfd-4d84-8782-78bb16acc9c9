/**
 * Supabase Service - API calls to VoiceHype edge functions
 * <PERSON><PERSON><PERSON><PERSON> rahmanir raheem
 *
 * This service handles all communication with VoiceHype Supabase edge functions
 * following the same patterns as the VS Code extension
 */

import { createClient } from '@supabase/supabase-js';

export class SupabaseService {
  constructor(configManager, secureStorage = null) {
    this.config = configManager;
    this.secureStorage = secureStorage;
    this.supabase = null;
    this.isInitialized = false;

    // Request tracking for debugging
    this.requestCount = 0;
    this.activeRequests = new Map();
  }

  // ==================== INITIALIZATION ====================

  /**
   * Initialize Supabase client (same as website)
   */
  async initialize() {
    try {
      const apiConfig = this.config.getSection('api');

      if (!apiConfig?.supabaseUrl || !apiConfig?.supabaseAnonKey) {
        throw new Error('Supabase configuration missing');
      }

      // Create Supabase client (same as voicehype-website/src/lib/supabase.ts)
      this.supabase = createClient(apiConfig.supabaseUrl, apiConfig.supabaseAnonKey, {
        auth: {
          autoRefreshToken: true,
          persistSession: false, // We handle session persistence manually via AuthService
          detectSessionInUrl: false
        }
      });

      this.isInitialized = true;
      console.log('VoiceHype: Supabase service initialized');

      return this.supabase;
    } catch (error) {
      console.error('VoiceHype: Failed to initialize Supabase service:', error);
      throw error;
    }
  }

  /**
   * Ensure service is initialized
   */
  ensureInitialized() {
    if (!this.isInitialized || !this.supabase) {
      throw new Error('Supabase service not initialized. Call initialize() first.');
    }
  }

  // ==================== API KEY MANAGEMENT ====================

  /**
   * Get current API key (with secure storage fallback)
   */
  async getApiKey() {
    if (this.secureStorage) {
      return await this.secureStorage.getApiKeySecure();
    }
    return await this.config.getApiKey();
  }

  /**
   * Validate API key with VoiceHype backend
   */
  async validateApiKey(apiKey = null) {
    this.ensureInitialized();
    
    try {
      const keyToValidate = apiKey || await this.getApiKey();
      
      if (!keyToValidate) {
        throw new Error('No API key provided');
      }

      if (!keyToValidate.startsWith('vhkey_')) {
        throw new Error('Invalid VoiceHype API key format');
      }

      const response = await this.makeRequest('/functions/v1/validate-api-key', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${keyToValidate}`
        }
      });

      return response.ok;
    } catch (error) {
      console.error('VoiceHype: API key validation failed:', error);
      return false;
    }
  }

  // ==================== TRANSCRIPTION SERVICES ====================

  /**
   * Transcribe audio using VoiceHype edge function
   * Same pattern as VS Code extension
   */
  async transcribeAudio(base64Audio, options = {}) {
    this.ensureInitialized();
    
    const apiKey = await this.getApiKey();
    if (!apiKey) {
      throw new Error('No API key found. Please authenticate first.');
    }

    const transcriptionConfig = this.config.getSection('transcription');
    
    const requestBody = {
      audio: base64Audio,
      service: options.service || transcriptionConfig.service || 'lemonfox',
      model: options.model || transcriptionConfig.model || 'whisper-1',
      language: options.language || transcriptionConfig.language || 'en',
      translate: options.translate || transcriptionConfig.translate || false,
      realtime: options.realtime || transcriptionConfig.realtime || false
    };

    console.log('VoiceHype: Starting transcription with service:', requestBody.service);

    const response = await this.makeRequest('/functions/v1/transcribe', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'apikey': apiKey
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Transcription failed: ${errorText}`);
    }

    const result = await response.json();
    console.log('VoiceHype: Transcription completed successfully');
    
    return result;
  }

  /**
   * Transcribe and optimize in one call (same as VS Code extension)
   */
  async transcribeAndOptimize(base64Audio, options = {}) {
    this.ensureInitialized();
    
    const apiKey = await this.getApiKey();
    if (!apiKey) {
      throw new Error('No API key found. Please authenticate first.');
    }

    const transcriptionConfig = this.config.getSection('transcription');
    const optimizationConfig = this.config.getSection('optimization');
    
    const requestBody = {
      // Transcription options
      audio: base64Audio,
      service: options.service || transcriptionConfig.service || 'lemonfox',
      model: options.model || transcriptionConfig.model || 'whisper-1',
      language: options.language || transcriptionConfig.language || 'en',
      translate: options.translate || transcriptionConfig.translate || false,
      realtime: options.realtime || transcriptionConfig.realtime || false,
      
      // Optimization options
      optimizationModel: options.optimizationModel || optimizationConfig.model || 'claude-3-5-sonnet-20241022',
      customPrompt: options.customPrompt || optimizationConfig.customPrompt || 'Optimize this transcription for clarity and conciseness:',
      voiceCommandsEnabled: options.voiceCommandsEnabled !== false && optimizationConfig.voiceCommandsEnabled !== false
    };

    console.log('VoiceHype: Starting transcription and optimization');

    const response = await this.makeRequest('/functions/v1/transcribe-and-optimize', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'apikey': apiKey
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Transcription and optimization failed: ${errorText}`);
    }

    const result = await response.json();
    console.log('VoiceHype: Transcription and optimization completed successfully');
    
    return result;
  }

  /**
   * Optimize existing text
   */
  async optimizeText(text, options = {}) {
    this.ensureInitialized();
    
    const apiKey = await this.getApiKey();
    if (!apiKey) {
      throw new Error('No API key found. Please authenticate first.');
    }

    const optimizationConfig = this.config.getSection('optimization');
    
    const requestBody = {
      text,
      model: options.model || optimizationConfig.model || 'claude-3-5-sonnet-20241022',
      customPrompt: options.customPrompt || optimizationConfig.customPrompt || 'Optimize this text for clarity and conciseness:',
      voiceCommandsEnabled: options.voiceCommandsEnabled !== false && optimizationConfig.voiceCommandsEnabled !== false,
      maxTokens: options.maxTokens || optimizationConfig.maxTokens || 4000,
      temperature: options.temperature || optimizationConfig.temperature || 0.1
    };

    const response = await this.makeRequest('/functions/v1/optimize', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'apikey': apiKey
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Text optimization failed: ${errorText}`);
    }

    return await response.json();
  }

  // ==================== USER DATA & USAGE ====================

  /**
   * Get user usage data (same as website)
   */
  async getUserUsage() {
    this.ensureInitialized();
    
    const apiKey = await this.getApiKey();
    if (!apiKey) {
      throw new Error('No API key found. Please authenticate first.');
    }

    const response = await this.makeRequest('/functions/v1/get-usage', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'apikey': apiKey
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to fetch usage data: ${errorText}`);
    }

    return await response.json();
  }

  /**
   * Get user subscription data
   */
  async getUserSubscription() {
    this.ensureInitialized();
    
    const apiKey = await this.getApiKey();
    if (!apiKey) {
      throw new Error('No API key found. Please authenticate first.');
    }

    const response = await this.makeRequest('/functions/v1/get-subscription', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'apikey': apiKey
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to fetch subscription data: ${errorText}`);
    }

    return await response.json();
  }

  /**
   * Get user profile data
   */
  async getUserProfile() {
    this.ensureInitialized();
    
    try {
      const { data: session } = await this.supabase.auth.getSession();
      
      if (session?.user) {
        return {
          id: session.user.id,
          email: session.user.email,
          fullName: session.user.user_metadata?.full_name,
          avatarUrl: session.user.user_metadata?.avatar_url,
          provider: session.user.app_metadata?.provider
        };
      }
      
      return null;
    } catch (error) {
      console.error('VoiceHype: Failed to get user profile:', error);
      return null;
    }
  }

  // ==================== AUTHENTICATION ====================

  /**
   * Get current Supabase session
   */
  async getSession() {
    this.ensureInitialized();
    
    try {
      const { data: session, error } = await this.supabase.auth.getSession();
      
      if (error) {
        console.error('VoiceHype: Session error:', error);
        return null;
      }
      
      return session;
    } catch (error) {
      console.error('VoiceHype: Failed to get session:', error);
      return null;
    }
  }

  /**
   * Sign out user
   */
  async signOut() {
    this.ensureInitialized();
    
    try {
      const { error } = await this.supabase.auth.signOut();
      
      if (error) {
        console.error('VoiceHype: Sign out error:', error);
        throw error;
      }
      
      // Clear API key
      await this.config.clearApiKey();
      
      console.log('VoiceHype: User signed out successfully');
      return true;
    } catch (error) {
      console.error('VoiceHype: Failed to sign out:', error);
      throw error;
    }
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Make HTTP request to Supabase edge function (same pattern as VS Code extension)
   */
  async makeRequest(endpoint, options = {}) {
    const apiConfig = this.config.getSection('api');
    const baseUrl = apiConfig.supabaseUrl;

    // Clean endpoint (remove leading slash if present)
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint.substring(1) : endpoint;
    const fullUrl = `${baseUrl}/${cleanEndpoint}`;

    // Track request for debugging
    const requestId = `req-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;
    this.activeRequests.set(requestId, {
      url: fullUrl,
      method: options.method || 'POST',
      timestamp: new Date().toISOString()
    });

    try {
      // Prepare headers (same as VS Code extension)
      const headerEntries = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Request-ID': requestId,
        'X-Client-Info': 'voicehype-web-extension',
        'User-Agent': 'VoiceHype-WebExtension/1.0.0',
        ...options.headers
      };

      // Add audio duration header if provided
      if (options.body && typeof options.body === 'string') {
        try {
          const bodyObj = JSON.parse(options.body);
          if (bodyObj?.metadata?.accurateDuration) {
            headerEntries['Audio-Duration-Seconds'] = bodyObj.metadata.accurateDuration.toString();
          }
        } catch (e) {
          // Ignore JSON parse errors
        }
      }

      console.log(`VoiceHype: Making request ${requestId} to ${cleanEndpoint}`, {
        method: options.method || 'POST',
        headers: this.maskSensitiveHeaders(headerEntries),
        bodyLength: options.body ? options.body.length : 0
      });

      const response = await fetch(fullUrl, {
        method: 'POST',
        ...options,
        headers: headerEntries
      });

      this.activeRequests.delete(requestId);

      console.log(`VoiceHype: Request ${requestId} completed with status ${response.status}`);

      // Handle errors (same as VS Code extension)
      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}`;
        try {
          const errorText = await response.text();
          if (errorText) {
            errorMessage = errorText;
          }
        } catch (e) {
          // Ignore error text parsing failures
        }

        if (response.status === 401) {
          throw new Error(`Authentication failed (${response.status}): Your API key may be invalid, disabled, or expired. Please check your API key in the settings.`);
        } else if (response.status === 400) {
          throw new Error(`Request failed (${response.status}): ${errorMessage}`);
        } else {
          throw new Error(`Request failed (${response.status}): ${errorMessage}`);
        }
      }

      return response;
    } catch (error) {
      this.activeRequests.delete(requestId);
      console.error(`VoiceHype: Request ${requestId} failed:`, {
        url: fullUrl,
        endpoint: cleanEndpoint,
        error: error.message
      });

      if (error.cause?.code === 'ENOTFOUND') {
        throw new Error('Could not connect to VoiceHype API. Please check your internet connection.');
      }

      throw error;
    }
  }

  /**
   * Mask sensitive headers for logging
   */
  maskSensitiveHeaders(headers) {
    const masked = { ...headers };

    if (masked.apikey) {
      masked.apikey = `${masked.apikey.substring(0, 5)}...${masked.apikey.substring(masked.apikey.length - 5)}`;
    }

    if (masked.Authorization) {
      const parts = masked.Authorization.split(' ');
      if (parts.length === 2) {
        const token = parts[1];
        masked.Authorization = `${parts[0]} ${token.substring(0, 5)}...${token.substring(token.length - 5)}`;
      }
    }

    return masked;
  }

  /**
   * Get service status
   */
  getStatus() {
    return {
      initialized: this.isInitialized,
      activeRequests: this.activeRequests.size,
      totalRequests: this.requestCount,
      hasApiKey: this.config ? this.config.hasApiKey() : false
    };
  }

  /**
   * Clear all active requests (for cleanup)
   */
  clearActiveRequests() {
    this.activeRequests.clear();
    console.log('VoiceHype: Cleared all active requests');
  }
}

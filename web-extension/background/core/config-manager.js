/**
 * Configuration Manager - Central configuration and settings management
 * <PERSON><PERSON><PERSON><PERSON> rahmanir raheem
 */

import { StorageAdapter } from '../adapters/storage-adapter.js';

export class ConfigManager {
  constructor(browserAdapter) {
    this.browser = browserAdapter;
    this.storage = new StorageAdapter(browserAdapter);
    this.config = null;
    this.listeners = new Set();
    
    // Default configuration
    this.defaultConfig = {
      // VoiceHype API Configuration
      api: {
        supabaseUrl: 'https://nffixzoqnqxpcqpcxpps.supabase.co',
        supabaseAnonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiYW5vbiIsImlzcyI6ImFidWgxMjMxIiwiaWF0IjoxNzQ6Mzg1MjAwLCJleHAiOjE5MDQxNTE2MDB9.EkgKcDa0V0VEmPsXPmyal3YvxYuu9Q1k8OZZv7Gs8_o',
        realtimeUrl: 'ws://***************:3001/realtime',
        authUrl: 'https://voicehype.netlify.app/app',
        endpoints: {
          transcribe: '/functions/v1/transcribe',
          transcribeAndOptimize: '/functions/v1/transcribe-and-optimize',
          validateApiKey: '/functions/v1/validate-api-key',
          getUsage: '/functions/v1/get-usage',
          webExtensionAuth: '/functions/v1/web-extension-auth'
        }
      },
      
      // Transcription Settings
      transcription: {
        service: 'lemonfox', // 'lemonfox', 'assemblyai-best', 'assemblyai-nano'
        model: 'whisper-1',
        language: 'en',
        translate: false,
        realtime: false,
        autoOptimize: false,
        maxDuration: 300, // 5 minutes
        audioFormat: 'webm',
        sampleRate: 16000,
        channels: 1
      },
      
      // Optimization Settings
      optimization: {
        model: 'claude-3-5-sonnet-20241022',
        customPrompt: 'Optimize this transcription for clarity and conciseness:',
        voiceCommandsEnabled: true,
        maxTokens: 4000,
        temperature: 0.1
      },
      
      // UI Settings
      ui: {
        theme: 'auto', // 'light', 'dark', 'auto'
        widgetPosition: 'bottom-right',
        showNotifications: true,
        showBadge: true,
        animationsEnabled: true,
        compactMode: false,
        language: 'en'
      },
      
      // Keyboard Shortcuts
      shortcuts: {
        startRecording: 'Ctrl+Shift+8',
        startWithOptimization: 'Ctrl+Shift+9',
        stopRecording: 'Escape',
        toggleWidget: 'Ctrl+Shift+V'
      },
      
      // Privacy Settings
      privacy: {
        autoSave: true,
        maxHistoryItems: 100,
        clearHistoryOnExit: false,
        anonymousUsage: false,
        shareErrorReports: true
      },
      
      // Performance Settings
      performance: {
        enableRealtime: true,
        bufferSize: 4096,
        compressionEnabled: true,
        batchProcessing: false,
        maxConcurrentRequests: 3
      },
      
      // Debug Settings
      debug: {
        enabled: false,
        logLevel: 'info', // 'debug', 'info', 'warn', 'error'
        logToConsole: true,
        logToStorage: false,
        maxLogEntries: 1000
      }
    };
  }

  // ==================== INITIALIZATION ====================

  /**
   * Initialize configuration manager
   */
  async initialize() {
    try {
      console.log('VoiceHype: Initializing configuration manager...');
      
      // Load user settings
      const userSettings = await this.storage.getUserSettings();
      
      // Merge with defaults
      this.config = this.mergeConfig(this.defaultConfig, userSettings);
      
      // Validate configuration
      this.validateConfig();
      
      console.log('VoiceHype: Configuration manager initialized');
      return this.config;
    } catch (error) {
      console.error('VoiceHype: Failed to initialize configuration:', error);
      // Fallback to default config
      this.config = { ...this.defaultConfig };
      throw error;
    }
  }

  // ==================== CONFIGURATION ACCESS ====================

  /**
   * Get full configuration
   */
  getConfig() {
    return this.config ? { ...this.config } : null;
  }

  /**
   * Get configuration section
   */
  getSection(section) {
    return this.config?.[section] ? { ...this.config[section] } : null;
  }

  /**
   * Get specific configuration value
   */
  get(path) {
    if (!this.config) return null;
    
    const keys = path.split('.');
    let value = this.config;
    
    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key];
      } else {
        return null;
      }
    }
    
    return value;
  }

  /**
   * Set configuration value
   */
  async set(path, value) {
    if (!this.config) {
      throw new Error('Configuration not initialized');
    }
    
    const keys = path.split('.');
    const lastKey = keys.pop();
    let target = this.config;
    
    // Navigate to parent object
    for (const key of keys) {
      if (!target[key] || typeof target[key] !== 'object') {
        target[key] = {};
      }
      target = target[key];
    }
    
    // Set value
    target[lastKey] = value;
    
    // Save to storage
    await this.saveConfig();
    
    // Notify listeners
    this.notifyListeners(path, value);
    
    console.log(`VoiceHype: Configuration updated: ${path} = ${JSON.stringify(value)}`);
  }

  /**
   * Update multiple configuration values
   */
  async update(updates) {
    if (!this.config) {
      throw new Error('Configuration not initialized');
    }
    
    const changes = [];
    
    for (const [path, value] of Object.entries(updates)) {
      const keys = path.split('.');
      const lastKey = keys.pop();
      let target = this.config;
      
      // Navigate to parent object
      for (const key of keys) {
        if (!target[key] || typeof target[key] !== 'object') {
          target[key] = {};
        }
        target = target[key];
      }
      
      // Set value
      target[lastKey] = value;
      changes.push({ path, value });
    }
    
    // Save to storage
    await this.saveConfig();
    
    // Notify listeners
    for (const { path, value } of changes) {
      this.notifyListeners(path, value);
    }
    
    console.log('VoiceHype: Configuration updated:', changes);
  }

  // ==================== API KEY MANAGEMENT ====================

  /**
   * Get API key
   */
  async getApiKey() {
    return await this.storage.getApiKey();
  }

  /**
   * Set API key
   */
  async setApiKey(apiKey) {
    await this.storage.storeApiKey(apiKey);
    console.log('VoiceHype: API key updated');
  }

  /**
   * Clear API key
   */
  async clearApiKey() {
    await this.storage.clearApiKey();
    console.log('VoiceHype: API key cleared');
  }

  /**
   * Check if API key is configured
   */
  async hasApiKey() {
    const apiKey = await this.getApiKey();
    return !!apiKey;
  }

  // ==================== SHORTCUTS MANAGEMENT ====================

  /**
   * Get keyboard shortcuts
   */
  getShortcuts() {
    return this.get('shortcuts') || {};
  }

  /**
   * Update keyboard shortcut
   */
  async updateShortcut(action, shortcut) {
    await this.set(`shortcuts.${action}`, shortcut);
  }

  /**
   * Reset shortcuts to defaults
   */
  async resetShortcuts() {
    await this.set('shortcuts', { ...this.defaultConfig.shortcuts });
  }

  // ==================== THEME MANAGEMENT ====================

  /**
   * Get current theme
   */
  getTheme() {
    const theme = this.get('ui.theme') || 'auto';
    
    if (theme === 'auto') {
      // Detect system theme
      return window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches 
        ? 'dark' : 'light';
    }
    
    return theme;
  }

  /**
   * Set theme
   */
  async setTheme(theme) {
    await this.set('ui.theme', theme);
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Reset configuration to defaults
   */
  async resetToDefaults() {
    this.config = { ...this.defaultConfig };
    await this.saveConfig();
    this.notifyListeners('*', this.config);
    console.log('VoiceHype: Configuration reset to defaults');
  }

  /**
   * Save configuration to storage
   */
  async saveConfig() {
    if (!this.config) return;
    
    try {
      await this.storage.saveUserSettings(this.config);
    } catch (error) {
      console.error('VoiceHype: Failed to save configuration:', error);
      throw error;
    }
  }

  /**
   * Merge configuration objects
   */
  mergeConfig(defaultConfig, userConfig) {
    const merged = { ...defaultConfig };
    
    if (!userConfig || typeof userConfig !== 'object') {
      return merged;
    }
    
    for (const [key, value] of Object.entries(userConfig)) {
      if (value !== null && typeof value === 'object' && !Array.isArray(value)) {
        merged[key] = this.mergeConfig(merged[key] || {}, value);
      } else {
        merged[key] = value;
      }
    }
    
    return merged;
  }

  /**
   * Validate configuration
   */
  validateConfig() {
    if (!this.config) {
      throw new Error('Configuration is null');
    }
    
    // Validate required sections
    const requiredSections = ['api', 'transcription', 'optimization', 'ui'];
    for (const section of requiredSections) {
      if (!this.config[section]) {
        console.warn(`VoiceHype: Missing configuration section: ${section}`);
        this.config[section] = this.defaultConfig[section] || {};
      }
    }
    
    // Validate API configuration
    if (!this.config.api.supabaseUrl) {
      throw new Error('Supabase URL is required');
    }
    
    console.log('VoiceHype: Configuration validation passed');
  }

  // ==================== EVENT LISTENERS ====================

  /**
   * Add configuration change listener
   */
  addListener(callback) {
    if (typeof callback === 'function') {
      this.listeners.add(callback);
    }
  }

  /**
   * Remove configuration change listener
   */
  removeListener(callback) {
    this.listeners.delete(callback);
  }

  /**
   * Notify all listeners of configuration changes
   */
  notifyListeners(path, value) {
    for (const listener of this.listeners) {
      try {
        listener(path, value, this.config);
      } catch (error) {
        console.error('VoiceHype: Configuration listener error:', error);
      }
    }
  }

  /**
   * Clear all listeners
   */
  clearListeners() {
    this.listeners.clear();
  }
}

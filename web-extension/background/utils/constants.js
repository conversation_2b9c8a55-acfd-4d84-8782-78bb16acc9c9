/**
 * Constants - Application-wide constants for VoiceHype extension
 * Bismilla<PERSON> rahmanir raheem
 */

export const Constants = {
  // Extension Information
  VERSION: '1.0.0',
  NAME: 'VoiceHype',
  DESCRIPTION: 'Voice-to-prompt productivity tool for web browsers',
  
  // API Configuration
  API: {
    SUPABASE_URL: 'https://nffixzoqnqxpcqpcxpps.supabase.co',
    SUPABASE_ANON_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiYW5vbiIsImlzcyI6ImFidWgxMjMxIiwiaWF0IjoxNzQ6Mzg1MjAwLCJleHAiOjE5MDQxNTE2MDB9.EkgKcDa0V0VEmPsXPmyal3YvxYuu9Q1k8OZZv7Gs8_o',
    REALTIME_URL: 'ws://***************:3001/realtime',
    AUTH_URL: 'https://voicehype.netlify.app/app',
    API_KEY_PREFIX: 'vhkey_'
  },
  
  // Storage Keys
  STORAGE: {
    API_KEY: 'vh_api_key',
    API_KEY_HASH: 'vh_api_key_hash',
    USER_SETTINGS: 'vh_user_settings',
    RECORDING_HISTORY: 'vh_recording_history',
    USAGE_STATS: 'vh_usage_stats',
    AUTH_STATE: 'vh_auth_state',
    LAST_SYNC: 'vh_last_sync',
    WIDGET_POSITIONS: 'vh_widget_positions',
    SHORTCUTS: 'vh_shortcuts',
    THEME: 'vh_theme'
  },
  
  // Audio Configuration
  AUDIO: {
    MAX_DURATION: 300, // 5 minutes in seconds
    SAMPLE_RATE: 16000,
    CHANNELS: 1,
    FORMAT: 'webm',
    ECHO_CANCELLATION: true,
    NOISE_SUPPRESSION: true,
    AUTO_GAIN_CONTROL: true,
    REALTIME_TIMESLICE: 100, // 100ms chunks for real-time
    REGULAR_TIMESLICE: 1000, // 1s chunks for regular recording
    AUDIO_BITS_PER_SECOND: 128000, // 128 kbps
    MIME_TYPES: [
      'audio/webm;codecs=opus',
      'audio/webm',
      'audio/mp4',
      'audio/ogg;codecs=opus',
      'audio/ogg',
      'audio/wav'
    ]
  },
  
  // Transcription Services
  TRANSCRIPTION: {
    SERVICES: {
      LEMONFOX: 'lemonfox',
      ASSEMBLYAI_BEST: 'assemblyai-best',
      ASSEMBLYAI_NANO: 'assemblyai-nano',
      ASSEMBLYAI_REALTIME: 'assemblyai-realtime'
    },
    MODELS: {
      WHISPER_1: 'whisper-1',
      WHISPER_LARGE: 'whisper-large-v3'
    },
    // File size limits for transcription services (in MB)
    SIZE_LIMITS: {
      LEMONFOX: 25,        // Whisper limit: 25MB
      WHISPER: 25,         // Whisper limit: 25MB
      ASSEMBLYAI: 100,     // Assembly AI limit: 100MB
      'assemblyai-best': 100,
      'assemblyai-nano': 100,
      'assemblyai-realtime': 100
    },
    // Safe slice sizes with margin (in MB)
    SLICE_LIMITS: {
      LEMONFOX: 20,        // 20MB per slice (5MB safety margin)
      WHISPER: 20,         // 20MB per slice (5MB safety margin)
      ASSEMBLYAI: 80,      // 80MB per slice (20MB safety margin)
      'assemblyai-best': 80,
      'assemblyai-nano': 80,
      'assemblyai-realtime': 80
    },
    LANGUAGES: {
      ENGLISH: 'en',
      SPANISH: 'es',
      FRENCH: 'fr',
      GERMAN: 'de',
      ITALIAN: 'it',
      PORTUGUESE: 'pt',
      RUSSIAN: 'ru',
      JAPANESE: 'ja',
      KOREAN: 'ko',
      CHINESE: 'zh'
    }
  },
  
  // Optimization Models
  OPTIMIZATION: {
    MODELS: {
      CLAUDE_SONNET: 'claude-3-5-sonnet-20241022',
      CLAUDE_HAIKU: 'claude-3-haiku-20240307',
      LLAMA_70B: 'meta-llama/llama-3.1-70b-instruct',
      LLAMA_8B: 'meta-llama/llama-3.1-8b-instruct',
      DEEPSEEK_V3: 'deepseek-chat'
    },
    MAX_TOKENS: 4000,
    TEMPERATURE: 0.1
  },
  
  // UI Configuration
  UI: {
    THEMES: {
      LIGHT: 'light',
      DARK: 'dark',
      AUTO: 'auto'
    },
    WIDGET_POSITIONS: {
      TOP_LEFT: 'top-left',
      TOP_RIGHT: 'top-right',
      BOTTOM_LEFT: 'bottom-left',
      BOTTOM_RIGHT: 'bottom-right',
      CENTER: 'center'
    },
    NOTIFICATION_DURATION: 5000, // 5 seconds
    ANIMATION_DURATION: 300 // 300ms
  },
  
  // Keyboard Shortcuts
  SHORTCUTS: {
    START_RECORDING: 'Ctrl+Shift+8',
    START_WITH_OPTIMIZATION: 'Ctrl+Shift+9',
    STOP_RECORDING: 'Escape',
    TOGGLE_WIDGET: 'Ctrl+Shift+V'
  },
  
  // Performance Limits
  LIMITS: {
    MAX_HISTORY_ITEMS: 100,
    MAX_CONCURRENT_REQUESTS: 3,
    MAX_LOG_ENTRIES: 1000,
    BUFFER_SIZE: 4096,
    REQUEST_TIMEOUT: 30000 // 30 seconds
  },
  
  // Error Messages
  ERRORS: {
    NO_API_KEY: 'No API key found. Please authenticate first.',
    INVALID_API_KEY: 'Invalid VoiceHype API key format.',
    TRANSCRIPTION_FAILED: 'Transcription failed. Please try again.',
    OPTIMIZATION_FAILED: 'Text optimization failed. Please try again.',
    NETWORK_ERROR: 'Network error. Please check your connection.',
    PERMISSION_DENIED: 'Microphone permission denied.',
    BROWSER_NOT_SUPPORTED: 'Browser not supported.',
    SERVICE_UNAVAILABLE: 'VoiceHype service is temporarily unavailable.'
  },
  
  // Success Messages
  SUCCESS: {
    TRANSCRIPTION_COMPLETE: 'Transcription completed successfully!',
    OPTIMIZATION_COMPLETE: 'Text optimization completed!',
    SETTINGS_SAVED: 'Settings saved successfully!',
    API_KEY_SAVED: 'API key saved securely!',
    SIGNED_OUT: 'Signed out successfully!'
  },
  
  // Regular Expressions
  REGEX: {
    API_KEY: /^vhkey_[a-zA-Z0-9]{32,}$/,
    EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    URL: /^https?:\/\/.+/
  },
  
  // Browser Detection
  BROWSERS: {
    CHROME: 'chrome',
    FIREFOX: 'firefox',
    EDGE: 'edge',
    SAFARI: 'safari'
  },
  
  // Manifest Versions
  MANIFEST: {
    V2: 2,
    V3: 3
  },
  
  // Content Script Selectors
  SELECTORS: {
    TEXT_INPUTS: 'input[type="text"], input[type="email"], input[type="search"], textarea, [contenteditable="true"]',
    EDITABLE_ELEMENTS: '[contenteditable="true"], textarea, input[type="text"]',
    FORM_ELEMENTS: 'form, .form, [role="form"]'
  },
  
  // Widget Configuration
  WIDGET: {
    WIDTH: 300,
    HEIGHT: 200,
    MIN_WIDTH: 250,
    MIN_HEIGHT: 150,
    MAX_WIDTH: 500,
    MAX_HEIGHT: 400,
    BORDER_RADIUS: 8,
    SHADOW: '0 4px 12px rgba(0, 0, 0, 0.15)',
    Z_INDEX: 999999
  },
  
  // Animation Easing
  EASING: {
    EASE_IN: 'cubic-bezier(0.4, 0, 1, 1)',
    EASE_OUT: 'cubic-bezier(0, 0, 0.2, 1)',
    EASE_IN_OUT: 'cubic-bezier(0.4, 0, 0.2, 1)'
  }
};

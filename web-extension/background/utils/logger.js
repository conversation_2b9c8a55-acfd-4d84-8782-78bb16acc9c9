/**
 * Logger Utility - Centralized logging for VoiceHype extension
 * Bismilla<PERSON> rahmanir raheem
 */

export class Logger {
  constructor(context = 'VoiceHype') {
    this.context = context;
    this.logLevel = this.getLogLevel();
    this.logLevels = {
      debug: 0,
      info: 1,
      warn: 2,
      error: 3
    };
  }

  /**
   * Get current log level from configuration
   */
  getLogLevel() {
    // Default to 'info' level
    return 'info';
  }

  /**
   * Check if message should be logged based on level
   */
  shouldLog(level) {
    return this.logLevels[level] >= this.logLevels[this.logLevel];
  }

  /**
   * Format log message with timestamp and context
   */
  formatMessage(level, message, ...args) {
    const timestamp = new Date().toISOString();
    const prefix = `[${timestamp}] [${this.context}] [${level.toUpperCase()}]`;
    
    if (args.length > 0) {
      return [prefix, message, ...args];
    }
    return [prefix, message];
  }

  /**
   * Debug level logging
   */
  debug(message, ...args) {
    if (this.shouldLog('debug')) {
      console.debug(...this.formatMessage('debug', message, ...args));
    }
  }

  /**
   * Info level logging
   */
  info(message, ...args) {
    if (this.shouldLog('info')) {
      console.info(...this.formatMessage('info', message, ...args));
    }
  }

  /**
   * Warning level logging
   */
  warn(message, ...args) {
    if (this.shouldLog('warn')) {
      console.warn(...this.formatMessage('warn', message, ...args));
    }
  }

  /**
   * Error level logging
   */
  error(message, ...args) {
    if (this.shouldLog('error')) {
      console.error(...this.formatMessage('error', message, ...args));
    }
  }

  /**
   * Log with custom level
   */
  log(level, message, ...args) {
    if (this.shouldLog(level)) {
      const method = console[level] || console.log;
      method(...this.formatMessage(level, message, ...args));
    }
  }
}

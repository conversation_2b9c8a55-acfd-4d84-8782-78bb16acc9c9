/**
 * VoiceHype Web Extension - Background Script Entry Point
 * <PERSON><PERSON><PERSON><PERSON> rahmanir raheem
 * 
 * Main service worker that coordinates all extension functionality
 * Universal backend that works across Chrome, Firefox, and Edge
 */

// Import core services
import { BrowserAdapter } from './adapters/browser-adapter.js';
import { StorageAdapter } from './adapters/storage-adapter.js';
import { MessagingAdapter } from './adapters/messaging-adapter.js';
import { ConfigManager } from './core/config-manager.js';
import { SupabaseService } from './core/supabase-service.js';

// Import authentication services
import { SecureStorage } from './services/secure-storage.js';
import { AuthService } from './services/auth-service.js';
import { WebExtensionAuth } from './services/web-extension-auth.js';

// Import recording services
import { RecordingService } from './services/recording-service.js';

// Import utilities
import { Logger } from './utils/logger.js';
import { Constants } from './utils/constants.js';

class VoiceHypeBackground {
  constructor() {
    this.isInitialized = false;
    this.services = {};
    this.logger = new Logger('Background');
    
    // Service initialization order matters
    this.initializationOrder = [
      'browserAdapter',
      'storageAdapter',
      'configManager',
      'secureStorage',
      'supabaseService',
      'authService',
      'webExtensionAuth',
      'recordingService',
      'messagingAdapter'
    ];
  }

  // ==================== INITIALIZATION ====================

  /**
   * Initialize all services in correct order
   */
  async initialize() {
    try {
      this.logger.info('VoiceHype: Starting background script initialization...');
      
      // Initialize browser adapter first
      this.services.browserAdapter = new BrowserAdapter();
      this.logger.info(`Browser detected: ${this.services.browserAdapter.browserInfo.name}`);
      
      // Initialize storage adapter
      this.services.storageAdapter = new StorageAdapter(this.services.browserAdapter);
      
      // Initialize configuration manager
      this.services.configManager = new ConfigManager(this.services.browserAdapter);
      await this.services.configManager.initialize();

      // Initialize secure storage
      this.services.secureStorage = new SecureStorage(this.services.storageAdapter, this.services.browserAdapter);
      await this.services.secureStorage.initialize();

      // Initialize Supabase service with secure storage
      this.services.supabaseService = new SupabaseService(this.services.configManager, this.services.secureStorage);
      await this.services.supabaseService.initialize();

      // Initialize authentication service
      this.services.authService = new AuthService(
        this.services.configManager,
        this.services.secureStorage,
        this.services.browserAdapter,
        null // messaging adapter not ready yet
      );
      await this.services.authService.initialize();

      // Initialize web extension auth
      this.services.webExtensionAuth = new WebExtensionAuth(
        this.services.authService,
        this.services.browserAdapter,
        null // messaging adapter not ready yet
      );

      // Initialize recording service
      this.services.recordingService = new RecordingService(
        this.services.configManager,
        this.services.secureStorage,
        this.services.supabaseService,
        null, // messaging adapter not ready yet
        this.services.browserAdapter
      );
      
      // Initialize messaging adapter (must be last)
      this.services.messagingAdapter = new MessagingAdapter(this.services.browserAdapter);

      // Update services with messaging adapter
      this.services.authService.messaging = this.services.messagingAdapter;
      this.services.webExtensionAuth.messaging = this.services.messagingAdapter;
      this.services.recordingService.messaging = this.services.messagingAdapter;

      this.setupMessageHandlers();
      
      // Setup event listeners
      this.setupEventListeners();
      
      // Setup keyboard commands
      this.setupKeyboardCommands();
      
      // Setup context menus
      this.setupContextMenus();
      
      this.isInitialized = true;
      this.logger.info('VoiceHype: Background script initialized successfully');
      
      // Show initialization notification
      await this.services.browserAdapter.showNotification({
        message: 'VoiceHype extension loaded successfully!',
        type: 'basic'
      });
      
    } catch (error) {
      this.logger.error('VoiceHype: Background script initialization failed:', error);
      throw error;
    }
  }

  // ==================== MESSAGE HANDLERS ====================

  /**
   * Setup all message handlers
   */
  setupMessageHandlers() {
    const { messagingAdapter, supabaseService, configManager, storageAdapter, webExtensionAuth, recordingService } = this.services;
    const { MESSAGE_TYPES } = MessagingAdapter;

    // Authentication handlers (updated to use new auth services)
    messagingAdapter.registerHandler(MESSAGE_TYPES.AUTH_CHECK, async () => {
      return await webExtensionAuth.checkAuthStatus();
    });

    messagingAdapter.registerHandler(MESSAGE_TYPES.AUTH_LOGIN, async (data) => {
      const { provider = 'google' } = data || {};
      return await webExtensionAuth.authenticate(provider);
    });

    messagingAdapter.registerHandler(MESSAGE_TYPES.AUTH_GET_USER, async () => {
      const authStatus = await webExtensionAuth.checkAuthStatus();
      if (!authStatus.authenticated) {
        return { profile: null, usage: null, subscription: null };
      }

      const [usage, subscription] = await Promise.all([
        supabaseService.getUserUsage().catch(() => null),
        supabaseService.getUserSubscription().catch(() => null)
      ]);

      return {
        profile: authStatus.user,
        usage,
        subscription
      };
    });

    messagingAdapter.registerHandler(MESSAGE_TYPES.AUTH_LOGOUT, async () => {
      return await webExtensionAuth.signOut();
    });

    // Recording handlers
    messagingAdapter.registerHandler(MESSAGE_TYPES.RECORDING_START, async (data) => {
      const { realtime = false, service, model, language } = data || {};
      return await recordingService.startRecording({
        realtime,
        service,
        model,
        language
      });
    });

    messagingAdapter.registerHandler(MESSAGE_TYPES.RECORDING_STOP, async () => {
      return await recordingService.stopRecording();
    });

    messagingAdapter.registerHandler(MESSAGE_TYPES.RECORDING_PAUSE, async () => {
      return await recordingService.pauseRecording();
    });

    messagingAdapter.registerHandler(MESSAGE_TYPES.RECORDING_RESUME, async () => {
      return await recordingService.resumeRecording();
    });

    messagingAdapter.registerHandler(MESSAGE_TYPES.RECORDING_STATUS, async () => {
      return recordingService.getRecordingStatus();
    });

    messagingAdapter.registerHandler(MESSAGE_TYPES.RECORDING_GET_CURRENT, async () => {
      return recordingService.getCurrentRecording();
    });

    messagingAdapter.registerHandler(MESSAGE_TYPES.RECORDING_CLEAR_CURRENT, async () => {
      recordingService.clearCurrentRecording();
      return { success: true };
    });

    messagingAdapter.registerHandler(MESSAGE_TYPES.RECORDING_CHECK_MIC, async () => {
      return await recordingService.checkMicrophoneAvailability();
    });

    // Manual transcription with slicing support
    messagingAdapter.registerHandler(MESSAGE_TYPES.TRANSCRIBE_AUDIO, async (data) => {
      const { base64Audio, service, model, language, translate } = data || {};
      return await recordingService.transcribeAudio(base64Audio, {
        service,
        model,
        language,
        translate
      });
    });

    // Transcription handlers
    messagingAdapter.registerHandler(MESSAGE_TYPES.TRANSCRIBE_AUDIO, async (data) => {
      const { base64Audio, options = {} } = data;
      
      if (options.optimize) {
        return await supabaseService.transcribeAndOptimize(base64Audio, options);
      } else {
        return await supabaseService.transcribeAudio(base64Audio, options);
      }
    });

    messagingAdapter.registerHandler(MESSAGE_TYPES.OPTIMIZE_TEXT, async (data) => {
      const { text, options = {} } = data;
      return await supabaseService.optimizeText(text, options);
    });

    // Settings handlers
    messagingAdapter.registerHandler(MESSAGE_TYPES.SETTINGS_GET, async () => {
      return configManager.getConfig();
    });

    messagingAdapter.registerHandler(MESSAGE_TYPES.SETTINGS_UPDATE, async (data) => {
      await configManager.update(data);
      return configManager.getConfig();
    });

    messagingAdapter.registerHandler(MESSAGE_TYPES.SETTINGS_RESET, async () => {
      await configManager.resetToDefaults();
      return configManager.getConfig();
    });

    // History handlers
    messagingAdapter.registerHandler(MESSAGE_TYPES.HISTORY_GET, async (data) => {
      const { limit } = data || {};
      return await storageAdapter.getHistory(limit);
    });

    messagingAdapter.registerHandler(MESSAGE_TYPES.HISTORY_ADD, async (data) => {
      return await storageAdapter.addToHistory(data);
    });

    messagingAdapter.registerHandler(MESSAGE_TYPES.HISTORY_CLEAR, async () => {
      await storageAdapter.clearHistory();
      return { success: true };
    });

    // Usage handlers
    messagingAdapter.registerHandler(MESSAGE_TYPES.USAGE_GET, async () => {
      const [localStats, remoteUsage] = await Promise.all([
        storageAdapter.getUsageStats(),
        supabaseService.getUserUsage().catch(() => null)
      ]);
      
      return {
        local: localStats,
        remote: remoteUsage
      };
    });

    messagingAdapter.registerHandler(MESSAGE_TYPES.USAGE_UPDATE, async (data) => {
      return await storageAdapter.updateUsageStats(data);
    });

    // System handlers
    messagingAdapter.registerHandler(MESSAGE_TYPES.SYSTEM_STATUS, async () => {
      return {
        initialized: this.isInitialized,
        services: Object.keys(this.services),
        browser: this.services.browserAdapter.browserInfo,
        supabase: this.services.supabaseService.getStatus(),
        config: !!configManager.getConfig()
      };
    });

    messagingAdapter.registerHandler(MESSAGE_TYPES.SYSTEM_PING, async () => {
      return { pong: true, timestamp: new Date().toISOString() };
    });

    // UI handlers
    messagingAdapter.registerHandler(MESSAGE_TYPES.UI_OPEN_OPTIONS, async () => {
      await this.services.browserAdapter.openOptionsPage();
      return { success: true };
    });

    // Content Script handlers
    messagingAdapter.registerHandler('CONTENT_SCRIPT_READY', async (data, sender) => {
      this.logger.info(`Content script ready on: ${data.url}`);
      return { success: true };
    });

    messagingAdapter.registerHandler('GET_SETTINGS', async () => {
      return {
        success: true,
        settings: configManager.getConfig()
      };
    });

    messagingAdapter.registerHandler('START_FIELD_RECORDING', async (data, sender) => {
      const { fieldId, fieldInfo } = data;
      this.logger.info(`Starting field recording for: ${fieldId}`);

      try {
        // Start recording with field context
        const result = await recordingService.startRecording({
          realtime: false,
          fieldId,
          fieldInfo,
          tabId: sender.tab?.id
        });

        return {
          success: true,
          recordingId: result.recordingId
        };
      } catch (error) {
        this.logger.error('Error starting field recording:', error);
        return {
          success: false,
          error: error.message
        };
      }
    });

    messagingAdapter.registerHandler('STOP_FIELD_RECORDING', async (data, sender) => {
      const { fieldId } = data;
      this.logger.info(`Stopping field recording for: ${fieldId}`);

      try {
        const result = await recordingService.stopRecording();

        if (result.transcript) {
          // Send transcript back to content script
          await this.services.browserAdapter.sendMessageToTab(sender.tab.id, {
            type: 'INSERT_TRANSCRIPT',
            fieldId,
            transcript: result.transcript
          });
        }

        return {
          success: true,
          transcript: result.transcript
        };
      } catch (error) {
        this.logger.error('Error stopping field recording:', error);
        return {
          success: false,
          error: error.message
        };
      }
    });

    messagingAdapter.registerHandler('TOGGLE_RECORDING', async (data, sender) => {
      try {
        const status = recordingService.getRecordingStatus();

        if (status.isRecording) {
          const result = await recordingService.stopRecording();
          return {
            success: true,
            action: 'stopped',
            transcript: result.transcript
          };
        } else {
          const result = await recordingService.startRecording({
            realtime: false,
            tabId: sender.tab?.id
          });
          return {
            success: true,
            action: 'started',
            recordingId: result.recordingId
          };
        }
      } catch (error) {
        this.logger.error('Error toggling recording:', error);
        return {
          success: false,
          error: error.message
        };
      }
    });

    // Authentication credential storage handler
    messagingAdapter.registerHandler('STORE_AUTH_CREDENTIALS', async (data, sender) => {
      try {
        this.logger.info('Storing authentication credentials...');

        // Store credentials using auth service
        await this.services.authService.storeUserData(data.user);
        await this.services.authService.storeApiKey(data.apiKey);

        if (data.session) {
          await this.services.authService.storeSessionData(data.session);
        }

        this.logger.info('Authentication credentials stored successfully');

        return {
          success: true,
          message: 'Credentials stored successfully'
        };

      } catch (error) {
        this.logger.error('Error storing auth credentials:', error);
        return {
          success: false,
          error: error.message
        };
      }
    });

    // Auth completion handler
    messagingAdapter.registerHandler('AUTH_COMPLETE', async (data, sender) => {
      this.logger.info('Authentication completed for user:', data.user.email);

      // Update extension badge or icon to show authenticated state
      try {
        await this.services.browserAdapter.setBadgeText('✓');
        await this.services.browserAdapter.setBadgeBackgroundColor('#28a745');
      } catch (error) {
        this.logger.warn('Could not update badge:', error);
      }

      return { success: true };
    });

    // Close auth window handler
    messagingAdapter.registerHandler('CLOSE_AUTH_WINDOW', async (data, sender) => {
      try {
        if (sender.tab) {
          await this.services.browserAdapter.closeTab(sender.tab.id);
        }
        return { success: true };
      } catch (error) {
        this.logger.error('Error closing auth window:', error);
        return { success: false, error: error.message };
      }
    });

    // Start authentication handler
    messagingAdapter.registerHandler('START_AUTHENTICATION', async (data, sender) => {
      try {
        this.logger.info(`Starting authentication with ${data.provider}...`);

        const result = await this.services.webExtensionAuth.authenticate(data.provider);

        if (result.success) {
          // Notify popup of successful authentication
          await messagingAdapter.broadcast('AUTH_COMPLETED', {
            user: result.user,
            timestamp: new Date().toISOString()
          });
        }

        return result;

      } catch (error) {
        this.logger.error('Authentication failed:', error);

        // Notify popup of failed authentication
        await messagingAdapter.broadcast('AUTH_FAILED', {
          error: error.message,
          timestamp: new Date().toISOString()
        });

        return {
          success: false,
          error: error.message
        };
      }
    });

    // Sign out handler
    messagingAdapter.registerHandler('SIGN_OUT', async (data, sender) => {
      try {
        this.logger.info('Processing sign out request...');

        const result = await this.services.webExtensionAuth.signOut();

        // Update extension badge
        await this.services.browserAdapter.setBadgeText('');

        return result;

      } catch (error) {
        this.logger.error('Sign out failed:', error);
        return {
          success: false,
          error: error.message
        };
      }
    });

    // Get user data handler
    messagingAdapter.registerHandler('GET_USER_DATA', async (data, sender) => {
      try {
        const authStatus = await this.services.authService.getAuthStatus();

        if (authStatus.authenticated) {
          // Get additional user data
          const subscription = await this.services.authService.getSubscriptionData();
          const credits = await this.services.authService.getCreditsData();
          const usage = await this.services.authService.getUsageData();
          const settings = await this.services.authService.getSettings();

          return {
            success: true,
            user: authStatus.user,
            subscription,
            credits,
            usage,
            settings
          };
        } else {
          return {
            success: false,
            authenticated: false
          };
        }

      } catch (error) {
        this.logger.error('Error getting user data:', error);
        return {
          success: false,
          error: error.message
        };
      }
    });

    this.logger.info('VoiceHype: Message handlers registered');
  }

  // ==================== EVENT LISTENERS ====================

  /**
   * Setup browser event listeners
   */
  setupEventListeners() {
    const { browserAdapter } = this.services;

    // Extension installation/update
    if (browserAdapter.api.runtime.onInstalled) {
      browserAdapter.api.runtime.onInstalled.addListener(async (details) => {
        this.logger.info('VoiceHype: Extension installed/updated:', details.reason);
        
        if (details.reason === 'install') {
          // First time installation
          await this.handleFirstInstall();
        } else if (details.reason === 'update') {
          // Extension update
          await this.handleUpdate(details.previousVersion);
        }
      });
    }

    // Extension startup
    if (browserAdapter.api.runtime.onStartup) {
      browserAdapter.api.runtime.onStartup.addListener(async () => {
        this.logger.info('VoiceHype: Extension startup');
        await this.handleStartup();
      });
    }

    // Tab updates (for content script injection)
    if (browserAdapter.api.tabs && browserAdapter.api.tabs.onUpdated) {
      browserAdapter.api.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
        if (changeInfo.status === 'complete' && tab.url && !tab.url.startsWith('chrome://')) {
          // Tab loaded, content script should be ready
          this.logger.debug(`VoiceHype: Tab ${tabId} loaded: ${tab.url}`);
        }
      });
    }

    this.logger.info('VoiceHype: Event listeners setup complete');
  }

  // ==================== KEYBOARD COMMANDS ====================

  /**
   * Setup keyboard command handlers
   */
  setupKeyboardCommands() {
    const { browserAdapter, messagingAdapter } = this.services;

    browserAdapter.onCommand(async (command) => {
      this.logger.info(`VoiceHype: Keyboard command triggered: ${command}`);
      
      try {
        switch (command) {
          case 'toggle-recording':
            // Send message to active tab's content script
            const activeTab = await browserAdapter.getActiveTab();
            if (activeTab) {
              await browserAdapter.sendMessageToTab(activeTab.id, {
                type: 'TOGGLE_RECORDING'
              });
            }
            break;

          default:
            this.logger.warn(`VoiceHype: Unknown command: ${command}`);
        }
      } catch (error) {
        this.logger.error(`VoiceHype: Command handler error for ${command}:`, error);
      }
    });

    this.logger.info('VoiceHype: Keyboard commands setup complete');
  }

  // ==================== CONTEXT MENUS ====================

  /**
   * Setup context menu items
   */
  setupContextMenus() {
    const { browserAdapter, messagingAdapter } = this.services;

    // Create context menu items
    browserAdapter.createContextMenu({
      id: 'voicehype-transcribe',
      title: 'VoiceHype: Start Recording',
      contexts: ['editable']
    });

    browserAdapter.createContextMenu({
      id: 'voicehype-transcribe-optimize',
      title: 'VoiceHype: Record & Optimize',
      contexts: ['editable']
    });

    browserAdapter.createContextMenu({
      id: 'voicehype-separator',
      type: 'separator',
      contexts: ['editable']
    });

    browserAdapter.createContextMenu({
      id: 'voicehype-options',
      title: 'VoiceHype: Settings',
      contexts: ['editable']
    });

    // Handle context menu clicks
    browserAdapter.onContextMenuClick(async (info, tab) => {
      this.logger.info(`VoiceHype: Context menu clicked: ${info.menuItemId}`);
      
      try {
        switch (info.menuItemId) {
          case 'voicehype-transcribe':
            await messagingAdapter.sendToContent(
              MessagingAdapter.MESSAGE_TYPES.RECORDING_START,
              { optimize: false },
              tab.id
            );
            break;
            
          case 'voicehype-transcribe-optimize':
            await messagingAdapter.sendToContent(
              MessagingAdapter.MESSAGE_TYPES.RECORDING_START,
              { optimize: true },
              tab.id
            );
            break;
            
          case 'voicehype-options':
            await browserAdapter.openOptionsPage();
            break;
        }
      } catch (error) {
        this.logger.error(`VoiceHype: Context menu handler error:`, error);
      }
    });

    this.logger.info('VoiceHype: Context menus setup complete');
  }

  // ==================== LIFECYCLE HANDLERS ====================

  /**
   * Handle first installation
   */
  async handleFirstInstall() {
    try {
      this.logger.info('VoiceHype: Handling first installation');
      
      // Show welcome notification
      await this.services.browserAdapter.showNotification({
        message: 'Welcome to VoiceHype! Click to get started.',
        type: 'basic'
      });
      
      // Open options page for setup
      setTimeout(() => {
        this.services.browserAdapter.openOptionsPage();
      }, 2000);
      
    } catch (error) {
      this.logger.error('VoiceHype: First install handler error:', error);
    }
  }

  /**
   * Handle extension update
   */
  async handleUpdate(previousVersion) {
    try {
      this.logger.info(`VoiceHype: Updated from version ${previousVersion}`);
      
      // Show update notification
      await this.services.browserAdapter.showNotification({
        message: `VoiceHype updated to v${Constants.VERSION}`,
        type: 'basic'
      });
      
    } catch (error) {
      this.logger.error('VoiceHype: Update handler error:', error);
    }
  }

  /**
   * Handle extension startup
   */
  async handleStartup() {
    try {
      this.logger.info('VoiceHype: Handling startup');
      
      // Validate API key on startup
      const hasApiKey = await this.services.configManager.hasApiKey();
      if (hasApiKey) {
        const isValid = await this.services.supabaseService.validateApiKey();
        if (!isValid) {
          this.logger.warn('VoiceHype: Invalid API key detected on startup');
          await this.services.configManager.clearApiKey();
        }
      }
      
    } catch (error) {
      this.logger.error('VoiceHype: Startup handler error:', error);
    }
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Get service by name
   */
  getService(name) {
    return this.services[name];
  }

  /**
   * Check if extension is ready
   */
  isReady() {
    return this.isInitialized && Object.keys(this.services).length > 0;
  }

  /**
   * Cleanup resources
   */
  async cleanup() {
    try {
      this.logger.info('VoiceHype: Cleaning up background script');
      
      // Clear message handlers
      if (this.services.messagingAdapter) {
        this.services.messagingAdapter.clearAllHandlers();
      }
      
      // Clear active requests
      if (this.services.supabaseService) {
        this.services.supabaseService.clearActiveRequests();
      }
      
      this.isInitialized = false;
      this.logger.info('VoiceHype: Cleanup complete');
      
    } catch (error) {
      this.logger.error('VoiceHype: Cleanup error:', error);
    }
  }
}

// ==================== INITIALIZATION ====================

// Create and initialize the background service
const voiceHypeBackground = new VoiceHypeBackground();

// Initialize when script loads
voiceHypeBackground.initialize().catch(error => {
  console.error('VoiceHype: Fatal initialization error:', error);
});

// Export for testing/debugging
if (typeof globalThis !== 'undefined') {
  globalThis.voiceHypeBackground = voiceHypeBackground;
}

// Handle service worker termination (Chrome)
if (typeof self !== 'undefined' && self.addEventListener) {
  self.addEventListener('beforeunload', () => {
    voiceHypeBackground.cleanup();
  });
}

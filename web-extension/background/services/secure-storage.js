/**
 * Secure Storage Service - Enhanced security for sensitive data
 * <PERSON><PERSON><PERSON><PERSON> rahmanir raheem
 * 
 * Provides additional security layers beyond the basic StorageAdapter
 * Following the same security patterns as the VS Code extension
 */

export class SecureStorage {
  constructor(storageAdapter, browserAdapter) {
    this.storage = storageAdapter;
    this.browser = browserAdapter;
    this.encryptionKey = null;
    this.isInitialized = false;
    
    // Security constants
    this.SALT_LENGTH = 32;
    this.IV_LENGTH = 16;
    this.KEY_DERIVATION_ITERATIONS = 100000;
    this.ENCRYPTION_ALGORITHM = 'AES-GCM';
  }

  // ==================== INITIALIZATION ====================

  /**
   * Initialize secure storage with encryption key derivation
   */
  async initialize() {
    try {
      console.log('VoiceHype: Initializing secure storage...');
      
      // Generate or retrieve master key
      await this.initializeEncryptionKey();
      
      this.isInitialized = true;
      console.log('VoiceHype: Secure storage initialized');
      
    } catch (error) {
      console.error('VoiceHype: Failed to initialize secure storage:', error);
      throw error;
    }
  }

  /**
   * Initialize encryption key for secure storage
   */
  async initializeEncryptionKey() {
    try {
      // Try to get existing salt
      let salt = await this.storage.browser.getStorage('vh_security_salt');
      
      if (!salt) {
        // Generate new salt for first-time setup
        salt = this.generateSalt();
        await this.storage.browser.setStorage('vh_security_salt', salt);
        console.log('VoiceHype: Generated new security salt');
      }

      // Derive encryption key from browser fingerprint + salt
      const fingerprint = await this.generateBrowserFingerprint();
      this.encryptionKey = await this.deriveKey(fingerprint, salt);
      
    } catch (error) {
      console.error('VoiceHype: Failed to initialize encryption key:', error);
      throw error;
    }
  }

  // ==================== API KEY MANAGEMENT ====================

  /**
   * Store API key with enhanced security (same pattern as VS Code extension)
   */
  async storeApiKeySecure(apiKey) {
    this.ensureInitialized();
    
    try {
      if (!apiKey || !apiKey.startsWith('vhkey_')) {
        throw new Error('Invalid VoiceHype API key format');
      }

      console.log('VoiceHype: Storing API key securely...');

      // Encrypt the API key
      const encryptedKey = await this.encryptData(apiKey);
      
      // Generate integrity hash (same as StorageAdapter but with additional security)
      const integrityHash = await this.generateIntegrityHash(apiKey);
      
      // Store encrypted key and hash
      await Promise.all([
        this.storage.browser.setStorage('vh_api_key_encrypted', encryptedKey),
        this.storage.browser.setStorage('vh_api_key_integrity', integrityHash),
        this.storage.browser.setStorage('vh_api_key_timestamp', Date.now())
      ]);

      // Also store using the basic method for backward compatibility
      await this.storage.storeApiKey(apiKey);

      console.log('VoiceHype: API key stored with enhanced security');
      return true;
      
    } catch (error) {
      console.error('VoiceHype: Failed to store API key securely:', error);
      throw error;
    }
  }

  /**
   * Retrieve API key with security verification
   */
  async getApiKeySecure() {
    this.ensureInitialized();
    
    try {
      // Get encrypted data
      const [encryptedKey, storedHash, timestamp] = await Promise.all([
        this.storage.browser.getStorage('vh_api_key_encrypted'),
        this.storage.browser.getStorage('vh_api_key_integrity'),
        this.storage.browser.getStorage('vh_api_key_timestamp')
      ]);

      if (!encryptedKey || !storedHash) {
        // Fallback to basic storage
        return await this.storage.getApiKey();
      }

      // Check if key is too old (optional security measure)
      if (timestamp && Date.now() - timestamp > 30 * 24 * 60 * 60 * 1000) { // 30 days
        console.warn('VoiceHype: API key is older than 30 days, consider refreshing');
      }

      // Decrypt the API key
      const apiKey = await this.decryptData(encryptedKey);
      
      if (!apiKey) {
        console.warn('VoiceHype: Failed to decrypt API key');
        return await this.storage.getApiKey();
      }

      // Verify integrity
      const currentHash = await this.generateIntegrityHash(apiKey);
      if (currentHash !== storedHash) {
        console.warn('VoiceHype: API key integrity check failed');
        await this.clearApiKeySecure();
        return null;
      }

      return apiKey;
      
    } catch (error) {
      console.error('VoiceHype: Failed to retrieve API key securely:', error);
      // Fallback to basic storage
      return await this.storage.getApiKey();
    }
  }

  /**
   * Clear secure API key storage
   */
  async clearApiKeySecure() {
    try {
      await Promise.all([
        this.storage.browser.removeStorage('vh_api_key_encrypted'),
        this.storage.browser.removeStorage('vh_api_key_integrity'),
        this.storage.browser.removeStorage('vh_api_key_timestamp'),
        this.storage.clearApiKey() // Also clear basic storage
      ]);
      
      console.log('VoiceHype: Secure API key storage cleared');
      
    } catch (error) {
      console.error('VoiceHype: Failed to clear secure API key storage:', error);
      throw error;
    }
  }

  // ==================== SUPABASE SESSION MANAGEMENT ====================

  /**
   * Store Supabase session securely (same pattern as VS Code extension)
   */
  async storeSupabaseSession(session) {
    this.ensureInitialized();
    
    try {
      if (!session) {
        await this.clearSupabaseSession();
        return;
      }

      console.log('VoiceHype: Storing Supabase session securely...');

      // Encrypt the session data
      const encryptedSession = await this.encryptData(JSON.stringify(session));
      
      // Store with timestamp
      await Promise.all([
        this.storage.browser.setStorage('vh_supabase_session_encrypted', encryptedSession),
        this.storage.browser.setStorage('vh_supabase_session_timestamp', Date.now())
      ]);

      console.log('VoiceHype: Supabase session stored securely');
      
    } catch (error) {
      console.error('VoiceHype: Failed to store Supabase session:', error);
      throw error;
    }
  }

  /**
   * Retrieve Supabase session
   */
  async getSupabaseSession() {
    this.ensureInitialized();
    
    try {
      const [encryptedSession, timestamp] = await Promise.all([
        this.storage.browser.getStorage('vh_supabase_session_encrypted'),
        this.storage.browser.getStorage('vh_supabase_session_timestamp')
      ]);

      if (!encryptedSession) {
        return null;
      }

      // Check if session is expired (based on timestamp)
      if (timestamp && Date.now() - timestamp > 24 * 60 * 60 * 1000) { // 24 hours
        console.log('VoiceHype: Supabase session expired, clearing...');
        await this.clearSupabaseSession();
        return null;
      }

      // Decrypt session
      const sessionStr = await this.decryptData(encryptedSession);
      if (!sessionStr) {
        return null;
      }

      return JSON.parse(sessionStr);
      
    } catch (error) {
      console.error('VoiceHype: Failed to retrieve Supabase session:', error);
      return null;
    }
  }

  /**
   * Clear Supabase session
   */
  async clearSupabaseSession() {
    try {
      await Promise.all([
        this.storage.browser.removeStorage('vh_supabase_session_encrypted'),
        this.storage.browser.removeStorage('vh_supabase_session_timestamp')
      ]);
      
      console.log('VoiceHype: Supabase session cleared');
      
    } catch (error) {
      console.error('VoiceHype: Failed to clear Supabase session:', error);
      throw error;
    }
  }

  // ==================== ENCRYPTION UTILITIES ====================

  /**
   * Encrypt data using AES-GCM
   */
  async encryptData(data) {
    try {
      const encoder = new TextEncoder();
      const dataBuffer = encoder.encode(data);
      
      // Generate random IV
      const iv = crypto.getRandomValues(new Uint8Array(this.IV_LENGTH));
      
      // Encrypt data
      const encryptedBuffer = await crypto.subtle.encrypt(
        { name: this.ENCRYPTION_ALGORITHM, iv },
        this.encryptionKey,
        dataBuffer
      );
      
      // Combine IV and encrypted data
      const combined = new Uint8Array(iv.length + encryptedBuffer.byteLength);
      combined.set(iv);
      combined.set(new Uint8Array(encryptedBuffer), iv.length);
      
      // Return as base64
      return btoa(String.fromCharCode(...combined));
      
    } catch (error) {
      console.error('VoiceHype: Encryption failed:', error);
      throw error;
    }
  }

  /**
   * Decrypt data using AES-GCM
   */
  async decryptData(encryptedData) {
    try {
      // Decode from base64
      const combined = new Uint8Array(
        atob(encryptedData).split('').map(char => char.charCodeAt(0))
      );
      
      // Extract IV and encrypted data
      const iv = combined.slice(0, this.IV_LENGTH);
      const encrypted = combined.slice(this.IV_LENGTH);
      
      // Decrypt data
      const decryptedBuffer = await crypto.subtle.decrypt(
        { name: this.ENCRYPTION_ALGORITHM, iv },
        this.encryptionKey,
        encrypted
      );
      
      // Convert back to string
      const decoder = new TextDecoder();
      return decoder.decode(decryptedBuffer);
      
    } catch (error) {
      console.error('VoiceHype: Decryption failed:', error);
      return null;
    }
  }

  /**
   * Generate integrity hash for verification
   */
  async generateIntegrityHash(data) {
    const encoder = new TextEncoder();
    const dataBuffer = encoder.encode(data + 'voicehype_integrity_salt');
    const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
    
    return Array.from(new Uint8Array(hashBuffer))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
  }

  /**
   * Generate browser fingerprint for key derivation
   */
  async generateBrowserFingerprint() {
    const components = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      new Date().getTimezoneOffset().toString(),
      'voicehype_extension'
    ];
    
    const fingerprint = components.join('|');
    const encoder = new TextEncoder();
    const hashBuffer = await crypto.subtle.digest('SHA-256', encoder.encode(fingerprint));
    
    return Array.from(new Uint8Array(hashBuffer))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
  }

  /**
   * Generate cryptographic salt
   */
  generateSalt() {
    const salt = crypto.getRandomValues(new Uint8Array(this.SALT_LENGTH));
    return Array.from(salt).map(b => b.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Derive encryption key from password and salt
   */
  async deriveKey(password, salt) {
    const encoder = new TextEncoder();
    const passwordBuffer = encoder.encode(password);
    const saltBuffer = encoder.encode(salt);
    
    // Import password as key material
    const keyMaterial = await crypto.subtle.importKey(
      'raw',
      passwordBuffer,
      'PBKDF2',
      false,
      ['deriveKey']
    );
    
    // Derive actual encryption key
    return await crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt: saltBuffer,
        iterations: this.KEY_DERIVATION_ITERATIONS,
        hash: 'SHA-256'
      },
      keyMaterial,
      { name: this.ENCRYPTION_ALGORITHM, length: 256 },
      false,
      ['encrypt', 'decrypt']
    );
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Ensure secure storage is initialized
   */
  ensureInitialized() {
    if (!this.isInitialized) {
      throw new Error('Secure storage not initialized. Call initialize() first.');
    }
  }

  /**
   * Get security status
   */
  getSecurityStatus() {
    return {
      initialized: this.isInitialized,
      hasEncryptionKey: !!this.encryptionKey,
      algorithm: this.ENCRYPTION_ALGORITHM,
      keyDerivationIterations: this.KEY_DERIVATION_ITERATIONS
    };
  }

  /**
   * Clear all secure storage
   */
  async clearAllSecureData() {
    try {
      const secureKeys = [
        'vh_api_key_encrypted',
        'vh_api_key_integrity',
        'vh_api_key_timestamp',
        'vh_supabase_session_encrypted',
        'vh_supabase_session_timestamp',
        'vh_security_salt'
      ];

      await Promise.all(
        secureKeys.map(key => this.storage.browser.removeStorage(key))
      );

      console.log('VoiceHype: All secure data cleared');
      
    } catch (error) {
      console.error('VoiceHype: Failed to clear secure data:', error);
      throw error;
    }
  }
}

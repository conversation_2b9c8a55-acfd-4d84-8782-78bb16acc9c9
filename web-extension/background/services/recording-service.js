/**
 * Recording Service - Main coordinator for audio recording and transcription
 * <PERSON><PERSON><PERSON><PERSON> rah<PERSON>r raheem
 * 
 * Coordinates AudioRecordingService and RealtimeTranscriptionService
 * Following the same patterns as VS Code extension RecordingService
 */

import { AudioRecordingService } from './audio-recording-service.js';
import { RealtimeTranscriptionService } from './realtime-transcription-service.js';
import { CONSTANTS } from '../utils/constants.js';

export class RecordingService {
  constructor(configManager, secureStorage, supabaseService, messagingAdapter, browserAdapter) {
    this.config = configManager;
    this.secureStorage = secureStorage;
    this.supabaseService = supabaseService;
    this.messaging = messagingAdapter;
    this.browser = browserAdapter;
    
    // Initialize sub-services
    this.audioService = new AudioRecordingService(configManager, messagingAdapter, browserAdapter);
    this.realtimeService = new RealtimeTranscriptionService(configManager, secureStorage, messagingAdapter);
    
    // Recording state
    this.isRecording = false;
    this.isPaused = false;
    this.useRealtime = false;
    this.currentRecording = null;
    this.recordingStartTime = null;
    
    // Transcription state
    this.currentTranscript = '';
    this.isTranscribing = false;
    this.transcriptionResult = null;
  }

  // ==================== RECORDING CONTROL ====================

  /**
   * Start recording with optional real-time transcription (same pattern as VS Code extension)
   */
  async startRecording(options = {}) {
    if (this.isRecording) {
      throw new Error('Recording already in progress');
    }

    try {
      console.log('VoiceHype: Starting recording session...');
      
      // Get configuration
      const transcriptionConfig = this.config.getSection('transcription');
      const audioConfig = this.config.getSection('audio');
      
      this.useRealtime = options.realtime ?? transcriptionConfig.realtime ?? false;
      
      console.log(`VoiceHype: Recording mode: ${this.useRealtime ? 'real-time' : 'regular'}`);

      // Check API key
      const apiKey = await this.secureStorage.getApiKeySecure();
      if (!apiKey) {
        throw new Error('No API key found. Please authenticate first.');
      }

      // Start real-time transcription if enabled
      if (this.useRealtime) {
        try {
          console.log('VoiceHype: Connecting to real-time transcription service...');
          
          await this.realtimeService.connect({
            service: options.service || transcriptionConfig.service,
            model: options.model || transcriptionConfig.model,
            language: options.language || transcriptionConfig.language,
            sampleRate: audioConfig.sampleRate
          });
          
          console.log('VoiceHype: Real-time transcription connected');
          
          // Show notification
          await this.browser.showNotification({
            message: 'Connection established, start speaking!',
            type: 'basic'
          });

        } catch (connectionError) {
          console.error('VoiceHype: Failed to connect to real-time transcription:', connectionError);
          
          // Fall back to regular recording
          console.log('VoiceHype: Falling back to regular recording...');
          this.useRealtime = false;
          
          await this.browser.showNotification({
            message: 'Real-time transcription unavailable, using regular recording',
            type: 'basic'
          });
        }
      }

      // Start audio recording
      const recordingOptions = {
        realtime: this.useRealtime,
        realtimeCallback: this.useRealtime ? this.handleRealtimeAudioChunk.bind(this) : null,
        audioSettings: {
          sampleRate: audioConfig.sampleRate,
          ...options.audioSettings
        }
      };

      const recordingResult = await this.audioService.startRecording(recordingOptions);
      
      // Update state
      this.isRecording = true;
      this.isPaused = false;
      this.recordingStartTime = Date.now();
      this.currentTranscript = '';
      this.transcriptionResult = null;

      console.log('VoiceHype: Recording session started successfully');

      // Show notification
      const shortcut = this.useRealtime ? 'Ctrl+Shift+9' : 'Ctrl+Shift+8';
      await this.browser.showNotification({
        message: `Recording started! Press ${shortcut} again to stop.`,
        type: 'basic'
      });

      // Notify components
      await this.messaging.broadcast('recording_session_started', {
        realtime: this.useRealtime,
        audioSettings: recordingResult.audioSettings,
        timestamp: this.recordingStartTime
      });

      return {
        success: true,
        realtime: this.useRealtime,
        audioSettings: recordingResult.audioSettings
      };

    } catch (error) {
      console.error('VoiceHype: Failed to start recording session:', error);
      await this.cleanup();
      throw error;
    }
  }

  /**
   * Stop recording and process transcription (same pattern as VS Code extension)
   */
  async stopRecording() {
    if (!this.isRecording) {
      console.warn('VoiceHype: No recording session in progress');
      return null;
    }

    try {
      console.log('VoiceHype: Stopping recording session...');

      // Stop audio recording
      const recordingResult = await this.audioService.stopRecording();
      
      if (!recordingResult) {
        throw new Error('Failed to stop audio recording');
      }

      // Handle real-time transcription cleanup
      if (this.useRealtime) {
        // Send final chunk and disconnect
        this.realtimeService.sendFinalChunk();
        await this.realtimeService.disconnect();
        
        // Get final transcript from real-time service
        const realtimeTranscript = this.realtimeService.getCurrentTranscript();
        this.currentTranscript = realtimeTranscript.combined;
        
        console.log('VoiceHype: Real-time transcription completed:', this.currentTranscript);
      }

      // Process regular transcription if not using real-time
      if (!this.useRealtime && recordingResult.base64Audio) {
        console.log('VoiceHype: Processing regular transcription...');

        try {
          this.isTranscribing = true;

          // Notify components
          await this.messaging.broadcast('transcription_started', {
            timestamp: Date.now()
          });

          // Check if audio needs slicing
          const transcriptionConfig = this.config.getSection('transcription');
          const audioSize = this.calculateBase64Size(recordingResult.base64Audio);
          const needsSlicing = this.checkIfAudioNeedsSlicing(audioSize, transcriptionConfig.service);

          let transcriptionResult;

          if (needsSlicing) {
            console.log(`VoiceHype: Audio size ${(audioSize / 1024 / 1024).toFixed(2)}MB exceeds limit, using slicing...`);
            transcriptionResult = await this.transcribeAudioWithSlicing(recordingResult.base64Audio, {
              service: transcriptionConfig.service,
              model: transcriptionConfig.model,
              language: transcriptionConfig.language,
              translate: transcriptionConfig.translate
            });
          } else {
            // Call transcription API directly
            transcriptionResult = await this.supabaseService.transcribeAudio(
              recordingResult.base64Audio,
              {
                service: transcriptionConfig.service,
                model: transcriptionConfig.model,
                language: transcriptionConfig.language,
                translate: transcriptionConfig.translate
              }
            );
          }

          if (transcriptionResult.success && transcriptionResult.data?.text) {
            this.currentTranscript = transcriptionResult.data.text;
            console.log('VoiceHype: Regular transcription completed:', this.currentTranscript);
          } else {
            throw new Error('Transcription failed: ' + (transcriptionResult.error || 'Unknown error'));
          }

        } catch (transcriptionError) {
          console.error('VoiceHype: Transcription failed:', transcriptionError);
          this.currentTranscript = '[Transcription failed: ' + transcriptionError.message + ']';
        } finally {
          this.isTranscribing = false;
        }
      }

      // Calculate final duration
      const duration = Date.now() - this.recordingStartTime;

      // Create final result
      const finalResult = {
        success: true,
        transcript: this.currentTranscript,
        audioBlob: recordingResult.audioBlob,
        base64Audio: recordingResult.base64Audio,
        duration: duration,
        audioSize: recordingResult.size,
        mimeType: recordingResult.mimeType,
        realtime: this.useRealtime,
        chunkCount: recordingResult.chunkCount,
        totalBytes: recordingResult.totalBytes,
        timestamp: this.recordingStartTime
      };

      // Store current recording
      this.currentRecording = finalResult;

      // Reset state
      this.isRecording = false;
      this.isPaused = false;
      this.isTranscribing = false;

      console.log('VoiceHype: Recording session completed successfully');

      // Show notification
      await this.browser.showNotification({
        message: `Recording completed! Transcript: ${this.currentTranscript.substring(0, 50)}${this.currentTranscript.length > 50 ? '...' : ''}`,
        type: 'basic'
      });

      // Notify components
      await this.messaging.broadcast('recording_session_completed', finalResult);

      return finalResult;

    } catch (error) {
      console.error('VoiceHype: Failed to stop recording session:', error);
      await this.cleanup();
      throw error;
    }
  }

  /**
   * Pause recording
   */
  async pauseRecording() {
    if (!this.isRecording || this.isPaused) {
      throw new Error('Cannot pause: not recording or already paused');
    }

    try {
      console.log('VoiceHype: Pausing recording session...');
      
      await this.audioService.pauseRecording();
      this.isPaused = true;

      await this.messaging.broadcast('recording_session_paused', {
        timestamp: Date.now()
      });

      console.log('VoiceHype: Recording session paused');
      return { success: true, paused: true };

    } catch (error) {
      console.error('VoiceHype: Failed to pause recording session:', error);
      throw error;
    }
  }

  /**
   * Resume recording
   */
  async resumeRecording() {
    if (!this.isRecording || !this.isPaused) {
      throw new Error('Cannot resume: not recording or not paused');
    }

    try {
      console.log('VoiceHype: Resuming recording session...');
      
      await this.audioService.resumeRecording();
      this.isPaused = false;

      await this.messaging.broadcast('recording_session_resumed', {
        timestamp: Date.now()
      });

      console.log('VoiceHype: Recording session resumed');
      return { success: true, paused: false };

    } catch (error) {
      console.error('VoiceHype: Failed to resume recording session:', error);
      throw error;
    }
  }

  // ==================== REAL-TIME AUDIO HANDLING ====================

  /**
   * Handle real-time audio chunks (same pattern as VS Code extension)
   */
  handleRealtimeAudioChunk(audioChunk) {
    try {
      if (!this.useRealtime || !this.realtimeService.isConnected) {
        return;
      }

      // Send audio chunk to real-time transcription service
      const success = this.realtimeService.sendAudioChunk(audioChunk);
      
      if (!success) {
        console.warn('VoiceHype: Failed to send audio chunk to real-time service');
      }

    } catch (error) {
      console.error('VoiceHype: Error handling real-time audio chunk:', error);
    }
  }

  // ==================== TRANSCRIPTION METHODS ====================

  /**
   * Transcribe existing audio (for manual transcription)
   */
  async transcribeAudio(base64Audio, options = {}) {
    try {
      console.log('VoiceHype: Starting manual transcription...');

      this.isTranscribing = true;

      await this.messaging.broadcast('transcription_started', {
        manual: true,
        timestamp: Date.now()
      });

      const transcriptionConfig = this.config.getSection('transcription');
      const service = options.service || transcriptionConfig.service;

      // Check if audio needs slicing based on service limits
      const audioSize = this.calculateBase64Size(base64Audio);
      const needsSlicing = this.checkIfAudioNeedsSlicing(audioSize, service);

      let result;

      if (needsSlicing) {
        console.log(`VoiceHype: Audio size ${(audioSize / 1024 / 1024).toFixed(2)}MB exceeds limit for ${service}, slicing...`);
        result = await this.transcribeAudioWithSlicing(base64Audio, options);
      } else {
        result = await this.supabaseService.transcribeAudio(base64Audio, {
          service: service,
          model: options.model || transcriptionConfig.model,
          language: options.language || transcriptionConfig.language,
          translate: options.translate || transcriptionConfig.translate
        });
      }

      this.isTranscribing = false;

      if (result.success) {
        console.log('VoiceHype: Manual transcription completed');

        await this.messaging.broadcast('transcription_completed', {
          transcript: result.data.text,
          manual: true,
          timestamp: Date.now()
        });
      }

      return result;

    } catch (error) {
      console.error('VoiceHype: Manual transcription failed:', error);
      this.isTranscribing = false;
      throw error;
    }
  }

  /**
   * Check if audio needs slicing based on service limits (same as VS Code extension)
   */
  checkIfAudioNeedsSlicing(audioSizeBytes, service) {
    const sizeMB = audioSizeBytes / 1024 / 1024;

    // Use constants for service limits
    const limit = CONSTANTS.TRANSCRIPTION.SIZE_LIMITS[service.toUpperCase()] ||
                  CONSTANTS.TRANSCRIPTION.SIZE_LIMITS[service] ||
                  25; // Default to 25MB for unknown services

    console.log(`VoiceHype: Audio size: ${sizeMB.toFixed(2)}MB, Service: ${service}, Limit: ${limit}MB`);

    return sizeMB > limit;
  }

  /**
   * Calculate base64 audio size in bytes
   */
  calculateBase64Size(base64Audio) {
    // Base64 encoding increases size by ~33%, so actual size is ~75% of base64 length
    return Math.floor(base64Audio.length * 0.75);
  }

  /**
   * Transcribe audio with slicing (same pattern as VS Code extension)
   */
  async transcribeAudioWithSlicing(base64Audio, options = {}) {
    try {
      console.log('VoiceHype: Starting audio slicing transcription...');

      const transcriptionConfig = this.config.getSection('transcription');
      const service = options.service || transcriptionConfig.service;

      // Convert base64 to blob for slicing
      const audioBlob = this.base64ToBlob(base64Audio);

      // Calculate slice parameters
      const sliceParams = this.calculateSliceParameters(audioBlob.size, service);
      console.log('VoiceHype: Slice parameters:', sliceParams);

      // Slice the audio
      const audioSlices = await this.sliceAudioBlob(audioBlob, sliceParams);
      console.log(`VoiceHype: Created ${audioSlices.length} audio slices`);

      // Transcribe each slice
      const transcriptionPromises = audioSlices.map(async (slice, index) => {
        try {
          console.log(`VoiceHype: Transcribing slice ${index + 1}/${audioSlices.length}...`);

          // Convert slice back to base64
          const sliceBase64 = await this.blobToBase64(slice);

          // Transcribe slice
          const result = await this.supabaseService.transcribeAudio(sliceBase64, {
            service: service,
            model: options.model || transcriptionConfig.model,
            language: options.language || transcriptionConfig.language,
            translate: options.translate || transcriptionConfig.translate
          });

          if (result.success && result.data?.text) {
            console.log(`VoiceHype: Slice ${index + 1} transcribed: "${result.data.text.substring(0, 50)}..."`);
            return result.data.text;
          } else {
            console.warn(`VoiceHype: Slice ${index + 1} transcription failed:`, result.error);
            return `[Slice ${index + 1} transcription failed]`;
          }

        } catch (error) {
          console.error(`VoiceHype: Error transcribing slice ${index + 1}:`, error);
          return `[Slice ${index + 1} error: ${error.message}]`;
        }
      });

      // Wait for all transcriptions to complete
      const transcripts = await Promise.all(transcriptionPromises);

      // Combine transcripts
      const combinedTranscript = transcripts
        .filter(text => text && !text.startsWith('[Slice'))
        .join(' ')
        .trim();

      console.log(`VoiceHype: Combined transcript from ${audioSlices.length} slices: "${combinedTranscript.substring(0, 100)}..."`);

      return {
        success: true,
        data: {
          text: combinedTranscript,
          slices: audioSlices.length,
          individual_transcripts: transcripts
        }
      };

    } catch (error) {
      console.error('VoiceHype: Audio slicing transcription failed:', error);
      throw error;
    }
  }

  /**
   * Calculate slice parameters based on service limits
   */
  calculateSliceParameters(audioSizeBytes, service) {
    const sizeMB = audioSizeBytes / 1024 / 1024;

    // Use constants for slice limits with safety margin
    const maxSliceSizeMB = CONSTANTS.TRANSCRIPTION.SLICE_LIMITS[service.toUpperCase()] ||
                           CONSTANTS.TRANSCRIPTION.SLICE_LIMITS[service] ||
                           20; // Default to 20MB for unknown services

    const maxSliceSizeBytes = maxSliceSizeMB * 1024 * 1024;

    // Calculate number of slices needed
    const numSlices = Math.ceil(audioSizeBytes / maxSliceSizeBytes);
    const sliceSizeBytes = Math.floor(audioSizeBytes / numSlices);

    return {
      totalSize: audioSizeBytes,
      totalSizeMB: sizeMB,
      numSlices: numSlices,
      sliceSizeBytes: sliceSizeBytes,
      sliceSizeMB: sliceSizeBytes / 1024 / 1024,
      maxSliceSizeMB: maxSliceSizeMB
    };
  }

  /**
   * Slice audio blob into smaller chunks
   */
  async sliceAudioBlob(audioBlob, sliceParams) {
    const slices = [];
    const { numSlices, sliceSizeBytes } = sliceParams;

    for (let i = 0; i < numSlices; i++) {
      const start = i * sliceSizeBytes;
      const end = Math.min(start + sliceSizeBytes, audioBlob.size);

      const slice = audioBlob.slice(start, end, audioBlob.type);
      slices.push(slice);

      console.log(`VoiceHype: Created slice ${i + 1}/${numSlices}: ${start}-${end} bytes (${(slice.size / 1024 / 1024).toFixed(2)}MB)`);
    }

    return slices;
  }

  /**
   * Convert base64 to blob
   */
  base64ToBlob(base64Audio, mimeType = 'audio/webm') {
    try {
      const byteCharacters = atob(base64Audio);
      const byteNumbers = new Array(byteCharacters.length);

      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }

      const byteArray = new Uint8Array(byteNumbers);
      return new Blob([byteArray], { type: mimeType });

    } catch (error) {
      console.error('VoiceHype: Error converting base64 to blob:', error);
      throw error;
    }
  }

  /**
   * Convert blob to base64
   */
  async blobToBase64(blob) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const base64 = reader.result.split(',')[1]; // Remove data:audio/... prefix
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }

  // ==================== STATUS METHODS ====================

  /**
   * Get current recording status
   */
  getRecordingStatus() {
    const audioStatus = this.audioService.getRecordingStatus();
    const realtimeStatus = this.realtimeService.getStatus();
    
    return {
      isRecording: this.isRecording,
      isPaused: this.isPaused,
      isTranscribing: this.isTranscribing,
      useRealtime: this.useRealtime,
      currentTranscript: this.currentTranscript,
      duration: this.recordingStartTime ? Date.now() - this.recordingStartTime : 0,
      audio: audioStatus,
      realtime: realtimeStatus,
      hasCurrentRecording: !!this.currentRecording
    };
  }

  /**
   * Get current recording
   */
  getCurrentRecording() {
    return this.currentRecording;
  }

  /**
   * Clear current recording
   */
  clearCurrentRecording() {
    this.currentRecording = null;
    console.log('VoiceHype: Current recording cleared');
  }

  /**
   * Check microphone availability
   */
  async checkMicrophoneAvailability() {
    return await this.audioService.checkMicrophoneAvailability();
  }

  // ==================== CLEANUP ====================

  /**
   * Cleanup all recording resources
   */
  async cleanup() {
    try {
      console.log('VoiceHype: Cleaning up recording service...');

      // Stop audio recording
      await this.audioService.cleanup();
      
      // Disconnect real-time transcription
      await this.realtimeService.cleanup();

      // Reset state
      this.isRecording = false;
      this.isPaused = false;
      this.isTranscribing = false;
      this.useRealtime = false;
      this.currentTranscript = '';
      this.recordingStartTime = null;

      console.log('VoiceHype: Recording service cleanup complete');

    } catch (error) {
      console.error('VoiceHype: Error during recording service cleanup:', error);
    }
  }

  /**
   * Get service status
   */
  getServiceStatus() {
    return {
      isRecording: this.isRecording,
      isPaused: this.isPaused,
      isTranscribing: this.isTranscribing,
      useRealtime: this.useRealtime,
      audioService: this.audioService.getRecordingStatus(),
      realtimeService: this.realtimeService.getStatus(),
      hasCurrentRecording: !!this.currentRecording
    };
  }

  // ==================== ENHANCED ERROR HANDLING ====================

  /**
   * Enhanced error handling with user-friendly messages
   */
  handleError(error, context = 'Recording') {
    console.error(`VoiceHype ${context} Error:`, error);

    // Enhanced error categorization
    const errorType = this.categorizeError(error);
    const userMessage = this.getUserFriendlyMessage(error, errorType);

    // Notify content script of error
    this.notifyContentScript('error', {
      message: userMessage,
      context: context,
      type: errorType,
      canRetry: this.canRetryError(errorType)
    });

    // Clean up any active recording
    if (this.isRecording) {
      this.stopRecording();
    }

    // Log for analytics (without sensitive data)
    this.logErrorForAnalytics(errorType, context);
  }

  categorizeError(error) {
    if (error.name === 'NotAllowedError') return 'MICROPHONE_PERMISSION';
    if (error.name === 'NotFoundError') return 'MICROPHONE_NOT_FOUND';
    if (error.name === 'NetworkError') return 'NETWORK_ERROR';
    if (error.message?.includes('API key')) return 'AUTHENTICATION_ERROR';
    if (error.message?.includes('quota')) return 'QUOTA_EXCEEDED';
    if (error.message?.includes('timeout')) return 'TIMEOUT_ERROR';
    return 'UNKNOWN_ERROR';
  }

  getUserFriendlyMessage(error, errorType) {
    const messages = {
      'MICROPHONE_PERMISSION': 'Please allow microphone access to use voice recording.',
      'MICROPHONE_NOT_FOUND': 'No microphone found. Please check your audio devices.',
      'NETWORK_ERROR': 'Network connection issue. Please check your internet connection.',
      'AUTHENTICATION_ERROR': 'Authentication failed. Please sign in again.',
      'QUOTA_EXCEEDED': 'Usage quota exceeded. Please upgrade your plan or wait for reset.',
      'TIMEOUT_ERROR': 'Request timed out. Please try again.',
      'UNKNOWN_ERROR': 'An unexpected error occurred. Please try again.'
    };

    return messages[errorType] || error.message || 'An unexpected error occurred.';
  }

  canRetryError(errorType) {
    const retryableErrors = ['NETWORK_ERROR', 'TIMEOUT_ERROR', 'UNKNOWN_ERROR'];
    return retryableErrors.includes(errorType);
  }

  logErrorForAnalytics(errorType, context) {
    // Log error without sensitive information
    try {
      console.log('VoiceHype Error Analytics:', {
        type: errorType,
        context: context,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: 'extension'
      });
    } catch (e) {
      // Ignore analytics errors
    }
  }

  // ==================== PERFORMANCE MONITORING ====================

  /**
   * Monitor performance metrics
   */
  startPerformanceMonitoring(operation) {
    const startTime = performance.now();
    const startMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;

    return {
      operation,
      startTime,
      startMemory,
      end: () => {
        const endTime = performance.now();
        const endMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
        const duration = endTime - startTime;
        const memoryDelta = endMemory - startMemory;

        console.log(`VoiceHype Performance [${operation}]:`, {
          duration: `${duration.toFixed(2)}ms`,
          memoryDelta: `${(memoryDelta / 1024 / 1024).toFixed(2)}MB`
        });

        return { duration, memoryDelta };
      }
    };
  }
}

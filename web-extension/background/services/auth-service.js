/**
 * Authentication Service - OAuth flow and session management
 * <PERSON><PERSON><PERSON><PERSON> rahmanir raheem
 * 
 * Handles authentication following the same patterns as VS Code extension
 * Supports OAuth with Google/GitHub and API key management
 */

import { createClient } from '@supabase/supabase-js';

export class AuthService {
  constructor(configManager, secureStorage, browserAdapter, messagingAdapter) {
    this.config = configManager;
    this.secureStorage = secureStorage;
    this.browser = browserAdapter;
    this.messaging = messagingAdapter;
    
    this.supabase = null;
    this.currentSession = null;
    this.currentUser = null;
    this.isInitialized = false;
    
    // OAuth configuration
    this.oauthConfig = {
      redirectUrl: 'https://voicehype.netlify.app/app',
      scopes: 'openid email profile',
      providers: ['google', 'github']
    };
  }

  // ==================== INITIALIZATION ====================

  /**
   * Initialize authentication service
   */
  async initialize() {
    try {
      console.log('VoiceHype: Initializing authentication service...');
      
      // Initialize Supabase client (same as website)
      const apiConfig = this.config.getSection('api');
      this.supabase = createClient(apiConfig.supabaseUrl, apiConfig.supabaseAnonKey, {
        auth: {
          autoRefreshToken: true,
          persistSession: false, // We handle session persistence manually
          detectSessionInUrl: false
        }
      });

      // Load existing session
      await this.loadStoredSession();
      
      // Setup auth state change listener
      this.setupAuthStateListener();
      
      this.isInitialized = true;
      console.log('VoiceHype: Authentication service initialized');
      
    } catch (error) {
      console.error('VoiceHype: Failed to initialize authentication service:', error);
      throw error;
    }
  }

  // ==================== OAUTH AUTHENTICATION ====================

  /**
   * Start OAuth flow (same pattern as VS Code extension)
   */
  async startOAuthFlow(provider = 'google') {
    this.ensureInitialized();
    
    try {
      console.log(`VoiceHype: Starting OAuth flow with ${provider}...`);
      
      if (!this.oauthConfig.providers.includes(provider)) {
        throw new Error(`Unsupported OAuth provider: ${provider}`);
      }

      // Generate state parameter for security
      const state = this.generateState();
      
      // Store state for verification
      await this.browser.setStorage('vh_oauth_state', state);
      await this.browser.setStorage('vh_oauth_provider', provider);
      
      // Get OAuth URL from Supabase
      const { data, error } = await this.supabase.auth.signInWithOAuth({
        provider: provider,
        options: {
          redirectTo: this.oauthConfig.redirectUrl,
          scopes: this.oauthConfig.scopes,
          queryParams: {
            state: state,
            client_type: 'web_extension'
          }
        }
      });

      if (error) {
        throw new Error(`OAuth URL generation failed: ${error.message}`);
      }

      if (!data?.url) {
        throw new Error('No OAuth URL received from Supabase');
      }

      console.log('VoiceHype: Opening OAuth popup...');
      
      // Open OAuth popup
      const authWindow = await this.browser.createTab({
        url: data.url,
        active: true
      });

      // Monitor the popup for completion
      return await this.monitorOAuthFlow(authWindow.id, state);
      
    } catch (error) {
      console.error('VoiceHype: OAuth flow failed:', error);
      throw error;
    }
  }

  /**
   * Monitor OAuth flow completion
   */
  async monitorOAuthFlow(tabId, expectedState) {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.browser.closeTab(tabId);
        reject(new Error('OAuth flow timed out'));
      }, 300000); // 5 minutes timeout

      // Listen for tab updates
      const tabUpdateListener = async (updatedTabId, changeInfo, tab) => {
        if (updatedTabId !== tabId || !tab.url) return;

        try {
          const url = new URL(tab.url);
          
          // Check if we're back at the redirect URL
          if (url.origin === new URL(this.oauthConfig.redirectUrl).origin) {
            
            // Extract parameters from URL
            const urlParams = new URLSearchParams(url.search);
            const hashParams = new URLSearchParams(url.hash.substring(1));
            
            // Check for error
            const error = urlParams.get('error') || hashParams.get('error');
            if (error) {
              throw new Error(`OAuth error: ${error}`);
            }

            // Verify state parameter
            const state = urlParams.get('state') || hashParams.get('state');
            if (state !== expectedState) {
              throw new Error('OAuth state mismatch - possible CSRF attack');
            }

            // Extract tokens
            const accessToken = hashParams.get('access_token');
            const refreshToken = hashParams.get('refresh_token');
            
            if (!accessToken) {
              throw new Error('No access token received from OAuth');
            }

            console.log('VoiceHype: OAuth flow completed successfully');
            
            // Clean up
            clearTimeout(timeout);
            this.browser.api.tabs.onUpdated.removeListener(tabUpdateListener);
            this.browser.closeTab(tabId);
            
            // Process the authentication
            const result = await this.processOAuthSuccess(accessToken, refreshToken);
            resolve(result);
          }
          
        } catch (error) {
          clearTimeout(timeout);
          this.browser.api.tabs.onUpdated.removeListener(tabUpdateListener);
          this.browser.closeTab(tabId);
          reject(error);
        }
      };

      this.browser.api.tabs.onUpdated.addListener(tabUpdateListener);
    });
  }

  /**
   * Process successful OAuth authentication
   */
  async processOAuthSuccess(accessToken, refreshToken) {
    try {
      console.log('VoiceHype: Processing OAuth success...');
      
      // Set the session in Supabase client
      const { data: sessionData, error: sessionError } = await this.supabase.auth.setSession({
        access_token: accessToken,
        refresh_token: refreshToken
      });

      if (sessionError) {
        throw new Error(`Session setup failed: ${sessionError.message}`);
      }

      const session = sessionData.session;
      const user = sessionData.user;

      if (!session || !user) {
        throw new Error('Invalid session or user data received');
      }

      // Store session securely
      await this.secureStorage.storeSupabaseSession(session);
      
      // Update current state
      this.currentSession = session;
      this.currentUser = user;

      // Create API key for the user (same as VS Code extension)
      const apiKey = await this.createApiKeyForUser(user);
      
      if (apiKey) {
        await this.secureStorage.storeApiKeySecure(apiKey);
      }

      console.log('VoiceHype: Authentication completed successfully');
      
      // Notify other components
      await this.messaging.broadcast('auth_success', {
        user: this.getUserProfile(),
        hasApiKey: !!apiKey
      });

      return {
        success: true,
        user: this.getUserProfile(),
        hasApiKey: !!apiKey
      };
      
    } catch (error) {
      console.error('VoiceHype: Failed to process OAuth success:', error);
      throw error;
    }
  }

  // ==================== API KEY MANAGEMENT ====================

  /**
   * Create API key for authenticated user (same pattern as VS Code extension)
   */
  async createApiKeyForUser(user) {
    try {
      console.log('VoiceHype: Creating API key for user...');
      
      // Call the web extension auth edge function
      const response = await fetch(`${this.config.get('api.supabaseUrl')}/functions/v1/web-extension-auth`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.currentSession.access_token}`,
          'apikey': this.config.get('api.supabaseAnonKey')
        },
        body: JSON.stringify({
          user_id: user.id,
          email: user.email,
          client_type: 'web_extension'
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('VoiceHype: API key creation failed:', errorText);
        return null;
      }

      const result = await response.json();
      
      if (result.success && result.api_key) {
        console.log('VoiceHype: API key created successfully');
        return result.api_key;
      }

      console.warn('VoiceHype: No API key in response');
      return null;
      
    } catch (error) {
      console.error('VoiceHype: Failed to create API key:', error);
      return null;
    }
  }

  /**
   * Validate current API key
   */
  async validateApiKey() {
    try {
      const apiKey = await this.secureStorage.getApiKeySecure();
      
      if (!apiKey) {
        return false;
      }

      // Use the same validation endpoint as VS Code extension
      const response = await fetch(`${this.config.get('api.supabaseUrl')}/functions/v1/validate-api-key`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`,
          'apikey': apiKey
        }
      });

      return response.ok;
      
    } catch (error) {
      console.error('VoiceHype: API key validation failed:', error);
      return false;
    }
  }

  // ==================== SESSION MANAGEMENT ====================

  /**
   * Load stored session on startup
   */
  async loadStoredSession() {
    try {
      const session = await this.secureStorage.getSupabaseSession();
      
      if (session) {
        console.log('VoiceHype: Loading stored session...');
        
        // Verify session is still valid
        const { data, error } = await this.supabase.auth.setSession({
          access_token: session.access_token,
          refresh_token: session.refresh_token
        });

        if (error) {
          console.warn('VoiceHype: Stored session invalid:', error.message);
          await this.secureStorage.clearSupabaseSession();
          return;
        }

        this.currentSession = data.session;
        this.currentUser = data.user;
        
        console.log('VoiceHype: Session loaded successfully');
      }
      
    } catch (error) {
      console.error('VoiceHype: Failed to load stored session:', error);
      await this.secureStorage.clearSupabaseSession();
    }
  }

  /**
   * Setup auth state change listener
   */
  setupAuthStateListener() {
    this.supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('VoiceHype: Auth state changed:', event);
      
      try {
        if (event === 'SIGNED_IN' && session) {
          this.currentSession = session;
          this.currentUser = session.user;
          await this.secureStorage.storeSupabaseSession(session);
          
        } else if (event === 'SIGNED_OUT') {
          this.currentSession = null;
          this.currentUser = null;
          await this.secureStorage.clearSupabaseSession();
          await this.secureStorage.clearApiKeySecure();
          
        } else if (event === 'TOKEN_REFRESHED' && session) {
          this.currentSession = session;
          await this.secureStorage.storeSupabaseSession(session);
        }
        
        // Notify other components
        await this.messaging.broadcast('auth_state_changed', {
          event,
          user: this.getUserProfile(),
          authenticated: this.isAuthenticated()
        });
        
      } catch (error) {
        console.error('VoiceHype: Auth state change handler error:', error);
      }
    });
  }

  /**
   * Sign out user
   */
  async signOut() {
    this.ensureInitialized();
    
    try {
      console.log('VoiceHype: Signing out user...');
      
      // Sign out from Supabase
      const { error } = await this.supabase.auth.signOut();
      
      if (error) {
        console.warn('VoiceHype: Supabase sign out error:', error.message);
      }

      // Clear all stored data
      await this.secureStorage.clearSupabaseSession();
      await this.secureStorage.clearApiKeySecure();
      
      // Clear OAuth state
      await this.browser.removeStorage('vh_oauth_state');
      await this.browser.removeStorage('vh_oauth_provider');
      
      // Reset current state
      this.currentSession = null;
      this.currentUser = null;
      
      console.log('VoiceHype: User signed out successfully');
      
      return { success: true };
      
    } catch (error) {
      console.error('VoiceHype: Sign out failed:', error);
      throw error;
    }
  }

  // ==================== USER DATA ====================

  /**
   * Get current user profile
   */
  getUserProfile() {
    if (!this.currentUser) {
      return null;
    }

    return {
      id: this.currentUser.id,
      email: this.currentUser.email,
      fullName: this.currentUser.user_metadata?.full_name || this.currentUser.user_metadata?.name,
      avatarUrl: this.currentUser.user_metadata?.avatar_url || this.currentUser.user_metadata?.picture,
      provider: this.currentUser.app_metadata?.provider,
      emailVerified: this.currentUser.email_confirmed_at !== null,
      createdAt: this.currentUser.created_at,
      lastSignIn: this.currentUser.last_sign_in_at
    };
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated() {
    return !!(this.currentSession && this.currentUser);
  }

  /**
   * Get current session
   */
  getSession() {
    return this.currentSession;
  }

  /**
   * Get authentication status
   */
  async getAuthStatus() {
    const hasApiKey = await this.secureStorage.getApiKeySecure();
    
    return {
      authenticated: this.isAuthenticated(),
      user: this.getUserProfile(),
      hasApiKey: !!hasApiKey,
      session: this.currentSession ? {
        expiresAt: this.currentSession.expires_at,
        tokenType: this.currentSession.token_type
      } : null
    };
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Generate secure state parameter for OAuth
   */
  generateState() {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array).map(b => b.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Ensure service is initialized
   */
  ensureInitialized() {
    if (!this.isInitialized) {
      throw new Error('Authentication service not initialized. Call initialize() first.');
    }
  }

  /**
   * Get service status
   */
  getStatus() {
    return {
      initialized: this.isInitialized,
      authenticated: this.isAuthenticated(),
      hasSession: !!this.currentSession,
      hasUser: !!this.currentUser,
      providers: this.oauthConfig.providers
    };
  }
}

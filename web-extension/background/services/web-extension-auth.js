/**
 * Web Extension Auth Flow - Browser extension specific authentication
 * <PERSON><PERSON><PERSON><PERSON> rahmanir raheem
 * 
 * Handles the complete authentication flow for web extensions
 * including popup management and token exchange
 */

export class WebExtensionAuth {
  constructor(authService, browserAdapter, messagingAdapter) {
    this.authService = authService;
    this.browser = browserAdapter;
    this.messaging = messagingAdapter;
    
    this.authPopup = null;
    this.authPromise = null;
    this.isAuthInProgress = false;
  }

  // ==================== PUBLIC AUTH METHODS ====================

  /**
   * Start authentication flow
   */
  async authenticate(provider = 'google') {
    if (this.isAuthInProgress) {
      throw new Error('Authentication already in progress');
    }

    try {
      this.isAuthInProgress = true;
      console.log(`VoiceHype: Starting web extension auth with ${provider}...`);

      // Check if already authenticated
      const authStatus = await this.authService.getAuthStatus();
      if (authStatus.authenticated && authStatus.hasApiKey) {
        console.log('VoiceHype: User already authenticated');
        return {
          success: true,
          user: authStatus.user,
          alreadyAuthenticated: true
        };
      }

      // Start OAuth flow with popup
      const result = await this.startOAuthPopupFlow(provider);

      console.log('VoiceHype: Web extension authentication completed');
      return result;

    } catch (error) {
      console.error('VoiceHype: Web extension authentication failed:', error);
      throw error;
    } finally {
      this.isAuthInProgress = false;
      this.authPopup = null;
    }
  }

  /**
   * Start OAuth flow with popup window
   */
  async startOAuthPopupFlow(provider = 'google') {
    try {
      // Get browser info
      const browserInfo = this.browser.browserInfo;
      const extensionId = this.browser.getExtensionId();

      // Construct auth URL
      const authUrl = `https://voicehype.netlify.app/web-extension-auth?browser=${browserInfo.name}&extension_id=${extensionId}&provider=${provider}`;

      console.log('VoiceHype: Opening auth popup:', authUrl);

      // Open popup window
      this.authPopup = await this.browser.createTab({
        url: authUrl,
        active: true
      });

      // Wait for authentication to complete
      return await this.waitForAuthCompletion();

    } catch (error) {
      console.error('VoiceHype: OAuth popup flow failed:', error);
      throw error;
    }
  }

  /**
   * Wait for authentication completion
   */
  async waitForAuthCompletion() {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Authentication timeout'));
      }, 300000); // 5 minutes timeout

      // Listen for auth completion message
      const messageListener = (message, sender, sendResponse) => {
        if (message.type === 'STORE_AUTH_CREDENTIALS') {
          clearTimeout(timeout);
          chrome.runtime.onMessage.removeListener(messageListener);

          this.handleAuthCredentials(message.data)
            .then(result => resolve(result))
            .catch(error => reject(error));
        }
      };

      chrome.runtime.onMessage.addListener(messageListener);

      // Also listen for popup close
      if (this.authPopup) {
        const checkClosed = setInterval(() => {
          this.browser.getTab(this.authPopup.id).then(tab => {
            if (!tab) {
              clearInterval(checkClosed);
              clearTimeout(timeout);
              chrome.runtime.onMessage.removeListener(messageListener);
              reject(new Error('Authentication popup was closed'));
            }
          }).catch(() => {
            // Tab doesn't exist anymore
            clearInterval(checkClosed);
            clearTimeout(timeout);
            chrome.runtime.onMessage.removeListener(messageListener);
            reject(new Error('Authentication popup was closed'));
          });
        }, 1000);
      }
    });
  }

  /**
   * Handle received auth credentials
   */
  async handleAuthCredentials(authData) {
    try {
      console.log('VoiceHype: Handling auth credentials...');

      // Store user data
      await this.authService.storeUserData(authData.user);

      // Store API key securely
      await this.authService.storeApiKey(authData.apiKey);

      // Store session data if available
      if (authData.session) {
        await this.authService.storeSessionData(authData.session);
      }

      // Close auth popup
      if (this.authPopup) {
        await this.browser.closeTab(this.authPopup.id);
        this.authPopup = null;
      }

      // Notify components of successful auth
      await this.messaging.broadcast('auth_completed', {
        user: authData.user,
        timestamp: authData.timestamp
      });

      return {
        success: true,
        user: authData.user,
        apiKey: authData.apiKey
      };

    } catch (error) {
      console.error('VoiceHype: Error handling auth credentials:', error);
      throw error;
    }
  }

  /**
   * Sign out user
   */
  async signOut() {
    try {
      console.log('VoiceHype: Starting sign out...');
      
      // Close any open auth popup
      if (this.authPopup) {
        await this.browser.closeTab(this.authPopup);
        this.authPopup = null;
      }

      // Sign out from auth service
      const result = await this.authService.signOut();
      
      // Notify all components
      await this.messaging.broadcast('auth_signed_out', {
        timestamp: new Date().toISOString()
      });

      console.log('VoiceHype: Sign out completed');
      return result;
      
    } catch (error) {
      console.error('VoiceHype: Sign out failed:', error);
      throw error;
    }
  }

  /**
   * Check authentication status
   */
  async checkAuthStatus() {
    try {
      const authStatus = await this.authService.getAuthStatus();
      
      // Validate API key if present
      if (authStatus.hasApiKey) {
        const isValidKey = await this.authService.validateApiKey();
        authStatus.apiKeyValid = isValidKey;
        
        if (!isValidKey) {
          console.warn('VoiceHype: API key validation failed');
          authStatus.hasApiKey = false;
        }
      }

      return authStatus;
      
    } catch (error) {
      console.error('VoiceHype: Failed to check auth status:', error);
      return {
        authenticated: false,
        user: null,
        hasApiKey: false,
        apiKeyValid: false,
        error: error.message
      };
    }
  }

  /**
   * Refresh authentication if needed
   */
  async refreshAuth() {
    try {
      console.log('VoiceHype: Refreshing authentication...');
      
      const session = this.authService.getSession();
      if (!session) {
        throw new Error('No session to refresh');
      }

      // Supabase client handles token refresh automatically
      // We just need to check if the session is still valid
      const authStatus = await this.checkAuthStatus();
      
      if (!authStatus.authenticated) {
        throw new Error('Session refresh failed');
      }

      console.log('VoiceHype: Authentication refreshed successfully');
      return authStatus;
      
    } catch (error) {
      console.error('VoiceHype: Auth refresh failed:', error);
      
      // Clear invalid session
      await this.authService.signOut();
      throw error;
    }
  }

  // ==================== POPUP MANAGEMENT ====================

  /**
   * Create authentication popup window
   */
  async createAuthPopup(url) {
    try {
      console.log('VoiceHype: Creating auth popup...');
      
      // Close existing popup if any
      if (this.authPopup) {
        await this.browser.closeTab(this.authPopup);
      }

      // Create new popup tab
      const tab = await this.browser.createTab({
        url: url,
        active: true
      });

      this.authPopup = tab.id;
      
      // Setup popup monitoring
      this.setupPopupMonitoring(tab.id);
      
      return tab;
      
    } catch (error) {
      console.error('VoiceHype: Failed to create auth popup:', error);
      throw error;
    }
  }

  /**
   * Setup popup monitoring for auth completion
   */
  setupPopupMonitoring(tabId) {
    // Monitor tab closure
    const tabRemovedListener = (removedTabId) => {
      if (removedTabId === tabId) {
        console.log('VoiceHype: Auth popup closed by user');
        this.authPopup = null;
        this.browser.api.tabs.onRemoved.removeListener(tabRemovedListener);
        
        // Reject auth promise if still pending
        if (this.authPromise) {
          this.authPromise.reject(new Error('Authentication cancelled by user'));
          this.authPromise = null;
        }
      }
    };

    this.browser.api.tabs.onRemoved.addListener(tabRemovedListener);
  }

  /**
   * Close authentication popup
   */
  async closeAuthPopup() {
    if (this.authPopup) {
      try {
        await this.browser.closeTab(this.authPopup);
        console.log('VoiceHype: Auth popup closed');
      } catch (error) {
        console.warn('VoiceHype: Failed to close auth popup:', error);
      } finally {
        this.authPopup = null;
      }
    }
  }

  // ==================== AUTH STATE MANAGEMENT ====================

  /**
   * Handle authentication success
   */
  async handleAuthSuccess(authData) {
    try {
      console.log('VoiceHype: Handling authentication success...');
      
      // Close popup
      await this.closeAuthPopup();
      
      // Notify components
      await this.messaging.broadcast('auth_success', {
        user: authData.user,
        hasApiKey: authData.hasApiKey,
        timestamp: new Date().toISOString()
      });

      // Show success notification
      await this.browser.showNotification({
        message: `Welcome to VoiceHype, ${authData.user?.fullName || authData.user?.email}!`,
        type: 'basic'
      });

      return authData;
      
    } catch (error) {
      console.error('VoiceHype: Failed to handle auth success:', error);
      throw error;
    }
  }

  /**
   * Handle authentication error
   */
  async handleAuthError(error) {
    try {
      console.error('VoiceHype: Handling authentication error:', error);
      
      // Close popup
      await this.closeAuthPopup();
      
      // Notify components
      await this.messaging.broadcast('auth_error', {
        error: error.message,
        timestamp: new Date().toISOString()
      });

      // Show error notification
      await this.browser.showNotification({
        message: `Authentication failed: ${error.message}`,
        type: 'basic'
      });

      throw error;
      
    } catch (notificationError) {
      console.error('VoiceHype: Failed to handle auth error:', notificationError);
      throw error; // Throw original error
    }
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Get authentication providers
   */
  getAvailableProviders() {
    return [
      {
        id: 'google',
        name: 'Google',
        icon: 'https://developers.google.com/identity/images/g-logo.png'
      },
      {
        id: 'github',
        name: 'GitHub',
        icon: 'https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png'
      }
    ];
  }

  /**
   * Check if authentication is in progress
   */
  isAuthenticationInProgress() {
    return this.isAuthInProgress;
  }

  /**
   * Get current authentication popup status
   */
  getPopupStatus() {
    return {
      hasPopup: !!this.authPopup,
      popupId: this.authPopup,
      isAuthInProgress: this.isAuthInProgress
    };
  }

  /**
   * Cleanup resources
   */
  async cleanup() {
    try {
      console.log('VoiceHype: Cleaning up web extension auth...');
      
      // Close popup
      await this.closeAuthPopup();
      
      // Reset state
      this.authPromise = null;
      this.isAuthInProgress = false;
      
      console.log('VoiceHype: Web extension auth cleanup complete');
      
    } catch (error) {
      console.error('VoiceHype: Auth cleanup failed:', error);
    }
  }

  /**
   * Get authentication flow status
   */
  getAuthFlowStatus() {
    return {
      isInProgress: this.isAuthInProgress,
      hasPopup: !!this.authPopup,
      popupId: this.authPopup,
      availableProviders: this.getAvailableProviders(),
      authServiceStatus: this.authService.getStatus()
    };
  }
}

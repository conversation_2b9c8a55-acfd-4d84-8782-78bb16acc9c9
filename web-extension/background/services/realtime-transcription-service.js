/**
 * Real-time Transcription Service - WebSocket connection for live transcription
 * <PERSON><PERSON><PERSON><PERSON> rahmanir raheem
 * 
 * Handles real-time transcription using WebSocket connection to Express.js server
 * Following the same patterns as VS Code extension
 */

export class RealtimeTranscriptionService {
  constructor(configManager, secureStorage, messagingAdapter) {
    this.config = configManager;
    this.secureStorage = secureStorage;
    this.messaging = messagingAdapter;
    
    // WebSocket connection
    this.ws = null;
    this.isConnected = false;
    this.isConnecting = false;
    this.connectionAttempts = 0;
    this.maxConnectionAttempts = 3;
    this.reconnectDelay = 2000;
    
    // Transcription state
    this.currentTranscript = '';
    this.partialTranscript = '';
    this.isTranscribing = false;
    this.lastMessageTime = null;

    // Graceful shutdown tracking (enhanced from VS Code extension)
    this.pendingChunks = new Map(); // Map<chunkId, { timestamp, retryCount }>
    this.chunkCounter = 0;
    this.isShuttingDown = false;
    this.shutdownTimeout = null;
    this.allChunksProcessed = false;

    // Constants for graceful shutdown
    this.MAX_SHUTDOWN_WAIT = 10000; // 10 seconds max wait
    this.MAX_CHUNK_RETRIES = 3;
    this.CHUNK_TIMEOUT = 3000; // 3 seconds per chunk timeout
    
    // Connection monitoring
    this.heartbeatInterval = null;
    this.connectionTimeout = null;
    this.messageTimeout = 30000; // 30 seconds timeout for messages
    
    // Status tracking (same as VS Code extension)
    this.status = 'disconnected'; // disconnected, connecting, connected, transcribing, error
    this.statusListeners = [];
  }

  // ==================== CONNECTION MANAGEMENT ====================

  /**
   * Connect to real-time transcription WebSocket (same pattern as VS Code extension)
   */
  async connect(options = {}) {
    if (this.isConnecting || this.isConnected) {
      console.log('VoiceHype: Already connecting or connected to real-time transcription');
      return;
    }

    try {
      this.isConnecting = true;
      this.connectionAttempts = 0;
      
      console.log('VoiceHype: Starting real-time transcription connection...');
      this.emitStatus('connecting', 'Connecting to real-time transcription service...');

      // Get API key
      const apiKey = await this.secureStorage.getApiKeySecure();
      if (!apiKey) {
        throw new Error('No API key found. Please authenticate first.');
      }

      // Get transcription settings
      const transcriptionConfig = this.config.getSection('transcription');
      const audioConfig = this.config.getSection('audio');
      
      const service = options.service || transcriptionConfig.service || 'assemblyai';
      const model = options.model || transcriptionConfig.model || 'best';
      const language = options.language || transcriptionConfig.language || 'en';
      const sampleRate = options.sampleRate || audioConfig.sampleRate || 16000;

      console.log('VoiceHype: Real-time transcription settings:', {
        service, model, language, sampleRate
      });

      // Attempt connection
      await this.attemptConnection(apiKey, service, model, language, sampleRate);

    } catch (error) {
      console.error('VoiceHype: Failed to connect to real-time transcription:', error);
      this.isConnecting = false;
      this.emitStatus('error', error.message);
      throw error;
    }
  }

  /**
   * Attempt WebSocket connection (same pattern as VS Code extension)
   */
  async attemptConnection(apiKey, service, model, language, sampleRate) {
    return new Promise((resolve, reject) => {
      try {
        this.connectionAttempts++;
        console.log(`VoiceHype: Connection attempt ${this.connectionAttempts}/${this.maxConnectionAttempts}`);

        // Build WebSocket URL (same as VS Code extension)
        const serverUrl = '***************:3001'; // Your DigitalOcean server
        const encodedApiKey = encodeURIComponent(apiKey);
        const encodedLanguage = encodeURIComponent(language);
        
        const wsUrl = `ws://${serverUrl}/realtime?apiKey=${encodedApiKey}&service=${service}&model=${model}&language=${encodedLanguage}&sampleRate=${sampleRate}`;
        
        // Log URL with masked API key
        const safeUrl = wsUrl.replace(encodedApiKey, '***API_KEY_HIDDEN***');
        console.log(`VoiceHype: Connecting to WebSocket: ${safeUrl}`);

        // Create WebSocket connection
        this.ws = new WebSocket(wsUrl);

        // Set connection timeout
        this.connectionTimeout = setTimeout(() => {
          console.error('VoiceHype: Connection timeout after 15 seconds');
          this.emitStatus('error', 'Connection timeout after 15 seconds');
          this.cleanup();
          reject(new Error('Connection timeout'));
        }, 15000);

        // Setup WebSocket event handlers
        this.setupWebSocketHandlers(resolve, reject);

      } catch (error) {
        console.error('VoiceHype: Error creating WebSocket:', error);
        this.handleConnectionError(error, resolve, reject);
      }
    });
  }

  /**
   * Setup WebSocket event handlers (same pattern as VS Code extension)
   */
  setupWebSocketHandlers(resolve, reject) {
    this.ws.onopen = () => {
      console.log('VoiceHype: WebSocket connection opened');
      
      // Clear connection timeout
      if (this.connectionTimeout) {
        clearTimeout(this.connectionTimeout);
        this.connectionTimeout = null;
      }

      this.isConnected = true;
      this.isConnecting = false;
      this.connectionAttempts = 0;
      this.lastMessageTime = Date.now();
      
      this.emitStatus('connected', 'Connected to real-time transcription service');
      
      // Start heartbeat monitoring
      this.startHeartbeat();
      
      resolve();
    };

    this.ws.onmessage = (event) => {
      try {
        this.lastMessageTime = Date.now();
        const message = JSON.parse(event.data);
        
        console.log('VoiceHype: WebSocket message received:', {
          type: message.type,
          hasText: !!message.text,
          textLength: message.text?.length || 0
        });

        this.handleTranscriptionMessage(message);

      } catch (error) {
        console.error('VoiceHype: Error parsing WebSocket message:', error);
      }
    };

    this.ws.onerror = (error) => {
      console.error('VoiceHype: WebSocket error:', error);
      this.handleConnectionError(error, resolve, reject);
    };

    this.ws.onclose = (event) => {
      console.log('VoiceHype: WebSocket connection closed:', {
        code: event.code,
        reason: event.reason,
        wasClean: event.wasClean
      });

      this.isConnected = false;
      this.isConnecting = false;
      
      // Clear heartbeat
      this.stopHeartbeat();
      
      if (event.code !== 1000) { // Not a normal closure
        this.emitStatus('error', `Connection closed unexpectedly: ${event.reason || 'Unknown reason'}`);
      } else {
        this.emitStatus('disconnected', 'Disconnected from real-time transcription service');
      }
    };
  }

  /**
   * Handle transcription messages (same pattern as VS Code extension)
   */
  async handleTranscriptionMessage(message) {
    try {
      switch (message.type) {
        case 'partial':
          this.partialTranscript = message.text || '';
          this.emitStatus('transcribing', 'Transcribing...');

          // Mark chunk as processed for partial transcripts
          this.markChunkProcessed();

          // Notify components of partial transcript
          await this.messaging.broadcast('realtime_partial', {
            text: this.partialTranscript,
            timestamp: Date.now()
          });
          break;

        case 'final':
          const finalText = message.text || '';
          this.currentTranscript += (this.currentTranscript ? ' ' : '') + finalText;
          this.partialTranscript = '';

          console.log('VoiceHype: Final transcript received:', finalText);

          // Mark chunk as processed since we got a final transcript
          this.markChunkProcessed();

          // Notify components of final transcript
          await this.messaging.broadcast('realtime_final', {
            text: finalText,
            fullTranscript: this.currentTranscript,
            timestamp: Date.now()
          });
          break;

        case 'error':
          console.error('VoiceHype: Transcription error:', message.error);
          this.emitStatus('error', message.error || 'Transcription error');
          break;

        case 'session_begins':
          console.log('VoiceHype: Transcription session started');
          this.isTranscribing = true;
          this.currentTranscript = '';
          this.partialTranscript = '';
          this.emitStatus('transcribing', 'Transcription session started');
          break;

        case 'session_terminated':
          console.log('VoiceHype: Transcription session ended');
          this.isTranscribing = false;
          this.emitStatus('connected', 'Transcription session ended');
          break;

        default:
          console.log('VoiceHype: Unknown message type:', message.type);
      }

    } catch (error) {
      console.error('VoiceHype: Error handling transcription message:', error);
    }
  }

  // ==================== AUDIO STREAMING ====================

  /**
   * Send audio chunk for transcription (enhanced with pending chunk tracking)
   */
  sendAudioChunk(audioChunk) {
    if (!this.isConnected || !this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.warn('VoiceHype: Cannot send audio chunk - WebSocket not connected');
      return false;
    }

    try {
      // Assign chunk ID for tracking
      const chunkId = ++this.chunkCounter;

      // Track this chunk as pending (unless we're already shutting down)
      if (!this.isShuttingDown) {
        this.pendingChunks.set(chunkId, {
          timestamp: Date.now(),
          retryCount: 0
        });
      }

      // Send raw audio data (same as VS Code extension)
      this.ws.send(audioChunk.data);

      // Only log occasional chunks to avoid flooding logs
      if (Math.random() < 0.05) {
        console.log(`VoiceHype: Sent audio chunk #${chunkId}, size: ${audioChunk.data.length} bytes, pending: ${this.pendingChunks.size}`);
      }

      return true;

    } catch (error) {
      console.error('VoiceHype: Error sending audio chunk:', error);
      return false;
    }
  }

  /**
   * Send final audio chunk to end transcription and start graceful shutdown
   */
  sendFinalChunk() {
    if (!this.isConnected || !this.ws || this.ws.readyState !== WebSocket.OPEN) {
      return false;
    }

    try {
      // Send empty buffer to signal end of audio
      this.ws.send(new ArrayBuffer(0));
      console.log(`VoiceHype: Sent final audio chunk, starting graceful shutdown with ${this.pendingChunks.size} pending chunks`);

      // Start graceful shutdown process
      this.startGracefulShutdown();

      return true;

    } catch (error) {
      console.error('VoiceHype: Error sending final chunk:', error);
      return false;
    }
  }

  // ==================== CONNECTION MONITORING ====================

  /**
   * Start heartbeat monitoring
   */
  startHeartbeat() {
    this.stopHeartbeat(); // Clear any existing heartbeat
    
    this.heartbeatInterval = setInterval(() => {
      if (!this.isConnected) {
        this.stopHeartbeat();
        return;
      }

      // Check if we've received messages recently
      const timeSinceLastMessage = Date.now() - this.lastMessageTime;
      if (timeSinceLastMessage > this.messageTimeout) {
        console.warn('VoiceHype: No messages received for 30 seconds, connection may be stale');
        this.emitStatus('error', 'Connection timeout - no response from server');
        this.disconnect();
      }
    }, 10000); // Check every 10 seconds
  }

  /**
   * Stop heartbeat monitoring
   */
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * Handle connection errors
   */
  handleConnectionError(error, resolve, reject) {
    console.error('VoiceHype: Connection error:', error);
    
    this.isConnected = false;
    this.isConnecting = false;
    
    // Clear timeouts
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout);
      this.connectionTimeout = null;
    }
    
    this.stopHeartbeat();
    
    // Attempt reconnection if we haven't exceeded max attempts
    if (this.connectionAttempts < this.maxConnectionAttempts) {
      console.log(`VoiceHype: Retrying connection in ${this.reconnectDelay}ms...`);
      
      setTimeout(() => {
        if (resolve && reject) {
          this.attemptConnection(...arguments).then(resolve).catch(reject);
        }
      }, this.reconnectDelay);
      
      this.reconnectDelay *= 2; // Exponential backoff
    } else {
      this.emitStatus('error', 'Failed to connect after multiple attempts');
      if (reject) {
        reject(error);
      }
    }
  }

  // ==================== CONTROL METHODS ====================

  /**
   * Disconnect from real-time transcription
   */
  async disconnect() {
    try {
      console.log('VoiceHype: Disconnecting from real-time transcription...');
      
      this.isConnecting = false;
      this.isTranscribing = false;
      
      // Send final chunk if connected
      if (this.isConnected) {
        this.sendFinalChunk();
      }
      
      // Cleanup connection
      await this.cleanup();
      
      this.emitStatus('disconnected', 'Disconnected from real-time transcription service');
      
      console.log('VoiceHype: Real-time transcription disconnected');

    } catch (error) {
      console.error('VoiceHype: Error disconnecting:', error);
    }
  }

  /**
   * Cleanup connection resources
   */
  async cleanup() {
    try {
      // Stop heartbeat
      this.stopHeartbeat();
      
      // Clear timeouts
      if (this.connectionTimeout) {
        clearTimeout(this.connectionTimeout);
        this.connectionTimeout = null;
      }

      // Clear graceful shutdown timeout
      if (this.shutdownTimeout) {
        clearTimeout(this.shutdownTimeout);
        this.shutdownTimeout = null;
      }

      // Close WebSocket
      if (this.ws) {
        if (this.ws.readyState === WebSocket.OPEN || this.ws.readyState === WebSocket.CONNECTING) {
          this.ws.close(1000, 'Normal closure');
        }
        this.ws = null;
      }

      // Reset state
      this.isConnected = false;
      this.isConnecting = false;
      this.isTranscribing = false;
      this.connectionAttempts = 0;
      this.reconnectDelay = 2000;

      // Reset graceful shutdown state
      this.pendingChunks.clear();
      this.chunkCounter = 0;
      this.isShuttingDown = false;
      this.allChunksProcessed = false;
      
      console.log('VoiceHype: Real-time transcription cleanup complete');

    } catch (error) {
      console.error('VoiceHype: Error during cleanup:', error);
    }
  }

  // ==================== STATUS MANAGEMENT ====================

  /**
   * Emit status change (same pattern as VS Code extension)
   */
  emitStatus(status, message = '') {
    this.status = status;
    console.log(`VoiceHype: Real-time transcription status - ${status}${message ? `: ${message}` : ''}`);
    
    // Notify status listeners
    this.statusListeners.forEach(listener => {
      try {
        listener(status, message);
      } catch (error) {
        console.error('VoiceHype: Error in status listener:', error);
      }
    });
    
    // Broadcast to components
    this.messaging.broadcast('realtime_status', {
      status: status,
      message: message,
      timestamp: Date.now()
    });
  }

  /**
   * Add status listener
   */
  addStatusListener(listener) {
    this.statusListeners.push(listener);
  }

  /**
   * Remove status listener
   */
  removeStatusListener(listener) {
    const index = this.statusListeners.indexOf(listener);
    if (index > -1) {
      this.statusListeners.splice(index, 1);
    }
  }

  /**
   * Get current status
   */
  getStatus() {
    return {
      status: this.status,
      isConnected: this.isConnected,
      isConnecting: this.isConnecting,
      isTranscribing: this.isTranscribing,
      currentTranscript: this.currentTranscript,
      partialTranscript: this.partialTranscript,
      connectionAttempts: this.connectionAttempts,
      lastMessageTime: this.lastMessageTime
    };
  }

  /**
   * Get current transcript
   */
  getCurrentTranscript() {
    return {
      full: this.currentTranscript,
      partial: this.partialTranscript,
      combined: this.currentTranscript + (this.partialTranscript ? ' ' + this.partialTranscript : '')
    };
  }

  // ==================== GRACEFUL SHUTDOWN WITH PENDING CHUNK TRACKING ====================

  /**
   * Start graceful shutdown process - wait for pending chunks to be processed
   */
  startGracefulShutdown() {
    if (this.isShuttingDown) {
      console.log('VoiceHype: Graceful shutdown already in progress');
      return;
    }

    this.isShuttingDown = true;
    const pendingCount = this.pendingChunks.size;

    console.log(`VoiceHype: Starting graceful shutdown with ${pendingCount} pending chunks`);
    this.emitStatus('shutting_down', `Waiting for ${pendingCount} pending chunks...`);

    // Set maximum wait timeout
    this.shutdownTimeout = setTimeout(() => {
      console.log(`VoiceHype: Graceful shutdown timeout after ${this.MAX_SHUTDOWN_WAIT}ms, forcing completion`);
      this.forceShutdownCompletion('Timeout waiting for pending chunks');
    }, this.MAX_SHUTDOWN_WAIT);

    // Start monitoring pending chunks
    this.monitorPendingChunks();

    // Send close message to server
    this.sendCloseMessage();

    // If no pending chunks, complete immediately
    if (pendingCount === 0) {
      console.log('VoiceHype: No pending chunks, completing shutdown immediately');
      this.completeGracefulShutdown();
    }
  }

  /**
   * Monitor pending chunks and handle timeouts/retries
   */
  monitorPendingChunks() {
    const checkInterval = setInterval(() => {
      if (!this.isShuttingDown) {
        clearInterval(checkInterval);
        return;
      }

      const now = Date.now();
      const chunksToRetry = [];
      const chunksToRemove = [];

      // Check each pending chunk for timeout
      this.pendingChunks.forEach((chunkInfo, chunkId) => {
        const age = now - chunkInfo.timestamp;

        if (age > this.CHUNK_TIMEOUT) {
          if (chunkInfo.retryCount < this.MAX_CHUNK_RETRIES) {
            // Mark for retry
            chunksToRetry.push(chunkId);
            console.log(`VoiceHype: Chunk #${chunkId} timed out after ${age}ms, retry ${chunkInfo.retryCount + 1}/${this.MAX_CHUNK_RETRIES}`);
          } else {
            // Max retries reached, give up on this chunk
            chunksToRemove.push(chunkId);
            console.log(`VoiceHype: Chunk #${chunkId} failed after ${this.MAX_CHUNK_RETRIES} retries, giving up`);
          }
        }
      });

      // Handle retries (in real implementation, we'd need to resend the audio data)
      chunksToRetry.forEach(chunkId => {
        const chunkInfo = this.pendingChunks.get(chunkId);
        if (chunkInfo) {
          chunkInfo.retryCount++;
          chunkInfo.timestamp = now; // Reset timestamp for retry
          console.log(`VoiceHype: Retrying chunk #${chunkId} (attempt ${chunkInfo.retryCount})`);
          // Note: In a full implementation, we'd resend the actual audio data here
        }
      });

      // Remove failed chunks
      chunksToRemove.forEach(chunkId => {
        this.pendingChunks.delete(chunkId);
      });

      // Check if all chunks are processed
      if (this.pendingChunks.size === 0) {
        console.log('VoiceHype: All pending chunks processed, completing graceful shutdown');
        clearInterval(checkInterval);
        this.completeGracefulShutdown();
      } else {
        console.log(`VoiceHype: Still waiting for ${this.pendingChunks.size} pending chunks`);
        this.emitStatus('shutting_down', `Waiting for ${this.pendingChunks.size} pending chunks...`);
      }
    }, 1000); // Check every second
  }

  /**
   * Send close message to server requesting final transcript
   */
  sendCloseMessage() {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      try {
        console.log('VoiceHype: Sending close message to request final transcript');
        this.ws.send(JSON.stringify({
          type: 'close',
          requestFinalTranscript: true,
          pendingChunks: this.pendingChunks.size
        }));

        this.emitStatus('shutting_down', 'Requesting final transcription...');
      } catch (error) {
        console.error('VoiceHype: Error sending close message:', error);
      }
    }
  }

  /**
   * Complete graceful shutdown successfully
   */
  completeGracefulShutdown() {
    if (this.shutdownTimeout) {
      clearTimeout(this.shutdownTimeout);
      this.shutdownTimeout = null;
    }

    console.log('VoiceHype: Graceful shutdown completed successfully');

    // Wait a bit more for any final transcripts
    setTimeout(() => {
      this.finalizeTranscription('Graceful shutdown completed');
    }, 2000); // Wait 2 seconds for final transcripts
  }

  /**
   * Force shutdown completion when timeout is reached
   */
  forceShutdownCompletion(reason) {
    if (this.shutdownTimeout) {
      clearTimeout(this.shutdownTimeout);
      this.shutdownTimeout = null;
    }

    const remainingChunks = this.pendingChunks.size;
    console.log(`VoiceHype: Forcing shutdown completion - ${reason}, ${remainingChunks} chunks remaining`);

    // Clear pending chunks
    this.pendingChunks.clear();

    this.finalizeTranscription(`Forced completion: ${reason}`);
  }

  /**
   * Finalize transcription and emit completion events
   */
  finalizeTranscription(reason) {
    if (!this.allChunksProcessed) {
      this.allChunksProcessed = true;

      // Use accumulated transcript or partial as fallback
      if (this.currentTranscript.trim() !== '') {
        console.log(`VoiceHype: Using accumulated final transcripts: "${this.currentTranscript.substring(0, 100)}..."`);
        this.emitStatus('completed', 'Using accumulated transcripts');
      }
      else if (this.partialTranscript.trim() !== '') {
        console.log(`VoiceHype: Using last partial transcript: "${this.partialTranscript}"`);
        this.currentTranscript = this.partialTranscript;
        this.emitStatus('completed', 'Using partial transcript');
      }
      else {
        console.log('VoiceHype: No transcription received (user may not have spoken)');
        this.emitStatus('completed', 'No speech detected');
      }
    }

    console.log(`VoiceHype: Transcription finalized - ${reason}`);
  }

  /**
   * Mark a chunk as processed (called when we receive transcription for it)
   */
  markChunkProcessed(chunkId) {
    if (chunkId && this.pendingChunks.has(chunkId)) {
      this.pendingChunks.delete(chunkId);
      console.log(`VoiceHype: Chunk #${chunkId} processed, ${this.pendingChunks.size} remaining`);
    } else {
      // If no specific chunk ID, remove the oldest pending chunk
      const oldestChunk = Array.from(this.pendingChunks.keys())[0];
      if (oldestChunk) {
        this.pendingChunks.delete(oldestChunk);
        console.log(`VoiceHype: Oldest chunk #${oldestChunk} processed, ${this.pendingChunks.size} remaining`);
      }
    }

    // If we're shutting down and no more pending chunks, complete shutdown
    if (this.isShuttingDown && this.pendingChunks.size === 0) {
      console.log('VoiceHype: Last pending chunk processed during shutdown');
      this.completeGracefulShutdown();
    }
  }
}

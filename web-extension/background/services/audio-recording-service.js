/**
 * Audio Recording Service - MediaRecorder API with real-time transcription
 * Bismillahir rahmanir raheem
 * 
 * Handles audio recording using browser MediaRecorder API
 * Following the same patterns as VS Code extension
 */

export class AudioRecordingService {
  constructor(configManager, messagingAdapter, browserAdapter) {
    this.config = configManager;
    this.messaging = messagingAdapter;
    this.browser = browserAdapter;
    
    // Recording state
    this.mediaRecorder = null;
    this.audioStream = null;
    this.audioChunks = [];
    this.isRecording = false;
    this.isPaused = false;
    this.isRealtime = false;
    
    // Real-time transcription
    this.realtimeCallback = null;
    this.chunkCount = 0;
    this.totalBytes = 0;
    this.recordingStartTime = null;
    
    // Audio settings (same as VS Code extension)
    this.defaultAudioSettings = {
      sampleRate: 16000, // Changed from 22050 to 16000 for better real-time support
      channelCount: 1,
      echoCancellation: true,
      noiseSuppression: true,
      autoGainControl: true
    };
  }

  // ==================== RECORDING CONTROL ====================

  /**
   * Start audio recording (same pattern as VS Code extension)
   */
  async startRecording(options = {}) {
    if (this.isRecording) {
      throw new Error('Recording already in progress');
    }

    try {
      console.log('VoiceHype: Starting audio recording...');
      
      // Get audio settings from config
      const audioConfig = this.config.getSection('audio');
      const audioSettings = {
        ...this.defaultAudioSettings,
        sampleRate: audioConfig.sampleRate || this.defaultAudioSettings.sampleRate,
        ...options.audioSettings
      };

      console.log('VoiceHype: Audio settings:', audioSettings);

      // Request microphone access
      this.audioStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: audioSettings.sampleRate,
          channelCount: audioSettings.channelCount,
          echoCancellation: audioSettings.echoCancellation,
          noiseSuppression: audioSettings.noiseSuppression,
          autoGainControl: audioSettings.autoGainControl
        }
      });

      console.log('VoiceHype: Microphone access granted');

      // Set up MediaRecorder with optimal settings
      const mimeType = this.getSupportedMimeType();
      console.log('VoiceHype: Using MIME type:', mimeType);

      this.mediaRecorder = new MediaRecorder(this.audioStream, {
        mimeType: mimeType,
        audioBitsPerSecond: 128000 // 128 kbps for good quality
      });

      // Reset recording state
      this.audioChunks = [];
      this.chunkCount = 0;
      this.totalBytes = 0;
      this.recordingStartTime = Date.now();
      this.isRealtime = options.realtime || false;
      this.realtimeCallback = options.realtimeCallback || null;

      // Set up event handlers (same pattern as VS Code extension)
      this.setupRecordingEventHandlers();

      // Start recording with timeslice for real-time processing
      const timeslice = this.isRealtime ? 100 : 1000; // 100ms for real-time, 1s for regular
      this.mediaRecorder.start(timeslice);
      
      this.isRecording = true;
      this.isPaused = false;

      console.log(`VoiceHype: Recording started with timeslice: ${timeslice}ms, realtime: ${this.isRealtime}`);

      // Notify components
      await this.messaging.broadcast('recording_started', {
        realtime: this.isRealtime,
        audioSettings: audioSettings,
        timestamp: this.recordingStartTime
      });

      return {
        success: true,
        realtime: this.isRealtime,
        audioSettings: audioSettings
      };

    } catch (error) {
      console.error('VoiceHype: Failed to start recording:', error);
      await this.cleanup();
      throw this.handleRecordingError(error);
    }
  }

  /**
   * Stop audio recording
   */
  async stopRecording() {
    if (!this.isRecording) {
      console.warn('VoiceHype: No recording in progress');
      return null;
    }

    try {
      console.log('VoiceHype: Stopping audio recording...');

      return new Promise((resolve, reject) => {
        // Set up stop handler
        const handleStop = async () => {
          try {
            const duration = Date.now() - this.recordingStartTime;
            console.log(`VoiceHype: Recording stopped after ${duration}ms`);

            // Create final audio blob
            const audioBlob = new Blob(this.audioChunks, { 
              type: this.mediaRecorder.mimeType 
            });

            console.log(`VoiceHype: Created audio blob: ${audioBlob.size} bytes, type: ${audioBlob.type}`);

            // Convert to base64 for API calls (same as VS Code extension)
            const base64Audio = await this.blobToBase64(audioBlob);

            const result = {
              success: true,
              audioBlob: audioBlob,
              base64Audio: base64Audio,
              duration: duration,
              size: audioBlob.size,
              mimeType: audioBlob.type,
              chunkCount: this.chunkCount,
              totalBytes: this.totalBytes
            };

            // Cleanup
            await this.cleanup();

            // Notify components
            await this.messaging.broadcast('recording_stopped', result);

            resolve(result);

          } catch (error) {
            console.error('VoiceHype: Error handling recording stop:', error);
            await this.cleanup();
            reject(error);
          }
        };

        // Handle the stop event
        this.mediaRecorder.onstop = handleStop;

        // Stop the recording
        if (this.mediaRecorder.state === 'recording' || this.mediaRecorder.state === 'paused') {
          this.mediaRecorder.stop();
        } else {
          // Already stopped, handle immediately
          handleStop();
        }
      });

    } catch (error) {
      console.error('VoiceHype: Failed to stop recording:', error);
      await this.cleanup();
      throw error;
    }
  }

  /**
   * Pause recording
   */
  async pauseRecording() {
    if (!this.isRecording || this.isPaused) {
      throw new Error('Cannot pause: not recording or already paused');
    }

    try {
      console.log('VoiceHype: Pausing recording...');
      
      this.mediaRecorder.pause();
      this.isPaused = true;

      await this.messaging.broadcast('recording_paused', {
        timestamp: Date.now()
      });

      console.log('VoiceHype: Recording paused');
      return { success: true, paused: true };

    } catch (error) {
      console.error('VoiceHype: Failed to pause recording:', error);
      throw error;
    }
  }

  /**
   * Resume recording
   */
  async resumeRecording() {
    if (!this.isRecording || !this.isPaused) {
      throw new Error('Cannot resume: not recording or not paused');
    }

    try {
      console.log('VoiceHype: Resuming recording...');
      
      this.mediaRecorder.resume();
      this.isPaused = false;

      await this.messaging.broadcast('recording_resumed', {
        timestamp: Date.now()
      });

      console.log('VoiceHype: Recording resumed');
      return { success: true, paused: false };

    } catch (error) {
      console.error('VoiceHype: Failed to resume recording:', error);
      throw error;
    }
  }

  // ==================== EVENT HANDLERS ====================

  /**
   * Setup MediaRecorder event handlers (same pattern as VS Code extension)
   */
  setupRecordingEventHandlers() {
    // Handle data available (audio chunks)
    this.mediaRecorder.ondataavailable = async (event) => {
      if (event.data.size > 0) {
        this.chunkCount++;
        this.totalBytes += event.data.size;
        
        console.log(`VoiceHype: Received audio chunk #${this.chunkCount}, size: ${event.data.size} bytes, total: ${this.totalBytes} bytes`);

        // Store chunk for final audio
        this.audioChunks.push(event.data);

        // Handle real-time transcription (same pattern as VS Code extension)
        if (this.isRealtime && this.realtimeCallback) {
          try {
            // Convert blob to ArrayBuffer for real-time processing
            const arrayBuffer = await event.data.arrayBuffer();
            const audioChunk = {
              data: new Uint8Array(arrayBuffer),
              isLastChunk: false,
              chunkNumber: this.chunkCount,
              timestamp: Date.now()
            };

            // Send to real-time transcription
            this.realtimeCallback(audioChunk);

          } catch (error) {
            console.error('VoiceHype: Error processing real-time chunk:', error);
          }
        }

        // Notify components of chunk received
        await this.messaging.broadcast('recording_chunk', {
          chunkNumber: this.chunkCount,
          size: event.data.size,
          totalBytes: this.totalBytes,
          realtime: this.isRealtime
        });
      }
    };

    // Handle recording errors
    this.mediaRecorder.onerror = async (event) => {
      console.error('VoiceHype: MediaRecorder error:', event.error);
      
      await this.messaging.broadcast('recording_error', {
        error: event.error.message,
        timestamp: Date.now()
      });

      await this.cleanup();
    };

    // Handle state changes
    this.mediaRecorder.onstart = async () => {
      console.log('VoiceHype: MediaRecorder started');
    };

    this.mediaRecorder.onpause = async () => {
      console.log('VoiceHype: MediaRecorder paused');
    };

    this.mediaRecorder.onresume = async () => {
      console.log('VoiceHype: MediaRecorder resumed');
    };
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Get supported MIME type for recording
   */
  getSupportedMimeType() {
    const types = [
      'audio/webm;codecs=opus',
      'audio/webm',
      'audio/mp4',
      'audio/ogg;codecs=opus',
      'audio/ogg',
      'audio/wav'
    ];

    for (const type of types) {
      if (MediaRecorder.isTypeSupported(type)) {
        console.log(`VoiceHype: Using supported MIME type: ${type}`);
        return type;
      }
    }

    console.warn('VoiceHype: No preferred MIME types supported, using default');
    return '';
  }

  /**
   * Convert blob to base64 (same as VS Code extension)
   */
  async blobToBase64(blob) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const base64 = reader.result.split(',')[1]; // Remove data:audio/... prefix
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }

  /**
   * Handle recording errors with user-friendly messages
   */
  handleRecordingError(error) {
    let userMessage = 'Recording failed';
    
    if (error.name === 'NotAllowedError') {
      userMessage = 'Microphone access denied. Please allow microphone access and try again.';
    } else if (error.name === 'NotFoundError') {
      userMessage = 'No microphone found. Please connect a microphone and try again.';
    } else if (error.name === 'NotSupportedError') {
      userMessage = 'Audio recording not supported in this browser.';
    } else if (error.name === 'NotReadableError') {
      userMessage = 'Microphone is already in use by another application.';
    }

    return new Error(userMessage);
  }

  /**
   * Cleanup recording resources
   */
  async cleanup() {
    try {
      console.log('VoiceHype: Cleaning up recording resources...');

      // Stop MediaRecorder if active
      if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
        this.mediaRecorder.stop();
      }

      // Stop audio stream
      if (this.audioStream) {
        this.audioStream.getTracks().forEach(track => {
          track.stop();
          console.log('VoiceHype: Stopped audio track');
        });
        this.audioStream = null;
      }

      // Reset state
      this.mediaRecorder = null;
      this.isRecording = false;
      this.isPaused = false;
      this.isRealtime = false;
      this.realtimeCallback = null;
      this.chunkCount = 0;
      this.totalBytes = 0;
      this.recordingStartTime = null;

      console.log('VoiceHype: Recording cleanup complete');

    } catch (error) {
      console.error('VoiceHype: Error during cleanup:', error);
    }
  }

  // ==================== STATUS METHODS ====================

  /**
   * Get current recording status
   */
  getRecordingStatus() {
    return {
      isRecording: this.isRecording,
      isPaused: this.isPaused,
      isRealtime: this.isRealtime,
      chunkCount: this.chunkCount,
      totalBytes: this.totalBytes,
      duration: this.recordingStartTime ? Date.now() - this.recordingStartTime : 0,
      mediaRecorderState: this.mediaRecorder?.state || 'inactive'
    };
  }

  /**
   * Check if microphone is available
   */
  async checkMicrophoneAvailability() {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      const audioInputs = devices.filter(device => device.kind === 'audioinput');
      
      return {
        available: audioInputs.length > 0,
        devices: audioInputs.map(device => ({
          deviceId: device.deviceId,
          label: device.label || `Microphone ${device.deviceId.substring(0, 8)}`,
          groupId: device.groupId
        }))
      };
      
    } catch (error) {
      console.error('VoiceHype: Failed to check microphone availability:', error);
      return { available: false, devices: [], error: error.message };
    }
  }

  /**
   * Get audio settings
   */
  getAudioSettings() {
    const audioConfig = this.config.getSection('audio');
    return {
      ...this.defaultAudioSettings,
      ...audioConfig
    };
  }
}

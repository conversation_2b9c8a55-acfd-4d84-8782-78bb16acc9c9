/**
 * Text Field Detector
 * Bismillahir rahmanir raheem
 * 
 * Detects text input fields on web pages using Intersection Observer
 * for efficient monitoring and widget injection
 */

class TextFieldDetector {
  constructor() {
    this.detectedFields = new Map()
    this.observer = null
    this.isEnabled = true
    this.settings = {
      enableOnAllSites: true,
      widgetPosition: 'bottom-right'
    }
    
    // Selectors for text input fields
    this.textFieldSelectors = [
      'input[type="text"]',
      'input[type="email"]', 
      'input[type="search"]',
      'input[type="url"]',
      'input[type="tel"]',
      'input:not([type])', // Default input type is text
      'textarea',
      '[contenteditable="true"]',
      '[contenteditable=""]',
      '.ql-editor', // Quill editor
      '.CodeMirror-code', // CodeMirror
      '.monaco-editor', // Monaco editor
      '.ace_editor', // Ace editor
      '[role="textbox"]',
      '[data-testid*="input"]',
      '[data-testid*="textarea"]',
      '[class*="input"]',
      '[class*="textarea"]',
      '[class*="editor"]'
    ]
    
    // Sites to exclude (if enableOnAllSites is false)
    this.excludedSites = [
      'chrome://',
      'chrome-extension://',
      'moz-extension://',
      'about:',
      'file://'
    ]
    
    this.init()
  }

  // ==================== INITIALIZATION ====================

  async init() {
    try {
      // Load settings from extension storage
      await this.loadSettings()
      
      // Check if we should run on this site
      if (!this.shouldRunOnCurrentSite()) {
        console.log('VoiceHype: Skipping site due to settings')
        return
      }
      
      // Set up intersection observer
      this.setupIntersectionObserver()
      
      // Initial scan for existing text fields
      this.scanForTextFields()
      
      // Listen for dynamic content changes
      this.setupMutationObserver()
      
      // Listen for settings changes
      this.setupMessageListener()
      
      console.log('VoiceHype: Text field detector initialized')
      
    } catch (error) {
      console.error('VoiceHype: Error initializing text field detector:', error)
    }
  }

  async loadSettings() {
    try {
      const response = await chrome.runtime.sendMessage({ 
        type: 'GET_SETTINGS' 
      })
      
      if (response.success) {
        Object.assign(this.settings, response.settings)
      }
    } catch (error) {
      console.warn('VoiceHype: Could not load settings, using defaults')
    }
  }

  shouldRunOnCurrentSite() {
    const currentUrl = window.location.href
    
    // Always exclude certain protocols
    if (this.excludedSites.some(site => currentUrl.startsWith(site))) {
      return false
    }
    
    // Check if enabled on all sites
    if (!this.settings.enableOnAllSites) {
      // Could add whitelist logic here
      return false
    }
    
    return true
  }

  // ==================== INTERSECTION OBSERVER ====================

  setupIntersectionObserver() {
    const options = {
      root: null, // Use viewport as root
      rootMargin: '50px', // Detect fields 50px before they enter viewport
      threshold: 0.1 // Trigger when 10% of element is visible
    }
    
    this.observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        const field = entry.target
        const fieldId = this.getFieldId(field)
        
        if (entry.isIntersecting) {
          // Field entered viewport
          if (!this.detectedFields.has(fieldId)) {
            this.onFieldDetected(field)
          }
        } else {
          // Field left viewport
          if (this.detectedFields.has(fieldId)) {
            this.onFieldHidden(field)
          }
        }
      })
    }, options)
  }

  // ==================== FIELD DETECTION ====================

  scanForTextFields() {
    const selector = this.textFieldSelectors.join(', ')
    const fields = document.querySelectorAll(selector)
    
    fields.forEach(field => {
      if (this.isValidTextField(field)) {
        this.observer.observe(field)
      }
    })
    
    console.log(`VoiceHype: Found ${fields.length} potential text fields`)
  }

  isValidTextField(field) {
    // Skip if field is hidden
    if (!this.isElementVisible(field)) {
      return false
    }
    
    // Skip if field is too small
    const rect = field.getBoundingClientRect()
    if (rect.width < 50 || rect.height < 20) {
      return false
    }
    
    // Skip if field is disabled or readonly
    if (field.disabled || field.readOnly) {
      return false
    }
    
    // Skip password fields for security
    if (field.type === 'password') {
      return false
    }
    
    // Skip if field is inside an iframe (security limitation)
    if (window !== window.top) {
      return false
    }
    
    return true
  }

  isElementVisible(element) {
    const style = window.getComputedStyle(element)
    return style.display !== 'none' && 
           style.visibility !== 'hidden' && 
           style.opacity !== '0'
  }

  getFieldId(field) {
    // Create unique ID for field
    return field.id || 
           field.name || 
           field.className || 
           `field-${Array.from(document.querySelectorAll('*')).indexOf(field)}`
  }

  // ==================== FIELD EVENTS ====================

  onFieldDetected(field) {
    const fieldId = this.getFieldId(field)
    
    console.log('VoiceHype: Text field detected:', fieldId)
    
    // Store field info
    this.detectedFields.set(fieldId, {
      element: field,
      widget: null,
      isActive: false
    })
    
    // Add event listeners
    this.addFieldListeners(field)
    
    // Create widget for this field
    this.createWidget(field, fieldId)
  }

  onFieldHidden(field) {
    const fieldId = this.getFieldId(field)
    const fieldData = this.detectedFields.get(fieldId)
    
    if (fieldData) {
      console.log('VoiceHype: Text field hidden:', fieldId)
      
      // Remove widget
      if (fieldData.widget) {
        fieldData.widget.remove()
      }
      
      // Remove from tracking
      this.detectedFields.delete(fieldId)
      
      // Stop observing
      this.observer.unobserve(field)
    }
  }

  addFieldListeners(field) {
    // Focus/blur events to show/hide widget
    field.addEventListener('focus', () => this.onFieldFocus(field))
    field.addEventListener('blur', () => this.onFieldBlur(field))
    
    // Input events to track changes
    field.addEventListener('input', () => this.onFieldInput(field))
  }

  onFieldFocus(field) {
    const fieldId = this.getFieldId(field)
    const fieldData = this.detectedFields.get(fieldId)
    
    if (fieldData) {
      fieldData.isActive = true
      if (fieldData.widget) {
        fieldData.widget.classList.add('active')
      }
    }
  }

  onFieldBlur(field) {
    const fieldId = this.getFieldId(field)
    const fieldData = this.detectedFields.get(fieldId)
    
    if (fieldData) {
      fieldData.isActive = false
      if (fieldData.widget) {
        fieldData.widget.classList.remove('active')
      }
    }
  }

  onFieldInput(field) {
    // Could track typing patterns or content changes here
    console.log('VoiceHype: Field input detected')
  }

  // ==================== WIDGET CREATION ====================

  createWidget(field, fieldId) {
    try {
      // Create widget container
      const widget = document.createElement('div')
      widget.className = 'voicehype-widget'
      widget.setAttribute('data-field-id', fieldId)
      
      // Position widget relative to field
      this.positionWidget(widget, field)
      
      // Add widget content
      widget.innerHTML = `
        <button class="voicehype-trigger" title="Start voice recording">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
            <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
          </svg>
        </button>
      `
      
      // Add event listeners
      const trigger = widget.querySelector('.voicehype-trigger')
      trigger.addEventListener('click', (e) => {
        e.preventDefault()
        e.stopPropagation()
        this.onWidgetClick(field, fieldId)
      })
      
      // Insert widget into DOM
      document.body.appendChild(widget)
      
      // Store widget reference
      const fieldData = this.detectedFields.get(fieldId)
      if (fieldData) {
        fieldData.widget = widget
      }
      
      console.log('VoiceHype: Widget created for field:', fieldId)
      
    } catch (error) {
      console.error('VoiceHype: Error creating widget:', error)
    }
  }

  positionWidget(widget, field) {
    const rect = field.getBoundingClientRect()
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft
    
    // Position based on settings
    let top, left
    
    switch (this.settings.widgetPosition) {
      case 'bottom-right':
        top = rect.bottom + scrollTop - 30
        left = rect.right + scrollLeft - 30
        break
      case 'bottom-left':
        top = rect.bottom + scrollTop - 30
        left = rect.left + scrollLeft + 5
        break
      case 'top-right':
        top = rect.top + scrollTop + 5
        left = rect.right + scrollLeft - 30
        break
      case 'top-left':
        top = rect.top + scrollTop + 5
        left = rect.left + scrollLeft + 5
        break
      default:
        top = rect.bottom + scrollTop - 30
        left = rect.right + scrollLeft - 30
    }
    
    widget.style.position = 'absolute'
    widget.style.top = `${top}px`
    widget.style.left = `${left}px`
    widget.style.zIndex = '10000'
  }

  onWidgetClick(field, fieldId) {
    console.log('VoiceHype: Widget clicked for field:', fieldId)
    
    // Send message to background script to start recording
    chrome.runtime.sendMessage({
      type: 'START_FIELD_RECORDING',
      fieldId: fieldId,
      fieldInfo: {
        tagName: field.tagName,
        type: field.type,
        placeholder: field.placeholder,
        value: field.value
      }
    })
  }

  // ==================== MUTATION OBSERVER ====================

  setupMutationObserver() {
    const mutationObserver = new MutationObserver((mutations) => {
      let shouldRescan = false
      
      mutations.forEach(mutation => {
        if (mutation.type === 'childList') {
          // Check if new nodes contain text fields
          mutation.addedNodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const hasTextFields = node.matches && 
                node.matches(this.textFieldSelectors.join(', '))
              const containsTextFields = node.querySelectorAll && 
                node.querySelectorAll(this.textFieldSelectors.join(', ')).length > 0
              
              if (hasTextFields || containsTextFields) {
                shouldRescan = true
              }
            }
          })
        }
      })
      
      if (shouldRescan) {
        // Debounce rescanning
        clearTimeout(this.rescanTimeout)
        this.rescanTimeout = setTimeout(() => {
          this.scanForTextFields()
        }, 500)
      }
    })
    
    mutationObserver.observe(document.body, {
      childList: true,
      subtree: true
    })
  }

  // ==================== MESSAGE LISTENER ====================

  setupMessageListener() {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      switch (message.type) {
        case 'UPDATE_SETTINGS':
          Object.assign(this.settings, message.settings)
          this.updateWidgetPositions()
          break
          
        case 'INSERT_TRANSCRIPT':
          this.insertTranscript(message.fieldId, message.transcript)
          break
          
        case 'TOGGLE_DETECTOR':
          this.isEnabled = message.enabled
          if (!this.isEnabled) {
            this.hideAllWidgets()
          } else {
            this.showAllWidgets()
          }
          break
      }
    })
  }

  updateWidgetPositions() {
    this.detectedFields.forEach((fieldData, fieldId) => {
      if (fieldData.widget && fieldData.element) {
        this.positionWidget(fieldData.widget, fieldData.element)
      }
    })
  }

  insertTranscript(fieldId, transcript) {
    const fieldData = this.detectedFields.get(fieldId)
    if (fieldData && fieldData.element) {
      const field = fieldData.element
      
      // Insert transcript into field
      if (field.tagName === 'TEXTAREA' || field.tagName === 'INPUT') {
        field.value = transcript
        field.dispatchEvent(new Event('input', { bubbles: true }))
      } else if (field.contentEditable === 'true') {
        field.textContent = transcript
        field.dispatchEvent(new Event('input', { bubbles: true }))
      }
      
      // Focus field and position cursor at end
      field.focus()
      if (field.setSelectionRange) {
        field.setSelectionRange(transcript.length, transcript.length)
      }
    }
  }

  hideAllWidgets() {
    this.detectedFields.forEach(fieldData => {
      if (fieldData.widget) {
        fieldData.widget.style.display = 'none'
      }
    })
  }

  showAllWidgets() {
    this.detectedFields.forEach(fieldData => {
      if (fieldData.widget) {
        fieldData.widget.style.display = 'block'
      }
    })
  }

  // ==================== CLEANUP ====================

  destroy() {
    if (this.observer) {
      this.observer.disconnect()
    }
    
    this.detectedFields.forEach(fieldData => {
      if (fieldData.widget) {
        fieldData.widget.remove()
      }
    })
    
    this.detectedFields.clear()
  }
}

// ==================== INITIALIZATION ====================

// Initialize detector when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new TextFieldDetector()
  })
} else {
  new TextFieldDetector()
}

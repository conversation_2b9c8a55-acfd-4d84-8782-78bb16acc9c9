/**
 * Content Script Manager
 * Bismillahir rahmanir raheem
 * 
 * Main content script that coordinates text field detection,
 * widget management, and communication with background script
 */

class ContentScriptManager {
  constructor() {
    this.isInitialized = false
    this.textFieldDetector = null
    this.activeRecording = null
    this.settings = {
      enableOnAllSites: true,
      widgetPosition: 'bottom-right',
      showNotifications: true,
      enableKeyboardShortcuts: true
    }

    // Cross-browser API detection
    this.browserAPI = this.detectBrowserAPI()
    
    // Keyboard shortcut state
    this.keyboardShortcut = {
      keys: ['Control', 'Shift', 'KeyV'],
      pressed: new Set()
    }
    
    this.init()
  }

  // ==================== BROWSER COMPATIBILITY ====================

  detectBrowserAPI() {
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      return {
        api: chrome,
        name: 'chrome',
        isFirefox: false,
        isEdge: navigator.userAgent.includes('Edg/')
      }
    } else if (typeof browser !== 'undefined' && browser.runtime) {
      return {
        api: browser,
        name: 'firefox',
        isFirefox: true,
        isEdge: false
      }
    } else {
      console.warn('VoiceHype: No browser extension API detected')
      return null
    }
  }

  // Cross-browser message sending
  async sendMessage(message) {
    if (!this.browserAPI) {
      throw new Error('No browser API available')
    }

    return new Promise((resolve, reject) => {
      this.browserAPI.api.runtime.sendMessage(message, (response) => {
        if (this.browserAPI.api.runtime.lastError) {
          reject(new Error(this.browserAPI.api.runtime.lastError.message))
        } else {
          resolve(response)
        }
      })
    })
  }

  // ==================== INITIALIZATION ====================

  async init() {
    try {
      console.log('VoiceHype: Content script manager initializing...')
      
      // Check if we should run on this page
      if (!this.shouldRunOnPage()) {
        console.log('VoiceHype: Skipping page due to restrictions')
        return
      }
      
      // Load settings
      await this.loadSettings()
      
      // Inject widget styles
      this.injectStyles()
      
      // Initialize text field detector
      await this.initializeTextFieldDetector()
      
      // Set up keyboard shortcuts
      if (this.settings.enableKeyboardShortcuts) {
        this.setupKeyboardShortcuts()
      }
      
      // Set up message listeners
      this.setupMessageListeners()
      
      // Set up page visibility handling
      this.setupVisibilityHandling()
      
      this.isInitialized = true
      console.log('VoiceHype: Content script manager initialized successfully')
      
      // Notify background script
      this.sendMessage({
        type: 'CONTENT_SCRIPT_READY',
        url: window.location.href
      })
      
    } catch (error) {
      console.error('VoiceHype: Error initializing content script manager:', error)
    }
  }

  shouldRunOnPage() {
    const url = window.location.href
    
    // Skip on extension pages
    if (url.startsWith('chrome-extension://') || 
        url.startsWith('moz-extension://') ||
        url.startsWith('chrome://') ||
        url.startsWith('about:') ||
        url.startsWith('file://')) {
      return false
    }
    
    // Skip on VoiceHype website to avoid conflicts
    if (url.includes('voicehype.netlify.app') || 
        url.includes('voicehype.ai')) {
      return false
    }
    
    // Skip on iframes for security
    if (window !== window.top) {
      return false
    }
    
    return true
  }

  async loadSettings() {
    try {
      const response = await this.sendMessage({
        type: 'GET_SETTINGS'
      })
      
      if (response.success) {
        Object.assign(this.settings, response.settings)
        console.log('VoiceHype: Settings loaded:', this.settings)
      }
    } catch (error) {
      console.warn('VoiceHype: Could not load settings, using defaults:', error)
    }
  }

  // ==================== STYLE INJECTION ====================

  injectStyles() {
    // Check if styles already injected
    if (document.getElementById('voicehype-widget-styles')) {
      return
    }
    
    // Create style element
    const styleElement = document.createElement('style')
    styleElement.id = 'voicehype-widget-styles'
    
    // Load CSS content (in real implementation, this would be the actual CSS)
    styleElement.textContent = `
      /* VoiceHype Widget Styles - Injected by Content Script */
      .voicehype-widget {
        position: absolute !important;
        z-index: 2147483647 !important;
        pointer-events: auto !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        transition: all 0.2s ease !important;
      }
      
      .voicehype-trigger {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 32px !important;
        height: 32px !important;
        border: none !important;
        border-radius: 50% !important;
        background: #007bff !important;
        color: white !important;
        cursor: pointer !important;
        box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3) !important;
        transition: all 0.2s ease !important;
      }
      
      .voicehype-trigger:hover {
        background: #0056b3 !important;
        transform: scale(1.1) !important;
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4) !important;
      }
      
      .voicehype-trigger svg {
        width: 16px !important;
        height: 16px !important;
        fill: currentColor !important;
      }
      
      .voicehype-widget.recording .voicehype-trigger {
        background: #dc3545 !important;
        animation: voicehype-pulse 1.5s ease-in-out infinite !important;
      }
      
      @keyframes voicehype-pulse {
        0%, 100% { transform: scale(1) !important; }
        50% { transform: scale(1.05) !important; }
      }
      
      .voicehype-widget.processing .voicehype-trigger {
        background: #ffc107 !important;
        color: #333 !important;
      }
      
      @media (prefers-reduced-motion: reduce) {
        .voicehype-widget, .voicehype-trigger {
          animation: none !important;
          transition: none !important;
        }
      }
    `
    
    // Inject into head
    document.head.appendChild(styleElement)
    console.log('VoiceHype: Widget styles injected')
  }

  // ==================== TEXT FIELD DETECTOR ====================

  async initializeTextFieldDetector() {
    try {
      // Import and initialize text field detector
      // In a real implementation, this would be imported properly
      if (typeof TextFieldDetector !== 'undefined') {
        this.textFieldDetector = new TextFieldDetector()
      } else {
        console.warn('VoiceHype: TextFieldDetector not available')
      }
    } catch (error) {
      console.error('VoiceHype: Error initializing text field detector:', error)
    }
  }

  // ==================== KEYBOARD SHORTCUTS ====================

  setupKeyboardShortcuts() {
    document.addEventListener('keydown', (event) => {
      this.keyboardShortcut.pressed.add(event.code)
      
      // Check if shortcut is pressed (Ctrl+Shift+V)
      if (this.isShortcutPressed()) {
        event.preventDefault()
        event.stopPropagation()
        this.handleKeyboardShortcut()
      }
    })
    
    document.addEventListener('keyup', (event) => {
      this.keyboardShortcut.pressed.delete(event.code)
    })
    
    // Clear pressed keys when window loses focus
    window.addEventListener('blur', () => {
      this.keyboardShortcut.pressed.clear()
    })
  }

  isShortcutPressed() {
    return this.keyboardShortcut.keys.every(key => 
      this.keyboardShortcut.pressed.has(key)
    )
  }

  handleKeyboardShortcut() {
    console.log('VoiceHype: Keyboard shortcut triggered')
    
    // Find focused text field
    const activeElement = document.activeElement
    if (this.isTextField(activeElement)) {
      this.startRecordingForField(activeElement)
    } else {
      // Show notification that no text field is focused
      if (this.settings.showNotifications) {
        this.showNotification('Please focus on a text field first', 'info')
      }
    }
  }

  isTextField(element) {
    if (!element) return false
    
    const tagName = element.tagName.toLowerCase()
    const type = element.type?.toLowerCase()
    
    return (
      (tagName === 'input' && ['text', 'email', 'search', 'url', 'tel'].includes(type)) ||
      tagName === 'textarea' ||
      element.contentEditable === 'true'
    )
  }

  // ==================== RECORDING MANAGEMENT ====================

  async startRecordingForField(field) {
    try {
      if (this.activeRecording) {
        console.log('VoiceHype: Recording already in progress')
        return
      }
      
      console.log('VoiceHype: Starting recording for field')
      
      // Create field ID
      const fieldId = this.generateFieldId(field)
      
      // Store active recording info
      this.activeRecording = {
        fieldId: fieldId,
        field: field,
        startTime: Date.now()
      }
      
      // Update widget state
      this.updateWidgetState(fieldId, 'recording')
      
      // Send message to background script
      const response = await this.sendMessage({
        type: 'START_FIELD_RECORDING',
        fieldId: fieldId,
        fieldInfo: {
          tagName: field.tagName,
          type: field.type,
          placeholder: field.placeholder,
          value: field.value,
          url: window.location.href
        }
      })
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to start recording')
      }
      
      // Show notification
      if (this.settings.showNotifications) {
        this.showNotification('Recording started...', 'success')
      }
      
    } catch (error) {
      console.error('VoiceHype: Error starting recording:', error)
      this.activeRecording = null
      
      if (this.settings.showNotifications) {
        this.showNotification('Failed to start recording', 'error')
      }
    }
  }

  async stopRecording() {
    try {
      if (!this.activeRecording) {
        console.log('VoiceHype: No active recording to stop')
        return
      }
      
      console.log('VoiceHype: Stopping recording')
      
      const fieldId = this.activeRecording.fieldId
      
      // Update widget state
      this.updateWidgetState(fieldId, 'processing')
      
      // Send message to background script
      const response = await this.sendMessage({
        type: 'STOP_FIELD_RECORDING',
        fieldId: fieldId
      })
      
      if (response.success && response.transcript) {
        // Insert transcript into field
        this.insertTranscript(this.activeRecording.field, response.transcript)
        
        if (this.settings.showNotifications) {
          this.showNotification('Transcript inserted!', 'success')
        }
      } else {
        throw new Error(response.error || 'Failed to get transcript')
      }
      
    } catch (error) {
      console.error('VoiceHype: Error stopping recording:', error)
      
      if (this.settings.showNotifications) {
        this.showNotification('Failed to process recording', 'error')
      }
    } finally {
      // Reset recording state
      if (this.activeRecording) {
        this.updateWidgetState(this.activeRecording.fieldId, 'idle')
        this.activeRecording = null
      }
    }
  }

  generateFieldId(field) {
    return field.id || 
           field.name || 
           field.className || 
           `field-${Array.from(document.querySelectorAll('*')).indexOf(field)}`
  }

  updateWidgetState(fieldId, state) {
    const widget = document.querySelector(`[data-field-id="${fieldId}"]`)
    if (widget) {
      // Remove all state classes
      widget.classList.remove('recording', 'processing', 'idle')
      // Add new state class
      widget.classList.add(state)
    }
  }

  insertTranscript(field, transcript) {
    if (!field || !transcript) return
    
    try {
      // Insert transcript based on field type
      if (field.tagName === 'TEXTAREA' || field.tagName === 'INPUT') {
        field.value = transcript
        field.dispatchEvent(new Event('input', { bubbles: true }))
        field.dispatchEvent(new Event('change', { bubbles: true }))
      } else if (field.contentEditable === 'true') {
        field.textContent = transcript
        field.dispatchEvent(new Event('input', { bubbles: true }))
      }
      
      // Focus field and position cursor at end
      field.focus()
      if (field.setSelectionRange) {
        field.setSelectionRange(transcript.length, transcript.length)
      }
      
      console.log('VoiceHype: Transcript inserted successfully')
      
    } catch (error) {
      console.error('VoiceHype: Error inserting transcript:', error)
    }
  }

  // ==================== MESSAGE HANDLING ====================

  setupMessageListeners() {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      switch (message.type) {
        case 'UPDATE_SETTINGS':
          this.handleSettingsUpdate(message.settings)
          break
          
        case 'INSERT_TRANSCRIPT':
          this.handleTranscriptInsertion(message)
          break
          
        case 'RECORDING_STATUS_UPDATE':
          this.handleRecordingStatusUpdate(message)
          break
          
        case 'TOGGLE_RECORDING':
          this.handleToggleRecording()
          break
          
        case 'GET_PAGE_INFO':
          sendResponse({
            url: window.location.href,
            title: document.title,
            hasTextFields: document.querySelectorAll('input, textarea, [contenteditable]').length > 0
          })
          break
      }
    })
  }

  handleSettingsUpdate(newSettings) {
    Object.assign(this.settings, newSettings)
    console.log('VoiceHype: Settings updated:', this.settings)
    
    // Update text field detector if needed
    if (this.textFieldDetector) {
      this.textFieldDetector.updateSettings(this.settings)
    }
  }

  handleTranscriptInsertion(message) {
    if (this.activeRecording && this.activeRecording.fieldId === message.fieldId) {
      this.insertTranscript(this.activeRecording.field, message.transcript)
    }
  }

  handleRecordingStatusUpdate(message) {
    if (message.status === 'stopped') {
      this.stopRecording()
    }
  }

  handleToggleRecording() {
    if (this.activeRecording) {
      this.stopRecording()
    } else {
      const activeElement = document.activeElement
      if (this.isTextField(activeElement)) {
        this.startRecordingForField(activeElement)
      }
    }
  }

  // ==================== NOTIFICATIONS ====================

  showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div')
    notification.className = `voicehype-notification voicehype-notification-${type}`
    notification.textContent = message
    
    // Style notification
    Object.assign(notification.style, {
      position: 'fixed',
      top: '20px',
      right: '20px',
      background: type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#007bff',
      color: 'white',
      padding: '12px 16px',
      borderRadius: '6px',
      fontSize: '14px',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
      zIndex: '2147483647',
      opacity: '0',
      transform: 'translateY(-10px)',
      transition: 'all 0.3s ease'
    })
    
    // Insert into DOM
    document.body.appendChild(notification)
    
    // Animate in
    requestAnimationFrame(() => {
      notification.style.opacity = '1'
      notification.style.transform = 'translateY(0)'
    })
    
    // Remove after delay
    setTimeout(() => {
      notification.style.opacity = '0'
      notification.style.transform = 'translateY(-10px)'
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification)
        }
      }, 300)
    }, 3000)
  }

  // ==================== VISIBILITY HANDLING ====================

  setupVisibilityHandling() {
    document.addEventListener('visibilitychange', () => {
      if (document.hidden && this.activeRecording) {
        console.log('VoiceHype: Page hidden, stopping recording')
        this.stopRecording()
      }
    })
    
    window.addEventListener('beforeunload', () => {
      if (this.activeRecording) {
        console.log('VoiceHype: Page unloading, stopping recording')
        this.stopRecording()
      }
    })
  }

  // ==================== CLEANUP ====================

  destroy() {
    if (this.textFieldDetector) {
      this.textFieldDetector.destroy()
    }
    
    if (this.activeRecording) {
      this.stopRecording()
    }
    
    // Remove injected styles
    const styleElement = document.getElementById('voicehype-widget-styles')
    if (styleElement) {
      styleElement.remove()
    }
    
    console.log('VoiceHype: Content script manager destroyed')
  }
}

// ==================== INITIALIZATION ====================

// Initialize content script manager
let contentScriptManager = null

if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    contentScriptManager = new ContentScriptManager()
  })
} else {
  contentScriptManager = new ContentScriptManager()
}

// Handle extension context invalidation
if (chrome?.runtime?.onConnect) {
  chrome.runtime.onConnect.addListener((port) => {
    port.onDisconnect.addListener(() => {
      if (chrome.runtime.lastError) {
        console.warn('VoiceHype: Extension context invalidated')
        if (contentScriptManager) {
          contentScriptManager.destroy()
        }
      }
    })
  })
}

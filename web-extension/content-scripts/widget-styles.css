/**
 * VoiceHype Widget Styles
 * <PERSON><PERSON><PERSON><PERSON> rahmanir raheem
 * 
 * Styles for the floating voice recording widget that appears
 * next to text fields on web pages
 */

/* ==================== WIDGET CONTAINER ==================== */

.voicehype-widget {
  position: absolute !important;
  z-index: 2147483647 !important; /* Maximum z-index */
  pointer-events: auto !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  font-size: 14px !important;
  line-height: 1.4 !important;
  color: #333 !important;
  
  /* Reset any inherited styles */
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  background: none !important;
  box-shadow: none !important;
  text-align: left !important;
  text-decoration: none !important;
  text-transform: none !important;
  letter-spacing: normal !important;
  word-spacing: normal !important;
  
  /* Prevent interference with page layout */
  float: none !important;
  clear: none !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  
  /* Animation */
  transition: all 0.2s ease !important;
  transform: scale(0.9) !important;
  opacity: 0 !important;
  animation: voicehype-widget-appear 0.3s ease forwards !important;
}

@keyframes voicehype-widget-appear {
  to {
    transform: scale(1) !important;
    opacity: 1 !important;
  }
}

/* ==================== WIDGET TRIGGER BUTTON ==================== */

.voicehype-trigger {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 32px !important;
  height: 32px !important;
  border: none !important;
  border-radius: 50% !important;
  background: #007bff !important;
  color: white !important;
  cursor: pointer !important;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3) !important;
  transition: all 0.2s ease !important;
  
  /* Reset button styles */
  margin: 0 !important;
  padding: 0 !important;
  font-family: inherit !important;
  font-size: 14px !important;
  font-weight: normal !important;
  text-align: center !important;
  text-decoration: none !important;
  text-transform: none !important;
  letter-spacing: normal !important;
  word-spacing: normal !important;
  line-height: 1 !important;
  
  /* Prevent interference */
  float: none !important;
  clear: none !important;
  position: relative !important;
  top: auto !important;
  left: auto !important;
  right: auto !important;
  bottom: auto !important;
  z-index: auto !important;
  overflow: visible !important;
  clip: auto !important;
  opacity: 1 !important;
  visibility: visible !important;
  transform: none !important;
}

.voicehype-trigger:hover {
  background: #0056b3 !important;
  transform: scale(1.1) !important;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4) !important;
}

.voicehype-trigger:active {
  transform: scale(0.95) !important;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3) !important;
}

.voicehype-trigger:focus {
  outline: 2px solid #007bff !important;
  outline-offset: 2px !important;
}

/* ==================== WIDGET STATES ==================== */

/* Active state when field is focused */
.voicehype-widget.active .voicehype-trigger {
  background: #28a745 !important;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3) !important;
}

.voicehype-widget.active .voicehype-trigger:hover {
  background: #1e7e34 !important;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4) !important;
}

/* Recording state */
.voicehype-widget.recording .voicehype-trigger {
  background: #dc3545 !important;
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3) !important;
  animation: voicehype-pulse 1.5s ease-in-out infinite !important;
}

@keyframes voicehype-pulse {
  0%, 100% {
    transform: scale(1) !important;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3) !important;
  }
  50% {
    transform: scale(1.05) !important;
    box-shadow: 0 4px 16px rgba(220, 53, 69, 0.5) !important;
  }
}

/* Processing state */
.voicehype-widget.processing .voicehype-trigger {
  background: #ffc107 !important;
  color: #333 !important;
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3) !important;
}

.voicehype-widget.processing .voicehype-trigger svg {
  animation: voicehype-spin 1s linear infinite !important;
}

@keyframes voicehype-spin {
  from {
    transform: rotate(0deg) !important;
  }
  to {
    transform: rotate(360deg) !important;
  }
}

/* Disabled state */
.voicehype-widget.disabled {
  opacity: 0.5 !important;
  pointer-events: none !important;
}

.voicehype-widget.disabled .voicehype-trigger {
  background: #6c757d !important;
  cursor: not-allowed !important;
}

/* ==================== WIDGET ICON ==================== */

.voicehype-trigger svg {
  width: 16px !important;
  height: 16px !important;
  fill: currentColor !important;
  transition: transform 0.2s ease !important;
  
  /* Reset SVG styles */
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  background: none !important;
  box-shadow: none !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* ==================== WIDGET TOOLTIP ==================== */

.voicehype-widget::before {
  content: attr(data-tooltip) !important;
  position: absolute !important;
  bottom: 100% !important;
  left: 50% !important;
  transform: translateX(-50%) translateY(-8px) !important;
  background: rgba(0, 0, 0, 0.8) !important;
  color: white !important;
  padding: 4px 8px !important;
  border-radius: 4px !important;
  font-size: 12px !important;
  white-space: nowrap !important;
  opacity: 0 !important;
  pointer-events: none !important;
  transition: opacity 0.2s ease !important;
  z-index: 1 !important;
}

.voicehype-widget::after {
  content: '' !important;
  position: absolute !important;
  bottom: 100% !important;
  left: 50% !important;
  transform: translateX(-50%) translateY(-2px) !important;
  border: 4px solid transparent !important;
  border-top-color: rgba(0, 0, 0, 0.8) !important;
  opacity: 0 !important;
  pointer-events: none !important;
  transition: opacity 0.2s ease !important;
}

.voicehype-widget:hover::before,
.voicehype-widget:hover::after {
  opacity: 1 !important;
}

/* ==================== RESPONSIVE DESIGN ==================== */

/* Smaller widgets on mobile */
@media (max-width: 768px) {
  .voicehype-trigger {
    width: 28px !important;
    height: 28px !important;
  }
  
  .voicehype-trigger svg {
    width: 14px !important;
    height: 14px !important;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .voicehype-trigger {
    box-shadow: 0 1px 4px rgba(0, 123, 255, 0.3) !important;
  }
  
  .voicehype-trigger:hover {
    box-shadow: 0 2px 6px rgba(0, 123, 255, 0.4) !important;
  }
}

/* ==================== DARK MODE SUPPORT ==================== */

@media (prefers-color-scheme: dark) {
  .voicehype-widget::before {
    background: rgba(255, 255, 255, 0.9) !important;
    color: #333 !important;
  }
  
  .voicehype-widget::after {
    border-top-color: rgba(255, 255, 255, 0.9) !important;
  }
}

/* ==================== ACCESSIBILITY ==================== */

/* High contrast mode */
@media (prefers-contrast: high) {
  .voicehype-trigger {
    border: 2px solid currentColor !important;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .voicehype-widget,
  .voicehype-trigger,
  .voicehype-trigger svg {
    animation: none !important;
    transition: none !important;
  }
  
  .voicehype-widget.recording .voicehype-trigger {
    animation: none !important;
  }
}

/* Focus visible for keyboard navigation */
.voicehype-trigger:focus-visible {
  outline: 2px solid #007bff !important;
  outline-offset: 2px !important;
}

/* ==================== PRINT STYLES ==================== */

@media print {
  .voicehype-widget {
    display: none !important;
  }
}

/* ==================== WIDGET POSITIONING VARIANTS ==================== */

/* Bottom-right positioning (default) */
.voicehype-widget[data-position="bottom-right"] {
  /* Default positioning handled in JS */
}

/* Bottom-left positioning */
.voicehype-widget[data-position="bottom-left"] {
  /* Positioning handled in JS */
}

/* Top-right positioning */
.voicehype-widget[data-position="top-right"] {
  /* Positioning handled in JS */
}

/* Top-left positioning */
.voicehype-widget[data-position="top-left"] {
  /* Positioning handled in JS */
}

/* ==================== WIDGET THEMES ==================== */

/* Light theme (default) */
.voicehype-widget[data-theme="light"] .voicehype-trigger {
  background: #007bff !important;
  color: white !important;
}

/* Dark theme */
.voicehype-widget[data-theme="dark"] .voicehype-trigger {
  background: #0d6efd !important;
  color: white !important;
  box-shadow: 0 2px 8px rgba(13, 110, 253, 0.3) !important;
}

/* Minimal theme */
.voicehype-widget[data-theme="minimal"] .voicehype-trigger {
  background: rgba(0, 0, 0, 0.1) !important;
  color: #333 !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
}

/* ==================== WIDGET ANIMATIONS ==================== */

/* Entrance animation */
.voicehype-widget.entering {
  animation: voicehype-widget-enter 0.3s ease forwards !important;
}

@keyframes voicehype-widget-enter {
  from {
    transform: scale(0.8) translateY(10px) !important;
    opacity: 0 !important;
  }
  to {
    transform: scale(1) translateY(0) !important;
    opacity: 1 !important;
  }
}

/* Exit animation */
.voicehype-widget.exiting {
  animation: voicehype-widget-exit 0.2s ease forwards !important;
}

@keyframes voicehype-widget-exit {
  from {
    transform: scale(1) translateY(0) !important;
    opacity: 1 !important;
  }
  to {
    transform: scale(0.8) translateY(-10px) !important;
    opacity: 0 !important;
  }
}

# VoiceHype Extension Icons

This directory contains the icon files for the VoiceHype browser extension.

## Required Icons

- `icon-16.png` - 16x16 pixels (toolbar icon)
- `icon-32.png` - 32x32 pixels (extension management)
- `icon-48.png` - 48x48 pixels (extension management)
- `icon-128.png` - 128x128 pixels (Chrome Web Store)

## Icon Design Guidelines

- Use the VoiceHype brand colors
- Include microphone symbol for voice recognition
- Ensure icons are clear at all sizes
- Use transparent backgrounds
- Follow platform-specific design guidelines

## Temporary Placeholder

For development purposes, you can use simple colored squares or the browser's default icons until proper icons are designed.

## Creating Icons

You can create these icons using:
- Adobe Illustrator/Photoshop
- Figma
- GIMP (free alternative)
- Online icon generators

Make sure to export in PNG format with transparent backgrounds.

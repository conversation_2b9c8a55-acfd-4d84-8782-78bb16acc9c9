# VoiceHype Web Extension Testing Guide
*<PERSON><PERSON><PERSON><PERSON> rahmanir raheem*

## 🚀 Quick Start Testing

### Prerequisites
- Node.js 16+ installed
- Chrome, Firefox, and/or Edge browsers
- Microphone access permissions
- VoiceHype account (create at https://voicehype.netlify.app)

### Build the Extension
```bash
cd web-extension
node build.js
```

This creates browser-specific packages in the `build/` directory:
- `build/chrome/` - Chrome/Chromium build
- `build/firefox/` - Firefox build  
- `build/edge/` - Microsoft Edge build
- `build/voicehype-*.zip` - Distribution packages

## 🔧 Browser-Specific Installation

### Chrome/Chromium/Edge
1. Open `chrome://extensions/` (or `edge://extensions/`)
2. Enable "Developer mode" (top right toggle)
3. Click "Load unpacked"
4. Select the `build/chrome/` directory (or `build/edge/` for Edge)
5. Extension should appear in toolbar

### Firefox
1. Open `about:debugging`
2. Click "This Firefox"
3. Click "Load Temporary Add-on"
4. Select any file in `build/firefox/` directory
5. Extension loads temporarily (until browser restart)

**For permanent Firefox installation:**
1. Zip the `build/firefox/` directory
2. Go to `about:addons`
3. Click gear icon → "Install Add-on From File"
4. Select the ZIP file

## 🧪 Testing Checklist

### ✅ Authentication Flow
- [ ] Click extension icon opens popup
- [ ] "Sign in with Google" button works
- [ ] "Sign in with GitHub" button works
- [ ] Auth popup opens VoiceHype website
- [ ] OAuth flow completes successfully
- [ ] Extension shows authenticated state
- [ ] User profile data displays correctly
- [ ] Sign out works and clears data

### ✅ Voice Recording
- [ ] Microphone permission requested
- [ ] Recording button appears on text fields
- [ ] Click widget starts recording (red state)
- [ ] Keyboard shortcut (Ctrl+Shift+V) works
- [ ] Recording stops and processes
- [ ] Transcript appears in text field
- [ ] Works on different field types:
  - [ ] `<input type="text">`
  - [ ] `<textarea>`
  - [ ] `contenteditable` divs
  - [ ] Code editors (CodeMirror, Monaco)

### ✅ Real-time Transcription
- [ ] Real-time mode toggle works
- [ ] Partial transcripts show during recording
- [ ] Final transcript replaces partial text
- [ ] Connection status indicators work
- [ ] Graceful shutdown handles pending chunks

### ✅ Cross-Browser Compatibility
- [ ] Chrome: All features work
- [ ] Firefox: All features work  
- [ ] Edge: All features work
- [ ] Manifest V3 (Chrome/Edge) vs V2 (Firefox)
- [ ] API differences handled correctly

### ✅ UI/UX Testing
- [ ] Extension popup responsive design
- [ ] Dark/light theme toggle
- [ ] Navigation between tabs works
- [ ] Profile page shows user data
- [ ] Usage page displays statistics
- [ ] Settings page saves preferences
- [ ] Widgets position correctly
- [ ] Notifications appear properly

### ✅ Error Handling
- [ ] Network errors handled gracefully
- [ ] Microphone access denied
- [ ] Invalid API key scenarios
- [ ] Extension context invalidation
- [ ] Large file upload limits
- [ ] Real-time connection failures

## 🌐 Test Websites

Test the extension on various websites:

### Text-Heavy Sites
- [ ] **GitHub** - Code editors, issue forms
- [ ] **Gmail** - Compose emails
- [ ] **Google Docs** - Document editing
- [ ] **Notion** - Note-taking
- [ ] **Slack** - Message composition
- [ ] **Discord** - Chat messages

### Development Sites  
- [ ] **CodePen** - Code editors
- [ ] **JSFiddle** - Multiple editors
- [ ] **Replit** - Online IDE
- [ ] **StackOverflow** - Question/answer forms

### Social Media
- [ ] **Twitter/X** - Tweet composition
- [ ] **LinkedIn** - Post creation
- [ ] **Reddit** - Comment forms
- [ ] **Facebook** - Status updates

### E-commerce
- [ ] **Amazon** - Review forms
- [ ] **eBay** - Item descriptions
- [ ] **Shopify** - Product forms

## 🐛 Common Issues & Solutions

### Extension Not Loading
- Check manifest.json syntax
- Verify file paths are correct
- Check browser console for errors
- Ensure all required permissions

### Authentication Fails
- Check VoiceHype website is accessible
- Verify OAuth redirect URLs
- Check browser popup blockers
- Clear extension storage and retry

### Recording Not Working
- Check microphone permissions
- Verify HTTPS context (required for getUserMedia)
- Check browser console for errors
- Test on different websites

### Widgets Not Appearing
- Check content script injection
- Verify CSS is loading
- Check for conflicting page styles
- Test on simpler websites first

### Cross-Browser Issues
- Firefox: Check manifest V2 compatibility
- Edge: Verify Chromium-based features
- Safari: Not supported (different extension system)

## 📊 Performance Testing

### Memory Usage
- Monitor extension memory in browser task manager
- Check for memory leaks during long sessions
- Test with multiple tabs open

### CPU Usage
- Monitor during voice recording
- Check real-time transcription impact
- Test with background processing

### Network Usage
- Monitor API calls to VoiceHype services
- Check WebSocket connection stability
- Test with slow network conditions

## 🔒 Security Testing

### Data Privacy
- Verify no transcripts stored locally
- Check API key encryption
- Test secure storage implementation
- Verify HTTPS-only connections

### Permissions
- Minimal required permissions only
- No unnecessary host permissions
- Proper content script isolation

## 📝 Test Reports

Document any issues found:

```markdown
## Issue Report

**Browser:** Chrome 120.0
**OS:** Windows 11
**Issue:** Widget positioning incorrect on Gmail
**Steps to Reproduce:**
1. Open Gmail compose
2. Click in message body
3. Widget appears outside viewport

**Expected:** Widget near text cursor
**Actual:** Widget at top-left corner
**Severity:** Medium
```

## 🚀 Deployment Testing

Before publishing:

### Store Validation
- [ ] Chrome Web Store policy compliance
- [ ] Firefox Add-ons policy compliance  
- [ ] Edge Add-ons policy compliance
- [ ] Privacy policy links work
- [ ] Screenshots and descriptions accurate

### Final Checks
- [ ] Version numbers consistent
- [ ] All features working in production
- [ ] Analytics/tracking configured
- [ ] Support documentation complete
- [ ] Update mechanisms tested

## 📞 Support

For testing issues:
- Check browser console logs
- Enable extension debug mode
- Test in incognito/private mode
- Compare across different browsers

Happy testing! 🎉

#!/usr/bin/env node

/**
 * VoiceHype Web Extension Build Script
 * Bismillahir rahmanir raheem
 * 
 * Builds extension packages for Chrome, Firefox, and Edge
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class ExtensionBuilder {
  constructor() {
    this.rootDir = __dirname;
    this.buildDir = path.join(this.rootDir, 'build');
    this.browsers = ['chrome', 'firefox', 'edge'];
    
    console.log('🚀 VoiceHype Web Extension Builder');
    console.log('Bismillahir rahmanir raheem\n');
  }

  // Clean build directory
  clean() {
    console.log('🧹 Cleaning build directory...');
    if (fs.existsSync(this.buildDir)) {
      fs.rmSync(this.buildDir, { recursive: true, force: true });
    }
    fs.mkdirSync(this.buildDir, { recursive: true });
  }

  // Build frontend with Vite
  async buildFrontend() {
    console.log('🔨 Building frontend with Vite...');
    try {
      // Use execSync to run npm build command
      execSync('npm run build', { stdio: 'inherit', cwd: this.rootDir });
      console.log('  ✅ Frontend build complete');
    } catch (error) {
      console.error('  ❌ Frontend build failed:', error.message);
      throw error;
    }
  }

  // Copy shared files to browser-specific build
  copySharedFiles(browserDir) {
    const sharedDirs = [
      'background',
      'content-scripts',
      'icons'
    ];

    sharedDirs.forEach(dir => {
      const srcDir = path.join(this.rootDir, dir);
      const destDir = path.join(browserDir, dir);
      
      if (fs.existsSync(srcDir)) {
        this.copyRecursive(srcDir, destDir);
      }
    });
  }

  // Copy built frontend files
  copyFrontendBuild(browserDir) {
    const distDir = path.join(this.rootDir, 'dist');
    const frontendDestDir = path.join(browserDir, 'frontend');
    
    if (fs.existsSync(distDir)) {
      this.copyRecursive(distDir, frontendDestDir);
      console.log('  ✅ Copied built frontend files');
    } else {
      console.warn('  ⚠️  No dist directory found');
    }
  }

  // Copy files recursively
  copyRecursive(src, dest) {
    if (!fs.existsSync(dest)) {
      fs.mkdirSync(dest, { recursive: true });
    }

    const items = fs.readdirSync(src);
    
    items.forEach(item => {
      const srcPath = path.join(src, item);
      const destPath = path.join(dest, item);
      
      if (fs.statSync(srcPath).isDirectory()) {
        this.copyRecursive(srcPath, destPath);
      } else {
        fs.copyFileSync(srcPath, destPath);
      }
    });
  }

  // Build for specific browser
  buildBrowser(browser) {
    console.log(`📦 Building for ${browser.toUpperCase()}...`);
    
    const browserBuildDir = path.join(this.buildDir, browser);
    fs.mkdirSync(browserBuildDir, { recursive: true });

    // Copy shared files
    this.copySharedFiles(browserBuildDir);

    // Copy built frontend files
    this.copyFrontendBuild(browserBuildDir);

    // Copy browser-specific manifest
    const manifestSrc = path.join(this.rootDir, 'browser-specific', browser, 'manifest.json');
    const manifestDest = path.join(browserBuildDir, 'manifest.json');
    
    if (fs.existsSync(manifestSrc)) {
      fs.copyFileSync(manifestSrc, manifestDest);
      console.log(`  ✅ Copied ${browser} manifest`);
    } else {
      console.warn(`  ⚠️  No manifest found for ${browser}`);
    }

    // Copy any browser-specific files
    const browserSpecificDir = path.join(this.rootDir, 'browser-specific', browser);
    if (fs.existsSync(browserSpecificDir)) {
      const items = fs.readdirSync(browserSpecificDir);
      items.forEach(item => {
        if (item !== 'manifest.json') {
          const srcPath = path.join(browserSpecificDir, item);
          const destPath = path.join(browserBuildDir, item);
          
          if (fs.statSync(srcPath).isDirectory()) {
            this.copyRecursive(srcPath, destPath);
          } else {
            fs.copyFileSync(srcPath, destPath);
          }
        }
      });
    }

    console.log(`  ✅ ${browser.toUpperCase()} build complete`);
  }

  // Create ZIP packages
  createPackages() {
    console.log('\n📦 Creating ZIP packages...');
    
    this.browsers.forEach(browser => {
      const browserDir = path.join(this.buildDir, browser);
      const zipFile = path.join(this.buildDir, `voicehype-${browser}.zip`);
      
      if (fs.existsSync(browserDir)) {
        try {
          // Use system zip command
          execSync(`cd "${browserDir}" && zip -r "../voicehype-${browser}.zip" .`, { 
            stdio: 'inherit' 
          });
          console.log(`  ✅ Created voicehype-${browser}.zip`);
        } catch (error) {
          console.error(`  ❌ Failed to create ${browser} package:`, error.message);
        }
      }
    });
  }

  // Validate builds
  validate() {
    console.log('\n🔍 Validating builds...');
    
    this.browsers.forEach(browser => {
      const browserDir = path.join(this.buildDir, browser);
      const manifestPath = path.join(browserDir, 'manifest.json');
      
      if (fs.existsSync(manifestPath)) {
        try {
          const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
          console.log(`  ✅ ${browser.toUpperCase()}: ${manifest.name} v${manifest.version}`);
          
          // Check required files
          const requiredFiles = [
            'background/index.js',
            'content-scripts/content-script-manager.js',
            'frontend/popup.html',
            'frontend/assets/main.js',
            'icons/icon-48.png'
          ];
          
          requiredFiles.forEach(file => {
            const filePath = path.join(browserDir, file);
            if (!fs.existsSync(filePath)) {
              console.warn(`    ⚠️  Missing: ${file}`);
            }
          });
          
        } catch (error) {
          console.error(`  ❌ ${browser.toUpperCase()}: Invalid manifest.json`);
        }
      } else {
        console.error(`  ❌ ${browser.toUpperCase()}: No manifest.json found`);
      }
    });
  }

  // Main build process
  async build() {
    try {
      this.clean();
      
      // Build frontend first
      await this.buildFrontend();
      
      // Build for each browser
      this.browsers.forEach(browser => {
        this.buildBrowser(browser);
      });
      
      this.validate();
      this.createPackages();
      
      console.log('\n🎉 Build complete! Check the build/ directory for packages.');
      console.log('\nNext steps:');
      console.log('1. Test each browser build locally');
      console.log('2. Upload to respective browser stores');
      console.log('3. Submit for review');
      
    } catch (error) {
      console.error('\n❌ Build failed:', error.message);
      process.exit(1);
    }
  }
}

// Run builder if called directly
if (require.main === module) {
  const builder = new ExtensionBuilder();
  builder.build().catch((error) => {
    console.error('Build failed:', error);
    process.exit(1);
  });
}

module.exports = ExtensionBuilder;

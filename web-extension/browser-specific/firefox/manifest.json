{"manifest_version": 2, "name": "VoiceHype", "version": "1.0.0", "description": "Voice-to-prompt productivity tool for web browsers", "applications": {"gecko": {"id": "<EMAIL>", "strict_min_version": "57.0"}}, "permissions": ["activeTab", "storage", "tabs", "https://nffixzoqnqxpcqpcxpps.supabase.co/*", "https://supabase.voicehype.ai/*", "https://voicehype.netlify.app/*", "http://***************:3001/*"], "background": {"scripts": ["../../background/index.js"], "persistent": false}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["../../content-scripts/text-field-detector.js", "../../content-scripts/content-script-manager.js"], "css": ["../../content-scripts/widget-styles.css"], "run_at": "document_end", "all_frames": false}], "browser_action": {"default_popup": "../../frontend/popup.html", "default_title": "VoiceHype - Voice to Prompt", "default_icon": {"16": "../../icons/icon-16.png", "32": "../../icons/icon-32.png", "48": "../../icons/icon-48.png", "128": "../../icons/icon-128.png"}}, "icons": {"16": "../../icons/icon-16.png", "32": "../../icons/icon-32.png", "48": "../../icons/icon-48.png", "128": "../../icons/icon-128.png"}, "web_accessible_resources": ["../../content-scripts/*", "../../icons/*", "../../frontend/*", "../../frontend/assets/*"], "options_ui": {"page": "../../frontend/popup.html", "open_in_tab": true}, "commands": {"toggle-recording": {"suggested_key": {"default": "Ctrl+Shift+V", "mac": "Command+Shift+V"}, "description": "Start/Stop voice recording"}}}
{"manifest_version": 3, "name": "VoiceHype", "version": "1.0.0", "description": "Voice-to-prompt productivity tool for web browsers", "permissions": ["activeTab", "storage", "scripting"], "host_permissions": ["https://nffixzoqnqxpcqpcxpps.supabase.co/*", "https://supabase.voicehype.ai/*", "https://voicehype.netlify.app/*", "http://***************:3001/*"], "background": {"service_worker": "../../background/index.js", "type": "module"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["../../content-scripts/text-field-detector.js", "../../content-scripts/content-script-manager.js"], "css": ["../../content-scripts/widget-styles.css"], "run_at": "document_end", "all_frames": false}], "action": {"default_popup": "../../frontend/popup.html", "default_title": "VoiceHype - Voice to Prompt", "default_icon": {"16": "../../icons/icon-16.png", "32": "../../icons/icon-32.png", "48": "../../icons/icon-48.png", "128": "../../icons/icon-128.png"}}, "icons": {"16": "../../icons/icon-16.png", "32": "../../icons/icon-32.png", "48": "../../icons/icon-48.png", "128": "../../icons/icon-128.png"}, "web_accessible_resources": [{"resources": ["../../content-scripts/*", "../../icons/*", "../../frontend/*", "../../frontend/assets/*"], "matches": ["<all_urls>"]}], "options_page": "../../frontend/popup.html", "commands": {"toggle-recording": {"suggested_key": {"default": "Ctrl+Shift+V", "mac": "Command+Shift+V"}, "description": "Start/Stop voice recording"}}}
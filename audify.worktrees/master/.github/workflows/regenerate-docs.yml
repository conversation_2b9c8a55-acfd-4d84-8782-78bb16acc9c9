name: Re-Generate Docs

on:
  push:
    branches:
      - master
    paths:
      - "README.md"
      - "**.d.ts"

jobs:
  regenerate-docs:
    runs-on: ubuntu-latest

    steps:
      - name: Check-Out Code
        uses: actions/checkout@v3
        with:
          submodules: true
          token: ${{ secrets.DOCS_GITHUB_PAT }}

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: lts/*

      - name: Install Audify Dependencies
        run: npm ci --ignore-scripts

      - name: Re-Generate Docs
        run: |
          rm -rf ./docs
          npm run docs

      - name: Commit Docs
        uses: stefanzweifel/git-auto-commit-action@v4
        with:
          commit_message: "chore: regenerate docs"
          file_pattern: "docs"

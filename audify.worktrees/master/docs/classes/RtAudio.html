<!DOCTYPE html><html class="default" lang="en"><head><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>RtAudio | Audify.js</title><meta name="description" content="Documentation for Audify.js"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script defer src="../assets/main.js"></script><script async src="../assets/icons.js" id="tsd-icons-script"></script><script async src="../assets/search.js" id="tsd-search-script"></script><script async src="../assets/navigation.js" id="tsd-nav-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os";document.body.style.display="none";setTimeout(() => app?app.showPage():document.body.style.removeProperty("display"),500)</script><header class="tsd-page-toolbar"><div class="tsd-toolbar-contents container"><div class="table-cell" id="tsd-search" data-base=".."><div class="field"><label for="tsd-search-field" class="tsd-widget tsd-toolbar-icon search no-caption"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><use href="../assets/icons.svg#icon-search"></use></svg></label><input type="text" id="tsd-search-field" aria-label="Search"/></div><div class="field"><div id="tsd-toolbar-links"></div></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">Audify.js</a></div><div class="table-cell" id="tsd-widgets"><a href="#" class="tsd-widget tsd-toolbar-icon menu no-caption" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><use href="../assets/icons.svg#icon-menu"></use></svg></a></div></div></header><div class="container container-main"><div class="col-content"><div class="tsd-page-title"><ul class="tsd-breadcrumb"><li><a href="../modules.html">Audify.js</a></li><li><a href="RtAudio.html">RtAudio</a></li></ul><h1>Class RtAudio</h1></div><section class="tsd-panel tsd-comment"><div class="tsd-comment tsd-typography"><p>RtAudio provides a common API (Application Programming Interface)
for realtime audio input/output across Linux (native ALSA, Jack,
and OSS), Macintosh OS X (CoreAudio and Jack), and Windows
(DirectSound, ASIO and WASAPI) operating systems.</p>
</div><div class="tsd-comment tsd-typography"></div></section><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L243">index.d.ts:243</a></li></ul></aside><section class="tsd-panel-group tsd-index-group"><section class="tsd-panel tsd-index-panel"><details class="tsd-index-content tsd-index-accordion" open><summary class="tsd-accordion-summary tsd-index-summary"><h5 class="tsd-index-heading uppercase" role="button" aria-expanded="false" tabIndex="0"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><use href="../assets/icons.svg#icon-chevronSmall"></use></svg> Index</h5></summary><div class="tsd-accordion-details"><section class="tsd-index-section"><h3 class="tsd-index-heading">Constructors</h3><div class="tsd-index-list"><a href="RtAudio.html#constructor" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-512"></use></svg><span>constructor</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">Properties</h3><div class="tsd-index-list"><a href="RtAudio.html#outputVolume" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-1024"></use></svg><span>output<wbr/>Volume</span></a>
<a href="RtAudio.html#streamTime" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-1024"></use></svg><span>stream<wbr/>Time</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">Methods</h3><div class="tsd-index-list"><a href="RtAudio.html#clearOutputQueue" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>clear<wbr/>Output<wbr/>Queue</span></a>
<a href="RtAudio.html#closeStream" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>close<wbr/>Stream</span></a>
<a href="RtAudio.html#getApi" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Api</span></a>
<a href="RtAudio.html#getDefaultInputDevice" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Default<wbr/>Input<wbr/>Device</span></a>
<a href="RtAudio.html#getDefaultOutputDevice" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Default<wbr/>Output<wbr/>Device</span></a>
<a href="RtAudio.html#getDevices" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Devices</span></a>
<a href="RtAudio.html#getStreamLatency" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Stream<wbr/>Latency</span></a>
<a href="RtAudio.html#getStreamSampleRate" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Stream<wbr/>Sample<wbr/>Rate</span></a>
<a href="RtAudio.html#isStreamOpen" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>is<wbr/>Stream<wbr/>Open</span></a>
<a href="RtAudio.html#isStreamRunning" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>is<wbr/>Stream<wbr/>Running</span></a>
<a href="RtAudio.html#openStream" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>open<wbr/>Stream</span></a>
<a href="RtAudio.html#setFrameOutputCallback" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>set<wbr/>Frame<wbr/>Output<wbr/>Callback</span></a>
<a href="RtAudio.html#setInputCallback" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>set<wbr/>Input<wbr/>Callback</span></a>
<a href="RtAudio.html#start" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>start</span></a>
<a href="RtAudio.html#stop" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>stop</span></a>
<a href="RtAudio.html#write" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>write</span></a>
</div></section></div></details></section></section><section class="tsd-panel-group tsd-member-group"><h2>Constructors</h2><section class="tsd-panel tsd-member"><a id="constructor" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>constructor</span><a href="#constructor" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="constructor.new_RtAudio" class="tsd-anchor"></a><span class="tsd-kind-constructor-signature">new <wbr/>Rt<wbr/>Audio</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">api</span><span class="tsd-signature-symbol">?</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="RtAudio.html" class="tsd-signature-type tsd-kind-class">RtAudio</a><a href="#constructor.new_RtAudio" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-comment tsd-typography"><p>Create an RtAudio instance.</p>
</div><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><code class="tsd-tag ts-flagOptional">Optional</code> <span class="tsd-kind-parameter">api</span>: <a href="../enums/RtAudioApi.html" class="tsd-signature-type tsd-kind-enum">RtAudioApi</a></span><div class="tsd-comment tsd-typography"><p>The audio API to use. (Default will be automatically selected)</p>
</div><div class="tsd-comment tsd-typography"></div></li></ul></div><h4 class="tsd-returns-title">Returns <a href="RtAudio.html" class="tsd-signature-type tsd-kind-class">RtAudio</a></h4><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L254">index.d.ts:254</a></li></ul></aside></li></ul></section></section><section class="tsd-panel-group tsd-member-group"><h2>Properties</h2><section class="tsd-panel tsd-member"><a id="outputVolume" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>output<wbr/>Volume</span><a href="#outputVolume" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">output<wbr/>Volume</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div><div class="tsd-comment tsd-typography"><p>The volume of the output device. This should be a number between 0 and 1.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L245">index.d.ts:245</a></li></ul></aside></section><section class="tsd-panel tsd-member"><a id="streamTime" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>stream<wbr/>Time</span><a href="#streamTime" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">stream<wbr/>Time</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div><div class="tsd-comment tsd-typography"><p>The number of elapsed seconds since the stream was started. This should be a time in seconds greater than or equal to 0.0.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L248">index.d.ts:248</a></li></ul></aside></section></section><section class="tsd-panel-group tsd-member-group"><h2>Methods</h2><section class="tsd-panel tsd-member"><a id="clearOutputQueue" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>clear<wbr/>Output<wbr/>Queue</span><a href="#clearOutputQueue" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="clearOutputQueue.clearOutputQueue-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">clear<wbr/>Output<wbr/>Queue</span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span><a href="#clearOutputQueue.clearOutputQueue-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-comment tsd-typography"><p>Clears the output stream queue.</p>
</div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L317">index.d.ts:317</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="closeStream" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>close<wbr/>Stream</span><a href="#closeStream" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="closeStream.closeStream-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">close<wbr/>Stream</span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span><a href="#closeStream.closeStream-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-comment tsd-typography"><p>A function that closes a stream and frees any associated stream memory.</p>
</div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L286">index.d.ts:286</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="getApi" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>get<wbr/>Api</span><a href="#getApi" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="getApi.getApi-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">get<wbr/>Api</span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><a href="#getApi.getApi-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-comment tsd-typography"><p>Returns the full display name of the current used API.</p>
</div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L322">index.d.ts:322</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="getDefaultInputDevice" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>get<wbr/>Default<wbr/>Input<wbr/>Device</span><a href="#getDefaultInputDevice" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="getDefaultInputDevice.getDefaultInputDevice-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">get<wbr/>Default<wbr/>Input<wbr/>Device</span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><a href="#getDefaultInputDevice.getDefaultInputDevice-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-comment tsd-typography"><p>Returns the id of the default input device.</p>
</div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">number</span></h4><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L342">index.d.ts:342</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="getDefaultOutputDevice" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>get<wbr/>Default<wbr/>Output<wbr/>Device</span><a href="#getDefaultOutputDevice" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="getDefaultOutputDevice.getDefaultOutputDevice-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">get<wbr/>Default<wbr/>Output<wbr/>Device</span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><a href="#getDefaultOutputDevice.getDefaultOutputDevice-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-comment tsd-typography"><p>Returns the id of the default output device.</p>
</div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">number</span></h4><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L347">index.d.ts:347</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="getDevices" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>get<wbr/>Devices</span><a href="#getDevices" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="getDevices.getDevices-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">get<wbr/>Devices</span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="../interfaces/RtAudioDeviceInfo.html" class="tsd-signature-type tsd-kind-interface">RtAudioDeviceInfo</a><span class="tsd-signature-symbol">[]</span><a href="#getDevices.getDevices-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-comment tsd-typography"><p>Returns the list of available devices.</p>
</div><h4 class="tsd-returns-title">Returns <a href="../interfaces/RtAudioDeviceInfo.html" class="tsd-signature-type tsd-kind-interface">RtAudioDeviceInfo</a><span class="tsd-signature-symbol">[]</span></h4><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L337">index.d.ts:337</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="getStreamLatency" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>get<wbr/>Stream<wbr/>Latency</span><a href="#getStreamLatency" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="getStreamLatency.getStreamLatency-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">get<wbr/>Stream<wbr/>Latency</span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><a href="#getStreamLatency.getStreamLatency-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-comment tsd-typography"><p>Returns the internal stream latency in sample frames.</p>
</div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">number</span></h4><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L327">index.d.ts:327</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="getStreamSampleRate" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>get<wbr/>Stream<wbr/>Sample<wbr/>Rate</span><a href="#getStreamSampleRate" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="getStreamSampleRate.getStreamSampleRate-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">get<wbr/>Stream<wbr/>Sample<wbr/>Rate</span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><a href="#getStreamSampleRate.getStreamSampleRate-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-comment tsd-typography"><p>Returns actual sample rate in use by the stream.</p>
</div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">number</span></h4><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L332">index.d.ts:332</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="isStreamOpen" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>is<wbr/>Stream<wbr/>Open</span><a href="#isStreamOpen" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="isStreamOpen.isStreamOpen-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">is<wbr/>Stream<wbr/>Open</span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><a href="#isStreamOpen.isStreamOpen-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-comment tsd-typography"><p>Returns true if a stream is open and false if not.</p>
</div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L291">index.d.ts:291</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="isStreamRunning" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>is<wbr/>Stream<wbr/>Running</span><a href="#isStreamRunning" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="isStreamRunning.isStreamRunning-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">is<wbr/>Stream<wbr/>Running</span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><a href="#isStreamRunning.isStreamRunning-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-comment tsd-typography"><p>Returns true if the stream is running and false if it is stopped or not open.</p>
</div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L306">index.d.ts:306</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="openStream" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>open<wbr/>Stream</span><a href="#openStream" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="openStream.openStream-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">open<wbr/>Stream</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">outputParameters</span>, <span class="tsd-kind-parameter">inputParameters</span>, <span class="tsd-kind-parameter">format</span>, <span class="tsd-kind-parameter">sampleRate</span>, <span class="tsd-kind-parameter">frameSize</span>, <span class="tsd-kind-parameter">streamName</span>, <span class="tsd-kind-parameter">inputCallback</span>, <span class="tsd-kind-parameter">frameOutputCallback</span>, <span class="tsd-kind-parameter">flags</span><span class="tsd-signature-symbol">?</span>, <span class="tsd-kind-parameter">errorCallback</span><span class="tsd-signature-symbol">?</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><a href="#openStream.openStream-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-comment tsd-typography"><p>A public function for opening a stream with the specified parameters. Returns the actual frameSize used by the stream, useful if a frameSize of 0 is passed.</p>
</div><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">outputParameters</span>: <span class="tsd-signature-type">null</span><span class="tsd-signature-symbol"> | </span><a href="../interfaces/RtAudioStreamParameters.html" class="tsd-signature-type tsd-kind-interface">RtAudioStreamParameters</a></span><div class="tsd-comment tsd-typography"><p>Specifies output stream parameters to use when opening a stream. For input-only streams, this argument should be null.</p>
</div><div class="tsd-comment tsd-typography"></div></li><li><span><span class="tsd-kind-parameter">inputParameters</span>: <span class="tsd-signature-type">null</span><span class="tsd-signature-symbol"> | </span><a href="../interfaces/RtAudioStreamParameters.html" class="tsd-signature-type tsd-kind-interface">RtAudioStreamParameters</a></span><div class="tsd-comment tsd-typography"><p>Specifies input stream parameters to use when opening a stream. For output-only streams, this argument should be null.</p>
</div><div class="tsd-comment tsd-typography"></div></li><li><span><span class="tsd-kind-parameter">format</span>: <a href="../enums/RtAudioFormat.html" class="tsd-signature-type tsd-kind-enum">RtAudioFormat</a></span><div class="tsd-comment tsd-typography"><p>An RtAudio.Format specifying the desired sample data format.</p>
</div><div class="tsd-comment tsd-typography"></div></li><li><span><span class="tsd-kind-parameter">sampleRate</span>: <span class="tsd-signature-type">number</span></span><div class="tsd-comment tsd-typography"><p>The desired sample rate (sample frames per second).</p>
</div><div class="tsd-comment tsd-typography"></div></li><li><span><span class="tsd-kind-parameter">frameSize</span>: <span class="tsd-signature-type">number</span></span><div class="tsd-comment tsd-typography"><p>The amount of samples per frame. Can be 0 for some APIs, in which case the lowest allowable value is determined; this is necessary for the ASIO &amp; Jack APIs where the user can set an overriding global buffer size for their device.</p>
</div><div class="tsd-comment tsd-typography"></div></li><li><span><span class="tsd-kind-parameter">streamName</span>: <span class="tsd-signature-type">string</span></span><div class="tsd-comment tsd-typography"><p>A stream name (currently used only in Jack).</p>
</div><div class="tsd-comment tsd-typography"></div></li><li><span><span class="tsd-kind-parameter">inputCallback</span>: <span class="tsd-signature-type">null</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">inputData</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">)</span></span><div class="tsd-comment tsd-typography"><p>A callback that is called when a new input signal is available. Should be null for output-only streams.</p>
</div><div class="tsd-comment tsd-typography"></div></li><li><span><span class="tsd-kind-parameter">frameOutputCallback</span>: <span class="tsd-signature-type">null</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">)</span></span><div class="tsd-comment tsd-typography"><p>A callback that is called when a frame is finished playing in the output device.</p>
</div><div class="tsd-comment tsd-typography"></div></li><li><span><code class="tsd-tag ts-flagOptional">Optional</code> <span class="tsd-kind-parameter">flags</span>: <a href="../enums/RtAudioStreamFlags.html" class="tsd-signature-type tsd-kind-enum">RtAudioStreamFlags</a></span><div class="tsd-comment tsd-typography"><p>A bit-mask of stream flags (RtAudio.StreamFlags).</p>
</div><div class="tsd-comment tsd-typography"></div></li><li><span><code class="tsd-tag ts-flagOptional">Optional</code> <span class="tsd-kind-parameter">errorCallback</span>: <span class="tsd-signature-type">null</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">type</span>, <span class="tsd-kind-parameter">msg</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">)</span></span><div class="tsd-comment tsd-typography"><p>A callback that is called when an error has occurred.</p>
</div><div class="tsd-comment tsd-typography"></div></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">number</span></h4><p>The actual frame-size used for stream. Useful if passed 0 as frameSize.</p>
<div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L270">index.d.ts:270</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="setFrameOutputCallback" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>set<wbr/>Frame<wbr/>Output<wbr/>Callback</span><a href="#setFrameOutputCallback" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="setFrameOutputCallback.setFrameOutputCallback-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">set<wbr/>Frame<wbr/>Output<wbr/>Callback</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">callback</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span><a href="#setFrameOutputCallback.setFrameOutputCallback-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-comment tsd-typography"><p>Sets the frame output playback for the output device.</p>
</div><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">callback</span>: <span class="tsd-signature-type">null</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">)</span></span><div class="tsd-comment tsd-typography"><p>A callback that is called when a frame is finished playing in the output device.</p>
</div><div class="tsd-comment tsd-typography"></div></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L359">index.d.ts:359</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="setInputCallback" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>set<wbr/>Input<wbr/>Callback</span><a href="#setInputCallback" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="setInputCallback.setInputCallback-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">set<wbr/>Input<wbr/>Callback</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">callback</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span><a href="#setInputCallback.setInputCallback-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-comment tsd-typography"><p>Sets the input callback function for the input device.</p>
</div><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">callback</span>: <span class="tsd-signature-type">null</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">inputData</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">)</span></span><div class="tsd-comment tsd-typography"><p>A callback that is called when a new input signal is available. Should be null for output-only streams.</p>
</div><div class="tsd-comment tsd-typography"></div></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L353">index.d.ts:353</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="start" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>start</span><a href="#start" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="start.start-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">start</span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span><a href="#start.start-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-comment tsd-typography"><p>Start the stream.</p>
</div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L296">index.d.ts:296</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="stop" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>stop</span><a href="#stop" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="stop.stop-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">stop</span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span><a href="#stop.stop-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-comment tsd-typography"><p>Stop the stream.</p>
</div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L301">index.d.ts:301</a></li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="write" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>write</span><a href="#write" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="write.write-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">write</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">pcm</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span><a href="#write.write-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-comment tsd-typography"><p>Queues a new output PCM data to be played using the stream.</p>
</div><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">pcm</span>: <span class="tsd-signature-type">Buffer</span></span><div class="tsd-comment tsd-typography"><p>The raw PCM data. The length should be frame_size * no_of_output_channels * size_of_sample.</p>
</div><div class="tsd-comment tsd-typography"></div></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L312">index.d.ts:312</a></li></ul></aside></li></ul></section></section></div><div class="col-sidebar"><div class="page-menu"><div class="tsd-navigation settings"><details class="tsd-index-accordion"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Settings</h3></summary><div class="tsd-accordion-details"><div class="tsd-filter-visibility"><h4 class="uppercase">Member Visibility</h4><form><ul id="tsd-filter-options"><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-protected" name="protected"/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Protected</span></label></li><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-private" name="private"/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Private</span></label></li><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-inherited" name="inherited" checked/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Inherited</span></label></li><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-external" name="external"/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>External</span></label></li></ul></form></div><div class="tsd-theme-toggle"><h4 class="uppercase">Theme</h4><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div><details open class="tsd-index-accordion tsd-page-navigation"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>On This Page</h3></summary><div class="tsd-accordion-details"><a href="#constructor" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-512"></use></svg><span>constructor</span></a><a href="#outputVolume" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-1024"></use></svg><span>output<wbr/>Volume</span></a><a href="#streamTime" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-1024"></use></svg><span>stream<wbr/>Time</span></a><a href="#clearOutputQueue" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>clear<wbr/>Output<wbr/>Queue</span></a><a href="#closeStream" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>close<wbr/>Stream</span></a><a href="#getApi" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Api</span></a><a href="#getDefaultInputDevice" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Default<wbr/>Input<wbr/>Device</span></a><a href="#getDefaultOutputDevice" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Default<wbr/>Output<wbr/>Device</span></a><a href="#getDevices" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Devices</span></a><a href="#getStreamLatency" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Stream<wbr/>Latency</span></a><a href="#getStreamSampleRate" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Stream<wbr/>Sample<wbr/>Rate</span></a><a href="#isStreamOpen" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>is<wbr/>Stream<wbr/>Open</span></a><a href="#isStreamRunning" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>is<wbr/>Stream<wbr/>Running</span></a><a href="#openStream" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>open<wbr/>Stream</span></a><a href="#setFrameOutputCallback" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>set<wbr/>Frame<wbr/>Output<wbr/>Callback</span></a><a href="#setInputCallback" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>set<wbr/>Input<wbr/>Callback</span></a><a href="#start" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>start</span></a><a href="#stop" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>stop</span></a><a href="#write" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>write</span></a></div></details></div><div class="site-menu"><nav class="tsd-navigation"><a href="../modules.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-1"></use></svg><span>Audify.js</span></a><ul class="tsd-small-nested-navigation" id="tsd-nav-container" data-base=".."><li>Loading...</li></ul></nav></div></div></div><footer></footer><div class="overlay"></div></body></html>
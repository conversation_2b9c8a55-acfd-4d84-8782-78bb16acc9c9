#!/usr/bin/env python3
import asyncio
import aiosmtplib
import csv
import os
import aiohttp
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import formataddr
from dotenv import load_dotenv
from typing import List, Dict, Optional

# Load environment variables
load_dotenv()

class EmailSender:
    def __init__(self):
        self.smtp_host = os.getenv("SMTP_HOST", "smtp.elasticemail.com")
        self.smtp_port = int(os.getenv("SMTP_PORT", "2525"))
        self.smtp_user = os.getenv("SMTP_USER", "")
        self.smtp_password = os.getenv("SMTP_PASSWORD", "")
        self.api_key = os.getenv("ELASTICEMAIL_API_KEY", "")
        self.from_email = os.getenv("FROM_EMAIL", "<EMAIL>")
        self.from_name = os.getenv("FROM_NAME", "VoiceHype")
        
    async def fetch_template(self, template_name: str) -> str:
        """Fetch email template from Elastic Email API"""
        url = f"https://api.elasticemail.com/v4/templates/{template_name}"
        headers = {"X-ElasticEmail-ApiKey": self.api_key}
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get("Body", "")
                else:
                    error = await response.text()
                    raise Exception(f"Failed to fetch template: {error}")
    
    async def send_email(
        self,
        to_email: str,
        to_name: str,
        subject: str,
        html_content: str,
        is_template: bool = False,
        template_name: Optional[str] = None
    ) -> bool:
        """Send email via SMTP"""
        message = MIMEMultipart("alternative")
        message["Subject"] = subject
        message["From"] = formataddr((self.from_name, self.from_email))
        message["To"] = formataddr((to_name, to_email))
        
        # If using template, we need to add merge fields
        if is_template and template_name:
            message.add_header("X-ElasticEmail-Template", template_name)
            message.add_header("X-ElasticEmail-Merge-name", to_name)
        
        # Attach HTML content
        part = MIMEText(html_content, "html")
        message.attach(part)
        
        try:
            async with aiosmtplib.SMTP(
                hostname=self.smtp_host,
                port=self.smtp_port,
                use_tls=True
            ) as smtp:
                await smtp.login(self.smtp_user, self.smtp_password)
                await smtp.send_message(message)
                return True
        except Exception as e:
            print(f"Failed to send email to {to_email}: {str(e)}")
            return False
    
    async def process_csv(
        self,
        csv_path: str,
        subject: str,
        html_content: str,
        is_template: bool = False,
        template_name: Optional[str] = None
    ) -> Dict[str, int]:
        """Process CSV file and send emails"""
        results = {"success": 0, "failed": 0}
        tasks = []
        
        with open(csv_path, mode="r", encoding="utf-8") as file:
            reader = csv.DictReader(file)
            recipients = list(reader)
            
            # Create tasks for each recipient
            for row in recipients:
                name = row.get("name", "")
                email = row.get("email", "")
                
                if not email:
                    print(f"Skipping row with missing email: {row}")
                    results["failed"] += 1
                    continue
                
                # Personalize HTML content
                personalized_html = html_content.replace("{name}", name)
                
                task = self.send_email(
                    to_email=email,
                    to_name=name,
                    subject=subject,
                    html_content=personalized_html,
                    is_template=is_template,
                    template_name=template_name
                )
                tasks.append(task)
        
        # Execute all tasks concurrently with semaphore to limit connections
        semaphore = asyncio.Semaphore(10)  # Limit to 10 concurrent connections
        
        async def send_with_semaphore(task):
            async with semaphore:
                return await task
        
        results_list = await asyncio.gather(
            *[send_with_semaphore(task) for task in tasks],
            return_exceptions=True
        )
        
        # Count results
        for result in results_list:
            if isinstance(result, Exception) or result is False:
                results["failed"] += 1
            else:
                results["success"] += 1
        
        return results

async def interactive_mode():
    """Interactive mode for the email sender"""
    print("🚀 VoiceHype Email Sender")
    print("=" * 40)
    
    # Initialize email sender
    sender = EmailSender()
    
    # Get CSV file path
    csv_path = input("Enter path to CSV file (name,email columns): ").strip()
    if not os.path.exists(csv_path):
        print(f"❌ File not found: {csv_path}")
        return
    
    # Get email type
    print("\nSelect email type:")
    print("1. Regular email (HTML file)")
    print("2. Elastic Email template")
    email_type = input("Enter your choice (1 or 2): ").strip()
    
    if email_type == "1":
        # Regular email
        html_path = input("Enter path to HTML file: ").strip()
        if not os.path.exists(html_path):
            print(f"❌ File not found: {html_path}")
            return
        
        with open(html_path, "r", encoding="utf-8") as f:
            html_content = f.read()
        
        template_name = None
        is_template = False
    elif email_type == "2":
        # Template email
        template_name = input("Enter Elastic Email template name: ").strip()
        try:
            html_content = await sender.fetch_template(template_name)
            is_template = True
        except Exception as e:
            print(f"❌ Failed to fetch template: {str(e)}")
            return
    else:
        print("❌ Invalid choice")
        return
    
    # Get email subject
    subject = input("Enter email subject: ").strip()
    if not subject:
        print("❌ Subject cannot be empty")
        return
    
    # Confirm before sending
    print("\n📧 Email Details:")
    print(f"CSV file: {csv_path}")
    print(f"Subject: {subject}")
    if is_template:
        print(f"Template: {template_name}")
    else:
        print(f"HTML file: {html_path}")
    
    confirm = input("\nSend emails? (y/n): ").strip().lower()
    if confirm != "y":
        print("❌ Sending cancelled")
        return
    
    # Process CSV and send emails
    print("\n📤 Sending emails...")
    results = await sender.process_csv(
        csv_path=csv_path,
        subject=subject,
        html_content=html_content,
        is_template=is_template,
        template_name=template_name
    )
    
    # Display results
    print("\n✅ Sending complete!")
    print(f"Successfully sent: {results['success']}")
    print(f"Failed: {results['failed']}")

def main():
    """Main entry point"""
    try:
        asyncio.run(interactive_mode())
    except KeyboardInterrupt:
        print("\n❌ Operation cancelled by user")
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")

if __name__ == "__main__":
    main()
# Voice Hype Blog - Technical Specification

## Project Overview
**URL**: blog.voicehive.ai  
**Tech Stack**: Astro + Vue.js + Supabase  
**Hosting**: DigitalOcean (self-hosted)  
**Purpose**: Content marketing blog for Voice Hype product

---

## Architecture Overview

### Frontend Stack
- **Astro**: Static site generation for optimal SEO
- **Vue.js**: Interactive components and admin interface
- **TailwindCSS**: Styling framework
- **Markdown-it**: Markdown parsing
- **CodeMirror 6**: Real-time markdown editor
- **Prism.js**: Code syntax highlighting

### Backend Stack
- **Supabase**: Database, authentication, real-time features
- **PostgreSQL**: Primary database
- **Supabase Storage**: Image/media uploads
- **Supabase Auth**: User management

---

## Database Schema

### Core Tables

```sql
-- Authors/Users table
CREATE TABLE profiles (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    username TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    email TEXT NOT NULL,
    avatar_url TEXT,
    role TEXT DEFAULT 'author' CHECK (role IN ('admin', 'editor', 'author')),
    bio TEXT,
    social_links JSONB, -- {twitter: "...", linkedin: "..."}
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Articles table
CREATE TABLE articles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    content TEXT NOT NULL, -- Markdown content
    excerpt TEXT,
    author_id UUID REFERENCES profiles(id) NOT NULL,
    co_author_id UUID REFERENCES profiles(id), -- For collaboration
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'review', 'published', 'archived')),
    published_at TIMESTAMPTZ,
    featured_image_url TEXT,
    featured_image_alt TEXT,
    meta_title TEXT, -- SEO title (can differ from main title)
    meta_description TEXT,
    tags TEXT[] DEFAULT '{}',
    categories TEXT[] DEFAULT '{}',
    reading_time INTEGER, -- Calculated in minutes
    view_count INTEGER DEFAULT 0,
    like_count INTEGER DEFAULT 0,
    comment_count INTEGER DEFAULT 0,
    featured BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Comments system
CREATE TABLE comments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    article_id UUID REFERENCES articles(id) ON DELETE CASCADE,
    parent_id UUID REFERENCES comments(id) ON DELETE CASCADE, -- For threaded comments
    author_name TEXT NOT NULL,
    author_email TEXT NOT NULL,
    author_website TEXT,
    content TEXT NOT NULL,
    approved BOOLEAN DEFAULT FALSE,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Article reactions (likes, bookmarks, etc.)
CREATE TABLE article_reactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    article_id UUID REFERENCES articles(id) ON DELETE CASCADE,
    user_id UUID REFERENCES profiles(id),
    session_id TEXT, -- For anonymous users
    reaction_type TEXT DEFAULT 'like' CHECK (reaction_type IN ('like', 'bookmark', 'share')),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(article_id, user_id, reaction_type),
    UNIQUE(article_id, session_id, reaction_type)
);

-- Tags management
CREATE TABLE tags (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    description TEXT,
    color TEXT DEFAULT '#3B82F6', -- Hex color for tag display
    article_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Categories management
CREATE TABLE categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    description TEXT,
    parent_id UUID REFERENCES categories(id),
    article_count INTEGER DEFAULT 0,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Newsletter subscribers
CREATE TABLE newsletter_subscribers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    name TEXT,
    subscribed BOOLEAN DEFAULT TRUE,
    confirmed BOOLEAN DEFAULT FALSE,
    confirmation_token TEXT,
    subscribed_at TIMESTAMPTZ DEFAULT NOW(),
    unsubscribed_at TIMESTAMPTZ
);

-- Analytics/tracking
CREATE TABLE article_views (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    article_id UUID REFERENCES articles(id) ON DELETE CASCADE,
    user_id UUID REFERENCES profiles(id),
    session_id TEXT,
    ip_address INET,
    user_agent TEXT,
    referrer TEXT,
    viewed_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Database Indexes
```sql
-- Performance indexes
CREATE INDEX idx_articles_status ON articles(status);
CREATE INDEX idx_articles_published_at ON articles(published_at DESC) WHERE status = 'published';
CREATE INDEX idx_articles_author ON articles(author_id);
CREATE INDEX idx_articles_slug ON articles(slug);
CREATE INDEX idx_articles_tags ON articles USING GIN(tags);
CREATE INDEX idx_comments_article ON comments(article_id);
CREATE INDEX idx_article_views_article ON article_views(article_id);
CREATE INDEX idx_article_views_date ON article_views(viewed_at);

-- Text search index for full-text search
CREATE INDEX idx_articles_search ON articles USING GIN(
    to_tsvector('english', title || ' ' || content || ' ' || coalesce(excerpt, ''))
);
```

---

## Real-Time Markdown Editor

### Editor Features
- **Split-pane view**: Markdown on left, rendered preview on right
- **Live preview**: Updates as you type (debounced)
- **Syntax highlighting**: In the markdown editor
- **Auto-save**: Draft saves every 30 seconds
- **Image upload**: Drag & drop with Supabase Storage
- **Table editor**: Visual table creation
- **Code block**: Language selection with syntax highlighting
- **Math support**: KaTeX for mathematical expressions
- **Emoji picker**: Quick emoji insertion
- **Word count**: Live word/character count
- **Reading time**: Auto-calculated
- **Distraction-free mode**: Full-screen writing

### Editor Implementation

```javascript
// Editor component structure
const MarkdownEditor = {
  components: {
    CodeMirror, // Markdown input
    MarkdownRenderer, // Live preview
    ImageUploader,
    TableEditor,
    EmojiPicker
  },
  
  data() {
    return {
      content: '',
      previewMode: 'split', // 'split', 'preview', 'editor'
      autoSaveInterval: null,
      lastSaved: null,
      wordCount: 0,
      readingTime: 0,
      isFullscreen: false
    }
  },
  
  methods: {
    updatePreview: debounce(function() {
      // Update preview pane
      this.calculateStats()
    }, 300),
    
    autoSave: debounce(function() {
      // Save to Supabase
      this.saveDraft()
    }, 30000),
    
    uploadImage(file) {
      // Handle image uploads to Supabase Storage
    },
    
    insertTable() {
      // Insert markdown table
    },
    
    toggleFullscreen() {
      // Toggle distraction-free mode
    }
  }
}
```

### Editor Toolbar
- **Formatting**: Bold, italic, strikethrough, code
- **Headers**: H1-H6 quick buttons
- **Lists**: Bulleted, numbered, task lists
- **Links**: Link insertion with preview
- **Images**: Upload or URL insertion
- **Tables**: Visual table creator
- **Code blocks**: Language selection
- **Quotes**: Blockquote insertion
- **Math**: LaTeX/KaTeX formulas
- **Preview toggle**: Switch between edit/preview modes

---

## Project Structure

```
voice-hype-blog/
├── src/
│   ├── components/
│   │   ├── Editor/
│   │   │   ├── MarkdownEditor.vue
│   │   │   ├── CodeMirrorEditor.vue
│   │   │   ├── PreviewPane.vue
│   │   │   ├── Toolbar.vue
│   │   │   ├── ImageUploader.vue
│   │   │   └── TableEditor.vue
│   │   ├── Blog/
│   │   │   ├── ArticleCard.vue
│   │   │   ├── ArticleList.vue
│   │   │   ├── TagCloud.vue
│   │   │   ├── CategoryNav.vue
│   │   │   └── Newsletter.vue
│   │   ├── Comments/
│   │   │   ├── CommentSection.vue
│   │   │   ├── CommentForm.vue
│   │   │   └── CommentThread.vue
│   │   ├── Admin/
│   │   │   ├── Dashboard.vue
│   │   │   ├── ArticleManager.vue
│   │   │   ├── UserManager.vue
│   │   │   └── Analytics.vue
│   │   └── Common/
│   │       ├── Header.astro
│   │       ├── Footer.astro
│   │       ├── SEO.astro
│   │       └── Layout.astro
│   ├── pages/
│   │   ├── index.astro (Blog homepage)
│   │   ├── blog/
│   │   │   ├── [slug].astro (Individual articles)
│   │   │   └── index.astro (Article listing)
│   │   ├── admin/
│   │   │   ├── index.astro (Dashboard)
│   │   │   ├── editor/
│   │   │   │   ├── new.astro
│   │   │   │   └── [id].astro
│   │   │   └── settings.astro
│   │   ├── tag/
│   │   │   └── [slug].astro
│   │   ├── category/
│   │   │   └── [slug].astro
│   │   ├── author/
│   │   │   └── [slug].astro
│   │   ├── about.astro
│   │   ├── contact.astro
│   │   ├── privacy.astro
│   │   ├── sitemap.xml.js
│   │   └── rss.xml.js
│   ├── lib/
│   │   ├── supabase.js
│   │   ├── markdown.js
│   │   ├── seo.js
│   │   ├── utils.js
│   │   └── auth.js
│   └── styles/
│       ├── global.css
│       ├── editor.css
│       └── components.css
├── public/
│   ├── robots.txt
│   ├── favicon.ico
│   └── og-image.jpg
├── astro.config.mjs
├── tailwind.config.js
├── package.json
└── README.md
```

---

## User Roles & Permissions

### Role Hierarchy
1. **Admin**: Full access to everything
2. **Editor**: Can publish, edit all articles, manage comments
3. **Author**: Can create/edit own articles, submit for review

### Permission Matrix
| Action | Admin | Editor | Author |
|--------|-------|--------|--------|
| Create articles | ✅ | ✅ | ✅ |
| Edit own articles | ✅ | ✅ | ✅ |
| Edit others' articles | ✅ | ✅ | ❌ |
| Publish articles | ✅ | ✅ | ❌* |
| Delete articles | ✅ | ✅ | Own only |
| Manage comments | ✅ | ✅ | ❌ |
| Manage users | ✅ | ❌ | ❌ |
| View analytics | ✅ | ✅ | Own only |
| Site settings | ✅ | ❌ | ❌ |

*Authors can submit for review, editors/admins approve

---

## SEO Implementation

### On-Page SEO
- **Dynamic meta tags**: Title, description, keywords
- **Open Graph**: Social media sharing
- **Twitter Cards**: Enhanced Twitter sharing
- **Structured data**: JSON-LD for articles
- **Canonical URLs**: Prevent duplicate content
- **XML sitemap**: Auto-generated
- **RSS feed**: For syndication

### Technical SEO
- **Page speed**: Astro's static generation
- **Mobile-first**: Responsive design
- **Core Web Vitals**: Optimized loading
- **Clean URLs**: `/blog/article-slug`
- **Internal linking**: Related articles
- **Image optimization**: WebP, lazy loading
- **Schema markup**: Article, Author, Organization

### SEO Component Example
```astro
---
// src/components/SEO.astro
export interface Props {
  title: string
  description: string
  image?: string
  article?: boolean
  publishedTime?: string
  modifiedTime?: string
}

const { title, description, image, article, publishedTime, modifiedTime } = Astro.props
const canonicalURL = new URL(Astro.url.pathname, Astro.site)
---

<title>{title} | Voice Hype Blog</title>
<meta name="description" content={description} />
<meta name="robots" content="index, follow" />
<link rel="canonical" href={canonicalURL} />

<!-- Open Graph -->
<meta property="og:type" content={article ? "article" : "website"} />
<meta property="og:title" content={title} />
<meta property="og:description" content={description} />
<meta property="og:url" content={canonicalURL} />
<meta property="og:site_name" content="Voice Hype Blog" />
{image && <meta property="og:image" content={image} />}
{publishedTime && <meta property="article:published_time" content={publishedTime} />}
{modifiedTime && <meta property="article:modified_time" content={modifiedTime} />}

<!-- Twitter -->
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content={title} />
<meta name="twitter:description" content={description} />
{image && <meta name="twitter:image" content={image} />}
```

---

## Development Phases

### Phase 1: Core Foundation (Week 1-2)
- [ ] Supabase setup and schema implementation
- [ ] Astro project initialization
- [ ] Authentication system
- [ ] Basic layouts and routing
- [ ] Article display (static)

### Phase 2: Content Management (Week 3-4)
- [ ] Real-time markdown editor
- [ ] Image upload functionality  
- [ ] Draft/publish workflow
- [ ] Author management
- [ ] Basic admin dashboard

### Phase 3: Reader Features (Week 5-6)
- [ ] Comments system
- [ ] Article reactions (likes, bookmarks)
- [ ] Search functionality
- [ ] Newsletter signup
- [ ] Social sharing

### Phase 4: SEO & Analytics (Week 7-8)
- [ ] Complete SEO implementation
- [ ] XML sitemap generation
- [ ] RSS feed
- [ ] Analytics dashboard
- [ ] Performance optimization

### Phase 5: Advanced Features (Week 9-10)
- [ ] Related articles algorithm
- [ ] Full-text search
- [ ] Email notifications
- [ ] Advanced editor features
- [ ] Mobile app considerations

---

## Technical Considerations

### Performance
- **Static generation**: Pre-build all public pages
- **Image optimization**: WebP conversion, lazy loading
- **Code splitting**: Load admin features separately
- **CDN**: Use for assets
- **Database**: Proper indexing for queries

### Security
- **RLS policies**: Supabase row-level security
- **Input validation**: Sanitize all user inputs
- **CSRF protection**: For admin actions
- **Rate limiting**: Prevent spam/abuse
- **Content security**: XSS prevention

### Monitoring
- **Error tracking**: Sentry or similar
- **Performance**: Core Web Vitals monitoring
- **Uptime**: Server monitoring
- **Analytics**: Custom dashboard + Google Analytics
- **Database**: Query performance monitoring

---

## Deployment Strategy

### Production Environment
- **DigitalOcean Droplet**: Main hosting
- **Domain**: blog.voicehive.ai
- **SSL**: Let's Encrypt
- **CDN**: CloudFlare for static assets
- **Backup**: Automated database backups

### CI/CD Pipeline
1. **Git push** to main branch
2. **GitHub Actions** triggers build
3. **Astro build** generates static site
4. **Deploy** to DigitalOcean
5. **Cache invalidation** if using CDN

### Environment Variables
```bash
# Supabase
SUPABASE_URL=
SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

# Site
SITE_URL=https://blog.voicehive.ai
ADMIN_EMAIL=

# Analytics
GOOGLE_ANALYTICS_ID=
```

---

## Success Metrics

### Technical KPIs
- **Page Speed**: < 3 seconds loading time
- **SEO Score**: 90+ on PageSpeed Insights
- **Uptime**: 99.9% availability
- **Core Web Vitals**: Green scores

### Content KPIs
- **Organic traffic**: Monthly growth tracking
- **Search rankings**: Target keywords monitoring
- **Engagement**: Time on page, bounce rate
- **Conversions**: Newsletter signups, product referrals

### User Experience
- **Editor efficiency**: Time to publish articles
- **Mobile experience**: Mobile-first design
- **Accessibility**: WCAG compliance
- **Loading performance**: Fast content delivery

---

## Future Enhancements

### Content Features
- **Multi-language support**: i18n implementation
- **Podcast integration**: Audio content
- **Video embedding**: YouTube integration
- **Interactive content**: Polls, quizzes

### Technical Improvements
- **PWA**: Progressive Web App features
- **Offline reading**: Service worker caching
- **Real-time collaboration**: Multiple authors editing
- **AI assistance**: Content suggestions, grammar check

### Marketing Integration
- **Email marketing**: Advanced segmentation
- **Social automation**: Auto-posting
- **A/B testing**: Content optimization
- **Affiliate tracking**: Revenue attribution

---

*This specification should serve as your north star for building the Voice Hype blog. Each section can be expanded as development progresses. May Allah grant success in this endeavor!*

**Last Updated**: August 2025  
**Version**: 1.0
# Blog Database Relationships & Flow Guide

## Overview
This document explains how all the tables in the blog schema relate to each other and the data flow for common operations.

## Table Relationships Diagram

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   profiles      │    │   articles      │    │   categories    │
│   (users)       │    │   (blog posts)  │    │   (grouping)    │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ id (PK)         │◄───┤ id (PK)         │    │ id (PK)         │
│ username        │    │ title           │    │ name            │
│ full_name       │    │ slug            │    │ slug            │
│ role            │    │ content         │    │ description     │
│ avatar_url      │    │ author_id (FK)  │◄───┤                 │
└─────────────────┘    │ status          │    └─────────────────┘
                       │ published_at    │
                       │ view_count      │
                       └─────────────────┘
                                │
                                │
                       ┌─────────────────┐
                       │   article_tags  │ (junction)
                       ├─────────────────┤
                       │ article_id (FK) │
                       │ tag_id (FK)     │
                       └─────────────────┘
                                │
                       ┌─────────────────┐
                       │      tags       │
                       ├─────────────────┤
                       │ id (PK)         │
                       │ name            │
                       │ slug            │
                       │ description     │
                       └─────────────────┘
```

## Detailed Relationships

### 1. **User System (profiles)**
- **One-to-Many** with `articles`: One user can write many articles
- **One-to-Many** with `comments`: One user can write many comments
- **One-to-Many** with `article_reactions`: One user can react to many articles
- **One-to-Many** with `article_views`: One user can view many articles

### 2. **Article System (articles)**
- **Many-to-One** with `profiles`: Each article has one author
- **Many-to-Many** with `tags` through `article_tags`
- **Many-to-Many** with `categories` through `article_categories`
- **One-to-Many** with `comments`: One article can have many comments
- **One-to-Many** with `article_reactions`: One article can have many reactions
- **One-to-Many** with `article_views`: One article can have many views

### 3. **Content Organization**
- **Categories**: Broad grouping (e.g., "Technology", "Lifestyle")
- **Tags**: Specific topics (e.g., "JavaScript", "Vue.js")
- **Many-to-Many** relationships allow flexible content organization

### 4. **Engagement System**
- **Comments**: User-generated discussion on articles
- **Reactions**: Simple engagement (likes, loves, etc.)
- **Views**: Analytics tracking for article performance

## Data Flow Examples

### 1. **Publishing a New Article**
```
1. User (profile) creates article
   ↓
2. Article gets assigned to author_id
   ↓
3. Tags and categories are linked via junction tables
   ↓
4. Article status changes to 'published'
   ↓
5. Article becomes visible to public
```

### 2. **Article Display Flow**
```
1. User visits /articles/[slug]
   ↓
2. Query article by slug
   ↓
3. Get author info from profiles
   ↓
4. Get tags via article_tags → tags
   ↓
5. Get categories via article_categories → categories
   ↓
6. Get approved comments
   ↓
7. Get reaction counts
   ↓
8. Record view in article_views
```

### 3. **User Engagement Flow**
```
1. User reads article
   ↓
2. User can:
   - Leave comment (creates comment record)
   - React to article (creates article_reactions record)
   - View article (creates article_views record)
   ↓
3. Comments go through moderation (status field)
   ↓
4. Reactions are immediately visible
```

### 4. **Content Discovery Flow**
```
1. User visits homepage
   ↓
2. Query latest published articles
   ↓
3. Filter by category/tag if specified
   ↓
4. Show article metadata (author, tags, view count)
   ↓
5. Link to full article
```

## Key Queries & Use Cases

### 1. **Get Article with Full Details**
```sql
SELECT 
  a.*,
  p.username as author_username,
  p.full_name as author_name,
  p.avatar_url as author_avatar,
  array_agg(DISTINCT t.name) as tags,
  array_agg(DISTINCT c.name) as categories,
  COUNT(DISTINCT ar.id) as reaction_count,
  COUNT(DISTINCT co.id) as comment_count
FROM blog.articles a
JOIN blog.profiles p ON a.author_id = p.id
LEFT JOIN blog.article_tags at ON a.id = at.article_id
LEFT JOIN blog.tags t ON at.tag_id = t.id
LEFT JOIN blog.article_categories ac ON a.id = ac.article_id
LEFT JOIN blog.categories c ON ac.category_id = c.id
LEFT JOIN blog.article_reactions ar ON a.id = ar.article_id
LEFT JOIN blog.comments co ON a.id = co.article_id AND co.status = 'approved'
WHERE a.slug = $1 AND a.status = 'published'
GROUP BY a.id, p.username, p.full_name, p.avatar_url;
```

### 2. **Get Articles by Category**
```sql
SELECT a.*, p.username as author_username
FROM blog.articles a
JOIN blog.profiles p ON a.author_id = p.id
JOIN blog.article_categories ac ON a.id = ac.article_id
JOIN blog.categories c ON ac.category_id = c.id
WHERE c.slug = $1 AND a.status = 'published'
ORDER BY a.published_at DESC;
```

### 3. **Get User's Articles**
```sql
SELECT a.*, 
       array_agg(DISTINCT t.name) as tags,
       COUNT(DISTINCT av.id) as view_count
FROM blog.articles a
LEFT JOIN blog.article_tags at ON a.id = at.article_id
LEFT JOIN blog.tags t ON at.tag_id = t.id
LEFT JOIN blog.article_views av ON a.id = av.article_id
WHERE a.author_id = $1
GROUP BY a.id
ORDER BY a.created_at DESC;
```

## Security & RLS Summary

- **Public**: Can view published articles, approved comments, tags, categories
- **Authenticated**: Can create articles, comments, reactions
- **Authors**: Can edit/delete their own articles
- **Admins/Editors**: Can manage categories, tags, moderate comments
- **Anonymous**: Can subscribe to newsletter, leave comments

## Performance Considerations

- **Indexes** on frequently queried fields (slug, status, published_at)
- **Junction tables** for many-to-many relationships
- **Materialized views** could be added for complex aggregations
- **Caching** strategies for popular articles and tags

This schema supports all planned features including:
- ✅ Public blog display
- ✅ Private editor functionality
- ✅ User authentication
- ✅ Content organization
- ✅ Analytics tracking
- ✅ Newsletter subscriptions
- ✅ Social engagement features
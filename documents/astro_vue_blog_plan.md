# Technical Architecture Plan – Astro + Vue Blog (<PERSON><PERSON><PERSON> Allah)

## 1. Goals
We are building a **beautiful, modern, and minimalistic blog website** using Astro + Vue + Tailwind CSS, with a private local editor, a custom markdown parser, and a clean architecture approach.

---

## 2. Architecture Overview

### Clean Architecture Layers

**Core Layer (Domain & Entities)**
- Business rules, domain models, pure logic.
- Examples: Article, Author, Tag, Category models; Markdown AST schema; parser logic.
- No dependency on Astro/Vue — plain TypeScript modules.
- Folder: `src/core/`

**Application Layer (Use Cases / Services)**
- Orchestrates domain logic, handles fetching/saving data.
- Examples:
  - `GetPublishedArticlesService`
  - `SaveArticleDraftService`
  - `GenerateSlugService`
- No direct UI.
- Folder: `src/application/`

**Infrastructure Layer (Adapters)**
- Concrete implementations for database, APIs, storage.
- Examples:
  - Supabase repositories
  - Markdown parser → HTML renderer
  - Image uploader adapter
- Folder: `src/infrastructure/`

**Presentation Layer (UI)**
- Astro pages: For static generation.
- Vue components: Interactive parts (editor, comments, admin).
- Stateless components → purely presentational, no fetching.
- Stateful components → fetch data via services and pass into stateless components.
- Folder: `src/presentation/`

---

## 3. Custom Markdown Parser Plan

**Parsing Flow:**
1. Lexer: Tokenizes markdown + custom syntax (marketing widgets, notes, etc.).
2. Parser: Converts tokens into a custom AST.
3. Renderer: Converts AST into HTML + Vue components.

**Custom Extensions:**
- `<ProductCard id="123" />` → renders marketing component.
- `::: tip` → renders styled info boxes.

**Implementation:**
- Plain TypeScript in `src/core/markdown/`.

---

## 4. Database Schema (Supabase, `blog` schema)

**Tables:**
- `blog.profiles` → with `icon_url`.
- `blog.articles`
- `blog.tags`
- `blog.categories`
- `blog.comments`
- `blog.article_reactions`
- `blog.newsletter_subscribers`
- `blog.article_views`

**RLS Policies:**
- Only authors with `profiles.role` in (`admin`, `editor`, `author`) can insert/update articles.
- Public can only `SELECT` published articles.

---

## 5. Editor Architecture (Local-Only)

**Frontend:**
- Vue SPA (inside `/admin/editor`).

**Storage:**
- Save drafts to local SQLite or Supabase.

**Auth:**
- Supabase Auth → check `blog.profiles` role before editor access.

**Features:**
- Split-pane editor (CodeMirror).
- Live AST preview.
- Drag-drop image upload.
- Auto-save to drafts.

---

## 6. Folder Structure

```plaintext
src/
├── core/              # Entities, domain logic
│   ├── models/
│   ├── markdown/
│   └── utils/
├── application/       # Use cases/services
│   ├── articles/
│   ├── profiles/
│   └── analytics/
├── infrastructure/    # Supabase repos, adapters
│   ├── repositories/
│   ├── storage/
│   └── parser/
├── presentation/
│   ├── components/
│   │   ├── stateless/
│   │   └── stateful/
│   ├── pages/
│   └── layouts/
```

---

## 7. Scaffolding a New Astro + Vue Project

From Astro docs: https://docs.astro.build/en/guides/integrations-guide/vue/

**Steps:**
```bash
# 1. Create new Astro project
npm create astro@latest my-blog
cd my-blog

# 2. Install Vue integration
npx astro add vue

# 3. Install Tailwind CSS
npx astro add tailwind

# 4. Run the project
npm run dev
```

Now you can create `.vue` components in `src/components/` and use them inside `.astro` files.

---

## 8. Planned Pages

**Public Pages:**
1. **Home Page** (`/`)
   - Shows recent articles, featured article, tags filter.
2. **Article Page** (`/articles/[slug]`)
   - Renders markdown + components.
3. **Tag Page** (`/tags/[tag]`)
   - Lists all articles under a tag.
4. **Category Page** (`/categories/[category]`)
   - Lists all articles under a category.
5. **About Page** (`/about`)
   - Information about the blog and author.
6. **Newsletter Signup Page** (`/newsletter`)
   - Email subscription form.

**Private Pages (Editor App):**
1. **Dashboard** (`/admin`)
   - Overview of drafts, published articles.
2. **Editor** (`/admin/editor/[id?]`)
   - Create/edit article.
3. **Media Manager** (`/admin/media`)
   - Manage uploaded images.
4. **Analytics** (`/admin/analytics`)
   - View article performance.

---

## 9. Deployment Plan

**Main Site:**
- Astro build → Static pages + Vue hydration.
- Deployed to DigitalOcean.

**Editor App:**
- Private build → Only accessible to allowed users.

**CI/CD:**
- Main branch → Build → Deploy via GitHub Actions.
- Database migrations via `supabase/migrations`.

---

## 10. Development Phases

1. Schema & Auth.
2. Public Blog Rendering.
3. Custom Markdown Parser.
4. Private Editor.
5. SEO, Analytics, Polish.

## Todolist

- Set up the clean architecture folder structure (src/core, src/application, src/infrastructure, src/presentation)
- Create basic domain models (Article, Author, Tag, Category)
- Set up Supabase database schema and RLS policies
- Implement custom markdown parser (lexer, parser, renderer)
- Create public blog pages (Home, Article, Tag, Category, About, Newsletter)
- Implement private editor application with Vue components
- Add authentication and authorization with Supabase Auth
- Implement SEO, analytics, and final polish
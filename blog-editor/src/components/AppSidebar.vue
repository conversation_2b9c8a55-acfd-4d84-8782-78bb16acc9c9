<script setup lang="ts">
import { computed } from 'vue'
import type { SidebarProps } from '@/components/ui/sidebar'
import { useRouter } from 'vue-router'

import {
  BarChart3,
  FileText,
  User,
  Settings,
} from "lucide-vue-next"
import NavMain from '@/components/NavMain.vue'
import NavUser from '@/components/NavUser.vue'
import { useAuth } from '@/composables/useAuth'

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from '@/components/ui/sidebar'

const router = useRouter()
const auth = useAuth()
const props = withDefaults(defineProps<SidebarProps>(), {
  collapsible: "icon",
})

// Custom navigation data matching original structure
const data = computed(() => {
  // Get user data from auth user object (OAuth providers populate this)
  const authUser = auth.user || null
  const userProfile = auth.profile || null

  // For OAuth users, extract name from user_metadata or email
  let displayName = "User"
  let displayEmail = "No email"
  let avatarUrl = ""

  if (authUser) {
    // Debug: Log user data to understand what's available
    console.log('AppSidebar - Auth User:', authUser)
    console.log('AppSidebar - User Profile:', userProfile)

    // Try to get email from auth user
    displayEmail = authUser.email || "No email"

    // Try to get name from various sources
    if (userProfile?.full_name) {
      displayName = userProfile.full_name
    } else if (userProfile?.username) {
      displayName = userProfile.username
    } else if (authUser.user_metadata?.full_name) {
      displayName = authUser.user_metadata.full_name
    } else if (authUser.user_metadata?.name) {
      displayName = authUser.user_metadata.name
    } else if (authUser.email) {
      // Extract name from email as fallback
      displayName = authUser.email.split('@')[0]
    }

    // Try to get avatar from various sources
    if (userProfile?.avatar_url) {
      avatarUrl = userProfile.avatar_url
    } else if (authUser.user_metadata?.avatar_url) {
      avatarUrl = authUser.user_metadata.avatar_url
    } else if (authUser.user_metadata?.picture) {
      avatarUrl = authUser.user_metadata.picture
    }
  }

  return {
    user: {
      name: displayName,
      email: displayEmail,
      avatar: avatarUrl,
    },
    navMain: [
    {
      title: "Dashboard",
      url: "/dashboard",
      icon: BarChart3,
      isActive: false,
    },
    {
      title: "Articles",
      url: "/articles",
      icon: FileText,
      isActive: false,
      items: [
        {
          title: "All articles",
          url: "/articles",
        },
        {
          title: "Write a new article",
          url: "/articles/new",
        },
      ],
    },
    {
      title: "Profile",
      url: "/profile",
      icon: User,
      isActive: false,
    },
    {
      title: "Settings",
      url: "/settings",
      icon: Settings,
      isActive: false,
    },
  ],
  }
})

// Handle navigation for non-collapsible items
const handleNavigation = (url: string) => {
  router.push(url)
}
</script>

<template>
  <Sidebar v-bind="props">
    <SidebarHeader>
      <div class="flex items-center gap-2 px-4 py-2">
        <div class="bg-primary flex items-center justify-center w-8 h-8 rounded-lg">
          <span class="text-primary-foreground font-bold">V</span>
        </div>
        <span class="text-lg font-bold">BlogEditor</span>
      </div>
    </SidebarHeader>
    <SidebarContent>
      <NavMain
        :items="data.navMain"
        :onNavigate="handleNavigation"
        :collapsibleOnly="true"
      />
    </SidebarContent>
    <SidebarFooter>
      <NavUser :user="data.user" />
    </SidebarFooter>
    <SidebarRail />
  </Sidebar>
</template>

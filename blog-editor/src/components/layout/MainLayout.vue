<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Separator } from '@/components/ui/separator'
import {
  LogOut,
  Sun,
  Moon
} from 'lucide-vue-next'
import AppSidebar from '@/components/AppSidebar.vue'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from '@/components/ui/sidebar'
import { useAuth } from '@/composables/useAuth'
import { useTheme } from '@/composables/useTheme'

const router = useRouter()
const route = useRoute()
const { user, profile, logout } = useAuth()
const { isDarkMode, toggleTheme } = useTheme()


// User data comes from auth composable now

const handleLogout = async () => {
  await logout()
}

</script>

<template>
  <SidebarProvider>
    <AppSidebar />
    <SidebarInset>
      <header class="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
        <div class="flex items-center gap-2 px-4">
          <SidebarTrigger class="-ml-1" />
          <Separator orientation="vertical" class="h-4 mr-2" />
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem class="md:block hidden">
                <BreadcrumbLink @click.prevent="router.push('/dashboard')">
                  VoiceHype Blog
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator class="md:block hidden" />
              <BreadcrumbItem>
                <BreadcrumbPage>{{ route.meta.title || 'Blog Editor' }}</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </header>
      <div class="flex flex-col flex-1 gap-4 p-4 pt-0">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-xl font-semibold">{{ route.meta.title || 'Blog Editor' }}</h1>
          </div>
          <div class="flex items-center gap-4">
            <!-- New Article Button -->
            <Button @click="router.push('/articles/new')" class="">
              <span class="mr-2">+</span> New Article
            </Button>
            
            <!-- Theme Toggle -->
            <Button
              variant="ghost"
              size="sm"
              @click="toggleTheme"
              :title="isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'"
            >
              <component :is="isDarkMode ? Sun : Moon" class="w-5 h-5" />
            </Button>
            
            <!-- User Menu -->
            <DropdownMenu>
              <DropdownMenuTrigger as-child>
                <Button variant="ghost" size="sm" class="relative">
                  <Avatar class="w-8 h-8">
                    <AvatarImage :src="profile?.avatar_url || ''" />
                    <AvatarFallback>
                      {{ profile?.full_name?.charAt(0) || profile?.username?.charAt(0) || user?.email?.charAt(0) || 'U' }}
                    </AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" class="w-56">
                <div class="flex items-center gap-2 p-2">
                  <Avatar class="w-8 h-8">
                    <AvatarImage :src="profile?.avatar_url || ''" />
                    <AvatarFallback>
                      {{ profile?.full_name?.charAt(0) || profile?.username?.charAt(0) || user?.email?.charAt(0) || 'U' }}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p class="font-medium">{{ profile?.full_name || profile?.username || user?.email || 'User' }}</p>
                    <p class="text-muted-foreground text-xs">{{ user?.email || 'No email' }}</p>
                  </div>
                </div>
                <Separator />
                <DropdownMenuItem @click="handleLogout">
                  <LogOut class="w-4 h-4 mr-2" />
                  <span>Sign out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
        
        <!-- Page Content -->
        <div class="flex-1 overflow-y-auto">
          <slot />
        </div>
      </div>
    </SidebarInset>
  </SidebarProvider>
</template>
<template>
  <div class="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950 dark:to-emerald-950 border border-green-200 dark:border-green-800 rounded-lg p-6 my-6">
    <div class="text-center">
      <div class="bg-green-500 text-white p-3 rounded-full w-12 h-12 mx-auto mb-4 flex items-center justify-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
        </svg>
      </div>
      
      <h3 class="text-xl font-bold text-green-900 dark:text-green-100 mb-2">
        Stay Updated with VoiceHype
      </h3>
      
      <p class="text-green-700 dark:text-green-300 mb-6">
        Get the latest updates, tips, and tutorials about voice-to-prompt technology and AI development tools.
      </p>
      
      <form @submit.prevent="handleSubmit" class="max-w-md mx-auto">
        <div class="flex gap-2">
          <input
            v-model="email"
            type="email"
            placeholder="Enter your email"
            required
            class="flex-1 px-4 py-2 border border-green-300 dark:border-green-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-green-900 dark:text-green-100"
          />
          <button
            type="submit"
            :disabled="isLoading"
            class="px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white rounded-lg transition-colors"
          >
            {{ isLoading ? 'Subscribing...' : 'Subscribe' }}
          </button>
        </div>
      </form>
      
      <p v-if="message" :class="messageClass" class="mt-3 text-sm">
        {{ message }}
      </p>
      
      <p class="text-green-600 dark:text-green-400 text-xs mt-4">
        No spam, unsubscribe at any time.
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

const email = ref('')
const isLoading = ref(false)
const message = ref('')
const isSuccess = ref(false)

const messageClass = computed(() => {
  return isSuccess.value 
    ? 'text-green-700 dark:text-green-300' 
    : 'text-red-700 dark:text-red-300'
})

const handleSubmit = async () => {
  if (!email.value) return
  
  isLoading.value = true
  message.value = ''
  
  try {
    // TODO: Implement newsletter subscription API call
    // For now, just simulate success
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    isSuccess.value = true
    message.value = 'Successfully subscribed! Check your email for confirmation.'
    email.value = ''
  } catch (error) {
    isSuccess.value = false
    message.value = 'Failed to subscribe. Please try again.'
  } finally {
    isLoading.value = false
  }
}
</script>

<template>
  <div class="markdown-preview">
    <component 
      v-for="(item, index) in renderedItems" 
      :key="index"
      :is="item.component"
      v-bind="item.props"
      v-html="item.html"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, defineAsyncComponent } from 'vue'
import { markdownProcessor } from '@/markdown'

interface Props {
  content: string
}

const props = defineProps<Props>()

// Import blog components
const TryOutVoiceHype = defineAsyncComponent(() => import('@/components/blog/TryOutVoiceHype.vue'))
const Tip = defineAsyncComponent(() => import('@/components/blog/Tip.vue'))
const SubscribeNewsletter = defineAsyncComponent(() => import('@/components/blog/SubscribeNewsletter.vue'))

// Component registry
const componentRegistry = {
  'TryOutVoiceHype': TryOutVoiceHype,
  'Tip': Tip,
  'SubscribeNewsletter': SubscribeNewsletter,
}

const renderedItems = computed(() => {
  if (!props.content) {
    return [{
      component: 'div',
      props: { class: 'text-gray-500' },
      html: '<p>Start writing your article...</p>'
    }]
  }

  try {
    // Process markdown to HTML
    const html = markdownProcessor.process(props.content)
    
    // Parse HTML and extract custom components
    const items = parseHtmlWithComponents(html)
    
    return items
  } catch (error) {
    console.error('Markdown processing error:', error)
    return [{
      component: 'div',
      props: { class: 'text-red-500' },
      html: '<p>Error rendering markdown</p>'
    }]
  }
})

function parseHtmlWithComponents(html: string) {
  const items: Array<{
    component: string | object
    props: Record<string, any>
    html?: string
  }> = []

  // Split HTML by custom component tags
  const componentRegex = /<(TryOutVoiceHype|Tip|SubscribeNewsletter)([^>]*?)\/?>(?:<\/\1>)?/g
  
  let lastIndex = 0
  let match

  while ((match = componentRegex.exec(html)) !== null) {
    // Add HTML content before the component
    if (match.index > lastIndex) {
      const htmlContent = html.slice(lastIndex, match.index).trim()
      if (htmlContent) {
        items.push({
          component: 'div',
          props: {},
          html: htmlContent
        })
      }
    }

    // Parse component attributes
    const componentName = match[1]
    const attributesString = match[2] || ''
    const props = parseAttributes(attributesString)

    // Add the component
    const ComponentClass = componentRegistry[componentName as keyof typeof componentRegistry]
    if (ComponentClass) {
      items.push({
        component: ComponentClass,
        props
      })
    } else {
      // Fallback for unknown components
      items.push({
        component: 'div',
        props: { class: 'bg-yellow-100 p-2 rounded border' },
        html: `<p>Unknown component: ${componentName}</p>`
      })
    }

    lastIndex = componentRegex.lastIndex
  }

  // Add remaining HTML content
  if (lastIndex < html.length) {
    const htmlContent = html.slice(lastIndex).trim()
    if (htmlContent) {
      items.push({
        component: 'div',
        props: {},
        html: htmlContent
      })
    }
  }

  // If no components were found, return the entire HTML as one item
  if (items.length === 0) {
    items.push({
      component: 'div',
      props: {},
      html
    })
  }

  return items
}

function parseAttributes(attributesString: string): Record<string, any> {
  const props: Record<string, any> = {}

  if (!attributesString.trim()) return props

  // Enhanced attribute parser - handles quoted values with spaces
  const attrRegex = /(\w+)=(?:"([^"]*)"|'([^']*)'|([^\s>]+))/g
  let match

  while ((match = attrRegex.exec(attributesString)) !== null) {
    const key = match[1]
    // Get the value from whichever capture group matched
    let value: any = match[2] || match[3] || match[4]

    // Try to parse as JSON for complex values
    if (value === 'true') {
      value = true
    } else if (value === 'false') {
      value = false
    } else if (value.startsWith('{') || value.startsWith('[')) {
      try {
        value = JSON.parse(value)
      } catch {
        // Keep as string if JSON parsing fails
      }
    }
    // For numbers
    else if (/^\d+$/.test(value)) {
      value = parseInt(value, 10)
    } else if (/^\d+\.\d+$/.test(value)) {
      value = parseFloat(value)
    }

    props[key] = value
  }

  return props
}
</script>

<style scoped>
.markdown-preview :deep(h1) {
  font-size: 1.875rem;
  font-weight: bold;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.markdown-preview :deep(h1:first-child) {
  margin-top: 0;
}

.markdown-preview :deep(h2) {
  font-size: 1.5rem;
  font-weight: 600;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
}

.markdown-preview :deep(h3) {
  font-size: 1.25rem;
  font-weight: 600;
  margin-top: 1.25rem;
  margin-bottom: 0.5rem;
}

.markdown-preview :deep(h4) {
  font-size: 1.125rem;
  font-weight: 500;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

.markdown-preview :deep(h5) {
  font-size: 1rem;
  font-weight: 500;
  margin-top: 0.75rem;
  margin-bottom: 0.25rem;
}

.markdown-preview :deep(h6) {
  font-size: 0.875rem;
  font-weight: 500;
  margin-top: 0.5rem;
  margin-bottom: 0.25rem;
}

.markdown-preview :deep(p) {
  margin-bottom: 1rem;
  line-height: 1.625;
}

.markdown-preview :deep(ul) {
  list-style-type: disc;
  list-style-position: inside;
  margin-bottom: 1rem;
}

.markdown-preview :deep(ul li) {
  margin-bottom: 0.25rem;
}

.markdown-preview :deep(ol) {
  list-style-type: decimal;
  list-style-position: inside;
  margin-bottom: 1rem;
}

.markdown-preview :deep(ol li) {
  margin-bottom: 0.25rem;
}

.markdown-preview :deep(li) {
  line-height: 1.625;
}

.markdown-preview :deep(blockquote) {
  border-left: 4px solid #d1d5db;
  padding-left: 1rem;
  font-style: italic;
  margin: 1rem 0;
  color: #6b7280;
}

/* Inline code styling */
.markdown-preview :deep(code:not(pre code)) {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}

/* Code block styling - let Prism.js handle colors */
.markdown-preview :deep(pre) {
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin-bottom: 1rem;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  /* Don't override background - let Prism.js theme handle it */
}

.markdown-preview :deep(pre code) {
  background-color: transparent !important;
  padding: 0;
  border-radius: 0;
  font-size: inherit;
}

/* Dark mode adjustments for inline code */
.dark .markdown-preview :deep(code:not(pre code)) {
  background-color: #374151;
  color: #e5e7eb;
}

.markdown-preview :deep(a) {
  color: #2563eb;
  text-decoration: underline;
}

.markdown-preview :deep(a:hover) {
  color: #1d4ed8;
}

.markdown-preview :deep(hr) {
  border-color: #d1d5db;
  margin: 1.5rem 0;
}

.markdown-preview :deep(table) {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #d1d5db;
  margin-bottom: 1rem;
}

.markdown-preview :deep(th),
.markdown-preview :deep(td) {
  border: 1px solid #d1d5db;
  padding: 0.75rem;
  text-align: left;
}

.markdown-preview :deep(th) {
  background-color: #f3f4f6;
  font-weight: 600;
}

/* Dark mode styles */
@media (prefers-color-scheme: dark) {
  .markdown-preview :deep(blockquote) {
    border-left-color: #4b5563;
    color: #9ca3af;
  }

  .markdown-preview :deep(code) {
    background-color: #1f2937;
  }

  .markdown-preview :deep(pre) {
    background-color: #1f2937;
  }

  .markdown-preview :deep(hr) {
    border-color: #4b5563;
  }

  .markdown-preview :deep(table) {
    border-color: #4b5563;
  }

  .markdown-preview :deep(th),
  .markdown-preview :deep(td) {
    border-color: #4b5563;
  }

  .markdown-preview :deep(th) {
    background-color: #1f2937;
  }
}
</style>
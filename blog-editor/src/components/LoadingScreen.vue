<script setup lang="ts">
import { Loader2 } from 'lucide-vue-next'
</script>

<template>
  <div class="bg-background fixed inset-0 z-50 flex items-center justify-center">
    <div class="max-w-md px-4 mx-auto space-y-8 text-center">
      <!-- Logo/Brand -->
      <div class="flex justify-center">
        <div class="relative">
          <!-- Outer ring animation -->
          <div class="bg-primary/20 animate-ping absolute inset-0 rounded-full"></div>
          <!-- Inner circle -->
          <div class="bg-primary text-primary-foreground relative flex items-center justify-center w-16 h-16 rounded-full">
            <span class="text-xl font-bold">V</span>
          </div>
        </div>
      </div>

      <!-- Loading Spinner with enhanced styling -->
      <div class="flex justify-center space-y-4">
        <div class="relative">
          <Loader2 class="animate-spin text-primary w-12 h-12" />
          <!-- Rotating accent circles -->
          <div class="absolute inset-0 flex items-center justify-center">
            <div class="border-primary/20 animate-spin w-16 h-16 border-2 rounded-full" style="animation-direction: reverse;"></div>
          </div>
        </div>
      </div>

      <!-- Loading Text -->
      <div class="space-y-2">
        <h2 class="text-foreground text-2xl font-bold">VoiceHype Blog</h2>
        <p class="text-muted-foreground text-lg">Initializing your blog editor</p>
        <p class="text-muted-foreground/70 text-sm">Please wait while we set everything up...</p>
      </div>

      <!-- Animated Dots -->
      <div class="flex justify-center space-x-1">
        <div class="bg-primary animate-bounce w-2 h-2 rounded-full"></div>
        <div class="bg-primary animate-bounce w-2 h-2 rounded-full" style="animation-delay: 0.1s"></div>
        <div class="bg-primary animate-bounce w-2 h-2 rounded-full" style="animation-delay: 0.2s"></div>
      </div>
    </div>
  </div>
</template>

<style scoped>
@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

.animate-bounce {
  animation: bounce 1.4s ease-in-out infinite;
}

@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

.animate-ping {
  animation: ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;
}
</style>

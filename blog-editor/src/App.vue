<script setup lang="ts">
import { RouterView } from 'vue-router'
import MainLayout from '@/components/layout/MainLayout.vue'
import LoadingScreen from '@/components/LoadingScreen.vue'
import { useAuth } from '@/composables/useAuth'
import { useTheme } from '@/composables/useTheme'
import { Toaster } from '@/components/ui/sonner'
import { computed } from 'vue'
import 'vue-sonner/style.css' // vue-sonner v2 requires this import

const { isAuthenticated, loading } = useAuth()
const { isInitialized: themeInitialized } = useTheme()

// Show loading screen if auth is loading OR theme is not initialized
// Don't include isAuthenticated in loading condition as that determines which layout to show
const showLoading = computed(() => loading || !themeInitialized)
</script>

<template>
  <!-- Loading Screen -->
  <LoadingScreen v-if="showLoading" />

  <!-- Authenticated Layout -->
  <div v-else-if="isAuthenticated">
    <MainLayout>
      <RouterView />
    </MainLayout>
  </div>

  <!-- Unauthenticated Routes -->
  <RouterView v-else />

  <!-- Toast Notifications -->
  <Toaster />
</template>

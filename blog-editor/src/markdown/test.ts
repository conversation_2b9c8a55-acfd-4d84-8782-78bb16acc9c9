import { MarkdownProcessor } from './index';
import { MermaidASTRenderer } from './mermaid-renderer';

// Test the markdown parser
const processor = new MarkdownProcessor();
const mermaidRenderer = new MermaidASTRenderer();

const testMarkdown = `
# Hello World

This is a **bold** and *italic* text with \`inline code\`.

## Features

- Custom components: <ProductCard id="123" name="Test Product" />
- Markdown syntax support
- Vue component integration

### Code Example

\`\`\`javascript
console.log('Hello World');
\`\`\`

[Click here](https://example.com) to learn more.

![Alt text](image.jpg)


Line 1
Line 2
Line 3
`;

console.log('='.repeat(60));
console.log('MARKDOWN TO AST VISUALIZATION');
console.log('='.repeat(60));

console.log('\nInput Markdown:');
console.log('-'.repeat(40));
console.log(testMarkdown);

// Debug: Show tokens
console.log('\nTokens:');
console.log('-'.repeat(40));
const lexer = new (require('./lexer').MarkdownLexer)(testMarkdown);
const tokens = lexer.tokenize();
console.log(tokens.map((t: any, i: number) => `${i}: ${t.type} "${t.value}"`).join('\n'));

// Parse to AST
console.log('\nParsing to AST...');
const parser = new (require('./parser').MarkdownParser)(tokens);
const ast = parser.parse();

// Generate Mermaid diagrams
console.log('\nMermaid AST Visualization:');
console.log('-'.repeat(40));

console.log('\n1. Basic Graph:');
console.log(mermaidRenderer.render(ast));

console.log('\n2. Detailed Graph with Styling:');
console.log(mermaidRenderer.renderDetailed(ast));

console.log('\n3. Tree View:');
console.log(mermaidRenderer.renderTree(ast));

console.log('\nOutput HTML:');
console.log('-'.repeat(40));
console.log(processor.process(testMarkdown));

console.log('\n' + '='.repeat(60));
console.log('Copy the mermaid code above and paste it into a mermaid live editor');
console.log('or any mermaid.js supported environment to visualize the AST.');
console.log('='.repeat(60));
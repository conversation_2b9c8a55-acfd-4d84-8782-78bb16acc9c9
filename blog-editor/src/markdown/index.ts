import { MarkdownLexer } from './lexer';
import { MarkdownParser } from './parser';
import { MarkdownRenderer } from './renderer';
import type { LexerOptions, ParserOptions, RendererOptions } from './types';

export class MarkdownProcessor {
  private lexerOptions: LexerOptions;
  private parserOptions: ParserOptions;
  private rendererOptions: RendererOptions;

  constructor(
    lexerOptions: LexerOptions = {},
    parserOptions: ParserOptions = {},
    rendererOptions: RendererOptions = {}
  ) {
    this.lexerOptions = lexerOptions;
    this.parserOptions = parserOptions;
    this.rendererOptions = rendererOptions;
  }

  process(markdown: string): string {
    // 1. Tokenize
    const lexer = new MarkdownLexer(markdown, this.lexerOptions);
    const tokens = lexer.tokenize();
    
    // 2. Parse to AST
    const parser = new MarkdownParser(tokens, this.parserOptions);
    const ast = parser.parse();
    
    // 3. Render to HTML
    const renderer = new MarkdownRenderer(this.rendererOptions);
    return renderer.render(ast);
  }

  // Utility method to process with custom options
  processWithOptions(
    markdown: string,
    lexerOptions?: Partial<LexerOptions>,
    parserOptions?: Partial<ParserOptions>,
    rendererOptions?: Partial<RendererOptions>
  ): string {
    const processor = new MarkdownProcessor(
      { ...this.lexerOptions, ...lexerOptions },
      { ...this.parserOptions, ...parserOptions },
      { ...this.rendererOptions, ...rendererOptions }
    );
    
    return processor.process(markdown);
  }
}

// Default processor instance
export const markdownProcessor = new MarkdownProcessor();

// Re-export types
export * from './types';
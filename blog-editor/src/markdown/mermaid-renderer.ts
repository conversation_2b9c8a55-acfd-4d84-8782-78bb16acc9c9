import type { ASTNode, NodeType } from './types';

export class Mermaid<PERSON><PERSON>enderer {
  /**
   * Convert AST to Mermaid diagram code
   */
  render(ast: ASTNode): string {
    let mermaidCode: string[] = ['graph TD'];
    
    // Start with root node
    mermaidCode.push('    A[Document]');
    
    // Generate nodes and connections
    this.generateNodes(ast, 'A', mermaidCode);
    
    return mermaidCode.join('\n');
  }

  /**
   * Generate nodes and connections recursively
   */
  private generateNodes(node: ASTNode, parentId: string, mermaidCode: string[], level: number = 1): void {
    if (!node.children || node.children.length === 0) {
      return;
    }

    const children = node.children;
    
    for (let i = 0; i < children.length; i++) {
      const child = children[i];
      const childId = `${parentId}${i + 1}`;
      
      // Add child node
      const nodeLabel = this.getNodeLabel(child);
      mermaidCode.push(`    ${childId}[${nodeLabel}]`);
      
      // Add connection from parent
      mermaidCode.push(`    ${parentId} --> ${childId}`);
      
      // Recursively process children
      if (child.children && child.children.length > 0) {
        this.generateNodes(child, childId, mermaidCode, level + 1);
      }
    }
  }

  /**
   * Get a descriptive label for node type
   */
  private getNodeTypeLabel(type: NodeType): string {
    const labels: Record<NodeType, string> = {
      'document': 'Document',
      'heading': 'Heading',
      'paragraph': 'Paragraph',
      'text': 'Text',
      'bold': 'Bold',
      'italic': 'Italic',
      'code': 'Code',
      'codeBlock': 'Code Block',
      'link': 'Link',
      'image': 'Image',
      'list': 'List',
      'listItem': 'List Item',
      'blockquote': 'Blockquote',
      'horizontalRule': 'Horizontal Rule',
      'customComponent': 'Custom Component',
      'lineBreak': 'Line Break'
    };
    
    return labels[type] || type;
  }

    /**
   * Escape and format node labels for Mermaid
   */
  private getNodeLabel(node: ASTNode): string {
    const typeLabel = this.getNodeTypeLabel(node.type);

    // Utility: escape " and wrap everything in ["..."]
    const formatLabel = (text: string) => {
      const escaped = text.replace(/"/g, "'");
      return `["${typeLabel}: ${escaped}"]`;
    };

    // Add content information
    if (node.content) {
      const preview = node.content.length > 20
        ? node.content.substring(0, 20) + '...'
        : node.content;
      return formatLabel(preview);
    }

    // Special cases
    if (node.type === 'heading' && node.metadata?.level) {
      return `["${typeLabel} (Level ${node.metadata.level})"]`;
    }

    if (node.type === 'link' && node.attributes?.href) {
      const href = node.attributes.href.length > 30
        ? node.attributes.href.substring(0, 30) + '...'
        : node.attributes.href;
      return formatLabel(href);
    }

    if (node.type === 'image' && node.attributes?.alt) {
      return formatLabel(node.attributes.alt);
    }

    if (node.type === 'codeBlock' && node.metadata?.language) {
      return `["${typeLabel} (${node.metadata.language})"]`;
    }

    if (node.type === 'customComponent' && node.metadata?.componentName) {
      return formatLabel(node.metadata.componentName);
    }

    // Default
    return `["${typeLabel}"]`;
  }

  /**
   * Generate a detailed mermaid diagram with styling
   */
  renderDetailed(ast: ASTNode): string {
    let mermaidCode: string[] = [
      'graph TD',
      '    classDef document fill:#f9f,stroke:#333,stroke-width:2px',
      '    classDef heading fill:#bbf,stroke:#333,stroke-width:2px',
      '    classDef paragraph fill:#bfb,stroke:#333,stroke-width:2px',
      '    classDef text fill:#fff,stroke:#333,stroke-width:1px',
      '    classDef bold fill:#fbb,stroke:#333,stroke-width:1px',
      '    classDef italic fill:#fbf,stroke:#333,stroke-width:1px',
      '    classDef code fill:#bbf,stroke:#333,stroke-width:1px',
      '    classDef codeBlock fill:#bfb,stroke:#333,stroke-width:2px',
      '    classDef link fill:#f9f,stroke:#333,stroke-width:1px',
      '    classDef image fill:#ff9,stroke:#333,stroke-width:1px',
      '    classDef list fill:#9f9,stroke:#333,stroke-width:1px',
      '    classDef listItem fill:#9ff,stroke:#333,stroke-width:1px',
      '    classDef blockquote fill:#f99,stroke:#333,stroke-width:1px',
      '    classDef horizontalRule fill:#999,stroke:#333,stroke-width:2px',
      '    classDef customComponent fill:#f0f,stroke:#333,stroke-width:2px',
      '    classDef lineBreak fill:#ccc,stroke:#333,stroke-width:1px',
      '',
      '    A[Document]',
      '    class A document'
    ];
    
    this.generateDetailedNodes(ast, 'A', mermaidCode);
    
    return mermaidCode.join('\n');
  }

  /**
   * Generate detailed nodes with styling
   */
  private generateDetailedNodes(node: ASTNode, parentId: string, mermaidCode: string[], level: number = 1): void {
    if (!node.children || node.children.length === 0) {
      return;
    }

    const children = node.children;
    
    for (let i = 0; i < children.length; i++) {
      const child = children[i];
      const childId = `${parentId}${i + 1}`;
      const nodeLabel = this.getNodeLabel(child);
      
      // Add child node with styling
      mermaidCode.push(`    ${childId}[${nodeLabel}]`);
      mermaidCode.push(`    class ${childId} ${child.type}`);
      
      // Add connection from parent
      mermaidCode.push(`    ${parentId} --> ${childId}`);
      
      // Recursively process children
      if (child.children && child.children.length > 0) {
        this.generateDetailedNodes(child, childId, mermaidCode, level + 1);
      }
    }
  }

  /**
   * Generate a tree-style mermaid diagram
   */
  renderTree(ast: ASTNode): string {
    let mermaidCode: string[] = [
      'graph TD',
      '    A[Document]',
      ''
    ];
    
    this.generateTreeNodes(ast, 'A', mermaidCode, 1);
    
    return mermaidCode.join('\n');
  }

  /**
   * Generate tree nodes with indentation
   */
  private generateTreeNodes(node: ASTNode, parentId: string, mermaidCode: string[], level: number = 1): void {
    if (!node.children || node.children.length === 0) {
      return;
    }

    const children = node.children;
    const indent = '    '.repeat(level);
    
    for (let i = 0; i < children.length; i++) {
      const child = children[i];
      const childId = `${parentId}${i + 1}`;
      const nodeLabel = this.getNodeLabel(child);
      
      // Add child node
      mermaidCode.push(`${indent}${childId}[${nodeLabel}]`);
      
      // Add connection from parent
      mermaidCode.push(`${indent}${parentId} --> ${childId}`);
      
      // Add blank line for better readability
      mermaidCode.push('');
      
      // Recursively process children
      if (child.children && child.children.length > 0) {
        this.generateTreeNodes(child, childId, mermaidCode, level + 1);
      }
    }
  }
}
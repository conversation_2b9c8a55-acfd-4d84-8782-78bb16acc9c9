import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { supabase, getCurrentProfile, signOut, type Profile } from '@/lib/supabase'
import type { User } from '@supabase/supabase-js'

export const useProfileStore = defineStore('profile', () => {
  // State
  const user = ref<User | null>(null)
  const profile = ref<Profile | null>(null)
  const loading = ref(true)
  const error = ref<string | null>(null)

  // Computed
  const isAuthenticated = computed(() => !!user.value)
  const isAdmin = computed(() => profile.value?.role === 'admin')
  const isEditor = computed(() => ['admin', 'editor'].includes(profile.value?.role || ''))
  const canWrite = computed(() => ['admin', 'editor', 'writer'].includes(profile.value?.role || ''))

  // Initialize auth state
  const initialize = async () => {
    try {
      loading.value = true
      error.value = null
      
      // Get current session
      const { data: { session }, error: sessionError } = await supabase.auth.getSession()
      
      if (sessionError) throw sessionError
      
      if (session?.user) {
        user.value = session.user
        await fetchProfile()
      } else {
        user.value = null
        profile.value = null
      }
    } catch (err) {
      console.error('Auth initialization error:', err)
      error.value = 'Failed to initialize authentication'
      user.value = null
      profile.value = null
    } finally {
      loading.value = false
    }
  }

  // Fetch user profile
  const fetchProfile = async () => {
    if (!user.value) return
    
    try {
      profile.value = await getCurrentProfile()
    } catch (err) {
      console.error('Error fetching profile:', err)
      error.value = 'Failed to load profile'
    }
  }

  // Sign out
  const logout = async () => {
    try {
      loading.value = true
      await signOut()
      user.value = null
      profile.value = null
    } catch (err) {
      console.error('Logout error:', err)
      error.value = 'Failed to sign out'
    } finally {
      loading.value = false
    }
  }

  // Refresh profile data
  const refreshProfile = async () => {
    if (!user.value) return
    
    try {
      await fetchProfile()
    } catch (err) {
      console.error('Error refreshing profile:', err)
    }
  }

  // Check if user has specific role
  const hasRole = (role: string) => {
    return profile.value?.role === role
  }

  // Check if user has any of the specified roles
  const hasAnyRole = (roles: string[]) => {
    return roles.includes(profile.value?.role || '')
  }

  return {
    // State
    user,
    profile,
    loading,
    error,
    
    // Computed
    isAuthenticated,
    isAdmin,
    isEditor,
    canWrite,
    
    // Actions
    initialize,
    fetchProfile,
    logout,
    refreshProfile,
    hasRole,
    hasAnyRole
  }
})
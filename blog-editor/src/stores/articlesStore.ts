import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { supabase, getUserArticles, type Article } from '@/lib/supabase'
import { useProfileStore } from './profileStore'

// Define an extended interface for Article that includes tags
interface ArticleWithTags extends Article {
  article_tags?: Array<{ tag_name: string }>
}

export const useArticlesStore = defineStore('articles', () => {
  // State - Use the extended interface
  const articles = ref<ArticleWithTags[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const subscription = ref<any>(null)
  
  // Computed
  const publishedArticles = computed(() => 
    articles.value.filter(article => article.status === 'published')
  )
  
  const draftArticles = computed(() => 
    articles.value.filter(article => article.status === 'draft')
  )
  
  const archivedArticles = computed(() => 
    articles.value.filter(article => article.status === 'archived')
  )
  
  const totalArticles = computed(() => articles.value.length)
  const totalPublished = computed(() => publishedArticles.value.length)
  const totalDrafts = computed(() => draftArticles.value.length)
  
  // Actions
  const loadUserArticles = async () => {
    const profileStore = useProfileStore()
    
    if (!profileStore.user) {
      error.value = 'User not authenticated'
      return
    }
    
    try {
      loading.value = true
      error.value = null
      
      const userArticles = await getUserArticles()
      articles.value = userArticles || []
      
    } catch (err: any) {
      console.error('Error loading articles:', err)
      error.value = err.message || 'Failed to load articles'
      
      // Handle authentication error
      if (err.message?.includes('not authenticated')) {
        // Redirect handled by router guard
      }
    } finally {
      loading.value = false
    }
  }
  
  // Setup real-time subscription for articles
  const setupArticlesSubscription = () => {
    const profileStore = useProfileStore()
    
    if (!profileStore.user) return
    
    // Clean up existing subscription
    if (subscription.value) {
      subscription.value.unsubscribe()
    }
    
    subscription.value = supabase
      .channel('articles-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'blog',
          table: 'articles',
          filter: `author_id=eq.${profileStore.user.id}`
        },
        (payload) => {
          console.log('Article change detected:', payload)
          
          if (payload.eventType === 'INSERT' && payload.new) {
            // Add new article
            const newArticle = payload.new as ArticleWithTags
            articles.value.unshift(newArticle)
            
          } else if (payload.eventType === 'UPDATE' && payload.new) {
            // Update existing article
            const updatedArticle = payload.new as ArticleWithTags
            const index = articles.value.findIndex(a => a.id === updatedArticle.id)
            if (index !== -1) {
              articles.value[index] = updatedArticle
            }
            
          } else if (payload.eventType === 'DELETE' && payload.old) {
            // Remove deleted article
            const deletedId = payload.old.id
            articles.value = articles.value.filter(a => a.id !== deletedId)
          }
        }
      )
      .subscribe()
      
    return subscription.value
  }
  
  // Add article to local state (for optimistic updates)
  const addArticle = (article: ArticleWithTags) => {
    articles.value.unshift(article)
  }
  
  // Update article in local state
  const updateArticle = (articleId: string, updates: Partial<ArticleWithTags>) => {
    const index = articles.value.findIndex(a => a.id === articleId)
    if (index !== -1) {
      articles.value[index] = { ...articles.value[index], ...updates }
    }
  }
  
  // Remove article from local state
  const removeArticle = (articleId: string) => {
    articles.value = articles.value.filter(a => a.id !== articleId)
  }
  
  // Get article by ID
  const getArticleById = (id: string) => {
    return articles.value.find(article => article.id === id)
  }
  
  // Get articles by status
  const getArticlesByStatus = (status: 'draft' | 'published' | 'archived') => {
    return articles.value.filter(article => article.status === status)
  }
  
  // Search articles
  const searchArticles = (query: string) => {
    const lowercaseQuery = query.toLowerCase()
    return articles.value.filter(article => 
      article.title.toLowerCase().includes(lowercaseQuery) ||
      (article.content && article.content.toLowerCase().includes(lowercaseQuery)) ||
      (article.excerpt && article.excerpt.toLowerCase().includes(lowercaseQuery))
    )
  }
  
  // Get article tags
  const getArticleTags = (articleId: string) => {
    const article = articles.value.find(a => a.id === articleId)
    if (!article || !article.article_tags) return []
    
    // Extract tag names from the article_tags array
    return article.article_tags.map(tag => tag.tag_name)
  }
  
  // Clean up subscription
  const cleanup = () => {
    if (subscription.value) {
      subscription.value.unsubscribe()
      subscription.value = null
    }
  }
  
  // Reset store
  const reset = () => {
    articles.value = []
    loading.value = false
    error.value = null
    cleanup()
  }
  
  return {
    // State
    articles,
    loading,
    error,
    
    // Computed
    publishedArticles,
    draftArticles,
    archivedArticles,
    totalArticles,
    totalPublished,
    totalDrafts,
    
    // Actions
    loadUserArticles,
    setupArticlesSubscription,
    addArticle,
    updateArticle,
    removeArticle,
    getArticleById,
    getArticlesByStatus,
    searchArticles,
    getArticleTags,
    cleanup,
    reset
  }
})
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useProfileStore } from './profileStore'
import { useArticlesStore } from './articlesStore'

export const useDashboardStore = defineStore('dashboard', () => {
  // State
  const loading = ref(false)
  const error = ref<string | null>(null)
  const lastUpdated = ref<Date | null>(null)
  
  // Get other stores
  const profileStore = useProfileStore()
  const articlesStore = useArticlesStore()
  
  // Computed dashboard stats
  const stats = computed(() => ({
    totalArticles: articlesStore.totalArticles,
    publishedArticles: articlesStore.totalPublished,
    draftArticles: articlesStore.totalDrafts,
    archivedArticles: articlesStore.archivedArticles.length,
    totalWords: articlesStore.articles.reduce((sum, article) => {
      return sum + (article.content?.split(/\s+/).filter(w => w.length > 0).length || 0)
    }, 0)
  }))
  
  // Recent articles (last 5)
  const recentArticles = computed(() => {
    return [...articlesStore.articles]
      .sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())
      .slice(0, 5)
  })
  
  // Recent drafts (last 3)
  const recentDrafts = computed(() => {
    return [...articlesStore.draftArticles]
      .sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())
      .slice(0, 3)
  })
  
  // Articles by month for charts
  const articlesByMonth = computed(() => {
    const monthlyData: { [key: string]: number } = {}
    
    articlesStore.articles.forEach(article => {
      const date = new Date(article.created_at)
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
      monthlyData[monthKey] = (monthlyData[monthKey] || 0) + 1
    })
    return Object.entries(monthlyData)
      .map(([month, count]) => ({ month, count }))
      .sort((a, b) => a.month.localeCompare(b.month))
  })
  
  // Publishing activity (last 6 months)
  const publishingActivity = computed(() => {
    const sixMonthsAgo = new Date()
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6)
    const recentArticles = articlesStore.publishedArticles.filter(article => 
      article.published_at && new Date(article.published_at) >= sixMonthsAgo
    )
    const monthlyActivity: { [key: string]: number } = {}
    
    recentArticles.forEach(article => {
      if (article.published_at) {
        const date = new Date(article.published_at)
        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
        monthlyActivity[monthKey] = (monthlyActivity[monthKey] || 0) + 1
      }
    })
    return Object.entries(monthlyActivity)
      .map(([month, count]) => ({ month, count }))
      .sort((a, b) => a.month.localeCompare(b.month))
  })
  
  // Actions
  const loadDashboardData = async () => {
    try {
      loading.value = true
      error.value = null
      
      // Load articles data
      await articlesStore.loadUserArticles()
      
      lastUpdated.value = new Date()
      
    } catch (err: any) {
      console.error('Error loading dashboard data:', err)
      error.value = err.message || 'Failed to load dashboard data'
    } finally {
      loading.value = false
    }
  }
  
  // Initialize dashboard
  const initialize = async () => {
    // Load initial data
    await loadDashboardData()
  }
  
  // Refresh dashboard data
  const refresh = async () => {
    await loadDashboardData()
  }
  
  // Get dashboard summary
  const getSummary = () => {
    return {
      user: {
        name: profileStore.profile?.full_name || profileStore.user?.email || 'Unknown',
        role: profileStore.profile?.role || 'reader',
        avatar: profileStore.profile?.avatar_url
      },
      stats: stats.value,
      recentActivity: {
        articles: recentArticles.value,
        drafts: recentDrafts.value
      },
      charts: {
        articlesByMonth: articlesByMonth.value,
        publishingActivity: publishingActivity.value
      },
      lastUpdated: lastUpdated.value
    }
  }
  
  // Reset dashboard
  const reset = () => {
    loading.value = false
    error.value = null
    lastUpdated.value = null
    articlesStore.reset()
  }
  
  return {
    // State
    loading,
    error,
    lastUpdated,
    
    // Computed
    stats,
    recentArticles,
    recentDrafts,
    articlesByMonth,
    publishingActivity,
    
    // Actions
    loadDashboardData,
    initialize,
    refresh,
    getSummary,
    reset
  }
})
import { ref, computed, watch } from 'vue'

export type ThemeMode = 'light' | 'dark' | 'system'
export type ThemeColor = 'default' | 'blue' | 'green' | 'purple' | 'orange' | 'red'
export type ThemeRadius = 'none' | 'small' | 'default' | 'large'

export interface ThemeSettings {
  theme: ThemeMode
  primaryColor: ThemeColor
  radius: ThemeRadius
}

const STORAGE_KEY = 'blog-appearance-settings'

const defaultSettings: ThemeSettings = {
  theme: 'system',
  primaryColor: 'default',
  radius: 'default'
}

const colorSchemes = [
  { name: 'Default', value: 'default', primary: 'oklch(0.7 0.1 161)' },
  { name: 'Blue', value: 'blue', primary: 'oklch(0.6 0.2 240)' },
  { name: 'Green', value: 'green', primary: 'oklch(0.6 0.2 140)' },
  { name: 'Purple', value: 'purple', primary: 'oklch(0.6 0.2 280)' },
  { name: 'Orange', value: 'orange', primary: 'oklch(0.7 0.2 50)' },
  { name: 'Red', value: 'red', primary: 'oklch(0.6 0.2 20)' }
]

const radiusOptions = [
  { name: 'None', value: 'none', radius: '0px' },
  { name: 'Small', value: 'small', radius: '0.25rem' },
  { name: 'Default', value: 'default', radius: '0.625rem' },
  { name: 'Large', value: 'large', radius: '1rem' }
]

// Theme state
const settings = ref<ThemeSettings>({ ...defaultSettings })
const isInitialized = ref(false)

// Apply theme to document
const applyTheme = (theme: ThemeMode) => {
  const root = document.documentElement
  if (theme === 'dark') {
    root.classList.add('dark')
  } else if (theme === 'light') {
    root.classList.remove('dark')
  } else { // system
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
    if (prefersDark) {
      root.classList.add('dark')
    } else {
      root.classList.remove('dark')
    }
  }
}

// Apply color scheme
const applyColorScheme = (colorValue: ThemeColor) => {
  const root = document.documentElement
  const colorScheme = colorSchemes.find(c => c.value === colorValue)
  if (colorScheme) {
    root.style.setProperty('--primary', colorScheme.primary)
  }
}

// Apply border radius
const applyRadius = (radiusValue: ThemeRadius) => {
  const root = document.documentElement
  const radiusOption = radiusOptions.find(r => r.value === radiusValue)
  if (radiusOption) {
    // Set the main radius variable
    root.style.setProperty('--radius', radiusOption.radius)
    
    // Also set the theme radius variables with !important to override CSS
    root.style.setProperty('--radius-sm', radiusOption.radius, 'important')
    root.style.setProperty('--radius-md', radiusOption.radius, 'important')
    root.style.setProperty('--radius-lg', radiusOption.radius, 'important')
    root.style.setProperty('--radius-xl', radiusOption.radius, 'important')
  }
}

// Apply all settings
const applyAllSettings = () => {
  applyTheme(settings.value.theme)
  applyColorScheme(settings.value.primaryColor)
  applyRadius(settings.value.radius)
}

// Save settings to localStorage
const saveSettings = () => {
  localStorage.setItem(STORAGE_KEY, JSON.stringify(settings.value))
}

// Load settings from localStorage
const loadSettings = () => {
  const saved = localStorage.getItem(STORAGE_KEY)
  if (saved) {
    try {
      const parsed = JSON.parse(saved)
      settings.value = { ...defaultSettings, ...parsed }
    } catch (error) {
      console.error('Error loading settings:', error)
    }
  }
}

// Reset to default settings
const resetSettings = () => {
  settings.value = { ...defaultSettings }
  applyAllSettings()
  saveSettings()
}

// Toggle theme between light and dark
const toggleTheme = () => {
  if (settings.value.theme === 'system') {
    // If currently on system, switch to the opposite of current system preference
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
    settings.value.theme = prefersDark ? 'light' : 'dark'
  } else if (settings.value.theme === 'dark') {
    settings.value.theme = 'light'
  } else {
    settings.value.theme = 'dark'
  }
  // Apply the new theme immediately and save to localStorage
  applyTheme(settings.value.theme)
  saveSettings()
}

// Watch for system theme changes
const watchSystemTheme = () => {
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
  const handleSystemThemeChange = () => {
    if (settings.value.theme === 'system') {
      applyTheme('system')
    }
  }
  mediaQuery.addEventListener('change', handleSystemThemeChange)
  return () => mediaQuery.removeEventListener('change', handleSystemThemeChange)
}

// Initialize theme
const initializeTheme = () => {
  if (isInitialized.value) return
  
  loadSettings()
  applyAllSettings()
  
  // Watch for system theme changes
  const cleanupSystemTheme = watchSystemTheme()
  
  // Set initialization to complete after everything is set up
  setTimeout(() => {
    isInitialized.value = true
  }, 0)
  
  return cleanupSystemTheme
}

// Reactive computed properties
const currentTheme = computed(() => {
  if (settings.value.theme === 'system') {
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
  }
  return settings.value.theme
})

const isDarkMode = computed(() => currentTheme.value === 'dark')

// Export the theme composable
export function useTheme() {
  // Initialize on first use
  if (!isInitialized.value) {
    initializeTheme()
  }

  // Watch for settings changes and apply them immediately
  watch(() => settings.value.theme, (newTheme) => {
    applyTheme(newTheme)
    saveSettings()
  })

  watch(() => settings.value.primaryColor, (newColor) => {
    applyColorScheme(newColor)
    saveSettings()
  })

  watch(() => settings.value.radius, (newRadius) => {
    applyRadius(newRadius)
    saveSettings()
  })

  return {
    // State
    settings,
    isInitialized,

    // Computed
    currentTheme,
    isDarkMode,

    // Methods
    initializeTheme,
    applyTheme,
    applyColorScheme,
    applyRadius,
    applyAllSettings,
    saveSettings,
    loadSettings,
    resetSettings,
    toggleTheme,

    // Options for UI
    colorSchemes,
    radiusOptions,
    themeModes: ['light', 'dark', 'system'] as ThemeMode[]
  }
}

// Initialize theme globally when this module is imported
let globalCleanup: (() => void) | null = null

export function initializeGlobalTheme() {
  if (globalCleanup) return
  
  const cleanup = initializeTheme()
  if (cleanup) {
    globalCleanup = cleanup
  }
  return globalCleanup
}

// Cleanup function for when the app is destroyed
export function cleanupGlobalTheme() {
  if (globalCleanup) {
    globalCleanup()
    globalCleanup = null
  }
}
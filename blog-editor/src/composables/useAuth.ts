import { useProfileStore } from '@/stores/profileStore'

// Auto-initialize flag
let initialized = false

export function useAuth() {
  const profileStore = useProfileStore()
  
  // Auto-initialize on first use
  if (!initialized) {
    initialized = true
    // Initialize asynchronously
    profileStore.initialize().catch(error => {
      console.error('Auth initialization failed:', error)
    })
  }

  // Return store properties and methods
  return {
    // State
    user: profileStore.user,
    profile: profileStore.profile,
    loading: profileStore.loading,
    error: profileStore.error,
    
    // Computed
    isAuthenticated: profileStore.isAuthenticated,
    isAdmin: profileStore.isAdmin,
    isEditor: profileStore.isEditor,
    canWrite: profileStore.canWrite,
    
    // Methods
    initialize: profileStore.initialize,
    logout: profileStore.logout,
    refreshProfile: profileStore.refreshProfile,
    hasRole: profileStore.hasRole,
    hasAnyRole: profileStore.hasAnyRole
  }
}

export async function initializeAuth() {
  if (initialized) return
  const profileStore = useProfileStore()
  await profileStore.initialize()
  initialized = true
}
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import { initializeAuth } from '@/composables/useAuth'
import { initializeGlobalTheme } from '@/composables/useTheme'

// Import Prism.js CSS for syntax highlighting
import 'prismjs/themes/prism-tomorrow.css'

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)

// Initialize theme globally before authentication
initializeGlobalTheme()

// Initialize authentication before mounting
initializeAuth().then(() => {
  app.mount('#app')
})

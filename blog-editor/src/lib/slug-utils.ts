/**
 * Utility functions for generating and validating article slugs
 */

export interface SlugValidationResult {
  isValid: boolean
  isUnique: boolean
  suggestions?: string[]
  error?: string
}

/**
 * Generate a base slug from a title
 */
export function generateBaseSlug(title: string): string {
  return title
    .toLowerCase()
    .trim()
    // Replace Arabic/Urdu characters with transliterations
    .replace(/[أإآا]/g, 'a')
    .replace(/[ب]/g, 'b')
    .replace(/[ت]/g, 't')
    .replace(/[ث]/g, 'th')
    .replace(/[ج]/g, 'j')
    .replace(/[ح]/g, 'h')
    .replace(/[خ]/g, 'kh')
    .replace(/[د]/g, 'd')
    .replace(/[ذ]/g, 'dh')
    .replace(/[ر]/g, 'r')
    .replace(/[ز]/g, 'z')
    .replace(/[س]/g, 's')
    .replace(/[ش]/g, 'sh')
    .replace(/[ص]/g, 's')
    .replace(/[ض]/g, 'd')
    .replace(/[ط]/g, 't')
    .replace(/[ظ]/g, 'z')
    .replace(/[ع]/g, 'a')
    .replace(/[غ]/g, 'gh')
    .replace(/[ف]/g, 'f')
    .replace(/[ق]/g, 'q')
    .replace(/[ك]/g, 'k')
    .replace(/[ل]/g, 'l')
    .replace(/[م]/g, 'm')
    .replace(/[ن]/g, 'n')
    .replace(/[ه]/g, 'h')
    .replace(/[و]/g, 'w')
    .replace(/[ي]/g, 'y')
    // Remove special characters except hyphens and underscores
    .replace(/[^\w\s-]/g, '')
    // Replace multiple spaces/underscores with single hyphen
    .replace(/[\s_]+/g, '-')
    // Replace multiple hyphens with single hyphen
    .replace(/-+/g, '-')
    // Remove leading/trailing hyphens
    .replace(/^-+|-+$/g, '')
    // Limit length to 100 characters
    .substring(0, 100)
    // Remove trailing hyphen if created by substring
    .replace(/-+$/, '')
}

/**
 * Validate slug format
 */
export function validateSlugFormat(slug: string): boolean {
  // Slug should be 3-100 characters, lowercase, alphanumeric with hyphens
  const slugRegex = /^[a-z0-9]([a-z0-9-]*[a-z0-9])?$/
  return slug.length >= 3 && slug.length <= 100 && slugRegex.test(slug)
}

/**
 * Generate unique slug suggestions when there's a conflict
 */
export function generateSlugSuggestions(baseSlug: string, existingSlugs: string[]): string[] {
  const suggestions: string[] = []
  
  // Try with numbers
  for (let i = 2; i <= 10; i++) {
    const suggestion = `${baseSlug}-${i}`
    if (!existingSlugs.includes(suggestion)) {
      suggestions.push(suggestion)
    }
    if (suggestions.length >= 5) break
  }
  
  // Try with current date
  const today = new Date()
  const dateStr = today.toISOString().split('T')[0] // YYYY-MM-DD
  const dateSlug = `${baseSlug}-${dateStr}`
  if (!existingSlugs.includes(dateSlug) && !suggestions.includes(dateSlug)) {
    suggestions.push(dateSlug)
  }
  
  // Try with month-year
  const monthYear = `${today.getFullYear()}-${(today.getMonth() + 1).toString().padStart(2, '0')}`
  const monthYearSlug = `${baseSlug}-${monthYear}`
  if (!existingSlugs.includes(monthYearSlug) && !suggestions.includes(monthYearSlug)) {
    suggestions.push(monthYearSlug)
  }
  
  // Try with random suffix
  for (let i = 0; i < 3; i++) {
    const randomSuffix = Math.random().toString(36).substring(2, 6)
    const randomSlug = `${baseSlug}-${randomSuffix}`
    if (!existingSlugs.includes(randomSlug) && !suggestions.includes(randomSlug)) {
      suggestions.push(randomSlug)
    }
  }
  
  return suggestions.slice(0, 5)
}

/**
 * Check if slug is unique using Supabase
 */
export async function checkSlugUniqueness(slug: string, excludeId?: string): Promise<boolean> {
  try {
    // Import here to avoid circular dependencies
    const { checkSlugExists } = await import('./supabase')
    const exists = await checkSlugExists(slug, excludeId)
    return !exists
  } catch (error) {
    console.error('Error checking slug uniqueness:', error)
    // Fallback to assuming it's unique if there's an error
    return true
  }
}

/**
 * Validate slug completely (format + uniqueness)
 */
export async function validateSlug(slug: string, excludeId?: string): Promise<SlugValidationResult> {
  // Check format first
  if (!validateSlugFormat(slug)) {
    return {
      isValid: false,
      isUnique: false,
      error: 'Slug must be 3-100 characters, lowercase, and contain only letters, numbers, and hyphens'
    }
  }
  
  // Check uniqueness
  const isUnique = await checkSlugUniqueness(slug, excludeId)
  
  if (!isUnique) {
    // Generate suggestions
    const existingSlugs = [
      'getting-started-with-vue',
      'introduction-to-react', 
      'javascript-fundamentals',
      'css-grid-layout',
      'nodejs-best-practices'
    ]
    const suggestions = generateSlugSuggestions(slug, existingSlugs)
    
    return {
      isValid: true,
      isUnique: false,
      suggestions,
      error: 'This slug is already taken. Try one of the suggestions below.'
    }
  }
  
  return {
    isValid: true,
    isUnique: true
  }
}

/**
 * Generate a unique slug from title
 */
export async function generateUniqueSlug(title: string, excludeId?: string): Promise<string> {
  const baseSlug = generateBaseSlug(title)
  
  if (!baseSlug) {
    // Fallback for empty or invalid titles
    const fallbackSlug = `article-${Date.now()}`
    return fallbackSlug
  }
  
  const validation = await validateSlug(baseSlug, excludeId)
  
  if (validation.isUnique) {
    return baseSlug
  }
  
  // Try suggestions
  if (validation.suggestions && validation.suggestions.length > 0) {
    for (const suggestion of validation.suggestions) {
      const suggestionValidation = await validateSlug(suggestion, excludeId)
      if (suggestionValidation.isUnique) {
        return suggestion
      }
    }
  }
  
  // Last resort: timestamp-based slug
  const timestamp = Date.now()
  return `${baseSlug}-${timestamp}`
}

/**
 * Debounced slug validation for real-time feedback
 */
export function createDebouncedSlugValidator(
  callback: (result: SlugValidationResult) => void,
  delay: number = 500
) {
  let timeoutId: NodeJS.Timeout | null = null
  
  return async (slug: string, excludeId?: string) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    
    timeoutId = setTimeout(async () => {
      const result = await validateSlug(slug, excludeId)
      callback(result)
    }, delay)
  }
}

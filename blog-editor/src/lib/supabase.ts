import { createClient } from '@supabase/supabase-js'
// Supabase configuration
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'http://***************:8000'
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiYW5vbiIsImlzcyI6ImFidWgxMjMxIiwiaWF0IjoxNzQ6Mzg1MjAwLCJleHAiOjE5MDQxNTE2MDB9.EkgKcDa0V0VEmPsXPmyal3YvxYuu9Q1k8OZZv7Gs8_o'
// Get the correct redirect URL based on environment
const getRedirectUrl = () => {
  const isDev = import.meta.env.DEV
  if (isDev) {
    return 'http://localhost:5173'
  }
  
  // Check the current domain and return the appropriate URL
  const currentHost = window.location.hostname
  if (currentHost === 'editor.voicehype.ai') {
    return 'https://editor.voicehype.ai'
  }
  return 'https://blog.voicehype.ai'
}
// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  db: {
    schema: 'blog'
  },
  auth: {
    // Enable automatic token refresh
    autoRefreshToken: true,
    // Persist session in localStorage
    persistSession: true,
    // Detect session from URL fragment
    detectSessionInUrl: true,
    // Add retry configuration
    flowType: 'pkce'
  },
  // Add global error handling and retry logic
  global: {
    headers: {
      'X-Client-Info': 'voicehype-editor'
    }
  }
})
// Database types
export interface Profile {
  id: string
  username: string | null
  full_name: string | null
  email: string | null
  avatar_url: string | null
  website: string | null
  role: 'admin' | 'editor' | 'writer' | 'author' | 'reader'
  bio: string | null
  created_at: string
  updated_at: string
}
export interface Article {
  id: string
  title: string
  slug: string
  content: string | null
  excerpt: string | null
  cover_image_url: string | null
  author_id: string
  status: 'draft' | 'published' | 'archived'
  published_at: string | null
  created_at: string
  updated_at: string
  meta_title: string | null
  meta_description: string | null
  featured: boolean
}
// Simplified article tag interface
export interface ArticleTag {
  article_id: string
  tag_name: string
}
// Authentication helpers
export const getCurrentUser = async () => {
  const { data: { user }, error } = await supabase.auth.getUser()
  if (error) throw error
  return user
}
export const getCurrentProfile = async () => {
  const user = await getCurrentUser()
  if (!user) return null
  const { data: profile, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()
  if (error) throw error
  return profile as Profile
}
export const signIn = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password
  })
  if (error) throw error
  return data
}
export const signOut = async () => {
  const { error } = await supabase.auth.signOut()
  if (error) throw error
}
// OAuth authentication
export const signInWithOAuth = async (provider: 'google' | 'github') => {
  const redirectUrl = getRedirectUrl()
  const { data, error } = await supabase.auth.signInWithOAuth({
    provider,
    options: {
      redirectTo: redirectUrl
    }
  })
  if (error) throw error
  return data
}
export const signInWithGoogle = async () => {
  return signInWithOAuth('google')
}
export const signInWithGitHub = async () => {
  return signInWithOAuth('github')
}
// Handle OAuth callback
export const handleOAuthCallback = async () => {
  const { data, error } = await supabase.auth.getSession()
  if (error) throw error
  return data
}

// Article operations
export const createArticle = async (
  articleData: Partial<Article>,
  tags: string[] = []
) => {
  const user = await getCurrentUser()
  if (!user) throw new Error('User not authenticated')
  const { data: article, error: articleError } = await supabase
    .from('articles')
    .insert({
      ...articleData,
      author_id: user.id
    })
    .select()
    .single()
  if (articleError) throw articleError
  
  // Add tags directly to article_tags table
  if (tags.length > 0) {
    try {
      const articleTags = tags.map(tagName => ({
        article_id: article.id,
        tag_name: tagName.trim()
      }))
      const { error: tagError } = await supabase
        .from('article_tags')
        .insert(articleTags)
      if (tagError) {
        console.error('Error linking tags:', tagError)
        throw tagError // Throw the error so it can be caught and handled
      }
    } catch (error) {
      console.error('Error processing tags:', error)
      throw error // Re-throw the error
    }
  }
  return article as Article
}

export const updateArticle = async (
  id: string, 
  articleData: Partial<Article>, 
  tags?: string[]
) => {
  // Update article data
  const { data, error } = await supabase
    .from('articles')
    .update({
      ...articleData,
      updated_at: new Date().toISOString()
    })
    .eq('id', id)
    .select()
    .single()
  if (error) throw error
  
  // Update tags if provided
  if (tags !== undefined) {
    await setArticleTags(id, tags)
  }
  
  return data as Article
}
export const getArticle = async (id: string) => {
  const { data, error } = await supabase
    .from('articles')
    .select(`
      *,
      profiles:author_id (
        id,
        username,
        full_name,
        avatar_url
      ),
      article_tags (tag_name)
    `)
    .eq('id', id)
    .single()
  if (error) throw error
  return data
}
export const getArticleBySlug = async (slug: string) => {
  const { data, error } = await supabase
    .from('articles')
    .select(`
      *,
      profiles:author_id (
        id,
        username,
        full_name,
        avatar_url
      ),
      article_tags (tag_name)
    `)
    .eq('slug', slug)
    .single()
  if (error) throw error
  return data
}
export const getUserArticles = async (userId?: string) => {
  const user = userId || (await getCurrentUser())?.id
  if (!user) throw new Error('User not authenticated')
  const { data, error } = await supabase
    .from('articles')
    .select(`
      *,
      profiles:author_id (
        id,
        username,
        full_name,
        avatar_url
      ),
      article_tags (tag_name)
    `)
    .eq('author_id', user)
    .order('updated_at', { ascending: false })
  if (error) throw error
  return data
}
export const deleteArticle = async (id: string) => {
  // Tags will be automatically deleted due to ON DELETE CASCADE
  const { error } = await supabase
    .from('articles')
    .delete()
    .eq('id', id)
  if (error) throw error
}
// Slug validation
export const checkSlugExists = async (slug: string, excludeId?: string) => {
  let query = supabase
    .from('articles')
    .select('id')
    .eq('slug', slug)
  if (excludeId) {
    query = query.neq('id', excludeId)
  }
  const { data, error } = await query
  if (error) throw error
  return data && data.length > 0
}
// Article tags management
export const setArticleTags = async (articleId: string, tagNames: string[]) => {
  // First, remove existing tags
  await supabase
    .from('article_tags')
    .delete()
    .eq('article_id', articleId)
  
  // Then add new tags (if any)
  if (tagNames.length > 0) {
    const { error } = await supabase
      .from('article_tags')
      .insert(
        tagNames.map(tagName => ({
          article_id: articleId,
          tag_name: tagName.trim()
        }))
      )
    if (error) throw error
  }
}

// Get all unique tags from the system
export const getAllTags = async () => {
  const { data, error } = await supabase
    .from('article_tags')
    .select('tag_name')
    .order('tag_name')
  if (error) throw error
  
  // Extract unique tag names
  const uniqueTags = [...new Set(data.map(item => item.tag_name))]
  return uniqueTags
}

// For backward compatibility - returns tag names instead of tag objects
export const getTags = async () => {
  const tagNames = await getAllTags()
  // Convert to objects to match the expected interface
  return tagNames.map(name => ({ name }))
}
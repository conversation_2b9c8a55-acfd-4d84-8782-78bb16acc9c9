<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Textarea } from '@/components/ui/textarea'
import { getCurrentProfile, getUserArticles, supabase, type Profile } from '@/lib/supabase'

const router = useRouter()

// State
const isEditing = ref(false)
const isLoading = ref(true)
const isSaving = ref(false)
const error = ref<string | null>(null)
const currentProfile = ref<Profile | null>(null)
const articles = ref<any[]>([])

// Form data
const form = ref({
  username: '',
  full_name: '',
  email: '',
  bio: '',
  website: ''
})

// Computed stats
const stats = computed(() => {
  const totalArticles = articles.value.length
  const publishedArticles = articles.value.filter(a => a.status === 'published').length
  const draftArticles = articles.value.filter(a => a.status === 'draft').length
  const totalWords = articles.value.reduce((sum, article) => {
    return sum + (article.content?.split(/\s+/).filter((w: string) => w.length > 0).length || 0)
  }, 0)

  return {
    totalArticles,
    publishedArticles,
    draftArticles,
    totalWords
  }
})

// Load profile data
const loadProfileData = async () => {
  try {
    isLoading.value = true
    error.value = null

    const [profile, userArticles] = await Promise.all([
      getCurrentProfile(),
      getUserArticles()
    ])

    currentProfile.value = profile
    articles.value = userArticles || []

    // Initialize form with current profile data
    if (profile) {
      form.value = {
        username: profile.username || '',
        full_name: profile.full_name || '',
        email: profile.email || '',
        bio: profile.bio || '',
        website: profile.website || ''
      }
    }

  } catch (err) {
    console.error('Error loading profile:', err)
    error.value = 'Failed to load profile data'

    if ((err as Error).message?.includes('not authenticated')) {
      router.push('/login')
    }
  } finally {
    isLoading.value = false
  }
}

const handleEdit = () => {
  isEditing.value = true
}

const handleSave = async () => {
  if (!currentProfile.value) return

  try {
    isSaving.value = true

    const { data, error: updateError } = await supabase
      .from('profiles')
      .update({
        username: form.value.username || null,
        full_name: form.value.full_name || null,
        bio: form.value.bio || null,
        website: form.value.website || null,
        updated_at: new Date().toISOString()
      })
      .eq('id', currentProfile.value.id)
      .select()
      .single()

    if (updateError) throw updateError

    // Update local state
    currentProfile.value = { ...currentProfile.value, ...data }
    isEditing.value = false

    alert('Profile updated successfully!')

  } catch (err) {
    console.error('Error updating profile:', err)
    alert('Failed to update profile. Please try again.')
  } finally {
    isSaving.value = false
  }
}

const handleCancel = () => {
  isEditing.value = false
  // Reset form to current profile data
  if (currentProfile.value) {
    form.value = {
      username: currentProfile.value.username || '',
      full_name: currentProfile.value.full_name || '',
      email: currentProfile.value.email || '',
      bio: currentProfile.value.bio || '',
      website: currentProfile.value.website || ''
    }
  }
}

onMounted(() => {
  loadProfileData()
})
</script>

<template>
  <div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold">Profile</h1>
        <p class="text-muted-foreground">Manage your account information</p>
      </div>
      <Button v-if="!isEditing" @click="handleEdit">Edit Profile</Button>
    </div>

    <div class="lg:grid-cols-3 grid grid-cols-1 gap-6">
      <!-- Profile Info -->
      <div class="lg:col-span-1">
        <Card>
          <CardHeader class="text-center">
            <div class="flex justify-center mb-4">
              <Avatar class="w-24 h-24">
                <AvatarImage :src="currentProfile?.avatar_url || ''" />
                <AvatarFallback class="text-2xl">
                  {{ currentProfile?.full_name?.charAt(0) || currentProfile?.username?.charAt(0) || 'U' }}
                </AvatarFallback>
              </Avatar>
            </div>
            <CardTitle class="text-xl">
              {{ currentProfile?.full_name || currentProfile?.username || 'User' }}
            </CardTitle>
            <p class="text-muted-foreground">@{{ currentProfile?.username || 'username' }}</p>
            <Badge :variant="currentProfile?.role === 'admin' ? 'default' : 'secondary'" class="mt-2">
              {{ currentProfile?.role }}
            </Badge>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="text-center">
              <p class="text-muted-foreground mb-2 text-sm">
                {{ currentProfile?.bio || 'No bio provided yet.' }}
              </p>
            </div>
            
            <Separator />
            

          </CardContent>
        </Card>

        <!-- Stats -->
        <Card class="mt-6">
          <CardHeader>
            <CardTitle>Statistics</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="grid grid-cols-2 gap-4">
              <div class="text-center">
                <div class="text-2xl font-bold">{{ stats.totalArticles }}</div>
                <div class="text-muted-foreground text-sm">Total Articles</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold">{{ stats.publishedArticles }}</div>
                <div class="text-muted-foreground text-sm">Published</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold">{{ stats.draftArticles }}</div>
                <div class="text-muted-foreground text-sm">Drafts</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold">{{ stats.totalWords.toLocaleString() }}</div>
                <div class="text-muted-foreground text-sm">Total Words</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Edit Form -->
      <div class="lg:col-span-2">
        <Card>
          <CardHeader>
            <CardTitle>Profile Information</CardTitle>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="md:grid-cols-2 grid grid-cols-1 gap-4">
              <div class="space-y-2">
                <Label for="name">Full Name</Label>
                <Input
                  id="name"
                  v-model="form.full_name"
                  :disabled="!isEditing"
                />
              </div>
              <div class="space-y-2">
                <Label for="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  v-model="form.email"
                  :disabled="!isEditing"
                />
              </div>
            </div>

            <div class="space-y-2">
              <Label for="bio">Bio</Label>
              <Textarea
                id="bio"
                v-model="form.bio"
                :disabled="!isEditing"
                placeholder="Tell us about yourself..."
                class="min-h-[100px]"
              />
            </div>


            <div class="flex gap-2">
              <Button 
                v-if="isEditing" 
                @click="handleSave"
                class="hover:bg-green-700 bg-green-600"
              >
                Save Changes
              </Button>
              <Button 
                v-if="isEditing" 
                variant="outline" 
                @click="handleCancel"
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  </div>
</template>
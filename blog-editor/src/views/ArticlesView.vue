<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useArticlesStore } from '@/stores/articlesStore'
import { useProfileStore } from '@/stores/profileStore'

const router = useRouter()
const articlesStore = useArticlesStore()
const profileStore = useProfileStore()

// State
const searchQuery = ref('')
const sortBy = ref('date')
const filterStatus = ref('all')

// Load articles data
const loadArticles = async () => {
  await articlesStore.loadUserArticles()
}
// Helper functions
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const calculateReadTime = (content: string) => {
  const wordsPerMinute = 200
  const wordCount = content?.split(/\s+/).filter(w => w.length > 0).length || 0
  const readTime = Math.ceil(wordCount / wordsPerMinute)
  return `${readTime} min read`
}

// Computed properties
const filteredArticles = computed(() => {
  if (!searchQuery.value) return articlesStore.articles

  return articlesStore.searchArticles(searchQuery.value)
})

// Load articles on mount
onMounted(() => {
  loadArticles()
})

// Navigation functions
const editArticle = (articleId: string) => {
  router.push(`/articles/edit/${articleId}`)
}

const viewArticle = (articleId: string) => {
  router.push(`/articles/${articleId}`)
}



const getStatusColor = (status: string) => {
  switch (status) {
    case 'published':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
    case 'draft':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
    case 'archived':
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    default:
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
  }
}
</script>

<template>
  <div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold">Articles</h1>
        <p class="text-muted-foreground">Manage your blog posts and drafts</p>
      </div>
      <Button @click="router.push('/articles/new')">
        <span class="mr-2">+</span> New Article
      </Button>
    </div>

    <!-- Search and Filters -->
    <Card>
      <CardContent class="pt-6">
        <div class="md:flex-row flex flex-col gap-4">
          <div class="flex-1">
            <Input
              placeholder="Search articles..."
              v-model="searchQuery"
              class="w-full"
            />
          </div>
          <div class="flex gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger as-child>
                <Button variant="outline" class="w-[180px] justify-start">
                  {{ sortBy === 'date' ? 'Sort by Date' : sortBy === 'views' ? 'Sort by Views' : 'Sort by Title' }}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem @click="sortBy = 'date'">Sort by Date</DropdownMenuItem>
                <DropdownMenuItem @click="sortBy = 'views'">Sort by Views</DropdownMenuItem>
                <DropdownMenuItem @click="sortBy = 'title'">Sort by Title</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            
            <DropdownMenu>
              <DropdownMenuTrigger as-child>
                <Button variant="outline" class="w-[180px] justify-start">
                  {{ filterStatus === 'all' ? 'All Status' : filterStatus === 'published' ? 'Published' : 'Draft' }}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem @click="filterStatus = 'all'">All Status</DropdownMenuItem>
                <DropdownMenuItem @click="filterStatus = 'published'">Published</DropdownMenuItem>
                <DropdownMenuItem @click="filterStatus = 'draft'">Draft</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Articles List -->
    <div class="grid gap-6">
      <Card v-for="article in filteredArticles" :key="article.id" class="hover:shadow-md transition-shadow">
        <CardHeader>
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <CardTitle class="mb-2 text-xl">{{ article.title }}</CardTitle>
              <p class="text-muted-foreground mb-3">{{ article.excerpt }}</p>
              
              <!-- Article Info -->
              <div class="text-muted-foreground flex items-center gap-4 text-sm">
                <div class="flex items-center gap-2">
                  <Avatar class="w-6 h-6">
                    <AvatarFallback>
                      {{ profileStore.profile?.full_name?.charAt(0) || profileStore.profile?.username?.charAt(0) || 'U' }}
                    </AvatarFallback>
                  </Avatar>
                  <span>{{ profileStore.profile?.full_name || profileStore.profile?.username || 'Author' }}</span>
                </div>
                <span>{{ formatDate(article.created_at) }}</span>
                <span>{{ calculateReadTime(article.content || '') }}</span>
                <Badge :class="getStatusColor(article.status)">
                  {{ article.status }}
                </Badge>
              </div>
            </div>
            
            <div class="flex flex-col gap-2 ml-4">
              <Badge :class="getStatusColor(article.status)">
                {{ article.status }}
              </Badge>
              <Button variant="outline" size="sm" @click="editArticle(article.id)">Edit</Button>
              <Button variant="outline" size="sm" @click="viewArticle(article.id)">View</Button>
            </div>
          </div>
        </CardHeader>
      </Card>
    </div>

    <!-- No Results -->
    <div v-if="filteredArticles.length === 0" class="py-12 text-center">
      <p class="text-muted-foreground">No articles found matching your criteria.</p>
    </div>
  </div>
</template>
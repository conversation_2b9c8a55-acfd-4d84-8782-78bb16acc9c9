<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { toast } from 'vue-sonner'
import { Sun, Moon, Monitor } from 'lucide-vue-next'
import { useTheme } from '@/composables/useTheme'

const { settings, colorSchemes, radiusOptions, themeModes, resetSettings } = useTheme()

// Add a flag to track initialization
const localInitialized = ref(false)

// Apply all current settings

// Watch for settings changes and show toast
watch(() => settings.value, () => {
  // Settings are automatically saved by the centralized system
  if (localInitialized.value) {
    toast.success('Settings updated successfully!')
  }
}, { deep: true })

// Load settings on component mount
onMounted(() => {
  // Settings are already loaded by the centralized theme system
  localInitialized.value = true
})
</script>

<template>
  <div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold">Settings</h1>
        <p class="text-muted-foreground">Customize your blog editor appearance</p>
      </div>
      <div class="flex gap-2">
        <Button variant="outline" @click="resetSettings">Reset to Default</Button>
      </div>
    </div>

    <!-- Theme Settings -->
    <Card>
      <CardHeader>
        <CardTitle>Theme</CardTitle>
      </CardHeader>
      <CardContent class="space-y-6">
        <div class="space-y-3">
          <Label class="text-base font-medium">Choose your theme</Label>
          <div class="grid grid-cols-3 gap-3">
            <button
              v-for="theme in themeModes"
              :key="theme"
              @click="settings.theme = theme"
              :class="[
                'flex flex-col items-center gap-2 p-4 rounded-lg border-2 transition-all',
                settings.theme === theme
                  ? 'border-primary bg-primary/5'
                  : 'border-border hover:border-primary/50'
              ]"
            >
              <div :class="[
                'w-8 h-8 rounded-full flex items-center justify-center',
                theme === 'light' ? 'bg-white border border-gray-300' :
                theme === 'dark' ? 'bg-gray-900 border border-gray-600' :
                'bg-gray-100 border border-gray-300 dark:bg-gray-800 dark:border-gray-600'
              ]">
                <Sun v-if="theme === 'light'" class="w-4 h-4 text-yellow-500" />
                <Moon v-else-if="theme === 'dark'" class="w-4 h-4 text-blue-400" />
                <Monitor v-else class="dark:text-gray-300 w-4 h-4 text-gray-600" />
              </div>
              <span class="text-sm font-medium capitalize">{{ theme }}</span>
            </button>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Color Scheme -->
    <Card>
      <CardHeader>
        <CardTitle>Color Scheme</CardTitle>
      </CardHeader>
      <CardContent class="space-y-6">
        <div class="space-y-3">
          <Label class="text-base font-medium">Choose your accent color</Label>
          <div class="grid grid-cols-3 gap-3">
            <button
              v-for="color in colorSchemes"
              :key="color.value"
              @click="settings.primaryColor = color.value as any"
              :class="[
                'flex flex-col items-center gap-2 p-4 rounded-lg border-2 transition-all',
                settings.primaryColor === color.value
                  ? 'border-primary bg-primary/5'
                  : 'border-border hover:border-primary/50'
              ]"
            >
              <div
                class="w-8 h-8 border border-gray-300 rounded-full"
                :style="{ backgroundColor: color.primary }"
              ></div>
              <span class="text-sm font-medium">{{ color.name }}</span>
            </button>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Border Radius -->
    <Card>
      <CardHeader>
        <CardTitle>Border Radius</CardTitle>
      </CardHeader>
      <CardContent class="space-y-6">
        <div class="space-y-3">
          <Label class="text-base font-medium">Choose your preferred border radius</Label>
          <div class="grid grid-cols-4 gap-3">
            <button
              v-for="radius in radiusOptions"
              :key="radius.value"
              @click="settings.radius = radius.value as any"
              :class="[
                'flex flex-col items-center gap-2 p-4 rounded-lg border-2 transition-all',
                settings.radius === radius.value
                  ? 'border-primary bg-primary/5'
                  : 'border-border hover:border-primary/50'
              ]"
            >
              <div
                class="bg-primary/20 border-primary/30 w-8 h-8 border"
                :style="{ borderRadius: radius.radius }"
              ></div>
              <span class="text-sm font-medium">{{ radius.name }}</span>
            </button>
          </div>
        </div>
      </CardContent>
    </Card>

  </div>
</template>
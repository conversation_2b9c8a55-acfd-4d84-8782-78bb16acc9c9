<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { GalleryVerticalEnd, LockIcon } from "lucide-vue-next"
import { useRouter, useRoute } from 'vue-router'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import OAuthButton from '@/components/OAuthButton.vue'
import { signIn, signInWithGoogle, signInWithGitHub, handleOAuthCallback } from '@/lib/supabase'

const router = useRouter()
const route = useRoute()

// State
const email = ref('')
const password = ref('')
const rememberMe = ref(false)

// Loading states
const loading = ref(false)
const googleLoading = ref(false)
const githubLoading = ref(false)
const error = ref('')

// Check for OAuth callback on mount
onMounted(async () => {
  // Check if we have OAuth tokens in the URL
  const urlParams = new URLSearchParams(window.location.search)
  const accessToken = urlParams.get('access_token')

  if (accessToken) {
    try {
      // Handle OAuth callback
      await handleOAuthCallback()

      // Clear the URL parameters
      window.history.replaceState({}, document.title, window.location.pathname)

      // Redirect to intended page or dashboard
      const redirectPath = route.query.redirect as string || '/dashboard'
      router.replace(redirectPath)
    } catch (err) {
      console.error('OAuth callback error:', err)
      error.value = 'Authentication failed. Please try again.'
    }
  }
})

// OAuth sign-in methods
const handleGoogleSignIn = async () => {
  googleLoading.value = true
  error.value = ''

  try {
    const result = await signInWithGoogle()

    if (result && result.url) {
      // Redirect to the OAuth provider
      window.location.href = result.url
    } else {
      error.value = 'Failed to initiate Google sign-in. Please try again.'
    }
  } catch (err) {
    console.error('Google sign-in error:', err)
    error.value = 'An unexpected error occurred during Google sign-in.'
  } finally {
    googleLoading.value = false
  }
}

const handleGitHubSignIn = async () => {
  githubLoading.value = true
  error.value = ''

  try {
    const result = await signInWithGitHub()

    if (result && result.url) {
      // Redirect to the OAuth provider
      window.location.href = result.url
    } else {
      error.value = 'Failed to initiate GitHub sign-in. Please try again.'
    }
  } catch (err) {
    console.error('GitHub sign-in error:', err)
    error.value = 'An unexpected error occurred during GitHub sign-in.'
  } finally {
    githubLoading.value = false
  }
}

// Email/password sign-in
const handleLogin = async () => {
  loading.value = true
  error.value = ''

  try {
    const data = await signIn(email.value, password.value)

    if (data.user) {
      // Redirect to the original requested page or dashboard
      const redirectPath = route.query.redirect as string || '/dashboard'
      router.replace(redirectPath)
    } else {
      error.value = 'Failed to sign in. Please check your credentials.'
    }
  } catch (err) {
    console.error('Login error:', err)
    error.value = 'Invalid email or password. Please try again.'
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <div class="min-h-svh bg-muted md:p-10 flex flex-col items-center justify-center gap-6 p-6">
    <div class="flex flex-col w-full max-w-sm gap-6">
      <!-- Header -->
      <div class="flex items-center self-center gap-2 font-medium">
        <div class="bg-primary text-primary-foreground flex items-center justify-center w-6 h-6 rounded-md">
          <GalleryVerticalEnd class="size-4" />
        </div>
        VoiceHype Blog
      </div>

      <div class="text-center">
        <h2 class="text-2xl font-bold">Sign in to your account</h2>
        <p class="text-muted-foreground mt-2 text-sm">
          Access the VoiceHype blog editor
        </p>
      </div>

      <!-- Error Message -->
      <div v-if="error" class="px-4 py-3 text-sm text-red-700 bg-red-100 border border-red-400 rounded">
        {{ error }}
      </div>

      <!-- OAuth Buttons -->
      <div class="space-y-3">
        <OAuthButton
          provider="google"
          :loading="googleLoading"
          @click="handleGoogleSignIn"
        />
        <OAuthButton
          provider="github"
          :loading="githubLoading"
          @click="handleGitHubSignIn"
        />

        <div class="relative my-4">
          <div class="absolute inset-0 flex items-center">
            <Separator class="w-full" />
          </div>
          <div class="relative flex justify-center text-sm">
            <span class="text-muted-foreground bg-muted px-2">
              Or continue with email
            </span>
          </div>
        </div>
      </div>

      <!-- Email/Password Form -->
      <form class="space-y-4" @submit.prevent="handleLogin">
        <div class="space-y-2">
          <Label for="email">Email address</Label>
          <Input
            id="email"
            type="email"
            v-model="email"
            placeholder="<EMAIL>"
            required
          />
        </div>

        <div class="space-y-2">
          <Label for="password">Password</Label>
          <Input
            id="password"
            type="password"
            v-model="password"
            placeholder="Your password"
            required
          />
        </div>

        <div class="flex items-center space-x-2">
          <input
            id="remember-me"
            type="checkbox"
            v-model="rememberMe"
            class="text-primary focus:ring-primary w-4 h-4 border-gray-300 rounded"
          />
          <Label for="remember-me" class="text-sm">
            Remember me
          </Label>
        </div>

        <Button
          type="submit"
          class="w-full"
          :disabled="loading"
        >
          <LockIcon v-if="loading" class="animate-spin w-4 h-4 mr-2" />
          <LockIcon v-else class="w-4 h-4 mr-2" />
          {{ loading ? 'Signing in...' : 'Sign in' }}
        </Button>
      </form>
    </div>
  </div>
</template>
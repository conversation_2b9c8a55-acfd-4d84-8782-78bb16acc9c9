<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Card, CardContent } from '@/components/ui/card'
import MarkdownPreview from '@/components/MarkdownPreview.vue'
import { supabase } from '@/lib/supabase'
import { toast } from 'vue-sonner'

const route = useRoute()
const router = useRouter()

// Article data
const article = ref<any>(null)
const isLoading = ref(true)
const tags = ref<string[]>([])

// Load article data
const loadArticle = async () => {
  try {
    const articleId = route.params.id as string

    // Load article
    const { data: articleData, error: articleError } = await supabase
      .from('articles')
      .select('*')
      .eq('id', articleId)
      .single()

    if (articleError) {
      console.error('Error loading article:', articleError)
      toast.error('Article not found')
      router.push('/articles')
      return
    }

    article.value = articleData

    // Load article tags
    const { data: tagsData, error: tagsError } = await supabase
      .from('article_tags')
      .select('tag')
      .eq('article_id', articleId)

    if (!tagsError && tagsData) {
      tags.value = tagsData.map(t => t.tag)
    }

  } catch (error) {
    console.error('Error loading article:', error)
    toast.error('Failed to load article')
    router.push('/articles')
  } finally {
    isLoading.value = false
  }
}





// Load article on mount
onMounted(() => {
  loadArticle()
})
</script>

<template>
  <div class="p-6 space-y-6">
    <!-- Loading State -->
    <div v-if="isLoading" class="flex items-center justify-center py-12">
      <div class="text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
        <p class="text-muted-foreground">Loading article...</p>
      </div>
    </div>

    <!-- Article Content -->
    <div v-else-if="article">
      <!-- Markdown-only Article View -->
      <Card>
        <CardContent class="pt-6">
          <div class="prose prose-lg max-w-none dark:prose-invert">
            <MarkdownPreview :content="article.content || ''" />
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- Article Not Found -->
    <div v-else class="flex items-center justify-center py-12">
      <div class="text-center">
        <h2 class="text-2xl font-bold mb-2">Article Not Found</h2>
        <p class="text-muted-foreground mb-4">The article you're looking for doesn't exist.</p>
        <Button @click="router.push('/articles')">Back to Articles</Button>
      </div>
    </div>
  </div>
</template>


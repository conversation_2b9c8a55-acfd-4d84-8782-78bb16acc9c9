[build]
  command = "npm run build"
  publish = "dist"

# Redirect all paths to index.html for Vue Router history mode
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Headers for better security and caching
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "geolocation=(), microphone=()"

# Cache control for static assets
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

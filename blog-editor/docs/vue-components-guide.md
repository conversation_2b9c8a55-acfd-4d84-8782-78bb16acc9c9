# Vue Components Guide for VoiceHype Blog

This guide explains how to use Vue components in your markdown articles for the VoiceHype blog editor.

## Available Components

### 1. TryOutVoiceHype Component

A promotional component that encourages readers to try VoiceHype.

**Usage:**
```markdown
<TryOutVoiceHype />
```

**Description:**
- Displays a beautiful call-to-action box with VoiceHype branding
- Includes links to the VoiceHype website and VS Code extension
- No props required - it's a self-contained promotional component

**Example in Article:**
```markdown
# Getting Started with Voice-to-Prompt Development

Voice-to-prompt technology is revolutionizing how developers interact with AI...

<TryOutVoiceHype />

Now let's dive into the technical details...
```

---

### 2. Tip Component

A styled tip/note box for highlighting important information.

**Usage:**
```markdown
<Tip title="Your Title" content="Your tip content here" />
```

**Props:**
- `title` (optional): The title of the tip box. Defaults to "Tip"
- `content` (optional): The main content of the tip

**Important Notes:**
- **Always use quotes** for content with spaces
- Both title and content support text only (no HTML)
- The component displays with an amber/yellow color scheme

**Examples:**

**Simple tip with default title:**
```markdown
<Tip content="Remember to test your code before deploying!" />
```

**Custom title and content:**
```markdown
<Tip title="Flutter Performance" content="Use const constructors whenever possible for better widget performance" />
```

**Multi-word content (quotes required):**
```markdown
<Tip title="Important Security Note" content="Never expose your API keys in client-side code or public repositories" />
```

**Pro tip for developers:**
```markdown
<Tip title="VS Code Tip" content="Use Ctrl+Shift+P to open the command palette and access all VS Code features quickly" />
```

---

### 3. SubscribeNewsletter Component

A newsletter subscription form for collecting email addresses.

**Usage:**
```markdown
<SubscribeNewsletter />
```

**Description:**
- Displays a green-themed subscription form
- Includes email validation
- Shows loading states and success/error messages
- No props required - it's a self-contained subscription component

**Example in Article:**
```markdown
# Advanced Flutter Techniques

We've covered a lot of ground in this article...

<SubscribeNewsletter />

Thanks for reading! Don't forget to follow us for more Flutter tips.
```

---

## Best Practices

### 1. Component Placement
- Place components between paragraphs, not in the middle of sentences
- Use components to break up long sections of text
- Consider the visual flow of your article

### 2. Content Guidelines
- **Always use quotes** for any prop values that contain spaces
- Keep tip content concise and actionable
- Use descriptive titles for tip components

### 3. Markdown Integration
```markdown
# Your Article Title

Regular markdown content here...

## Section with a Tip

<Tip title="Pro Tip" content="This is how you integrate components seamlessly" />

More markdown content...

### Code Example

```python
def hello_world():
    print("Hello, VoiceHype!")
```

<TryOutVoiceHype />

## Conclusion

Final thoughts...

<SubscribeNewsletter />
```

---

## Common Mistakes and Solutions

### ❌ Wrong: Content without quotes
```markdown
<Tip content=Remember to use quotes for multi-word content />
```

### ✅ Correct: Content with quotes
```markdown
<Tip content="Remember to use quotes for multi-word content" />
```

### ❌ Wrong: HTML in content
```markdown
<Tip content="Use <strong>bold</strong> text here" />
```

### ✅ Correct: Plain text content
```markdown
<Tip content="Use bold text formatting in your markdown instead" />
```

### ❌ Wrong: Component in middle of sentence
```markdown
This is a sentence <Tip content="tip here" /> that continues.
```

### ✅ Correct: Component between paragraphs
```markdown
This is a complete sentence.

<Tip content="This tip is properly placed between paragraphs" />

This starts a new paragraph.
```

---

## Technical Notes

- Components are rendered as actual Vue components in the preview
- The markdown processor automatically detects component tags
- Component props are parsed and passed to the Vue components
- Styling is handled by the individual component files
- Components are responsive and work on all device sizes

---

## Need Help?

If you encounter issues with components:
1. Check that you're using quotes for multi-word content
2. Verify the component name is spelled correctly
3. Ensure components are placed between paragraphs
4. Check the browser console for any error messages

For more advanced component usage or custom components, contact the VoiceHype development team.

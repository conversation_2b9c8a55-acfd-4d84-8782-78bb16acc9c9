# Supabase Self-Hosted Backup Solution

This documentation provides a comprehensive guide for backing up your self-hosted Supabase instance running on a DigitalOcean droplet using Docker.

## Table of Contents

- [Overview](#overview)
- [Backup Components](#backup-components)
- [Prerequisites](#prerequisites)
- [Setup Instructions](#setup-instructions)
- [Manual Backup Commands](#manual-backup-commands)
- [Restoring from Backups](#restoring-from-backups)
- [Troubleshooting](#troubleshooting)
- [Security Considerations](#security-considerations)

## Overview

This backup solution addresses several critical needs:

1. **Database backups**: Regular PostgreSQL dumps of your Supabase database
2. **Docker volume backups**: Preservation of persistent data stored in Docker volumes
3. **Configuration backups**: Saving Docker Compose files and environment variables
4. **Remote backup transfers**: Optional offsite storage of backup files

The solution includes automated scheduling via cron jobs and retention policies to manage disk space.

## Backup Components

The backup system consists of four main scripts:

1. **`supabase-backup.sh`**: Creates database dumps and configuration backups
2. **`supabase-volume-backup.sh`**: Backs up Docker volumes used by Supabase
3. **`supabase-remote-backup.sh`**: Transfers backups to remote storage (optional)
4. **`setup-backup-cron.sh`**: Sets up automated cron jobs for the above scripts

## Prerequisites

- A DigitalOcean droplet running Supabase via Docker
- Docker and Docker Compose installed
- Sufficient disk space for backups
- Root or sudo access to the droplet

## Setup Instructions

### 1. Prepare the Environment

```bash
# Create directories for scripts and backups
mkdir -p ~/scripts
mkdir -p ~/supabase-backups
mkdir -p ~/supabase-backups/volumes

# Create log directory if it doesn't exist
sudo mkdir -p /var/log
```

### 2. Deploy the Scripts

Copy each script to your droplet. You can use various methods:

- Copy-paste through an SSH session
- Use `scp` to upload the files from your local machine
- Use `nano` or another editor to create the files directly on the server

Ensure all scripts are in the `~/scripts` directory:

```bash
# Set correct permissions
chmod +x ~/scripts/supabase-backup.sh
chmod +x ~/scripts/supabase-volume-backup.sh
chmod +x ~/scripts/supabase-remote-backup.sh
chmod +x ~/scripts/setup-backup-cron.sh
```

### 3. Configure the Scripts

Edit each script to customize paths and settings for your environment:

```bash
# Edit the database backup script
nano ~/scripts/supabase-backup.sh

# Important settings to customize:
# - BACKUP_DIR: Where backups will be stored
# - DOCKER_COMPOSE_DIR: Path to your Supabase docker-compose directory
# - RETENTION_DAYS: How many days to keep backups
```

Repeat this process for each script, ensuring all paths and configurations match your environment.

### 4. Set Up Automated Backups

Run the cron setup script to configure automated backups:

```bash
~/scripts/setup-backup-cron.sh
```

This will create the following schedule:
- Database backups: Daily at 2 AM
- Volume backups: Weekly on Sundays at 3 AM
- Remote backups (if configured): Daily at 4 AM

You can verify the cron jobs with:

```bash
crontab -l
```

### 5. Configure Remote Backups (Optional)

Edit the remote backup script to set up offsite storage:

```bash
nano ~/scripts/supabase-remote-backup.sh
```

Uncomment and configure one of the transfer methods:
- SCP to another server
- AWS S3 bucket
- SFTP server
- Rclone with various cloud storage providers

## Manual Backup Commands

### Finding Your PostgreSQL Container

First, identify your PostgreSQL container name:

```bash
# List all running containers
docker ps | grep postgres
```

In your case, the PostgreSQL container is:
```
b6c460ce8103   supabase/postgres:**********   "docker-entrypoint.s…"   12 days ago   Up 12 days (healthy)   5432/tcp   supabase-db
```

So you'll use `supabase-db` as your container name in the commands below:

### Complete Database Dump

For a full database dump including all data that properly handles circular foreign key dependencies:

```bash
# Find the PostgreSQL container
POSTGRES_CONTAINER=$(docker ps -qf "name=supabase-db")

# Create complete dump with options to handle circular dependencies
docker exec $POSTGRES_CONTAINER pg_dump -U postgres -d postgres \
  --clean --if-exists --no-owner --no-comments --disable-triggers \
  --format=custom -f /tmp/supabase_full_dump.backup

# Copy the backup file from the container to the host
docker cp $POSTGRES_CONTAINER:/tmp/supabase_full_dump.backup ~/supabase-backups/supabase_full_$(date +%Y-%m-%d).backup
```

The options used in this command:
- `--clean`: Adds commands to drop database objects before recreating them
- `--if-exists`: Uses IF EXISTS when dropping objects
- `--no-owner`: Skip restoration of object ownership
- `--no-comments`: Skip comments (reduces size and potential issues)
- `--disable-triggers`: Disables triggers during data loading, which solves circular dependency issues
- `--format=custom`: Uses PostgreSQL's custom format which is more flexible for restoration

### Docker Compose Configuration

To back up your Docker Compose configuration:

```bash
# Backup docker-compose files and environment variables
tar -czf ~/supabase-backups/supabase_compose_$(date +%Y-%m-%d).tar.gz -C /path/to/your/supabase/docker/directory .
```

### Manual Volume Backup

To manually back up Docker volumes:

```bash
# Run the volume backup script
~/scripts/supabase-volume-backup.sh
```

**Note**: This temporarily stops Supabase services to ensure data consistency.

## Restoring from Backups

### Restoring the Database

For restoring from a custom format backup:

```bash
# Find the PostgreSQL container
POSTGRES_CONTAINER=$(docker ps -qf "name=supabase-db")

# Copy the backup file to the container
docker cp ~/supabase-backups/supabase_full_YYYY-MM-DD.backup $POSTGRES_CONTAINER:/tmp/

# Restore from a custom format backup
docker exec $POSTGRES_CONTAINER pg_restore -U postgres -d postgres \
  --clean --if-exists --no-owner --no-comments --disable-triggers \
  --no-privileges --verbose /tmp/supabase_full_YYYY-MM-DD.backup
```

For restoring from a plain SQL dump:

```bash
# Find the PostgreSQL container
POSTGRES_CONTAINER=$(docker ps -qf "name=supabase-db")

# Restore from a SQL dump file
cat ~/supabase-backups/supabase_db_YYYY-MM-DD_HH-MM-SS.sql | \
  docker exec -i $POSTGRES_CONTAINER psql -U postgres -d postgres
```

### Restoring Volumes

To restore a Docker volume:

1. Stop the Supabase services:
   ```bash
   cd /path/to/your/supabase/docker-compose/directory
   docker-compose down
   ```

2. Remove the existing volume:
   ```bash
   docker volume rm supabase_db_data  # Replace with your volume name
   ```

3. Create a new volume:
   ```bash
   docker volume create supabase_db_data  # Replace with your volume name
   ```

4. Restore from backup:
   ```bash
   # Decompress the backup if needed
   gunzip ~/supabase-backups/volumes/supabase_db_data_YYYY-MM-DD_HH-MM-SS.tar.gz
   
   # Restore the volume
   docker run --rm -v supabase_db_data:/target -v ~/supabase-backups/volumes:/backup alpine sh -c "cd /target && tar xf /backup/supabase_db_data_YYYY-MM-DD_HH-MM-SS.tar"
   ```

5. Restart Supabase:
   ```bash
   docker-compose up -d
   ```

### Restoring Configuration

```bash
# Extract the configuration backup to a temporary directory
mkdir -p ~/temp-config
tar -xzf ~/supabase-backups/supabase_compose_YYYY-MM-DD_HH-MM-SS.tar.gz -C ~/temp-config

# Review and copy the files to your Supabase directory
cp -r ~/temp-config/* /path/to/your/supabase/docker/directory/
```

## Troubleshooting

### Backup Script Errors

Check the log files for detailed error messages:

```bash
cat /var/log/supabase-db-backup.log
cat /var/log/supabase-volume-backup.log
cat /var/log/supabase-remote-backup.log
```

### Common Issues

1. **Insufficient disk space**: 
   ```bash
   # Check available disk space
   df -h
   ```

2. **Permission issues**:
   ```bash
   # Ensure proper permissions
   sudo chown -R $(whoami) ~/scripts
   sudo chown -R $(whoami) ~/supabase-backups
   ```

3. **Docker container not found**:
   ```bash
   # List all containers to verify names
   docker ps
   ```

## Security Considerations

1. **Backup encryption**: For sensitive data, consider encrypting your backups:
   ```bash
   # Encrypt a backup file
   gpg --symmetric --cipher-algo AES256 ~/supabase-backups/supabase_db_YYYY-MM-DD.sql
   ```

2. **Access control**: Restrict access to backup files:
   ```bash
   # Set restrictive permissions
   chmod 600 ~/supabase-backups/*.sql
   chmod 600 ~/supabase-backups/*.tar.gz
   ```

3. **Secure transfer**: When transferring backups, use secure methods like SFTP or encrypted S3 buckets.

4. **API keys and secrets**: Be careful with configuration backups that might contain sensitive credentials.

---

This documentation was created on June 16, 2025, for VoiceHype's self-hosted Supabase backup solution.

# Quick Command for Immediate Database Backup

Here's a command you can run right now on your DigitalOcean droplet to create a full database dump:

```bash
# Create backup directory
mkdir -p ~/supabase-backups

# Set the PostgreSQL container name to your specific container
POSTGRES_CONTAINER="supabase-db"

# Create a full backup in custom format
docker exec $POSTGRES_CONTAINER pg_dump -U postgres -d postgres \
  --clean --if-exists --no-owner --no-comments --disable-triggers \
  --format=custom -f /tmp/supabase_full_dump.backup

# Copy the backup file from the container to your host system
docker cp $POSTGRES_CONTAINER:/tmp/supabase_full_dump.backup ~/supabase-backups/supabase_full_$(date +%Y-%m-%d).backup

# Verify backup file exists and check size
ls -lh ~/supabase-backups/
```

This command has been customized for your specific Supabase setup with container name `supabase-db`.

## Using the Fixed Backup Scripts

I've created updated versions of the backup scripts specifically for your setup:

1. `supabase-backup-fixed.sh` - Database backup script with your container name
2. `supabase-volume-backup-fixed.sh` - Volume backup script 

To use these scripts:

```bash
# Upload the scripts to your server
# (Use scp or copy-paste content into files on the server)

# Make them executable
chmod +x ~/scripts/supabase-backup-fixed.sh
chmod +x ~/scripts/supabase-volume-backup-fixed.sh

# Run the database backup script
~/scripts/supabase-backup-fixed.sh

# Run the volume backup script (use with caution as it might require stopping services)
~/scripts/supabase-volume-backup-fixed.sh
```

## Connecting to Your Droplet with FileZilla

If you need to transfer files between your local machine and your DigitalOcean droplet (for example, to download backup files), you can use FileZilla. Here are different methods depending on your situation:

### Option 1: Using Password Authentication (Temporary Solution)

If your SSH key is on another laptop and you need access from your current laptop:

1. **Temporarily enable password authentication on your droplet**:
   ```bash
   # SSH into your droplet from the laptop that has your SSH key
   ssh root@your-droplet-ip
   
   # Edit the SSH config file
   nano /etc/ssh/sshd_config
   
   # Find and change these lines
   PasswordAuthentication yes
   
   # Save the file (Ctrl+O, then Enter, then Ctrl+X)
   
   # Restart the SSH service
   systemctl restart sshd
   ```

2. **Connect with FileZilla**:
   - Open FileZilla
   - Host: `sftp://your-droplet-ip`
   - Username: `root` (or your username)
   - Password: Your droplet's root password
   - Port: 22
   - Click "Quickconnect"

3. **Important: Disable password authentication after use**:
   ```bash
   # Change back to key-based authentication only
   nano /etc/ssh/sshd_config
   
   # Set back to
   PasswordAuthentication no
   
   # Restart SSH service
   systemctl restart sshd
   ```

### Option 2: Copy Your SSH Key to Your Current Laptop

If you have access to both laptops:

1. **Copy the SSH key from the other laptop**:
   - On the laptop with the key, locate your private key file (typically `~/.ssh/id_rsa`)
   - Copy this file to a secure USB drive or use secure file transfer

2. **Import the key on your current laptop**:
   - Create an `.ssh` directory if it doesn't exist: `mkdir -p ~/.ssh`
   - Copy the key file to `~/.ssh/`
   - Set proper permissions: `chmod 600 ~/.ssh/id_rsa`

3. **Connect with FileZilla using the key**:
   - Open FileZilla
   - Go to Edit > Settings
   - Select SFTP under Connection
   - Click "Add key file" and browse to your key
   - Then connect using:
     - Host: `sftp://your-droplet-ip`
     - Username: `root`
     - Password: (leave empty)
     - Port: 22

### Option 3: Create a Temporary New SSH Key Pair

1. **Generate a new key pair on your current laptop**:
   ```bash
   ssh-keygen -t rsa -b 4096 -f ~/.ssh/do_temp_key
   ```

2. **Add the public key to your droplet** (from the laptop that has SSH access):
   ```bash
   # Copy the public key content (from your current laptop)
   cat ~/.ssh/do_temp_key.pub
   
   # On the laptop with SSH access, add this key to the droplet
   ssh root@your-droplet-ip
   echo "public-key-content-here" >> ~/.ssh/authorized_keys
   ```

3. **Connect with FileZilla using the new key**:
   - Configure FileZilla to use your newly created `do_temp_key` private key
   - Connect using SFTP with your droplet IP and username

### Option 4: Use SCP to Transfer Files via Command Line

If you just need to download backup files:

```bash
# On your current laptop, use scp with the -i flag for identity file
# (If you've copied your SSH key or created a temporary one)
scp -i ~/.ssh/your_key_file root@your-droplet-ip:/path/to/backup/file /local/destination/path
```

## Downloading Backup Files from the Droplet

### Option 1: Using Digital Ocean's Console and Browser Download

If you have access to your droplet through Digital Ocean's web console, you can download the backup file directly:

1. **Make the backup file downloadable** (from the DO console):
   ```bash
   # Navigate to the backup directory
   cd ~/supabase-backups

   # Ensure the file is readable
   chmod 644 supabase_full_*.backup

   # Move the file to a web-accessible location
   mkdir -p /var/www/html/temp
   cp supabase_full_*.backup /var/www/html/temp/

   # Create a temporary web server
   cd /var/www/html/temp
   python3 -m http.server 8080
   ```

2. **Create a temporary firewall rule** to allow access:
   ```bash
   # Add a temporary firewall rule to allow port 8080
   ufw allow 8080/tcp
   ```

3. **Download the file**:
   - Open your browser and navigate to `http://your-droplet-ip:8080`
   - Click on the backup f
   ile to download it
   - After downloading, stop the server with Ctrl+C in the console

4. **Clean up**:
   ```bash
   # Remove the temporary server and firewall rule
   ufw delete allow 8080/tcp
   rm -rf /var/www/html/temp
   ```

### Option 2: Using Base64 for Small Files

For smaller backup files (under 100MB), you can use base64 encoding:

```bash
# On the droplet (in DO console)
cd ~/supabase-backups
base64 supabase_full_2025-06-16.backup > encoded_backup.txt

# View the beginning of the file to copy
head -c 1000 encoded_backup.txt
# (copy this output)

# Continue viewing and copying in chunks
tail -c +1001 encoded_backup.txt | head -c 1000
# (continue until you've copied the entire file)
```

Then on your local machine:
```bash
# Create a file with the copied base64 content
nano encoded_backup.txt
# (paste all the content you copied)

# Decode it
base64 -d encoded_backup.txt > supabase_full_2025-06-16.backup
```

### Option 3: Transfer Using Digital Ocean Spaces

If you have Digital Ocean Spaces enabled:

```bash
# Install AWS CLI on the droplet
apt update && apt install -y awscli

# Configure AWS CLI for Spaces
aws configure
# Enter your Spaces access key and secret
# Use the region for your Space (e.g., nyc3)

# Upload to Spaces
aws s3 cp ~/supabase-backups/supabase_full_2025-06-16.backup s3://your-space-name/backups/ --endpoint=https://nyc3.digitaloceanspaces.com

# The file will then be available in your DO Spaces account for download
```

### Option 4: Using SFTP in the DO Console

Digital Ocean's console has a built-in file manager you can use:

1. In the DO dashboard, select your droplet
2. Go to "Access" > "Launch Console"
3. Use the file manager feature (if available) to download files
4. Navigate to ~/supabase-backups and download your backup file

## Extracting Only Database Functions

If you only need the function definitions from your database (rather than the entire backup), you can extract just the functions using this approach:

### Method 1: Direct Extraction from the Database

This is the recommended approach:

```bash
# On your droplet
POSTGRES_CONTAINER="supabase-db"

# Extract only functions to a text file
docker exec $POSTGRES_CONTAINER pg_dump -U postgres -d postgres \
  --schema-only \
  --section=pre-data \
  --section=post-data \
  | grep -A 1000000 -P "^--\n-- Name: .*; Type: FUNCTION" \
  | grep -B 1000000 -P "^--\n-- PostgreSQL database dump complete" \
  > supabase_functions.sql

# View the extracted functions
less supabase_functions.sql

# Count the number of functions
grep -c "CREATE OR REPLACE FUNCTION" supabase_functions.sql
```

This will create a plain SQL file containing just the function definitions, which you can easily download using any of the previously mentioned methods.

### Method 2: For a Specific Function

If you need just one specific function (like `create_api_key`):

```bash
# Extract a specific function by name
docker exec $POSTGRES_CONTAINER pg_dump -U postgres -d postgres \
  --schema-only \
  | grep -A 100 -B 10 "CREATE OR REPLACE FUNCTION.*create_api_key" \
  | grep -B 1000 -m 1 "ALTER FUNCTION" \
  > create_api_key_function.sql
```

### Using the extract_functions.sh Script

I've created a script to help with this task:

```bash
# Upload the script to your droplet
# Make it executable
chmod +x extract_functions.sh

# Run it
./extract_functions.sh
```

The script will:
1. Extract only the function definitions
2. Save them to a file named `supabase_functions_YYYY-MM-DD.sql`
3. List all the extracted functions

This gives you a plain text SQL file containing just the functions, which is much easier to work with than the binary custom format backup.

#!/bin/bash
# Supabase Remote Backup Script
# Transfers local backups to a remote location

# Configuration
LOCAL_BACKUP_DIR="/root/supabase-backups"
TIMESTAMP=$(date +%Y-%m-%d_%H-%M-%S)
LOG_FILE="/var/log/supabase-remote-backup.log"

# Remote backup options - uncomment and configure ONE of these options:

# 1. SCP to another server
#REMOTE_USER="your_username"
#REMOTE_HOST="your_remote_host"
#REMOTE_DIR="/path/to/remote/backup/directory"

# 2. AWS S3 configuration (requires AWS CLI to be installed and configured)
#S3_BUCKET="your-backup-bucket"
#S3_PREFIX="supabase-backups"

# 3. SFTP configuration
#SFTP_USER="your_sftp_username"
#SFTP_HOST="your_sftp_host"
#SFTP_DIR="/path/to/sftp/directory"

# 4. Rclone configuration (requires rclone to be installed and configured)
#RCLONE_REMOTE="your_rclone_remote"
#RCLONE_DIR="supabase-backups"

# Function to log messages
log_message() {
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "${LOG_FILE}"
}

log_message "Starting remote backup process"

# Create daily archive of all local backups
DAILY_ARCHIVE="${LOCAL_BACKUP_DIR}/supabase_all_backups_${TIMESTAMP}.tar.gz"
log_message "Creating daily archive: ${DAILY_ARCHIVE}"
tar -czf "${DAILY_ARCHIVE}" -C "${LOCAL_BACKUP_DIR}" --exclude="*.tar.gz" .

# Now transfer the daily archive to your preferred remote location
# Uncomment ONE of the following sections based on your preference:

# 1. SCP Transfer
#if [ -n "${REMOTE_USER}" ] && [ -n "${REMOTE_HOST}" ] && [ -n "${REMOTE_DIR}" ]; then
#  log_message "Transferring archive to remote server via SCP"
#  scp "${DAILY_ARCHIVE}" "${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_DIR}/"
#  if [ $? -eq 0 ]; then
#    log_message "SCP transfer successful"
#  else
#    log_message "ERROR: SCP transfer failed"
#  fi
#fi

# 2. AWS S3 Transfer
#if [ -n "${S3_BUCKET}" ]; then
#  log_message "Transferring archive to AWS S3"
#  aws s3 cp "${DAILY_ARCHIVE}" "s3://${S3_BUCKET}/${S3_PREFIX}/$(basename ${DAILY_ARCHIVE})"
#  if [ $? -eq 0 ]; then
#    log_message "AWS S3 transfer successful"
#  else
#    log_message "ERROR: AWS S3 transfer failed"
#  fi
#fi

# 3. SFTP Transfer
#if [ -n "${SFTP_USER}" ] && [ -n "${SFTP_HOST}" ] && [ -n "${SFTP_DIR}" ]; then
#  log_message "Transferring archive via SFTP"
#  echo "put ${DAILY_ARCHIVE} ${SFTP_DIR}/$(basename ${DAILY_ARCHIVE})" | sftp "${SFTP_USER}@${SFTP_HOST}"
#  if [ $? -eq 0 ]; then
#    log_message "SFTP transfer successful"
#  else
#    log_message "ERROR: SFTP transfer failed"
#  fi
#fi

# 4. Rclone Transfer
#if [ -n "${RCLONE_REMOTE}" ] && [ -n "${RCLONE_DIR}" ]; then
#  log_message "Transferring archive via Rclone"
#  rclone copy "${DAILY_ARCHIVE}" "${RCLONE_REMOTE}:${RCLONE_DIR}"
#  if [ $? -eq 0 ]; then
#    log_message "Rclone transfer successful"
#  else
#    log_message "ERROR: Rclone transfer failed"
#  fi
#fi

log_message "Remote backup process completed"

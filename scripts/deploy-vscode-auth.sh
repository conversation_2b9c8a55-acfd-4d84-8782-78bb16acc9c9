#!/bin/bash

# VS Code Extension Authentication Deployment Script
# This script deploys the authentication edge functions and verifies the setup

set -e  # Exit on any error

echo "🚀 Starting VS Code Authentication Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "supabase/config.toml" ]; then
    print_error "Please run this script from the project root directory (where supabase/config.toml exists)"
    exit 1
fi

print_status "Found Supabase configuration"

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    print_error "Supabase CLI is not installed. Please install it first:"
    echo "npm install -g supabase"
    exit 1
fi

print_status "Supabase CLI is installed"

# Check if logged in to Supabase
if ! supabase projects list &> /dev/null; then
    print_error "Not logged in to Supabase. Please run: supabase login"
    exit 1
fi

print_status "Authenticated with Supabase"

echo ""
echo "📊 Checking current project status..."

# Get project info
PROJECT_REF=$(grep 'project_id' supabase/config.toml | cut -d'"' -f2)
if [ -z "$PROJECT_REF" ]; then
    print_error "Could not find project_id in supabase/config.toml"
    exit 1
fi

print_status "Project ID: $PROJECT_REF"

echo ""
echo "🗄️ Applying database migrations..."

# Apply migrations
if supabase db push; then
    print_status "Database migrations applied successfully"
else
    print_error "Failed to apply database migrations"
    exit 1
fi

echo ""
echo "🔧 Deploying edge function..."

# Deploy vscode-extension-auth function
echo "Deploying vscode-extension-auth function..."
if supabase functions deploy vscode-extension-auth; then
    print_status "vscode-extension-auth function deployed"
else
    print_error "Failed to deploy vscode-extension-auth function"
    exit 1
fi

echo ""
echo "🔐 Checking environment variables..."

# Check if required secrets are set
SUPABASE_URL="https://${PROJECT_REF}.supabase.co"
echo "SUPABASE_URL should be: $SUPABASE_URL"

print_warning "Please verify these environment variables are set in your Supabase project:"
echo "  - SUPABASE_URL: $SUPABASE_URL"
echo "  - SUPABASE_SERVICE_ROLE_KEY: (your service role key)"
echo ""
echo "You can set them with:"
echo "  supabase secrets set SUPABASE_URL=$SUPABASE_URL"
echo "  supabase secrets set SUPABASE_SERVICE_ROLE_KEY=your-service-role-key"

echo ""
echo "🔍 Verifying deployment..."

# List deployed functions
echo "Currently deployed functions:"
supabase functions list

echo ""
echo "🎯 Testing function endpoints..."

# Test if functions are accessible (basic connectivity)
AUTH_URL="https://${PROJECT_REF}.supabase.co/functions/v1/vscode-extension-auth"
WEBSITE_URL="https://voicehype.ai/vscode-auth"

echo "Edge function URL: $AUTH_URL"
echo "Website auth URL: $WEBSITE_URL"

echo ""
echo "✅ Deployment completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Deploy the updated VoiceHype website with the new /vscode-auth route"
echo "2. Test the authentication flow in the VS Code extension"
echo "3. Check function logs if there are any issues:"
echo "   supabase functions logs vscode-extension-auth"
echo ""
echo "📖 For detailed setup instructions, see:"
echo "   extension/docs/vscode-auth-deployment-guide.md"

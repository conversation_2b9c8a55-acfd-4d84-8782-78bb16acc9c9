#!/bin/bash
# Supabase Backup Cron Setup Script
# Sets up cron jobs to automate backups

# Define backup paths
DB_BACKUP_SCRIPT="/root/scripts/supabase-backup.sh"
VOLUME_BACKUP_SCRIPT="/root/scripts/supabase-volume-backup.sh"
REMOTE_BACKUP_SCRIPT="/root/scripts/supabase-remote-backup.sh"

# Make scripts executable
chmod +x "${DB_BACKUP_SCRIPT}"
chmod +x "${VOLUME_BACKUP_SCRIPT}"
[ -f "${REMOTE_BACKUP_SCRIPT}" ] && chmod +x "${REMOTE_BACKUP_SCRIPT}"

# Create temp file for cron jobs
TEMP_CRON=$(mktemp)

# Dump existing crontab
crontab -l > "${TEMP_CRON}" 2>/dev/null || echo "# Supabase backup cron jobs" > "${TEMP_CRON}"

# Add database backup job (daily at 2 AM)
if ! grep -q "${DB_BACKUP_SCRIPT}" "${TEMP_CRON}"; then
  echo "0 2 * * * ${DB_BACKUP_SCRIPT} >> /var/log/supabase-db-backup.log 2>&1" >> "${TEMP_CRON}"
  echo "Added database backup cron job"
fi

# Add volume backup job (weekly on Sunday at 3 AM)
if ! grep -q "${VOLUME_BACKUP_SCRIPT}" "${TEMP_CRON}"; then
  echo "0 3 * * 0 ${VOLUME_BACKUP_SCRIPT} >> /var/log/supabase-volume-backup.log 2>&1" >> "${TEMP_CRON}"
  echo "Added volume backup cron job"
fi

# Add remote backup job if exists (daily at 4 AM)
if [ -f "${REMOTE_BACKUP_SCRIPT}" ] && ! grep -q "${REMOTE_BACKUP_SCRIPT}" "${TEMP_CRON}"; then
  echo "0 4 * * * ${REMOTE_BACKUP_SCRIPT} >> /var/log/supabase-remote-backup.log 2>&1" >> "${TEMP_CRON}"
  echo "Added remote backup cron job"
fi

# Install crontab
crontab "${TEMP_CRON}"
rm "${TEMP_CRON}"

echo "Cron jobs installed successfully"
echo "You can check the backup logs in /var/log/"
echo "  - Database backups: /var/log/supabase-db-backup.log"
echo "  - Volume backups: /var/log/supabase-volume-backup.log"
echo "  - Remote backups (if configured): /var/log/supabase-remote-backup.log"

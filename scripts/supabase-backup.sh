#!/bin/bash
# Supabase PostgreSQL Backup Script
# Creates a daily backup of your Supabase PostgreSQL database

# Configuration
BACKUP_DIR="/root/supabase-backups"
DOCKER_COMPOSE_DIR="/path/to/your/supabase/docker-compose/directory"
DATE=$(date +%Y-%m-%d)
TIMESTAMP=$(date +%Y-%m-%d_%H-%M-%S)
POSTGRES_CONTAINER=$(docker ps -qf "name=supabase-db")
BACKUP_FILENAME="supabase_db_${TIMESTAMP}.sql"
RETENTION_DAYS=14  # How many days to keep backups

# Create backup directory if it doesn't exist
mkdir -p "${BACKUP_DIR}"

# Ensure we're in the docker-compose directory to access env vars
cd "${DOCKER_COMPOSE_DIR}"

# Get database credentials from .env file (if using Docker Compose with .env)
source .env 2>/dev/null

# Set default values if not found in .env
DB_NAME=${POSTGRES_DB:-postgres}
DB_USER=${POSTGRES_USER:-postgres}
DB_PASSWORD=${POSTGRES_PASSWORD:-postgres}

echo "Creating backup of ${DB_NAME} database..."

# Create the database dump using custom format to handle circular dependencies
echo "Creating backup of ${DB_NAME} database in custom format..."
docker exec "${POSTGRES_CONTAINER}" pg_dump -U "${DB_USER}" -d "${DB_NAME}" \
  --clean --if-exists --no-owner --no-comments --disable-triggers \
  --format=custom -f /tmp/supabase_dump.backup

# Copy the backup file from the container to the host
docker cp "${POSTGRES_CONTAINER}:/tmp/supabase_dump.backup" "${BACKUP_DIR}/${BACKUP_FILENAME}.backup"

# Compress the backup
gzip "${BACKUP_DIR}/${BACKUP_FILENAME}.backup"
echo "Database backup created: ${BACKUP_DIR}/${BACKUP_FILENAME}.backup.gz"

# Check if the backup was successful
if [ $? -eq 0 ]; then
    echo "Backup completed successfully"
    
    # Optional: Cleanup old backups
    find "${BACKUP_DIR}" -name "supabase_db_*.backup.gz" -type f -mtime +${RETENTION_DAYS} -delete
    echo "Removed backups older than ${RETENTION_DAYS} days"
    
    # Optional: Calculate backup size
    BACKUP_SIZE=$(du -h "${BACKUP_DIR}/${BACKUP_FILENAME}.backup.gz" | cut -f1)
    echo "Backup size: ${BACKUP_SIZE}"
else
    echo "Backup failed"
    exit 1
fi

# Create a backup of the Docker Compose files and environment variables
COMPOSE_BACKUP="${BACKUP_DIR}/supabase_compose_${TIMESTAMP}.tar.gz"
tar -czf "${COMPOSE_BACKUP}" -C "${DOCKER_COMPOSE_DIR}" .
echo "Docker Compose configuration backed up to: ${COMPOSE_BACKUP}"

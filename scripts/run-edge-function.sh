#!/bin/bash
# <PERSON>ript to run a Supabase edge function locally with Deno

# Check if function name is provided
if [ -z "$1" ]; then
  echo "Usage: $0 <function-name>"
  echo "Example: $0 create-paddle-checkout"
  exit 1
fi

FUNCTION_NAME=$1

# Check if the function directory exists
if [ ! -d "./supabase/functions/$FUNCTION_NAME" ]; then
  echo "Error: Function '$FUNCTION_NAME' not found in ./supabase/functions/"
  exit 1
fi

echo "Running $FUNCTION_NAME locally..."
echo "Press Ctrl+C to stop the server"

# Run the function with Deno
cd supabase/functions
deno run --allow-net --allow-env --allow-read --watch ./$FUNCTION_NAME/index.ts

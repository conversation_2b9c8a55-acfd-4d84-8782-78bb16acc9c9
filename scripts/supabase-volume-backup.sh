#!/bin/bash
# Supabase Docker Volume Backup Script
# Creates a backup of the Docker volumes used by Supabase

# Configuration
BACKUP_DIR="/root/supabase-backups/volumes"
TIMESTAMP=$(date +%Y-%m-%d_%H-%M-%S)
RETENTION_DAYS=7  # How many days to keep volume backups

# Volumes to backup - customize based on your Supabase setup
VOLUMES=(
  "supabase_db_data"
  "supabase_storage_data" 
  "supabase_kong_data"
  "supabase_redis_data"
  # Add any other volumes your Supabase instance uses
)

# Create backup directory if it doesn't exist
mkdir -p "${BACKUP_DIR}"

echo "Starting Docker volume backup..."

# Temporarily stop Supabase services to ensure data consistency
echo "Stopping Supabase services..."
cd /path/to/your/supabase/docker-compose/directory
docker-compose down

# Backup each volume
for VOLUME in "${VOLUMES[@]}"; do
  echo "Backing up volume: ${VOLUME}"
  BACKUP_FILE="${BACKUP_DIR}/${VOLUME}_${TIMESTAMP}.tar"
  
  # Create a temporary container to access the volume and create a backup
  docker run --rm -v "${VOLUME}:/source" -v "${BACKUP_DIR}:/backup" alpine tar -cf "/backup/$(basename ${BACKUP_FILE})" -C /source .
  
  # Compress the backup
  gzip "${BACKUP_FILE}"
  echo "Volume ${VOLUME} backed up to: ${BACKUP_FILE}.gz"
done

# Restart Supabase services
echo "Restarting Supabase services..."
docker-compose up -d

# Clean up old volume backups
echo "Removing volume backups older than ${RETENTION_DAYS} days..."
find "${BACKUP_DIR}" -name "*.tar.gz" -type f -mtime +${RETENTION_DAYS} -delete

echo "Docker volume backup completed."

# Optional: Calculate total backup size
TOTAL_SIZE=$(du -sh "${BACKUP_DIR}" | cut -f1)
echo "Total volume backup size: ${TOTAL_SIZE}"

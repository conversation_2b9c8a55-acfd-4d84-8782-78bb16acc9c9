#!/bin/bash

# Monthly PAYG Billing Cron Job Script
# This script calls the Supabase Edge Function to process monthly PAYG billing

# Configuration
SUPABASE_URL="http://***************:8000"  # Your self-hosted Supabase URL
CRON_SECRET="your-secure-cron-secret-here"   # Set this to a secure random string
LOG_FILE="/var/log/voicehype/monthly-billing.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# Create log directory if it doesn't exist
mkdir -p "$(dirname "$LOG_FILE")"

# Function to log messages
log_message() {
    echo "[$DATE] $1" | tee -a "$LOG_FILE"
}

log_message "Starting monthly PAYG billing process..."

# Call the Supabase Edge Function
response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
    -X POST \
    -H "Content-Type: application/json" \
    -H "x-cron-secret: $CRON_SECRET" \
    "$SUPABASE_URL/functions/v1/process-monthly-payg" \
    -d '{}')

# Extract HTTP status and body
http_status=$(echo "$response" | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
body=$(echo "$response" | sed -e 's/HTTPSTATUS:.*//g')

log_message "HTTP Status: $http_status"
log_message "Response: $body"

# Check if the request was successful
if [ "$http_status" -eq 200 ]; then
    log_message "Monthly PAYG billing completed successfully"
    
    # Parse the response to get processing stats
    processed=$(echo "$body" | grep -o '"processed":[0-9]*' | cut -d':' -f2)
    successful=$(echo "$body" | grep -o '"successful":[0-9]*' | cut -d':' -f2)
    errors=$(echo "$body" | grep -o '"errors":[0-9]*' | cut -d':' -f2)
    
    if [ ! -z "$processed" ]; then
        log_message "Processed: $processed users, Successful: $successful, Errors: $errors"
    fi
    
    exit 0
else
    log_message "ERROR: Monthly PAYG billing failed with HTTP status $http_status"
    log_message "Response body: $body"
    
    # Send alert (you can customize this)
    # Example: send email, Slack notification, etc.
    
    exit 1
fi

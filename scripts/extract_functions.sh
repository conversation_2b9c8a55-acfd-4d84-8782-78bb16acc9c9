#!/bin/bash
# Script to extract just functions from a Supabase database
# This creates a plain SQL dump containing only database functions

# Configure these variables
POSTGRES_CONTAINER="supabase-db"
OUTPUT_FILE="supabase_functions_$(date +%Y-%m-%d).sql"

echo "Extracting only functions from the database..."

# Create the function-only dump directly
docker exec $POSTGRES_CONTAINER pg_dump -U postgres -d postgres \
  --schema-only \
  --section=pre-data \
  --section=post-data \
  | grep -A 1000000 -P "^--\n-- Name: .*; Type: FUNCTION" \
  | grep -B 1000000 -P "^--\n-- PostgreSQL database dump complete" \
  > $OUTPUT_FILE

echo "Function definitions saved to $OUTPUT_FILE"
echo "File size: $(du -h $OUTPUT_FILE | cut -f1)"

# Optional: Display a list of the functions
echo "Functions extracted:"
grep -P "^CREATE OR REPLACE FUNCTION" $OUTPUT_FILE | sed 's/CREATE OR REPLACE FUNCTION //g' | sed 's/(.*//g' | sort

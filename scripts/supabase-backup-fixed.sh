#!/bin/bash
# Supabase PostgreSQL Backup Script for your specific setup
# Creates a daily backup of your Supabase PostgreSQL database

# Configuration
BACKUP_DIR="/root/supabase-backups"
DATE=$(date +%Y-%m-%d)
TIMESTAMP=$(date +%Y-%m-%d_%H-%M-%S)
POSTGRES_CONTAINER="supabase-db"  # Your specific container name
BACKUP_FILENAME="supabase_db_${TIMESTAMP}"
RETENTION_DAYS=14  # How many days to keep backups

# Create backup directory if it doesn't exist
mkdir -p "${BACKUP_DIR}"

echo "Verifying connection to PostgreSQL container..."
if ! docker exec "${POSTGRES_CONTAINER}" psql -U postgres -c "SELECT version();" > /dev/null 2>&1; then
    echo "ERROR: Cannot connect to PostgreSQL. Check container name and database credentials."
    exit 1
fi

echo "Creating backup of postgres database in custom format..."
docker exec "${POSTGRES_CONTAINER}" pg_dump -U postgres -d postgres \
  --clean --if-exists --no-owner --no-comments --disable-triggers \
  --format=custom -f /tmp/supabase_dump.backup

# Copy the backup file from the container to the host
docker cp "${POSTGRES_CONTAINER}:/tmp/supabase_dump.backup" "${BACKUP_DIR}/${BACKUP_FILENAME}.backup"

# Compress the backup
gzip "${BACKUP_DIR}/${BACKUP_FILENAME}.backup"
echo "Database backup created: ${BACKUP_DIR}/${BACKUP_FILENAME}.backup.gz"

# Check if the backup was successful
if [ $? -eq 0 ]; then
    echo "Backup completed successfully"
    
    # Optional: Cleanup old backups
    find "${BACKUP_DIR}" -name "supabase_db_*.backup.gz" -type f -mtime +${RETENTION_DAYS} -delete
    echo "Removed backups older than ${RETENTION_DAYS} days"
    
    # Optional: Calculate backup size
    BACKUP_SIZE=$(du -h "${BACKUP_DIR}/${BACKUP_FILENAME}.backup.gz" | cut -f1)
    echo "Backup size: ${BACKUP_SIZE}"
else
    echo "Backup failed"
    exit 1
fi

# Create a backup of the Docker Compose files and environment variables
if [ -d "/root/supabase-project" ]; then
    COMPOSE_BACKUP="${BACKUP_DIR}/supabase_compose_${TIMESTAMP}.tar.gz"
    tar -czf "${COMPOSE_BACKUP}" -C "/root/supabase-project" .
    echo "Docker Compose configuration backed up to: ${COMPOSE_BACKUP}"
else
    echo "Warning: Docker Compose directory not found at /root/supabase-project"
    echo "Please update the script with the correct path to your Supabase Docker Compose files"
fi

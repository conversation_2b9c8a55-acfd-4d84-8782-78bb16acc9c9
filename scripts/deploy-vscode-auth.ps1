# VS Code Extension Authentication Deployment Script (PowerShell)
# This script deploys the authentication edge functions and verifies the setup

param(
    [switch]$Force = $false
)

# Colors for output
$Green = "Green"
$Yellow = "Yellow"
$Red = "Red"

function Write-Status {
    param($Message)
    Write-Host "✓ $Message" -ForegroundColor $Green
}

function Write-Warning {
    param($Message)
    Write-Host "⚠ $Message" -ForegroundColor $Yellow
}

function Write-Error {
    param($Message)
    Write-Host "✗ $Message" -ForegroundColor $Red
}

Write-Host "🚀 Starting VS Code Authentication Deployment..." -ForegroundColor Cyan

# Check if we're in the right directory
if (!(Test-Path "supabase\config.toml")) {
    Write-Error "Please run this script from the project root directory (where supabase\config.toml exists)"
    exit 1
}

Write-Status "Found Supabase configuration"

# Check if Supabase CLI is installed
try {
    $null = Get-Command supabase -ErrorAction Stop
    Write-Status "Supabase CLI is installed"
} catch {
    Write-Error "Supabase CLI is not installed. Please install it first:"
    Write-Host "npm install -g supabase"
    exit 1
}

# Check if logged in to Supabase
try {
    $null = supabase projects list 2>$null
    Write-Status "Authenticated with Supabase"
} catch {
    Write-Error "Not logged in to Supabase. Please run: supabase login"
    exit 1
}

Write-Host ""
Write-Host "📊 Checking current project status..." -ForegroundColor Cyan

# Get project info
$configContent = Get-Content "supabase\config.toml"
$projectLine = $configContent | Where-Object { $_ -match 'project_id\s*=\s*"([^"]+)"' }
if ($projectLine -and $matches) {
    $PROJECT_REF = $matches[1]
    Write-Status "Project ID: $PROJECT_REF"
} else {
    Write-Error "Could not find project_id in supabase\config.toml"
    exit 1
}

Write-Host ""
Write-Host "🗄️ Applying database migrations..." -ForegroundColor Cyan

# Apply migrations
try {
    supabase db push
    if ($LASTEXITCODE -eq 0) {
        Write-Status "Database migrations applied successfully"
    } else {
        throw "Migration failed"
    }
} catch {
    Write-Error "Failed to apply database migrations"
    exit 1
}

Write-Host ""
Write-Host "🔧 Deploying edge function..." -ForegroundColor Cyan

# Deploy vscode-extension-auth function
Write-Host "Deploying vscode-extension-auth function..."
try {
    supabase functions deploy vscode-extension-auth
    if ($LASTEXITCODE -eq 0) {
        Write-Status "vscode-extension-auth function deployed"
    } else {
        throw "Deployment failed"
    }
} catch {
    Write-Error "Failed to deploy vscode-extension-auth function"
    exit 1
}

Write-Host ""
Write-Host "🔐 Checking environment variables..." -ForegroundColor Cyan

# Check if required secrets are set
$SUPABASE_URL = "https://$PROJECT_REF.supabase.co"
Write-Host "SUPABASE_URL should be: $SUPABASE_URL"

Write-Warning "Please verify these environment variables are set in your Supabase project:"
Write-Host "  - SUPABASE_URL: $SUPABASE_URL"
Write-Host "  - SUPABASE_SERVICE_ROLE_KEY: (your service role key)"
Write-Host ""
Write-Host "You can set them with:"
Write-Host "  supabase secrets set SUPABASE_URL=$SUPABASE_URL"
Write-Host "  supabase secrets set SUPABASE_SERVICE_ROLE_KEY=your-service-role-key"

Write-Host ""
Write-Host "🔍 Verifying deployment..." -ForegroundColor Cyan

# List deployed functions
Write-Host "Currently deployed functions:"
supabase functions list

Write-Host ""
Write-Host "🎯 Testing function endpoints..." -ForegroundColor Cyan

# Test if functions are accessible (basic connectivity)
$AUTH_URL = "https://$PROJECT_REF.supabase.co/functions/v1/vscode-extension-auth"
$WEBSITE_URL = "https://voicehype.ai/vscode-auth"

Write-Host "Edge function URL: $AUTH_URL"
Write-Host "Website auth URL: $WEBSITE_URL"

Write-Host ""
Write-Host "✅ Deployment completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next steps:" -ForegroundColor Cyan
Write-Host "1. Deploy the updated VoiceHype website with the new /vscode-auth route"
Write-Host "2. Test the authentication flow in the VS Code extension"
Write-Host "3. Check function logs if there are any issues:"
Write-Host "   supabase functions logs vscode-extension-auth"
Write-Host ""
Write-Host "📖 For detailed setup instructions, see:" -ForegroundColor Cyan
Write-Host "   extension\docs\vscode-auth-deployment-guide.md"

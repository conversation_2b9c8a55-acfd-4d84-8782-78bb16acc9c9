#!/bin/bash
# Script to test the Paddle API directly

# Load environment variables from .env.local if it exists
if [ -f supabase/functions/.env.local ]; then
  source <(grep -v '^#' supabase/functions/.env.local | sed -E 's/^([^=]+)=(.*)$/export \1="\2"/')
  echo "Loaded environment variables from .env.local"
else
  echo "Warning: .env.local not found. Please set PADDLE_API_KEY manually."
fi

# Check if PADDLE_API_KEY is set
if [ -z "$PADDLE_API_KEY" ]; then
  echo "Error: PADDLE_API_KEY environment variable not set"
  exit 1
fi

# Default values
API_ENDPOINT="https://sandbox-api.paddle.com/transactions"
HTTP_METHOD="POST"

# Parse command line options
while [[ $# -gt 0 ]]; do
  key="$1"
  case $key in
    -e|--endpoint)
      API_ENDPOINT="$2"
      shift
      shift
      ;;
    -m|--method)
      HTTP_METHOD="$2"
      shift
      shift
      ;;
    -p|--payload)
      PAYLOAD_FILE="$2"
      shift
      shift
      ;;
    *)
      echo "Unknown option: $1"
      exit 1
      ;;
  esac
done

# Check if this is a GET request
if [[ "$HTTP_METHOD" == "GET" ]]; then
  echo "Sending GET request to: $API_ENDPOINT"
  curl -X GET "$API_ENDPOINT" \
    -H "Authorization: Bearer $PADDLE_API_KEY" \
    -H "Content-Type: application/json" \
    -v
  exit 0
fi

# For POST/PUT/PATCH requests, check if payload file is provided
if [ -z "$PAYLOAD_FILE" ]; then
  # Default payload
  echo "No payload file specified, using default payload"
  
  # Create a temporary file
  PAYLOAD_FILE=$(mktemp)
  
  # Write default payload to temporary file
  cat > "$PAYLOAD_FILE" << EOF
{
  "items": [
    {
      "price_id": "pri_01jw5t0fkatcmphb77m0t32zmj",
      "quantity": 1
    }
  ],
  "customer_id": "ctm_example",
  "currency_code": "USD",
  "collection_mode": "automatic"
}
EOF
fi

# Check if payload file exists
if [ ! -f "$PAYLOAD_FILE" ]; then
  echo "Error: Payload file not found: $PAYLOAD_FILE"
  exit 1
fi

echo "Sending $HTTP_METHOD request to: $API_ENDPOINT"
echo "Using payload from file: $PAYLOAD_FILE"
echo "Payload content:"
cat "$PAYLOAD_FILE"
echo

# Send the request
curl -X "$HTTP_METHOD" "$API_ENDPOINT" \
  -H "Authorization: Bearer $PADDLE_API_KEY" \
  -H "Content-Type: application/json" \
  -d @"$PAYLOAD_FILE" \
  -v

# Clean up temporary file if we created one
if [[ "$PAYLOAD_FILE" == /tmp/* ]]; then
  rm "$PAYLOAD_FILE"
fi

{"name": "test-pvrecorder", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "test-pvrecorder", "version": "1.0.0", "license": "ISC", "dependencies": {"@picovoice/pvrecorder-node": "^1.2.8", "wavefile": "^11.0.0"}}, "node_modules/@picovoice/pvrecorder-node": {"version": "1.2.8", "resolved": "https://registry.npmjs.org/@picovoice/pvrecorder-node/-/pvrecorder-node-1.2.8.tgz", "integrity": "sha512-dbLJlplQQNRkM2ja/hP4sRADGDILuJ54dEf8cU5eULeNrddxXvOtE8IiJ5F2VhhbXmIv3Qmn79DqhttCOxjH8Q==", "license": "Apache-2.0", "engines": {"node": ">=18.0.0"}}, "node_modules/wavefile": {"version": "11.0.0", "resolved": "https://registry.npmjs.org/wavefile/-/wavefile-11.0.0.tgz", "integrity": "sha512-/OBiAALgWU24IG7sC84cDO/KfFuvajWc5Uec0oV2zrpOOZZDgGdOwHwgEzOrwh8jkubBk7PtZfQBIcI1OaE5Ng==", "license": "MIT", "bin": {"wavefile": "bin/wavefile.js"}, "engines": {"node": ">=8"}}}}
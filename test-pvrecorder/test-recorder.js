const { PvRecorder } = require('@picovoice/pvrecorder-node');
const fs = require('fs');
const path = require('path');
const wavefile = require('wavefile');

// Configuration
const FRAME_LENGTH = 512;
const SAMPLE_RATE = 16000;
const RECORDING_SECONDS = 5;
const OUTPUT_FILE = path.join(__dirname, 'pvrecorder-test.wav');

// Function to convert Int16Array to Buffer
function int16ArrayToBuffer(array) {
    return Buffer.from(new Uint8Array(array.buffer));
}

// Function to write WAV header
function createWavFile(audioData, sampleRate, channels = 1) {
    const wav = new wavefile.WaveFile();
    wav.fromScratch(channels, sampleRate, '16', audioData);
    return Buffer.from(wav.toBuffer());
}

async function main() {
    console.log('=== PvRecorder Test ===');
    
    try {
        // List available devices
        const devices = PvRecorder.getAvailableDevices();
        console.log('Available audio devices:');
        devices.forEach((device, index) => {
            console.log(`[${index}] ${device}`);
        });
        
        // Default to device index 1 (which is typically the microphone, index 0 is often monitor)
        const deviceIndex = 1;
        console.log(`\nUsing device: ${deviceIndex === -1 ? 'Default' : devices[deviceIndex]}`);
        
        // Create recorder
        const recorder = new PvRecorder(FRAME_LENGTH, deviceIndex);
        console.log(`Sample rate: ${recorder.sampleRate} Hz`);
        console.log(`Frame length: ${FRAME_LENGTH} samples`);
        console.log(`Recording for ${RECORDING_SECONDS} seconds...`);
        
        // Prepare recording data
        const totalFrames = Math.ceil((RECORDING_SECONDS * recorder.sampleRate) / FRAME_LENGTH);
        const recordedChunks = [];
        
        // Start recording
        recorder.start();
        console.log('Recording started...');
        
        // Read frames
        for (let i = 0; i < totalFrames; i++) {
            const frame = await recorder.read();
            recordedChunks.push(int16ArrayToBuffer(frame));
            
            // Simple progress indicator
            if (i % 20 === 0) {
                process.stdout.write('.');
            }
        }
        
        // Stop recording
        recorder.stop();
        recorder.release();
        console.log('\nRecording stopped.');
        
        // Combine all audio chunks
        const audioBuffer = Buffer.concat(recordedChunks);
        
        // Create and save WAV file
        const wavBuffer = createWavFile(audioBuffer, SAMPLE_RATE);
        fs.writeFileSync(OUTPUT_FILE, wavBuffer);
        
        // Get file stats
        const stats = fs.statSync(OUTPUT_FILE);
        console.log(`\nRecording saved to: ${OUTPUT_FILE}`);
        console.log(`File size: ${stats.size} bytes`);
        
        console.log('\nTest completed successfully!');
    } catch (error) {
        console.error('Error during recording test:', error);
    }
}

main();

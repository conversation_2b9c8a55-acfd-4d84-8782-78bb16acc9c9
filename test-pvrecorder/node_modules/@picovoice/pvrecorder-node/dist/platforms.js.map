{"version": 3, "file": "platforms.js", "sourceRoot": "", "sources": ["../src/platforms.ts"], "names": [], "mappings": "AAAA,EAAE;AACF,gCAAgC;AAChC,EAAE;AACF,qHAAqH;AACrH,iCAAiC;AACjC,EAAE;AACF,sHAAsH;AACtH,qHAAqH;AACrH,6EAA6E;AAC7E,EAAE;AACF,YAAY,CAAC;;;AAEb,yBAAyB;AACzB,yBAAyB;AACzB,6BAA6B;AAE7B,MAAM,YAAY,GAAG,OAAO,CAAC;AAC7B,MAAM,UAAU,GAAG,QAAQ,CAAC;AAC5B,MAAM,cAAc,GAAG,OAAO,CAAC;AAE/B,MAAM,MAAM,GAAG,KAAK,CAAC;AACrB,MAAM,MAAM,GAAG,KAAK,CAAC;AACrB,MAAM,MAAM,GAAG,OAAO,CAAC;AAEvB,MAAM,cAAc,GAAG,OAAO,CAAC;AAC/B,MAAM,YAAY,GAAG,KAAK,CAAC;AAC3B,MAAM,qBAAqB,GAAG,cAAc,CAAC;AAC7C,MAAM,gBAAgB,GAAG,SAAS,CAAC;AAEnC,MAAM,UAAU,GAAG,UAAU,CAAC;AAC9B,MAAM,kBAAkB,GAAG,YAAY,CAAC;AACxC,MAAM,kBAAkB,GAAG,YAAY,CAAC;AACxC,MAAM,kBAAkB,GAAG,YAAY,CAAC;AAExC,MAAM,wBAAwB,GAAG,IAAI,GAAG,CAAC;IACvC,YAAY;IACZ,UAAU;IACV,cAAc;CACf,CAAC,CAAC;AAEH,MAAM,mBAAmB,GAAG,SAAS,CAAC;AACtC,MAAM,sBAAsB,GAAG,IAAI,GAAG,EAAE,CAAC;AACzC,sBAAsB,CAAC,GAAG,CACxB,GAAG,UAAU,IAAI,MAAM,EAAE,EACzB,GAAG,YAAY,0BAA0B,CAC1C,CAAC;AACF,sBAAsB,CAAC,GAAG,CACxB,GAAG,UAAU,IAAI,MAAM,EAAE,EACzB,GAAG,YAAY,yBAAyB,CACzC,CAAC;AACF,sBAAsB,CAAC,GAAG,CACxB,GAAG,YAAY,IAAI,MAAM,EAAE,EAC3B,GAAG,cAAc,0BAA0B,CAC5C,CAAC;AACF,sBAAsB,CAAC,GAAG,CACxB,GAAG,YAAY,IAAI,kBAAkB,EAAE,EACvC,GAAG,qBAAqB,IAAI,kBAAkB,mBAAmB,CAClE,CAAC;AACF,sBAAsB,CAAC,GAAG,CACxB,GAAG,YAAY,IAAI,kBAAkB,GAAG,UAAU,EAAE,EACpD,GAAG,qBAAqB,IAAI,kBAAkB,GAAG,UAAU,mBAAmB,CAC/E,CAAC;AACF,sBAAsB,CAAC,GAAG,CACxB,GAAG,YAAY,IAAI,kBAAkB,EAAE,EACvC,GAAG,qBAAqB,IAAI,kBAAkB,mBAAmB,CAClE,CAAC;AACF,sBAAsB,CAAC,GAAG,CACxB,GAAG,YAAY,IAAI,kBAAkB,GAAG,UAAU,EAAE,EACpD,GAAG,qBAAqB,IAAI,kBAAkB,GAAG,UAAU,mBAAmB,CAC/E,CAAC;AACF,sBAAsB,CAAC,GAAG,CACxB,GAAG,YAAY,IAAI,kBAAkB,EAAE,EACvC,GAAG,qBAAqB,IAAI,kBAAkB,mBAAmB,CAClE,CAAC;AACF,sBAAsB,CAAC,GAAG,CACxB,GAAG,YAAY,IAAI,kBAAkB,GAAG,UAAU,EAAE,EACpD,GAAG,qBAAqB,IAAI,kBAAkB,GAAG,UAAU,mBAAmB,CAC/E,CAAC;AACF,sBAAsB,CAAC,GAAG,CACxB,GAAG,cAAc,IAAI,MAAM,EAAE,EAC7B,GAAG,gBAAgB,yBAAyB,CAC7C,CAAC;AACF,sBAAsB,CAAC,GAAG,CACxB,GAAG,cAAc,IAAI,MAAM,EAAE,EAC7B,GAAG,gBAAgB,yBAAyB,CAC7C,CAAC;AAEF,SAAS,mBAAmB,CAAC,WAAmB;IAC9C,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,mBAAmB,EAAE,WAAW,CAAC,CAAC;AACnE,CAAC;AAED,SAAS,UAAU;IACjB,MAAM,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;IAC1D,KAAK,MAAM,QAAQ,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QAC1C,IAAI,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;YACjC,MAAM,aAAa,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC1C,OAAO,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;SAC9D;KACF;IACD,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;AACtC,CAAC;AAED,SAAS,gBAAgB;IACvB,MAAM,OAAO,GAAG,UAAU,EAAE,CAAC;IAC7B,QAAQ,OAAO,EAAE;QACf,KAAK,OAAO,CAAC;QACb,KAAK,OAAO,CAAC;QACb,KAAK,OAAO;YACV,OAAO,qBAAqB,CAAC;QAC/B;YACE,MAAM,IAAI,KAAK,CAAC,qBAAqB,OAAO,GAAG,CAAC,CAAC;KACpD;AACH,CAAC;AAED,SAAS,eAAe,CAAC,IAAY;IACnC,IAAI,QAAQ,GAAG,EAAE,CAAC;IAClB,IAAI,IAAI,KAAK,MAAM,EAAE;QACnB,QAAQ,GAAG,UAAU,CAAC;KACvB;IAED,MAAM,OAAO,GAAG,UAAU,EAAE,CAAC;IAC7B,QAAQ,OAAO,EAAE;QACf,KAAK,OAAO;YACV,OAAO,kBAAkB,GAAG,QAAQ,CAAC;QACvC,KAAK,OAAO;YACV,OAAO,kBAAkB,GAAG,QAAQ,CAAC;QACvC,KAAK,OAAO;YACV,OAAO,kBAAkB,GAAG,QAAQ,CAAC;QACvC;YACE,MAAM,IAAI,KAAK,CAAC,qBAAqB,OAAO,GAAG,CAAC,CAAC;KACpD;AACH,CAAC;AAED,SAAgB,WAAW;IACzB,MAAM,MAAM,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;IAC7B,MAAM,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC;IAEvB,IAAI,MAAM,KAAK,UAAU,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,MAAM,CAAC,EAAE;QACjE,OAAO,YAAY,CAAC;KACrB;IAED,IAAI,MAAM,KAAK,cAAc,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,MAAM,CAAC,EAAE;QACrE,OAAO,gBAAgB,CAAC;KACzB;IAED,IAAI,MAAM,KAAK,YAAY,EAAE;QAC3B,IAAI,IAAI,KAAK,MAAM,EAAE;YACnB,OAAO,cAAc,CAAC;SACvB;QACD,OAAO,gBAAgB,EAAE,CAAC;KAC3B;IAED,MAAM,UAAU,MAAM,IAAI,IAAI,oCAAoC,CAAC;AACrE,CAAC;AApBD,kCAoBC;AAED,SAAgB,oBAAoB;IAClC,MAAM,MAAM,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;IAC7B,MAAM,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC;IAEvB,IAAI,wBAAwB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;QACxC,QAAQ,MAAM,EAAE;YACd,KAAK,UAAU,CAAC,CAAC;gBACf,IAAI,IAAI,KAAK,MAAM,EAAE;oBACnB,OAAO,mBAAmB,CACxB,sBAAsB,CAAC,GAAG,CAAC,GAAG,UAAU,IAAI,MAAM,EAAE,CAAC,CACtD,CAAC;iBACH;qBAAM,IAAI,IAAI,KAAK,MAAM,EAAE;oBAC1B,OAAO,mBAAmB,CACxB,sBAAsB,CAAC,GAAG,CAAC,GAAG,UAAU,IAAI,MAAM,EAAE,CAAC,CACtD,CAAC;iBACH;gBACD,MAAM;aACP;YACD,KAAK,YAAY,CAAC,CAAC;gBACjB,IAAI,IAAI,KAAK,MAAM,EAAE;oBACnB,OAAO,mBAAmB,CACxB,sBAAsB,CAAC,GAAG,CAAC,GAAG,YAAY,IAAI,MAAM,EAAE,CAAC,CACxD,CAAC;iBACH;qBAAM,IAAI,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,MAAM,EAAE;oBAC7C,MAAM,YAAY,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;oBAC3C,IAAI,YAAY,KAAK,IAAI,EAAE;wBACzB,OAAO,mBAAmB,CACxB,sBAAsB,CAAC,GAAG,CAAC,GAAG,YAAY,IAAI,YAAY,EAAE,CAAC,CAC9D,CAAC;qBACH;oBACD,MAAM,IAAI,KAAK,CACb,UAAU,MAAM,IAAI,IAAI,iDAAiD,CAC1E,CAAC;iBACH;gBACD,MAAM;aACP;YACD,KAAK,cAAc,CAAC,CAAC;gBACnB,IAAI,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,MAAM,EAAE;oBACtC,OAAO,mBAAmB,CACxB,sBAAsB,CAAC,GAAG,CAAC,GAAG,cAAc,IAAI,IAAI,EAAE,CAAC,CACxD,CAAC;iBACH;gBACD,MAAM;aACP;YACD,OAAO,CAAC,CAAC;gBACP,MAAM,IAAI,KAAK,CACb,UAAU,MAAM,IAAI,IAAI,oCAAoC,CAC7D,CAAC;aACH;SACF;KACF;IAED,MAAM,IAAI,KAAK,CACb,UAAU,MAAM,IAAI,IAAI,oCAAoC,CAC7D,CAAC;AACJ,CAAC;AAvDD,oDAuDC"}
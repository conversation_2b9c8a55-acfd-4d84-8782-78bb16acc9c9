//
// Copyright 2021-2022 Picovoice Inc.
//
// You may not use this file except in compliance with the license. A copy of the license is located in the "LICENSE"
// file accompanying this source.
//
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.
//
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PvRecorder = void 0;
const pv_recorder_1 = require("./pv_recorder");
exports.PvRecorder = pv_recorder_1.default;
//# sourceMappingURL=index.js.map
{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../src/errors.ts"], "names": [], "mappings": "AAAA,EAAE;AACF,qCAAqC;AACrC,EAAE;AACF,qHAAqH;AACrH,iCAAiC;AACjC,EAAE;AACF,sHAAsH;AACtH,qHAAqH;AACrH,6EAA6E;AAC7E,EAAE;AACF,YAAY,CAAC;;AAEb,iEAAsD;AAEtD,MAAM,gCAAiC,SAAQ,KAAK;CAAG;AACvD,MAAM,oCAAqC,SAAQ,KAAK;CAAG;AAC3D,MAAM,iCAAkC,SAAQ,KAAK;CAAG;AACxD,MAAM,4BAA6B,SAAQ,KAAK;CAAG;AACnD,MAAM,6CAA8C,SAAQ,KAAK;CAAG;AACpE,MAAM,yCAA0C,SAAQ,KAAK;CAAG;AAChE,MAAM,uBAAwB,SAAQ,KAAK;CAAG;AAC9C,MAAM,4BAA6B,SAAQ,KAAK;CAAG;AAEnD,SAAS,2BAA2B,CAAC,MAAwB,EAAE,YAAoB;IACjF,QAAQ,MAAM,EAAE;QACd,KAAK,8BAAgB,CAAC,aAAa;YACjC,OAAO,IAAI,gCAAgC,CAAC,YAAY,CAAC,CAAC;QAC5D,KAAK,8BAAgB,CAAC,gBAAgB;YACpC,OAAO,IAAI,oCAAoC,CAAC,YAAY,CAAC,CAAC;QAChE,KAAK,8BAAgB,CAAC,aAAa;YACjC,OAAO,IAAI,iCAAiC,CAAC,YAAY,CAAC,CAAC;QAC7D,KAAK,8BAAgB,CAAC,aAAa;YACjC,OAAO,IAAI,4BAA4B,CAAC,YAAY,CAAC,CAAC;QACxD,KAAK,8BAAgB,CAAC,0BAA0B;YAC9C,OAAO,IAAI,6CAA6C,CAAC,YAAY,CAAC,CAAC;QACzE,KAAK,8BAAgB,CAAC,sBAAsB;YAC1C,OAAO,IAAI,yCAAyC,CAAC,YAAY,CAAC,CAAC;QACrE,KAAK,8BAAgB,CAAC,QAAQ;YAC5B,OAAO,IAAI,uBAAuB,CAAC,YAAY,CAAC,CAAC;QACnD,KAAK,8BAAgB,CAAC,aAAa;YACjC,OAAO,IAAI,4BAA4B,CAAC,YAAY,CAAC,CAAC;QACxD;YACE,2BAA2B;YAC3B,OAAO,CAAC,IAAI,CAAC,uBAAuB,MAAM,EAAE,CAAC,CAAC;YAC9C,OAAO,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;KAClC;AACH,CAAC;AAED,kBAAe,2BAA2B,CAAC"}
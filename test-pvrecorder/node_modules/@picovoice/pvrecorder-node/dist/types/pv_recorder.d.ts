/**
 * PvRecorder class for recording audio.
 */
declare class PvRecorder {
    private static _pvRecorder;
    private readonly _handle;
    private readonly _frameLength;
    private readonly _sampleRate;
    private readonly _version;
    /**
     * PvRecorder constructor.
     *
     * @param frameLength Length of the audio frames to receive per read call.
     * @param deviceIndex The audio device index to use to record audio. A value of (-1) will use machine's default audio device.
     * @param bufferedFramesCount The number of audio frames buffered internally for reading - i.e. internal circular buffer
     * will be of size `frameLength` * `bufferedFramesCount`. If this value is too low, buffer overflows could occur
     * and audio frames could be dropped. A higher value will increase memory usage.
     */
    constructor(frameLength: number, deviceIndex?: number, bufferedFramesCount?: number);
    /**
     * @returns Length of the audio frames to receive per read call.
     */
    get frameLength(): number;
    /**
     * @returns Audio sample rate used by PvRecorder.
     */
    get sampleRate(): number;
    /**
     * @returns the version of the PvRecorder
     */
    get version(): string;
    /**
     * @returns Whether PvRecorder is currently recording audio or not.
     */
    get isRecording(): boolean;
    /**
     * Starts recording audio.
     */
    start(): void;
    /**
     * Stops recording audio.
     */
    stop(): void;
    /**
     * Asynchronous call to read a frame of audio data.
     *
     * @returns {Promise<Int16Array>} Audio data frame.
     */
    read(): Promise<Int16Array>;
    /**
     * Synchronous call to read a frame of audio data.
     *
     * @returns {Int16Array} Audio data frame.
     */
    readSync(): Int16Array;
    /**
     * Enable or disable debug logging for PvRecorder. Debug logs will indicate when there are overflows in the internal
     * frame buffer and when an audio source is generating frames of silence.
     *
     * @param isDebugLoggingEnabled Boolean indicating whether the debug logging is enabled or disabled.
     */
    setDebugLogging(isDebugLoggingEnabled: boolean): void;
    /**
     * Returns the name of the selected device used to capture audio.
     *
     * @returns {string} Name of the selected audio device.
     */
    getSelectedDevice(): string;
    /**
     * Destructor. Releases resources acquired by PvRecorder.
     */
    release(): void;
    /**
     * Helper function to get the list of available audio devices.
     *
     * @returns {Array<string>} An array of the available device names.
     */
    static getAvailableDevices(): string[];
}
export default PvRecorder;
//# sourceMappingURL=pv_recorder.d.ts.map
{"name": "@picovoice/pvrecorder-node", "version": "1.2.8", "description": "Audio recorder sdk for Nodejs.", "main": "dist/index.js", "types": "dist/types", "keywords": ["audio, audio recorder"], "author": "Picovoice Inc.", "license": "Apache-2.0", "repository": {"type": "git", "url": "https://github.com/Picovoice/pvrecorder.git", "directory": "binding/nodejs"}, "scripts": {"build": "npm-run-all --parallel build:**", "build:all": "tsc", "build:types": "tsc --declaration --declarationMap --emitDeclarationOnly --outDir ./dist/types", "prepack": "npm run build", "prepare": "node copy.js", "test": "jest --no-cache", "lint": "eslint . --ext .js,.ts"}, "devDependencies": {"@types/jest": "^27.4.1", "@types/node": "^17.0.21", "@typescript-eslint/eslint-plugin": "^5.19.0", "@typescript-eslint/parser": "^5.19.0", "eslint": "^8.13.0", "eslint-plugin-jest": "^27.1.6", "jest": "^27.5.1", "mkdirp": "^1.0.4", "ncp": "^2.0.0", "npm-run-all": "^4.1.5", "prettier": "^2.6.2", "ts-jest": "^27.1.3", "typescript": "^4.6.2"}, "engines": {"node": ">=18.0.0"}}
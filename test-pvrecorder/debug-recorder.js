const fs = require('fs');
const path = require('path');

// Try to load the PvRecorder module and handle any errors
try {
    console.log('Trying to load @picovoice/pvrecorder-node...');
    const { PvRecorder } = require('@picovoice/pvrecorder-node');
    console.log('Successfully loaded PvRecorder module.');
    
    // Test if we can get available devices
    try {
        const devices = PvRecorder.getAvailableDevices();
        console.log('Available devices:');
        console.log(devices);
    } catch (e) {
        console.error('Error getting available devices:', e);
    }
    
    // Try to create a recorder
    try {
        console.log('Trying to create a PvRecorder instance...');
        const recorder = new PvRecorder(512, -1);
        console.log('Successfully created PvRecorder with properties:');
        console.log('- Sample rate:', recorder.sampleRate);
        console.log('- Frame length:', recorder.frameLength);
        console.log('- Version:', recorder.version);
        
        // Clean up
        recorder.release();
    } catch (e) {
        console.error('Error creating PvRecorder instance:', e);
    }
} catch (e) {
    console.error('Error loading PvRecorder module:', e);
    console.log('Node.js version:', process.version);
    console.log('Platform:', process.platform);
    console.log('Architecture:', process.arch);
    
    // Check if the module files exist
    const modulePath = path.join(__dirname, 'node_modules', '@picovoice', 'pvrecorder-node');
    console.log('Checking if module directory exists:', modulePath);
    console.log('Directory exists:', fs.existsSync(modulePath));
    
    if (fs.existsSync(modulePath)) {
        console.log('Files in module directory:');
        fs.readdirSync(modulePath).forEach(file => {
            console.log('  -', file);
        });
    }
}

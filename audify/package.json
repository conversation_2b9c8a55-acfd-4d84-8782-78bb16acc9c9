{"name": "@voicehype/audify-plus", "version": "2.0.9", "description": "Play/Stream/Record PCM audio data & Encode/Decode Opus to PCM audio data", "main": "index.js", "types": "index.d.ts", "scripts": {"docs": "typedoc index.d.ts --hideGenerator --name Audify.js", "install": "node scripts/check-binary.js"}, "repository": {"type": "git", "url": "git+https://github.com/kq1231/audify-plus.git"}, "keywords": ["play", "stream", "pcm", "encode", "decode", "opus", "rtaudio", "alsa", "jack", "pulseaudio", "oss", "<PERSON><PERSON><PERSON>", "asio", "<PERSON><PERSON>i", "record", "audio io", "microphone", "input", "output", "speaker", "sound", "audio", "voip", "headphones"], "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "homepage": "https://github.com/kq1231/audify-plus#readme", "dependencies": {"node-abi": "^3.62.0"}, "devDependencies": {"@types/node": "^20.12.7", "typedoc": "^0.25.13", "typescript": "^5.4.5"}, "binary": {"napi_versions": [5, 6, 7, 8, 9]}}
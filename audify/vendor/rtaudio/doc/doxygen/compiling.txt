/*! \page compiling Debugging & Compiling

\section debug Debugging

If you are having problems getting RtAudio to run on your system, make sure to pass a value of \e true to the RtAudio::showWarnings() function (this is the default setting).  A variety of warning messages will be displayed that may help in determining the problem.  Also, try using the programs included in the <tt>tests</tt> directory.  The program <tt>audioprobe</tt> displays the queried capabilities of all hardware devices found for all APIs compiled.  When using the ALSA and JACK APIs, further information can be displayed by defining the preprocessor definition __RTAUDIO_DEBUG__.

\section compile Compiling

In order to compile RtAudio for a specific OS and audio API, it is necessary to supply the appropriate preprocessor definition and library within the compiler statement:
<P>

<TABLE BORDER=2 COLS=5 WIDTH="100%">
<TR BGCOLOR="beige">
  <TD WIDTH="5%"><B>OS:</B></TD>
  <TD WIDTH="5%"><B>Audio API:</B></TD>
  <TD WIDTH="5%"><B>C++ Class:</B></TD>
  <TD WIDTH="5%"><B>Preprocessor Definition:</B></TD>
  <TD WIDTH="5%"><B>Library or Framework:</B></TD>
  <TD><B>Example Compiler Statement:</B></TD>
</TR>
<TR>
  <TD>Linux</TD>
  <TD>ALSA</TD>
  <TD>RtApiAlsa</TD>
  <TD>__LINUX_ALSA__</TD>
  <TD><TT>asound, pthread</TT></TD>
  <TD><TT>g++ -Wall -D__LINUX_ALSA__ -o audioprobe audioprobe.cpp RtAudio.cpp -lasound -lpthread</TT></TD>
</TR>
<TR>
  <TD>Linux</TD>
  <TD>PulseAudio</TD>
  <TD>RtApiPulse</TD>
  <TD>__LINUX_PULSE__</TD>
  <TD><TT>pthread</TT></TD>
  <TD><TT>g++ -Wall -D__LINUX_PULSE__ -o audioprobe audioprobe.cpp RtAudio.cpp -lpthread -lpulse-simple -lpulse</TT></TD>
</TR>
<TR>
  <TD>Linux</TD>
  <TD>OSS</TD>
  <TD>RtApiOss</TD>
  <TD>__LINUX_OSS__</TD>
  <TD><TT>pthread</TT></TD>
  <TD><TT>g++ -Wall -D__LINUX_OSS__ -o audioprobe audioprobe.cpp RtAudio.cpp -lpthread</TT></TD>
</TR>
<TR>
  <TD>Linux or Macintosh OS-X</TD>
  <TD>Jack Audio Server</TD>
  <TD>RtApiJack</TD>
  <TD>__UNIX_JACK__</TD>
  <TD><TT>jack, pthread</TT></TD>
  <TD><TT>g++ -Wall -D__UNIX_JACK__ -o audioprobe audioprobe.cpp RtAudio.cpp $(pkg-config --cflags --libs jack) -lpthread</TT></TD>
</TR>

<TR>
  <TD>Macintosh OS-X</TD>
  <TD>CoreAudio</TD>
  <TD>RtApiCore</TD>
  <TD>__MACOSX_CORE__</TD>
  <TD><TT>pthread, CoreAudio</TT></TD>
  <TD><TT>g++ -Wall -D__MACOSX_CORE__ -o audioprobe audioprobe.cpp RtAudio.cpp -framework CoreAudio -framework CoreFoundation -lpthread</TT></TD>
</TR>
<TR>
  <TD>Windows</TD>
  <TD>DirectSound</TD>
  <TD>RtApiDs</TD>
  <TD>__WINDOWS_DS__</TD>
  <TD><TT>dsound.lib (ver. 5.0 or higher), multithreaded</TT></TD>
  <TD>MinGW: <TT>g++ -Wall -D__WINDOWS_DS__ -o audioprobe audioprobe.cpp RtAudio.cpp -lole32 -lwinmm -ldsound</TT></TD>
</TR>
<TR>
  <TD>Windows</TD>
  <TD>ASIO</TD>
  <TD>RtApiAsio</TD>
  <TD>__WINDOWS_ASIO__</TD>
  <TD><I>various ASIO header and source files</I></TD>
  <TD>MinGW: <TT>g++ -Wall -D__WINDOWS_ASIO__ -Iinclude -o audioprobe audioprobe.cpp RtAudio.cpp asio.cpp asiolist.cpp asiodrivers.cpp iasiothiscallresolver.cpp -lole32</TT></TD>
</TR>
<TR>
  <TD>Windows</TD>
  <TD>WASAPI</TD>
  <TD>RtApiWasapi</TD>
  <TD>__WINDOWS_WASAPI__</TD>
  <TD>MinGW: <TT>FunctionDiscoveryKeys_devpkey.h, lksuser, lmfplat, lmfuuid, lwmcodecdspuuid, lwinmm, lole32</TT></TD>
  <TD>MinGW: <TT>g++ -Wall -D__WINDOWS_WASAPI__ -Iinclude -o audioprobe audioprobe.cpp RtAudio.cpp -lole32 -lwinmm -lksuser -lmfplat -lmfuuid -lwmcodecdspuuid</TT></TD>
</TR>
</TABLE>
<P>

The example compiler statements above could be used to compile the <TT>audioprobe.cpp</TT> example file, assuming that <TT>audioprobe.cpp</TT>, <TT>RtAudio.h</TT>, <TT>RtAudio.cpp</TT> and any other necessary files all exist in the same directory or the include directory.


*/

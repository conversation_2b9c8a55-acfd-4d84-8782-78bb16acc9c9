/*! \page probe Probing Device Capabilities

A programmer may wish to query the available audio device capabilities before deciding which to use.  The following example outlines how this can be done. RtAudio creates its own device IDs for each device and these values will remain valid for a given instance of the class, no matter whether new devices are plugged in or previously available devices are removed. 

\code

// audioprobe.cpp

#include <iostream>
#include "RtAudio.h"

int main()
{
  RtAudio audio;

  // Get the list of device IDs
  std::vector< unsigned int > ids = audio.getDeviceIds();
  if ( ids.size() == 0 ) {
    std::cout << "No devices found." << std::endl;
    return 0;
  }

  // Scan through devices for various capabilities
  RtAudio::DeviceInfo info;
  for ( unsigned int n=0; n<ids.size(); n++ ) {

    info = audio.getDeviceInfo( ids[n] );

    // Print, for example, the name and maximum number of output channels for each device
      std::cout << "device name = " << info.name << std::endl;
      std::cout << ": maximum output channels = " << info.outputChannels << std::endl;
    }
  }

  return 0;
}
\endcode

The RtAudio::DeviceInfo structure is defined in RtAudio.h and provides a variety of information useful in assessing the capabilities of a device:

\code
  typedef struct RtAudio::DeviceInfo {
    unsigned int ID;                       // Device ID used to specify a device to RtAudio.
    std::string name;                      // Character string device name.
    unsigned int outputChannels;           // Maximum output channels supported by device.
    unsigned int inputChannels;            // Maximum input channels supported by device.
    unsigned int duplexChannels;           // Maximum simultaneous input/output channels supported by device.
    bool isDefaultOutput;                  // true if this is the default output device.
    bool isDefaultInput;                   // true if this is the default input device.
    std::vector<unsigned int> sampleRates; // Supported sample rates.
    unsigned int preferredSampleRate;      // Preferred sample rate, e.g. for WASAPI the system sample rate.
    RtAudioFormat nativeFormats;           // Bit mask of supported data formats.
  };
\endcode

The following data formats are defined and fully supported by RtAudio:

\code
  typedef unsigned long RtAudioFormat;
  static const RtAudioFormat RTAUDIO_SINT8 = 0x1;    // 8-bit signed integer.
  static const RtAudioFormat RTAUDIO_SINT16 = 0x2;   // 16-bit signed integer.
  static const RtAudioFormat RTAUDIO_SINT24 = 0x4;   // 24-bit signed integer.
  static const RtAudioFormat RTAUDIO_SINT32 = 0x8;   // 32-bit signed integer.
  static const RtAudioFormat RTAUDIO_FLOAT32 = 0x10; // Normalized between plus/minus 1.0.
  static const RtAudioFormat RTAUDIO_FLOAT64 = 0x20; // Normalized between plus/minus 1.0.
\endcode

The \c nativeFormats member of the RtAudio::DeviceInfo structure is a bit mask of the above formats which are natively supported by the device.  However, RtAudio will automatically provide format conversion if a particular format is not natively supported.  When the \c probed member of the RtAudio::DeviceInfo structure is false, the remaining structure members are undefined and the device is probably unusable.

Some audio devices may require a minimum channel value greater than one.  RtAudio will provide automatic channel number compensation when the number of channels set by the user is less than that required by the device.  Channel compensation is <I>NOT</I> possible when the number of channels set by the user is greater than that supported by the device.

Note that the device enumeration is system specific. As stated above, the device IDs should remain valid for a given device for the duration of a given RtAudio instance, as long as that device remains connected. If a device is removed and then reconnected, it will have a different ID. The user must requery the system when devices are plugged or unplugged in order to determine the updated information. As well, if a user unplugs a device while an open stream is using that device, the resulting stream behaviour will potentially vary among the different APIs. Ideally, the stream will automatically be closed in such an event.

Also, the capabilities reported by a device driver or underlying audio API are not always accurate and/or may be dependent on a combination of device settings.  For this reason, RtAudio does not rely on the queried values when attempting to open a stream.

Device names are not guaranteed to be unique. Systems often report the same device multiple times, sometimes splitting those devices according to input and output functionality. As well, systems do not necessarily provide unique names for multiple devices of the same type. However, the IDs provided by RtAudio are unique per device, as reported by the corresponding API.

*/

/*! \page errors Error Handling

RtAudio no longer makes use of C++ exceptions.  Instead, the functions RtAudio::openStream(), RtAudio::startStream(), RtAudio::stopStream(), RtAudio::abortStream()) return an RtAudioErrorType variable, which will be RTAUDIO_NO_ERROR (value = 0) when successful. Any non-zero return value from those functions indicates an error has occurred. If an RtAudioErrorCallback() function has been set, the #RtAudioErrorType and textual details will be provided through that function. One can also obtain the last error message by calling the RtAudio::getErrorText() function. For all other functions, a warning message may be produced (returned through the RtAudioErrorCallback() if set, otherwise printed to <tt>stderr</tt>) and an appropriate value is returned.  For example, if a system error occurs when processing the RtAudio::getDeviceCount() function, the return value is zero.  In such a case, the user cannot expect to make use of most other RtAudio functions because no devices are available (and thus a stream cannot be opened).  A client can call the function RtAudio::showWarnings() with a boolean argument to enable or disable the return or printing of warning messages (default value is enabled).  There is a protected RtAudio method, error(), that can be modified to globally control how these messages are handled and reported.

*/

/*! \mainpage The RtAudio Home Page

RtAudio is a set of C++ classes that provide a common API (Application Programming Interface) for realtime audio input/output across Linux, Macintosh OS-X and Windows operating systems.  RtAudio significantly simplifies the process of interacting with computer audio hardware.  It was designed with the following objectives:

- object-oriented C++ design
- simple, common API across all supported platforms
- only one source and one header file for easy inclusion in programming projects
- allow simultaneous multi-api support
- support dynamic connection of devices
- provide extensive audio device parameter control
- allow audio device capability probing
- automatic internal conversion for data format, channel number compensation, (de)interleaving, and byte-swapping

RtAudio incorporates the concept of audio streams, which represent audio output (playback) and/or input (recording).  Available audio devices and their capabilities can be enumerated and then specified when opening a stream.  Where applicable, multiple API support can be compiled and a particular API specified when creating an RtAudio instance.  See the \ref apinotes section for information specific to each of the supported audio APIs.

\section whatsnew Latest Updates (Version 6.0.1)

Changes in this release (from 6.0.0) include:

- complete rewrite of device enumeration scheme for all APIs to provide persistent IDs within a given instance (significant API change)
- discontinued use of C++ exceptions, switched to error code return values from various functions (significant API change)
- corresponding updates to test programs and documentation
- see git history for complete list of changes

\section download Download

Latest Release (1 August 2023): <A href="http://www.music.mcgill.ca/~gary/rtaudio/release/rtaudio-6.0.1.tar.gz">Version 6.0.1</A>

\section documentation Documentation Links

-# \ref errors
-# \ref probe
-# \ref settings
-# \ref playback
-# \ref recording
-# \ref duplex
-# \ref multi
-# \ref compiling
-# \ref apinotes
-# \ref acknowledge
-# \ref license
-# <A href="http://github.com/thestk/rtaudio">RtAudio on GitHub</A>

*/

-# <A href="bugs.html">Bug Tracker (out of date)</A>
-# <A href="updates.html">Possible Updates (out of date)</A>

/*! \page multi Using Simultaneous Multiple APIs

Because support for each audio API is encapsulated in a specific RtApi subclass, it is possible to compile and instantiate multiple API-specific subclasses on a given operating system.  For example, one can compile both the RtApiDs and RtApiAsio classes on Windows operating systems by providing the appropriate preprocessor definitions, include files, and libraries for each.  In a run-time situation, one might first attempt to determine whether any ASIO device drivers exist.  This can be done by specifying the api argument RtAudio::WINDOWS_ASIO when attempting to create an instance of RtAudio.  If no available devices are found, then an instance of RtAudio with the api argument RtAudio::WINDOWS_DS can be created.  Alternately, if no api argument is specified, RtAudio will first look for an ASIO instance, then a WASAPI instance and then a DirectSound instance (on Linux systems, the default API search order is Alsa, Jack, Pulse and finally OSS).  In theory, it should also be possible to have separate instances of RtAudio open at the same time with different underlying audio API support, though this has not been tested.  It is difficult to know how well different audio APIs can simultaneously coexist on a given operating system.  In particular, it is unlikely that the same device could be simultaneously controlled with two different audio APIs.

The static function RtAudio::getCompiledApi() is provided to determine the available compiled API support.  The function RtAudio::getCurrentApi() indicates the API selected for a given RtAudio instance.

*/

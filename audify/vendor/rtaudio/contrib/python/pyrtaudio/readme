PyRtAudio - a python wrapper around RtAudio that allows to perform audio i/o operations in real-time from the python language.

By <PERSON>, 2011

This software is in the development stage. Do not expect compatibility
with future versions. Comments, suggestions, new features, bug fixes,
etc. are welcome.


This distribution of PyRtAudio contains the following:

- rtaudiomodule.cpp: the python wrapper code
- setup.py: a setup script use to compile and install PyRtAudio
- examples: a single PyRtAudioTest.py script

INSTALLATION

The compilation and installation of the PyRtAudio module is handled by
the python Distribution Utilities ("Distutils"). Provided that your
system has a C++ compiler and is properly configure, the following
command should be sufficient:

>> python setup.py install

Please refer to the distutils documentation for installation problems: http://docs.python.org/distutils/index.html 

LEGAL AND ETHICAL:

The PyRtAudio license is the same as the RtAudio license:

    PyRtAudio: a python wrapper around RtAudio
    Copyright (c)2011 <PERSON>

    Permission is hereby granted, free of charge, to any person
    obtaining a copy of this software and associated documentation files
    (the "Software"), to deal in the Software without restriction,
    including without limitation the rights to use, copy, modify, merge,
    publish, distribute, sublicense, and/or sell copies of the Software,
    and to permit persons to whom the Software is furnished to do so,
    subject to the following conditions:

    The above copyright notice and this permission notice shall be
    included in all copies or substantial portions of the Software.

    Any person wishing to distribute modifications to the Software is
    asked to send the modifications to the original developer so that
    they can be incorporated into the canonical version.  This is,
    however, not a binding provision of this license.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
    EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
    MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
    IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR
    ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF
    CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
    WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.


To build this source code, simply type:

% make

If this does not work, or if you want to change the default configuration
(e.g., to compile for a fixed-point architecture), simply edit the options
in the Makefile.

An up-to-date implementation conforming to this standard is available in a
Git repository at https://gitlab.xiph.org/xiph/opus.git or on a website at:
https://opus-codec.org/
However, although that implementation is expected to remain conformant
with the standard, it is the code in this RFC that shall remain normative.
To build from the git repository instead of using this RFC, follow these
steps:

1) Clone the repository (latest implementation of this standard at the time
of publication)

% git clone https://gitlab.xiph.org/xiph/opus.git
% cd opus

2) Compile

% ./autogen.sh
% ./configure
% make

Once you have compiled the codec, there will be a opus_demo executable in
the top directory.

Usage: opus_demo [-e] <application> <sampling rate (Hz)> <channels (1/2)>
         <bits per second> [options] <input> <output>
       opus_demo -d <sampling rate (Hz)> <channels (1/2)> [options]
         <input> <output>

mode: voip | audio | restricted-lowdelay
options:
-e                   : only runs the encoder (output the bit-stream)
-d                   : only runs the decoder (reads the bit-stream as input)
-cbr                 : enable constant bitrate; default: variable bitrate
-cvbr                : enable constrained variable bitrate; default: unconstrained
-bandwidth <NB|MB|WB|SWB|FB> : audio bandwidth (from narrowband to fullband);
                               default: sampling rate
-framesize <2.5|5|10|20|40|60> : frame size in ms; default: 20
-max_payload <bytes> : maximum payload size in bytes, default: 1024
-complexity <comp>   : complexity, 0 (lowest) ... 10 (highest); default: 10
-inbandfec           : enable SILK inband FEC
-forcemono           : force mono encoding, even for stereo input
-dtx                 : enable SILK DTX
-loss <perc>         : simulate packet loss, in percent (0-100); default: 0

input and output are little endian signed 16-bit PCM files or opus bitstreams
with simple opus_demo proprietary framing.

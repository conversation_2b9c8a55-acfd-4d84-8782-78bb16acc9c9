/* Copyright (c) 2017 Google Inc.
   Written by <PERSON> */
/*
   Redistribution and use in source and binary forms, with or without
   modification, are permitted provided that the following conditions
   are met:

   - Redistributions of source code must retain the above copyright
   notice, this list of conditions and the following disclaimer.

   - Redistributions in binary form must reproduce the above copyright
   notice, this list of conditions and the following disclaimer in the
   documentation and/or other materials provided with the distribution.

   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
   ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
   LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
   A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER
   OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
   EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
   PROCUREMENT OF SUBS<PERSON><PERSON>TE GOODS OR SERVICES; LOSS OF USE, DATA, OR
   PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
   LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
   NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
   SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*/

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

#include "arch.h"
#include "float_cast.h"
#include "opus_private.h"
#include "opus_defines.h"
#include "mapping_matrix.h"

#define MATRIX_INDEX(nb_rows, row, col) (nb_rows * col + row)

opus_int32 mapping_matrix_get_size(int rows, int cols)
{
  opus_int32 size;

  /* Mapping Matrix must only support up to 255 channels in or out.
   * Additionally, the total cell count must be <= 65004 octets in order
   * for the matrix to be stored in an OGG header.
   */
  if (rows > 255 || cols > 255)
      return 0;
  size = rows * (opus_int32)cols * sizeof(opus_int16);
  if (size > 65004)
    return 0;

  return align(sizeof(MappingMatrix)) + align(size);
}

opus_int16 *mapping_matrix_get_data(const MappingMatrix *matrix)
{
  /* void* cast avoids clang -Wcast-align warning */
  return (opus_int16*)(void*)((char*)matrix + align(sizeof(MappingMatrix)));
}

void mapping_matrix_init(MappingMatrix * const matrix,
  int rows, int cols, int gain, const opus_int16 *data, opus_int32 data_size)
{
  int i;
  opus_int16 *ptr;

#if !defined(ENABLE_ASSERTIONS)
  (void)data_size;
#endif
  celt_assert(align(data_size) == align(rows * cols * sizeof(opus_int16)));

  matrix->rows = rows;
  matrix->cols = cols;
  matrix->gain = gain;
  ptr = mapping_matrix_get_data(matrix);
  for (i = 0; i < rows * cols; i++)
  {
     ptr[i] = data[i];
  }
}

#ifndef DISABLE_FLOAT_API
void mapping_matrix_multiply_channel_in_float(
    const MappingMatrix *matrix,
    const float *input,
    int input_rows,
    opus_val16 *output,
    int output_row,
    int output_rows,
    int frame_size)
{
  /* Matrix data is ordered col-wise. */
  opus_int16* matrix_data;
  int i, col;

  celt_assert(input_rows <= matrix->cols && output_rows <= matrix->rows);

  matrix_data = mapping_matrix_get_data(matrix);

  for (i = 0; i < frame_size; i++)
  {
    float tmp = 0;
    for (col = 0; col < input_rows; col++)
    {
      tmp +=
        matrix_data[MATRIX_INDEX(matrix->rows, output_row, col)] *
        input[MATRIX_INDEX(input_rows, col, i)];
    }
#if defined(FIXED_POINT)
    output[output_rows * i] = FLOAT2INT16((1/32768.f)*tmp);
#else
    output[output_rows * i] = (1/32768.f)*tmp;
#endif
  }
}

void mapping_matrix_multiply_channel_out_float(
    const MappingMatrix *matrix,
    const opus_val16 *input,
    int input_row,
    int input_rows,
    float *output,
    int output_rows,
    int frame_size
)
{
  /* Matrix data is ordered col-wise. */
  opus_int16* matrix_data;
  int i, row;
  float input_sample;

  celt_assert(input_rows <= matrix->cols && output_rows <= matrix->rows);

  matrix_data = mapping_matrix_get_data(matrix);

  for (i = 0; i < frame_size; i++)
  {
#if defined(FIXED_POINT)
    input_sample = (1/32768.f)*input[input_rows * i];
#else
    input_sample = input[input_rows * i];
#endif
    for (row = 0; row < output_rows; row++)
    {
      float tmp =
        (1/32768.f)*matrix_data[MATRIX_INDEX(matrix->rows, row, input_row)] *
        input_sample;
      output[MATRIX_INDEX(output_rows, row, i)] += tmp;
    }
  }
}
#endif /* DISABLE_FLOAT_API */

void mapping_matrix_multiply_channel_in_short(
    const MappingMatrix *matrix,
    const opus_int16 *input,
    int input_rows,
    opus_val16 *output,
    int output_row,
    int output_rows,
    int frame_size)
{
  /* Matrix data is ordered col-wise. */
  opus_int16* matrix_data;
  int i, col;

  celt_assert(input_rows <= matrix->cols && output_rows <= matrix->rows);

  matrix_data = mapping_matrix_get_data(matrix);

  for (i = 0; i < frame_size; i++)
  {
    opus_val32 tmp = 0;
    for (col = 0; col < input_rows; col++)
    {
#if defined(FIXED_POINT)
      tmp +=
        ((opus_int32)matrix_data[MATRIX_INDEX(matrix->rows, output_row, col)] *
        (opus_int32)input[MATRIX_INDEX(input_rows, col, i)]) >> 8;
#else
      tmp +=
        matrix_data[MATRIX_INDEX(matrix->rows, output_row, col)] *
        input[MATRIX_INDEX(input_rows, col, i)];
#endif
    }
#if defined(FIXED_POINT)
    output[output_rows * i] = (opus_int16)((tmp + 64) >> 7);
#else
    output[output_rows * i] = (1/(32768.f*32768.f))*tmp;
#endif
  }
}

void mapping_matrix_multiply_channel_out_short(
    const MappingMatrix *matrix,
    const opus_val16 *input,
    int input_row,
    int input_rows,
    opus_int16 *output,
    int output_rows,
    int frame_size)
{
  /* Matrix data is ordered col-wise. */
  opus_int16* matrix_data;
  int i, row;
  opus_int32 input_sample;

  celt_assert(input_rows <= matrix->cols && output_rows <= matrix->rows);

  matrix_data = mapping_matrix_get_data(matrix);

  for (i = 0; i < frame_size; i++)
  {
#if defined(FIXED_POINT)
    input_sample = (opus_int32)input[input_rows * i];
#else
    input_sample = (opus_int32)FLOAT2INT16(input[input_rows * i]);
#endif
    for (row = 0; row < output_rows; row++)
    {
      opus_int32 tmp =
        (opus_int32)matrix_data[MATRIX_INDEX(matrix->rows, row, input_row)] *
        input_sample;
      output[MATRIX_INDEX(output_rows, row, i)] += (tmp + 16384) >> 15;
    }
  }
}

const MappingMatrix mapping_matrix_foa_mixing = { 6, 6, 0 };
const opus_int16 mapping_matrix_foa_mixing_data[36] = {
     16384,      0, -16384,  23170,      0,      0,  16384,  23170,
     16384,      0,      0,      0,  16384,      0, -16384, -23170,
         0,      0,  16384, -23170,  16384,      0,      0,      0,
         0,      0,      0,      0,  32767,      0,      0,      0,
         0,      0,      0,  32767
};

const MappingMatrix mapping_matrix_soa_mixing = { 11, 11, 0 };
const opus_int16 mapping_matrix_soa_mixing_data[121] = {
     10923,   7723,  13377, -13377,  11585,   9459,   7723, -16384,
     -6689,      0,      0,  10923,   7723,  13377,  13377, -11585,
      9459,   7723,  16384,  -6689,      0,      0,  10923, -15447,
     13377,      0,      0, -18919,   7723,      0,  13377,      0,
         0,  10923,   7723, -13377, -13377,  11585,  -9459,   7723,
     16384,  -6689,      0,      0,  10923,  -7723,      0,  13377,
    -16384,      0, -15447,      0,   9459,      0,      0,  10923,
     -7723,      0, -13377,  16384,      0, -15447,      0,   9459,
         0,      0,  10923,  15447,      0,      0,      0,      0,
    -15447,      0, -18919,      0,      0,  10923,   7723, -13377,
     13377, -11585,  -9459,   7723, -16384,  -6689,      0,      0,
     10923, -15447, -13377,      0,      0,  18919,   7723,      0,
     13377,      0,      0,      0,      0,      0,      0,      0,
         0,      0,      0,      0,  32767,      0,      0,      0,
         0,      0,      0,      0,      0,      0,      0,      0,
     32767
};

const MappingMatrix mapping_matrix_toa_mixing = { 18, 18, 0 };
const opus_int16 mapping_matrix_toa_mixing_data[324] = {
      8208,      0,   -881,  14369,      0,      0,  -8192,  -4163,
     13218,      0,      0,      0,  11095,  -8836,  -6218,  14833,
         0,      0,   8208, -10161,    881,  10161, -13218,  -2944,
     -8192,   2944,      0, -10488,  -6218,   6248, -11095,  -6248,
         0, -10488,      0,      0,   8208,  10161,    881, -10161,
    -13218,   2944,  -8192,  -2944,      0,  10488,  -6218,  -6248,
    -11095,   6248,      0,  10488,      0,      0,   8176,   5566,
    -11552,   5566,   9681, -11205,   8192, -11205,      0,   4920,
    -15158,   9756,  -3334,   9756,      0,  -4920,      0,      0,
      8176,   7871,  11552,      0,      0,  15846,   8192,      0,
     -9681,  -6958,      0,  13797,   3334,      0, -15158,      0,
         0,      0,   8176,      0,  11552,   7871,      0,      0,
      8192,  15846,   9681,      0,      0,      0,   3334,  13797,
     15158,   6958,      0,      0,   8176,   5566, -11552,  -5566,
     -9681, -11205,   8192,  11205,      0,   4920,  15158,   9756,
     -3334,  -9756,      0,   4920,      0,      0,   8208,  14369,
      -881,      0,      0,  -4163,  -8192,      0, -13218, -14833,
         0,  -8836,  11095,      0,   6218,      0,      0,      0,
      8208,  10161,    881,  10161,  13218,   2944,  -8192,   2944,
         0,  10488,   6218,  -6248, -11095,  -6248,      0, -10488,
         0,      0,   8208, -14369,   -881,      0,      0,   4163,
     -8192,      0, -13218,  14833,      0,   8836,  11095,      0,
      6218,      0,      0,      0,   8208,      0,   -881, -14369,
         0,      0,  -8192,   4163,  13218,      0,      0,      0,
     11095,   8836,  -6218, -14833,      0,      0,   8176,  -5566,
    -11552,   5566,  -9681,  11205,   8192, -11205,      0,  -4920,
     15158,  -9756,  -3334,   9756,      0,  -4920,      0,      0,
      8176,      0,  11552,  -7871,      0,      0,   8192, -15846,
      9681,      0,      0,      0,   3334, -13797,  15158,  -6958,
         0,      0,   8176,  -7871,  11552,      0,      0, -15846,
      8192,      0,  -9681,   6958,      0, -13797,   3334,      0,
    -15158,      0,      0,      0,   8176,  -5566, -11552,  -5566,
      9681,  11205,   8192,  11205,      0,  -4920, -15158,  -9756,
     -3334,  -9756,      0,   4920,      0,      0,   8208, -10161,
       881, -10161,  13218,  -2944,  -8192,  -2944,      0, -10488,
      6218,   6248, -11095,   6248,      0,  10488,      0,      0,
         0,      0,      0,      0,      0,      0,      0,      0,
         0,      0,      0,      0,      0,      0,      0,      0,
     32767,      0,      0,      0,      0,      0,      0,      0,
         0,      0,      0,      0,      0,      0,      0,      0,
         0,      0,      0,  32767
};

const MappingMatrix mapping_matrix_fourthoa_mixing = { 27, 27, 0 };
const opus_int16 mapping_matrix_fourthoa_mixing_data[729] = {
      9243,      0,  16010,      0,      0,      0,  20669,      0,
         0,      0,      0,      0,  24456,      0,      0,      0,
         0,      0,      0,      0,  27731,      0,      0,      0,
         0,      0,      0,   9243,      0,  10884,  11741,      0,
         0,   3995,  17849,   9626,      0,      0,      0,  -5727,
     14399,  17315,   7625,      0,      0,      0,      0, -11747,
      2574,  18637,  15552,   5930,      0,      0,   9243, -14302,
     -2682,  -6677,  13337,   5357,  -9464,   2501, -11170,   4770,
     -5911,  11501,   5858,   5369,   4951,  17901, -19071,  -2397,
     -9281,  -9198,   7576,  -4294,   7773,  -8997,  -3399,      0,
         0,   9243,   9940,  11991,  -3705,  -5144,  16647,   7057,
     -6206,  -5941,  -2698, -10194,  16781,  -1788,  -6256, -11772,
      4935,   3912,  -6062, -13039,   9446,  -9758,  -3521, -15058,
     11089,    565,      0,      0,   9243, -15376,   3720,   2461,
     -5285,  -7989,  -8660,   1278, -16087,  15811,  -3249,  10500,
     -7757,  -1680,  -9890,  -8153,  10884,  11022,   2847,  12828,
      5137,  -2053,   8666,  -5684,  14776,      0,      0,   9243,
    -10577,  10304,  -6186,   9139, -15222,   2507,  -8902,  -5140,
      -145,  15562, -10596,  -7311,  -6197,  -8753,   8667,  -6014,
      -281,  15033,    938, -11859,    548,  -8456,  16735,  -3654,
         0,      0,   9243,   8974,   4839, -12343, -15472,   6066,
     -7501,  -8343,   5015,  15920, -12374,  -4559,  -9400,   6271,
      4011,   5191,  -9932,  14438,   4828,  -8768,   1909,  12059,
     -1565,   4707, -13711,      0,      0,   9243,  15799,   2085,
     -1534,  -3386,   4602,  -9808,   -447, -17267, -18054,  -1167,
    -13525,  -4644,   1313,  -5951,   5397,   7485,  -7056,   2584,
     -8120,   8669,    788,  13177,   2109,  18349,      0,      0,
      9243,  12371, -10036,   1597,   2760, -17341,   1848,  -2239,
    -10509,  -8474,  -4577,  11164,   7935,   1441,  17430,  -3436,
     -3713,  15936,   4184,   2647, -11730,    341, -15934,   6462,
      6581,      0,      0,   9243,  -8963,   2184,  13084, -16381,
     -2734,  -9757,   3991,   6345, -18297,  -5912,   7604,  -4849,
    -11100,   2290,  -4304, -13305,  -7488,  12338,   4805,   8505,
     -7014,  -4779,  -1761, -14597,      0,      0,   9243,   1301,
    -15498,   3799,    690,  -2816,  18718,  -8223,    889,    255,
     -1768,   4485, -19951,  13097,  -2278,    167,     78,   -740,
      3324,  -6139,  19488, -17925,   4283,   -486,     20,      0,
         0,   9243, -13470,  -6719,   5452, -10257,  12641,  -4873,
     -5116, -10595,   5856,  11389,   1502,  10876,   -608,  11765,
    -13218,  13911,  -7373,  -2070, -13679,  -4154,   5536,  -2138,
     16643,    451,      0,      0,   9243,   2455,  -3679, -15387,
     -5277,  -1261,  -8697,   7906,  16112,   8147,   3208,  -1690,
      7687,  10593,  -9796, -15852, -10884,  -5616,   2881,   2032,
      5246, -12735,  -8796,  10928,  14833,      0,      0,   9243,
     -6849,   2775, -14202,  13586,  -2655,  -9402,  -5505,  10809,
    -18013,   6231,   5444,  -6041,  11288,   4958,  -4078,  18799,
     -9368,  -9291,   4535,   7383,   9405,  -7391,  -2121,  -4336,
         0,      0,   9243,   6423,  -9040,  11548,  10359,  -8109,
      -450, -14580,   6431,  10857, -15475,   3569,   9707,   6416,
     -9607,    521,   8528, -18391,  11049,   3815, -10423,   6860,
      6860,   -883,  -4221,      0,      0,   9243,  11932,  -5968,
     -8850, -14749,  -9946,  -6026,   7377,  -4472,   5206,  14547,
     -3406,  10508,   2526,   4411,  14543,   8444,  -5822,    347,
     12347,  -1709,  -9158,    105, -16265, -12642,      0,      0,
      9243,  13044,   -150,   9282,  16910,   -274, -10332,   -194,
     -5864,   5428,   -420, -12196,    344,  -8679,    145, -18554,
    -12695,   -152, -14635,    503,  10389,    358,   5076,    522,
    -16100,      0,      0,   9243,  -8374, -13590,  -1221,   1428,
     15896,  12005,   2318,  -4793,   2590,  -3209, -20390,  -6256,
     -2974,  10766,   1202,   -876,  -6597,   5004,  19896,  -1541,
      2902, -16788,  -3062,   1340,      0,      0,   9243,   9879,
     10267,   7300,  10073,  14167,   2416,  10469,  -3094,   2899,
     17092,   9762,  -7400,   7214,  -5250,  -8238,  -3989,   5578,
     16392,  -1050, -11848,   -776,  -5034, -15850,  -5882,      0,
         0,   9243,  -4974,  -9068,  12221,  -8490,   6299,   -388,
    -15478,   8702,  -9920,  12723,  -2810,   9668,   6905, -13040,
      4325,  -9456,  16856,  -9159,  -2909, -10476,   7149,   9387,
     -7350,    233,      0,      0,   9243,   3627, -13823,  -7218,
     -3656,  -7002,  12776,  13935,   2719,   2446,   8352,   9252,
     -7676, -18413,  -6212,   -429,  -1272,  -6335, -13356,  -9510,
       295,  18926,   9934,   1112,   -382,      0,      0,   9243,
     -6383,  -9343, -11326,  10097,   8329,    223,  14780,   6114,
    -10348, -15590,  -4195,   9257,  -7445,  -9439,   -323,   7902,
     18117,  12101,  -3142, -10944,  -5577,   7327,    566,  -4133,
         0,      0,   9243,   2626,    865,  15769,   5783,    317,
    -10244,   1905,  16884,   9144,    826,  -2420,  -1972, -14536,
      2413,  16939,  12500,   1482,  -4906,   -578,  10096,  -3476,
    -14323,   2745,  16105,      0,      0,   9243,  -8975,  12086,
      5450,  -6832, -15149,   7333,   9200,  -3550,   -362, -13645,
    -15525,  -1391,   9428,  -7091,  -5442,   3105,   -820, -17685,
     -9175,  -9462,   5572,  -9191, -12325,  -2180,      0,      0,
      9243,   -114,  11576, -11058,    177,   -185,   5875, -17880,
      8539,   -198,    339,   -173,  -3411, -16698,  16336,  -6369,
       193,   -430,    408,    -75, -10806,  -7225,  19670, -13817,
      4665,      0,      0,      0,      0,      0,      0,      0,
         0,      0,      0,      0,      0,      0,      0,      0,
         0,      0,      0,      0,      0,      0,      0,      0,
         0,      0,      0,      0,  32767,      0,      0,      0,
         0,      0,      0,      0,      0,      0,      0,      0,
         0,      0,      0,      0,      0,      0,      0,      0,
         0,      0,      0,      0,      0,      0,      0,      0,
     32767
};

const MappingMatrix mapping_matrix_fifthoa_mixing = { 38, 38, 0 };
const opus_int16 mapping_matrix_fifthoa_mixing_data[1444] = {
      9243,      0,  16010,      0,      0,      0,  20669,      0,
         0,      0,      0,      0,  24456,      0,      0,      0,
         0,      0,      0,      0,  27731,      0,      0,      0,
         0,      0,      0,      0,      0,      0,  30657,      0,
         0,      0,      0,      0,      0,      0,   9243,      0,
     -7023,  14387,      0,      0,  -4369, -14112,  14455,      0,
         0,      0,  10931,   -510, -16777,  14031,      0,      0,
         0,      0,  -5118,  14286,   4343, -18465,  13374,      0,
         0,      0,      0,      0,  -6494, -12221,  11761,   8513,
    -19458,  12605,      0,      0,   9243, -14128,   5093,   5547,
    -10946, -10050,  -7197,   3945, -11790,   7142,  -9213,   6529,
     -9701,  -2563,  -9923, -14846,  16521,   6816,   2764,  14103,
      1118,  -5537,   2977, -14168,   1228,   4866,  17430,   -528,
     10639,   2641,  10437,  -1037,  11460,   1098,   1296,  15737,
         0,      0,   9243,   1128, -14775,   6062,    955,  -2329,
     16069, -12511,   2477,    579,  -2333,   3440, -14197,  18478,
     -6050,    940,    303,  -1604,   4106,  -4223,   9829, -22688,
     10647,  -2604,    334,    145,   -927,   3203,  -6017,   4507,
     -3812,  24212, -15600,   5198,  -1023,    110,      0,      0,
      9243,   1158,  12997,   9277,   1501,   2103,  10097,  16840,
      5916,   1402,   3225,   2488,   2929,  19916,  12706,   3585,
      1137,   3415,   4698,   2078,  -5442,  16634,  18511,   8731,
      2095,    850,   3061,   5733,   5225,    960, -11728,   7689,
     20588,  14659,   5642,   1187,      0,      0,   9243,  -4663,
     -3081, -15003,   9771,   2007,  -9185,   6457,  14199, -14357,
     -4976,   3554,   6625,  11434,  -7231, -11297,  17760,   8291,
     -6267,  -3368,   6712, -10837,  -9107,   6524,   6793, -19531,
    -11338,   7934,   7335,  -2205,  -9215,  -7094,  10659,   6243,
     -4337,  -1250,      0,      0,   9243, -13515,   7679,  -3831,
      7232, -14496,  -3201,  -4109, -11731,   8828,   9178,  -1901,
    -10848,   -539, -14888,   9626, -10860,  12703,   3824,  12334,
     -7104,   3496,  -6203,  13852,   5461,  -2109, -17277,   7837,
     -4714,  13901,   4097,   3940,   7647,   8546,   8688, -10986,
         0,      0,   9243,   8113,  -9860,   9657,  10943, -11174,
      1426, -13300,   1915,   8178, -17833,   6805,   8309,   8100,
     -3121,  -4742,   2683, -15111,  15688,   2358, -11590,   2807,
      2746,   8762,  -7430,  -2251,  -5481,  16370,  -4081,  -9694,
      5872, -11539,   -714,  -9492,  15177,  -6126,      0,      0,
      9243,   9933,  -9215,  -8528, -11831, -12785,    -62,  10976,
     -1811,   5593,  18018,   6100,   9455,  -5237,   2758,   8971,
      2743,  -9659, -13517,   5330, -10737,  -4576,  -2069, -15491,
     -8749,  -7226,  -5237,   9191,   -181, -12277,   2815,  10540,
       -27,  14741,  16703,   3103,      0,      0,   9243, -10067,
     -8881,  -8723,  12265,  12487,   -793,  10821,  -1762,  -6021,
    -18002,  -5072,   9912,  -4395,   2587,   9368,  -2767,  10021,
     12259,  -6468, -10113,  -5605,  -1761, -15590,  -9430,   7800,
      5092,  -8835,   2293,  12314,   1222,  10671,   -329,  13745,
     17349,   3563,      0,      0,   9243,  -6485,  12991,  -6743,
      6108, -11768,  10080, -12236,    238,  -2883,  13115, -13907,
      2900, -14460,    511,   2564,    186,  -7019,  19094, -11597,
     -5472, -12058,    744,   6243,  -2384,    930,    501, -11778,
     21214,  -5330, -11746,  -5542,    827,  10475,  -6418,   1132,
         0,      0,   9243,   3862,   5238, -14627,  -7891,   2826,
     -7015, -10701,  13900,  11410,  -6831,  -1679,  -9861,   6359,
     12032, -11660, -14041,  11199,   1713,  -3895,    657,  14749,
     -3017, -11445,   8380,  15575, -15236,   -346,   7690,   -923,
     10317,   3498, -13545,    354,   9093,  -4476,      0,      0,
      9243,  -8417,  13183,   3418,  -4018, -15498,  10685,   6294,
     -4132,   1419,  -8755, -18818,   3926,   7642,  -9001,  -3235,
      2125,   3506, -13037, -16570,  -4337,   6729, -13404,  -7991,
        59,    443,   5804,   6005, -15011,  -9060, -11044,   3679,
    -15434, -13685,    161,   1185,      0,      0,   9243,  -5288,
      6773, -13508,   9977,  -5002,  -4784, -12780,  10790, -12942,
     11168,    519, -10890,   1326,  12078,  -6274,  13780, -16427,
      2186,   5352,  -4328,  13671,   2364,  -7963,   1080, -12568,
     19336,  -6557,  -8574,   4084,   7277,  10433,  -9273,  -3178,
      1516,   3817,      0,      0,   9243,   9660,   7817,  10093,
     13619,  10548,  -2942,  11021,    597,   9663,  17594,   1736,
    -10794,   1814,    771,  -8469,   1041,  14155,   7891,  -8597,
     -7498,  -8982,    346, -12407, -11848,  -6809,   1686,   9181,
     -8306, -10247,   3538, -10706,   -364,  -8047, -19188,  -8493,
         0,      0,   9243,  -7163,  -1020,  14282, -14289,   1021,
    -10208,  -2036,  10660, -18919,   2410,   6564,   2323, -13088,
     -1798,   3365, -19498,   3619,  12022,  -1858,   9978,   3705,
     -8969,   -643,  -5794, -15523,   4123,  15113,  -3949,  -6265,
     -3596,  12490,   2946,  -2688,   1225, -14570,      0,      0,
      9243, -12187,    772, -10354,  17623,  -1314, -10262,  -1117,
     -2885,  -9937,   2249,  11267,  -1763,   9572,   -368,  16506,
     -6510,  -1438, -15014,   2402,  10157,   2041,   2458,   2389,
    -19346,  19860,  -1041,   8067,  -3704, -10931,   2743,  -9286,
       606, -13399,  -3095,   7924,      0,      0,   9243,  15545,
     -2367,  -3011,  -6538,  -5139,  -9657,    995, -16242, -15706,
      2557, -12952,   5226,   2508,   6353,  10156,  13593,   6966,
      4795,   8960,   8183,  -1735,  11914,  -4504,  14149,  11727,
     -6665,  10460,  -3962,  10145,  -7648,  -1965,  -9845,  -6764,
     -6938, -16633,      0,      0,   9243,   3098,  12983,  -8841,
     -3826,   5618,  10053, -16031,   4787,   3283,  -8209,   6632,
      2856, -18922,  10272,  -2055,  -2344,   7987, -11939,   5516,
     -5520, -15739,  14940,  -5001,    530,   1465,  -6306,  13388,
    -13243,   2513, -11772,  -7170,  16572,  -8384,   1426,    168,
         0,      0,   9243, -15767,  -2008,  -1916,   4220,   4422,
     -9846,    537, -17105,  17650,  -1400,  13589,   4481,   1651,
      5677,   6701,  -9241,  -6642,  -3252,  -7827,   8792,   -951,
     13182,  -2522,  17586, -17005,   3845, -12562,   2213, -11472,
     -6688,  -1394,  -8970,  -4769,  -7316, -11753,      0,      0,
      9243, -13344,  -3829,   7975, -14863,   7136,  -8561,  -4265,
     -7992,   -801,   9405,   8912,   7937,  -5326,   5057, -17681,
     15207,    575,   7717, -11360,   4847,   6789,   4150,  12686,
    -10050,  16730, -12063,    322, -12920,  -3313, -10267,   1980,
     -6948,   7112,   7972,   8042,      0,      0,   9243,   7791,
     -1021,  13949,  15180,  -1111, -10208,  -1989,   9348,  19199,
     -2561,  -7140,   2323, -12782,  -1577,    817,  18164,  -3673,
    -12771,   2022,   9978,   3620,  -7865,   -156,  -9155,  11924,
     -3842, -15336,   4196,   6814,  -3596,  12199,   2583,   -652,
      1936, -17637,      0,      0,   9243,  -4810, -15144,  -1958,
      1315,  10175,  17406,   4142,  -1348,    263,  -3292, -15632,
    -17046,  -6363,   3374,    605,   -227,   -748,   5997,  20334,
     14481,   8277,  -6146,  -1717,      5,     27,    712,   1542,
     -9197, -23572, -10163,  -9595,   9425,   3539,    -17,    -72,
         0,      0,   9243,  -7366,   8261,  11568, -11901,  -8499,
     -2079,  13347,   5556, -12049, -16247,  -2282, -10529,   3584,
      7585,  -1577,  -8464, -18652,  -8902,   5913,  -8688,  -9287,
      4156,  -2442,  -7089,  -2993, -14485, -13949,   5422,   8459,
      1638, -13285,  -2531,  -1826, -12132,  -9456,      0,      0,
      9243,  11716,    698, -10889, -17818,   1143, -10275,  -1062,
     -1305,  12057,  -2057, -10855,  -1595,  10088,   -150,  15043,
      2978,   1578,  15225,  -2090,  10201,   1943,   1115,   1969,
    -20211, -17636,    430,  -9826,   3391,  10572,   2485,  -9826,
       248, -12259,  -2924,  12131,      0,      0,   9243,   4361,
     -4594, -14703,  -8956,  -2798,  -7781,   9434,  13769,  12936,
      6800,  -2400,   9082,   8091, -10453, -11023, -15786, -11136,
      3285,   4153,   2658, -14002,  -5051,   9489,   7000,  17206,
     15024,  -2777,  -8491,    -42, -10626,    141,  13053,   2366,
     -6662,  -2231,      0,      0,   9243,   -752, -11933, -10646,
      1119,   1254,   6890,  17745,   7875,  -1203,  -2207,  -1251,
      2024, -17706, -15532,  -5600,   1128,   2691,   2800,    683,
     -9927,   9661,  19706,  12522,   3889,   -978,  -2789,  -3992,
     -2440,    206,  12695,   2921, -17173, -18575,  -9616,  -2657,
         0,      0,   9243,   4791, -15001,  -2887,  -1931, -10037,
     16885,   6048,  -1020,     46,   4789,  15191, -15922,  -9154,
      2530,    823,    252,   -130,  -8608, -19335,  12613,  11651,
     -4549,  -2314,   -172,   -101,   -784,    265,  12975,  21741,
     -7551, -13101,   6856,   4710,    535,    -46,      0,      0,
      9243, -12153, -10395,    754,  -1281,  17644,   2735,  -1095,
    -10274,   8359,   2200, -12593,   7083,    782,  17650,  -1573,
      1685, -16282,  -2164,   -530, -11878,     32, -17359,   3065,
      6651,  -5212,  -3628,  19365,    965,  13180,   8243,   -818,
      7746,  -3645, -14323,   1670,      0,      0,   9243,  -6961,
    -11198,   9081,  -8829,  10887,   4833, -14202,   2374,  -6524,
     16339,  -9417,   4737,  12284,  -4394,  -2691,  -2683,  13690,
    -18539,   2830, -11438,  -3692,   4985,   5648,  -4628,    514,
      6225, -18409,  12672,   5311,  11170,  -6928,  -3407,  -7595,
     10737,  -3977,      0,      0,   9243,  12099, -10405,   1294,
      2187, -17582,   2760,  -1880, -10105,  -8058,  -3760,  12583,
      7058,   1346,  17376,  -2667,  -2829,  15710,   3705,    468,
    -11880,     50, -17123,   5201,   6230,   4698,   6098, -18716,
     -1665, -13088,   8285,  -1400,   7696,  -6196, -13429,   2770,
         0,      0,   9243,   8602,  13392,   1722,   2070,  16090,
     11359,   3222,  -4960,  -2638,   4581,  20106,   5099,   4026,
    -10978,  -1778,  -1314,  -6620,   6988,  18701,  -2965,   3745,
    -16745,  -4461,   1300,    584,  -3646, -11588,   8350,  11847,
    -10050,   2372, -20010,  -7809,   3608,    887,      0,      0,
      9243,  14252,  -1958,   7026,  13986,  -3899,  -9870,  -1922,
    -10736,  -3693,  -4527, -12333,   4376,  -6080,   3475, -18537,
    -19222,   1355, -10843,   6913,   8869,   3408,   8323,   6804,
     -5141, -13648,   7800,   2649,   7171,  10505,  -6548,   5179,
     -5505,  13299,   2086,  15579,      0,      0,   9243,  11323,
      9021,  -6835, -10810,  14267,   -489,  -8613,  -5689,    639,
    -16117,   6224,  -9731,  -3757,  -8482,  10882,   7873,   1080,
    -11447,  -6791, -10388,   4099,  -6025,  18396,  -5407,  -7536,
     14714,    984,   1267, -13940,  -1889,   8416,    666,  16762,
    -10106,  -3418,      0,      0,   9243,    871,   4833,  15238,
      1855,    588,  -7508,  10287,  16162,   2857,   1481,   -443,
     -9392,  -7758,  12910,  16506,   3837,   2588,   -581,   -851,
      1928, -14879,  -5066,  14950,  16498,   4773,   3842,   -425,
     -1785,    -82,  10578,  -1435, -15554,  -2459,  16520,  16250,
         0,      0,   9243,  14762,   5967,   1673,   3450,  12303,
     -6027,   1394, -15022, -14571,   3402,  -4217, -10507,   -478,
    -14813,  -5131,  -6634, -16293,    -82, -15276,  -1705,  -1731,
       358,  -5738,  13681,  12503,  -8200,  -3023,  -3290,  -7384,
      9272,   -837,  14328,  -1064,  16913,   7915,      0,      0,
         0,      0,      0,      0,      0,      0,      0,      0,
         0,      0,      0,      0,      0,      0,      0,      0,
         0,      0,      0,      0,      0,      0,      0,      0,
         0,      0,      0,      0,      0,      0,      0,      0,
         0,      0,      0,      0,  32767,      0,      0,      0,
         0,      0,      0,      0,      0,      0,      0,      0,
         0,      0,      0,      0,      0,      0,      0,      0,
         0,      0,      0,      0,      0,      0,      0,      0,
         0,      0,      0,      0,      0,      0,      0,      0,
         0,      0,      0,  32767
};

const MappingMatrix mapping_matrix_foa_demixing = { 6, 6, 0 };
const opus_int16 mapping_matrix_foa_demixing_data[36] = {
     16384,  16384,  16384,  16384,      0,      0,      0,  23170,
         0, -23170,      0,      0, -16384,  16384, -16384,  16384,
         0,      0,  23170,      0, -23170,      0,      0,      0,
         0,      0,      0,      0,  32767,      0,      0,      0,
         0,      0,      0,  32767
};

const MappingMatrix mapping_matrix_soa_demixing = { 11, 11, 3050 };
const opus_int16 mapping_matrix_soa_demixing_data[121] = {
      2771,   2771,   2771,   2771,   2771,   2771,   2771,   2771,
      2771,      0,      0,  10033,  10033, -20066,  10033,  14189,
     14189, -28378,  10033, -20066,      0,      0,   3393,   3393,
      3393,  -3393,      0,      0,      0,  -3393,  -3393,      0,
         0, -17378,  17378,      0, -17378, -24576,  24576,      0,
     17378,      0,      0,      0, -14189,  14189,      0, -14189,
    -28378,  28378,      0,  14189,      0,      0,      0,   2399,
      2399,  -4799,  -2399,      0,      0,      0,  -2399,   4799,
         0,      0,   1959,   1959,   1959,   1959,  -3918,  -3918,
     -3918,   1959,   1959,      0,      0,  -4156,   4156,      0,
      4156,      0,      0,      0,  -4156,      0,      0,      0,
      8192,   8192, -16384,   8192,  16384,  16384, -32768,   8192,
    -16384,      0,      0,      0,      0,      0,      0,      0,
         0,      0,      0,      0,   8312,      0,      0,      0,
         0,      0,      0,      0,      0,      0,      0,      0,
      8312
};

const MappingMatrix mapping_matrix_toa_demixing = { 18, 18, 0 };
const opus_int16 mapping_matrix_toa_demixing_data[324] = {
      8192,   8192,   8192,   8192,   8192,   8192,   8192,   8192,
      8192,   8192,   8192,   8192,   8192,   8192,   8192,   8192,
         0,      0,      0,  -9779,   9779,   6263,   8857,      0,
      6263,  13829,   9779, -13829,      0,  -6263,      0,  -8857,
     -6263,  -9779,      0,      0,  -3413,   3413,   3413, -11359,
     11359,  11359, -11359,  -3413,   3413,  -3413,  -3413, -11359,
     11359,  11359, -11359,   3413,      0,      0,  13829,   9779,
     -9779,   6263,      0,   8857,  -6263,      0,   9779,      0,
    -13829,   6263,  -8857,      0,  -6263,  -9779,      0,      0,
         0, -15617, -15617,   6406,      0,      0,  -6406,      0,
     15617,      0,      0,  -6406,      0,      0,   6406,  15617,
         0,      0,      0,  -5003,   5003, -10664,  15081,      0,
    -10664,  -7075,   5003,   7075,      0,  10664,      0, -15081,
     10664,  -5003,      0,      0,  -8176,  -8176,  -8176,   8208,
      8208,   8208,   8208,  -8176,  -8176,  -8176,  -8176,   8208,
      8208,   8208,   8208,  -8176,      0,      0,  -7075,   5003,
     -5003, -10664,      0,  15081,  10664,      0,   5003,      0,
      7075, -10664, -15081,      0,  10664,  -5003,      0,      0,
     15617,      0,      0,      0,  -6406,   6406,      0, -15617,
         0, -15617,  15617,      0,   6406,  -6406,      0,      0,
         0,      0,      0, -11393,  11393,   2993,  -4233,      0,
      2993, -16112,  11393,  16112,      0,  -2993,      0,   4233,
     -2993, -11393,      0,      0,      0,  -9974,  -9974, -13617,
         0,      0,  13617,      0,   9974,      0,      0,  13617,
         0,      0, -13617,   9974,      0,      0,      0,   5579,
     -5579,  10185,  14403,      0,  10185,  -7890,  -5579,   7890,
         0, -10185,      0, -14403, -10185,   5579,      0,      0,
     11826, -11826, -11826,   -901,    901,    901,   -901,  11826,
    -11826,  11826,  11826,   -901,    901,    901,   -901, -11826,
         0,      0,  -7890,  -5579,   5579,  10185,      0,  14403,
    -10185,      0,  -5579,      0,   7890,  10185, -14403,      0,
    -10185,   5579,      0,      0,  -9974,      0,      0,      0,
    -13617,  13617,      0,   9974,      0,   9974,  -9974,      0,
     13617, -13617,      0,      0,      0,      0,  16112, -11393,
     11393,  -2993,      0,   4233,   2993,      0, -11393,      0,
    -16112,  -2993,  -4233,      0,   2993,  11393,      0,      0,
         0,      0,      0,      0,      0,      0,      0,      0,
         0,      0,      0,      0,      0,      0,      0,      0,
     32767,      0,      0,      0,      0,      0,      0,      0,
         0,      0,      0,      0,      0,      0,      0,      0,
         0,      0,      0,  32767
};

const MappingMatrix mapping_matrix_fourthoa_demixing = { 27, 27, 0 };
const opus_int16 mapping_matrix_fourthoa_demixing_data[729] = {
      4870,   4484,   4870,   4347,   4440,   4726,   4683,   4821,
      4883,   4842,   4603,   4484,   4683,   4698,   4234,   4368,
      4603,   4783,   4783,   4820,   4821,   4347,   4820,   4440,
      4698,      0,      0,    101,     84,  -7818,   4640,  -7178,
     -5492,   4629,   8384,   6547,  -4966,    617,  -6345,   1061,
     -3241,   2939,   5549,   6390,  -4434,   4994,  -2610,   1993,
     -2873,   1547,  -4356,   -164,      0,      0,   8797,   5074,
     -1553,   5383,   1906,   5297,   2722,   1158,  -5226,   1311,
     -7760,  -3327,  -1940,   1586,  -4093,  -2951,   -214,  -6873,
      5450,  -4875,  -7193,  -4438,    558,   5593,   5607,      0,
         0,    -26,   5761,  -3723,  -1460,   1195,  -3065,  -6357,
     -1175,    608,   6965,   2310,   2759,  -8023,  -7138,   5162,
     -3624,   5006,   -809,   3592,   6209,  -4159,  -4968,   8150,
      2513,  -5702,      0,      0,    301,    109,   7161,  -2462,
     -2443,   5044,  -7125,  -2256,   1967,  -9107,    259,  -4928,
     -2592,   6514,   4111,  -7236,   8695,    635,   5009,  -4025,
     -1937,   4794,   3420,  -3507,   -400,      0,      0,   -134,
        85,   2771,   7842,  -3649,  -8225,   2866,   2586,  -9200,
     -1945,  -1563,   6155,   -720,  -1061,  -3494,  -4513,   -487,
      8389,   7317,   3348,  -3721,   3806,    371,  -6896,     70,
         0,      0,  10919,   2072,  -4867,   3472,  -4429,   1721,
     -4066,  -5193,   1032,  -5253,   9501,  -2017,  -3971,  -5261,
      -306,  -2737,  -5137,   5713,   1237,     -8,   6387,    364,
     -5423,   3364,   2888,      0,      0,    -48,   8946,   1048,
     -2691,    602,  -4332,  -4302,   -514,  -1730,   2459,  -4328,
     -2156,   3335,  -2748,  -6029,   4023,    155,    897,   5268,
     -8380,   7625,   7395,    508,   3945,  -8951,      0,      0,
        39,   4151,  -5965,  -3398,  -7006,  -3534,   2697,  -8989,
     -5237,   2913,     46,  -5540,   8196,   5766,   2711,  -2520,
     -3043,  -2146,   -948,   4965,   1806,   2472,   8988,  -1266,
      4840,      0,      0,   -407,   -189,   2179,  -1627,   6516,
       259,   7196,  -9449,  -4905,  -9766,    561,   4021,   3371,
     -8650,   5032,   3329,   2534,    641,   2224,  -5747,   1047,
     -4074,   5252,    -24,    674,      0,      0,    664,    237,
     -2837,  -4072,  -1205,   8252,  -5875,  -1670,  -2743,  -3984,
       381,   5059,   1765,   2666,  -8295,   7403,   1154,  -2086,
      7622,   7105,   3677,  -6943,   1050,  -6632,   -694,      0,
         0,    382,   -133,   5699,   7650,   5154,  -5713,  -1645,
     -6902,   6181,   4450,   1151,    410,   -993,   3829,   2444,
     -2405,  -6618,  -9514,   5366,  -1896,   5844,  -2886,  -1524,
     -7321,  -1007,      0,      0,  12767,  -2530,   3183,  -1409,
     -4015,  -2894,  -5155,  -1710,   3841,  -2107, -10274,   5119,
      3979,  -4010,   5550,   4822,   -746,  -2507,  -3080,   4289,
     -3675,   4333,  -1416,  -1230,  -1122,      0,      0,     17,
      8048,   2398,  -2167,    -73,  -3606,   3125,    398,    731,
     -5973,   5705,  -1032,   4679,   7305,   3134,   1301,  -3858,
       -89,   2938,   4359,  -9155,  -4805,  -8407,   3673,  -8645,
         0,      0,    187,   7355,   3145,  -6719,  -4432,  -5939,
      2541,  -2810,   9723,    778,  -1105,   5687,  -4174,   2534,
     -4461,   1017,   -244,   5481,  -1655,  -6765,  -3350,  -4894,
      1592,  -2318,   8827,      0,      0,    196,   3588,   9631,
      3063,  -4564,   6043,   2683,   2595,  -2488,  -2186,    173,
     -6059,  -8270,  -2386,    409,   7441,  -8608,    376,  -4364,
      2321,   -280,     97,   8331,  -3022,  -4721,      0,      0,
       117,   -748, -10833,   1533,   4200,  -2875,   -997,   -109,
     -3661,  -6119,   4454,   8808,  -9189,   8294,   1521,   7265,
     -2348,  -5094,   -948,  -5400,  -3193,   8914,   5763,   1716,
     -1070,      0,      0,   2497,    399,  -5201,  -2038,   7843,
      -376,   7567,  -5073,   7616,  -5537,   2086,  -3453,  -5544,
       -56, -11648,  -1314,   3546,  -3432,   -117,   8694,  -4245,
      9621,   3098,  -2582,  -2351,      0,      0,   4386,  -3104,
     -3132, -10512,    566,   5217,   5128,   4967,   1348,   7035,
     -1470,     91,   -125,  -3548,   8244,  -3029, -10033,   2186,
      9745,  -6440,  -2074,   3638,  -1477,  -7045,   -562,      0,
         0,   2154,   8116,  -6102,   6570,  12998,   -712,  -4126,
     -4996,     30,   1571,  -6393, -12794,    425,   5036,   1190,
      5763,   5653,  12933,  -6671,   5197,  -2964,  -3316,  -6354,
    -10554,  -2652,      0,      0,  12618,  -3737,     93,  -5901,
      4262,  -3364,   4444,   3103,  -2767,   3403,   4925,  -2584,
      -989,   4977,  -3714,  -1965,   3076,    326,  -2946,  -2568,
      1026,  -2980,   3362,  -6132,  -5966,      0,      0,   6001,
        48,  -1979,  -7275,   3476,  -2096,  10591,   3793,   2629,
      -447, -14747,  -3689,  -5525,   8358,   6883,  -9703,  -4556,
      7471,   2965,   4056,  13221,  -7327,  -3073,  -2353,  -6720,
         0,      0,    621,  11034,    -44,  -2828,   5978,  -1850,
     -1772,   3894,  -7471,  -1397,    945,  -2028,  -2928,  -2240,
      3172,   2222,   4544,  -4243,  -5645,   3745,   2573,   3511,
     -8206,  -7286,   5700,      0,      0,    321,  10818,  -4982,
      7813,   -749,   9907,   1360,  -1443,    568,  -1103,   2305,
      6045,   2270,  -1063,  -1920,  -3073,   5893,  -3476, -11346,
     -1657,   -588,   2957,  -2287,  -8527,  -8041,      0,      0,
       119,   -268,   2372,  -3040,   4979,  -3789,  -5630,  10619,
      5900,  -5109,  -4585,  -3862,  10467,  -3527,   -385, -10034,
     -9991,   4860,    984,   2362,   2311,  -6804,   6324,    433,
      5291,      0,      0,      0,      0,      0,      0,      0,
         0,      0,      0,      0,      0,      0,      0,      0,
         0,      0,      0,      0,      0,      0,      0,      0,
         0,      0,      0,      0,  32767,      0,      0,      0,
         0,      0,      0,      0,      0,      0,      0,      0,
         0,      0,      0,      0,      0,      0,      0,      0,
         0,      0,      0,      0,      0,      0,      0,      0,
     32767
};

const MappingMatrix mapping_matrix_fifthoa_demixing = { 38, 38, 0 };
const opus_int16 mapping_matrix_fifthoa_demixing_data[1444] = {
      3188,   3247,   3268,   3368,   3368,   3138,   3268,   3099,
      3211,   3368,   3099,   3247,   3211,   3368,   3368,   3368,
      3149,   3268,   3247,   3211,   3099,   3188,   3138,   3149,
      3099,   3188,   3368,   3149,   3188,   3247,   3268,   3138,
      3211,   3368,   3138,   3149,      0,      0,    118,    -47,
     -5011,    282,    333,  -1497,  -4584,   2908,   3388,  -3647,
     -2493,   1139,  -2882,  -1719,   3604,  -2543,  -4328,   5443,
      1286,  -5498,  -4583,   2510,  -1743,  -2556,   4168,   1446,
      -290,   1812,  -4074,  -2377,   4152,   2847,   4991,   3980,
       393,   5072,      0,      0,   5489,  -2235,   1507,  -5326,
      4609,  -1096,   2926,  -3427,  -3301,  -3078,   4226,   1730,
      4627,   2561,   2966,   -592,    143,   -677,   4617,   -755,
      -956,   -433,  -5138,   3037,    157,  -1394,  -4498,  -4984,
     -3661,  -4112,  -3756,   4628,   -570,   3356,   1605,   1803,
         0,      0,   -162,   5162,   2132,   2392,   3556,  -5141,
     -1536,   2975,  -3001,  -3350,  -2231,  -5230,   1294,  -4965,
      3494,   5230,  -3292,  -1359,  -2945,   -773,   2670,   4867,
      -660,   3720,  -3415,  -5112,  -3700,  -1211,    407,   3013,
       763,    591,   2481,  -2657,   5210,    784,      0,      0,
      -156,    338,  -4246,    510,    462,   3296,   2846,   3333,
     -4292,   4574,   1940,  -2986,  -1275,   3701,   5022,  -5250,
      5780,  -2676,  -1180,   1516,  -4852,   4877,    342,  -3923,
     -5703,  -2920,    379,   -657,   -361,  -3346,   1044,    795,
      5257,  -4004,    698,   1115,      0,      0,     47,   -140,
     -3292,  -1097,    652,    855,  -5260,  -3691,  -4470,   4521,
     -3863,   1093,  -5552,  -2016,   3831,    334,   -456,  -1532,
      2068,   1788,   2054,   -295,   3668,  -2820,    328,   -994,
       295,  -3301,   5770,   4282,  -6353,   5632,  -1371,   5005,
       238,   4041,      0,      0,   6764,  -1659,  -2730,   5726,
      3715,  -3216,   -933,    531,    -52,   -345,   3022,  -2818,
      4005,  -1617,  -1189,  -3748,  -3403,  -3592,   4040,  -3553,
     -2806,  -3444,   6023,   -711,  -3298,  -2503,   2548,   5564,
       940,   1848,   1207,   4010,  -3488,   -358,  -2511,  -1966,
         0,      0,    -64,  -5039,   1403,  -4455,   6240,   2189,
     -1716,  -4348,   4183,   3951,  -4042,  -3606,   2399,  -4563,
      4050,   -612,   -395,    348,  -5791,    391,  -1440,   -735,
      1398,   4359,   -518,   2969,   6556,   1951,   -518,  -4993,
      -925,    998,   -569,  -2934,   3460,    420,      0,      0,
        16,   5482,  -4122,    770,   2082,   5020,  -3961,    485,
      -584,   -793,      3,   5222,  -1416,   3673,     78,   3549,
      -937,  -5723,   1673,  -6162,  -2540,   3082,   -355,   1838,
      -615,   4601,   2832,   -359,  -3346,    668,  -3393,  -1583,
     -3774,  -2206,   5754,  -4961,      0,      0,   -328,    299,
      2470,    317,    525,  -4494,   2805,   2617,   2383,  -2363,
     -1037,   4085,    895,  -4622,   3218,  -6607,  -3381,  -5933,
      1397,   6394,   -446,   5694,     14,  -4510,   4329,   3690,
      -334,      0,   2932,  -2478,  -2944,   -577,   -599,   -230,
      1553,  -4736,      0,      0,   -324,    142,  -3252,   -867,
      1111,  -1882,   3378,  -6055,   6502,  -6840,   4280,  -2694,
     -2876,   4190,   6454,    655,   1061,    626,  -2669,   -798,
      3192,   -985,   -898,  -5482,   -548,   2315,   -558,   1302,
       900,   5747,  -1325,   1599,  -1384,  -5749,    624,   1110,
         0,      0,    321,    312,   2188,   1322,    237,    708,
      -304,   2463,   1500,  -1094,  -5112,  -1010,  -6799,    646,
       992,   1969,   3423,  -3996,   2628,   4451,   3432,  -2833,
     -6101,   -330,  -3768,     -3,   -707,   5961,  -4037,  -3736,
      4080,   7254,  -4113,   2151,     54,  -2150,      0,      0,
      7735,   4064,  -3884,  -5240,    577,   2229,  -3947,   2914,
      3555,   4011,    774,  -3519,   1985,  -3701,  -3824,    330,
      -905,   2085,   1155,   2176,   3006,    340,  -5533,  -3264,
      -902,   3114,    344,  -5060,   1524,   1805,   1926,   2350,
      1905,  -3203,  -2762,  -4162,      0,      0,    193,   -151,
     -1434,   6289,   7354,   4234,    169,   2868,  -1977,  -1375,
     -4987,   2345,   2742,    599,    939,  -4837,   2688,    991,
     -6907,    716,  -1542,  -4346,  -1833,   1493,   3134,   2903,
     -7019,  -2835,     93,   4395,    621,    870,  -2357,   -975,
     -2933,   -127,      0,      0,   -616,  -5968,  -3479,  -1651,
      4932,  -2445,  -5512,  -1451,    691,    739,    479,   4227,
     -2886,   3853,      8,   -501,    188,   1990,   3842,   2270,
      1662,   -174,   1290,   2456,     67,  -3267,  -5535,    483,
      5721,  -1642,   6501,  -3432,   1184,  -3246,   4101,  -4880,
         0,      0,   -465,   5264,  -4812,    682,   1683,  -4539,
      2916,  -1985,   2899,   3324,   1060,  -4398,   -745,  -2137,
     -3827,   1044,   6225,   3609,   -532,   1980,  -6001,    564,
      -209,  -1299,   5336,  -3605,  -1484,     37,     19,  -1295,
      -665,   -385,  -6773,   3651,   6153,  -1291,      0,      0,
       193,   -415,   5166,   -110,    626,   6743,  -2860,   1425,
      1101,  -1341,     80,  -4533,    249,   4231,   -119,  -6009,
     -2970,   5170,   -822,  -2610,   4527,   5948,    182,  -2589,
       837,  -5471,    371,    -43,    373,   -665,  -1233,   -626,
     -7353,   2606,   1339,  -1398,      0,      0,   -533,    147,
      2075,   -672,   1043,   3503,   4402,  -4971,  -3287,   3731,
     -2606,   3817,   1972,  -5603,   5114,   1185,  -1318,   1906,
      3018,  -1999,    343,  -1943,    207,  -6744,    913,  -4060,
       645,   -349,  -5667,   4766,   5575,  -1733,   1116,    160,
      1534,  -5690,      0,      0,   -137,    -36,   1556,   1325,
      1553,  -2230,   1188,   5296,  -5104,   4673,   6295,    498,
     -4723,    933,   2994,   4067,  -4700,   1758,  -4116,  -1252,
      2444,  -4092,   1653,  -2802,   5069,   1133,    790,  -2355,
      -934,  -6304,   1642,   2045,  -4259,  -3873,   -213,    215,
         0,      0,   -364,    423,   4888,  -1316,    118,   -950,
      4027,    114,   2961,  -3136,  -3012,   -883,  -6192,   1340,
     -3210,  -1193,   1376,   3128,   1596,  -2994,  -3194,    533,
      8502,   2487,  -1485,   1032,    301,  -8007,   -577,    887,
       297,   7778,   3121,  -1901,    -94,  -6401,      0,      0,
      9260,  -1845,    668,   2787,  -2255,   2699,  -2512,  -3737,
     -3675,  -3601,  -1803,    210,  -1701,  -1442,  -2700,   3457,
      2868,   2079,  -2113,   3178,   1277,   3578,   5240,  -2482,
      3324,   1020,  -4027,   3835,  -3758,  -3633,  -3170,  -1310,
      2509,  -3110,    713,    174,      0,      0,   -399,   4969,
     -2321,  -7744,   6494,  -3776,   1478,    758,  -1794,  -2233,
     -4059,   4932,   2770,   4761,  -3475,   1243,    829,   -651,
     -5358,   -436,   2381,   1360,   2561,  -3118,    858,  -4366,
      3933,   3646,    -43,  -1310,    -16,    924,   1197,   1415,
     -5036,   -376,      0,      0,    100,   1410,   1290,   3199,
      7091,  -3638,  -2641,   1118,     45,   -441,    794,   -974,
     -5033,    889,    438,  -3102,    895,   3555,   4672,   4795,
      1129,  -2408,  -2153,   1742,    159,  -2040,   7578,  -2006,
     -5737,   1986,  -5568,  -6413,   2428,  -1387,  -2441,    667,
         0,      0,    -37,  -6031,  -4434,   -904,   3290,   1806,
      4736,   2516,  -5905,  -5927,   1754,  -4300,  -2468,  -2203,
     -4836,   -672,   1444,  -1591,  -1631,  -1789,   4311,   -153,
      -688,  -1222,   1058,   3139,   4659,   -353,   1543,   1838,
      2180,  -1448,   2432,   6277,   5304,  -1692,      0,      0,
      -280,   4506,    807,   -477,    823,   3550,   1427,  -1856,
     -3003,  -3501,  -1203,   2679,    933,    778,  -4954,  -1977,
     -7458,   4687,    435,   7045,  -4053,  -3130,    257,  -3917,
     -6165,   1889,    927,    235,   1889,  -1097,   1985,    630,
     -2172,  -2130,   7080,   4810,      0,      0,   -300,    496,
      2808,    279,    667,  -7179,  -2661,   -526,  -2832,   1751,
      2849,   4829,   -906,  -4151,  -1124,  -3062,   8166,   5361,
     -1656,  -6017,   3265,   2551,   -864,   -432,  -6966,   6295,
      -168,    901,    442,   -582,    269,    236,  -3574,    799,
       472,    565,      0,      0,    805,  -2466,   6208,  -4592,
      -170,  -6701,  -5610,   3678,  -4242,   4561,   -724,  -5534,
      2415,   7354,   2761,   2699,   -349,   3822,  -2372,   1756,
     -5523,  -3445,   -588,  -5749,  -3986,   9804,  -3871,   5375,
     -2308,   5504,  -2766,  -1651,   1472,   6832,   2705,  -5104,
         0,      0,   -700,  -1179,   4402,    400,   1383,    939,
     -1342,   6013,   2577,  -3472,    472,   2883,   1450,  -3917,
      2849,   5084,   4990,   5392,    342,  -4925,  -3329,  -5372,
     -2674,  -6035,  -5072,   -836,    179,   2506,   7987,  -3647,
     -8202,  -1437,   1891,   2400,   1607,  -3611,      0,      0,
     -4706,  -4003,   9928,   -379,   5557,   3738,  -8789,    685,
      1937,  -5157,  13388,   7995,  -4119,  -9909,  -5079,   4804,
      5586,    774,  -5430,    299,  -9943,   3264,  -3690,  -3901,
     -1133,  -6199,   3182,   1544,   5467,   3686,  -2639,   4068,
      1163,   -185,  -1299,   -506,      0,      0,    843,   1005,
     -1059,    467,  -1279,  -2259,   6057,  -1694,  -5885,   5342,
     -5160,  -3748,  -1382,   4420,   -697,  -2000,  -3808,   3100,
      2685,  -4073,    531,    318,  -7822,   2414,   2901,   3399,
     -1340,   8449,   3685,    463,  -3341,   2423,   2304,  -2723,
        84,  -2622,      0,      0,  12088,   -265,   2562,   -435,
     -4348,  -2426,   3538,   1552,   1279,    883,  -4166,   2634,
     -6130,   2994,   3729,  -1570,   -601,  -1753,  -5124,  -2788,
     -2096,  -1920,  -2649,   2793,  -1079,  -1952,   2983,  -1530,
      2499,   1769,   1492,  -6757,  -2108,   2841,   1466,   2597,
         0,      0,  -3830,  -4093,   2448,  12720,   7737,   -665,
      -832,  -9257,   2971,  -2400,    791,   1873,   1072,   -587,
     -7440,   8055,   1531,  -4736,    616,  -1782,  -2982,   9663,
     -5057,  -5926,   1610,  -4489,   7033,  -8658,   6010,  -5673,
      5648,    812,   -271,  -1802,  -4500,   4392,      0,      0,
      -888,   -327,   3373,  -1084,   7959,   2430,   1898,  -2360,
     -1820,  -1377,  -1090,  -4436,  -3422,  -1106,  -3230,   3876,
       -41,  -5128,   6375,  -1848,  -3824,   5844,    617,  -1957,
      4232,   1345,  -1439,    -83,   3046,   -214,   5458,  -5566,
     -4387,  -3738,  -5740,   8657,      0,      0,   6978,   6239,
     -3686,   -981,  -2854,     78,   5859,   -357,   4618,   7391,
      -138,    971,  -5799,   2135,   4478,  -7004,  -5949,   1668,
     -6933,  -1163,   7010,  -5624,   2990,   6192,  -8075,   3567,
     -8308,   2236,  -5098,  -2120,  -4355,  -4238,   4955,  10230,
       692,  -5606,      0,      0,  -1348,  -7069,    -12,  -4927,
      1211,    651,   1360,   7744,   3404,   5069,  -2438,   -105,
      2332,   1494,  -4686,   1336,  -3628,   -881,   2474,   1736,
       -26,   -257,   2135,  -4452,    446,   -641,  -4704,   2605,
     -6436,   6662,  -4939,    990,  -1100,  -3782,   5028,   4753,
         0,      0,  -2875,   6410,   3518,   3950,   1271,    869,
     -2842,  -5837,   1532,  -2899,   1140,   -597,   1712,  -1988,
     -4819,  -4783,   4773,  -8796,   2240,  -4596,   3565,  -4853,
      -556,  -3974,   7366,  -4370,   3113,  -3548,   3552,  -5450,
      3869,   2514,   6736,  -4570,   6074,   3151,      0,      0,
         0,      0,      0,      0,      0,      0,      0,      0,
         0,      0,      0,      0,      0,      0,      0,      0,
         0,      0,      0,      0,      0,      0,      0,      0,
         0,      0,      0,      0,      0,      0,      0,      0,
         0,      0,      0,      0,  32767,      0,      0,      0,
         0,      0,      0,      0,      0,      0,      0,      0,
         0,      0,      0,      0,      0,      0,      0,      0,
         0,      0,      0,      0,      0,      0,      0,      0,
         0,      0,      0,      0,      0,      0,      0,      0,
         0,      0,      0,  32767
};

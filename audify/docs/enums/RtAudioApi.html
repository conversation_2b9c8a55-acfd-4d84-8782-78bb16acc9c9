<!DOCTYPE html><html class="default" lang="en"><head><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>RtAudioApi | Audify.js</title><meta name="description" content="Documentation for Audify.js"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script defer src="../assets/main.js"></script><script async src="../assets/icons.js" id="tsd-icons-script"></script><script async src="../assets/search.js" id="tsd-search-script"></script><script async src="../assets/navigation.js" id="tsd-nav-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os";document.body.style.display="none";setTimeout(() => app?app.showPage():document.body.style.removeProperty("display"),500)</script><header class="tsd-page-toolbar"><div class="tsd-toolbar-contents container"><div class="table-cell" id="tsd-search" data-base=".."><div class="field"><label for="tsd-search-field" class="tsd-widget tsd-toolbar-icon search no-caption"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><use href="../assets/icons.svg#icon-search"></use></svg></label><input type="text" id="tsd-search-field" aria-label="Search"/></div><div class="field"><div id="tsd-toolbar-links"></div></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">Audify.js</a></div><div class="table-cell" id="tsd-widgets"><a href="#" class="tsd-widget tsd-toolbar-icon menu no-caption" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><use href="../assets/icons.svg#icon-menu"></use></svg></a></div></div></header><div class="container container-main"><div class="col-content"><div class="tsd-page-title"><ul class="tsd-breadcrumb"><li><a href="../modules.html">Audify.js</a></li><li><a href="RtAudioApi.html">RtAudioApi</a></li></ul><h1>Enumeration RtAudioApi<code class="tsd-tag ts-flagConst">Const</code> </h1></div><section class="tsd-panel tsd-comment"><div class="tsd-comment tsd-typography"><p>Audio API specifier arguments.</p>
</div><div class="tsd-comment tsd-typography"></div></section><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L16">index.d.ts:16</a></li></ul></aside><section class="tsd-panel-group tsd-index-group"><section class="tsd-panel tsd-index-panel"><details class="tsd-index-content tsd-index-accordion" open><summary class="tsd-accordion-summary tsd-index-summary"><h5 class="tsd-index-heading uppercase" role="button" aria-expanded="false" tabIndex="0"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><use href="../assets/icons.svg#icon-chevronSmall"></use></svg> Index</h5></summary><div class="tsd-accordion-details"><section class="tsd-index-section"><h3 class="tsd-index-heading">Enumeration Members</h3><div class="tsd-index-list"><a href="RtAudioApi.html#LINUX_ALSA" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>LINUX_<wbr/>ALSA</span></a>
<a href="RtAudioApi.html#LINUX_OSS" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>LINUX_<wbr/>OSS</span></a>
<a href="RtAudioApi.html#LINUX_PULSE" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>LINUX_<wbr/>PULSE</span></a>
<a href="RtAudioApi.html#MACOSX_CORE" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>MACOSX_<wbr/>CORE</span></a>
<a href="RtAudioApi.html#RTAUDIO_DUMMY" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>RTAUDIO_<wbr/>DUMMY</span></a>
<a href="RtAudioApi.html#UNIX_JACK" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>UNIX_<wbr/>JACK</span></a>
<a href="RtAudioApi.html#UNSPECIFIED" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>UNSPECIFIED</span></a>
<a href="RtAudioApi.html#WINDOWS_ASIO" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>WINDOWS_<wbr/>ASIO</span></a>
<a href="RtAudioApi.html#WINDOWS_DS" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>WINDOWS_<wbr/>DS</span></a>
<a href="RtAudioApi.html#WINDOWS_WASAPI" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>WINDOWS_<wbr/>WASAPI</span></a>
</div></section></div></details></section></section><section class="tsd-panel-group tsd-member-group"><h2>Enumeration Members</h2><section class="tsd-panel tsd-member"><a id="LINUX_ALSA" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>LINUX_<wbr/>ALSA</span><a href="#LINUX_ALSA" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-enum-member">LINUX_<wbr/>ALSA</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">2</span></div><div class="tsd-comment tsd-typography"><p>The Advanced Linux Sound Architecture API.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L24">index.d.ts:24</a></li></ul></aside></section><section class="tsd-panel tsd-member"><a id="LINUX_OSS" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>LINUX_<wbr/>OSS</span><a href="#LINUX_OSS" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-enum-member">LINUX_<wbr/>OSS</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">5</span></div><div class="tsd-comment tsd-typography"><p>The Linux Open Sound System API.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L33">index.d.ts:33</a></li></ul></aside></section><section class="tsd-panel tsd-member"><a id="LINUX_PULSE" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>LINUX_<wbr/>PULSE</span><a href="#LINUX_PULSE" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-enum-member">LINUX_<wbr/>PULSE</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">4</span></div><div class="tsd-comment tsd-typography"><p>The Linux PulseAudio API.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L30">index.d.ts:30</a></li></ul></aside></section><section class="tsd-panel tsd-member"><a id="MACOSX_CORE" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>MACOSX_<wbr/>CORE</span><a href="#MACOSX_CORE" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-enum-member">MACOSX_<wbr/>CORE</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">1</span></div><div class="tsd-comment tsd-typography"><p>Macintosh OS-X Core Audio API.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L21">index.d.ts:21</a></li></ul></aside></section><section class="tsd-panel tsd-member"><a id="RTAUDIO_DUMMY" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>RTAUDIO_<wbr/>DUMMY</span><a href="#RTAUDIO_DUMMY" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-enum-member">RTAUDIO_<wbr/>DUMMY</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">9</span></div><div class="tsd-comment tsd-typography"><p>A compilable but non-functional API.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L45">index.d.ts:45</a></li></ul></aside></section><section class="tsd-panel tsd-member"><a id="UNIX_JACK" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>UNIX_<wbr/>JACK</span><a href="#UNIX_JACK" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-enum-member">UNIX_<wbr/>JACK</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">3</span></div><div class="tsd-comment tsd-typography"><p>The Jack Low-Latency Audio Server API.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L27">index.d.ts:27</a></li></ul></aside></section><section class="tsd-panel tsd-member"><a id="UNSPECIFIED" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>UNSPECIFIED</span><a href="#UNSPECIFIED" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-enum-member">UNSPECIFIED</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">0</span></div><div class="tsd-comment tsd-typography"><p>Search for a working compiled API.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L18">index.d.ts:18</a></li></ul></aside></section><section class="tsd-panel tsd-member"><a id="WINDOWS_ASIO" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>WINDOWS_<wbr/>ASIO</span><a href="#WINDOWS_ASIO" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-enum-member">WINDOWS_<wbr/>ASIO</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">6</span></div><div class="tsd-comment tsd-typography"><p>The Steinberg Audio Stream I/O API.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L36">index.d.ts:36</a></li></ul></aside></section><section class="tsd-panel tsd-member"><a id="WINDOWS_DS" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>WINDOWS_<wbr/>DS</span><a href="#WINDOWS_DS" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-enum-member">WINDOWS_<wbr/>DS</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">8</span></div><div class="tsd-comment tsd-typography"><p>The Microsoft DirectSound API.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L42">index.d.ts:42</a></li></ul></aside></section><section class="tsd-panel tsd-member"><a id="WINDOWS_WASAPI" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>WINDOWS_<wbr/>WASAPI</span><a href="#WINDOWS_WASAPI" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-enum-member">WINDOWS_<wbr/>WASAPI</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">7</span></div><div class="tsd-comment tsd-typography"><p>The Microsoft WASAPI API.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/almoghamdani/audify/blob/fc65dbd7ca2da2580148f5d916706e320795db11/index.d.ts#L39">index.d.ts:39</a></li></ul></aside></section></section></div><div class="col-sidebar"><div class="page-menu"><div class="tsd-navigation settings"><details class="tsd-index-accordion"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Settings</h3></summary><div class="tsd-accordion-details"><div class="tsd-filter-visibility"><h4 class="uppercase">Member Visibility</h4><form><ul id="tsd-filter-options"><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-protected" name="protected"/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Protected</span></label></li><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-private" name="private"/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Private</span></label></li><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-inherited" name="inherited" checked/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Inherited</span></label></li><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-external" name="external"/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>External</span></label></li></ul></form></div><div class="tsd-theme-toggle"><h4 class="uppercase">Theme</h4><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div><details open class="tsd-index-accordion tsd-page-navigation"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>On This Page</h3></summary><div class="tsd-accordion-details"><a href="#LINUX_ALSA" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>LINUX_<wbr/>ALSA</span></a><a href="#LINUX_OSS" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>LINUX_<wbr/>OSS</span></a><a href="#LINUX_PULSE" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>LINUX_<wbr/>PULSE</span></a><a href="#MACOSX_CORE" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>MACOSX_<wbr/>CORE</span></a><a href="#RTAUDIO_DUMMY" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>RTAUDIO_<wbr/>DUMMY</span></a><a href="#UNIX_JACK" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>UNIX_<wbr/>JACK</span></a><a href="#UNSPECIFIED" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>UNSPECIFIED</span></a><a href="#WINDOWS_ASIO" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>WINDOWS_<wbr/>ASIO</span></a><a href="#WINDOWS_DS" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>WINDOWS_<wbr/>DS</span></a><a href="#WINDOWS_WASAPI" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-16"></use></svg><span>WINDOWS_<wbr/>WASAPI</span></a></div></details></div><div class="site-menu"><nav class="tsd-navigation"><a href="../modules.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-1"></use></svg><span>Audify.js</span></a><ul class="tsd-small-nested-navigation" id="tsd-nav-container" data-base=".."><li>Loading...</li></ul></nav></div></div></div><footer></footer><div class="overlay"></div></body></html>
{
	"compilerOptions": {
		"module": "commonjs",
		"target": "ES2020",
		"outDir": "out",
		"lib": [
			"ES2020",
			"DOM"
		],
		"sourceMap": true,
		"rootDir": "src",
		"strict": true,   /* enable all strict type-checking options */
		"allowSyntheticDefaultImports": true,
		"esModuleInterop": true,
		"skipLibCheck": true,
		"noImplicitReturns": true,
		"noFallthroughCasesInSwitch": true,
		"noUnusedParameters": true,
		/* Additional Checks */
		// "noImplicitReturns": true, /* Report error when not all code paths in function return a value. */
		// "noFallthroughCasesInSwitch": true, /* Report errors for fallthrough cases in switch statement. */
		// "noUnusedParameters": true,  /* Report errors on unused parameters. */
	},
	"exclude": [
		"node_modules",
		"supabase",
		"create_api_key.ts",
		"test_optimize.ts",
		"test_transcribe.ts",
		"webview-ui",
		"webview-ui-vue",
		".vscode-test"
	]
}

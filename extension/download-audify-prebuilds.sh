#!/bin/bash

# Create prebuilds directory structure
mkdir -p node_modules/audify/prebuilds

# Base URL for GitHub releases
BASE_URL="https://github.com/almoghamdani/audify/releases/download/v1.9.0"

# Array of all platform combinations
PLATFORMS=(
    "darwin-arm64"
    "darwin-x64"
    "linux-arm"
    "linux-arm64"
    "linux-x64"
    "win32-ia32"
    "win32-x64"
)

# Array of N-API versions
NAPI_VERSIONS=(
    "v5"
    "v6"
    "v7"
    "v8"
    "v9"
)

# Download and extract all combinations
for platform in "${PLATFORMS[@]}"; do
    for napi in "${NAPI_VERSIONS[@]}"; do
        filename="audify-v1.9.0-napi-${napi}-${platform}.tar.gz"
        url="${BASE_URL}/${filename}"
        
        echo "Downloading ${filename}..."
        curl -L "${url}" -o "${filename}"
        
        # Create platform-specific directory
        mkdir -p "node_modules/audify/prebuilds/${platform}"
        
        echo "Extracting ${filename}..."
        tar xzf "${filename}" -C "node_modules/audify/prebuilds/${platform}"
        
        # Clean up downloaded tar.gz
        rm "${filename}"
    done
done

echo "All prebuilds downloaded and extracted!"

import { readFileSync, existsSync } from 'fs';
import { resolve } from 'path';

const VOICEHYPE_API_KEY = 'vhkey_ac4b2fc104a96e5d46ed30690ae5d384';
const SUPABASE_PROJECT_URL = 'https://supabase.voicehype.ai';

async function testTranscribe() {
  try {
    // Check if audio file exists
    const audioPath = resolve('./recording.wav');
    if (!existsSync(audioPath)) {
      console.error('Error: recording.wav not found in current directory');
      return;
    }

    // Read and convert audio file to base64
    const audioBuffer = readFileSync(audioPath);
    const base64Audio = audioBuffer.toString('base64');

    console.log('Making request to transcribe function...');

    // Make request to edge function
    const response = await fetch(
      `${SUPABASE_PROJECT_URL}/functions/v1/transcribe`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': VOICEHYPE_API_KEY
        },
        body: JSON.stringify({
          audioUrl: base64Audio,
          service: 'whisper',
          model: 'whisper-1',
          language: 'en'
        })
      }
    );

    const responseText = await response.text();
    console.log('Raw response:', responseText);

    let responseData;
    try {
      responseData = JSON.parse(responseText);
    } catch (e) {
      console.error('Failed to parse response:', e);
      return;
    }

    if (!response.ok) {
      console.error('Error:', {
        status: response.status,
        statusText: response.statusText,
        data: responseData
      });
      return;
    }

    console.log('Full response data:', responseData);
    console.log('Transcription:', responseData?.data?.transcription);
  } catch (error) {
    console.error('Error:', error);
  }
}

testTranscribe();
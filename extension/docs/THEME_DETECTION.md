# Robust Theme Detection

The VoiceHype extension now uses a robust theme detection system based on VS Code's official ColorTheme API.

## Overview

The `RobustThemeDetector` class uses VS Code's official `ColorThemeKind` enum to accurately determine theme types:

- `ColorThemeKind.Light` → 'light'
- `ColorThemeKind.Dark` → 'dark'
- `ColorThemeKind.HighContrastLight` → 'light'
- `ColorThemeKind.HighContrast` → 'dark'

## Usage

### Basic Usage

```typescript
import { RobustThemeDetector } from './services/RobustThemeDetector';

const detector = new RobustThemeDetector();
const currentTheme = detector.getCurrentTheme(); // 'light' | 'dark'
```

### Theme Change Events

```typescript
const disposable = detector.onThemeChanged((theme) => {
    console.log(`Theme changed to: ${theme}`);
    // Update UI accordingly
});

// Clean up when done
disposable.dispose();
```

### Debugging

```typescript
// Get detailed theme information
const info = detector.getThemeInfo();
console.log(info);
// Output: { kind, kindName, detectedType }

// Print debug information
detector.debugTheme();
```

## Integration with VoiceHypePanelService

The `VoiceHypePanelService` automatically uses the robust theme detector:

- Theme changes are detected and propagated to the webview
- Webview receives `themeChanged` messages with `'vscode-light'` or `'vscode-dark'`
- CSS variables handle the actual theme switching

## VS Code API Reference

Based on the official VS Code API documentation:

- **ColorTheme.kind**: Returns the theme kind as `ColorThemeKind`
- **ColorThemeKind**: Enumeration with values:
  - `Light: 1`
  - `Dark: 2`
  - `HighContrast: 3`
  - `HighContrastLight: 4`

## Testing

Run the theme detection test:

```typescript
import { RobustThemeDetector } from './services/RobustThemeDetector';

const detector = new RobustThemeDetector();
console.log('Current theme:', detector.getCurrentTheme());
detector.debugTheme();
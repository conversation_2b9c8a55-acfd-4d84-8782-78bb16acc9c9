# VS Code Extension Authentication Deployment Guide (Simplified)

## Overview

This guide provides deployment instructions for the simplified VS Code extension authentication system that leverages your existing VoiceHype website authentication instead of separate OAuth flows.

## Authentication Flow

**Simplified Flow Using Your Website:**
1. **VS Code Extension** → Opens browser to `https://voicehype.ai/vscode-auth`
2. **VoiceHype Website** → User signs in with existing auth (Google/GitHub/Email) or is already signed in
3. **VoiceHype Website** → Shows authorization confirmation page
4. **VoiceHype Website** → Calls edge function to create API key and redirects to VS Code
5. **VS Code Extension** → Receives API key and completes setup

This approach:
- ✅ Uses your existing authentication system
- ✅ Maintains consistent branding and user experience
- ✅ Requires only ONE edge function instead of two
- ✅ Leverages your existing Google/GitHub OAuth setup
- ✅ Handles both signed-in and non-signed-in users gracefully

## Prerequisites

- Supabase CLI installed and configured
- Access to your Supabase project
- VoiceHype website with existing authentication (Google/GitHub/Email)
- Extension development environment set up

## Components to Deploy

1. **Single Edge Function**: `vscode-extension-auth` - Creates API keys and handles VS Code redirects
2. **Website Route**: `/vscode-auth` - New page on your existing VoiceHype website
3. **VS Code Extension**: Updated to redirect to your website instead of direct OAuth

## 1. Database Schema Verification

### Ensure Required Migrations Are Applied

The same migrations from the previous setup are still required:

```powershell
# Navigate to your Supabase directory
cd supabase

# Apply all pending migrations
supabase db push
```

Required migrations:
- `20250312_002_fix_api_key_return.sql` - Fixes API key creation to return the actual key
- `20250312_003_api_key_status_function.sql` - Adds API key status management
- `20250312_004_limit_api_key_expiration.sql` - Limits API key expiration to 1 year
- `20250312_007_fix_validate_api_key.sql` - Fixes API key validation with proper hashing

## 2. Edge Function Deployment

### Deploy Single Authentication Function

Deploy the simplified authentication edge function:

```powershell
# Navigate to your Supabase directory
cd supabase

# Deploy the single auth function
supabase functions deploy vscode-extension-auth

# Verify deployment
supabase functions list
```

### Set Environment Variables

Ensure these environment variables are set in your Supabase project:

```powershell
supabase secrets set SUPABASE_URL=https://your-project.supabase.co
supabase secrets set SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

## 3. Website Deployment

### Add New Route to VoiceHype Website

1. **Add the Vue component**: `VSCodeAuthView.vue` has been created in `src/views/`
2. **Add the route**: The route `/vscode-auth` has been added to your router
3. **Deploy the website**: Deploy your updated VoiceHype website with the new route

### Test the New Route

After deployment, test that the route works:
- Visit: `https://voicehype.ai/vscode-auth?state=test&redirect_uri=https://example.com`
- Should show the authorization page
- Should handle both signed-in and non-signed-in states

## 4. Extension Configuration

The VS Code extension has been updated to use your website instead of direct OAuth:

```typescript
// Updated in AuthenticationService.ts
private _buildAuthUrl(state: string, callbackUri: string): string {
  const params = new URLSearchParams();
  params.append('state', state);
  params.append('redirect_uri', callbackUri);
  
  // Now uses your existing VoiceHype website
  return `https://voicehype.ai/vscode-auth?${params.toString()}`;
}
```

## 5. Testing the Complete Flow

### Quick Deployment

Use the automated deployment script:

```powershell
.\scripts\deploy-vscode-auth.ps1
```

### Manual Testing Steps

1. **Install Extension in Development**:
   ```powershell
   cd extension
   npm run compile
   # Press F5 in VS Code to launch Extension Development Host
   ```

2. **Test Authentication Flow**:
   - Open extension webview panel
   - Should show onboarding screen with "Sign in with Browser" button
   - Click "Sign in with Browser"
   - Browser opens to `https://voicehype.ai/vscode-auth`
   - If not signed in: Shows auth options (Google/GitHub/Email)
   - If signed in: Shows authorization confirmation
   - Click "Authorize Extension"
   - Browser shows success page and redirects to VS Code
   - VS Code receives callback and stores API key

## 6. User Experience Walkthrough

### New User (Not Signed In)

1. **Extension Installation**: User installs and opens extension
2. **Initial Setup**: Sees onboarding screen, clicks "Sign in with Browser"
3. **Browser Opens**: Goes to VoiceHype website auth page
4. **Sign In**: User chooses Google, GitHub, or Email sign-in
5. **Authorization**: After signing in, sees "Authorize VS Code Extension" confirmation
6. **Confirmation**: Clicks "Authorize Extension"
7. **Completion**: Browser shows success page, redirects to VS Code
8. **Ready**: Extension is now authenticated and ready to use

### Existing User (Already Signed In)

1. **Extension Installation**: User installs and opens extension
2. **Initial Setup**: Sees onboarding screen, clicks "Sign in with Browser"
3. **Browser Opens**: Goes to VoiceHype website, user is already signed in
4. **Authorization**: Immediately sees "Authorize VS Code Extension" confirmation
5. **Confirmation**: Clicks "Authorize Extension"
6. **Completion**: Browser shows success page, redirects to VS Code
7. **Ready**: Extension is now authenticated and ready to use

## 7. Advantages of This Approach

### For Users
- **Familiar Experience**: Uses the same sign-in flow they know from your website
- **No Duplicate Accounts**: Uses their existing VoiceHype account
- **Consistent Branding**: Stays within VoiceHype ecosystem
- **Multiple Auth Options**: Can choose Google, GitHub, or Email

### For Development
- **Simplified Architecture**: Only one edge function instead of two
- **Reuses Existing Auth**: Leverages your current OAuth setup
- **Easier Maintenance**: Less code to maintain and debug
- **Better Integration**: Tight integration with your existing user system

## 8. Troubleshooting

### Common Issues

1. **Website Route Not Found**:
   - Verify the VoiceHype website is deployed with the new route
   - Check that `VSCodeAuthView.vue` is properly imported
   - Ensure router includes the `/vscode-auth` route

2. **API Key Creation Fails**:
   - Check Supabase function logs: `supabase functions logs vscode-extension-auth`
   - Verify database migrations are applied
   - Ensure user has permission to create API keys

3. **Extension Not Receiving Callback**:
   - Check VS Code extension URI handler registration
   - Verify the redirect URL format matches expected pattern
   - Check browser security settings

### Debug Commands

```powershell
# Check function logs
supabase functions logs vscode-extension-auth

# Test function locally
supabase functions serve vscode-extension-auth

# Check website route (after deployment)
curl https://voicehype.ai/vscode-auth
```

## 9. Security Considerations

### API Key Security
- **Always New Keys**: System creates a new API key on each authorization
- **Limited Expiration**: API keys expire after 1 year
- **Secure Storage**: Keys stored in VS Code's secure secrets storage
- **Hash Validation**: Keys hashed using bcrypt for database storage

### Website Security
- **Session-Based**: Uses existing website session management
- **CSRF Protection**: State parameter prevents CSRF attacks
- **User Consent**: Clear authorization confirmation before creating API key

## 10. Deployment Checklist

- [ ] Database migrations applied
- [ ] Edge function `vscode-extension-auth` deployed
- [ ] VoiceHype website updated with new route
- [ ] Website deployed to production
- [ ] VS Code extension tested in development
- [ ] End-to-end authentication flow tested
- [ ] Error handling tested (denied authorization, network errors)

## Conclusion

This simplified approach provides a much better user experience by leveraging your existing VoiceHype website authentication system. Users get a familiar, branded experience while developers maintain a simpler, more integrated codebase.

The flow is now optimized for your actual infrastructure and user base, making it both more maintainable and more user-friendly.

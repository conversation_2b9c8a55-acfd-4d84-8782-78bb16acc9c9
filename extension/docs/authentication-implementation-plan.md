# VS Code Extension Authentication Implementation Plan

## Overview

This document outlines the plan for implementing a modern browser-based authentication flow for the Voice Hype VS Code extension, similar to what's used in Cursor, Root Code, and Augment Code extensions.

## Current State Analysis

- **API Key Storage**: Currently using `SecretsService` for secure API key storage
- **Authentication**: Basic `AuthenticationService` already exists but appears incomplete
- **Webview UI**: 
  - React webview with `ApiKeyInput` component
  - Vue.js webview with `ApiKeyInput` component
- **Backend**: Self-hosted Supabase on DigitalOcean using Docker

## Implementation Plan

### 1. Update Authentication Service ✅

The `AuthenticationService` class is already structured correctly but needs to be properly integrated with the main extension. Key features:

- OAuth flow handling with PKCE (Proof Key for Code Exchange)
- VS Code URI handler for browser callbacks
- API key validation and secure storage
- User profile management

### 2. Supabase Edge Function ⬜

Create a Supabase Edge function to:
- Handle authentication requests
- Generate API keys for authenticated users
- Return user profile data

```typescript
// Example Edge Function (to be implemented in Supabase)
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle OPTIONS for CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders, status: 204 })
  }

  // Parse request
  const { code, redirect_uri } = await req.json()
  
  // Create admin client
  const supabase = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  )
  
  try {
    // Exchange code for session
    const { data, error } = await supabase.auth.exchangeCodeForSession(code)
    
    if (error) throw error
    
    // Get user data
    const user = data.user
    
    // Generate or retrieve API key for the user
    const { data: apiKey, error: apiKeyError } = await supabase
      .from('api_keys')
      .select('key')
      .eq('user_id', user.id)
      .eq('is_active', true)
      .maybeSingle()
    
    if (apiKeyError) throw apiKeyError
    
    // Create new API key if none exists
    let keyValue = apiKey?.key
    if (!keyValue) {
      keyValue = crypto.randomUUID()
      // Store new API key
      await supabase.from('api_keys').insert({
        user_id: user.id,
        key: keyValue,
        name: 'VS Code Extension',
        is_active: true
      })
    }
    
    // Return user data and API key
    return new Response(
      JSON.stringify({
        user_id: user.id,
        email: user.email,
        api_key: keyValue
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 200 }
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
    )
  }
})
```

### 3. Update VoiceHypeExtension Class ✅

Integrate the authentication service properly:

```typescript
// Add to VoiceHypeExtension class properties
private authenticationService: AuthenticationService;

// Initialize in constructor
this.authenticationService = new AuthenticationService(
  context,
  this.secretsService,
  this.configService
);

// Update getService method
public getService(serviceName: string): any {
  switch (serviceName) {
    // ... existing cases ...
    case 'authService':
      return this.authenticationService;
    default:
      return undefined;
  }
}
```

### 4. Create Onboarding Screen ✅

Update the webview UI to show an onboarding screen when no API key is present:

#### React Webview

Created a new `OnboardingScreen.tsx` component that provides:
- "Sign in with Browser" button to initiate OAuth flow
- "Enter API Key Manually" alternative option
- Link to sign up at voicehype.ai for new users

```tsx
import * as React from 'react';
import { vscode } from '../utilities/vscode';

const OnboardingScreen: React.FC = () => {
  const handleSignIn = () => {
    vscode.postMessage({
      command: 'authenticate'
    });
  };

  const handleManualApiKey = () => {
    vscode.postMessage({
      command: 'showApiKeyInput'
    });
  };

  return (
    <div className="flex flex-col items-center justify-center h-full p-6 text-center">
      <div className="mb-8">
        <img src="voicehype-logo.png" alt="Voice Hype Logo" className="w-32 h-32" />
        <h1 className="text-2xl font-bold mt-4">Welcome to Voice Hype</h1>
        <p className="mt-2 text-gray-400">
          Voice-to-prompt productivity tool for developers
        </p>
      </div>
      
      <div className="w-full max-w-md space-y-4">
        <button 
          onClick={handleSignIn}
          className="w-full py-3 px-4 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium flex items-center justify-center"
        >
          <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 0C4.477 0 0 4.477 0 10c0 4.42 2.865 8.166 6.839 9.489.5.092.682-.217.682-.482 0-.237-.008-.866-.013-1.7-2.782.603-3.369-1.34-3.369-1.34-.454-1.157-1.11-1.465-1.11-1.465-.908-.62.069-.608.069-.608 1.003.07 1.531 1.03 1.531 1.03.892 1.529 2.341 1.087 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.11-4.555-4.943 0-1.091.39-1.984 1.029-2.683-.103-.253-.446-1.27.098-2.647 0 0 .84-.268 2.75 1.026A9.578 9.578 0 0110 4.836c.85.004 1.705.114 2.504.336 1.909-1.294 2.747-1.026 2.747-1.026.546 1.377.203 2.394.1 2.647.64.699 1.028 1.592 1.028 2.683 0 3.842-2.339 4.687-4.566 4.934.359.309.678.919.678 1.852 0 1.336-.012 2.415-.012 2.743 0 .267.18.578.688.48C17.14 18.163 20 14.418 20 10 20 4.477 15.523 0 10 0z" clipRule="evenodd" />
          </svg>
          Sign in with Browser
        </button>
        
        <div className="relative my-4">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-600"></div>
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-[#1e1e1e] text-gray-400">or</span>
          </div>
        </div>
        
        <button 
          onClick={handleManualApiKey}
          className="w-full py-3 px-4 bg-transparent border border-gray-600 hover:bg-gray-800 text-white rounded-lg font-medium"
        >
          Enter API Key Manually
        </button>
      </div>
      
      <p className="mt-8 text-sm text-gray-400">
        Don't have an account? <a href="#" className="text-blue-400 hover:underline" onClick={() => vscode.postMessage({ command: 'openExternalLink', url: 'https://voicehype.ai/signup' })}>Sign up at voicehype.ai</a>
      </p>
    </div>
  );
};

export default OnboardingScreen;
```

#### Vue.js Webview

Created a new `OnboardingScreen.vue` component with the same functionality as the React version.

```vue
<script setup lang="ts">
import { vscode } from '../utilities/vscode'

const handleSignIn = () => {
  vscode.postMessage({
    command: 'authenticate'
  })
}

const handleManualApiKey = () => {
  vscode.postMessage({
    command: 'showApiKeyInput'
  })
}

const openExternalLink = (url: string) => {
  vscode.postMessage({
    command: 'openExternalLink',
    url
  })
}
</script>

<template>
  <div class="flex flex-col items-center justify-center h-full p-6 text-center">
    <div class="mb-8">
      <img src="voicehype-logo.png" alt="Voice Hype Logo" class="w-32 h-32" />
      <h1 class="text-2xl font-bold mt-4">Welcome to Voice Hype</h1>
      <p class="mt-2 text-gray-400">
        Voice-to-prompt productivity tool for developers
      </p>
    </div>
    
    <div class="w-full max-w-md space-y-4">
      <button 
        @click="handleSignIn"
        class="w-full py-3 px-4 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium flex items-center justify-center"
      >
        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 0C4.477 0 0 4.477 0 10c0 4.42 2.865 8.166 6.839 9.489.5.092.682-.217.682-.482 0-.237-.008-.866-.013-1.7-2.782.603-3.369-1.34-3.369-1.34-.454-1.157-1.11-1.465-1.11-1.465-.908-.62.069-.608.069-.608 1.003.07 1.531 1.03 1.531 1.03.892 1.529 2.341 1.087 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.11-4.555-4.943 0-1.091.39-1.984 1.029-2.683-.103-.253-.446-1.27.098-2.647 0 0 .84-.268 2.75 1.026A9.578 9.578 0 0110 4.836c.85.004 1.705.114 2.504.336 1.909-1.294 2.747-1.026 2.747-1.026.546 1.377.203 2.394.1 2.647.64.699 1.028 1.592 1.028 2.683 0 3.842-2.339 4.687-4.566 4.934.359.309.678.919.678 1.852 0 1.336-.012 2.415-.012 2.743 0 .267.18.578.688.48C17.14 18.163 20 14.418 20 10 20 4.477 15.523 0 10 0z" clip-rule="evenodd" />
        </svg>
        Sign in with Browser
      </button>
      
      <div class="relative my-4">
        <div class="absolute inset-0 flex items-center">
          <div class="w-full border-t border-gray-600"></div>
        </div>
        <div class="relative flex justify-center text-sm">
          <span class="px-2 bg-[#1e1e1e] text-gray-400">or</span>
        </div>
      </div>
      
      <button 
        @click="handleManualApiKey"
        class="w-full py-3 px-4 bg-transparent border border-gray-600 hover:bg-gray-800 text-white rounded-lg font-medium"
      >
        Enter API Key Manually
      </button>
    </div>
    
    <p class="mt-8 text-sm text-gray-400">
      Don't have an account? 
      <a href="#" class="text-blue-400 hover:underline" @click="openExternalLink('https://voicehype.ai/signup')">
        Sign up at voicehype.ai
      </a>
    </p>
  </div>
</template>
```

### 5. Update VoiceHypePanel Service ⬜

Add authentication message handling to the webview panel:

```typescript
// Add to _onDidReceiveMessage method in VoiceHypePanel
private _onDidReceiveMessage(message: any) {
  // ... existing message handling ...
  
  switch (message.command) {
    // ... existing cases ...
    
    case 'authenticate':
      // Get auth service and start sign-in flow
      const authService = this._extensionContext.getService('authService');
      if (authService) {
        authService.signIn().catch(err => {
          console.error('Authentication failed:', err);
          this._webviewView?.webview.postMessage({
            command: 'authError',
            error: err.message
          });
        });
      }
      break;
      
    case 'showApiKeyInput':
      // Show API key input instead of onboarding screen
      this._webviewView?.webview.postMessage({
        command: 'showApiKeyInput'
      });
      break;
      
    case 'openExternalLink':
      if (message.url) {
        vscode.env.openExternal(vscode.Uri.parse(message.url));
      }
      break;
  }
}
```

### 6. Update CommandService ⬜

Add authentication-related commands:

```typescript
// Add to registerCommands method in CommandService
public registerCommands(): void {
  // ... existing command registrations ...
  
  // Register authentication commands
  this._context.subscriptions.push(
    vscode.commands.registerCommand('voicehype.signIn', async () => {
      const authService = this._context.getService('authService');
      if (authService) {
        try {
          await authService.signIn();
          vscode.window.showInformationMessage('Successfully signed in to VoiceHype!');
        } catch (error) {
          vscode.window.showErrorMessage(`Failed to sign in: ${error.message}`);
        }
      }
    }),
    
    vscode.commands.registerCommand('voicehype.signOut', async () => {
      const authService = this._context.getService('authService');
      if (authService) {
        try {
          await authService.signOut();
          vscode.window.showInformationMessage('Successfully signed out from VoiceHype.');
        } catch (error) {
          vscode.window.showErrorMessage(`Failed to sign out: ${error.message}`);
        }
      }
    })
  );
}
```

### 7. Update package.json ⬜

Add authentication-related commands to package.json:

```json
"commands": [
  // ... existing commands ...
  {
    "command": "voicehype.signIn",
    "title": "Sign In",
    "category": "VoiceHype"
  },
  {
    "command": "voicehype.signOut",
    "title": "Sign Out",
    "category": "VoiceHype"
  }
]
```

## TODO List

- [x] Analyze existing `AuthenticationService` implementation
- [x] Create implementation plan document (this file)
- [x] Create Supabase Edge Function for authentication (already existed!)
- [x] Update `VoiceHypeExtension` class to integrate authentication service
- [x] Implement onboarding screen in React webview
- [x] Implement onboarding screen in Vue.js webview
- [x] Update package.json with new commands (already existed!)
- [x] Add authentication commands to `CommandService`
- [x] Update `VoiceHypePanel` to handle authentication messages
- [ ] Test authentication flow end-to-end
- [x] Document the authentication flow

## Technical Details

### Authentication Flow

1. User clicks "Sign In with Browser" button in the extension
2. Extension calls `authService.signIn()`
3. Authentication service generates a random state for PKCE
4. Browser opens to `https://supabase.voicehype.ai/auth/v1/authorize` with appropriate parameters
5. User authenticates in the browser
6. Browser redirects to callback URI with state and code
7. VS Code intercepts the URI via URI handler
8. Authentication service validates state and exchanges code for session
9. Edge function generates/retrieves API key for the user
10. API key is stored securely in extension's secret storage
11. Extension UI updates to show authenticated state

### Security Considerations

- PKCE (Proof Key for Code Exchange) for secure authorization code flow
- Secure storage of API keys using VS Code's SecretStorage API
- State validation to prevent CSRF attacks
- API key validation on startup and periodic refreshing

### Data Flow

1. **VS Code Extension → Browser**: Initial auth request with state
2. **Browser → Supabase Auth**: User authentication
3. **Supabase Auth → VS Code**: Callback with auth code
4. **VS Code → Supabase Edge Function**: Code exchange for token
5. **Supabase Edge Function → Supabase Database**: API key creation/retrieval
6. **Supabase Edge Function → VS Code**: User data and API key
7. **VS Code → Supabase API**: Subsequent API calls using the API key

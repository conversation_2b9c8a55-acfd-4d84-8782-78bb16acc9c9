# Understanding the Authentication Flow

This document explains in detail how the browser-to-VS Code authentication flow works in the Voice Hype extension.

## Overview

The Voice Hype extension implements a modern authentication flow similar to other VS Code extensions like Cursor, Roo Code, and Augment Code. This approach eliminates the need for users to manually copy API keys from the website to the extension.

## Authentication Flow Explained

Here's a step-by-step explanation of how the entire authentication process works:

### 1. User Initiates Sign-In

When a user opens the Voice Hype extension for the first time (or after signing out), they'll see the onboarding screen with two options:
- **Sign in with <PERSON><PERSON><PERSON>** (recommended)
- **Enter API Key Manually** (alternative)

If they click "Sign in with <PERSON><PERSON><PERSON>", the extension will call `authenticationService.signIn()`.

### 2. Extension Prepares Authentication Request

The `AuthenticationService` class handles the authentication flow:

1. It generates a random state parameter (a security measure to prevent CSRF attacks)
2. It builds an authentication URL for Supabase with these parameters:
   - The redirect URI (`vscode://voicehype.voicehype/auth-callback`)
   - The state parameter
   - Required scopes

### 3. Browser Authentication

The extension opens the user's default browser to the Supabase authentication page:
```
https://supabase.voicehype.ai/auth/v1/authorize?
  response_type=code
  &client_id=your-client-id
  &redirect_uri=vscode://voicehype.voicehype/auth-callback
  &state=random-state-parameter
  &scope=openid+email+profile
```

The user enters their credentials on this page or creates a new account.

### 4. Redirect Back to VS Code

After successful authentication, Supabase redirects to the callback URI with:
- The authorization code
- The state parameter (same one sent in step 2)

The redirect looks like:
```
vscode://voicehype.voicehype/auth-callback?code=authentication-code&state=random-state-parameter
```

### 5. VS Code URI Handler Intercepts the Callback

VS Code sees the URI starting with `vscode://` and activates the extension's URI handler. The handler in `AuthenticationService` processes the URI:

1. Extracts the authorization code and state
2. Verifies the state matches the one sent in step 2
3. If states match, it proceeds with code exchange

### 6. Exchanging Code for API Key

The extension calls the Supabase Edge Function with the authorization code:
```
POST https://supabase.voicehype.ai/functions/v1/vscode-auth
{
  "code": "authorization-code",
  "redirect_uri": "vscode://voicehype.voicehype/auth-callback"
}
```

### 7. Supabase Edge Function Processes the Request

The Supabase Edge Function:
1. Exchanges the code for a session token using Supabase's `exchangeCodeForSession`
2. Gets the user details from the session
3. Checks if the user already has an API key in the database
4. If not, it generates a new API key and stores it in the database
5. Returns the API key and user profile information to the extension

### 8. Extension Stores the API Key

The `AuthenticationService` receives the API key and user profile:
1. Stores the API key securely using `SecretsService`
2. Updates the user profile information in memory
3. Sets the authentication state to `SignedIn`
4. Notifies the webview that authentication has succeeded

### 9. Webview Updates UI

The webview receives the authentication state change notification and:
1. Updates the UI to show the authenticated state
2. Displays the normal extension functionality instead of the onboarding screen
3. May show user profile information if applicable

## Using the API Key

Once authenticated, the extension:
1. Uses the stored API key for all API requests to the Supabase backend
2. Periodically validates the API key to ensure it's still valid
3. Provides sign-out functionality that clears the stored API key

## Alternative: Manual API Key Entry

If users prefer the manual approach:
1. They click "Enter API Key Manually" on the onboarding screen
2. The extension shows the API key input form
3. Users paste their API key from the Voice Hype dashboard
4. The extension validates and stores the key securely
5. The UI updates to show the authenticated state

## Security Considerations

The authentication flow implements several security best practices:

1. **PKCE (Proof Key for Code Exchange)**: A security extension to OAuth that prevents authorization code interception attacks.

2. **State Parameter**: A random string generated for each authentication request to prevent CSRF attacks.

3. **Secure Storage**: The API key is stored securely using VS Code's `SecretStorage` API, which encrypts the data on disk.

4. **Code Exchange on Server**: The authorization code is exchanged for tokens on the secure server-side, not in the client.

5. **Limited Scope**: The authentication request only asks for the minimum permissions needed.

This modern authentication approach provides a seamless user experience while maintaining high security standards.

# Complete VoiceHype VS Code Extension Authentication Flow

## Overview

This document provides a comprehensive explanation of the complete authentication flow between the VS Code extension, VoiceHype website, and Supabase database, including all data transmission details.

## Simplified Architecture (Current Implementation)

The system now uses a streamlined 3-component architecture:

```
VS Code Extension ──► VoiceHype Website ──► Supabase Database (RPC)
                  ◄──                   ◄──
```

**Benefits of this approach:**
- ✅ Eliminates unnecessary edge function complexity
- ✅ Uses existing website authentication system
- ✅ Maintains all security features
- ✅ Provides consistent user experience

## Complete Step-by-Step Flow

### 1. User Initiates Authentication

**In VS Code Extension:**
- User clicks "Sign in with <PERSON><PERSON><PERSON>" in the webview panel
- `AuthenticationService.signIn()` is called
- A cryptographically secure random `state` parameter is generated for CSRF protection

**Code Location:** `extension/src/services/AuthenticationService.ts`

```typescript
public async signIn(): Promise<void> {
  this._authState = AuthState.SigningIn;
  const state = randomBytes(32).toString('hex'); // CSRF protection
  const callbackUri = await this._getCallbackUri(); // vscode://VoiceHype.voicehype/auth-callback
  const authUrl = this._buildAuthUrl(state, callbackUri);
  
  // Store the promise to resolve when auth completes
  const authPromise = new Promise((resolve, reject) => {
    this._pendingStates.set(state, { resolve, reject });
  });
  
  // Open browser to VoiceHype website
  await vscode.env.openExternal(vscode.Uri.parse(authUrl));
  
  // Wait for the callback
  const authResult = await authPromise;
  // ... handle result
}
```

### 2. Browser Opens to VoiceHype Website

**URL Structure:**
```
https://voicehype.ai/vscode-auth?state=<random_state>&redirect_uri=vscode://VoiceHype.voicehype/auth-callback
```

**Parameters:**
- `state`: Random CSRF protection token
- `redirect_uri`: VS Code callback URI

**Code Location:** `voicehype-website/src/views/VSCodeAuthView.vue`

### 3. Website Authentication Check

The website checks if the user is already authenticated:

```typescript
// Check if user is already signed in
const { data: { session } } = await supabase.auth.getSession()

if (session?.user) {
  user.value = session.user
  isSignedIn.value = true
} else {
  // Show sign-in options (Google, GitHub, Email)
}
```

### 4. User Authentication (if not signed in)

**For Google OAuth:**
```typescript
await supabase.auth.signInWithOAuth({
  provider: 'google',
  options: {
    redirectTo: `${window.location.origin}/vscode-auth?state=${state.value}&redirect_uri=${encodeURIComponent(redirectUri.value)}`
  }
})
```

**For GitHub OAuth:**
```typescript
await supabase.auth.signInWithOAuth({
  provider: 'github',
  options: {
    redirectTo: `${window.location.origin}/vscode-auth?state=${state.value}&redirect_uri=${encodeURIComponent(redirectUri.value)}`
  }
})
```

**For Email/Password:**
```typescript
await supabase.auth.signInWithPassword({
  email: email.value,
  password: password.value
})
```

### 5. Authorization Confirmation

Once authenticated, the user sees a confirmation screen:
- User profile information (name, email)
- Security notice about API key creation
- "Authorize Extension" or "Deny Access" buttons

### 6. API Key Creation (Direct Database Call)

When user clicks "Authorize Extension", the website calls the database directly:

```typescript
async function authorizeExtension() {
  // Generate a friendly name for the VS Code extension key
  const name = `VS Code Extension (${new Date().toISOString().split('T')[0]})`
  
  // Call the create_api_key RPC function directly (no edge function needed!)
  const { data: apiKeyData, error: apiKeyError } = await supabase.rpc('create_api_key', {
    p_name: name,
    // Set expiration to 1 year from now for security
    p_expires_at: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()
  })
  
  if (apiKeyError) {
    throw new Error(`Failed to create API key: ${apiKeyError.message}`)
  }
  
  // Add the vhkey_ prefix to the generated key
  const fullApiKey = `vhkey_${apiKeyData.key_secret}`
  
  // ... continue with redirect
}
```

### 7. Database RPC Function Execution

**Function:** `create_api_key(p_name TEXT, p_expires_at TIMESTAMPTZ)`

**Security Features:**
- **Authentication Check:** `auth.uid()` ensures user is authenticated
- **API Key Limit:** Maximum 25 keys per user enforced
- **Secure Generation:** `gen_random_bytes(24)` for cryptographic randomness
- **Hashing:** Blowfish encryption with `crypt(v_key_secret, gen_salt('bf'))`
- **Expiration Limit:** Maximum 1 year from creation date
- **RLS Policies:** Row Level Security ensures users only access their own keys

**Database Function Logic:**
```sql
-- Get the current user's ID from auth context
v_user_id := auth.uid();

-- Check if user is authenticated
IF v_user_id IS NULL THEN
    RAISE EXCEPTION 'Not authenticated';
END IF;

-- Check API key limit (25 keys maximum)
SELECT COUNT(*) INTO v_key_count FROM public.api_keys WHERE user_id = v_user_id;
IF v_key_count >= 25 THEN
    RAISE EXCEPTION 'Maximum number of API keys (25) reached for this user';
END IF;

-- Generate a secure API key
v_key_secret := encode(gen_random_bytes(24), 'base64');
v_key_prefix := substring(v_key_secret from 1 for 8);
v_key_hash := crypt(v_key_secret, gen_salt('bf'));

-- Set expiration date to one year from creation if not provided
IF p_expires_at IS NULL THEN
    v_final_expires_at := v_created_at + INTERVAL '1 year';
ELSE
    -- Limit expiration date to a maximum of 1 year from creation
    v_final_expires_at := LEAST(p_expires_at, v_max_expires_at);
END IF;

-- Insert the new API key
INSERT INTO public.api_keys (...)
RETURNING id INTO v_api_key_id;

-- Return both the ID and the key secret
v_result.id := v_api_key_id;
v_result.key_secret := v_key_secret;
RETURN v_result;
```

### 8. Website Creates Redirect Response

The website creates an HTML page that automatically redirects to VS Code:

```typescript
// Build the redirect URL back to VS Code
const redirectUrl = new URL(redirectUri.value) // vscode://VoiceHype.voicehype/auth-callback

// Add the parameters for the VS Code extension
redirectUrl.searchParams.append('state', state.value)
redirectUrl.searchParams.append('api_key', fullApiKey) // vhkey_<generated_key>
redirectUrl.searchParams.append('user_id', user.value.id)
redirectUrl.searchParams.append('email', user.value.email || '')

// Add optional user metadata if available
if (fullName) {
  redirectUrl.searchParams.append('full_name', fullName)
}
if (avatarUrl) {
  redirectUrl.searchParams.append('avatar_url', avatarUrl)
}

// Create an HTML page with automatic redirect to VS Code
const html = `...success page with JavaScript redirect...`
document.write(html)
```

**Final Redirect URL Example:**
```
vscode://VoiceHype.voicehype/auth-callback?state=abc123&api_key=vhkey_AbCdEfGh12345678&user_id=uuid&email=<EMAIL>&full_name=John%20Doe&avatar_url=https://...
```

### 9. VS Code URI Handler Processes Callback

VS Code intercepts the `vscode://` URI and calls the registered URI handler:

```typescript
private async _handleAuthRedirect(uri: vscode.Uri): Promise<void> {
  const query = new URLSearchParams(uri.query);
  const state = query.get('state');
  const apiKey = query.get('api_key');
  const error = query.get('error');
  
  // Validate state parameter for CSRF protection
  const pendingAuth = this._pendingStates.get(state);
  if (!pendingAuth) {
    console.error('No pending authentication found for state:', state);
    return;
  }
  
  // Remove the pending state
  this._pendingStates.delete(state);
  
  if (error) {
    pendingAuth.reject(new Error(`Authentication error: ${error}`));
    return;
  }
  
  // Extract user information from query params
  const userId = query.get('user_id') || '';
  const email = query.get('email') || '';
  const fullName = query.get('full_name') || undefined;
  const avatarUrl = query.get('avatar_url') || undefined;
  
  // Resolve the authentication promise with all data
  pendingAuth.resolve({
    apiKey,
    userId,
    email,
    fullName,
    avatarUrl
  });
}
```

### 10. Extension Stores Credentials Securely

After receiving the authentication data:

```typescript
// Store API key securely using VS Code's SecretStorage
await this._secretsService.storeApiKey(authResult.apiKey);

// Update user profile in memory
this._userProfile = {
  id: authResult.userId,
  email: authResult.email,
  fullName: authResult.fullName,
  avatarUrl: authResult.avatarUrl
};

// Update authentication state
this._authState = AuthState.SignedIn;

// Notify listeners (webview panel, etc.)
this._authStateChangeEmitter.fire(this._authState);
```

### 11. Webview Panel Updates

The webview panel receives the authentication status update:

```typescript
// In VoiceHypePanel
this._view?.webview.postMessage({
  command: 'authStatusUpdated',
  authenticated: true,
  userProfile: userProfile
});
```

## Data Transmission Summary

### VS Code → Website
- `state`: CSRF protection token
- `redirect_uri`: VS Code callback URI (`vscode://VoiceHype.voicehype/auth-callback`)

### Website → Database (RPC)
- `p_name`: API key name (e.g., "VS Code Extension (2024-01-15)")
- `p_expires_at`: Expiration timestamp (1 year from creation)

### Database → Website
- `id`: UUID of created API key record
- `key_secret`: Base64-encoded random key (without prefix)

### Website → VS Code
- `state`: Same CSRF token for validation
- `api_key`: Full API key with `vhkey_` prefix
- `user_id`: User's UUID
- `email`: User's email address
- `full_name`: User's display name (optional)
- `avatar_url`: User's profile picture URL (optional)

## Security Features

### 1. CSRF Protection
- Random `state` parameter prevents cross-site request forgery
- State validation ensures the callback matches the original request

### 2. Secure API Key Generation
- **Cryptographic Random**: `gen_random_bytes(24)` provides 192 bits of entropy
- **Blowfish Hashing**: `crypt()` with `gen_salt('bf')` for secure storage
- **Prefix Storage**: Only first 8 characters stored for lookup efficiency
- **No Plain Text**: Full key never stored in database

### 3. Access Control
- **Authentication Required**: `auth.uid()` enforces user authentication
- **Row Level Security**: Users can only access their own API keys
- **Function Security**: `SECURITY DEFINER` ensures proper execution context
- **Limited Permissions**: Functions only accessible to authenticated users

### 4. API Key Management
- **Expiration Enforcement**: Maximum 1 year lifespan
- **Usage Tracking**: `last_used_at` timestamp for monitoring
- **Active Status**: `is_active` boolean for enabling/disabling
- **Quantity Limits**: Maximum 25 keys per user

### 5. Secure Storage in VS Code
- **SecretStorage API**: Uses VS Code's secure credential storage
- **OS Integration**: Leverages Windows Credential Manager, macOS Keychain, or Linux Secret Service
- **Encryption**: Credentials encrypted at rest

## Error Handling

### Authentication Errors
- Invalid state parameter
- User denies authorization
- Database errors during key creation
- Network connectivity issues

### Validation Errors
- API key limit exceeded (25 keys)
- Invalid expiration date
- Unauthenticated requests

### Recovery Mechanisms
- Manual API key entry as fallback
- Clear error messages to user
- Retry mechanisms for network issues
- State cleanup for failed authentications

## API Key Validation Flow

When the extension makes API calls, the key is validated using:

```sql
-- The validate_api_key function
CREATE OR REPLACE FUNCTION public.validate_api_key(p_key TEXT)
RETURNS TABLE(user_id UUID, api_key_id UUID) AS $$
DECLARE
    v_key_prefix TEXT;
    v_actual_key TEXT;
BEGIN
    -- Handle keys with or without vhkey_ prefix
    IF starts_with(p_key, 'vhkey_') THEN
        v_actual_key := substring(p_key from 7); -- Remove 'vhkey_' prefix
    ELSE
        v_actual_key := p_key;
    END IF;
    
    -- Extract prefix (first 8 characters)
    v_key_prefix := substring(v_actual_key from 1 for 8);
    
    -- Update last used time and validate
    UPDATE public.api_keys
    SET last_used_at = NOW()
    WHERE key_prefix = v_key_prefix
    AND crypt(v_actual_key, key_hash) = key_hash  -- Verify hash
    AND is_active = TRUE
    AND (expires_at IS NULL OR expires_at > NOW());
    
    -- Return user and key IDs if valid
    RETURN QUERY
    SELECT a.user_id, a.id
    FROM public.api_keys a
    WHERE a.key_prefix = v_key_prefix
    AND crypt(v_actual_key, a.key_hash) = a.key_hash
    AND a.is_active = TRUE
    AND (a.expires_at IS NULL OR a.expires_at > NOW());
END;
$$;
```

## Advantages of Current Architecture

### For Users
- **Familiar Experience**: Uses existing VoiceHype website authentication
- **No Duplicate Accounts**: Integrates with existing user accounts
- **Multiple Auth Options**: Google, GitHub, or email/password
- **Consistent Branding**: Stays within VoiceHype ecosystem

### For Developers
- **Simplified Architecture**: Eliminated unnecessary edge function
- **Reuses Existing Auth**: Leverages current OAuth setup
- **Easier Maintenance**: Less code complexity
- **Better Integration**: Direct database access with proper security

### For Security
- **Production-Ready**: Uses established security patterns
- **Auditable**: Clear data flow and access controls
- **Compliant**: Follows OAuth 2.0 and PKCE best practices
- **Secure by Design**: Multiple layers of protection

This architecture provides a robust, secure, and user-friendly authentication system that scales well and integrates seamlessly with the existing VoiceHype ecosystem.

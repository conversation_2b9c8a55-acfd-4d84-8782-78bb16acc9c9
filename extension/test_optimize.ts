const fs = require('fs');
const path = require('path');

const VOICEHYPE_API_KEY = 'vhkey_ac4b2fc104a96e5d46ed30690ae5d384';
const SUPABASE_PROJECT_URL = 'https://supabase.voicehype.ai';

async function testOptimize() {
    try {
        // Sample text to optimize
        const textToOptimize = `Um, so I was thinking about, you know, the project we discussed last week. It's, uh, going pretty well I think. We've made some progress on the, the frontend components and, like, integrated the API calls. There were some issues with, um, the authentication flow but we, we fixed those. I'm thinking we should probably, you know, focus on the database optimization next because it's running a bit slow. And also maybe look at the, uh, the caching strategy. Yeah, that's pretty much my update for now.`;

        // Generate a request ID to track this request in logs
        const requestId = `test-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;

        console.log(`Making request to optimize function with request ID: ${requestId}...`);
        console.log('Text length:', textToOptimize.length, 'characters');

        // Make request to edge function
        const response = await fetch(
            `${SUPABASE_PROJECT_URL}/functions/v1/optimize`,
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'apikey': VOICEHYPE_API_KEY,
                    'X-Request-ID': requestId
                },
                body: JSON.stringify({
                    text: textToOptimize,
                    // Test with our new default model
                    model: 'claude-4-sonnet',
                    debug: true,  // Add debug flag to get more detailed logs
                    customPrompt: 'Please optimize this text for clarity and conciseness:'
                })
            }
        );

        // Log response headers for debugging
        console.log('Response status:', response.status, response.statusText);
        console.log('Response headers:');
        response.headers.forEach((value, key) => {
            console.log(`  ${key}: ${value}`);
        });

        const responseText = await response.text();
        console.log('Response received, length:', responseText.length, 'characters');

        let responseData;
        try {
            responseData = JSON.parse(responseText);
        } catch (e) {
            console.error('Failed to parse response:', e);
            return;
        }

        if (!response.ok) {
            console.error('Error:', {
                status: response.status,
                statusText: response.statusText,
                data: responseData,
                requestId
            });

            // Try to get more information about the error
            console.log('Checking function logs for request ID:', requestId);
            console.log('Please check Supabase logs with filter:', requestId);
            return;
        }

        console.log('Response structure received successfully');
        console.log('Optimized text length:', responseData?.data?.optimizedText?.length || 0, 'characters');
        console.log('Metadata:', responseData?.data?.metadata);
    } catch (error) {
        console.error('Error:', error);
    }
}

testOptimize();
//@ts-check
'use strict';

const path = require('path');
const fs = require('path');
const TerserPlugin = require('terser-webpack-plugin');

/**@type {import('webpack').Configuration}*/
const config = {
  target: 'node',
  mode: 'production', // Use production mode for marketplace builds

  entry: './src/index.ts',
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'index.js',
    libraryTarget: 'commonjs2'
  },
  externals: {
    vscode: 'commonjs vscode',
    // Don't bundle native modules and their dependencies
    'node-microphone': 'commonjs node-microphone',
    '@voicehype/audify-plus': 'commonjs @voicehype/audify-plus',
    'prebuild-install': 'commonjs prebuild-install',
    // Other core Node.js modules that should not be bundled
    'fs': 'commonjs fs',
    'path': 'commonjs path',
    'os': 'commonjs os',
    'child_process': 'commonjs child_process',
    'events': 'commonjs events',
    'stream': 'commonjs stream',
    'util': 'commonjs util',
    // Include music-metadata in the bundle
    // Don't externalize sox-static packages so they get bundled
    // 'sox-static-macos': 'commonjs sox-static-macos',
    // 'sox-static-linux': 'commonjs sox-static-linux',
  },
  resolve: {
    extensions: ['.ts', '.js']
  },
  module: {
    rules: [
      {
        test: /\.ts$/,
        exclude: /node_modules/,
        use: [
          {
            loader: 'ts-loader'
          }
        ]
      }
    ]
  },
  optimization: {
    minimize: true,
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          compress: {
            drop_console: true, // Remove console.* statements
            drop_debugger: true,  // Remove debugger statements
            pure_funcs: ['console.log', 'console.info', 'console.warn', 'console.error'] // Remove specific console methods
          },
          mangle: {
            keep_fnames: true // Keep function names for debugging
          }
        }
      })
    ]
  },
  plugins: [],
  devtool: 'nosources-source-map',
  infrastructureLogging: {
    level: "log", // enables logging required for problem matchers
  },
};
module.exports = config;
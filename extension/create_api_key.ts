import { createClient } from '@supabase/supabase-js';
import { randomBytes, createHash } from 'crypto';

const SUPABASE_URL = 'https://nffixzoqnqxpcqpcxpps.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5mZml4em9xbnF4cGNxcGN4cHBzIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczOTYzMTM4OCwiZXhwIjoyMDU1MjA3Mzg4fQ.QWNzvxRxmtatPhAx0e-hEDyCyIEJ6v5GgfxjqcobOeU';

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

async function createApiKey() {
    try {
        // Generate a new API key
        const apiKey = `vhkey_${randomBytes(16).toString('hex')}`;
        const keyPrefix = apiKey.split('_')[1].substring(0, 4);
        const keyHash = createHash('sha256').update(apiKey).digest('hex');

        // Create a test user profile if it doesn't exist
        const { data: profileData, error: profileError } = await supabase
            .from('profiles')
            .upsert({
                id: '00000000-0000-0000-0000-000000000000',
                email: '<EMAIL>',
                full_name: 'Test User',
                company_name: 'Test Company'
            })
            .select()
            .single();

        if (profileError) {
            throw new Error(`Failed to create profile: ${profileError.message}`);
        }

        // Insert the API key
        const { data: keyData, error: keyError } = await supabase
            .from('api_keys')
            .insert({
                user_id: profileData.id,
                name: 'Test Key',
                key_prefix: keyPrefix,
                key_hash: keyHash,
                is_active: true,
                created_at: new Date().toISOString()
            })
            .select()
            .single();

        if (keyError) {
            throw new Error(`Failed to create API key: ${keyError.message}`);
        }

        console.log('API key created successfully!');
        console.log('API Key:', apiKey);
        console.log('Key Prefix:', keyPrefix);
        
    } catch (error: any) {
        console.error('Error creating API key:', error.message);
    }
}

// Run the function
createApiKey(); 
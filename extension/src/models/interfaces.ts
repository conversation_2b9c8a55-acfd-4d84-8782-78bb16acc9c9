import * as vscode from 'vscode';

// Error handling types
export type TranscriptionErrorPhase = 'transcription' | 'optimization';

export class TranscriptionError extends Error {
    constructor(
        public readonly phase: TranscriptionErrorPhase,
        public readonly originalError: Error
    ) {
        super(originalError.message);
        this.name = 'TranscriptionError';
        // Preserve the stack trace
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, TranscriptionError);
        }
    }

    public getPhase(): TranscriptionErrorPhase {
        return this.phase;
    }

    public getOriginalError(): Error {
        return this.originalError;
    }
}

// File tracking interfaces
export interface FileTrackingEntry {
    timestamp: number;
    fileName: string;
    filePath: string;
    action: 'opened' | 'closed' | 'selected';
    selection?: {
        startLine: number;
        endLine: number;
    };
}

// History interfaces
export interface RecordingHistoryEntry {
    timestamp: number;
    transcript: string;
    optimizedTranscript?: string;
    fileReferences: FileTrackingEntry[];
    service?: string;
    model?: string;
    language?: string;
    duration?: number;
}

// Language interfaces
export interface Language {
    label: string;
    value: string;
}

// Response interfaces
export interface VoiceHypeResponse {
    transcription: string;
    optimizedText?: string;
    duration: number;
    metadata: {
        audioSize: number;
        estimatedDuration: number;
        actualDuration: number;
        model: string;
        service: string;
    };
}

// Service interfaces
export interface IFileTrackingService {
    startTracking(): void;
    stopTracking(): FileTrackingEntry[];
    dispose(): void;
}

export interface IHistoryService {
    addEntry(entry: RecordingHistoryEntry): void;
    clearHistory(): void;
    getHistory(): RecordingHistoryEntry[];
    updateLastEntryWithOptimization(optimizedText: string): void;
    updateEntryWithOptimization(index: number, optimizedText: string): void;
    getPresets?(): { name: string, prompt: string }[];
    addPreset?(): void;
    updatePreset?(): void;
    deletePreset?(): void;
    showPresetSelector?(): Promise<void>;
    createNewPreset?(): Promise<void>;
    registerCommands(): vscode.Disposable[];
    dispose(): void;
}

export interface IRecordingService {
    isRecording(): boolean;
    isPaused(): boolean;
    getElapsedTime(): number;
    startRecording(shouldOptimize: boolean): Promise<void>;
    stopRecording(shouldOptimize: boolean): Promise<void>;
    pauseRecording(): Promise<void>;
    resumeRecording(): Promise<void>;
    cancelRecording(): Promise<void>;
    onStateChange(listener: (isRecording: boolean, isPaused: boolean, elapsedTime: number) => void): void;
    getAvailableDevices(forceRefresh?: boolean): Promise<Array<{ id: string; name: string }>>;
    getStatusBarService(): IStatusBarService | null;
    playLastRecording(): void;
    pasteOriginalTranscript(): Promise<void>;
    hasRecording(): boolean;
    dispose(): void;
}

export interface ITranscriptionService {
    transcribeAudio(filePath: string, optimize: boolean, translate?: boolean, realtime?: boolean, customPrompt?: string): Promise<{
        transcription: string;
        optimizedText?: string;
        duration: number;
    }>;
    optimizeText(text: string, customPrompt?: string): Promise<string>;
    optimizeWithCustomPrompt(text: string, customPrompt: string): Promise<string>;
    setupRealTimeConnection(): any; // Return type will be RealtimeConnectionManager
    getAudioDuration(audioFilePath: string): Promise<number>;
}

export interface IConfigurationService {
    onDidChangeConfiguration: vscode.Event<string>;
    onDidChangeSampleRate: vscode.Event<number>;
    getApiKey(): string | undefined;
    getApiKeyAsync(): Promise<string | undefined>;
    getTranscriptionModel(): string;
    getTranscriptionService(): string;
    getTranscriptionLanguage(): string;
    getOptimizationModel(): string;
    getShouldOptimize(): boolean;
    getCustomPrompt(): string;
    getTranslate(): boolean;
    getTranscriptionRealtime(): boolean;
    getVoiceCommandsEnabled(): boolean;
    getAudioSettings(): { sampleRate: number; device?: string };
    updateSetting(key: string, value: any): Promise<void>;
    handleRealtimeToggle(isRealtimeEnabled: boolean): Promise<void>;

    // Configuration management methods
    getConfigurations(): any[];
    getActiveConfigurationId(): string | null;
    createConfiguration(data: any): Promise<any>;
    updateConfiguration(data: any): Promise<void>;
    deleteConfiguration(configId: string): Promise<void>;
    switchConfiguration(configId: string): Promise<string>;
    getConfigurationName(configId: string): string;
    getWhatsNewStatus(version: string): boolean;
    markWhatsNewSeen(version: string): Promise<void>;

    dispose(): void;
}

export interface IStatusBarService {
    updateStatusBarItems(isRecording: boolean, startedWithOptimize: boolean, isPaused: boolean): void;
    dispose(): void;
}
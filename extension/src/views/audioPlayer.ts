import * as vscode from 'vscode';
import * as path from 'path';

export class AudioPlayerPanel {
    public static currentPanel: AudioPlayerPanel | undefined;
    private readonly _panel: vscode.WebviewPanel;
    private _disposables: vscode.Disposable[] = [];

    private constructor(
        panel: vscode.WebviewPanel,
        private recordingPath: string,
        private context: vscode.ExtensionContext
    ) {
        this._panel = panel;
        this.updateContent();
        this._panel.onDidDispose(() => this.dispose(), null, this._disposables);
    }

    public static show(recordingPath: string, context: vscode.ExtensionContext) {
        const column = vscode.window.activeTextEditor
            ? vscode.window.activeTextEditor.viewColumn
            : undefined;

        if (AudioPlayerPanel.currentPanel) {
            AudioPlayerPanel.currentPanel._panel.reveal(column);
            AudioPlayerPanel.currentPanel.recordingPath = recordingPath;
            AudioPlayerPanel.currentPanel.updateContent();
            return;
        }

        const panel = vscode.window.createWebviewPanel(
            'audioPlayer',
            'Audio Player',
            column || vscode.ViewColumn.Two,
            {
                enableScripts: true,
                localResourceRoots: [
                    vscode.Uri.file(path.dirname(recordingPath))
                ]
            }
        );

        AudioPlayerPanel.currentPanel = new AudioPlayerPanel(panel, recordingPath, context);
    }

    private updateContent() {
        const audioUri = this._panel.webview.asWebviewUri(
            vscode.Uri.file(this.recordingPath)
        );

        this._panel.webview.html = this.getWebviewContent(audioUri.toString());
    }

    private getWebviewContent(audioUrl: string) {
        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <meta http-equiv="Content-Security-Policy" content="default-src 'none'; media-src ${this._panel.webview.cspSource}; script-src 'unsafe-inline'; style-src 'unsafe-inline';">
            <title>Audio Player</title>
            <style>
                body {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    padding: 20px;
                    font-family: var(--vscode-font-family);
                    color: var(--vscode-foreground);
                    background-color: var(--vscode-editor-background);
                }
                .player-container {
                    width: 100%;
                    max-width: 500px;
                    background: var(--vscode-editor-background);
                    border-radius: 8px;
                    padding: 20px;
                }
                .controls {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    gap: 10px;
                    margin: 10px 0;
                }
                .time-slider {
                    width: 100%;
                    margin: 10px 0;
                }
                .time-display {
                    font-family: monospace;
                    font-size: 14px;
                    text-align: center;
                    margin: 5px 0;
                }
                button {
                    background: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    cursor: pointer;
                }
                button:hover {
                    background: var(--vscode-button-hoverBackground);
                }
                button:disabled {
                    opacity: 0.5;
                    cursor: not-allowed;
                }
                input[type="range"] {
                    width: 100%;
                }
                .error-message {
                    color: var(--vscode-errorForeground);
                    margin: 10px 0;
                    text-align: center;
                }
            </style>
        </head>
        <body>
            <div class="player-container">
                <audio id="audio" src="${audioUrl}" type="audio/wav"></audio>
                <div id="errorMessage" class="error-message" style="display: none;"></div>
                <input type="range" class="time-slider" id="timeSlider" value="0" step="0.1">
                <div class="time-display">
                    <span id="currentTime">0:00</span> / <span id="duration">0:00</span>
                </div>
                <div class="controls">
                    <button id="playPauseBtn">Play</button>
                    <button id="stopBtn">Stop</button>
                    <input type="range" id="volumeSlider" min="0" max="1" step="0.1" value="1">
                </div>
            </div>
            <script>
                const audio = document.getElementById('audio');
                const playPauseBtn = document.getElementById('playPauseBtn');
                const stopBtn = document.getElementById('stopBtn');
                const timeSlider = document.getElementById('timeSlider');
                const volumeSlider = document.getElementById('volumeSlider');
                const currentTimeDisplay = document.getElementById('currentTime');
                const durationDisplay = document.getElementById('duration');
                const errorMessage = document.getElementById('errorMessage');

                audio.addEventListener('error', (e) => {
                    console.error('Audio error:', e);
                    errorMessage.textContent = 'Error playing audio: ' + (e.target.error ? e.target.error.message : 'Unknown error');
                    errorMessage.style.display = 'block';
                    playPauseBtn.disabled = true;
                    stopBtn.disabled = true;
                });

                audio.addEventListener('loadeddata', () => {
                    console.log('Audio loaded successfully');
                    errorMessage.style.display = 'none';
                    playPauseBtn.disabled = false;
                    stopBtn.disabled = false;
                });

                function formatTime(seconds) {
                    const mins = Math.floor(seconds / 60);
                    const secs = Math.floor(seconds % 60);
                    return \`\${mins}:\${secs.toString().padStart(2, '0')}\`;
                }

                audio.addEventListener('loadedmetadata', () => {
                    console.log('Audio metadata loaded:', {
                        duration: audio.duration,
                        readyState: audio.readyState
                    });
                    timeSlider.max = audio.duration;
                    durationDisplay.textContent = formatTime(audio.duration);
                });

                audio.addEventListener('timeupdate', () => {
                    timeSlider.value = audio.currentTime;
                    currentTimeDisplay.textContent = formatTime(audio.currentTime);
                });

                audio.addEventListener('ended', () => {
                    playPauseBtn.textContent = 'Play';
                });

                playPauseBtn.addEventListener('click', () => {
                    if (audio.paused) {
                        const playPromise = audio.play();
                        if (playPromise !== undefined) {
                            playPromise.then(() => {
                                playPauseBtn.textContent = 'Pause';
                            }).catch(error => {
                                console.error('Playback error:', error);
                                errorMessage.textContent = 'Error playing audio: ' + error.message;
                                errorMessage.style.display = 'block';
                            });
                        }
                    } else {
                        audio.pause();
                        playPauseBtn.textContent = 'Play';
                    }
                });

                stopBtn.addEventListener('click', () => {
                    audio.pause();
                    audio.currentTime = 0;
                    playPauseBtn.textContent = 'Play';
                });

                timeSlider.addEventListener('input', () => {
                    audio.currentTime = timeSlider.value;
                });

                volumeSlider.addEventListener('input', () => {
                    audio.volume = volumeSlider.value;
                });

                playPauseBtn.disabled = true;
                stopBtn.disabled = true;
            </script>
        </body>
        </html>`;
    }

    public dispose() {
        AudioPlayerPanel.currentPanel = undefined;
        this._panel.dispose();
        while (this._disposables.length) {
            const disposable = this._disposables.pop();
            if (disposable) {
                disposable.dispose();
            }
        }
    }
} 
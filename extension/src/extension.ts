// The module 'vscode' contains the VS Code extensibility API
// Import the module and reference it with the alias vscode in your code below
import * as vscode from 'vscode';

// Import services
import { VoiceHypeExtension } from './VoiceHypeExtension';


let extensionInstance: VoiceHypeExtension | undefined;

export function activate(context: vscode.ExtensionContext) {
	console.log('VoiceHype: Starting activation...');

	// Create and activate the VoiceHype extension
	extensionInstance = new VoiceHypeExtension(context);
	extensionInstance.activate();
	
	// Register command to notify webviews of auth changes
	context.subscriptions.push(
		vscode.commands.registerCommand('voicehype.notifyAuthChange', (authData) => {
			// Get the VoiceHypePanel service to broadcast to webviews
			const voiceHypePanel = extensionInstance?.getService('voiceHypePanel');
			if (voiceHypePanel && voiceHypePanel._view && voiceHypePanel._view.webview) {
				console.log('Broadcasting auth change to webview:', authData.authenticated ? 'Authenticated' : 'Not authenticated');
				voiceHypePanel._view.webview.postMessage({
					command: 'authStatusUpdated',
					authenticated: authData.authenticated,
					userProfile: authData.userProfile,
					apiKey: authData.apiKey
				});
			}
		})
	);

	// Expose the services via exports
	return {
		getService: (serviceName: string) => extensionInstance?.getService(serviceName)
	};
}

// This method is called when your extension is deactivated
export function deactivate() {
	if (extensionInstance) {
		extensionInstance.dispose();
		extensionInstance = undefined;
	}
}

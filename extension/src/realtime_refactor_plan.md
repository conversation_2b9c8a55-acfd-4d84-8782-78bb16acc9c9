# Real-Time Transcription Refactoring Plan

## Current Architecture
Currently, the extension follows this flow:
1. Record full audio
2. Save to a file
3. Send the full file to `transcribeAudioRealtime()`
4. Establish WebSocket connection
5. Stream the pre-recorded file in chunks
6. Receive transcription
7. Return results

## Target Architecture
We want to change to this flow:
1. Establish WebSocket connection
2. Start recording audio
3. Stream audio chunks to WebSocket as they're captured
4. Receive and display transcription in real-time
5. Stop recording when user is done
6. Finalize the WebSocket session
7. Return the accumulated transcription

## Required Modifications

### 1. Create a New Real-Time Connection Manager Class

```typescript
// src/services/RealtimeConnectionManager.ts

import { WebSocket } from 'ws';
import { TranscriptionStatus } from './TranscriptionService';

export interface AudioChunk {
  data: Uint8Array;
  isLastChunk: boolean;
}

export class RealtimeConnectionManager {
  private ws: WebSocket | null = null;
  private isConnected = false;
  private transcript = '';
  private lastPartialTranscript = '';
  private statusListeners: ((status: TranscriptionStatus, message?: string) => void)[] = [];
  private transcriptionListeners: ((text: string, isFinal: boolean) => void)[] = [];
  private readyListeners: (() => void)[] = [];
  
  constructor(
    private apiKey: string,
    private service: string,
    private model: string,
    private language: string
  ) {}
  
  // Connect to the WebSocket before recording starts
  public async connect(): Promise<void> {
    this.emitStatus(TranscriptionStatus.ConnectingWebSocket);
    
    return new Promise((resolve, reject) => {
      const supabaseUrl = 'nffixzoqnqxpcqpcxpps.supabase.co';
      const wsUrl = `wss://${supabaseUrl}/functions/v1/transcribe/realtime?apiKey=${this.apiKey}&service=${this.service}&model=${this.model}&language=${this.language}`;
      
      console.log(`VoiceHype: Connecting to real-time transcription WebSocket: ${wsUrl}`);
      
      this.ws = new WebSocket(wsUrl);
      
      // Set up a connection timeout
      const connectionTimeout = setTimeout(() => {
        console.error('VoiceHype: Connection timeout after 15 seconds');
        this.emitStatus(TranscriptionStatus.Error, 'Connection timeout after 15 seconds');
        reject(new Error('Connection timeout'));
      }, 15000);
      
      this.ws.on('open', () => {
        console.log('VoiceHype: WebSocket connection opened');
        this.emitStatus(TranscriptionStatus.WebSocketConnected);
        clearTimeout(connectionTimeout);
      });
      
      this.ws.on('message', (data: Buffer) => {
        try {
          const message = data.toString();
          console.log('VoiceHype: WebSocket message received:', message.substring(0, 200));
          
          const parsedData = JSON.parse(message);
          
          if (parsedData.type === 'connected') {
            console.log('VoiceHype: Real-time transcription session established:', parsedData.sessionId);
            this.emitStatus(TranscriptionStatus.SessionEstablished, `Session ID: ${parsedData.sessionId}`);
            this.isConnected = true;
            
            // Notify that we're ready to record
            this.readyListeners.forEach(listener => listener());
            resolve();
          } else if (parsedData.type === 'final' || parsedData.message_type === 'FinalTranscript') {
            // Handle final transcriptions from either OpenAI or AssemblyAI
            const text = parsedData.text || '';
            if (text.trim() !== '') {
              this.transcript += text + ' ';
              this.emitStatus(TranscriptionStatus.TranscriptionReceived, text);
              this.transcriptionListeners.forEach(listener => listener(text, true));
            }
          } else if (parsedData.type === 'partial' || parsedData.message_type === 'PartialTranscript') {
            // Handle partial transcriptions
            const text = parsedData.text || '';
            if (text.trim() !== '') {
              this.lastPartialTranscript = text;
              this.transcriptionListeners.forEach(listener => listener(text, false));
            }
          } else if (parsedData.type === 'error') {
            console.error('VoiceHype: Real-time transcription error:', parsedData.message);
            this.emitStatus(TranscriptionStatus.Error, parsedData.message);
          } else if (parsedData.type === 'timeout') {
            console.log('VoiceHype: Real-time transcription timeout:', parsedData.message);
            this.emitStatus(TranscriptionStatus.Error, `Timeout: ${parsedData.message}`);
          } else if (parsedData.type === 'service_disconnected') {
            console.log('VoiceHype: Real-time transcription service disconnected:', parsedData.reason);
            this.emitStatus(TranscriptionStatus.Error, `Service disconnected: ${parsedData.reason}`);
            this.isConnected = false;
          }
        } catch (error) {
          console.error('VoiceHype: Error parsing WebSocket message:', error);
        }
      });
      
      this.ws.on('error', (error) => {
        console.error('VoiceHype: WebSocket error:', error);
        this.emitStatus(TranscriptionStatus.Error, `WebSocket error: ${error.message}`);
        this.isConnected = false;
        clearTimeout(connectionTimeout);
        reject(error);
      });
      
      this.ws.on('close', (code, reason) => {
        console.log(`VoiceHype: WebSocket connection closed with code ${code}, reason: ${reason || 'None provided'}`);
        
        // If we have no final transcript but have partial ones, use the last partial
        if (this.transcript.trim() === '' && this.lastPartialTranscript.trim() !== '') {
          console.log(`VoiceHype: No final transcript received, using last partial: "${this.lastPartialTranscript}"`);
          this.transcript = this.lastPartialTranscript;
        }
        
        this.isConnected = false;
        this.emitStatus(TranscriptionStatus.TranscriptionComplete);
      });
    });
  }
  
  // Send audio chunk as it's captured
  public sendAudioChunk(chunk: AudioChunk): void {
    if (!this.ws || !this.isConnected || this.ws.readyState !== WebSocket.OPEN) {
      console.log('VoiceHype: Cannot send audio chunk: WebSocket not connected');
      return;
    }
    
    try {
      this.ws.send(Buffer.from(chunk.data));
      
      if (chunk.isLastChunk) {
        console.log('VoiceHype: Sending final audio chunk, closing session');
        
        // Send close message after a short delay to ensure processing
        setTimeout(() => {
          if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            console.log('VoiceHype: Sending close message to server');
            this.ws.send(JSON.stringify({ type: 'close' }));
          }
        }, 1000);
      }
    } catch (error) {
      console.error('VoiceHype: Error sending audio chunk:', error);
      this.emitStatus(TranscriptionStatus.Error, 
        `Error sending audio: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
  
  // Close the connection
  public close(): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.close();
    }
  }
  
  // Get the current transcript
  public getTranscript(): string {
    return this.transcript.trim();
  }
  
  // Event listeners
  public onStatusChange(listener: (status: TranscriptionStatus, message?: string) => void): void {
    this.statusListeners.push(listener);
  }
  
  public onTranscriptionUpdate(listener: (text: string, isFinal: boolean) => void): void {
    this.transcriptionListeners.push(listener);
  }
  
  public onReady(listener: () => void): void {
    this.readyListeners.push(listener);
    
    // If already connected, call immediately
    if (this.isConnected) {
      listener();
    }
  }
  
  private emitStatus(status: TranscriptionStatus, message?: string): void {
    console.log(`VoiceHype: Status update - ${status}${message ? `: ${message}` : ''}`);
    this.statusListeners.forEach(listener => listener(status, message));
  }
}
```

### 2. Modify the Audio Recording Service

We need to refactor the audio recording service to accept a callback for processing audio chunks in real-time:

```typescript
// src/services/AudioRecordingService.ts

export class AudioRecordingService {
  private mediaRecorder: MediaRecorder | null = null;
  private chunks: Blob[] = [];
  private chunkCallback: ((chunk: AudioChunk) => void) | null = null;
  
  // Modified to accept a real-time chunk callback
  public startRecording(chunkCallback?: (chunk: AudioChunk) => void): void {
    this.chunks = [];
    this.chunkCallback = chunkCallback || null;
    
    // Set up audio recording with the Web Audio API
    navigator.mediaDevices.getUserMedia({ audio: true })
      .then(stream => {
        this.mediaRecorder = new MediaRecorder(stream);
        
        this.mediaRecorder.ondataavailable = (event) => {
          if (event.data.size > 0) {
            this.chunks.push(event.data);
            
            // If real-time transcription is enabled, convert and forward the chunk
            if (this.chunkCallback) {
              this.blobToArrayBuffer(event.data).then(buffer => {
                const chunk: AudioChunk = {
                  data: new Uint8Array(buffer),
                  isLastChunk: false
                };
                this.chunkCallback!(chunk);
              });
            }
          }
        };
        
        // Set a suitable timeslice (e.g., 100ms) to get frequent chunks
        this.mediaRecorder.start(100);
      })
      .catch(error => {
        console.error('VoiceHype: Error starting audio recording:', error);
      });
  }
  
  public stopRecording(): Promise<Blob> {
    return new Promise((resolve) => {
      if (!this.mediaRecorder) {
        resolve(new Blob([]));
        return;
      }
      
      this.mediaRecorder.onstop = () => {
        const audioBlob = new Blob(this.chunks, { type: 'audio/wav' });
        
        // Send a final chunk if real-time transcription is enabled
        if (this.chunkCallback) {
          this.blobToArrayBuffer(this.chunks[this.chunks.length - 1]).then(buffer => {
            const chunk: AudioChunk = {
              data: new Uint8Array(buffer),
              isLastChunk: true
            };
            this.chunkCallback!(chunk);
          });
        }
        
        resolve(audioBlob);
      };
      
      this.mediaRecorder.stop();
    });
  }
  
  private blobToArrayBuffer(blob: Blob): Promise<ArrayBuffer> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as ArrayBuffer);
      reader.onerror = reject;
      reader.readAsArrayBuffer(blob);
    });
  }
}
```

### 3. Update TranscriptionService to Support Real-Time Mode

```typescript
// src/services/TranscriptionService.ts (modified)

import { RealtimeConnectionManager } from './RealtimeConnectionManager';

export class TranscriptionService implements ITranscriptionService {
  // ... existing code ...
  
  // New method to set up real-time transcription
  public setupRealTimeConnection(): RealtimeConnectionManager {
    const service = this.configService.getTranscriptionService();
    const model = this.configService.getTranscriptionModel();
    const language = this.configService.getTranscriptionLanguage();
    const apiKey = this.configService.getApiKey();
    
    if (!apiKey) {
      throw new Error('API key not set');
    }
    
    return new RealtimeConnectionManager(apiKey, service, model, language);
  }
  
  // Keep the existing transcribeAudio method for backward compatibility
  // but modify it to use the new flow when realtime is true
  async transcribeAudio(
    filePath: string, 
    optimize: boolean = false, 
    translate: boolean = false,
    realtime: boolean = false
  ): Promise<{ 
    transcription: string;
    optimizedText?: string;
    duration: number;
  }> {
    // ... existing code for non-realtime path ...
  }
}
```

### 4. Update the Main Command Handler

```typescript
// src/commands/StartRecordingCommand.ts

import { RealtimeConnectionManager } from '../services/RealtimeConnectionManager';
import { AudioRecordingService } from '../services/AudioRecordingService';
import { TranscriptionService } from '../services/TranscriptionService';

export class StartRecordingCommand {
  private realtimeConnection: RealtimeConnectionManager | null = null;
  
  constructor(
    private audioRecordingService: AudioRecordingService,
    private transcriptionService: TranscriptionService
  ) {}
  
  public async execute(realtime: boolean = false): Promise<void> {
    try {
      if (realtime) {
        // Real-time transcription flow
        await this.startRealtimeRecording();
      } else {
        // Regular recording flow
        await this.startRegularRecording();
      }
    } catch (error) {
      console.error('VoiceHype: Error in recording command:', error);
      vscode.window.showErrorMessage(`Recording error: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
  
  private async startRealtimeRecording(): Promise<void> {
    // Show "Establishing connection" notification
    vscode.window.showInformationMessage('Establishing connection, please wait...');
    
    // 1. Set up real-time connection
    this.realtimeConnection = this.transcriptionService.setupRealTimeConnection();
    
    // 2. Register status listeners
    this.realtimeConnection.onStatusChange((status, message) => {
      switch (status) {
        case TranscriptionStatus.SessionEstablished:
          vscode.window.showInformationMessage('Connection established, start speaking!');
          break;
        case TranscriptionStatus.Error:
          vscode.window.showErrorMessage(`Error: ${message}`);
          break;
        // Add other status handlers as needed
      }
    });
    
    // 3. Setup transcription update listener (optional)
    this.realtimeConnection.onTranscriptionUpdate((text, isFinal) => {
      if (isFinal) {
        // Could update a status bar or other UI element
        console.log('Final transcription received:', text);
      } else {
        // Could show partial results in real-time
        console.log('Partial transcription:', text);
      }
    });
    
    // 4. Connect to service
    await this.realtimeConnection.connect();
    
    // 5. When connection is ready, start recording
    this.realtimeConnection.onReady(() => {
      this.audioRecordingService.startRecording((chunk) => {
        // This callback sends each audio chunk as it's captured
        if (this.realtimeConnection) {
          this.realtimeConnection.sendAudioChunk(chunk);
        }
      });
    });
  }
  
  private async startRegularRecording(): Promise<void> {
    // Existing non-realtime flow
    this.audioRecordingService.startRecording();
  }
  
  public async stopRecording(): Promise<string> {
    // Stop the recording
    const audioBlob = await this.audioRecordingService.stopRecording();
    
    if (this.realtimeConnection) {
      // For real-time mode, get the accumulated transcript
      const transcript = this.realtimeConnection.getTranscript();
      
      // Clean up
      this.realtimeConnection.close();
      this.realtimeConnection = null;
      
      return transcript;
    } else {
      // For non-realtime mode, process the audio file as before
      // ... existing code ...
    }
  }
}
```

## Implementation Plan

1. **Phase 1: Create New Components**
   - Implement RealtimeConnectionManager class
   - Update AudioRecordingService to support chunk callbacks

2. **Phase 2: Integrate Components**
   - Modify TranscriptionService to use the new components
   - Update command handlers to support the new flow

3. **Phase 3: UI Enhancements**
   - Add status indicators for real-time transcription
   - Implement partial transcript display (optional)
   - Add toggle for real-time mode in the UI

4. **Phase 4: Testing and Refinement**
   - Test with different audio sources
   - Optimize chunk size and timing
   - Fine-tune error handling

## Technical Considerations

1. **Audio Format Compatibility**
   - Ensure that the audio chunks are in a format compatible with AssemblyAI/OpenAI
   - May need to convert formats on-the-fly

2. **WebSocket Management**
   - Handle reconnection scenarios
   - Implement proper cleanup of resources

3. **Performance Optimization**
   - Find the optimal chunk size and frequency
   - Balance between real-time feedback and network overhead

4. **Error Handling**
   - Provide meaningful error messages to users
   - Gracefully recover from connection issues

## Future Enhancements

1. **Live Visualization**
   - Show audio waveform during recording
   - Highlight words as they are transcribed

2. **Speaker Diarization**
   - Support identifying different speakers in real-time

3. **Advanced Controls**
   - Allow users to pause/resume transcription
   - Provide manual correction capabilities 
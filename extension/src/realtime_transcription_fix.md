# Real-Time Transcription Fix

## Issue Description

When using real-time transcription, the edge function is correctly collecting the partial transcripts but not sending back the final complete transcript to the client. The issue appears to be in how the client handles the "close" signal and the connection may be closing too quickly before the final transcript is received.

## Solution

Make the following changes to the codebase:

### In RealtimeConnectionManager.ts:

1. Update the `sendAudioChunk` method to include an explicit flag when sending the final chunk:

```typescript
if (chunk.isLastChunk) {
  console.log('VoiceHype: Sending final audio chunk, closing session');
  this.emitStatus(TranscriptionStatus.StreamingAudio, 'Sending final audio...');

  // Give the server more time to process before sending close message
  setTimeout(() => {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      console.log('VoiceHype: Sending close message to request final transcript');
      this.ws.send(JSON.stringify({ 
        type: 'close',
        requestFinalTranscript: true // Add explicit request for final transcript
      }));

      // Update status to show we're waiting for server to finish
      this.emitStatus(TranscriptionStatus.StreamingAudio, 'Waiting for final transcription...');

      // Wait longer (10s) for the final transcript before giving up
      setTimeout(() => {
        if (!this.allChunksProcessed) {
          console.log('VoiceHype: Auto-marking chunks as processed after waiting 10 seconds');
          this.allChunksProcessed = true;
          this.emitStatus(TranscriptionStatus.AllChunksProcessed, 'Finalizing with available transcript');
        }
      }, 10000); // Increase wait time to 10 seconds
    }
  }, 1000); // Keep the 1 second delay before sending close
}
```

2. Modify the `close` method to not immediately close the connection when we're still expecting the final transcript:

```typescript
public close() {
  console.log("VoiceHype: Closing WebSocket connection");

  // Clear heartbeat and monitor
  this.clearHeartbeatAndMonitor();

  if (this.ws && (this.ws.readyState === WebSocket.OPEN || this.ws.readyState === WebSocket.CONNECTING)) {
    // Report that we're processing any final chunks
    if (this.isConnected && !this.allChunksProcessed) {
      console.log('VoiceHype: Waiting for final transcript before closing');
      // Don't close the connection immediately if we're still waiting for the final transcript
      // Instead, send a close message and let the server close the connection
      if (this.ws.readyState === WebSocket.OPEN) {
        console.log('VoiceHype: Sending close message to get final transcript');
        this.ws.send(JSON.stringify({ 
          type: 'close', 
          requestFinalTranscript: true 
        }));
        
        // Don't close the connection immediately - let the server close it
        // after it sends the final transcript
        return;
      }
    }
    
    // If we're not waiting for the final transcript, close normally
    this.ws.close();
    this.ws = null;
  }

  this.isConnected = false;
}
```

### In the supabase/functions/transcribe/realtime.ts file:

1. Modify the socket message handler to check for the explicit request for final transcript:

```typescript
socket.onmessage = (event) => {
  try {
    // ... existing code ...
    
    if (event.data instanceof Object && !ArrayBuffer.isView(event.data)) {
      const message = JSON.parse(event.data.toString());
      
      if (message.type === 'close') {
        console.log(`[DEBUG] Received close message from client, requestFinalTranscript=${message.requestFinalTranscript}`);
        
        if (message.requestFinalTranscript) {
          // Prepare and send the complete transcript
          const completeTranscript = finalTranscript.trim().length > 0 ? 
            finalTranscript.trim() : lastPartialTranscript.trim();
          
          if (completeTranscript.length > 0) {
            console.log(`[DEBUG] Sending complete transcript for session ${sessionId}, length: ${completeTranscript.length}`);
            
            // Send the complete transcript
            socket.send(JSON.stringify({
                message_type: 'CompleteTranscript',
                text: completeTranscript,
                sessionId: sessionId
            }));
            
            // Wait a bit before closing to ensure the message gets delivered
            setTimeout(() => {
              socket.close();
            }, 1000);
          } else {
            socket.close();
          }
        } else {
          socket.close();
        }
      }
      // ... existing code ...
    }
  } catch (error) {
    // ... existing error handling ...
  }
};
```

## How to Apply This Fix

1. Manually edit the RealtimeConnectionManager.ts file to update the sendAudioChunk and close methods
2. Edit the edge function in the supabase/functions/transcribe/realtime.ts file to handle the explicit request for the final transcript

These changes will ensure that:
1. When the client finishes sending audio, it explicitly requests the final transcript
2. The server properly responds with the complete transcript before closing
3. The client keeps the connection open long enough to receive the final transcript

import * as vscode from 'vscode';
import { TranscriptionService } from '../services/TranscriptionService';
import { ConfigurationService } from '../services/ConfigurationService';
import { RealtimeConnectionManager } from '../services/RealtimeConnectionManager';
import { AudioChunk } from '../services/RealtimeConnectionManager';
import { TranscriptionStatus } from '../services/TranscriptionService';
import * as fs from 'fs';
import * as path from 'path';

/**
 * A simple test script to verify the real-time transcription functionality.
 * This can be run manually to check if the implementation works as expected.
 */
export async function testRealtimeTranscription(): Promise<void> {
    // Create the necessary services
    const configService = new ConfigurationService();
    const transcriptionService = new TranscriptionService(configService);
    
    try {
        // Ensure API key is set
        const apiKey = await configService.getApiKeyAsync();
        if (!apiKey) {
            throw new Error('VoiceHype API key not found. Please set your API key in the VoiceHype panel. You can create one at voicehype.ai');
        }
        
        // Setup real-time connection
        console.log('Setting up real-time connection...');
        const realtimeConnection = await transcriptionService.setupRealTimeConnection();
        
        // Register status listeners to monitor connection state
        realtimeConnection.onStatusChange((status: TranscriptionStatus, message?: string) => {
            console.log(`Status update: ${status}${message ? `: ${message}` : ''}`);
        });
        
        // Setup transcription update listener for real-time feedback
        realtimeConnection.onTranscriptionUpdate((text: string, isFinal: boolean) => {
            console.log(`Transcription update (${isFinal ? 'final' : 'partial'}): ${text}`);
        });
        
        // Connect to the service
        console.log('Connecting to the service...');
        await realtimeConnection.connect();
        
        // Wait for transcription service to be ready
        await new Promise<void>(resolve => {
            realtimeConnection.onTranscriptionReady(() => {
                console.log('Transcription service is ready');
                resolve();
            });
        });
        
        // Read a test audio file and send it in chunks
        console.log('Ready to process audio. Reading test file...');
        
        // Path to a test audio file
        const audioFilePath = path.join(__dirname, '../../test/fixtures/test-audio.wav');
        
        if (!fs.existsSync(audioFilePath)) {
            throw new Error(`Test audio file not found: ${audioFilePath}`);
        }
        
        const audioData = fs.readFileSync(audioFilePath);
        console.log(`Read ${audioData.length} bytes from test audio file`);
        
        // Send audio data in chunks
        const chunkSize = 3200; // 100ms of 16kHz 16-bit audio
        const totalChunks = Math.ceil(audioData.length / chunkSize);
        
        console.log(`Sending audio in ${totalChunks} chunks of ${chunkSize} bytes each`);
        
        for (let i = 0; i < audioData.length; i += chunkSize) {
            const end = Math.min(i + chunkSize, audioData.length);
            const chunk = audioData.slice(i, end);
            
            const audioChunk: AudioChunk = {
                data: new Uint8Array(chunk),
                isLastChunk: end === audioData.length
            };
            
            realtimeConnection.sendAudioChunk(audioChunk);
            
            // Log progress occasionally
            if (i % (chunkSize * 10) === 0 || i + chunkSize >= audioData.length) {
                const progress = Math.round((i / audioData.length) * 100);
                console.log(`Sent ${progress}% of audio data`);
            }
            
            // Add a small delay to simulate real-time streaming
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        // Wait for processing to complete
        console.log('Finished sending audio data. Waiting for final transcription...');
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // Get final transcript
        const transcript = realtimeConnection.getTranscript();
        console.log('Final transcript:', transcript);
        
        // Wait for processing to complete and check results
        const hasTranscript = realtimeConnection.hasTranscriptData();
        const summary = realtimeConnection.getTranscriptionSummary();
        console.log('Transcription results:', summary);
        
        // Close the connection and clean up
        realtimeConnection.close();
        console.log('Connection closed');
        
        if (!hasTranscript) {
            throw new Error('No transcription data was received');
        }
        
        return;
    } catch (error) {
        console.error('Error in real-time transcription test:', error instanceof Error ? error.message : String(error));
        throw error;
    }
} 
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { AudifyMicrophone } from '../utils/audifyMicrophone';

/**
 * Test script for the Audify-based microphone implementation
 * 
 * This script demonstrates:
 * 1. Listing available audio devices
 * 2. Recording audio for a specified duration
 * 3. Saving the recording to a WAV file
 * 
 * Usage: 
 *   npm run test:audify
 *   
 * or
 * 
 *   ts-node src/test/test-audify-microphone.ts
 */

// Set up recording parameters
const RECORDING_SECONDS = 5; // Duration in seconds
const tempDir = os.tmpdir();
const outputFile = path.join(tempDir, 'voicehype_audify_test.wav');

async function testAudifyMicrophone() {
    console.log('VoiceHype Audify Microphone Test');
    console.log('===============================');
    console.log(`Platform: ${os.platform()}`);
    console.log(`Recording will be saved to: ${outputFile}`);
    
    try {
        // Create an AudifyMicrophone instance
        const mic = new AudifyMicrophone({
            rate: 44100,
            channels: 1
        });
        
        // List available devices
        console.log('\nAvailable audio devices:');
        const devices = await mic.getDevices();
        devices.forEach((device, index) => {
            console.log(`[${index}] ${device.name} (ID: ${device.id})`);
        });
        
        console.log(`\nRecording for ${RECORDING_SECONDS} seconds...`);
        
        // Start recording
        const stream = mic.startRecording();
        const startTime = Date.now();
        
        // Set a timeout to stop recording after the specified duration
        setTimeout(() => {
            console.log('Stopping recording...');
            mic.stopRecording();
            
            const duration = (Date.now() - startTime) / 1000;
            console.log(`Recording stopped after ${duration.toFixed(1)} seconds`);
            console.log(`Recording saved to: ${outputFile}`);
            
            // Check if the file exists and get its size
            if (fs.existsSync(outputFile)) {
                const stats = fs.statSync(outputFile);
                console.log(`File size: ${stats.size} bytes`);
            } else {
                console.log('WARNING: Output file not found');
            }
            
            process.exit(0);
        }, RECORDING_SECONDS * 1000);
        
        // Handle errors
        stream.on('error', (err) => {
            console.error('Error during recording:', err);
            process.exit(1);
        });
        
        // Save the recording to a file
        const writeStream = fs.createWriteStream(outputFile);
        stream.pipe(writeStream);
        
        // Test pause/resume if recording for more than 3 seconds
        if (RECORDING_SECONDS > 3) {
            setTimeout(() => {
                console.log('\nPausing recording...');
                mic.pauseRecording();
                
                // Resume after 1 second
                setTimeout(() => {
                    console.log('Resuming recording...');
                    mic.resumeRecording();
                }, 1000);
            }, 2000); // Pause after 2 seconds
        }
    } catch (error) {
        console.error('Test failed:', error);
        process.exit(1);
    }
}

// Run the test
testAudifyMicrophone();

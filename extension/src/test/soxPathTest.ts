import * as fs from 'fs';
import * as os from 'os';
import * as path from 'path';
import { getSoxPath } from '../utils/soxPath';
import { execSync } from 'child_process';

/**
 * This script tests the getSoxPath function to ensure it can correctly find
 * the sox binary from the appropriate location for each platform.
 */
function testSoxPath() {
    console.log('Testing getSoxPath function...');
    
    try {
        // Get the path to the sox binary using our utility function
        const soxPath = getSoxPath();
        console.log(`SoX path from getSoxPath(): ${soxPath}`);
        
        // Check if the file exists
        if (fs.existsSync(soxPath)) {
            console.log(`SoX binary exists at: ${soxPath}`);
            
            // Try to execute the binary to check if it works
            try {
                const version = execSync(`"${soxPath}" --version`).toString();
                console.log(`SoX version: ${version.trim()}`);
                console.log('SoX binary is working correctly!');
            } catch (error) {
                console.error('Error executing SoX binary:', error);
            }
        } else {
            console.log(`SoX binary not found at: ${soxPath}`);
        }
        
        // Check platform-specific paths
        console.log('\nChecking platform-specific paths:');
        
        const extensionPath = path.resolve(__dirname, '..', '..');
        const platform = os.platform();
        
        if (platform === 'win32') {
            // Check for Windows binary
            const soxPath = path.join(extensionPath, 'resources', 'binaries', 'win32', 'sox-14.4.2-win32', 'sox.exe');
            
            console.log(`Windows SoX binary: ${fs.existsSync(soxPath) ? 'Found' : 'Not found'}`);
            
            if (fs.existsSync(soxPath)) {
                console.log(`Using SoX binary at: ${soxPath}`);
                
                // List all DLL files in the directory to ensure dependencies are present
                const soxDir = path.dirname(soxPath);
                const dllFiles = fs.readdirSync(soxDir).filter(file => file.endsWith('.dll'));
                console.log(`Found ${dllFiles.length} DLL files in the directory:`);
                dllFiles.forEach(file => console.log(`- ${file}`));
            } else {
                console.log('No Windows SoX binary found');
            }
        } else if (platform === 'darwin') {
            try {
                const macSoxPath = require('sox-static-macos');
                console.log(`macOS SoX path from package: ${macSoxPath}`);
                console.log(`Binary exists: ${fs.existsSync(macSoxPath)}`);
            } catch (err) {
                console.error('Error loading macOS SoX binary:', err);
            }
        } else if (platform === 'linux') {
            try {
                const linuxSoxPath = require('sox-static-linux');
                console.log(`Linux SoX path from package: ${linuxSoxPath}`);
                console.log(`Binary exists: ${fs.existsSync(linuxSoxPath)}`);
            } catch (err) {
                console.error('Error loading Linux SoX binary:', err);
            }
        }
    } catch (error) {
        console.error('Error testing getSoxPath:', error);
    }
}

// Run the test
testSoxPath(); 
import * as fs from 'fs';
import * as path from 'path';
import { Microphone } from '../utils/microphone';

/**
 * Simple test script to verify that our microphone implementation works correctly.
 * This can be run directly with ts-node to test recording functionality.
 * 
 * Usage: npx ts-node src/test/test-microphone.ts
 */

// Create a temporary file to store the recording
const outputFile = path.join(__dirname, '..', '..', 'test-recording.wav');
const writeStream = fs.createWriteStream(outputFile);

console.log(`Recording to ${outputFile}`);
console.log('Press Ctrl+C to stop recording');

// Create a microphone instance
const mic = new Microphone({
    rate: 16000,
    // Uncomment and set a device if needed
    // device: 'pulse'
});

// Start recording
const audioStream = mic.startRecording();

// Log data chunks
let dataChunks = 0;
let totalBytes = 0;
audioStream.on('data', (data: Buffer) => {
    dataChunks++;
    totalBytes += data.length;
    if (dataChunks % 10 === 0) {
        console.log(`Received ${dataChunks} chunks, ${totalBytes} bytes total`);
    }
});

// Handle errors
audioStream.on('error', (err) => {
    console.error('Error:', err);
    process.exit(1);
});

// Pipe the audio to the file
audioStream.pipe(writeStream);

// Handle process termination
process.on('SIGINT', () => {
    console.log('Stopping recording...');
    mic.stopRecording();
    writeStream.end();
    console.log(`Recording saved to ${outputFile}`);
    process.exit(0);
}); 
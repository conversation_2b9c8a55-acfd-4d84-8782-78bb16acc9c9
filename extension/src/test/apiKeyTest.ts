import * as vscode from 'vscode';
import { ConfigurationService } from '../services/ConfigurationService';
import { SecretsService } from '../services/SecretsService';

/**
 * Test function to verify secure API key storage and retrieval
 * This can be run from the extension development host using
 * the Command Palette with "Developer: Run Test API Key Storage"
 */
export async function testApiKeyStorage(context: vscode.ExtensionContext): Promise<void> {
    // Create the services
    const secretsService = new SecretsService(context);
    const configService = new ConfigurationService(secretsService);
    
    // Test API key
    const testApiKey = "test-api-key-" + Date.now();
    
    // Output channel for logging
    const outputChannel = vscode.window.createOutputChannel("VoiceHype API Key Test");
    outputChannel.show();
    
    try {
        // Clear any existing API key
        outputChannel.appendLine("Clearing existing API key from settings...");
        await vscode.workspace.getConfiguration('voicehype').update('apiKey', '', vscode.ConfigurationTarget.Global);
        
        // Step 1: Set API key and verify secure storage
        outputChannel.appendLine(`\nSTEP 1: Setting API key: ${testApiKey}`);
        await configService.setApiKey(testApiKey);
        
        // Check if setting was cleared
        const settingValue = vscode.workspace.getConfiguration('voicehype').get<string>('apiKey', '');
        outputChannel.appendLine(`API key in settings: '${settingValue}'`);
        
        if (settingValue) {
            outputChannel.appendLine("WARNING: API key still exists in settings - it should be empty if secure storage worked.");
        } else {
            outputChannel.appendLine("SUCCESS: API key was removed from settings as expected.");
        }
        
        // Step 2: Retrieve API key via async method
        outputChannel.appendLine("\nSTEP 2: Retrieving API key via async method...");
        const retrievedKey = await configService.getApiKeyAsync();
        
        if (retrievedKey === testApiKey) {
            outputChannel.appendLine(`SUCCESS: Retrieved API key matches: ${retrievedKey}`);
        } else {
            outputChannel.appendLine(`ERROR: Retrieved API key does not match. Expected: ${testApiKey}, Got: ${retrievedKey || 'undefined'}`);
        }
        
        // Step 3: Test synchronous method (which handles the promise internally)
        outputChannel.appendLine("\nSTEP 3: Testing synchronous getApiKey method...");
        const syncKey = configService.getApiKey();
        outputChannel.appendLine(`Synchronous API key retrieval result: ${syncKey || 'undefined'}`);
        outputChannel.appendLine("Note: The sync method doesn't return the API key directly due to promises, but it shouldn't throw errors.");
        
        // Step 4: Delete the API key
        outputChannel.appendLine("\nSTEP 4: Deleting API key...");
        await secretsService.deleteApiKey();
        
        // Verify deletion
        const keyAfterDeletion = await secretsService.getApiKey();
        
        if (!keyAfterDeletion) {
            outputChannel.appendLine("SUCCESS: API key was successfully deleted from secure storage.");
        } else {
            outputChannel.appendLine(`ERROR: API key still exists after deletion: ${keyAfterDeletion}`);
        }
        
        outputChannel.appendLine("\nAPI Key storage test completed!");
    } catch (error) {
        outputChannel.appendLine(`\nERROR during test: ${error instanceof Error ? error.message : String(error)}`);
        if (error instanceof Error && error.stack) {
            outputChannel.appendLine(`Stack trace: ${error.stack}`);
        }
    }
}

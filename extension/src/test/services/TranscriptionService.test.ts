import * as assert from 'assert';
import * as vscode from 'vscode';
import { TranscriptionService } from '../../services/TranscriptionService';
import { ConfigurationService } from '../../services/ConfigurationService';
import { PromptFormatter } from '../../utils/PromptFormatter';

suite('TranscriptionService Test Suite', () => {
    class MockConfigService extends ConfigurationService {
        private voiceCommandsEnabled = true;

        constructor() {
            super();
        }

        override getVoiceCommandsEnabled(): boolean {
            return this.voiceCommandsEnabled;
        }

        async setVoiceCommandsEnabled(enabled: boolean): Promise<void> {
            this.voiceCommandsEnabled = enabled;
        }
    }

    let transcriptionService: TranscriptionService;
    let mockConfigService: MockConfigService;

    setup(() => {
        mockConfigService = new MockConfigService();
        transcriptionService = new TranscriptionService(mockConfigService);
    });

    test('should handle optimization with voice commands enabled', async () => {
        const input = "Voice Hype generate a hello world program";
        const customPrompt = "";
        
        mockConfigService.setVoiceCommandsEnabled(true);
        const optimizedText = await transcriptionService.optimizeText(input, customPrompt);
        assert.ok(optimizedText, "Should return optimized text");
    });

    test('should handle optimization with voice commands disabled', async () => {
        const input = "Voice Hype generate a hello world program";
        const customPrompt = "";
        
        mockConfigService.setVoiceCommandsEnabled(false);
        const optimizedText = await transcriptionService.optimizeText(input, customPrompt);
        assert.ok(optimizedText, "Should return optimized text");
    });
});

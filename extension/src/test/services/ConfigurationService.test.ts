import * as assert from 'assert';
import * as vscode from 'vscode';
import { ConfigurationService } from '../../services/ConfigurationService';

suite('ConfigurationService Test Suite', () => {
    let configService: ConfigurationService;

    setup(() => {
        configService = new ConfigurationService();
    });

    test('getVoiceCommandsEnabled should return default value when not set', async () => {
        // Default value is true
        assert.strictEqual(configService.getVoiceCommandsEnabled(), true);
    });

    test('getVoiceCommandsEnabled should return configured value', async () => {
        // Set the configuration value
        await vscode.workspace.getConfiguration('voicehype').update('voiceCommandsEnabled', false);
        assert.strictEqual(configService.getVoiceCommandsEnabled(), false);
        
        // Reset to default
        await vscode.workspace.getConfiguration('voicehype').update('voiceCommandsEnabled', undefined);
    });
});

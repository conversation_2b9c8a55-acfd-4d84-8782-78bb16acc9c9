import * as vscode from 'vscode';

export class RobustThemeDetector {
    private _onThemeChanged = new vscode.EventEmitter<'light' | 'dark'>();
    public readonly onThemeChanged = this._onThemeChanged.event;
    
    constructor() {
        // Listen for theme changes
        vscode.window.onDidChangeActiveColorTheme(() => {
            const newTheme = this.getCurrentTheme();
            this._onThemeChanged.fire(newTheme);
        });
    }
    
    /**
     * Gets the current theme type based on ColorThemeKind
     */
    getCurrentTheme(): 'light' | 'dark' {
        const theme = vscode.window.activeColorTheme;
        
        switch (theme.kind) {
            case vscode.ColorThemeKind.Light:
                return 'light';
                
            case vscode.ColorThemeKind.Dark:
                return 'dark';
                
            case vscode.ColorThemeKind.HighContrastLight:
                return 'light';
                
            case vscode.ColorThemeKind.HighContrast:
                // Default high contrast themes are typically dark
                return 'dark';
                
            default:
                // Fallback to dark for any unknown theme kinds
                return 'dark';
        }
    }
    
    /**
     * Convenience method to check if current theme is dark
     */
    isDark(): boolean {
        return this.getCurrentTheme() === 'dark';
    }
    
    /**
     * Convenience method to check if current theme is light
     */
    isLight(): boolean {
        return this.getCurrentTheme() === 'light';
    }
    
    /**
     * Get detailed theme information for debugging
     */
    getThemeInfo(): {
        kind: vscode.ColorThemeKind;
        kindName: string;
        detectedType: 'light' | 'dark';
    } {
        const theme = vscode.window.activeColorTheme;
        
        const kindNames = {
            [vscode.ColorThemeKind.Light]: 'Light',
            [vscode.ColorThemeKind.Dark]: 'Dark',
            [vscode.ColorThemeKind.HighContrast]: 'HighContrast',
            [vscode.ColorThemeKind.HighContrastLight]: 'HighContrastLight'
        };
        
        return {
            kind: theme.kind,
            kindName: kindNames[theme.kind] || 'Unknown',
            detectedType: this.getCurrentTheme()
        };
    }
    
    /**
     * Log detailed theme information for debugging
     */
    debugTheme(): void {
        const info = this.getThemeInfo();
        console.log('=== Theme Detection Debug ===');
        console.log(`Theme Kind: ${info.kind} (${info.kindName})`);
        console.log(`Detected Type: ${info.detectedType}`);
        console.log('=============================');
    }
    
    /**
     * Dispose of event listeners
     */
    dispose(): void {
        this._onThemeChanged.dispose();
    }
}

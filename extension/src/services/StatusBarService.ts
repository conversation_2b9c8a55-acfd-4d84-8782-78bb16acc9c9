import * as vscode from 'vscode';
import { IStatusBarService } from '../models/interfaces';
import { IConfigurationService } from '../models/interfaces';

export class StatusBarService implements IStatusBarService {
    private modeToggleButton: vscode.StatusBarItem;
    private recordButton: vscode.StatusBarItem;
    private pauseResumeButton: vscode.StatusBarItem;
    private cancelButton: vscode.StatusBarItem;
    private settingsBarItem: vscode.StatusBarItem;
    private disposables: vscode.Disposable[] = [];
    private updateDebounceTimer: NodeJS.Timeout | null = null;
    private lastRecordingState: boolean = false;
    private lastPausedState: boolean = false;

    constructor(private configService: IConfigurationService) {
        // Create status bar items
        this.modeToggleButton = vscode.window.createStatusBarItem(
            vscode.StatusBarAlignment.Right,
            1002  // Higher priority to appear on the left
        );
        this.modeToggleButton.command = 'voicehype.toggleOptimizationMode';

        this.recordButton = vscode.window.createStatusBarItem(
            vscode.StatusBarAlignment.Right,
            1001
        );
        this.recordButton.command = 'voicehype.toggleRecording';

        this.pauseResumeButton = vscode.window.createStatusBarItem(
            vscode.StatusBarAlignment.Right,
            1000
        );
        this.pauseResumeButton.command = 'voicehype.togglePauseResume';
        this.pauseResumeButton.hide(); // Initially hidden until recording starts

        this.cancelButton = vscode.window.createStatusBarItem(
            vscode.StatusBarAlignment.Right,
            999
        );
        this.cancelButton.command = 'voicehype.cancelRecording';
        this.cancelButton.text = "$(close) Cancel";
        this.cancelButton.tooltip = "Cancel Recording";
        this.cancelButton.hide(); // Initially hidden until recording starts

        this.settingsBarItem = vscode.window.createStatusBarItem(
            vscode.StatusBarAlignment.Right,
            998
        );
        this.settingsBarItem.command = 'voicehype.showQuickSettings';
        this.settingsBarItem.text = "$(gear) VoiceHype";
        this.settingsBarItem.tooltip = "Configure VoiceHype Settings";
        this.settingsBarItem.show();

        this.updateStatusBarItems(false, false, false);
        this.modeToggleButton.show();
        this.recordButton.show();

        // Subscribe to configuration changes
        this.disposables.push(
            this.configService.onDidChangeConfiguration(key => {
                console.log(`VoiceHype: Configuration changed - ${key}`);
                if (key.startsWith('voicehype.transcription') || key.startsWith('voicehype.audio')) {
                    // Update the UI when relevant settings change
                    this.updateStatusBarItems(false, false, false);
                }
            })
        );

        // Also listen for workspace configuration changes
        this.disposables.push(
            vscode.workspace.onDidChangeConfiguration(e => {
                if (e.affectsConfiguration('voicehype.transcription') || e.affectsConfiguration('voicehype.audio')) {
                    this.updateStatusBarItems(false, false, false);
                }
            })
        );
    }

    updateStatusBarItems(isRecording: boolean, startedWithOptimize: boolean, isPaused: boolean): void {
        // Check if the recording state has actually changed
        const recordingStateChanged = isRecording !== this.lastRecordingState || isPaused !== this.lastPausedState;

        // Update the last known states
        this.lastRecordingState = isRecording;
        this.lastPausedState = isPaused;

        const shouldOptimize = this.configService.getShouldOptimize();
        const service = this.configService.getTranscriptionService();
        const model = this.configService.getTranscriptionModel();
        const language = this.configService.getTranscriptionLanguage();
        const optimizationModel = this.configService.getOptimizationModel();

        console.log('VoiceHype [DEBUG]: Updating status bar items with:', { shouldOptimize, service, model, language, isRecording, isPaused, recordingStateChanged });
        console.log('VoiceHype [DEBUG]: Status bar update called from:', new Error().stack);

        // If there's a pending update, cancel it
        if (this.updateDebounceTimer) {
            clearTimeout(this.updateDebounceTimer);
            this.updateDebounceTimer = null;
        }

        // Use a small debounce to prevent rapid state changes
        this.updateDebounceTimer = setTimeout(() => {

            // Update mode toggle button
            this.modeToggleButton.text = shouldOptimize ? "$(record) $(sparkle)" : "$(record)";
            this.modeToggleButton.tooltip = shouldOptimize ?
                `AI-Optimized Mode using ${optimizationModel || 'default model'} (Click to toggle)` :
                'Basic Mode (Click to toggle)';

            // Update record button
            if (isRecording) {
                const newText = isPaused ? "Paused" : "Recording...";
                console.log(`VoiceHype [DEBUG]: Setting record button text to "${newText}" (was "${this.recordButton.text}")`);
                this.recordButton.text = newText;
                this.recordButton.tooltip = `Press Ctrl+Shift+${startedWithOptimize ? '9' : '8'} to stop recording`;
                this.recordButton.backgroundColor = isPaused ?
                    new vscode.ThemeColor('statusBarItem.warningBackground') :
                    new vscode.ThemeColor('statusBarItem.errorBackground');

                // Show pause/resume button when recording
                this.pauseResumeButton.text = isPaused ? "$(debug-continue) Resume" : "$(debug-pause) Pause";
                this.pauseResumeButton.tooltip = isPaused ? "Resume Recording (Ctrl+Shift+0)" : "Pause Recording (Ctrl+Shift+0)";
                this.pauseResumeButton.show();

                // Show cancel button when recording
                this.cancelButton.show();
            } else {
                const serviceName = service === 'assemblyai' ? 'AssemblyAI' : 'Whisper';
                let modelDisplay = model;

                // Shorten long model names for display if needed
                if (model === 'gpt-4o-mini-transcribe') {
                    modelDisplay = 'GPT-4o-mini';
                } else if (model === 'gpt-4o-transcribe') {
                    modelDisplay = 'GPT-4o';
                }

                console.log(`VoiceHype [DEBUG]: Setting record button text to "Record" (was "${this.recordButton.text}")`);
                this.recordButton.text = "Record";
                this.recordButton.tooltip = `Start Recording (${serviceName} | ${modelDisplay} | ${language?.toUpperCase()})`;
                this.recordButton.backgroundColor = undefined;

                // Hide pause/resume button when not recording
                this.pauseResumeButton.hide();

                // Hide cancel button when not recording
                this.cancelButton.hide();
            }

            // Clear the timer reference
            this.updateDebounceTimer = null;
        }, 50); // 50ms debounce should be enough to prevent flicker but not noticeable to users
    }

    dispose(): void {
        this.modeToggleButton.dispose();
        this.recordButton.dispose();
        this.pauseResumeButton.dispose();
        this.cancelButton.dispose();
        this.settingsBarItem.dispose();

        // Dispose all disposables
        this.disposables.forEach(d => d.dispose());
    }
}
import * as vscode from 'vscode';
import { IRecordingService } from '../models/interfaces';
import { IConfigurationService } from '../models/interfaces';
import { ITranscriptionService } from '../models/interfaces';
import { IHistoryService } from '../models/interfaces';
import { IStatusBarService } from '../models/interfaces';
import { FileTrackingEntry } from '../models/interfaces';
import { AuthenticationService, AuthState } from './AuthenticationService';

export class CommandService {
    private disposables: vscode.Disposable[] = [];

    constructor(
        private context: vscode.ExtensionContext,
        private recordingService: IRecordingService,
        private configService: IConfigurationService,
        private transcriptionService: ITranscriptionService,
        private historyService: IHistoryService,
        private authenticationService?: AuthenticationService,
        private statusBarService?: IStatusBarService
    ) { }

    registerCommands(): void {
        // Helper function to check API key before any operation
        const checkApiKey = async (): Promise<boolean> => {
            const currentApiKey = await this.configService.getApiKeyAsync();
            if (!currentApiKey) {
                // If authentication service is available, offer sign-in option
                if (this.authenticationService) {
                    const response = await vscode.window.showErrorMessage(
                        'VoiceHype API key not set. Please sign in or enter an API key to use this feature.',
                        'Sign In',
                        'Enter API Key',
                        'Visit voicehype.ai'
                    );
                    
                    if (response === 'Sign In') {
                        // Trigger sign-in flow
                        await vscode.commands.executeCommand('voicehype.signIn');
                        // After sign-in, check if we have an API key now
                        const newApiKey = await this.configService.getApiKeyAsync();
                        return !!newApiKey;
                    } else if (response === 'Enter API Key') {
                        // Open and focus the VoiceHype panel
                        await vscode.commands.executeCommand('voicehype.controlPanel.focus');
                        // Show a message guiding the user
                        vscode.window.showInformationMessage('Enter your API key in the VoiceHype panel above');
                    } else if (response === 'Visit voicehype.ai') {
                        await vscode.env.openExternal(vscode.Uri.parse('https://voicehype.ai'));
                    }
                } else {
                    // Fall back to original behavior if authentication service isn't available
                    const response = await vscode.window.showErrorMessage(
                        'VoiceHype API key not set. Please set your API key to use this feature.',
                        'Enter API Key',
                        'Visit voicehype.ai'
                    );
                    if (response === 'Enter API Key') {
                        // Open and focus the VoiceHype panel
                        await vscode.commands.executeCommand('voicehype.controlPanel.focus');
                        // Show a message guiding the user
                        vscode.window.showInformationMessage('Enter your API key in the VoiceHype panel above');
                    } else if (response === 'Visit voicehype.ai') {
                        await vscode.env.openExternal(vscode.Uri.parse('https://voicehype.ai'));
                    }
                }
                return false;
            }
            return true;
        };

        // Register toggle optimization mode command
        this.disposables.push(
            vscode.commands.registerCommand('voicehype.toggleOptimizationMode', async () => {
                if (this.recordingService.isRecording()) {
                    vscode.window.showWarningMessage('Cannot change optimization mode while recording.');
                    return;
                }

                const currentOptimize = this.configService.getShouldOptimize();

                await this.configService.updateSetting('voicehype.transcription.shouldOptimize', !currentOptimize);

                // Explicitly update status bar if available
                if (this.statusBarService) {
                    console.log('VoiceHype: Explicitly updating status bar after mode toggle');
                    this.statusBarService.updateStatusBarItems(
                        this.recordingService.isRecording(),
                        false,
                        this.recordingService.isPaused()
                    );
                }

                vscode.window.showInformationMessage(
                    `AI Optimization ${!currentOptimize ? 'enabled' : 'disabled'}`
                );
            })
        );

        // Register toggle pause/resume command
        this.disposables.push(
            vscode.commands.registerCommand('voicehype.togglePauseResume', async () => {
                if (!this.recordingService.isRecording()) {
                    vscode.window.showInformationMessage('No active recording to pause or resume.');
                    return;
                }

                if (this.recordingService.isPaused()) {
                    // Resume recording
                    await this.recordingService.resumeRecording();
                } else {
                    // Pause recording
                    await this.recordingService.pauseRecording();
                }
            })
        );

        // Register paste original transcript command
        this.disposables.push(
            vscode.commands.registerCommand('voicehype.pasteOriginalTranscript', async () => {
                await this.recordingService.pasteOriginalTranscript();
            })
        );

        // Register commands with API key check
        this.disposables.push(
            vscode.commands.registerCommand('voicehype.startRecording', async () => {
                if (!await checkApiKey()) {
                    return;
                }
                console.log('VoiceHype: Start recording command triggered');
                this.recordingService.startRecording(false);
            })
        );

        this.disposables.push(
            vscode.commands.registerCommand('voicehype.startRecordingWithOptimize', async () => {
                if (!await checkApiKey()) {
                    return;
                }
                console.log('VoiceHype: Start recording with optimize command triggered');
                this.recordingService.startRecording(true);
            })
        );

        this.disposables.push(
            vscode.commands.registerCommand('voicehype.stopRecording', async () => {
                if (!await checkApiKey()) {
                    return;
                }
                console.log('VoiceHype: Stop recording command triggered (without optimization)');
                this.recordingService.stopRecording(false);
            })
        );

        this.disposables.push(
            vscode.commands.registerCommand('voicehype.stopRecordingAndOptimize', async () => {
                if (!await checkApiKey()) {
                    return;
                }
                console.log('VoiceHype: Stop recording command triggered (with optimization)');
                this.recordingService.stopRecording(true);
            })
        );

        this.disposables.push(
            vscode.commands.registerCommand('voicehype.playLastRecording', () => {
                console.log('VoiceHype: Play last recording command triggered');
                this.recordingService.playLastRecording();
            })
        );

        this.disposables.push(
            vscode.commands.registerCommand('voicehype.clearHistory', () => {
                console.log('VoiceHype: Clear history command triggered');
                this.historyService.clearHistory();
                vscode.window.showInformationMessage('Recording history cleared!');
            })
        );

        this.disposables.push(
            vscode.commands.registerCommand('voicehype.optimizeSelectedText', async () => {
                const editor = vscode.window.activeTextEditor;
                if (!editor) {
                    vscode.window.showInformationMessage('Please select some text to optimize');
                    return;
                }

                const selection = editor.selection;
                const text = editor.document.getText(selection);
                if (!text) {
                    vscode.window.showInformationMessage('Please select some text to optimize');
                    return;
                }

                try {
                    const optimizedText = await this.transcriptionService.optimizeText(text);
                    const shouldCopy = vscode.workspace.getConfiguration('voicehype').get('copyResultsToClipboard', true);
                    if (shouldCopy) {
                        await vscode.env.clipboard.writeText(optimizedText);
                    }
                    await vscode.commands.executeCommand('paste');
                } catch (error) {
                    console.error('VoiceHype: Error optimizing text:', error);
                    vscode.window.showErrorMessage('Failed to optimize text. Please try again.');
                }
            })
        );

        this.disposables.push(
            vscode.commands.registerCommand('voicehype.toggleRecording', async () => {
                if (!await checkApiKey()) {
                    return;
                }
                console.log('VoiceHype: Toggle recording command triggered');

                // Get the current optimization setting
                const shouldOptimize = this.configService.getShouldOptimize();
                console.log('VoiceHype: Should optimize from config:', shouldOptimize);

                if (this.recordingService.isRecording()) {
                    console.log('VoiceHype: Currently recording, stopping with optimize:', shouldOptimize);
                    this.recordingService.stopRecording(shouldOptimize);
                } else {
                    console.log('VoiceHype: Not recording, starting with optimize:', shouldOptimize);
                    this.recordingService.startRecording(shouldOptimize);
                }
            })
        );

        // Add the showQuickSettings command
        this.disposables.push(
            vscode.commands.registerCommand('voicehype.showQuickSettings', async () => {
                console.log('VoiceHype: Show quick settings command triggered');
                await this.showQuickSettings();
            })
        );

        this.disposables.push(
            vscode.commands.registerCommand('voicehype.selectOptimizationModel', async () => {
                if (!await checkApiKey()) {
                    return;
                }
                console.log('VoiceHype: Select optimization model command triggered');
                await this.showOptimizationModelSelectionQuickPick();
            })
        );

        // Register commands for history view
        this.disposables.push(
            vscode.commands.registerCommand('voicehype.openTranscript', async (text: string, _: boolean) => {
                const document = await vscode.workspace.openTextDocument({
                    content: text,
                    language: 'markdown'
                });
                await vscode.window.showTextDocument(document, {
                    preview: false,
                    viewColumn: vscode.ViewColumn.One
                });
            })
        );

        this.disposables.push(
            vscode.commands.registerCommand('voicehype.openFileReference', async (reference: FileTrackingEntry) => {
                try {
                    const files = await vscode.workspace.findFiles(`**/${reference.fileName}`, null, 1);
                    if (files.length > 0) {
                        const document = await vscode.workspace.openTextDocument(files[0]);
                        const editor = await vscode.window.showTextDocument(document);

                        if (reference.action === 'selected' && reference.selection) {
                            const range = new vscode.Range(
                                reference.selection.startLine - 1,
                                0,
                                reference.selection.endLine - 1,
                                0
                            );
                            editor.selection = new vscode.Selection(range.start, range.end);
                            editor.revealRange(range);
                        }
                    } else {
                        vscode.window.showWarningMessage(`File not found: ${reference.fileName}`);
                    }
                } catch (error) {
                    console.error('Error opening file reference:', error);
                    vscode.window.showErrorMessage(`Failed to open file: ${reference.fileName}`);
                }
            })
        );

        // Command for pasting text into the editor
        this.disposables.push(
            vscode.commands.registerCommand('voicehype.pasteText', async (text: string) => {
                if (text) {
                    const editor = vscode.window.activeTextEditor;
                    if (editor) {
                        const selections = editor.selections;
                        await editor.edit(editBuilder => {
                            selections.forEach(selection => {
                                editBuilder.replace(selection, text);
                            });
                        });
                        vscode.window.showInformationMessage('Text pasted into editor');
                    } else {
                        // If no editor is open, check clipboard configuration before copying
                        const shouldCopy = vscode.workspace.getConfiguration('voicehype').get('copyResultsToClipboard', true);
                        if (shouldCopy) {
                            await vscode.env.clipboard.writeText(text);
                            vscode.window.showInformationMessage('Text copied to clipboard');
                        } else {
                            vscode.window.showInformationMessage('Clipboard operations are currently disabled in settings');
                        }
                    }
                }
            })
        );

        // Register the commands from the History Service
        const historyCommands = this.historyService.registerCommands();
        this.disposables.push(...historyCommands);

        // Add a new method to register a command for toggling real-time transcription mode
        this.disposables.push(this.registerToggleRealtimeTranscriptionCommand());

        // Cancel recording - with informative handling for real-time mode
        this.disposables.push(
            vscode.commands.registerCommand('voicehype.cancelRecording', async () => {
                try {
                    await this.recordingService.cancelRecording();
                } catch (error) {
                    console.error('Error cancelling recording:', error);
                    vscode.window.showErrorMessage('Failed to cancel recording');
                }
            })
        );
        
        // Register authentication commands if the authentication service is available
        if (this.authenticationService) {
            const authCommands = this.registerAuthenticationCommands();
            this.disposables.push(...authCommands);
        }

        // Command to sign in
        this.disposables.push(
            vscode.commands.registerCommand('voicehype.signIn', async () => {
                console.log('VoiceHype: Sign in command triggered');
                
                if (!this.authenticationService) {
                    vscode.window.showErrorMessage('Authentication service not available');
                    return;
                }
                
                try {
                    // Show a progress notification during sign-in
                    await vscode.window.withProgress(
                        {
                            location: vscode.ProgressLocation.Notification,
                            title: 'Signing in to VoiceHype...',
                            cancellable: false
                        },
                        async () => {
                            await this.authenticationService!.signIn();
                        }
                    );
                    
                    vscode.window.showInformationMessage('Successfully signed in to VoiceHype!');
                } catch (error: unknown) {
                    console.error('VoiceHype: Sign in error:', error);
                    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                    vscode.window.showErrorMessage(`Failed to sign in: ${errorMessage}`);
                }
            })
        );

        // Command to sign out
        this.disposables.push(
            vscode.commands.registerCommand('voicehype.signOut', async () => {
                console.log('VoiceHype: Sign out command triggered');
                
                if (!this.authenticationService) {
                    vscode.window.showErrorMessage('Authentication service not available');
                    return;
                }
                
                try {
                    await this.authenticationService.signOut();
                    vscode.window.showInformationMessage('Signed out from VoiceHype');
                } catch (error: unknown) {
                    console.error('VoiceHype: Sign out error:', error);
                    vscode.window.showErrorMessage(`Failed to sign out: ${error instanceof Error ? error.message : 'Unknown error'}`);
                }
            })
        );
        
        // Command to show current auth status
        this.disposables.push(
            vscode.commands.registerCommand('voicehype.showAuthStatus', async () => {
                console.log('VoiceHype: Show auth status command triggered');
                
                if (!this.authenticationService) {
                    vscode.window.showErrorMessage('Authentication service not available');
                    return;
                }
                
                const authState = this.authenticationService.getAuthState();
                const userProfile = this.authenticationService.getUserProfile();
                
                if (authState === AuthState.SignedIn && userProfile) {
                    vscode.window.showInformationMessage(`Signed in as ${userProfile.email}`);
                } else if (authState === AuthState.SigningIn) {
                    vscode.window.showInformationMessage('Currently signing in...');
                } else {
                    vscode.window.showInformationMessage('Not signed in to VoiceHype');
                }
            })
        );

        // Configuration switching commands
        this.disposables.push(
            vscode.commands.registerCommand('voicehype.nextConfiguration', async () => {
                await this.switchToNextConfiguration();
            })
        );

        this.disposables.push(
            vscode.commands.registerCommand('voicehype.previousConfiguration', async () => {
                await this.switchToPreviousConfiguration();
            })
        );
    }
    
    private async showServiceSelectionQuickPick(): Promise<void> {
        if (!this.configService) {
            vscode.window.showErrorMessage('Configuration service not available');
            return;
        }

        const services = [
            {
                label: 'AssemblyAI',
                id: 'assemblyai'
            },
            {
                label: 'Whisper',
                id: 'lemonfox'
            }
        ];

        try {
            const currentService = this.configService.getTranscriptionService();
            const selected = await vscode.window.showQuickPick(services, {
                placeHolder: 'Select Transcription Service'
            });

            if (selected && this.configService) {
                // Update the service setting
                await this.configService.updateSetting('voicehype.transcription.service', selected.id);

                // Update model to match the selected service
                const currentModel = this.configService.getTranscriptionModel();
                if (selected.id === 'lemonfox' && currentModel !== 'whisper-1') {
                    await this.configService.updateSetting('voicehype.transcription.model', 'whisper-1');
                    vscode.window.showInformationMessage(`Transcription service set to ${selected.label} and model automatically set to whisper-1`);
                } else if (selected.id === 'assemblyai' && !['best', 'nano'].includes(currentModel)) {
                    await this.configService.updateSetting('voicehype.transcription.model', 'best');
                    vscode.window.showInformationMessage(`Transcription service set to ${selected.label} and model automatically set to best`);
                } else {
                    vscode.window.showInformationMessage(`Transcription service set to ${selected.label}`);
                }
            }
        } catch (error) {
            console.error('Error in showServiceSelectionQuickPick:', error);
            vscode.window.showErrorMessage('Failed to update transcription service');
        }
    }

    private async showModelSelectionQuickPick(): Promise<void> {
        if (!this.configService) {
            vscode.window.showErrorMessage('Configuration service not available');
            return;
        }

        try {
            const service = this.configService.getTranscriptionService();
            const currentModel = this.configService.getTranscriptionModel();
            const currentLanguage = this.configService.getTranscriptionLanguage();

            // Initialize models array based on the selected service
            let models: { label: string; id: string }[] = [];

            if (service === 'assemblyai') {
                models = [
                    { label: 'Best - Higher accuracy', id: 'best' },
                    { label: 'Nano - Faster processing', id: 'nano' }
                ];
            } else if (service === 'lemonfox') {
                models = [
                    { label: 'Whisper-v3 - LemonFox Whisper model', id: 'whisper-1' }
                ];
            }

            let validModel = currentModel;

            // If current model is not valid for the service, set a default
            if (service === 'lemonfox' && currentModel !== 'whisper-1') {
                validModel = 'whisper-1';
                await this.configService.updateSetting('voicehype.transcription.model', validModel);
                vscode.window.showInformationMessage(`Model automatically set to whisper-1 for Whisper service`);
            } else if (service === 'assemblyai' && !['best', 'nano'].includes(currentModel)) {
                validModel = 'best';
                await this.configService.updateSetting('voicehype.transcription.model', validModel);
                vscode.window.showInformationMessage(`Model automatically set to best for AssemblyAI service`);
            }

            // Show the quick pick with the valid models
            const selected = await vscode.window.showQuickPick(models, {
                placeHolder: 'Select Transcription Model'
            });

            if (selected && this.configService) {
                await this.configService.updateSetting('voicehype.transcription.model', selected.id);
                vscode.window.showInformationMessage(`Transcription model set to ${selected.label}`);

                // Check if the current language is compatible with the new model
                if (service === 'assemblyai') {
                    const supportedLanguages = selected.id === 'best'
                        ? this.getAssemblyAIBestLanguages().map(l => l.id)
                        : this.getAssemblyAINanoLanguages().map(l => l.id);

                    if (!supportedLanguages.includes(currentLanguage)) {
                        // Language not supported, reset to English
                        await this.configService.updateSetting('voicehype.transcription.language', 'en');
                        vscode.window.showInformationMessage(
                            `Language '${currentLanguage}' is not supported by the selected model. Reset to English.`
                        );
                    }
                }
            }
        } catch (error) {
            console.error('Error in showModelSelectionQuickPick:', error);
            vscode.window.showErrorMessage('Failed to update transcription model');
        }
    }

    private async showLanguageSelectionQuickPick(): Promise<void> {
        if (!this.configService) {
            vscode.window.showErrorMessage('Configuration service not available');
            return;
        }

        try {
            const service = this.configService.getTranscriptionService();
            const model = this.configService.getTranscriptionModel();
            let languages: { label: string; id: string }[] = [];

            if (service === 'whisper' || service === 'lemonfox') {
                // Both Whisper and OpenAI support many languages regardless of model
                languages = [
                    { label: 'Auto-detect', id: 'auto' },
                    { label: 'Afrikaans', id: 'af' },
                    { label: 'Arabic', id: 'ar' },
                    { label: 'Armenian', id: 'hy' },
                    { label: 'Azerbaijani', id: 'az' },
                    { label: 'Belarusian', id: 'be' },
                    { label: 'Bosnian', id: 'bs' },
                    { label: 'Bulgarian', id: 'bg' },
                    { label: 'Catalan', id: 'ca' },
                    { label: 'Chinese', id: 'zh' },
                    { label: 'Croatian', id: 'hr' },
                    { label: 'Czech', id: 'cs' },
                    { label: 'Danish', id: 'da' },
                    { label: 'Dutch', id: 'nl' },
                    { label: 'English', id: 'en' },
                    { label: 'Estonian', id: 'et' },
                    { label: 'Finnish', id: 'fi' },
                    { label: 'French', id: 'fr' },
                    { label: 'Galician', id: 'gl' },
                    { label: 'German', id: 'de' },
                    { label: 'Greek', id: 'el' },
                    { label: 'Hindi', id: 'hi' },
                    { label: 'Hungarian', id: 'hu' },
                    { label: 'Icelandic', id: 'is' },
                    { label: 'Indonesian', id: 'id' },
                    { label: 'Italian', id: 'it' },
                    { label: 'Japanese', id: 'ja' },
                    { label: 'Kannada', id: 'kn' },
                    { label: 'Kazakh', id: 'kk' },
                    { label: 'Korean', id: 'ko' },
                    { label: 'Latvian', id: 'lv' },
                    { label: 'Lithuanian', id: 'lt' },
                    { label: 'Macedonian', id: 'mk' },
                    { label: 'Malay', id: 'ms' },
                    { label: 'Marathi', id: 'mr' },
                    { label: 'Maori', id: 'mi' },
                    { label: 'Nepali', id: 'ne' },
                    { label: 'Norwegian', id: 'no' },
                    { label: 'Persian', id: 'fa' },
                    { label: 'Polish', id: 'pl' },
                    { label: 'Portuguese', id: 'pt' },
                    { label: 'Romanian', id: 'ro' },
                    { label: 'Russian', id: 'ru' },
                    { label: 'Serbian', id: 'sr' },
                    { label: 'Slovak', id: 'sk' },
                    { label: 'Slovenian', id: 'sl' },
                    { label: 'Spanish', id: 'es' },
                    { label: 'Swahili', id: 'sw' },
                    { label: 'Swedish', id: 'sv' },
                    { label: 'Tagalog', id: 'tl' },
                    { label: 'Tamil', id: 'ta' },
                    { label: 'Thai', id: 'th' },
                    { label: 'Turkish', id: 'tr' },
                    { label: 'Ukrainian', id: 'uk' },
                    { label: 'Urdu', id: 'ur' },
                    { label: 'Vietnamese', id: 'vi' },
                    { label: 'Welsh', id: 'cy' }
                ];
            } else if (service === 'assemblyai') {
                if (model === 'best') {
                    // AssemblyAI 'best' model supports these languages
                    languages = this.getAssemblyAIBestLanguages();
                } else {
                    // AssemblyAI 'nano' model supports many more languages
                    languages = this.getAssemblyAINanoLanguages();
                }
            }

            // Add a search box to help users find languages in the long list
            const selected = await vscode.window.showQuickPick(languages, {
                placeHolder: 'Select Transcription Language (type to search)',
                matchOnDescription: true,
                matchOnDetail: true
            });

            if (selected && this.configService) {
                await this.configService.updateSetting('voicehype.transcription.language', selected.id);
                vscode.window.showInformationMessage(`Transcription language set to ${selected.label}`);
            }
        } catch (error) {
            console.error('Error in showLanguageSelectionQuickPick:', error);
            vscode.window.showErrorMessage('Failed to update transcription language');
        }
    }

    private async showOptimizationModelSelectionQuickPick(): Promise<void> {
        if (!this.configService) {
            vscode.window.showErrorMessage('Configuration service not available');
            return;
        }

        try {
            const currentModel = this.configService.getOptimizationModel();

            const models = [
                // Claude Models (Premium - Listed First)
                { label: 'Claude 4 Sonnet', description: 'Anthropic\'s most advanced model', id: 'claude-4-sonnet', picked: currentModel === 'claude-4-sonnet' },
                { label: 'Claude 3.7 Sonnet', description: 'Anthropic\'s latest model', id: 'claude-3.7-sonnet', picked: currentModel === 'claude-3.7-sonnet' },
                { label: 'Claude 3.5 Sonnet', description: 'Anthropic\'s balanced model', id: 'claude-3.5-sonnet', picked: currentModel === 'claude-3.5-sonnet' },
                { label: 'Claude Haiku', description: 'Anthropic\'s fast model', id: 'claude-haiku', picked: currentModel === 'claude-haiku' },

                // Llama Models
                { label: 'Llama 3.1 70B', description: 'Meta\'s 70B parameter model', id: 'llama-3.1-70b', picked: currentModel === 'llama-3.1-70b' },
                { label: 'Llama 3.1 8B Instruct Turbo', description: 'Meta\'s 8B parameter model optimized for speed', id: 'llama-3.1-8b-instruct-turbo', picked: currentModel === 'llama-3.1-8b-instruct-turbo' },

                // DeepSeek Models
                { label: 'DeepSeek V3', description: 'DeepSeek\'s latest model', id: 'deepseek-v3', picked: currentModel === 'deepseek-v3' }
            ];

            // Add a visual indicator to the current model
            const modelsWithHighlight = models.map(model => {
                if (model.id === currentModel) {
                    return { ...model, label: `$(check) ${model.label}` };
                }
                return model;
            });

            const selected = await vscode.window.showQuickPick(modelsWithHighlight, {
                placeHolder: 'Optimization Model'
            });

            if (selected && this.configService) {
                await this.configService.updateSetting('voicehype.transcription.optimizationModel', selected.id);
                vscode.window.showInformationMessage(`Optimization model set to ${selected.label.replace('$(check) ', '')}`);
            }
        } catch (error) {
            console.error('Error in showOptimizationModelSelectionQuickPick:', error);
            vscode.window.showErrorMessage('Failed to update optimization model');
        }
    }

    private async showAudioSettingsQuickPick(): Promise<void> {
        const audioSettings = this.configService.getAudioSettings();
        const currentDevice = audioSettings.device || '(Default)';

        const audioSettingItems = [
            {
                label: 'Sample Rate',
                description: `Current: ${audioSettings.sampleRate}Hz`,
                id: 'sample-rate'
            },
            {
                label: 'Audio Device',
                description: `Current: ${currentDevice}`,
                id: 'audio-device'
            },
            {
                label: 'Select Audio Device',
                description: 'Choose from detected input devices',
                id: 'select-audio-device'
            }
        ];

        const selected = await vscode.window.showQuickPick(audioSettingItems, {
            placeHolder: 'Select Audio Setting to Modify'
        });

        if (!selected) {
            return;
        }

        if (selected.id === 'sample-rate') {
            const sampleRates = [
                { label: '16000 Hz', description: 'Recommended for transcription' },
                { label: '22050 Hz', description: 'Balanced quality' },
                { label: '44100 Hz', description: 'CD Quality' },
                { label: '48000 Hz', description: 'Professional Audio' }
            ];

            const newRate = await vscode.window.showQuickPick(sampleRates, {
                placeHolder: 'Select Sample Rate'
            });

            if (newRate) {
                await this.configService.updateSetting(
                    'voicehype.audio.sampleRate',
                    parseInt(newRate.label)
                );
                vscode.window.showInformationMessage(`Sample rate set to ${newRate.label}`);
            }
        } else if (selected.id === 'audio-device') {
            const deviceOptions = [
                { label: '(Default)', description: 'Use system default audio device' },
                { label: 'pulse', description: 'PulseAudio (Linux)' },
                { label: 'alsa', description: 'ALSA (Linux)' },
                { label: 'coreaudio', description: 'CoreAudio (macOS)' },
                { label: 'directsound', description: 'DirectSound (Windows)' }
            ];

            const newDevice = await vscode.window.showQuickPick(deviceOptions, {
                placeHolder: 'Select Audio Device'
            });

            if (newDevice) {
                // If default is selected, set to empty string
                const deviceValue = newDevice.label === '(Default)' ? '' : newDevice.label;
                await this.configService.updateSetting('voicehype.audio.device', deviceValue);
                vscode.window.showInformationMessage(
                    `Audio device set to ${newDevice.label === '(Default)' ? 'system default' : newDevice.label}`
                );
            }
        } else if (selected.id === 'select-audio-device') {
            // Use AudioDeviceManager to show device selection dialog
            await vscode.commands.executeCommand('voicehype.selectAudioDevice');
        }
    }

    // Common function to update settings and UI
    private async updateSettingAndUI(key: string, value: any, message?: string): Promise<void> {
        await this.configService.updateSetting(key, value);

        // Explicitly update status bar if available
        if (this.statusBarService) {
            console.log(`VoiceHype: Explicitly updating status bar after changing ${key}`);
            this.statusBarService.updateStatusBarItems(
                this.recordingService.isRecording(),
                false,
                this.recordingService.isPaused()
            );
        }

        if (message) {
            vscode.window.showInformationMessage(message);
        }
    }

    private getAssemblyAIBestLanguages(): { label: string; id: string }[] {
        return [
            { label: 'Global English', id: 'en' },
            { label: 'Australian English', id: 'en_au' },
            { label: 'British English', id: 'en_uk' },
            { label: 'US English', id: 'en_us' },
            { label: 'Spanish', id: 'es' },
            { label: 'French', id: 'fr' },
            { label: 'German', id: 'de' },
            { label: 'Italian', id: 'it' },
            { label: 'Portuguese', id: 'pt' },
            { label: 'Dutch', id: 'nl' },
            { label: 'Hindi', id: 'hi' },
            { label: 'Japanese', id: 'ja' },
            { label: 'Chinese', id: 'zh' },
            { label: 'Finnish', id: 'fi' },
            { label: 'Korean', id: 'ko' },
            { label: 'Polish', id: 'pl' },
            { label: 'Russian', id: 'ru' },
            { label: 'Turkish', id: 'tr' },
            { label: 'Ukrainian', id: 'uk' },
            { label: 'Vietnamese', id: 'vi' }
        ];
    }

    private getAssemblyAINanoLanguages(): { label: string; id: string }[] {
        return [
            { label: 'Global English', id: 'en' },
            { label: 'Australian English', id: 'en_au' },
            { label: 'British English', id: 'en_uk' },
            { label: 'American English', id: 'en_us' },
            { label: 'Spanish', id: 'es' },
            { label: 'French', id: 'fr' },
            { label: 'German', id: 'de' },
            { label: 'Italian', id: 'it' },
            { label: 'Portuguese', id: 'pt' },
            { label: 'Dutch', id: 'nl' },
            { label: 'Afrikaans', id: 'af' },
            { label: 'Albanian', id: 'sq' },
            { label: 'Amharic', id: 'am' },
            { label: 'Arabic', id: 'ar' },
            { label: 'Armenian', id: 'hy' },
            { label: 'Assamese', id: 'as' },
            { label: 'Azerbaijani', id: 'az' },
            { label: 'Bashkir', id: 'ba' },
            { label: 'Basque', id: 'eu' },
            { label: 'Belarusian', id: 'be' },
            { label: 'Bengali', id: 'bn' },
            { label: 'Bosnian', id: 'bs' },
            { label: 'Breton', id: 'br' },
            { label: 'Bulgarian', id: 'bg' },
            { label: 'Burmese', id: 'my' },
            { label: 'Catalan', id: 'ca' },
            { label: 'Chinese', id: 'zh' },
            { label: 'Croatian', id: 'hr' },
            { label: 'Czech', id: 'cs' },
            { label: 'Danish', id: 'da' },
            { label: 'Estonian', id: 'et' },
            { label: 'Faroese', id: 'fo' },
            { label: 'Finnish', id: 'fi' },
            { label: 'Galician', id: 'gl' },
            { label: 'Georgian', id: 'ka' },
            { label: 'Greek', id: 'el' },
            { label: 'Gujarati', id: 'gu' },
            { label: 'Haitian', id: 'ht' },
            { label: 'Hausa', id: 'ha' },
            { label: 'Hawaiian', id: 'haw' },
            { label: 'Hindi', id: 'hi' },
            { label: 'Hungarian', id: 'hu' },
            { label: 'Icelandic', id: 'is' },
            { label: 'Indonesian', id: 'id' },
            { label: 'Japanese', id: 'ja' },
            { label: 'Javanese', id: 'jv' },
            { label: 'Kannada', id: 'kn' },
            { label: 'Kazakh', id: 'kk' },
            { label: 'Khmer', id: 'km' },
            { label: 'Korean', id: 'ko' },
            { label: 'Lao', id: 'lo' },
            { label: 'Latin', id: 'la' },
            { label: 'Latvian', id: 'lv' },
            { label: 'Lingala', id: 'ln' },
            { label: 'Lithuanian', id: 'lt' },
            { label: 'Luxembourgish', id: 'lb' },
            { label: 'Macedonian', id: 'mk' },
            { label: 'Malagasy', id: 'mg' },
            { label: 'Malay', id: 'ms' },
            { label: 'Malayalam', id: 'ml' },
            { label: 'Maltese', id: 'mt' },
            { label: 'Maori', id: 'mi' },
            { label: 'Marathi', id: 'mr' },
            { label: 'Mongolian', id: 'mn' },
            { label: 'Nepali', id: 'ne' },
            { label: 'Norwegian', id: 'no' },
            { label: 'Norwegian Nynorsk', id: 'nn' },
            { label: 'Occitan', id: 'oc' },
            { label: 'Panjabi', id: 'pa' },
            { label: 'Pashto', id: 'ps' },
            { label: 'Persian', id: 'fa' },
            { label: 'Polish', id: 'pl' },
            { label: 'Romanian', id: 'ro' },
            { label: 'Russian', id: 'ru' },
            { label: 'Sanskrit', id: 'sa' },
            { label: 'Serbian', id: 'sr' },
            { label: 'Shona', id: 'sn' },
            { label: 'Sindhi', id: 'sd' },
            { label: 'Sinhala', id: 'si' },
            { label: 'Slovak', id: 'sk' },
            { label: 'Slovenian', id: 'sl' },
            { label: 'Somali', id: 'so' },
            { label: 'Sundanese', id: 'su' },
            { label: 'Swahili', id: 'sw' },
            { label: 'Swedish', id: 'sv' },
            { label: 'Tagalog', id: 'tl' },
            { label: 'Tajik', id: 'tg' },
            { label: 'Tamil', id: 'ta' },
            { label: 'Tatar', id: 'tt' },
            { label: 'Telugu', id: 'te' },
            { label: 'Thai', id: 'th' },
            { label: 'Tibetan', id: 'bo' },
            { label: 'Turkish', id: 'tr' },
            { label: 'Turkmen', id: 'tk' },
            { label: 'Ukrainian', id: 'uk' },
            { label: 'Urdu', id: 'ur' },
            { label: 'Uzbek', id: 'uz' },
            { label: 'Vietnamese', id: 'vi' },
            { label: 'Welsh', id: 'cy' },
            { label: 'Yiddish', id: 'yi' },
            { label: 'Yoruba', id: 'yo' }
        ];
    }

    private async showQuickSettings(): Promise<void> {
        const service = this.configService.getTranscriptionService();
        const model = this.configService.getTranscriptionModel();
        const realtime = this.configService.getTranscriptionRealtime();

        const items = [
            {
                label: `$(mic) ${this.configService.getShouldOptimize() ? 'AI-Optimized' : 'Basic'} Recording Mode`,
                description: this.configService.getShouldOptimize() ? 'AI optimization enabled' : 'Basic transcription only',
                detail: 'Toggle between basic transcription and AI-optimized mode',
                id: 'toggle-mode'
            },
            {
                label: '$(device-desktop) Audio Settings',
                description: 'Configure microphone and audio quality',
                detail: 'Set sample rate and audio device',
                id: 'audio-settings'
            },
            {
                label: '$(server) Transcription Service',
                description: `Current: ${service === 'lemonfox' ? 'Whisper' : service}`,
                detail: 'Select between AssemblyAI and Whisper',
                id: 'service-selection'
            },
            // Only show model selection for AssemblyAI
            ...(service === 'assemblyai' ? [{
                label: '$(versions) Transcription Model',
                description: `Current: ${model}`,
                detail: 'Select the model for transcription',
                id: 'model-selection'
            }] : []),
            {
                label: '$(globe) Transcription Language',
                description: `Current: ${this.configService.getTranscriptionLanguage()}`,
                detail: 'Select the language for transcription',
                id: 'language-selection'
            }
        ];

        // Add real-time option only for AssemblyAI service
        if (service === 'assemblyai' && ['best', 'nano'].includes(model)) {
            items.push({
                label: `$(bolt) Real-time Transcription`,
                description: realtime ? 'Enabled' : 'Disabled',
                detail: 'Toggle real-time transcription',
                id: 'toggle-realtime'
            });
        }

        items.push(
            {
                label: '$(sparkle) Optimization Model',
                description: `Current: ${this.configService.getOptimizationModel()}`,
                detail: 'Choose the AI model used for optimizing transcriptions',
                id: 'optimization-model-selection'
            },
            {
                label: '$(key) API Key',
                description: 'Configure your VoiceHype API key',
                detail: 'Set up your VoiceHype API key',
                id: 'api-key'
            },
            {
                label: '$(settings-gear) All Settings',
                description: 'Open all VoiceHype settings',
                detail: 'Configure all VoiceHype settings',
                id: 'all-settings'
            }
        );

        const selected = await vscode.window.showQuickPick(items, {
            placeHolder: 'VoiceHype Quick Settings'
        });

        if (!selected) {
            return;
        }

        switch (selected.id) {
            case 'toggle-mode':
                await vscode.commands.executeCommand('voicehype.toggleOptimizationMode');
                break;
            case 'audio-settings':
                await this.showAudioSettingsQuickPick();
                break;
            case 'service-selection':
                await this.showServiceSelectionQuickPick();
                break;
            case 'model-selection':
                await this.showModelSelectionQuickPick();
                break;
            case 'language-selection':
                await this.showLanguageSelectionQuickPick();
                break;
            case 'toggle-realtime':
                const newRealtimeState = !realtime;
                await this.configService.updateSetting('voicehype.transcription.realtime', newRealtimeState);
                // Handle sample rate switching
                await this.configService.handleRealtimeToggle(newRealtimeState);
                vscode.window.showInformationMessage(
                    `Real-time transcription ${newRealtimeState ? 'enabled' : 'disabled'}${
                        newRealtimeState ? ' (Sample rate set to 16000Hz for optimal performance)' : ''
                    }`
                );
                break;
            case 'optimization-model-selection':
                await this.showOptimizationModelSelectionQuickPick();
                break;
            case 'api-key':
                await vscode.commands.executeCommand('workbench.action.openSettings', 'voicehype.apiKey');
                break;
            case 'all-settings':
                await vscode.commands.executeCommand('workbench.action.openSettings', 'voicehype');
                break;
        }
    }

    // Add a new method to register a command for toggling real-time transcription mode
    private registerToggleRealtimeTranscriptionCommand(): vscode.Disposable {
        return vscode.commands.registerCommand('voicehype.toggleRealtimeTranscription', async () => {
            const currentValue = this.configService.getTranscriptionRealtime();
            const newValue = !currentValue;
            const currentService = this.configService.getTranscriptionService();

            // Check if we're using Whisper service
            if (currentService === 'lemonfox') {
                vscode.window.showInformationMessage(
                    'Real-time transcription is only available with AssemblyAI service.'
                );
                return;
            }

            if (this.configService) {
                await this.configService.updateSetting('voicehype.transcription.realtime', newValue);

                // Handle sample rate switching
                await this.configService?.handleRealtimeToggle(newValue);
            }

            vscode.window.showInformationMessage(
                `Real-time transcription mode ${newValue ? 'enabled' : 'disabled'}${newValue ? ' (Sample rate set to 16000Hz for optimal performance)' : ''}`
            );
        });
    }

    private registerAuthenticationCommands(): vscode.Disposable[] {
        const disposables: vscode.Disposable[] = [];

        // These commands are already registered in the main registerCommands method
        // This is just a placeholder method to maintain compatibility

        return disposables;
    }

    /**
     * Switch to the next configuration in the list
     */
    private async switchToNextConfiguration(): Promise<void> {
        try {
            const configurations = this.configService.getConfigurations();
            if (configurations.length === 0) {
                vscode.window.showInformationMessage('No configurations available. Create one first.');
                return;
            }

            const activeId = this.configService.getActiveConfigurationId();
            let nextIndex = 0;

            if (activeId) {
                const currentIndex = configurations.findIndex((c: any) => c.id === activeId);
                nextIndex = (currentIndex + 1) % configurations.length;
            }

            const nextConfig = configurations[nextIndex];
            const configName = await this.configService.switchConfiguration(nextConfig.id);

            vscode.window.showInformationMessage(`Switched to '${configName}'`);
        } catch (error) {
            console.error('VoiceHype: Error switching to next configuration:', error);
            vscode.window.showErrorMessage('Failed to switch configuration');
        }
    }

    /**
     * Switch to the previous configuration in the list
     */
    private async switchToPreviousConfiguration(): Promise<void> {
        try {
            const configurations = this.configService.getConfigurations();
            if (configurations.length === 0) {
                vscode.window.showInformationMessage('No configurations available. Create one first.');
                return;
            }

            const activeId = this.configService.getActiveConfigurationId();
            let prevIndex = configurations.length - 1;

            if (activeId) {
                const currentIndex = configurations.findIndex((c: any) => c.id === activeId);
                prevIndex = currentIndex > 0 ? currentIndex - 1 : configurations.length - 1;
            }

            const prevConfig = configurations[prevIndex];
            const configName = await this.configService.switchConfiguration(prevConfig.id);

            vscode.window.showInformationMessage(`Switched to '${configName}'`);
        } catch (error) {
            console.error('VoiceHype: Error switching to previous configuration:', error);
            vscode.window.showErrorMessage('Failed to switch configuration');
        }
    }

    dispose(): void {
        if (this.disposables) {
            this.disposables.forEach(d => {
                if (d) {d.dispose();}
            });
        }
    }
}

import * as vscode from 'vscode';
import { EventEmitter } from 'events';
import { IConfigurationService } from '../models/interfaces';
import { ASSEMBLYAI_BEST_LANGUAGES, ASSEMBLYAI_NANO_LANGUAGES, WHISPER_LANGUAGES } from '../utils/replicateModels';
import { SecretsService } from './SecretsService';

export class ConfigurationService implements IConfigurationService {
    // Add an event emitter for settings changes
    private _onDidChangeConfiguration = new vscode.EventEmitter<string>();
    readonly onDidChangeConfiguration = this._onDidChangeConfiguration.event;

    // Add an event specifically for sample rate changes
    private _onDidChangeSampleRate = new vscode.EventEmitter<number>();
    readonly onDidChangeSampleRate = this._onDidChangeSampleRate.event;

    // Add debounce timer for multiple configuration changes
    private configChangeTimer: NodeJS.Timeout | null = null;
    private pendingConfigChanges: Set<string> = new Set();

    // In-memory fallback for custom prompt when configuration is not registered
    private _inMemoryCustomPrompt: string = '';

    // Store previous sample rate when switching to real-time mode
    private _previousSampleRate: number = 44100;
    
    // Secrets service for secure storage
    private _secretsService: SecretsService | undefined;

    constructor(secretsService?: SecretsService) {
        this._secretsService = secretsService;
        // Validate settings on initialization
        this.validateSettings();
    }

    private async validateSettings(): Promise<void> {
        const service = this.getTranscriptionService();
        const model = this.getTranscriptionModel();
        const realtime = this.getTranscriptionRealtime();

        // Simple validation: ensure compatible model with service
        if (service === 'lemonfox' && !['whisper-1', 'gpt-4o-mini-transcribe', 'gpt-4o-transcribe'].includes(model)) {
            await this.updateSetting('voicehype.transcription.model', 'whisper-1');
            console.log('ConfigurationService: Set model to whisper-1 for OpenAI service');
        } else if (service === 'assemblyai' && !['best', 'nano'].includes(model)) {
            await this.updateSetting('voicehype.transcription.model', 'best');
            console.log('ConfigurationService: Set model to best for AssemblyAI service');
        }

        // Disable realtime for Whisper service (lemonfox)
        if (realtime && service === 'lemonfox') {
            await this.updateSetting('voicehype.transcription.realtime', false);
            console.log('ConfigurationService: Disabled realtime for Whisper service');
        }
    }

    getApiKey(): string | undefined {
        // This synchronous method is deprecated and will always return undefined
        // Use getApiKeyAsync instead
        console.warn('[ConfigService] getApiKey() is deprecated, use getApiKeyAsync() instead');
        return undefined;
    }

    /**
     * Asynchronously gets the API key, preferring secure storage if available
     * @returns Promise resolving to the API key or undefined
     */
    async getApiKeyAsync(): Promise<string | undefined> {
        try {
            if (!this._secretsService) {
                console.error('[ConfigService] No secrets service available');
                return undefined;
            }
            
            return await this._secretsService.getApiKey();
        } catch (e) {
            console.error('[ConfigService] Error retrieving API key:', e);
            return undefined;
        }
    }

    /**
     * Sets the API key, storing it securely if possible
     * @param apiKey The API key to store
     */
    async setApiKey(apiKey: string): Promise<void> {
        try {
            if (!this._secretsService) {
                console.error('[ConfigService] No secrets service available');
                return;
            }
            
            await this._secretsService.storeApiKey(apiKey);
            this._queueConfigChangeNotification('apiKey');
        } catch (e) {
            console.error('[ConfigService] Error storing API key:', e);
        }
    }

    getTranscriptionModel(): string {
        try {
            const service = this.getTranscriptionService();

            // Use the direct setting path based on package.json
            if (service === 'lemonfox') {
                return vscode.workspace.getConfiguration('voicehype.transcription').get<string>('model', 'whisper-1');
            } else if (service === 'assemblyai') {
                return vscode.workspace.getConfiguration('voicehype.transcription').get<string>('model', 'best');
            }

            // Fallback to just returning the model setting
            const model = vscode.workspace.getConfiguration('voicehype.transcription').get<string>('model', 'whisper-1');
            console.log(`[ConfigService] Retrieved ${service} model: ${model}`);
            return model;
        } catch (e) {
            console.error('[ConfigService] Error retrieving transcription model:', e);
            return 'whisper-1';
        }
    }

    getTranscriptionService(): string {
        try {
            const service = vscode.workspace.getConfiguration('voicehype.transcription').get<string>('service', 'lemonfox');
            console.log(`[ConfigService] Retrieved transcription service: ${service}`);

            // For backwards compatibility: if service is still "whisper", convert it to "lemonfox"
            if (service === 'whisper') {
                console.log(`[ConfigService] Converting legacy service name 'whisper' to 'lemonfox'`);
                // Update the setting to the new name
                this.updateSetting('voicehype.transcription.service', 'lemonfox')
                    .catch(err => console.error('[ConfigService] Error updating legacy service name:', err));
                return 'lemonfox';
            }

            return service;
        } catch (e) {
            console.error('[ConfigService] Error retrieving transcription service:', e);
            return 'lemonfox'; // Updated default to lemonfox instead of whisper
        }
    }

    getTranscriptionLanguage(): string {
        try {
            // Use the path that matches package.json: voicehype.transcription.language
            const language = vscode.workspace.getConfiguration('voicehype.transcription').get<string>('language', 'en');
            console.log(`[ConfigService] Retrieved transcription language: ${language}`);
            return language;
        } catch (e) {
            console.error('[ConfigService] Error retrieving transcription language:', e);
            return 'en';
        }
    }

    getTranscriptionRealtime(): boolean {
        return vscode.workspace.getConfiguration('voicehype').get<boolean>('transcription.realtime') || false;
    }

    getOptimizationModel(): string {
        try {
            const model = vscode.workspace.getConfiguration('voicehype.transcription').get<string>('optimizationModel', 'gpt-4o');
            console.log(`[ConfigService] Retrieved optimization model: ${model}`);
            return model;
        } catch (e) {
            console.error('[ConfigService] Error retrieving optimization model:', e);
            return 'gpt-4o';
        }
    }

    getShouldOptimize(): boolean {
        try {
            const optimize = vscode.workspace.getConfiguration('voicehype.transcription').get<boolean>('shouldOptimize', false);
            console.log(`[ConfigService] Retrieved shouldOptimize setting: ${optimize}`);
            return optimize;
        } catch (e) {
            console.error('[ConfigService] Error retrieving shouldOptimize setting:', e);
            return false;
        }
    }

    getPromptModes(): { custom: any[], active: string } {
        try {
            const customModes = vscode.workspace.getConfiguration('voicehype.promptModes').get<any[]>('custom', []);
            const activeMode = vscode.workspace.getConfiguration('voicehype.promptModes').get<string>('active', 'clean-up');
            
            console.log(`[ConfigService] Retrieved prompt modes - active: ${activeMode}, custom count: ${customModes?.length || 0}`);
            
            return {
                custom: customModes || [],
                active: activeMode
            };
        } catch (e) {
            console.error('[ConfigService] Error retrieving prompt modes:', e);
            return {
                custom: [],
                active: ''
            };
        }
    }

    getCustomPrompt(): string {
        try {
            const customPrompt = vscode.workspace.getConfiguration('voicehype.transcription').get<string>('customPrompt');
            // If there's a custom prompt and it's valid, return it
            if (customPrompt && typeof customPrompt === 'string' && customPrompt.trim() !== '') {
                console.log(`[ConfigService] Retrieved custom prompt (length: ${customPrompt.length})`);
                return customPrompt;
            }
        } catch (error) {
            console.log('[ConfigService] Error getting custom prompt from configuration, using in-memory fallback');
            // If there's an error getting from configuration, use the in-memory fallback
            if (this._inMemoryCustomPrompt && this._inMemoryCustomPrompt.trim() !== '') {
                return this._inMemoryCustomPrompt;
            }
        }
        // Otherwise return an empty string (default prompt will be used instead)
        return '';
    }

    getTranslate(): boolean {
        try {
            const translate = vscode.workspace.getConfiguration('voicehype.transcription').get<boolean>('translate', false);
            console.log(`[ConfigService] Retrieved translate setting: ${translate}`);
            return translate;
        } catch (e) {
            console.error('[ConfigService] Error retrieving translate setting:', e);
            return false;
        }
    }

    getVoiceCommandsEnabled(): boolean {
        try {
            const voiceCommandsEnabled = vscode.workspace.getConfiguration('voicehype').get<boolean>('voiceCommandsEnabled', true);
            console.log(`[ConfigService] Retrieved voiceCommandsEnabled setting: ${voiceCommandsEnabled}`);
            return voiceCommandsEnabled;
        } catch (e) {
            console.error('[ConfigService] Error retrieving voiceCommandsEnabled setting:', e);
            return true; // Default to true if there's an error
        }
    }

    async setVoiceCommandsEnabled(enabled: boolean): Promise<void> {
        try {
            console.log(`[ConfigService] Setting voiceCommandsEnabled to: ${enabled}`);
            await this.updateSetting('voicehype.voiceCommandsEnabled', enabled);
        } catch (e) {
            console.error('[ConfigService] Error setting voiceCommandsEnabled:', e);
        }
    }

    getAudioDevice(): string | null {
        try {
            const device = vscode.workspace.getConfiguration('voicehype.audio').get<string>('device', '');
            return device?.trim() || null;
        } catch (e) {
            console.error('[ConfigService] Error retrieving audio device:', e);
            return null;
        }
    }

    getAudioSettings(): { sampleRate: number; device?: string } {
        try {
            const sampleRate = vscode.workspace.getConfiguration('voicehype.audio').get<number>('sampleRate', 22050);
            const deviceSetting = vscode.workspace.getConfiguration('voicehype.audio').get<string>('device', '');

            console.log(`[ConfigService] Retrieved audio settings - sampleRate: ${sampleRate}, device: ${deviceSetting || 'default'} (type: ${typeof deviceSetting})`);
            
            // Ensure device is a string if it's defined
            const deviceSettingProcessed = deviceSetting && deviceSetting.toString().trim() !== '' ? 
                deviceSetting.toString() : undefined;
                
            return {
                sampleRate,
                ...(deviceSettingProcessed ? { device: deviceSettingProcessed } : {})
            };
        } catch (e) {
            console.error('[ConfigService] Error retrieving audio settings:', e);
            return { sampleRate: 22050 };
        }
    }

    async updateSetting(key: string, value: any): Promise<void> {
        try {
            console.log(`[ConfigService] Updating setting ${key} to:`, value);

            // Special handling for audio settings
            if (key === 'voicehype.audio.sampleRate') {
                await vscode.workspace.getConfiguration().update(key, value, true);
                this._onDidChangeSampleRate.fire(value);
                this._queueConfigChangeNotification(key);
                return;
            }

            // Handle customPrompt separately to ensure it's a string
            if (key === 'customPrompt' && typeof value !== 'string') {
                value = String(value || '');
                console.log(`[ConfigService] Converted customPrompt to string: ${value.substring(0, 20)}...`);
            }

            // Determine the correct setting path based on known settings
            let settingKey = key;

            // Map short keys to their proper paths
            const settingMap: Record<string, string> = {
                'service': 'voicehype.transcription.service',
                'transcriptionService': 'voicehype.transcription.service',
                'model': 'voicehype.transcription.model',
                'whisperModel': 'voicehype.transcription.model',
                'assemblyAIModel': 'voicehype.transcription.model',
                'language': 'voicehype.transcription.language',
                'optimize': 'voicehype.transcription.shouldOptimize',
                'shouldOptimize': 'voicehype.transcription.shouldOptimize',
                'customPrompt': 'voicehype.transcription.customPrompt',
                'translate': 'voicehype.transcription.translate',
                'realtime': 'voicehype.transcription.realtime',
                'optimizationModel': 'voicehype.transcription.optimizationModel',
                'voiceCommandsEnabled': 'voicehype.voiceCommandsEnabled'
            };

            // Use the mapping if it exists
            if (settingMap[key]) {
                settingKey = settingMap[key];
                console.log(`[ConfigService] Mapped setting key: ${key} -> ${settingKey}`);
            }
            // If the key already has a full path format, keep it as is
            else if (key.startsWith('voicehype.')) {
                settingKey = key;
            }
            // Otherwise, add the prefix (deprecated, kept for backwards compatibility)
            else {
                console.warn(`[ConfigService] Warning: Using unmapped setting key: ${key}`);
            }

            // Break the key into its components for configuration
            let configSection: string;
            let configKey: string;

            if (settingKey.includes('.')) {
                const parts = settingKey.split('.');
                if (parts.length >= 2) {
                    // For keys like 'voicehype.transcription.language', use the first two parts as the section
                    configSection = parts.slice(0, -1).join('.');
                    configKey = parts[parts.length - 1];
                } else {
                    configSection = parts[0];
                    configKey = parts[1];
                }
            } else {
                // Fallback to using the whole key for simple keys
                configSection = 'voicehype';
                configKey = settingKey;
            }

            // Save to VSCode settings
            await vscode.workspace.getConfiguration(configSection).update(configKey, value, vscode.ConfigurationTarget.Global);
            console.log(`[ConfigService] Successfully saved ${settingKey} to VSCode settings`);

            // Notify UI immediately about setting change
            this._queueConfigChangeNotification(settingKey);

            // Special handling for transcription service changes
            if (key === 'service' || key === 'transcriptionService' || key === 'voicehype.transcription.service') {
                this._handleServiceChange(value as string);
            }

            // Update in-memory values for faster access
            if (key === 'customPrompt' || key === 'voicehype.transcription.customPrompt') {
                this._inMemoryCustomPrompt = value;
                console.log(`[ConfigService] Updated in-memory customPrompt`);
            }
        } catch (e) {
            console.error(`[ConfigService] Error updating setting ${key}:`, e);
        }

        // If updating service, validate model and language
        if (key === 'voicehype.transcription.service') {
            const service = value;
            const currentModel = this.getTranscriptionModel();
            const currentRealtime = this.getTranscriptionRealtime();

            console.log(`VoiceHype: Service changed to ${service}. Applying simple defaults...`);

            // Simple defaults for service change
            if (service === 'lemonfox' && !['whisper-1', 'gpt-4o-mini-transcribe', 'gpt-4o-transcribe'].includes(currentModel)) {
                await this.updateSetting('voicehype.transcription.model', 'whisper-1');
                await this.updateSetting('voicehype.transcription.language', 'en');
                vscode.window.showInformationMessage(
                    `VoiceHype: Set to Whisper model with English language.`
                );
            } else if (service === 'assemblyai' && !['best', 'nano'].includes(currentModel)) {
                await this.updateSetting('voicehype.transcription.model', 'best');
                await this.updateSetting('voicehype.transcription.language', 'en');
                vscode.window.showInformationMessage(
                    `VoiceHype: Set to AssemblyAI service with best model and Global English language.`
                );
            }

            // Validate realtime setting
            if (currentRealtime && service === 'lemonfox') {
                await this.updateSetting('voicehype.transcription.realtime', false);
                vscode.window.showInformationMessage(
                    'VoiceHype: Realtime transcription is only available with AssemblyAI service.'
                );
            }
        }

        // If updating model, check if realtime needs to be disabled
        if (key === 'voicehype.transcription.model') {
            const model = value;
            const service = this.getTranscriptionService();
            const currentRealtime = this.getTranscriptionRealtime();

            // Disable realtime for Whisper service
            if (currentRealtime && service === 'lemonfox') {
                await this.updateSetting('voicehype.transcription.realtime', false);
                vscode.window.showInformationMessage(
                    'VoiceHype: Realtime transcription is only available with AssemblyAI service.'
                );
            }
        }
    }

    /**
     * Handle service change using simplified logic
     */
    private _handleServiceChange(service: string): void {
        console.log(`[ConfigService] Handling service change to: ${service}`);
        try {
            // Normalize service name - ensure we use 'lemonfox' consistently
            if (service === 'whisper') {
                service = 'lemonfox';
                // Update the setting to the new name
                this.updateSetting('voicehype.transcription.service', 'lemonfox');
                console.log(`[ConfigService] Normalized service name from 'whisper' to 'lemonfox'`);
            }

            // Apply simple defaults based on service
            if (service === 'lemonfox') {
                this.updateSetting('voicehype.transcription.model', 'whisper-1');
                this.updateSetting('voicehype.transcription.language', 'en');
                console.log(`[ConfigService] Set defaults for Whisper: model=whisper-1, language=en`);
            } else if (service === 'assemblyai') {
                this.updateSetting('voicehype.transcription.model', 'best');
                this.updateSetting('voicehype.transcription.language', 'en');
                console.log(`[ConfigService] Set defaults for AssemblyAI: model=best, language=en (Global English)`);

                // Disable translate for AssemblyAI
                if (this.getTranslate()) {
                    this.updateSetting('voicehype.transcription.translate', false);
                    console.log(`[ConfigService] Disabled translate for AssemblyAI service`);
                }
            }
        } catch (e) {
            console.error(`[ConfigService] Error handling service change:`, e);
        }
    }

    /**
     * Queue a configuration change notification with debouncing
     */
    private _queueConfigChangeNotification(key: string): void {
        // Add the key to pending changes
        this.pendingConfigChanges.add(key);

        // Clear existing timer if there is one
        if (this.configChangeTimer) {
            clearTimeout(this.configChangeTimer);
        }

        // Set a new timer to fire events for all pending changes
        this.configChangeTimer = setTimeout(() => {
            // Fire events for all pending changes
            this.pendingConfigChanges.forEach(pendingKey => {
                console.log(`[ConfigService] Firing configuration change event for: ${pendingKey}`);
                this._onDidChangeConfiguration.fire(pendingKey);
            });

            // Clear the pending changes
            this.pendingConfigChanges.clear();
            this.configChangeTimer = null;
        }, 100); // Small delay to debounce multiple rapid changes
    }

    // Method to handle auto sample rate switching based on real-time mode
    async handleRealtimeToggle(isRealtimeEnabled: boolean): Promise<void> {
        try {
            // Check if we're using Whisper service
            const currentService = this.getTranscriptionService();
            if (currentService === 'lemonfox') {
                console.log(`[ConfigService] Whisper service detected, real-time not supported`);

                // Force real-time off for Whisper service
                if (isRealtimeEnabled) {
                    console.log(`[ConfigService] Forcing real-time off for Whisper service`);
                    await this.updateSetting('voicehype.transcription.realtime', false);
                    return;
                }

                // Don't modify sample rate for Whisper service
                return;
            }

            // Only proceed with sample rate changes for AssemblyAI service

            if (isRealtimeEnabled) {
                // Save current sample rate before switching
                const currentSettings = this.getAudioSettings();
                this._previousSampleRate = currentSettings.sampleRate;

                // Always set to 16000Hz for real-time mode
                if (this._previousSampleRate !== 16000) {
                    console.log(`[ConfigService] Saving previous sample rate (${this._previousSampleRate}Hz) and switching to 16000Hz for real-time mode`);
                    await this.updateSetting('voicehype.audio.sampleRate', 16000);
                    // Emit the sample rate change event
                    this._onDidChangeSampleRate.fire(16000);

                    // Show notification about sample rate change
                    vscode.window.showInformationMessage(
                        'Sample rate set to 16000Hz for optimal real-time transcription performance.'
                    );
                    return;
                }
            } else {
                // Restore previous sample rate when disabling real-time mode
                if (this._previousSampleRate && this._previousSampleRate !== 16000) {
                    console.log(`[ConfigService] Restoring previous sample rate (${this._previousSampleRate}Hz) after disabling real-time mode`);
                    await this.updateSetting('voicehype.audio.sampleRate', this._previousSampleRate);
                    // Emit the sample rate change event
                    this._onDidChangeSampleRate.fire(this._previousSampleRate);
                    return;
                }
            }
        } catch (error) {
            console.error('[ConfigService] Error handling real-time sample rate toggle:', error);
        }
    }

    // Configuration Management Methods

    /**
     * Get all configurations
     */
    getConfigurations(): any[] {
        const config = vscode.workspace.getConfiguration('voicehype');
        return config.get('configurations', []);
    }

    /**
     * Get active configuration ID
     */
    getActiveConfigurationId(): string | null {
        const config = vscode.workspace.getConfiguration('voicehype');
        return config.get('activeConfigurationId', null);
    }

    /**
     * Create a new configuration
     */
    async createConfiguration(data: any): Promise<any> {
        const configurations = this.getConfigurations();

        // Generate unique ID
        const id = `config_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        const newConfig = {
            id,
            title: data.title,
            description: data.description || '',
            settings: data.settings,
            isDefault: false,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        configurations.push(newConfig);

        await vscode.workspace.getConfiguration('voicehype').update(
            'configurations',
            configurations,
            vscode.ConfigurationTarget.Global
        );

        return newConfig;
    }

    /**
     * Update an existing configuration
     */
    async updateConfiguration(data: any): Promise<void> {
        const configurations = this.getConfigurations();
        const index = configurations.findIndex(c => c.id === data.id);

        if (index >= 0) {
            configurations[index] = {
                ...configurations[index],
                title: data.title,
                description: data.description,
                settings: data.settings,
                updatedAt: new Date().toISOString()
            };

            await vscode.workspace.getConfiguration('voicehype').update(
                'configurations',
                configurations,
                vscode.ConfigurationTarget.Global
            );
        }
    }

    /**
     * Delete a configuration
     */
    async deleteConfiguration(configId: string): Promise<void> {
        const configurations = this.getConfigurations();
        const filteredConfigs = configurations.filter(c => c.id !== configId);

        await vscode.workspace.getConfiguration('voicehype').update(
            'configurations',
            filteredConfigs,
            vscode.ConfigurationTarget.Global
        );

        // If the deleted config was active, clear the active ID
        const activeId = this.getActiveConfigurationId();
        if (activeId === configId) {
            await vscode.workspace.getConfiguration('voicehype').update(
                'activeConfigurationId',
                null,
                vscode.ConfigurationTarget.Global
            );
        }
    }

    /**
     * Switch to a configuration
     */
    async switchConfiguration(configId: string): Promise<string> {
        const configurations = this.getConfigurations();
        const config = configurations.find(c => c.id === configId);

        if (!config) {
            throw new Error('Configuration not found');
        }

        // Apply all settings from the configuration
        const settings = config.settings;

        await Promise.all([
            this.updateSetting('voicehype.transcription.service', settings.service),
            this.updateSetting('voicehype.transcription.model', settings.model),
            this.updateSetting('voicehype.transcription.language', settings.language),
            this.updateSetting('voicehype.transcription.translate', settings.translate),
            this.updateSetting('voicehype.transcription.realtime', settings.realtime),
            this.updateSetting('voicehype.transcription.shouldOptimize', settings.optimizeEnabled),
            this.updateSetting('voicehype.transcription.optimizationModel', settings.optimizationModel),
            this.updateSetting('voicehype.transcription.customPrompt', settings.customPrompt),
            this.updateSetting('voicehype.audio.sampleRate', settings.sampleRate),
            this.updateSetting('voicehype.audio.deviceId', settings.deviceId)
        ]);

        // Set as active configuration
        await vscode.workspace.getConfiguration('voicehype').update(
            'activeConfigurationId',
            configId,
            vscode.ConfigurationTarget.Global
        );

        return config.title;
    }

    /**
     * Get configuration name by ID
     */
    getConfigurationName(configId: string): string {
        const configurations = this.getConfigurations();
        const config = configurations.find(c => c.id === configId);
        return config?.title || 'Unknown Configuration';
    }

    /**
     * Get What's New status for a version
     */
    getWhatsNewStatus(version: string): boolean {
        const config = vscode.workspace.getConfiguration('voicehype');
        const whatsNewSeen = config.get('whatsNewSeen', {}) as Record<string, boolean>;
        return whatsNewSeen[version] || false;
    }

    /**
     * Mark What's New as seen for a version
     */
    async markWhatsNewSeen(version: string): Promise<void> {
        const config = vscode.workspace.getConfiguration('voicehype');
        const whatsNewSeen = config.get('whatsNewSeen', {}) as Record<string, boolean>;
        whatsNewSeen[version] = true;

        await config.update(
            'whatsNewSeen',
            whatsNewSeen,
            vscode.ConfigurationTarget.Global
        );
    }

    dispose(): void {
        this._onDidChangeConfiguration.dispose();
        this._onDidChangeSampleRate.dispose();
        if (this.configChangeTimer) {
            clearTimeout(this.configChangeTimer);
        }
    }
}
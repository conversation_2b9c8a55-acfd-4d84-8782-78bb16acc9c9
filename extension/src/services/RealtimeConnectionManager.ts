import { WebSocket } from 'ws';
import { TranscriptionStatus } from './TranscriptionService';

export interface AudioChunk {
  data: Uint8Array;
  isLastChunk: boolean;
}

export class RealtimeConnectionManager {
  private ws: WebSocket | null = null;
  private isConnected = false;
  private isTranscriptionReady = false; // New flag to track when transcription service is ready
  private transcript = '';
  private lastPartialTranscript = '';
  private statusListeners: ((status: TranscriptionStatus, message?: string) => void)[] = [];
  private transcriptionListeners: ((text: string, isFinal: boolean) => void)[] = [];
  private readyListeners: (() => void)[] = [];
  private transcriptionReadyListeners: (() => void)[] = []; // New listeners for transcription readiness

  // Add new properties for tracking processing state
  private processedDuration: number = 0;
  private allChunksProcessed: boolean = false;
  private finalDurationReceived: boolean = false;

  // Graceful shutdown with pending chunk tracking
  private pendingChunks: Map<number, { timestamp: number, retryCount: number }> = new Map();
  private chunkCounter: number = 0;
  private isShuttingDown: boolean = false;
  private shutdownTimeout: NodeJS.Timeout | null = null;
  private readonly MAX_SHUTDOWN_WAIT = 10000; // 10 seconds max wait
  private readonly MAX_CHUNK_RETRIES = 3;
  private readonly CHUNK_TIMEOUT = 3000; // 3 seconds per chunk timeout

  // Pause timeout functionality
  private pauseTimeout: NodeJS.Timeout | null = null;
  private isPaused: boolean = false;
  private lastActivityTime: number = Date.now();
  private readonly PAUSE_TIMEOUT_MINUTES = 1; // 1 minute pause timeout

  // Circuit breaker state
  private circuitState: 'closed' | 'open' | 'half-open' = 'closed';
  private failureCount = 0;
  private lastFailureTime = 0;
  private nextRetryTime = 0;
  private readonly failureThreshold = 3;
  private readonly retryTimeoutBase = 2000; // 2 seconds
  private readonly maxRetryTimeout = 30000; // 30 seconds

  private connectionAttempts = 0;
  private readonly MAX_CONNECTION_ATTEMPTS = 3;

  // Add heartbeat mechanism
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private readonly HEARTBEAT_INTERVAL = 15000; // 15 seconds
  private lastMessageReceivedTime: number = 0;
  private readonly CONNECTION_TIMEOUT = 30000; // 30 seconds
  private connectionMonitorInterval: NodeJS.Timeout | null = null;

  constructor(
    private apiKey: string,
    private service: string,
    private model: string,
    private language: string,
    private sampleRate: number = 16000 // Default to 16kHz if not specified
  ) {
    // Initialize state - but preserve transcript data if this is a reconnection
    this.ws = null;
    this.isConnected = false;
    this.isTranscriptionReady = false;

    // Only reset transcript data if it's truly empty (new session)
    if (!this.transcript) {
      this.transcript = '';
    }
    if (!this.lastPartialTranscript) {
      this.lastPartialTranscript = '';
    }

    this.processedDuration = 0;
    this.allChunksProcessed = false;
    this.finalDurationReceived = false;
    this.connectionAttempts = 0;
    this.statusListeners = [];
    this.transcriptionListeners = [];
    this.readyListeners = [];

    // Reset graceful shutdown state
    this.pendingChunks.clear();
    this.chunkCounter = 0;
    this.isShuttingDown = false;
    if (this.shutdownTimeout) {
      clearTimeout(this.shutdownTimeout);
      this.shutdownTimeout = null;
    }

    console.log(`VoiceHype: Created RealtimeConnectionManager with service: ${service}, model: ${model}, language: ${language}, sample rate: ${sampleRate}Hz`);
    console.log(`VoiceHype: Preserving existing transcript data: ${this.transcript.length} chars, partial: ${this.lastPartialTranscript.length} chars`);
  }

  // Add new TranscriptionStatus for duration and processing
  public static readonly UsageReceived = 'usage_received';
  public static readonly AllChunksProcessed = 'all_chunks_processed';
  public static readonly TranscriptionReady = 'transcription_ready'; // New status for transcription readiness
  public static readonly Paused = 'paused';
  public static readonly PauseTimeout = 'pause_timeout';

  // Connect to the WebSocket before recording starts
  public async connect(): Promise<void> {
    // Ensure any previous connection is properly closed before starting a new one
    if (this.ws) {
      console.log('VoiceHype: Closing previous connection before starting new one');
      await this.forceCloseConnection();
    }
    
    this.emitStatus(TranscriptionStatus.ConnectingWebSocket);
    this.connectionAttempts = 0;
    this.lastMessageReceivedTime = Date.now(); // Initialize last message time

    return this.attemptConnection();
  }

  /**
   * Forcefully close any existing connection to ensure clean state
   */
  private async forceCloseConnection(): Promise<void> {
    return new Promise<void>((resolve) => {
      if (!this.ws) {
        resolve();
        return;
      }

      console.log('VoiceHype: Force closing existing WebSocket connection');
      
      // Clear any existing intervals/timeouts
      this.clearHeartbeatAndMonitor();
      
      // Set a short timeout to ensure we don't hang
      const forceTimeout = setTimeout(() => {
        console.log('VoiceHype: Force close timeout reached, terminating connection');
        if (this.ws) {
          try {
            this.ws.terminate();
          } catch (error) {
            console.log('VoiceHype: Error during force termination:', error);
          }
          this.ws = null;
        }
        this.isConnected = false;
        this.isTranscriptionReady = false;
        resolve();
      }, 2000); // 2 second timeout
      
      // Try to close gracefully first
      if (this.ws.readyState === WebSocket.OPEN || this.ws.readyState === WebSocket.CONNECTING) {
        const handleClose = () => {
          clearTimeout(forceTimeout);
          this.ws = null;
          this.isConnected = false;
          this.isTranscriptionReady = false;
          console.log('VoiceHype: Previous connection closed gracefully');
          resolve();
        };
        
        this.ws.once('close', handleClose);
        
        try {
          if (this.ws.readyState === WebSocket.OPEN) {
            // Send close message
            this.ws.send(JSON.stringify({ type: 'close', reason: 'new_session_starting' }));
          }
          this.ws.close();
        } catch (error) {
          console.log('VoiceHype: Error during graceful close:', error);
          // Force close will handle this via timeout
        }
      } else {
        // Connection is already closed or closing
        clearTimeout(forceTimeout);
        this.ws = null;
        this.isConnected = false;
        this.isTranscriptionReady = false;
        resolve();
      }
    });
  }

  private async attemptConnection(): Promise<void> {
    // Check circuit breaker state
    if (this.circuitState === 'open') {
      if (Date.now() < this.nextRetryTime) {
        throw new Error('Service unavailable - circuit breaker open');
      }
      this.circuitState = 'half-open';
    }

    this.connectionAttempts++;
    console.log(`VoiceHype: Connection attempt ${this.connectionAttempts}/${this.MAX_CONNECTION_ATTEMPTS}`);

    // Clear any existing intervals
    this.clearHeartbeatAndMonitor();

    return new Promise((resolve, reject) => {
      // Record start time for retry timeout calculation
      const startTime = Date.now();
      const serverUrl = '***************:3001';

      // Properly encode API key and parameters to ensure Windows compatibility
      const encodedApiKey = encodeURIComponent(this.apiKey);
      const encodedLanguage = encodeURIComponent(this.language);

      // Use the new Express.js server URL (HTTP since SSL not yet implemented)
      const wsUrl = `ws://${serverUrl}/realtime?apiKey=${encodedApiKey}&service=${this.service}&model=${this.model}&language=${encodedLanguage}&enable_extra_session_information=true`;

      // Log the URL (hide API key)
      const safeUrl = wsUrl.replace(encodedApiKey, '***API_KEY_HIDDEN***');
      console.log(`VoiceHype: Connecting to real-time transcription WebSocket: ${safeUrl}`);

      try {
        // Use a consistent WebSocket configuration for all platforms
        this.ws = new WebSocket(wsUrl, {
          headers: {
            'User-Agent': 'VoiceHype-VSCode-Extension'
          },
          handshakeTimeout: 20 * 60 * 1000 // Set it to 20 minutes
        });

        console.log('VoiceHype: Created WebSocket connection to Express.js server with 20m handshake timeout');
        
      } catch (error) {
        console.error('VoiceHype: Error creating WebSocket:', error);
        return this.handleConnectionError(error, resolve, reject);
      }

      // Set up a connection timeout
      const connectionTimeout = setTimeout(() => {
        console.error('VoiceHype: Connection timeout after 30 seconds');
        this.emitStatus(TranscriptionStatus.Error, 'Connection timeout after 30 seconds');

        if (this.ws) {
          this.ws.terminate();
          this.ws = null;
        }

        this.handleConnectionError(new Error('Connection timeout'), resolve, reject);
      }, 30000); // Increase to 30 seconds

      this.ws.on('open', () => {
        const openTime = new Date().toISOString();
        console.log(`VoiceHype: [DEBUG] WebSocket connection opened at ${openTime}`);
        console.log(`VoiceHype: [DEBUG] WebSocket state after open: readyState=${this.ws?.readyState}, isConnected=${this.isConnected}`);

        this.emitStatus(TranscriptionStatus.WebSocketConnected);
        clearTimeout(connectionTimeout);
        this.lastMessageReceivedTime = Date.now(); // Update last message time

        console.log(`VoiceHype: [DEBUG] Setting up heartbeat and connection monitoring`);
        // Set up heartbeat to keep the connection alive
        this.setupHeartbeatAndMonitor();

        // Socket is connected, but transcription service may not be ready yet
        // Connection ready - this is different from transcription ready
        if (!this.isConnected) {
          console.log(`VoiceHype: [DEBUG] Connection ready, waiting for transcription service initialization`);
          this.isConnected = true;
          this.readyListeners.forEach(listener => listener());
          
          // Send initial connection message with sample rate
          this.ws?.send(JSON.stringify({
            type: 'connected',
            sampleRate: this.sampleRate
          }));
        }
        
        // Note: We'll emit TranscriptionReady status when we receive the 'connected' message from the service
      });

      this.ws.on('message', (data: Buffer) => {
        try {
          // Update last message received time for connection monitoring
          const now = Date.now();
          const timeSinceLastMessage = now - this.lastMessageReceivedTime;
          this.lastMessageReceivedTime = now;

          // Log message receipt with timing information
          console.log(`VoiceHype: [DEBUG] Message received after ${timeSinceLastMessage}ms, size: ${data.length} bytes`);

          // Handle binary audio data which shouldn't be parsed
          if (data instanceof Buffer) {
            // Check if this looks like binary audio data (not starting with "{")
            const firstByte = data.length > 0 ? data[0] : 0;
            // '{' character is ASCII 123
            if (firstByte !== 123) {
              console.log(`VoiceHype: [DEBUG] Received binary data of ${data.length} bytes, not trying to parse`);
              return; // Skip processing binary data
            }
          }

          // Check if data is valid before trying to convert to string
          if (!data || data.length === 0) {
            console.log('VoiceHype: [DEBUG] Received empty message data');
            return;
          }

          // Try to safely convert to string and log raw data in case of errors
          let message: string;
          try {
            message = data.toString();
            // Verify it looks like JSON before proceeding
            if (!message.startsWith('{') && !message.startsWith('[')) {
              console.log(`VoiceHype: [DEBUG] Received non-JSON data, length: ${message.length} bytes`);
              return;
            }

            // Log only the first part to avoid huge logs
            console.log(`VoiceHype: [DEBUG] WebSocket message received (${message.length} bytes): ${message.substring(0, 200)}${message.length > 200 ? '...' : ''}`);
          } catch (stringError) {
            console.error(`VoiceHype: [DEBUG] Error converting message to string: ${stringError instanceof Error ? stringError.message : String(stringError)}`);
            console.log(`VoiceHype: [DEBUG] Raw message data: type=${typeof data}, isBuffer=${data instanceof Buffer}, length=${data instanceof Buffer ? data.length : 'unknown'}`);

            // Try to show the first few bytes if it's a buffer
            if (data instanceof Buffer && data.length > 0) {
              try {
                // Use Buffer.subarray instead of slice (which is deprecated)
                const preview = Buffer.from(data.subarray(0, Math.min(50, data.length))).toString('hex');
                console.log(`VoiceHype: [DEBUG] First ${Math.min(50, data.length)} bytes as hex: ${preview}`);
              } catch (bufferError) {
                console.error(`VoiceHype: [DEBUG] Could not convert buffer to hex: ${bufferError instanceof Error ? bufferError.message : String(bufferError)}`);
              }
            }
            return;
          }

          // Safely try to parse JSON
          let parsedData: any;
          try {
            parsedData = JSON.parse(message);
            console.log(`VoiceHype: [DEBUG] Successfully parsed JSON message of type: ${parsedData.type || parsedData.message_type || 'unknown'}`);
          } catch (jsonError) {
            console.error(`VoiceHype: [DEBUG] Error parsing JSON message: ${jsonError instanceof Error ? jsonError.message : String(jsonError)}`);
            console.log(`VoiceHype: [DEBUG] Raw message content (first 200 chars): ${message.substring(0, 200)}${message.length > 200 ? '...' : ''}`);
            return;
          }

          // Extra defensive check for parsedData
          if (!parsedData) {
            console.log('VoiceHype: [DEBUG] Parsed data is null or undefined');
            return;
          }

          // Handle heartbeat responses
          if (parsedData.type === 'pong' || parsedData.message_type === 'pong') {
            const timeSinceLastHeartbeat = Date.now() - this.lastMessageReceivedTime;
            console.log(`VoiceHype: [DEBUG] Received heartbeat response after ${timeSinceLastHeartbeat}ms, connection state: ${this.ws?.readyState}`);
            return;
          }

          // Handle messages based on type field
          const messageType = parsedData.type || parsedData.message_type || 'unknown';
          console.log(`VoiceHype: Processing message type: ${messageType}`);

          // Add handlers for duration and finalization messages
          if (messageType === 'duration') {
            console.log(`VoiceHype: Received duration information: ${parsedData.duration}s, sessionId: ${parsedData.sessionId || 'unknown'}`);
            this.processedDuration = parsedData.duration || 0;
            this.finalDurationReceived = true;
            this.emitStatus(TranscriptionStatus.UsageReceived, `Duration: ${this.processedDuration}s`);
          }
          else if (messageType === 'CompleteTranscript') {
            // Handle the complete transcript from AssemblyAI (at the end of recording)
            const text = parsedData.text || '';
            console.log(`VoiceHype: Received CompleteTranscript message with length: ${text.length}`);
            
            if (text.trim() !== '') {
              // Compare with our accumulated transcript data
              const existingText = this.transcript.trim();
              const newText = text.trim();
              
              // If our accumulated transcript is empty, use the complete transcript
              if (existingText === '') {
                this.transcript = newText;
                console.log(`VoiceHype: Using CompleteTranscript as final: "${newText.substring(0, 100)}${newText.length > 100 ? '...' : ''}"`);
                this.transcriptionListeners.forEach(listener => listener(newText, true));
              } 
              // If the complete transcript is longer, prefer it
              else if (newText.length > existingText.length) {
                console.log(`VoiceHype: CompleteTranscript is longer (${newText.length} vs ${existingText.length} chars), using it`);
                this.transcript = newText;
                this.transcriptionListeners.forEach(listener => listener(newText, true));
              } 
              // Otherwise keep our accumulated transcript
              else {
                console.log(`VoiceHype: Keeping accumulated transcript (${existingText.length} chars) instead of CompleteTranscript (${newText.length} chars)`);
              }

              // Mark chunks as processed since we got the complete transcript
              this.allChunksProcessed = true;
              this.emitStatus(TranscriptionStatus.AllChunksProcessed, 'Complete transcript processed');
              this.emitStatus(TranscriptionStatus.TranscriptionComplete, 'Transcription complete');
            } else {
              console.log('VoiceHype: Received empty CompleteTranscript message');
              
              // If we have accumulated transcripts, use those instead
              if (this.transcript.trim() !== '') {
                console.log(`VoiceHype: Using accumulated transcript: "${this.transcript.substring(0, 100)}${this.transcript.length > 100 ? '...' : ''}"`);
                this.allChunksProcessed = true;
                this.emitStatus(TranscriptionStatus.AllChunksProcessed, 'Using accumulated transcript');
                this.emitStatus(TranscriptionStatus.TranscriptionComplete, 'Transcription complete');
              }
              // Otherwise, mark as empty but processed
              else {
                this.allChunksProcessed = true;
                this.emitStatus(TranscriptionStatus.AllChunksProcessed, 'No speech detected');
                this.emitStatus(TranscriptionStatus.TranscriptionComplete, 'Transcription complete (no speech detected)');
              }
            }
          }
          else if (messageType === 'finalized') {
            console.log(`VoiceHype: Session finalized: ${parsedData.message || 'No message'}, sessionId: ${parsedData.sessionId || 'unknown'}`);
            this.allChunksProcessed = true;
            this.emitStatus(TranscriptionStatus.AllChunksProcessed, 'All audio processed');

            // If we're done processing, emit the final status
            if (this.finalDurationReceived) {
              this.emitStatus(TranscriptionStatus.TranscriptionComplete, `Completed: ${this.processedDuration.toFixed(1)}s audio`);
            } else {
              // Even without duration info, mark as complete
              this.emitStatus(TranscriptionStatus.TranscriptionComplete, 'Transcription complete');
              console.log('VoiceHype: Received transcription complete status');
            }
          }
          else if (messageType === 'connected') {
            console.log('VoiceHype: Real-time transcription session established:', parsedData.sessionId || 'no-session-id');
            this.emitStatus(TranscriptionStatus.SessionEstablished, `Session ID: ${parsedData.sessionId || 'unknown'}`);

            // Now we consider the transcription service ready
            if (!this.isTranscriptionReady) {
              console.log(`VoiceHype: [DEBUG] Transcription service ready, notifying ${this.transcriptionReadyListeners.length} listeners`);
              this.isTranscriptionReady = true;
              this.emitStatus(TranscriptionStatus.TranscriptionReady);
              this.transcriptionReadyListeners.forEach(listener => listener());
              
              // If not connected yet, also mark as connected
              if (!this.isConnected) {
                console.log('VoiceHype: Setting connected state and notifying listeners');
                this.isConnected = true;
                this.readyListeners.forEach(listener => listener());
              }
              
              resolve(); // Resolve the promise now that transcription is ready
            } else {
              console.log(`VoiceHype: [DEBUG] Transcription service was already marked as ready`);
            }
          } else if (messageType === 'SessionInformation') {
            // Handle SessionInformation message from AssemblyAI
            console.log('VoiceHype: Received SessionInformation message:', parsedData);
            
            // Extract audio duration from the message
            const audioDurationSeconds = parsedData.audio_duration_seconds || 0;
            console.log(`VoiceHype: Audio duration from service: ${audioDurationSeconds}s`);
            
            // Update the processed duration with the value from the service
            this.processedDuration = audioDurationSeconds;
            this.finalDurationReceived = true;
            
            // Emit usage received status
            this.emitStatus(TranscriptionStatus.UsageReceived, `Duration: ${this.processedDuration}s`);
          } else if (messageType === 'final' || messageType === 'FinalTranscript') {
            // Handle final transcriptions from any service
            const text = parsedData.text || '';
            if (text.trim() !== '') {
              // Check if this text is already included in our transcript to avoid duplicates
              const existingText = this.transcript.trim();
              const newText = text.trim();

              if (existingText === '' || !existingText.includes(newText)) {
                // Only append if it's new content
                this.transcript += (existingText ? ' ' : '') + newText;
                console.log(`VoiceHype: Final transcription appended: "${newText}"`);
                this.emitStatus(TranscriptionStatus.TranscriptionReceived, newText);
                this.transcriptionListeners.forEach(listener => listener(newText, true));

                // Mark chunk as processed since we got a final transcript
                this.markChunkProcessed();

                // Since we got a final transcript, consider this chunk processed
                // This ensures we don't hang waiting for a CompleteTranscript that might never come
                this.allChunksProcessed = true;
                this.emitStatus(TranscriptionStatus.AllChunksProcessed, 'Final transcript received');
              } else {
                console.log(`VoiceHype: Final transcription ignored (duplicate): "${newText}"`);
              }
            }
          } else if (messageType === 'partial' || messageType === 'PartialTranscript') {
            // Handle partial transcriptions for real-time feedback
            const text = parsedData.text || '';
            if (text.trim() !== '') {
              const newPartial = text.trim();

              // Keep the longest/most complete partial transcript as fallback
              if (newPartial.length > this.lastPartialTranscript.length) {
                this.lastPartialTranscript = newPartial;

                // Emit partial transcript to listeners for real-time display
                this.transcriptionListeners.forEach(listener => listener(newPartial, false));

                // Update status with partial transcript for visual feedback
                const displayText = newPartial.length > 50 ? newPartial.substring(0, 47) + '...' : newPartial;
                this.emitStatus(TranscriptionStatus.StreamingAudio, `Partial: ${displayText}`);

                // Mark chunk as processed for partial transcripts too
                this.markChunkProcessed();

                // Only log occasionally to reduce noise
                if (Math.random() < 0.1) { // Log ~10% of partials for debugging
                  console.log(`VoiceHype: Partial transcript: "${newPartial.substring(0, 50)}${newPartial.length > 50 ? '...' : ''}"`);
                }
              }
            }
          } else if (messageType === 'error') {
            console.error('VoiceHype: Real-time transcription error:', parsedData.message || 'Unknown error');
            this.emitStatus(TranscriptionStatus.Error, parsedData.message || 'Unknown error');
          } else if (messageType === 'timeout') {
            console.log('VoiceHype: Real-time transcription timeout:', parsedData.message || 'Unknown timeout');
            
            // Handle specific timeout types
            if (parsedData.reason === 'no_audio_activity') {
              console.log('VoiceHype: Server detected no audio activity - connection timed out');
              this.emitStatus(TranscriptionStatus.Error, `No audio detected for ${parsedData.message || '60 seconds'}. Connection closed.`);
            } else {
              this.emitStatus(TranscriptionStatus.Error, `Timeout: ${parsedData.message || 'Unknown reason'}`);
            }
            
            // Mark connection as no longer active
            this.isConnected = false;
          } else if (messageType === 'service_disconnected') {
            console.log('VoiceHype: Real-time transcription service disconnected:', parsedData.reason || 'Unknown reason');
            this.emitStatus(TranscriptionStatus.Error, `Service disconnected: ${parsedData.reason || 'Unknown reason'}`);
            this.isConnected = false;
          } else {
            // Handle unknown message types
            console.log('VoiceHype: Received unknown message type:', messageType, 'Content:', JSON.stringify(parsedData).substring(0, 200));
          }
        } catch (error) {
          console.error('VoiceHype: Error handling WebSocket message:', error);
          console.log('VoiceHype: Original data type:', typeof data, 'Is Buffer:', data instanceof Buffer);

          if (data instanceof Buffer) {
            try {
              // Use Buffer.subarray instead of slice (which is deprecated)
              const preview = Buffer.from(data.subarray(0, Math.min(50, data.length))).toString('hex');
              console.log(`VoiceHype: [DEBUG] First ${Math.min(50, data.length)} bytes as hex: ${preview}`);
            } catch (bufferError) {
              console.error(`VoiceHype: [DEBUG] Could not convert buffer to hex: ${bufferError instanceof Error ? bufferError.message : String(bufferError)}`);
            }
          }
        }
      });

      this.ws.on('error', (error) => {
        const errorTime = new Date().toISOString();
        console.error(`VoiceHype: [DEBUG] WebSocket error at ${errorTime}:`, error);
        console.error(`VoiceHype: [DEBUG] WebSocket state during error: readyState=${this.ws?.readyState}, isConnected=${this.isConnected}`);
        console.error(`VoiceHype: [DEBUG] Time since last message: ${Date.now() - this.lastMessageReceivedTime}ms`);

        this.emitStatus(TranscriptionStatus.Error, `WebSocket error: ${error.message}`);
        this.isConnected = false;
        clearTimeout(connectionTimeout);

        // Clear heartbeat and monitor
        this.clearHeartbeatAndMonitor();

        // Try to reconnect if this was the initial connection attempt
        this.handleConnectionError(error, resolve, reject);
      });

      this.ws.on('close', (code, reason) => {
        const closeTime = new Date().toISOString();
        console.log(`VoiceHype: [DEBUG] WebSocket connection closed at ${closeTime} with code ${code}, reason: ${reason || 'None provided'}`);
        console.log(`VoiceHype: [DEBUG] WebSocket state during close: isConnected=${this.isConnected}`);
        console.log(`VoiceHype: [DEBUG] Time since last message: ${Date.now() - this.lastMessageReceivedTime}ms`);
        console.log(`VoiceHype: [DEBUG] Transcript status: length=${this.transcript.trim().length}, lastPartialLength=${this.lastPartialTranscript.trim().length}`);

        // Make sure we have a final transcript by the time we close
        if (this.transcript.trim() === '') {
          // If we have partial ones, use the last partial
          if (this.lastPartialTranscript.trim() !== '') {
            console.log(`VoiceHype: [DEBUG] No final transcript received, using last partial: "${this.lastPartialTranscript}"`);
            this.transcript = this.lastPartialTranscript;
            // Notify listeners that we're using the partial as final
            this.transcriptionListeners.forEach(listener => listener(this.lastPartialTranscript, true));
          } else {
            console.log(`VoiceHype: [DEBUG] No transcript data available (neither final nor partial)`);
          }
        } else {
          console.log(`VoiceHype: [DEBUG] Final transcript available from accumulated segments: "${this.transcript.substring(0, 100)}${this.transcript.length > 100 ? '...' : ''}"`);
          // Make sure listeners receive the final transcript
          this.transcriptionListeners.forEach(listener => listener(this.transcript, true));
        }

        this.isConnected = false;

        // Clear heartbeat and monitor
        this.clearHeartbeatAndMonitor();

        // Log close code meaning
        if (code === 1000) {
          console.log(`VoiceHype: [DEBUG] Normal closure (1000): The connection successfully completed the purpose for which it was created`);
        } else if (code === 1001) {
          console.log(`VoiceHype: [DEBUG] Going Away (1001): The endpoint is going away (e.g., server shutdown or browser page navigation)`);
        } else if (code === 1002) {
          console.log(`VoiceHype: [DEBUG] Protocol Error (1002): The endpoint terminated the connection due to a protocol error`);
        } else if (code === 1003) {
          console.log(`VoiceHype: [DEBUG] Unsupported Data (1003): The connection was terminated because the endpoint received data of a type it cannot accept`);
        } else if (code === 1005) {
          console.log(`VoiceHype: [DEBUG] No Status Received (1005): No status code was provided even though one was expected`);
        } else if (code === 1006) {
          console.log(`VoiceHype: [DEBUG] Abnormal Closure (1006): The connection was closed abnormally (e.g., without sending or receiving a Close control frame)`);
        } else if (code === 1007) {
          console.log(`VoiceHype: [DEBUG] Invalid frame payload data (1007): The endpoint terminated the connection because a message contained inconsistent data`);
        } else if (code === 1008) {
          console.log(`VoiceHype: [DEBUG] Policy Violation (1008): The endpoint terminated the connection because it received a message that violates its policy`);
        } else if (code === 1009) {
          console.log(`VoiceHype: [DEBUG] Message Too Big (1009): The endpoint terminated the connection because a data frame was too large`);
        } else if (code === 1010) {
          console.log(`VoiceHype: [DEBUG] Missing Extension (1010): The client terminated the connection because it expected the server to negotiate one or more extensions`);
        } else if (code === 1011) {
          console.log(`VoiceHype: [DEBUG] Internal Error (1011): The server terminated the connection because it encountered an unexpected condition`);
        } else if (code === 1012) {
          console.log(`VoiceHype: [DEBUG] Service Restart (1012): The server is restarting`);
        } else if (code === 1013) {
          console.log(`VoiceHype: [DEBUG] Try Again Later (1013): The server is terminating the connection due to a temporary condition`);
        } else if (code === 1014) {
          console.log(`VoiceHype: [DEBUG] Bad Gateway (1014): The server was acting as a gateway or proxy and received an invalid response from an upstream server`);
        } else if (code === 1015) {
          console.log(`VoiceHype: [DEBUG] TLS Handshake (1015): The connection was closed due to a failure to perform a TLS handshake`);
        } else {
          console.log(`VoiceHype: [DEBUG] Unknown close code (${code}): The connection was closed for an unknown reason`);
        }

        // Ensure we've marked all chunks as processed
        if (!this.allChunksProcessed) {
          this.allChunksProcessed = true;
          
          // If we have any transcript, emit completion event
          if (this.transcript.trim() !== '') {
            this.emitStatus(TranscriptionStatus.AllChunksProcessed, 'Finalizing with accumulated transcripts');
            this.emitStatus(TranscriptionStatus.TranscriptionComplete, 'Transcription complete');
          } else {
            this.emitStatus(TranscriptionStatus.AllChunksProcessed, 'No transcript received');
            this.emitStatus(TranscriptionStatus.TranscriptionComplete, 'Transcription complete (no speech detected)');
          }
        }

        // Attempt to reconnect if this wasn't a normal closure and we're still recording
        if (code !== 1000 && code !== 1001) {
          console.log(`VoiceHype: [DEBUG] Abnormal closure (${code}), attempting to reconnect...`);
          this.attemptReconnection();
        } else {
          console.log(`VoiceHype: [DEBUG] Normal closure (${code}), not attempting to reconnect`);
          this.emitStatus(TranscriptionStatus.TranscriptionComplete);
        }
      });
    });
  }

  private handleConnectionError(error: any, resolve: Function, reject: Function): void {
    // Record failure
    this.failureCount++;
    this.lastFailureTime = Date.now();

    // Check if we should trip the circuit breaker
    if (this.failureCount >= this.failureThreshold) {
      this.circuitState = 'open';
      const retryTimeout = this.calculateRetryTimeout();
      this.nextRetryTime = Date.now() + retryTimeout;
      console.error(`VoiceHype: Circuit breaker tripped - service unavailable for ${retryTimeout}ms`);
      this.emitStatus(TranscriptionStatus.Error, `Service unavailable - retrying in ${Math.round(retryTimeout/1000)}s`);
      reject(error);
      return;
    }

    if (this.connectionAttempts < this.MAX_CONNECTION_ATTEMPTS) {
      console.log(`VoiceHype: Connection failed, retrying (${this.connectionAttempts}/${this.MAX_CONNECTION_ATTEMPTS})...`);

      // Calculate retry timeout with exponential backoff
      const retryTimeout = this.calculateRetryTimeout();
      console.log(`VoiceHype: Waiting ${retryTimeout}ms before retry...`);

      // Add a delay before retrying
      setTimeout(() => {
        this.attemptConnection()
          .then(() => resolve())
          .catch(err => reject(err));
      }, retryTimeout);
    } else {
      console.error('VoiceHype: Failed to connect after multiple attempts:', error);
      this.emitStatus(TranscriptionStatus.Error, `Connection failed: ${error.message}`);
      reject(error);
    }
  }

  private calculateRetryTimeout(): number {
    const timeout = Math.min(
      this.retryTimeoutBase * Math.pow(2, this.failureCount),
      this.maxRetryTimeout
    );
    return timeout;
  }

  // Send audio chunk as it's captured
  public sendAudioChunk(chunk: AudioChunk): void {
    if (!this.ws || !this.isConnected || this.ws.readyState !== WebSocket.OPEN) {
      console.log('VoiceHype: Cannot send audio chunk: WebSocket not connected');
      return;
    }

    // Reset pause timeout on any activity
    this.resetPauseTimeout();

    try {
      // Assign chunk ID for tracking
      const chunkId = ++this.chunkCounter;

      // Track this chunk as pending (unless we're already shutting down)
      if (!this.isShuttingDown) {
        this.pendingChunks.set(chunkId, {
          timestamp: Date.now(),
          retryCount: 0
        });
      }

      // Get platform to determine how to send data
      const platform = process.platform;

      // Only log occasional chunks to avoid flooding logs
      if (Math.random() < 0.05) {
        console.log(`VoiceHype: Sending audio chunk #${chunkId} of ${chunk.data.length} bytes, isLastChunk: ${chunk.isLastChunk}, platform: ${platform}, pending: ${this.pendingChunks.size}`);
      }

      // Signal that audio is being streamed
      this.emitStatus(TranscriptionStatus.StreamingAudio);

      if (platform === 'linux') {
        // For Linux, convert to ArrayBuffer before sending
        const arrayBuffer = chunk.data.buffer.slice(
          chunk.data.byteOffset,
          chunk.data.byteOffset + chunk.data.byteLength
        );
        this.ws.send(arrayBuffer);
      } else {
        // For Windows and macOS, send the Buffer directly
        this.ws.send(Buffer.from(chunk.data));
      }

      if (chunk.isLastChunk) {
        console.log(`VoiceHype: Sending final audio chunk #${chunkId}, starting graceful shutdown with ${this.pendingChunks.size} pending chunks`);
        this.emitStatus(TranscriptionStatus.StreamingAudio, 'Sending final audio...');

        // Start graceful shutdown process
        this.startGracefulShutdown();
      }
    } catch (error) {
      console.error('VoiceHype: Error sending audio chunk:', error);
      this.emitStatus(TranscriptionStatus.Error,
        `Error sending audio: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  // ==================== GRACEFUL SHUTDOWN WITH PENDING CHUNK TRACKING ====================

  /**
   * Start graceful shutdown process - wait for pending chunks to be processed
   */
  private startGracefulShutdown(): void {
    if (this.isShuttingDown) {
      console.log('VoiceHype: Graceful shutdown already in progress');
      return;
    }

    this.isShuttingDown = true;
    const pendingCount = this.pendingChunks.size;

    console.log(`VoiceHype: Starting graceful shutdown with ${pendingCount} pending chunks`);
    this.emitStatus(TranscriptionStatus.StreamingAudio, `Waiting for ${pendingCount} pending chunks...`);

    // Set maximum wait timeout
    this.shutdownTimeout = setTimeout(() => {
      console.log(`VoiceHype: Graceful shutdown timeout after ${this.MAX_SHUTDOWN_WAIT}ms, forcing completion`);
      this.forceShutdownCompletion('Timeout waiting for pending chunks');
    }, this.MAX_SHUTDOWN_WAIT);

    // Start monitoring pending chunks
    this.monitorPendingChunks();

    // Send close message to server
    this.sendCloseMessage();

    // If no pending chunks, complete immediately
    if (pendingCount === 0) {
      console.log('VoiceHype: No pending chunks, completing shutdown immediately');
      this.completeGracefulShutdown();
    }
  }

  /**
   * Monitor pending chunks and handle timeouts/retries
   */
  private monitorPendingChunks(): void {
    const checkInterval = setInterval(() => {
      if (!this.isShuttingDown) {
        clearInterval(checkInterval);
        return;
      }

      const now = Date.now();
      const chunksToRetry: number[] = [];
      const chunksToRemove: number[] = [];

      // Check each pending chunk for timeout
      this.pendingChunks.forEach((chunkInfo, chunkId) => {
        const age = now - chunkInfo.timestamp;

        if (age > this.CHUNK_TIMEOUT) {
          if (chunkInfo.retryCount < this.MAX_CHUNK_RETRIES) {
            // Mark for retry
            chunksToRetry.push(chunkId);
            console.log(`VoiceHype: Chunk #${chunkId} timed out after ${age}ms, retry ${chunkInfo.retryCount + 1}/${this.MAX_CHUNK_RETRIES}`);
          } else {
            // Max retries reached, give up on this chunk
            chunksToRemove.push(chunkId);
            console.log(`VoiceHype: Chunk #${chunkId} failed after ${this.MAX_CHUNK_RETRIES} retries, giving up`);
          }
        }
      });

      // Handle retries (in real implementation, we'd need to resend the audio data)
      chunksToRetry.forEach(chunkId => {
        const chunkInfo = this.pendingChunks.get(chunkId);
        if (chunkInfo) {
          chunkInfo.retryCount++;
          chunkInfo.timestamp = now; // Reset timestamp for retry
          console.log(`VoiceHype: Retrying chunk #${chunkId} (attempt ${chunkInfo.retryCount})`);
          // Note: In a full implementation, we'd resend the actual audio data here
          // For now, we just update the tracking info
        }
      });

      // Remove failed chunks
      chunksToRemove.forEach(chunkId => {
        this.pendingChunks.delete(chunkId);
      });

      // Check if all chunks are processed
      if (this.pendingChunks.size === 0) {
        console.log('VoiceHype: All pending chunks processed, completing graceful shutdown');
        clearInterval(checkInterval);
        this.completeGracefulShutdown();
      } else {
        console.log(`VoiceHype: Still waiting for ${this.pendingChunks.size} pending chunks`);
        this.emitStatus(TranscriptionStatus.StreamingAudio, `Waiting for ${this.pendingChunks.size} pending chunks...`);
      }
    }, 1000); // Check every second
  }

  /**
   * Send close message to server requesting final transcript
   */
  private sendCloseMessage(): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      try {
        console.log('VoiceHype: Sending close message to request final transcript');
        this.ws.send(JSON.stringify({
          type: 'close',
          requestFinalTranscript: true,
          pendingChunks: this.pendingChunks.size
        }));

        this.emitStatus(TranscriptionStatus.StreamingAudio, 'Requesting final transcription...');
      } catch (error) {
        console.error('VoiceHype: Error sending close message:', error);
      }
    }
  }

  /**
   * Complete graceful shutdown successfully
   */
  private completeGracefulShutdown(): void {
    if (this.shutdownTimeout) {
      clearTimeout(this.shutdownTimeout);
      this.shutdownTimeout = null;
    }

    console.log('VoiceHype: Graceful shutdown completed successfully');

    // Wait a bit more for any final transcripts
    setTimeout(() => {
      this.finalizeTranscription('Graceful shutdown completed');
    }, 2000); // Wait 2 seconds for final transcripts
  }

  /**
   * Force shutdown completion when timeout is reached
   */
  private forceShutdownCompletion(reason: string): void {
    if (this.shutdownTimeout) {
      clearTimeout(this.shutdownTimeout);
      this.shutdownTimeout = null;
    }

    const remainingChunks = this.pendingChunks.size;
    console.log(`VoiceHype: Forcing shutdown completion - ${reason}, ${remainingChunks} chunks remaining`);

    // Clear pending chunks
    this.pendingChunks.clear();

    this.finalizeTranscription(`Forced completion: ${reason}`);
  }

  /**
   * Finalize transcription and emit completion events
   */
  private finalizeTranscription(reason: string): void {
    if (!this.allChunksProcessed) {
      this.allChunksProcessed = true;

      // Use accumulated transcript or partial as fallback
      if (this.transcript.trim() !== '') {
        console.log(`VoiceHype: Using accumulated final transcripts: "${this.transcript.substring(0, 100)}..."`);
        this.emitStatus(TranscriptionStatus.AllChunksProcessed, 'Using accumulated transcripts');
        this.emitStatus(TranscriptionStatus.TranscriptionComplete, 'Transcription complete');
      }
      else if (this.lastPartialTranscript.trim() !== '') {
        console.log(`VoiceHype: Using last partial transcript: "${this.lastPartialTranscript}"`);
        this.transcript = this.lastPartialTranscript;
        this.transcriptionListeners.forEach(listener => listener(this.lastPartialTranscript, true));
        this.emitStatus(TranscriptionStatus.AllChunksProcessed, 'Using partial transcript');
        this.emitStatus(TranscriptionStatus.TranscriptionComplete, 'Transcription complete');
      }
      else {
        console.log('VoiceHype: No transcription received (user may not have spoken)');
        this.emitStatus(TranscriptionStatus.AllChunksProcessed, 'No speech detected');
        this.emitStatus(TranscriptionStatus.TranscriptionComplete, 'Transcription complete (no speech detected)');
      }
    }

    console.log(`VoiceHype: Transcription finalized - ${reason}`);
  }

  /**
   * Mark a chunk as processed (called when we receive transcription for it)
   */
  private markChunkProcessed(chunkId?: number): void {
    if (chunkId && this.pendingChunks.has(chunkId)) {
      this.pendingChunks.delete(chunkId);
      console.log(`VoiceHype: Chunk #${chunkId} processed, ${this.pendingChunks.size} remaining`);
    } else {
      // If no specific chunk ID, remove the oldest pending chunk
      const oldestChunk = Array.from(this.pendingChunks.keys())[0];
      if (oldestChunk) {
        this.pendingChunks.delete(oldestChunk);
        console.log(`VoiceHype: Oldest chunk #${oldestChunk} processed, ${this.pendingChunks.size} remaining`);
      }
    }

    // If we're shutting down and no more pending chunks, complete shutdown
    if (this.isShuttingDown && this.pendingChunks.size === 0) {
      console.log('VoiceHype: Last pending chunk processed during shutdown');
      this.completeGracefulShutdown();
    }
  }

  // Pause timeout functionality
  private startPauseTimeout(): void {
    this.clearPauseTimeout();
    this.pauseTimeout = setTimeout(() => {
      console.log(`VoiceHype: Pause timeout reached after ${this.PAUSE_TIMEOUT_MINUTES} minutes`);
      this.emitStatus(TranscriptionStatus.PauseTimeout, `Session paused for ${this.PAUSE_TIMEOUT_MINUTES} minutes - closing connection`);
      this.close();
    }, this.PAUSE_TIMEOUT_MINUTES * 60 * 1000);
  }

  private clearPauseTimeout(): void {
    if (this.pauseTimeout) {
      clearTimeout(this.pauseTimeout);
      this.pauseTimeout = null;
    }
  }

  private resetPauseTimeout(): void {
    this.clearPauseTimeout();
    this.lastActivityTime = Date.now();
  }

  public pause(): void {
    if (!this.isPaused) {
      this.isPaused = true;
      this.startPauseTimeout();
      this.emitStatus(TranscriptionStatus.Paused, 'Transcription paused');
    }
  }

  public resume(): void {
    if (this.isPaused) {
      this.isPaused = false;
      this.clearPauseTimeout();
      this.resetPauseTimeout();
      this.emitStatus(TranscriptionStatus.StreamingAudio, 'Transcription resumed');
    }
  }

  // Set up heartbeat and connection monitoring
  private setupHeartbeatAndMonitor(): void {
    // Clear any existing intervals
    this.clearHeartbeatAndMonitor();

    console.log(`VoiceHype: [DEBUG] Setting up heartbeat (${this.HEARTBEAT_INTERVAL}ms) and connection monitor`);

    // Set up heartbeat to keep the connection alive
    this.heartbeatInterval = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        const timeSinceLastMessage = Date.now() - this.lastMessageReceivedTime;
        console.log(`VoiceHype: [DEBUG] Sending heartbeat ping. WebSocket state: ${this.ws.readyState}, ${timeSinceLastMessage}ms since last message`);
        try {
          this.ws.send(JSON.stringify({
            type: 'ping',
            timestamp: Date.now(),
            debug: {
              timeSinceLastMessage,
              connectionState: this.ws.readyState
            }
          }));
        } catch (error) {
          console.error(`VoiceHype: [DEBUG] Error sending heartbeat: ${error instanceof Error ? error.message : String(error)}`);

          // Log detailed WebSocket state
          console.error(`VoiceHype: [DEBUG] WebSocket state during error: readyState=${this.ws.readyState}, isConnected=${this.isConnected}`);
        }
      } else {
        const state = this.ws ? this.ws.readyState : 'null';
        console.log(`VoiceHype: [DEBUG] WebSocket not open (state: ${state}), clearing heartbeat`);
        this.clearHeartbeatAndMonitor();
      }
    }, this.HEARTBEAT_INTERVAL);

    // Set up connection monitor to detect stalled connections
    this.connectionMonitorInterval = setInterval(() => {
      const now = Date.now();
      const timeSinceLastMessage = now - this.lastMessageReceivedTime;

      console.log(`VoiceHype: [DEBUG] Connection monitor check - ${timeSinceLastMessage}ms since last message, timeout threshold: ${this.CONNECTION_TIMEOUT}ms`);

      // Log WebSocket state
      if (this.ws) {
        console.log(`VoiceHype: [DEBUG] WebSocket state: readyState=${this.ws.readyState}, isConnected=${this.isConnected}`);
      } else {
        console.log(`VoiceHype: [DEBUG] WebSocket is null, isConnected=${this.isConnected}`);
      }

      if (timeSinceLastMessage > this.CONNECTION_TIMEOUT) {
        console.error(`VoiceHype: [DEBUG] Connection appears stalled (${timeSinceLastMessage}ms since last message, threshold: ${this.CONNECTION_TIMEOUT}ms)`);
        this.emitStatus(TranscriptionStatus.Error, `Connection stalled - no response from server for ${Math.round(timeSinceLastMessage/1000)}s`);

        // Attempt to reconnect
        this.attemptReconnection();
      }
    }, 5000); // Check every 5 seconds for more frequent monitoring
  }

  // Clear heartbeat and connection monitoring intervals
  private clearHeartbeatAndMonitor(): void {
    if (this.heartbeatInterval !== null) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }

    if (this.connectionMonitorInterval !== null) {
      clearInterval(this.connectionMonitorInterval);
      this.connectionMonitorInterval = null;
    }
  }

  // Attempt to reconnect to the WebSocket
  private attemptReconnection(): void {
    const reconnectStartTime = Date.now();
    console.log(`VoiceHype: [DEBUG] Attempting to reconnect to WebSocket at ${new Date().toISOString()}`);

    // Log detailed connection state before reconnection
    if (this.ws) {
      console.log(`VoiceHype: [DEBUG] Current WebSocket state before reconnection: readyState=${this.ws.readyState}, isConnected=${this.isConnected}`);
      console.log(`VoiceHype: [DEBUG] Time since last message: ${Date.now() - this.lastMessageReceivedTime}ms`);
    } else {
      console.log(`VoiceHype: [DEBUG] WebSocket is null before reconnection, isConnected=${this.isConnected}`);
    }

    // Close existing connection if it's still open
    if (this.ws) {
      try {
        console.log(`VoiceHype: [DEBUG] Closing existing WebSocket connection with readyState=${this.ws.readyState}`);
        this.ws.close();
      } catch (error) {
        console.error(`VoiceHype: [DEBUG] Error closing existing WebSocket: ${error instanceof Error ? error.message : String(error)}`);
      }
      this.ws = null;
    }

    // Clear existing intervals
    this.clearHeartbeatAndMonitor();

    // Attempt to reconnect
    this.emitStatus(TranscriptionStatus.Reconnecting, `Reconnecting to transcription service...`);
    this.connect().then(() => {
      const reconnectTime = Date.now() - reconnectStartTime;
      console.log(`VoiceHype: [DEBUG] Successfully reconnected after ${reconnectTime}ms`);

      // Log new connection state
      if (this.ws) {
        console.log(`VoiceHype: [DEBUG] New WebSocket state after reconnection: readyState=${this.ws.readyState}, isConnected=${this.isConnected}`);
      } else {
        console.log(`VoiceHype: [DEBUG] WebSocket is null after reconnection (unexpected), isConnected=${this.isConnected}`);
      }

      this.emitStatus(TranscriptionStatus.Reconnected, `Reconnected successfully after ${Math.round(reconnectTime/1000)}s`);
    }).catch(error => {
      const reconnectTime = Date.now() - reconnectStartTime;
      console.error(`VoiceHype: [DEBUG] Failed to reconnect after ${reconnectTime}ms: ${error instanceof Error ? error.message : String(error)}`);
      this.emitStatus(TranscriptionStatus.Error, `Failed to reconnect to transcription service: ${error instanceof Error ? error.message : String(error)}`);
    });
  }

  /**
   * Closes the WebSocket connection and cleans up resources
   */
  public close() {
    console.log("VoiceHype: Closing WebSocket connection");

    // Clear heartbeat and monitor
    this.clearHeartbeatAndMonitor();

    // Clear graceful shutdown timeout
    if (this.shutdownTimeout) {
      clearTimeout(this.shutdownTimeout);
      this.shutdownTimeout = null;
    }

    // Reset graceful shutdown state
    this.pendingChunks.clear();
    this.chunkCounter = 0;
    this.isShuttingDown = false;

    // Make sure we mark the transcription as complete before closing
    // This ensures we don't leave the user waiting
    if (!this.allChunksProcessed) {
      this.allChunksProcessed = true;
      
      // If we have any transcript data, finalize it
      if (this.transcript.trim() !== '') {
        console.log(`VoiceHype: Finalizing with accumulated transcript: "${this.transcript.substring(0, 100)}${this.transcript.length > 100 ? '...' : ''}"`);
        this.emitStatus(TranscriptionStatus.AllChunksProcessed, 'Finalizing with accumulated transcript');
        this.emitStatus(TranscriptionStatus.TranscriptionComplete, 'Transcription complete');
      }
      // If no final transcript but have partial, use partial
      else if (this.lastPartialTranscript.trim() !== '') {
        console.log(`VoiceHype: Using last partial as final transcript: "${this.lastPartialTranscript}"`);
        this.transcript = this.lastPartialTranscript;
        this.transcriptionListeners.forEach(listener => listener(this.lastPartialTranscript, true));
        this.emitStatus(TranscriptionStatus.AllChunksProcessed, 'Using partial as final transcript');
        this.emitStatus(TranscriptionStatus.TranscriptionComplete, 'Transcription complete');
      }
      // If no transcript at all, just finalize
      else {
        console.log('VoiceHype: No transcript received - finalizing empty transcript');
        this.emitStatus(TranscriptionStatus.AllChunksProcessed, 'No speech detected');
        this.emitStatus(TranscriptionStatus.TranscriptionComplete, 'Transcription complete (no speech detected)');
      }
    }

    if (this.ws && (this.ws.readyState === WebSocket.OPEN || this.ws.readyState === WebSocket.CONNECTING)) {
      // Try to send a clean close message but don't wait for response
      if (this.ws.readyState === WebSocket.OPEN) {
        try {
          console.log('VoiceHype: Sending final close message before terminating connection');
          this.ws.send(JSON.stringify({
            type: 'close',
            forceClose: true // Add explicit signal that we're forcing a close
          }));
        } catch (error) {
          console.log(`VoiceHype: Error sending close message: ${error instanceof Error ? error.message : String(error)}`);
        }
      }

      // Force close the connection with a timeout to ensure it always happens
      const forceCloseTimeout = setTimeout(() => {
        if (this.ws) {
          console.log('VoiceHype: Force terminating WebSocket connection');
          try {
            this.ws.terminate(); // Use terminate instead of close for forceful disconnection
          } catch (error) {
            console.log(`VoiceHype: Error terminating WebSocket: ${error instanceof Error ? error.message : String(error)}`);
          }
          this.ws = null;
          this.isConnected = false;
        }
      }, 200); // Force close after 200ms if normal close doesn't complete

      // Try normal close first
      try {
        console.log('VoiceHype: Closing WebSocket connection normally');
        this.ws.close(1000, 'Client requested close');
        clearTimeout(forceCloseTimeout); // Clear timeout if close succeeds
      } catch (error) {
        console.log(`VoiceHype: Error closing WebSocket normally: ${error instanceof Error ? error.message : String(error)}`);
        // Let the force close timeout handle it
      }
      
      this.ws = null;
    }

    this.isConnected = false;
    this.isTranscriptionReady = false;
  }

  // Get the current transcript
  public getTranscript(): string {
    return this.transcript.trim();
  }

  // Event listeners
  public onStatusChange(listener: (status: TranscriptionStatus, message?: string) => void): void {
    this.statusListeners.push(listener);
  }

  public onTranscriptionUpdate(listener: (text: string, isFinal: boolean) => void): void {
    this.transcriptionListeners.push(listener);
  }

  public onReady(listener: () => void): void {
    this.readyListeners.push(listener);

    // If already connected, call immediately
    if (this.isConnected) {
      listener();
    }
  }

  /**
   * Register a listener to be notified when the transcription service is ready.
   * This is different from onReady which is called when the WebSocket connects.
   * This event happens after the connection is established AND the remote service is ready.
   */
  public onTranscriptionReady(listener: () => void): void {
    this.transcriptionReadyListeners.push(listener);
    
    // If transcription already ready, call immediately
    if (this.isTranscriptionReady) {
      listener();
    }
  }

  private emitStatus(status: TranscriptionStatus, message?: string): void {
    console.log(`VoiceHype: Status update - ${status}${message ? `: ${message}` : ''}`);
    this.statusListeners.forEach(listener => listener(status, message));
  }

  // Add a method to get the processed duration
  public getProcessedDuration(): number {
    return this.processedDuration;
  }

  // Add a method to check if all chunks have been processed
  public areAllChunksProcessed(): boolean {
    return this.allChunksProcessed;
  }

  // Add a method to wait for all chunks to be processed with timeout
  public async waitForProcessing(timeoutMs: number = 5000): Promise<boolean> {
    if (this.allChunksProcessed) {
      console.log('VoiceHype: All chunks already processed, no need to wait');
      return true;
    }

    console.log(`VoiceHype: Waiting for all chunks to be processed (max ${timeoutMs}ms)...`);
    this.emitStatus(TranscriptionStatus.StreamingAudio, 'Finalizing transcription...');

    return new Promise<boolean>((resolve) => {
      const startTime = Date.now();
      const timeout = setTimeout(() => {
        const elapsed = Date.now() - startTime;
        console.log(`VoiceHype: Timeout after ${elapsed}ms waiting for all chunks to be processed`);
        // Even if we time out, return true - we'll use whatever transcript we have
        this.emitStatus(TranscriptionStatus.AllChunksProcessed, 'Proceeding with available transcript');
        resolve(true);
      }, timeoutMs);

      // Keep track of how long we've been waiting
      let waitCount = 0;

      const checkProcessed = () => {
        if (this.allChunksProcessed) {
          const elapsed = Date.now() - startTime;
          console.log(`VoiceHype: All chunks have been processed after ${elapsed}ms`);
          clearTimeout(timeout);
          resolve(true);
          return;
        }

        // Report waiting status periodically
        waitCount++;
        if (waitCount % 3 === 0) { // Every 1.5 seconds (500ms * 3)
          const elapsed = Date.now() - startTime;
          const message = `Finalizing... (${(elapsed/1000).toFixed(1)}s)`;
          console.log(`VoiceHype: ${message}`);
          this.emitStatus(TranscriptionStatus.StreamingAudio, message);

          // If we've been waiting more than 3 seconds, just proceed anyway
          if (elapsed > 3000) {
            console.log('VoiceHype: Been waiting for 3s, proceeding with available transcript');
            clearTimeout(timeout);
            this.emitStatus(TranscriptionStatus.AllChunksProcessed, 'Proceeding with available transcript');
            resolve(true);
            return;
          }
        }

        // Check every 500ms
        setTimeout(checkProcessed, 500);
      };

      checkProcessed();
    });
  }

  // Get a summary of the transcription process
  public getTranscriptionSummary(): string {
    const transcriptLength = this.transcript.trim().length;
    const words = this.transcript.trim().split(/\s+/).length;

    if (transcriptLength === 0) {
      return 'No transcription available yet.';
    }

    let summary = `Transcribed ${words} words`;

    if (this.processedDuration > 0) {
      summary += ` from ${this.processedDuration.toFixed(1)}s of audio`;
      const wordsPerSecond = words / this.processedDuration;
      summary += ` (${wordsPerSecond.toFixed(1)} words/sec)`;
    }

    return summary;
  }

  // Method to update sample rate
  public updateSampleRate(newSampleRate: number): void {
    if (this.sampleRate === newSampleRate) {
      return; // No change needed
    }

    console.log(`VoiceHype: Updating sample rate from ${this.sampleRate}Hz to ${newSampleRate}Hz`);
    this.sampleRate = newSampleRate;
  }

  // Method to manually reset transcript data (for new recording sessions)
  public resetTranscriptData(): void {
    console.log(`VoiceHype: Manually resetting transcript data (was ${this.transcript.length} chars)`);
    this.transcript = '';
    this.lastPartialTranscript = '';
    this.processedDuration = 0;
    this.allChunksProcessed = false;
    this.finalDurationReceived = false;
    this.isTranscriptionReady = false; // Reset transcription ready state for a new session
  }

  // Method to check if we have any transcript data
  public hasTranscriptData(): boolean {
    return this.transcript.trim().length > 0 || this.lastPartialTranscript.trim().length > 0;
  }
  
  // Method to check if transcription service is ready
  public isTranscriptionServiceReady(): boolean {
    return this.isTranscriptionReady;
  }
}
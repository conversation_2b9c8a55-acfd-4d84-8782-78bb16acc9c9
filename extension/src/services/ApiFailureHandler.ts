import * as vscode from 'vscode';
import { TranscriptionError } from '../models/interfaces';
import { TranscriptionStatus } from './TranscriptionService';

export enum CircuitState {
  Closed = 'closed',
  Open = 'open',
  HalfOpen = 'half-open'
}

export class ApiFailureHandler {
  private state: CircuitState = CircuitState.Closed;
  private failureCount = 0;
  private lastFailureTime = 0;
  private nextRetryTime = 0;
  private readonly failureThreshold = 3;
  private readonly retryTimeoutBase = 2000; // 2 seconds
  private readonly maxRetryTimeout = 1000; // 1 seconds
  private statusListeners: ((status: TranscriptionStatus, message?: string) => void)[] = [];

  constructor(
    private readonly serviceName: string,
    private readonly maxRetries = 3,
    private readonly isRealtime = false,
    private readonly autoRetry = true // New parameter to control automatic retry behavior
  ) {}

  public addStatusListener(listener: (status: TranscriptionStatus, message?: string) => void): void {
    this.statusListeners.push(listener);
  }

  private emitStatus(status: TranscriptionStatus, message?: string): void {
    console.log(`${this.serviceName}: Status update - ${status}${message ? `: ${message}` : ''}`);
    this.statusListeners.forEach(listener => listener(status, message));
  }

  public async executeWithRetry<T>(
    apiCall: () => Promise<T>
  ): Promise<T> {
    if (this.state === CircuitState.Open) {
      if (Date.now() < this.nextRetryTime) {
        throw new Error(`Service ${this.serviceName} is temporarily unavailable`);
      }
      this.state = CircuitState.HalfOpen;
    }

    try {
      const result = await apiCall();
      this.recordSuccess();
      return result;
    } catch (error: any) {
      // Handle TranscriptionError specifically
      if (error instanceof TranscriptionError) {
        return this.handleTranscriptionError(error, apiCall);
      }

      // Enhanced error handling for AssemblyAI
      if (this.serviceName.toLowerCase().includes('assemblyai')) {
        // Detect network connectivity issues
        if (error.message?.includes('ENOTFOUND') || 
            error.message?.includes('ECONNREFUSED') ||
            error.message?.includes('Could not connect')) {
          console.log('AssemblyAI network error detected:', error.message);
          this.recordFailure();
          
          // Don't show error immediately, let retry mechanism handle it
          if (this.failureCount >= this.maxRetries) {
            // Only show notification after all retry attempts
            vscode.window.showInformationMessage('Network connection failed. Please check your internet connection.');
          }
          throw new Error('Network connection failed. Please check your internet connection.');
        }
      }

      // Extract error message from API response if available
      let errorMessage = error.message || 'Unknown error occurred';
      if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      // Record the failure
      this.recordFailure();
      
      // Only show error notification after all retries have failed
      if (this.failureCount >= this.maxRetries) {
        vscode.window.showInformationMessage(`${this.serviceName} error: ${errorMessage}`);
        throw new Error(`Max retries (${this.maxRetries}) exceeded for ${this.serviceName}`);
      }
      
      // Log retry attempt but don't show notification to user
      console.log(`VoiceHype: Automatic retry attempt ${this.failureCount}/${this.maxRetries}`);
      
      // Add a small delay before retrying
      const retryTimeout = this.calculateRetryTimeout();
      await new Promise(resolve => setTimeout(resolve, retryTimeout));
      
      // Recursively retry
      return this.executeWithRetry(apiCall);
    }
  }

  private async handleTranscriptionError<T>(
    error: TranscriptionError,
    apiCall: () => Promise<T>
  ): Promise<T> {
    this.recordFailure();

    // Check if we've reached max retries
    if (this.failureCount >= this.maxRetries) {
      // Only show error notification after all retries have failed
      if (error.phase === 'transcription') {
        this.emitStatus(TranscriptionStatus.TranscriptionFailed, this.getTranscriptionErrorMessage(error));
        
        // Show information message with retry buttons instead of error dialog
        const errorMessage = this.getTranscriptionErrorMessage(error);
        const choice = await vscode.window.showInformationMessage(
          errorMessage,
          'Retry Transcription and Optimization',
          'Retry Only Transcription'
        );

        if (choice === 'Retry Transcription and Optimization' || choice === 'Retry Only Transcription') {
          // Reset failure count for a new attempt
          this.failureCount = 0;
          
          const progressMessage = vscode.window.setStatusBarMessage(
            `$(sync~spin) ${choice === 'Retry Transcription and Optimization' ? 'Retrying transcription and optimization...' : 'Retrying transcription only...'}`
          );

          try {
            if (choice === 'Retry Only Transcription') {
              // Set flag to skip optimization
              (apiCall as any).skipOptimization = true;
            }
            const retryTimeout = this.calculateRetryTimeout();
            await new Promise(resolve => setTimeout(resolve, retryTimeout));
            this.emitStatus(TranscriptionStatus.Initializing, 'Retrying transcription...');
            const result = await this.executeWithRetry(apiCall);
            return result;
          } finally {
            progressMessage.dispose();
          }
        }
      } else if (error.phase === 'optimization') {
        this.emitStatus(TranscriptionStatus.OptimizationFailed, this.getOptimizationErrorMessage(error));
        
        // Show information message with retry buttons for optimization
        const errorMessage = this.getOptimizationErrorMessage(error);
        const choice = await vscode.window.showInformationMessage(
          errorMessage,
          'Retry Optimization',
          'Copy Transcript'
        );

        if (choice === 'Retry Optimization') {
          // Reset failure count for a new attempt
          this.failureCount = 0;
          
          const progressMessage = vscode.window.setStatusBarMessage(
            '$(sync~spin) Retrying optimization...'
          );

          try {
            const retryTimeout = this.calculateRetryTimeout();
            await new Promise(resolve => setTimeout(resolve, retryTimeout));
            this.emitStatus(TranscriptionStatus.Initializing, 'Retrying optimization...');
            const result = await this.executeWithRetry(apiCall);
            return result;
          } finally {
            progressMessage.dispose();
          }
        } else if (choice === 'Copy Transcript') {
          this.emitStatus(TranscriptionStatus.PartialSuccess, 'Using original transcription');
          if (error.originalError && 'transcription' in error.originalError) {
            const originalTranscript = error.originalError.transcription as string;
            // Copy to clipboard instead of returning
            await vscode.env.clipboard.writeText(originalTranscript);
            vscode.window.showInformationMessage('Original transcript copied to clipboard');
            return originalTranscript as T;
          }
          throw new Error('Could not find original transcription');
        }
      }
      
      // If no action was chosen or unknown phase
      throw error;
    }

    // Automatic retry for failures before max retry count
    console.log(`VoiceHype: Automatic retry attempt ${this.failureCount}/${this.maxRetries} for ${error.phase} error`);
    
    // Add a small delay before retrying
    const retryTimeout = this.calculateRetryTimeout();
    await new Promise(resolve => setTimeout(resolve, retryTimeout));
    
    // Show subtle status bar indicator for automatic retry
    const progressMessage = vscode.window.setStatusBarMessage(
      `$(sync~spin) Automatically retrying ${error.phase}... (${this.failureCount}/${this.maxRetries})`
    );
    
    try {
      // For transcription failures, if we're on the last automatic retry, skip optimization
      if (error.phase === 'transcription' && this.failureCount === this.maxRetries - 1) {
        (apiCall as any).skipOptimization = true;
      }
      
      this.emitStatus(
        error.phase === 'transcription' ? TranscriptionStatus.Initializing : TranscriptionStatus.Initializing, 
        `Automatic retry for ${error.phase}...`
      );
      
      const result = await this.executeWithRetry(apiCall);
      return result;
    } finally {
      progressMessage.dispose();
    }
  }

  private recordSuccess(): void {
    this.failureCount = 0;
    this.state = CircuitState.Closed;
    if (this.isRealtime) {
      vscode.window.showInformationMessage(`${this.serviceName} connection restored`);
    }
  }

  private recordFailure(): void {
    this.failureCount++;
    this.lastFailureTime = Date.now();
    if (this.failureCount >= this.failureThreshold) {
      this.state = CircuitState.Open;
      this.nextRetryTime = Date.now() + this.calculateRetryTimeout();
      
      // Only show notification after maximum retries (use information message instead of error)
      if (this.failureCount >= this.maxRetries) {
        vscode.window.showInformationMessage(`${this.serviceName} is temporarily unavailable.`);
      } else {
        // Just log the circuit state change without showing UI notification
        console.log(`VoiceHype: Circuit breaker opened for ${this.serviceName} (attempt ${this.failureCount}/${this.maxRetries})`);
      }
    }
  }

  private calculateRetryTimeout(): number {
    const timeout = Math.min(
      this.retryTimeoutBase * Math.pow(2, this.failureCount),
      this.maxRetryTimeout
    );
    return timeout;
  }

  private getTranscriptionErrorMessage(error: TranscriptionError): string {
    // Extract useful information from the error
    const errorDetails = this.getErrorDetails(error.originalError);
    return `${this.serviceName} transcription failed: ${errorDetails}`;
  }

  private getOptimizationErrorMessage(error: TranscriptionError): string {
    // Extract useful information from the error
    const errorDetails = this.getErrorDetails(error.originalError);
    return `${this.serviceName} optimization failed: ${errorDetails}`;
  }

  private getErrorDetails(error: Error): string {
    if (!error) {
        return 'An unknown error occurred';
    }

    // Import and use the shared error cleaner
    const { cleanErrorMessage } = require('../utils/errorCleaner');
    const msg = error.message.toLowerCase();
    const cleanMessage = cleanErrorMessage(error.message);

    // Network connectivity issues
    if (msg.includes('enotfound') || msg.includes('econnrefused') || msg.includes('timeout') ||
        msg.includes('network') || msg.includes('connection') || msg.includes('econn')) {
        return 'Network connection error. Please check your internet connection and try again.';
    }

    // Authentication issues
    if (msg.includes('401') || msg.includes('unauthorized') || msg.includes('invalid key') ||
        msg.includes('authentication failed')) {
        return 'Invalid API key or authentication failed. Please check your API key.';
    }

    // Rate limiting
    if (msg.includes('429') || msg.includes('too many requests') || msg.includes('rate limit')) {
        return 'Rate limit exceeded. Please wait a moment and try again.';
    }

    // Server errors
    if (msg.includes('500') || msg.includes('502') || msg.includes('503') || msg.includes('504')) {
        return 'Server is temporarily unavailable. Please try again in a few minutes.';
    }

    // Audio issues
    if (msg.includes('audio') || msg.includes('wav') || msg.includes('recording')) {
        if (msg.includes('empty') || msg.includes('invalid')) {
            return 'No audio detected or invalid audio format. Please try recording again.';
        }
        if (msg.includes('too long') || msg.includes('duration')) {
            return 'Audio recording is too long. Please try a shorter recording.';
        }
        return 'Audio processing error. Please try recording again.';
    }

    return cleanMessage || 'An unexpected error occurred';
  }
}

import * as vscode from 'vscode';
import * as path from 'path';
import { IFileTrackingService, FileTrackingEntry } from '../models/interfaces';

export class FileTrackingService implements IFileTrackingService {
    private fileTrackingLog: FileTrackingEntry[] = [];
    private disposables: vscode.Disposable[] = [];
    private isTracking: boolean = false;

    constructor() {}

    startTracking(): void {
        if (this.isTracking) {
            return;
        }

        this.isTracking = true;
        this.fileTrackingLog = [];
        this.setupFileTracking();
    }

    stopTracking(): FileTrackingEntry[] {
        this.isTracking = false;
        this.disposables.forEach(d => d.dispose());
        this.disposables = [];
        return [...this.fileTrackingLog];
    }

    private setupFileTracking(): void {
        // Track file opens
        this.disposables.push(
            vscode.window.onDidChangeActiveTextEditor(editor => {
                if (this.isTracking && editor) {
                    this.fileTrackingLog.push({
                        timestamp: Date.now(),
                        fileName: path.basename(editor.document.fileName),
                        filePath: editor.document.fileName,
                        action: 'opened'
                    });
                }
            })
        );

        // Track selections
        this.disposables.push(
            vscode.window.onDidChangeTextEditorSelection(event => {
                if (this.isTracking && !event.selections[0].isEmpty) {
                    const editor = event.textEditor;
                    const selection = event.selections[0]; // Using primary selection
                    const startLine = selection.start.line + 1; // Convert to 1-based line numbers
                    const endLine = selection.end.line + 1;
                    
                    // Only log if it's a meaningful selection (more than one character)
                    if (startLine !== endLine || selection.start.character !== selection.end.character) {
                        this.fileTrackingLog.push({
                            timestamp: Date.now(),
                            fileName: path.basename(editor.document.fileName),
                            filePath: editor.document.fileName,
                            action: 'selected',
                            selection: {
                                startLine,
                                endLine
                            }
                        });
                    }
                }
            })
        );
    }

    dispose(): void {
        this.disposables.forEach(d => d.dispose());
    }
} 
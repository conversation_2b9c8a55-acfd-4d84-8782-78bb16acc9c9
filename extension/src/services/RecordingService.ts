import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { EventEmitter } from 'events';
import { Microphone } from '../utils/microphone';
import { AudioValidator } from '../utils/audio';
import { AudioPlayerPanel } from '../views/audioPlayer';
import { IRecordingService } from '../models/interfaces';
import { IConfigurationService } from '../models/interfaces';
import { IStatusBarService } from '../models/interfaces';
import { IFileTrackingService } from '../models/interfaces';
import { ITranscriptionService } from '../models/interfaces';
import { IHistoryService } from '../models/interfaces';
import { isRetryableError, getUserFriendlyErrorMessage } from '../types/errors';
import { RealtimeConnectionManager, AudioChunk } from './RealtimeConnectionManager';
import { TranscriptionStatus } from './TranscriptionService';
import { VoiceHypePanel } from './VoiceHypePanelService';

export class RecordingService implements IRecordingService {
    private mic: Microphone;
    private _isRecording: boolean = false;
    private _isPaused: boolean = false;
    private recordingPath: string;
    private recordingStartTime: number = 0;
    private pauseStartTime: number = 0;
    private totalPausedTime: number = 0;
    private disposables: vscode.Disposable[] = [];
    private progressNotification?: vscode.Progress<{ message?: string; increment?: number }>;
    private progressResolver?: () => void;
    private currentTranscript: string = '';
    private lastError: Error | null = null;
    private retryCount: number = 0;
    private readonly MAX_RETRIES = 3;
    private _hasRecording: boolean = false;
    private startedWithOptimize: boolean = false;  // Track which shortcut started recording
    private writeStream: fs.WriteStream | null = null;
    private audioStream: NodeJS.ReadableStream | null = null;
    private realtimeConnection: RealtimeConnectionManager | null = null;
    private useRealtime: boolean = false;

    // Static property to track if there's any active real-time connection globally
    private static activeRealtimeConnection: RealtimeConnectionManager | null = null;
    private realTimeTranscript: string = '';
    private stateChangeEmitter = new EventEmitter();
    private timerInterval: NodeJS.Timeout | null = null;
    private elapsedTime: number = 0;

    private voiceHypePanel?: VoiceHypePanel;

    /**
     * Force cleanup of any active real-time connections globally
     * This should be called before starting a new real-time session to ensure clean state
     */
    public static async forceCleanupActiveConnections(): Promise<void> {
        if (RecordingService.activeRealtimeConnection) {
            console.log('VoiceHype: Force cleaning up active real-time connection');
            try {
                RecordingService.activeRealtimeConnection.close();
                // Give a moment for cleanup
                await new Promise(resolve => setTimeout(resolve, 1000));
            } catch (error) {
                console.error('VoiceHype: Error during force cleanup:', error);
            }
            RecordingService.activeRealtimeConnection = null;
        }
    }

    constructor(
        private context: vscode.ExtensionContext,
        private configService: IConfigurationService,
        private statusBarService: IStatusBarService,
        private fileTrackingService: IFileTrackingService,
        private transcriptionService: ITranscriptionService,
        private historyService: IHistoryService
    ) {
        console.log('VoiceHype: Initializing recorder...');

        // Use the same temporary file as the microphone class
        const tmpDir = os.tmpdir();
        // Use different filename based on platform
        if (os.platform() === 'linux') {
            this.recordingPath = path.join(tmpDir, 'voicehype_recording.wav');
        } else {
            this.recordingPath = path.join(tmpDir, 'voicehype_recording.wav');
        }
        console.log('VoiceHype: Recording path:', this.recordingPath);

        const audioSettings = this.configService.getAudioSettings();
        console.log('VoiceHype: Audio settings from config:', JSON.stringify(audioSettings));

        const micOptions = {
            rate: audioSettings.sampleRate,
            ...(audioSettings.device ? { device: audioSettings.device } : {})
        };
        console.log('VoiceHype: Creating microphone with options:', JSON.stringify(micOptions));

        this.mic = new Microphone(micOptions);
        console.log('VoiceHype: Created microphone instance with settings:', audioSettings);

        // Check if there's an existing recording
        if (fs.existsSync(this.recordingPath)) {
            this._hasRecording = true;
            vscode.commands.executeCommand('setContext', 'voicehype.hasRecording', true);
        }

        // Listen for configuration changes
        this.disposables.push(
            vscode.workspace.onDidChangeConfiguration(e => {
                if (e.affectsConfiguration('voicehype.audio')) {
                    const newAudioSettings = this.configService.getAudioSettings();

                    if (!this._isRecording) {
                        // If not recording, we can create a new microphone instance
                        this.mic = new Microphone({
                            rate: newAudioSettings.sampleRate,
                            ...(newAudioSettings.device ? { device: newAudioSettings.device } : {})
                        });
                        console.log('VoiceHype: Updated microphone instance with settings:', newAudioSettings);
                    } else if (this._isRecording && this.realtimeConnection) {
                        // If recording with real-time connection, update the sample rate
                        console.log('VoiceHype: Updating real-time connection sample rate to:', newAudioSettings.sampleRate);
                        this.realtimeConnection.updateSampleRate(newAudioSettings.sampleRate);
                        vscode.window.showInformationMessage(`Sample rate updated to ${newAudioSettings.sampleRate}Hz. This affects how speech is processed in real-time.`);
                    } else {
                        vscode.window.showWarningMessage('Audio settings will be applied after stopping the current recording.');
                    }
                }
            })
        );

        // Listen for direct sample rate changes from the ConfigurationService
        this.disposables.push(
            this.configService.onDidChangeSampleRate(newSampleRate => {
                console.log(`VoiceHype: Sample rate changed to ${newSampleRate}Hz from ConfigurationService`);

                if (this._isRecording && this.realtimeConnection) {
                    // If we're recording with real-time connection, update the sample rate
                    console.log('VoiceHype: Updating real-time connection sample rate to:', newSampleRate);
                    this.realtimeConnection.updateSampleRate(newSampleRate);
                }
            })
        );
    }

    /**
     * Set the VoiceHypePanel reference for webview updates
     */
    public setVoiceHypePanel(panel: VoiceHypePanel): void {
        this.voiceHypePanel = panel;
    }

    /**
     * Send connection status updates to webview
     */
    private sendConnectionStatusToWebview(status: string, message: string): void {
        try {
            if (this.voiceHypePanel) {
                this.voiceHypePanel.sendMessageToWebview({
                    command: 'realtimeConnectionStatus',
                    status: status,
                    message: message,
                    timestamp: Date.now()
                });
                console.log(`VoiceHype: Sent connection status to webview: ${status} - ${message}`);
            }
        } catch (error) {
            console.error('VoiceHype: Error sending connection status to webview:', error);
        }
    }

    isRecording(): boolean {
        return this._isRecording;
    }

    isPaused(): boolean {
        return this._isPaused;
    }

    hasRecording(): boolean {
        return this._hasRecording;
    }

    private async validateRecording(): Promise<void> {
        try {
            await AudioValidator.validateAudioFile(this.recordingPath);
        } catch (error: any) {
            this.lastError = error;
            throw error;
        }
    }

    /**
     * Calculate expected file size based on recording duration and settings
     */
    private calculateExpectedFileSize(durationMs: number): number {
        const audioSettings = this.configService.getAudioSettings();
        const bytesPerSecond = audioSettings.sampleRate * 2 * 2; // Assuming stereo (2 channels) and 16-bit (2 bytes)
        return (durationMs / 1000) * bytesPerSecond + 44; // 44-byte WAV header
    }

    /**
     * Check if recording file is too large for the current service
     */
    private async checkFileSize(): Promise<{
        isTooLarge: boolean;
        fileSizeMB: number;
        serviceLimit: number;
        service: string;
    }> {
        const stats = fs.statSync(this.recordingPath);
        const fileSizeMB = stats.size / (1024 * 1024);

        const service = this.configService.getTranscriptionService();
        const serviceLimits = {
            assemblyai: 100,
            whisper: 25,
            openai: 25
        };

        const serviceLimit = serviceLimits[service as keyof typeof serviceLimits] || 25;

        return {
            isTooLarge: fileSizeMB > serviceLimit,
            fileSizeMB,
            serviceLimit,
            service
        };
    }

    private async retryTranscription(): Promise<void> {
        if (!this.lastError || !isRetryableError(this.lastError) || this.retryCount >= this.MAX_RETRIES) {
            throw this.lastError;
        }

        this.retryCount++;
        await this.processRecording(this.startedWithOptimize);
    }

    async startRecording(withOptimize: boolean = false): Promise<void> {
        console.log('VoiceHype: Starting recording... (withOptimize:', withOptimize, ')');
        if (this._isRecording && !this._isPaused) {
            console.log('VoiceHype: Already recording, ignoring start command');
            return;
        }

        // If recording is paused, resume it
        if (this._isRecording && this._isPaused) {
            await this.resumeRecording();
            return;
        }

        try {
            // Determine if we should use real-time mode
            this.useRealtime = this.configService.getTranscriptionRealtime();

            this.startedWithOptimize = withOptimize;
            this.recordingStartTime = Date.now();
            this._isRecording = true;
            this._isPaused = false;
            this.totalPausedTime = 0;
            this.currentTranscript = '';
            this.realTimeTranscript = '';
            this.lastError = null;
            this.retryCount = 0;
            this.startTimer(); // Start the timer
            this.emitStateChange();
            console.log(`VoiceHype: RECORDING START DETAILS:`);
            console.log(`VoiceHype: - Start time: ${new Date(this.recordingStartTime).toISOString()}`);
            console.log(`VoiceHype: - Total paused time reset to: ${this.totalPausedTime}ms`);
            console.log(`VoiceHype: - Real-time mode: ${this.useRealtime}`);
            console.log(`VoiceHype: - Optimization: ${withOptimize}`);

            vscode.commands.executeCommand('setContext', 'voicehype.isRecording', true);
            vscode.commands.executeCommand('setContext', 'voicehype.isPaused', false);
            console.log('VoiceHype: Set recording context');

            this.statusBarService.updateStatusBarItems(true, withOptimize, false);
            console.log('VoiceHype: Updated status bar for recording');

            // Start file tracking
            this.fileTrackingService.startTracking();
            console.log('VoiceHype: Started file tracking');

            if (this.useRealtime) {
                await this.startRealtimeRecording();
            } else {
                await this.startRegularRecording();
            }

            // Set recording state
            this._hasRecording = false;
            vscode.commands.executeCommand('setContext', 'voicehype.hasRecording', false);
        } catch (error: any) {
            console.error('VoiceHype: Error starting recording:', error);
            vscode.window.showInformationMessage(getUserFriendlyErrorMessage(error));
            this._isRecording = false;
            this._isPaused = false;
            this.stopTimer(); // Stop the timer
            this.emitStateChange();
            vscode.commands.executeCommand('setContext', 'voicehype.isRecording', false);
            vscode.commands.executeCommand('setContext', 'voicehype.isPaused', false);
        }
    }

    private async startRealtimeRecording(): Promise<void> {
        console.log('VoiceHype: Starting real-time transcription recording');

        try {
            // Force cleanup of any active real-time connections globally
            await RecordingService.forceCleanupActiveConnections();

            // Also ensure any previous connection on this instance is properly closed
            if (this.realtimeConnection) {
                console.log('VoiceHype: Closing previous real-time connection on this instance');
                this.realtimeConnection.close();
                this.realtimeConnection = null;

                // Give a moment for cleanup
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            // First show establishing connection message
            vscode.window.showInformationMessage('Establishing connection, please wait...');

            // Set up the real-time connection
            this.realtimeConnection = await this.transcriptionService.setupRealTimeConnection();

            if (!this.realtimeConnection) {
                throw new Error('Failed to create real-time connection');
            }

            // Track this as the active connection globally
            RecordingService.activeRealtimeConnection = this.realtimeConnection;

            // Create directory for recording
            const tmpDir = path.dirname(this.recordingPath);
            console.log('VoiceHype: Recording directory is', tmpDir);

            try {
                fs.accessSync(tmpDir, fs.constants.W_OK);
                console.log('VoiceHype: Recording directory is writable:', tmpDir);
            } catch (error) {
                console.error('VoiceHype: Recording directory is not writable:', tmpDir, error);
                throw new Error(`Cannot write to recording directory: ${tmpDir}`);
            }

            // Connect to the real-time transcription service
            try {
                // Wait for the connection to be established
                await this.realtimeConnection.connect();

                // Show connected message
                vscode.window.showInformationMessage('Connection established, start speaking!');

                // Set up real-time transcription UI updates
                this.showRealtimeTranscriptionUpdates();

                // When connection is ready, start recording
                this.startMicrophoneForRealtime();

            } catch (connectionError) {
                console.error('VoiceHype: Failed to connect to real-time transcription service:', connectionError);

                // Fall back to regular recording if we can't connect
                console.log('VoiceHype: Falling back to regular recording...');
                this.useRealtime = false;
                await this.startRegularRecording();
                return;
            }
        } catch (error) {
            console.error('VoiceHype: Error setting up real-time transcription:', error);
            vscode.window.showErrorMessage(`Error starting real-time transcription: ${error instanceof Error ? error.message : String(error)}`);
            throw error;
        }
    }

    private startMicrophoneForRealtime(): void {
        // Ensure the recording file is writable (we still record to a file as backup)
        try {
            const dirPath = path.dirname(this.recordingPath);
            fs.accessSync(dirPath, fs.constants.W_OK);
            console.log('VoiceHype: Recording directory is writable:', dirPath);
        } catch (error: any) {
            throw new Error(`Cannot access recording directory: ${error.message}`);
        }

        // Create a write stream with proper WAV headers
        this.writeStream = fs.createWriteStream(this.recordingPath, { flags: 'w' });
        console.log('VoiceHype: Created write stream for recording');

        this.writeStream.on('error', (error) => {
            console.error('Error writing to file:', error);
            vscode.window.showErrorMessage(`Failed to save recording: ${error.message}`);
            this.stopRecording(false);
        });

        console.log('VoiceHype: Starting microphone for recording with real-time transcription');
        this.audioStream = this.mic.startRecording();

        // Process audio chunks for real-time transcription
        let dataChunks = 0;
        let totalBytes = 0;

        this.audioStream.on('data', (data: Buffer) => {
            dataChunks++;
            totalBytes += data.length;
            console.log(`VoiceHype: Received audio chunk #${dataChunks}, size: ${data.length} bytes, total: ${totalBytes} bytes`);

            // Write to file as backup
            if (this.writeStream) {
                this.writeStream.write(data);
            }

            // Send to real-time transcription service
            if (this.realtimeConnection) {
                const chunk: AudioChunk = {
                    data: new Uint8Array(data),
                    isLastChunk: false
                };
                this.realtimeConnection.sendAudioChunk(chunk);
            }
        });

        this.audioStream.on('end', () => {
            console.log('VoiceHype: Audio stream ended');
        });

        this.audioStream.on('error', (error: Error) => {
            console.error('Microphone error:', error);
            vscode.window.showInformationMessage(getUserFriendlyErrorMessage(error));
            this.stopRecording(false);
        });

        vscode.window.showInformationMessage(`Real-time recording started! Press Ctrl+Shift+${this.startedWithOptimize ? '9' : '8'} again to stop, or use the pause button to pause.`);
    }

    private async startRegularRecording(): Promise<void> {
        // Ensure the recording file is writable
        try {
            const dirPath = path.dirname(this.recordingPath);
            await fs.promises.access(dirPath, fs.constants.W_OK);
            console.log('VoiceHype: Recording directory is writable:', dirPath);
        } catch (error: any) {
            throw new Error(`Cannot access recording directory: ${error.message}`);
        }

        // Create a write stream with proper WAV headers
        this.writeStream = fs.createWriteStream(this.recordingPath, { flags: 'w' });
        console.log('VoiceHype: Created write stream for recording');

        this.writeStream.on('error', (error) => {
            console.error('Error writing to file:', error);
            vscode.window.showErrorMessage(`Failed to save recording: ${error.message}`);
            this.stopRecording(false);
        });

        this.writeStream.on('finish', () => {
            console.log('VoiceHype: Write stream finished');
            const stats = fs.statSync(this.recordingPath);
            console.log('VoiceHype: Recording file size:', stats.size, 'bytes');
        });

        console.log('VoiceHype: Starting microphone for recording');
        this.audioStream = this.mic.startRecording();

        // Add data event logging
        let dataChunks = 0;
        let totalBytes = 0;
        this.audioStream.on('data', (data: Buffer) => {
            dataChunks++;
            totalBytes += data.length;
            console.log(`VoiceHype: Received audio chunk #${dataChunks}, size: ${data.length} bytes, total: ${totalBytes} bytes`);
        });

        this.audioStream.on('end', () => {
            console.log('VoiceHype: Audio stream ended');
        });

        this.audioStream.on('error', (error: Error) => {
            console.error('Microphone error:', error);
            vscode.window.showInformationMessage(getUserFriendlyErrorMessage(error));
            this.stopRecording(false);
        });

        // Pipe with error handling
        this.audioStream.pipe(this.writeStream).on('error', (error: Error) => {
            console.error('Error piping audio stream:', error);
            vscode.window.showInformationMessage(`Failed to save recording: ${error.message}`);
            this.stopRecording(false);
        });

        console.log('VoiceHype: Started microphone recording');
        vscode.window.showInformationMessage(`Recording started! Press Ctrl+Shift+${this.startedWithOptimize ? '9' : '8'} again to stop, or use the pause button to pause.`);
    }

    async pauseRecording(): Promise<void> {
        console.log('VoiceHype: Pausing recording...');

        if (!this._isRecording || this._isPaused) {
            console.log('VoiceHype: Not recording or already paused, ignoring pause command');
            return;
        }

        try {
            this._isPaused = true;
            this.pauseTimer(); // Pause the timer
            this.emitStateChange();
            this.pauseStartTime = Date.now();
            console.log(`VoiceHype: Pause started at ${new Date(this.pauseStartTime).toISOString()}, current total paused time: ${this.totalPausedTime}ms`);

            vscode.commands.executeCommand('setContext', 'voicehype.isPaused', true);

            // Update status bar to show paused state
            this.statusBarService.updateStatusBarItems(true, this.startedWithOptimize, true);

            // Pause the microphone
            this.mic.pauseRecording();

            console.log('VoiceHype: Recording paused');
            vscode.window.showInformationMessage('Recording paused. Press the resume button to continue recording.');
        } catch (error: any) {
            console.error('VoiceHype: Error pausing recording:', error);
            vscode.window.showErrorMessage(`Failed to pause recording: ${error.message}`);
        }
    }

    async resumeRecording(): Promise<void> {
        console.log('VoiceHype: Resuming recording...');

        if (!this._isRecording || !this._isPaused) {
            console.log('VoiceHype: Not recording or not paused, ignoring resume command');
            return;
        }

        try {
            // Calculate paused time
            const currentTime = Date.now();
            const pauseDuration = currentTime - this.pauseStartTime;
            const previousTotalPaused = this.totalPausedTime;
            this.totalPausedTime += pauseDuration;

            console.log(`VoiceHype: PAUSE DETAILS - Resume time: ${new Date(currentTime).toISOString()}`);
            console.log(`VoiceHype: PAUSE DETAILS - Pause start time: ${new Date(this.pauseStartTime).toISOString()}`);
            console.log(`VoiceHype: PAUSE DETAILS - This pause duration: ${pauseDuration}ms (${pauseDuration / 1000} seconds)`);
            console.log(`VoiceHype: PAUSE DETAILS - Previous total paused time: ${previousTotalPaused}ms (${previousTotalPaused / 1000} seconds)`);
            console.log(`VoiceHype: PAUSE DETAILS - New total paused time: ${this.totalPausedTime}ms (${this.totalPausedTime / 1000} seconds)`);

            this._isPaused = false;
            this.resumeTimer(); // Resume the timer
            this.emitStateChange();
            vscode.commands.executeCommand('setContext', 'voicehype.isPaused', false);

            // Update status bar to show recording state
            this.statusBarService.updateStatusBarItems(true, this.startedWithOptimize, false);

            // Resume the microphone and get the stream
            this.audioStream = this.mic.resumeRecording();

            // IMPORTANT: Do NOT pipe the resumed audio stream to the write stream again!
            // The microphone implementation already handles piping the audio data correctly.
            // The previous code was causing double-piping which led to audio distortion.
            console.log('VoiceHype: Audio stream resumed - using microphone class handling');

            console.log('VoiceHype: Recording resumed');
            vscode.window.showInformationMessage('Recording resumed.');
        } catch (error: any) {
            console.error('VoiceHype: Error resuming recording:', error);
            vscode.window.showInformationMessage(`Failed to resume recording: ${error.message}`);
        }
    }

    async stopRecording(shouldOptimize: boolean = false): Promise<void> {
        console.log('VoiceHype: Stopping recording... (shouldOptimize:', shouldOptimize, ')');

        if (!this._isRecording) {
            console.log('VoiceHype: Not recording, ignoring stop command');
            return;
        }

        try {
            const stopTime = Date.now();
            const totalElapsedTime = stopTime - this.recordingStartTime;
            const totalActiveDuration = totalElapsedTime - this.totalPausedTime;

            console.log(`VoiceHype: RECORDING STOP DETAILS:`);
            console.log(`VoiceHype: - Start time: ${new Date(this.recordingStartTime).toISOString()}`);
            console.log(`VoiceHype: - Stop time: ${new Date(stopTime).toISOString()}`);
            console.log(`VoiceHype: - Total elapsed time: ${totalElapsedTime}ms (${(totalElapsedTime / 1000).toFixed(2)}s)`);
            console.log(`VoiceHype: - Total paused time: ${this.totalPausedTime}ms (${(this.totalPausedTime / 1000).toFixed(2)}s)`);
            console.log(`VoiceHype: - Total active recording time: ${totalActiveDuration}ms (${(totalActiveDuration / 1000).toFixed(2)}s)`);
            console.log(`VoiceHype: - Was paused: ${this._isPaused}`);

            // Stop recording context
            this._isRecording = false;
            this._isPaused = false;
            this.stopTimer(); // Stop the timer
            this.emitStateChange();
            vscode.commands.executeCommand('setContext', 'voicehype.isRecording', false);
            vscode.commands.executeCommand('setContext', 'voicehype.isPaused', false);

            // Update UI
            this.statusBarService.updateStatusBarItems(false, this.startedWithOptimize, false);

            // Stop file tracking and get log
            const fileTrackingLog = this.fileTrackingService.stopTracking();

            // If in real-time mode, implement a 3-second grace period for the microphone
            if (this.useRealtime && this.realtimeConnection) {
                console.log('VoiceHype: Starting 3-second grace period for microphone');
                
                // Wait for 1 second before stopping the microphone
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Send final empty chunk to signal end of stream to the transcription service
                const finalChunk: AudioChunk = {
                    data: new Uint8Array(0),
                    isLastChunk: true
                };
                this.realtimeConnection.sendAudioChunk(finalChunk);
                
                
                console.log('VoiceHype: Grace period completed, stopping microphone');
            }

            // Stop the microphone recording
            if (this.audioStream) {
                console.log('VoiceHype: Stopping audio stream');
                this.audioStream.removeAllListeners();
                this.mic.stopRecording();
                this.audioStream = null;
            }

            // If in real-time mode, implement grace period for better user experience
            if (this.useRealtime && this.realtimeConnection) {
                console.log('VoiceHype: Starting real-time transcription with grace period');

                // Get immediate transcript
                const immediateTranscript = this.realtimeConnection.getTranscript();
                console.log('VoiceHype: Immediate transcript length:', immediateTranscript.length);


                // Paste immediately if we have content and not optimizing
                if (immediateTranscript.trim() && !(shouldOptimize || this.startedWithOptimize)) {
                    await this.pasteTextIntoEditor(immediateTranscript);
                    vscode.window.showInformationMessage('Real-time transcription pasted! Waiting for any remaining content...');
                } else if (immediateTranscript.trim()) {
                    vscode.window.showInformationMessage('Completing transcription for optimization...');
                } else {
                    vscode.window.showInformationMessage('Finalizing real-time transcription...');
                }

                // Start grace period collection (8.5 seconds)
                const graceResult = await this.handleRealtimeGracePeriod(immediateTranscript);

                // Update current transcript with final result
                this.currentTranscript = graceResult.finalTranscript;

                // Handle optimization or additional content from grace period
                if (shouldOptimize || this.startedWithOptimize) {
                    // Get the custom prompt if optimization is enabled
                    const customPrompt = this.configService.getCustomPrompt();
                    console.log('VoiceHype: Using custom prompt for real-time optimization:', customPrompt);

                    // Optimize the complete transcript
                    await this.optimizeCurrentTranscript();
                } else if (graceResult.additionalContent) {
                    // Append any additional content that came during grace period
                    await this.pasteTextIntoEditor(" " + graceResult.additionalContent);
                    vscode.window.showInformationMessage('Additional transcription content added!');
                }

                // Clean up real-time connection
                console.log('VoiceHype: Closing real-time connection');
                this.realtimeConnection.close();

                // Clear global reference if this was the active connection
                if (RecordingService.activeRealtimeConnection === this.realtimeConnection) {
                    RecordingService.activeRealtimeConnection = null;
                }

                this.realtimeConnection = null;

                // If we still have a write stream open, close it (we don't need the audio file for real-time)
                if (this.writeStream) {
                    this.writeStream.end();
                    this.writeStream = null;
                }

                // Process the real-time transcription result
                if (this.currentTranscript && this.currentTranscript.trim()) {
                    console.log(`VoiceHype: Real-time transcription with grace period complete: "${this.currentTranscript.substring(0, 100)}${this.currentTranscript.length > 100 ? '...' : ''}"`);

                    // Add to history
                    // Get the audio duration from metadata via TranscriptionService
                    const audioDuration = await this.transcriptionService.getAudioDuration(this.recordingPath);

                    console.log('VoiceHype: REALTIME DURATION DETAILS:');
                    console.log(`VoiceHype: - Audio duration from metadata: ${audioDuration.toFixed(3)} seconds`);

                    // For real-time transcription, we need to update the history entry after optimization
                    // rather than creating it with the optimized transcript directly
                    this.historyService.addEntry({
                        timestamp: new Date().getTime(),
                        transcript: this.currentTranscript,
                        // Don't set optimizedTranscript here - we'll update it after optimization if needed
                        optimizedTranscript: undefined,
                        service: this.configService.getTranscriptionService(),
                        model: this.configService.getTranscriptionModel(),
                        language: this.configService.getTranscriptionLanguage(),
                        fileReferences: fileTrackingLog,
                        duration: audioDuration // Using actual audio duration from metadata
                    });

                    // Set recording state
                    this._hasRecording = true;
                    vscode.commands.executeCommand('setContext', 'voicehype.hasRecording', true);

                    // Notify VoiceHypePanel to update WebView with new transcription history
                    setTimeout(() => {
                        try {
                            if (this.voiceHypePanel) {
                                // Directly update the WebView with the latest history
                                this.voiceHypePanel.updateTranscriptionHistory();
                                console.log('VoiceHype: WebView notified of history update after real-time transcription');
                            } else {
                                console.log('VoiceHype: VoiceHypePanel not available for history update after real-time transcription');
                            }
                        } catch (err) {
                            console.error('VoiceHype: Failed to notify WebView of history update after real-time transcription:', err);
                        }
                    }, 300);
                } else {
                    // No transcription received from real-time - this is expected if user didn't speak
                    console.log('VoiceHype: No real-time transcription received (user may not have spoken)');
                    vscode.window.showInformationMessage('No speech detected during recording.');

                    // Still set recording state so user can play back the audio if needed
                    this._hasRecording = true;
                    vscode.commands.executeCommand('setContext', 'voicehype.hasRecording', true);

                    // Even with no speech, update WebView to refresh the UI state
                    setTimeout(() => {
                        try {
                            if (this.voiceHypePanel) {
                                // Update the WebView to reflect the current state
                                this.voiceHypePanel.updateTranscriptionHistory();
                                console.log('VoiceHype: WebView notified of state update after no-speech real-time recording');
                            } else {
                                console.log('VoiceHype: VoiceHypePanel not available for state update after no-speech real-time recording');
                            }
                        } catch (err) {
                            console.error('VoiceHype: Failed to notify WebView of state update after no-speech real-time recording:', err);
                        }
                    }, 300);
                }
            } else {
                // In regular mode, close the write stream and process the recording
                if (this.writeStream) {
                    console.log('VoiceHype: Closing write stream');
                    this.writeStream.end();
                    this.writeStream = null;
                }

                // Process the recording
                await this.processRecording(shouldOptimize || this.startedWithOptimize, fileTrackingLog);
            }
        } catch (error: any) {
            console.error('VoiceHype: Error stopping recording:', error);
            vscode.window.showErrorMessage(getUserFriendlyErrorMessage(error));

            // Clean up resources even if there was an error
            this._isRecording = false;
            this._isPaused = false;
            this.stopTimer(); // Stop the timer
            this.emitStateChange();
            vscode.commands.executeCommand('setContext', 'voicehype.isRecording', false);
            vscode.commands.executeCommand('setContext', 'voicehype.isPaused', false);

            if (this.audioStream) {
                this.audioStream.removeAllListeners();
                this.mic.stopRecording();
                this.audioStream = null;
            }

            if (this.writeStream) {
                this.writeStream.end();
                this.writeStream = null;
            }

            if (this.realtimeConnection) {
                this.realtimeConnection.close();

                // Clear global reference if this was the active connection
                if (RecordingService.activeRealtimeConnection === this.realtimeConnection) {
                    RecordingService.activeRealtimeConnection = null;
                }

                this.realtimeConnection = null;
            }
        }
    }

    private async processRecording(shouldOptimize: boolean = false, fileTrackingLog: any[] = []): Promise<void> {
        // Create a disposable for the status message
        let statusMessageDisposable: vscode.Disposable | undefined;
        let transcriptionResult: any = null;
        let adjustedAudioDuration = 0; // Define adjustedAudioDuration here so it's available outside the progress block
        let optimizationFailed = false;
        let optimizationErrorMessage = '';

        try {
            // Format message based on optimization setting
            const message = shouldOptimize ? 'Transcribing with AI optimization...' : 'Transcribing audio...';
            // Store the disposable to clear the message later
            statusMessageDisposable = vscode.window.setStatusBarMessage(message);

            // Get the translate setting
            const translate = this.configService.getTranslate();
            console.log('VoiceHype: Using translate setting:', translate);

            // Get the custom prompt if optimization is enabled
            const customPrompt = shouldOptimize ? this.configService.getCustomPrompt() : undefined;
            console.log('VoiceHype: Custom prompt for optimization:', customPrompt);

            // Show progress notification

            await vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: 'VoiceHype',
                cancellable: false
            }, async (progress) => {
                progress.report({ message: shouldOptimize ? 'Transcribing and optimizing your recording...' : 'Transcribing your recording...' });

                // Process with the transcription service
                // NOTE: processRecording should NEVER use real-time mode - it's for processing saved audio files
                console.log('VoiceHype: Starting NON-REALTIME transcription with optimize =', shouldOptimize, 'translate =', translate);

                let optimizationFailed = false;
                let optimizationErrorMessage = '';

                try {
                    transcriptionResult = await this.transcriptionService.transcribeAudio(
                        this.recordingPath,
                        shouldOptimize,
                        translate,
                        false, // NEVER use real-time in processRecording - this is for saved audio files only
                        customPrompt
                    );
                } catch (error: any) {
                    // Check if this is an optimization failure (transcription succeeded but optimization failed)
                    const errorMessage = error.message || '';
                    if (errorMessage.includes('optimization') || errorMessage.includes('Optimization')) {
                        console.log('VoiceHype: Optimization failed but transcription succeeded, handling gracefully');
                        optimizationFailed = true;
                        optimizationErrorMessage = errorMessage;

                        // In case of optimization failure, we should still have the transcription
                        // The TranscriptionService should handle this and return the transcription
                        if (!transcriptionResult && this.currentTranscript) {
                            transcriptionResult = {
                                transcription: this.currentTranscript,
                                duration: 0 // Will be calculated below
                            };
                        }
                    } else {
                        // This is a transcription failure, re-throw
                        throw error;
                    }
                }

                // Ensure we have a valid transcription result
                if (!transcriptionResult || !transcriptionResult.transcription) {
                    throw new Error('No transcription result available');
                }

                // Set current transcript for future paste operations
                this.currentTranscript = transcriptionResult.transcription;

                // Get the metadata for the recording operation
                const service = this.configService.getTranscriptionService();
                const model = this.configService.getTranscriptionModel();
                const language = this.configService.getTranscriptionLanguage();

                // Display the result
                console.log('VoiceHype: Transcription result received, length:', transcriptionResult.transcription.length);

                // Clear status bar message as transcription is complete
                if (statusMessageDisposable) {
                    statusMessageDisposable.dispose();
                    statusMessageDisposable = undefined;
                }

                // Get the audio duration using music-metadata via TranscriptionService
                adjustedAudioDuration = await this.transcriptionService.getAudioDuration(this.recordingPath);

                // If duration is 0 (metadata couldn't be read), fall back to the duration from the transcription result
                if (adjustedAudioDuration === 0 && transcriptionResult.duration) {
                    adjustedAudioDuration = transcriptionResult.duration;
                    console.log(`VoiceHype: Using duration from transcription result: ${adjustedAudioDuration.toFixed(3)} seconds`);
                }

                // Log the determined duration
                console.log(`VoiceHype: Final audio duration: ${adjustedAudioDuration.toFixed(3)} seconds`);

                // Add to history with accurate audio duration - always add entry even if optimization fails
                this.historyService.addEntry({
                    timestamp: new Date().getTime(),
                    transcript: transcriptionResult.transcription,
                    optimizedTranscript: shouldOptimize && !optimizationFailed ? transcriptionResult.optimizedText : undefined,
                    service: this.configService.getTranscriptionService(),
                    model: this.configService.getTranscriptionModel(),
                    language: this.configService.getTranscriptionLanguage(),
                    fileReferences: fileTrackingLog,
                    duration: adjustedAudioDuration // Using accurate audio duration from metadata
                });

                // Show appropriate message based on optimization success/failure
                if (optimizationFailed) {
                    vscode.window.showWarningMessage(
                        `Transcription completed but optimization failed. Original transcript will be used.`,
                        { modal: false }
                    );
                }

                // Reset for next recording
                this.lastError = null;
                this.retryCount = 0;

                // Notify VoiceHypePanel to update WebView with new transcription history
                // This happens after we return and the progress notification is dismissed
                setTimeout(() => {
                    try {
                        if (this.voiceHypePanel) {
                            // Directly update the WebView with the latest history
                            this.voiceHypePanel.updateTranscriptionHistory();
                            console.log('VoiceHype: WebView notified of history update');
                        } else {
                            console.log('VoiceHype: VoiceHypePanel not available for history update');
                        }
                    } catch (err) {
                        console.error('VoiceHype: Failed to notify WebView of history update:', err);
                    }
                }, 300);

                // Return from progress - this will complete the progress notification
                return;
            });

            // After progress notification is dismissed, continue with displaying results

            // Format the output based on whether optimization was requested and succeeded
            if (shouldOptimize && this.currentTranscript && transcriptionResult?.optimizedText && !optimizationFailed) {
                console.log('VoiceHype: Using optimized text');

                // Paste the optimized text into the active editor
                await this.pasteTextIntoEditor(transcriptionResult.optimizedText);

                // Show success message with option to use original transcript
                const action = await vscode.window.showInformationMessage(
                    `Transcription ${translate ? 'translated and ' : ''}AI-optimized and pasted (${adjustedAudioDuration.toFixed(1)}s)`,
                    'Use original transcript'
                );

                // If user clicks "Use original transcript", paste the original
                if (action === 'Use original transcript') {
                    await this.pasteTextIntoEditor(transcriptionResult.transcription);
                    vscode.window.showInformationMessage('Original transcript copied to clipboard!');
                }
            } else if (this.currentTranscript && transcriptionResult) {
                // Paste the transcription into the active editor (original transcript)
                await this.pasteTextIntoEditor(this.currentTranscript);

                // Show appropriate message based on optimization outcome
                let actionMessage: string;
                if (shouldOptimize && optimizationFailed) {
                    actionMessage = `Transcription pasted - optimization failed (${adjustedAudioDuration.toFixed(1)}s)`;
                } else {
                    actionMessage = translate ?
                        `Translation pasted (${adjustedAudioDuration.toFixed(1)}s)` :
                        `Transcription pasted (${adjustedAudioDuration.toFixed(1)}s)`;
                }

                const action = await vscode.window.showInformationMessage(
                    actionMessage,
                    ...(shouldOptimize && !optimizationFailed ? ['Optimize transcript'] : [])
                );

                // If user clicks "Optimize transcript", optimize the transcription
                if (action === 'Optimize transcript') {
                    await this.optimizeCurrentTranscript();
                }
            }

            // Indicate that we have a recording available for playback
            this._hasRecording = true;
            vscode.commands.executeCommand('setContext', 'voicehype.hasRecording', true);

        } catch (error: any) {
            console.error('VoiceHype: Processing error:', error);

            // Clear status bar message on error
            if (statusMessageDisposable) {
                statusMessageDisposable.dispose();
                statusMessageDisposable = undefined;
            }

            // Check if this is a retryable error
            const isRetryable = !error.message.includes('Invalid API key') &&
                !error.message.includes('API key not found');

            // Clean up error message
            const cleanErrorMessage = this.getCleanErrorMessage(error.message || 'Unknown transcription error');

            // Show error message with retry option if applicable
            if (isRetryable) {
                const action = await vscode.window.showErrorMessage(
                    `Transcription failed: ${cleanErrorMessage}`,
                    { modal: false },
                    'Retry'
                );

                if (action === 'Retry') {
                    await this.processRecording(shouldOptimize, fileTrackingLog);
                }
            } else {
                await vscode.window.showErrorMessage(
                    `Transcription failed: ${cleanErrorMessage}`,
                    { modal: true }
                );
            }
        }
    }

    private shouldCopyToClipboard(): boolean {
        return vscode.workspace.getConfiguration('voicehype').get('copyResultsToClipboard', true);
    }

    /**
     * Cleans up error messages to remove JSON and provide user-friendly text
     */
    private getCleanErrorMessage(errorMessage: string): string {
        // Import and use the shared error cleaner
        const { cleanErrorMessage } = require('../utils/errorCleaner');
        return cleanErrorMessage(errorMessage);
    }

    /**
     * Pastes text into the active editor at the current cursor position
     */
    private async pasteTextIntoEditor(text: string): Promise<void> {
        try {
            if (this.shouldCopyToClipboard()) {
                // Write to clipboard
                await vscode.env.clipboard.writeText(text);
            }

            // Use the editor's paste action
            await vscode.commands.executeCommand('editor.action.clipboardPasteAction');
        } catch (error) {
            console.error('VoiceHype: Error pasting text:', error);
            vscode.window.showInformationMessage('Failed to paste text. Please try pasting manually with Ctrl+V');
        }
    }

    /**
     * Optimizes the current transcript and pastes it into the editor
     */
    private async optimizeCurrentTranscript(): Promise<void> {
        if (!this.currentTranscript) {
            vscode.window.showWarningMessage('No transcript available to optimize');
            return;
        }

        // Create a disposable for the status message
        let statusMessageDisposable: vscode.Disposable | undefined;

        try {
            // Store the original transcript for potential reversion
            const originalTranscript = this.currentTranscript;
            let optimizedText: string = '';

            // Set the status bar message and store its disposable
            statusMessageDisposable = vscode.window.setStatusBarMessage('Optimizing transcript...');

            // Show progress notification for optimization
            await vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: 'VoiceHype',
                cancellable: false
            }, async (progress) => {
                progress.report({ message: 'Optimizing transcript...' });

                // Optimize the transcript
                // Get the custom prompt if available
                const customPrompt = this.configService.getCustomPrompt();
                console.log('VoiceHype: Using custom prompt for optimization:', customPrompt);

                // Pass the custom prompt to the optimization method
                optimizedText = await this.transcriptionService.optimizeText(this.currentTranscript, customPrompt);

                // Clear status bar message as optimization is complete
                if (statusMessageDisposable) {
                    statusMessageDisposable.dispose();
                    statusMessageDisposable = undefined;
                }

                // Update history entry with optimized text
                this.historyService.updateLastEntryWithOptimization(optimizedText);

                // Also update the webview panel with the new history
                setTimeout(() => {
                    try {
                        if (this.voiceHypePanel) {
                            // Directly update the WebView with the latest history
                            this.voiceHypePanel.updateTranscriptionHistory();
                            console.log('VoiceHype: WebView notified of history update after optimization');
                        } else {
                            console.log('VoiceHype: VoiceHypePanel not available for history update after optimization');
                        }
                    } catch (err) {
                        console.error('VoiceHype: Failed to notify WebView of history update after optimization:', err);
                    }
                }, 100);

                // Return to complete the progress notification
                return;
            });

            // After progress notification is dismissed, continue with displaying results

            // Paste the optimized text into the active editor (this will handle clipboard operations)
            await this.pasteTextIntoEditor(optimizedText);

            // Show success message with option to revert
            const action = await vscode.window.showInformationMessage(
                'Transcript optimized and pasted',
                'Revert to original'
            );

            // If user clicks "Revert to original", paste the original transcript
            if (action === 'Revert to original') {
                await this.pasteTextIntoEditor(originalTranscript);
                vscode.window.showInformationMessage('Reverted to original transcript');
            }
        } catch (error: any) {
            console.error('VoiceHype: Optimization error:', error);

            // Clear status bar message on error
            if (statusMessageDisposable) {
                statusMessageDisposable.dispose();
                statusMessageDisposable = undefined;
            }

            // Clean up error message to avoid showing raw JSON
            const cleanErrorMessage = this.getCleanErrorMessage(error.message || 'Unknown optimization error');

            // Check if this is a retryable error
            const isRetryable = !cleanErrorMessage.includes('Invalid API key') &&
                !cleanErrorMessage.includes('API key not found') &&
                !cleanErrorMessage.includes('authentication failed');

            if (isRetryable) {
                const action = await vscode.window.showErrorMessage(
                    `Optimization failed: ${cleanErrorMessage}`,
                    { modal: false },
                    'Retry Optimization',
                    'Use Original Transcript'
                );

                if (action === 'Retry Optimization') {
                    // Retry the optimization
                    await this.optimizeCurrentTranscript();
                } else if (action === 'Use Original Transcript') {
                    // Paste original transcript
                    await this.pasteTextIntoEditor(this.currentTranscript || '');
                    vscode.window.showInformationMessage('Using original transcript');
                }
            } else {
                // Non-retryable error
                await vscode.window.showErrorMessage(
                    `Optimization failed: ${cleanErrorMessage}`,
                    { modal: true }
                );

                // Still offer to use original transcript
                const useOriginal = await vscode.window.showInformationMessage(
                    'Would you like to use the original transcript instead?',
                    'Use Original Transcript'
                );

                if (useOriginal === 'Use Original Transcript') {
                    await this.pasteTextIntoEditor(this.currentTranscript || '');
                }
            }
        }
    }

    playLastRecording(): void {
        if (!this._hasRecording || !fs.existsSync(this.recordingPath)) {
            vscode.window.showErrorMessage('No recording available to play.');
            this._hasRecording = false;
            vscode.commands.executeCommand('setContext', 'voicehype.hasRecording', false);
            return;
        }

        AudioPlayerPanel.show(this.recordingPath, this.context);
    }

    /**
     * Pastes the original (non-optimized) transcript into the editor
     */
    async pasteOriginalTranscript(): Promise<void> {
        if (!this.currentTranscript) {
            vscode.window.showWarningMessage('No original transcript available');
            return;
        }

        try {
            // Paste the original transcript into the active editor
            await this.pasteTextIntoEditor(this.currentTranscript);
            vscode.window.showInformationMessage('Original transcript copied to clipboard!');
        } catch (error: any) {
            console.error('VoiceHype: Error pasting original transcript:', error);
            vscode.window.showErrorMessage(`Failed to paste original transcript: ${error.message}`);
        }
    }

    // Add a method to show real-time transcription updates in a VS Code snackbar
    private showRealtimeTranscriptionUpdates(): void {
        // Skip if no connection
        if (!this.realtimeConnection) {
            return;
        }

        console.log('VoiceHype: Setting up real-time transcription UI updates');

        // Create a single, simple snackbar message
        let snackbarDisposable = vscode.window.setStatusBarMessage('$(mic) Ready for transcription...');
        this.disposables.push(snackbarDisposable);

        // Track information bar notifications
        let lastInfoBarTime = 0;
        const INFO_BAR_THROTTLE = 3000; // Show info bar at most every 3 seconds

        // Listen for transcription updates and update the snackbar in real-time
        this.realtimeConnection.onTranscriptionUpdate((text: string, isFinal: boolean) => {
            if (!text || text.trim().length === 0) {
                return;
            }

            // Update the snackbar with the incoming text
            if (snackbarDisposable) {
                snackbarDisposable.dispose();
            }

            // Create a cleaner display of the text (trimmed, truncated if needed)
            const displayText = text.length > 80 ? text.substring(0, 77) + '...' : text;

            // Use different icons and styling based on whether it's final or partial
            let icon: string;
            let prefix: string;

            if (isFinal) {
                icon = '$(check)';
                prefix = 'Final';
            } else {
                icon = '$(comment-discussion)';
                prefix = 'Live';
            }

            // Create a simple snackbar showing what's being transcribed
            snackbarDisposable = vscode.window.setStatusBarMessage(`${icon} ${prefix}: ${displayText}`);

            // Send real-time transcript updates to webview
            try {
                if (this.voiceHypePanel) {
                    if (isFinal) {
                        this.voiceHypePanel.sendMessageToWebview({
                            command: 'realtimeFinalTranscript',
                            text: text,
                            timestamp: Date.now()
                        });
                        console.log('VoiceHype: Sent final transcript to webview:', text.substring(0, 50) + '...');
                    } else {
                        this.voiceHypePanel.sendMessageToWebview({
                            command: 'realtimePartialTranscript',
                            text: text,
                            timestamp: Date.now()
                        });
                        console.log('VoiceHype: Sent partial transcript to webview:', text.substring(0, 30) + '...');
                    }
                }
            } catch (error) {
                console.error('VoiceHype: Error sending real-time transcript to webview:', error);
            }

            // Show information bar notifications for significant transcripts
            const currentTime = Date.now();
            if (isFinal && text.trim().length > 10 && (currentTime - lastInfoBarTime) > INFO_BAR_THROTTLE) {
                lastInfoBarTime = currentTime;
                const shortText = text.length > 60 ? text.substring(0, 57) + '...' : text;
                vscode.window.showInformationMessage(`🎤 Transcribed: "${shortText}"`, { modal: false });
                console.log('VoiceHype: Showed information bar for final transcript');
            }
        });

        // Also listen for status changes to update the snackbar accordingly
        this.realtimeConnection.onStatusChange((status: TranscriptionStatus, message?: string) => {
            if (status === TranscriptionStatus.ConnectingWebSocket) {
                if (snackbarDisposable) {
                    snackbarDisposable.dispose();
                }
                snackbarDisposable = vscode.window.setStatusBarMessage('$(sync~spin) Connecting to transcription service...');

                // Send connection status to webview
                this.sendConnectionStatusToWebview('connecting', 'Connecting to transcription service...');
            } else if (status === TranscriptionStatus.WebSocketConnected) {
                if (snackbarDisposable) {
                    snackbarDisposable.dispose();
                }
                snackbarDisposable = vscode.window.setStatusBarMessage('$(plug) Connected - Start speaking!');

                // Send connection status to webview
                this.sendConnectionStatusToWebview('connected', 'Connected - Start speaking!');
            } else if (status === TranscriptionStatus.StreamingAudio && message?.startsWith('Partial:')) {
                // Handle partial transcript status updates
                if (snackbarDisposable) {
                    snackbarDisposable.dispose();
                }
                const partialText = message.replace('Partial: ', '');
                snackbarDisposable = vscode.window.setStatusBarMessage(`$(comment-discussion) Live: ${partialText}`);

                // Send transcribing status to webview
                this.sendConnectionStatusToWebview('transcribing', 'Transcribing...');
            } else if (status === TranscriptionStatus.TranscriptionComplete) {
                if (snackbarDisposable) {
                    snackbarDisposable.dispose();
                }
                snackbarDisposable = vscode.window.setStatusBarMessage('$(check) Transcription complete', 5000);

                // Send completion status to webview
                this.sendConnectionStatusToWebview('completed', 'Transcription complete');
            } else if (status === TranscriptionStatus.Error) {
                if (snackbarDisposable) {
                    snackbarDisposable.dispose();
                }
                const errorMessage = message || 'Connection error';
                snackbarDisposable = vscode.window.setStatusBarMessage(`$(error) ${errorMessage}`, 8000);

                // Send error status to webview
                this.sendConnectionStatusToWebview('error', errorMessage);
            }
        });

        // Set up a clean-up function to dispose the snackbar when done
        const cleanup = () => {
            if (snackbarDisposable) {
                snackbarDisposable.dispose();
            }
        };

        // Add cleanup to disposables
        this.disposables.push({ dispose: cleanup });
    }

    // Add event listener method
    onStateChange(listener: (isRecording: boolean, isPaused: boolean, elapsedTime: number) => void): void {
        this.stateChangeEmitter.on('stateChange', listener);
    }

    // Add method to emit state changes
    private emitStateChange(): void {
        console.log(`VoiceHype [DEBUG]: Emitting state change: isRecording=${this._isRecording}, isPaused=${this._isPaused}, elapsedTime=${this.elapsedTime}ms`);
        console.log('VoiceHype [DEBUG]: State change emitted from:', new Error().stack);
        this.stateChangeEmitter.emit('stateChange', this._isRecording, this._isPaused, this.elapsedTime);
    }

    private startTimer(): void {
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
        }
        this.elapsedTime = 0;
        this.timerInterval = setInterval(() => {
            if (!this._isPaused) {
                this.elapsedTime++;
                // Only emit minimal state changes for time updates to avoid triggering heavy operations
                // The webview can get time updates without triggering the full state change chain
                if (this.stateChangeEmitter.listenerCount('stateChange') > 0) {
                    // Emit a time-only update that still includes all state for UI consistency,
                    // but listeners can differentiate based on the call frequency
                    this.stateChangeEmitter.emit('stateChange', this._isRecording, this._isPaused, this.elapsedTime);
                }
            }
        }, 1000);
    }

    private stopTimer(): void {
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
            this.timerInterval = null;
        }
        this.elapsedTime = 0;
        this.emitStateChange();
    }

    private pauseTimer(): void {
        // No need to clear interval, we just check isPaused in the timer
        this.emitStateChange();
    }

    private resumeTimer(): void {
        this.emitStateChange();
    }

    dispose(): void {
        this.stateChangeEmitter.removeAllListeners();
        this.disposables.forEach(d => d.dispose());

        // Make sure to clean up any running SoX processes when the extension is deactivated
        if (process.platform === 'win32') {
            try {
                console.log('VoiceHype: Cleaning up any remaining sox.exe processes on extension deactivation');
                const { execSync } = require('child_process');
                execSync('taskkill /F /IM sox.exe 2>nul', { windowsHide: true, stdio: 'ignore' });
                console.log('VoiceHype: Successfully cleaned up sox.exe processes');
            } catch (error) {
                // Ignore errors here as there might not be any sox processes running
                console.log('VoiceHype: No sox.exe processes to clean up');
            }
        } else if (process.platform === 'darwin' || process.platform === 'linux') {
            try {
                console.log('VoiceHype: Cleaning up any remaining sox processes on extension deactivation');
                const { execSync } = require('child_process');
                execSync('pkill -9 sox 2>/dev/null || true', { stdio: 'ignore' });
                console.log('VoiceHype: Successfully cleaned up sox processes');
            } catch (error) {
                // Ignore errors here as there might not be any sox processes running
                console.log('VoiceHype: No sox processes to clean up');
            }
        }

        if (this.timerInterval) {
            clearInterval(this.timerInterval);
        }
    }

    /**
     * Get a list of available audio devices
     * @param forceRefresh Whether to force a refresh of the device list
     */
    async getAvailableDevices(forceRefresh: boolean = false): Promise<Array<{ id: string; name: string }>> {
        // Use the AudioDeviceManager to get the devices
        const { AudioDeviceManager } = await import('../utils/audioDeviceManager');
        const deviceManager = AudioDeviceManager.getInstance();
        return deviceManager.getDevices(forceRefresh);
    }

    /**
     * Get the status bar service for external updates
     */
    getStatusBarService(): IStatusBarService | null {
        return this.statusBarService;
    }

    // Add this method to get the current elapsed time
    public getElapsedTime(): number {
        return this.elapsedTime;
    }

    /**
     * Cancel the current recording without saving it
     * For real-time mode, shows informative message instead of cancelling
     */
    public async cancelRecording(): Promise<void> {
        if (!this.isRecording()) {
            return;
        }

        // Check if we're in real-time mode
        if (this.useRealtime && this.realtimeConnection) {
            // Show informative message for real-time mode
            const action = await vscode.window.showInformationMessage(
                'Real-time transcription cannot be cancelled. You can either stop the session or pause it (maximum 1 minute).',
                'Stop Session',
                'Pause Recording'
            );

            if (action === 'Stop Session') {
                // Stop the recording instead of cancelling
                await this.stopRecording(this.startedWithOptimize);
            } else if (action === 'Pause Recording') {
                // Pause the recording
                await this.pauseRecording();
            }

            return;
        }

        try {
            console.log('VoiceHype: Cancelling recording...');

            // Stop the microphone
            if (this.audioStream) {
                console.log('VoiceHype: Stopping audio stream');
                this.audioStream.removeAllListeners();
                this.mic.stopRecording();
                this.audioStream = null;
            }

            // Close the write stream if it exists
            if (this.writeStream) {
                console.log('VoiceHype: Closing write stream');
                this.writeStream.end();
                this.writeStream = null;
            }

            // Close realtime connection if it exists - with timeout to prevent hanging
            if (this.realtimeConnection) {
                console.log('VoiceHype: Closing realtime connection during cancellation');
                try {
                    // Set a timeout to prevent hanging on close
                    const closePromise = new Promise<void>((resolve) => {
                        this.realtimeConnection!.close();
                        // Give it a moment to close gracefully
                        setTimeout(resolve, 1000);
                    });

                    await closePromise;

                    // Clear global reference if this was the active connection
                    if (RecordingService.activeRealtimeConnection === this.realtimeConnection) {
                        RecordingService.activeRealtimeConnection = null;
                    }

                    this.realtimeConnection = null;
                    console.log('VoiceHype: Realtime connection closed successfully');
                } catch (error) {
                    console.error('VoiceHype: Error closing realtime connection:', error);

                    // Clear references even if there was an error
                    if (RecordingService.activeRealtimeConnection === this.realtimeConnection) {
                        RecordingService.activeRealtimeConnection = null;
                    }

                    this.realtimeConnection = null;
                }
            }

            // Reset recording state
            this._isRecording = false;
            this._isPaused = false;
            this.elapsedTime = 0;
            this.currentTranscript = '';

            // Clear any ongoing timers
            if (this.timerInterval) {
                clearInterval(this.timerInterval);
                this.timerInterval = null;
            }

            // Update VS Code context
            vscode.commands.executeCommand('setContext', 'voicehype.isRecording', false);
            vscode.commands.executeCommand('setContext', 'voicehype.isPaused', false);

            // Update status bar
            this.statusBarService.updateStatusBarItems(false, this.startedWithOptimize, false);

            // Stop file tracking
            this.fileTrackingService.stopTracking();

            // Emit state change to update any listeners (including the panel)
            this.emitStateChange();

            // Show notification
            vscode.window.showInformationMessage('Recording cancelled');

            console.log('VoiceHype: Recording successfully cancelled');

        } catch (error) {
            console.error('Error cancelling recording:', error);
            vscode.window.showErrorMessage('Failed to cancel recording');

            // Make sure we reset the state even if there's an error
            this._isRecording = false;
            this._isPaused = false;
            vscode.commands.executeCommand('setContext', 'voicehype.isRecording', false);
            vscode.commands.executeCommand('setContext', 'voicehype.isPaused', false);
            this.statusBarService.updateStatusBarItems(false, false, false);
        }
    }

    /**
     * Handle grace period for real-time transcription
     * Collects additional transcripts for 8.5 seconds after stop signal
     */
    private async handleRealtimeGracePeriod(immediateTranscript: string): Promise<{
        finalTranscript: string;
        additionalContent: string | null;
    }> {
        console.log('VoiceHype: Starting grace period collection...');

        const gracePeriodMs = 8500; // 8.5 seconds
        const startTime = Date.now();
        let additionalTranscript = '';
        let hasReceivedAdditional = false;

        // Send grace period start status to webview
        this.sendGracePeriodStatusToWebview(true, Math.ceil(gracePeriodMs / 1000));

        return new Promise((resolve) => {
            // Set up countdown timer for UI updates
            const countdownInterval = setInterval(() => {
                const elapsed = Date.now() - startTime;
                const remaining = Math.max(0, Math.ceil((gracePeriodMs - elapsed) / 1000));

                if (remaining > 0) {
                    this.sendGracePeriodStatusToWebview(true, remaining);
                } else {
                    clearInterval(countdownInterval);
                }
            }, 1000);

            // Set up listener for additional transcripts during grace period
            const gracePeriodHandler = (text: string, isFinal: boolean) => {
                const currentTime = Date.now();
                const elapsed = currentTime - startTime;

                console.log(`VoiceHype: Grace period update - elapsed: ${elapsed}ms, isFinal: ${isFinal}, text length: ${text.length}`);

                // Only collect additional content that wasn't in the immediate transcript
                if (text.length > immediateTranscript.length) {
                    const newContent = text.substring(immediateTranscript.length);
                    if (newContent.trim()) {
                        additionalTranscript = newContent;
                        hasReceivedAdditional = true;
                        console.log('VoiceHype: Additional content received during grace period:', newContent.substring(0, 50));
                    }
                }
            };

            // Set up status change listener for early completion
            const statusChangeHandler = (status: TranscriptionStatus) => {
                if (status === TranscriptionStatus.TranscriptionComplete ||
                    status === TranscriptionStatus.AllChunksProcessed) {
                    console.log('VoiceHype: Transcription completed during grace period');
                }
            };

            // Add listeners if connection is still available
            if (this.realtimeConnection) {
                this.realtimeConnection.onTranscriptionUpdate(gracePeriodHandler);
                this.realtimeConnection.onStatusChange(statusChangeHandler);
            }

            // Set timeout for grace period
            setTimeout(() => {
                clearInterval(countdownInterval);
                console.log(`VoiceHype: Grace period completed after ${gracePeriodMs}ms`);

                // Send grace period end status to webview
                this.sendGracePeriodStatusToWebview(false, 0);

                const finalTranscript = immediateTranscript + additionalTranscript;

                console.log('VoiceHype: Grace period results:');
                console.log(`- Immediate transcript length: ${immediateTranscript.length}`);
                console.log(`- Additional content length: ${additionalTranscript.length}`);
                console.log(`- Final transcript length: ${finalTranscript.length}`);
                console.log(`- Has additional content: ${hasReceivedAdditional}`);

                resolve({
                    finalTranscript,
                    additionalContent: hasReceivedAdditional ? additionalTranscript : null
                });
            }, gracePeriodMs);
        });
    }

    /**
     * Send grace period status updates to webview
     */
    private sendGracePeriodStatusToWebview(isActive: boolean, remainingSeconds: number): void {
        try {
            if (this.voiceHypePanel) {
                this.voiceHypePanel.sendMessageToWebview({
                    command: 'gracePeriodStatus',
                    isActive: isActive,
                    remainingSeconds: remainingSeconds,
                    timestamp: Date.now()
                });
                console.log(`VoiceHype: Sent grace period status to webview: active=${isActive}, remaining=${remainingSeconds}s`);
            }
        } catch (error) {
            console.error('VoiceHype: Error sending grace period status to webview:', error);
        }
    }
}
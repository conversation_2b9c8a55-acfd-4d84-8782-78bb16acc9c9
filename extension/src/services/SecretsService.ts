import * as vscode from 'vscode';

/**
 * Service for securely storing and retrieving sensitive information
 * using VS Code's SecretStorage API.
 */
export class SecretsService {
    private readonly secretStorage: vscode.SecretStorage;
    private static readonly API_KEY_SECRET_KEY = 'voicehype.apiKey';
    private static readonly SUPABASE_SESSION_SECRET_KEY = 'voicehype.supabaseSession';

    constructor(context: vscode.ExtensionContext) {
        this.secretStorage = context.secrets;
    }

    /**
     * Stores the API key securely
     * @param apiKey The API key to store
     */
    public async storeApiKey(apiKey: string): Promise<void> {
        try {
            await this.secretStorage.store(SecretsService.API_KEY_SECRET_KEY, apiKey);
            console.log('API key stored securely');
        } catch (error) {
            console.error('Failed to store API key securely:', error);
            throw error;
        }
    }

    /**
     * Retrieves the stored API key
     * @returns The API key if found, undefined otherwise
     */
    public async getApiKey(): Promise<string | undefined> {
        try {
            return await this.secretStorage.get(SecretsService.API_KEY_SECRET_KEY);
        } catch (error) {
            console.error('Failed to retrieve API key from secure storage:', error);
            return undefined;
        }
    }

    /**
     * Deletes the stored API key
     */
    public async deleteApiKey(): Promise<void> {
        try {
            await this.secretStorage.delete(SecretsService.API_KEY_SECRET_KEY);
            console.log('API key deleted from secure storage');
        } catch (error) {
            console.error('Failed to delete API key from secure storage:', error);
            throw error;
        }
    }

    /**
     * Stores the Supabase session securely
     * @param session The Supabase session string to store (JSON)
     */
    public async storeSupabaseSession(session: string): Promise<void> {
        try {
            await this.secretStorage.store(SecretsService.SUPABASE_SESSION_SECRET_KEY, session);
            console.log('Supabase session stored securely');
        } catch (error) {
            console.error('Failed to store Supabase session securely:', error);
            throw error;
        }
    }

    /**
     * Retrieves the stored Supabase session
     * @returns The session string if found, undefined otherwise
     */
    public async getSupabaseSession(): Promise<string | undefined> {
        try {
            return await this.secretStorage.get(SecretsService.SUPABASE_SESSION_SECRET_KEY);
        } catch (error) {
            console.error('Failed to retrieve Supabase session from secure storage:', error);
            return undefined;
        }
    }

    /**
     * Deletes the stored Supabase session
     */
    public async deleteSupabaseSession(): Promise<void> {
        try {
            await this.secretStorage.delete(SecretsService.SUPABASE_SESSION_SECRET_KEY);
            console.log('Supabase session deleted from secure storage');
        } catch (error) {
            console.error('Failed to delete Supabase session from secure storage:', error);
            throw error;
        }
    }

    /**
     * Registers a listener for secret storage changes
     * @param listener The callback function to be called when the API key changes
     * @returns A disposable that can be used to unregister the listener
     */
    public onApiKeyChanged(listener: (key: string) => void): vscode.Disposable {
        return this.secretStorage.onDidChange(e => {
            if (e.key === SecretsService.API_KEY_SECRET_KEY) {
                listener(e.key);
            }
        });
    }
}

import * as vscode from 'vscode';
import { IHistoryService, RecordingHistoryEntry, FileTrackingEntry, ITranscriptionService } from '../models/interfaces';
import * as path from 'path';

export class HistoryTreeItem extends vscode.TreeItem {
    constructor(
        public readonly label: string,
        public readonly collapsibleState: vscode.TreeItemCollapsibleState,
        public readonly index: number,
        public readonly type: 'recording' | 'transcript' | 'optimized' | 'refs' | 'ref' = 'recording',
        public readonly timestamp?: number,
        public readonly data?: any
    ) {
        super(label, collapsibleState);

        switch (type) {
            case 'recording':
                this.iconPath = new vscode.ThemeIcon('history');
                this.contextValue = 'recording';
                
                // Format timestamp for better display
                if (timestamp) {
                    const date = new Date(timestamp);
                    const now = new Date();
                    const isToday = date.getDate() === now.getDate() && 
                                  date.getMonth() === now.getMonth() && 
                                  date.getFullYear() === now.getFullYear();
                    
                    // Show time for today's recordings, or date for older ones
                    let formattedTime = '';
                    if (isToday) {
                        formattedTime = date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                        this.description = `Today, ${formattedTime}`;
                    } else {
                        formattedTime = date.toLocaleDateString([], { 
                            month: 'short', 
                            day: 'numeric',
                            hour: '2-digit', 
                            minute: '2-digit'
                        });
                        this.description = formattedTime;
                    }
                }
                break;
            case 'transcript':
                this.iconPath = new vscode.ThemeIcon('file-text');
                this.contextValue = 'transcript';
                break;
            case 'optimized':
                this.iconPath = new vscode.ThemeIcon('sparkle');
                this.contextValue = 'optimized';
                break;
            case 'refs':
                this.iconPath = new vscode.ThemeIcon('references');
                this.contextValue = 'refs';
                break;
            case 'ref':
                this.iconPath = new vscode.ThemeIcon('file-code');
                this.contextValue = 'ref';
                // For file references, display the file name better
                if (data && data.filePath) {
                    this.tooltip = data.filePath;
                    // Show just the base filename
                    this.description = path.basename(data.filePath);
                }
                break;
        }
    }
}

export class HistoryService implements IHistoryService, vscode.TreeDataProvider<HistoryTreeItem> {
    private _onDidChangeTreeData: vscode.EventEmitter<HistoryTreeItem | undefined | null | void> = new vscode.EventEmitter<HistoryTreeItem | undefined | null | void>();
    readonly onDidChangeTreeData: vscode.Event<HistoryTreeItem | undefined | null | void> = this._onDidChangeTreeData.event;

    private history: RecordingHistoryEntry[] = [];
    private static readonly MAX_HISTORY_ENTRIES = 50;

    constructor(
        private context: vscode.ExtensionContext, 
        private transcriptionService?: ITranscriptionService
    ) {
        // Load history from storage
        this.history = context.globalState.get('voicehype.history', []);
        
        // Ensure history doesn't exceed max entries on load
        if (this.history.length > HistoryService.MAX_HISTORY_ENTRIES) {
            this.history = this.history.slice(-HistoryService.MAX_HISTORY_ENTRIES);
            this.context.globalState.update('voicehype.history', this.history);
        }
    }

    getTreeItem(element: HistoryTreeItem): vscode.TreeItem {
        return element;
    }

    getChildren(element?: HistoryTreeItem): Thenable<HistoryTreeItem[]> {
        if (!element) {
            // Root level - show recordings
            return Promise.resolve(
                this.history.map((entry, index) => {
                    // Create a truncated preview of the transcript
                    const previewText = entry.transcript.slice(0, 30).replace(/\n/g, ' ');
                    const previewSuffix = entry.transcript.length > 30 ? '...' : '';
                    const label = `${previewText}${previewSuffix}`;
                    
                    const item = new HistoryTreeItem(
                        label,
                        vscode.TreeItemCollapsibleState.Collapsed,
                        index,
                        'recording',
                        entry.timestamp
                    );
                    
                    item.tooltip = 'Click to expand for details';
                    return item;
                }).reverse() // Show newest first
            );
        } else if (element.type === 'recording') {
            const entry = this.history[element.index];
            const items: HistoryTreeItem[] = [];

            // Show transcript
            const transcriptItem = new HistoryTreeItem(
                'Original Transcript',
                vscode.TreeItemCollapsibleState.None,
                element.index,
                'transcript'
            );
            transcriptItem.tooltip = entry.transcript;
            transcriptItem.description = `${entry.transcript.length} chars`;
            transcriptItem.command = {
                command: 'voicehype.pasteText',
                title: 'Paste Text',
                arguments: [entry.transcript]
            };
            items.push(transcriptItem);
            
            // Show optimized transcript if available
            if (entry.optimizedTranscript) {
                const optimizedItem = new HistoryTreeItem(
                    'Optimized Transcript',
                    vscode.TreeItemCollapsibleState.None,
                    element.index,
                    'optimized'
                );
                optimizedItem.tooltip = entry.optimizedTranscript;
                optimizedItem.description = `${entry.optimizedTranscript.length} chars`;
                optimizedItem.command = {
                    command: 'voicehype.pasteText',
                    title: 'Paste Text',
                    arguments: [entry.optimizedTranscript]
                };
                items.push(optimizedItem);
            }

            // Add file references if any exist
            if (entry.fileReferences.length > 0) {
                const refsItem = new HistoryTreeItem(
                    'File References',
                    vscode.TreeItemCollapsibleState.Collapsed,
                    element.index,
                    'refs'
                );
                refsItem.description = `${entry.fileReferences.length} references`;
                items.push(refsItem);
            }

            return Promise.resolve(items);
        } else if (element.type === 'refs') {
            // Show individual file references
            const entry = this.history[element.index];
            return Promise.resolve(
                entry.fileReferences.map((ref, _) => {
                    let label = `@${ref.fileName}`;
                    if (ref.action === 'selected' && ref.selection) {
                        label += `:${ref.selection.startLine}`;
                        if (ref.selection.endLine !== ref.selection.startLine) {
                            label += `-${ref.selection.endLine}`;
                        }
                    }
                    const item = new HistoryTreeItem(
                        label,
                        vscode.TreeItemCollapsibleState.None,
                        element.index,
                        'ref',
                        undefined,
                        { filePath: ref.filePath }
                    );
                    item.tooltip = `Action: ${ref.action}`;
                    item.command = {
                        command: 'voicehype.openFileReference',
                        title: 'Open File Reference',
                        arguments: [ref]
                    };
                    return item;
                })
            );
        }

        return Promise.resolve([]);
    }

    addEntry(entry: RecordingHistoryEntry): void {
        this.history.push(entry);
        
        // Remove oldest entries if exceeding limit
        if (this.history.length > HistoryService.MAX_HISTORY_ENTRIES) {
            this.history = this.history.slice(-HistoryService.MAX_HISTORY_ENTRIES);
        }
        
        this.context.globalState.update('voicehype.history', this.history);
        this._onDidChangeTreeData.fire();
    }

    clearHistory(): void {
        this.history = [];
        this.context.globalState.update('voicehype.history', this.history);
        this._onDidChangeTreeData.fire();
    }

    getHistory(): RecordingHistoryEntry[] {
        return [...this.history];
    }

    updateLastEntryWithOptimization(optimizedText: string): void {
        if (this.history.length > 0) {
            this.history[this.history.length - 1].optimizedTranscript = optimizedText;
            this.context.globalState.update('voicehype.history', this.history);
            this._onDidChangeTreeData.fire();
        }
    }

    updateEntryWithOptimization(index: number, optimizedText: string): void {
        if (index >= 0 && index < this.history.length) {
            this.history[index].optimizedTranscript = optimizedText;
            this.context.globalState.update('voicehype.history', this.history);
            this._onDidChangeTreeData.fire();
        }
    }

    // Implementation of required interface methods
    getPresets(): { name: string, prompt: string }[] {
        return [];
    }

    addPreset(): void {
        // No implementation needed
    }

    updatePreset(): void {
        // No implementation needed
    }

    deletePreset(): void {
        // No implementation needed
    }

    showPresetSelector(): Promise<void> {
        return Promise.resolve();
    }

    createNewPreset(): Promise<void> {
        return Promise.resolve();
    }

    registerCommands(): vscode.Disposable[] {
        return [];
    }

    dispose(): void {
        // Nothing to dispose
    }
} 
import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { getNonce } from '../utils/getNonce';
import { ConfigurationService } from './ConfigurationService';
import { RecordingService } from './RecordingService';
import { TranscriptionService } from './TranscriptionService';
import { HistoryService } from './HistoryService';
import { RobustThemeDetector } from './RobustThemeDetector';
import { IRecordingService } from '../models/interfaces';
import { IConfigurationService } from '../models/interfaces';
import { IHistoryService } from '../models/interfaces';
import { VoiceHypeExtension } from '../VoiceHypeExtension';

interface PromptMode {
    id: string;
    name: string;
    prompt: string;
    isCustom?: boolean;
}

export class VoiceHypePanel implements vscode.WebviewViewProvider {
    public static readonly viewType = 'voicehype.controlPanel';
    public _view?: vscode.WebviewView;

    // Track when options have just been updated to avoid feedback loops
    private _recentlyUpdatedOptions: Set<string> = new Set();
    private _isProcessingCommand = false;
    private _hasShownCustomPromptError = false;
    private _disposables: vscode.Disposable[] = [];
    private _previousSampleRate: number = 44100; // Store previous sample rate
    private _promptModes: PromptMode[] = []; // Store available prompt modes
    private _themeDetector: RobustThemeDetector;

    // Default prompt modes
    private _defaultPromptModes: PromptMode[] = [
        {
            id: 'default',
            name: 'Default',
            prompt: '',
            isCustom: false
        },
        {
            id: 'code',
            name: 'Code',
            prompt: 'Format as code with proper indentation and syntax.',
            isCustom: false
        },
        {
            id: 'markdown',
            name: 'Markdown',
            prompt: 'Format as markdown with headings, lists, and code blocks as appropriate.',
            isCustom: false
        }
    ];

    constructor(
        private readonly _extensionUri: vscode.Uri,
        private configurationService: ConfigurationService,
        private recordingService: RecordingService,
        private transcriptionService: TranscriptionService,
        private historyService: HistoryService,
        private _recordingService: IRecordingService,
        private _configService: IConfigurationService,
        private _historyService: IHistoryService,
        private voiceHypeExtension?: VoiceHypeExtension
    ) {
        // Initialize the robust theme detector
        this._themeDetector = new RobustThemeDetector();

        // Listen for configuration changes from other sources like quick settings menu
        this._disposables.push(
            this.configurationService.onDidChangeConfiguration(key => {
                console.log(`VoiceHypePanelService: Configuration changed - ${key}`);
                // Update the webview when settings change from outside
                this._forceSendConfiguration(true).catch(error => {
                    console.error('Error force-sending configuration:', error);
                });
            })
        );

        // Listen for recording state changes
        this.recordingService.onStateChange((_isRecording: boolean, _isPaused: boolean) => {
            this._updateRecordingState();
        });

        // Register the recording state listener
        this._registerRecordingStateListener();
        // Load custom prompt modes from configuration
        this._loadPromptModesFromConfig();
    }

    /**
     * Load custom prompt modes from VS Code configuration
     * This ensures custom modes persist across VS Code sessions
     */
    private _loadPromptModesFromConfig(): void {
        try {
            // Get custom modes from configuration
            const promptModesConfig = this.configurationService.getPromptModes();
            const customModes = promptModesConfig.custom || [];
            const activeMode = promptModesConfig.active || 'clean-up';

            console.log(`VoiceHype: Loading ${customModes.length} custom prompt modes from configuration`);
            console.log(`VoiceHype: Active prompt mode is "${activeMode}"`);

            // Initialize with default modes
            this._promptModes = [...this._defaultPromptModes];

            // Add custom modes from configuration
            if (customModes && customModes.length > 0) {
                // Ensure all custom modes have the isCustom flag set
                const validCustomModes = customModes.map(mode => ({
                    ...mode,
                    isCustom: true
                }));

                // Log details of loaded custom modes for debugging
                validCustomModes.forEach(mode => {
                    console.log(`VoiceHype: Loaded custom mode "${mode.name}" with ID "${mode.id}"`);
                });

                // Add to prompt modes
                this._promptModes = [...this._promptModes, ...validCustomModes];
                console.log(`VoiceHype: Successfully loaded ${validCustomModes.length} custom prompt modes`);
            } else {
                console.log('VoiceHype: No custom prompt modes found in configuration');
            }
        } catch (error) {
            console.error('VoiceHype: Error loading custom prompt modes from configuration:', error);
        }
    }

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        _context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        this._view = webviewView;

        // Create a specific resource root for the webview build directory
        const webviewBuildPath = vscode.Uri.joinPath(this._extensionUri, 'webview-ui', 'build');

        // Get the workspace root directory
        const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri;

        webviewView.webview.options = {
            // Enable JavaScript in the webview
            enableScripts: true,
            // Restrict the webview to only load resources from the extension's directory and workspace root
            localResourceRoots: [
                this._extensionUri,
                webviewBuildPath,
                workspaceRoot // Allow access to workspace root for logo
            ].filter(Boolean) as vscode.Uri[] // Filter out undefined values
        };

        // Set the HTML content
        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

        // Send initial configuration immediately (async)
        this._sendInitialConfiguration(webviewView.webview)
            .then(() => {
                // After configuration is sent, update recording state
                this._updateRecordingState();
            })
            .catch(error => {
                console.error('Error sending initial configuration:', error);
            });

        // Handle messages from the webview
        this._registerMessageListener();

        // Initialize authentication status
        this._initializeAuthStatus(webviewView.webview).catch(error => {
            console.error('Error initializing auth status:', error);
        });

        // Listen for theme changes using the robust theme detector
        const themeChangeListener = this._themeDetector.onThemeChanged((theme) => {
            if (this._view) {
                console.log(`Theme changed to ${theme}, updating webview...`);
                // Update the webview HTML with new theme class
                this._view.webview.html = this._getHtmlForWebview(this._view.webview);

                // Notify the webview about theme change with consistent data structure
                this._view.webview.postMessage({
                    command: 'themeChanged',
                    data: {
                        theme: theme
                    }
                });
            }
        });

        this._disposables.push(themeChangeListener);
    }

    private async _sendInitialConfiguration(webview: vscode.Webview): Promise<void> {
        // Get prompt modes configuration
        const promptModesConfig = this.configurationService.getPromptModes();

        // Get API key asynchronously to ensure we use the securely stored one
        const apiKey = await this.configurationService.getApiKeyAsync() || '';
        console.log('[VoiceHypePanel] Initializing webview with API key:', apiKey ? 'present' : 'not set');

        // Initialize authentication status
        await this._initializeAuthStatus(webview);

        // Get audio settings to extract individual values
        const audioSettings = this.configurationService.getAudioSettings();

        // Send initial configuration to the webview
        const initialConfig = {
            service: this.configurationService.getTranscriptionService(),
            model: this.configurationService.getTranscriptionModel(),
            language: this.configurationService.getTranscriptionLanguage(),
            optimize: this.configurationService.getShouldOptimize(),
            optimizationModel: this.configurationService.getOptimizationModel(),
            customPrompt: this.configurationService.getCustomPrompt(),
            translate: this.configurationService.getTranslate(),
            realtime: this.configurationService.getTranscriptionRealtime(),
            apiKey: apiKey || '', // Ensure API key is always a string
            audioDevice: this.configurationService.getAudioDevice() || '',
            audioSettings: audioSettings,
            sampleRate: audioSettings.sampleRate || 22050, // Add missing sampleRate
            activePromptMode: promptModesConfig.active || 'clean-up',
            customPromptModes: this._promptModes.filter(m => m.isCustom),
            voiceCommandsEnabled: this.configurationService.getVoiceCommandsEnabled(),
            theme: this._themeDetector.getCurrentTheme()
        };

        // Log before sending to see exactly what's being sent
        console.log('[VoiceHypePanel] Sending initial configuration to webview:', JSON.stringify(initialConfig));

        // Verify the values are as expected
        console.log('[VoiceHypePanel] Config verification:');
        console.log(`Service from config: ${this.configurationService.getTranscriptionService()}`);
        console.log(`Model from config: ${this.configurationService.getTranscriptionModel()}`);
        console.log(`Language from config: ${this.configurationService.getTranscriptionLanguage()}`);
        console.log(`Optimize from config: ${this.configurationService.getShouldOptimize()}`);
        console.log(`Custom prompt from config (length): ${this.configurationService.getCustomPrompt().length}`);
        console.log(`Translate from config: ${this.configurationService.getTranslate()}`);
        console.log(`Realtime from config: ${this.configurationService.getTranscriptionRealtime()}`);
        console.log(`Audio device from config: ${this.configurationService.getAudioDevice()}`);
        console.log(`Audio settings: ${JSON.stringify(this.configurationService.getAudioSettings())}`);
        console.log(`Voice commands enabled: ${this.configurationService.getVoiceCommandsEnabled()}`);

        webview.postMessage({
            command: 'initialConfiguration',
            options: initialConfig
        });

        // We've removed the automatic retry mechanism that was causing frequent updates
        // This was contributing to the janky UI behavior
        // The initial configuration should be sufficient
    }

    /**
     * Force-send current configuration to the WebView
     * @param force Set to true to force the WebView to apply the update regardless of recent changes
     */
    private async _forceSendConfiguration(force: boolean = false): Promise<void> {
        if (!this._view) {
            console.log('[VoiceHypePanel] Cannot send configuration - no view available');
            return;
        }

        // Check if the webview is visible/active
        if (!this._view.visible) {
            console.log('[VoiceHypePanel] Webview not visible, scheduling configuration send for when it becomes visible');
            // Try again after a short delay
            setTimeout(() => {
                this._forceSendConfiguration(force).catch(error => {
                    console.error('[VoiceHypePanel] Error sending delayed configuration:', error);
                });
            }, 1000);
            return;
        }

        // If force=true, clear the recently updated options to ensure all options are sent
        if (force) {
            this._recentlyUpdatedOptions.clear();
            console.log('[VoiceHypePanel] Forcing configuration update - cleared recent updates');
        }

        // Get API key asynchronously for secure storage
        const apiKey = await this.configurationService.getApiKeyAsync() || '';
        console.log('[VoiceHypePanel] Sending configuration update with API key:', apiKey ? 'present' : 'not set');

        // Get audio settings to extract individual values
        const audioSettings = this.configurationService.getAudioSettings();

        const config = {
            service: this.configurationService.getTranscriptionService(),
            model: this.configurationService.getTranscriptionModel(),
            language: this.configurationService.getTranscriptionLanguage(),
            optimize: this.configurationService.getShouldOptimize(),
            optimizationModel: this.configurationService.getOptimizationModel(),
            customPrompt: this.configurationService.getCustomPrompt(),
            translate: this.configurationService.getTranslate(),
            realtime: this.configurationService.getTranscriptionRealtime(),
            audioDevice: this.configurationService.getAudioDevice() || '',
            sampleRate: audioSettings.sampleRate || 22050, // Add missing sampleRate
            apiKey: apiKey,
            theme: this._themeDetector.getCurrentTheme()
        };

        console.log(`[VoiceHypePanel] Force-sending configuration to webview (force=${force}):`, {
            ...config,
            apiKey: apiKey ? '(present)' : '(not set)'
        });

        this._view.webview.postMessage({
            command: 'updateOptions',
            options: config,
            force: force
        });
    }

    private _markOptionAsRecentlyUpdated(option: string): void {
        this._recentlyUpdatedOptions.add(option);

        // Automatically clear the flag after a delay
        setTimeout(() => {
            this._recentlyUpdatedOptions.delete(option);
        }, 2000); // 2 seconds should be enough to avoid feedback loops
    }

    private _updateRecordingState(): void {
        if (!this._view) {
            return;
        }

        // Get the current state directly from the recording service
        const isRecording = this.recordingService.isRecording();
        const isPaused = this.recordingService.isPaused();
        const elapsedTime = this.recordingService.getElapsedTime();

        console.log('VoiceHype [DEBUG]: Updating WebView recording state:', { isRecording, isPaused, elapsedTime });
        console.log('VoiceHype [DEBUG]: WebView update called from:', new Error().stack);

        // Send current state to webview
        this._view.webview.postMessage({
            command: 'recordingState',
            isRecording,
            isPaused,
            elapsedTime
        });
    }

    /**
     * Update the recording state in the UI when stopped externally
     * This should be called by other services when they stop recording
     */
    public updateRecordingState(): void {
        this._updateRecordingState();
    }

    /**
     * Send a message to the webview
     */
    public sendMessageToWebview(message: any): void {
        if (!this._view) {
            console.log('VoiceHype: WebView not available for message sending');
            return;
        }

        this._view.webview.postMessage(message);
    }

    /**
     * Update the transcription history in the WebView
     * This should be called after new entries are added to history
     */
    public async updateTranscriptionHistory(): Promise<void> {
        console.log('VoiceHype: updateTranscriptionHistory() called');

        if (!this._view) {
            console.log('VoiceHype: Cannot update WebView transcription history - no view available');
            return;
        }

        if (!this._view.webview) {
            console.log('VoiceHype: Cannot update WebView transcription history - webview not available');
            return;
        }

        console.log('VoiceHype: WebView and webview are available, proceeding with update');
        console.log('VoiceHype: WebView visible state:', this._view.visible);

        // Get the latest history
        const history = await this.historyService.getHistory();
        console.log(`VoiceHype: Retrieved ${history.length} transcriptions from history service`);

        if (history.length > 0) {
            // Log the most recent entry for debugging
            const latest = history[history.length - 1];
            console.log('VoiceHype: Most recent transcription:', {
                timestamp: new Date(latest.timestamp).toISOString(),
                textLength: latest.transcript.length,
                hasOptimized: !!latest.optimizedTranscript,
                service: latest.service || 'unknown',
                model: latest.model || 'unknown'
            });
        }

        // Format history for webview
        const formattedHistory = history.map(entry => ({
            id: entry.timestamp.toString(),
            timestamp: new Date(entry.timestamp).toISOString(),
            originalText: entry.transcript,
            optimizedText: entry.optimizedTranscript,
            service: entry.service || 'unknown',
            model: entry.model || 'unknown',
            language: entry.language || 'en',
            duration: entry.duration
        }));

        // Send to WebView
        try {
            console.log(`VoiceHype: Sending ${formattedHistory.length} transcriptions to WebView`);
            console.log('VoiceHype: Message payload:', {
                command: 'updateTranscriptions',
                transcriptionsCount: formattedHistory.length,
                firstTranscription: formattedHistory.length > 0 ? {
                    id: formattedHistory[0].id,
                    timestamp: formattedHistory[0].timestamp,
                    textLength: formattedHistory[0].originalText.length
                } : null
            });

            this._view.webview.postMessage({
                command: 'updateTranscriptions',
                transcriptions: formattedHistory
            });
            console.log('VoiceHype: Successfully sent transcription update to WebView');

            // If webview is not visible/active, try again after a short delay
            if (!this._view.visible) {
                console.log('VoiceHype: WebView not visible/active, scheduling retry in 1 second');
                setTimeout(() => {
                    if (this._view && this._view.webview) {
                        console.log('VoiceHype: Retrying transcription update to WebView');
                        this._view.webview.postMessage({
                            command: 'updateTranscriptions',
                            transcriptions: formattedHistory
                        });
                    }
                }, 1000);
            }
        } catch (error) {
            console.error('VoiceHype: Error sending transcription history to WebView:', error);
        }
    }

    private _sendCurrentOptionsToWebview(): void {
        if (!this._view) {
            return;
        }

        // Don't send options if we're currently processing a command
        if (this._isProcessingCommand) {
            console.log('Skipping options update while processing command');
            return;
        }

        // Create options object with only the properties that haven't been recently updated
        const options: Record<string, any> = {};

        // Only add options that haven't been recently updated by the UI
        if (!this._recentlyUpdatedOptions.has('service')) {
            options.service = this.configurationService.getTranscriptionService();
        }
        if (!this._recentlyUpdatedOptions.has('model')) {
            options.model = this.configurationService.getTranscriptionModel();
        }
        if (!this._recentlyUpdatedOptions.has('language')) {
            options.language = this.configurationService.getTranscriptionLanguage();
        }
        if (!this._recentlyUpdatedOptions.has('optimize')) {
            options.optimize = this.configurationService.getShouldOptimize();
        }
        if (!this._recentlyUpdatedOptions.has('optimizationModel')) {
            options.optimizationModel = this.configurationService.getOptimizationModel();
        }
        if (!this._recentlyUpdatedOptions.has('customPrompt')) {
            options.customPrompt = this.configurationService.getCustomPrompt();
        }
        if (!this._recentlyUpdatedOptions.has('translate')) {
            options.translate = this.configurationService.getTranslate();
        }
        if (!this._recentlyUpdatedOptions.has('realtime')) {
            options.realtime = this.configurationService.getTranscriptionRealtime();
        }

        // Only send the message if there are options to update
        if (Object.keys(options).length > 0) {
            console.log('Sending options to webview:', options);
            this._view.webview.postMessage({
                command: 'updateOptions',
                options
            });
        }
    }

    private _getHtmlForWebview(webview: vscode.Webview): string {
        console.log('Building webview HTML content...');

        // Get the local paths for webview resources
        const webviewBuildPath = path.join('webview-ui', 'build');
        const webviewStaticPath = path.join(webviewBuildPath, 'static');

        // Use a nonce to only allow specific scripts to be run
        const nonce = getNonce();

        // Find the main JS and CSS files from the React build
        let scriptPath = '';
        let stylePath = '';
        let runtimeScriptPath = '';
        let vendorScriptPath = '';

        try {
            // Debug log for WebView base path
            console.log(`WebView build path: ${path.join(this._extensionUri.fsPath, webviewBuildPath)}`);

            // React build places files in 'static/js' and 'static/css' with hash filenames
            const jsPath = path.join(this._extensionUri.fsPath, webviewStaticPath, 'js');
            const cssPath = path.join(this._extensionUri.fsPath, webviewStaticPath, 'css');

            console.log(`Looking for JS files in: ${jsPath}`);
            console.log(`Looking for CSS files in: ${cssPath}`);

            const jsFiles = fs.readdirSync(jsPath);
            const mainJsFiles = jsFiles.filter(file => file.startsWith('main.') && file.endsWith('.chunk.js'));
            const runtimeJsFiles = jsFiles.filter(file => file.startsWith('runtime') && file.endsWith('.js'));
            const vendorJsFiles = jsFiles.filter(file => file.indexOf('2.') > -1 && file.endsWith('.chunk.js'));

            const cssFiles = fs.readdirSync(cssPath).filter(file => file.startsWith('main.') && file.endsWith('.chunk.css'));

            if (mainJsFiles.length > 0) {
                scriptPath = path.join(webviewStaticPath, 'js', mainJsFiles[0]);
                console.log(`Found JS file: ${scriptPath}`);
            } else {
                console.error('No main.*.chunk.js file found in build/static/js directory');
            }

            if (runtimeJsFiles.length > 0) {
                runtimeScriptPath = path.join(webviewStaticPath, 'js', runtimeJsFiles[0]);
                console.log(`Found runtime JS file: ${runtimeScriptPath}`);
            }

            if (vendorJsFiles.length > 0) {
                vendorScriptPath = path.join(webviewStaticPath, 'js', vendorJsFiles[0]);
                console.log(`Found vendor JS file: ${vendorScriptPath}`);
            }

            if (cssFiles.length > 0) {
                stylePath = path.join(webviewStaticPath, 'css', cssFiles[0]);
                console.log(`Found CSS file: ${stylePath}`);
            } else {
                console.error('No main.*.css file found in build/static/css directory');
            }
        } catch (error) {
            console.error('Error finding webview resources:', error);
            // Fallbacks if files cannot be found dynamically
            scriptPath = path.join(webviewStaticPath, 'js', 'main.chunk.js');
            stylePath = path.join(webviewStaticPath, 'css', 'main.chunk.css');
        }

        // Convert the local paths to webview URIs
        const scriptUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, scriptPath));
        const styleUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, stylePath));
        const runtimeScriptUri = runtimeScriptPath ?
            webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, runtimeScriptPath)) : '';
        const vendorScriptUri = vendorScriptPath ?
            webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, vendorScriptPath)) : '';

        // Create URIs for both light and dark mode logos
        const logoLightUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'voicehype_logo.png'));
        const logoDarkUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'voicehype_logo_dark.png'));

        // Debug log for resources
        console.log(`Script URI: ${scriptUri}`);
        console.log(`Style URI: ${styleUri}`);
        console.log(`Logo Light URI: ${logoLightUri}`);
        console.log(`Logo Dark URI: ${logoDarkUri}`);

        // Improved CSP to allow React to work properly and load images from workspace
        const csp = `default-src 'none';
                    style-src ${webview.cspSource} 'unsafe-inline';
                    script-src ${webview.cspSource} 'unsafe-inline' 'unsafe-eval';
                    img-src ${webview.cspSource} https: data:;
                    font-src ${webview.cspSource};
                    connect-src ${webview.cspSource} https:;`;

        return `<!DOCTYPE html>
            <html lang="en" class="${this._getVSCodeThemeClass()}">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <meta http-equiv="Content-Security-Policy" content="${csp}">
                <link href="${styleUri}" rel="stylesheet">
                <title>VoiceHype Control Panel</title>
                <script>
                    window.voicehypeLogoLight = "${logoLightUri.toString()}";
                    window.voicehypeLogoDark = "${logoDarkUri.toString()}";
                </script>
            </head>
            <body class="${this._getVSCodeThemeClass()}">
                <div id="root"></div>
                ${runtimeScriptUri ? `<script src="${runtimeScriptUri}"></script>` : ''}
                ${vendorScriptUri ? `<script src="${vendorScriptUri}"></script>` : ''}
                <script src="${scriptUri}"></script>
            </body>
            </html>`;
    }

    private _getVSCodeThemeClass(): string {
        // Use the robust theme detector for accurate theme detection
        const theme = this._themeDetector.getCurrentTheme();
        return theme === 'dark' ? 'vscode-dark' : 'vscode-light';
    }

    public dispose(): void {
        // Clean up all disposables
        this._disposables.forEach(d => d.dispose());

        // Dispose of the theme detector
        this._themeDetector.dispose();
    }

    private _registerRecordingStateListener(): void {
        // Listen for recording state changes
        this.recordingService.onStateChange((isRecording: boolean, isPaused: boolean, elapsedTime: number) => {
            console.log(`VoiceHype [DEBUG]: Recording state change listener triggered: isRecording=${isRecording}, isPaused=${isPaused}`);
            if (this._view) {
                console.log('VoiceHype [DEBUG]: Sending recording state to webview from listener');
                this._view.webview.postMessage({
                    command: 'recordingState',
                    isRecording,
                    isPaused,
                    elapsedTime
                });
            }
        });
    }

    private async _handleRealtimeToggle(realtime: boolean): Promise<void> {
        // Get current audio settings
        const currentSettings = this.configurationService.getAudioSettings();
        const currentSampleRate = currentSettings.sampleRate;
        const currentService = this.configurationService.getTranscriptionService();

        console.log(`VoiceHype: Handling realtime toggle. Current sample rate: ${currentSampleRate}Hz, Realtime: ${realtime}, Service: ${currentService}`);

        // Skip sample rate adjustment in these cases:
        // 1. When we're processing a command (starting recording)
        // 2. When the service is Whisper (lemonfox) as it doesn't support real-time
        if (this._isProcessingCommand) {
            console.log('VoiceHype: Skipping sample rate adjustment during recording start');
            return;
        }

        if (currentService === 'lemonfox') {
            console.log('VoiceHype: Whisper service detected, skipping sample rate adjustment');
            // Force real-time off for Whisper service
            if (realtime) {
                console.log('VoiceHype: Forcing real-time off for Whisper service');
                await this.configurationService.updateSetting('voicehype.transcription.realtime', false);
                this._markOptionAsRecentlyUpdated('realtime');
            }
            return;
        }

        if (realtime) {
            // Store current sample rate before switching to real-time
            this._previousSampleRate = currentSampleRate;

            // Only change if not already at 16000 Hz
            if (currentSampleRate !== 16000) {
                console.log(`VoiceHype: Changing sample rate from ${currentSampleRate}Hz to 16000Hz for real-time mode`);

                // Set sample rate to 16000 Hz for real-time
                await this.configurationService.updateSetting('voicehype.audio.sampleRate', 16000);

                // No need to send to WebView - it's already updated optimistically
            } else {
                console.log('VoiceHype: Sample rate already at 16000Hz, no change needed');
            }
        } else {
            // Only restore if we have a previous sample rate and it's different
            if (this._previousSampleRate && this._previousSampleRate !== currentSampleRate) {
                console.log(`VoiceHype: Restoring sample rate from ${currentSampleRate}Hz to ${this._previousSampleRate}Hz`);

                // Restore previous sample rate
                await this.configurationService.updateSetting('voicehype.audio.sampleRate', this._previousSampleRate);

                // No need to send to WebView - it's already updated optimistically
            } else {
                console.log('VoiceHype: No sample rate restoration needed');
            }
        }
    }

    private _registerMessageListener(): void {
        if (!this._view) {
            return;
        }

        this._view.webview.onDidReceiveMessage(async (message) => {
            try {
                switch (message.command) {
                    case 'updateAudioDevice':
                        if (message.deviceId !== undefined) {
                            console.log('VoiceHype: Updating audio device to:', message.deviceId);
                            // Update the configuration setting
                            await this.configurationService.updateSetting('voicehype.audio.device', message.deviceId);
                            this._markOptionAsRecentlyUpdated('audioDevice');

                            // Show a confirmation message
                            const deviceName = message.deviceId
                                ? (await this.recordingService.getAvailableDevices()).find(d => d.id === message.deviceId)?.name || message.deviceId
                                : 'System Default';
                            vscode.window.showInformationMessage(`VoiceHype: Audio device set to ${deviceName}`);
                        }
                        break;

                    case 'getPromptModes':
                        // Get active mode ID from configuration
                        const promptModesConfig = this.configurationService.getPromptModes();
                        const activeMode = promptModesConfig.active || 'clean-up';

                        console.log(`VoiceHype: Sending prompt modes to webview with activeMode: ${activeMode}`);
                        console.log(`VoiceHype: Custom modes count: ${this._promptModes.filter(m => m.isCustom).length}`);

                        this._view?.webview.postMessage({
                            command: 'promptModes',
                            modes: this._promptModes,
                            activeMode: activeMode
                        });
                        break;

                    case 'savePromptMode':
                        if (message.mode) {
                            // Make sure mode has isCustom flag
                            const customMode = {
                                ...message.mode,
                                isCustom: true
                            };

                            console.log(`VoiceHype: Saving custom prompt mode "${customMode.name}"`);

                            // Add to in-memory store
                            this._promptModes = [...this._promptModes, customMode];

                            // Get current custom modes only
                            const customModes = this._promptModes.filter(m => m.isCustom);
                            console.log(`VoiceHype: Total custom modes to save: ${customModes.length}`);

                            try {
                                // Persist to settings (global scope)
                                await this.configurationService.updateSetting(
                                    'voicehype.promptModes.custom',
                                    customModes
                                );

                                console.log(`VoiceHype: Successfully saved custom prompt mode "${customMode.name}" to global configuration`);
                            } catch (error) {
                                console.error(`VoiceHype: Error saving custom prompt mode:`, error);
                            }

                            // Send updated modes back to webview
                            this._view?.webview.postMessage({
                                command: 'promptModes',
                                modes: this._promptModes,
                                activeMode: this.configurationService.getPromptModes().active
                            });
                        }
                        break;

                    case 'setActivePromptMode':
                        if (message.modeId) {
                            console.log(`VoiceHype: Setting active prompt mode to "${message.modeId}"`);

                            try {
                                await this.configurationService.updateSetting(
                                    'voicehype.promptModes.active',
                                    message.modeId
                                );
                                console.log(`VoiceHype: Successfully saved active prompt mode "${message.modeId}" to configuration`);

                                // Confirm the change to the webview
                                this._view?.webview.postMessage({
                                    command: 'activePromptModeUpdated',
                                    modeId: message.modeId
                                });
                            } catch (error) {
                                console.error(`VoiceHype: Error setting active prompt mode:`, error);
                            }
                        }
                        break;

                    case 'deletePromptMode':
                        if (message.modeId) {
                            console.log(`VoiceHype: Deleting prompt mode with ID: ${message.modeId}`);

                            // Filter out the mode to delete
                            const updatedModes = this._promptModes.filter(mode => mode.id !== message.modeId);

                            // Only allow deleting custom modes
                            if (updatedModes.length < this._promptModes.length) {
                                // Update in-memory store
                                this._promptModes = updatedModes;

                                // Persist to settings
                                await this.configurationService.updateSetting(
                                    'voicehype.promptModes.custom',
                                    this._promptModes.filter(m => m.isCustom)
                                );

                                // Send updated modes back to webview
                                this._view?.webview.postMessage({
                                    command: 'promptModes',
                                    modes: this._promptModes
                                });

                                console.log(`VoiceHype: Prompt mode deleted and settings updated`);
                            }
                        }
                        break;
                    case 'startRecording':
                        // Set flag to indicate we're processing a command
                        this._isProcessingCommand = true;

                        const options = message.options || {};
                        // Update settings with the options received
                        if (options.service) {
                            await this.configurationService.updateSetting('voicehype.transcription.service', options.service);
                            this._markOptionAsRecentlyUpdated('service');
                        }
                        if (options.model) {
                            await this.configurationService.updateSetting('voicehype.transcription.model', options.model);
                            this._markOptionAsRecentlyUpdated('model');
                        }
                        if (options.language) {
                            await this.configurationService.updateSetting('voicehype.transcription.language', options.language);
                            this._markOptionAsRecentlyUpdated('language');
                        }
                        if (options.optimize !== undefined) {
                            await this.configurationService.updateSetting('voicehype.transcription.shouldOptimize', options.optimize);
                            this._markOptionAsRecentlyUpdated('optimize');
                        }
                        if (options.customPrompt !== undefined) {
                            await this.configurationService.updateSetting('voicehype.transcription.customPrompt', options.customPrompt);
                            this._markOptionAsRecentlyUpdated('customPrompt');
                        }
                        if (options.translate !== undefined) {
                            await this.configurationService.updateSetting('voicehype.transcription.translate', options.translate);
                            this._markOptionAsRecentlyUpdated('translate');
                        }
                        if (options.realtime !== undefined) {
                            // Check if we're switching to Whisper (lemonfox) service with real-time on
                            // If so, force real-time off as it's not supported for Whisper
                            const currentService = this.configurationService.getTranscriptionService();
                            if (options.realtime && currentService === 'lemonfox') {
                                console.log('VoiceHype: Whisper service detected, forcing real-time off');
                                options.realtime = false;
                            }

                            await this.configurationService.updateSetting('voicehype.transcription.realtime', options.realtime);
                            this._markOptionAsRecentlyUpdated('realtime');

                            // Only handle sample rate changes if real-time is enabled
                            if (options.realtime) {
                                console.log('VoiceHype: Real-time enabled, adjusting sample rate');
                                await this._handleRealtimeToggle(options.realtime);
                            } else {
                                console.log('VoiceHype: Real-time disabled, keeping current sample rate');
                            }
                        }
                        // Start recording
                        console.log('VoiceHype: Starting recording from webview with optimize:', options.optimize || false);
                        await this.recordingService.startRecording(options.optimize || false);

                        // Ensure VS Code context and status bar are updated
                        vscode.commands.executeCommand('setContext', 'voicehype.isRecording', true);
                        vscode.commands.executeCommand('setContext', 'voicehype.isPaused', false);

                        // Force update the status bar
                        const statusBarService = this.recordingService.getStatusBarService();
                        if (statusBarService) {
                            statusBarService.updateStatusBarItems(true, options.optimize || false, false);
                        }

                        // Reset the processing flag
                        this._isProcessingCommand = false;
                        break;

                    case 'stopRecording':
                        console.log('VoiceHype: Stopping recording from webview');
                        await this.recordingService.stopRecording(this.configurationService.getShouldOptimize());

                        // Ensure VS Code context and status bar are updated
                        vscode.commands.executeCommand('setContext', 'voicehype.isRecording', false);
                        vscode.commands.executeCommand('setContext', 'voicehype.isPaused', false);

                        // Force update the status bar
                        const stopStatusBarService = this.recordingService.getStatusBarService();
                        if (stopStatusBarService) {
                            stopStatusBarService.updateStatusBarItems(false, false, false);
                        }
                        break;

                    case 'pauseRecording':
                        console.log('VoiceHype: Pausing recording from webview');
                        await this.recordingService.pauseRecording();

                        // Ensure VS Code context is updated
                        vscode.commands.executeCommand('setContext', 'voicehype.isPaused', true);
                        break;

                    case 'resumeRecording':
                        console.log('VoiceHype: Resuming recording from webview');
                        await this.recordingService.resumeRecording();

                        // Ensure VS Code context is updated
                        vscode.commands.executeCommand('setContext', 'voicehype.isPaused', false);
                        break;

                    case 'cancelRecording':
                        console.log('VoiceHype: Cancelling recording from webview');
                        await this.recordingService.cancelRecording();

                        // Ensure VS Code context and status bar are updated
                        vscode.commands.executeCommand('setContext', 'voicehype.isRecording', false);
                        vscode.commands.executeCommand('setContext', 'voicehype.isPaused', false);

                        // Force update the status bar
                        const cancelStatusBarService = this.recordingService.getStatusBarService();
                        if (cancelStatusBarService) {
                            cancelStatusBarService.updateStatusBarItems(false, false, false);
                        }
                        break;

                    case 'getOptions':
                        this._sendCurrentOptionsToWebview();
                        break;

                    case 'getTranscriptions':
                        // Send all historical transcriptions to the webview
                        const history = await this.historyService.getHistory();
                        this._view?.webview.postMessage({
                            command: 'updateTranscriptions',
                            transcriptions: history.map(entry => ({
                                id: entry.timestamp.toString(),
                                timestamp: new Date(entry.timestamp).toISOString(),
                                originalText: entry.transcript,
                                optimizedText: entry.optimizedTranscript,
                                service: entry.service || 'unknown',
                                model: entry.model || 'unknown',
                                language: entry.language || 'en',
                                duration: entry.duration
                            }))
                        });
                        break;

                    case 'updateOptions':
                        // Set flag to indicate we're processing a command
                        this._isProcessingCommand = true;

                        if (message.options) {
                            const { service, model, language, customPrompt, optimize, optimizationModel, translate, realtime, apiKey } = message.options;

                            // Track which options we're updating to avoid sending them back
                            const updatedOptions: string[] = [];

                            if (service !== undefined) {
                                await this.configurationService.updateSetting('voicehype.transcription.service', service);
                                this._markOptionAsRecentlyUpdated('service');
                                updatedOptions.push('service');

                                // If service is changed to AssemblyAI, reset translate to false
                                if (service === 'assemblyai') {
                                    await this.configurationService.updateSetting('voicehype.transcription.translate', false);
                                    this._markOptionAsRecentlyUpdated('translate');
                                    updatedOptions.push('translate');
                                }

                                // If service is changed to Whisper (lemonfox), ensure real-time is off
                                // This prevents the sample rate from being forced to 16000Hz
                                if (service === 'lemonfox') {
                                    const currentRealtime = this.configurationService.getTranscriptionRealtime();
                                    if (currentRealtime) {
                                        console.log('VoiceHype: Switching to Whisper service, disabling real-time transcription');
                                        await this.configurationService.updateSetting('voicehype.transcription.realtime', false);
                                        this._markOptionAsRecentlyUpdated('realtime');
                                        updatedOptions.push('realtime');

                                        // Skip sample rate adjustment since we're turning off real-time
                                        this._isProcessingCommand = true;
                                    }
                                }
                            }
                            if (model !== undefined) {
                                await this.configurationService.updateSetting('voicehype.transcription.model', model);
                                this._markOptionAsRecentlyUpdated('model');
                                updatedOptions.push('model');
                            }
                            if (language !== undefined) {
                                await this.configurationService.updateSetting('voicehype.transcription.language', language);
                                this._markOptionAsRecentlyUpdated('language');
                                updatedOptions.push('language');
                            }
                            if (optimize !== undefined) {
                                await this.configurationService.updateSetting('voicehype.transcription.shouldOptimize', optimize);
                                this._markOptionAsRecentlyUpdated('optimize');
                                updatedOptions.push('optimize');
                            }
                            if (optimizationModel !== undefined) {
                                await this.configurationService.updateSetting('voicehype.transcription.optimizationModel', optimizationModel);
                                this._markOptionAsRecentlyUpdated('optimizationModel');
                                updatedOptions.push('optimizationModel');
                                console.log(`[VoiceHypePanel] Updated optimization model to: ${optimizationModel}`);
                            }
                            if (customPrompt !== undefined) {
                                try {
                                    console.log(`[VoiceHypePanel] Updating custom prompt, length: ${customPrompt.length}`);
                                    await this.configurationService.updateSetting('voicehype.transcription.customPrompt', customPrompt);
                                    this._markOptionAsRecentlyUpdated('customPrompt');
                                    updatedOptions.push('customPrompt');
                                    console.log(`[VoiceHypePanel] Successfully saved custom prompt to VS Code settings`);
                                } catch (error) {
                                    console.error('[VoiceHypePanel] Error saving custom prompt:', error);
                                    // Still mark as updated to prevent feedback loops
                                    this._markOptionAsRecentlyUpdated('customPrompt');
                                    // Don't add to updatedOptions list as it failed to save

                                    // We'll still use the custom prompt value for the current session
                                    // It's stored in memory in the ConfigurationService

                                    // Notify the webview of the error (only show once per session)
                                    if (!this._hasShownCustomPromptError) {
                                        this._hasShownCustomPromptError = true;
                                        this._view?.webview.postMessage({
                                            command: 'error',
                                            message: 'Custom prompt is temporarily stored but may not persist between sessions. Please restart VS Code after the first use.'
                                        });

                                        // Also show a VS Code warning
                                        vscode.window.showWarningMessage(
                                            'VoiceHype: Custom prompt saved in memory but may not persist. Please restart VS Code after using once to complete registration.'
                                        );
                                    }
                                }
                            }
                            if (translate !== undefined) {
                                await this.configurationService.updateSetting('voicehype.transcription.translate', translate);
                                this._markOptionAsRecentlyUpdated('translate');
                                updatedOptions.push('translate');
                            }
                            if (realtime !== undefined) {
                                await this.configurationService.updateSetting('voicehype.transcription.realtime', realtime);
                                this._markOptionAsRecentlyUpdated('realtime');

                                // Handle sample rate changes for real-time
                                await this._handleRealtimeToggle(realtime);
                            }
                            if (apiKey !== undefined) {
                                try {
                                    console.log('[VoiceHypePanel] Received API key update from webview');
                                    // Use the setApiKey method to securely store the API key
                                    await this.configurationService.setApiKey(apiKey);

                                    // After setting the API key, check if we have an authentication service
                                    // to validate the key and update authentication state
                                    const authService = this.voiceHypeExtension?.getService('authService');
                                    if (authService) {
                                        try {
                                            const isValid = await authService.validateApiKey();
                                            if (isValid) {
                                                const userProfile = authService.getUserProfile();
                                                if (userProfile) {
                                                    // Send authentication status to the webview
                                                    if (this._view && this._view.webview) {
                                                        this._view.webview.postMessage({
                                                            command: 'authStatusUpdated',
                                                            authenticated: true,
                                                            userProfile: userProfile
                                                        });
                                                    }
                                                }
                                            }
                                        } catch (error) {
                                            console.error('[VoiceHypePanel] Error validating API key:', error);
                                        }
                                    }
                                    this._markOptionAsRecentlyUpdated('apiKey');
                                    updatedOptions.push('apiKey');

                                    // Force configuration resend to ensure API key is reflected in UI
                                    await this._forceSendConfiguration(true);

                                    // Show a confirmation message to the user
                                    vscode.window.showInformationMessage('VoiceHype API key securely stored');

                                    // Clear any old references to the API key in settings
                                    await vscode.workspace.getConfiguration('voicehype').update('apiKey', undefined, vscode.ConfigurationTarget.Global);
                                } catch (error) {
                                    console.error('[VoiceHypePanel] Failed to securely store API key:', error);
                                    vscode.window.showErrorMessage('Failed to store API key securely. Please try again.');
                                }
                            }

                            // Log updated options for debugging
                            if (updatedOptions.length > 0) {
                                console.log(`[VoiceHypePanel] Updated options: ${updatedOptions.join(', ')}`);
                            }
                        }

                        // Reset the processing flag
                        this._isProcessingCommand = false;
                        break;

                    case 'copyToClipboard':
                        if (message.text) {
                            const shouldCopy = vscode.workspace.getConfiguration('voicehype').get('copyResultsToClipboard', true);
                            if (shouldCopy) {
                                await vscode.env.clipboard.writeText(message.text);
                                vscode.window.showInformationMessage('Text copied to clipboard');
                            } else {
                                vscode.window.showInformationMessage('Clipboard operations are currently disabled in settings');
                            }
                        }
                        break;

                    case 'optimizeTranscription':
                        if (message.text) {
                            const optimized = await this.transcriptionService.optimizeText(message.text, message.customPrompt);
                            // Update the history entry if an ID was provided
                            if (message.id) {
                                // Find the entry by timestamp
                                const timestamp = parseInt(message.id, 10);
                                if (!isNaN(timestamp)) {
                                    // Update the entry in history
                                    const history = this.historyService.getHistory();
                                    const entryIndex = history.findIndex(entry => entry.timestamp === timestamp);
                                    if (entryIndex >= 0) {
                                        this.historyService.updateEntryWithOptimization(entryIndex, optimized);
                                    }
                                }
                            }
                            this._view?.webview.postMessage({
                                command: 'optimizationComplete',
                                id: message.id,
                                optimizedText: optimized
                            });
                        }
                        break;

                    case 'deleteTranscription':
                        if (message.id) {
                            const timestamp = parseInt(message.id, 10);
                            if (!isNaN(timestamp)) {
                                // Find the entry in history
                                const history = this.historyService.getHistory();
                                const entryIndex = history.findIndex(entry => entry.timestamp === timestamp);
                                if (entryIndex >= 0) {
                                    // Remove the entry from history
                                    history.splice(entryIndex, 1);
                                    await this.historyService.clearHistory();
                                    for (const entry of history) {
                                        this.historyService.addEntry(entry);
                                    }
                                    // Notify webview that the transcription was deleted
                                    this._view?.webview.postMessage({
                                        command: 'transcriptionDeleted',
                                        id: message.id
                                    });
                                }
                            }
                        }
                        break;

                    case 'clearTranscriptionHistory':
                        try {
                            console.log('VoiceHypePanel: Received clearTranscriptionHistory command');

                            // Clear history from both service instances to ensure consistency
                            this._historyService.clearHistory();
                            this.historyService.clearHistory();
                            console.log('VoiceHypePanel: History cleared in both service instances');

                            // Send empty transcriptions list to WebView
                            if (this._view) {
                                console.log('VoiceHypePanel: Sending messages to webview to clear history');

                                // First send updateTranscriptions with empty array
                                this._view.webview.postMessage({
                                    command: 'updateTranscriptions',
                                    transcriptions: []
                                });
                                console.log('VoiceHypePanel: Sent updateTranscriptions with empty array');

                                // Then send allTranscriptionsCleared message to ensure the webview clears its state
                                this._view.webview.postMessage({
                                    command: 'allTranscriptionsCleared'
                                });
                                console.log('VoiceHypePanel: Sent allTranscriptionsCleared message');

                                // Show confirmation message
                                vscode.window.showInformationMessage('Transcription history cleared');
                            } else {
                                console.error('VoiceHypePanel: View is not available, cannot send messages to webview');
                            }
                        } catch (error) {
                            console.error('VoiceHype: Error clearing transcription history:', error);
                            vscode.window.showErrorMessage('Failed to clear transcription history');
                        }
                        break;

                    case 'getAudioSettings':
                        this._sendAudioSettings();
                        break;

                    case 'getAudioDevices':
                        // Get available audio devices from the AudioDeviceManager
                        const devices = await this.recordingService.getAvailableDevices(true); // Force refresh when explicitly requested
                        console.log('VoiceHype: Sending audio devices to WebView:', devices);
                        this._view?.webview.postMessage({
                            command: 'updateAudioDevices',
                            devices: devices.map(device => ({
                                id: device.id,
                                name: device.name
                            }))
                        });
                        break;

                    case 'updateSetting':
                        if (message.key === 'voicehype.audio.sampleRate') {
                            console.log(`VoiceHype: Received sample rate update from WebView: ${message.value}`);
                            this.configurationService.updateSetting(message.key, message.value);
                            // No need to send settings back to WebView - it's already updated optimistically
                        }
                        break;

                    case 'showConfirmation':
                        if (message.message && message.onConfirm) {
                            console.log(`VoiceHypePanel: Received showConfirmation with onConfirm=${message.onConfirm}`);

                            const result = await vscode.window.showWarningMessage(
                                message.message,
                                { modal: true },
                                'Yes',
                                'No'
                            );

                            console.log(`VoiceHypePanel: User selected: ${result}`);

                            if (result === 'Yes') {
                                console.log(`VoiceHypePanel: Forwarding command: ${message.onConfirm}`);
                                // Handle the confirmation by sending the original command
                                await this._handleMessage({ command: message.onConfirm });
                            } else {
                                console.log('VoiceHypePanel: User cancelled the operation');
                            }
                        } else {
                            console.error('VoiceHypePanel: Received showConfirmation without message or onConfirm');
                        }
                        break;

                    // Handle authentication-related commands
                    case 'authCancelled':
                        console.log('VoiceHype: Auth flow was cancelled by user');
                        // Clear any pending auth states
                        if (this.voiceHypeExtension) {
                            const authService = this.voiceHypeExtension.getService('authService');
                            if (authService) {
                                authService.clearPendingStates();
                            }
                        }
                        break;

                    case 'authenticate':
                        console.log('VoiceHype: Starting authentication flow from webview');
                        if (this.voiceHypeExtension) {
                            const authService = this.voiceHypeExtension.getService('authService');
                            if (authService) {
                                try {
                                    // Show a progress notification during sign-in
                                    await vscode.window.withProgress(
                                        {
                                            location: vscode.ProgressLocation.Notification,
                                            title: 'Signing in to VoiceHype...',
                                            cancellable: true
                                        },
                                        async (_, token) => {
                                            // Add cancellation token handling
                                            token.onCancellationRequested(() => {
                                                console.log('VoiceHype: Authentication cancelled by user');
                                                this._view?.webview.postMessage({
                                                    command: 'authStatusUpdated',
                                                    authenticated: false,
                                                    error: 'Authentication was cancelled'
                                                });
                                            });

                                            await authService.signIn();

                                            // Check authentication state after sign-in
                                            const authState = authService.getAuthState();
                                            const userProfile = authService.getUserProfile();

                                            // Only show success message and update if actually signed in
                                            if (authState === 'signedIn') {
                                                // Send auth status to webview
                                                this._view?.webview.postMessage({
                                                    command: 'authStatusUpdated',
                                                    authenticated: true,
                                                    userProfile: userProfile
                                                });

                                                vscode.window.showInformationMessage('Successfully signed in to VoiceHype!');
                                            } else {
                                                // User likely cancelled the authentication process
                                                this._view?.webview.postMessage({
                                                    command: 'authStatusUpdated',
                                                    authenticated: false,
                                                    error: 'Authentication was cancelled'
                                                });
                                            }
                                        }
                                    );
                                } catch (error) {
                                    console.error('VoiceHype: Sign in error:', error);
                                    vscode.window.showErrorMessage(`Failed to sign in: ${error instanceof Error ? error.message : 'Unknown error'}`);

                                    // Notify the webview about the failure
                                    this._view?.webview.postMessage({
                                        command: 'authStatusUpdated',
                                        authenticated: false,
                                        error: error instanceof Error ? error.message : 'Unknown error'
                                    });
                                }
                            } else {
                                console.error('VoiceHype: Authentication service not available');
                                vscode.window.showErrorMessage('Authentication service not available');
                            }
                        } else {
                            console.error('VoiceHype: Extension reference not available');
                            vscode.window.showErrorMessage('Cannot authenticate at this time');
                        }
                        break;

                    case 'signOut':
                        console.log('VoiceHype: Sign out requested from webview');
                        if (this.voiceHypeExtension) {
                            const authService = this.voiceHypeExtension.getService('authService');
                            if (authService) {
                                try {
                                    await authService.signOut();

                                    // Notify the webview about the successful sign out
                                    this._view?.webview.postMessage({
                                        command: 'authStatusUpdated',
                                        authenticated: false
                                    });

                                    vscode.window.showInformationMessage('Signed out from VoiceHype');
                                } catch (error) {
                                    console.error('VoiceHype: Sign out error:', error);
                                    vscode.window.showErrorMessage(`Failed to sign out: ${error instanceof Error ? error.message : 'Unknown error'}`);
                                }
                            }
                        }
                        break;

                    case 'checkAuthStatus':
                        console.log('VoiceHype: Checking authentication status from webview');
                        if (this.voiceHypeExtension) {
                            const authService = this.voiceHypeExtension.getService('authService');
                            if (authService) {
                                try {
                                    const isValid = await authService.validateApiKey();
                                    const authState = authService.getAuthState();
                                    const userProfile = authService.getUserProfile();

                                    // Send auth status to webview
                                    this._view?.webview.postMessage({
                                        command: 'authStatusUpdated',
                                        authenticated: isValid && authState === 'signedIn',
                                        userProfile: isValid ? userProfile : undefined
                                    });
                                } catch (error) {
                                    console.error('VoiceHype: Error checking auth status:', error);

                                    // Send error status to webview
                                    this._view?.webview.postMessage({
                                        command: 'authStatusUpdated',
                                        authenticated: false,
                                        error: error instanceof Error ? error.message : 'Unknown error'
                                    });
                                }
                            }
                        }
                        break;

                    case 'authStatusUpdated':
                        console.log('VoiceHype: Received auth status update from webview');
                        if (message.authenticated && message.apiKey) {
                            try {
                                await this.configurationService.setApiKey(message.apiKey);
                                console.log('API key stored securely');

                                // Notify webview of successful update
                                this._view?.webview.postMessage({
                                    command: 'apiKeySaved',
                                    success: true,
                                    apiKey: message.apiKey
                                });
                            } catch (error) {
                                console.error('Error storing API key:', error);
                                this._view?.webview.postMessage({
                                    command: 'apiKeySaved',
                                    success: false,
                                    error: 'Failed to store API key'
                                });
                            }
                        }
                        break;

                    case 'manualApiKeyVerified':
                        console.log('VoiceHype: Manual API key verification received');
                        if (message.apiKey) {
                            try {
                                await this.configurationService.setApiKey(message.apiKey);
                                console.log('Manual API key stored securely');

                                // Notify webview of successful verification
                                this._view?.webview.postMessage({
                                    command: 'apiKeySaved',
                                    success: true,
                                    apiKey: message.apiKey
                                });
                            } catch (error) {
                                console.error('Error storing manual API key:', error);
                                this._view?.webview.postMessage({
                                    command: 'apiKeySaved',
                                    success: false,
                                    error: 'Failed to store API key'
                                });
                            }
                        }
                        break;

                    case 'updateView':
                        console.log('VoiceHype: Received view update request');
                        if (message.view === 'main') {
                            // Update VS Code context to reflect authenticated state
                            vscode.commands.executeCommand('setContext', 'voicehype.isAuthenticated', true);

                            // Notify webview of successful view update
                            this._view?.webview.postMessage({
                                command: 'viewUpdated',
                                view: 'main'
                            });
                        }
                        break;

                    case 'getApiKey':
                        // Fetch API key from secure storage and send back to webview
                        try {
                            const apiKey = await this.configurationService.getApiKeyAsync();
                            console.log('Fetched API key for webview initialization:', apiKey ? 'API key present' : 'No API key');

                            // Also get auth status if possible
                            let authenticated = false;
                            let userProfile = undefined;

                            if (this.voiceHypeExtension) {
                                const authService = this.voiceHypeExtension.getService('authService');
                                if (authService) {
                                    const isValid = await authService.validateApiKey();
                                    const authState = authService.getAuthState();
                                    authenticated = isValid && authState === 'signedIn';
                                    userProfile = authService.getUserProfile();
                                }
                            }

                            this._view?.webview.postMessage({
                                command: 'apiKeyResponse',
                                apiKey: apiKey,
                                authenticated: authenticated,
                                userProfile: userProfile
                            });
                        } catch (error) {
                            console.error('Error fetching API key:', error);
                            this._view?.webview.postMessage({
                                command: 'apiKeyResponse',
                                apiKey: undefined,
                                authenticated: false,
                                error: 'Failed to fetch API key'
                            });
                        }
                        break;

                    case 'showApiKeyInput':
                        // Toggle back to the API key input UI
                        this._view?.webview.postMessage({
                            command: 'showApiKeyInput'
                        });
                        break;

                    // Configuration management commands
                    case 'getConfigurations':
                        await this._sendConfigurations();
                        break;

                    case 'createConfiguration':
                        if (message.data) {
                            await this._handleCreateConfiguration(message.data);
                        }
                        break;

                    case 'updateConfiguration':
                        if (message.data) {
                            await this._handleUpdateConfiguration(message.data);
                        }
                        break;

                    case 'deleteConfiguration':
                        if (message.data?.id) {
                            await this._handleDeleteConfiguration(message.data.id);
                        }
                        break;

                    case 'switchConfiguration':
                        if (message.data?.id) {
                            await this._handleSwitchConfiguration(message.data.id);
                        }
                        break;

                    case 'getWhatsNewStatus':
                        await this._sendWhatsNewStatus();
                        break;

                    case 'markWhatsNewSeen':
                        if (message.data?.version) {
                            await this._handleMarkWhatsNewSeen(message.data.version);
                        }
                        break;

                    case 'refreshConfigAndTheme':
                        console.log('VoiceHype: Received refreshConfigAndTheme request from webview');
                        // Check if view is available, if not, wait a bit and try again
                        if (!this._view) {
                            console.log('VoiceHype: View not available, scheduling retry...');
                            setTimeout(() => {
                                this._handleRefreshConfigAndTheme();
                            }, 500);
                            break;
                        }

                        // Send current configuration and theme to webview
                        setTimeout(() => {
                            this._forceSendConfiguration(true).catch(error => {
                                console.error('Error sending configuration to webview:', error);
                            });

                            // Send theme information
                            const currentTheme = this._themeDetector.getCurrentTheme();
                            this._view?.webview.postMessage({
                                command: 'themeChanged',
                                data: {
                                    theme: currentTheme
                                }
                            });
                        }, 100); // Small delay to ensure webview is ready
                        break;

                    default:
                        console.warn(`VoiceHypePanel: Unknown command received from webview: ${message.command}`);
                }
            } catch (error) {
                console.error('VoiceHypePanel: Error handling message from webview:', error);
            }
        });
    }

    /**
     * Send audio settings to the webview
     */
    private _sendAudioSettings(): void {
        if (!this._view) {
            return;
        }

        // Get available audio devices
        this.recordingService.getAvailableDevices().then(devices => {
            const selectedDeviceId = this.configurationService.getAudioDevice();

            this._view?.webview.postMessage({
                command: 'updateAudioDevices',
                devices,
                selectedDeviceId
            });
        }).catch(error => {
            console.error('Error getting audio devices:', error);
        });
    }

    /**
     * Handle a message recursively
     * @param message The message to handle
     */
    private async _handleMessage(message: any): Promise<void> {
        // Process the message based on its command
        switch (message.command) {
            case 'startRecording':
                await vscode.commands.executeCommand('voicehype.startRecording');
                break;
            case 'stopRecording':
                await vscode.commands.executeCommand('voicehype.stopRecording');
                break;
            case 'authenticate':
                await vscode.commands.executeCommand('voicehype.signIn');
                break;
            case 'signOut':
                await vscode.commands.executeCommand('voicehype.signOut');
                break;
            case 'checkAuthStatus':
                await vscode.commands.executeCommand('voicehype.showAuthStatus');
                break;
            case 'clearTranscriptionHistory':
                try {
                    console.log('VoiceHypePanel: Received clearTranscriptionHistory command in _handleMessage');

                    // Clear history from both service instances to ensure consistency
                    this._historyService.clearHistory();
                    this.historyService.clearHistory();
                    console.log('VoiceHypePanel: History cleared in both service instances via _handleMessage');

                    // Send empty transcriptions list to WebView
                    if (this._view) {
                        console.log('VoiceHypePanel: Sending messages to webview to clear history from _handleMessage');

                        // First send updateTranscriptions with empty array
                        this._view.webview.postMessage({
                            command: 'updateTranscriptions',
                            transcriptions: []
                        });
                        console.log('VoiceHypePanel: Sent updateTranscriptions with empty array from _handleMessage');

                        // Then send allTranscriptionsCleared message to ensure the webview clears its state
                        this._view.webview.postMessage({
                            command: 'allTranscriptionsCleared'
                        });
                        console.log('VoiceHypePanel: Sent allTranscriptionsCleared message from _handleMessage');

                        // Show confirmation message
                        vscode.window.showInformationMessage('Transcription history cleared');
                    } else {
                        console.error('VoiceHypePanel: View is not available, cannot send messages to webview from _handleMessage');
                    }
                } catch (error) {
                    console.error('VoiceHype: Error clearing transcription history in _handleMessage:', error);
                    vscode.window.showErrorMessage('Failed to clear transcription history');
                }
                break;
            // Add other commands as needed
            default:
                console.log(`Unknown command: ${message.command}`);
        }
    }

    /**
     * Initialize authentication status and send it to the webview
     */
    private async _initializeAuthStatus(webview: vscode.Webview): Promise<void> {
        if (!this.voiceHypeExtension) {
            console.log('[VoiceHypePanel] No extension reference available for auth status');
            return;
        }

        const authService = this.voiceHypeExtension.getService('authService');
        if (!authService) {
            console.log('[VoiceHypePanel] No authentication service available');
            return;
        }

        try {
            const isValid = await authService.validateApiKey();
            const authState = authService.getAuthState();
            const userProfile = authService.getUserProfile();

            // Get API key for completeness
            const apiKey = await this.configurationService.getApiKeyAsync();

            console.log(`[VoiceHypePanel] Auth status: valid=${isValid}, state=${authState}, apiKey=${apiKey ? 'present' : 'not set'}`);

            webview.postMessage({
                command: 'authStatusUpdated',
                authenticated: isValid && authState === 'signedIn',
                userProfile: isValid ? userProfile : undefined,
                apiKey: apiKey // Include API key in the auth status update
            });
        } catch (error) {
            console.error('[VoiceHypePanel] Error checking auth status:', error);
        }
    }

    /**
     * Send configurations to the webview
     */
    private async _sendConfigurations(): Promise<void> {
        if (!this._view) {
            return;
        }

        try {
            // Get configurations from ConfigurationService
            const configurations = this.configurationService.getConfigurations();
            const activeConfigurationId = this.configurationService.getActiveConfigurationId();

            this._view.webview.postMessage({
                command: 'configurationsUpdated',
                data: {
                    configurations,
                    activeConfigurationId
                }
            });
        } catch (error) {
            console.error('VoiceHype: Error sending configurations:', error);
            this._view?.webview.postMessage({
                command: 'error',
                error: 'Failed to load configurations'
            });
        }
    }

    /**
     * Handle creating a new configuration
     */
    private async _handleCreateConfiguration(data: any): Promise<void> {
        try {
            const newConfig = await this.configurationService.createConfiguration(data);

            // Send updated configurations
            await this._sendConfigurations();

            vscode.window.showInformationMessage(`Configuration "${data.title}" created successfully`);
        } catch (error) {
            console.error('VoiceHype: Error creating configuration:', error);
            this._view?.webview.postMessage({
                command: 'error',
                error: 'Failed to create configuration'
            });
        }
    }

    /**
     * Handle updating an existing configuration
     */
    private async _handleUpdateConfiguration(data: any): Promise<void> {
        try {
            await this.configurationService.updateConfiguration(data);

            // Send updated configurations
            await this._sendConfigurations();

            vscode.window.showInformationMessage(`Configuration "${data.title}" updated successfully`);
        } catch (error) {
            console.error('VoiceHype: Error updating configuration:', error);
            this._view?.webview.postMessage({
                command: 'error',
                error: 'Failed to update configuration'
            });
        }
    }

    /**
     * Handle deleting a configuration
     */
    private async _handleDeleteConfiguration(configId: string): Promise<void> {
        try {
            const configName = this.configurationService.getConfigurationName(configId);
            await this.configurationService.deleteConfiguration(configId);

            // Send updated configurations
            await this._sendConfigurations();

            vscode.window.showInformationMessage(`Configuration "${configName}" deleted successfully`);
        } catch (error) {
            console.error('VoiceHype: Error deleting configuration:', error);
            this._view?.webview.postMessage({
                command: 'error',
                error: 'Failed to delete configuration'
            });
        }
    }

    /**
     * Handle switching to a different configuration
     */
    private async _handleSwitchConfiguration(configId: string): Promise<void> {
        try {
            const configName = await this.configurationService.switchConfiguration(configId);

            // Send updated configurations
            await this._sendConfigurations();

            // Send configuration switched message with name for snackbar
            this._view?.webview.postMessage({
                command: 'configurationSwitched',
                data: {
                    configurationId: configId,
                    configurationName: configName
                }
            });

            // Force send updated options to reflect the new configuration
            await this._forceSendConfiguration(true);

        } catch (error) {
            console.error('VoiceHype: Error switching configuration:', error);
            this._view?.webview.postMessage({
                command: 'error',
                error: 'Failed to switch configuration'
            });
        }
    }

    /**
     * Handle refreshConfigAndTheme command from webview
     */
    private async _handleRefreshConfigAndTheme(): Promise<void> {
        if (!this._view) {
            console.log('VoiceHype: View not available for refreshConfigAndTheme');
            return;
        }

        // Send current configuration and theme to webview
        await this._forceSendConfiguration(true);

        // Send theme information
        const currentTheme = this._themeDetector.getCurrentTheme();
        this._view.webview.postMessage({
            command: 'themeChanged',
            data: {
                theme: currentTheme
            }
        });
    }


    /**
     * Send What's New status to the webview
     */
    private async _sendWhatsNewStatus(): Promise<void> {
        if (!this._view) {
            return;
        }

        try {
            const hasBeenShown = this.configurationService.getWhatsNewStatus('1.4.1');

            this._view.webview.postMessage({
                command: 'whatsNewStatus',
                data: {
                    hasBeenShown
                }
            });
        } catch (error) {
            console.error('VoiceHype: Error sending What\'s New status:', error);
        }
    }

    /**
     * Handle marking What's New as seen
     */
    private async _handleMarkWhatsNewSeen(version: string): Promise<void> {
        try {
            await this.configurationService.markWhatsNewSeen(version);
        } catch (error) {
            console.error('VoiceHype: Error marking What\'s New as seen:', error);
        }
    }
}

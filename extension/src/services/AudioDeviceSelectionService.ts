import * as vscode from 'vscode';
import { AudioDeviceManager } from '../utils/audioDeviceManager';

/**
 * Service for managing audio device selection
 */
export class AudioDeviceSelectionService {
    private disposables: vscode.Disposable[] = [];
    private deviceManager: AudioDeviceManager;

    constructor() {
        this.deviceManager = AudioDeviceManager.getInstance();
    }

    /**
     * Register the audio device selection command
     */
    public registerCommands(): vscode.Disposable[] {
        // Register the command to show the audio device selection menu
        const command = vscode.commands.registerCommand('voicehype.selectAudioDevice', async () => {
            await this.deviceManager.showDeviceSelectionMenu();
        });
        
        this.disposables.push(command);
        return this.disposables;
    }

    /**
     * Dispose of all registered disposables
     */
    public dispose(): void {
        for (const disposable of this.disposables) {
            disposable.dispose();
        }
        this.disposables = [];
    }
}

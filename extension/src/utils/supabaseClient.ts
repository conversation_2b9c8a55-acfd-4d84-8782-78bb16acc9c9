import * as vscode from 'vscode';
import { PromptFormatter } from './PromptFormatter';
import { IConfigurationService, TranscriptionError } from '../models/interfaces';

let configService: IConfigurationService | null = null;

// Initialize the config service (call this during extension activation)
export function initializeConfigService(service: IConfigurationService) {
    configService = service;
}

// Get API configuration asynchronously 
async function getApiConfigAsync(): Promise<{ apiKey: string; apiUrl: string }> {
    if (!configService) {
        throw new Error('ConfigurationService not initialized');
    }

    const apiKey = await configService.getApiKeyAsync();
    if (!apiKey) {
        throw new Error('API key not found. Please add your VoiceHype API key in settings.');
    }

    return {
        apiKey,
        apiUrl: 'https://supabase.voicehype.ai/functions/v1'
    };
}

// Helper function to make authenticated requests
export async function makeAuthenticatedRequest(
    endpoint: string,
    body: any,
    options: RequestInit = {}
): Promise<Response> {
    const config = await getApiConfigAsync();
    const baseUrl = config.apiUrl;

    // Remove the leading slash if present in the endpoint
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint.substring(1) : endpoint;

    // For Supabase Edge Functions, we need to include the endpoint in the URL
    const fullUrl = `${baseUrl}/${cleanEndpoint}`;

    try {
        // Prepare headers with request ID for tracing
        const requestId = `req-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;

        // Create base header values
        const headerEntries: Record<string, string> = {
            'apikey': config.apiKey,
            'Authorization': `Bearer ${config.apiKey}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'X-Request-ID': requestId,
            'X-Client-Info': 'voicehype-vscode-extension'
        };

        // Add audio duration header if provided in metadata
        if (body?.metadata?.accurateDuration) {
            headerEntries['Audio-Duration-Seconds'] = body.metadata.accurateDuration.toString();
            console.log(`VoiceHype: Including Audio-Duration-Seconds header: ${body.metadata.accurateDuration}`);
        }

        // Merge with any headers from options
        const headersForLogging = { ...headerEntries };

        // Create headers object for fetch
        const headers = new Headers(headerEntries);

        // Add any additional headers from options
        if (options.headers) {
            const optionHeaders = options.headers as Record<string, string>;
            Object.keys(optionHeaders).forEach(key => {
                headers.append(key, optionHeaders[key]);
                headersForLogging[key] = optionHeaders[key];
            });
        }

        console.log(`VoiceHype: Making request to: ${fullUrl}`, {
            endpoint,
            requestId,
            headers: {
                // Mask API key for security in logs
                ...headersForLogging,
                apikey: `${config.apiKey.substring(0, 5)}...${config.apiKey.substring(config.apiKey.length - 5)}`,
                Authorization: `Bearer ${config.apiKey.substring(0, 5)}...${config.apiKey.substring(config.apiKey.length - 5)}`
            },
            bodyLength: body ? JSON.stringify(body).length : 0,
            method: options.method || 'POST'
        });

        const response = await fetch(fullUrl, {
            method: 'POST',
            body: JSON.stringify(body),
            ...options,
            // Override options.headers with our Headers object
            headers: headers
        });

        // Log response headers
        const responseHeaders: Record<string, string> = {};
        response.headers.forEach((value, key) => {
            responseHeaders[key] = value;
        });

        console.log(`VoiceHype: Response received:`, {
            status: response.status,
            statusText: response.statusText,
            headers: responseHeaders,
            requestId
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('VoiceHype: Error response:', {
                status: response.status,
                statusText: response.statusText,
                body: errorText,
                requestId
            });

            let errorMessage;
            try {
                const errorJson = JSON.parse(errorText);
                if (typeof errorJson === 'object') {
                    // Stringify the error object to prevent [object Object] display
                    errorMessage = JSON.stringify(errorJson.error || errorJson.message || errorJson);
                } else {
                    errorMessage = errorJson.error || errorJson.message || 'Unknown error';
                }
            } catch {
                errorMessage = errorText || response.statusText;
            }

            if (response.status === 401) {
                throw new Error(`Authentication failed (${response.status}): Your API key may be invalid, disabled, or expired. Please check your API key in the settings.`);
            } else if (response.status === 400) {
                throw new Error(`Request failed (${response.status}): ${errorMessage}`);
            } else {
                throw new Error(`Request failed (${response.status}): ${errorMessage}`);
            }
        }

        return response;
    } catch (error: any) {
        console.error('VoiceHype: API request failed:', {
            url: fullUrl,
            endpoint,
            error: error.message,
            stack: error.stack
        });

        if (error.cause?.code === 'ENOTFOUND') {
            throw new Error('Could not connect to VoiceHype API. Please check your internet connection.');
        }

        throw error;
    }
}

// Add type for transcription response
export interface TranscriptionResponse {
    transcription: string;
    duration: number;
    metadata: {
        audioSize: number;
        estimatedDuration: number;
        actualDuration: number;
        model: string;
        service: string;
        isTranslation?: boolean;
    };
}

// Transcription function
export async function transcribeAudio(
    audioUrl: string,
    model: string = 'whisper-1',
    service: string = 'lemonfox',
    language: string = 'en',
    translate: boolean = false,
    realtime: boolean = false,
    metadata?: {
        accurateDuration?: number;
        [key: string]: any;
    }
): Promise<TranscriptionResponse> {
    try {
        // If service is AssemblyAI, ignore translate parameter
        if (service === 'assemblyai' && translate) {
            console.log('VoiceHype: Ignoring translate parameter for AssemblyAI service');
            translate = false;
        }

        console.log('VoiceHype: Transcribing audio:', {
            audioUrlType: audioUrl.startsWith('data:') ? 'data-url' : 'raw-base64',
            audioUrlLength: audioUrl.length,
            model,
            service,
            language,
            translate,
            realtime,
            hasAccurateDuration: !!metadata?.accurateDuration
        });

        // Validate inputs
        if (!audioUrl) {
            throw new Error('No audio URL provided');
        }

        // If translate is true, force service to lemonfox and model to whisper-1
        if (translate) {
            if (service !== 'lemonfox') {
                console.log('Forcing service to lemonfox for translation');
                service = 'lemonfox';
            }
            if (model !== 'whisper-1') {
                console.log('Forcing model to whisper-1 for translation');
                model = 'whisper-1';
            }
        }

        // Prepare request body
        const requestBody: any = {
            audioUrl: audioUrl, // This is now a raw base64 string, not a data URL
            model,
            service,
            translate, // Add the translate parameter
            realtime // Add the realtime parameter
        };

        // Add any metadata (including accurate duration) if provided
        if (metadata) {
            requestBody.metadata = metadata;

            if (metadata.accurateDuration) {
                console.log(`VoiceHype: Sending accurate duration: ${metadata.accurateDuration.toFixed(3)} seconds`);
            }
        }

        // Only include language parameter if it's not 'auto' and not empty
        // Also, if translate is true, we don't need to send language
        if (!translate && !(service === 'lemonfox' && (language === 'auto' || language === ''))) {
            requestBody.language = language;
            console.log('VoiceHype: Using specified language:', language);
        } else if (translate) {
            console.log('VoiceHype: Using translation endpoint (language parameter not needed)');
        } else {
            console.log('VoiceHype: Using auto language detection (omitting language parameter)');
        }

        // Validate model based on service
        if (service === 'lemonfox') {
            if (!['whisper-1', 'gpt-4o-mini-transcribe', 'gpt-4o-transcribe'].includes(model)) {
                console.warn(`Invalid model "${model}" for OpenAI service. Using "whisper-1" instead.`);
                model = 'whisper-1';
            }

            // Real-time is only supported for gpt-4o models
            if (realtime && !model.includes('gpt-4o')) {
                console.warn(`Real-time transcription is only supported for GPT-4o models. Disabling real-time.`);
                realtime = false;
            }
        } else if (service === 'assemblyai') {
            // Validate the AssemblyAI model
            if (!['best', 'nano'].includes(model)) {
                console.warn(`Invalid model "${model}" for AssemblyAI service. Using "best" instead.`);
                model = 'best';
            }

            // Real-time is only supported for the 'best' model in AssemblyAI
            if (realtime && model !== 'best') {
                console.warn(`Real-time transcription is only supported for AssemblyAI's 'best' model. Disabling real-time.`);
                realtime = false;
            }
        }

        // Make the API request
        const response = await makeAuthenticatedRequest('transcribe', requestBody);

        // Log the response status
        console.log('Response status:', response.status);

        // Parse the response
        const responseText = await response.text();
        console.log('Response text length:', responseText.length);

        let data;
        try {
            data = JSON.parse(responseText);
            console.log('Parsed response data:', {
                hasTranscription: !!data.data?.transcription,
                transcriptionLength: data.data?.transcription?.length || 0,
                duration: data.data?.duration,
                hasMetadata: !!data.data?.metadata
            });
        } catch (error) {
            console.error('Failed to parse response JSON:', responseText.substring(0, 200) + '...');
            throw new Error('Invalid response format from transcription service');
        }

        // Check if the response has the expected structure
        if (!data.data?.transcription) {
            console.error('No transcription in response:', data);
            throw new Error('No transcription in response');
        }

        // Return the data in the expected format
        return {
            transcription: data.data.transcription,
            duration: data.data.duration || 0,
            metadata: data.data.metadata || {
                audioSize: 0,
                estimatedDuration: 0,
                actualDuration: data.data.duration || 0,
                model,
                service
            }
        };
    } catch (error: any) {
        console.error('VoiceHype: Transcription failed:', {
            error: error.message,
            stack: error.stack,
            audioUrlType: audioUrl.startsWith('data:') ? 'data-url' : 'raw-base64',
            audioUrlLength: audioUrl.length
        });
        throw new Error(`Transcription failed: ${error.message}`);
    }
}

// Optimization function
export async function optimizeText(
    text: string,
    model: string = 'gpt-4o-mini',
    customPrompt: string = 'Optimize this text for clarity and conciseness:'
): Promise<string> {
    try {
        console.log('VoiceHype: Optimizing text with model:', model);
        console.log('VoiceHype: Text length:', text.length);
        console.log('VoiceHype: Using custom prompt:', customPrompt);

        // Validate input
        if (!text || text.trim().length === 0) {
            console.error('VoiceHype: Empty text provided for optimization');
            return text; // Return original text if empty
        }

        // Generate a request ID to track this request in logs
        const requestId = `vh-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;
        console.log(`VoiceHype: Making optimization request with ID: ${requestId}`);

        // Log the full request we're sending
        const requestBody = {
            text,
            model,
            customPrompt, // Changed from 'prompt' to 'customPrompt'
            debug: true,
            requestId
        };

        console.log('VoiceHype: Full optimization request:', {
            textLength: text.length,
            model,
            customPromptLength: customPrompt.length,
            customPromptPreview: customPrompt,
            endpoint: 'optimize',
            requestId
        });

        const response = await makeAuthenticatedRequest('optimize', requestBody);

        console.log('VoiceHype: Optimize response status:', response.status);
        console.log('VoiceHype: Response headers:', response.headers);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('VoiceHype: Optimization API error:', errorText);
            console.error(`VoiceHype: Check Supabase logs with filter: ${requestId}`);
            throw new Error(`Optimization failed: ${errorText}`);
        }

        // Get the raw response text first for debugging
        const responseText = await response.text();
        console.log('VoiceHype: Raw optimization response:', responseText.substring(0, 200) + '...');

        let data;
        try {
            // Server guarantees JSON response, so parse directly
            const parsedResponse = JSON.parse(responseText);
            console.log('VoiceHype: Successfully parsed JSON response');

            // Handle the guaranteed format: {"data":{"optimizedText":"...","metadata":{...}}}
            if (parsedResponse.data) {
                data = parsedResponse.data;
                console.log('VoiceHype: Using data from response.data');
            } else {
                data = parsedResponse;
                console.log('VoiceHype: Using direct response data');
            }
        } catch (e) {
            console.error('VoiceHype: Failed to parse JSON response:', e);
            console.error('VoiceHype: Could not parse JSON from response, returning original text');
            return text;
        }

        console.log('VoiceHype: Optimization response data:', {
            hasOptimizedText: !!data.optimizedText,
            optimizedTextLength: data.optimizedText ? data.optimizedText.length : 0,
            hasMetadata: !!data.metadata
        });

        // Server guarantees optimizedText in data.optimizedText
        const optimizedText = data.optimizedText;

        if (!optimizedText || typeof optimizedText !== 'string') {
            console.error('VoiceHype: No optimized text found in response structure:', JSON.stringify(data, null, 2));
            throw new Error('No optimized text received from server');
        }

        return optimizedText;
    } catch (error) {
        console.error('VoiceHype: Error in optimizeText:', error);
        // Re-throw the error instead of returning original text
        throw error;
    }
}

// New function to optimize text using structured messages
export async function optimizeWithMessages(
    text: string,
    model: string = 'gpt-4o-mini',
    messages: Array<{ role: string, content: string }>
): Promise<string> {
    try {
        console.log('VoiceHype: Optimizing text with structured messages');
        console.log('VoiceHype: Using model:', model);
        console.log('VoiceHype: Text length:', text.length);
        console.log('VoiceHype: Message count:', messages.length);

        // Validate input
        if (!text || text.trim().length === 0) {
            console.error('VoiceHype: Empty text provided for optimization');
            return text; // Return original text if empty
        }

        // Generate a request ID to track this request in logs
        const requestId = `vh-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;

        // Prepare the request body with client messages
        const requestBody = {
            text,  // Include original text for backward compatibility
            model,
            clientMessages: messages,
            debug: true,
            requestId,
            source: 'vscode-extension'
        };

        console.log('VoiceHype: Optimization request with messages:', {
            textLength: text.length,
            model,
            messageCount: messages.length,
            systemMessageLength: messages[0]?.content?.length || 0,
            systemMessagePreview: messages[0]?.content?.substring(0, 100) || '' +
                (messages[0]?.content?.length > 100 ? '...' : ''),
            endpoint: 'optimize',
            requestId
        });

        // Log the entire messages array
        console.log('VoiceHype: Full messages array:', JSON.stringify(messages, null, 2));

        const response = await makeAuthenticatedRequest('optimize', requestBody);

        console.log('VoiceHype: Optimize response status:', response.status);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('VoiceHype: Optimization API error:', errorText);
            console.error(`VoiceHype: Check Supabase logs with filter: ${requestId}`);
            return text; // Return original text on error
        }

        // Get the raw response text first for debugging
        const responseText = await response.text();
        console.log('VoiceHype: Raw optimization response:', responseText.substring(0, 200) + '...');

        let data;
        try {
            // Server guarantees JSON response, so parse directly
            const parsedResponse = JSON.parse(responseText);
            console.log('VoiceHype: Successfully parsed JSON response');

            // Handle the guaranteed format: {"data":{"optimizedText":"...","metadata":{...}}}
            if (parsedResponse.data) {
                data = parsedResponse.data;
                console.log('VoiceHype: Using data from response.data');
            } else {
                data = parsedResponse;
                console.log('VoiceHype: Using direct response data');
            }
        } catch (e) {
            console.error('VoiceHype: Failed to parse JSON response:', e);
            console.error('VoiceHype: Could not parse JSON from response, returning original text');
            return text;
        }

        console.log('VoiceHype: Optimization response data:', {
            hasOptimizedText: !!data.optimizedText,
            optimizedTextLength: data.optimizedText ? data.optimizedText.length : 0,
            hasMetadata: !!data.metadata
        });

        // Server guarantees optimizedText in data.optimizedText
        const optimizedText = data.optimizedText;

        if (!optimizedText || typeof optimizedText !== 'string') {
            console.error('VoiceHype: No optimized text found in response structure:', JSON.stringify(data, null, 2));
            return text; // Return original text if no optimized text found
        }

        return optimizedText;
    } catch (error) {
        console.error('VoiceHype: Error in optimizeWithMessages:', error);
        throw error; // Re-throw the error to be handled by the caller
    }
}

// Combined transcribe and optimize function
export async function transcribeAndOptimize(
    audioUrl: string,
    transcriptionModel: string = 'whisper-1',
    optimizationModel: string = 'gpt-4o-mini',
    customPrompt: string = 'Optimize this transcription for clarity and conciseness:',
    service: string = 'lemonfox',
    language: string = 'en',
    translate: boolean = false,
    realtime: boolean = false,
    voiceCommandsEnabled: boolean = true,
    metadata?: {
        accurateDuration?: number;
        [key: string]: any;
    }
): Promise<{ transcription: string; optimizedText: string; duration: number }> {
    console.log('VoiceHype: Starting transcribe and optimize:', {
        transcriptionModel,
        optimizationModel,
        service,
        language,
        translate,
        realtime,
        customPromptLength: customPrompt.length,
        customPromptPreview: customPrompt.substring(0, 100) + (customPrompt.length > 100 ? '...' : ''),
        hasAccurateDuration: !!metadata?.accurateDuration
    });

    try {
        // Step 1: Transcribe the audio
        let transcriptionResult;
        try {
            transcriptionResult = await transcribeAudio(
                audioUrl,
                transcriptionModel,
                service,
                language,
                translate,
                realtime,
                metadata
            );
            console.log('VoiceHype: Transcription result:', {
                hasTranscription: !!transcriptionResult.transcription,
                transcriptionLength: transcriptionResult.transcription.length,
                duration: transcriptionResult.duration,
                isTranslation: transcriptionResult.metadata?.isTranslation
            });
        } catch (transcribeError: any) {
            console.error('VoiceHype: Transcription failed:', transcribeError);
            // Convert to TranscriptionError if not already
            throw new TranscriptionError('transcription', transcribeError);
        }

        // Only proceed with optimization if transcription succeeded
        if (!transcriptionResult?.transcription) {
            throw new TranscriptionError('transcription', new Error('No transcription result'));
        }

        // Step 2: Optimize the transcription
        let optimizedText = '';
        try {
            console.log('VoiceHype: Using optimizeWithMessages with model:', optimizationModel);
            console.log('VoiceHype: Using custom prompt:', customPrompt.substring(0, 100) + (customPrompt.length > 100 ? '...' : ''));

            // Create structured messages using the PromptFormatter
            const promptFormatter = new PromptFormatter();
            const clientMessages = promptFormatter.createOptimizationMessages(transcriptionResult.transcription, customPrompt, voiceCommandsEnabled);

            optimizedText = await optimizeWithMessages(
                transcriptionResult.transcription,
                optimizationModel,
                clientMessages
            );
            console.log('VoiceHype: Optimization result:', {
                hasOptimizedText: !!optimizedText,
                optimizedTextLength: optimizedText.length
            });
        } catch (optimizeError: any) {
            console.error('VoiceHype: Optimization failed:', optimizeError);
            // Return transcription with empty optimized text instead of throwing error
            console.log('VoiceHype: Returning transcription without optimization due to failure');
            optimizedText = '';
        }

        // If optimization returned empty, still return the transcription
        if (!optimizedText) {
            console.log('VoiceHype: Optimization returned empty result, using original transcription');
        }

        // Use the accurate duration if available, otherwise use the API-provided duration
        const accurateDuration = metadata?.accurateDuration || transcriptionResult.duration;

        return {
            transcription: transcriptionResult.transcription,
            optimizedText,
            duration: accurateDuration
        };
    } catch (error: any) {
        // Re-throw TranscriptionError as is, wrap other errors
        if (error instanceof TranscriptionError) {
            throw error;
        }
        console.error('VoiceHype: Unexpected error in transcribe and optimize:', error);
        throw new TranscriptionError('transcription', error);
    }
}
/**
 * Utility for cleaning error messages while preserving original API messages
 */
export function cleanErrorMessage(errorMessage: string): string {
    if (!errorMessage) {
        return 'An unknown error occurred';
    }

    // First, try JSON parsing for edge function error format:
    // JSON.stringify({ error: { code, message, details } })
    try {
        // Try to parse as JSON first
        const parsed = JSON.parse(errorMessage);
        if (parsed.error) {
            const { message, details } = parsed.error;
            if (message && details) {
                return `${message}: ${details}`;
            }
            return message || details || 'An unknown error occurred';
        }
    } catch (e) {
        // JSON parsing failed, fall back to regex extraction
    }

    // Extract the actual message content from JSON while preserving the original text
    let cleanMessage = errorMessage;

    // Try to extract from various JSON formats
    const jsonPatterns = [
        /"message"\s*:\s*"([^"]*)"/,
        /"error"\s*:\s*"([^"]*)"/,
        /"details"\s*:\s*"([^"]*)"/,
        /"msg"\s*:\s*"([^"]*)"/
    ];

    for (const pattern of jsonPatterns) {
        const match = errorMessage.match(pattern);
        if (match && match[1]) {
            cleanMessage = match[1];
            break;
        }
    }

    // Extract from nested JSON structures
    const nestedMessageRegex = /"message"\s*:\s*"([^"]*)".*"details"\s*:\s*"([^"]*)"/;
    const nestedMatch = errorMessage.match(nestedMessageRegex);
    if (nestedMatch && nestedMatch[2]) {
        cleanMessage = `${nestedMatch[1]}: ${nestedMatch[2]}`;
    }

    // Clean up JSON formatting but preserve actual content
    cleanMessage = cleanMessage
        .replace(/\{[^{}]*\}/g, '')  // Remove JSON objects but keep their content if extracted above
        .replace(/\[[^\[\]]*\]/g, '') // Remove arrays
        .replace(/\\"/g, '"')        // Unescape quotes
        .replace(/\\n/g, ' ')        // Replace newlines with spaces
        .replace(/\\t/g, ' ')        // Replace tabs with spaces
        .trim();

    // Extract HTTP status and message without hard-coding responses
    const httpStatusRegex = /(\d{3})\s*:\s*(.+?)(?:\s*[\{\[]|$)/;
    const httpMatch = cleanMessage.match(httpStatusRegex);
    if (httpMatch) {
        const statusText = httpMatch[2].trim();
        return `${statusText}`;
    }

    // Extract the meaningful part after common prefixes
    const prefixPatterns = [
        /^error:\s*/i,
        /^transcription failed:\s*/i,
        /^optimization failed:\s*/i,
        /^voicehype:\s*/i,
        /^request failed\s*\([^)]*\):\s*/i,
        /^\s*-\s*/,
        /^api error:\s*/i,
        /^http error:\s*/i
    ];

    for (const pattern of prefixPatterns) {
        cleanMessage = cleanMessage.replace(pattern, '');
    }

    // Clean up excessive whitespace
    cleanMessage = cleanMessage.replace(/\s+/g, ' ').trim();

    return cleanMessage || 'An unexpected error occurred';
}
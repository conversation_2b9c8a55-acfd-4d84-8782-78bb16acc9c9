import * as os from 'os';
import { EventEmitter } from 'events';
import { Readable } from 'stream';
import { AudifyMicrophone } from './audifyMicrophone';

/**
 * Compatibility layer for the microphone implementation.
 * This class provides the same interface as the old Microphone class but
 * uses Audify JS internally for all platforms.
 */
export class MicrophoneCompat extends EventEmitter {
    private mic: AudifyMicrophone;

    /**
     * Create a new MicrophoneCompat instance
     * @param options Audio recording options
     */
    constructor(options: any = {}) {
        super();
        
        console.log('MicrophoneCompat: Constructor called');
        console.log('MicrophoneCompat: Platform:', os.platform());
        console.log('MicrophoneCompat: Options:', JSON.stringify(options));
        
        // Create the Audify-based microphone
        this.mic = new AudifyMicrophone(options);
        
        // Forward all events from the underlying microphone
        this.mic.on('error', (err) => this.emit('error', err));
        this.mic.on('pause', () => this.emit('pause'));
        this.mic.on('resume', () => this.emit('resume'));
        this.mic.on('close', () => this.emit('close'));
    }
    
    /**
     * Start recording audio from the microphone
     * @returns A readable stream of audio data
     */
    public startRecording(): Readable {
        console.log('MicrophoneCompat: startRecording called');
        return this.mic.startRecording();
    }
    
    /**
     * Stop recording audio
     */
    public stopRecording(): void {
        console.log('MicrophoneCompat: stopRecording called');
        this.mic.stopRecording();
    }
    
    /**
     * Pause recording audio
     */
    public pauseRecording(): void {
        console.log('MicrophoneCompat: pauseRecording called');
        this.mic.pauseRecording();
    }
    
    /**
     * Resume recording audio
     * @returns The readable stream
     */
    public resumeRecording(): Readable {
        console.log('MicrophoneCompat: resumeRecording called');
        return this.mic.resumeRecording();
    }
    
    /**
     * Check if recording is paused
     */
    public isPausedState(): boolean {
        return this.mic.isPausedState();
    }
    
    /**
     * Get available audio devices
     * @returns Array of audio devices
     */
    async getDevices(): Promise<Array<{ id: string; name: string }>> {
        return this.mic.getDevices();
    }
}

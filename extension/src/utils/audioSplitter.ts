import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';

export interface SplitOptions {
  maxChunkSizeMB?: number;
  maxChunkDurationMs?: number;
  detectSilence?: boolean;
  overlapMs?: number;
  service?: 'assemblyai' | 'whisper';
}

export interface AudioChunk {
  path: string;
  startTime: number;
  duration: number;
  size: number;
}

export class AudioSplitter {
  private static instance: AudioSplitter;
  
  public static getInstance(): AudioSplitter {
    if (!AudioSplitter.instance) {
      AudioSplitter.instance = new AudioSplitter();
    }
    return AudioSplitter.instance;
  }

  /**
   * Split audio file into chunks based on size or duration
   * @param inputPath Path to input audio file (WAV format)
   * @param options Splitting options
   * @returns Array of audio chunks
   */
  async splitAudioFile(
    inputPath: string,
    options: SplitOptions = {}
  ): Promise<AudioChunk[]> {
    const {
      maxChunkSizeMB = 25,
      maxChunkDurationMs = 300000, // 5 minutes
      detectSilence = false,
      overlapMs = 1000, // 1 second overlap
      service = 'whisper'
    } = options;

    // Service-specific limits
    const serviceLimits = {
      assemblyai: 100,
      whisper: 25
    };
    
    const actualMaxSizeMB = serviceLimits[service] || maxChunkSizeMB;

    try {
      // Read the input file
      const audioBuffer = fs.readFileSync(inputPath);
      
      // Parse WAV header
      const wavInfo = this.parseWavHeader(audioBuffer);
      if (!wavInfo) {
        throw new Error('Invalid WAV file format');
      }

      // Calculate total duration
      const totalDuration = this.calculateDuration(audioBuffer, wavInfo);
      const totalSizeMB = audioBuffer.length / (1024 * 1024);

      console.log(`VoiceHype: Splitting audio file - Size: ${totalSizeMB.toFixed(2)}MB, Duration: ${totalDuration.toFixed(2)}s`);

      // Determine chunk strategy
      let chunkCount: number;
      let chunkDuration: number;

      if (totalSizeMB <= actualMaxSizeMB && totalDuration * 1000 <= maxChunkDurationMs) {
        // No splitting needed
        return [{
          path: inputPath,
          startTime: 0,
          duration: totalDuration,
          size: audioBuffer.length
        }];
      }

      // Calculate optimal chunk count
      const sizeBasedChunks = Math.ceil(totalSizeMB / actualMaxSizeMB);
      const durationBasedChunks = Math.ceil(totalDuration * 1000 / maxChunkDurationMs);
      
      chunkCount = Math.max(sizeBasedChunks, durationBasedChunks);
      chunkDuration = totalDuration / chunkCount;

      console.log(`VoiceHype: Creating ${chunkCount} chunks of ~${chunkDuration.toFixed(2)}s each`);

      // Create chunks
      const chunks: AudioChunk[] = [];
      const tempDir = os.tmpdir();
      const fileName = path.basename(inputPath, path.extname(inputPath));

      for (let i = 0; i < chunkCount; i++) {
        const startTime = Math.max(0, i * chunkDuration - (i > 0 ? overlapMs / 1000 : 0));
        const endTime = Math.min(totalDuration, (i + 1) * chunkDuration + (i < chunkCount - 1 ? overlapMs / 1000 : 0));
        const actualDuration = endTime - startTime;

        // Calculate byte positions
        const startByte = Math.floor(startTime * wavInfo.sampleRate * wavInfo.channels * 2) + 44;
        const endByte = Math.floor(endTime * wavInfo.sampleRate * wavInfo.channels * 2) + 44;

        // Extract chunk data
        const chunkData = audioBuffer.slice(startByte, endByte);
        
        // Create new WAV header for chunk
        const chunkWav = this.createWavHeader(chunkData.length, wavInfo);
        const chunkBuffer = Buffer.concat([chunkWav, chunkData]);

        // Write chunk file
        const chunkPath = path.join(tempDir, `${fileName}_chunk_${i + 1}.wav`);
        fs.writeFileSync(chunkPath, chunkBuffer);

        chunks.push({
          path: chunkPath,
          startTime,
          duration: actualDuration,
          size: chunkBuffer.length
        });
      }

      console.log(`VoiceHype: Created ${chunks.length} audio chunks`);
      return chunks;

    } catch (error) {
      console.error('VoiceHype: Audio splitting failed:', error);
      throw new Error(`Audio splitting failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Parse WAV header to extract audio parameters
   */
  private parseWavHeader(buffer: Buffer): { sampleRate: number; channels: number; bitsPerSample: number } | null {
    if (buffer.length < 44) {return null;}
    
    // Check RIFF header
    if (buffer.toString('ascii', 0, 4) !== 'RIFF') {return null;}
    if (buffer.toString('ascii', 8, 12) !== 'WAVE') {return null;}
    
    // Find fmt chunk
    let offset = 12;
    while (offset < buffer.length - 8) {
      const chunkId = buffer.toString('ascii', offset, offset + 4);
      const chunkSize = buffer.readUInt32LE(offset + 4);
      
      if (chunkId === 'fmt ') {
        const audioFormat = buffer.readUInt16LE(offset + 8);
        if (audioFormat !== 1) {return null;} // PCM only
        
        const channels = buffer.readUInt16LE(offset + 10);
        const sampleRate = buffer.readUInt32LE(offset + 12);
        const bitsPerSample = buffer.readUInt16LE(offset + 22);
        
        return { sampleRate, channels, bitsPerSample };
      }
      
      offset += 8 + chunkSize;
    }
    
    {return null;}
  }

  /**
   * Calculate duration from WAV data
   */
  private calculateDuration(buffer: Buffer, wavInfo: { sampleRate: number; channels: number; bitsPerSample: number }): number {
    // Find data chunk
    let offset = 12;
    while (offset < buffer.length - 8) {
      const chunkId = buffer.toString('ascii', offset, offset + 4);
      const chunkSize = buffer.readUInt32LE(offset + 4);
      
      if (chunkId === 'data') {
        const dataSize = chunkSize;
        const bytesPerSecond = wavInfo.sampleRate * wavInfo.channels * (wavInfo.bitsPerSample / 8);
        return dataSize / bytesPerSecond;
      }
      
      offset += 8 + chunkSize;
    }
    
    return 0;
  }

  /**
   * Create new WAV header for chunk
   */
  private createWavHeader(dataSize: number, wavInfo: { sampleRate: number; channels: number; bitsPerSample: number }): Buffer {
    const header = Buffer.alloc(44);
    
    // RIFF header
    header.write('RIFF', 0);
    header.writeUInt32LE(36 + dataSize, 4);
    header.write('WAVE', 8);
    
    // fmt chunk
    header.write('fmt ', 12);
    header.writeUInt32LE(16, 16);
    header.writeUInt16LE(1, 20); // PCM
    header.writeUInt16LE(wavInfo.channels, 22);
    header.writeUInt32LE(wavInfo.sampleRate, 24);
    header.writeUInt32LE(wavInfo.sampleRate * wavInfo.channels * (wavInfo.bitsPerSample / 8), 28);
    header.writeUInt16LE(wavInfo.channels * (wavInfo.bitsPerSample / 8), 32);
    header.writeUInt16LE(wavInfo.bitsPerSample, 34);
    
    // data chunk
    header.write('data', 36);
    header.writeUInt32LE(dataSize, 40);
    
    return header;
  }

  /**
   * Estimate number of chunks needed
   */
  estimateChunkCount(
    fileSizeMB: number,
    durationSeconds: number,
    service: 'assemblyai' | 'whisper' = 'whisper'
  ): number {
    const serviceLimits = {
      assemblyai: 100,
      whisper: 25
    };
    
    const maxSizeMB = serviceLimits[service];
    const sizeBasedChunks = Math.ceil(fileSizeMB / maxSizeMB);
    const durationBasedChunks = Math.ceil(durationSeconds / 300); // 5 minutes max
    
    return Math.max(sizeBasedChunks, durationBasedChunks);
  }

  /**
   * Clean up temporary chunk files
   */
  cleanupChunks(chunks: AudioChunk[]): void {
    chunks.forEach(chunk => {
      if (fs.existsSync(chunk.path)) {
        fs.unlinkSync(chunk.path);
      }
    });
  }
}

export default AudioSplitter;
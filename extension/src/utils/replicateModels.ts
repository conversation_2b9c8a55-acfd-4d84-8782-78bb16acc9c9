import * as vscode from 'vscode';
import * as fs from 'fs';
import { transcribeAudio, optimizeText, transcribeAndOptimize } from './supabaseClient';

// Queue for managing concurrent requests
class RequestQueue {
    private queue: Array<() => Promise<any>> = [];
    private isProcessing = false;

    async add<T>(task: () => Promise<T>): Promise<T> {
        return new Promise((resolve, reject) => {
            this.queue.push(async () => {
                try {
                    const result = await task();
                    resolve(result);
                } catch (error) {
                    reject(error);
                }
            });
            this.processQueue();
        });
    }

    private async processQueue() {
        if (this.isProcessing || this.queue.length === 0) {return;}

        this.isProcessing = true;
        const task = this.queue.shift();

        if (task) {
            try {
                await task();
            } catch (error) {
                console.error('Task error:', error);
            }

            this.isProcessing = false;
            this.processQueue();
        }
    }
}

const requestQueue = new RequestQueue();

interface WhisperSegment {
    text: string;
    id: number;
    start: number;
    end: number;
}

type WhisperOutput = {
    segments?: WhisperSegment[];
    text?: string;
} | string;

// Language support configurations
export const ASSEMBLYAI_BEST_LANGUAGES = [
    { label: 'Global English', value: 'en' },
    { label: 'Australian English', value: 'en_au' },
    { label: 'British English', value: 'en_uk' },
    { label: 'US English', value: 'en_us' },
    { label: 'Spanish', value: 'es' },
    { label: 'French', value: 'fr' },
    { label: 'German', value: 'de' },
    { label: 'Italian', value: 'it' },
    { label: 'Portuguese', value: 'pt' },
    { label: 'Dutch', value: 'nl' },
    { label: 'Hindi', value: 'hi' },
    { label: 'Japanese', value: 'ja' },
    { label: 'Chinese', value: 'zh' },
    { label: 'Finnish', value: 'fi' },
    { label: 'Korean', value: 'ko' },
    { label: 'Polish', value: 'pl' },
    { label: 'Russian', value: 'ru' },
    { label: 'Turkish', value: 'tr' },
    { label: 'Ukrainian', value: 'uk' },
    { label: 'Vietnamese', value: 'vi' }
];

export const ASSEMBLYAI_NANO_LANGUAGES = [
    { label: 'Global English', value: 'en' },
    { label: 'Australian English', value: 'en_au' },
    { label: 'British English', value: 'en_uk' },
    { label: 'American English', value: 'en_us' },
    { label: 'Spanish', value: 'es' },
    { label: 'French', value: 'fr' },
    { label: 'German', value: 'de' },
    { label: 'Italian', value: 'it' },
    { label: 'Portuguese', value: 'pt' },
    { label: 'Dutch', value: 'nl' },
    { label: 'Afrikaans', value: 'af' },
    { label: 'Albanian', value: 'sq' },
    { label: 'Amharic', value: 'am' },
    { label: 'Arabic', value: 'ar' },
    { label: 'Armenian', value: 'hy' },
    { label: 'Assamese', value: 'as' },
    { label: 'Azerbaijani', value: 'az' },
    { label: 'Bashkir', value: 'ba' },
    { label: 'Basque', value: 'eu' },
    { label: 'Belarusian', value: 'be' },
    { label: 'Bengali', value: 'bn' },
    { label: 'Bosnian', value: 'bs' },
    { label: 'Breton', value: 'br' },
    { label: 'Bulgarian', value: 'bg' },
    { label: 'Burmese', value: 'my' },
    { label: 'Catalan', value: 'ca' },
    { label: 'Chinese', value: 'zh' },
    { label: 'Croatian', value: 'hr' },
    { label: 'Czech', value: 'cs' },
    { label: 'Danish', value: 'da' },
    { label: 'Estonian', value: 'et' },
    { label: 'Faroese', value: 'fo' },
    { label: 'Finnish', value: 'fi' },
    { label: 'Galician', value: 'gl' },
    { label: 'Georgian', value: 'ka' },
    { label: 'Greek', value: 'el' },
    { label: 'Gujarati', value: 'gu' },
    { label: 'Haitian', value: 'ht' },
    { label: 'Hausa', value: 'ha' },
    { label: 'Hawaiian', value: 'haw' },
    { label: 'Hindi', value: 'hi' },
    { label: 'Hungarian', value: 'hu' },
    { label: 'Icelandic', value: 'is' },
    { label: 'Indonesian', value: 'id' },
    { label: 'Japanese', value: 'ja' },
    { label: 'Javanese', value: 'jv' },
    { label: 'Kannada', value: 'kn' },
    { label: 'Kazakh', value: 'kk' },
    { label: 'Khmer', value: 'km' },
    { label: 'Korean', value: 'ko' },
    { label: 'Lao', value: 'lo' },
    { label: 'Latin', value: 'la' },
    { label: 'Latvian', value: 'lv' },
    { label: 'Lingala', value: 'ln' },
    { label: 'Lithuanian', value: 'lt' },
    { label: 'Luxembourgish', value: 'lb' },
    { label: 'Macedonian', value: 'mk' },
    { label: 'Malagasy', value: 'mg' },
    { label: 'Malay', value: 'ms' },
    { label: 'Malayalam', value: 'ml' },
    { label: 'Maltese', value: 'mt' },
    { label: 'Maori', value: 'mi' },
    { label: 'Marathi', value: 'mr' },
    { label: 'Mongolian', value: 'mn' },
    { label: 'Nepali', value: 'ne' },
    { label: 'Norwegian', value: 'no' },
    { label: 'Norwegian Nynorsk', value: 'nn' },
    { label: 'Occitan', value: 'oc' },
    { label: 'Panjabi', value: 'pa' },
    { label: 'Pashto', value: 'ps' },
    { label: 'Persian', value: 'fa' },
    { label: 'Polish', value: 'pl' },
    { label: 'Romanian', value: 'ro' },
    { label: 'Russian', value: 'ru' },
    { label: 'Sanskrit', value: 'sa' },
    { label: 'Serbian', value: 'sr' },
    { label: 'Shona', value: 'sn' },
    { label: 'Sindhi', value: 'sd' },
    { label: 'Sinhala', value: 'si' },
    { label: 'Slovak', value: 'sk' },
    { label: 'Slovenian', value: 'sl' },
    { label: 'Somali', value: 'so' },
    { label: 'Sundanese', value: 'su' },
    { label: 'Swahili', value: 'sw' },
    { label: 'Swedish', value: 'sv' },
    { label: 'Tagalog', value: 'tl' },
    { label: 'Tajik', value: 'tg' },
    { label: 'Tamil', value: 'ta' },
    { label: 'Tatar', value: 'tt' },
    { label: 'Telugu', value: 'te' },
    { label: 'Thai', value: 'th' },
    { label: 'Tibetan', value: 'bo' },
    { label: 'Turkish', value: 'tr' },
    { label: 'Turkmen', value: 'tk' },
    { label: 'Ukrainian', value: 'uk' },
    { label: 'Urdu', value: 'ur' },
    { label: 'Uzbek', value: 'uz' },
    { label: 'Vietnamese', value: 'vi' },
    { label: 'Welsh', value: 'cy' },
    { label: 'Yiddish', value: 'yi' },
    { label: 'Yoruba', value: 'yo' }
];

export const WHISPER_LANGUAGES = [
    { label: 'Afrikaans', value: 'af' },
    { label: 'Arabic', value: 'ar' },
    { label: 'Armenian', value: 'hy' },
    { label: 'Azerbaijani', value: 'az' },
    { label: 'Belarusian', value: 'be' },
    { label: 'Bosnian', value: 'bs' },
    { label: 'Bulgarian', value: 'bg' },
    { label: 'Catalan', value: 'ca' },
    { label: 'Chinese', value: 'zh' },
    { label: 'Croatian', value: 'hr' },
    { label: 'Czech', value: 'cs' },
    { label: 'Danish', value: 'da' },
    { label: 'Dutch', value: 'nl' },
    { label: 'English', value: 'en' },
    { label: 'Estonian', value: 'et' },
    { label: 'Finnish', value: 'fi' },
    { label: 'French', value: 'fr' },
    { label: 'Galician', value: 'gl' },
    { label: 'German', value: 'de' },
    { label: 'Greek', value: 'el' },
    { label: 'Hindi', value: 'hi' },
    { label: 'Hungarian', value: 'hu' },
    { label: 'Icelandic', value: 'is' },
    { label: 'Indonesian', value: 'id' },
    { label: 'Italian', value: 'it' },
    { label: 'Japanese', value: 'ja' },
    { label: 'Kannada', value: 'kn' },
    { label: 'Kazakh', value: 'kk' },
    { label: 'Korean', value: 'ko' },
    { label: 'Latvian', value: 'lv' },
    { label: 'Lithuanian', value: 'lt' },
    { label: 'Macedonian', value: 'mk' },
    { label: 'Malay', value: 'ms' },
    { label: 'Marathi', value: 'mr' },
    { label: 'Maori', value: 'mi' },
    { label: 'Nepali', value: 'ne' },
    { label: 'Norwegian', value: 'no' },
    { label: 'Persian', value: 'fa' },
    { label: 'Polish', value: 'pl' },
    { label: 'Portuguese', value: 'pt' },
    { label: 'Romanian', value: 'ro' },
    { label: 'Russian', value: 'ru' },
    { label: 'Serbian', value: 'sr' },
    { label: 'Slovak', value: 'sk' },
    { label: 'Slovenian', value: 'sl' },
    { label: 'Spanish', value: 'es' },
    { label: 'Swahili', value: 'sw' },
    { label: 'Swedish', value: 'sv' },
    { label: 'Tagalog', value: 'tl' },
    { label: 'Tamil', value: 'ta' },
    { label: 'Thai', value: 'th' },
    { label: 'Turkish', value: 'tr' },
    { label: 'Ukrainian', value: 'uk' },
    { label: 'Urdu', value: 'ur' },
    { label: 'Vietnamese', value: 'vi' },
    { label: 'Welsh', value: 'cy' }
];

export function getSupportedLanguages(service: string, model: string) {
    if (service === 'assemblyai') {
        return model === 'best' ? ASSEMBLYAI_BEST_LANGUAGES : ASSEMBLYAI_NANO_LANGUAGES;
    } else if (service === 'whisper') {
        return WHISPER_LANGUAGES;
    }
    return [];
}

// Define the WhisperResponse interface
export interface WhisperResponse {
	text: string;
	segments: WhisperSegment[];
	language: string;
}

export async function transcribeWithWhisper(audioFile: string): Promise<WhisperResponse> {
	console.log('VoiceHype: Starting Whisper transcription');

	// Get the selected model from workspace configuration
	const config = vscode.workspace.getConfiguration('voicehype.transcription');
	const selectedModel = config.get<string>('model') || 'whisper-1';

	console.log(`VoiceHype: Using Whisper model: ${selectedModel}`);

	try {
		// Read the audio file
		const audioData = await fs.promises.readFile(audioFile);
		const base64Audio = audioData.toString('base64');
		const audioUrl = `data:audio/wav;base64,${base64Audio}`;

		console.log('VoiceHype: Audio file read successfully, calling transcribeAudio');

		// Call the transcribeAudio function with the audio data
		const response = await transcribeAudio(audioUrl, selectedModel);

		console.log('VoiceHype: Transcription completed successfully');

		return {
			text: response.transcription,
			segments: [],
			language: 'en'
		};
    } catch (error: any) {
		console.error('VoiceHype: Whisper transcription error:', error);
		throw new Error(`Whisper transcription failed: ${error.message}`);
	}
}

export async function optimizePrompt(originalPrompt: string): Promise<string> {
	try {
		// Get the selected optimization model from workspace configuration
		const config = vscode.workspace.getConfiguration('voicehype.transcription');
		const selectedModel = config.get<string>('optimizationModel') || 'gpt-4o';

		// Format the model name for display
		const displayModelName = selectedModel.includes('/')
			? selectedModel.split('/').pop()
			: selectedModel;

		console.log(`VoiceHype: Optimizing prompt with model: ${displayModelName}`);
		console.log('VoiceHype: Original prompt length:', originalPrompt.length);

		// Define the optimization prompt
		const optimizationPrompt = 'Optimize this transcription by removing verbal fillers (um, uh, like, you know), false starts, and repetitions. Format for clarity and readability. Keep the meaning intact.';

		// Generate a request ID to track this request in logs
        const requestId = `optimize-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;

        console.log(`VoiceHype: Making request to optimize function with request ID: ${requestId}...`);

        // Get API key and URL from configuration
        const apiKey = vscode.workspace.getConfiguration('voicehype').get<string>('apiKey') || '';
        const apiUrl = vscode.workspace.getConfiguration('voicehype').get<string>('apiUrl') || 'https://supabase.voicehype.ai/functions/v1';

        // Make request to edge function
        const response = await fetch(
            `${apiUrl}/optimize`,
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'apikey': apiKey,
                    'X-Request-ID': requestId
                },
                body: JSON.stringify({
                    text: originalPrompt,
                    model: selectedModel,
                    customPrompt: optimizationPrompt,
                    debug: true // Add debug flag to get more detailed logs
                })
            }
        );

        console.log('VoiceHype: Response status:', response.status, response.statusText);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('VoiceHype: Error response:', errorText);

            let errorMessage = `Optimization failed with status ${response.status}`;

            try {
                const errorData = JSON.parse(errorText);
                if (errorData.error && errorData.error.message) {
                    errorMessage = errorData.error.message;
                }
            } catch (e) {
                // If we can't parse the error, just use the status message
            }

            throw new Error(errorMessage);
        }

        const data = await response.json();
        console.log('VoiceHype: Optimization successful');

        if (data && data.data && data.data.optimizedText) {
            return data.data.optimizedText;
        } else {
            throw new Error('No optimized text in response');
        }
	} catch (error: any) {
		console.error('VoiceHype: Optimization error:', error);
		throw error;
	}
}
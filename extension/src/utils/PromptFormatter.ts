/**
 * PromptFormatter - Simplified version with clear, child-friendly logic
 */

export interface Message {
    role: 'system' | 'user' | 'assistant';
    content: string;
}

export class PromptFormatter {
    private readonly OPTIMIZED_TAG = '<optimizedText>...</optimizedText>';

    public createOptimizationMessages(transcript: string, customPrompt?: string, voiceCommandsEnabled: boolean = true): Message[] {
        return [
            {
                role: 'system',
                content: "You are VoiceHype, a helpful assistant that optimizes text based on user instructions. Always follow the instructions and format the response correctly."
            },
            {
                role: 'user',
                content: this.createUserMessage(transcript, customPrompt || this.getDefaultPrompt(), voiceCommandsEnabled)
            }
        ];
    }

    private createUserMessage(transcript: string, customPrompt: string, voiceCommandsEnabled: boolean): string {
        const processedPrompt = this.processVariables(customPrompt, transcript);
        const transcriptBlock = this.formatTranscript(transcript);
        const responseFormat = this.getResponseFormat();
        
        // Structure: Optimization instructions first, then voice commands if enabled
        let message = `You are VoiceHype, a helpful assistant that optimizes text based on user instructions. Always follow the instructions and format the response correctly.
        ${this.getOptimizeInstructions()}\n\n${processedPrompt}\n\n${transcriptBlock}\n\n${responseFormat}`;
        
        if (voiceCommandsEnabled) {
            message += `\n\n--- VOICE COMMANDS ---\n${this.getVoiceInstructions()}`;
        }

        return message;
    }

    private getVoiceInstructions(): string {
        return `ONLY if transcript contains "Voice Hype" or similar sounds like "voice type", "voice eye", "hey voice hype" (in any language), it means you are being asked to do something (like write an email, etc.) so execute the request exactly as requested. Ignore all previous optimization instructions for these cases.`;
    }

    private getOptimizeInstructions(): string {
        return ``;
    }

    private processVariables(prompt: string, transcript: string): string {
        if (prompt.includes('{{transcript}}')) {
            const transcriptBlock = this.formatTranscript(transcript);
            return prompt.replace(/\{\{transcript\}\}/g, transcriptBlock);
        }
        return prompt;
    }

    private formatTranscript(transcript: string): string {
        return `### TRANSCRIPT ###\n\`\`\`\n${transcript}\n\`\`\``;
    }

    private getDefaultPrompt(): string {
        return `Make this sound professional and fix any grammar mistakes. Use clear paragraphs and proper structure.`;
    }

    private getResponseFormat(): string {
        return `Return just the optimized text in this format only: ${this.OPTIMIZED_TAG}\nDo not include any other text or formatting.`;
    }
}
import * as vscode from 'vscode';
import { AudifyMicrophone } from './audifyMicrophone';

/**
 * Class for managing audio devices using Audify
 */
export class AudioDeviceManager {
    private static instance: AudioDeviceManager;
    private cachedDevices: Array<{ id: string; name: string }> = [];
    private lastRefreshTime: number = 0;
    private readonly CACHE_TIMEOUT = 60000; // 1 minute cache
    private initializationError: boolean = false;
    
    /**
     * Get the singleton instance of AudioDeviceManager
     */
    public static getInstance(): AudioDeviceManager {
        if (!AudioDeviceManager.instance) {
            AudioDeviceManager.instance = new AudioDeviceManager();
        }
        return AudioDeviceManager.instance;
    }
    
    /**
     * Private constructor for singleton pattern
     */
    private constructor() {
        console.log('AudioDeviceManager: Initialized');
    }
    
    /**
     * Get all available audio input devices
     * @param forceRefresh Whether to force a refresh of the device list
     * @returns Array of audio devices
     */
    public async getDevices(forceRefresh: boolean = false): Promise<Array<{ id: string; name: string }>> {
        const currentTime = Date.now();
        
        // If we had an initialization error and this isn't a forced refresh, return a default device
        if (this.initializationError && !forceRefresh) {
            return [{ id: '', name: 'System Default' }];
        }
        
        // Use cached devices if available and not expired
        if (!forceRefresh && 
            this.cachedDevices.length > 0 && 
            (currentTime - this.lastRefreshTime) < this.CACHE_TIMEOUT) {
            return this.cachedDevices;
        }
        
        try {
            // Create a temporary microphone to get devices
            const tempMic = new AudifyMicrophone();
            const devices = await tempMic.getDevices();
            
            // AudifyMicrophone.getDevices already adds a default device
            // No need to add it again
            
            // Update the cache
            this.cachedDevices = devices;
            this.lastRefreshTime = currentTime;
            this.initializationError = false;
            
            return devices;
        } catch (error) {
            console.error('AudioDeviceManager: Error getting devices:', error);
            this.initializationError = true;

            // If we have no cached devices, return just the default device
            if (this.cachedDevices.length === 0) {
                return [{ id: '', name: 'System Default' }];
            }
            
            vscode.window.showErrorMessage(`VoiceHype: Error getting audio devices - ${error instanceof Error ? error.message : String(error)}`);
            return this.cachedDevices; // Return cached devices as fallback
        }
    }
    
    /**
     * Get the current selected audio device from settings
     * @returns The device ID or empty string for default
     */
    public getCurrentDevice(): string {
        const deviceId = vscode.workspace.getConfiguration('voicehype.audio').get('device', '');
        console.log(`AudioDeviceManager: getCurrentDevice returning: '${deviceId}' (type: ${typeof deviceId})`);
        return deviceId;
    }
    
    /**
     * Set the current audio device in settings
     * @param deviceId The device ID to set
     * @returns Promise that resolves when the setting is updated
     */
    public async setCurrentDevice(deviceId: string): Promise<void> {
        console.log(`AudioDeviceManager: setCurrentDevice called with: '${deviceId}' (type: ${typeof deviceId})`);
        return vscode.workspace.getConfiguration('voicehype.audio').update('device', deviceId, true);
    }
    
    /**
     * Show a quick pick menu to select an audio device
     */
    public async showDeviceSelectionMenu(): Promise<void> {
        try {
            // Get available devices
            const devices = await this.getDevices(true);
            
            if (devices.length === 0) {
                vscode.window.showWarningMessage('VoiceHype: No audio input devices found');
                return;
            }
            
            // Add a "Default" option
            const defaultOption = { label: 'Default (System)', description: 'Use system default device', id: '' };
            
            // Create quick pick items
            const currentDeviceId = this.getCurrentDevice();
            const quickPickItems = [
                defaultOption,
                ...devices.map(device => ({
                    label: device.name,
                    description: device.id === currentDeviceId ? '(Current)' : '',
                    id: device.id
                }))
            ];
            
            // Show the quick pick
            const selection = await vscode.window.showQuickPick(quickPickItems, {
                placeHolder: 'Select audio input device',
                title: 'VoiceHype Audio Device Selection'
            });
            
            if (selection) {
                // Update the setting
                await this.setCurrentDevice(selection.id);
                vscode.window.showInformationMessage(`VoiceHype: Audio device set to ${selection.label}`);
            }
        } catch (error) {
            console.error('AudioDeviceManager: Error showing device selection menu:', error);
            vscode.window.showErrorMessage(`VoiceHype: Error selecting audio device - ${error instanceof Error ? error.message : String(error)}`);
        }
    }
}

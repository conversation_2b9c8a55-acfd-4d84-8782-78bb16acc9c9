import * as fs from 'fs';
import * as os from 'os';
import * as path from 'path';
import * as vscode from 'vscode';
import { EventEmitter } from 'events';
import { Readable, PassThrough } from 'stream';
import { RtAudio, RtAudioFormat } from '@voicehype/audify-plus';

/**
 * Implementation of a microphone class using Audify that provides the same
 * interface as the previous Microphone class
 */
export class AudifyMicrophone extends EventEmitter {
    private options: any;
    private rtAudio: RtAudio | null = null;
    private isPaused: boolean = false;
    private passThrough: PassThrough | null = null;
    private recordedChunks: Buffer[] = [];
    private isRecording: boolean = false;
    private frameSize: number = 1024;
    private tempFilePath: string = '';

    /**
     * Creates a WAV header buffer
     * @param format The audio format settings
     * @returns Buffer containing the WAV header
     */
    private createWavHeader(format: { sampleRate: number, channels: number, bitDepth: number }): Buffer {
        const { sampleRate = 48000, channels = 1, bitDepth = 16 } = format;

        // Calculate byte rate and block align
        const byteDepth = bitDepth / 8;
        const blockAlign = channels * byteDepth;
        const byteRate = sampleRate * blockAlign;

        // Create WAV file header
        const headerBuffer = Buffer.alloc(44);

        // RIFF chunk descriptor
        headerBuffer.write('RIFF', 0);                                // ChunkID
        headerBuffer.writeUInt32LE(36, 4);                           // ChunkSize (placeholder)
        headerBuffer.write('WAVE', 8);                                // Format

        // "fmt " sub-chunk
        headerBuffer.write('fmt ', 12);                               // Subchunk1ID
        headerBuffer.writeUInt32LE(16, 16);                          // Subchunk1Size
        headerBuffer.writeUInt16LE(1, 20);                           // AudioFormat
        headerBuffer.writeUInt16LE(channels, 22);                    // NumChannels
        headerBuffer.writeUInt32LE(sampleRate, 24);                  // SampleRate
        headerBuffer.writeUInt32LE(byteRate, 28);                    // ByteRate
        headerBuffer.writeUInt16LE(blockAlign, 32);                  // BlockAlign
        headerBuffer.writeUInt16LE(bitDepth, 34);                    // BitsPerSample

        // "data" sub-chunk
        headerBuffer.write('data', 36);                              // Subchunk2ID
        headerBuffer.writeUInt32LE(0, 40);                           // Subchunk2Size (placeholder)

        return headerBuffer;
    }

    /**
     * Create a new AudifyMicrophone instance
     * @param options Audio recording options
     */
    constructor(options: any = {}) {
        super();
        this.options = options || {};

        console.log('AudifyMicrophone: Constructor called with options:', JSON.stringify({
            device: this.options.device,
            rate: this.options.rate,
            channels: this.options.channels
        }));

        try {
            // Test RtAudio availability immediately
            const testRtAudio = new RtAudio();
            testRtAudio.closeStream();
        } catch (error) {
            console.warn('AudifyMicrophone: RtAudio not available:', error);
            // Don't throw here, we'll handle the error when actually trying to use audio
        }

        // Set up a temp file path for pause/resume functionality
        const tempDir = os.tmpdir();
        this.tempFilePath = path.join(tempDir, 'voicehype_recording.wav');

        // Create a PassThrough stream that we'll use to handle the audio data
        this.passThrough = new PassThrough();

        // Log initialization after successful setup
        console.log('AudifyMicrophone: Initialized with options:', {
            platform: os.platform(),
            options: this.options,
            tempPath: this.tempFilePath
        });
    }

    /**
     * Start recording audio from the microphone
     * @returns A readable stream of audio data
     */
    public startRecording(): Readable {
        console.log('AudifyMicrophone: startRecording called');

        // Reset the state
        this.isPaused = false;
        this.isRecording = true;
        this.recordedChunks = [];

        // Reset the PassThrough stream
        if (this.passThrough) {
            console.log('AudifyMicrophone: Destroying existing PassThrough stream');
            this.passThrough.destroy();
        }
        this.passThrough = new PassThrough();
        console.log('AudifyMicrophone: New PassThrough stream created');

        try {
            // Create a new RtAudio instance
            this.rtAudio = new RtAudio();
            console.log('AudifyMicrophone: RtAudio instance created');

            // Get available devices for logging
            const devices = this.rtAudio.getDevices();
            console.log('AudifyMicrophone: Available audio devices:');
            devices.forEach((device, index) => {
                console.log(`[${index}] ${device.name} (Inputs: ${device.inputChannels})`);
            });

            // Use our dedicated method to select the best input device
            const { deviceId, isUsingDefault } = this.getBestInputDeviceId(devices, this.options.device);

            // Set up sample rate and frame size for optimal performance
            const sampleRate = parseInt(this.options.rate) || 48000; // Use 48kHz by default
            const channels = this.options.channels || 1;
            // this.frameSize = Math.floor(sampleRate / 1); // ~1 second of audio per frame for better accuracy
            this.frameSize = 1920;

            console.log(`AudifyMicrophone: Opening stream with device ${deviceId}`, {
                sampleRate,
                channels,
                frameSize: this.frameSize,
                isUsingDefault
            });

            // Verify device is valid for input (this should never fail due to our selection logic)
            const selectedDevice = devices[deviceId];
            if (!selectedDevice || selectedDevice.inputChannels === 0) {
                throw new Error(`Selected device ${deviceId} is not valid for input`);
            }


            // Create input parameters object
            const inputParams: any = {
                nChannels: channels,
                firstChannel: 0
            };

            // Only add deviceId if we're not using the default device
            if (!isUsingDefault) {
                inputParams.deviceId = deviceId;
            }

            this.rtAudio.openStream(
                null, // No output
                inputParams,
                RtAudioFormat.RTAUDIO_SINT16,
                sampleRate,
                this.frameSize, // Use calculated frame size (~1 second for better accuracy)
                "VoiceHypeRecording", // Stream name
                (pcmData: Buffer) => {
                    console.log('AudifyMicrophone: Audio data received: ', pcmData.length, 'bytes');
                    if (this.isRecording && !this.isPaused && pcmData) {
                        try {
                            const buffer = Buffer.from(pcmData);
                            this.recordedChunks.push(buffer);

                            // Only pass PCM data through the stream
                            if (this.passThrough && !this.passThrough.destroyed) {
                                this.passThrough.write(buffer);
                            }
                        } catch (error) {
                            console.error('AudifyMicrophone: Error processing audio data:', error);
                        }
                    }
                }
            );

            // Start recording
            this.rtAudio.start();
            console.log('AudifyMicrophone: Audio stream started successfully');

        } catch (error) {
            console.error('AudifyMicrophone: Error starting recording:', error);
            this.emit('error', error);

            // Show error in VS Code UI
            vscode.window.showErrorMessage(`VoiceHype: Recording failed - ${error instanceof Error ? error.message : String(error)}`);

            // Make sure we still return a stream even if there's an error
            if (!this.passThrough) {
                this.passThrough = new PassThrough();
            }
        }

        return this.passThrough;
    }

    /**
     * Stop recording audio
     */
    public  stopRecording(): void {
        console.log('AudifyMicrophone: stopRecording called');

        this.isRecording = false;
        this.isPaused = false;

        // Wait for 2 seconds as grace period before closing the stream
        // await new Promise(resolve => setTimeout(resolve, 2000));

        // Close the audio stream
        if (this.rtAudio) {
            try {
                if (this.rtAudio.isStreamRunning()) {
                    this.rtAudio.stop();
                }
                if (this.rtAudio.isStreamOpen()) {
                    this.rtAudio.closeStream();
                }
                console.log('AudifyMicrophone: Audio stream closed');
            } catch (error) {
                console.error('AudifyMicrophone: Error closing audio stream:', error);
            }

            this.rtAudio = null;
        }            // Convert PCM data to WAV and save the file
        if (this.recordedChunks.length > 0) {
            try {
                // Combine all PCM chunks
                const combinedPcmData = Buffer.concat(this.recordedChunks);

                // Convert to WAV using our pcmToWav function
                const wavData = this.pcmToWav(combinedPcmData, {
                    sampleRate: parseInt(this.options.rate) || 48000,
                    channels: this.options.channels || 1,
                    bitDepth: 16  // RtAudio uses SINT16 format
                });

                // Write the complete WAV file
                fs.writeFileSync(this.tempFilePath, wavData);
                console.log('AudifyMicrophone: Saved WAV file with size:', wavData.length, 'bytes');
            } catch (error) {
                console.error('AudifyMicrophone: Error updating WAV header:', error);
            }
        }

        // End the PassThrough stream
        if (this.passThrough) {
            this.passThrough.end();
            console.log('AudifyMicrophone: PassThrough stream ended');
        }

        console.log('AudifyMicrophone: Recording stopped');
    }

    /**
     * Pause recording audio
     */
    public pauseRecording(): void {
        if (this.isPaused) {
            console.log('AudifyMicrophone: Recording already paused');
            return;
        }

        console.log('AudifyMicrophone: pauseRecording called');

        this.isPaused = true;

        // We don't stop the rtAudio stream, just set the flag to stop processing audio in the callback

        this.emit('pause');
        console.log('AudifyMicrophone: Recording paused');
    }

    /**
     * Resume recording audio
     * @returns The readable stream
     */
    public resumeRecording(): Readable {
        if (!this.isPaused) {
            console.log('AudifyMicrophone: Recording not paused');
            return this.passThrough as Readable;
        }

        console.log('AudifyMicrophone: resumeRecording called');

        this.isPaused = false;

        // We simply resume processing audio in the callback by updating the flag

        this.emit('resume');
        console.log('AudifyMicrophone: Recording resumed');

        return this.passThrough as Readable;
    }

    /**
     * Check if recording is paused
     */
    public isPausedState(): boolean {
        return this.isPaused;
    }

    /**
     * Get available audio devices
     * @returns Array of audio devices
     */
    async getDevices(): Promise<Array<{ id: string; name: string }>> {
        try {
            let devices;
            try {
                // Create a temporary RtAudio instance just to get devices
                const tempAudio = new RtAudio();
                devices = tempAudio.getDevices();
                tempAudio.closeStream();
            } catch (rtError) {
                console.warn('AudifyMicrophone: Error creating RtAudio instance:', rtError);
                return [{ id: '', name: 'System Default' }];
            }

            // Process ALL devices and use their actual RtAudio device IDs
            const result = Array.isArray(devices) ? devices
                .map((device, index) => {
                    if (!device) { return null; }

                    // Determine device type for display
                    let deviceType = '';
                    if (device.inputChannels > 0 && device.outputChannels > 0) {
                        deviceType = ' (Input/Output)';
                    } else if (device.inputChannels > 0) {
                        deviceType = ' (Input)';
                    } else if (device.outputChannels > 0) {
                        deviceType = ' (Output)';
                    }

                    return {
                        id: index.toString(), // Use actual RtAudio device index
                        name: (device && typeof device.name === 'string') ?
                            `${device.name}${deviceType}` :
                            `Device ${index}${deviceType}`
                    };
                })
                .filter(device => device !== null) as Array<{ id: string; name: string }> : [];

            // Always add a default device at the start
            result.unshift({
                id: '', // Use empty string for system default
                name: 'System Default'
            });

            // Log all devices found
            if (result.length > 1) {
                console.log(`AudifyMicrophone: Found ${result.length - 1} audio devices (showing all input/output devices)`);
                result.slice(1).forEach(device => {
                    console.log(`  [${device.id}] ${device.name}`);
                });
            }
            return result;
        } catch (error) {
            console.error('AudifyMicrophone: Error getting devices:', error);
            // Return at least a default device
            return [{
                id: '',
                name: 'System Default'
            }];
        }
    }

    /**
     * Convert PCM data to WAV format
     * @param pcmData PCM audio data
     * @param format Audio format parameters
     * @returns WAV buffer
     */
    private pcmToWav(pcmData: Buffer, format: { sampleRate: number, channels: number, bitDepth: number }): Buffer {
        const { sampleRate = 44100, channels = 1, bitDepth = 16 } = format;

        // Calculate byte rate and block align
        const byteDepth = bitDepth / 8;
        const blockAlign = channels * byteDepth;
        const byteRate = sampleRate * blockAlign;

        // Create WAV file header
        const headerBuffer = Buffer.alloc(44);

        // RIFF chunk descriptor
        headerBuffer.write('RIFF', 0);                               // ChunkID
        headerBuffer.writeUInt32LE(36 + pcmData.length, 4);          // ChunkSize: 36 + SubChunk2Size
        headerBuffer.write('WAVE', 8);                               // Format

        // "fmt " sub-chunk
        headerBuffer.write('fmt ', 12);                              // Subchunk1ID
        headerBuffer.writeUInt32LE(16, 16);                          // Subchunk1Size (16 for PCM)
        headerBuffer.writeUInt16LE(1, 20);                           // AudioFormat (1 for PCM)
        headerBuffer.writeUInt16LE(channels, 22);                    // NumChannels
        headerBuffer.writeUInt32LE(sampleRate, 24);                  // SampleRate
        headerBuffer.writeUInt32LE(byteRate, 28);                    // ByteRate
        headerBuffer.writeUInt16LE(blockAlign, 32);                  // BlockAlign
        headerBuffer.writeUInt16LE(bitDepth, 34);                    // BitsPerSample

        // "data" sub-chunk
        headerBuffer.write('data', 36);                              // Subchunk2ID
        headerBuffer.writeUInt32LE(pcmData.length, 40);              // Subchunk2Size

        // Combine header and PCM data
        return Buffer.concat([headerBuffer, pcmData]);
    }

    /**
 * Selects the best input device based on platform, available devices, and user preferences
 * @param devices List of available audio devices
 * @param userDeviceOption User-specified device option from constructor
 * @returns Object containing the selected device ID and whether it's using the default device
 */
    private getBestInputDeviceId(devices: any[], userDeviceOption?: string): { deviceId: number, isUsingDefault: boolean } {
        console.log('AudifyMicrophone: Selecting best input device');

        // Filter for devices with input capabilities
        const inputDevices = devices
            .map((device, index) => ({ ...device, index }))
            .filter(device => device.inputChannels > 0);

        if (inputDevices.length === 0) {
            console.warn('AudifyMicrophone: No input devices found with input channels');
            return {
                deviceId: this.rtAudio!.getDefaultInputDevice(),
                isUsingDefault: true
            };
        }

        console.log('AudifyMicrophone: Available input devices:');
        inputDevices.forEach(device => {
            console.log(`[${device.index}] ${device.name} (Inputs: ${device.inputChannels})`);
        });

        // Handle user-specified device
        if (userDeviceOption && userDeviceOption !== '' && userDeviceOption !== 'default') {
            console.log(`AudifyMicrophone: User specified device: '${userDeviceOption}'`);
            const numericId = parseInt(userDeviceOption);
            if (!isNaN(numericId) && devices[numericId] && devices[numericId].inputChannels > 0) {
                console.log(`AudifyMicrophone: Using user-specified device ${numericId}: ${devices[numericId].name}`);
                return { deviceId: numericId, isUsingDefault: false };
            } else {
                console.warn(`AudifyMicrophone: Invalid user-specified device '${userDeviceOption}', will select best available device`);
            }
        } else if (userDeviceOption === 'default' || userDeviceOption === '') {
            console.log(`AudifyMicrophone: User requested default device (value: '${userDeviceOption}')`);
            return { deviceId: this.rtAudio!.getDefaultInputDevice(), isUsingDefault: true };
        }

        // Platform-specific preferences
        if (os.platform() === 'linux') {
            const pulseAudioDevice = inputDevices.find(device =>
                device.name.toLowerCase().includes('pulse') ||
                device.name.toLowerCase().includes('default')
            );
            if (pulseAudioDevice) {
                console.log(`AudifyMicrophone: Selected Linux preferred device ${pulseAudioDevice.index}: ${pulseAudioDevice.name}`);
                return { deviceId: pulseAudioDevice.index, isUsingDefault: false };
            }
        }

        // Try to select a device that sounds like a microphone
        const micKeywords = ['mic', 'microphone', 'usb audio', 'hd audio', 'hd microphone', 'external', 'headset', 'input'];
        const micLikeDevices = inputDevices.filter(device =>
            micKeywords.some(keyword => device.name.toLowerCase().includes(keyword))
        );

        if (micLikeDevices.length > 0) {
            // Pick the one with the most input channels among mic-like devices
            const bestMic = micLikeDevices.reduce((prev, curr) =>
                curr.inputChannels > prev.inputChannels ? curr : prev
            );
            console.log(`AudifyMicrophone: Selected mic-like device ${bestMic.index}: ${bestMic.name}`);
            return { deviceId: bestMic.index, isUsingDefault: false };
        }

        // Use system default if valid
        const defaultDevice = this.rtAudio!.getDefaultInputDevice();
        if (devices[defaultDevice] && devices[defaultDevice].inputChannels > 0) {
            console.log(`AudifyMicrophone: Using system default device ${defaultDevice}: ${devices[defaultDevice].name}`);
            return { deviceId: defaultDevice, isUsingDefault: true };
        }

        // Fallback to device with most input channels
        const mostInputsDevice = inputDevices.reduce((prev, curr) =>
            curr.inputChannels > prev.inputChannels ? curr : prev
        );
        console.log(`AudifyMicrophone: Fallback to device with most inputs ${mostInputsDevice.index}: ${mostInputsDevice.name}`);
        return { deviceId: mostInputsDevice.index, isUsingDefault: false };
    }
}

import * as os from 'os';
import * as fs from 'fs';
import * as path from 'path';
import * as vscode from 'vscode';

/**
 * Returns the path to the SoX binary based on the current platform.
 * Uses the helper methods from sox-static packages for macOS and Linux,
 * and the uploaded sox-14.4.2-win32 folder for Windows.
 */
export function getSoxPath(): string {
    const platform = os.platform();
    
    try {
        // Get the extension's directory path - go up one level from __dirname
        // __dirname will be in dist/utils in the packaged extension
        const extensionPath = path.resolve(__dirname);
        
        // Show the resolved extension path for debugging
        const debugMsg = `VoiceHype: Extension path resolved to: ${extensionPath}`;
        console.log(debugMsg);
        // vscode.window.showInformationMessage(debugMsg);
        
        if (platform === 'win32') {
            // Windows - try multiple possible paths for the SoX binary
            const possiblePaths = [
                // Path in the packaged extension (resources copied by webpack)
                path.join(extensionPath, 'resources', 'binaries', 'win32', 'sox-14.4.2-win32', 'sox.exe'),
                // Original development path
                path.join(extensionPath, 'extension', 'resources', 'binaries', 'win32', 'sox-14.4.2-win32', 'sox.exe'),
                // Alternative paths
                path.join(extensionPath, 'dist', 'resources', 'binaries', 'win32', 'sox-14.4.2-win32', 'sox.exe'),
                path.join(extensionPath, 'binaries', 'win32', 'sox-14.4.2-win32', 'sox.exe'),
                path.join(path.dirname(extensionPath), 'resources', 'binaries', 'win32', 'sox-14.4.2-win32', 'sox.exe')
            ];
            
            // Log all paths we're checking
            console.log('VoiceHype: Checking the following paths for Windows SoX binary:');
            possiblePaths.forEach(p => console.log(` - ${p}`));
            
            // Try each path
            for (const soxPath of possiblePaths) {
                if (fs.existsSync(soxPath)) {
                    const msg = `VoiceHype: Found Windows SoX binary at: ${soxPath}`;
                    console.log(msg);
                    // vscode.window.showInformationMessage(msg);
                    return soxPath;
                }
            }
            
            // If we get here, none of the paths worked
            const errorMsg = `VoiceHype: Windows SoX binary not found in any of the expected locations`;
            console.error(errorMsg);
            vscode.window.showErrorMessage(errorMsg);
            
            // List directory contents for debugging
            try {
                // List the extension directory
                console.log(`VoiceHype: Contents of extension directory (${extensionPath}):`);
                if (fs.existsSync(extensionPath)) {
                    const files = fs.readdirSync(extensionPath);
                    console.log(files);
                    
                    // Check if resources directory exists
                    const resourcesDir = path.join(extensionPath, 'resources');
                    if (fs.existsSync(resourcesDir)) {
                        console.log(`VoiceHype: Contents of resources directory (${resourcesDir}):`);
                        const resourceFiles = fs.readdirSync(resourcesDir);
                        console.log(resourceFiles);
                        
                        // Check if binaries directory exists
                        const binariesDir = path.join(resourcesDir, 'binaries');
                        if (fs.existsSync(binariesDir)) {
                            console.log(`VoiceHype: Contents of binaries directory (${binariesDir}):`);
                            const binariesFiles = fs.readdirSync(binariesDir);
                            console.log(binariesFiles);
                            
                            // Check if win32 directory exists
                            const win32Dir = path.join(binariesDir, 'win32');
                            if (fs.existsSync(win32Dir)) {
                                console.log(`VoiceHype: Contents of win32 directory (${win32Dir}):`);
                                const win32Files = fs.readdirSync(win32Dir);
                                console.log(win32Files);
                            }
                        }
                    }
                }
            } catch (err) {
                console.error('VoiceHype: Error listing directory contents:', err);
            }
            
        } else if (platform === 'darwin') {
            // macOS - use sox-static-macos
            try {
                const soxPath = require('sox-static-macos');
                if (fs.existsSync(soxPath)) {
                    // Ensure the binary is executable on macOS
                    try {
                        fs.chmodSync(soxPath, 0o755); // rwxr-xr-x
                    } catch (err) {
                        const errorMsg = `VoiceHype: Failed to set executable permissions on macOS binary: ${err}`;
                        console.error(errorMsg);
                        vscode.window.showErrorMessage(errorMsg);
                    }
                    return soxPath;
                }
            } catch (err) {
                const errorMsg = `VoiceHype: Error loading macOS SoX binary: ${err}`;
                console.error(errorMsg);
                vscode.window.showErrorMessage(errorMsg);
            }
        } else if (platform === 'linux') {
            // Linux - use sox-static-linux
            try {
                const soxPath = require('sox-static-linux');
                if (fs.existsSync(soxPath)) {
                    // Ensure the binary is executable on Linux
                    try {
                        fs.chmodSync(soxPath, 0o755); // rwxr-xr-x
                    } catch (err) {
                        const errorMsg = `VoiceHype: Failed to set executable permissions on Linux binary: ${err}`;
                        console.error(errorMsg);
                        vscode.window.showErrorMessage(errorMsg);
                    }
                    return soxPath;
                }
            } catch (err) {
                const errorMsg = `VoiceHype: Error loading Linux SoX binary: ${err}`;
                console.error(errorMsg);
                vscode.window.showErrorMessage(errorMsg);
            }
        }
        
        // If we couldn't find the bundled binary, try to use the system's SoX
        const fallbackMsg = `VoiceHype: Bundled SoX binary not found for ${platform}, falling back to system SoX`;
        console.log(fallbackMsg);
        vscode.window.showWarningMessage(fallbackMsg);
        return 'sox';
    } catch (error) {
        const errorMsg = `VoiceHype: Error finding SoX binary for ${platform}: ${error}`;
        console.error(errorMsg);
        vscode.window.showErrorMessage(errorMsg);
        // Fallback to system SoX if there's an error
        return 'sox';
    }
} 
import * as os from 'os';
import { EventEmitter } from 'events';
import { Readable } from 'stream';
import * as vscode from 'vscode';

// Import our Audify-based microphone implementation
import { MicrophoneCompat } from './microphoneCompat';

/**
 * Custom microphone implementation that uses:
 * - Audify JS on all platforms for consistent behavior
 * 
 * This implementation matches the interface of the original microphone class.
 */
export class Microphone extends EventEmitter {
    private micCompat: MicrophoneCompat | null = null;
    private options: any;

    constructor(options: any = {}) {
        super();
        this.options = options || {};
        
        console.log('Microphone: Constructor called (using Audify)');
        console.log('Microphone: Platform:', os.platform());
        console.log('Microphone: Options:', JSON.stringify(this.options));
        
        // Create the MicrophoneCompat instance
        this.micCompat = new MicrophoneCompat(this.options);
        
        // Forward all events from the MicrophoneCompat
        this.micCompat.on('error', (err) => this.emit('error', err));
        this.micCompat.on('pause', () => this.emit('pause'));
        this.micCompat.on('resume', () => this.emit('resume'));
        this.micCompat.on('close', () => this.emit('close'));
    }

    /**
     * Start recording audio from the microphone
     * This method matches the node-microphone interface
     */
    public startRecording(): Readable {
        console.log('Microphone: startRecording called');
        
        if (!this.micCompat) {
            console.error('Microphone: MicrophoneCompat not initialized');
            throw new Error('Microphone compatibility layer not initialized');
        }
        
        // Use the compatibility layer to start recording
        const sourceStream = this.micCompat.startRecording();
        
        // Forward events
        sourceStream.on('close', () => {
            console.log('Microphone: Source stream CLOSE event in startRecording');
        });

        sourceStream.on('end', () => {
            console.log('Microphone: Source stream END event in startRecording');
        });

        sourceStream.on('error', (err) => {
            console.error('Microphone: Source stream ERROR event in startRecording:', err);
        });
        
        return sourceStream;
    }

    /**
     * Stop recording audio
     * This method matches the node-microphone interface
     */
    public stopRecording(): void {
        console.log('Microphone: stopRecording called');
        
        if (!this.micCompat) {
            console.error('Microphone: MicrophoneCompat not initialized');
            return;
        }
        
        // Use the compatibility layer to stop recording
        this.micCompat.stopRecording();
    }

    /**
     * Pause recording audio
     * This is a new method to support pause functionality
     */
    public pauseRecording(): void {
        console.log('Microphone: pauseRecording called');
        
        if (!this.micCompat) {
            console.error('Microphone: MicrophoneCompat not initialized');
            return;
        }
        
        // Use the compatibility layer to pause recording
        this.micCompat.pauseRecording();
    }

    /**
     * Resume recording audio
     * This is a new method to support resume functionality
     */
    public resumeRecording(): Readable {
        console.log('Microphone: resumeRecording called');
        
        if (!this.micCompat) {
            console.error('Microphone: MicrophoneCompat not initialized');
            throw new Error('Microphone compatibility layer not initialized');
        }
        
        // Use the compatibility layer to resume recording
        return this.micCompat.resumeRecording();
    }

    /**
     * Check if recording is paused
     */
    public isPausedState(): boolean {
        if (!this.micCompat) {
            return false;
        }
        
        return this.micCompat.isPausedState();
    }

    /**
     * Get available audio devices
     * @returns Array of audio devices
     */
    async getDevices(): Promise<Array<{ id: string; name: string }>> {
        if (!this.micCompat) {
            return [];
        }
        
        return this.micCompat.getDevices();
    }
}

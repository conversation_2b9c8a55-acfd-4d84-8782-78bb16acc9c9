import * as fs from 'fs';
import { TranscriptionError, TranscriptionErrorCode } from '../types/errors';

export class AudioValidator {
    static async validateAudioFile(filePath: string): Promise<void> {
        try {
            const stats = await fs.promises.stat(filePath);
            
            // Check if file exists and has content
            if (!stats.isFile() || stats.size === 0) {
                throw new TranscriptionError(
                    'No audio was recorded',
                    TranscriptionErrorCode.EMPTY_AUDIO,
                    false
                );
            }

            // Check if file size is too small (less than 1KB probably means no real audio)
            if (stats.size < 1024) {
                throw new TranscriptionError(
                    'Recording is too short or empty',
                    TranscriptionErrorCode.EMPTY_AUDIO,
                    false
                );
            }
        } catch (error: any) {
            if (error instanceof TranscriptionError) {
                throw error;
            }
            throw new TranscriptionError(
                `Failed to validate audio file: ${error.message}`,
                TranscriptionErrorCode.UNKNOWN,
                false,
                error
            );
        }
    }
}

export class AudioPlayer {
    private audio: HTMLAudioElement | null = null;
    private isPlaying: boolean = false;

    constructor(private audioUrl: string) {
        this.audio = new Audio(audioUrl);
        this.setupEventListeners();
    }

    private setupEventListeners() {
        if (!this.audio) return;

        this.audio.addEventListener('ended', () => {
            this.isPlaying = false;
        });

        this.audio.addEventListener('error', (e) => {
            console.error('Audio playback error:', e);
            this.isPlaying = false;
        });
    }

    play() {
        if (!this.audio || this.isPlaying) return;
        this.audio.play();
        this.isPlaying = true;
    }

    pause() {
        if (!this.audio || !this.isPlaying) return;
        this.audio.pause();
        this.isPlaying = false;
    }

    stop() {
        if (!this.audio) return;
        this.audio.pause();
        this.audio.currentTime = 0;
        this.isPlaying = false;
    }

    setVolume(volume: number) {
        if (!this.audio) return;
        this.audio.volume = Math.max(0, Math.min(1, volume));
    }

    getCurrentTime(): number {
        return this.audio?.currentTime ?? 0;
    }

    getDuration(): number {
        return this.audio?.duration ?? 0;
    }

    seek(time: number) {
        if (!this.audio) return;
        this.audio.currentTime = Math.max(0, Math.min(time, this.audio.duration));
    }

    isCurrentlyPlaying(): boolean {
        return this.isPlaying;
    }

    dispose() {
        if (this.audio) {
            this.audio.pause();
            this.audio.src = '';
            this.audio.remove();
            this.audio = null;
        }
    }
} 
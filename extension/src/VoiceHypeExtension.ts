import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';

// Import services
import { ConfigurationService } from './services/ConfigurationService';
import { StatusBarService } from './services/StatusBarService';
import { FileTrackingService } from './services/FileTrackingService';
import { HistoryService } from './services/HistoryService';
import { TranscriptionService } from './services/TranscriptionService';
import { RecordingService } from './services/RecordingService';
import { CommandService } from './services/CommandService';
import { VoiceHypePanel } from './services/VoiceHypePanelService';
import { AudioDeviceSelectionService } from './services/AudioDeviceSelectionService';
import { SecretsService } from './services/SecretsService';
import { AuthenticationService } from './services/AuthenticationService';
import { initializeConfigService } from './utils/supabaseClient';
import { testApiKeyStorage } from './test/apiKeyTest';

export class VoiceHypeExtension {
    private configService: ConfigurationService;
    private statusBarService: StatusBarService;
    private fileTrackingService: FileTrackingService;
    private historyService: HistoryService;
    private transcriptionService: TranscriptionService;
    private recordingService: RecordingService;
    private commandService: CommandService;
    private voiceHypePanel: VoiceHypePanel;
    private audioDeviceSelectionService: AudioDeviceSelectionService;
    private secretsService: SecretsService;
    private authenticationService: AuthenticationService;

    constructor(private context: vscode.ExtensionContext) {
        console.log('VoiceHype: Initializing services...');

        // Initialize secrets service first to make it available to other services
        this.secretsService = new SecretsService(context);

        // Initialize services with dependency injection
        this.configService = new ConfigurationService(this.secretsService);
        
        // Initialize supabaseClient with config service
        initializeConfigService(this.configService);
        
        // Initialize authentication service (only once)
        this.authenticationService = new AuthenticationService(
            context,
            this.secretsService,
            this.configService
        );

        // Initialize remaining services
        this.statusBarService = new StatusBarService(this.configService);
        this.fileTrackingService = new FileTrackingService();
        this.transcriptionService = new TranscriptionService(this.configService);
        this.historyService = new HistoryService(context, this.transcriptionService);
        this.recordingService = new RecordingService(
            context,
            this.configService,
            this.statusBarService,
            this.fileTrackingService,
            this.transcriptionService,
            this.historyService
        );

        // Create and register the webview panel
        this.voiceHypePanel = new VoiceHypePanel(
            context.extensionUri,
            this.configService,
            this.recordingService,
            this.transcriptionService,
            this.historyService,
            this.recordingService,
            this.configService,
            this.historyService,
            this
        );

        // Set the VoiceHypePanel reference in RecordingService for webview updates
        this.recordingService.setVoiceHypePanel(this.voiceHypePanel);

        this.commandService = new CommandService(
            context,
            this.recordingService,
            this.configService,
            this.transcriptionService,
            this.historyService,
            this.authenticationService,
            this.statusBarService
        );

        // Initialize the audio device selection service
        this.audioDeviceSelectionService = new AudioDeviceSelectionService();

        console.log('VoiceHype: Services initialized');
    }

    public activate(): void {
        console.log('VoiceHype: Starting activation...');

        // Show clipboard notice for first-time users
        this.showFirstTimeClipboardNotice();

        // Register the webview view provider
        vscode.window.registerWebviewViewProvider(
            VoiceHypePanel.viewType,
            this.voiceHypePanel,
            { webviewOptions: { retainContextWhenHidden: true } }
        );

        // Register history commands - REMOVED - No longer needed
        // const historyCommands = this.historyService.registerCommands();
        // this.context.subscriptions.push(...historyCommands);

        // Register all commands
        this.commandService.registerCommands();
        
        // Register audio device selection command
        const audioDeviceCommands = this.audioDeviceSelectionService.registerCommands();
        this.context.subscriptions.push(...audioDeviceCommands);

        // Check for authentication status and API key on startup
        this.checkAuthenticationStatus();
        
        // Register configuration change listener
        this.context.subscriptions.push(
            vscode.workspace.onDidChangeConfiguration(async e => {

                // Handle real-time transcription setting changes from VS Code settings
                if (e.affectsConfiguration('voicehype.transcription.realtime')) {
                    const isRealtimeEnabled = this.configService.getTranscriptionRealtime();
                    console.log(`VoiceHype: Real-time transcription setting changed to ${isRealtimeEnabled}`);

                    // Handle sample rate adjustment for real-time mode
                    this.configService.handleRealtimeToggle(isRealtimeEnabled).then(() => {
                        if (isRealtimeEnabled) {
                            const currentService = this.configService.getTranscriptionService();
                            if (currentService !== 'lemonfox') { // Only show for AssemblyAI
                                vscode.window.showInformationMessage(
                                    'Real-time transcription enabled. Sample rate set to 16000Hz for optimal performance.'
                                );
                            }
                        }
                    });
                }

                // Stop recording and process with new settings if configuration changes during recording
                if (this.recordingService.isRecording() &&
                    (e.affectsConfiguration('voicehype.transcription.service') ||
                     e.affectsConfiguration('voicehype.transcription.model') ||
                     e.affectsConfiguration('voicehype.transcription.language'))) {

                    // Get the current configuration
                    const service = this.configService.getTranscriptionService();
                    const model = this.configService.getTranscriptionModel();
                    const language = this.configService.getTranscriptionLanguage();

                    // Stop the recording and process with the latest configuration
                    this.recordingService.stopRecording(this.configService.getShouldOptimize())
                        .then(() => {
                            // Show notification about the change
                            vscode.window.showInformationMessage(
                                `Recording stopped and processed with updated settings: ${service} (${model}) - ${language}`
                            );

                            // Update UI to reflect recording stopped
                            this.voiceHypePanel.updateRecordingState();
                        })
                        .catch(error => {
                            vscode.window.showInformationMessage(`Error processing recording: ${error.message}`);

                            // Update UI even on error
                            this.voiceHypePanel.updateRecordingState();
                        });
                }
            })
        );

        // Add all services to disposables
        this.context.subscriptions.push(this.configService);
        this.context.subscriptions.push(this.statusBarService);
        this.context.subscriptions.push(this.fileTrackingService);
        this.context.subscriptions.push(this.recordingService);
        this.context.subscriptions.push(this.historyService);
        this.context.subscriptions.push(this.commandService);
        this.context.subscriptions.push(this.authenticationService);

        // Add configuration contribution to package.json
        this.updatePackageJson();

        // Register test commands for development use
        this.registerTestCommands();

        console.log('VoiceHype: Extension is now active!');
        
        // Check if API key exists and show welcome message
        this.configService.getApiKeyAsync().then(apiKey => {
            if (apiKey) {
                vscode.window.showInformationMessage('VoiceHype is now active! Use Ctrl+Shift+8 for basic transcription or Ctrl+Shift+9 for AI-optimized version.');
            }
        });
    }

    private updatePackageJson(): void {
        const packageJsonPath = path.join(this.context.extensionPath, 'package.json');
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

        if (!packageJson.contributes) {
            packageJson.contributes = {};
        }
        if (!packageJson.contributes.configuration) {
            packageJson.contributes.configuration = {
                title: 'VoiceHype',
                properties: {}
            };
        }

        // Remove the old API key configuration if it exists
        if (packageJson.contributes.configuration.properties['voicehype.apiKey']) {
            delete packageJson.contributes.configuration.properties['voicehype.apiKey'];
        }

        // Save updated package.json
        fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
    }

    public deactivate(): void {
        // Nothing to do here, as all disposables are registered with the context
    }

    /**
     * Get a service instance by name
     * This allows other parts of the extension to get a reference to specific services
     * @param serviceName The name of the service to get
     * @returns The service instance or undefined if not found
     */
    public getService(serviceName: string): any {
        switch (serviceName) {
            case 'voiceHypePanel':
                return this.voiceHypePanel;
            case 'historyService':
                return this.historyService;
            case 'recordingService':
                return this.recordingService;
            case 'transcriptionService':
                return this.transcriptionService;
            case 'configService':
                return this.configService;
            case 'authService':
                return this.authenticationService;
            default:
                return undefined;
        }
    }

    // Add a dispose method to clean up resources
    public dispose(): void {
        // Clean up all services
        if (this.statusBarService) {
            this.statusBarService.dispose();
        }
        if (this.fileTrackingService) {
            this.fileTrackingService.dispose();
        }
        if (this.recordingService) {
            this.recordingService.dispose();
        }
        if (this.commandService) {
            this.commandService.dispose();
        }
        if (this.historyService) {
            this.historyService.dispose();
        }
        if (this.configService) {
            this.configService.dispose();
        }
        if (this.voiceHypePanel) {
            this.voiceHypePanel.dispose();
        }
        if (this.audioDeviceSelectionService) {
            this.audioDeviceSelectionService.dispose();
        }
        if (this.authenticationService) {
            this.authenticationService.dispose();
        }
    }

    /**
     * Register test commands for development
     * These commands are only intended for testing purposes
     */
    private registerTestCommands(): void {
        // Register command to test API key storage
        this.context.subscriptions.push(
            vscode.commands.registerCommand('voicehype.test.apiKeyStorage', () => {
                testApiKeyStorage(this.context).catch(err => {
                    console.error('Error testing API key storage:', err);
                    vscode.window.showErrorMessage('API key storage test failed. See output for details.');
                });
            })
        );

        console.log('VoiceHype: Test commands registered');
    }

    private async showFirstTimeClipboardNotice(): Promise<void> {
        const hasShownClipboardNotice = this.context.globalState.get('hasShownClipboardNotice');
        
        if (!hasShownClipboardNotice) {
            const response = await vscode.window.showInformationMessage(
                'In addition to pasting into the active text field, VoiceHype automatically copies results to your system clipboard as well. Would you like to disable this?',
                'Yes, disable',
                'Keep enabled'
            );

            if (response === 'Yes, disable') {
                await vscode.workspace.getConfiguration('voicehype').update(
                    'copyResultsToClipboard',
                    false,
                    vscode.ConfigurationTarget.Global
                );
            }

            await this.context.globalState.update('hasShownClipboardNotice', true);
        }
    }

    /**
     * Check the authentication status and API key
     * If no valid authentication is found, prompt the user
     */
    private async checkAuthenticationStatus(): Promise<void> {
        try {
            // First try to validate through the authentication service
            const isAuthenticated = await this.authenticationService.validateApiKey();
            
            if (isAuthenticated) {
                console.log('VoiceHype: User is authenticated');
                return;
            }
            
            // Fallback to checking for API key directly
            const apiKey = await this.configService.getApiKeyAsync();
            
            // Clear any old references to the API key in settings
            await vscode.workspace.getConfiguration('voicehype').update('apiKey', undefined, vscode.ConfigurationTarget.Global);
            
            if (!apiKey) {
                // No authentication found, show sign-in options
                const message = 'Welcome to VoiceHype! Please sign in to get started.';
                const selection = await vscode.window.showInformationMessage(
                    message,
                    'Sign in with Browser',
                    'Enter API Key',
                    'Visit voicehype.ai'
                );
                
                if (selection === 'Sign in with Browser') {
                    // Start the browser authentication flow
                    await this.authenticationService.signIn();
                } else if (selection === 'Enter API Key') {
                    // Focus the VoiceHype panel view
                    await vscode.commands.executeCommand('voicehype.controlPanel.focus');
                    // Show a helpful message after the panel is focused
                    await vscode.window.showInformationMessage('Enter your API key in the VoiceHype panel above');
                } else if (selection === 'Visit voicehype.ai') {
                    await vscode.env.openExternal(vscode.Uri.parse('https://voicehype.ai'));
                }
            }
        } catch (error) {
            console.error('Error checking authentication status:', error);
        }
    }
}
declare module 'replicate' {
    interface ReplicateOptions {
        auth: string;
    }

    interface ReplicateInput {
        [key: string]: any;
    }

    interface ReplicateOutput {
        [key: string]: any;
    }

    class Replicate {
        constructor(options: ReplicateOptions);
        
        run(
            model: string,
            options: { input: ReplicateInput }
        ): Promise<ReplicateOutput>;

        stream(
            model: string,
            options: { input: ReplicateInput }
        ): AsyncIterableIterator<string>;
    }

    export default Replicate;
} 
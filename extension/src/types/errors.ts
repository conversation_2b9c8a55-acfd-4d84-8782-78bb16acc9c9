export class TranscriptionError extends Error {
    constructor(
        message: string,
        public readonly code: TranscriptionErrorCode,
        public readonly isRetryable: boolean = true,
        public readonly originalError?: Error
    ) {
        super(message);
        this.name = 'TranscriptionError';
    }
}

export enum TranscriptionErrorCode {
  EMPTY_AUDIO = 'EMPTY_AUDIO',
  UPLOAD_FAILED = 'UPLOAD_FAILED',
  NETWORK_ERROR = 'NETWORK_ERROR',
  API_ERROR = 'API_ERROR',
  INVALID_RESPONSE = 'INVALID_RESPONSE',
  AUDIO_RECORDING_ERROR = 'AUDIO_RECORDING_ERROR', // Add this
  UNKNOWN = 'UNKNOWN'
}

export function isRetryableError(error: Error): boolean {
    if (error instanceof TranscriptionError) {
        return error.isRetryable;
    }
    // Network errors are generally retryable
    if (error.message.toLowerCase().includes('network') ||
        error.message.toLowerCase().includes('timeout') ||
        error.message.toLowerCase().includes('connection')) {
        return true;
    }
    return false;
}

export function getErrorCode(error: Error): TranscriptionErrorCode {
    if (error instanceof TranscriptionError) {
        return error.code;
    }

    const msg = error.message.toLowerCase();
    if (msg.includes('network') || msg.includes('timeout') || msg.includes('connection')) {
        return TranscriptionErrorCode.NETWORK_ERROR;
    }
    if (msg.includes('upload')) {
        return TranscriptionErrorCode.UPLOAD_FAILED;
    }
    if (msg.includes('api') || msg.includes('status')) {
        return TranscriptionErrorCode.API_ERROR;
    }
    return TranscriptionErrorCode.UNKNOWN;
}

export function getUserFriendlyErrorMessage(error: Error): string {
    if (error instanceof TranscriptionError) {
        return error.message;
    }

    const code = getErrorCode(error);
    switch (code) {
        case TranscriptionErrorCode.EMPTY_AUDIO:
            return 'No audio was detected in the recording. Please try recording again.';
        case TranscriptionErrorCode.UPLOAD_FAILED:
            return 'Failed to upload the recording. Please check your internet connection and try again.';
        case TranscriptionErrorCode.NETWORK_ERROR:
            return 'Network error occurred. Please check your internet connection and try again.';
        case TranscriptionErrorCode.API_ERROR:
            // Check if it's likely an audio recording issue
            if (error.message.toLowerCase().includes('audio') ||
                error.message.toLowerCase().includes('invalid') ||
                error.message.toLowerCase().includes('duration')) {
                return 'Audio recording failed. Please check if your microphone is working and try recording again.';
            }
            return `Service error: ${error.message}. Please try again later.`;
        case TranscriptionErrorCode.INVALID_RESPONSE:
            return `Received invalid response from the service. Please try again. ${error.message}`;
        default:
            return `An unexpected error occurred: ${error.message}`;
    }
} 
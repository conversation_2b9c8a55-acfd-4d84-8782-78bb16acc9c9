declare module 'assemblyai' {
    interface AssemblyAIConfig {
        apiKey: string;
    }

    interface TranscriptResult {
        text?: string;
        error?: string;
        status?: string;
    }

    interface TranscriptOptions {
        audio: string;
        speech_model?: string;
        language_code?: string;
    }

    interface RealtimeTranscript {
        message_type: 'PartialTranscript' | 'FinalTranscript';
        text?: string;
        created: string;
        end_timestamp: number;
        start_timestamp: number;
        confidence: number;
        words: Array<{
            text: string;
            confidence: number;
            start_timestamp: number;
            end_timestamp: number;
        }>;
    }

    interface RealtimeTranscriberOptions {
        sampleRate?: number;
        encoding?: 'pcm' | 'mulaw';
        endUtteranceSilenceThreshold?: number;
        speech_model?: string;
        language_code?: string;
    }

    interface RealtimeTranscriber {
        connect(): Promise<void>;
        disconnect(): Promise<void>;
        send(data: Buffer): void;
        stream(): WritableStream;
        on(event: 'transcript', callback: (transcript: RealtimeTranscript) => void): void;
        on(event: 'transcript.partial', callback: (transcript: RealtimeTranscript) => void): void;
        on(event: 'transcript.final', callback: (transcript: RealtimeTranscript) => void): void;
        on(event: 'error', callback: (error: Error) => void): void;
        on(event: 'close', callback: (code: number, reason: string) => void): void;
        on(event: 'open', callback: (data: { sessionId: string }) => void): void;
    }

    interface RealtimeAPI {
        transcriber(options?: RealtimeTranscriberOptions): RealtimeTranscriber;
    }

    interface TranscriptsAPI {
        transcribe(options: TranscriptOptions): Promise<TranscriptResult>;
    }

    export class AssemblyAI {
        constructor(config: AssemblyAIConfig);
        transcripts: TranscriptsAPI;
        realtime: RealtimeAPI;
    }
} 
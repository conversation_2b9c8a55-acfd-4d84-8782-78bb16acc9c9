declare module 'node-microphone' {
    import { Readable } from 'stream';

    class Microphone {
        constructor(options?: {
            endian?: 'little' | 'big';
            bitwidth?: 8 | 16 | 24 | 32;
            encoding?: 'signed-integer' | 'unsigned-integer';
            rate?: number;
            channels?: number;
            device?: string;
            debug?: boolean;
        });

        startRecording(): Readable;
        stopRecording(): void;
    }

    export = Microphone;
} 
declare module '@voicehype/audify-plus' {
    import { EventEmitter } from 'events';
    import { Stream, Readable } from 'stream';

    export enum RtAudioFormat {
        RTAUDIO_SINT8 = 0x1,    // 8-bit signed integer
        RTAUDIO_SINT16 = 0x2,   // 16-bit signed integer
        RTAUDIO_SINT24 = 0x4,   // 24-bit signed integer
        RTAUDIO_SINT32 = 0x8,   // 32-bit signed integer
        RTAUDIO_FLOAT32 = 0x10, // 32-bit float
        RTAUDIO_FLOAT64 = 0x20  // 64-bit float
    }

    export enum RtAudioApi {
        UNSPECIFIED,
        LINUX_ALSA,
        LINUX_PULSE,
        LINUX_OSS,
        UNIX_JACK,
        MACOSX_CORE,
        WINDOWS_WASAPI,
        WINDOWS_ASIO,
        WINDOWS_DS,
        RTAUDIO_DUMMY
    }

    export interface RtAudioDevice {
        name: string;
        outputChannels: number;
        inputChannels: number;
        duplexChannels: number;
        isDefaultOutput: boolean;
        isDefaultInput: boolean;
        sampleRates: number[];
        preferredSampleRate: number;
        nativeFormats: number;
    }

    export interface RtAudioStreamParameters {
        deviceId?: number;
        nChannels: number;
        firstChannel: number;
    }

    export class RtAudio extends EventEmitter {
        constructor(api?: RtAudioApi);
        
        /**
         * Get information for the specified audio device
         */
        getDeviceInfo(device: number): RtAudioDevice;
        
        /**
         * Get array of audio device information
         */
        getDevices(): RtAudioDevice[];
        
        /**
         * Get the default input device ID
         */
        getDefaultInputDevice(): number;
        
        /**
         * Get the default output device ID
         */
        getDefaultOutputDevice(): number;
        
        /**
         * Open a stream for audio input/output
         */
        openStream(
            outputParameters: RtAudioStreamParameters | null,
            inputParameters: RtAudioStreamParameters | null,
            format: RtAudioFormat,
            sampleRate: number,
            frameSize: number,
            streamName: string,
            callback: (pcmData: Buffer) => void
        ): void;
        
        /**
         * Start the audio stream
         */
        start(): void;
        
        /**
         * Stop the audio stream
         */
        stop(): void;
        
        /**
         * Close the audio stream
         */
        closeStream(): void;
        
        /**
         * Returns true if the stream is open
         */
        isStreamOpen(): boolean;
        
        /**
         * Returns true if the stream is running
         */
        isStreamRunning(): boolean;
    }
}

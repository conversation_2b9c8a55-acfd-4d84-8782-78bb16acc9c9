// The module 'vscode' contains the VS Code extensibility API
import * as vscode from 'vscode';
import { activate as mainActivate, deactivate as mainDeactivate } from './extension';

// This method is called when your extension is activated
export function activate(context: vscode.ExtensionContext) {
    return mainActivate(context);
}

// This method is called when your extension is deactivated
export function deactivate() {
    return mainDeactivate();
}
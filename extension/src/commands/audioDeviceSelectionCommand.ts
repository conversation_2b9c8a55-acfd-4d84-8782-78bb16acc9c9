import * as vscode from 'vscode';
import { AudioDeviceManager } from '../utils/audioDeviceManager';

/**
 * Command to show the audio device selection UI
 */
export function registerAudioDeviceSelectionCommand(context: vscode.ExtensionContext): void {
    const command = vscode.commands.registerCommand('voicehype.selectAudioDevice', async () => {
        // Get the AudioDeviceManager instance
        const deviceManager = AudioDeviceManager.getInstance();
        
        // Show the device selection menu
        await deviceManager.showDeviceSelectionMenu();
    });
    
    context.subscriptions.push(command);
}

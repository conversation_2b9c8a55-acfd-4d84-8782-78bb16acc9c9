interface VSCodeAPI {
  postMessage(message: any): void
  getState(): any
  setState(state: any): void
}

// Theme state management
let currentTheme = 'light'

// Mock VSCode API implementation
const mockVscode = {
  postMessage: (message: any) => {
    console.log('Mock VSCode postMessage:', message)
    // Handle theme change messages
    if (message.command === 'updateTheme') {
      currentTheme = message.theme
      updateDocumentTheme()
    }
  },
  getState: () => ({ theme: currentTheme }),
  setState: (state: any) => {
    console.log('Mock VSCode setState:', state)
    if (state?.theme) {
      currentTheme = state.theme
      updateDocumentTheme()
    }
  }
}

// Update document theme classes
const updateDocumentTheme = () => {
  if (currentTheme === 'dark') {
    document.documentElement.classList.add('dark')
    document.body.classList.add('vscode-dark')
    document.body.classList.remove('vscode-light')
  } else {
    document.documentElement.classList.remove('dark')
    document.body.classList.add('vscode-light')
    document.body.classList.remove('vscode-dark')
  }
}

// Initialize theme from localStorage or system preference
const savedTheme = localStorage.getItem('voicehype-theme')
if (savedTheme) {
  currentTheme = savedTheme
} else {
  currentTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
}
updateDocumentTheme()

// Acquire the VS Code API object
const acquireVsCodeApi = (): VSCodeAPI => {
  // Use mock implementation in test environment
  if (process.env.NODE_ENV === 'development') {
    return mockVscode
  }

  // Use real implementation in production
  return (window as any).acquireVsCodeApi()
}

// Export the VS Code API instance
export const vscode = acquireVsCodeApi()

/**
 * Get the URI for a resource in the extension
 * @param path Path to the resource relative to the extension root
 * @returns The URI for the resource
 */
export const getResourceUri = (path: string): string => {
  // In development mode, we can't use the VS Code URI scheme
  if (process.env.NODE_ENV === 'development') {
    return path
  }

  // Get the URI from the current document URL
  const baseUrl = document.location.href.split('/').slice(0, -1).join('/')

  // The path should be relative to the extension root
  // For root level files, we need to use a special format
  if (path.startsWith('/')) {
    path = path.substring(1)
  }

  // Use the vscode-resource scheme for accessing files
  return `${baseUrl}/../../../../${path}`
}

<script setup lang="ts">
import { ref, onMounted, watch, computed, reactive } from 'vue'
import ApiKeyInput from './components/ApiKeyInput.vue'
import DarkModeToggle from './components/DarkModeToggle.vue'
import CustomPrompt from './components/CustomPrompt.vue'
import Logo from './components/Logo.vue'
import ModelSelector from './components/ModelSelector.vue'
import OptimizationModelSelector from './components/OptimizationModelSelector.vue'
import RecordingControls from './components/RecordingControls.vue'
import ServiceSelector from './components/ServiceSelector.vue'
import LanguageSelector from './components/LanguageSelector.vue'
import AudioSettings from './components/AudioSettings.vue'
import Switch from './components/Switch.vue'
import RecentTranscriptions from './components/RecentTranscriptions.vue'
import PromptModeSelector from './components/PromptModeSelector.vue'
import ConfigurationManager from './components/ConfigurationManager.vue'
import Snackbar from './components/Snackbar.vue'
import WhatsNewModal from './components/WhatsNewModal.vue'
import { vscode } from './utilities/vscode'
import type { Configuration, ConfigurationSettings, SnackbarMessage, WhatsNewInfo } from './types/configuration'
import { DEFAULT_CONFIGURATION_SETTINGS, WHATS_NEW_141 } from './types/configuration'

interface Transcription {
  id: string
  timestamp: string
  originalText: string
  optimizedText?: string
  service: string
  model: string
  language: string
}

interface LastUpdates {
  service: number
  model: number
  language: number
  optimize: number
  optimizationModel: number
  translate: number
  customPrompt: number
  realtime: number
  sampleRate: number
  deviceId: number
}

interface AudioDevice {
  id: string
  name: string
}

interface StateRef {
  service: string
  model: string
  language: string
  optimizeEnabled: boolean
  optimizationModel: string
  translate: boolean
  realtime: boolean
  customPrompt: string
  sampleRate: number
  deviceId: string | null
  apiKey: string
}

// State management
const initialConfigLoaded = ref(false)
const isRecording = ref(false)
const isPaused = ref(false)
const elapsedTime = ref(0)
const service = ref('assemblyai')
const model = ref('whisper-1')
const lastAssemblyAIModel = ref('best')
const language = ref('en')
const customPrompt = ref('Correct any grammar issues and improve clarity. Keep the meaning intact.')
const handlePromptModeChange = (modeId: string) => {
  // Handle mode change if needed
}
const optimizeEnabled = ref(true)
const optimizationModel = ref('gpt-4o')
const translate = ref(false)
const realtime = ref(false)
const sampleRate = ref(22050)
const deviceId = ref<string | null>(null)
const availableDevices = ref<AudioDevice[]>([])
const apiKey = ref('')
const transcriptions = ref<Transcription[]>([])

// Configuration state
const configurations = ref<Configuration[]>([])
const activeConfigurationId = ref<string | null>(null)
const isConfigurationsLoading = ref(false)
const showConfigurationManager = ref(false)
const snackbarMessage = ref<SnackbarMessage | null>(null)
const whatsNewInfo = ref<WhatsNewInfo>(WHATS_NEW_141)
const showWhatsNew = ref(false)

const lastUpdates = ref<LastUpdates>({
  service: 0,
  model: 0,
  language: 0,
  optimize: 0,
  optimizationModel: 0,
  translate: 0,
  customPrompt: 0,
  realtime: 0,
  sampleRate: 0,
  deviceId: 0
})

const stateRef = ref<StateRef>({
  service: service.value,
  model: model.value,
  language: language.value,
  optimizeEnabled: optimizeEnabled.value,
  optimizationModel: optimizationModel.value,
  translate: translate.value,
  realtime: realtime.value,
  customPrompt: customPrompt.value,
  sampleRate: sampleRate.value,
  deviceId: deviceId.value,
  apiKey: apiKey.value
})

// Keep stateRef in sync
watch([service, model, language, optimizeEnabled, optimizationModel, translate, realtime, customPrompt, sampleRate, deviceId, apiKey], () => {
  stateRef.value = {
    service: service.value,
    model: model.value,
    language: language.value,
    optimizeEnabled: optimizeEnabled.value,
    optimizationModel: optimizationModel.value,
    translate: translate.value,
    realtime: realtime.value,
    customPrompt: customPrompt.value,
    sampleRate: sampleRate.value,
    deviceId: deviceId.value,
    apiKey: apiKey.value
  }
})

// Message handling
const handleMessage = (event: MessageEvent) => {
  const message = event.data

  switch (message.command) {
    case 'updateOptions':
      if (message.options) {
        if (message.options.service !== undefined) service.value = message.options.service
        if (message.options.model !== undefined) model.value = message.options.model
        if (message.options.language !== undefined) language.value = message.options.language
        if (message.options.optimize !== undefined) optimizeEnabled.value = message.options.optimize
        if (message.options.optimizationModel !== undefined) optimizationModel.value = message.options.optimizationModel
        if (message.options.translate !== undefined) translate.value = message.options.translate
        if (message.options.realtime !== undefined) realtime.value = message.options.realtime
        if (message.options.customPrompt !== undefined) customPrompt.value = message.options.customPrompt
        if (message.options.sampleRate !== undefined) sampleRate.value = message.options.sampleRate
        if (message.options.deviceId !== undefined) deviceId.value = message.options.deviceId
        if (message.options.apiKey !== undefined) apiKey.value = message.options.apiKey
      }
      break

    case 'updateTranscriptions':
      if (message.transcriptions) {
        transcriptions.value = message.transcriptions
      }
      break

    case 'updateRecordingState':
      if (message.state) {
        isRecording.value = message.state.isRecording || false
        isPaused.value = message.state.isPaused || false
        elapsedTime.value = message.state.elapsedTime || 0
      }
      break

    case 'configurationsUpdated':
      if (message.data) {
        configurations.value = message.data.configurations || []
        activeConfigurationId.value = message.data.activeConfigurationId || null
      }
      break

    case 'configurationSwitched':
      if (message.data) {
        activeConfigurationId.value = message.data.configurationId
        if (message.data.configurationName) {
          showSnackbar(`Switched to '${message.data.configurationName}'`, 'success')
        }
      }
      break

    case 'whatsNewStatus':
      if (message.data) {
        showWhatsNew.value = !message.data.hasBeenShown
      }
      break

    case 'error':
      if (message.error) {
        showSnackbar(message.error, 'error')
      }
      break
  }
}

onMounted(() => {
  window.addEventListener('message', handleMessage)
  vscode.postMessage({ command: 'getOptions' })
  vscode.postMessage({ command: 'getTranscriptions' })
  vscode.postMessage({ command: 'getAudioSettings' })
  vscode.postMessage({ command: 'getConfigurations' })
  vscode.postMessage({ command: 'getWhatsNewStatus' })

  return () => {
    window.removeEventListener('message', handleMessage)
  }
})

// Recording handlers
const handleStartRecording = () => {
  isRecording.value = true
  isPaused.value = false
  vscode.postMessage({ command: 'startRecording' })
}

const handleStopRecording = () => {
  isRecording.value = false
  isPaused.value = false
  vscode.postMessage({ command: 'stopRecording' })
}

const handlePauseRecording = () => {
  isPaused.value = true
  vscode.postMessage({ command: 'pauseRecording' })
}

const handleResumeRecording = () => {
  isPaused.value = false
  vscode.postMessage({ command: 'resumeRecording' })
}

const handleCancelRecording = () => {
  isRecording.value = false
  isPaused.value = false
  vscode.postMessage({ command: 'cancelRecording' })
}

// Service and model handlers
const handleServiceChange = (newService: string) => {
  service.value = newService
  vscode.postMessage({ command: 'updateService', service: newService })
}

const handleModelChange = (newModel: string) => {
  model.value = newModel
  vscode.postMessage({ command: 'updateModel', model: newModel })
}

const handleLanguageChange = (newLanguage: string) => {
  language.value = newLanguage
  vscode.postMessage({ command: 'updateLanguage', language: newLanguage })
}

// Audio settings handlers
const handleSampleRateChange = (newSampleRate: number) => {
  sampleRate.value = newSampleRate
  vscode.postMessage({ command: 'updateSampleRate', sampleRate: newSampleRate })
}

const handleDeviceChange = (newDeviceId: string | null) => {
  deviceId.value = newDeviceId
  vscode.postMessage({ command: 'updateDevice', deviceId: newDeviceId })
}

// Toggle handlers
const handleTranslateToggle = (checked: boolean) => {
  translate.value = checked
  vscode.postMessage({ command: 'updateTranslate', translate: checked })
}

const handleRealtimeToggle = (checked: boolean) => {
  realtime.value = checked
  vscode.postMessage({ command: 'updateRealtime', realtime: checked })
}

const handleOptimizeToggle = (checked: boolean) => {
  optimizeEnabled.value = checked
  vscode.postMessage({ command: 'updateOptimize', optimize: checked })
}

// Transcription handlers
const handleCopyOriginal = (text: string) => {
  vscode.postMessage({ command: 'copyText', text })
}

const handleCopyOptimized = (text: string) => {
  vscode.postMessage({ command: 'copyText', text })
}

const handleOptimizationModelChange = (newModel: string) => {
  optimizationModel.value = newModel
  vscode.postMessage({ command: 'updateOptimizationModel', model: newModel })
}

const handleOptimize = (transcriptionId: string) => {
  vscode.postMessage({ command: 'optimizeTranscription', id: transcriptionId })
}

// Computed properties
const currentSettings = computed<ConfigurationSettings>(() => ({
  service: service.value,
  model: model.value,
  language: language.value,
  translate: translate.value,
  realtime: realtime.value,
  optimizeEnabled: optimizeEnabled.value,
  optimizationModel: optimizationModel.value,
  customPrompt: customPrompt.value,
  sampleRate: sampleRate.value,
  deviceId: deviceId.value
}))

// Configuration handlers
const handleCreateConfiguration = (configData: Partial<Configuration>) => {
  vscode.postMessage({ command: 'createConfiguration', data: configData })
}

const handleUpdateConfiguration = (configData: Partial<Configuration>) => {
  vscode.postMessage({ command: 'updateConfiguration', data: configData })
}

const handleDeleteConfiguration = (configId: string) => {
  vscode.postMessage({ command: 'deleteConfiguration', data: { id: configId } })
}

const handleActivateConfiguration = (configId: string) => {
  vscode.postMessage({ command: 'switchConfiguration', data: { id: configId } })
}

const handleDuplicateConfiguration = (configId: string) => {
  const config = configurations.value.find(c => c.id === configId)
  if (config) {
    const duplicateData = {
      title: `${config.title} (Copy)`,
      description: config.description,
      settings: { ...config.settings }
    }
    handleCreateConfiguration(duplicateData)
  }
}

// Snackbar handlers
const showSnackbar = (message: string, type: 'success' | 'error' | 'info' | 'warning' = 'info', duration = 3000) => {
  const id = Date.now().toString()
  snackbarMessage.value = {
    id,
    message,
    type,
    duration,
    visible: true
  }
}

const handleCloseSnackbar = (id: string) => {
  if (snackbarMessage.value?.id === id) {
    snackbarMessage.value = { ...snackbarMessage.value, visible: false }
    setTimeout(() => {
      snackbarMessage.value = null
    }, 200)
  }
}

// What's New handlers
const handleMarkWhatsNewSeen = () => {
  vscode.postMessage({ command: 'markWhatsNewSeen', data: { version: whatsNewInfo.value.version } })
  showWhatsNew.value = false
}

const handleCloseWhatsNew = () => {
  showWhatsNew.value = false
}

// Configuration Manager toggle
const toggleConfigurationManager = () => {
  showConfigurationManager.value = !showConfigurationManager.value
}
</script>

<template>
  <div class="min-w-[320px] flex flex-col h-full px-2 sm:px-4 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100">
    <!-- Header Section -->
    <div class="flex items-center justify-between py-4 mb-2">
      <Logo />
      <div class="flex items-center gap-2">
        <button
          @click="toggleConfigurationManager"
          class="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
          title="Manage Configurations"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        </button>
        <DarkModeToggle />
      </div>
    </div>

    <!-- API Key Input -->
    <ApiKeyInput :initialApiKey="apiKey" />

    <!-- Recording controls -->
    <div class="mb-5">
      <RecordingControls
        :isRecording="isRecording"
        :isPaused="isPaused"
        :elapsedTime="elapsedTime"
        @start="handleStartRecording"
        @stop="handleStopRecording"
        @pause="handlePauseRecording"
        @resume="handleResumeRecording"
        @cancel="handleCancelRecording"
      />
    </div>

    <!-- Service and model settings -->
    <div class="min-w-0 mb-4 space-y-4">
      <ServiceSelector
        :service="service"
        @change="handleServiceChange"
      />

      <ModelSelector
        :service="service"
        :model="model"
        @change="handleModelChange"
      />

      <LanguageSelector
        :service="service"
        :model="model"
        :language="language"
        @change="handleLanguageChange"
      />

      <div class="space-y-2">
        <Switch
          v-if="service === 'assemblyai' && model === 'best'"
          :checked="realtime"
          @change="handleRealtimeToggle"
          label="Use real-time transcription"
        />
      </div>
    </div>

    <!-- Audio Settings -->
    <div class="mb-4">
      <AudioSettings
        :sampleRate="sampleRate"
        :deviceId="deviceId"
        :availableDevices="availableDevices"
        @sampleRateChange="handleSampleRateChange"
        @deviceChange="handleDeviceChange"
      />
    </div>

    <!-- Optimization and translation toggles -->
    <div class="mb-4 space-y-2">
      <Switch
        :checked="optimizeEnabled"
        @change="handleOptimizeToggle"
        label="Optimize transcription"
      />
      <Switch
        v-if="service === 'lemonfox'"
        :checked="translate"
        @change="handleTranslateToggle"
        label="Translate to English"
      />
    </div>

    <!-- Custom Prompt -->
    <div class="mb-4">
      <CustomPrompt v-model="customPrompt" />
    </div>

    <!-- Prompt Mode Selector -->
    <div class="mb-4">
      <PromptModeSelector
        v-model="customPrompt"
        @modeChange="handlePromptModeChange"
      />
    </div>

    <!-- Optimization Model Selector -->
    <div class="mb-4">
      <OptimizationModelSelector
        :optimizationModel="optimizationModel"
        @change="handleOptimizationModelChange"
      />
    </div>


    <!-- Recent transcriptions -->
    <div class="h-[calc(100%-1.75rem)] overflow-auto">
      <RecentTranscriptions
        :transcriptions="transcriptions"
        @copyOriginal="handleCopyOriginal"
        @copyOptimized="handleCopyOptimized"
        @optimize="handleOptimize"
      />
    </div>

    <!-- Configuration Manager Modal -->
    <ConfigurationManager
      v-if="showConfigurationManager"
      :configurations="configurations"
      :activeConfigurationId="activeConfigurationId"
      :currentSettings="currentSettings"
      :isLoading="isConfigurationsLoading"
      @create="handleCreateConfiguration"
      @update="handleUpdateConfiguration"
      @delete="handleDeleteConfiguration"
      @activate="handleActivateConfiguration"
      @duplicate="handleDuplicateConfiguration"
      class="fixed inset-0 z-50 bg-white dark:bg-gray-900 overflow-y-auto p-4"
    />

    <!-- Snackbar -->
    <Snackbar
      :message="snackbarMessage"
      @close="handleCloseSnackbar"
    />

    <!-- What's New Modal -->
    <WhatsNewModal
      :isOpen="showWhatsNew"
      :whatsNewInfo="whatsNewInfo"
      @close="handleCloseWhatsNew"
      @markAsSeen="handleMarkWhatsNewSeen"
    />
  </div>
</template>

<script setup lang="ts">
interface Props {
  className?: string
}

defineProps<Props>()
</script>

<template>
  <div :class="['logo-container', className]">
    <img 
      src="/assets/voicehype_logo.png" 
      alt="VoiceHype Logo" 
      class="logo-image"
    />
  </div>
</template>

<style scoped>
.logo-container {
  display: flex;
  justify-content: center;
}

.logo-image {
  max-width: 100%;
  height: auto;
}
</style>

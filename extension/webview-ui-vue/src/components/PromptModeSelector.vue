<script setup lang="ts">
import { ref, watch } from 'vue'
import { vscode } from '../utilities/vscode'
import Button from './Button.vue'

interface PromptMode {
  id: string
  name: string
  prompt: string
  isCustom?: boolean
}

interface Props {
  modelValue: string
  onModeChange?: (modeId: string) => void
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue'])

// Add custom mode option
const CUSTOM_MODE: PromptMode = {
  id: 'custom',
  name: 'Custom',
  prompt: '',
  isCustom: true
}

const DEFAULT_MODES: PromptMode[] = [
  {
    id: 'clean-up',
    name: 'Clean Up',
    prompt: 'Clean up the text by removing filler words like "um", "you know", "like", fix any grammar mistakes, and make the transcript coherent and smooth, while preserving the original meaning. Return ONLY the cleaned text without any additional commentary or conversational responses.'
  },
  {
    id: 'translate',
    name: 'Translate',
    prompt: 'Translate the following transcript into [TARGET_LANGUAGE], keeping it natural and respectful of tone and cultural context. Do not summarize, just translate as clearly as possible.'
  },
  {
    id: 'summarize',
    name: 'Summarize',
    prompt: 'Summarize the following transcript into a short, coherent paragraph that captures the key ideas, tone, and intention of the speaker. Return ONLY the summarized text without any introductory phrases or additional commentary.'
  },
  {
    id: 'polish',
    name: 'Polish',
    prompt: 'Rewrite the following transcript to make it sound formal and professional, correcting grammar and sentence structure, while preserving the speaker\'s message.'
  },
  {
    id: 'expand',
    name: 'Expand',
    prompt: 'Expand and elaborate the transcript into a well-written, detailed explanation or article, adding transitions and clarifications where needed, while staying faithful to the speaker\'s intent.'
  },
  {
    id: 'light-touch',
    name: 'Light Touch',
    prompt: 'Apply minimal editing to fix major grammatical issues and incoherence, but keep the casual tone and natural flow of the original speech.'
  },
  {
    id: 'bullet-points',
    name: 'Bullet Points',
    prompt: 'Convert the following transcript into a clear, concise list of bullet points summarizing the main ideas discussed.'
  },
  {
    id: 'islamic-tone',
    name: 'Islamic Tone',
    prompt: 'Rewrite the following transcript with motivational Islamic guidance. Use an uplifting tone with Islamic values and etiquette, incorporating phrases like "InshaAllah", "Alhamdulillah", and motivational reminders from Quran and Sunnah. Encourage positive action while maintaining the original meaning. Focus on inspiring the reader to grow spiritually and improve themselves.'
  },
  {
    id: 'whatsapp-style',
    name: 'WhatsApp Style',
    prompt: 'Rewrite the following text as a short, casual WhatsApp message. Keep it friendly and easy to understand.'
  },
  {
    id: 'email-format',
    name: 'Email Format',
    prompt: 'Rewrite the following transcript into a professional email. Include a greeting, body, and closing, and keep the tone polite and formal.'
  },
  {
    id: 'reminder-note',
    name: 'Reminder Note',
    prompt: 'Convert the following transcript into a short personal reminder, using brief and clear language.'
  },
  {
    id: 'to-do-list',
    name: 'To-Do List',
    prompt: 'Extract a to-do list from the following transcript. Format it as simple, actionable tasks in bullet points.'
  },
  {
    id: 'meeting-minutes',
    name: 'Meeting Minutes',
    prompt: 'Format the following text into structured meeting minutes, listing topics discussed, decisions made, and action items clearly.'
  },
  {
    id: 'social-caption',
    name: 'Social Caption',
    prompt: 'Rewrite the text into a short, engaging social media caption suitable for Instagram, Twitter, or Facebook, keeping the tone casual and inviting.'
  }
]

const modes = ref<PromptMode[]>([...DEFAULT_MODES, CUSTOM_MODE])
const activeMode = ref('clean-up')
const showCustomPrompt = ref(false)
const selectedPrompt = ref('')
const customModeName = ref('')
const targetLanguage = ref('English')
const debouncedLanguage = ref('English')

// Debounce target language updates (500ms delay)
let debounceTimeout: ReturnType<typeof setTimeout>
watch(targetLanguage, (newVal) => {
  clearTimeout(debounceTimeout)
  debounceTimeout = setTimeout(() => {
    debouncedLanguage.value = newVal
  }, 500)
})

// Load saved custom modes from extension
vscode.postMessage({
  command: 'getPromptModes',
  persist: true
})

window.addEventListener('message', (event: MessageEvent) => {
  if (event.data.command === 'promptModes') {
    const customModes = event.data.modes || []
    modes.value = [...DEFAULT_MODES, ...customModes, CUSTOM_MODE]
  }
})

// Update selectedPrompt whenever mode changes or debounced language updates
watch([activeMode, debouncedLanguage], () => {
  const selectedMode = modes.value.find(mode => mode.id === activeMode.value)
  if (selectedMode && selectedMode.id !== 'custom') {
    let prompt = selectedMode.prompt
    if (selectedMode.id === 'translate') {
      prompt = prompt.replace('[TARGET_LANGUAGE]', debouncedLanguage.value)
      emit('update:modelValue', prompt)
    }
    selectedPrompt.value = prompt
  }
})

const handleModeSelect = (mode: PromptMode) => {
  activeMode.value = mode.id
  
  if (mode.id !== 'custom') {
    emit('update:modelValue', mode.prompt)
    selectedPrompt.value = mode.prompt
  }
  
  props.onModeChange?.(mode.id)
  showCustomPrompt.value = mode.id === 'custom'
}

const handleDeleteMode = (mode: PromptMode) => {
  vscode.postMessage({
    command: 'deletePromptMode',
    modeId: mode.id
  })
  modes.value = modes.value.filter(m => m.id !== mode.id)
  if (activeMode.value === mode.id) {
    activeMode.value = 'clean-up'
  }
}

const saveCustomMode = () => {
  if (props.modelValue && props.modelValue.trim()) {
    const modeName = customModeName.value.trim()
      ? customModeName.value.trim()
      : `Custom ${new Date().toLocaleString()}`
      
    vscode.postMessage({
      command: 'savePromptMode',
      mode: {
        id: `custom-${Date.now()}`,
        name: modeName,
        prompt: selectedPrompt.value,
        isCustom: true
      }
    })
    
    customModeName.value = ''
    emit('update:modelValue', '')
  }
}
</script>

<template>
  <div class="space-y-3">
    <div class="flex flex-wrap gap-2">
      <div
        v-for="mode in modes"
        :key="mode.id"
        class="flex items-center"
      >
        <div
          @click="handleModeSelect(mode)"
          :class="[
            'px-3 py-1.5 rounded-full border transition-colors cursor-pointer',
            activeMode === mode.id
              ? 'bg-primary dark:bg-primary border-primary dark:border-primary text-black font-bold'
              : 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
          ]"
        >
          <span class="text-sm font-medium select-none">
            {{ mode.name }}
          </span>
        </div>
        <button
          v-if="mode.isCustom && mode.id !== 'custom'"
            @click="handleDeleteMode(mode)"
          class="hover:text-red-700 p-1 ml-1 text-red-500 rounded-full"
          title="Delete custom mode"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        </button>
      </div>
    </div>

    <div v-if="activeMode === 'translate'" class="mt-3">
      <label class="dark:text-gray-300 block mb-1 text-sm font-medium text-gray-700">
        Target Language
      </label>
      <input
        type="text"
        v-model="targetLanguage"
        placeholder="Enter target language (e.g. French, Arabic)"
        class="dark:border-gray-600 dark:bg-gray-700 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 w-full p-2 bg-white border border-gray-300 rounded-md shadow-sm"
      />
    </div>

    <div
      v-if="activeMode !== 'custom' && !showCustomPrompt && selectedPrompt"
      class="dark:border-gray-600 bg-gray-50 dark:bg-gray-800 p-3 mt-3 border border-gray-300 rounded-md"
    >
      <p class="dark:text-gray-300 text-sm text-gray-600">
        {{ selectedPrompt }}
      </p>
    </div>

    <div v-if="showCustomPrompt" class="mt-3">
      <div class="mb-3">
        <label class="dark:text-gray-300 block mb-1 text-sm font-medium text-gray-700">
          Mode Name
        </label>
        <input
          type="text"
          v-model="customModeName"
          placeholder="Enter a name for this mode"
          class="dark:border-gray-600 dark:bg-gray-700 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 w-full p-2 bg-white border border-gray-300 rounded-md shadow-sm"
        />
      </div>

      <label class="dark:text-gray-300 block mb-1 text-sm font-medium text-gray-700">
        Prompt Text
      </label>
      <textarea
        :value="modelValue"
        @input="(e) => emit('update:modelValue', (e.target as HTMLTextAreaElement).value)"
        placeholder="Enter custom prompt..."
        rows="4"
        class="dark:border-gray-600 dark:bg-gray-700 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 w-full p-2 bg-white border border-gray-300 rounded-md shadow-sm resize-none"
      />
      <div class="flex items-center justify-between mt-2">
        <div class="dark:text-gray-400 text-sm text-gray-500">
          Create a custom optimization mode
        </div>
        <Button
          size="sm"
          @click="saveCustomMode"
          :disabled="!modelValue || !modelValue.trim()"
        >
          Save Mode
        </Button>
      </div>
    </div>
  </div>
</template>

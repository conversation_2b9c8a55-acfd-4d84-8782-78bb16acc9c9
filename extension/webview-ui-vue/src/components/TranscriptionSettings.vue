<script setup lang="ts">
import { computed } from 'vue'
import { BoltIcon } from '@heroicons/vue/24/solid'
import Switch from './Switch.vue'

interface Props {
  service: string
  model: string
  language: string
  shouldOptimize: boolean
  realtime: boolean
}

const props = defineProps<Props>()
const emit = defineEmits([
  'update:service',
  'update:model',
  'update:language',
  'update:shouldOptimize',
  'update:realtime'
])

const showRealtime = computed(() => 
  (props.service === 'lemonfox' && ['gpt-4o-mini-transcribe', 'gpt-4o-transcribe'].includes(props.model)) ||
  (props.service === 'assemblyai' && props.model === 'best')
)
</script>

<template>

</template>

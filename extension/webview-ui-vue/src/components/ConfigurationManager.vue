<script setup lang="ts">
import { ref, computed } from 'vue'
import type { Configuration, ConfigurationSettings } from '../types/configuration'
import ConfigurationCard from './ConfigurationCard.vue'
import ConfigurationEditor from './ConfigurationEditor.vue'
import Button from './Button.vue'

interface Props {
  configurations: Configuration[]
  activeConfigurationId: string | null
  currentSettings: ConfigurationSettings
  isLoading: boolean
}

interface Emits {
  (e: 'create', config: Partial<Configuration>): void
  (e: 'update', config: Partial<Configuration>): void
  (e: 'delete', configId: string): void
  (e: 'activate', configId: string): void
  (e: 'duplicate', configId: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Local state
const isEditorOpen = ref(false)
const editingConfiguration = ref<Configuration | null>(null)
const showDeleteConfirm = ref(false)
const configToDelete = ref<string | null>(null)

// Computed
const sortedConfigurations = computed(() => {
  return [...props.configurations].sort((a, b) => {
    // Default configuration first
    if (a.isDefault && !b.isDefault) return -1
    if (!a.isDefault && b.isDefault) return 1
    
    // Then by creation date
    return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
  })
})

const hasConfigurations = computed(() => props.configurations.length > 0)

// Methods
const handleCreateNew = () => {
  editingConfiguration.value = null
  isEditorOpen.value = true
}

const handleEdit = (configId: string) => {
  const config = props.configurations.find(c => c.id === configId)
  if (config) {
    editingConfiguration.value = config
    isEditorOpen.value = true
  }
}

const handleDelete = (configId: string) => {
  const config = props.configurations.find(c => c.id === configId)
  if (config && !config.isDefault) {
    configToDelete.value = configId
    showDeleteConfirm.value = true
  }
}

const confirmDelete = () => {
  if (configToDelete.value) {
    emit('delete', configToDelete.value)
    configToDelete.value = null
    showDeleteConfirm.value = false
  }
}

const cancelDelete = () => {
  configToDelete.value = null
  showDeleteConfirm.value = false
}

const handleActivate = (configId: string) => {
  emit('activate', configId)
}

const handleDuplicate = (configId: string) => {
  emit('duplicate', configId)
}

const handleSaveConfiguration = (configData: Partial<Configuration>) => {
  if (editingConfiguration.value) {
    emit('update', configData)
  } else {
    emit('create', configData)
  }
  isEditorOpen.value = false
  editingConfiguration.value = null
}

const handleCloseEditor = () => {
  isEditorOpen.value = false
  editingConfiguration.value = null
}

const getConfigToDeleteName = () => {
  if (!configToDelete.value) return ''
  const config = props.configurations.find(c => c.id === configToDelete.value)
  return config?.title || ''
}
</script>

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100">
          Configurations
        </h2>
        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Manage your VoiceHype configurations for different use cases
        </p>
      </div>
      
      <Button
        variant="primary"
        @click="handleCreateNew"
        class="flex items-center gap-2"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
        </svg>
        New Configuration
      </Button>
    </div>
    
    <!-- Loading State -->
    <div v-if="isLoading" class="flex items-center justify-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary dark:border-dark-primary"></div>
    </div>
    
    <!-- Empty State -->
    <div v-else-if="!hasConfigurations" class="text-center py-12">
      <div class="mx-auto w-24 h-24 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
        <svg class="w-12 h-12 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      </div>
      <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
        No configurations yet
      </h3>
      <p class="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
        Create your first configuration to save and quickly switch between different VoiceHype settings.
      </p>
      <Button variant="primary" @click="handleCreateNew">
        Create Your First Configuration
      </Button>
    </div>
    
    <!-- Configurations Grid -->
    <div v-else class="grid grid-cols-1 lg:grid-cols-2 gap-4">
      <ConfigurationCard
        v-for="config in sortedConfigurations"
        :key="config.id"
        :configuration="config"
        :isActive="config.id === activeConfigurationId"
        @activate="handleActivate"
        @edit="handleEdit"
        @delete="handleDelete"
        @duplicate="handleDuplicate"
      />
    </div>
    
    <!-- Keyboard Shortcuts Info -->
    <div class="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
      <h4 class="font-medium text-blue-900 dark:text-blue-100 mb-2">
        ⌨️ Keyboard Shortcuts
      </h4>
      <div class="text-sm text-blue-800 dark:text-blue-200 space-y-1">
        <div><kbd class="px-2 py-1 bg-blue-100 dark:bg-blue-800 rounded text-xs">Ctrl+Shift+PageUp</kbd> - Switch to next configuration</div>
        <div><kbd class="px-2 py-1 bg-blue-100 dark:bg-blue-800 rounded text-xs">Ctrl+Shift+PageDown</kbd> - Switch to previous configuration</div>
      </div>
    </div>
    
    <!-- Configuration Editor Modal -->
    <ConfigurationEditor
      :isOpen="isEditorOpen"
      :configuration="editingConfiguration"
      :currentSettings="currentSettings"
      @close="handleCloseEditor"
      @save="handleSaveConfiguration"
    />
    
    <!-- Delete Confirmation Modal -->
    <Transition
      enter-active-class="transition-opacity duration-300"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition-opacity duration-200"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div
        v-if="showDeleteConfirm"
        class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
        @click="cancelDelete"
      >
        <div
          class="bg-white dark:bg-dark-surface rounded-lg shadow-xl max-w-md w-full p-6"
          @click.stop
        >
          <div class="flex items-center gap-3 mb-4">
            <div class="w-10 h-10 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center">
              <svg class="w-5 h-5 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                Delete Configuration
              </h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">
                This action cannot be undone.
              </p>
            </div>
          </div>
          
          <p class="text-gray-700 dark:text-gray-300 mb-6">
            Are you sure you want to delete the configuration "<strong>{{ getConfigToDeleteName() }}</strong>"?
          </p>
          
          <div class="flex items-center justify-end gap-3">
            <Button variant="secondary" @click="cancelDelete">
              Cancel
            </Button>
            <Button variant="danger" @click="confirmDelete">
              Delete Configuration
            </Button>
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>

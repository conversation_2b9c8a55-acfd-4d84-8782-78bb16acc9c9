<script setup lang="ts">
import { ref, watch } from 'vue'
import { InformationCircleIcon } from '@heroicons/vue/24/outline'

interface Props {
  modelValue: string
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue'])

const localValue = ref(props.modelValue)
const isFocused = ref(false)

// Update local value when prop changes, but only if not focused
watch(() => props.modelValue, (newValue) => {
  if (!isFocused.value) {
    localValue.value = newValue
  }
})

const handleChange = (e: Event) => {
  const newValue = (e.target as HTMLTextAreaElement).value
  localValue.value = newValue
  emit('update:modelValue', newValue)
}
</script>

<template>
  <div class="space-y-3">
    <textarea
      :value="localValue"
      @input="handleChange"
      @focus="isFocused = true"
      @blur="isFocused = false"
      placeholder="Enter a custom prompt for transcript optimization..."
      rows="4"
      class="input focus:ring-2 focus:ring-primary/50 dark:focus:ring-dark-primary/50 w-full resize-none"
    />
    <div class="text-muted dark:text-dark-muted flex items-start text-sm">
      <InformationCircleIcon class="flex-shrink-0 w-5 h-5 mr-2" />
      <p>
        This prompt will be used to generate optimized transcriptions.
        Example: "Remove verbal fillers, fix grammar, and format as clear paragraphs."
        <br />
          You can use variables like <code>&#123;&#123;transcript&#125;&#125;</code> in your prompt, which will be replaced with the actual transcript.
      </p>
    </div>
  </div>
</template>

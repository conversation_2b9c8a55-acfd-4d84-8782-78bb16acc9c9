<script setup lang="ts">
interface Props {
  sampleRate: number
  deviceId: string | null
  availableDevices: Array<{ id: string; name: string }>
  onSampleRateChange: (rate: number) => void
  onDeviceChange: (deviceId: string | null) => void
}

const props = defineProps<Props>()

const sampleRates = [8000, 16000, 22050, 44100, 48000]

const handleDeviceChange = (e: Event) => {
  const value = (e.target as HTMLInputElement).value
  props.onDeviceChange(value === '' ? null : value)
}

const handleSampleRateChange = (e: Event) => {
  props.onSampleRateChange(Number((e.target as HTMLSelectElement).value))
}
</script>

<template>
  <div class="space-y-4">
    <div>
      <label class="text-text dark:text-dark-text block mb-2 text-sm font-medium">Audio Device</label>
      <input
        type="text"
        :value="deviceId || ''"
        @input="handleDeviceChange"
        placeholder="System Default (e.g. hw:1,0 for Linux)"
        class="w-full px-4 py-2.5 bg-gray-100 dark:bg-dark-surface hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary/50 dark:focus:ring-dark-primary/50"
      />
    </div>

    <div>
      <label class="text-text dark:text-dark-text block mb-2 text-sm font-medium">Sample Rate</label>
      <div class="relative">
        <select
          :value="sampleRate"
          @change="handleSampleRateChange"
          class="w-full px-4 py-2.5 bg-gray-100 dark:bg-dark-surface hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary/50 dark:focus:ring-dark-primary/50 appearance-none"
        >
          <option v-for="rate in sampleRates" :key="rate" :value="rate">
            {{ rate.toLocaleString() }} Hz
          </option>
        </select>
        <div class="dark:text-gray-200 absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 pointer-events-none">
          <svg class="w-4 h-4 fill-current" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
            <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
          </svg>
        </div>
      </div>
    </div>
  </div>
</template>
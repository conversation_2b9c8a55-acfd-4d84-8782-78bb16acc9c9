<script setup lang="ts">
interface Props {
  service: string
  onChange: (service: string) => void
}

const props = defineProps<Props>()

const services = [
  { id: 'assemblyai', name: 'AssemblyAI' },
  { id: 'lemonfox', name: 'Whisper' }
]

const handleServiceClick = (serviceId: string) => {
  if (props.service !== serviceId) {
    props.onChange(serviceId)
  }
}
</script>

<template>
  <div class="flex gap-2" role="radiogroup" aria-label="Select transcription service">
    <button
      v-for="s in services"
      :key="s.id"
      class="flex-1 py-2.5 px-4 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-primary/50 dark:focus:ring-dark-primary/50"
      :class="{
        'bg-primary dark:bg-dark-primary text-black shadow-sm font-bold': service === s.id,
        'bg-gray-100 dark:bg-dark-surface hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 font-medium': service !== s.id
      }"
      @click="handleServiceClick(s.id)"
      role="radio"
      :aria-checked="service === s.id"
    >
      {{ s.name }}
    </button>
  </div>
</template>
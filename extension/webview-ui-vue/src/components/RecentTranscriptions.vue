<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  ClipboardDocumentIcon, 
  SparklesIcon, 
  ClockIcon, 
  MagnifyingGlassIcon, 
  XMarkIcon, 
  TrashIcon, 
  ArrowDownIcon 
} from '@heroicons/vue/24/outline'
import { vscode } from '../utilities/vscode'

interface Props {
  transcriptions: Transcription[]
  onCopyOriginal: (id: string) => void
  onCopyOptimized: (id: string) => void
  onOptimize: (id: string) => void
}

interface Transcription {
  id: string
  timestamp: string
  originalText: string
  optimizedText?: string
  service: string
  model: string
  language: string
  duration?: number
}

const ITEMS_PER_PAGE = 5

const props = defineProps<Props>()
const searchQuery = ref('')
const sortOption = ref('newest')
const displayCount = ref(ITEMS_PER_PAGE)

const formatTimestamp = (timestamp: string): string => {
  try {
    const date = new Date(timestamp)
    const now = new Date()
    const isToday = date.getDate() === now.getDate() &&
                  date.getMonth() === now.getMonth() &&
                  date.getFullYear() === now.getFullYear()

    if (isToday) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    } else {
      return date.toLocaleDateString([], {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  } catch (error) {
    console.error("Error formatting timestamp:", error)
    return "Invalid date"
  }
}

const formatServiceModel = (service: string, model: string): string => {
  const displayService = service || "unknown"
  const displayModel = model || "unknown"
  return `${displayService} - ${displayModel}`
}

const truncateText = (text: string, maxLength: number = 100): string => {
  if (!text) return ''
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

const handleClearAllHistory = () => {
  console.log('RecentTranscriptions: Clear all history button clicked. Current transcriptions count:', props.transcriptions.length)
  vscode.postMessage({
    command: 'showConfirmation',
    message: 'Are you sure you want to delete all transcription history? This cannot be undone.',
    onConfirm: 'clearTranscriptionHistory'
  })
}

const handleDeleteTranscription = (id: string) => {
  vscode.postMessage({
    command: 'deleteTranscription',
    id: id
  })
}

const handleLoadMore = () => {
  displayCount.value += ITEMS_PER_PAGE
}

const filteredTranscriptions = computed(() => {
  let filtered = [...props.transcriptions]

  if (searchQuery.value) {
    filtered = filtered.filter(item =>
      item.originalText.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      (item.optimizedText && item.optimizedText.toLowerCase().includes(searchQuery.value.toLowerCase()))
    )
  }

  switch (sortOption.value) {
    case "newest":
      filtered.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      break
    case "oldest":
      filtered.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())
      break
    default:
      filtered.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
  }

  return filtered
})

const displayedTranscriptions = computed(() => {
  return filteredTranscriptions.value.slice(0, displayCount.value)
})

const hasMoreItems = computed(() => {
  return displayedTranscriptions.value.length < filteredTranscriptions.value.length
})

const formatDuration = (seconds?: number): string => {
  if (!seconds) return ''
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}
</script>

<template>
  <div class="flex flex-col h-full">
    <!-- Search and Filter Controls -->
    <div class="border-border dark:border-dark-border p-2 mb-2 border-b">
      <div class="flex items-center justify-between mb-2">
        <div class="relative flex-grow">
          <input
            type="text"
            placeholder="Search transcriptions..."
            v-model="searchQuery"
            class="w-full px-3 py-1.5 pl-8 text-sm bg-gray-50 dark:bg-dark-surface border border-border dark:border-dark-border rounded-md"
          />
          <MagnifyingGlassIcon class="absolute left-2.5 top-1/2 transform -translate-y-1/2 w-3.5 h-3.5 text-muted dark:text-dark-muted" />
          <button
            v-if="searchQuery"
            @click="searchQuery = ''"
            class="absolute right-2.5 top-1/2 transform -translate-y-1/2"
          >
            <XMarkIcon class="w-3.5 h-3.5 text-muted dark:text-dark-muted hover:text-primary dark:hover:text-dark-primary" />
          </button>
        </div>
        <button
          v-if="transcriptions.length > 0"
          @click="handleClearAllHistory"
          class="ml-2 p-1.5 text-red-600 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-md"
          title="Clear all transcription history"
        >
          <TrashIcon class="w-4 h-4" />
        </button>
      </div>

      <div class="flex items-center justify-between">
        <div class="flex items-center justify-center space-x-2 bg-gray-100 dark:bg-gray-800 rounded-full p-1 max-w-[200px]">
          <button
            :class="`flex-1 py-1 px-3 text-xs font-medium rounded-full transition-colors ${
              sortOption === 'newest'
                ? 'bg-white dark:bg-gray-700 shadow-sm text-primary dark:text-dark-primary'
                : 'text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
            }`"
            @click="sortOption = 'newest'"
          >
            Newest
          </button>
          <button
            :class="`flex-1 py-1 px-3 text-xs font-medium rounded-full transition-colors ${
              sortOption === 'oldest'
                ? 'bg-white dark:bg-gray-700 shadow-sm text-primary dark:text-dark-primary'
                : 'text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
            }`"
            @click="sortOption = 'oldest'"
          >
            Oldest
          </button>
        </div>
        <span v-if="transcriptions.length > 0" class="text-muted dark:text-dark-muted text-xs">
          {{ transcriptions.length }}/50 messages
        </span>
      </div>
    </div>

    <!-- Transcription List -->
    <div class="flex-grow px-2 pb-2 space-y-3 overflow-y-auto">
      <div v-if="filteredTranscriptions.length === 0" class="text-muted dark:text-dark-muted border-border dark:border-dark-border flex flex-col items-center justify-center p-4 text-sm text-center border border-dashed rounded-md">
        <ClockIcon class="w-8 h-8 mb-2 opacity-50" />
        {{ searchQuery ? 'No matching transcriptions found' : 'No transcription history yet' }}
      </div>

      <template v-else>
        <div v-for="transcription in displayedTranscriptions" :key="transcription.id" class="dark:bg-dark-surface/50 border-border dark:border-dark-border group overflow-hidden bg-white border rounded-md shadow-sm">
          <div class="flex items-center justify-between px-3 py-1.5 bg-gray-50 dark:bg-dark-surface border-b border-border dark:border-dark-border">
            <div class="text-muted dark:text-dark-muted flex items-center text-xs">
              <ClockIcon class="w-3 h-3 mr-1" />
              <span>{{ formatTimestamp(transcription.timestamp) }}</span>
              <span v-if="transcription.duration" class="ml-2">({{ formatDuration(transcription.duration) }})</span>
            </div>
            <div class="flex items-center">
              <div class="text-muted dark:text-dark-muted mr-2 text-xs">
                {{ formatServiceModel(transcription.service, transcription.model) }}
              </div>
              <button
                @click="handleDeleteTranscription(transcription.id)"
                class="group-hover:opacity-100 text-muted hover:text-red-500 dark:text-dark-muted dark:hover:text-red-400 transition-opacity opacity-0"
                title="Delete transcription"
              >
                <TrashIcon class="w-3.5 h-3.5" />
              </button>
            </div>
          </div>

          <div class="p-2 space-y-2">
            <!-- Original Transcript -->
            <div class="space-y-1">
              <div class="text-muted dark:text-dark-muted text-xs font-semibold uppercase">Original</div>
              <div class="flex">
                <div class="flex-grow p-1.5 bg-gray-50 dark:bg-dark-surface/50 rounded text-xs">
                  {{ truncateText(transcription.originalText) }}
                </div>
                <button
                  class="text-muted hover:text-primary dark:text-dark-muted dark:hover:text-dark-primary p-1 ml-1"
                  @click="onCopyOriginal(transcription.id)"
                  title="Copy original transcript"
                >
                  <ClipboardDocumentIcon class="w-4 h-4" />
                </button>
              </div>
            </div>

            <!-- Optimized Transcript or Optimize Button -->
            <div v-if="transcription.optimizedText" class="space-y-1">
              <div class="text-accent dark:text-dark-accent text-xs font-semibold uppercase">Optimized</div>
              <div class="flex">
                <div class="flex-grow p-1.5 bg-gray-50 dark:bg-dark-surface/50 rounded text-xs">
                  {{ truncateText(transcription.optimizedText) }}
                </div>
                <button
                  class="text-muted hover:text-primary dark:text-dark-muted dark:hover:text-dark-primary p-1 ml-1"
                  @click="onCopyOptimized(transcription.id)"
                  title="Copy optimized transcript"
                >
                  <ClipboardDocumentIcon class="w-4 h-4" />
                </button>
              </div>
            </div>
            <div v-else class="flex justify-end">
              <button
                class="bg-primary/10 hover:bg-primary/20 dark:bg-dark-primary/20 dark:hover:bg-dark-primary/30 text-primary dark:text-dark-primary flex items-center px-2 py-1 text-xs font-medium rounded"
                @click="onOptimize(transcription.id)"
                title="Optimize this transcript"
                >
                <SparklesIcon class="w-3 h-3 mr-1" />
                Optimize
              </button>
            </div>
          </div>
        </div>

        <!-- Show More Button -->
        <div v-if="hasMoreItems" class="flex justify-center pt-2">
          <button
            @click="handleLoadMore"
            class="flex items-center px-4 py-1.5 text-xs font-medium text-primary dark:text-dark-primary border border-primary/30 dark:border-dark-primary/30 hover:bg-primary/5 dark:hover:bg-dark-primary/10 rounded-md"
          >
            <ArrowDownIcon class="w-3.5 h-3.5 mr-1.5" />
            Show More ({{ filteredTranscriptions.length - displayedTranscriptions.length }} remaining)
          </button>
        </div>
      </template>
    </div>
  </div>
</template>
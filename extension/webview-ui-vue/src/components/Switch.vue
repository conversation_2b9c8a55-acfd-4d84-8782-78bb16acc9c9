<script setup lang="ts">
interface Props {
  label: string
  checked: boolean
  onChange: (checked: boolean) => void
  className?: string
}

const props = defineProps<Props>()

const handleChange = (e: Event) => {
  props.onChange((e.target as HTMLInputElement).checked)
}
</script>

<template>
  <div :class="`flex items-center justify-between p-3 mb-3 bg-white dark:bg-dark-surface border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm hover:border-primary/70 dark:hover:border-primary/70 transition-colors duration-200 ${className}`">
    <label class="flex items-center justify-between w-full cursor-pointer">
      <span class="dark:text-white text-sm font-medium text-gray-900">{{ label }}</span>
      <div class="relative ml-auto">
        <input
          type="checkbox"
          class="sr-only"
          :checked="checked"
          @change="handleChange"
        />
        <div :class="`block w-10 h-6 rounded-full transition-colors duration-200 ease-in-out ${
          checked ? 'bg-green-600 dark:bg-green-500' : 'bg-gray-300 dark:bg-gray-700'
        }`" />
        <div :class="`absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition-transform duration-200 ease-in-out ${
          checked ? 'transform translate-x-4' : 'transform translate-x-0'
        }`" />
      </div>
    </label>
  </div>
</template>

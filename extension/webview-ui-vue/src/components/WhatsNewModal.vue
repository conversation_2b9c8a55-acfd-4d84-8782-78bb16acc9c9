<script setup lang="ts">
import { computed } from 'vue'
import type { WhatsNewInfo } from '../types/configuration'
import Button from './Button.vue'

interface Props {
  isOpen: boolean
  whatsNewInfo: WhatsNewInfo
}

interface Emits {
  (e: 'close'): void
  (e: 'markAsSeen'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const handleClose = () => {
  emit('close')
}

const handleMarkAsSeen = () => {
  emit('markAsSeen')
  emit('close')
}

const handleBackdropClick = (e: Event) => {
  if (e.target === e.currentTarget) {
    handleClose()
  }
}
</script>

<template>
  <!-- Modal Overlay -->
  <Transition
    enter-active-class="transition-opacity duration-300"
    enter-from-class="opacity-0"
    enter-to-class="opacity-100"
    leave-active-class="transition-opacity duration-200"
    leave-from-class="opacity-100"
    leave-to-class="opacity-0"
  >
    <div
      v-if="isOpen"
      class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
      @click="handleBackdropClick"
    >
      <!-- Modal Content -->
      <Transition
        enter-active-class="transition-all duration-300 ease-out"
        enter-from-class="transform scale-95 opacity-0"
        enter-to-class="transform scale-100 opacity-100"
        leave-active-class="transition-all duration-200 ease-in"
        leave-from-class="transform scale-100 opacity-100"
        leave-to-class="transform scale-95 opacity-0"
      >
        <div
          v-if="isOpen"
          class="bg-white dark:bg-dark-surface rounded-lg shadow-xl max-w-md w-full overflow-hidden"
          @click.stop
        >
          <!-- Header with Party Background -->
          <div class="relative bg-gradient-to-r from-purple-500 to-pink-500 p-6 text-white">
            <!-- Close Button -->
            <button
              @click="handleClose"
              class="absolute top-4 right-4 text-white/80 hover:text-white transition-colors"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
            
            <!-- Party Emoji and Title -->
            <div class="text-center">
              <div class="text-4xl mb-2">🎉</div>
              <h2 class="text-2xl font-bold mb-1">
                {{ whatsNewInfo.title }}
              </h2>
              <p class="text-white/90 text-sm">
                VoiceHype {{ whatsNewInfo.version }}
              </p>
            </div>
          </div>
          
          <!-- Content -->
          <div class="p-6">
            <div class="space-y-4">
              <p class="text-gray-600 dark:text-gray-400 text-sm">
                We're excited to introduce some amazing new features:
              </p>
              
              <!-- Features List -->
              <ul class="space-y-3">
                <li
                  v-for="(feature, index) in whatsNewInfo.features"
                  :key="index"
                  class="flex items-start gap-3"
                >
                  <div class="w-6 h-6 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <svg class="w-3 h-3 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <span class="text-gray-700 dark:text-gray-300 text-sm">
                    {{ feature }}
                  </span>
                </li>
              </ul>
              
              <!-- Highlight Box -->
              <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mt-6">
                <div class="flex items-center gap-2 mb-2">
                  <div class="text-blue-600 dark:text-blue-400">⌨️</div>
                  <h4 class="font-medium text-blue-900 dark:text-blue-100 text-sm">
                    Quick Tip
                  </h4>
                </div>
                <p class="text-blue-800 dark:text-blue-200 text-sm">
                  Use <kbd class="px-1.5 py-0.5 bg-blue-100 dark:bg-blue-800 rounded text-xs">Ctrl+Shift+PageUp/PageDown</kbd> 
                  to quickly switch between your configurations!
                </p>
              </div>
            </div>
          </div>
          
          <!-- Footer -->
          <div class="flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
            <button
              @click="handleClose"
              class="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
            >
              Remind me later
            </button>
            
            <Button
              variant="primary"
              @click="handleMarkAsSeen"
              class="flex items-center gap-2"
            >
              <span>Got it!</span>
              <div class="text-lg">👍</div>
            </Button>
          </div>
        </div>
      </Transition>
    </div>
  </Transition>
</template>

<style scoped>
kbd {
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: 0.75rem;
  font-weight: 600;
}
</style>

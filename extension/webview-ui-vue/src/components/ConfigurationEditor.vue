<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import type { Configuration, ConfigurationSettings } from '../types/configuration'
import ServiceSelector from './ServiceSelector.vue'
import ModelSelector from './ModelSelector.vue'
import LanguageSelector from './LanguageSelector.vue'
import OptimizationModelSelector from './OptimizationModelSelector.vue'
import AudioSettings from './AudioSettings.vue'
import Switch from './Switch.vue'
import Button from './Button.vue'

interface Props {
  isOpen: boolean
  configuration?: Configuration | null
  currentSettings?: ConfigurationSettings
}

interface Emits {
  (e: 'close'): void
  (e: 'save', config: Partial<Configuration>): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const isEditing = computed(() => !!props.configuration)

// Form data
const formData = reactive({
  title: '',
  description: '',
  settings: {
    service: 'assemblyai',
    model: 'whisper-1',
    language: 'en',
    translate: false,
    realtime: false,
    optimizeEnabled: true,
    optimizationModel: 'gpt-4o',
    customPrompt: 'Correct any grammar issues and improve clarity. Keep the meaning intact.',
    sampleRate: 22050,
    deviceId: null as string | null
  } as ConfigurationSettings
})

const errors = ref<Record<string, string>>({})

// Watch for configuration changes to populate form
watch(() => props.configuration, (config) => {
  if (config) {
    formData.title = config.title
    formData.description = config.description
    Object.assign(formData.settings, config.settings)
  } else {
    // Reset form for new configuration
    formData.title = ''
    formData.description = ''
    if (props.currentSettings) {
      Object.assign(formData.settings, props.currentSettings)
    }
  }
  errors.value = {}
}, { immediate: true })

// Watch for current settings to populate new configuration
watch(() => props.currentSettings, (settings) => {
  if (settings && !props.configuration) {
    Object.assign(formData.settings, settings)
  }
}, { immediate: true })

const validateForm = () => {
  errors.value = {}
  
  if (!formData.title.trim()) {
    errors.value.title = 'Title is required'
  }
  
  if (!formData.description.trim()) {
    errors.value.description = 'Description is required'
  }
  
  return Object.keys(errors.value).length === 0
}

const handleSave = () => {
  if (!validateForm()) return
  
  const configData: Partial<Configuration> = {
    title: formData.title.trim(),
    description: formData.description.trim(),
    settings: { ...formData.settings }
  }
  
  if (isEditing.value && props.configuration) {
    configData.id = props.configuration.id
  }
  
  emit('save', configData)
}

const handleClose = () => {
  emit('close')
}

const handleServiceChange = (service: string) => {
  formData.settings.service = service
}

const handleModelChange = (model: string) => {
  formData.settings.model = model
}

const handleLanguageChange = (language: string) => {
  formData.settings.language = language
}

const handleOptimizationModelChange = (model: string) => {
  formData.settings.optimizationModel = model
}

const handleSampleRateChange = (rate: number) => {
  formData.settings.sampleRate = rate
}

const handleDeviceChange = (deviceId: string | null) => {
  formData.settings.deviceId = deviceId
}

const handleOptimizeToggle = (enabled: boolean) => {
  formData.settings.optimizeEnabled = enabled
}

const handleRealtimeToggle = (enabled: boolean) => {
  formData.settings.realtime = enabled
}

const handleTranslateToggle = (enabled: boolean) => {
  formData.settings.translate = enabled
}
</script>

<template>
  <!-- Modal Overlay -->
  <Transition
    enter-active-class="transition-opacity duration-300"
    enter-from-class="opacity-0"
    enter-to-class="opacity-100"
    leave-active-class="transition-opacity duration-200"
    leave-from-class="opacity-100"
    leave-to-class="opacity-0"
  >
    <div
      v-if="isOpen"
      class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
      @click="handleClose"
    >
      <!-- Modal Content -->
      <Transition
        enter-active-class="transition-all duration-300 ease-out"
        enter-from-class="transform scale-95 opacity-0"
        enter-to-class="transform scale-100 opacity-100"
        leave-active-class="transition-all duration-200 ease-in"
        leave-from-class="transform scale-100 opacity-100"
        leave-to-class="transform scale-95 opacity-0"
      >
        <div
          v-if="isOpen"
          class="bg-white dark:bg-dark-surface rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
          @click.stop
        >
          <!-- Header -->
          <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100">
              {{ isEditing ? 'Edit Configuration' : 'Create New Configuration' }}
            </h2>
            <button
              @click="handleClose"
              class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <!-- Form Content -->
          <div class="p-6 space-y-6">
            <!-- Basic Info -->
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Configuration Title *
                </label>
                <input
                  v-model="formData.title"
                  type="text"
                  placeholder="e.g., English Development, Urdu WhatsApp"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary dark:focus:ring-dark-primary focus:border-transparent"
                  :class="{ 'border-red-500': errors.title }"
                />
                <p v-if="errors.title" class="mt-1 text-sm text-red-600 dark:text-red-400">
                  {{ errors.title }}
                </p>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Description *
                </label>
                <textarea
                  v-model="formData.description"
                  rows="2"
                  placeholder="Describe when to use this configuration..."
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary dark:focus:ring-dark-primary focus:border-transparent resize-none"
                  :class="{ 'border-red-500': errors.description }"
                />
                <p v-if="errors.description" class="mt-1 text-sm text-red-600 dark:text-red-400">
                  {{ errors.description }}
                </p>
              </div>
            </div>
            
            <!-- Settings -->
            <div class="space-y-6">
              <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2">
                Configuration Settings
              </h3>
              
              <!-- Service and Model -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <ServiceSelector
                  :service="formData.settings.service"
                  @change="handleServiceChange"
                />
                
                <ModelSelector
                  :service="formData.settings.service"
                  :model="formData.settings.model"
                  @change="handleModelChange"
                />
              </div>
              
              <!-- Language -->
              <LanguageSelector
                :service="formData.settings.service"
                :model="formData.settings.model"
                :language="formData.settings.language"
                @change="handleLanguageChange"
              />
              
              <!-- Switches -->
              <div class="space-y-3">
                <Switch
                  :checked="formData.settings.optimizeEnabled"
                  @change="handleOptimizeToggle"
                  label="Enable optimization"
                />
                
                <Switch
                  v-if="formData.settings.service === 'assemblyai' && formData.settings.model === 'best'"
                  :checked="formData.settings.realtime"
                  @change="handleRealtimeToggle"
                  label="Use real-time transcription"
                />
                
                <Switch
                  :checked="formData.settings.translate"
                  @change="handleTranslateToggle"
                  label="Enable translation"
                />
              </div>
              
              <!-- Optimization Model -->
              <div v-if="formData.settings.optimizeEnabled">
                <OptimizationModelSelector
                  :model="formData.settings.optimizationModel"
                  @change="handleOptimizationModelChange"
                />
              </div>
              
              <!-- Custom Prompt -->
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Custom Prompt
                </label>
                <textarea
                  v-model="formData.settings.customPrompt"
                  rows="3"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary dark:focus:ring-dark-primary focus:border-transparent resize-none"
                />
              </div>
              
              <!-- Audio Settings -->
              <AudioSettings
                :sampleRate="formData.settings.sampleRate"
                :deviceId="formData.settings.deviceId"
                :availableDevices="[]"
                @sampleRateChange="handleSampleRateChange"
                @deviceChange="handleDeviceChange"
              />
            </div>
          </div>
          
          <!-- Footer -->
          <div class="flex items-center justify-end gap-3 p-6 border-t border-gray-200 dark:border-gray-700">
            <Button
              variant="secondary"
              @click="handleClose"
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              @click="handleSave"
            >
              {{ isEditing ? 'Update Configuration' : 'Create Configuration' }}
            </Button>
          </div>
        </div>
      </Transition>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { vscode } from '../utilities/vscode'

const isDark = ref(false)

// Function to update document class
const updateDarkMode = (dark: boolean) => {
  if (dark) {
    document.documentElement.classList.add('dark')
  } else {
    document.documentElement.classList.remove('dark')
  }
}

// Initialize theme from VSCode state
onMounted(() => {
  const state = vscode.getState()
  if (state?.theme) {
    isDark.value = state.theme === 'dark'
  } else {
    // Default to system preference
    isDark.value = window.matchMedia('(prefers-color-scheme: dark)').matches
    vscode.setState({ theme: isDark.value ? 'dark' : 'light' })
  }
  updateDarkMode(isDark.value)
})

// Watch for changes to isDark
watch(isDark, (newValue) => {
  updateDarkMode(newValue)
})

const toggleDarkMode = () => {
  isDark.value = !isDark.value
  const newTheme = isDark.value ? 'dark' : 'light'
  vscode.postMessage({ command: 'updateTheme', theme: newTheme })
  vscode.setState({ theme: newTheme })
}
</script>

<template>
  <button
    @click="toggleDarkMode"
    class="hover:bg-gray-100 dark:hover:bg-gray-700 p-2 transition-colors rounded-full"
    aria-label="Toggle dark mode"
  >
    <svg
      v-if="isDark"
      xmlns="http://www.w3.org/2000/svg"
      class="w-5 h-5 text-yellow-400"
      viewBox="0 0 20 20"
      fill="currentColor"
    >
      <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
    </svg>
    <svg
      v-else
      xmlns="http://www.w3.org/2000/svg"
      class="dark:text-gray-300 w-5 h-5 text-gray-700"
      viewBox="0 0 20 20"
      fill="currentColor"
    >
      <path
        fill-rule="evenodd"
        d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z"
        clip-rule="evenodd"
      />
    </svg>
  </button>
</template>

<script setup lang="ts">
interface Props {
  onClick?: () => void
  variant?: 'primary' | 'secondary'
  size?: 'sm' | 'md' | 'lg'
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'primary',
  size: 'md',
  disabled: false
})

const baseStyles = 'rounded font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-1'
const sizeStyles = {
  sm: 'px-2 py-1 text-sm',
  md: 'px-3 py-1.5 text-base', 
  lg: 'px-4 py-2 text-lg'
}
const variantStyles = {
  primary: 'bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 focus:ring-blue-500',
  secondary: 'bg-gray-200 text-gray-800 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 dark:border dark:border-gray-600 focus:ring-gray-500'
}
</script>

<template>
  <button
    :disabled="props.disabled"
    :class="[
      baseStyles,
      sizeStyles[props.size],
      variantStyles[props.variant],
      props.disabled ? 'opacity-50 cursor-not-allowed' : ''
    ]"
    @click="props.onClick"
  >
    <slot />
  </button>
</template>

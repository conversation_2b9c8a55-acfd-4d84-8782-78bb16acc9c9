<script setup lang="ts">
import { ref } from 'vue'
import { vscode } from '../utilities/vscode'

interface Props {
  initialApiKey?: string
}

const props = defineProps<Props>()

const apiKey = ref(props.initialApiKey || '')
const showApiKey = ref(false)
const isSaving = ref(false)

const handleSave = async () => {
  isSaving.value = true
  vscode.postMessage({
    command: 'updateOptions',
    options: { apiKey: apiKey.value }
  })
  setTimeout(() => isSaving.value = false, 1000)
}
</script>

<template>
  <div class="bg-opacity-10 p-4 mb-4 bg-white rounded-lg">
    <div class="flex flex-col space-y-2">
      <label class="text-sm font-medium">
        API Key
      </label>
      <div class="sm:flex-row flex flex-col w-full gap-2">
        <div class="flex-1 min-w-0">
          <input
            :type="showApiKey ? 'text' : 'password'"
            v-model="apiKey"
            placeholder="Enter your VoiceHype API key"
            class="bg-opacity-10 focus:border-blue-500 focus:outline-none w-full px-3 py-2 bg-white border border-gray-600 rounded"
          />
        </div>
        <div class="flex gap-2">
          <button
            @click="showApiKey = !showApiKey"
            class="bg-opacity-10 hover:bg-opacity-20 whitespace-nowrap px-3 py-2 bg-white border border-gray-600 rounded"
          >
            {{ showApiKey ? "Hide" : "Show" }}
          </button>
          <button
            @click="handleSave"
            :disabled="isSaving"
            class="hover:bg-blue-700 disabled:opacity-50 whitespace-nowrap dark:text-white px-4 py-2 text-black bg-blue-600 rounded"
          >
            {{ isSaving ? "Saving..." : "Save" }}
          </button>
        </div>
      </div>
      <p class="text-xs text-gray-500">
        Your API key is stored securely. Get one at voicehype.ai
      </p>
    </div>
  </div>
</template>

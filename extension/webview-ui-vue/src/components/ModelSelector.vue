<script setup lang="ts">
import { InformationCircleIcon } from '@heroicons/vue/24/outline'
import { computed } from 'vue'

interface Props {
  service: string
  model: string
  onChange: (model: string) => void
}

interface ModelOption {
  id: string
  name: string
  description: string
}

const props = defineProps<Props>()

const modelOptions = computed(() => {
  if (props.service === 'assemblyai') {
    return [
      { id: 'best', name: 'best', description: 'High accuracy, optimized for various accents and domains' },
      { id: 'nano', name: 'nano', description: 'Faster transcription with small quality tradeoff' }
    ]
  } else if (props.service === 'lemonfox') {
    return [
      { id: 'whisper-1', name: 'whisper-1', description: 'High-quality speech recognition model' }
    ]
  }
  return []
})

const selectedModel = computed(() => 
  modelOptions.value.find(o => o.id === props.model)
)

const handleModelChange = (e: Event) => {
  const newModel = (e.target as HTMLSelectElement).value
  if (newModel !== props.model) {
    props.onChange(newModel)
  }
}

// For Whisper service, auto-select whisper-1 model if not already selected
if (props.service === 'lemonfox' && props.model !== 'whisper-1') {
  props.onChange('whisper-1')
}
</script>

<template>
  <div v-if="service === 'assemblyai'" class="space-y-3">
    <div class="relative">
      <select
        class="w-full px-3 py-2 appearance-none rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500/50 dark:focus:ring-blue-500/50 cursor-pointer"
        :value="model"
        @change="handleModelChange"
        aria-label="Select model"
      >
        <option v-for="option in modelOptions" :key="option.id" :value="option.id">
          {{ option.name }}
        </option>
      </select>
      <div class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
        <svg class="h-4 w-4 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 20 20" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7l3-3 3 3m0 6l-3 3-3-3" />
        </svg>
      </div>
    </div>

    <div v-if="selectedModel?.description" class="flex items-start text-sm text-gray-600 dark:text-gray-300 bg-gray-50 dark:bg-gray-800/40 p-3 rounded-md">
      <InformationCircleIcon class="w-5 h-5 mr-2 flex-shrink-0 text-blue-500 dark:text-blue-400" />
      <p>{{ selectedModel.description }}</p>
    </div>
  </div>
  <div v-else class="space-y-3"></div>
</template>

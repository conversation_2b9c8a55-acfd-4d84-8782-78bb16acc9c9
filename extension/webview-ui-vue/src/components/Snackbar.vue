<script setup lang="ts">
import { computed, onMounted, onUnmounted } from 'vue'
import type { SnackbarMessage } from '../types/configuration'

interface Props {
  message: SnackbarMessage | null
}

interface Emits {
  (e: 'close', id: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const isVisible = computed(() => props.message?.visible ?? false)

const getIconClass = (type: string) => {
  switch (type) {
    case 'success':
      return 'text-green-500'
    case 'error':
      return 'text-red-500'
    case 'warning':
      return 'text-yellow-500'
    case 'info':
    default:
      return 'text-blue-500'
  }
}

const getIcon = (type: string) => {
  switch (type) {
    case 'success':
      return '✓'
    case 'error':
      return '✗'
    case 'warning':
      return '⚠'
    case 'info':
    default:
      return 'ℹ'
  }
}

const getBgClass = (type: string) => {
  switch (type) {
    case 'success':
      return 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
    case 'error':
      return 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
    case 'warning':
      return 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800'
    case 'info':
    default:
      return 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800'
  }
}

const handleClose = () => {
  if (props.message) {
    emit('close', props.message.id)
  }
}

// Auto-close timer
let autoCloseTimer: NodeJS.Timeout | null = null

onMounted(() => {
  if (props.message && props.message.duration > 0) {
    autoCloseTimer = setTimeout(() => {
      handleClose()
    }, props.message.duration)
  }
})

onUnmounted(() => {
  if (autoCloseTimer) {
    clearTimeout(autoCloseTimer)
  }
})
</script>

<template>
  <Transition
    enter-active-class="transition-all duration-300 ease-out"
    enter-from-class="transform translate-y-full opacity-0"
    enter-to-class="transform translate-y-0 opacity-100"
    leave-active-class="transition-all duration-200 ease-in"
    leave-from-class="transform translate-y-0 opacity-100"
    leave-to-class="transform translate-y-full opacity-0"
  >
    <div
      v-if="isVisible && message"
      :class="[
        'fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50',
        'min-w-80 max-w-md mx-auto',
        'border rounded-lg shadow-lg',
        'flex items-center gap-3 p-4',
        getBgClass(message.type)
      ]"
    >
      <!-- Icon -->
      <div :class="['text-lg font-bold', getIconClass(message.type)]">
        {{ getIcon(message.type) }}
      </div>
      
      <!-- Message -->
      <div class="flex-1 text-sm font-medium text-gray-800 dark:text-gray-200">
        {{ message.message }}
      </div>
      
      <!-- Close button -->
      <button
        @click="handleClose"
        class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>
  </Transition>
</template>

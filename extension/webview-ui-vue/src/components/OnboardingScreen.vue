<script setup lang="ts">
import { vscode } from '../utilities/vscode'

const handleSignIn = () => {
  vscode.postMessage({
    command: 'authenticate'
  })
}

const handleManualApiKey = () => {
  vscode.postMessage({
    command: 'showApiKeyInput'
  })
}

const openExternalLink = (url: string) => {
  vscode.postMessage({
    command: 'openExternalLink',
    url
  })
}
</script>

<template>
  <div class="flex flex-col items-center justify-center h-full p-6 text-center">
    <div class="mb-8">
      <img src="voicehype-logo.png" alt="Voice Hype Logo" class="w-32 h-32" />
      <h1 class="text-2xl font-bold mt-4">Welcome to Voice Hype</h1>
      <p class="mt-2 text-gray-400">
        Voice-to-prompt productivity tool for developers
      </p>
    </div>
    
    <div class="w-full max-w-md space-y-4">
      <button 
        @click="handleSignIn"
        class="w-full py-3 px-4 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium flex items-center justify-center"
      >
        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2V5zm11 1H6v8l4-2 4 2V6z" clip-rule="evenodd" />
        </svg>
        Sign in with Browser
      </button>
      
      <div class="relative my-4">
        <div class="absolute inset-0 flex items-center">
          <div class="w-full border-t border-gray-600"></div>
        </div>
        <div class="relative flex justify-center text-sm">
          <span class="px-2 bg-[#1e1e1e] text-gray-400">or</span>
        </div>
      </div>
      
      <button 
        @click="handleManualApiKey"
        class="w-full py-3 px-4 bg-transparent border border-gray-600 hover:bg-gray-800 text-white rounded-lg font-medium"
      >
        Enter API Key Manually
      </button>
    </div>
    
    <p class="mt-8 text-sm text-gray-400">
      Don't have an account? 
      <a href="#" class="text-blue-400 hover:underline" @click="openExternalLink('https://voicehype.ai/signup')">
        Sign up at voicehype.ai
      </a>
    </p>
  </div>
</template>

<script setup lang="ts">
import { MicrophoneIcon, StopIcon, PauseIcon, PlayIcon, XMarkIcon } from '@heroicons/vue/24/solid'

interface Props {
  isRecording: boolean
  isPaused: boolean
  elapsedTime: number
  onStart: () => void
  onStop: () => void
  onPause: () => void
  onResume: () => void
  onCancel: () => void
}

const props = defineProps<Props>()

const formatDuration = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

const handleStart = () => {
  console.log('[RecordingControls] [DEBUG] Start button clicked, current state:', { 
    isRecording: props.isRecording, 
    isPaused: props.isPaused 
  })
  props.onStart()
}

const handleStop = () => {
  console.log('[RecordingControls] [DEBUG] Stop button clicked, current state:', { 
    isRecording: props.isRecording, 
    isPaused: props.isPaused 
  })
  props.onStop()
}

const handlePauseResume = () => {
  console.log('[RecordingControls] [DEBUG] Pause/Resume button clicked, current state:', { 
    isRecording: props.isRecording, 
    isPaused: props.isPaused 
  })
  if (props.isPaused) {
    console.log('[RecordingControls] [DEBUG] Resuming recording')
    props.onResume()
  } else {
    console.log('[RecordingControls] [DEBUG] Pausing recording')
    props.onPause()
  }
}
</script>

<template>
  <div class="flex flex-col space-y-4">
    <!-- Timer Display -->
    <div class="flex items-center justify-center">
      <div class="bg-surface dark:bg-dark-surface/80 border-primary/30 dark:border-primary/30 p-3 font-mono text-3xl border rounded-lg shadow-inner">
        {{ formatDuration(elapsedTime) }}
      </div>
    </div>

    <!-- Control Buttons -->
    <div class="flex items-center justify-center space-x-4">
      <button
        v-if="!isRecording"
        class="bg-primary hover:bg-primary-hover flex items-center justify-center w-20 h-20 text-black transition-colors rounded-full shadow-md"
        @click="handleStart"
        aria-label="Start recording"
      >
        <MicrophoneIcon class="w-20 h-20" />
      </button>

      <template v-else>
        <button
          class="bg-primary hover:bg-primary-hover flex items-center justify-center w-20 h-20 text-black transition-colors rounded-full shadow-md"
          @click="handlePauseResume"
          :aria-label="isPaused ? 'Resume recording' : 'Pause recording'"
        >
          <component :is="isPaused ? PlayIcon : PauseIcon" class="w-20 h-20" />
        </button>

        <button
          class="bg-secondary hover:bg-secondary-hover flex items-center justify-center w-20 h-20 text-red-500 transition-colors rounded-full shadow-md"
          @click="handleStop"
          aria-label="Stop recording"
        >
          <StopIcon class="w-20 h-20" />
        </button>

        <button
          class="bg-accent hover:bg-accent-hover flex items-center justify-center w-20 h-20 text-black transition-colors rounded-full shadow-md"
          @click="onCancel"
          aria-label="Cancel recording"
        >
          <XMarkIcon class="w-20 h-20" />
        </button>
      </template>
    </div>
  </div>
</template>

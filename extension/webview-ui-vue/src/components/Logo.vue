<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

declare global {
  interface Window {
    voicehypeLogoLight?: string
    voicehypeLogoDark?: string
  }
}

interface Props {
  className?: string
}

defineProps<Props>()

const isDarkMode = ref(false)

// Detect VS Code theme
const checkTheme = () => {
  const isDark = document.body.classList.contains('vscode-dark') ||
                document.documentElement.classList.contains('vscode-dark')
  isDarkMode.value = isDark
}

onMounted(() => {
  // Check initially
  checkTheme()

  // Set up a mutation observer to detect theme changes
  const observer = new MutationObserver(checkTheme)
  observer.observe(document.body, { attributes: true, attributeFilter: ['class'] })
  observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] })

  // Cleanup observer on unmount
  onUnmounted(() => observer.disconnect())
})

// Use the appropriate logo URI based on the theme
const logoSrc = computed(() => 
  isDarkMode.value
    ? window.voicehypeLogoLight  // Use light logo (with white text) on dark background
    : window.voicehypeLogoDark   // Use dark logo (with dark text) on light background
)
</script>

<template>
  <div :class="`logo-container ${className}`">
    <img
      v-if="logoSrc"
      :src="logoSrc"
      alt="VoiceHype Logo"
      class="logo-image"
    />
    <div v-else class="logo-placeholder">VoiceHype</div>
  </div>
</template>

<style scoped>
.logo-container {
  display: flex;
  align-items: center;
}

.logo-image {
  height: 40px;
}

.logo-placeholder {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--vscode-foreground);
}
</style>
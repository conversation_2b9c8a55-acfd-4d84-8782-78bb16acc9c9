<script setup lang="ts">
import { computed } from 'vue'
import { InformationCircleIcon } from '@heroicons/vue/24/outline'

// interface ModelOption {
//   id: string
//   name: string
//   description: string
//   category: 'llama' | 'claude' | 'deepseek'
// }

interface Props {
  optimizationModel: string
}

const props = defineProps<Props>()
const emit = defineEmits(['change'])

const modelOptions = computed(() => [
  // Llama Models
  {
    id: 'llama-3.1-70b',
    name: 'Llama 3.1 70B',
    description: 'Meta\'s 70B parameter model with strong performance',
    category: 'llama'
  },
  {
    id: 'llama-3.1-8b-instruct-turbo',
    name: 'Llama 3.1 8B Instruct Turbo',
    description: 'Meta\'s 8B parameter model optimized for speed and instruction following',
    category: 'llama'
  },

  // Claude Models
  {
    id: 'claude-4-sonnet',
    name: 'Claude 4 Sonnet',
    description: 'Anthropic\'s most advanced model with superior reasoning',
    category: 'claude'
  },
  {
    id: 'claude-3.7-sonnet',
    name: 'Claude 3.7 Sonnet',
    description: 'Anthrop<PERSON>\'s latest model with excellent reasoning and instruction following',
    category: 'claude'
  },
  {
    id: 'claude-3.5-sonnet',
    name: 'Claude 3.5 Sonnet',
    description: 'Anthropic\'s balanced model with good performance',
    category: 'claude'
  },
  {
    id: 'claude-haiku',
    name: 'Claude Haiku',
    description: 'Anthropic\'s fast model optimized for efficiency',
    category: 'claude'
  },

  // DeepSeek Models
  {
    id: 'deepseek-v3',
    name: 'DeepSeek V3',
    description: 'DeepSeek\'s latest model with improved performance',
    category: 'deepseek'
  }
])

const selectedModel = computed(() => 
  modelOptions.value.find(o => o.id === props.optimizationModel) || modelOptions.value[0]
)

const groupedOptions = computed(() => {
  const llamaModels = modelOptions.value.filter(m => m.category === 'llama')
  const claudeModels = modelOptions.value.filter(m => m.category === 'claude')
  const deepseekModels = modelOptions.value.filter(m => m.category === 'deepseek')

  return { llamaModels, claudeModels, deepseekModels }
})

const handleModelChange = (e: Event) => {
  const newModel = (e.target as HTMLSelectElement).value
  if (newModel !== props.optimizationModel) {
    emit('change', newModel)
  }
}
</script>

<template>
  <div class="space-y-3">
    <div class="relative">
      <select
        class="border-border dark:border-dark-border dark:bg-dark-surface text-text dark:text-dark-text focus:outline-none focus:ring-2 focus:ring-primary/50 dark:focus:ring-dark-primary/50 w-full px-3 py-2 bg-white border rounded-md appearance-none cursor-pointer"
        :value="optimizationModel"
        @change="handleModelChange"
        aria-label="Select optimization model"
      >
        <optgroup label="Llama Models">
          <option 
            v-for="option in groupedOptions.llamaModels" 
            :key="option.id" 
            :value="option.id"
          >
            {{ option.name }}
          </option>
        </optgroup>
        <optgroup label="Claude Models">
          <option 
            v-for="option in groupedOptions.claudeModels" 
            :key="option.id" 
            :value="option.id"
          >
            {{ option.name }}
          </option>
        </optgroup>
        <optgroup label="DeepSeek Models">
          <option 
            v-for="option in groupedOptions.deepseekModels" 
            :key="option.id" 
            :value="option.id"
          >
            {{ option.name }}
          </option>
        </optgroup>
      </select>
      <div class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
        <svg class="text-muted dark:text-dark-muted w-4 h-4" fill="none" viewBox="0 0 20 20" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7l3-3 3 3m0 6l-3 3-3-3" />
        </svg>
      </div>
    </div>

    <div 
      v-if="selectedModel?.description"
      class="text-muted dark:text-dark-muted bg-gray-50 dark:bg-dark-surface/40 flex items-start p-3 text-sm rounded-md"
    >
      <InformationCircleIcon class="text-primary dark:text-dark-primary flex-shrink-0 w-5 h-5 mr-2" />
      <p>{{ selectedModel.description }}</p>
    </div>
  </div>
</template>

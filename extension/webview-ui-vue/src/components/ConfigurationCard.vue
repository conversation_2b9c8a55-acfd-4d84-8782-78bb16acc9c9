<script setup lang="ts">
import { computed } from 'vue'
import type { Configuration } from '../types/configuration'

interface Props {
  configuration: Configuration
  isActive: boolean
}

interface Emits {
  (e: 'activate', configId: string): void
  (e: 'edit', configId: string): void
  (e: 'delete', configId: string): void
  (e: 'duplicate', configId: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const cardClasses = computed(() => [
  'border rounded-lg p-4 transition-all duration-200 cursor-pointer',
  props.isActive 
    ? 'border-primary dark:border-dark-primary bg-primary/5 dark:bg-dark-primary/5 shadow-md' 
    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 hover:shadow-sm'
])

const getServiceDisplayName = (service: string) => {
  switch (service) {
    case 'assemblyai':
      return 'AssemblyAI'
    case 'lemonfox':
      return 'LemonFox'
    case 'whisper':
      return 'Whisper'
    default:
      return service
  }
}

const getModelDisplayName = (model: string) => {
  switch (model) {
    case 'whisper-1':
      return 'Whisper-1'
    case 'best':
      return 'Best'
    case 'nano':
      return 'Nano'
    default:
      return model
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}

const handleActivate = () => {
  if (!props.isActive) {
    emit('activate', props.configuration.id)
  }
}

const handleEdit = (e: Event) => {
  e.stopPropagation()
  emit('edit', props.configuration.id)
}

const handleDelete = (e: Event) => {
  e.stopPropagation()
  emit('delete', props.configuration.id)
}

const handleDuplicate = (e: Event) => {
  e.stopPropagation()
  emit('duplicate', props.configuration.id)
}
</script>

<template>
  <div :class="cardClasses" @click="handleActivate">
    <!-- Header -->
    <div class="flex items-start justify-between mb-3">
      <div class="flex-1">
        <div class="flex items-center gap-2">
          <h3 class="font-semibold text-gray-900 dark:text-gray-100">
            {{ configuration.title }}
          </h3>
          <span v-if="configuration.isDefault" class="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 rounded-full">
            Default
          </span>
          <span v-if="isActive" class="px-2 py-1 text-xs bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 rounded-full">
            Active
          </span>
        </div>
        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
          {{ configuration.description }}
        </p>
      </div>
      
      <!-- Actions -->
      <div class="flex items-center gap-1 ml-3">
        <button
          @click="handleEdit"
          class="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
          title="Edit configuration"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
        </button>
        
        <button
          @click="handleDuplicate"
          class="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
          title="Duplicate configuration"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
          </svg>
        </button>
        
        <button
          v-if="!configuration.isDefault"
          @click="handleDelete"
          class="p-1.5 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors"
          title="Delete configuration"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        </button>
      </div>
    </div>
    
    <!-- Settings Summary -->
    <div class="space-y-2 text-sm">
      <div class="flex items-center justify-between">
        <span class="text-gray-600 dark:text-gray-400">Service:</span>
        <span class="font-medium text-gray-900 dark:text-gray-100">
          {{ getServiceDisplayName(configuration.settings.service) }}
        </span>
      </div>
      
      <div class="flex items-center justify-between">
        <span class="text-gray-600 dark:text-gray-400">Model:</span>
        <span class="font-medium text-gray-900 dark:text-gray-100">
          {{ getModelDisplayName(configuration.settings.model) }}
        </span>
      </div>
      
      <div class="flex items-center justify-between">
        <span class="text-gray-600 dark:text-gray-400">Language:</span>
        <span class="font-medium text-gray-900 dark:text-gray-100">
          {{ configuration.settings.language.toUpperCase() }}
        </span>
      </div>
      
      <div class="flex items-center justify-between">
        <span class="text-gray-600 dark:text-gray-400">Features:</span>
        <div class="flex gap-1">
          <span v-if="configuration.settings.realtime" class="px-1.5 py-0.5 text-xs bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-200 rounded">
            Real-time
          </span>
          <span v-if="configuration.settings.optimizeEnabled" class="px-1.5 py-0.5 text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 rounded">
            Optimize
          </span>
          <span v-if="configuration.settings.translate" class="px-1.5 py-0.5 text-xs bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 rounded">
            Translate
          </span>
        </div>
      </div>
    </div>
    
    <!-- Footer -->
    <div class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700 text-xs text-gray-500 dark:text-gray-400">
      Created: {{ formatDate(configuration.createdAt) }}
    </div>
  </div>
</template>

/**
 * Configuration Types for VoiceHype
 * Bism<PERSON><PERSON> rahmanir raheem
 */

export interface ConfigurationSettings {
  // Transcription settings
  service: string;
  model: string;
  language: string;
  translate: boolean;
  realtime: boolean;
  
  // Optimization settings
  optimizeEnabled: boolean;
  optimizationModel: string;
  customPrompt: string;
  
  // Audio settings
  sampleRate: number;
  deviceId: string | null;
}

export interface Configuration {
  id: string;
  title: string;
  description: string;
  isDefault: boolean;
  settings: ConfigurationSettings;
  createdAt: string;
  updatedAt: string;
}

export interface ConfigurationState {
  configurations: Configuration[];
  activeConfigurationId: string | null;
  isLoading: boolean;
  error: string | null;
}

export interface WhatsNewInfo {
  version: string;
  title: string;
  features: string[];
  hasBeenShown: boolean;
}

export interface SnackbarMessage {
  id: string;
  message: string;
  type: 'success' | 'error' | 'info' | 'warning';
  duration: number;
  visible: boolean;
}

// Message types for communication with extension
export interface ConfigurationMessage {
  command: 'createConfiguration' | 'updateConfiguration' | 'deleteConfiguration' | 
           'switchConfiguration' | 'getConfigurations' | 'nextConfiguration' | 
           'previousConfiguration' | 'markWhatsNewSeen' | 'getWhatsNewStatus';
  data?: any;
}

// Default configuration settings based on current App.vue state
export const DEFAULT_CONFIGURATION_SETTINGS: ConfigurationSettings = {
  service: 'assemblyai',
  model: 'whisper-1',
  language: 'en',
  translate: false,
  realtime: false,
  optimizeEnabled: true,
  optimizationModel: 'gpt-4o',
  customPrompt: 'Correct any grammar issues and improve clarity. Keep the meaning intact.',
  sampleRate: 22050,
  deviceId: null
};

// What's new content for version 1.4.1
export const WHATS_NEW_141: WhatsNewInfo = {
  version: '1.4.1',
  title: '🎉 New Configuration Feature!',
  features: [
    'Create and manage multiple configurations',
    'Quick switch between settings for different use cases',
    'Perfect for multilingual workflows (English/Urdu)',
    'Keyboard shortcuts: Ctrl+Shift+PageUp/PageDown',
    'Instant switching with snackbar notifications'
  ],
  hasBeenShown: false
};

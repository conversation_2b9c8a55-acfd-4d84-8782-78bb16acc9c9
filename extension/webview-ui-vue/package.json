{"name": "webview-ui-vue", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build-with-types": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@heroicons/vue": "^2.2.0", "@pinia/nuxt": "^0.11.0", "@tailwindcss/vite": "^4.1.7", "autoprefixer": "^10.4.21", "pinia": "^3.0.2", "postcss": "^8.5.3", "tailwindcss": "^4.1.7", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@types/node": "^22.15.18", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}
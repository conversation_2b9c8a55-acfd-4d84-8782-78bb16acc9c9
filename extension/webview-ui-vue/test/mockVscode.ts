// Mock VSCode API for standalone testing
const mockVscode = {
  postMessage: (message: any) => {
    console.log('Mock VSCode postMessage:', message)
  },
  getState: () => ({}),
  setState: (state: any) => {}
}

// Mock window object
window.vscode = mockVscode

// Set mock logo URLs
window.voicehypeLogoLight = '/assets/voicehype_logo_light.png'
window.voicehypeLogoDark = '/assets/voicehype_logo_dark.png'

// Mock message handler
window.addEventListener('message', (event) => {
  if (event.data.command === 'getOptions') {
    // Respond with mock options
    window.postMessage({
      command: 'options',
      options: {
        apiKey: 'test-api-key',
        service: 'assemblyai',
        model: 'whisper-1',
        language: 'en'
      }
    })
  }
})

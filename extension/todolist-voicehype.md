# VoiceHype TODO List:

1. Fix WebView State Management **DONE**
   - Fix desync between backend and frontend **DONE**
   - Update stop button state in frontend when backend changes **DONE**

*الحمد للہ*

2. Fix Timer Implementation **DONE**   
   - Make timer global (independent of WebView) **DONE**
   - Timer should start on record button press, not WebView open **DONE**

*الحمد للہ*

3. Bundle FFmpeg Binaries
   - Add FFmpeg binaries to extension bundle
   - Use FFmpeg to compress WAV files to M4A format

4. Add More Settings to WebView **DONE**
   - Add settings interface in appropriate location **DONE**
   - Reference package.json for settings format **DONE**
   - Include sample rate and other audio settings **DONE**

*الحمد للہ*

5. Fix Transcription History Issues (webview) **DONE**
   - Fix "Clear Transcription History" button functionality **DONE**
   - Implement 50 message limit for transcription history **DONE**

*الحمد للہ*

6. Fix Sample Rate Auto-Switching **DONE**
   - When real-time transcription is enabled, automatically force-set sample rate of extension to 16000 Hz. Webview should be updated with the updated sample rate as well.  **DONE**
   - When real-time transcription is disabled, restore previous sample rate **DONE**

*الحمد للہ*

7. Improve Edge Function Audio Duration Reporting
   - Calculate audio duration on client side using same formula as normal transcription
   - Send calculated duration to edge function before connection close
   - Update edge function to log the correct duration and cost in usage database 

8. Cancel feature in the extension, very important! **DONE**

*الحمد للہ*
.vscode/**
.vscode-test/**
node_modules/**
**/*.ts
**/*.map
.gitignore
.git/**
.github/**
**/.git/**
**/tsconfig.json
**/tsconfig.tsbuildinfo
**/webpack.config.js
**/webview-ui/src/**
**/webview-ui/node_modules/**
**/webview-ui/public/**
**/webview-ui/package.json
**/webview-ui/package-lock.json
**/webview-ui/tsconfig.json

# Exclude webview-ui-vue development files
**/webview-ui-vue/src/**
**/webview-ui-vue/public/**
**/webview-ui-vue/node_modules/**
**/webview-ui-vue/test/**
**/webview-ui-vue/.*
**/webview-ui-vue/*.config.*
**/webview-ui-vue/*.json
# Only include the built files from webview-ui-vue
!**/webview-ui-vue/dist/**
echo
.eslintrc.js
eslint.config.mjs
.editorconfig
ARCHITECTURE.md
CHUNKING.md
REFACTORING.md
SUMMARY.md
vsc-extension-quickstart.md
.nyc*

# Explicitly exclude parent directory content
../**
../.*/**

# Exclude test files
test_*.ts
create_api_key.ts

# Exclude source files and only include distribution
src/**

# Temporarily exclude pre-compiled binaries for marketplace submission
# extension/resources/binaries/**

# IMPORTANT: Do NOT exclude webview-ui/build directory
!**/webview-ui/build/**

# Include assets directory for icons
!assets/**

# Exclude all node_modules by default
node_modules/**

# Only include important native modules and their dependencies
!node_modules/node-microphone/**
!node_modules/@voicehype/audify-plus/**
!node_modules/bindings/**
!node_modules/node-addon-api/**
!node_modules/file-uri-to-path/**

# Ignore .env files
.env
# Real-time Transcription Issues in VoiceHype Extension

## Overview

This document outlines several critical issues with the real-time transcription functionality that need to be addressed. These issues affect usage tracking, billing accuracy, and the overall user experience.

## Current Issues

### 1. Usage Tracking and Finalization

**Problem**: The `finalized_usage` function is either not being called or not working correctly in the WebSocket connection.

- The system creates a pending entry in the usage history table in the database
- However, it fails to create a "success" entry after the transcription is complete
- The user should only be charged for audio that was successfully processed (e.g., if 6 seconds were recorded but only 5.5 seconds were successfully processed, bill for 5.5 seconds)
- The extension needs to report accurate usage data for billing purposes

**Expected Behavior**:
- Track the duration of successfully processed audio chunks
- Call the `finalized_usage` function with the accurate processing duration
- Create a "success" entry in the database after transcription is complete

### 2. Duration Reporting

**Problem**: The UI shows incorrect duration information.

- When displaying results, the duration shows "0.0 seconds" 
- The extension needs to receive and display the actual processed audio duration
- This affects both the UI display and potentially the usage tracking
- The snackbar notification should show the correct processing duration

**Expected Behavior**:
- Receive duration information from the server after processing
- Update UI components with the correct duration
- Show accurate timing in notifications and history entries

### 3. Audio Chunk Processing

**Problem**: The system is discarding the last few audio chunks.

- When a user stops recording, not all chunks are being fully processed
- The WebSocket connection may be closing prematurely before all chunks are processed
- This causes both data loss and incorrect usage tracking
- Last few seconds of speech might be missing from the transcription

**Expected Behavior**:
- Send all recorded audio chunks to the server
- Keep the connection open until all chunks are processed
- Wait for server confirmation that all chunks have been processed
- Only then close the WebSocket connection

## Implementation Requirements

To fix these issues, the following changes are needed:

### 1. Proper Usage Finalization

- Ensure the WebSocket connection sends a proper "finalize" message to the server
- Add explicit handling for usage-related messages from the server
- Implement proper tracking of processed chunk durations
- Wait for the server to acknowledge receipt of all chunks before closing the connection
- Track the duration of successfully processed audio chunks and use this for billing

### 2. Duration Tracking

- Add listeners for duration information from the server
- Store and propagate this information to the UI components
- Update history entries with correct duration values
- Ensure notification messages use accurate duration information

### 3. Complete Chunk Processing

- Modify the connection closing protocol to ensure all chunks are processed
- Keep the WebSocket connection open after sending the last chunk
- Add a dedicated message to signal the end of audio data
- Wait for server confirmation that all chunks were processed
- Implement timeout handling to prevent hanging connections

## Technical Implementation Details

### RealtimeConnectionManager.ts Changes

```typescript
// Add fields to track duration and processed chunks
private processedDuration: number = 0;
private finalDurationReceived: boolean = false;

// Update message handling to track duration
this.ws.on('message', (data: Buffer) => {
  try {
    const parsedData = JSON.parse(data.toString());
    
    // Add handler for usage/duration information
    if (parsedData.type === 'usage' || parsedData.type === 'duration') {
      this.processedDuration = parsedData.duration || 0;
      this.finalDurationReceived = true;
      this.emitStatus(TranscriptionStatus.UsageReceived, `Duration: ${this.processedDuration}s`);
    }
    
    // Add handler for the 'all_processed' confirmation
    if (parsedData.type === 'all_processed' || parsedData.type === 'finalized') {
      this.allChunksProcessed = true;
      this.emitStatus(TranscriptionStatus.AllChunksProcessed);
    }
  } catch (error) {
    // Error handling
  }
});

// Modify the sendAudioChunk method to handle the last chunk properly
public sendAudioChunk(chunk: AudioChunk): void {
  // ... existing code ...
  
  if (chunk.isLastChunk) {
    console.log('VoiceHype: Sending final audio chunk');
    
    // Send a special message to indicate the end of audio but don't close yet
    setTimeout(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        console.log('VoiceHype: Sending finalize message to server');
        this.ws.send(JSON.stringify({ 
          type: 'finalize',
          action: 'end_of_audio'
        }));
      }
    }, 2000);
  }
}

// Add method to get the processed duration
public getProcessedDuration(): number {
  return this.processedDuration;
}

// Add method to wait for all chunks to be processed with timeout
public async waitForProcessing(timeoutMs: number = 15000): Promise<boolean> {
  if (this.allChunksProcessed) {
    return true;
  }
  
  return new Promise((resolve) => {
    const timeout = setTimeout(() => {
      console.log('VoiceHype: Timeout waiting for all chunks to be processed');
      resolve(false);
    }, timeoutMs);
    
    const checkProcessed = () => {
      if (this.allChunksProcessed) {
        clearTimeout(timeout);
        resolve(true);
      } else {
        setTimeout(checkProcessed, 500);
      }
    };
    
    checkProcessed();
  });
}
```

### RecordingService.ts Changes

```typescript
// Update stopRecording method
async stopRecording(shouldOptimize: boolean = false): Promise<void> {
  // ... existing code ...
  
  if (this.useRealtime && this.realtimeConnection) {
    // Send final chunk
    const finalChunk: AudioChunk = {
      data: new Uint8Array(0),
      isLastChunk: true
    };
    this.realtimeConnection.sendAudioChunk(finalChunk);
    
    // Show waiting message
    vscode.window.showInformationMessage('Processing final transcription...');
    
    // Wait for all chunks to be processed with a reasonable timeout
    await this.realtimeConnection.waitForProcessing(15000);
    
    // Get transcription and duration
    this.currentTranscript = this.realtimeConnection.getTranscript();
    const processedDuration = this.realtimeConnection.getProcessedDuration();
    
    // Close the connection
    this.realtimeConnection.close();
    this.realtimeConnection = null;
    
    // Process the transcription with correct duration
    if (this.currentTranscript.trim()) {
      // ... existing code ...
      
      // Add to history with correct duration
      this.historyService.addEntry({
        timestamp: new Date().getTime(),
        transcript: this.currentTranscript,
        optimizedTranscript: shouldOptimize ? this.currentTranscript : undefined,
        service: this.configService.getTranscriptionService(),
        model: this.configService.getTranscriptionModel(),
        fileReferences: fileTrackingLog,
        duration: processedDuration // Use the processed duration from the server
      });
      
      // Show notification with correct duration
      vscode.window.showInformationMessage(
        `Transcription complete (${processedDuration.toFixed(1)}s)`
      );
    }
  }
}
```

## Testing Plan

To verify the fixes, the following tests should be performed:

1. **Usage Tracking Test**:
   - Record a short audio clip in real-time mode
   - Check the database for both pending and success entries
   - Verify that the duration matches the actual processing time

2. **Duration Display Test**:
   - Record an audio clip
   - Verify that the notification shows the correct duration
   - Check history entries for accurate duration information

3. **Complete Processing Test**:
   - Record an audio clip with content at the end
   - Verify that the final words are included in the transcription
   - Check that all chunks are properly processed

4. **Edge Cases**:
   - Test with very short recordings
   - Test with network interruptions
   - Test with server delays in processing

## Conclusion

Fixing these issues will ensure accurate billing, improve user experience, and maintain data integrity in the real-time transcription feature. The changes primarily involve modifying how the WebSocket connection is managed and ensuring all audio data is properly processed before closing the connection. 
# VoiceHype

<div align="center">
 <p>
 <strong>Stop Typing. Start Talking.</strong><br>
 Transcribe accurately, optimize with AI, and super-charge your workflow.
 </p>
 <p>
 <a href="https://open-vsx.org/extension/VoiceHype/voicehype">
 <img src="https://img.shields.io/badge/Open%20VSX-Extension-orange?style=for-the-badge&logo=visualstudiocode" alt="VS Code Extension">
 </a>
 <a href="https://marketplace.visualstudio.com/items?itemName=VoiceHype.voicehype">
 <img src="https://img.shields.io/badge/VS%20Code-Extension-blue?style=for-the-badge&logo=visualstudiocode" alt="VS Code Extension">
 </a>
 <a href="https://voicehype.ai">
 <img src="https://img.shields.io/badge/Learn%20More-voicehype.ai-green?style=for-the-badge" alt="Learn More">
 </a>
 </p>
</div>

> Write prompts faster with your voice. Transform spoken ideas into perfect prompts.

VoiceHype is a voice-to-prompt productivity tool designed specifically for developers. Speak naturally and watch your ideas transform into well-crafted prompts - no more tedious typing when working with AI tools.

## 🎤 Microphone Permissions Required

**Important:** VoiceHype requires microphone access to function properly. You may need to grant VS Code permission to use your microphone:

- **macOS**: Go to **System Settings** > **Privacy & Security** > **Microphone** and ensure **Visual Studio Code / CURSOR / any other vscode fork you are using** is enabled

If recording doesn't work, please check these settings first.

## Why VoiceHype?

- **Save Time**: Speak your prompt ideas 3x faster than typing them
- **Reduce Fatigue**: Give your fingers a break from constant typing
- **Improve Focus**: Stay in your creative flow without keyboard interruptions
- **Perfect for Remote Work**: Ideal for creating prompts from anywhere

## Key Features

- 🎙️ **Voice Recording**: Record your voice directly within VS Code
- 🔄 **High-Quality Transcription**: Convert speech to text using AssemblyAI (best/nano models) or Whisper
- 🧠 **AI Optimization**: Enhance transcripts with powerful LLMs (Llama, Claude, DeepSeek models) to create better prompts
- ⚡ **Real-time Transcription**: Get immediate feedback as you speak (with AssemblyAI best model)
- ⏯️ **Pause & Resume**: Control your recording flow with simple keyboard shortcuts
- 🌐 **Multiple Languages**: Support for various languages
- 💾 **Recording History**: Access and reuse your past voice memos
- 🖥️ **Cross-Platform**: Works on Windows, macOS, and Linux

## Quick Start

1. Install the extension from the VS Code marketplace
2. Open VoiceHype from the activity bar or use the keyboard shortcuts to start recording
3. Sign in with your VoiceHype account (or create one if you don't have it)
4. Start recording with the basic or AI optimization shortcuts
5. Speak naturally - no need for perfect syntax
6. Stop recording with the same shortcut
7. Your transcribed (and optionally optimized) prompt appears instantly

## Keyboard Shortcuts

- **Basic Recording**: `Ctrl+Shift+8` (Windows/Linux) • `Cmd+Shift+8` (Mac)
- **AI Optimized Recording**: `Ctrl+Shift+9` (Windows/Linux) • `Cmd+Shift+9` (Mac)
- **Pause/Resume**: `Ctrl+Shift+0` (Windows/Linux) • `Cmd+Shift+0` (Mac)

## Supported Models

### Transcription
- AssemblyAI best model (with real-time capability)
- AssemblyAI nano model
- Whisper model

### Optimization
- **Llama models**: Llama 4 Scout/Maverick, Llama 3 70B/8B
- **Claude models**: Claude 3.7 Sonnet, Claude 3.5 Sonnet/Haiku
- **DeepSeek models**: DeepSeek R1, DeepSeek V3

---

## License

VoiceHype is proprietary software provided under a custom license. By installing and using this extension, you agree to the terms outlined in the [LICENSE.md](LICENSE.md) file included with this extension.

VoiceHype is committed to operating in accordance with Islamic principles, including transparency in business dealings, avoiding interest-based transactions, and treating all customers with fairness and respect.

## For Support

If you encounter any issues or have questions, please contact us at:
- Email: <EMAIL>
- Website: [https://voicehype.ai](https://voicehype.ai)

**Enjoy using VoiceHype!**
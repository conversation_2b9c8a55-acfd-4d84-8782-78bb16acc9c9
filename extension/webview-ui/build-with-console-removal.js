const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Custom build script for React webview with console.log removal
console.log('Building React webview with console.log removal...');

// Set environment variables for production build
process.env.NODE_ENV = 'production';
process.env.GENERATE_SOURCEMAP = 'false';
process.env.REACT_APP_DISABLE_CONSOLE = 'true';

// Build the React app
try {
  execSync('npm run build', { 
    stdio: 'inherit',
    cwd: __dirname,
    env: { ...process.env }
  });
  
  console.log('✅ React webview built successfully with console.log removal');
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}
/**
 * Utility for communicating with the VS Code extension's webview API.
 * This object provides a convenient way to post messages from the webview to the extension.
 */

interface VSCodeAPI {
  postMessage(message: any): void;
  getState(): any;
  setState(state: any): void;
}

// Acquire the VS Code API object
const acquireVsCodeApi = (): VSCodeAPI => {
  // If we're running in a webview context, we have access to the acquireVsCodeApi function
  if (typeof acquireVsCodeApi === 'function') {
    return (window as any).acquireVsCodeApi();
  }

  // For development outside of VS Code
  return {
    postMessage: (message: any) => {
      console.log('Development mode: posting message to extension', message);
    },
    getState: () => null,
    setState: (_state: any) => {
      console.log('Development mode: setting state', _state);
    }
  };
};

// Export the VS Code API instance
export const vscode = acquireVsCodeApi();

/**
 * Get the URI for a resource in the extension
 * @param path Path to the resource relative to the extension root
 * @returns The URI for the resource
 */
export const getResourceUri = (path: string): string => {
  // In development mode, we can't use the VS Code URI scheme
  if (process.env.NODE_ENV === 'development') {
    return path;
  }

  // Get the URI from the current document URL
  const baseUrl = document.location.href.split('/').slice(0, -1).join('/');

  // The path should be relative to the extension root
  // For root level files, we need to use a special format
  if (path.startsWith('/')) {
    path = path.substring(1);
  }

  // Use the vscode-resource scheme for accessing files
  return `${baseUrl}/../../../../${path}`;
};
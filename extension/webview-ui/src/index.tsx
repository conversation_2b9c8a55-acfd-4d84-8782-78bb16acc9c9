import * as React from 'react';
import * as ReactDOM from 'react-dom';
import './compiled.css';
import './index.css';
import App from './App';
import { vscode } from './utilities/vscode';

// Wait for theme from extension instead of DOM detection
let themeApplied = false;

const applyTheme = (isDark: boolean) => {
  if (themeApplied) return; // Only apply once initially
  
  // Apply the VSCode classes
  document.documentElement.classList.add(isDark ? 'vscode-dark' : 'vscode-light');
  document.body.classList.add(isDark ? 'vscode-dark' : 'vscode-light');
  
  // Add the 'dark' class for Tailwind - THIS IS THE KEY PART
  if (isDark) {
    document.documentElement.classList.add('dark');
  }
  
  console.log('VS Code theme applied from extension:', isDark ? 'dark' : 'light');
  themeApplied = true;
};

const updateTheme = (isDark: boolean) => {
  // Remove old classes
  document.documentElement.classList.remove('vscode-dark', 'vscode-light', 'dark');
  document.body.classList.remove('vscode-dark', 'vscode-light');
  
  // Apply new theme
  document.documentElement.classList.add(isDark ? 'vscode-dark' : 'vscode-light');
  document.body.classList.add(isDark ? 'vscode-dark' : 'vscode-light');
  
  // Add the 'dark' class for Tailwind
  if (isDark) {
    document.documentElement.classList.add('dark');
  }
  
  console.log('VS Code theme updated:', isDark ? 'dark' : 'light');
};

// Listen for initial theme from extension
const handleInitialTheme = (event: MessageEvent) => {
  const message = event.data;
  if (message.command === 'initialConfiguration' && message.options?.theme !== undefined) {
    const isDark = message.options.theme === 'dark';
    applyTheme(isDark);
    // Remove this specific listener after initial theme is applied
    window.removeEventListener('message', handleInitialTheme);
  } else if (message.command === 'themeChanged') {
    // Handle themeChanged message as well for initial theme
    const theme = message.data?.theme || message.theme;
    const isDark = theme === 'dark';
    applyTheme(isDark);
    // Remove this specific listener after initial theme is applied
    window.removeEventListener('message', handleInitialTheme);
  }
};

// Handle theme changes
const handleThemeChange = (event: MessageEvent) => {
  const message = event.data;
  if (message.command === 'themeChanged') {
    const theme = message.data?.theme || message.theme;
    const isDark = theme === 'dark';
    updateTheme(isDark);
  }
};

// Set up listeners
window.addEventListener('message', handleInitialTheme);
window.addEventListener('message', handleThemeChange);

// Request theme from extension if not applied within 3 seconds
setTimeout(() => {
  if (!themeApplied) {
    console.log('Requesting theme from extension...');
    // @ts-ignore
    vscode.postMessage({ command: 'refreshConfigAndTheme' });
  }
}, 3000);

ReactDOM.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
  document.getElementById('root')
);
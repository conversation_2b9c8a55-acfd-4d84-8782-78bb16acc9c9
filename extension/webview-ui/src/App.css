/* General styles */
:root {
  --vscode-font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  --container-padding: 12px;
  --input-padding: 6px;
  --border-radius: 4px;
}

body {
  padding: 0;
  margin: 0;
  color: var(--vscode-foreground);
  font-family: var(--vscode-font-family);
  background-color: var(--vscode-editor-background);
}

.app-container {
  padding: var(--container-padding);
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.section-header {
  margin-bottom: 8px;
}

.section-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--vscode-editor-foreground);
}

/* Translate toggle */
.translate-toggle {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 8px;
}

.translate-toggle label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  cursor: pointer;
}

.translate-toggle input[type="checkbox"] {
  margin: 0;
}

/* Recording controls */
.recording-controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px;
  border-radius: var(--border-radius);
  background-color: var(--vscode-editor-inactiveSelectionBackground);
}

.duration-display {
  font-size: 24px;
  font-weight: bold;
  text-align: center;
  font-family: monospace;
  margin-bottom: 8px;
}

.buttons-container {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-bottom: 8px;
}

.record-button, .stop-button, .pause-button {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 20px;
}

.record-button {
  background-color: var(--vscode-debugIcon-startForeground);
  color: white;
}

.stop-button {
  background-color: var(--vscode-debugIcon-stopForeground);
  color: white;
}

.pause-button {
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
}

.pause-button.paused {
  background-color: var(--vscode-button-secondaryBackground);
}

.optimize-toggle {
  display: flex;
  justify-content: center;
}

.optimize-toggle label {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
}

/* Service selector */
.service-selector {
  margin-bottom: 8px;
}

.toggle-group {
  display: flex;
  gap: 8px;
}

.toggle-button {
  flex: 1;
  padding: 8px;
  background-color: var(--vscode-button-secondaryBackground);
  color: var(--vscode-button-secondaryForeground);
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
}

.toggle-button.active {
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
}

/* Dropdowns (Model & Language) */
.dropdown-container {
  width: 100%;
}

select {
  width: 100%;
  padding: var(--input-padding);
  color: var(--vscode-input-foreground);
  background-color: var(--vscode-input-background);
  border: 1px solid var(--vscode-input-border);
  border-radius: var(--border-radius);
}

.model-description {
  font-size: 12px;
  color: var(--vscode-descriptionForeground);
  margin-top: 4px;
}

/* Custom prompt */
.custom-prompt {
  margin-bottom: 8px;
}

.textarea-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

textarea {
  width: 100%;
  padding: var(--input-padding);
  color: var(--vscode-input-foreground);
  background-color: var(--vscode-input-background);
  border: 1px solid var(--vscode-input-border);
  border-radius: var(--border-radius);
  resize: vertical;
  min-height: 80px;
}

.prompt-help {
  display: flex;
  gap: 4px;
  font-size: 12px;
  color: var(--vscode-descriptionForeground);
}

/* Recent transcriptions */
.transcription-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.transcription-item {
  padding: 8px;
  border-radius: var(--border-radius);
  background-color: var(--vscode-editor-inactiveSelectionBackground);
}

.transcription-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 11px;
  color: var(--vscode-descriptionForeground);
}

.transcription-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.original-transcript, .optimized-transcript {
  display: flex;
  justify-content: space-between;
  gap: 8px;
  align-items: center;
}

.transcript-text {
  flex: 1;
  font-size: 13px;
  word-break: break-word;
}

.optimize-actions {
  display: flex;
  justify-content: flex-end;
}

.copy-button, .optimize-button {
  background-color: var(--vscode-button-secondaryBackground);
  color: var(--vscode-button-secondaryForeground);
  border: none;
  border-radius: var(--border-radius);
  padding: 4px 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.optimize-button {
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
}

.no-transcriptions {
  color: var(--vscode-descriptionForeground);
  font-style: italic;
  text-align: center;
  padding: 12px;
}

/* Real-time Transcript Component Styles */
.realtime-transcript {
  border: 1px solid var(--vscode-panel-border);
  border-radius: var(--border-radius);
  background-color: var(--vscode-editor-background);
  margin-bottom: 16px;
  overflow: hidden;
}

.realtime-transcript-header {
  padding: 8px 12px;
  background-color: var(--vscode-panel-background);
  border-bottom: 1px solid var(--vscode-panel-border);
}

.status-indicator {
  display: flex;
  align-items: center;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--vscode-charts-red);
  transition: background-color 0.3s ease;
}

.status-indicator.connected .status-dot {
  background-color: var(--vscode-charts-green);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.realtime-transcript-content {
  max-height: 200px;
  overflow-y: auto;
  padding: 12px;
  min-height: 80px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.transcript-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 60px;
}

.transcript-segment {
  padding: 4px 8px;
  border-radius: 4px;
  line-height: 1.4;
  word-wrap: break-word;
}

.transcript-segment.final {
  background-color: var(--vscode-editor-selectionBackground);
  color: var(--vscode-editor-foreground);
}

.transcript-segment.partial {
  background-color: var(--vscode-editor-hoverHighlightBackground);
  color: var(--vscode-editor-foreground);
  border: 1px dashed var(--vscode-panel-border);
  display: flex;
  align-items: center;
  gap: 4px;
}

.segment-text {
  flex: 1;
  font-size: 13px;
}

.typing-indicator {
  color: var(--vscode-charts-blue);
  font-weight: bold;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* Beautiful Real-time Transcript Component Styles */
.beautiful-realtime-transcript {
  border: 1px solid var(--vscode-panel-border);
  border-radius: 8px;
  background-color: var(--vscode-editor-background);
  margin-bottom: 16px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.beautiful-realtime-transcript-header {
  padding: 12px 16px;
  background-color: var(--vscode-panel-background);
  border-bottom: 1px solid var(--vscode-panel-border);
}

.beautiful-realtime-transcript-header h3 {
  font-size: 14px;
  font-weight: 600;
  margin: 0;
}

.status-indicator {
  display: flex;
  align-items: center;
}

.status-indicator .mic-icon {
  width: 16px;
  height: 16px;
  color: var(--vscode-charts-red);
  transition: color 0.3s ease;
}

.status-indicator.connected .mic-icon {
  color: var(--vscode-charts-green);
}

.clear-button {
  background-color: var(--vscode-button-secondaryBackground);
  color: var(--vscode-button-secondaryForeground);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.clear-button:hover {
  background-color: var(--vscode-button-secondaryHoverBackground);
}

.beautiful-realtime-transcript-content {
  max-height: 200px;
  overflow-y: auto;
  padding: 16px;
  min-height: 100px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.transcript-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 80px;
}

.mic-icon-large {
  width: 24px;
  height: 24px;
  margin: 0 auto;
}

.transcript-segment {
  padding: 8px 12px;
  border-radius: 6px;
  line-height: 1.5;
  word-wrap: break-word;
  transition: all 0.2s ease;
}

.transcript-segment.final {
  background-color: var(--vscode-editor-selectionBackground);
  color: var(--vscode-editor-foreground);
  border-left: 3px solid var(--vscode-charts-green);
}

.transcript-segment.partial {
  background-color: var(--vscode-editor-hoverHighlightBackground);
  color: var(--vscode-editor-foreground);
  border: 1px dashed var(--vscode-panel-border);
  display: flex;
  align-items: center;
  gap: 6px;
}

.segment-text {
  flex: 1;
  font-size: 14px;
}

.typing-indicator {
  color: var(--vscode-charts-blue);
  font-weight: bold;
  animation: blink 1s infinite;
}

.transcript-segment.grace-period {
  background-color: var(--vscode-editor-inactiveSelectionBackground);
  border-left: 3px solid var(--vscode-charts-blue);
  padding: 12px;
}
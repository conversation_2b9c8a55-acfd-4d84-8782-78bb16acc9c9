// This file is used to generate a compatible PostCSS config for CRA 3.x
const fs = require('fs');
const path = require('path');

// Create a custom PostCSS config that works with CRA 3.x
const postcssConfig = `module.exports = {
  plugins: [
    require('tailwindcss'),
    require('autoprefixer'),
  ]
};`;

// Write the PostCSS config file
fs.writeFileSync(path.join(__dirname, '../postcss.config.js'), postcssConfig);

console.log('PostCSS configured for Tailwind CSS with Create React App 3.x'); 
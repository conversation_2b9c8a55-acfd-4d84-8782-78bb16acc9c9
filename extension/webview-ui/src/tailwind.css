/* Tailwind directives */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* VSCode theme detection */
:root {
  --vscode-font-family: var(--vscode-editor-font-family, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif);
  /* VoiceHype brand colors */
  --brand-primary: #14F195;
  --brand-primary-hover: #0ad680;
  --brand-primary-light: #7df8c7;
  --brand-secondary: #6366F1;
  --brand-secondary-hover: #4f46e5;
  --brand-accent: #0AD6DF;
  --brand-accent-hover: #08b8c0;
}

/* Theme-specific styles */
.vscode-dark {
  background-color: #080814;
  color: #f9fafb;
  --brand-primary: #14F195;
  --brand-primary-hover: #0ad680;
  --brand-secondary: #6366F1;
  --brand-secondary-hover: #4f46e5;
  --brand-accent: #0AD6DF;
  --brand-accent-hover: #08b8c0;
}

.vscode-light {
  background-color: #ffffff;
  color: #111827;
  --brand-primary: #14F195;
  --brand-primary-hover: #0ad680;
  --brand-secondary: #6366F1;
  --brand-secondary-hover: #4f46e5;
  --brand-accent: #0AD6DF;
  --brand-accent-hover: #08b8c0;
}

/* Component styles */
.ui-container {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

/* Logo styles */
.logo-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem 0;
  margin-bottom: 0.5rem;
}

.logo-image {
  height: 2.5rem;
  max-width: 100%;
  object-fit: contain;
}

.logo-placeholder {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--brand-primary);
  text-align: center;
  font-family: 'Arial', sans-serif;
}

.ui-card {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 1rem;
}

.vscode-dark .ui-card {
  background-color: #111827;
  border: 1px solid #374151;
}

.ui-card-header {
  padding: 0.75rem 1rem;
  font-weight: 600;
  font-size: 0.875rem;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.vscode-dark .ui-card-header {
  background-color: #1f2937;
  border-bottom: 1px solid #374151;
}

.ui-card-body {
  padding: 1rem;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.4);
}

.btn-primary {
  background-color: var(--brand-primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--brand-primary-hover);
}

.btn-secondary {
  background-color: var(--brand-secondary);
  color: white;
}

.btn-secondary:hover {
  background-color: var(--brand-secondary-hover);
}

.btn-accent {
  background-color: var(--brand-accent);
  color: white;
}

.btn-accent:hover {
  background-color: var(--brand-accent-hover);
}

.btn-icon {
  width: 2.5rem;
  height: 2.5rem;
  padding: 0;
  border-radius: 9999px;
}

.btn-icon-sm {
  width: 2rem;
  height: 2rem;
  padding: 0;
  border-radius: 9999px;
}

/* Form controls */
.input {
  width: 100%;
  padding: 0.625rem 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.2s, box-shadow 0.2s;
  background-color: white;
}

.input:focus {
  outline: none;
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
}

.vscode-dark .input {
  border-color: #374151;
  background-color: #1f2937;
  color: #f9fafb;
}

.vscode-dark .input:focus {
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.4);
}

.label {
  display: block;
  margin-bottom: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
}

.vscode-dark .label {
  color: #9ca3af;
}

/* Select styling */
.select-container {
  position: relative;
  width: 100%;
}

.select-container::after {
  content: '';
  position: absolute;
  top: 50%;
  right: 1rem;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #6b7280;
  pointer-events: none;
}

.vscode-dark .select-container::after {
  border-top-color: #9ca3af;
}

.select {
  width: 100%;
  padding: 0.625rem 2rem 0.625rem 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-color: white;
  cursor: pointer;
}

.select:focus {
  outline: none;
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
}

.vscode-dark .select {
  border-color: #374151;
  background-color: #1f2937;
  color: #f9fafb;
}

.vscode-dark .select:focus {
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.4);
}
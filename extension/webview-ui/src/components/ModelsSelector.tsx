import React, { memo, useCallback } from 'react';
import { InformationCircleIcon } from '@heroicons/react/24/outline';

interface ModelSelectorProps {
  model: string;
  onChange: (model: string, service: string) => void;
}

interface ModelOption {
  id: string;
  name: string;
  description: string;
  service: string;
  modelId: string; // The actual model ID to be sent to the API
}

const ModelsSelector: React.FC<ModelSelectorProps> = memo(({ model, onChange }) => {
  // Combined list of all models from different services
  const allModels: ModelOption[] = [
    { 
      id: 'assemblyai-best', 
      name: 'AssemblyAI Best', 
      description: 'High accuracy, optimized for various accents and domains',
      service: 'assemblyai',
      modelId: 'best'
    },
    { 
      id: 'assemblyai-nano', 
      name: 'AssemblyAI Nano', 
      description: 'Faster transcription with small quality tradeoff',
      service: 'assemblyai',
      modelId: 'nano'
    },
    { 
      id: 'whisper-v3', 
      name: 'Whisper v3', 
      description: 'Lemon Fox\'s high-quality, fast Whisper v3 model',
      service: 'lemonfox',
      modelId: 'whisper-v3'
    }
  ];

  const selectedModel = allModels.find(m => m.id === model) || allModels[0];

  const handleModelChange = useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedOption = allModels.find(m => m.id === e.target.value);
    if (selectedOption) {
      onChange(selectedOption.id, selectedOption.service);
    }
  }, [onChange, allModels]);

  return (
    <div className="space-y-3">
      <div className="relative">
        <select 
          className="border-border dark:border-dark-border dark:bg-dark-surface text-text dark:text-dark-text focus:outline-none focus:ring-2 focus:ring-primary/50 dark:focus:ring-dark-primary/50 w-full px-3 py-2 bg-white border rounded-md appearance-none cursor-pointer"
          value={model}
          onChange={handleModelChange}
          aria-label="Select model"
        >
          {allModels.map((option) => (
            <option key={option.id} value={option.id}>
              {option.name}
            </option>
          ))}
        </select>
        <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
          <svg className="text-muted dark:text-dark-muted w-4 h-4" fill="none" viewBox="0 0 20 20" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7l3-3 3 3m0 6l-3 3-3-3" />
          </svg>
        </div>
      </div>
      
      {selectedModel?.description && (
        <div className="text-muted dark:text-dark-muted bg-gray-50 dark:bg-dark-surface/40 flex items-start p-3 text-sm rounded-md">
          <InformationCircleIcon className="text-primary dark:text-dark-primary flex-shrink-0 w-5 h-5 mr-2" />
          <p>{selectedModel.description}</p>
        </div>
      )}
    </div>
  );
});

// Display name for debugging
ModelsSelector.displayName = 'ModelsSelector';

export default ModelsSelector; 
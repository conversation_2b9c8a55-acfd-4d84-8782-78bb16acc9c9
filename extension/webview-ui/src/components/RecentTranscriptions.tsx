import * as React from 'react';
import { useState, useMemo } from 'react';
import { ClipboardDocumentIcon, SparklesIcon, ClockIcon, MagnifyingGlassIcon, XMarkIcon, TrashIcon, ArrowDownIcon, ChevronDownIcon, ChevronUpIcon, EyeIcon } from '@heroicons/react/24/outline';
import { vscode } from '../utilities/vscode';

interface Transcription {
  id: string;
  timestamp: string;
  originalText: string;
  optimizedText?: string;
  service: string;
  model: string;
  language: string;
  duration?: number;
  isPreview?: boolean;
}

interface RecentTranscriptionsProps {
  transcriptions: Transcription[];
  onCopyOriginal: (id: string) => void;
  onCopyOptimized: (id: string) => void;
  onOptimize: (id: string) => void;
  onPreviewOptimization: (id: string, preview: boolean) => void;
}

const ITEMS_PER_PAGE = 5;
// Increased the default truncation limit significantly
const DEFAULT_TRUNCATION_LIMIT = 500;

const RecentTranscriptions: React.FC<RecentTranscriptionsProps> = ({
  transcriptions,
  onCopyOriginal,
  onCopyOptimized,
  onOptimize,
  onPreviewOptimization
}) => {
  // State for search and sorting
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [sortOption, setSortOption] = useState<string>("newest");
  // Pagination state
  const [displayCount, setDisplayCount] = useState<number>(ITEMS_PER_PAGE);
  // State to track expanded transcriptions (both original and optimized)
  const [expandedItems, setExpandedItems] = useState<{[key: string]: {original: boolean, optimized: boolean}}>({});
  // State to track previewed optimizations
  const [previewOptimizations, setPreviewOptimizations] = useState<{[key: string]: boolean}>({});

  // Format timestamp to human readable format
  const formatTimestamp = (timestamp: string): string => {
    try {
      const date = new Date(timestamp);
      const now = new Date();
      const isToday = date.getDate() === now.getDate() &&
                    date.getMonth() === now.getMonth() &&
                    date.getFullYear() === now.getFullYear();

      // Show time for today's recordings, or date for older ones
      if (isToday) {
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      } else {
        return date.toLocaleDateString([], {
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        });
      }
    } catch (error) {
      console.error("Error formatting timestamp:", error);
      return "Invalid date";
    }
  };

  // Format service and model for display
  const formatServiceModel = (service: string, model: string): string => {
    // Handle cases where service or model might be undefined
    const displayService = service || "unknown";
    const displayModel = model || "unknown";
    return `${displayService} - ${displayModel}`;
  };

  // Truncate text if it's too long, but check if it's expanded first
  const truncateText = (text: string, id: string, type: 'original' | 'optimized'): string => {
    if (!text) return '';
    
    // Check if this specific item and type (original/optimized) is expanded
    const isExpanded = expandedItems[id]?.[type] || false;
    
    if (isExpanded || text.length <= DEFAULT_TRUNCATION_LIMIT) {
      return text;
    }
    
    return text.substring(0, DEFAULT_TRUNCATION_LIMIT) + '...';
  };

  // Toggle expanded state for a specific item and type
  const toggleExpanded = (id: string, type: 'original' | 'optimized') => {
    setExpandedItems(prev => {
      const current = prev[id] || { original: false, optimized: false };
      return Object.assign({}, prev, {
        [id]: Object.assign({}, current, {
          [type]: !current[type]
        })
      });
    });
  };

  // Toggle preview for an optimization
  const togglePreviewOptimization = (id: string) => {
    const isPreviewing = previewOptimizations[id] || false;
    
    // Update local UI state
    setPreviewOptimizations(prev => Object.assign({}, prev, {
      [id]: !isPreviewing
    }));
    
    // Call parent handler with the new preview state
    onPreviewOptimization(id, !isPreviewing);
  };

  // Handle clearing all transcription history
  const handleClearAllHistory = () => {
    console.log('RecentTranscriptions: Clear all history button clicked. Current transcriptions count:', transcriptions.length);

    // Send message to extension to show confirmation dialog
    vscode.postMessage({
      command: 'showConfirmation',
      message: 'Are you sure you want to delete all transcription history? This cannot be undone.',
      onConfirm: 'clearTranscriptionHistory'
    });
    console.log('RecentTranscriptions: Sent showConfirmation message with onConfirm=clearTranscriptionHistory');

    // Clear local state will be handled when we receive the 'allTranscriptionsCleared' message from the extension (if the user confirms)
  };

  // Handle deleting a single transcription
  const handleDeleteTranscription = (id: string) => {
    vscode.postMessage({
      command: 'deleteTranscription',
      id: id
    });
  };

  // Load more items
  const handleLoadMore = () => {
    setDisplayCount(prev => prev + ITEMS_PER_PAGE);
  };

  // Filter and sort transcriptions
  const filteredTranscriptions = useMemo(() => {
    let filtered = Array.from(transcriptions);

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(item =>
        item.originalText.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (item.optimizedText && item.optimizedText.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Apply sorting
    switch (sortOption) {
      case "newest":
        filtered.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
        break;
      case "oldest":
        filtered.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
        break;
      default:
        filtered.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    }

    return filtered;
  }, [transcriptions, searchQuery, sortOption]);

  // Get the paginated items
  const displayedTranscriptions = useMemo(() => {
    return filteredTranscriptions.slice(0, displayCount);
  }, [filteredTranscriptions, displayCount]);

  // Check if there are more items to load
  const hasMoreItems = displayedTranscriptions.length < filteredTranscriptions.length;

  // Format duration to mm:ss format
  const formatDuration = (seconds?: number): string => {
    if (!seconds) return '';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="flex flex-col h-full">
      {/* Search and Filter Controls */}
      <div className="border-border dark:border-dark-border p-2 mb-2 border-b">
        <div className="flex items-center justify-between mb-2">
          <div className="relative flex-grow">
            <input
              type="text"
              placeholder="Search transcriptions..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-3 py-1.5 pl-8 text-sm bg-gray-50 dark:bg-dark-surface border border-border dark:border-dark-border rounded-md"
            />
            <MagnifyingGlassIcon className="absolute left-2.5 top-1/2 transform -translate-y-1/2 w-3.5 h-3.5 text-muted dark:text-dark-muted" />
            {searchQuery && (
              <button
                onClick={() => setSearchQuery('')}
                className="absolute right-2.5 top-1/2 transform -translate-y-1/2"
              >
                <XMarkIcon className="w-3.5 h-3.5 text-muted dark:text-dark-muted hover:text-primary dark:hover:text-dark-primary" />
              </button>
            )}
          </div>
          {transcriptions.length > 0 && (
            <button
              onClick={handleClearAllHistory}
              className="ml-2 p-1.5 text-red-600 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-md"
              title="Clear all transcription history"
            >
              <TrashIcon className="w-4 h-4" />
            </button>
          )}
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center justify-center space-x-2 bg-gray-100 dark:bg-gray-800 rounded-full p-1 max-w-[200px]">
            <button
              className={`flex-1 py-1 px-3 text-xs font-medium rounded-full transition-colors ${
                sortOption === "newest"
                  ? "bg-white dark:bg-gray-700 shadow-sm text-primary dark:text-dark-primary"
                  : "text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
              }`}
              onClick={() => setSortOption("newest")}
            >
              Newest
            </button>
            <button
              className={`flex-1 py-1 px-3 text-xs font-medium rounded-full transition-colors ${
                sortOption === "oldest"
                  ? "bg-white dark:bg-gray-700 shadow-sm text-primary dark:text-dark-primary"
                  : "text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
              }`}
              onClick={() => setSortOption("oldest")}
            >
              Oldest
            </button>
          </div>
          {transcriptions.length > 0 && (
            <span className="text-muted dark:text-dark-muted text-xs">
              {transcriptions.length}/50 messages
            </span>
          )}
        </div>
      </div>

      {/* Transcription List */}
      <div className="flex-grow px-2 pb-2 space-y-3 overflow-y-auto">
        {filteredTranscriptions.length === 0 ? (
          <div className="text-muted dark:text-dark-muted border-border dark:border-dark-border flex flex-col items-center justify-center p-4 text-sm text-center border border-dashed rounded-md">
            <ClockIcon className="w-8 h-8 mb-2 opacity-50" />
            {searchQuery ? 'No matching transcriptions found' : 'No transcription history yet'}
          </div>
        ) : (
          <>
            {displayedTranscriptions.map(transcription => (
              <div key={transcription.id} className="dark:bg-dark-surface/50 border-border dark:border-dark-border group overflow-hidden bg-white border rounded-md shadow-sm">
                <div className="flex items-center justify-between px-3 py-1.5 bg-gray-50 dark:bg-dark-surface border-b border-border dark:border-dark-border">
                  <div className="text-muted dark:text-dark-muted flex items-center text-xs">
                    <ClockIcon className="w-3 h-3 mr-1" />
                    <span>{formatTimestamp(transcription.timestamp)}</span>
                    {transcription.duration && (
                      <span className="ml-2">({formatDuration(transcription.duration)})</span>
                    )}
                  </div>
                  <div className="flex items-center">
                    <div className="text-muted dark:text-dark-muted mr-2 text-xs">
                      {formatServiceModel(transcription.service, transcription.model)}
                    </div>
                    <button
                      onClick={() => handleDeleteTranscription(transcription.id)}
                      className="group-hover:opacity-100 text-muted hover:text-red-500 dark:text-dark-muted dark:hover:text-red-400 transition-opacity opacity-0"
                      title="Delete transcription"
                    >
                      <TrashIcon className="w-3.5 h-3.5" />
                    </button>
                  </div>
                </div>

                <div className="p-2 space-y-2">
                  {/* Original Transcript */}
                  <div className="space-y-1">
                    <div className="text-muted dark:text-dark-muted flex items-center justify-between text-xs font-semibold uppercase">
                      <span>Original</span>
                      {transcription.originalText && transcription.originalText.length > DEFAULT_TRUNCATION_LIMIT && (
                        <button 
                          onClick={() => toggleExpanded(transcription.id, 'original')}
                          className="text-primary dark:text-dark-primary flex items-center text-xs font-normal normal-case"
                        >
                          {expandedItems[transcription.id]?.original ? (
                            <>Show Less <ChevronUpIcon className="w-3 h-3 ml-1" /></>
                          ) : (
                            <>Show More <ChevronDownIcon className="w-3 h-3 ml-1" /></>
                          )}
                        </button>
                      )}
                    </div>
                    <div className="flex">
                      <div className="flex-grow p-1.5 bg-gray-50 dark:bg-dark-surface/50 rounded text-xs whitespace-pre-wrap">
                        {truncateText(transcription.originalText, transcription.id, 'original')}
                      </div>
                      <button
                        className="text-muted hover:text-primary dark:text-dark-muted dark:hover:text-dark-primary p-1 ml-1"
                        onClick={() => onCopyOriginal(transcription.id)}
                        title="Copy original transcript"
                      >
                        <ClipboardDocumentIcon className="w-4 h-4" />
                      </button>
                    </div>
                  </div>

                  {/* Optimized Transcript or Optimize Button */}
                  {transcription.optimizedText ? (
                    <div className="space-y-1">
                      <div className="text-accent dark:text-dark-accent flex items-center justify-between text-xs font-semibold uppercase">
                        <div className="flex items-center">
                          <span>Optimized</span>
                          {transcription.isPreview && (
                            <span className="ml-2 text-xs font-normal text-gray-500 dark:text-gray-400">(Preview)</span>
                          )}
                        </div>
                        {transcription.optimizedText && transcription.optimizedText.length > DEFAULT_TRUNCATION_LIMIT && (
                          <button 
                            onClick={() => toggleExpanded(transcription.id, 'optimized')}
                            className="text-primary dark:text-dark-primary flex items-center text-xs font-normal normal-case"
                          >
                            {expandedItems[transcription.id]?.optimized ? (
                              <>Show Less <ChevronUpIcon className="w-3 h-3 ml-1" /></>
                            ) : (
                              <>Show More <ChevronDownIcon className="w-3 h-3 ml-1" /></>
                            )}
                          </button>
                        )}
                      </div>
                      <div className="flex">
                        <div className="flex-grow p-1.5 bg-gray-50 dark:bg-dark-surface/50 rounded text-xs whitespace-pre-wrap">
                          {truncateText(transcription.optimizedText, transcription.id, 'optimized')}
                        </div>
                        <div className="flex flex-col ml-1">
                          <button
                            className="text-muted hover:text-primary dark:text-dark-muted dark:hover:text-dark-primary p-1"
                            onClick={() => onCopyOptimized(transcription.id)}
                            title="Copy optimized transcript"
                          >
                            <ClipboardDocumentIcon className="w-4 h-4" />
                          </button>
                          {transcription.isPreview && (
                            <button
                              className="text-muted hover:text-primary dark:text-dark-muted dark:hover:text-dark-primary p-1"
                              onClick={() => togglePreviewOptimization(transcription.id)}
                              title="Cancel preview"
                            >
                              <XMarkIcon className="w-4 h-4" />
                            </button>
                          )}
                        </div>
                      </div>
                      {transcription.isPreview && (
                        <div className="flex justify-end mt-1">
                          <button
                            className="bg-primary/10 hover:bg-primary/20 dark:bg-dark-primary/20 dark:hover:bg-dark-primary/30 text-primary dark:text-dark-primary flex items-center px-2 py-1 text-xs font-medium rounded"
                            onClick={() => onOptimize(transcription.id)}
                            title="Apply this optimization"
                          >
                            <SparklesIcon className="w-3 h-3 mr-1" />
                            Apply Optimization
                          </button>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="flex justify-between">
                      {previewOptimizations[transcription.id] ? (
                        <div className="flex items-center space-x-2">
                          <div className="text-gray-600 dark:text-gray-400 text-xs">
                            Preview optimization in progress...
                          </div>
                          <button
                            className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 text-xs"
                            onClick={() => togglePreviewOptimization(transcription.id)}
                          >
                            Cancel
                          </button>
                        </div>
                      ) : (
                        <button
                          className="bg-primary/10 hover:bg-primary/20 dark:bg-dark-primary/20 dark:hover:bg-dark-primary/30 text-primary dark:text-dark-primary flex items-center px-2 py-1 text-xs font-medium rounded"
                          onClick={() => togglePreviewOptimization(transcription.id)}
                          title="Preview optimization before applying"
                        >
                          <EyeIcon className="w-3 h-3 mr-1" />
                          Preview
                        </button>
                      )}
                      <button
                        className="bg-primary/10 hover:bg-primary/20 dark:bg-dark-primary/20 dark:hover:bg-dark-primary/30 text-primary dark:text-dark-primary flex items-center px-2 py-1 text-xs font-medium rounded"
                        onClick={() => onOptimize(transcription.id)}
                        title="Optimize this transcript"
                      >
                        <SparklesIcon className="w-3 h-3 mr-1" />
                        Optimize
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ))}

            {/* Show More Button */}
            {hasMoreItems && (
              <div className="flex justify-center pt-2">
                <button
                  onClick={handleLoadMore}
                  className="flex items-center px-4 py-1.5 text-xs font-medium text-primary dark:text-dark-primary border border-primary/30 dark:border-dark-primary/30 hover:bg-primary/5 dark:hover:bg-dark-primary/10 rounded-md"
                >
                  <ArrowDownIcon className="w-3.5 h-3.5 mr-1.5" />
                  Show More ({filteredTranscriptions.length - displayedTranscriptions.length} remaining)
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default RecentTranscriptions;
import * as React from 'react';
import { useState, useEffect, useRef, forwardRef, useImperativeHandle } from 'react';

interface RealtimeTranscriptProps {
  isVisible: boolean;
  isRecording: boolean;
  className?: string;
}

interface TranscriptSegment {
  id: string;
  text: string;
  isFinal: boolean;
  timestamp: number;
}

export interface RealtimeTranscriptRef {
  handlePartialTranscript: (text: string) => void;
  handleFinalTranscript: (text: string) => void;
  handleConnectionStatus: (status: string, message?: string) => void;
  handleGracePeriodStatus: (isActive: boolean, remainingSeconds?: number) => void;
}

const RealtimeTranscript = forwardRef<RealtimeTranscriptRef, RealtimeTranscriptProps>(({
  isVisible,
  isRecording,
  className = ''
}, ref) => {
  const [segments, setSegments] = useState<TranscriptSegment[]>([]);
  const [partialText, setPartialText] = useState<string>('');
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [connectionStatus, setConnectionStatus] = useState<string>('');
  const [isGracePeriodActive, setIsGracePeriodActive] = useState<boolean>(false);
  const [gracePeriodRemaining, setGracePeriodRemaining] = useState<number>(0);
  const scrollRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new content is added
  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [segments, partialText]);

  // Clear transcript when recording stops
  useEffect(() => {
    if (!isRecording) {
      setSegments([]);
      setPartialText('');
      setIsConnected(false);
      setConnectionStatus('');
      setIsGracePeriodActive(false);
      setGracePeriodRemaining(0);
    }
  }, [isRecording]);

  // Handle real-time transcript updates
  const handlePartialTranscript = (text: string) => {
    setPartialText(text);
  };

  const handleFinalTranscript = (text: string) => {
    if (text.trim()) {
      const newSegment: TranscriptSegment = {
        id: Date.now().toString(),
        text: text.trim(),
        isFinal: true,
        timestamp: Date.now()
      };
      
      setSegments(prev => [...prev, newSegment]);
      setPartialText(''); // Clear partial text after final
    }
  };

  const handleConnectionStatus = (status: string, message?: string) => {
    setConnectionStatus(message || status);
    setIsConnected(status === 'connected' || status === 'transcribing');
  };

  const handleGracePeriodStatus = (isActive: boolean, remainingSeconds?: number) => {
    setIsGracePeriodActive(isActive);
    setGracePeriodRemaining(remainingSeconds || 0);

    if (isActive) {
      setConnectionStatus(`Waiting for remaining content... (${remainingSeconds}s)`);
    }
  };

  // Expose methods for parent component to call
  useImperativeHandle(ref, () => ({
    handlePartialTranscript,
    handleFinalTranscript,
    handleConnectionStatus,
    handleGracePeriodStatus
  }));

  if (!isVisible) {
    return null;
  }

  return (
    <div className={`realtime-transcript ${className}`}>
      {/* Header */}
      <div className="realtime-transcript-header">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className={`status-indicator ${isConnected ? 'connected' : 'disconnected'}`}>
              <div className="status-dot"></div>
            </div>
            <h3 className="text-sm font-medium text-text dark:text-dark-text">
              Live Transcription
            </h3>
          </div>
          {connectionStatus && (
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {connectionStatus}
            </span>
          )}
        </div>
      </div>

      {/* Transcript Content */}
      <div 
        ref={scrollRef}
        className="realtime-transcript-content"
      >
        {!isRecording && (
          <div className="transcript-placeholder">
            <div className="text-center text-gray-500 dark:text-gray-400 text-sm">
              <div className="mb-2">🎤</div>
              <div>Start recording to see live transcription</div>
            </div>
          </div>
        )}

        {isRecording && segments.length === 0 && !partialText && (
          <div className="transcript-placeholder">
            <div className="text-center text-gray-500 dark:text-gray-400 text-sm">
              <div className="mb-2">👂</div>
              <div>Listening... Start speaking!</div>
            </div>
          </div>
        )}

        {/* Final transcript segments */}
        {segments.map((segment) => (
          <div key={segment.id} className="transcript-segment final">
            <span className="segment-text">{segment.text}</span>
          </div>
        ))}

        {/* Partial transcript */}
        {partialText && (
          <div className="transcript-segment partial">
            <span className="segment-text">{partialText}</span>
            <span className="typing-indicator">|</span>
          </div>
        )}

        {/* Grace period indicator */}
        {isGracePeriodActive && (
          <div className="transcript-segment grace-period">
            <div className="text-center text-blue-600 dark:text-blue-400 text-sm">
              <div className="mb-1">⏳</div>
              <div>Waiting for remaining content... ({gracePeriodRemaining}s)</div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
});

RealtimeTranscript.displayName = 'RealtimeTranscript';

export default RealtimeTranscript;

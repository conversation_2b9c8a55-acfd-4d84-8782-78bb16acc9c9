import React from 'react';
import { Configuration } from '../types/configuration';
import Button from './Button';

interface ConfigurationCardProps {
    configuration: Configuration;
    isActive: boolean;
    onActivate: (id: string) => void;
    onEdit: (configuration: Configuration) => void;
    onDelete: (id: string) => void;
    onDuplicate: (configuration: Configuration) => void;
}

const ConfigurationCard: React.FC<ConfigurationCardProps> = ({
    configuration,
    isActive,
    onActivate,
    onEdit,
    onDelete,
    onDuplicate
}) => {
    const { settings } = configuration;

    const formatSettingsSummary = () => {
        const parts = [
            settings.service === 'assemblyai' ? 'AssemblyAI' : 'Whisper',
            settings.model,
            settings.language.toUpperCase(),
            settings.realtime ? 'Real-time' : 'Standard',
            settings.optimizeEnabled ? 'AI-Optimized' : 'Basic'
        ];
        return parts.join(' • ');
    };

    return (
        <div className={`
            border rounded-lg p-4 transition-all duration-200
            ${isActive 
                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
            }
        `}>
            <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                            {configuration.title}
                        </h3>
                        {isActive && (
                            <span className="px-2 py-1 text-xs bg-blue-500 text-white rounded-full">
                                Active
                            </span>
                        )}
                        {configuration.isDefault && (
                            <span className="px-2 py-1 text-xs bg-gray-500 text-white rounded-full">
                                Default
                            </span>
                        )}
                    </div>
                    {configuration.description && (
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                            {configuration.description}
                        </p>
                    )}
                    <p className="text-xs text-gray-500 dark:text-gray-500">
                        {formatSettingsSummary()}
                    </p>
                </div>
            </div>

            <div className="flex items-center gap-2">
                {!isActive && (
                    <Button
                        onClick={() => onActivate(configuration.id)}
                        variant="primary"
                        size="sm"
                    >
                        Activate
                    </Button>
                )}
                <Button
                    onClick={() => onEdit(configuration)}
                    variant="secondary"
                    size="sm"
                >
                    Edit
                </Button>
                <Button
                    onClick={() => onDuplicate(configuration)}
                    variant="secondary"
                    size="sm"
                >
                    Duplicate
                </Button>
                {!configuration.isDefault && (
                    <Button
                        onClick={() => onDelete(configuration.id)}
                        variant="danger"
                        size="sm"
                    >
                        Delete
                    </Button>
                )}
            </div>
        </div>
    );
};

export default ConfigurationCard;

import React, { useState, useEffect } from 'react';
import { vscode } from '../utilities/vscode';
import Button from './Button';
import {
  SparklesIcon,
  LanguageIcon,
  DocumentTextIcon,
  PencilIcon,
  ArrowsPointingOutIcon,
  HandRaisedIcon,
  ListBulletIcon,
  HeartIcon,
  ChatBubbleLeftRightIcon,
  EnvelopeIcon,
  ClipboardDocumentListIcon,
  CheckCircleIcon,
  CalendarDaysIcon,
  MegaphoneIcon,
  CommandLineIcon
} from '@heroicons/react/24/outline';

interface PromptMode {
  id: string;
  name: string;
  prompt: string;
  isCustom?: boolean;
  icon?: React.ComponentType<{ className?: string }>;
}

interface PromptModeSelectorProps {
  value: string;
  onChange: (prompt: string) => void;
  onModeChange?: (modeId: string) => void;
}

// Add custom mode option
const CUSTOM_MODE: PromptMode = {
  id: 'custom',
  name: 'Custom',
  prompt: '',
  isCustom: true
};

const DEFAULT_MODES: PromptMode[] = [
  {
    id: 'clean-up',
    name: 'Clean Up',
    prompt: 'Clean up the text by removing filler words like "um", "you know", "like", fix any grammar mistakes, and make the transcript coherent and smooth, while preserving the original meaning. Return ONLY the cleaned text without any additional commentary or conversational responses.',
    icon: SparklesIcon
  },
  {
    id: 'translate',
    name: 'Translate',
    prompt: 'Translate the following transcript into [TARGET_LANGUAGE], keeping it natural and respectful of tone and cultural context. Do not summarize, just translate as clearly as possible.',
    icon: LanguageIcon
  },
  {
    id: 'summarize',
    name: 'Summarize',
    prompt: 'Summarize the following transcript into a short, coherent paragraph that captures the key ideas, tone, and intention of the speaker. Return ONLY the summarized text without any introductory phrases or additional commentary.',
    icon: DocumentTextIcon
  },
  {
    id: 'polish',
    name: 'Polish',
    prompt: 'Rewrite the following transcript to make it sound formal and professional, correcting grammar and sentence structure, while preserving the speaker\'s message.',
    icon: PencilIcon
  },
  {
    id: 'expand',
    name: 'Expand',
    prompt: 'Expand and elaborate the transcript into a well-written, detailed explanation or article, adding transitions and clarifications where needed, while staying faithful to the speaker\'s intent.',
    icon: ArrowsPointingOutIcon
  },
  {
    id: 'light-touch',
    name: 'Light Touch',
    prompt: 'Apply minimal editing to fix major grammatical issues and incoherence, but keep the casual tone and natural flow of the original speech.',
    icon: HandRaisedIcon
  },
  {
    id: 'bullet-points',
    name: 'Bullet Points',
    prompt: 'Convert the following transcript into a clear, concise list of bullet points summarizing the main ideas discussed.',
    icon: ListBulletIcon
  },
  {
    id: 'islamic-tone',
    name: 'Islamic Tone',
    prompt: `Transform this transcript into an uplifting Islamic motivational message. Use Quranic verses and Hadith to inspire positive action while maintaining the original meaning. Include motivational Islamic phrases like "With Allah's help we can achieve", "Allah has promised us", "Let's strive to be better Muslims", and "This is our chance to earn Allah's pleasure". Keep the tone encouraging, hopeful and spiritually uplifting while preserving the original content's essence. The goal is to motivate the listener/reader to take positive action in their life while strengthening their faith.`,
    icon: HeartIcon
  },
  {
    id: 'whatsapp-style',
    name: 'WhatsApp Style',
    prompt: 'Rewrite the following text as a short, casual WhatsApp message. Keep it friendly and easy to understand.',
    icon: ChatBubbleLeftRightIcon
  },
  {
    id: 'email-format',
    name: 'Email Format',
    prompt: 'Rewrite the following transcript into a professional email. Include a greeting, body, and closing, and keep the tone polite and formal.',
    icon: EnvelopeIcon
  },
  {
    id: 'reminder-note',
    name: 'Reminder Note',
    prompt: 'Convert the following transcript into a short personal reminder, using brief and clear language.',
    icon: ClipboardDocumentListIcon
  },
  {
    id: 'to-do-list',
    name: 'To-Do List',
    prompt: 'Extract a to-do list from the following transcript. Format it as simple, actionable tasks in bullet points.',
    icon: CheckCircleIcon
  },
  {
    id: 'meeting-minutes',
    name: 'Meeting Minutes',
    prompt: 'Format the following text into structured meeting minutes, listing topics discussed, decisions made, and action items clearly.',
    icon: CalendarDaysIcon
  },
  {
    id: 'social-caption',
    name: 'Social Caption',
    prompt: 'Rewrite the text into a short, engaging social media caption suitable for Instagram, Twitter, or Facebook, keeping the tone casual and inviting.',
    icon: MegaphoneIcon
  },
  {
    id: 'prompt',
    name: 'Prompt',
    prompt: 'Enhance this into a prompt I can send to an LLM.',
    icon: CommandLineIcon
  }
];

const PromptModeSelector: React.FC<PromptModeSelectorProps> = ({
  value, 
  onChange,
  onModeChange
}) => {
  const [modes, setModes] = useState<PromptMode[]>([...DEFAULT_MODES, CUSTOM_MODE]);
  const [activeMode, setActiveMode] = useState<string>('clean-up');
  const [showCustomPrompt, setShowCustomPrompt] = useState(false);
  const [selectedPrompt, setSelectedPrompt] = useState<string>('');
  const [customModeName, setCustomModeName] = useState<string>('');
  const [targetLanguage, setTargetLanguage] = useState<string>('English');
  const [debouncedLanguage, setDebouncedLanguage] = useState<string>('English');

  // Debounce target language updates (500ms delay)
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedLanguage(targetLanguage);
    }, 500);

    return () => {
      clearTimeout(handler);
    };
  }, [targetLanguage]);

  useEffect(() => {
    console.log('PromptModeSelector: Requesting prompt modes with default activeMode:', activeMode);
    // Load saved custom modes from extension
    vscode.postMessage({
      command: 'getPromptModes',
      persist: true // Ensure modes are persisted
    });
    
    const handleMessage = (event: MessageEvent) => {
      if (event.data.command === 'promptModes') {
        const customModes = event.data.modes || [];
        console.log(`PromptModeSelector: Received ${customModes.length} custom modes from extension`);
        setModes([...DEFAULT_MODES, ...customModes, CUSTOM_MODE]);
        
        // Set active mode if provided
        if (event.data.activeMode) {
          console.log('PromptModeSelector: Setting active mode from configuration:', event.data.activeMode);
          setActiveMode(event.data.activeMode);
        } else {
          console.log('PromptModeSelector: No active mode provided, keeping default:', activeMode);
        }
      } else if (event.data.command === 'initialOptions') {
        // Initialize active prompt mode from saved configuration
        if (event.data.activePromptMode) {
          console.log('PromptModeSelector: Setting initial active mode from configuration:', event.data.activePromptMode);
          setActiveMode(event.data.activePromptMode);
        }
        
        // Initialize custom modes if provided
        if (Array.isArray(event.data.customPromptModes) && event.data.customPromptModes.length > 0) {
          console.log(`PromptModeSelector: Setting ${event.data.customPromptModes.length} initial custom modes from configuration`);
          setModes([...DEFAULT_MODES, ...event.data.customPromptModes, CUSTOM_MODE]);
        }
      } else if (event.data.command === 'activePromptModeUpdated') {
        // Handle confirmation of active mode update
        if (event.data.modeId) {
          console.log('PromptModeSelector: Active mode confirmed by extension:', event.data.modeId);
          setActiveMode(event.data.modeId);
        }
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [activeMode]);

  // Update selectedPrompt whenever mode changes or debounced language updates
  useEffect(() => {
    const selectedMode = modes.find(mode => mode.id === activeMode);
    if (selectedMode && selectedMode.id !== 'custom') {
      let prompt = selectedMode.prompt;
      if (selectedMode.id === 'translate') {
        prompt = prompt.replace('[TARGET_LANGUAGE]', debouncedLanguage);
        onChange(prompt); // Update backend with debounced value
      }
      setSelectedPrompt(prompt);
    }
  }, [activeMode, modes, debouncedLanguage, onChange]);

  const handleModeSelect = (mode: PromptMode) => {
    setActiveMode(mode.id);
    
    // For custom mode, don't change the prompt content
    if (mode.id !== 'custom') {
      onChange(mode.prompt);
      setSelectedPrompt(mode.prompt);
    }
    
    if (onModeChange) onModeChange(mode.id);
    setShowCustomPrompt(mode.id === 'custom');
  };

  return (
    <div className="space-y-3">
      <div className="flex flex-wrap gap-2">
        {modes.map(mode => (
          <div key={mode.id} className="flex items-center">
            <div
              onClick={() => handleModeSelect(mode)}
              className={`px-3 py-1.5 rounded-full border transition-colors cursor-pointer flex items-center gap-1.5
                ${activeMode === mode.id
                  ? 'bg-primary dark:bg-primary border-primary dark:border-primary text-black font-bold'
                  : 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
                }`}>
              {mode.icon && (
                <mode.icon className="w-4 h-4" />
              )}
              <span className="text-sm font-medium select-none">
                {mode.name}
              </span>
            </div>
            {/* Delete button for custom modes */}
            {mode.isCustom && mode.id !== 'custom' && (
              <button
                onClick={() => {
                  vscode.postMessage({
                    command: 'deletePromptMode',
                    modeId: mode.id
                  });
                  // Remove from local state immediately for better UX
                  setModes(modes.filter(m => m.id !== mode.id));
                  // If this was the active mode, switch to default
                  if (activeMode === mode.id) {
                    setActiveMode('clean-up');
                  }
                }}
                className="hover:text-red-700 p-1 ml-1 text-red-500 rounded-full"
                title="Delete custom mode"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </button>
            )}
          </div>
        ))}
      </div>

      {/* Language input for translate mode */}
      {activeMode === 'translate' && (
        <div className="mt-3">
          <label className="dark:text-gray-300 block mb-1 text-sm font-medium text-gray-700">
            Target Language
          </label>
          <input
            type="text"
            value={targetLanguage}
            onChange={(e) => setTargetLanguage(e.target.value)}
            placeholder="Enter target language (e.g. French, Arabic)"
            className="dark:border-gray-600 dark:bg-gray-700 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 w-full p-2 bg-white border border-gray-300 rounded-md shadow-sm"
          />
        </div>
      )}

      {/* Display selected prompt */}
      {activeMode !== 'custom' && !showCustomPrompt && selectedPrompt && (
        <div className="dark:border-gray-600 bg-gray-50 dark:bg-gray-800 p-3 mt-3 border border-gray-300 rounded-md">
          <p className="dark:text-gray-300 text-sm text-gray-600">
            {selectedPrompt}
          </p>
        </div>
      )}

      {showCustomPrompt && (
        <div className="mt-3">
          {/* Custom mode name input */}
          <div className="mb-3">
            <label className="dark:text-gray-300 block mb-1 text-sm font-medium text-gray-700">
              Mode Name
            </label>
            <input
              type="text"
              value={customModeName}
              onChange={(e) => setCustomModeName(e.target.value)}
              placeholder="Enter a name for this mode"
              className="dark:border-gray-600 dark:bg-gray-700 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 w-full p-2 bg-white border border-gray-300 rounded-md shadow-sm"
            />
          </div>

          {/* Custom prompt textarea */}
          <label className="dark:text-gray-300 block mb-1 text-sm font-medium text-gray-700">
            Prompt Text
          </label>
          <textarea
            value={value}
            onChange={(e) => {
              // Controlled input with immediate update
              const newValue = e.target.value;
              setSelectedPrompt(newValue); // Local state for immediate UI update
              onChange(newValue); // Parent update
            }}
            placeholder="Enter custom prompt..."
            rows={4}
            className="dark:border-gray-600 dark:bg-gray-700 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 w-full p-2 bg-white border border-gray-300 rounded-md shadow-sm resize-none"
          />
          <div className="flex items-center justify-between mt-2">
            <div className="dark:text-gray-400 text-sm text-gray-500">
              Create a custom optimization mode
            </div>
            <Button
              size="sm"
              onClick={() => {
                if (value && value.trim()) {
                  const modeName = customModeName.trim()
                    ? customModeName.trim()
                    : `Custom ${new Date().toLocaleString()}`;
                    
                  vscode.postMessage({
                    command: 'savePromptMode',
                    mode: {
                      id: `custom-${Date.now()}`,
                      name: modeName,
                      prompt: selectedPrompt, // Use the local state value
                      isCustom: true
                    }
                  });
                  
                  // Reset custom mode name and clear prompt text
                  setCustomModeName('');
                  onChange('');
                }
              }}
              disabled={!value || !value.trim()}
            >
              Save Mode
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default PromptModeSelector;

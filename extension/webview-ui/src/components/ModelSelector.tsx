import React, { useMemo, memo, useCallback } from 'react';
import { InformationCircleIcon } from '@heroicons/react/24/outline';

interface ModelSelectorProps {
  service: string;
  model: string;
  onChange: (model: string) => void;
}

interface ModelOption {
  id: string;
  name: string;
  description: string;
}

const ModelSelector: React.FC<ModelSelectorProps> = memo(({ service, model, onChange }) => {
  const modelOptions = useMemo(() => {
    if (service === 'assemblyai') {
      return [
        { id: 'best', name: 'best', description: 'High accuracy, optimized for various accents and domains' },
        { id: 'nano', name: 'nano', description: 'Faster transcription with small quality tradeoff' }
      ];
    } else if (service === 'lemonfox') {
      // For Whisper service (formerly OpenAI), we'll only use whisper-1 model
      // and hide the model selection dropdown
      return [
        { id: 'whisper-1', name: 'whisper-1', description: 'High-quality speech recognition model' }
      ];
    }
    return [];
  }, [service]);

  const selectedModel = useMemo(() =>
    modelOptions.find(o => o.id === model),
    [modelOptions, model]
  );

  const handleModelChange = useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    const newModel = e.target.value;
    if (newModel !== model) {
      onChange(newModel);
    }
  }, [model, onChange]);

  // For Whisper service, we don't show the model selector as we only use whisper-1
  if (service === 'lemonfox') {
    // Auto-select whisper-1 model if not already selected
    if (model !== 'whisper-1') {
      onChange('whisper-1');
    }

    // Return an empty div to maintain layout spacing
    return <div className="space-y-3"></div>;
  }

  // For other services (AssemblyAI), show the model selector
  return (
    <div className="space-y-3">
      <div className="relative">
        <select
          className="border-border dark:border-dark-border dark:bg-dark-surface text-text dark:text-dark-text focus:outline-none focus:ring-2 focus:ring-primary/50 dark:focus:ring-dark-primary/50 w-full px-3 py-2 bg-white border rounded-md appearance-none cursor-pointer"
          value={model}
          onChange={handleModelChange}
          aria-label="Select model"
        >
          {modelOptions.map((option: ModelOption) => (
            <option key={option.id} value={option.id}>
              {option.name}
            </option>
          ))}
        </select>
        <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
          <svg className="text-muted dark:text-dark-muted w-4 h-4" fill="none" viewBox="0 0 20 20" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7l3-3 3 3m0 6l-3 3-3-3" />
          </svg>
        </div>
      </div>

      {selectedModel?.description && (
        <div className="text-muted dark:text-dark-muted bg-gray-50 dark:bg-dark-surface/40 flex items-start p-3 text-sm rounded-md">
          <InformationCircleIcon className="text-primary dark:text-dark-primary flex-shrink-0 w-5 h-5 mr-2" />
          <p>{selectedModel.description}</p>
        </div>
      )}
    </div>
  );
});

// Display name for debugging
ModelSelector.displayName = 'ModelSelector';

export default ModelSelector;    // Return an empty div to maintain layout spacing

import * as React from 'react';
import { useState, useCallback, useEffect } from 'react';
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';
import { vscode } from '../utilities/vscode';

interface ApiKeyInputProps {
    initialApiKey?: string;
    onApiKeySaved?: (apiKey: string) => void;
}

const ApiKeyInput: React.FC<ApiKeyInputProps> = ({ initialApiKey, onApiKeySaved }) => {
    console.log('ApiKeyInput initializing with key:', initialApiKey ? 'present' : 'not set');
    
    const [apiKey, setApiKey] = useState<string>('');
    const [showApiKey, setShowApiKey] = useState<boolean>(false);
    const [isSaving, setIsSaving] = useState<boolean>(false);
    const [isExpanded, setIsExpanded] = useState<boolean>(true);

    // Effect to handle initialApiKey changes
    useEffect(() => {
        console.log('ApiKeyInput received new initialApiKey:', initialApiKey ? 'present' : 'not set');
        if (initialApiKey) {
            setApiKey(initialApiKey);
            setIsExpanded(false);  // Collapse the input when we have a key
        }
    }, [initialApiKey]);

    const handleSave = useCallback(async () => {
        try {
            setIsSaving(true);
            await vscode.postMessage({
                command: 'updateOptions',
                options: { apiKey }
            });
            
            if (onApiKeySaved) {
                onApiKeySaved(apiKey);
            }
        } finally {
            setIsSaving(false);
            setIsExpanded(false);
        }
    }, [apiKey, onApiKeySaved]);

    return (
        <div className="bg-opacity-10 mb-4 overflow-hidden bg-white rounded-lg">
            {/* Collapsible Header */}
            <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="hover:bg-opacity-20 flex items-center justify-between w-full px-4 py-3 transition-colors"
            >
                <span className="text-sm font-medium">API Key Configuration</span>
                {isExpanded ? (
                    <ChevronUpIcon className="w-5 h-5" />
                ) : (
                    <ChevronDownIcon className="w-5 h-5" />
                )}
            </button>

            {/* Expandable Content */}
            {isExpanded && (
                <div className="p-4 space-y-4">
                    {/* Input Container */}
                    <div className="flex flex-col space-y-2">
                        <div className="flex-1 min-w-0">
                            <input
                                type={showApiKey ? "text" : "password"}
                                value={apiKey}
                                onChange={(e) => setApiKey(e.target.value)}
                                placeholder="Enter your VoiceHype API key"
                                className="bg-opacity-10 focus:border-blue-500 focus:outline-none w-full px-3 py-2 bg-white border border-gray-600 rounded"
                            />
                        </div>
                        
                        {/* Responsive Button Container */}
                        <div className="sm:flex-row flex flex-col gap-2">
                            <button
                                onClick={() => setShowApiKey(!showApiKey)}
                                className="bg-opacity-10 hover:bg-opacity-20 sm:flex-none flex-1 px-3 py-2 bg-white border border-gray-600 rounded"
                            >
                                {showApiKey ? "Hide" : "Show"}
                            </button>
                            <button
                                onClick={handleSave}
                                disabled={isSaving}
                                className="hover:bg-blue-700 disabled:opacity-50 sm:flex-none flex-1 px-4 py-2 text-white bg-blue-600 rounded"
                            >
                                {isSaving ? "Saving..." : "Save"}
                            </button>
                        </div>
                    </div>
                    
                    <p className="text-xs text-gray-500">
                        Your API key is stored securely. Get one at voicehype.ai
                    </p>
                </div>
            )}
        </div>
    );
};

export default ApiKeyInput;
import React from 'react';

interface SwitchProps {
  label: string;
  checked: boolean;
  onChange: (checked: boolean) => void;
  className?: string;
}

const Switch: React.FC<SwitchProps> = ({ label, checked, onChange, className = '' }) => {
  return (
    <div className={`flex items-center justify-between p-3 mb-3 bg-white dark:bg-dark-surface border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm hover:border-primary/70 dark:hover:border-primary/70 transition-colors duration-200 ${className}`}>
      <label className="flex items-center justify-between w-full cursor-pointer">
        <span className="dark:text-white text-sm font-medium text-gray-900">{label}</span>
        <div className="relative ml-auto">
          <input
            type="checkbox"
            className="sr-only"
            checked={checked}
            onChange={(e) => onChange(e.target.checked)}
          />
          <div className={`block w-10 h-6 rounded-full transition-colors duration-200 ease-in-out ${
            checked ? 'bg-primary dark:bg-primary' : 'bg-gray-300 dark:bg-gray-700'
          }`} />
          <div className={`absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition-transform duration-200 ease-in-out ${
            checked ? 'transform translate-x-4' : 'transform translate-x-0'
          }`} />
        </div>
      </label>
    </div>
  );
};

export default React.memo(Switch);
import * as React from 'react';
import { render, fireEvent } from '@testing-library/react';
import TranscriptionSettings from '../../../webview-ui/src/components/TranscriptionSettings';

describe('TranscriptionSettings', () => {
    it('toggles voice commands when switch is clicked', () => {
        const mockOnVoiceCommandsChange = jest.fn();
        const { getByRole } = render(
            <TranscriptionSettings 
                voiceCommandsEnabled={true}
                onVoiceCommandsChange={mockOnVoiceCommandsChange}
            />
        );

        const toggle = getByRole('switch');
        fireEvent.click(toggle);

        expect(mockOnVoiceCommandsChange).toHaveBeenCalledWith(false);
    });

    it('displays correct initial state', () => {
        const { getByRole } = render(
            <TranscriptionSettings 
                voiceCommandsEnabled={true}
                onVoiceCommandsChange={() => {}}
            />
        );

        const toggle = getByRole('switch');
        expect(toggle).toHaveAttribute('aria-checked', 'true');
    });
});

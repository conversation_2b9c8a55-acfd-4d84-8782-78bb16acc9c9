import React, { useEffect } from 'react';
import { vscode } from '../utilities/vscode';

interface AudioSettingsProps {
  sampleRate: number;
  deviceId: string | null;
  availableDevices: Array<{ id: string; name: string }>;
  onSampleRateChange: (rate: number) => void;
  onDeviceChange: (deviceId: string | null) => void;
}

const AudioSettings: React.FC<AudioSettingsProps> = ({
  sampleRate,
  deviceId,
  availableDevices,
  onSampleRateChange,
  onDeviceChange,
}) => {
  const sampleRates = [8000, 16000, 22050, 44100, 48000];

  // Request audio devices when component mounts
  useEffect(() => {
    vscode.postMessage({ command: 'getAudioDevices' });
  }, []);

  // Format device name for display
  const getDeviceDisplayName = (device: { id: string; name: string }) => {
    return device.name.length > 50 ? `${device.name.substring(0, 47)}...` : device.name;
  };

  // Filter devices to avoid duplicates
  const filteredDevices = availableDevices.filter(device => {
    // Skip the "default" device and empty string devices since we add system default manually
    return device.id !== 'default' && device.id !== '' && device.name !== 'System Default';
  });

  return (
    <div className="space-y-4">
      <div>
        <label className="text-text dark:text-dark-text block mb-2 text-sm font-medium">
          Audio Device
          <button 
            className="text-primary dark:text-dark-primary hover:underline ml-2 text-xs"
            onClick={() => vscode.postMessage({ command: 'getAudioDevices' })}
          >
            Refresh
          </button>
        </label>
        <div className="relative">
          <select
            value={deviceId || ''}
            onChange={(e) => onDeviceChange(e.target.value === '' ? null : e.target.value)}
            className="w-full px-4 py-2.5 bg-gray-100 dark:bg-dark-surface hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary/50 dark:focus:ring-dark-primary/50 appearance-none"
          >
            <option value="">System Default</option>
            {filteredDevices.map((device) => (
              <option key={device.id} value={device.id}>
                {getDeviceDisplayName(device)}
              </option>
            ))}
          </select>
          <div className="dark:text-gray-200 absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 pointer-events-none">
            <svg className="w-4 h-4 fill-current" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
              <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
            </svg>
          </div>
        </div>
      </div>

      <div>
        <label className="text-text dark:text-dark-text block mb-2 text-sm font-medium">Sample Rate</label>
        <div className="relative">
          <select
            value={sampleRate}
            onChange={(e) => onSampleRateChange(Number(e.target.value))}
            className="w-full px-4 py-2.5 bg-gray-100 dark:bg-dark-surface hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary/50 dark:focus:ring-dark-primary/50 appearance-none"
          >
            {sampleRates.map((rate) => (
              <option key={rate} value={rate}>
                {rate.toLocaleString()} Hz
              </option>
            ))}
          </select>
          <div className="dark:text-gray-200 absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 pointer-events-none">
            <svg className="w-4 h-4 fill-current" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
              <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
            </svg>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AudioSettings; 
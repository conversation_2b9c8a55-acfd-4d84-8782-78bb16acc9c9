import React, { useState, useEffect, useRef } from 'react';
import { InformationCircleIcon } from '@heroicons/react/24/outline';

interface CustomPromptProps {
  value: string;
  onChange: (prompt: string) => void;
}

const CustomPrompt: React.FC<CustomPromptProps> = ({ value, onChange }) => {
  // Local state to manage the input value
  const [localValue, setLocalValue] = useState<string>(value);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [isFocused, setIsFocused] = useState<boolean>(false);

  // Update local value when prop changes, but only if not focused
  useEffect(() => {
    if (!isFocused) {
      setLocalValue(value);
    }
  }, [value, isFocused]);

  // Handle local changes
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setLocalValue(newValue);
    onChange(newValue);
  };

  return (
    <div className="space-y-3">
      <textarea
        ref={textareaRef}
        value={localValue}
        onChange={handleChange}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        placeholder="Enter a custom prompt for transcript optimization..."
        rows={4}
        className="input focus:ring-2 focus:ring-primary/50 dark:focus:ring-dark-primary/50 w-full resize-none"
      />
      <div className="text-muted dark:text-dark-muted flex items-start text-sm">
        <InformationCircleIcon className="flex-shrink-0 w-5 h-5 mr-2" />
        <p>
          This prompt will be used to generate optimized transcriptions.
          Example: "Remove verbal fillers, fix grammar, and format as clear paragraphs."
          <br />
          You can use variables like <code>{'{{transcript}}'}</code> in your prompt, which will be replaced with the actual transcript.
        </p>
      </div>
    </div>
  );
};

export default CustomPrompt;
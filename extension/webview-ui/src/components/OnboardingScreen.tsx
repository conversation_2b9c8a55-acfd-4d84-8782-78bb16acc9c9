import * as React from 'react';
import { useState, useEffect } from 'react';
import { vscode } from '../utilities/vscode';
import Logo from './Logo';
import ApiKeyInput from './ApiKeyInput';

interface OnboardingScreenProps {
  apiKey?: string;
}

const OnboardingScreen: React.FC<OnboardingScreenProps> = ({ apiKey }) => {
  const [isSigningIn, setIsSigningIn] = useState<boolean>(false);
  const [authError, setAuthError] = useState<string | null>(null);
  const [isTransitioning, setIsTransitioning] = useState<boolean>(false);

  // Listen for auth messages and handle view transitions
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      const message = event.data;
      console.log('OnboardingScreen received message:', message);
      
      switch (message.command) {
        case 'authStatusUpdated':
          setIsSigningIn(false);
          if (message.error) {
            setAuthError(message.error);
            setTimeout(() => setAuthError(null), 5000);
            // Explicitly reset all auth states on error
            setIsTransitioning(false);
            vscode.postMessage({
              command: 'authCancelled'
            });
          }
          // Reset transitioning state if auth fails
          if (!message.authenticated) {
            setIsTransitioning(false);
          }
          break;

        case 'apiKeySaved':
          if (message.success && !isTransitioning) {
            console.log('API key saved successfully, initiating transition...');
            setIsTransitioning(true);
            
            // Clear states
            setAuthError(null);
            setIsSigningIn(false);

            // Simplified flow - just notify the extension that manual API key was verified
            console.log('Onboarding: Sending manualApiKeyVerified message');
            vscode.postMessage({
              command: 'manualApiKeyVerified',
              apiKey: message.apiKey
            });
          } else if (message.error) {
            setIsTransitioning(false);
            setAuthError(message.error);
            setTimeout(() => setAuthError(null), 5000);
          }
          break;

        case 'manualApiKeyVerified':
          // This should trigger the App component to show the main view
          console.log('OnboardingScreen: Manual API key verified, transition should complete');
          setIsTransitioning(false);
          break;

        case 'updateView':
          // Handle view update confirmation
          if (message.view === 'main') {
            console.log('OnboardingScreen: View updated to main');
            setIsTransitioning(false);
          }
          break;

        case 'viewUpdated':
          // Reset transition state if we get confirmation of view update
          setIsTransitioning(false);
          break;
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [isTransitioning]);

  const handleSignIn = () => {
    if (isTransitioning) return;
    setIsSigningIn(true);
    setAuthError(null);
    vscode.postMessage({
      command: 'authenticate'
    });
  };

  const [showApiKeyInput, setShowApiKeyInput] = useState(false);

  const handleManualApiKey = () => {
    if (isTransitioning) return;
    setShowApiKeyInput(!showApiKeyInput);
  };

  const handleApiKeySaved = (apiKey: string) => {
    if (isTransitioning) return;
    
    console.log('OnboardingScreen: API key saved, starting transition');
    setIsTransitioning(true);
    setAuthError(null);
    setIsSigningIn(false);
    
    // Simply send the manualApiKeyVerified message
    // The App component should handle the rest
    vscode.postMessage({
      command: 'manualApiKeyVerified',
      apiKey: apiKey
    });
  };

  return (
    <div className="flex flex-col items-center justify-center h-full p-6 text-center">
      <div className="mb-8">
        <Logo className="mb-4" />
        <h1 className="mt-4 text-2xl font-bold">Welcome to VoiceHype</h1>
        <p className="mt-2 text-gray-400">
          Voice-to-prompt productivity tool for developers
        </p>
      </div>
      
      <div className="w-full max-w-md space-y-4">
        <button
          onClick={handleSignIn}
          disabled={isSigningIn || isTransitioning}
          className="hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center w-full px-4 py-3 font-medium text-white bg-blue-600 rounded-lg"
        >
          {isSigningIn || isTransitioning ? (
            <>
              <svg className="animate-spin w-5 h-5 mr-3 -ml-1 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {isTransitioning ? 'Loading...' : 'Signing in...'}
            </>
          ) : (
            <>
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2V5zm11 1H6v8l4-2 4 2V6z" clipRule="evenodd" />
              </svg>
              Sign in with Browser
            </>
          )}
        </button>
        
        <div className="relative my-4">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-600"></div>
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-[#1e1e1e] text-gray-400">or</span>
          </div>
        </div>
        
        <button
          onClick={handleManualApiKey}
          disabled={isSigningIn || isTransitioning}
          className="hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed w-full px-4 py-3 font-medium text-white bg-transparent border border-gray-600 rounded-lg"
        >
          {showApiKeyInput ? 'Hide API Key Input' : 'Enter API Key Manually'}
        </button>

        {showApiKeyInput && (
          <div className="w-full max-w-md mt-4">
            <ApiKeyInput
              initialApiKey={apiKey}
              onApiKeySaved={handleApiKeySaved}
            />
          </div>
        )}
      </div>
      
      {authError && (
        <div className="bg-red-900/50 max-w-md p-3 mt-4 border border-red-800 rounded-lg">
          <p className="text-sm text-white">{authError}</p>
        </div>
      )}
      
      <p className="mt-8 text-sm text-gray-400">
        Don't have an account? <button className="hover:underline p-0 text-blue-400 bg-transparent border-0 cursor-pointer" onClick={() => vscode.postMessage({ command: 'openExternalLink', url: 'https://voicehype.ai/signup' })}>Sign up at voicehype.ai</button>
      </p>
    </div>
  );
};

export default OnboardingScreen;
import React from 'react';
import { WHATS_NEW_141 } from '../types/configuration';
import Button from './Button';

interface WhatsNewModalProps {
    isOpen: boolean;
    onClose: () => void;
    onRemindLater: () => void;
}

const WhatsNewModal: React.FC<WhatsNewModalProps> = ({
    isOpen,
    onClose,
    onRemindLater
}) => {
    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white dark:bg-gray-800 rounded-lg w-full max-w-lg mx-4 overflow-hidden">
                {/* Header with gradient */}
                <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                            <span className="text-2xl">🎉</span>
                            <div>
                                <h2 className="text-xl font-bold">
                                    {WHATS_NEW_141.title}
                                </h2>
                                <p className="text-blue-100 text-sm">
                                    Version {WHATS_NEW_141.version}
                                </p>
                            </div>
                        </div>
                        <button
                            onClick={onClose}
                            className="text-white hover:text-gray-200 transition-colors"
                        >
                            ✕
                        </button>
                    </div>
                </div>

                {/* Content */}
                <div className="p-6">
                    <div className="mb-6">
                        <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">
                            New Features:
                        </h3>
                        <ul className="space-y-2">
                            {WHATS_NEW_141.features.map((feature, index) => (
                                <li key={index} className="flex items-start gap-2">
                                    <span className="text-green-500 mt-1">✓</span>
                                    <span className="text-gray-700 dark:text-gray-300 text-sm">
                                        {feature}
                                    </span>
                                </li>
                            ))}
                        </ul>
                    </div>

                    <div className="mb-6">
                        <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">
                            Keyboard Shortcuts:
                        </h3>
                        <div className="space-y-2">
                            {WHATS_NEW_141.shortcuts.map((shortcut, index) => (
                                <div key={index} className="flex items-center gap-2">
                                    <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded text-xs font-mono">
                                        {shortcut.split(' - ')[0]}
                                    </kbd>
                                    <span className="text-gray-600 dark:text-gray-400 text-sm">
                                        {shortcut.split(' - ')[1]}
                                    </span>
                                </div>
                            ))}
                        </div>
                    </div>

                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                            💡 <strong>Tip:</strong> Create different configurations for your various workflows 
                            (e.g., "English Coding", "Urdu Content", "WhatsApp Messages") and switch between 
                            them instantly with keyboard shortcuts!
                        </p>
                    </div>

                    <div className="flex justify-end gap-3">
                        <Button onClick={onRemindLater} variant="secondary">
                            Remind me later
                        </Button>
                        <Button onClick={onClose} variant="primary">
                            Got it! 🎉
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default WhatsNewModal;

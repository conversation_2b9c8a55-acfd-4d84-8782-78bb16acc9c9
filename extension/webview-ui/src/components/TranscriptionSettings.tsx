import BoltIcon from "@heroicons/react/24/solid/BoltIcon";
import InformationCircleIcon from "@heroicons/react/24/outline/InformationCircleIcon";
import MicrophoneIcon from "@heroicons/react/24/solid/MicrophoneIcon";
import Switch from "./Switch";

interface TranscriptionSettingsProps {
  service: string;
  model: string;
  language: string;
  shouldOptimize: boolean;
  realtime: boolean;
  voiceCommandsEnabled: boolean;
  onServiceChange: (service: string) => void;
  onModelChange: (model: string) => void;
  onLanguageChange: (language: string) => void;
  onOptimizeChange: (shouldOptimize: boolean) => void;
  onRealtimeChange: (realtime: boolean) => void;
  onVoiceCommandsChange: (enabled: boolean) => void;
}

const TranscriptionSettings: React.FC<TranscriptionSettingsProps> = ({
  service,
  model,
  language,
  shouldOptimize,
  realtime,
  voiceCommandsEnabled,
  onServiceChange,
  onModelChange,
  onLanguageChange,
  onOptimizeChange,
  onRealtimeChange,
  onVoiceCommandsChange
}) => {
  // ... existing code ...

  return (
    <div className="space-y-4">
      {/* ... existing service, model, and language selectors ... */}

      {/* Real-time switch - show for OpenAI GPT-4o models and AssemblyAI best model */}
      {(service === 'lemonfox' && ['gpt-4o-mini-transcribe', 'gpt-4o-transcribe'].includes(model)) || 
       (service === 'assemblyai' && model === 'best') ? (
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <BoltIcon className="text-primary dark:text-dark-primary w-5 h-5" />
            <span className="text-text dark:text-dark-text text-sm font-medium">
              Real-time Transcription
            </span>
          </div>
          <Switch
            label="Real-time Transcription"
            checked={realtime}
            onChange={onRealtimeChange}
          />
        </div>
      ) : null}

      {/* Voice Commands Toggle */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <MicrophoneIcon className="text-primary dark:text-dark-primary w-5 h-5" />
          <div className="flex items-center">
            <span className="text-text dark:text-dark-text mr-1 text-sm font-medium">
              Voice Commands
            </span>
            <InformationCircleIcon 
              className="dark:text-gray-400 cursor-help w-4 h-4 text-gray-500"
              title="Enable to use voice commands by saying 'Voice Hype' followed by your command. Disable to treat all input as text to optimize."
            />
          </div>
        </div>
        <Switch
          label="Voice Commands"
          checked={voiceCommandsEnabled}
          onChange={onVoiceCommandsChange}
        />
      </div>

      {/* ... rest of the component ... */}
    </div>
  );
};

export default TranscriptionSettings;
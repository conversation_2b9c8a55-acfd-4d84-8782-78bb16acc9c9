import React, { useEffect, useState } from 'react';

// Declare the global window property for TypeScript
declare global {
  interface Window {
    voicehypeLogoLight?: string;
    voicehypeLogoDark?: string;
  }
}

interface LogoProps {
  className?: string;
}

const Logo: React.FC<LogoProps> = ({ className = '' }) => {
  const [isDarkMode, setIsDarkMode] = useState(false);

  // Use theme information from extension via configuration
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      const message = event.data;
      
      if (message.command === 'initialConfiguration' && message.options?.theme) {
        // Set initial theme from extension
        const theme = message.options.theme;
        setIsDarkMode(theme === 'dark');
        console.log('Logo: Initial theme set from extension:', theme);
      } else if (message.command === 'themeChanged') {
        // Handle theme changes from extension
        const theme = message.data?.theme || message.theme;
        const isDark = theme === 'dark' || theme === 'vscode-dark';
        setIsDarkMode(isDark);
        console.log('Logo: Theme changed from extension:', theme);
      } else if (message.command === 'updateOptions' && message.options?.theme !== undefined) {
        // Handle theme updates via updateOptions
        const theme = message.options.theme;
        setIsDarkMode(theme === 'dark');
        console.log('Logo: Theme updated via options:', theme);
      }
    };

    window.addEventListener('message', handleMessage);

    // Request current theme if not provided in initial config
    const checkTheme = () => {
      // Fallback to DOM-based detection if extension hasn't provided theme yet
      const bodyClasses = document.body.className;
      const htmlClasses = document.documentElement.className;
      
      const hasDarkClass = bodyClasses.includes('vscode-dark') || htmlClasses.includes('vscode-dark');
      const hasLightClass = bodyClasses.includes('vscode-light') || htmlClasses.includes('vscode-light');
      
      let isDark = false;
      
      if (hasDarkClass && !hasLightClass) {
        isDark = true;
      } else if (!hasDarkClass && hasLightClass) {
        isDark = false;
      } else if (hasDarkClass && hasLightClass) {
        const bodyClassList = document.body.classList;
        const darkIndex = Array.from(bodyClassList).indexOf('vscode-dark');
        const lightIndex = Array.from(bodyClassList).indexOf('vscode-light');
        isDark = darkIndex > lightIndex;
      }
      
      setIsDarkMode(isDark);
    };

    // Use fallback detection if no theme from extension after a short delay
    const timeoutId = setTimeout(() => {
      checkTheme();
    }, 100);

    return () => {
      window.removeEventListener('message', handleMessage);
      clearTimeout(timeoutId);
    };
  }, []);

  // Use the appropriate logo URI based on the theme
  const logoSrc = isDarkMode
    ? window.voicehypeLogoLight  // Use light logo (with white text) on dark background
    : window.voicehypeLogoDark;  // Use dark logo (with dark text) on light background

  console.log(`Logo: Using ${isDarkMode ? 'light' : 'dark'} logo:`, logoSrc);

  return (
    <div className={`logo-container ${className}`}>
      {logoSrc ? (
        <img
          src={logoSrc}
          alt="VoiceHype Logo"
          className="logo-image"
          style={{ maxWidth: '100%', height: 'auto' }}
        />
      ) : (
        <div className="logo-placeholder">VoiceHype</div>
      )}
    </div>
  );
};

export default Logo;

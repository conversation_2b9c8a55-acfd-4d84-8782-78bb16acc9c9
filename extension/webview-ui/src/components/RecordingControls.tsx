import React from 'react';
import { MicrophoneIcon, StopIcon, PauseIcon, PlayIcon, XMarkIcon } from '@heroicons/react/24/solid';

interface RecordingControlsProps {
  isRecording: boolean;
  isPaused: boolean;
  elapsedTime: number;
  onStart: () => void;
  onStop: () => void;
  onPause: () => void;
  onResume: () => void;
  onCancel: () => void;
  // removing optimizeEnabled and onOptimizeToggle from the interface
}

const RecordingControls: React.FC<RecordingControlsProps> = ({
  isRecording,
  isPaused,
  elapsedTime,
  onStart,
  onStop,
  onPause,
  onResume,
  onCancel
}) => {
  // Format seconds to mm:ss
  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleStart = () => {
    console.log('[RecordingControls] [DEBUG] Start button clicked, current state:', { isRecording, isPaused });
    onStart();
  };

  const handleStop = () => {
    console.log('[RecordingControls] [DEBUG] Stop button clicked, current state:', { isRecording, isPaused });
    onStop();
  };

  const handlePauseResume = () => {
    console.log('[RecordingControls] [DEBUG] Pause/Resume button clicked, current state:', { isRecording, isPaused });
    if (isPaused) {
      console.log('[RecordingControls] [DEBUG] Resuming recording');
      onResume();
    } else {
      console.log('[RecordingControls] [DEBUG] Pausing recording');
      onPause();
    }
  };

  return (
    <div className="flex flex-col space-y-4">
      {/* Timer Display */}
      <div className="flex items-center justify-center">
        <div className="bg-surface dark:bg-dark-surface/80 border-primary/30 dark:border-primary/30 p-3 font-mono text-3xl border rounded-lg shadow-inner">
          {formatDuration(elapsedTime)}
        </div>
      </div>

      {/* Control Buttons */}
      <div className="flex items-center justify-center space-x-4">
        {!isRecording ? (
          <button
            className="w-14 h-14 bg-primary hover:bg-primary-hover flex items-center justify-center text-black transition-colors rounded-full shadow-md"
            onClick={handleStart}
            aria-label="Start recording"
          >
            <MicrophoneIcon className="w-6 h-6" />
          </button>
        ) : (
          <>
            <button
              className="bg-primary hover:bg-primary-hover flex items-center justify-center w-12 h-12 text-black transition-colors rounded-full shadow-md"
              onClick={handlePauseResume}
              aria-label={isPaused ? "Resume recording" : "Pause recording"}
            >
              {isPaused ? <PlayIcon className="w-5 h-5" /> : <PauseIcon className="w-5 h-5" />}
            </button>

            <button
              className="bg-secondary hover:bg-secondary-hover flex items-center justify-center w-12 h-12 text-white transition-colors rounded-full shadow-md"
              onClick={handleStop}
              aria-label="Stop recording"
            >
              <StopIcon className="w-5 h-5" />
            </button>

            <button
              className="bg-accent hover:bg-accent-hover flex items-center justify-center w-12 h-12 text-black transition-colors rounded-full shadow-md"
              onClick={onCancel}
              aria-label="Cancel recording"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </>
        )}
      </div>

       {/* Removed Optimize Toggle */}
    </div>
  );
};

export default RecordingControls;
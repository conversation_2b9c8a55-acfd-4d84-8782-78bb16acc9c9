import * as React from 'react';
import { useState, useEffect, useRef, forwardRef, useImperativeHandle } from 'react';

interface BeautifulRealtimeTranscriptProps {
  isVisible: boolean;
  isRecording: boolean;
  className?: string;
  onClear?: () => void;
}

interface TranscriptSegment {
  id: string;
  text: string;
  isFinal: boolean;
  timestamp: number;
}

export interface BeautifulRealtimeTranscriptRef {
  handlePartialTranscript: (text: string) => void;
  handleFinalTranscript: (text: string) => void;
  handleConnectionStatus: (status: string, message?: string) => void;
  handleGracePeriodStatus: (isActive: boolean, remainingSeconds?: number) => void;
}

const BeautifulRealtimeTranscript = forwardRef<BeautifulRealtimeTranscriptRef, BeautifulRealtimeTranscriptProps>(({
  isVisible,
  isRecording,
  className = '',
  onClear
}, ref) => {
  const [segments, setSegments] = useState<TranscriptSegment[]>([]);
  const [partialText, setPartialText] = useState<string>('');
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [connectionStatus, setConnectionStatus] = useState<string>('');
  const [isGracePeriodActive, setIsGracePeriodActive] = useState<boolean>(false);
  const [gracePeriodRemaining, setGracePeriodRemaining] = useState<number>(0);
  const scrollRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new content is added
  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [segments, partialText]);

  // Clear transcript when recording stops
  useEffect(() => {
    if (!isRecording) {
      setSegments([]);
      setPartialText('');
      setIsConnected(false);
      setConnectionStatus('');
      setIsGracePeriodActive(false);
      setGracePeriodRemaining(0);
    }
  }, [isRecording]);

  // Handle real-time transcript updates
  const handlePartialTranscript = (text: string) => {
    setPartialText(text);
  };

  const handleFinalTranscript = (text: string) => {
    if (text.trim()) {
      const newSegment: TranscriptSegment = {
        id: Date.now().toString(),
        text: text.trim(),
        isFinal: true,
        timestamp: Date.now()
      };
      
      setSegments(prev => [...prev, newSegment]);
      setPartialText(''); // Clear partial text after final
    }
  };

  const handleConnectionStatus = (status: string, message?: string) => {
    setConnectionStatus(message || status);
    setIsConnected(status === 'connected' || status === 'transcribing');
  };

  const handleGracePeriodStatus = (isActive: boolean, remainingSeconds?: number) => {
    setIsGracePeriodActive(isActive);
    setGracePeriodRemaining(remainingSeconds || 0);

    if (isActive) {
      setConnectionStatus(`Waiting for remaining content... (${remainingSeconds}s)`);
    }
  };

  const handleClearText = () => {
    setSegments([]);
    setPartialText('');
    if (onClear) {
      onClear();
    }
  };

  // Expose methods for parent component to call
  useImperativeHandle(ref, () => ({
    handlePartialTranscript,
    handleFinalTranscript,
    handleConnectionStatus,
    handleGracePeriodStatus
  }));

  if (!isVisible) {
    return null;
  }

  return (
    <div className={`beautiful-realtime-transcript ${className}`}>
      {/* Header */}
      <div className="beautiful-realtime-transcript-header">
        <div className="flex items-center justify-between">
          <h3 className="text-text dark:text-dark-text text-sm font-medium">
            Live Transcription
          </h3>
          {segments.length > 0 && (
            <button
              onClick={handleClearText}
              className="clear-button px-2 py-1 text-xs rounded"
              title="Clear transcript"
            >
              Clear
            </button>
          )}
        </div>
      </div>

      {/* Transcript Content */}
      <div
        ref={scrollRef}
        className="beautiful-realtime-transcript-content"
      >
        {!isRecording && (
          <div className="transcript-placeholder">
            <div className="dark:text-gray-400 text-sm text-center text-gray-500">
              Start recording to see live transcription
            </div>
          </div>
        )}

        {isRecording && segments.length === 0 && !partialText && (
          <div className="transcript-placeholder">
            <div className="dark:text-gray-400 text-sm text-center text-gray-500">
              Listening... Start speaking!
            </div>
          </div>
        )}

        {/* Final transcript segments */}
        {segments.map((segment) => (
          <div key={segment.id} className="transcript-segment final">
            <span className="segment-text">{segment.text}</span>
          </div>
        ))}

        {/* Partial transcript */}
        {partialText && (
          <div className="transcript-segment partial">
            <span className="segment-text">{partialText}</span>
            <span className="typing-indicator">|</span>
          </div>
        )}

        {/* Grace period indicator */}
        {isGracePeriodActive && (
          <div className="transcript-segment grace-period">
            <div className="dark:text-blue-400 text-sm text-center text-blue-600">
              Waiting for remaining content... ({gracePeriodRemaining}s)
            </div>
          </div>
        )}
      </div>
    </div>
  );
});

BeautifulRealtimeTranscript.displayName = 'BeautifulRealtimeTranscript';

export default BeautifulRealtimeTranscript;
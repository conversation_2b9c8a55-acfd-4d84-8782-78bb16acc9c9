import React, { useMemo, memo, useCallback, useEffect, useState } from 'react';
import { InformationCircleIcon, ArrowPathIcon } from '@heroicons/react/24/outline';

interface OptimizationModelSelectorProps {
  optimizationModel: string;
  onChange: (model: string) => void;
}

interface ModelOption {
  id: string;
  name: string;
}

// Default models as fallback
const DEFAULT_MODELS: ModelOption[] = [
  {
    id: 'Claude 4 Sonnet',
    name: '<PERSON> 4 Sonnet'
  },
  {
    id: 'Claude 3.7 Sonnet',
    name: 'Claude 3.7 Sonnet'
  },
  {
    id: 'Claude 3.5 Sonnet',
    name: '<PERSON> 3.5 Sonnet'
  },
  {
    id: '<PERSON>',
    name: '<PERSON>'
  },
  {
    id: 'Llama 3.1 70B',
    name: '<PERSON><PERSON><PERSON> 3.1 70B'
  },
  {
    id: 'DeepSeek V3',
    name: 'DeepSeek V3'
  }
];

// Supabase configuration
const SUPABASE_URL = 'https://supabase.voicehype.ai';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiYW5vbiIsImlzcyI6ImFidWgxMjMxIiwiaWF0IjoxNzQ2Mzg1MjAwLCJleHAiOjE5MDQxNTE2MDB9.EkgKcDa0V0VEmPsXPmyal3YvxYuu9Q1k8OZZv7Gs8_o';

const OptimizationModelSelector: React.FC<OptimizationModelSelectorProps> = memo(({ optimizationModel, onChange }) => {
  const [models, setModels] = useState<ModelOption[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  // Fetch models from Supabase with retry functionality
  const fetchModels = async (retryCount = 3): Promise<ModelOption[]> => {
    try {
      const response = await fetch(`${SUPABASE_URL}/rest/v1/models?select=*&order=friendly_name.asc&model_type=eq.optimization`, {
        method: 'GET',
        headers: {
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      // Transform database response to ModelOption format
      return data.map((model: any) => ({
        id: model.friendly_name, // Use friendly_name as the value to send
        name: model.friendly_name
      }));
    } catch (error) {
      if (retryCount > 0) {
        console.warn(`Failed to fetch models, retrying... (${retryCount} attempts left)`, error);
        // Exponential backoff: wait 1s, 2s, 4s before retrying
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, 3 - retryCount) * 1000));
        return fetchModels(retryCount - 1);
      }
      throw error;
    }
  };

  const loadModels = async (isManualRetry = false) => {
    try {
      setLoading(true);
      setError(null);

      if (isManualRetry) {
        setRetryCount(prev => prev + 1);
      }

      const fetchedModels = await fetchModels();

      if (fetchedModels.length > 0) {
        setModels(fetchedModels);
      } else {
        // Fallback to default models if no models returned from database
        console.warn('No models found in database, using defaults');
        setModels(DEFAULT_MODELS);
      }
    } catch (error) {
      console.error('Failed to fetch models after retries:', error);
      setError('Failed to load models. Using default models.');
      setModels(DEFAULT_MODELS);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadModels();
  }, []);

  const selectedModel = useMemo(() => {
    // Find the selected model, or default to Claude 4 Sonnet
    const found = models.find(o => o.id === optimizationModel);
    if (found) return found;

    // Default to Claude 4 Sonnet for new users
    return models.find(o => o.id === 'claude-4-sonnet') || models[0];
  }, [models, optimizationModel]);

  const handleModelChange = useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    const newModel = e.target.value;
    if (newModel !== optimizationModel) {
      onChange(newModel);
    }
  }, [optimizationModel, onChange]);

  const handleRetry = useCallback(() => {
    loadModels(true);
  }, []);

  if (loading) {
    return (
      <div className="space-y-3">
        <div className="relative">
          <div className="border-border dark:border-dark-border dark:bg-dark-surface text-text dark:text-dark-text w-full px-3 py-2 bg-white border rounded-md">
            <div className="animate-pulse dark:bg-gray-700 w-full h-4 bg-gray-200 rounded"></div>
          </div>
        </div>
        <div className="text-muted dark:text-dark-muted bg-gray-50 dark:bg-dark-surface/40 flex items-start p-3 text-sm rounded-md">
          <InformationCircleIcon className="text-primary dark:text-dark-primary flex-shrink-0 w-5 h-5 mr-2" />
          <p>Loading optimization models...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-3">
        <div className="relative">
          <select
            className="border-border dark:border-dark-border dark:bg-dark-surface text-text dark:text-dark-text focus:outline-none focus:ring-2 focus:ring-primary/50 dark:focus:ring-dark-primary/50 w-full px-3 py-2 bg-white border rounded-md appearance-none cursor-pointer"
            value={optimizationModel}
            onChange={handleModelChange}
            aria-label="Select optimization model"
          >
            {models.map((option) => (
              <option key={option.id} value={option.id}>
                {option.name}
              </option>
            ))}
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
            <svg className="text-muted dark:text-dark-muted w-4 h-4" fill="none" viewBox="0 0 20 20" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7l3-3 3 3m0 6l-3 3-3-3" />
            </svg>
          </div>
        </div>

        <div className="dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-900/20 flex items-start justify-between p-3 text-sm text-yellow-600 rounded-md">
          <div className="flex items-start">
            <InformationCircleIcon className="dark:text-yellow-400 flex-shrink-0 w-5 h-5 mr-2 text-yellow-500" />
            <p>{error}</p>
          </div>
          <button
            onClick={handleRetry}
            className="dark:bg-yellow-800 hover:bg-yellow-300 dark:hover:bg-yellow-700 flex items-center px-2 py-1 ml-2 text-xs transition-colors bg-yellow-200 rounded"
            title="Retry loading models"
          >
            <ArrowPathIcon className="w-3 h-3 mr-1" />
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <div className="relative">
        <select
          className="border-border dark:border-dark-border dark:bg-dark-surface text-text dark:text-dark-text focus:outline-none focus:ring-2 focus:ring-primary/50 dark:focus:ring-dark-primary/50 w-full px-3 py-2 bg-white border rounded-md appearance-none cursor-pointer"
          value={optimizationModel}
          onChange={handleModelChange}
          aria-label="Select optimization model"
        >
          {models.map((option) => (
            <option key={option.id} value={option.id}>
              {option.name}
            </option>
          ))}
        </select>
        <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
          <svg className="text-muted dark:text-dark-muted w-4 h-4" fill="none" viewBox="0 0 20 20" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7l3-3 3 3m0 6l-3 3-3-3" />
          </svg>
        </div>
      </div>
    </div>
  );
});

// Display name for debugging
OptimizationModelSelector.displayName = 'OptimizationModelSelector';

export default OptimizationModelSelector;

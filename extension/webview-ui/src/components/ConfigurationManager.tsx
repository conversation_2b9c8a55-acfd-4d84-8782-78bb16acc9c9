import React, { useState } from 'react';
import { Configuration, ConfigurationSettings } from '../types/configuration';
import ConfigurationCard from './ConfigurationCard';
import ConfigurationEditor from './ConfigurationEditor';
import Button from './Button';

interface ConfigurationManagerProps {
    configurations: Configuration[];
    activeConfigurationId: string | null;
    currentSettings: ConfigurationSettings;
    onCreateConfiguration: (data: Partial<Configuration>) => void;
    onUpdateConfiguration: (data: Partial<Configuration>) => void;
    onDeleteConfiguration: (id: string) => void;
    onSwitchConfiguration: (id: string) => void;
}

const ConfigurationManager: React.FC<ConfigurationManagerProps> = ({
    configurations,
    activeConfigurationId,
    currentSettings,
    onCreateConfiguration,
    onUpdateConfiguration,
    onDeleteConfiguration,
    onSwitchConfiguration
}) => {
    const [isEditorOpen, setIsEditorOpen] = useState(false);
    const [editingConfiguration, setEditingConfiguration] = useState<Configuration | null>(null);

    const handleCreateNew = () => {
        setEditingConfiguration(null);
        setIsEditorOpen(true);
    };

    const handleEdit = (configuration: Configuration) => {
        setEditingConfiguration(configuration);
        setIsEditorOpen(true);
    };

    const handleDuplicate = (configuration: Configuration) => {
        const duplicatedConfig = {
            title: `${configuration.title} (Copy)`,
            description: configuration.description,
            settings: { ...configuration.settings }
        };
        onCreateConfiguration(duplicatedConfig);
    };

    const handleSave = (data: Partial<Configuration>) => {
        if (editingConfiguration) {
            onUpdateConfiguration(data);
        } else {
            onCreateConfiguration(data);
        }
    };

    const handleDelete = (id: string) => {
        if (window.confirm('Are you sure you want to delete this configuration?')) {
            onDeleteConfiguration(id);
        }
    };

    return (
        <div className="p-6">
            <div className="mb-6">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    Configuration Manager
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Create and manage configuration profiles for different workflows
                </p>
            </div>

            {/* Keyboard shortcuts info */}
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
                <h3 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                    Keyboard Shortcuts
                </h3>
                <div className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                    <div><kbd className="px-2 py-1 bg-blue-100 dark:bg-blue-800 rounded">Ctrl+Shift+PageUp</kbd> - Switch to next configuration</div>
                    <div><kbd className="px-2 py-1 bg-blue-100 dark:bg-blue-800 rounded">Ctrl+Shift+PageDown</kbd> - Switch to previous configuration</div>
                </div>
            </div>

            {/* Configurations list */}
            {configurations.length === 0 ? (
                <div className="text-center py-12">
                    <div className="text-gray-400 dark:text-gray-600 mb-4">
                        <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                        </svg>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                        No configurations yet
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400 mb-4">
                        Create your first configuration profile to get started
                    </p>
                    <Button onClick={handleCreateNew} variant="primary">
                        Create Configuration
                    </Button>
                </div>
            ) : (
                <div className="space-y-4">
                    {configurations.map((config) => (
                        <ConfigurationCard
                            key={config.id}
                            configuration={config}
                            isActive={config.id === activeConfigurationId}
                            onActivate={onSwitchConfiguration}
                            onEdit={handleEdit}
                            onDelete={handleDelete}
                            onDuplicate={handleDuplicate}
                        />
                    ))}
                </div>
            )}

            {/* Configuration Editor Modal */}
            <ConfigurationEditor
                isOpen={isEditorOpen}
                onClose={() => setIsEditorOpen(false)}
                onSave={handleSave}
                configuration={editingConfiguration}
                currentSettings={currentSettings}
            />
        </div>
    );
};

export default ConfigurationManager;

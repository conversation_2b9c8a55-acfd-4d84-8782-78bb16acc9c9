import React, { useEffect } from 'react';

export interface SnackbarProps {
    message: string;
    type: 'success' | 'error' | 'warning' | 'info';
    isVisible: boolean;
    onClose: () => void;
    autoClose?: boolean;
    duration?: number;
}

const Snackbar: React.FC<SnackbarProps> = ({
    message,
    type,
    isVisible,
    onClose,
    autoClose = true,
    duration = 3000
}) => {
    useEffect(() => {
        if (isVisible && autoClose) {
            const timer = setTimeout(() => {
                onClose();
            }, duration);

            return () => clearTimeout(timer);
        }
    }, [isVisible, autoClose, duration, onClose]);

    if (!isVisible) return null;

    const getTypeStyles = () => {
        switch (type) {
            case 'success':
                return 'bg-green-600 text-white';
            case 'error':
                return 'bg-red-600 text-white';
            case 'warning':
                return 'bg-yellow-600 text-white';
            case 'info':
                return 'bg-blue-600 text-white';
            default:
                return 'bg-gray-600 text-white';
        }
    };

    const getIcon = () => {
        switch (type) {
            case 'success':
                return '✓';
            case 'error':
                return '✕';
            case 'warning':
                return '⚠';
            case 'info':
                return 'ℹ';
            default:
                return '';
        }
    };

    return (
        <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50 animate-slide-up">
            <div className={`
                flex items-center gap-2 px-4 py-3 rounded-lg shadow-lg min-w-64 max-w-96
                ${getTypeStyles()}
                transition-all duration-300 ease-in-out
            `}>
                <span className="text-lg">{getIcon()}</span>
                <span className="flex-1 text-sm font-medium">{message}</span>
                <button
                    onClick={onClose}
                    className="ml-2 text-white hover:text-gray-200 transition-colors"
                    aria-label="Close notification"
                >
                    ✕
                </button>
            </div>
        </div>
    );
};

export default Snackbar;

import React, { memo, useCallback } from 'react';

interface ServiceSelectorProps {
  service: string;
  onChange: (service: string) => void;
}

const ServiceSelector: React.FC<ServiceSelectorProps> = memo(({ service, onChange }) => {
  const services = [
    { id: 'assemblyai', name: 'AssemblyAI' },
    { id: 'lemonfox', name: 'Whisper' } // Changed from 'OpenAI' to 'Whisper' but keeping the ID as 'lemonfox' for compatibility
  ];

  // Memoize click handlers to prevent unnecessary re-renders
  const handleServiceClick = useCallback((serviceId: string) => {
    if (service !== serviceId) {
      onChange(serviceId);
    }
  }, [service, onChange]);

  return (
    <div className="flex gap-2" role="radiogroup" aria-label="Select transcription service">
      {services.map(s => (
        <button
          key={s.id}
          className={`flex-1 py-2.5 px-4 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-primary/50 dark:focus:ring-dark-primary/50
            ${service === s.id
              ? 'bg-primary dark:bg-dark-primary text-black shadow-sm font-bold'
              : 'bg-gray-100 dark:bg-dark-surface hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 font-medium'
            }`}
          onClick={() => handleServiceClick(s.id)}
          role="radio"
          aria-checked={service === s.id}
        >
          {s.name}
        </button>
      ))}
    </div>
  );
});

// Display name for debugging
ServiceSelector.displayName = 'ServiceSelector';

export default ServiceSelector;
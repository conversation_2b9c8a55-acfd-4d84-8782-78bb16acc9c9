import React, { useState, useEffect } from 'react';
import { Configuration, ConfigurationSettings } from '../types/configuration';
import Button from './Button';
import ServiceSelector from './ServiceSelector';
import ModelSelector from './ModelSelector';
import LanguageSelector from './LanguageSelector';
import OptimizationModelSelector from './OptimizationModelSelector';
import PromptModeSelector from './PromptModeSelector';
import Switch from './Switch';
import AudioSettings from './AudioSettings';

interface ConfigurationEditorProps {
    isOpen: boolean;
    onClose: () => void;
    onSave: (data: Partial<Configuration>) => void;
    configuration?: Configuration | null;
    currentSettings: ConfigurationSettings;
}

const ConfigurationEditor: React.FC<ConfigurationEditorProps> = ({
    isOpen,
    onClose,
    onSave,
    configuration,
    currentSettings
}) => {
    const [title, setTitle] = useState('');
    const [description, setDescription] = useState('');
    const [settings, setSettings] = useState<ConfigurationSettings>(currentSettings);
    const [errors, setErrors] = useState<{ title?: string }>({});

    useEffect(() => {
        if (isOpen) {
            if (configuration) {
                // Edit mode
                setTitle(configuration.title);
                setDescription(configuration.description);
                setSettings(configuration.settings);
            } else {
                // Create mode - use current settings
                setTitle('');
                setDescription('');
                setSettings(currentSettings);
            }
            setErrors({});
        }
    }, [isOpen, configuration, currentSettings]);

    const validateForm = () => {
        const newErrors: { title?: string } = {};
        
        if (!title.trim()) {
            newErrors.title = 'Title is required';
        }
        
        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSave = () => {
        if (!validateForm()) return;

        const configData: Partial<Configuration> = {
            title: title.trim(),
            description: description.trim(),
            settings
        };

        if (configuration) {
            configData.id = configuration.id;
        }

        onSave(configData);
        onClose();
    };

    const updateSetting = (key: keyof ConfigurationSettings, value: any) => {
        setSettings(prev => ({
            ...prev,
            [key]: value
        }));
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
                <div className="flex items-center justify-between mb-6">
                    <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                        {configuration ? 'Edit Configuration' : 'Create Configuration'}
                    </h2>
                    <button
                        onClick={onClose}
                        className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                    >
                        ✕
                    </button>
                </div>

                <div className="space-y-4">
                    {/* Title */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Title *
                        </label>
                        <input
                            type="text"
                            value={title}
                            onChange={(e) => setTitle(e.target.value)}
                            className={`w-full px-3 py-2 border rounded-md dark:bg-gray-700 dark:text-gray-100 ${
                                errors.title ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                            }`}
                            placeholder="e.g., English Workflow, Urdu Settings"
                        />
                        {errors.title && (
                            <p className="text-red-500 text-xs mt-1">{errors.title}</p>
                        )}
                    </div>

                    {/* Description */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Description
                        </label>
                        <textarea
                            value={description}
                            onChange={(e) => setDescription(e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-gray-100"
                            rows={2}
                            placeholder="Optional description for this configuration"
                        />
                    </div>

                    {/* Settings */}
                    <div className="border-t pt-4">
                        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                            Configuration Settings
                        </h3>
                        
                        <div className="space-y-4">
                            <ServiceSelector
                                service={settings.service}
                                onChange={(value) => updateSetting('service', value)}
                            />

                            <ModelSelector
                                service={settings.service}
                                model={settings.model}
                                onChange={(value) => updateSetting('model', value)}
                            />

                            <LanguageSelector
                                service={settings.service}
                                model={settings.model}
                                language={settings.language}
                                onChange={(value) => updateSetting('language', value)}
                            />

                            {/* Only show real-time switch for AssemblyAI best model */}
                            {settings.service === 'assemblyai' && settings.model === 'best' && (
                                <Switch
                                    label="Real-time Transcription"
                                    checked={settings.realtime}
                                    onChange={(checked) => updateSetting('realtime', checked)}
                                />
                            )}

                            <Switch
                                label="AI Optimization"
                                checked={settings.optimizeEnabled}
                                onChange={(checked) => updateSetting('optimizeEnabled', checked)}
                            />

                            {settings.optimizeEnabled && (
                                <>
                                    <OptimizationModelSelector
                                        optimizationModel={settings.optimizationModel}
                                        onChange={(value) => updateSetting('optimizationModel', value)}
                                    />

                                    <PromptModeSelector
                                        value={settings.customPrompt}
                                        onChange={(prompt) => updateSetting('customPrompt', prompt)}
                                        onModeChange={(modeId) => {
                                            // Store the selected mode ID
                                            updateSetting('promptMode', modeId);
                                            console.log('Configuration Editor: Prompt mode changed to:', modeId);
                                        }}
                                    />
                                </>
                            )}


                        </div>
                    </div>
                </div>

                <div className="flex justify-end gap-3 mt-6 pt-4 border-t">
                    <Button onClick={onClose} variant="secondary">
                        Cancel
                    </Button>
                    <Button onClick={handleSave} variant="primary">
                        {configuration ? 'Update' : 'Create'}
                    </Button>
                </div>
            </div>
        </div>
    );
};

export default ConfigurationEditor;

import * as React from 'react';
import { useState, useEffect, useCallback, useRef } from 'react';
import RecordingControls from './components/RecordingControls';
import ServiceSelector from './components/ServiceSelector';
import ModelSelector from './components/ModelSelector';
import LanguageSelector from './components/LanguageSelector';
import OptimizationModelSelector from './components/OptimizationModelSelector';
import RecentTranscriptions from './components/RecentTranscriptions';
import BeautifulRealtimeTranscript, { BeautifulRealtimeTranscriptRef } from './components/BeautifulRealtimeTranscript';
import { vscode } from './utilities/vscode';
import Switch from 'components/Switch';
import AudioSettings from './components/AudioSettings';
import Logo from './components/Logo';
import PromptModeSelector from 'components/PromptModeSelector';
import ApiKeyInput from 'components/ApiKeyInput';
import OnboardingScreen from 'components/OnboardingScreen';
import LoadingScreen from 'components/LoadingScreen';
import ConfigurationManager from 'components/ConfigurationManager';
import Snackbar from 'components/Snackbar';
import WhatsNewModal from 'components/WhatsNewModal';
import { Configuration, ConfigurationSettings } from './types/configuration';

// Add TypeScript declaration for the update timeout properties
declare global {
  interface Window {
    promptUpdateTimeout: NodeJS.Timeout | undefined;
    deviceUpdateTimeout: NodeJS.Timeout | undefined;
  }
}

interface Transcription {
  id: string;
  timestamp: string;
  originalText: string;
  optimizedText?: string;
  service: string;
  model: string;
  language: string;
  duration?: number;
  isPreview?: boolean;
}

// Track last update timestamps for each setting
interface LastUpdates {
  service: number;
  model: number;
  language: number;
  optimize: number;
  optimizationModel: number;
  translate: number;
  customPrompt: number;
  promptMode: number;
  realtime: number;
  sampleRate: number;
  deviceId: number;
  voiceCommands: number;
}

interface AudioDevice {
  id: string;
  name: string;
}

// Define state reference type
interface StateRef {
  service: string;
  model: string;
  language: string;
  optimizeEnabled: boolean;
  optimizationModel: string;
  translate: boolean;
  realtime: boolean;
  customPrompt: string;
  promptMode: string;
  sampleRate: number;
  deviceId: string | null;
  apiKey: string;
  voiceCommandsEnabled: boolean;
  isAuthenticated: boolean;
}

const App: React.FC = () => {
  // Track last update timestamps
  const lastUpdates = useRef<LastUpdates>({
    service: 0,
    model: 0,
    language: 0,
    optimize: 0,
    optimizationModel: 0,
    translate: 0,
    customPrompt: 0,
    promptMode: 0,
    realtime: 0,
    sampleRate: 0,
    deviceId: 0,
    voiceCommands: 0
  });

  // Initialize state reference
  const stateRef = useRef<StateRef>({
    service: '',
    model: '',
    language: '',
    optimizeEnabled: false,
    optimizationModel: 'gpt-4o',
    translate: false,
    realtime: false,
    customPrompt: '',
    promptMode: 'clean-up',
    sampleRate: 44100,
    deviceId: null,
    apiKey: '',
    voiceCommandsEnabled: true,
    isAuthenticated: false
  });

  // Flag to track initial configuration loading
  const [initialConfigLoaded, setInitialConfigLoaded] = useState<boolean>(false);

  // Recording state
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [isPaused, setIsPaused] = useState<boolean>(false);
  const [elapsedTime, setElapsedTime] = useState<number>(0);

  // Configuration state
  const [service, setService] = useState<string>('assemblyai');
  const [model, setModel] = useState<string>('whisper-1');
  const [lastAssemblyAIModel, setLastAssemblyAIModel] = useState<string>('best');
  const [language, setLanguage] = useState<string>('en');
  const [customPrompt, setCustomPrompt] = useState<string>('Correct any grammar issues and improve clarity. Keep the meaning intact.');
  const [promptMode, setPromptMode] = useState<string>('clean-up');
  const [optimizeEnabled, setOptimizeEnabled] = useState<boolean>(true);
  const [optimizationModel, setOptimizationModel] = useState<string>('gpt-4o');
  const [translate, setTranslate] = useState<boolean>(false);
  const [realtime, setRealtime] = useState<boolean>(false);

  // Audio settings state
  const [sampleRate, setSampleRate] = useState<number>(22050);
  const [deviceId, setDeviceId] = useState<string | null>(null);
  const [availableDevices, setAvailableDevices] = useState<AudioDevice[]>([]);

  // Add voice commands state
  const [voiceCommandsEnabled, setVoiceCommandsEnabled] = useState<boolean>(true);

  // Add API key state
  const [apiKey, setApiKey] = useState<string>('');
  // Authentication state
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  // Loading state to handle initialization
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Keep stateRef in sync with the actual state
  useEffect(() => {
    stateRef.current = {
      service,
      model,
      language,
      optimizeEnabled,
      optimizationModel,
      translate,
      realtime,
      customPrompt,
      promptMode,
      sampleRate,
      deviceId,
      apiKey,
      voiceCommandsEnabled,
      isAuthenticated
    };
  }, [service, model, language, optimizeEnabled, optimizationModel, translate, realtime, customPrompt, promptMode, sampleRate, deviceId, apiKey, voiceCommandsEnabled, isAuthenticated]);

  // We've removed the periodic polling for transcription updates
  // to prevent constant refreshing that causes janky UI behavior
  useEffect(() => {
    // Only request transcriptions once when initial config is loaded
    if (initialConfigLoaded) {
      console.log('WebView: Requesting initial transcriptions');
      vscode.postMessage({ command: 'getTranscriptions' });
      
      // Also request available audio devices
      console.log('WebView: Requesting audio devices on initial load');
      vscode.postMessage({ command: 'getAudioDevices' });
    }
  }, [initialConfigLoaded]);

  // Transcriptions
  const [transcriptions, setTranscriptions] = useState<Transcription[]>([]);

  // Configuration management state
  const [configurations, setConfigurations] = useState<Configuration[]>([]);
  const [activeConfigurationId, setActiveConfigurationId] = useState<string | null>(null);
  const [showConfigurationManager, setShowConfigurationManager] = useState<boolean>(false);

  // Snackbar state
  const [snackbar, setSnackbar] = useState<{
    message: string;
    type: 'success' | 'error' | 'warning' | 'info';
    isVisible: boolean;
  }>({
    message: '',
    type: 'info',
    isVisible: false
  });

  // What's New modal state
  const [showWhatsNewModal, setShowWhatsNewModal] = useState<boolean>(false);

  // Real-time transcript ref
  const realtimeTranscriptRef = useRef<BeautifulRealtimeTranscriptRef>(null);

  // Prevent updating state with older values from extension
  const updateStateIfNewer = useCallback((key: keyof LastUpdates, value: any, setter: React.Dispatch<React.SetStateAction<any>>) => {
    const now = Date.now();
    const lastUpdate = lastUpdates.current[key];

    // Always update if initial config hasn't been loaded yet
    if (!initialConfigLoaded) {
      lastUpdates.current[key] = now;
      setter(value);
      return true;
    }

    // If this is a newer update (or there's a threshold of 1000ms passed), apply it
    if (now - lastUpdate > 1000) {
      lastUpdates.current[key] = now;
      setter(value);
      return true;
    }
    return false;
  }, [initialConfigLoaded]);

  // Configuration helper functions
  const getCurrentSettings = useCallback((): ConfigurationSettings => ({
    service,
    model,
    language,
    translate,
    realtime,
    optimizeEnabled,
    optimizationModel,
    customPrompt,
    promptMode,
    sampleRate,
    deviceId
  }), [service, model, language, translate, realtime, optimizeEnabled, optimizationModel, customPrompt, promptMode, sampleRate, deviceId]);

  const showSnackbar = useCallback((message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') => {
    setSnackbar({ message, type, isVisible: true });
  }, []);

  const hideSnackbar = useCallback(() => {
    setSnackbar(prev => ({ ...prev, isVisible: false }));
  }, []);

  // Configuration handlers
  const handleCreateConfiguration = useCallback((data: Partial<Configuration>) => {
    vscode.postMessage({
      command: 'createConfiguration',
      data
    });
  }, []);

  const handleUpdateConfiguration = useCallback((data: Partial<Configuration>) => {
    vscode.postMessage({
      command: 'updateConfiguration',
      data
    });
  }, []);

  const handleDeleteConfiguration = useCallback((configId: string) => {
    vscode.postMessage({
      command: 'deleteConfiguration',
      configId
    });
  }, []);

  const handleSwitchConfiguration = useCallback((configId: string) => {
    vscode.postMessage({
      command: 'switchConfiguration',
      configId
    });
  }, []);

  // Initialize authentication state
  useEffect(() => {
    const initializeAuth = async () => {
      console.log('Initializing authentication state...');
      setIsLoading(true);
      
      try {
        // Request API key from extension
        vscode.postMessage({ command: 'getApiKey' });
        
        // We'll receive the API key in the message handler
        // The loading state will be cleared when we receive the API key
        // This is handled in the message handler for 'apiKeyResponse'
      } catch (error) {
        console.error('Error initializing authentication:', error);
        setIsLoading(false);
      }
    };
    
    initializeAuth();
  }, []);

  // Check if initial configuration was loaded, and if not, request it again
  useEffect(() => {
    const configCheckTimeout = setTimeout(() => {
      if (!initialConfigLoaded) {
        console.log('WebView: Initial config still not loaded after component mount, requesting again...');
        vscode.postMessage({ command: 'refreshConfigAndTheme' });
      }
    }, 2000); // 2 seconds after component mount

    return () => {
      clearTimeout(configCheckTimeout);
    };
  }, [initialConfigLoaded]);

  // Handle receiving messages from extension
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      const message = event.data;
      console.log('WebView: Received message from extension:', message.command);

      switch (message.command) {
        case 'apiKeySaved':
          console.log('App: Received apiKeySaved message', message);
          if (message.success && message.apiKey) {
            setApiKey(message.apiKey);
            setIsAuthenticated(true);

            // Optional: go to main view if not already
            vscode.postMessage({
              command: 'updateView',
              view: 'main',
              apiKey: message.apiKey
            });
          } else {
            console.warn('App: apiKeySaved received but either unsuccessful or no API key');
          }
          break;
        case 'authStatusUpdated':
          console.log('App: Received authStatusUpdated', message);
          setIsAuthenticated(message.authenticated);
          if (message.apiKey) {
            setApiKey(message.apiKey);
          }
          break;
          
        case 'manualApiKeyVerified':
          console.log('App: Received manualApiKeyVerified', message);
          setApiKey(message.apiKey);
          setIsAuthenticated(true);
          break;
          
        case 'updateView':
          console.log('App: Received updateView', message);
          if (message.view === 'main') {
            setIsAuthenticated(true);
            setApiKey(message.apiKey || apiKey);
            setIsLoading(false); // Ensure loading state is cleared
          }
          break;

        case 'themeChanged':
          const theme = message.data?.theme || message.theme;
          console.log('WebView: Theme changed to:', theme);
          // Force a re-render by updating a dummy state or by triggering a refresh
          // The theme change will be handled by the CSS variables and the Logo component
          break;

        case 'transcription':
          setTranscriptions((prev: Transcription[]) => [
            {
              id: Date.now().toString(),
              timestamp: new Date().toISOString(),
              originalText: message.text,
              optimizedText: message.optimizedText,
              // Use stateRef to access the current state values without dependency issues
              service: stateRef.current.service,
              model: stateRef.current.model,
              language: stateRef.current.language
            },
            ...prev
          ]);
          break;

        case 'realtimePartialTranscript':
          console.log('WebView: Received partial transcript:', message.text);
          if (realtimeTranscriptRef.current) {
            realtimeTranscriptRef.current.handlePartialTranscript(message.text || '');
          }
          break;

        case 'realtimeFinalTranscript':
          console.log('WebView: Received final transcript:', message.text);
          if (realtimeTranscriptRef.current) {
            realtimeTranscriptRef.current.handleFinalTranscript(message.text || '');
          }
          break;

        case 'realtimeConnectionStatus':
          console.log('WebView: Received connection status:', message.status, message.message);
          if (realtimeTranscriptRef.current) {
            realtimeTranscriptRef.current.handleConnectionStatus(message.status, message.message);
          }
          break;

        case 'gracePeriodStatus':
          console.log('WebView: Received grace period status:', message.isActive, message.remainingSeconds);
          if (realtimeTranscriptRef.current) {
            realtimeTranscriptRef.current.handleGracePeriodStatus(message.isActive, message.remainingSeconds);
          }
          break;

        case 'initialConfiguration':
          if (message.options) {
            console.log('Received initial configuration:', {
              ...message.options,
              apiKey: message.options.apiKey ? '(present)' : '(not set)'
            });

            const { service: newService, model: newModel, language: newLanguage,
              customPrompt: newPrompt, promptMode: newPromptMode, optimize: newOptimize, optimizationModel: newOptimizationModel, translate: newTranslate,
              realtime: newRealtime, apiKey: newApiKey, audioDevice: newAudioDevice, sampleRate: newSampleRate } = message.options;

            // Set all initial values directly without checking timestamps
            if (newService !== undefined) {
              console.log(`Setting service to: ${newService} (was: ${service})`);
              setService(newService);
            }
            if (newModel !== undefined) {
              console.log(`Setting model to: ${newModel} (was: ${model})`);
              setModel(newModel);
            }
            if (newLanguage !== undefined) {
              console.log(`Setting language to: ${newLanguage} (was: ${language})`);
              setLanguage(newLanguage);
            }
            if (newPrompt !== undefined) {
              console.log(`Setting custom prompt (length: ${newPrompt.length})`);
              setCustomPrompt(newPrompt);
            }
            if (newPromptMode !== undefined) {
              console.log(`Setting prompt mode to: ${newPromptMode} (was: ${promptMode})`);
              setPromptMode(newPromptMode);
            }
            if (newOptimize !== undefined) {
              console.log(`Setting optimize to: ${newOptimize} (was: ${optimizeEnabled})`);
              setOptimizeEnabled(newOptimize);
            }
            if (newTranslate !== undefined) {
              console.log(`Setting translate to: ${newTranslate} (was: ${translate})`);
              setTranslate(newTranslate);
            }
            if (newRealtime !== undefined) {
              console.log(`Setting realtime to: ${newRealtime} (was: ${realtime})`);
              setRealtime(newRealtime);
            }
            if (newOptimizationModel !== undefined) {
              console.log(`Setting optimization model to: ${newOptimizationModel} (was: ${optimizationModel})`);
              setOptimizationModel(newOptimizationModel);
            }
            if (newApiKey !== undefined) {            console.log('Setting API key:', newApiKey ? 'present' : 'not set');
            setApiKey(newApiKey);
            stateRef.current.apiKey = newApiKey;
            
            // Update authentication state based on API key presence
            setIsAuthenticated(!!newApiKey);
          }
            if (newAudioDevice !== undefined) {
              console.log('Setting audio device:', newAudioDevice);
              setDeviceId(newAudioDevice);
            }
            if (newSampleRate !== undefined) {
              console.log(`Setting sample rate to: ${newSampleRate} (was: ${sampleRate})`);
              setSampleRate(newSampleRate);
            }

            // Save AssemblyAI model if appropriate
            if (newService === 'assemblyai' && newModel !== undefined) {
              setLastAssemblyAIModel(newModel);
            }

            // Mark initial config as loaded
            setInitialConfigLoaded(true);

            // Update the last update timestamps
            Object.keys(lastUpdates.current).forEach(key => {
              lastUpdates.current[key as keyof LastUpdates] = Date.now();
            });

            // Log the final state after all updates
            setTimeout(() => {
              console.log('WebView state after initialization:');
              console.log(`Service: ${service}`);
              console.log(`Model: ${model}`);
              console.log(`Language: ${language}`);
              console.log(`Optimize: ${optimizeEnabled}`);
              console.log(`Custom prompt length: ${customPrompt.length}`);
              console.log(`Translate: ${translate}`);
              console.log(`Realtime: ${realtime}`);
            }, 100);
          } else {
            console.warn('Received initialConfiguration message with no options');
          }
          break;

        case 'updateOptions':
          if (message.options) {
            console.log('Received updateOptions:', message.options);

            const { service: newService, model: newModel, language: newLanguage,
              customPrompt: newPrompt, promptMode: newPromptMode, optimize: newOptimize, optimizationModel: newOptimizationModel, translate: newTranslate,
              realtime: newRealtime, audioDevice: newAudioDevice, sampleRate: newSampleRate } = message.options;

            // Track if any updates were applied
            let configUpdated = false;

            // Add more aggressive updates for certain cases
            const forceUpdate = !initialConfigLoaded || message.force === true;

            // Log current state for comparison
            console.log('Current state before updateOptions:');
            console.log(`Service: ${service}, Model: ${model}, Language: ${language}`);
            console.log(`Optimize: ${optimizeEnabled}, Translate: ${translate}, Realtime: ${realtime}`);

            // Only update if the incoming change is not a result of a recent local change
            // or if we're forcing the update
            if (newService !== undefined) {
              if (forceUpdate || (Date.now() - lastUpdates.current.service > 1000)) {
                if (newService !== service) {
                  console.log(`Updating service from ${service} to ${newService}`);
                  setService(newService);
                  lastUpdates.current.service = Date.now();
                  configUpdated = true;
                }
              } else {
                console.log(`Skipping service update (${newService}) due to recent local update`);
              }
            }

            if (newModel !== undefined) {
              if (forceUpdate || (Date.now() - lastUpdates.current.model > 1000)) {
                if (newModel !== model) {
                  console.log(`Updating model from ${model} to ${newModel}`);
                  setModel(newModel);
                  lastUpdates.current.model = Date.now();
                  configUpdated = true;

                  // Also update lastAssemblyAIModel if appropriate
                  if (newService === 'assemblyai' || service === 'assemblyai') {
                    setLastAssemblyAIModel(newModel);
                  }
                }
              } else {
                console.log(`Skipping model update (${newModel}) due to recent local update`);
              }
            }

            if (newLanguage !== undefined) {
              if (forceUpdate || (Date.now() - lastUpdates.current.language > 1000)) {
                if (newLanguage !== language) {
                  console.log(`Updating language from ${language} to ${newLanguage}`);
                  setLanguage(newLanguage);
                  lastUpdates.current.language = Date.now();
                  configUpdated = true;
                }
              } else {
                console.log(`Skipping language update (${newLanguage}) due to recent local update`);
              }
            }

            if (newPrompt !== undefined) {
              if (forceUpdate || (Date.now() - lastUpdates.current.customPrompt > 1000)) {
                console.log(`Updating custom prompt (length: ${newPrompt.length})`);
                setCustomPrompt(newPrompt);
                lastUpdates.current.customPrompt = Date.now();
                configUpdated = true;
              } else {
                console.log(`Skipping custom prompt update due to recent local update`);
              }
            }

            if (newPromptMode !== undefined) {
              if (forceUpdate || (Date.now() - lastUpdates.current.promptMode > 1000)) {
                if (newPromptMode !== promptMode) {
                  console.log(`Updating prompt mode from ${promptMode} to ${newPromptMode}`);
                  setPromptMode(newPromptMode);
                  lastUpdates.current.promptMode = Date.now();
                  configUpdated = true;
                }
              } else {
                console.log(`Skipping prompt mode update (${newPromptMode}) due to recent local update`);
              }
            }

            if (newOptimize !== undefined) {
              if (forceUpdate || (Date.now() - lastUpdates.current.optimize > 1000)) {
                if (newOptimize !== optimizeEnabled) {
                  console.log(`Updating optimize from ${optimizeEnabled} to ${newOptimize}`);
                  setOptimizeEnabled(newOptimize);
                  lastUpdates.current.optimize = Date.now();
                  configUpdated = true;
                }
              } else {
                console.log(`Skipping optimize update (${newOptimize}) due to recent local update`);
              }
            }

            if (newOptimizationModel !== undefined) {
              if (forceUpdate || (Date.now() - lastUpdates.current.optimizationModel > 1000)) {
                if (newOptimizationModel !== optimizationModel) {
                  console.log(`Updating optimization model from ${optimizationModel} to ${newOptimizationModel}`);
                  setOptimizationModel(newOptimizationModel);
                  lastUpdates.current.optimizationModel = Date.now();
                  configUpdated = true;
                }
              } else {
                console.log(`Skipping optimization model update (${newOptimizationModel}) due to recent local update`);
              }
            }

            if (newTranslate !== undefined) {
              if (forceUpdate || (Date.now() - lastUpdates.current.translate > 1000)) {
                if (newTranslate !== translate) {
                  console.log(`Updating translate from ${translate} to ${newTranslate}`);
                  setTranslate(newTranslate);
                  lastUpdates.current.translate = Date.now();
                  configUpdated = true;
                }
              } else {
                console.log(`Skipping translate update (${newTranslate}) due to recent local update`);
              }
            }

            if (newRealtime !== undefined) {
              if (forceUpdate || (Date.now() - lastUpdates.current.realtime > 1000)) {
                if (newRealtime !== realtime) {
                  console.log(`Updating realtime from ${realtime} to ${newRealtime}`);
                  setRealtime(newRealtime);
                  lastUpdates.current.realtime = Date.now();
                  configUpdated = true;
                }
              } else {
                console.log(`Skipping realtime update (${newRealtime}) due to recent local update`);
              }
            }

            if (newAudioDevice !== undefined) {
              if (forceUpdate || (Date.now() - lastUpdates.current.deviceId > 1000)) {
                if (newAudioDevice !== deviceId) {
                  console.log(`Updating audio device from ${deviceId} to ${newAudioDevice}`);
                  setDeviceId(newAudioDevice);
                  lastUpdates.current.deviceId = Date.now();
                  configUpdated = true;
                }
              } else {
                console.log(`Skipping audio device update (${newAudioDevice}) due to recent local update`);
              }
            }

            if (newSampleRate !== undefined) {
              if (forceUpdate || (Date.now() - lastUpdates.current.sampleRate > 1000)) {
                if (newSampleRate !== sampleRate) {
                  console.log(`Updating sample rate from ${sampleRate} to ${newSampleRate}`);
                  setSampleRate(newSampleRate);
                  lastUpdates.current.sampleRate = Date.now();
                  configUpdated = true;
                }
              } else {
                console.log(`Skipping sample rate update (${newSampleRate}) due to recent local update`);
              }
            }

            // If any config was updated and this is the initial load, mark as loaded
            if (!initialConfigLoaded && configUpdated) {
              console.log('Initial configuration loaded via updateOptions');
              setInitialConfigLoaded(true);
            }

            // Log if anything was updated
            if (configUpdated) {
              console.log('Configuration updated successfully');
            } else {
              console.log('No configuration changes applied');
            }
          } else {
            console.warn('Received updateOptions message with no options');
          }
          break;

        case 'recordingState':
          console.log('[WebView] [DEBUG] Received recordingState message:', message);
          console.log('[WebView] [DEBUG] Current recording state before update:', { isRecording, isPaused, elapsedTime });

          if (message.isRecording !== undefined) {
            console.log(`[WebView] [DEBUG] Updating isRecording from ${isRecording} to ${message.isRecording}`);
            setIsRecording(message.isRecording);
          }
          if (message.isPaused !== undefined) {
            console.log(`[WebView] [DEBUG] Updating isPaused from ${isPaused} to ${message.isPaused}`);
            setIsPaused(message.isPaused);
          }
          if (message.elapsedTime !== undefined) {
            console.log(`[WebView] [DEBUG] Updating elapsedTime from ${elapsedTime} to ${message.elapsedTime}`);
            setElapsedTime(message.elapsedTime);
          }
          break;

        case 'updateTranscriptions':
          if (message.transcriptions) {
            console.log(`WebView: Received ${message.transcriptions.length} transcriptions from extension`);
            console.log('WebView: Current transcriptions count before update:', transcriptions.length);

            if (message.transcriptions.length === 0) {
              console.log('WebView: Received empty transcriptions array - this should clear the history');
            } else if (message.transcriptions.length > 0) {
              // Log some details about the most recent transcription for debugging
              const latest = message.transcriptions[0]; // transcriptions come in reverse order (newest first)
              console.log('WebView: Most recent transcription:', {
                id: latest.id,
                timestamp: latest.timestamp,
                originalTextLength: latest.originalText?.length || 0,
                hasOptimized: !!latest.optimizedText,
                service: latest.service
              });
            }

            setTranscriptions(message.transcriptions);
            console.log('WebView: Transcription state updated to', message.transcriptions.length, 'items');
          } else {
            console.warn('WebView: Received updateTranscriptions message with no transcriptions data');
          }
          break;

        case 'optimizationComplete':
          if (message.id && message.optimizedText) {
            setTranscriptions((prev) =>
              prev.map((t) =>
                t.id === message.id
                  ? Object.assign({}, t, {optimizedText: message.optimizedText, isPreview: message.isPreview || false})
                  : t
              )
            );
          }
          break;
          
        case 'previewOptimizationComplete':
          if (message.id && message.optimizedText) {
            setTranscriptions((prev) =>
              prev.map((t) =>
                t.id === message.id
                  ? Object.assign({}, t, {optimizedText: message.optimizedText, isPreview: true})
                  : t
              )
            );
          }
          break;

        case 'transcriptionDeleted':
          if (message.id) {
            setTranscriptions((prev) => prev.filter((t) => t.id !== message.id));
          }
          break;

        case 'allTranscriptionsCleared':
          console.log('WebView: Received allTranscriptionsCleared message');
          console.log('WebView: Current transcriptions count before clearing:', transcriptions.length);
          setTranscriptions([]);
          console.log('WebView: Transcriptions cleared');
          break;
          
        // case 'authStatusUpdated':
        //   console.log('Auth status updated:', message.authenticated ? 'Authenticated' : 'Not authenticated');
          
        //   // If there's an error in the auth status, log it
        //   if (message.error) {
        //     console.error('Authentication error:', message.error);
        //   }
          
        //   setIsAuthenticated(message.authenticated || false);
          
        //   // If authenticated, we likely have an API key, so update that too
        //   if (message.authenticated && message.apiKey) {
        //     console.log('Setting API key from authStatusUpdated message');
        //     setApiKey(message.apiKey);
        //   }
          
        //   // Always turn off loading state when auth status is updated
        //   console.log('Turning off loading state after authStatusUpdated');
        //   setIsLoading(false);
        //   break;

        case 'apiKeyResponse':
          console.log('Received API key response:', message.apiKey ? 'API key present' : 'No API key');
          console.log('Authentication status from apiKeyResponse:', message.authenticated ? 'Authenticated' : 'Not authenticated');
          // Set the API key and authentication state
          setApiKey(message.apiKey || '');
          // Use the auth status from the response if available, otherwise infer from API key presence
          setIsAuthenticated(message.authenticated !== undefined ? message.authenticated : !!message.apiKey);
          // Turn off loading state
          console.log('Turning off loading state after apiKeyResponse');
          setIsLoading(false);
          break;

        case 'error':
          // Handle error messages from the extension
          console.error('Error from extension:', message.message);
          // You could show an error indicator in the UI here if needed
          break;

        case 'updateAudioDevices':
          if (message.devices) {
            setAvailableDevices(message.devices);
          }
          break;

        case 'updateAudioSettings':
          if (message.settings) {
            const { sampleRate: newRate, device: newDevice } = message.settings;

            // Only update the sample rate if it's related to real-time mode
            if (newRate === 16000 && realtime === true) {
              console.log('[WebView] Updating sample rate for real-time mode:', newRate);
              setSampleRate(newRate);
            } else if (newRate !== 16000 && realtime === false && previousSampleRate.current) {
              console.log('[WebView] Restoring previous sample rate after real-time mode:', previousSampleRate.current);
              setSampleRate(previousSampleRate.current);
            }

            // Only update the device ID during initial configuration
            if (newDevice !== undefined && !initialConfigLoaded) {
              console.log('[WebView] Initial setting of device ID from backend:', newDevice);
              setDeviceId(newDevice);
            }
          }
          break;

        case 'settings':
          if (message.voiceCommandsEnabled !== undefined) {
            setVoiceCommandsEnabled(message.voiceCommandsEnabled);
          }
          break;

        case 'debug':
          // Handle debug messages from the extension
          console.log('Debug from extension:', message.message);
          break;

        case 'authStateChanged':
          if (message.isAuthenticated !== undefined) {
            console.log('[WebView] Updating authentication state:', message.isAuthenticated);
            setIsAuthenticated(message.isAuthenticated);
          }
          if (message.apiKey !== undefined) {
            console.log('[WebView] Updating API key from authentication state change');
            setApiKey(message.apiKey);
            stateRef.current.apiKey = message.apiKey;
          }
          break;

        // Configuration management cases
        case 'configurationsResponse':
        case 'configurationsUpdated':
          if (message.data?.configurations) {
            setConfigurations(message.data.configurations);
          } else if (message.configurations) {
            setConfigurations(message.configurations);
          }
          if (message.data?.activeConfigurationId !== undefined) {
            setActiveConfigurationId(message.data.activeConfigurationId);
          } else if (message.activeConfigurationId !== undefined) {
            setActiveConfigurationId(message.activeConfigurationId);
          }
          break;

        case 'configurationCreated':
          if (message.configuration) {
            setConfigurations(prev => [...prev, message.configuration]);
            showSnackbar(`Configuration "${message.configuration.title}" created successfully`, 'success');
          }
          break;

        case 'configurationUpdated':
          if (message.configuration) {
            setConfigurations(prev =>
              prev.map(config =>
                config.id === message.configuration.id ? message.configuration : config
              )
            );
            showSnackbar(`Configuration "${message.configuration.title}" updated successfully`, 'success');
          }
          break;

        case 'configurationDeleted':
          if (message.configId) {
            setConfigurations(prev => prev.filter(config => config.id !== message.configId));
            showSnackbar('Configuration deleted successfully', 'success');
          }
          break;

        case 'configurationSwitched':
          if (message.configId && message.configName) {
            setActiveConfigurationId(message.configId);
            showSnackbar(`Switched to "${message.configName}"`, 'success');
          }
          break;

        case 'whatsNewStatus':
          if (message.shouldShow) {
            setShowWhatsNewModal(true);
          }
          break;

      }
    };

    window.addEventListener('message', handleMessage);

    // Request initial state from extension
    vscode.postMessage({ command: 'getOptions' });
    vscode.postMessage({ command: 'getTranscriptions' });

    // Request audio settings

    // Request theme and configuration refresh
    vscode.postMessage({ command: 'refreshConfigAndTheme' });

    // Set up a timeout to retry if initial config hasn't loaded
    const configTimeout = setTimeout(() => {
      if (!initialConfigLoaded) {
        console.log('WebView: Initial config not loaded after 5 seconds, retrying...');
        vscode.postMessage({ command: 'refreshConfigAndTheme' });
      }
    }, 5000); // 5 seconds timeout

    return () => {
      window.removeEventListener('message', handleMessage);
      clearTimeout(configTimeout);
    };
  }, [updateStateIfNewer, initialConfigLoaded, service, model, language, optimizeEnabled, optimizationModel, translate, realtime, customPrompt, customPrompt.length, sampleRate, deviceId, apiKey, transcriptions.length, isRecording, isPaused, elapsedTime]);

  // Initialize configurations and check what's new status
  useEffect(() => {
    if (initialConfigLoaded) {
      // Request configurations
      vscode.postMessage({ command: 'getConfigurations' });

      // Check what's new status
      vscode.postMessage({ command: 'getWhatsNewStatus', version: '1.4.1' });
    }
  }, [initialConfigLoaded]);

  // What's New modal handlers
  const handleWhatsNewClose = useCallback(() => {
    setShowWhatsNewModal(false);
    vscode.postMessage({ command: 'markWhatsNewSeen', version: '1.4.1' });
  }, []);

  const handleWhatsNewRemindLater = useCallback(() => {
    setShowWhatsNewModal(false);
    // Don't mark as seen, so it will show again later
  }, []);

  // Memoized handlers to avoid recreating functions on every render
  const handleStartRecording = useCallback((): void => {
    console.log('[WebView] [DEBUG] Start recording called, current state:', { isRecording, isPaused });
    vscode.postMessage({
      command: 'startRecording',
      options: {
        service,
        model,
        language,
        optimize: optimizeEnabled,
        optimizationModel: optimizeEnabled ? optimizationModel : '',
        customPrompt: optimizeEnabled ? customPrompt : '',
        translate,
        realtime
      }
    });
    // Optimistically update UI state
    console.log('[WebView] [DEBUG] Optimistically updating UI state to recording');
    setIsRecording(true);
    setIsPaused(false);
  }, [service, model, language, optimizeEnabled, optimizationModel, customPrompt, translate, realtime, isRecording, isPaused]);

  const handleStopRecording = useCallback((): void => {
    console.log('[WebView] [DEBUG] Stop recording called, current state:', { isRecording, isPaused });
    vscode.postMessage({ command: 'stopRecording' });
    // Optimistically update UI state
    console.log('[WebView] [DEBUG] Optimistically updating UI state to stopped');
    setIsRecording(false);
    setIsPaused(false);
  }, [isRecording, isPaused]);

  const handlePauseRecording = useCallback((): void => {
    console.log('[WebView] [DEBUG] Pause recording called, current state:', { isRecording, isPaused });
    vscode.postMessage({ command: 'pauseRecording' });
    // Optimistically update UI state
    console.log('[WebView] [DEBUG] Optimistically updating UI state to paused');
    setIsPaused(true);
  }, [isRecording, isPaused]);

  const handleResumeRecording = useCallback((): void => {
    console.log('[WebView] [DEBUG] Resume recording called, current state:', { isRecording, isPaused });
    vscode.postMessage({ command: 'resumeRecording' });
    // Optimistically update UI state
    console.log('[WebView] [DEBUG] Optimistically updating UI state to resumed');
    setIsPaused(false);
  }, [isRecording, isPaused]);

  const handleCancelRecording = useCallback((): void => {
    console.log('[WebView] [DEBUG] Cancel recording called, current state:', { isRecording, isPaused });
    vscode.postMessage({ command: 'cancelRecording' });
    // Optimistically update UI state
    console.log('[WebView] [DEBUG] Optimistically updating UI state to cancelled');
    setIsRecording(false);
    setIsPaused(false);
  }, [isRecording, isPaused]);

  // Handlers for configuration changes
  const handleServiceChange = useCallback((value: string): void => {
    // Record the timestamp of this update
    lastUpdates.current.service = Date.now();

    console.log(`WebView: Service change requested from ${service} to ${value}`);
    console.log(`WebView: Current model before change: ${model}`);
    console.log(`WebView: Current language before change: ${language}`);

    // Automatically set the appropriate model based on service
    let newModel = model;
    let newLanguage = language;
    let updateLanguage = false;

    if (value === 'lemonfox') {
      // Save current AssemblyAI model if we're switching from it
      if (service === 'assemblyai') {
        console.log(`WebView: Saving AssemblyAI model ${model} for future use`);
        setLastAssemblyAIModel(model);
      }
      newModel = 'whisper-1';
      console.log(`WebView: Setting model to ${newModel} for OpenAI service`);

      // Record model change timestamp
      lastUpdates.current.model = Date.now();

      // For OpenAI, we accept many languages - no need to update
    } else if (value === 'assemblyai') {
      // Restore the last used AssemblyAI model
      newModel = lastAssemblyAIModel;
      console.log(`WebView: Restoring AssemblyAI model to ${newModel}`);

      // Record model change timestamp
      lastUpdates.current.model = Date.now();

      // Check language compatibility for AssemblyAI - set to a limited subset of the most common ones
      // The backend will handle the full validation against the actual model-specific lists
      const commonAssemblyAILanguages = ['en', 'es', 'fr', 'de', 'it', 'pt', 'nl', 'hi', 'ja', 'zh', 'ar'];
      if (language === 'auto' || !commonAssemblyAILanguages.includes(language)) {
        console.log(`WebView: Language ${language} not supported by AssemblyAI, changing to en`);
        newLanguage = 'en';
        updateLanguage = true;

        // Record language change timestamp
        lastUpdates.current.language = Date.now();
      }

      // If switching to AssemblyAI, disable translate
      if (translate) {
        console.log(`WebView: Disabling translate for AssemblyAI service`);
        // Record translate change timestamp
        lastUpdates.current.translate = Date.now();
        setTranslate(false);
      }
    }

    // Update local state immediately
    setService(value);
    setModel(newModel);

    // Update language if needed
    if (updateLanguage) {
      setLanguage(newLanguage);
    }

    // Notify VS Code of the changes
    vscode.postMessage({
      command: 'updateOptions',
      options: {
        service: value,
        model: newModel,
        ...(updateLanguage ? { language: newLanguage } : {}),
        ...(value === 'assemblyai' && translate ? { translate: false } : {})
      }
    });

    console.log(`WebView: Service changed to ${value}, model set to ${newModel}, language: ${updateLanguage ? newLanguage : 'unchanged'}`);
  }, [service, model, language, lastAssemblyAIModel, translate]);

  const handleModelChange = useCallback((value: string): void => {
    // Record the timestamp of this update
    lastUpdates.current.model = Date.now();

    // Update state immediately
    setModel(value);

    // Notify VS Code of the changes
    vscode.postMessage({
      command: 'updateOptions',
      options: { model: value }
    });
  }, []);

  const handleLanguageChange = useCallback((value: string): void => {
    // Record the timestamp of this update
    lastUpdates.current.language = Date.now();

    // Update state immediately
    setLanguage(value);

    // Notify VS Code of the changes
    vscode.postMessage({
      command: 'updateOptions',
      options: { language: value }
    });
  }, []);

  // Debounced version of handlePromptChange to prevent too many updates
  const handlePromptChange = useCallback((value: string): void => {
    // Record the timestamp of this update
    lastUpdates.current.customPrompt = Date.now();

    // Update state immediately
    setCustomPrompt(value);

    // Use a debounce mechanism to avoid sending too many updates
    // Clear any existing timeout
    if (window.promptUpdateTimeout) {
      clearTimeout(window.promptUpdateTimeout);
    }

    // Set a new timeout to update after 500ms of inactivity
    window.promptUpdateTimeout = setTimeout(() => {
      // Notify VS Code of the changes
      vscode.postMessage({
        command: 'updateOptions',
        options: { customPrompt: value }
      });
      console.log(`Custom prompt updated (debounced): ${value.substring(0, 30)}${value.length > 30 ? '...' : ''}`);
    }, 500);
  }, []);

  // Add cleanup for the timeout
  useEffect(() => {
    return () => {
      if (window.promptUpdateTimeout) {
        clearTimeout(window.promptUpdateTimeout);
      }
    };
  }, []);

  const handleOptimizeToggle = useCallback((value: boolean): void => {
    // Record the timestamp of this update
    lastUpdates.current.optimize = Date.now();

    // Update state immediately
    setOptimizeEnabled(value);

    // Notify VS Code of the changes
    vscode.postMessage({
      command: 'updateOptions',
      options: { optimize: value }
    });
  }, []);

  const handleTranslateToggle = useCallback((checked: boolean): void => {
    // Don't update if we're still loading initial config
    if (!initialConfigLoaded) return;

    setTranslate(checked);
    lastUpdates.current.translate = Date.now();
    vscode.postMessage({
      command: 'updateOptions',
      options: { translate: checked }
    });
  }, [initialConfigLoaded]);

  // Store the previous sample rate when toggling real-time
  const previousSampleRate = useRef<number>(0);

  const handleRealtimeToggle = useCallback((checked: boolean): void => {
    // Don't update if we're still loading initial config
    if (!initialConfigLoaded) return;

    console.log('[WebView] Toggling real-time transcription:', checked);

    // Update realtime state immediately
    setRealtime(checked);

    // Optimistically update the sample rate UI
    if (checked) {
      // Store current sample rate before switching to real-time
      previousSampleRate.current = sampleRate;
      console.log('[WebView] Storing current sample rate before real-time:', previousSampleRate.current);

      // Immediately set to 16000Hz for real-time mode
      setSampleRate(16000);
    } else if (previousSampleRate.current) {
      // Immediately restore previous sample rate when disabling real-time
      console.log('[WebView] Restoring sample rate after real-time:', previousSampleRate.current);
      setSampleRate(previousSampleRate.current);
    }

    // Notify backend
    vscode.postMessage({
      command: 'updateOptions',
      options: { realtime: checked }
    });
  }, [initialConfigLoaded, sampleRate]);

  const handleOptimizationModelChange = useCallback((value: string): void => {
    // Record the timestamp of this update
    lastUpdates.current.optimizationModel = Date.now();

    // Update state immediately
    setOptimizationModel(value);

    // Notify VS Code of the changes
    vscode.postMessage({
      command: 'updateOptions',
      options: { optimizationModel: value }
    });

    console.log(`WebView: Optimization model changed to ${value}`);
  }, []);

  // Handler for voice commands toggle
  const handleVoiceCommandsToggle = useCallback((enabled: boolean): void => {
    // Record the timestamp of this update
    lastUpdates.current.voiceCommands = Date.now();

    console.log(`WebView: Voice commands ${enabled ? 'enabled' : 'disabled'}`);
    setVoiceCommandsEnabled(enabled);

    // Update the extension configuration
    vscode.postMessage({
      command: 'updateOptions',
      options: { voiceCommandsEnabled: enabled }
    });
  }, []);

  // Handlers for transcriptions
  const handleCopyOriginal = useCallback((id: string): void => {
    const transcription = transcriptions.find((t: Transcription) => t.id === id);
    if (transcription) {
      vscode.postMessage({
        command: 'copyToClipboard',
        text: transcription.originalText
      });
    }
  }, [transcriptions]);

  const handleCopyOptimized = useCallback((id: string): void => {
    const transcription = transcriptions.find((t: Transcription) => t.id === id);
    if (transcription && transcription.optimizedText) {
      vscode.postMessage({
        command: 'copyToClipboard',
        text: transcription.optimizedText
      });
    }
  }, [transcriptions]);

  // Preview optimization without saving
  const handlePreviewOptimization = useCallback((id: string, preview: boolean): void => {
    const transcription = transcriptions.find((t: Transcription) => t.id === id);
    if (transcription && preview) {
      // Send message to extension for preview only optimization
      vscode.postMessage({
        command: 'previewOptimization',
        id: id,
        text: transcription.originalText,
        customPrompt: customPrompt,
        optimizationModel: optimizationModel
      });

      // Optimistically show a loading state in preview mode
      setTranscriptions(prev =>
        prev.map(t =>
          t.id === id
            ? Object.assign({}, t, {optimizedText: 'Previewing optimization...', isPreview: true})
            : t
        )
      );
    } else if (!preview) {
      // If canceling preview, remove the optimized text if it's a preview
      setTranscriptions(prev =>
        prev.map(t =>
          t.id === id && t.isPreview
            ? Object.assign({}, t, {optimizedText: undefined, isPreview: false})
            : t
        )
      );
    }
  }, [transcriptions, customPrompt, optimizationModel]);

  const handleOptimize = useCallback((id: string): void => {
    const transcription = transcriptions.find((t: Transcription) => t.id === id);
    if (transcription) {
      vscode.postMessage({
        command: 'optimizeTranscription',
        id: id,
        text: transcription.originalText,
        customPrompt: customPrompt,
        optimizationModel: optimizationModel
      });

      // Optimistically show a loading state
      setTranscriptions(prev =>
        prev.map(t =>
          t.id === id
            ? Object.assign({}, t, {optimizedText: 'Optimizing...', isPreview: false})
            : t
        )
      );
    }
  }, [transcriptions, customPrompt, optimizationModel]);

  // Audio settings handlers
  const handleSampleRateChange = useCallback((newRate: number) => {
    console.log('[WebView] Changing sample rate to:', newRate);

    // Update local state immediately (optimistic update)
    setSampleRate(newRate);

    // Notify backend
    vscode.postMessage({
      command: 'updateSetting',
      key: 'voicehype.audio.sampleRate',
      value: newRate
    });
  }, []);

  const handleDeviceChange = useCallback((deviceId: string | null): void => {
    // Update local state first
    setDeviceId(deviceId);

    // Update timestamp to prevent backend update from overriding
    lastUpdates.current.deviceId = Date.now();

    // Use a debounce mechanism to avoid sending too many updates
    // Clear any existing timeout
    if (window.deviceUpdateTimeout) {
      clearTimeout(window.deviceUpdateTimeout);
    }

    // Set a new timeout to update after 500ms of inactivity
    window.deviceUpdateTimeout = setTimeout(() => {
      // Convert null to empty string for the backend
      const valueToSend = deviceId === null ? '' : deviceId;

      // Notify backend using the correct command for audio device changes
      vscode.postMessage({
        command: 'updateAudioDevice',
        deviceId: valueToSend
      });

      // Log the device ID for debugging
      console.log(`[WebView] Sending audio device change to backend (debounced): ${valueToSend}`);
    }, 500);
  }, []);

  // Add cleanup for the device update timeout
  useEffect(() => {
    return () => {
      if (window.deviceUpdateTimeout) {
        clearTimeout(window.deviceUpdateTimeout);
      }
    };
  }, []);

  return (
    <div className="sm:px-0 flex flex-col h-full px-1">
      {isLoading ? (
        <LoadingScreen />
      ) : !isAuthenticated && !apiKey ? (
        <OnboardingScreen apiKey={apiKey} />
      ) : (
        console.log('App: Rendering main view - isAuthenticated:', isAuthenticated, 'apiKey:', apiKey ? 'present' : 'not set'),
        <>
          {/* VoiceHype Logo Header */}
          <div className="flex items-center justify-center py-4 mb-2">
            <Logo />
          </div>

          {/* API Key Input - Always visible on main screen */}
          <div className="mb-4">
            <ApiKeyInput
              initialApiKey={apiKey}
              onApiKeySaved={(newApiKey) => {
                setApiKey(newApiKey);
                setIsAuthenticated(true);
              }}
            />
          </div>

          {/* Configuration Manager Button */}
          {/* <div className="flex justify-end mb-4">
            <button
              onClick={() => setShowConfigurationManager(true)}
              className="dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 dark:border-gray-600 flex items-center gap-2 px-3 py-2 text-sm text-gray-700 transition-colors border border-gray-200 rounded-md"
              title="Manage Configurations"
            >
              <CogIcon className="w-4 h-4" />
              Configurations
            </button>
          </div> */}

          {/* Recording controls */}
          <div className="mb-5">
            <RecordingControls
              isRecording={isRecording}
              isPaused={isPaused}
              elapsedTime={elapsedTime}
              onStart={handleStartRecording}
              onStop={handleStopRecording}
              onPause={handlePauseRecording}
              onResume={handleResumeRecording}
              onCancel={handleCancelRecording}
            />
          </div>

          {/* Real-time transcript - only show when real-time is enabled */}
          {realtime && (
            <div className="mb-5">
              <BeautifulRealtimeTranscript
                ref={realtimeTranscriptRef}
                isVisible={realtime}
                isRecording={isRecording}
              />
            </div>
          )}

      {/* Service and model settings */}
      <div className="mb-4 space-y-4">
        <ServiceSelector
          service={service}
          onChange={handleServiceChange}
        />

        <ModelSelector
          service={service}
          model={model}
          onChange={handleModelChange}
        />

        <LanguageSelector
          service={service}
          model={model}
          language={language}
          onChange={handleLanguageChange}
        />
      </div>

      {/* Audio Settings  */}
      <div className="mb-4">
        <AudioSettings
          sampleRate={sampleRate}
          deviceId={deviceId}
          availableDevices={availableDevices}
          onSampleRateChange={handleSampleRateChange}
          onDeviceChange={handleDeviceChange}
        />
      </div>

      {/* Optimization toggle (already present in RecordingControls) */}
      {/* Organized next to each other for related functionality */}
      <div className="mb-4 space-y-2">
        {/* Translate toggle - only show when OpenAI is selected */}
        {service === 'lemonfox' && (
          <Switch
            checked={translate}
            onChange={handleTranslateToggle}
            label="Translate to English"
          />
        )}

        {/* Real-time toggle - only show for AssemblyAI best model */}
        {service === 'assemblyai' && model === 'best' && (
          <Switch
            checked={realtime}
            onChange={handleRealtimeToggle}
            label="Use real-time transcription"
          />
        )}

        {/* Optimize toggle - moved from RecordingControls */}
        <Switch
          checked={optimizeEnabled}
          onChange={handleOptimizeToggle}
          label="Optimize transcription"
        />

        {/* Voice Commands toggle */}
        {/* <Switch
          checked={voiceCommandsEnabled}
          onChange={handleVoiceCommandsToggle}
          label="Enable voice commands"
        /> */}
      </div>

      {/* Optimization Mode & Model Selector */}
      {optimizeEnabled && (
        <div className="mb-4 space-y-4">
          <div>
            <h2 className="text-md mb-2 font-medium">Optimization Mode</h2>
            <PromptModeSelector
              value={customPrompt}
              onChange={handlePromptChange}
              onModeChange={(modeId) => {
                // Update the prompt mode state
                setPromptMode(modeId);
                // Store the active mode ID if needed
                vscode.postMessage({
                  command: 'setActivePromptMode',
                  modeId
                });
              }}
            />
          </div>
          <div>
            <h2 className="text-md mb-2 font-medium">Optimization Model</h2>
            <OptimizationModelSelector
              optimizationModel={optimizationModel}
              onChange={handleOptimizationModelChange}
            />
          </div>
        </div>
      )}

      {/* Optimization Prompt - now handled inside PromptModeSelector */}


      {/* Transcription History */}
      <div className="flex-grow overflow-hidden">
        <h2 className="text-md mb-2 font-medium">Transcription History</h2>
        <div className="h-[calc(100%-1.75rem)]">            <RecentTranscriptions
              transcriptions={transcriptions}
              onCopyOriginal={handleCopyOriginal}
              onCopyOptimized={handleCopyOptimized}
              onOptimize={handleOptimize}
              onPreviewOptimization={handlePreviewOptimization}
            />
        </div>
      </div>

      {/* Configuration Manager Modal */}
      {showConfigurationManager && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-4 border-b">
              <h2 className="text-xl font-semibold">Configuration Manager</h2>
              <button
                onClick={() => setShowConfigurationManager(false)}
                className="hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 text-gray-500"
              >
                ✕
              </button>
            </div>
            <div className="overflow-y-auto max-h-[calc(90vh-4rem)]">
              <ConfigurationManager
                configurations={configurations}
                activeConfigurationId={activeConfigurationId}
                currentSettings={getCurrentSettings()}
                onCreateConfiguration={handleCreateConfiguration}
                onUpdateConfiguration={handleUpdateConfiguration}
                onDeleteConfiguration={handleDeleteConfiguration}
                onSwitchConfiguration={handleSwitchConfiguration}
              />
            </div>
          </div>
        </div>
      )}

      {/* Snackbar */}
      <Snackbar
        message={snackbar.message}
        type={snackbar.type}
        isVisible={snackbar.isVisible}
        onClose={hideSnackbar}
      />

      {/* What's New Modal */}
      <WhatsNewModal
        isOpen={showWhatsNewModal}
        onClose={handleWhatsNewClose}
        onRemindLater={handleWhatsNewRemindLater}
      />
      </>
      )}
    </div>
  );
};

export default App;


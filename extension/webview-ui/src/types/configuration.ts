// Configuration types for VoiceHype
export interface ConfigurationSettings {
    service: string;
    model: string;
    language: string;
    translate: boolean;
    realtime: boolean;
    optimizeEnabled: boolean;
    optimizationModel: string;
    customPrompt: string;
    promptMode: string;
    sampleRate: number;
    deviceId: string | null;
}

export interface Configuration {
    id: string;
    title: string;
    description: string;
    settings: ConfigurationSettings;
    isDefault: boolean;
    createdAt: string;
    updatedAt: string;
}

export interface ConfigurationState {
    configurations: Configuration[];
    activeConfigurationId: string | null;
    isLoading: boolean;
    error: string | null;
}

// What's New feature constants
export const WHATS_NEW_141 = {
    version: '1.4.1',
    title: 'New Configuration Feature! 🎉',
    features: [
        'Create multiple configuration profiles for different workflows',
        'Quick switching with Ctrl+Shift+PageUp/PageDown shortcuts',
        'Save settings for English vs Urdu workflows',
        'WhatsApp-specific configurations',
        'Instant profile switching with snackbar notifications'
    ],
    shortcuts: [
        'Ctrl+Shift+PageUp - Switch to next configuration',
        'Ctrl+Shift+PageDown - Switch to previous configuration'
    ]
};

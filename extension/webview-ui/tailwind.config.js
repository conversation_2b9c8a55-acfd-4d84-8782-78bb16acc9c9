/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/**/*.{js,jsx,ts,tsx}',
    './public/index.html',
  ],
  darkMode: 'class', // Enable dark mode based on class
  theme: {
    extend: {
      colors: {
        // Light mode colors
        'primary': 'var(--brand-primary)',
        'secondary': 'var(--brand-secondary)',
        'accent': 'var(--brand-accent)',
        'background': '#ffffff',
        'surface': '#f9fafb',
        'text': '#111827',
        'muted': '#6b7280',
        'border': '#e5e7eb',
        
        // Dark mode colors
        'dark-primary': 'var(--brand-primary)',
        'dark-secondary': 'var(--brand-secondary)',
        'dark-accent': 'var(--brand-accent)',
        'dark-background': '#1f2937',
        'dark-surface': '#111827',
        'dark-text': '#f9fafb',
        'dark-muted': '#9ca3af',
        'dark-border': '#374151',
      },
      fontFamily: {
        sans: [
          'Inter',
          'ui-sans-serif',
          'system-ui',
          '-apple-system',
          'BlinkMacSystemFont',
          'Segoe UI',
          'Roboto',
          'Helvetica Neue',
          'Arial',
          'sans-serif',
        ],
      },
      gridTemplateRows: {
        'layout': 'auto 1fr auto',
      },
    },
  },
  plugins: [],
}


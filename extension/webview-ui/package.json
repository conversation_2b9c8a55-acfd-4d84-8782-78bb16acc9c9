{"name": "voicehype-webview-ui", "version": "0.1.0", "private": true, "scripts": {"start": "react-scripts start", "prebuild": "npx tailwindcss -i ./src/tailwind.css -o ./src/compiled.css --minify", "build": "npx cross-env NODE_OPTIONS=--openssl-legacy-provider CI=false react-scripts build", "build:prod": "node build-with-console-removal.js", "test": "react-scripts test", "eject": "react-scripts eject"}, "dependencies": {"@heroicons/react": "^2.2.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-scripts": "^3.0.1", "tailwindcss": "^3.4.17"}, "devDependencies": {"@types/node": "^16.18.11", "@types/react": "^18.3.19", "@types/react-dom": "^18.3.5", "@types/vscode-webview": "^1.57.1", "babel-plugin-transform-remove-console": "^6.9.4", "cross-env": "^7.0.3", "postcss-cli": "^11.0.1", "postcss-preset-env": "^10.1.5", "typescript": "^4.9.5"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}
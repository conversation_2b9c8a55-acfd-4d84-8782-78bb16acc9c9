(window.webpackJsonp=window.webpackJsonp||[]).push([[0],{12:function(e,t,a){},13:function(e,t,a){},14:function(e,t,a){"use strict";a.r(t);var n=a(0),o=a.n(n),r=a(3),i=(a(12),a(13),a(16)),s=a(17),l=a(18),c=a(19),m=a(20);var d=e=>{let{isRecording:t,isPaused:a,elapsedTime:n,onStart:r,onStop:d,onPause:u,onResume:g,onCancel:p}=e;return o.a.createElement("div",{className:"flex flex-col space-y-4"},o.a.createElement("div",{className:"flex items-center justify-center"},o.a.createElement("div",{className:"bg-surface dark:bg-dark-surface/80 border-primary/30 dark:border-primary/30 p-3 font-mono text-3xl border rounded-lg shadow-inner"},(e=>{const t=e%60;return`${Math.floor(e/60).toString().padStart(2,"0")}:${t.toString().padStart(2,"0")}`})(n))),o.a.createElement("div",{className:"flex items-center justify-center space-x-4"},t?o.a.createElement(o.a.Fragment,null,o.a.createElement("button",{className:"bg-primary hover:bg-primary-hover flex items-center justify-center w-12 h-12 text-black transition-colors rounded-full shadow-md",onClick:()=>{console.log("[RecordingControls] [DEBUG] Pause/Resume button clicked, current state:",{isRecording:t,isPaused:a}),a?(console.log("[RecordingControls] [DEBUG] Resuming recording"),g()):(console.log("[RecordingControls] [DEBUG] Pausing recording"),u())},"aria-label":a?"Resume recording":"Pause recording"},a?o.a.createElement(s.a,{className:"w-5 h-5"}):o.a.createElement(l.a,{className:"w-5 h-5"})),o.a.createElement("button",{className:"bg-secondary hover:bg-secondary-hover flex items-center justify-center w-12 h-12 text-white transition-colors rounded-full shadow-md",onClick:()=>{console.log("[RecordingControls] [DEBUG] Stop button clicked, current state:",{isRecording:t,isPaused:a}),d()},"aria-label":"Stop recording"},o.a.createElement(c.a,{className:"w-5 h-5"})),o.a.createElement("button",{className:"bg-accent hover:bg-accent-hover flex items-center justify-center w-12 h-12 text-black transition-colors rounded-full shadow-md",onClick:p,"aria-label":"Cancel recording"},o.a.createElement(m.a,{className:"w-5 h-5"}))):o.a.createElement("button",{className:"w-14 h-14 bg-primary hover:bg-primary-hover flex items-center justify-center text-black transition-colors rounded-full shadow-md",onClick:()=>{console.log("[RecordingControls] [DEBUG] Start button clicked, current state:",{isRecording:t,isPaused:a}),r()},"aria-label":"Start recording"},o.a.createElement(i.a,{className:"w-6 h-6"}))))};const u=Object(n.memo)(e=>{let{service:t,onChange:a}=e;const r=Object(n.useCallback)(e=>{t!==e&&a(e)},[t,a]);return o.a.createElement("div",{className:"flex gap-2",role:"radiogroup","aria-label":"Select transcription service"},[{id:"assemblyai",name:"AssemblyAI"},{id:"lemonfox",name:"Whisper"}].map(e=>o.a.createElement("button",{key:e.id,className:`flex-1 py-2.5 px-4 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-primary/50 dark:focus:ring-dark-primary/50\n            ${t===e.id?"bg-primary dark:bg-dark-primary text-black shadow-sm font-bold":"bg-gray-100 dark:bg-dark-surface hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 font-medium"}`,onClick:()=>r(e.id),role:"radio","aria-checked":t===e.id},e.name)))});u.displayName="ServiceSelector";var g=u,p=a(21);const b=Object(n.memo)(e=>{let{service:t,model:a,onChange:r}=e;const i=Object(n.useMemo)(()=>"assemblyai"===t?[{id:"best",name:"best",description:"High accuracy, optimized for various accents and domains"},{id:"nano",name:"nano",description:"Faster transcription with small quality tradeoff"}]:"lemonfox"===t?[{id:"whisper-1",name:"whisper-1",description:"High-quality speech recognition model"}]:[],[t]),s=Object(n.useMemo)(()=>i.find(e=>e.id===a),[i,a]),l=Object(n.useCallback)(e=>{const t=e.target.value;t!==a&&r(t)},[a,r]);return"lemonfox"===t?("whisper-1"!==a&&r("whisper-1"),o.a.createElement("div",{className:"space-y-3"})):o.a.createElement("div",{className:"space-y-3"},o.a.createElement("div",{className:"relative"},o.a.createElement("select",{className:"border-border dark:border-dark-border dark:bg-dark-surface text-text dark:text-dark-text focus:outline-none focus:ring-2 focus:ring-primary/50 dark:focus:ring-dark-primary/50 w-full px-3 py-2 bg-white border rounded-md appearance-none cursor-pointer",value:a,onChange:l,"aria-label":"Select model"},i.map(e=>o.a.createElement("option",{key:e.id,value:e.id},e.name))),o.a.createElement("div",{className:"absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none"},o.a.createElement("svg",{className:"text-muted dark:text-dark-muted w-4 h-4",fill:"none",viewBox:"0 0 20 20",stroke:"currentColor"},o.a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 7l3-3 3 3m0 6l-3 3-3-3"})))),(null===s||void 0===s?void 0:s.description)&&o.a.createElement("div",{className:"text-muted dark:text-dark-muted bg-gray-50 dark:bg-dark-surface/40 flex items-start p-3 text-sm rounded-md"},o.a.createElement(p.a,{className:"text-primary dark:text-dark-primary flex-shrink-0 w-5 h-5 mr-2"}),o.a.createElement("p",null,s.description)))});b.displayName="ModelSelector";var h=b,f=a(22);const v=Object(n.memo)(e=>{let{service:t,model:a,language:r,onChange:i}=e;const s=Object(n.useMemo)(()=>{const e=[{code:"en",name:"Global English"},{code:"en_au",name:"Australian English"},{code:"en_uk",name:"British English"},{code:"en_us",name:"US English"},{code:"es",name:"Spanish"},{code:"fr",name:"French"},{code:"de",name:"German"},{code:"it",name:"Italian"},{code:"pt",name:"Portuguese"},{code:"nl",name:"Dutch"},{code:"hi",name:"Hindi"},{code:"ja",name:"Japanese"},{code:"zh",name:"Chinese"},{code:"fi",name:"Finnish"},{code:"ko",name:"Korean"},{code:"pl",name:"Polish"},{code:"ru",name:"Russian"},{code:"tr",name:"Turkish"},{code:"uk",name:"Ukrainian"},{code:"vi",name:"Vietnamese"}],n=[{code:"en",name:"Global English"},{code:"en_au",name:"Australian English"},{code:"en_uk",name:"British English"},{code:"en_us",name:"American English"},{code:"es",name:"Spanish"},{code:"fr",name:"French"},{code:"de",name:"German"},{code:"it",name:"Italian"},{code:"pt",name:"Portuguese"},{code:"nl",name:"Dutch"},{code:"af",name:"Afrikaans"},{code:"sq",name:"Albanian"},{code:"am",name:"Amharic"},{code:"ar",name:"Arabic"},{code:"hy",name:"Armenian"},{code:"as",name:"Assamese"},{code:"az",name:"Azerbaijani"},{code:"ba",name:"Bashkir"},{code:"eu",name:"Basque"},{code:"be",name:"Belarusian"},{code:"bn",name:"Bengali"},{code:"bs",name:"Bosnian"},{code:"br",name:"Breton"},{code:"bg",name:"Bulgarian"},{code:"my",name:"Burmese"},{code:"ca",name:"Catalan"},{code:"zh",name:"Chinese"},{code:"hr",name:"Croatian"},{code:"cs",name:"Czech"},{code:"da",name:"Danish"},{code:"et",name:"Estonian"},{code:"fo",name:"Faroese"},{code:"fi",name:"Finnish"},{code:"gl",name:"Galician"},{code:"ka",name:"Georgian"},{code:"el",name:"Greek"},{code:"gu",name:"Gujarati"},{code:"ht",name:"Haitian"},{code:"ha",name:"Hausa"},{code:"haw",name:"Hawaiian"},{code:"hi",name:"Hindi"},{code:"hu",name:"Hungarian"},{code:"is",name:"Icelandic"},{code:"id",name:"Indonesian"},{code:"ja",name:"Japanese"},{code:"jv",name:"Javanese"},{code:"kn",name:"Kannada"},{code:"kk",name:"Kazakh"},{code:"km",name:"Khmer"},{code:"ko",name:"Korean"},{code:"lo",name:"Lao"},{code:"la",name:"Latin"},{code:"lv",name:"Latvian"},{code:"ln",name:"Lingala"},{code:"lt",name:"Lithuanian"},{code:"lb",name:"Luxembourgish"},{code:"mk",name:"Macedonian"},{code:"mg",name:"Malagasy"},{code:"ms",name:"Malay"},{code:"ml",name:"Malayalam"},{code:"mt",name:"Maltese"},{code:"mi",name:"Maori"},{code:"mr",name:"Marathi"},{code:"mn",name:"Mongolian"},{code:"ne",name:"Nepali"},{code:"no",name:"Norwegian"},{code:"nn",name:"Norwegian Nynorsk"},{code:"oc",name:"Occitan"},{code:"pa",name:"Panjabi"},{code:"ps",name:"Pashto"},{code:"fa",name:"Persian"},{code:"pl",name:"Polish"},{code:"ro",name:"Romanian"},{code:"ru",name:"Russian"},{code:"sa",name:"Sanskrit"},{code:"sr",name:"Serbian"},{code:"sn",name:"Shona"},{code:"sd",name:"Sindhi"},{code:"si",name:"Sinhala"},{code:"sk",name:"Slovak"},{code:"sl",name:"Slovenian"},{code:"so",name:"Somali"},{code:"su",name:"Sundanese"},{code:"sw",name:"Swahili"},{code:"sv",name:"Swedish"},{code:"tl",name:"Tagalog"},{code:"tg",name:"Tajik"},{code:"ta",name:"Tamil"},{code:"tt",name:"Tatar"},{code:"te",name:"Telugu"},{code:"th",name:"Thai"},{code:"bo",name:"Tibetan"},{code:"tr",name:"Turkish"},{code:"tk",name:"Turkmen"},{code:"uk",name:"Ukrainian"},{code:"ur",name:"Urdu"},{code:"uz",name:"Uzbek"},{code:"vi",name:"Vietnamese"},{code:"cy",name:"Welsh"},{code:"yi",name:"Yiddish"},{code:"yo",name:"Yoruba"}],o=[{code:"af",name:"Afrikaans"},{code:"ar",name:"Arabic"},{code:"hy",name:"Armenian"},{code:"az",name:"Azerbaijani"},{code:"be",name:"Belarusian"},{code:"bs",name:"Bosnian"},{code:"bg",name:"Bulgarian"},{code:"ca",name:"Catalan"},{code:"zh",name:"Chinese"},{code:"hr",name:"Croatian"},{code:"cs",name:"Czech"},{code:"da",name:"Danish"},{code:"nl",name:"Dutch"},{code:"en",name:"English"},{code:"et",name:"Estonian"},{code:"fi",name:"Finnish"},{code:"fr",name:"French"},{code:"gl",name:"Galician"},{code:"de",name:"German"},{code:"el",name:"Greek"},{code:"hi",name:"Hindi"},{code:"hu",name:"Hungarian"},{code:"is",name:"Icelandic"},{code:"id",name:"Indonesian"},{code:"it",name:"Italian"},{code:"ja",name:"Japanese"},{code:"kn",name:"Kannada"},{code:"kk",name:"Kazakh"},{code:"ko",name:"Korean"},{code:"lv",name:"Latvian"},{code:"lt",name:"Lithuanian"},{code:"mk",name:"Macedonian"},{code:"ms",name:"Malay"},{code:"mr",name:"Marathi"},{code:"mi",name:"Maori"},{code:"ne",name:"Nepali"},{code:"no",name:"Norwegian"},{code:"fa",name:"Persian"},{code:"pl",name:"Polish"},{code:"pt",name:"Portuguese"},{code:"ro",name:"Romanian"},{code:"ru",name:"Russian"},{code:"sr",name:"Serbian"},{code:"sk",name:"Slovak"},{code:"sl",name:"Slovenian"},{code:"es",name:"Spanish"},{code:"sw",name:"Swahili"},{code:"sv",name:"Swedish"},{code:"tl",name:"Tagalog"},{code:"ta",name:"Tamil"},{code:"th",name:"Thai"},{code:"tr",name:"Turkish"},{code:"uk",name:"Ukrainian"},{code:"ur",name:"Urdu"},{code:"vi",name:"Vietnamese"},{code:"cy",name:"Welsh"}];return"whisper"===t||"lemonfox"===t?o.sort((e,t)=>e.name.localeCompare(t.name)):"assemblyai"===t?"best"===a?e.sort((e,t)=>e.name.localeCompare(t.name)):n.sort((e,t)=>e.name.localeCompare(t.name)):o.sort((e,t)=>e.name.localeCompare(t.name))},[t,a]),l=Object(n.useCallback)(e=>{const t=e.target.value;t!==r&&i(t)},[r,i]);return o.a.createElement("div",{className:"relative mb-4"},o.a.createElement("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none"},o.a.createElement(f.a,{className:"text-muted dark:text-dark-muted w-5 h-5"})),o.a.createElement("select",{className:"border-border dark:border-dark-border dark:bg-dark-surface text-text dark:text-dark-text  focus:outline-none focus:ring-2 focus:ring-primary/50 dark:focus:ring-dark-primary/50  w-full py-2.5 pl-10 pr-8 bg-white border rounded-lg appearance-none cursor-pointer transition-colors duration-200 ease-in-out hover:border-primary/70 dark:hover:border-dark-primary/70",value:r,onChange:l,"aria-label":"Select language"},s.map(e=>o.a.createElement("option",{key:e.code,value:e.code,className:"py-1.5 px-2 hover:bg-primary/10 dark:hover:bg-dark-primary/10"},e.name))),o.a.createElement("div",{className:"absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none"},o.a.createElement("svg",{className:"text-muted dark:text-dark-muted w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},o.a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 9l-7 7-7-7"}))))});v.displayName="LanguageSelector";var y=v,x=a(23);const k=[{id:"Claude 4 Sonnet",name:"Claude 4 Sonnet"},{id:"Claude 3.7 Sonnet",name:"Claude 3.7 Sonnet"},{id:"Claude 3.5 Sonnet",name:"Claude 3.5 Sonnet"},{id:"Claude Haiku",name:"Claude Haiku"},{id:"Llama 3.1 70B",name:"Llama 3.1 70B"},{id:"DeepSeek V3",name:"DeepSeek V3"}],w="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiYW5vbiIsImlzcyI6ImFidWgxMjMxIiwiaWF0IjoxNzQ2Mzg1MjAwLCJleHAiOjE5MDQxNTE2MDB9.EkgKcDa0V0VEmPsXPmyal3YvxYuu9Q1k8OZZv7Gs8_o",E=Object(n.memo)(e=>{let{optimizationModel:t,onChange:a}=e;const[r,i]=Object(n.useState)([]),[s,l]=Object(n.useState)(!0),[c,m]=Object(n.useState)(null),[d,u]=Object(n.useState)(0),g=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:3;try{const t=await fetch("https://supabase.voicehype.ai/rest/v1/models?select=*&order=friendly_name.asc&model_type=eq.optimization",{method:"GET",headers:{apikey:w,Authorization:`Bearer ${w}`,"Content-Type":"application/json"}});if(!t.ok)throw new Error(`HTTP error! status: ${t.status} ${t.statusText}`);return(await t.json()).map(e=>({id:e.friendly_name,name:e.friendly_name}))}catch(c){if(e>0)return console.warn(`Failed to fetch models, retrying... (${e} attempts left)`,c),await new Promise(t=>setTimeout(t,1e3*Math.pow(2,3-e))),g(e-1);throw c}},b=async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{l(!0),m(null),e&&u(e=>e+1);const t=await g();t.length>0?i(t):(console.warn("No models found in database, using defaults"),i(k))}catch(c){console.error("Failed to fetch models after retries:",c),m("Failed to load models. Using default models."),i(k)}finally{l(!1)}};Object(n.useEffect)(()=>{b()},[]);Object(n.useMemo)(()=>{const e=r.find(e=>e.id===t);return e||(r.find(e=>"claude-4-sonnet"===e.id)||r[0])},[r,t]);const h=Object(n.useCallback)(e=>{const n=e.target.value;n!==t&&a(n)},[t,a]),f=Object(n.useCallback)(()=>{b(!0)},[]);return s?o.a.createElement("div",{className:"space-y-3"},o.a.createElement("div",{className:"relative"},o.a.createElement("div",{className:"border-border dark:border-dark-border dark:bg-dark-surface text-text dark:text-dark-text w-full px-3 py-2 bg-white border rounded-md"},o.a.createElement("div",{className:"animate-pulse dark:bg-gray-700 w-full h-4 bg-gray-200 rounded"}))),o.a.createElement("div",{className:"text-muted dark:text-dark-muted bg-gray-50 dark:bg-dark-surface/40 flex items-start p-3 text-sm rounded-md"},o.a.createElement(p.a,{className:"text-primary dark:text-dark-primary flex-shrink-0 w-5 h-5 mr-2"}),o.a.createElement("p",null,"Loading optimization models..."))):c?o.a.createElement("div",{className:"space-y-3"},o.a.createElement("div",{className:"relative"},o.a.createElement("select",{className:"border-border dark:border-dark-border dark:bg-dark-surface text-text dark:text-dark-text focus:outline-none focus:ring-2 focus:ring-primary/50 dark:focus:ring-dark-primary/50 w-full px-3 py-2 bg-white border rounded-md appearance-none cursor-pointer",value:t,onChange:h,"aria-label":"Select optimization model"},r.map(e=>o.a.createElement("option",{key:e.id,value:e.id},e.name))),o.a.createElement("div",{className:"absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none"},o.a.createElement("svg",{className:"text-muted dark:text-dark-muted w-4 h-4",fill:"none",viewBox:"0 0 20 20",stroke:"currentColor"},o.a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 7l3-3 3 3m0 6l-3 3-3-3"})))),o.a.createElement("div",{className:"dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-900/20 flex items-start justify-between p-3 text-sm text-yellow-600 rounded-md"},o.a.createElement("div",{className:"flex items-start"},o.a.createElement(p.a,{className:"dark:text-yellow-400 flex-shrink-0 w-5 h-5 mr-2 text-yellow-500"}),o.a.createElement("p",null,c)),o.a.createElement("button",{onClick:f,className:"dark:bg-yellow-800 hover:bg-yellow-300 dark:hover:bg-yellow-700 flex items-center px-2 py-1 ml-2 text-xs transition-colors bg-yellow-200 rounded",title:"Retry loading models"},o.a.createElement(x.a,{className:"w-3 h-3 mr-1"}),"Retry"))):o.a.createElement("div",{className:"space-y-3"},o.a.createElement("div",{className:"relative"},o.a.createElement("select",{className:"border-border dark:border-dark-border dark:bg-dark-surface text-text dark:text-dark-text focus:outline-none focus:ring-2 focus:ring-primary/50 dark:focus:ring-dark-primary/50 w-full px-3 py-2 bg-white border rounded-md appearance-none cursor-pointer",value:t,onChange:h,"aria-label":"Select optimization model"},r.map(e=>o.a.createElement("option",{key:e.id,value:e.id},e.name))),o.a.createElement("div",{className:"absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none"},o.a.createElement("svg",{className:"text-muted dark:text-dark-muted w-4 h-4",fill:"none",viewBox:"0 0 20 20",stroke:"currentColor"},o.a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 7l3-3 3 3m0 6l-3 3-3-3"})))))});E.displayName="OptimizationModelSelector";var N=E,C=a(24),S=a(25),O=a(26),j=a(27),M=a(28),T=a(29),z=a(30),D=a(31),A=a(32),$=a(33);const R=()=>"function"===typeof R?window.acquireVsCodeApi():{postMessage:e=>{console.log("Development mode: posting message to extension",e)},getState:()=>null,setState:e=>{console.log("Development mode: setting state",e)}},P=R();var V=e=>{let{transcriptions:t,onCopyOriginal:a,onCopyOptimized:o,onOptimize:r,onPreviewOptimization:i}=e;const[s,l]=Object(n.useState)(""),[c,m]=Object(n.useState)("newest"),[d,u]=Object(n.useState)(5),[g,p]=Object(n.useState)({}),[b,h]=Object(n.useState)({}),f=(e,t,a)=>{var n;return e?(null===(n=g[t])||void 0===n?void 0:n[a])||e.length<=500?e:e.substring(0,500)+"...":""},v=(e,t)=>{p(a=>{const n=a[e]||{original:!1,optimized:!1};return Object.assign({},a,{[e]:Object.assign({},n,{[t]:!n[t]})})})},y=e=>{const t=b[e]||!1;h(a=>Object.assign({},a,{[e]:!t})),i(e,!t)},x=Object(n.useMemo)(()=>{let e=Array.from(t);switch(s&&(e=e.filter(e=>e.originalText.toLowerCase().includes(s.toLowerCase())||e.optimizedText&&e.optimizedText.toLowerCase().includes(s.toLowerCase()))),c){case"newest":e.sort((e,t)=>new Date(t.timestamp).getTime()-new Date(e.timestamp).getTime());break;case"oldest":e.sort((e,t)=>new Date(e.timestamp).getTime()-new Date(t.timestamp).getTime());break;default:e.sort((e,t)=>new Date(t.timestamp).getTime()-new Date(e.timestamp).getTime())}return e},[t,s,c]),k=Object(n.useMemo)(()=>x.slice(0,d),[x,d]),w=k.length<x.length;return n.createElement("div",{className:"flex flex-col h-full"},n.createElement("div",{className:"border-border dark:border-dark-border p-2 mb-2 border-b"},n.createElement("div",{className:"flex items-center justify-between mb-2"},n.createElement("div",{className:"relative flex-grow"},n.createElement("input",{type:"text",placeholder:"Search transcriptions...",value:s,onChange:e=>l(e.target.value),className:"w-full px-3 py-1.5 pl-8 text-sm bg-gray-50 dark:bg-dark-surface border border-border dark:border-dark-border rounded-md"}),n.createElement(C.a,{className:"absolute left-2.5 top-1/2 transform -translate-y-1/2 w-3.5 h-3.5 text-muted dark:text-dark-muted"}),s&&n.createElement("button",{onClick:()=>l(""),className:"absolute right-2.5 top-1/2 transform -translate-y-1/2"},n.createElement(S.a,{className:"w-3.5 h-3.5 text-muted dark:text-dark-muted hover:text-primary dark:hover:text-dark-primary"}))),t.length>0&&n.createElement("button",{onClick:()=>{console.log("RecentTranscriptions: Clear all history button clicked. Current transcriptions count:",t.length),P.postMessage({command:"showConfirmation",message:"Are you sure you want to delete all transcription history? This cannot be undone.",onConfirm:"clearTranscriptionHistory"}),console.log("RecentTranscriptions: Sent showConfirmation message with onConfirm=clearTranscriptionHistory")},className:"ml-2 p-1.5 text-red-600 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-md",title:"Clear all transcription history"},n.createElement(O.a,{className:"w-4 h-4"}))),n.createElement("div",{className:"flex items-center justify-between"},n.createElement("div",{className:"flex items-center justify-center space-x-2 bg-gray-100 dark:bg-gray-800 rounded-full p-1 max-w-[200px]"},n.createElement("button",{className:`flex-1 py-1 px-3 text-xs font-medium rounded-full transition-colors ${"newest"===c?"bg-white dark:bg-gray-700 shadow-sm text-primary dark:text-dark-primary":"text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"}`,onClick:()=>m("newest")},"Newest"),n.createElement("button",{className:`flex-1 py-1 px-3 text-xs font-medium rounded-full transition-colors ${"oldest"===c?"bg-white dark:bg-gray-700 shadow-sm text-primary dark:text-dark-primary":"text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"}`,onClick:()=>m("oldest")},"Oldest")),t.length>0&&n.createElement("span",{className:"text-muted dark:text-dark-muted text-xs"},t.length,"/50 messages"))),n.createElement("div",{className:"flex-grow px-2 pb-2 space-y-3 overflow-y-auto"},0===x.length?n.createElement("div",{className:"text-muted dark:text-dark-muted border-border dark:border-dark-border flex flex-col items-center justify-center p-4 text-sm text-center border border-dashed rounded-md"},n.createElement(j.a,{className:"w-8 h-8 mb-2 opacity-50"}),s?"No matching transcriptions found":"No transcription history yet"):n.createElement(n.Fragment,null,k.map(e=>{var t,i;return n.createElement("div",{key:e.id,className:"dark:bg-dark-surface/50 border-border dark:border-dark-border group overflow-hidden bg-white border rounded-md shadow-sm"},n.createElement("div",{className:"flex items-center justify-between px-3 py-1.5 bg-gray-50 dark:bg-dark-surface border-b border-border dark:border-dark-border"},n.createElement("div",{className:"text-muted dark:text-dark-muted flex items-center text-xs"},n.createElement(j.a,{className:"w-3 h-3 mr-1"}),n.createElement("span",null,(e=>{try{const a=new Date(e),n=new Date;return a.getDate()===n.getDate()&&a.getMonth()===n.getMonth()&&a.getFullYear()===n.getFullYear()?a.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}):a.toLocaleDateString([],{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})}catch(t){return console.error("Error formatting timestamp:",t),"Invalid date"}})(e.timestamp)),e.duration&&n.createElement("span",{className:"ml-2"},"(",(e=>e?`${Math.floor(e/60)}:${Math.floor(e%60).toString().padStart(2,"0")}`:"")(e.duration),")")),n.createElement("div",{className:"flex items-center"},n.createElement("div",{className:"text-muted dark:text-dark-muted mr-2 text-xs"},((e,t)=>`${e||"unknown"} - ${t||"unknown"}`)(e.service,e.model)),n.createElement("button",{onClick:()=>(e=>{P.postMessage({command:"deleteTranscription",id:e})})(e.id),className:"group-hover:opacity-100 text-muted hover:text-red-500 dark:text-dark-muted dark:hover:text-red-400 transition-opacity opacity-0",title:"Delete transcription"},n.createElement(O.a,{className:"w-3.5 h-3.5"})))),n.createElement("div",{className:"p-2 space-y-2"},n.createElement("div",{className:"space-y-1"},n.createElement("div",{className:"text-muted dark:text-dark-muted flex items-center justify-between text-xs font-semibold uppercase"},n.createElement("span",null,"Original"),e.originalText&&e.originalText.length>500&&n.createElement("button",{onClick:()=>v(e.id,"original"),className:"text-primary dark:text-dark-primary flex items-center text-xs font-normal normal-case"},(null===(t=g[e.id])||void 0===t?void 0:t.original)?n.createElement(n.Fragment,null,"Show Less ",n.createElement(M.a,{className:"w-3 h-3 ml-1"})):n.createElement(n.Fragment,null,"Show More ",n.createElement(T.a,{className:"w-3 h-3 ml-1"})))),n.createElement("div",{className:"flex"},n.createElement("div",{className:"flex-grow p-1.5 bg-gray-50 dark:bg-dark-surface/50 rounded text-xs whitespace-pre-wrap"},f(e.originalText,e.id,"original")),n.createElement("button",{className:"text-muted hover:text-primary dark:text-dark-muted dark:hover:text-dark-primary p-1 ml-1",onClick:()=>a(e.id),title:"Copy original transcript"},n.createElement(z.a,{className:"w-4 h-4"})))),e.optimizedText?n.createElement("div",{className:"space-y-1"},n.createElement("div",{className:"text-accent dark:text-dark-accent flex items-center justify-between text-xs font-semibold uppercase"},n.createElement("div",{className:"flex items-center"},n.createElement("span",null,"Optimized"),e.isPreview&&n.createElement("span",{className:"ml-2 text-xs font-normal text-gray-500 dark:text-gray-400"},"(Preview)")),e.optimizedText&&e.optimizedText.length>500&&n.createElement("button",{onClick:()=>v(e.id,"optimized"),className:"text-primary dark:text-dark-primary flex items-center text-xs font-normal normal-case"},(null===(i=g[e.id])||void 0===i?void 0:i.optimized)?n.createElement(n.Fragment,null,"Show Less ",n.createElement(M.a,{className:"w-3 h-3 ml-1"})):n.createElement(n.Fragment,null,"Show More ",n.createElement(T.a,{className:"w-3 h-3 ml-1"})))),n.createElement("div",{className:"flex"},n.createElement("div",{className:"flex-grow p-1.5 bg-gray-50 dark:bg-dark-surface/50 rounded text-xs whitespace-pre-wrap"},f(e.optimizedText,e.id,"optimized")),n.createElement("div",{className:"flex flex-col ml-1"},n.createElement("button",{className:"text-muted hover:text-primary dark:text-dark-muted dark:hover:text-dark-primary p-1",onClick:()=>o(e.id),title:"Copy optimized transcript"},n.createElement(z.a,{className:"w-4 h-4"})),e.isPreview&&n.createElement("button",{className:"text-muted hover:text-primary dark:text-dark-muted dark:hover:text-dark-primary p-1",onClick:()=>y(e.id),title:"Cancel preview"},n.createElement(S.a,{className:"w-4 h-4"})))),e.isPreview&&n.createElement("div",{className:"flex justify-end mt-1"},n.createElement("button",{className:"bg-primary/10 hover:bg-primary/20 dark:bg-dark-primary/20 dark:hover:bg-dark-primary/30 text-primary dark:text-dark-primary flex items-center px-2 py-1 text-xs font-medium rounded",onClick:()=>r(e.id),title:"Apply this optimization"},n.createElement(D.a,{className:"w-3 h-3 mr-1"}),"Apply Optimization"))):n.createElement("div",{className:"flex justify-between"},b[e.id]?n.createElement("div",{className:"flex items-center space-x-2"},n.createElement("div",{className:"text-gray-600 dark:text-gray-400 text-xs"},"Preview optimization in progress..."),n.createElement("button",{className:"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 text-xs",onClick:()=>y(e.id)},"Cancel")):n.createElement("button",{className:"bg-primary/10 hover:bg-primary/20 dark:bg-dark-primary/20 dark:hover:bg-dark-primary/30 text-primary dark:text-dark-primary flex items-center px-2 py-1 text-xs font-medium rounded",onClick:()=>y(e.id),title:"Preview optimization before applying"},n.createElement(A.a,{className:"w-3 h-3 mr-1"}),"Preview"),n.createElement("button",{className:"bg-primary/10 hover:bg-primary/20 dark:bg-dark-primary/20 dark:hover:bg-dark-primary/30 text-primary dark:text-dark-primary flex items-center px-2 py-1 text-xs font-medium rounded",onClick:()=>r(e.id),title:"Optimize this transcript"},n.createElement(D.a,{className:"w-3 h-3 mr-1"}),"Optimize"))))}),w&&n.createElement("div",{className:"flex justify-center pt-2"},n.createElement("button",{onClick:()=>{u(e=>e+5)},className:"flex items-center px-4 py-1.5 text-xs font-medium text-primary dark:text-dark-primary border border-primary/30 dark:border-dark-primary/30 hover:bg-primary/5 dark:hover:bg-dark-primary/10 rounded-md"},n.createElement($.a,{className:"w-3.5 h-3.5 mr-1.5"}),"Show More (",x.length-k.length," remaining)")))))};const I=Object(n.forwardRef)((e,t)=>{let{isVisible:a,isRecording:o,className:r="",onClear:i}=e;const[s,l]=Object(n.useState)([]),[c,m]=Object(n.useState)(""),[d,u]=Object(n.useState)(!1),[g,p]=Object(n.useState)(""),[b,h]=Object(n.useState)(!1),[f,v]=Object(n.useState)(0),y=Object(n.useRef)(null);Object(n.useEffect)(()=>{y.current&&(y.current.scrollTop=y.current.scrollHeight)},[s,c]),Object(n.useEffect)(()=>{o||(l([]),m(""),u(!1),p(""),h(!1),v(0))},[o]);const x=e=>{m(e)},k=e=>{if(e.trim()){const t={id:Date.now().toString(),text:e.trim(),isFinal:!0,timestamp:Date.now()};l(e=>[...e,t]),m("")}},w=(e,t)=>{p(t||e),u("connected"===e||"transcribing"===e)},E=(e,t)=>{h(e),v(t||0),e&&p(`Waiting for remaining content... (${t}s)`)};return Object(n.useImperativeHandle)(t,()=>({handlePartialTranscript:x,handleFinalTranscript:k,handleConnectionStatus:w,handleGracePeriodStatus:E})),a?n.createElement("div",{className:`beautiful-realtime-transcript ${r}`},n.createElement("div",{className:"beautiful-realtime-transcript-header"},n.createElement("div",{className:"flex items-center justify-between"},n.createElement("h3",{className:"text-text dark:text-dark-text text-sm font-medium"},"Live Transcription"),s.length>0&&n.createElement("button",{onClick:()=>{l([]),m(""),i&&i()},className:"clear-button px-2 py-1 text-xs rounded",title:"Clear transcript"},"Clear"))),n.createElement("div",{ref:y,className:"beautiful-realtime-transcript-content"},!o&&n.createElement("div",{className:"transcript-placeholder"},n.createElement("div",{className:"dark:text-gray-400 text-sm text-center text-gray-500"},"Start recording to see live transcription")),o&&0===s.length&&!c&&n.createElement("div",{className:"transcript-placeholder"},n.createElement("div",{className:"dark:text-gray-400 text-sm text-center text-gray-500"},"Listening... Start speaking!")),s.map(e=>n.createElement("div",{key:e.id,className:"transcript-segment final"},n.createElement("span",{className:"segment-text"},e.text))),c&&n.createElement("div",{className:"transcript-segment partial"},n.createElement("span",{className:"segment-text"},c),n.createElement("span",{className:"typing-indicator"},"|")),b&&n.createElement("div",{className:"transcript-segment grace-period"},n.createElement("div",{className:"dark:text-blue-400 text-sm text-center text-blue-600"},"Waiting for remaining content... (",f,"s)")))):null});I.displayName="BeautifulRealtimeTranscript";var L=I;var U=o.a.memo(e=>{let{label:t,checked:a,onChange:n,className:r=""}=e;return o.a.createElement("div",{className:`flex items-center justify-between p-3 mb-3 bg-white dark:bg-dark-surface border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm hover:border-primary/70 dark:hover:border-primary/70 transition-colors duration-200 ${r}`},o.a.createElement("label",{className:"flex items-center justify-between w-full cursor-pointer"},o.a.createElement("span",{className:"dark:text-white text-sm font-medium text-gray-900"},t),o.a.createElement("div",{className:"relative ml-auto"},o.a.createElement("input",{type:"checkbox",className:"sr-only",checked:a,onChange:e=>n(e.target.checked)}),o.a.createElement("div",{className:`block w-10 h-6 rounded-full transition-colors duration-200 ease-in-out ${a?"bg-primary dark:bg-primary":"bg-gray-300 dark:bg-gray-700"}`}),o.a.createElement("div",{className:`absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition-transform duration-200 ease-in-out ${a?"transform translate-x-4":"transform translate-x-0"}`}))))});var W=e=>{let{sampleRate:t,deviceId:a,availableDevices:r,onSampleRateChange:i,onDeviceChange:s}=e;Object(n.useEffect)(()=>{P.postMessage({command:"getAudioDevices"})},[]);const l=r.filter(e=>"default"!==e.id&&""!==e.id&&"System Default"!==e.name);return o.a.createElement("div",{className:"space-y-4"},o.a.createElement("div",null,o.a.createElement("label",{className:"text-text dark:text-dark-text block mb-2 text-sm font-medium"},"Audio Device",o.a.createElement("button",{className:"text-primary dark:text-dark-primary hover:underline ml-2 text-xs",onClick:()=>P.postMessage({command:"getAudioDevices"})},"Refresh")),o.a.createElement("div",{className:"relative"},o.a.createElement("select",{value:a||"",onChange:e=>s(""===e.target.value?null:e.target.value),className:"w-full px-4 py-2.5 bg-gray-100 dark:bg-dark-surface hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary/50 dark:focus:ring-dark-primary/50 appearance-none"},o.a.createElement("option",{value:""},"System Default"),l.map(e=>o.a.createElement("option",{key:e.id,value:e.id},(e=>e.name.length>50?`${e.name.substring(0,47)}...`:e.name)(e)))),o.a.createElement("div",{className:"dark:text-gray-200 absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 pointer-events-none"},o.a.createElement("svg",{className:"w-4 h-4 fill-current",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20"},o.a.createElement("path",{d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"}))))),o.a.createElement("div",null,o.a.createElement("label",{className:"text-text dark:text-dark-text block mb-2 text-sm font-medium"},"Sample Rate"),o.a.createElement("div",{className:"relative"},o.a.createElement("select",{value:t,onChange:e=>i(Number(e.target.value)),className:"w-full px-4 py-2.5 bg-gray-100 dark:bg-dark-surface hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary/50 dark:focus:ring-dark-primary/50 appearance-none"},[8e3,16e3,22050,44100,48e3].map(e=>o.a.createElement("option",{key:e,value:e},e.toLocaleString()," Hz"))),o.a.createElement("div",{className:"dark:text-gray-200 absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 pointer-events-none"},o.a.createElement("svg",{className:"w-4 h-4 fill-current",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20"},o.a.createElement("path",{d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"}))))))};var K=e=>{let{className:t=""}=e;const[a,r]=Object(n.useState)(!1);Object(n.useEffect)(()=>{const e=e=>{var t,a;const n=e.data;if("initialConfiguration"===n.command&&(null===(t=n.options)||void 0===t?void 0:t.theme)){const e=n.options.theme;r("dark"===e),console.log("Logo: Initial theme set from extension:",e)}else if("themeChanged"===n.command){var o;const e=(null===(o=n.data)||void 0===o?void 0:o.theme)||n.theme;r("dark"===e||"vscode-dark"===e),console.log("Logo: Theme changed from extension:",e)}else if("updateOptions"===n.command&&void 0!==(null===(a=n.options)||void 0===a?void 0:a.theme)){const e=n.options.theme;r("dark"===e),console.log("Logo: Theme updated via options:",e)}};window.addEventListener("message",e);const t=setTimeout(()=>{(()=>{const e=document.body.className,t=document.documentElement.className,a=e.includes("vscode-dark")||t.includes("vscode-dark"),n=e.includes("vscode-light")||t.includes("vscode-light");let o=!1;if(a&&!n)o=!0;else if(!a&&n)o=!1;else if(a&&n){const e=document.body.classList;o=Array.from(e).indexOf("vscode-dark")>Array.from(e).indexOf("vscode-light")}r(o)})()},100);return()=>{window.removeEventListener("message",e),clearTimeout(t)}},[]);const i=a?window.voicehypeLogoLight:window.voicehypeLogoDark;return console.log(`Logo: Using ${a?"light":"dark"} logo:`,i),o.a.createElement("div",{className:`logo-container ${t}`},i?o.a.createElement("img",{src:i,alt:"VoiceHype Logo",className:"logo-image",style:{maxWidth:"100%",height:"auto"}}):o.a.createElement("div",{className:"logo-placeholder"},"VoiceHype"))};var B=e=>{let{children:t,onClick:a,variant:n="primary",size:r="md",disabled:i=!1}=e;return o.a.createElement("button",{onClick:a,disabled:i,className:`rounded font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-1 ${{sm:"px-2 py-1 text-sm",md:"px-3 py-1.5 text-base",lg:"px-4 py-2 text-lg"}[r]} ${{primary:"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 focus:ring-blue-500",secondary:"bg-gray-200 text-gray-800 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 dark:border dark:border-gray-600 focus:ring-gray-500",danger:"bg-red-600 text-white hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 focus:ring-red-500"}[n]} ${i?"opacity-50 cursor-not-allowed":""}`},t)},G=a(34),F=a(35),H=a(36),q=a(37),_=a(38),J=a(39),Y=a(40),Q=a(41),X=a(42),Z=a(43),ee=a(44),te=a(45),ae=a(46);const ne={id:"custom",name:"Custom",prompt:"",isCustom:!0},oe=[{id:"clean-up",name:"Clean Up",prompt:'Clean up the text by removing filler words like "um", "you know", "like", fix any grammar mistakes, and make the transcript coherent and smooth, while preserving the original meaning. Return ONLY the cleaned text without any additional commentary or conversational responses.',icon:D.a},{id:"translate",name:"Translate",prompt:"Translate the following transcript into [TARGET_LANGUAGE], keeping it natural and respectful of tone and cultural context. Do not summarize, just translate as clearly as possible.",icon:f.a},{id:"summarize",name:"Summarize",prompt:"Summarize the following transcript into a short, coherent paragraph that captures the key ideas, tone, and intention of the speaker. Return ONLY the summarized text without any introductory phrases or additional commentary.",icon:G.a},{id:"polish",name:"Polish",prompt:"Rewrite the following transcript to make it sound formal and professional, correcting grammar and sentence structure, while preserving the speaker's message.",icon:F.a},{id:"expand",name:"Expand",prompt:"Expand and elaborate the transcript into a well-written, detailed explanation or article, adding transitions and clarifications where needed, while staying faithful to the speaker's intent.",icon:H.a},{id:"light-touch",name:"Light Touch",prompt:"Apply minimal editing to fix major grammatical issues and incoherence, but keep the casual tone and natural flow of the original speech.",icon:q.a},{id:"bullet-points",name:"Bullet Points",prompt:"Convert the following transcript into a clear, concise list of bullet points summarizing the main ideas discussed.",icon:_.a},{id:"islamic-tone",name:"Islamic Tone",prompt:'Transform this transcript into an uplifting Islamic motivational message. Use Quranic verses and Hadith to inspire positive action while maintaining the original meaning. Include motivational Islamic phrases like "With Allah\'s help we can achieve", "Allah has promised us", "Let\'s strive to be better Muslims", and "This is our chance to earn Allah\'s pleasure". Keep the tone encouraging, hopeful and spiritually uplifting while preserving the original content\'s essence. The goal is to motivate the listener/reader to take positive action in their life while strengthening their faith.',icon:J.a},{id:"whatsapp-style",name:"WhatsApp Style",prompt:"Rewrite the following text as a short, casual WhatsApp message. Keep it friendly and easy to understand.",icon:Y.a},{id:"email-format",name:"Email Format",prompt:"Rewrite the following transcript into a professional email. Include a greeting, body, and closing, and keep the tone polite and formal.",icon:Q.a},{id:"reminder-note",name:"Reminder Note",prompt:"Convert the following transcript into a short personal reminder, using brief and clear language.",icon:X.a},{id:"to-do-list",name:"To-Do List",prompt:"Extract a to-do list from the following transcript. Format it as simple, actionable tasks in bullet points.",icon:Z.a},{id:"meeting-minutes",name:"Meeting Minutes",prompt:"Format the following text into structured meeting minutes, listing topics discussed, decisions made, and action items clearly.",icon:ee.a},{id:"social-caption",name:"Social Caption",prompt:"Rewrite the text into a short, engaging social media caption suitable for Instagram, Twitter, or Facebook, keeping the tone casual and inviting.",icon:te.a},{id:"prompt",name:"Prompt",prompt:"Enhance this into a prompt I can send to an LLM.",icon:ae.a}];var re=e=>{let{value:t,onChange:a,onModeChange:r}=e;const[i,s]=Object(n.useState)([...oe,ne]),[l,c]=Object(n.useState)("clean-up"),[m,d]=Object(n.useState)(!1),[u,g]=Object(n.useState)(""),[p,b]=Object(n.useState)(""),[h,f]=Object(n.useState)("English"),[v,y]=Object(n.useState)("English");Object(n.useEffect)(()=>{const e=setTimeout(()=>{y(h)},500);return()=>{clearTimeout(e)}},[h]),Object(n.useEffect)(()=>{console.log("PromptModeSelector: Requesting prompt modes with default activeMode:",l),P.postMessage({command:"getPromptModes",persist:!0});const e=e=>{if("promptModes"===e.data.command){const t=e.data.modes||[];console.log(`PromptModeSelector: Received ${t.length} custom modes from extension`),s([...oe,...t,ne]),e.data.activeMode?(console.log("PromptModeSelector: Setting active mode from configuration:",e.data.activeMode),c(e.data.activeMode)):console.log("PromptModeSelector: No active mode provided, keeping default:",l)}else"initialOptions"===e.data.command?(e.data.activePromptMode&&(console.log("PromptModeSelector: Setting initial active mode from configuration:",e.data.activePromptMode),c(e.data.activePromptMode)),Array.isArray(e.data.customPromptModes)&&e.data.customPromptModes.length>0&&(console.log(`PromptModeSelector: Setting ${e.data.customPromptModes.length} initial custom modes from configuration`),s([...oe,...e.data.customPromptModes,ne]))):"activePromptModeUpdated"===e.data.command&&e.data.modeId&&(console.log("PromptModeSelector: Active mode confirmed by extension:",e.data.modeId),c(e.data.modeId))};return window.addEventListener("message",e),()=>window.removeEventListener("message",e)},[l]),Object(n.useEffect)(()=>{const e=i.find(e=>e.id===l);if(e&&"custom"!==e.id){let t=e.prompt;"translate"===e.id&&(t=t.replace("[TARGET_LANGUAGE]",v),a(t)),g(t)}},[l,i,v,a]);return o.a.createElement("div",{className:"space-y-3"},o.a.createElement("div",{className:"flex flex-wrap gap-2"},i.map(e=>o.a.createElement("div",{key:e.id,className:"flex items-center"},o.a.createElement("div",{onClick:()=>(e=>{c(e.id),"custom"!==e.id&&(a(e.prompt),g(e.prompt)),r&&r(e.id),d("custom"===e.id)})(e),className:`px-3 py-1.5 rounded-full border transition-colors cursor-pointer flex items-center gap-1.5\n                ${l===e.id?"bg-primary dark:bg-primary border-primary dark:border-primary text-black font-bold":"bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"}`},e.icon&&o.a.createElement(e.icon,{className:"w-4 h-4"}),o.a.createElement("span",{className:"text-sm font-medium select-none"},e.name)),e.isCustom&&"custom"!==e.id&&o.a.createElement("button",{onClick:()=>{P.postMessage({command:"deletePromptMode",modeId:e.id}),s(i.filter(t=>t.id!==e.id)),l===e.id&&c("clean-up")},className:"hover:text-red-700 p-1 ml-1 text-red-500 rounded-full",title:"Delete custom mode"},o.a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",className:"w-4 h-4",viewBox:"0 0 20 20",fill:"currentColor"},o.a.createElement("path",{fillRule:"evenodd",d:"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z",clipRule:"evenodd"})))))),"translate"===l&&o.a.createElement("div",{className:"mt-3"},o.a.createElement("label",{className:"dark:text-gray-300 block mb-1 text-sm font-medium text-gray-700"},"Target Language"),o.a.createElement("input",{type:"text",value:h,onChange:e=>f(e.target.value),placeholder:"Enter target language (e.g. French, Arabic)",className:"dark:border-gray-600 dark:bg-gray-700 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 w-full p-2 bg-white border border-gray-300 rounded-md shadow-sm"})),"custom"!==l&&!m&&u&&o.a.createElement("div",{className:"dark:border-gray-600 bg-gray-50 dark:bg-gray-800 p-3 mt-3 border border-gray-300 rounded-md"},o.a.createElement("p",{className:"dark:text-gray-300 text-sm text-gray-600"},u)),m&&o.a.createElement("div",{className:"mt-3"},o.a.createElement("div",{className:"mb-3"},o.a.createElement("label",{className:"dark:text-gray-300 block mb-1 text-sm font-medium text-gray-700"},"Mode Name"),o.a.createElement("input",{type:"text",value:p,onChange:e=>b(e.target.value),placeholder:"Enter a name for this mode",className:"dark:border-gray-600 dark:bg-gray-700 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 w-full p-2 bg-white border border-gray-300 rounded-md shadow-sm"})),o.a.createElement("label",{className:"dark:text-gray-300 block mb-1 text-sm font-medium text-gray-700"},"Prompt Text"),o.a.createElement("textarea",{value:t,onChange:e=>{const t=e.target.value;g(t),a(t)},placeholder:"Enter custom prompt...",rows:4,className:"dark:border-gray-600 dark:bg-gray-700 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 w-full p-2 bg-white border border-gray-300 rounded-md shadow-sm resize-none"}),o.a.createElement("div",{className:"flex items-center justify-between mt-2"},o.a.createElement("div",{className:"dark:text-gray-400 text-sm text-gray-500"},"Create a custom optimization mode"),o.a.createElement(B,{size:"sm",onClick:()=>{if(t&&t.trim()){const e=p.trim()?p.trim():`Custom ${(new Date).toLocaleString()}`;P.postMessage({command:"savePromptMode",mode:{id:`custom-${Date.now()}`,name:e,prompt:u,isCustom:!0}}),b(""),a("")}},disabled:!t||!t.trim()},"Save Mode"))))};var ie=e=>{let{initialApiKey:t,onApiKeySaved:a}=e;console.log("ApiKeyInput initializing with key:",t?"present":"not set");const[o,r]=Object(n.useState)(""),[i,s]=Object(n.useState)(!1),[l,c]=Object(n.useState)(!1),[m,d]=Object(n.useState)(!0);Object(n.useEffect)(()=>{console.log("ApiKeyInput received new initialApiKey:",t?"present":"not set"),t&&(r(t),d(!1))},[t]);const u=Object(n.useCallback)(async()=>{try{c(!0),await P.postMessage({command:"updateOptions",options:{apiKey:o}}),a&&a(o)}finally{c(!1),d(!1)}},[o,a]);return n.createElement("div",{className:"bg-opacity-10 mb-4 overflow-hidden bg-white rounded-lg"},n.createElement("button",{onClick:()=>d(!m),className:"hover:bg-opacity-20 flex items-center justify-between w-full px-4 py-3 transition-colors"},n.createElement("span",{className:"text-sm font-medium"},"API Key Configuration"),m?n.createElement(M.a,{className:"w-5 h-5"}):n.createElement(T.a,{className:"w-5 h-5"})),m&&n.createElement("div",{className:"p-4 space-y-4"},n.createElement("div",{className:"flex flex-col space-y-2"},n.createElement("div",{className:"flex-1 min-w-0"},n.createElement("input",{type:i?"text":"password",value:o,onChange:e=>r(e.target.value),placeholder:"Enter your VoiceHype API key",className:"bg-opacity-10 focus:border-blue-500 focus:outline-none w-full px-3 py-2 bg-white border border-gray-600 rounded"})),n.createElement("div",{className:"sm:flex-row flex flex-col gap-2"},n.createElement("button",{onClick:()=>s(!i),className:"bg-opacity-10 hover:bg-opacity-20 sm:flex-none flex-1 px-3 py-2 bg-white border border-gray-600 rounded"},i?"Hide":"Show"),n.createElement("button",{onClick:u,disabled:l,className:"hover:bg-blue-700 disabled:opacity-50 sm:flex-none flex-1 px-4 py-2 text-white bg-blue-600 rounded"},l?"Saving...":"Save"))),n.createElement("p",{className:"text-xs text-gray-500"},"Your API key is stored securely. Get one at voicehype.ai")))};var se=e=>{let{apiKey:t}=e;const[a,o]=Object(n.useState)(!1),[r,i]=Object(n.useState)(null),[s,l]=Object(n.useState)(!1);Object(n.useEffect)(()=>{const e=e=>{const t=e.data;switch(console.log("OnboardingScreen received message:",t),t.command){case"authStatusUpdated":o(!1),t.error&&(i(t.error),setTimeout(()=>i(null),5e3),l(!1),P.postMessage({command:"authCancelled"})),t.authenticated||l(!1);break;case"apiKeySaved":t.success&&!s?(console.log("API key saved successfully, initiating transition..."),l(!0),i(null),o(!1),console.log("Onboarding: Sending manualApiKeyVerified message"),P.postMessage({command:"manualApiKeyVerified",apiKey:t.apiKey})):t.error&&(l(!1),i(t.error),setTimeout(()=>i(null),5e3));break;case"manualApiKeyVerified":console.log("OnboardingScreen: Manual API key verified, transition should complete"),l(!1);break;case"updateView":"main"===t.view&&(console.log("OnboardingScreen: View updated to main"),l(!1));break;case"viewUpdated":l(!1)}};return window.addEventListener("message",e),()=>window.removeEventListener("message",e)},[s]);const[c,m]=Object(n.useState)(!1);return n.createElement("div",{className:"flex flex-col items-center justify-center h-full p-6 text-center"},n.createElement("div",{className:"mb-8"},n.createElement(K,{className:"mb-4"}),n.createElement("h1",{className:"mt-4 text-2xl font-bold"},"Welcome to VoiceHype"),n.createElement("p",{className:"mt-2 text-gray-400"},"Voice-to-prompt productivity tool for developers")),n.createElement("div",{className:"w-full max-w-md space-y-4"},n.createElement("button",{onClick:()=>{s||(o(!0),i(null),P.postMessage({command:"authenticate"}))},disabled:a||s,className:"hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center w-full px-4 py-3 font-medium text-white bg-blue-600 rounded-lg"},a||s?n.createElement(n.Fragment,null,n.createElement("svg",{className:"animate-spin w-5 h-5 mr-3 -ml-1 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},n.createElement("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),n.createElement("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})),s?"Loading...":"Signing in..."):n.createElement(n.Fragment,null,n.createElement("svg",{className:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},n.createElement("path",{fillRule:"evenodd",d:"M3 5a2 2 0 012-2h10a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2V5zm11 1H6v8l4-2 4 2V6z",clipRule:"evenodd"})),"Sign in with Browser")),n.createElement("div",{className:"relative my-4"},n.createElement("div",{className:"absolute inset-0 flex items-center"},n.createElement("div",{className:"w-full border-t border-gray-600"})),n.createElement("div",{className:"relative flex justify-center text-sm"},n.createElement("span",{className:"px-2 bg-[#1e1e1e] text-gray-400"},"or"))),n.createElement("button",{onClick:()=>{s||m(!c)},disabled:a||s,className:"hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed w-full px-4 py-3 font-medium text-white bg-transparent border border-gray-600 rounded-lg"},c?"Hide API Key Input":"Enter API Key Manually"),c&&n.createElement("div",{className:"w-full max-w-md mt-4"},n.createElement(ie,{initialApiKey:t,onApiKeySaved:e=>{s||(console.log("OnboardingScreen: API key saved, starting transition"),l(!0),i(null),o(!1),P.postMessage({command:"manualApiKeyVerified",apiKey:e}))}}))),r&&n.createElement("div",{className:"bg-red-900/50 max-w-md p-3 mt-4 border border-red-800 rounded-lg"},n.createElement("p",{className:"text-sm text-white"},r)),n.createElement("p",{className:"mt-8 text-sm text-gray-400"},"Don't have an account? ",n.createElement("button",{className:"hover:underline p-0 text-blue-400 bg-transparent border-0 cursor-pointer",onClick:()=>P.postMessage({command:"openExternalLink",url:"https://voicehype.ai/signup"})},"Sign up at voicehype.ai")))};var le=()=>n.createElement("div",{className:"flex flex-col items-center justify-center h-full p-6 text-center"},n.createElement(K,{className:"mb-8"}),n.createElement("div",{className:"flex items-center justify-center"},n.createElement("div",{className:"w-6 h-6 border-2 border-t-2 border-gray-200 border-t-blue-600 rounded-full animate-spin"}),n.createElement("span",{className:"ml-3 text-gray-400"},"Initializing...")));var ce=e=>{let{configuration:t,isActive:a,onActivate:n,onEdit:r,onDelete:i,onDuplicate:s}=e;const{settings:l}=t;return o.a.createElement("div",{className:`\n            border rounded-lg p-4 transition-all duration-200\n            ${a?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"}\n        `},o.a.createElement("div",{className:"flex items-start justify-between mb-3"},o.a.createElement("div",{className:"flex-1"},o.a.createElement("div",{className:"flex items-center gap-2 mb-1"},o.a.createElement("h3",{className:"font-semibold text-gray-900 dark:text-gray-100"},t.title),a&&o.a.createElement("span",{className:"px-2 py-1 text-xs bg-blue-500 text-white rounded-full"},"Active"),t.isDefault&&o.a.createElement("span",{className:"px-2 py-1 text-xs bg-gray-500 text-white rounded-full"},"Default")),t.description&&o.a.createElement("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-2"},t.description),o.a.createElement("p",{className:"text-xs text-gray-500 dark:text-gray-500"},["assemblyai"===l.service?"AssemblyAI":"Whisper",l.model,l.language.toUpperCase(),l.realtime?"Real-time":"Standard",l.optimizeEnabled?"AI-Optimized":"Basic"].join(" \u2022 ")))),o.a.createElement("div",{className:"flex items-center gap-2"},!a&&o.a.createElement(B,{onClick:()=>n(t.id),variant:"primary",size:"sm"},"Activate"),o.a.createElement(B,{onClick:()=>r(t),variant:"secondary",size:"sm"},"Edit"),o.a.createElement(B,{onClick:()=>s(t),variant:"secondary",size:"sm"},"Duplicate"),!t.isDefault&&o.a.createElement(B,{onClick:()=>i(t.id),variant:"danger",size:"sm"},"Delete")))};var me=e=>{let{isOpen:t,onClose:a,onSave:r,configuration:i,currentSettings:s}=e;const[l,c]=Object(n.useState)(""),[m,d]=Object(n.useState)(""),[u,p]=Object(n.useState)(s),[b,f]=Object(n.useState)({});Object(n.useEffect)(()=>{t&&(i?(c(i.title),d(i.description),p(i.settings)):(c(""),d(""),p(s)),f({}))},[t,i,s]);const v=(e,t)=>{p(a=>({...a,[e]:t}))};return t?o.a.createElement("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},o.a.createElement("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto"},o.a.createElement("div",{className:"flex items-center justify-between mb-6"},o.a.createElement("h2",{className:"text-xl font-semibold text-gray-900 dark:text-gray-100"},i?"Edit Configuration":"Create Configuration"),o.a.createElement("button",{onClick:a,className:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"},"\u2715")),o.a.createElement("div",{className:"space-y-4"},o.a.createElement("div",null,o.a.createElement("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Title *"),o.a.createElement("input",{type:"text",value:l,onChange:e=>c(e.target.value),className:`w-full px-3 py-2 border rounded-md dark:bg-gray-700 dark:text-gray-100 ${b.title?"border-red-500":"border-gray-300 dark:border-gray-600"}`,placeholder:"e.g., English Workflow, Urdu Settings"}),b.title&&o.a.createElement("p",{className:"text-red-500 text-xs mt-1"},b.title)),o.a.createElement("div",null,o.a.createElement("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Description"),o.a.createElement("textarea",{value:m,onChange:e=>d(e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-gray-100",rows:2,placeholder:"Optional description for this configuration"})),o.a.createElement("div",{className:"border-t pt-4"},o.a.createElement("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4"},"Configuration Settings"),o.a.createElement("div",{className:"space-y-4"},o.a.createElement(g,{service:u.service,onChange:e=>v("service",e)}),o.a.createElement(h,{service:u.service,model:u.model,onChange:e=>v("model",e)}),o.a.createElement(y,{service:u.service,model:u.model,language:u.language,onChange:e=>v("language",e)}),"assemblyai"===u.service&&"best"===u.model&&o.a.createElement(U,{label:"Real-time Transcription",checked:u.realtime,onChange:e=>v("realtime",e)}),o.a.createElement(U,{label:"AI Optimization",checked:u.optimizeEnabled,onChange:e=>v("optimizeEnabled",e)}),u.optimizeEnabled&&o.a.createElement(o.a.Fragment,null,o.a.createElement(N,{optimizationModel:u.optimizationModel,onChange:e=>v("optimizationModel",e)}),o.a.createElement(re,{value:u.customPrompt,onChange:e=>v("customPrompt",e),onModeChange:e=>{v("promptMode",e),console.log("Configuration Editor: Prompt mode changed to:",e)}}))))),o.a.createElement("div",{className:"flex justify-end gap-3 mt-6 pt-4 border-t"},o.a.createElement(B,{onClick:a,variant:"secondary"},"Cancel"),o.a.createElement(B,{onClick:()=>{if(!(()=>{const e={};return l.trim()||(e.title="Title is required"),f(e),0===Object.keys(e).length})())return;const e={title:l.trim(),description:m.trim(),settings:u};i&&(e.id=i.id),r(e),a()},variant:"primary"},i?"Update":"Create")))):null};var de=e=>{let{configurations:t,activeConfigurationId:a,currentSettings:r,onCreateConfiguration:i,onUpdateConfiguration:s,onDeleteConfiguration:l,onSwitchConfiguration:c}=e;const[m,d]=Object(n.useState)(!1),[u,g]=Object(n.useState)(null),p=e=>{g(e),d(!0)},b=e=>{const t={title:`${e.title} (Copy)`,description:e.description,settings:{...e.settings}};i(t)},h=e=>{window.confirm("Are you sure you want to delete this configuration?")&&l(e)};return o.a.createElement("div",{className:"p-6"},o.a.createElement("div",{className:"mb-6"},o.a.createElement("h2",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100"},"Configuration Manager"),o.a.createElement("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1"},"Create and manage configuration profiles for different workflows")),o.a.createElement("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6"},o.a.createElement("h3",{className:"font-medium text-blue-900 dark:text-blue-100 mb-2"},"Keyboard Shortcuts"),o.a.createElement("div",{className:"text-sm text-blue-700 dark:text-blue-300 space-y-1"},o.a.createElement("div",null,o.a.createElement("kbd",{className:"px-2 py-1 bg-blue-100 dark:bg-blue-800 rounded"},"Ctrl+Shift+PageUp")," - Switch to next configuration"),o.a.createElement("div",null,o.a.createElement("kbd",{className:"px-2 py-1 bg-blue-100 dark:bg-blue-800 rounded"},"Ctrl+Shift+PageDown")," - Switch to previous configuration"))),0===t.length?o.a.createElement("div",{className:"text-center py-12"},o.a.createElement("div",{className:"text-gray-400 dark:text-gray-600 mb-4"},o.a.createElement("svg",{className:"mx-auto h-12 w-12",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},o.a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"}))),o.a.createElement("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2"},"No configurations yet"),o.a.createElement("p",{className:"text-gray-600 dark:text-gray-400 mb-4"},"Create your first configuration profile to get started"),o.a.createElement(B,{onClick:()=>{g(null),d(!0)},variant:"primary"},"Create Configuration")):o.a.createElement("div",{className:"space-y-4"},t.map(e=>o.a.createElement(ce,{key:e.id,configuration:e,isActive:e.id===a,onActivate:c,onEdit:p,onDelete:h,onDuplicate:b}))),o.a.createElement(me,{isOpen:m,onClose:()=>d(!1),onSave:e=>{u?s(e):i(e)},configuration:u,currentSettings:r}))};var ue=e=>{let{message:t,type:a,isVisible:r,onClose:i,autoClose:s=!0,duration:l=3e3}=e;if(Object(n.useEffect)(()=>{if(r&&s){const e=setTimeout(()=>{i()},l);return()=>clearTimeout(e)}},[r,s,l,i]),!r)return null;return o.a.createElement("div",{className:"fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50 animate-slide-up"},o.a.createElement("div",{className:`\n                flex items-center gap-2 px-4 py-3 rounded-lg shadow-lg min-w-64 max-w-96\n                ${(()=>{switch(a){case"success":return"bg-green-600 text-white";case"error":return"bg-red-600 text-white";case"warning":return"bg-yellow-600 text-white";case"info":return"bg-blue-600 text-white";default:return"bg-gray-600 text-white"}})()}\n                transition-all duration-300 ease-in-out\n            `},o.a.createElement("span",{className:"text-lg"},(()=>{switch(a){case"success":return"\u2713";case"error":return"\u2715";case"warning":return"\u26a0";case"info":return"\u2139";default:return""}})()),o.a.createElement("span",{className:"flex-1 text-sm font-medium"},t),o.a.createElement("button",{onClick:i,className:"ml-2 text-white hover:text-gray-200 transition-colors","aria-label":"Close notification"},"\u2715")))};const ge={version:"1.4.1",title:"New Configuration Feature! \ud83c\udf89",features:["Create multiple configuration profiles for different workflows","Quick switching with Ctrl+Shift+PageUp/PageDown shortcuts","Save settings for English vs Urdu workflows","WhatsApp-specific configurations","Instant profile switching with snackbar notifications"],shortcuts:["Ctrl+Shift+PageUp - Switch to next configuration","Ctrl+Shift+PageDown - Switch to previous configuration"]};var pe=e=>{let{isOpen:t,onClose:a,onRemindLater:n}=e;return t?o.a.createElement("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},o.a.createElement("div",{className:"bg-white dark:bg-gray-800 rounded-lg w-full max-w-lg mx-4 overflow-hidden"},o.a.createElement("div",{className:"bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6"},o.a.createElement("div",{className:"flex items-center justify-between"},o.a.createElement("div",{className:"flex items-center gap-3"},o.a.createElement("span",{className:"text-2xl"},"\ud83c\udf89"),o.a.createElement("div",null,o.a.createElement("h2",{className:"text-xl font-bold"},ge.title),o.a.createElement("p",{className:"text-blue-100 text-sm"},"Version ",ge.version))),o.a.createElement("button",{onClick:a,className:"text-white hover:text-gray-200 transition-colors"},"\u2715"))),o.a.createElement("div",{className:"p-6"},o.a.createElement("div",{className:"mb-6"},o.a.createElement("h3",{className:"font-semibold text-gray-900 dark:text-gray-100 mb-3"},"New Features:"),o.a.createElement("ul",{className:"space-y-2"},ge.features.map((e,t)=>o.a.createElement("li",{key:t,className:"flex items-start gap-2"},o.a.createElement("span",{className:"text-green-500 mt-1"},"\u2713"),o.a.createElement("span",{className:"text-gray-700 dark:text-gray-300 text-sm"},e))))),o.a.createElement("div",{className:"mb-6"},o.a.createElement("h3",{className:"font-semibold text-gray-900 dark:text-gray-100 mb-3"},"Keyboard Shortcuts:"),o.a.createElement("div",{className:"space-y-2"},ge.shortcuts.map((e,t)=>o.a.createElement("div",{key:t,className:"flex items-center gap-2"},o.a.createElement("kbd",{className:"px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded text-xs font-mono"},e.split(" - ")[0]),o.a.createElement("span",{className:"text-gray-600 dark:text-gray-400 text-sm"},e.split(" - ")[1]))))),o.a.createElement("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6"},o.a.createElement("p",{className:"text-sm text-gray-600 dark:text-gray-400"},"\ud83d\udca1 ",o.a.createElement("strong",null,"Tip:"),' Create different configurations for your various workflows (e.g., "English Coding", "Urdu Content", "WhatsApp Messages") and switch between them instantly with keyboard shortcuts!')),o.a.createElement("div",{className:"flex justify-end gap-3"},o.a.createElement(B,{onClick:n,variant:"secondary"},"Remind me later"),o.a.createElement(B,{onClick:a,variant:"primary"},"Got it! \ud83c\udf89"))))):null};var be=()=>{const e=Object(n.useRef)({service:0,model:0,language:0,optimize:0,optimizationModel:0,translate:0,customPrompt:0,promptMode:0,realtime:0,sampleRate:0,deviceId:0,voiceCommands:0}),t=Object(n.useRef)({service:"",model:"",language:"",optimizeEnabled:!1,optimizationModel:"gpt-4o",translate:!1,realtime:!1,customPrompt:"",promptMode:"clean-up",sampleRate:44100,deviceId:null,apiKey:"",voiceCommandsEnabled:!0,isAuthenticated:!1}),[a,o]=Object(n.useState)(!1),[r,i]=Object(n.useState)(!1),[s,l]=Object(n.useState)(!1),[c,m]=Object(n.useState)(0),[u,p]=Object(n.useState)("assemblyai"),[b,f]=Object(n.useState)("whisper-1"),[v,x]=Object(n.useState)("best"),[k,w]=Object(n.useState)("en"),[E,C]=Object(n.useState)("Correct any grammar issues and improve clarity. Keep the meaning intact."),[S,O]=Object(n.useState)("clean-up"),[j,M]=Object(n.useState)(!0),[T,z]=Object(n.useState)("gpt-4o"),[D,A]=Object(n.useState)(!1),[$,R]=Object(n.useState)(!1),[I,B]=Object(n.useState)(22050),[G,F]=Object(n.useState)(null),[H,q]=Object(n.useState)([]),[_,J]=Object(n.useState)(!0),[Y,Q]=Object(n.useState)(""),[X,Z]=Object(n.useState)(!1),[ee,te]=Object(n.useState)(!0);Object(n.useEffect)(()=>{t.current={service:u,model:b,language:k,optimizeEnabled:j,optimizationModel:T,translate:D,realtime:$,customPrompt:E,promptMode:S,sampleRate:I,deviceId:G,apiKey:Y,voiceCommandsEnabled:_,isAuthenticated:X}},[u,b,k,j,T,D,$,E,S,I,G,Y,_,X]),Object(n.useEffect)(()=>{a&&(console.log("WebView: Requesting initial transcriptions"),P.postMessage({command:"getTranscriptions"}),console.log("WebView: Requesting audio devices on initial load"),P.postMessage({command:"getAudioDevices"}))},[a]);const[ae,ne]=Object(n.useState)([]),[oe,ce]=Object(n.useState)([]),[me,ge]=Object(n.useState)(null),[be,he]=Object(n.useState)(!1),[fe,ve]=Object(n.useState)({message:"",type:"info",isVisible:!1}),[ye,xe]=Object(n.useState)(!1),ke=Object(n.useRef)(null),we=Object(n.useCallback)((t,n,o)=>{const r=Date.now(),i=e.current[t];return a?r-i>1e3&&(e.current[t]=r,o(n),!0):(e.current[t]=r,o(n),!0)},[a]),Ee=Object(n.useCallback)(()=>({service:u,model:b,language:k,translate:D,realtime:$,optimizeEnabled:j,optimizationModel:T,customPrompt:E,promptMode:S,sampleRate:I,deviceId:G}),[u,b,k,D,$,j,T,E,S,I,G]),Ne=Object(n.useCallback)(function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info";ve({message:e,type:t,isVisible:!0})},[]),Ce=Object(n.useCallback)(()=>{ve(e=>({...e,isVisible:!1}))},[]),Se=Object(n.useCallback)(e=>{P.postMessage({command:"createConfiguration",data:e})},[]),Oe=Object(n.useCallback)(e=>{P.postMessage({command:"updateConfiguration",data:e})},[]),je=Object(n.useCallback)(e=>{P.postMessage({command:"deleteConfiguration",configId:e})},[]),Me=Object(n.useCallback)(e=>{P.postMessage({command:"switchConfiguration",configId:e})},[]);Object(n.useEffect)(()=>{(async()=>{console.log("Initializing authentication state..."),te(!0);try{P.postMessage({command:"getApiKey"})}catch(e){console.error("Error initializing authentication:",e),te(!1)}})()},[]),Object(n.useEffect)(()=>{const e=setTimeout(()=>{a||(console.log("WebView: Initial config still not loaded after component mount, requesting again..."),P.postMessage({command:"refreshConfigAndTheme"}))},2e3);return()=>{clearTimeout(e)}},[a]),Object(n.useEffect)(()=>{const n=n=>{var d,g,h;const v=n.data;switch(console.log("WebView: Received message from extension:",v.command),v.command){case"apiKeySaved":console.log("App: Received apiKeySaved message",v),v.success&&v.apiKey?(Q(v.apiKey),Z(!0),P.postMessage({command:"updateView",view:"main",apiKey:v.apiKey})):console.warn("App: apiKeySaved received but either unsuccessful or no API key");break;case"authStatusUpdated":console.log("App: Received authStatusUpdated",v),Z(v.authenticated),v.apiKey&&Q(v.apiKey);break;case"manualApiKeyVerified":console.log("App: Received manualApiKeyVerified",v),Q(v.apiKey),Z(!0);break;case"updateView":console.log("App: Received updateView",v),"main"===v.view&&(Z(!0),Q(v.apiKey||Y),te(!1));break;case"themeChanged":const n=(null===(d=v.data)||void 0===d?void 0:d.theme)||v.theme;console.log("WebView: Theme changed to:",n);break;case"transcription":ne(e=>[{id:Date.now().toString(),timestamp:(new Date).toISOString(),originalText:v.text,optimizedText:v.optimizedText,service:t.current.service,model:t.current.model,language:t.current.language},...e]);break;case"realtimePartialTranscript":console.log("WebView: Received partial transcript:",v.text),ke.current&&ke.current.handlePartialTranscript(v.text||"");break;case"realtimeFinalTranscript":console.log("WebView: Received final transcript:",v.text),ke.current&&ke.current.handleFinalTranscript(v.text||"");break;case"realtimeConnectionStatus":console.log("WebView: Received connection status:",v.status,v.message),ke.current&&ke.current.handleConnectionStatus(v.status,v.message);break;case"gracePeriodStatus":console.log("WebView: Received grace period status:",v.isActive,v.remainingSeconds),ke.current&&ke.current.handleGracePeriodStatus(v.isActive,v.remainingSeconds);break;case"initialConfiguration":if(v.options){console.log("Received initial configuration:",{...v.options,apiKey:v.options.apiKey?"(present)":"(not set)"});const{service:a,model:n,language:r,customPrompt:i,promptMode:s,optimize:l,optimizationModel:c,translate:m,realtime:d,apiKey:g,audioDevice:h,sampleRate:y}=v.options;void 0!==a&&(console.log(`Setting service to: ${a} (was: ${u})`),p(a)),void 0!==n&&(console.log(`Setting model to: ${n} (was: ${b})`),f(n)),void 0!==r&&(console.log(`Setting language to: ${r} (was: ${k})`),w(r)),void 0!==i&&(console.log(`Setting custom prompt (length: ${i.length})`),C(i)),void 0!==s&&(console.log(`Setting prompt mode to: ${s} (was: ${S})`),O(s)),void 0!==l&&(console.log(`Setting optimize to: ${l} (was: ${j})`),M(l)),void 0!==m&&(console.log(`Setting translate to: ${m} (was: ${D})`),A(m)),void 0!==d&&(console.log(`Setting realtime to: ${d} (was: ${$})`),R(d)),void 0!==c&&(console.log(`Setting optimization model to: ${c} (was: ${T})`),z(c)),void 0!==g&&(console.log("Setting API key:",g?"present":"not set"),Q(g),t.current.apiKey=g,Z(!!g)),void 0!==h&&(console.log("Setting audio device:",h),F(h)),void 0!==y&&(console.log(`Setting sample rate to: ${y} (was: ${I})`),B(y)),"assemblyai"===a&&void 0!==n&&x(n),o(!0),Object.keys(e.current).forEach(t=>{e.current[t]=Date.now()}),setTimeout(()=>{console.log("WebView state after initialization:"),console.log(`Service: ${u}`),console.log(`Model: ${b}`),console.log(`Language: ${k}`),console.log(`Optimize: ${j}`),console.log(`Custom prompt length: ${E.length}`),console.log(`Translate: ${D}`),console.log(`Realtime: ${$}`)},100)}else console.warn("Received initialConfiguration message with no options");break;case"updateOptions":if(v.options){console.log("Received updateOptions:",v.options);const{service:t,model:n,language:r,customPrompt:i,promptMode:s,optimize:l,optimizationModel:c,translate:m,realtime:d,audioDevice:g,sampleRate:h}=v.options;let y=!1;const E=!a||!0===v.force;console.log("Current state before updateOptions:"),console.log(`Service: ${u}, Model: ${b}, Language: ${k}`),console.log(`Optimize: ${j}, Translate: ${D}, Realtime: ${$}`),void 0!==t&&(E||Date.now()-e.current.service>1e3?t!==u&&(console.log(`Updating service from ${u} to ${t}`),p(t),e.current.service=Date.now(),y=!0):console.log(`Skipping service update (${t}) due to recent local update`)),void 0!==n&&(E||Date.now()-e.current.model>1e3?n!==b&&(console.log(`Updating model from ${b} to ${n}`),f(n),e.current.model=Date.now(),y=!0,"assemblyai"!==t&&"assemblyai"!==u||x(n)):console.log(`Skipping model update (${n}) due to recent local update`)),void 0!==r&&(E||Date.now()-e.current.language>1e3?r!==k&&(console.log(`Updating language from ${k} to ${r}`),w(r),e.current.language=Date.now(),y=!0):console.log(`Skipping language update (${r}) due to recent local update`)),void 0!==i&&(E||Date.now()-e.current.customPrompt>1e3?(console.log(`Updating custom prompt (length: ${i.length})`),C(i),e.current.customPrompt=Date.now(),y=!0):console.log("Skipping custom prompt update due to recent local update")),void 0!==s&&(E||Date.now()-e.current.promptMode>1e3?s!==S&&(console.log(`Updating prompt mode from ${S} to ${s}`),O(s),e.current.promptMode=Date.now(),y=!0):console.log(`Skipping prompt mode update (${s}) due to recent local update`)),void 0!==l&&(E||Date.now()-e.current.optimize>1e3?l!==j&&(console.log(`Updating optimize from ${j} to ${l}`),M(l),e.current.optimize=Date.now(),y=!0):console.log(`Skipping optimize update (${l}) due to recent local update`)),void 0!==c&&(E||Date.now()-e.current.optimizationModel>1e3?c!==T&&(console.log(`Updating optimization model from ${T} to ${c}`),z(c),e.current.optimizationModel=Date.now(),y=!0):console.log(`Skipping optimization model update (${c}) due to recent local update`)),void 0!==m&&(E||Date.now()-e.current.translate>1e3?m!==D&&(console.log(`Updating translate from ${D} to ${m}`),A(m),e.current.translate=Date.now(),y=!0):console.log(`Skipping translate update (${m}) due to recent local update`)),void 0!==d&&(E||Date.now()-e.current.realtime>1e3?d!==$&&(console.log(`Updating realtime from ${$} to ${d}`),R(d),e.current.realtime=Date.now(),y=!0):console.log(`Skipping realtime update (${d}) due to recent local update`)),void 0!==g&&(E||Date.now()-e.current.deviceId>1e3?g!==G&&(console.log(`Updating audio device from ${G} to ${g}`),F(g),e.current.deviceId=Date.now(),y=!0):console.log(`Skipping audio device update (${g}) due to recent local update`)),void 0!==h&&(E||Date.now()-e.current.sampleRate>1e3?h!==I&&(console.log(`Updating sample rate from ${I} to ${h}`),B(h),e.current.sampleRate=Date.now(),y=!0):console.log(`Skipping sample rate update (${h}) due to recent local update`)),!a&&y&&(console.log("Initial configuration loaded via updateOptions"),o(!0)),y?console.log("Configuration updated successfully"):console.log("No configuration changes applied")}else console.warn("Received updateOptions message with no options");break;case"recordingState":console.log("[WebView] [DEBUG] Received recordingState message:",v),console.log("[WebView] [DEBUG] Current recording state before update:",{isRecording:r,isPaused:s,elapsedTime:c}),void 0!==v.isRecording&&(console.log(`[WebView] [DEBUG] Updating isRecording from ${r} to ${v.isRecording}`),i(v.isRecording)),void 0!==v.isPaused&&(console.log(`[WebView] [DEBUG] Updating isPaused from ${s} to ${v.isPaused}`),l(v.isPaused)),void 0!==v.elapsedTime&&(console.log(`[WebView] [DEBUG] Updating elapsedTime from ${c} to ${v.elapsedTime}`),m(v.elapsedTime));break;case"updateTranscriptions":if(v.transcriptions){if(console.log(`WebView: Received ${v.transcriptions.length} transcriptions from extension`),console.log("WebView: Current transcriptions count before update:",ae.length),0===v.transcriptions.length)console.log("WebView: Received empty transcriptions array - this should clear the history");else if(v.transcriptions.length>0){var y;const e=v.transcriptions[0];console.log("WebView: Most recent transcription:",{id:e.id,timestamp:e.timestamp,originalTextLength:(null===(y=e.originalText)||void 0===y?void 0:y.length)||0,hasOptimized:!!e.optimizedText,service:e.service})}ne(v.transcriptions),console.log("WebView: Transcription state updated to",v.transcriptions.length,"items")}else console.warn("WebView: Received updateTranscriptions message with no transcriptions data");break;case"optimizationComplete":v.id&&v.optimizedText&&ne(e=>e.map(e=>e.id===v.id?Object.assign({},e,{optimizedText:v.optimizedText,isPreview:v.isPreview||!1}):e));break;case"previewOptimizationComplete":v.id&&v.optimizedText&&ne(e=>e.map(e=>e.id===v.id?Object.assign({},e,{optimizedText:v.optimizedText,isPreview:!0}):e));break;case"transcriptionDeleted":v.id&&ne(e=>e.filter(e=>e.id!==v.id));break;case"allTranscriptionsCleared":console.log("WebView: Received allTranscriptionsCleared message"),console.log("WebView: Current transcriptions count before clearing:",ae.length),ne([]),console.log("WebView: Transcriptions cleared");break;case"apiKeyResponse":console.log("Received API key response:",v.apiKey?"API key present":"No API key"),console.log("Authentication status from apiKeyResponse:",v.authenticated?"Authenticated":"Not authenticated"),Q(v.apiKey||""),Z(void 0!==v.authenticated?v.authenticated:!!v.apiKey),console.log("Turning off loading state after apiKeyResponse"),te(!1);break;case"error":console.error("Error from extension:",v.message);break;case"updateAudioDevices":v.devices&&q(v.devices);break;case"updateAudioSettings":if(v.settings){const{sampleRate:e,device:t}=v.settings;16e3===e&&!0===$?(console.log("[WebView] Updating sample rate for real-time mode:",e),B(e)):16e3!==e&&!1===$&&Be.current&&(console.log("[WebView] Restoring previous sample rate after real-time mode:",Be.current),B(Be.current)),void 0===t||a||(console.log("[WebView] Initial setting of device ID from backend:",t),F(t))}break;case"settings":void 0!==v.voiceCommandsEnabled&&J(v.voiceCommandsEnabled);break;case"debug":console.log("Debug from extension:",v.message);break;case"authStateChanged":void 0!==v.isAuthenticated&&(console.log("[WebView] Updating authentication state:",v.isAuthenticated),Z(v.isAuthenticated)),void 0!==v.apiKey&&(console.log("[WebView] Updating API key from authentication state change"),Q(v.apiKey),t.current.apiKey=v.apiKey);break;case"configurationsResponse":case"configurationsUpdated":(null===(g=v.data)||void 0===g?void 0:g.configurations)?ce(v.data.configurations):v.configurations&&ce(v.configurations),void 0!==(null===(h=v.data)||void 0===h?void 0:h.activeConfigurationId)?ge(v.data.activeConfigurationId):void 0!==v.activeConfigurationId&&ge(v.activeConfigurationId);break;case"configurationCreated":v.configuration&&(ce(e=>[...e,v.configuration]),Ne(`Configuration "${v.configuration.title}" created successfully`,"success"));break;case"configurationUpdated":v.configuration&&(ce(e=>e.map(e=>e.id===v.configuration.id?v.configuration:e)),Ne(`Configuration "${v.configuration.title}" updated successfully`,"success"));break;case"configurationDeleted":v.configId&&(ce(e=>e.filter(e=>e.id!==v.configId)),Ne("Configuration deleted successfully","success"));break;case"configurationSwitched":v.configId&&v.configName&&(ge(v.configId),Ne(`Switched to "${v.configName}"`,"success"));break;case"whatsNewStatus":v.shouldShow&&xe(!0)}};window.addEventListener("message",n),P.postMessage({command:"getOptions"}),P.postMessage({command:"getTranscriptions"}),P.postMessage({command:"refreshConfigAndTheme"});const d=setTimeout(()=>{a||(console.log("WebView: Initial config not loaded after 5 seconds, retrying..."),P.postMessage({command:"refreshConfigAndTheme"}))},5e3);return()=>{window.removeEventListener("message",n),clearTimeout(d)}},[we,a,u,b,k,j,T,D,$,E,E.length,I,G,Y,ae.length,r,s,c]),Object(n.useEffect)(()=>{a&&(P.postMessage({command:"getConfigurations"}),P.postMessage({command:"getWhatsNewStatus",version:"1.4.1"}))},[a]);const Te=Object(n.useCallback)(()=>{xe(!1),P.postMessage({command:"markWhatsNewSeen",version:"1.4.1"})},[]),ze=Object(n.useCallback)(()=>{xe(!1)},[]),De=Object(n.useCallback)(()=>{console.log("[WebView] [DEBUG] Start recording called, current state:",{isRecording:r,isPaused:s}),P.postMessage({command:"startRecording",options:{service:u,model:b,language:k,optimize:j,optimizationModel:j?T:"",customPrompt:j?E:"",translate:D,realtime:$}}),console.log("[WebView] [DEBUG] Optimistically updating UI state to recording"),i(!0),l(!1)},[u,b,k,j,T,E,D,$,r,s]),Ae=Object(n.useCallback)(()=>{console.log("[WebView] [DEBUG] Stop recording called, current state:",{isRecording:r,isPaused:s}),P.postMessage({command:"stopRecording"}),console.log("[WebView] [DEBUG] Optimistically updating UI state to stopped"),i(!1),l(!1)},[r,s]),$e=Object(n.useCallback)(()=>{console.log("[WebView] [DEBUG] Pause recording called, current state:",{isRecording:r,isPaused:s}),P.postMessage({command:"pauseRecording"}),console.log("[WebView] [DEBUG] Optimistically updating UI state to paused"),l(!0)},[r,s]),Re=Object(n.useCallback)(()=>{console.log("[WebView] [DEBUG] Resume recording called, current state:",{isRecording:r,isPaused:s}),P.postMessage({command:"resumeRecording"}),console.log("[WebView] [DEBUG] Optimistically updating UI state to resumed"),l(!1)},[r,s]),Pe=Object(n.useCallback)(()=>{console.log("[WebView] [DEBUG] Cancel recording called, current state:",{isRecording:r,isPaused:s}),P.postMessage({command:"cancelRecording"}),console.log("[WebView] [DEBUG] Optimistically updating UI state to cancelled"),i(!1),l(!1)},[r,s]),Ve=Object(n.useCallback)(t=>{e.current.service=Date.now(),console.log(`WebView: Service change requested from ${u} to ${t}`),console.log(`WebView: Current model before change: ${b}`),console.log(`WebView: Current language before change: ${k}`);let a=b,n=k,o=!1;if("lemonfox"===t)"assemblyai"===u&&(console.log(`WebView: Saving AssemblyAI model ${b} for future use`),x(b)),a="whisper-1",console.log(`WebView: Setting model to ${a} for OpenAI service`),e.current.model=Date.now();else if("assemblyai"===t){a=v,console.log(`WebView: Restoring AssemblyAI model to ${a}`),e.current.model=Date.now();const t=["en","es","fr","de","it","pt","nl","hi","ja","zh","ar"];"auto"!==k&&t.includes(k)||(console.log(`WebView: Language ${k} not supported by AssemblyAI, changing to en`),n="en",o=!0,e.current.language=Date.now()),D&&(console.log("WebView: Disabling translate for AssemblyAI service"),e.current.translate=Date.now(),A(!1))}p(t),f(a),o&&w(n),P.postMessage({command:"updateOptions",options:{service:t,model:a,...o?{language:n}:{},..."assemblyai"===t&&D?{translate:!1}:{}}}),console.log(`WebView: Service changed to ${t}, model set to ${a}, language: ${o?n:"unchanged"}`)},[u,b,k,v,D]),Ie=Object(n.useCallback)(t=>{e.current.model=Date.now(),f(t),P.postMessage({command:"updateOptions",options:{model:t}})},[]),Le=Object(n.useCallback)(t=>{e.current.language=Date.now(),w(t),P.postMessage({command:"updateOptions",options:{language:t}})},[]),Ue=Object(n.useCallback)(t=>{e.current.customPrompt=Date.now(),C(t),window.promptUpdateTimeout&&clearTimeout(window.promptUpdateTimeout),window.promptUpdateTimeout=setTimeout(()=>{P.postMessage({command:"updateOptions",options:{customPrompt:t}}),console.log(`Custom prompt updated (debounced): ${t.substring(0,30)}${t.length>30?"...":""}`)},500)},[]);Object(n.useEffect)(()=>()=>{window.promptUpdateTimeout&&clearTimeout(window.promptUpdateTimeout)},[]);const We=Object(n.useCallback)(t=>{e.current.optimize=Date.now(),M(t),P.postMessage({command:"updateOptions",options:{optimize:t}})},[]),Ke=Object(n.useCallback)(t=>{a&&(A(t),e.current.translate=Date.now(),P.postMessage({command:"updateOptions",options:{translate:t}}))},[a]),Be=Object(n.useRef)(0),Ge=Object(n.useCallback)(e=>{a&&(console.log("[WebView] Toggling real-time transcription:",e),R(e),e?(Be.current=I,console.log("[WebView] Storing current sample rate before real-time:",Be.current),B(16e3)):Be.current&&(console.log("[WebView] Restoring sample rate after real-time:",Be.current),B(Be.current)),P.postMessage({command:"updateOptions",options:{realtime:e}}))},[a,I]),Fe=Object(n.useCallback)(t=>{e.current.optimizationModel=Date.now(),z(t),P.postMessage({command:"updateOptions",options:{optimizationModel:t}}),console.log(`WebView: Optimization model changed to ${t}`)},[]),He=(Object(n.useCallback)(t=>{e.current.voiceCommands=Date.now(),console.log(`WebView: Voice commands ${t?"enabled":"disabled"}`),J(t),P.postMessage({command:"updateOptions",options:{voiceCommandsEnabled:t}})},[]),Object(n.useCallback)(e=>{const t=ae.find(t=>t.id===e);t&&P.postMessage({command:"copyToClipboard",text:t.originalText})},[ae])),qe=Object(n.useCallback)(e=>{const t=ae.find(t=>t.id===e);t&&t.optimizedText&&P.postMessage({command:"copyToClipboard",text:t.optimizedText})},[ae]),_e=Object(n.useCallback)((e,t)=>{const a=ae.find(t=>t.id===e);a&&t?(P.postMessage({command:"previewOptimization",id:e,text:a.originalText,customPrompt:E,optimizationModel:T}),ne(t=>t.map(t=>t.id===e?Object.assign({},t,{optimizedText:"Previewing optimization...",isPreview:!0}):t))):t||ne(t=>t.map(t=>t.id===e&&t.isPreview?Object.assign({},t,{optimizedText:void 0,isPreview:!1}):t))},[ae,E,T]),Je=Object(n.useCallback)(e=>{const t=ae.find(t=>t.id===e);t&&(P.postMessage({command:"optimizeTranscription",id:e,text:t.originalText,customPrompt:E,optimizationModel:T}),ne(t=>t.map(t=>t.id===e?Object.assign({},t,{optimizedText:"Optimizing...",isPreview:!1}):t)))},[ae,E,T]),Ye=Object(n.useCallback)(e=>{console.log("[WebView] Changing sample rate to:",e),B(e),P.postMessage({command:"updateSetting",key:"voicehype.audio.sampleRate",value:e})},[]),Qe=Object(n.useCallback)(t=>{F(t),e.current.deviceId=Date.now(),window.deviceUpdateTimeout&&clearTimeout(window.deviceUpdateTimeout),window.deviceUpdateTimeout=setTimeout(()=>{const e=null===t?"":t;P.postMessage({command:"updateAudioDevice",deviceId:e}),console.log(`[WebView] Sending audio device change to backend (debounced): ${e}`)},500)},[]);return Object(n.useEffect)(()=>()=>{window.deviceUpdateTimeout&&clearTimeout(window.deviceUpdateTimeout)},[]),n.createElement("div",{className:"sm:px-0 flex flex-col h-full px-1"},ee?n.createElement(le,null):X||Y?(console.log("App: Rendering main view - isAuthenticated:",X,"apiKey:",Y?"present":"not set"),n.createElement(n.Fragment,null,n.createElement("div",{className:"flex items-center justify-center py-4 mb-2"},n.createElement(K,null)),n.createElement("div",{className:"mb-4"},n.createElement(ie,{initialApiKey:Y,onApiKeySaved:e=>{Q(e),Z(!0)}})),n.createElement("div",{className:"mb-5"},n.createElement(d,{isRecording:r,isPaused:s,elapsedTime:c,onStart:De,onStop:Ae,onPause:$e,onResume:Re,onCancel:Pe})),$&&n.createElement("div",{className:"mb-5"},n.createElement(L,{ref:ke,isVisible:$,isRecording:r})),n.createElement("div",{className:"mb-4 space-y-4"},n.createElement(g,{service:u,onChange:Ve}),n.createElement(h,{service:u,model:b,onChange:Ie}),n.createElement(y,{service:u,model:b,language:k,onChange:Le})),n.createElement("div",{className:"mb-4"},n.createElement(W,{sampleRate:I,deviceId:G,availableDevices:H,onSampleRateChange:Ye,onDeviceChange:Qe})),n.createElement("div",{className:"mb-4 space-y-2"},"lemonfox"===u&&n.createElement(U,{checked:D,onChange:Ke,label:"Translate to English"}),"assemblyai"===u&&"best"===b&&n.createElement(U,{checked:$,onChange:Ge,label:"Use real-time transcription"}),n.createElement(U,{checked:j,onChange:We,label:"Optimize transcription"})),j&&n.createElement("div",{className:"mb-4 space-y-4"},n.createElement("div",null,n.createElement("h2",{className:"text-md mb-2 font-medium"},"Optimization Mode"),n.createElement(re,{value:E,onChange:Ue,onModeChange:e=>{O(e),P.postMessage({command:"setActivePromptMode",modeId:e})}})),n.createElement("div",null,n.createElement("h2",{className:"text-md mb-2 font-medium"},"Optimization Model"),n.createElement(N,{optimizationModel:T,onChange:Fe}))),n.createElement("div",{className:"flex-grow overflow-hidden"},n.createElement("h2",{className:"text-md mb-2 font-medium"},"Transcription History"),n.createElement("div",{className:"h-[calc(100%-1.75rem)]"},"            ",n.createElement(V,{transcriptions:ae,onCopyOriginal:He,onCopyOptimized:qe,onOptimize:Je,onPreviewOptimization:_e}))),be&&n.createElement("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"},n.createElement("div",{className:"bg-white dark:bg-gray-800 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden"},n.createElement("div",{className:"flex items-center justify-between p-4 border-b"},n.createElement("h2",{className:"text-xl font-semibold"},"Configuration Manager"),n.createElement("button",{onClick:()=>he(!1),className:"hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 text-gray-500"},"\u2715")),n.createElement("div",{className:"overflow-y-auto max-h-[calc(90vh-4rem)]"},n.createElement(de,{configurations:oe,activeConfigurationId:me,currentSettings:Ee(),onCreateConfiguration:Se,onUpdateConfiguration:Oe,onDeleteConfiguration:je,onSwitchConfiguration:Me})))),n.createElement(ue,{message:fe.message,type:fe.type,isVisible:fe.isVisible,onClose:Ce}),n.createElement(pe,{isOpen:ye,onClose:Te,onRemindLater:ze}))):n.createElement(se,{apiKey:Y}))};let he=!1;const fe=e=>{he||(document.documentElement.classList.add(e?"vscode-dark":"vscode-light"),document.body.classList.add(e?"vscode-dark":"vscode-light"),e&&document.documentElement.classList.add("dark"),console.log("VS Code theme applied from extension:",e?"dark":"light"),he=!0)},ve=e=>{var t;const a=e.data;if("initialConfiguration"===a.command&&void 0!==(null===(t=a.options)||void 0===t?void 0:t.theme)){const e="dark"===a.options.theme;fe(e),window.removeEventListener("message",ve)}else if("themeChanged"===a.command){var n;const e=(null===(n=a.data)||void 0===n?void 0:n.theme)||a.theme;fe("dark"===e),window.removeEventListener("message",ve)}};window.addEventListener("message",ve),window.addEventListener("message",e=>{const t=e.data;if("themeChanged"===t.command){var a;(e=>{document.documentElement.classList.remove("vscode-dark","vscode-light","dark"),document.body.classList.remove("vscode-dark","vscode-light"),document.documentElement.classList.add(e?"vscode-dark":"vscode-light"),document.body.classList.add(e?"vscode-dark":"vscode-light"),e&&document.documentElement.classList.add("dark"),console.log("VS Code theme updated:",e?"dark":"light")})("dark"===((null===(a=t.data)||void 0===a?void 0:a.theme)||t.theme))}}),setTimeout(()=>{he||(console.log("Requesting theme from extension..."),P.postMessage({command:"refreshConfigAndTheme"}))},3e3),r.render(n.createElement(n.StrictMode,null,n.createElement(be,null)),document.getElementById("root"))},4:function(e,t,a){e.exports=a(14)}},[[4,1,2]]]);
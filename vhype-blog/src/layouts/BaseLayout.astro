---
import "../styles/global.css";
import Navbar from "../components/Navbar.astro";
import { Button } from "../components/ui/button";
export interface Props {
  title: string;
  description: string;
  image?: string;
  canonical?: string;
  type?: "website" | "article";
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  tags?: string[];
}
const {
  title,
  description,
  image = "/og-default.jpg",
  canonical,
  type = "website",
  publishedTime,
  modifiedTime,
  author,
  tags = [],
} = Astro.props;
const siteUrl =
  (import.meta as any).env.PUBLIC_SITE_URL || "https://blog.voicehype.ai";
const siteName = (import.meta as any).env.PUBLIC_SITE_NAME || "VoiceHype Blog";
const fullTitle = title.includes(siteName) ? title : `${title} | ${siteName}`;
const canonicalUrl = canonical || new URL(Astro.url.pathname, siteUrl).href;
const imageUrl =
  image && image.startsWith("http") ? image : new URL(image, siteUrl).href;
---

<!doctype html>
<html lang="en" class="scroll-smooth">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />

    <!-- Primary Meta Tags -->
    <title>{fullTitle}</title>
    <meta name="title" content={fullTitle} />
    <meta name="description" content={description} />
    <link rel="canonical" href={canonicalUrl} />
    
    <!-- RSS Feed Discovery -->
    <link
      rel="alternate"
      type="application/rss+xml"
      title="VoiceHype Blog RSS Feed"
      href="/rss.xml"
    />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content={type} />
    <meta property="og:url" content={canonicalUrl} />
    <meta property="og:title" content={fullTitle} />
    <meta property="og:description" content={description} />
    <meta property="og:image" content={imageUrl} />
    <meta property="og:site_name" content={siteName} />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content={canonicalUrl} />
    <meta property="twitter:title" content={fullTitle} />
    <meta property="twitter:description" content={description} />
    <meta property="twitter:image" content={imageUrl} />

    <!-- Article specific meta tags -->
    {
      type === "article" && (
        <>
          {publishedTime && (
            <meta property="article:published_time" content={publishedTime} />
          )}
          {modifiedTime && (
            <meta property="article:modified_time" content={modifiedTime} />
          )}
          {author && <meta property="article:author" content={author} />}
          {tags.map((tag: string) => (
            <meta property="article:tag" content={tag} />
          ))}
        </>
      )
    }

    <!-- Structured Data -->
    <script
      type="application/ld+json"
      is:inline
      set:html={JSON.stringify({
        "@context": "https://schema.org",
        "@type": type === "article" ? "BlogPosting" : "WebSite",
        headline: title,
        description: description,
        image: imageUrl,
        url: canonicalUrl,
        datePublished: publishedTime,
        dateModified: modifiedTime || publishedTime,
        author: author
          ? {
              "@type": "Person",
              name: author,
            }
          : undefined,
        publisher: {
          "@type": "Organization",
          name: siteName,
          url: siteUrl,
          logo: {
            "@type": "ImageObject",
            url: new URL("/favicon.svg", siteUrl).href,
          },
        },
        mainEntityOfPage: {
          "@type": "WebPage",
          "@id": canonicalUrl,
        },
      })}
    />

    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    <!-- Performance optimizations -->
    <link rel="dns-prefetch" href="https://supabase.voicehype.ai" />

    <!-- Theme detection script (inline for performance) -->
    <script is:inline>
      // Check for saved theme preference or default to 'dark'
      const theme = localStorage.getItem("theme") || "dark";
      if (theme === "dark") {
        document.documentElement.classList.add("dark");
      } else {
        document.documentElement.classList.remove("dark");
      }
    </script>
  </head>

  <body class="bg-background text-foreground transition-colors duration-200">
    <!-- Background overlay for dark mode -->
    <div class="bg-background -z-10 fixed inset-0"></div>
    <!-- Skip to main content for accessibility -->
    <a
      href="#main-content"
      class="focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 px-4 py-2 text-white bg-blue-600 rounded-md sr-only"
    >
      Skip to main content
    </a>

    <div class="flex flex-col min-h-screen">
      <!-- Header -->
      <Navbar />

      <!-- Main content -->
      <main id="main-content" class="flex-1">
        <slot />
      </main>

      <!-- Footer -->
      <footer class="bg-muted border-border border-t">
        <div class="max-w-7xl sm:px-6 lg:px-8 px-4 py-12 mx-auto">
          <div class="md:grid-cols-3 grid grid-cols-1 gap-8">
            <!-- About -->
            <div>
              <a href="/" class="voicehype-logo inline-block mb-4">
                <svg width="40" height="30" viewBox="0 0 40 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <!-- Monitor outline with white fill -->
                  <rect x="2" y="2" width="36" height="20" rx="2" fill="currentColor" />
                  <rect x="2" y="2" width="36" height="20" rx="2" stroke="var(--vhype-primary)" stroke-width="1.5" />
                  <!-- Sound wave visualization -->
                  <path d="M7 14 L10 9 L13 14 L16 9 L19 14 L22 9 L25 14 L28 9 L31 14 L34 9" stroke="var(--vhype-primary)"
                    stroke-width="1.5" stroke-linecap="round" />
                  <!-- Stand -->
                  <line x1="20" y1="22" x2="20" y2="26" stroke="var(--vhype-primary)" stroke-width="1.5" />
                  <line x1="14" y1="26" x2="26" y2="26" stroke="var(--vhype-primary)" stroke-width="1.5" />
                </svg>
                <h1 class="logo-text">Voice<span class="hype-text">Hype</span></h1>
              </a>
              <p class="text-muted-foreground mb-4">
                Insights and tutorials on voice AI, AI
                development, and the future of developer tools.
              </p>
              <div class="flex space-x-4">
                <Button variant="link" className="p-0 h-auto" asChild>
                  <a
                    href="https://voicehype.ai"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    VoiceHype Website
                  </a>
                </Button>
                <Button variant="link" className="p-0 h-auto" asChild>
                  <a
                    href="https://marketplace.visualstudio.com/items?itemName=voicehype.voicehype"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    VS Code Extension
                  </a>
                </Button>
              </div>
            </div>

            <!-- Quick Links -->
            <div>
              <h3 class="mb-4 text-lg font-semibold">Quick Links</h3>
              <ul class="space-y-2">
                <li>
                  <Button variant="ghost" className="p-0 h-auto" asChild
                    ><a
                      href="/"
                      class="text-muted-foreground hover:text-primary">Home</a
                    ></Button>
                </li>
                <li>
                  <Button variant="ghost" className="p-0 h-auto" asChild
                    ><a
                      href="/about"
                      class="text-muted-foreground hover:text-primary">About</a
                    ></Button>
                </li>
                <li>
                  <Button variant="ghost" className="p-0 h-auto" asChild
                    ><a
                      href="/rss.xml"
                      class="text-muted-foreground hover:text-primary"
                      >RSS Feed</a
                    ></Button>
                </li>
              </ul>
            </div>

            <!-- Contact -->
            <div>
              <h3 class="mb-4 text-lg font-semibold">Connect</h3>
              <p class="text-muted-foreground mb-2">
                Stay updated with the latest from VoiceHype
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  </body>
</html>

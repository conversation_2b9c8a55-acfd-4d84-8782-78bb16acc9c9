import { createClient } from '@supabase/supabase-js'
// Import supabase URL and anon key from environment variables
const supabaseUrl = import.meta.env.PUBLIC_SUPABASE_URL || ''
const supabaseAnonKey = import.meta.env.PUBLIC_SUPABASE_ANON_KEY || ''
// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: false, // We don't need auth persistence for public blog
  },
  db: {
    schema: 'blog'
  }
})

// Types for our blog data
export interface BlogArticle {
  id: string
  title: string
  slug: string
  content?: string
  excerpt: string
  cover_image_url?: string
  author_id: string
  status: 'draft' | 'published' | 'archived'
  published_at: string | null
  created_at: string
  updated_at: string
  meta_title?: string
  meta_description?: string
  featured: boolean
  view_count: number
  author_username: string
  author_name: string
  author_avatar?: string
  author_bio?: string
}

export interface BlogArticlePreview {
  id: string
  title: string
  slug: string
  excerpt: string
  cover_image_url?: string
  author_id: string
  published_at: string // safe because only published returned
  created_at: string
  updated_at: string
  meta_title?: string
  meta_description?: string
  featured: boolean
  view_count: number
  author_username: string
  author_name: string
  author_avatar?: string
}

// Helper function to transform article data
function transformArticleData(article: any): BlogArticlePreview {
  return {
    id: article.id,
    title: article.title,
    slug: article.slug,
    excerpt: article.excerpt || '',
    cover_image_url: article.cover_image_url,
    author_id: article.author_id,
    published_at: article.published_at || article.created_at,
    created_at: article.created_at,
    updated_at: article.updated_at,
    meta_title: article.meta_title,
    meta_description: article.meta_description,
    featured: article.featured || false,
    view_count: article.article_views?.view_count || 0, // Fixed: changed from view_count to article_views
    author_username: article.profiles?.username || '',
    author_name: article.profiles?.full_name || '',
    author_avatar: article.profiles?.avatar_url
  };
}

// NEW METHODS
export async function getLatestPostPreviews(limit: number = 5): Promise<BlogArticlePreview[]> {
  try {
    const { data, error } = await supabase
      .from('articles')
      .select(`
        id, title, slug, excerpt, cover_image_url, author_id, published_at, 
        created_at, updated_at, meta_title, meta_description, featured,
        profiles!articles_author_id_fkey(username, full_name, avatar_url),
        article_views(view_count)
      `)
      .eq('status', 'published')
      .order('published_at', { ascending: false })
      .limit(limit);
    if (error) {
      console.error('Error fetching latest post previews:', error);
      return [];
    }
    return data.map(transformArticleData);
  } catch (error) {
    console.error('Error fetching latest post previews:', error);
    return [];
  }
}

export async function getPopularPostPreviews(limit: number = 5): Promise<BlogArticlePreview[]> {
  try {
    const { data, error } = await supabase
      .from('article_views')
      .select(`
        view_count,
        articles!article_views_article_id_fkey(
          id, title, slug, excerpt, cover_image_url, author_id, published_at, 
          created_at, updated_at, meta_title, meta_description, featured, status,
          profiles!articles_author_id_fkey(username, full_name, avatar_url)
        )
      `)
      .eq('articles.status', 'published')
      .order('view_count', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching popular post previews:', error);
      return [];
    }

    return data
      .filter((item: any) => item.articles) // Filter out null articles
      .map((item: any) => ({
        id: item.articles.id,
        title: item.articles.title,
        slug: item.articles.slug,
        excerpt: item.articles.excerpt || '',
        cover_image_url: item.articles.cover_image_url,
        author_id: item.articles.author_id,
        published_at: item.articles.published_at || item.articles.created_at,
        created_at: item.articles.created_at,
        updated_at: item.articles.updated_at,
        meta_title: item.articles.meta_title,
        meta_description: item.articles.meta_description,
        featured: item.articles.featured || false,
        view_count: item.view_count || 0,
        author_username: item.articles.profiles?.username || '',
        author_name: item.articles.profiles?.full_name || '',
        author_avatar: item.articles.profiles?.avatar_url
      }));
  } catch (error) {
    console.error('Error fetching popular post previews:', error);
    return [];
  }
}

export async function getAllArticlePreviews(limit: number = 10, offset: number = 0): Promise<BlogArticlePreview[]> {
  try {
    const { data, error } = await supabase
      .from('articles')
      .select(`
        id, title, slug, excerpt, cover_image_url, author_id, published_at, 
        created_at, updated_at, meta_title, meta_description, featured,
        profiles!articles_author_id_fkey(username, full_name, avatar_url),
        article_views(view_count)
      `)
      .eq('status', 'published')
      .order('published_at', { ascending: false })
      .range(offset, offset + limit - 1);
    if (error) {
      console.error('Error fetching all article previews:', error);
      return [];
    }
    return data.map(transformArticleData);
  } catch (error) {
    console.error('Error fetching all article previews:', error);
    return [];
  }
}

// Keep existing methods
export async function getArticleBySlug(slug: string): Promise<BlogArticle | null> {
  try {
    const { data, error } = await supabase.rpc('get_article_with_view_increment', {
      slug_param: slug
    })
    if (error) {
      console.error('Error fetching article:', error)
      return null
    }
    return data?.[0] || null
  } catch (error) {
    console.error('Error fetching article:', error)
    return null
  }
}

// Get all unique tags with article counts
export async function getAllTagsWithCount() {
  try {
    const { data, error } = await supabase
      .from('article_tags')
      .select(`
        tag_name,
        articles!inner(id)
      `)
      .eq('articles.status', 'published');

    if (error) {
      console.error('Error fetching tags:', error);
      return [];
    }

    // Count articles per tag
    const tagCounts: Record<string, number> = {};
    data.forEach(item => {
      const tagName = item.tag_name;
      tagCounts[tagName] = (tagCounts[tagName] || 0) + 1;
    });

    // Convert to array of objects
    return Object.entries(tagCounts).map(([name, count]) => ({ name, count }));
  } catch (error) {
    console.error('Error fetching tags:', error);
    return [];
  }
}

// Get articles by tag name
export async function getArticlesByTag(tagName: string, limit = 10, offset = 0) {
  try {
    // First, get all article IDs that have this tag
    const { data: tagData, error: tagError } = await supabase
      .from('article_tags')
      .select('article_id')
      .eq('tag_name', tagName);

    if (tagError) {
      console.error('Error fetching article IDs by tag:', tagError);
      return [];
    }

    // If no articles found with this tag, return empty array
    if (!tagData || tagData.length === 0) {
      return [];
    }

    // Extract article IDs
    const articleIds = tagData.map(item => item.article_id);

    // Now fetch the articles with these IDs
    const { data, error } = await supabase
      .from('articles')
      .select(`
        id, title, slug, excerpt, cover_image_url, author_id, published_at, 
        created_at, updated_at, meta_title, meta_description, featured,
        profiles!articles_author_id_fkey(username, full_name, avatar_url),
        article_views(view_count)
      `)
      .in('id', articleIds)
      .eq('status', 'published')
      .order('published_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error fetching articles by tag:', error);
      return [];
    }

    return data.map(transformArticleData);
  } catch (error) {
    console.error('Error fetching articles by tag:', error);
    return [];
  }
}

export async function searchPublishedArticles(searchTerm: string): Promise<BlogArticlePreview[]> {
  try {
    const { data, error } = await supabase.rpc('search_published_articles', {
      search_term: searchTerm
    })
    if (error) {
      console.error('Error searching articles:', error)
      return []
    }
    return data || []
  } catch (error) {
    console.error('Error searching articles:', error)
    return []
  }
}

// Utility functions remain unchanged
export function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

export function calculateReadingTime(content: string): number {
  const wordsPerMinute = 200
  const wordCount = content.split(/\s+/).filter(word => word.length > 0).length
  return Math.ceil(wordCount / wordsPerMinute)
}

export function generateExcerpt(content: string, maxLength: number = 160): string {
  // Remove markdown syntax and HTML tags
  const plainText = content
    .replace(/#{1,6}\s+/g, '') // Remove headers
    .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
    .replace(/\*(.*?)\*/g, '$1') // Remove italic
    .replace(/`(.*?)`/g, '$1') // Remove inline code
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Remove links, keep text
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/\n+/g, ' ') // Replace newlines with spaces
    .trim()
  if (plainText.length <= maxLength) {
    return plainText
  }
  // Find the last complete word within the limit
  const truncated = plainText.substring(0, maxLength)
  const lastSpaceIndex = truncated.lastIndexOf(' ')

  if (lastSpaceIndex > 0) {
    return truncated.substring(0, lastSpaceIndex) + '...'
  }

  return truncated + '...'
}
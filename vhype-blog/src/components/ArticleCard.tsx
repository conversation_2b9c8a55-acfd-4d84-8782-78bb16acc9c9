// src/components/ArticleCard.tsx
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { formatDate } from '../lib/supabase';
import type { BlogArticlePreview } from '../lib/supabase';

interface ArticleCardProps {
  article: BlogArticlePreview;
}

export default function ArticleCard({ article }: ArticleCardProps) {
  return (
    <a 
      href={`/${article.slug}`} 
      className="focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 block rounded-lg"
      aria-label={`Read article: ${article.title}`}
    >
      <Card className="group hover:shadow-lg hover:border-primary/20 flex flex-col h-full transition-all duration-200 border-0">
        {/* Image section with fixed aspect ratio */}
        <div className="aspect-video bg-muted overflow-hidden rounded-t-lg">
          {article.cover_image_url ? (
            <img
              src={article.cover_image_url}
              alt={article.title}
              className="group-hover:scale-105 object-cover w-full h-full transition-transform duration-200"
              loading="lazy"
            />
          ) : (
            <div className="flex items-center justify-center w-full h-full">
              <svg xmlns="http://www.w3.org/2000/svg" className="text-muted-foreground w-12 h-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
          )}
        </div>
        
        {/* Content section that grows to fill available space */}
        <div className="flex flex-col flex-grow p-6">
          <div className="flex-grow">
            {/* Date and views */}
            <div className="text-muted-foreground flex items-center gap-2 mb-3 text-sm">
              <time dateTime={article.published_at}>
                {formatDate(article.published_at)}
              </time>
              <span>•</span>
              <Badge variant="secondary" className="text-xs">
                {article.view_count} views
              </Badge>
            </div>
            
            {/* Title and excerpt */}
            <CardTitle className="line-clamp-2 group-hover:text-primary mb-2 transition-colors">
              {article.title}
            </CardTitle>
            <CardDescription className="line-clamp-3">
              {article.excerpt}
            </CardDescription>
          </div>
          
          {/* Author and read more - always at bottom */}
          <div className="border-border flex items-center justify-between pt-4 mt-4 border-t">
            <div className="flex items-center gap-2">
              <div className="bg-muted relative flex items-center justify-center w-6 h-6 overflow-hidden rounded-full">
                {article.author_avatar ? (
                  <img
                    src={article.author_avatar}
                    alt={article.author_name || 'Author'}
                    className="object-cover w-full h-full"
                  />
                ) : (
                  <span className="text-muted-foreground text-xs font-medium">
                    {(article.author_name || 'A').split(' ').map(n => n[0]).join('').toUpperCase()}
                  </span>
                )}
              </div>
              <span className="text-muted-foreground text-sm">
                {article.author_name}
              </span>
            </div>
            <span className="text-primary hover:underline text-sm font-medium">
              Read more →
            </span>
          </div>
        </div>
      </Card>
    </a>
  );
}
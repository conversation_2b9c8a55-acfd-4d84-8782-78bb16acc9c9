import { useMemo, useEffect, useRef } from 'react';
import { MarkdownProcessor } from '../markdown/index';
import Tip from './blog/Tip';
import TryOutVoiceHype from './blog/TryOutVoiceHype';
import SubscribeNewsletter from './blog/SubscribeNewsletter';

// Import Prism.js and required languages
import Prism from 'prismjs';
import 'prismjs/themes/prism-tomorrow.css'; // Dark theme
import 'prismjs/components/prism-javascript';
import 'prismjs/components/prism-typescript';
import 'prismjs/components/prism-css';
import 'prismjs/components/prism-jsx';
import 'prismjs/components/prism-tsx';
import 'prismjs/components/prism-bash';
import 'prismjs/components/prism-json';
import 'prismjs/components/prism-markdown';
import 'prismjs/components/prism-python';
import 'prismjs/components/prism-dart';

// Component registry for custom components
const componentRegistry = {
  Tip,
  TryOutVoiceHype,
  SubscribeNewsletter,
};

const MarkdownContent = ({ content }) => {
  const contentRef = useRef(null);
  
  const processedContent = useMemo(() => {
    // Replace escaped newlines with actual newlines
    const processedMarkdown = content.replace(/\\n/g, '\n');
    
    // Process markdown to HTML using our custom parser
    const markdownProcessor = new MarkdownProcessor();
    return markdownProcessor.process(processedMarkdown);
  }, [content]);
  
  // Parse HTML and replace custom components
  const parseHtmlWithComponents = (html) => {
    const items = [];
    const componentRegex = /<(Tip|TryOutVoiceHype|SubscribeNewsletter)([^>]*?)\/?>(?:<\/\1>)?/g;
    
    let lastIndex = 0;
    let match;
    
    while ((match = componentRegex.exec(html)) !== null) {
      // Add HTML content before the component
      if (match.index > lastIndex) {
        const htmlContent = html.slice(lastIndex, match.index).trim();
        if (htmlContent) {
          items.push({
            type: 'html',
            content: htmlContent
          });
        }
      }
      
      // Parse component attributes
      const componentName = match[1];
      const attributesStr = match[2] || '';
      const props = parseAttributes(attributesStr);
      
      items.push({
        type: 'component',
        component: componentName,
        props
      });
      
      lastIndex = match.index + match[0].length;
    }
    
    // Add remaining HTML content
    if (lastIndex < html.length) {
      const htmlContent = html.slice(lastIndex).trim();
      if (htmlContent) {
        items.push({
          type: 'html',
          content: htmlContent
        });
      }
    }
    
    return items;
  };
  
  const parseAttributes = (attributesStr) => {
    const attributes = {};
    const attrRegex = /(\w+)=(?:"([^"]*)"|'([^']*)'|([^\s>]+))/g;
    let match;
    
    while ((match = attrRegex.exec(attributesStr)) !== null) {
      const key = match[1];
      const value = match[2] || match[3] || match[4] || '';
      attributes[key] = value;
    }
    
    return attributes;
  };
  
  const parsedItems = useMemo(() => {
    return parseHtmlWithComponents(processedContent);
  }, [processedContent]);
  
  // Apply Prism highlighting after content is rendered
  useEffect(() => {
    if (contentRef.current) {
      // Find all code blocks and apply Prism highlighting
      const codeBlocks = contentRef.current.querySelectorAll('pre code');
      codeBlocks.forEach((codeBlock) => {
        // Only highlight if not already highlighted
        if (!codeBlock.classList.contains('prism-highlighted')) {
          Prism.highlightElement(codeBlock);
          codeBlock.classList.add('prism-highlighted');
        }
      });
    }
  }, [parsedItems]);
  
  return (
    <div 
      ref={contentRef}
      className="markdown-content dark:prose-invert max-w-none prose prose-lg"
    >
      {parsedItems.map((item, index) => {
        if (item.type === 'html') {
          return <div 
            key={index} 
            dangerouslySetInnerHTML={{ __html: item.content }} 
            className="markdown-html"
          />;
        } else if (item.type === 'component' && item.component) {
          const Component = componentRegistry[item.component];
          if (Component) {
            return <Component key={index} {...(item.props || {})} />;
          }
        }
        return null;
      })}
    </div>
  );
};

export default MarkdownContent;
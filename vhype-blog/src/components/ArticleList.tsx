// src/components/ArticleList.tsx
import ArticleCard from './ArticleCard';
import type { BlogArticlePreview } from '../lib/supabase';

interface ArticleListProps {
  articles: BlogArticlePreview[];
  title: string;
}

export default function ArticleList({ articles, title }: ArticleListProps) {
  return (
    <div className="mb-12">
      <h2 className="mb-6 text-2xl font-bold">{title}</h2>
      <div className="md:grid-cols-2 lg:grid-cols-3 grid gap-6">
        {articles.map((article) => (
          <ArticleCard key={article.id} article={article} />
        ))}
      </div>
    </div>
  );
}
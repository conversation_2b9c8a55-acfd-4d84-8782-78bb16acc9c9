---
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
// Get current path for active link highlighting
const currentPath = Astro.url.pathname;
// Navigation items
const navItems = [
  { href: "/", label: "Home" },
  { href: "/all-articles", label: "All Articles" },
  { href: "/tags", label: "Tags" }, // Add this line
  { href: "/search", label: "Search" },
  { href: "/about", label: "About" },
];

// Check if a nav item is active
function isActive(href: string): boolean {
  if (href === "/") {
    return currentPath === "/";
  }
  return currentPath.startsWith(href);
}
---

<header
  class="sticky top-0 z-50 w-full border-b border-border/40 backdrop-blur supports-[backdrop-filter]:bg-background/60"
>
  <div
    class="h-14 max-w-screen-2xl sm:px-6 lg:px-8 container flex items-center px-4"
  >
    <!-- Hamburger <PERSON><PERSON> (Mobile only) -->
    <Button
      variant="ghost"
      size="sm"
      className="md:hidden mr-2"
      id="mobile-menu-button"
      aria-label="Toggle menu"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
        class="lucide lucide-menu"
      >
        <line x1="4" x2="20" y1="12" y2="12"></line>
        <line x1="4" x2="20" y1="6" y2="6"></line>
        <line x1="4" x2="20" y1="18" y2="18"></line>
      </svg>
    </Button>

    <!-- Logo Section -->
    <div class="mr-4">
      <a href="/" class="voicehype-logo">
        <svg
          width="40"
          height="30"
          viewBox="0 0 40 30"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          class="md:block hidden"
        >
          <!-- Monitor outline with white fill -->
          <rect x="2" y="2" width="36" height="20" rx="2" fill="currentColor"
          ></rect>
          <rect
            x="2"
            y="2"
            width="36"
            height="20"
            rx="2"
            stroke="var(--vhype-primary)"
            stroke-width="1.5"></rect>
          <!-- Sound wave visualization -->
          <path
            d="M7 14 L10 9 L13 14 L16 9 L19 14 L22 9 L25 14 L28 9 L31 14 L34 9"
            stroke="var(--vhype-primary)"
            stroke-width="1.5"
            stroke-linecap="round"></path>
          <!-- Stand -->
          <line
            x1="20"
            y1="22"
            x2="20"
            y2="26"
            stroke="var(--vhype-primary)"
            stroke-width="1.5"></line>
          <line
            x1="14"
            y1="26"
            x2="26"
            y2="26"
            stroke="var(--vhype-primary)"
            stroke-width="1.5"></line>
        </svg>
        <svg
          width="30"
          height="22"
          viewBox="0 0 40 30"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          class="md:hidden"
        >
          <!-- Monitor outline with white fill -->
          <rect x="2" y="2" width="36" height="20" rx="2" fill="currentColor"
          ></rect>
          <rect
            x="2"
            y="2"
            width="36"
            height="20"
            rx="2"
            stroke="var(--vhype-primary)"
            stroke-width="1.5"></rect>
          <!-- Sound wave visualization -->
          <path
            d="M7 14 L10 9 L13 14 L16 9 L19 14 L22 9 L25 14 L28 9 L31 14 L34 9"
            stroke="var(--vhype-primary)"
            stroke-width="1.5"
            stroke-linecap="round"></path>
          <!-- Stand -->
          <line
            x1="20"
            y1="22"
            x2="20"
            y2="26"
            stroke="var(--vhype-primary)"
            stroke-width="1.5"></line>
          <line
            x1="14"
            y1="26"
            x2="26"
            y2="26"
            stroke="var(--vhype-primary)"
            stroke-width="1.5"></line>
        </svg>
        <h1 class="logo-text">
          <span class="voice-text">Voice</span><span class="hype-text"
            >Hype</span
          >
        </h1>
      </a>
    </div>

    <!-- Navigation -->
    <div
      class="md:justify-end flex items-center justify-between flex-1 space-x-2"
    >
      <div class="md:w-auto md:flex-none flex-1 w-full">
        <nav
          id="navigation"
          class="text-foreground md:flex items-center hidden space-x-6 text-sm font-medium"
        >
          {
            navItems.map((item) => (
              <a
                href={item.href}
                class={`transition-colors hover:text-foreground/80 block md:inline py-2 px-4 md:px-0 ${
                  isActive(item.href) ? "font-semibold" : "text-foreground"
                }`}
              >
                {item.label}
              </a>
            ))
          }
          <Separator orientation="vertical" className="h-4 hidden md:block" />
        </nav>
      </div>
      <!-- Theme Toggle -->
      <div class="flex items-center space-x-2">
        <Button
          variant="ghost"
          size="sm"
          id="theme-toggle"
          aria-label="Toggle theme"
          className="text-foreground hover:text-foreground/80"
        >
          <svg
            id="theme-toggle-sun-icon"
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="lucide lucide-sun-icon lucide-sun"
            ><circle cx="12" cy="12" r="4"></circle><path d="M12 2v2"
            ></path><path d="M12 20v2"></path><path d="m4.93 4.93 1.41 1.41"
            ></path><path d="m17.66 17.66 1.41 1.41"></path><path d="M2 12h2"
            ></path><path d="M20 12h2"></path><path d="m6.34 17.66-1.41 1.41"
            ></path><path d="m19.07 4.93-1.41 1.41"></path></svg
          >
          <svg
            id="theme-toggle-moon-icon"
            class="hidden w-4 h-4"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"
            ></path>
          </svg>
          <span class="sr-only">Toggle theme</span>
        </Button>
      </div>
    </div>
  </div>

  <!-- Mobile Navigation Menu (Hidden by default) -->
  <div id="mobile-menu" class="md:hidden bg-background/95 hidden border-b">
    <div class="px-4 py-2 space-y-1">
      {
        navItems.map((item) => (
          <a
            href={item.href}
            class={`block py-2 px-4 text-sm rounded-md transition-colors hover:bg-accent hover:text-accent-foreground ${
              isActive(item.href)
                ? "bg-accent font-semibold text-accent-foreground"
                : "text-foreground"
            }`}
          >
            {item.label}
          </a>
        ))
      }
    </div>
  </div>
</header>

<!-- Theme toggle and mobile menu script -->
<script>
  // Theme toggle functionality
  const themeToggle = document.getElementById("theme-toggle");
  const sunIcon = document.getElementById("theme-toggle-sun-icon");
  const moonIcon = document.getElementById("theme-toggle-moon-icon");

  function updateThemeIcons() {
    if (document.documentElement.classList.contains("dark")) {
      // In dark mode, show sun icon (to switch to light mode)
      sunIcon?.classList.remove("hidden");
      moonIcon?.classList.add("hidden");
    } else {
      // In light mode, show moon icon (to switch to dark mode)
      sunIcon?.classList.add("hidden");
      moonIcon?.classList.remove("hidden");
    }
  }

  // Initialize icons
  updateThemeIcons();

  themeToggle?.addEventListener("click", () => {
    document.documentElement.classList.toggle("dark");
    const isDark = document.documentElement.classList.contains("dark");
    localStorage.setItem("theme", isDark ? "dark" : "light");
    updateThemeIcons();
  });

  // Mobile menu toggle functionality
  const mobileMenuButton = document.getElementById("mobile-menu-button");
  const mobileMenu = document.getElementById("mobile-menu");

  mobileMenuButton?.addEventListener("click", () => {
    mobileMenu?.classList.toggle("hidden");
  });
</script>

import React, { useState } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

const SubscribeNewsletter = () => {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState(null);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!email.trim()) return;
    
    setIsSubmitting(true);
    
    try {
      // TODO: Implement newsletter subscription API call
      // For now, just simulate a successful submission
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSubmitStatus('success');
      setEmail('');
    } catch (error) {
      console.error('Newsletter subscription error:', error);
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
      
      // Reset status after 3 seconds
      setTimeout(() => setSubmitStatus(null), 3000);
    }
  };

  return (
    <Card className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950 dark:to-emerald-950 dark:border-green-800 my-6 border-green-200">
      <CardContent className="p-6">
        <div className="flex items-start gap-4">
          <div className="p-3 text-white bg-green-500 rounded-lg">
            <svg xmlns="http://www.w3.org/2000/svg" className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          <div className="flex-1">
            <h3 className="text-foreground mb-2 text-lg font-semibold">
              Subscribe to VoiceHype Newsletter
            </h3>
            <p className="text-muted-foreground mb-4">
              Get the latest updates on voice-to-prompt technology, AI development tips, and VoiceHype features delivered to your inbox.
            </p>
            
            {submitStatus === 'success' && (
              <div className="dark:bg-green-900 dark:text-green-200 p-3 mb-4 text-green-800 bg-green-100 rounded-md">
                Thank you for subscribing! We'll be in touch soon.
              </div>
            )}
            
            {submitStatus === 'error' && (
              <div className="dark:bg-red-900 dark:text-red-200 p-3 mb-4 text-red-800 bg-red-100 rounded-md">
                Sorry, there was an error subscribing. Please try again later.
              </div>
            )}
            
            <form onSubmit={handleSubmit} className="sm:flex-row flex flex-col gap-3">
              <Input
                type="email"
                placeholder="Enter your email address"
                required
                className="flex-1"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                disabled={isSubmitting}
              />
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Subscribing...' : 'Subscribe'}
              </Button>
            </form>
            <p className="text-muted-foreground mt-2 text-sm">
              No spam, unsubscribe at any time.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SubscribeNewsletter;
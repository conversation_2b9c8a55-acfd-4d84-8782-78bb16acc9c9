import React from 'react';
import { Card, CardContent } from "@/components/ui/card";

const Tip = ({ title = 'Tip', content, children }) => {
  return (
    <Card className="bg-amber-50 dark:bg-amber-950 border-amber-200 dark:border-amber-800">
      <CardContent className="">
        <div className="flex items-start gap-3">
          <div className="bg-amber-500 flex-shrink-0 p-2 text-white rounded-lg">
            <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
            </svg>
          </div>
          <div className="flex-1">
            <h4 className="text-foreground mb-1 font-semibold">
              {title}
            </h4>
            <div className="text-muted-foreground">
              {content ? (
                <p>{content}</p>
              ) : (
                children
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default Tip;
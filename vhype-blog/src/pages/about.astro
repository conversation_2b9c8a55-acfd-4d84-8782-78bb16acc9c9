---
import BaseLayout from '../layouts/BaseLayout.astro';
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export const prerender = true;
---
<BaseLayout
  title="About VoiceHype"
  description="Learn about VoiceHype Blog, our mission to advance voice-to-prompt technology, and how we're helping developers work more efficiently with AI tools."
>
  <div class="sm:px-6 lg:px-8 max-w-4xl px-4 py-12 mx-auto">
    <!-- Hero Section -->
    <div class="mb-12 text-center">
      <h1 class="sm:text-5xl text-foreground mb-6 text-4xl font-bold">
        About <span class="voicehype-logo-text inline-block"><span class="voice-text">Voice</span><span class="hype-text">Hype</span></span>
      </h1>
      <p class="text-muted-foreground max-w-3xl mx-auto text-xl">
        Advancing the future of developer productivity through voice-to-prompt technology and AI innovation.
      </p>
    </div>
    <!-- Main Content -->
    <div class="dark:prose-invert max-w-none prose prose-lg">
      <!-- Mission Section -->
      <section class="mb-12">
        <h2 class="text-foreground mb-6 text-3xl font-semibold">
          Our Mission
        </h2>
        <p class="text-muted-foreground mb-6 leading-relaxed">
          VoiceHype Blog is dedicated to exploring and advancing voice-to-prompt technology for developers.
          We believe that the future of software development lies in seamless human-AI collaboration,
          where developers can express their ideas naturally through voice and receive optimized,
          contextual assistance from AI tools.
        </p>
        <p class="text-muted-foreground leading-relaxed">
          Our content focuses on practical insights, tutorials, and thought leadership in the rapidly
          evolving landscape of AI-assisted development, with a special emphasis on voice interfaces
          and prompt engineering.
        </p>
      </section>
      <!-- VoiceHype Product -->
      <section class="mb-12">
        <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950 border-blue-200 dark:border-blue-800">
          <CardContent className="p-8">
          <div class="flex items-start gap-6">
            <div class="sm:block hidden p-4 text-white bg-blue-500 rounded-lg">
              <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
              </svg>
            </div>
            <div class="flex-1">
              <h3 class="dark:text-blue-100 mb-4 text-2xl font-semibold text-blue-900">
                VoiceHype: Voice-to-Prompt for Developers
              </h3>
              <p class="dark:text-blue-200 mb-6 leading-relaxed text-blue-800">
                VoiceHype is our flagship product that transforms your voice into optimized prompts for
                Large Language Models (LLMs). Designed specifically for developers, it integrates seamlessly
                with your workflow through our VS Code extension and web application.
              </p>
              <div class="flex flex-wrap gap-4">
                <Button asChild>
                  <a
                    href="https://voicehype.ai"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Try VoiceHype
                  </a>
                </Button>
                <Button variant="secondary" asChild>
                  <a
                    href="https://marketplace.visualstudio.com/items?itemName=voicehype.voicehype"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                  VS Code Extension
                  </a>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>
      <!-- Contact -->
      <section class="mb-12">
        <h2 class="text-foreground mb-6 text-3xl font-semibold">
          Get in Touch
        </h2>
        <p class="text-muted-foreground mb-6 leading-relaxed">
          We love hearing from our community! Whether you have questions about voice-to-prompt
          technology, suggestions for blog topics, or feedback on VoiceHype, we're always eager
          to connect with fellow developers and AI enthusiasts.
        </p>
        <Card>
          <CardContent className="p-6">
            <h3 class="text-foreground mb-4 text-lg font-semibold">
              Connect with us:
            </h3>
            <ul class="text-muted-foreground space-y-2">
              <li>• Visit our main website: <Button asChild variant="link"><a href="https://voicehype.ai" target="_blank" rel="noopener noreferrer">voicehype.ai</a></Button></li>
              <li>• Try our VS Code extension from the <Button asChild variant="link"><a href="https://marketplace.visualstudio.com/items?itemName=voicehype.voicehype" target="_blank" rel="noopener noreferrer">Visual Studio Marketplace</a></Button></li>
              <li>• Follow our blog for the latest insights and tutorials</li>
            </ul>
          </CardContent>
        </Card>
      </section>
    </div>
  </div>
</BaseLayout>
---
// src/pages/[slug].astro

import BlogPost from "../layouts/BlogPost.astro";
import { createClient } from "@supabase/supabase-js";

// Disable prerendering – this will make it SSR-only
export const prerender = false;

// Get the slug from the URL
const { slug } = Astro.params;

if (!slug || typeof slug !== 'string' || slug.trim() === '') {
  Astro.response.status = 404;
  return Astro.redirect('/404');
}

// Create Supabase client
const supabase = createClient(
  import.meta.env.PUBLIC_SUPABASE_URL!,
  import.meta.env.PUBLIC_SUPABASE_ANON_KEY!,
  {
    db: {
      schema: "blog",
    },
  },
);

// Fetch article using RPC function
const { data, error } = await supabase
  .rpc("get_article_with_view_increment", {
    slug_param: slug.trim(),
  });

let article = null;
if (!data || data.length === 0) {
  Astro.response.status = 404;
  return Astro.redirect('/404');
} else {
  article = data[0]; // Function returns array of one item
}
---

<BlogPost article={article} />


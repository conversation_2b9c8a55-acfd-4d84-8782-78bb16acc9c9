---
import BaseLayout from "../../layouts/BaseLayout.astro";
import ArticleCard from "../../components/ArticleCard";
import { getArticlesByTag } from "../../lib/supabase";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import type { BlogArticlePreview } from "../../lib/supabase";

// Get the tag from the URL
const tag = Astro.params.tag;
const decodedTag = decodeURIComponent(tag!);

// Pagination
const currentPage = Astro.url.searchParams.get("page")
    ? parseInt(Astro.url.searchParams.get("page")!)
    : 1;
const limit = 10; // Number of articles per page
const offset = (currentPage - 1) * limit;

// Fetch articles for this tag
let articles: BlogArticlePreview[] = [];
let error: string | null = null;

try {
    articles = await getArticlesByTag(decodedTag, limit, offset);
} catch (e) {
    console.error("Error fetching articles by tag:", e);
    error = "Failed to load articles";
}

// Calculate total pages (for now, we'll assume there are more pages if we got the full limit)
const hasMorePages = articles.length === limit;
---

<BaseLayout
    title={`${decodedTag} - VoiceHype Blog`}
    description={`Browse all articles tagged with "${decodedTag}" on the VoiceHype Blog.`}
>
    <div
        class="max-w-7xl sm:px-6 lg:px-8 bg-background/50 backdrop-blur-sm px-4 py-12"
    >
        <div class="mb-8">
            <div class="flex items-center gap-2 mb-2">
                <h1 class="text-3xl font-bold">Tag: {decodedTag}</h1>
                <Badge variant="outline">{articles.length} articles</Badge>
            </div>
        </div>

        {
            error && (
                <Alert variant="destructive" className="mb-8">
                    <svg
                        class="w-4 h-4"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                    >
                        <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                            clip-rule="evenodd"
                        />
                    </svg>
                    <AlertTitle>Error loading articles</AlertTitle>
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
            )
        }

        {
            articles.length === 0 && !error && (
                <div class="py-12 text-center">
                    <h2 class="mb-2 text-xl font-semibold">
                        No articles found
                    </h2>
                    <p class="text-muted-foreground">
                        There are no articles with the tag "{decodedTag}".
                    </p>
                    <Button variant="outline" className="mt-4" asChild>
                        <a href="/tags">View all tags</a>
                    </Button>
                </div>
            )
        }

        {
            articles.length > 0 && (
                <>
                    <div class="md:grid-cols-2 lg:grid-cols-3 grid grid-cols-1 gap-6 mb-8">
                        {articles.map((article) => (
                            <ArticleCard article={article} />
                        ))}
                    </div>

                    {/* Pagination Controls */}
                    <div class="flex items-center justify-between">
                        <Button
                            variant="outline"
                            disabled={currentPage === 1}
                            asChild={currentPage > 1}
                        >
                            {currentPage > 1 ? (
                                <a
                                    href={`/tags/${tag}?page=${currentPage - 1}`}
                                >
                                    Previous Page
                                </a>
                            ) : (
                                <span>Previous Page</span>
                            )}
                        </Button>

                        <span class="text-muted-foreground">
                            Page {currentPage}
                        </span>

                        <Button
                            variant="outline"
                            disabled={!hasMorePages}
                            asChild={hasMorePages}
                        >
                            {hasMorePages ? (
                                <a
                                    href={`/tags/${tag}?page=${currentPage + 1}`}
                                >
                                    Next Page
                                </a>
                            ) : (
                                <span>Next Page</span>
                            )}
                        </Button>
                    </div>
                </>
            )
        }
    </div>
</BaseLayout>

---
import BaseLayout from '../layouts/BaseLayout.astro';
import ArticleList from '../components/ArticleList';
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { getLatestPostPreviews, getPopularPostPreviews } from '../lib/supabase'; // Updated import
import type { BlogArticlePreview } from '../lib/supabase';

// Fetch articles for different sections concurrently
let newestArticles: BlogArticlePreview[] = [];
let popularArticles: BlogArticlePreview[] = [];
let error: string | null = null;
try {
  // Fetch both newest and popular articles concurrently
  [newestArticles, popularArticles] = await Promise.all([
    getLatestPostPreviews(5), // Updated method call
    getPopularPostPreviews(5)  // Updated method call
  ]);
} catch (e) {
  console.error('Error fetching articles:', e);
  error = 'Failed to load articles';
}
const hasArticles = newestArticles.length > 0 || popularArticles.length > 0;
---

<BaseLayout
  title="The VoiceHype Blog"
  description="Discover the latest insights on technology, AI development, and developer tools. Learn how VoiceHype is revolutionizing the way developers interact with AI."
>
  <!-- Hero Section -->
  <section class="bg-gradient-to-br from-green-50 to-green-100 dark:from-gray-950 dark:to-gray-900 py-16">
    <div class="max-w-7xl sm:px-6 lg:px-8 px-4 mx-auto">
      <div class="text-center">
        <h1 class="sm:text-5xl lg:text-6xl dark:text-gray-100 mb-6 text-4xl font-bold text-gray-900">
          <a href="/" class="voicehype-logo-text inline-block">
            <span class="brand-name">
              <span class="voice-text">Voice</span><span class="hype-text">Hype</span>
            </span>
            <span class="blog-text">Blog</span>
          </a>
        </h1>
        <p class="sm:text-2xl dark:text-gray-300 max-w-3xl mx-auto mb-8 text-xl text-gray-600">
          Insights and tutorials on voice AI, LLM-accelerated development, and the future of developer tools.
        </p>
        <div class="sm:flex-row flex flex-col justify-center gap-4">
          <Button asChild >
            <a
              href="https://voicehype.ai"
              target="_blank"
              rel="noopener noreferrer"
            >
            Try VoiceHype
            </a>
          </Button>
          <Button variant="secondary" size="lg" asChild>
            <a
              href="https://marketplace.visualstudio.com/items?itemName=voicehype.voicehype"
              target="_blank"
              rel="noopener noreferrer"
            >
              VS Code Extension
            </a>
          </Button>
        </div>
      </div>
    </div>
  </section>
  
  <!-- Main Content -->
  <div class="max-w-7xl sm:px-6 lg:px-8 bg-background/50 backdrop-blur-sm px-4 py-12">
    {error && (
      <Alert variant="destructive" className="mb-8">
        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
        </svg>
        <AlertTitle>Error loading articles</AlertTitle>
        <AlertDescription>
          {error}
        </AlertDescription>
      </Alert>
    )}
    
    {!hasArticles && !error && (
      <!-- Coming Soon Section -->
      <section class="py-16 text-center">
        <div class="max-w-2xl mx-auto">
          <div class="mb-8">
            <svg class="text-muted-foreground w-24 h-24 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
            </svg>
          </div>
          <h2 class="text-foreground mb-4 text-3xl font-bold">
            Coming Soon
          </h2>
          <p class="text-muted-foreground mb-8 text-lg">
            We're working on amazing content about voice-to-prompt technology and AI development. Stay tuned for our first articles!
          </p>
          <div class="sm:flex-row flex flex-col justify-center gap-4">
            <Button asChild>
              <a href="https://voicehype.ai" target="_blank" rel="noopener noreferrer">
                Try VoiceHype Now
              </a>
            </Button>
          </div>
        </div>
      </section>
    )}
    
    {hasArticles && (
      <>
        <!-- Latest Articles Section -->
        <ArticleList articles={newestArticles} title="Latest Articles" />
        
        <!-- Popular Articles Section -->
        <ArticleList articles={popularArticles} title="Popular Articles" />
      </>
    )}
  </div>
  
  <!-- Add styles for the Blog text -->
  <style>
    .voicehype-logo-text {
      display: inline-flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
    }
    
    .brand-name {
      white-space: nowrap;
    }
    
    .blog-text {
      font-family: var(--vhype-font-display);
      color: #8b5cf6; /* Purple color */
      margin-left: 0.5rem;
    }
    
    .dark .blog-text {
      color: #a78bfa; /* Lighter purple for dark mode */
    }
    
    /* On small screens, make the blog text appear on a new line */
    @media (max-width: 640px) {
      .voicehype-logo-text {
        flex-direction: column;
      }
      
      .blog-text {
        margin-left: 0;
        margin-top: 0.25rem;
      }
    }
  </style>
</BaseLayout>
---
import BaseLayout from '../layouts/BaseLayout.astro';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { searchPublishedArticles, formatDate } from '../lib/supabase';
import type { BlogArticlePreview } from '../lib/supabase';

// Disable prerendering to enable SSR
export const prerender = false;

// Get search query from URL parameters
const searchQuery = Astro.url.searchParams.get('q') || '';

let searchResults: BlogArticlePreview[] = [];
let error: string | null = null;
let isSearching = false;

// Perform search if query exists
if (searchQuery.trim()) {
  isSearching = true;
  
  try {
    searchResults = await searchPublishedArticles(searchQuery.trim());
  } catch (e) {
    console.error('Error searching articles:', e);
    error = `Failed to search articles: ${e instanceof Error ? e.message : String(e)}`;
  }
}
---

<BaseLayout
  title={searchQuery ? `Search results for "${searchQuery}"` : 'Search Articles'}
  description={searchQuery ? `Search results for "${searchQuery}" on VoiceHype Blog` : 'Search articles on VoiceHype Blog about voice-to-prompt technology and AI development'}
>
  <div class="sm:px-6 lg:px-8 max-w-4xl px-4 py-8 mx-auto">
    <!-- Search Header -->
    <div class="mb-8">
      <h1 class="text-foreground mb-4 text-3xl font-bold">
        {searchQuery ? `Search results for "${searchQuery}"` : 'Search Articles'}
      </h1>
      
      <!-- Search Form -->
      <form class="mb-6" method="get" action="/search">
        <div class="flex gap-2">
          <Input
            type="text"
            name="q"
            value={searchQuery}
            placeholder="Search articles..."
            className="flex-1"
            required
          />
          <Button type="submit" size="icon">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </Button>
        </div>
      </form>
      
      <!-- Search Results Count -->
      {isSearching && (
        <p class="text-muted-foreground">
          {searchResults.length === 0
            ? 'No articles found'
            : `Found ${searchResults.length} article${searchResults.length === 1 ? '' : 's'}`
          }
        </p>
      )}
    </div>

    <!-- Error Message -->
    {error && (
      <Alert variant="destructive" className="mb-8">
        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
        </svg>
        <AlertTitle>Search Error</AlertTitle>
        <AlertDescription>
          {error}
        </AlertDescription>
      </Alert>
    )}

    <!-- Search Results -->
    {isSearching && searchResults.length > 0 && (
      <div class="space-y-6">
        {searchResults.map((article) => (
          <div class="bg-card text-card-foreground hover:shadow-lg transition-shadow border rounded-lg shadow-sm">
            <div class="p-6">
              <div class="md:flex-row flex flex-col gap-6">
                <!-- Article Image -->
                {article.cover_image_url && (
                  <div class="md:w-48 flex-shrink-0">
                    <img
                      src={article.cover_image_url}
                      alt={article.title}
                      class="md:h-24 object-cover w-full h-32 rounded-lg"
                      loading="lazy"
                    />
                  </div>
                )}

                <!-- Article Content -->
                <div class="flex-1">
                  <!-- Meta Info -->
                  <div class="text-muted-foreground flex items-center gap-2 mb-2 text-sm">
                    <time datetime={article.published_at}>
                      {formatDate(article.published_at)}
                    </time>
                    <span>•</span>
                    <span class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80">
                      {article.view_count} views
                    </span>
                    {article.author_name && (
                      <>
                        <span>•</span>
                        <span>by {article.author_name}</span>
                      </>
                    )}
                  </div>

                  <!-- Title -->
                  <h2 class="text-card-foreground mb-2 text-xl font-semibold">
                    <a href={`/${article.slug}`} class="hover:text-primary transition-colors">
                      {article.title}
                    </a>
                  </h2>

                  <!-- Excerpt -->
                  <p class="text-muted-foreground line-clamp-2 mb-3">
                    {article.excerpt}
                  </p>

                  <!-- Read More -->
                  <a
                    href={`/${article.slug}`}
                    class="text-primary hover:underline inline-flex items-center text-sm font-medium"
                  >
                    Read full article
                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    )}

    <!-- No Results -->
    {isSearching && searchResults.length === 0 && !error && (
      <div class="py-12 text-center">
        <div class="mb-6">
          <svg class="dark:text-gray-600 w-16 h-16 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
        <h2 class="text-foreground mb-4 text-2xl font-semibold">
          No articles found
        </h2>
        <p class="text-muted-foreground max-w-md mx-auto mb-6">
          We couldn't find any articles matching "{searchQuery}". Try different keywords or browse our latest articles.
        </p>
      </div>
    )}

    <!-- Search Tips - Only show when NOT searching -->
    {!isSearching && (
      <div class="bg-blue-50 dark:bg-blue-950 dark:border-blue-800 p-6 border border-blue-200 rounded-lg">
        <h2 class="dark:text-blue-100 mb-4 text-lg font-semibold text-blue-900">
          Feel free to
        </h2>
        <ul class="dark:text-blue-200 space-y-2 text-blue-800">
          <li>• Use keywords</li>
          <li>• Search for author names or specific topics you're interested in</li>
          <li>• Use multiple keywords to narrow down results</li>
        </ul>
      </div>
    )}
  </div>
</BaseLayout>

<style>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
gl---
import BaseLayout from '../layouts/BaseLayout.astro';
---

<BaseLayout
  title="Page Not Found"
  description="The page you're looking for doesn't exist."
>
  <div class="sm:px-6 lg:px-8 max-w-4xl px-4 py-16 mx-auto">
    <div class="text-center">
      <!-- 404 Icon -->
      <div class="align-items-center flex justify-center mb-8 text-gray-400">
       <svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search-x-icon lucide-search-x w-24 h-24"><path d="m13.5 8.5-5 5"/><path d="m8.5 8.5 5 5"/><circle cx="11" cy="11" r="8"/><path d="m21 21-4.3-4.3"/></svg>
      </div>
      
      <!-- 404 Text -->
      <h1 class="dark:text-gray-100 mb-4 text-6xl font-bold text-gray-900">
        404
      </h1>
      
      <h2 class="dark:text-gray-300 mb-4 text-2xl font-semibold text-gray-700">
        Article Not Found
      </h2>
      
      <p class="dark:text-gray-400 max-w-md mx-auto mb-8 text-lg text-gray-600">
        Sorry, we couldn't find the article you're looking for. It might have been moved, deleted, or the URL might be incorrect.
      </p>
      
      <!-- Action Buttons -->
      <div class="sm:flex-row flex flex-col justify-center gap-4">
        <a 
          href="/"
          class="hover:bg-blue-700 inline-flex items-center px-6 py-3 font-medium transition-colors bg-blue-600 rounded-lg"
        >
          <svg class="w -4 h-4 mr-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
          </svg>
          <span class="text-white">Back to Home</span>
        </a>
      </div>
    </div>
  </div>
</BaseLayout>

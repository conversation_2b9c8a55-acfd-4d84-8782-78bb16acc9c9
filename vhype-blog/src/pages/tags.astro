---
import BaseLayout from "../layouts/BaseLayout.astro";
import { getAllTagsWithCount } from "../lib/supabase";
import { Badge } from "@/components/ui/badge";

// Fetch all tags with counts
let tags: { name: string; count: number }[] = [];
let error: string | null = null;

try {
    tags = await getAllTagsWithCount();
} catch (e) {
    console.error("Error fetching tags:", e);
    error = "Failed to load tags";
}

// Sort tags by count (descending) and then by name (ascending)
tags.sort((a, b) => {
    if (b.count !== a.count) {
        return b.count - a.count;
    }
    return a.name.localeCompare(b.name);
});

// Define color variants for tags
const tagColors = [
    "bg-blue-100 text-blue-800 hover:bg-blue-200",
    "bg-green-100 text-green-800 hover:bg-green-200",
    "bg-yellow-100 text-yellow-800 hover:bg-yellow-200",
    "bg-red-100 text-red-800 hover:bg-red-200",
    "bg-purple-100 text-purple-800 hover:bg-purple-200",
    "bg-indigo-100 text-indigo-800 hover:bg-indigo-200",
    "bg-pink-100 text-pink-800 hover:bg-pink-200",
    "bg-teal-100 text-teal-800 hover:bg-teal-200",
    "bg-orange-100 text-orange-800 hover:bg-orange-200",
    "bg-cyan-100 text-cyan-800 hover:bg-cyan-200",
];

// Function to get a random color for a tag
const getTagColor = (tagName: string) => {
    // Use a simple hash function to get consistent color for the same tag
    let hash = 0;
    for (let i = 0; i < tagName.length; i++) {
        hash = tagName.charCodeAt(i) + ((hash << 5) - hash);
    }
    return tagColors[Math.abs(hash) % tagColors.length];
};
---

<BaseLayout
    title="All Tags - VoiceHype Blog"
    description="Browse all tags on the VoiceHype Blog and find articles by topic."
>
    <div
        class="max-w-7xl sm:px-6 lg:px-8 bg-background/50 backdrop-blur-sm px-4 py-12"
    >
        <div class="mb-8">
            <h1 class="mb-2 text-3xl font-bold">All Tags</h1>
        </div>

        {
            error && (
                <div class="bg-red-50 p-4 mb-6 border-l-4 border-red-500">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg
                                class="w-5 h-5 text-red-400"
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 20 20"
                                fill="currentColor"
                            >
                                <path
                                    fill-rule="evenodd"
                                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                    clip-rule="evenodd"
                                />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-red-700">{error}</p>
                        </div>
                    </div>
                </div>
            )
        }

        {
            tags.length === 0 && !error && (
                <div class="py-12 text-center">
                    <h2 class="mb-2 text-xl font-semibold">No tags found</h2>
                    <p class="text-muted-foreground">
                        Check back later for new content.
                    </p>
                </div>
            )
        }

        {
            tags.length > 0 && (
                <div class="flex flex-wrap gap-3">
                    {tags.map((tag) => {
                        const colorClass = getTagColor(tag.name);
                        return (
                            <a
                                href={`/tags/${encodeURIComponent(tag.name)}`}
                                class={`inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium transition-colors ${colorClass}`}
                            >
                                <span class="font-bold text-black">
                                    {tag.name}
                                </span>
                                <span class="ml-1.5  border-black border text-black px-1.5 rounded-full text-xs">
                                    {tag.count}
                                </span>
                            </a>
                        );
                    })}
                </div>
            )
        }
    </div>
</BaseLayout>

// src/pages/rss.xml.js
import rss from '@astrojs/rss';
import { getAllArticlePreviews } from '../lib/supabase';
// You'll need to define these constants or import them
const SITE_TITLE = 'VoiceHype Blog';
const SITE_DESCRIPTION = 'Latest articles about voice-to-prompt technology and AI development';
const SITE_URL = 'https://voicehype.ai'; // Replace with your actual domain

export async function GET(context) {
  try {
    // Fetch published articles from Supabase using the new function
    const articles = await getAllArticlePreviews(50, 0);
    
    return rss({
      title: SITE_TITLE,
      description: SITE_DESCRIPTION,
      site: context.site || SITE_URL,
      items: articles.map((article) => ({
        title: article.title,
        description: article.excerpt,
        link: `/${article.slug}`,
        pubDate: new Date(article.published_at),
        author: article.author_name,
        // Optional: Add more metadata
        categories: [], // Add categories if you have them
        guid: `${SITE_URL}/${article.slug}`, // Unique identifier
        // If you have full content and want to include it:
        // content: article.content,
      })),
      // Optional: Customize the feed
      customData: `
        <language>en-us</language>
        <managingEditor>${articles[0]?.author_name || 'VoiceHype Team'}</managingEditor>
        <webMaster><EMAIL></webMaster>
        <lastBuildDate>${new Date().toUTCString()}</lastBuildDate>
        <ttl>60</ttl>
      `,
    });
  } catch (error) {
    console.error('Error generating RSS feed:', error);
    
    // Return a minimal RSS feed on error
    return rss({
      title: SITE_TITLE,
      description: SITE_DESCRIPTION,
      site: context.site || SITE_URL,
      items: [],
    });
  }
}
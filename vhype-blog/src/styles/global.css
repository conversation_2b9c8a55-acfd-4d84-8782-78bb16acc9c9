@import "tailwindcss";
@import "tw-animate-css";
/*
  ---break---
 */
@custom-variant dark (&:is(.dark *));

/*
  The CSS in this style tag is based off of Bear Blog's default CSS.
  https://github.com/HermanMartinus/bearblog/blob/297026a877bc2ab2b3bdfbd6b9f7961c350917dd/templates/styles/blog/default.css
  License MIT: https://github.com/HermanMartinus/bearblog/blob/master/LICENSE.md
 */

:root {
	--accent: oklch(0.97 0.001 106.424);
	--accent-dark: #000d8a;
	--black: 15, 18, 25;
	--gray: 96, 115, 159;
	--gray-light: 229, 233, 240;
	--gray-dark: 34, 41, 57;
	--gray-gradient: rgba(var(--gray-light), 50%), #fff;
	--box-shadow:
		0 2px 6px rgba(var(--gray), 25%), 0 8px 24px rgba(var(--gray), 33%), 0 16px 32px
		rgba(var(--gray), 33%);
	--radius: 0.625rem;
	--background: oklch(1 0 0);
	--foreground: oklch(0.147 0.004 49.25);
	--card: oklch(1 0 0);
	--card-foreground: oklch(0.147 0.004 49.25);
	--popover: oklch(1 0 0);
	--popover-foreground: oklch(0.147 0.004 49.25);
	--primary: oklch(0.216 0.006 56.043);
	--primary-foreground: oklch(0.985 0.001 106.423);
	--secondary: oklch(0.97 0.001 106.424);
	--secondary-foreground: oklch(0.216 0.006 56.043);
	--muted: oklch(0.97 0.001 106.424);
	--muted-foreground: oklch(0.553 0.013 58.071);
	--accent-foreground: oklch(0.216 0.006 56.043);
	--destructive: oklch(0.577 0.245 27.325);
	--border: oklch(0.923 0.003 48.717);
	--input: oklch(0.923 0.003 48.717);
	--ring: oklch(0.709 0.01 56.259);
	--chart-1: oklch(0.646 0.222 41.116);
	--chart-2: oklch(0.6 0.118 184.704);
	--chart-3: oklch(0.398 0.07 227.392);
	--chart-4: oklch(0.828 0.189 84.429);
	--chart-5: oklch(0.769 0.188 70.08);
	--sidebar: oklch(0.985 0.001 106.423);
	--sidebar-foreground: oklch(0.147 0.004 49.25);
	--sidebar-primary: oklch(0.216 0.006 56.043);
	--sidebar-primary-foreground: oklch(0.985 0.001 106.423);
	--sidebar-accent: oklch(0.97 0.001 106.424);
	--sidebar-accent-foreground: oklch(0.216 0.006 56.043);
	--sidebar-border: oklch(0.923 0.003 48.717);
	--sidebar-ring: oklch(0.709 0.01 56.259);
	
	/* VoiceHype Logo Colors */
	--vhype-primary: #14F195;
	--vhype-primary-rgb: 20, 241, 149;
	--vhype-secondary: #0A84FF;
	--vhype-text: #ffffff;
	--vhype-text-dark: #161b22;
	
	/* VoiceHype Logo Styles */
	--vhype-font-display: 'Clash Display', sans-serif;
}
@font-face {
	font-family: "Atkinson";
	src: url("/fonts/atkinson-regular.woff") format("woff");
	font-weight: 400;
	font-style: normal;
	font-display: swap;
}
@font-face {
	font-family: "Atkinson";
	src: url("/fonts/atkinson-bold.woff") format("woff");
	font-weight: 700;
	font-style: normal;
	font-display: swap;
}

/* Clash Display Font */
@font-face {
  font-family: 'Clash Display';
  src: url('/fonts/clash-display/ClashDisplay-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Clash Display';
  src: url('/fonts/clash-display/ClashDisplay-Medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Clash Display';
  src: url('/fonts/clash-display/ClashDisplay-Semibold.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Clash Display';
  src: url('/fonts/clash-display/ClashDisplay-Bold.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Clash Display';
  src: url('/fonts/clash-display/ClashDisplay-Extralight.woff2') format('woff2');
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Clash Display';
  src: url('/fonts/clash-display/ClashDisplay-Light.woff2') format('woff2');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}
body {
	font-family: "Atkinson", sans-serif;
	margin: 0;
	padding: 0;
	text-align: left;
	background: linear-gradient(var(--gray-gradient)) no-repeat;
	background-size: 100% 600px;
	word-wrap: break-word;
	overflow-wrap: break-word;
	color: var(--foreground);
	font-size: 20px;
	line-height: 1.7;
}
main {
	width: 100%;
	max-width: 100%;
	margin: 0;
	padding: 0;
}
h1,
h2,
h3,
h4,
h5,
h6 {
	margin: 0 0 0.5rem 0;
	color: var(--foreground);
	line-height: 1.2;
}
h1 {
	font-size: 3.052em;
}
h2 {
	font-size: 2.441em;
}
h3 {
	font-size: 1.953em;
}
h4 {
	font-size: 1.563em;
}
h5 {
	font-size: 1.25em;
}
strong,
b {
	font-weight: 700;
}
a {
	color: var(--accent-foreground);
}
a:hover {
	color: var(--accent-foreground);
}

/* Dark mode link styles - use accent-foreground for dark mode */
.dark a {
	color: var(--accent-foreground);
}
.dark a:hover {
	color: var(--accent-foreground);
}
p {
	margin-bottom: 1em;
}
.prose p {
	margin-bottom: 2em;
}
.dark .prose p, ul, ol, li, h1, h2, h3, h4, h5, h6, blockquote {
  color: var(--foreground);
}

textarea {
	width: 100%;
	font-size: 16px;
}
input {
	font-size: 16px;
}
table {
	width: 100%;
}
img {
	max-width: 100%;
	height: auto;
	border-radius: 8px;
}
code {
	padding: 2px 5px;
	background-color: var(--muted);
	border-radius: 2px;
}
pre {
	padding: 1.5em;
	border-radius: 8px;
}
pre > code {
	all: unset;
}
blockquote {
	border-left: 4px solid var(--accent);
	padding: 0 0 0 20px;
	margin: 0px;
	font-size: 1.333em;
}
hr {
	border: none;
	border-top: 1px solid var(--border);
}
@media (max-width: 720px) {
	body {
		font-size: 18px;
	}
	main {
		padding: 1em;
	}
}

.sr-only {
	border: 0;
	padding: 0;
	margin: 0;
	position: absolute !important;
	height: 1px;
	width: 1px;
	overflow: hidden;
	/* IE6, IE7 - a 0 height clip, off to the bottom right of the visible 1px box */
	clip: rect(1px 1px 1px 1px);
	/* maybe deprecated but we need to support legacy browsers */
	clip: rect(1px, 1px, 1px, 1px);
	/* modern browsers, clip-path works inwards from each corner */
	clip-path: inset(50%);
	/* added line to stop words getting smushed together (as they go onto separate lines and some screen readers do not understand line feeds as a space */
	white-space: nowrap;
}

/*
  ---break---
 */

@theme inline {
	--radius-sm: calc(var(--radius) - 4px);
	--radius-md: calc(var(--radius) - 2px);
	--radius-lg: var(--radius);
	--radius-xl: calc(var(--radius) + 4px);
	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--color-card: var(--card);
	--color-card-foreground: var(--card-foreground);
	--color-popover: var(--popover);
	--color-popover-foreground: var(--popover-foreground);
	--color-primary: var(--primary);
	--color-primary-foreground: var(--primary-foreground);
	--color-secondary: var(--secondary);
	--color-secondary-foreground: var(--secondary-foreground);
	--color-muted: var(--muted);
	--color-muted-foreground: var(--muted-foreground);
	--color-accent: var(--accent);
	--color-accent-foreground: var(--accent-foreground);
	--color-destructive: var(--destructive);
	--color-border: var(--border);
	--color-input: var(--input);
	--color-ring: var(--ring);
	--color-chart-1: var(--chart-1);
	--color-chart-2: var(--chart-2);
	--color-chart-3: var(--chart-3);
	--color-chart-4: var(--chart-4);
	--color-chart-5: var(--chart-5);
	--color-sidebar: var(--sidebar);
	--color-sidebar-foreground: var(--sidebar-foreground);
	--color-sidebar-primary: var(--sidebar-primary);
	--color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
	--color-sidebar-accent: var(--sidebar-accent);
	--color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
	--color-sidebar-border: var(--sidebar-border);
	--color-sidebar-ring: var(--sidebar-ring);
}

/*
  ---break---
 */

.dark {
	--background: oklch(0.147 0.004 49.25);
	--foreground: oklch(0.985 0.001 106.423);
	--card: oklch(0.216 0.006 56.043);
	--card-foreground: oklch(0.985 0.001 106.423);
	--popover: oklch(0.216 0.006 56.043);
	--popover-foreground: oklch(0.985 0.001 106.423);
	--primary: oklch(0.923 0.003 48.717);
	--primary-foreground: oklch(0.216 0.006 56.043);
	--secondary: oklch(0.268 0.007 34.298);
	--secondary-foreground: oklch(0.985 0.001 106.423);
	--muted: oklch(0.268 0.007 34.298);
	--muted-foreground: oklch(0.709 0.01 56.259);
	--accent: oklch(0.268 0.007 34.298);
	--accent-foreground: oklch(0.985 0.001 106.423);
	--destructive: oklch(0.704 0.191 22.216);
	--border: oklch(1 0 0 / 10%);
	--input: oklch(1 0 0 / 15%);
	--ring: oklch(0.553 0.013 58.071);
	--chart-1: oklch(0.488 0.243 264.376);
	--chart-2: oklch(0.696 0.17 162.48);
	--chart-3: oklch(0.769 0.188 70.08);
	--chart-4: oklch(0.627 0.265 303.9);
	--chart-5: oklch(0.645 0.246 16.439);
	--sidebar: oklch(0.216 0.006 56.043);
	--sidebar-foreground: oklch(0.985 0.001 106.423);
	--sidebar-primary: oklch(0.488 0.243 264.376);
	--sidebar-primary-foreground: oklch(0.985 0.001 106.423);
	--sidebar-accent: oklch(0.268 0.007 34.298);
	--sidebar-accent-foreground: oklch(0.985 0.001 106.423);
	--sidebar-border: oklch(1 0 0 / 10%);
	--sidebar-ring: oklch(0.553 0.013 58.071);
}

/* VoiceHype Logo Component Styles */
.voicehype-logo {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  text-decoration: none;
  transition: all 0.3s ease;
}

.voicehype-logo:hover {
  transform: translateY(-2px);
}

.voicehype-logo svg {
  filter: drop-shadow(0 0 3px rgba(var(--vhype-primary-rgb), 0.2));
  transition: all 0.3s ease;
}

.voicehype-logo:hover svg {
  filter: drop-shadow(0 0 6px rgba(var(--vhype-primary-rgb), 0.4));
}

.logo-text {
  font-family: var(--vhype-font-display);
  font-size: 1.8rem;
  font-weight: 700;
  letter-spacing: -0.02em;
  display: flex;
  align-items: center;
  margin: 0;
  padding: 0;
  line-height: 1;
}

.logo-text .voice-text {
  color: #000; /* Black for light mode */
}

.logo-text .hype-text {
  color: var(--vhype-primary);
}

/* Dark mode adjustments */
.dark .logo-text .voice-text {
  color: #ffffff; /* White for dark mode */
}

/* Mobile logo styles */
.mobile-logo .logo-text {
  font-size: 1.4rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .voicehype-logo svg {
    width: 24px;
    height: 18px;
  }
  
  .logo-text {
    font-size: 1.4rem;
  }
}

/* Hero logo text styles */
.voicehype-logo-text {
  font-family: var(--vhype-font-display);
  font-size: 4rem;
  font-weight: 700;
  letter-spacing: -0.02em;
  display: inline-flex;
  align-items: center;
  transform: translateY(-2px);
  margin: 0;
  padding: 0;
  line-height: 1;
  transition: all 0.3s ease;
  vertical-align: middle;
}

.voicehype-logo-text:hover {
  transform: translateY(-2px);
}

.voicehype-logo-text .voice-text {
  color: #000; /* Black for light mode */
}

.voicehype-logo-text .hype-text {
  color: var(--vhype-primary);
}

/* Dark mode adjustments for hero logo text */
.dark .voicehype-logo-text .voice-text {
  color: #ffffff; /* White for dark mode */
}

/* Responsive adjustments for hero logo text */
@media (max-width: 768px) {
  .voicehype-logo-text {
    font-size: 3rem;
  }
}

@media (max-width: 480px) {
  .voicehype-logo-text {
    font-size: 2.5rem;
  }
}

/* Ensure VoiceHype and Blog appear on the same line */
.voicehype-logo-text + .blog-text {
  display: inline-block;
  vertical-align: middle;
  margin-left: 0.5rem;
}

/* Legacy hero styles (keeping for compatibility) */
.logo-text-hero {
  font-family: var(--vhype-font-display);
  font-size: 4rem;
  font-weight: 700;
  letter-spacing: -0.02em;
  display: flex;
  align-items: center;
  margin: 0;
  padding: 0;
  line-height: 1;
}

.logo-text-hero .voice-text {
  color: #000; /* Black for light mode */
}

.logo-text-hero .hype-text {
  color: var(--vhype-primary);
}

/* Dark mode adjustments for hero */
.dark .logo-text-hero .voice-text {
  color: #ffffff; /* White for dark mode */
}

/* Responsive adjustments for hero */
@media (max-width: 768px) {
  .logo-text-hero {
    font-size: 3rem;
  }
}

@media (max-width: 480px) {
  .logo-text-hero {
    font-size: 2.5rem;
  }
}

@media (max-width: 480px) {
  .voicehype-logo svg {
    width: 20px;
    height: 15px;
  }
  
  .logo-text {
    font-size: 1.2rem;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .voicehype-logo svg {
    width: 24px;
    height: 18px;
  }
  
  .logo-text {
    font-size: 1.4rem;
  }
}

@media (max-width: 480px) {
  .voicehype-logo svg {
    width: 20px;
    height: 15px;
  }
  
  .logo-text {
    font-size: 1.2rem;
  }
}

/*
  ---break---
 */

@layer base {
  * {
    @apply border-border outline-ring/50;
	}
  body {
    @apply bg-background text-foreground;
	}
  
  /* Text color utilities for theme-aware components */
  .text-foreground {
    color: var(--foreground);
  }
  
  .text-primary-foreground {
    color: var(--primary-foreground);
  }
  
  .text-secondary-foreground {
    color: var(--secondary-foreground);
  }
  
  .text-muted-foreground {
    color: var(--muted-foreground);
  }
  
  .text-accent-foreground {
    color: var(--accent-foreground);
  }
  
  .text-destructive {
    color: var(--destructive);
  }
  
  .text-card-foreground {
    color: var(--card-foreground);
  }
  
  .text-popover-foreground {
    color: var(--popover-foreground);
  }
  
  .text-sidebar-foreground {
    color: var(--sidebar-foreground);
  }
  
  .text-sidebar-primary-foreground {
    color: var(--sidebar-primary-foreground);
  }
  
  .text-sidebar-accent-foreground {
    color: var(--sidebar-accent-foreground);
  }
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

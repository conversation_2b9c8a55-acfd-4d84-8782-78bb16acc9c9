/* Markdown content styling */
.markdown-content {
  color: #374151;
  line-height: 1.7;
}

.markdown-content.dark {
  color: #e5e7eb;
}

/* Headings with bottom margin */
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin-bottom: 1rem;
  font-weight: 700;
  line-height: 1.3;
}

.markdown-content h1 {
  font-size: 2.25rem;
  margin-top: 1.5rem;
}

.markdown-content h2 {
  font-size: 1.875rem;
  margin-top: 1.25rem;
}

.markdown-content h3 {
  font-size: 1.5rem;
  margin-top: 1rem;
}

/* Lists with indentation and bullet styles */
.markdown-content ul,
.markdown-content ol {
  margin-left: 1.5rem;
  margin-bottom: 1rem;
}

.markdown-content ul {
  list-style-type: disc;
}

.markdown-content ol {
  list-style-type: decimal;
}

.markdown-content li {
  margin-bottom: 0.25rem;
}

/* Nested lists */
.markdown-content ul ul,
.markdown-content ol ol,
.markdown-content ul ol,
.markdown-content ol ul {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}

/* Paragraphs and other elements */
.markdown-content p {
  margin-bottom: 1rem;
}

.markdown-content blockquote {
  border-left: 4px solid #d1d5db;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #6b7280;
}

.markdown-content.dark blockquote {
  border-left-color: #4b5563;
  color: #9ca3af;
}

.markdown-content code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

.markdown-content.dark code {
  background-color: #374151;
}

.markdown-content pre {
  background-color: #1e293b;
  color: #e2e8f0;
  padding: 1rem;
  border-radius: 0.375rem;
  overflow-x: auto;
  margin-bottom: 1rem;
}

.markdown-content pre code {
  background-color: transparent;
  padding: 0;
  color: inherit;
}

/* Table styling */
.markdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1rem;
}

.markdown-content th,
.markdown-content td {
  border: 1px solid #d1d5db;
  padding: 0.5rem;
  text-align: left;
}

.markdown-content.dark th,
.markdown-content.dark td {
  border-color: #4b5563;
}

.markdown-content th {
  background-color: #f9fafb;
  font-weight: 600;
}

.markdown-content.dark th {
  background-color: #1f2937;
}

/* Link styling */
.markdown-content a {
  color: #3b82f6;
  text-decoration: underline;
}

.markdown-content.dark a {
  color: #60a5fa;
}

.markdown-content a:hover {
  color: #2563eb;
}

.markdown-content.dark a:hover {
  color: #93c5fd;
}
import type { Token, TokenType, ASTNode, ParserOptions } from './types';

export class MarkdownParser {
  private tokens: Token[];
  private current: number;
  private options: ParserOptions;

  constructor(tokens: Token[], options: ParserOptions = {}) {
    this.tokens = tokens;
    this.current = 0;
    this.options = {
      enableCustomComponents: true,
      ...options
    };
  }

  parse(): ASTNode {
    const children: ASTNode[] = [];
    
    // Skip initial whitespace
    this.skipWhitespace();
    
    while (!this.isAtEnd()) {
      const node = this.parseBlockElement();
      if (node) {
        children.push(node);
      }
      
      // Check for paragraph breaks (newlines)
      const newlineCount = this.countAndSkipNewlines();

      if (newlineCount > 0) {
        // Skip any whitespace after newlines
        this.skipWhitespace();

        // Only insert line break if:
        // 1. We had 2+ newlines (multiple empty lines)
        // 2. There's more content coming
        // 3. The last node was a paragraph
        // 4. The next node will be a paragraph
        if (!this.isAtEnd() && newlineCount >= 2 &&
            node && node.type === 'paragraph' &&
            this.willCreateParagraph()) {
          children.push({
            type: 'lineBreak'
          });
        }
      }
    }
    
    return {
      type: 'document',
      children
    };
  }

  private parseBlockElement(): ASTNode | null {
    const token = this.peek();
    
    switch (token.type) {
      case 'heading':
        return this.parseHeading();
      case 'list':
        return this.parseList();
      case 'blockquote':
        return this.parseBlockquote();
      case 'horizontalRule':
        return this.parseHorizontalRule();
      case 'codeBlock':
        return this.parseCodeBlock();
      default:
        return this.parseParagraph();
    }
  }

  private parseParagraph(): ASTNode | null {
    const inlineNodes: ASTNode[] = [];
    
    // Parse inline content until we hit a block element or newline
    while (!this.isAtEnd()) {
      const token = this.peek();
      
      // Stop at block elements
      if (['heading', 'list', 'blockquote', 'horizontalRule', 'codeBlock'].includes(token.type)) {
        break;
      }
      
      // Stop at newlines (don't consume them here - let the main parse loop handle them)
      if (token.type === 'newline') {
        break;
      }
      
      const node = this.parseInlineElement();
      if (node) {
        inlineNodes.push(node);
      }
    }
    
    // Only create paragraph if we have content
    if (inlineNodes.length === 0) {
      return null;
    }
    
    return {
      type: 'paragraph',
      children: inlineNodes
    };
  }

  private parseInlineElement(): ASTNode | null {
    const token = this.peek();
    
    switch (token.type) {
      case 'bold':
        return this.parseBold();
      case 'italic':
        return this.parseItalic();
      case 'code':
        return this.parseInlineCode();
      case 'link':
        return this.parseLink();
      case 'image':
        return this.parseImage();
      case 'customComponent':
        return this.parseCustomComponent();
      case 'text':
        return this.parseText();
      case 'whitespace':
        return this.parseWhitespace();
      default:
        this.advance();
        return null;
    }
  }

  private parseHeading(): ASTNode {
    const token = this.advance();
    const level = token.value.length;
    
    // Parse inline content until newline or end
    const content = this.parseInlineContentUntilNewline();
    
    return {
      type: 'heading',
      children: content,
      metadata: { level }
    };
  }

  private parseBold(): ASTNode {
    this.advance(); // consume **
    const content = this.parseInlineContentUntil('bold');
    
    // Try to consume closing **
    if (this.peek().type === 'bold') {
      this.advance();
    }
    
    return {
      type: 'bold',
      children: content
    };
  }

  private parseItalic(): ASTNode {
    this.advance(); // consume *
    const content = this.parseInlineContentUntil('italic');
    
    // Try to consume closing *
    if (this.peek().type === 'italic') {
      this.advance();
    }
    
    return {
      type: 'italic',
      children: content
    };
  }

  private parseInlineCode(): ASTNode {
    this.advance(); // consume `
    const content = this.parseTextContentUntil('code');
    
    // Try to consume closing `
    if (this.peek().type === 'code') {
      this.advance();
    }
    
    return {
      type: 'code',
      content
    };
  }

  private parseCodeBlock(): ASTNode {
    this.advance(); // consume ```
    
    // Check for language specification on the same line
    let language = '';
    if (this.peek().type === 'text') {
      const langToken = this.peek();
      // If it's on the same line (no newline before), it's the language
      language = langToken.value.trim();
      this.advance();
    }
    
    // Skip newline after language
    if (this.peek().type === 'newline') {
      this.advance();
    }
    
    const content = this.parseTextContentUntil('codeBlock');
    
    // Try to consume closing ```
    if (this.peek().type === 'codeBlock') {
      this.advance();
    }
    
    return {
      type: 'codeBlock',
      content,
      metadata: { language }
    };
  }

  private parseLink(): ASTNode {
    this.advance(); // consume [
    const text = this.parseTextContentUntil('text', ']');
    
    // Expect closing ]
    if (this.peek().type === 'text' && this.peek().value === ']') {
      this.advance(); // consume ]
      
      // Look for (url)
      if (this.peek().type === 'text' && this.peek().value === '(') {
        this.advance(); // consume (
        const url = this.parseTextContentUntil('text', ')');
        
        if (this.peek().type === 'text' && this.peek().value === ')') {
          this.advance(); // consume )
          
          return {
            type: 'link',
            content: text,
            attributes: { href: url }
          };
        }
      }
    }
    
    // If link parsing failed, return as text
    return {
      type: 'text',
      content: '[' + text
    };
  }

  private parseImage(): ASTNode {
    this.advance(); // consume !
    
    if (this.peek().type === 'link' || (this.peek().type === 'text' && this.peek().value === '[')) {
      this.advance(); // consume [
      const alt = this.parseTextContentUntil('text', ']');
      
      if (this.peek().type === 'text' && this.peek().value === ']') {
        this.advance(); // consume ]
        
        if (this.peek().type === 'text' && this.peek().value === '(') {
          this.advance(); // consume (
          const src = this.parseTextContentUntil('text', ')');
          
          if (this.peek().type === 'text' && this.peek().value === ')') {
            this.advance(); // consume )
            
            return {
              type: 'image',
              attributes: { src, alt }
            };
          }
        }
      }
    }
    
    return {
      type: 'text',
      content: '!'
    };
  }

  private parseList(): ASTNode {
    const items: ASTNode[] = [];
    
    while (this.peek().type === 'list') {
      this.advance(); // consume list marker
      
      // Parse list item content until newline
      const content = this.parseInlineContentUntilNewline();
      
      items.push({
        type: 'listItem',
        children: content
      });
    }
    
    return {
      type: 'list',
      children: items
    };
  }

  private parseBlockquote(): ASTNode {
    this.advance(); // consume >
    
    const content = this.parseInlineContentUntilNewline();
    
    return {
      type: 'blockquote',
      children: content
    };
  }

  private parseHorizontalRule(): ASTNode {
    this.advance(); // consume ---
    
    return {
      type: 'horizontalRule'
    };
  }

  private parseCustomComponent(): ASTNode {
    const token = this.advance();
    
    return {
      type: 'customComponent',
      content: token.value,
      attributes: token.metadata?.attributes,
      metadata: {
        componentName: token.metadata?.componentName
      }
    };
  }

  private parseText(): ASTNode {
    const token = this.advance();
    
    return {
      type: 'text',
      content: token.value
    };
  }

  private parseWhitespace(): ASTNode {
    const token = this.advance();

    return {
      type: 'text',
      content: token.value
    };
  }

  private parseInlineContentUntilNewline(): ASTNode[] {
    const nodes: ASTNode[] = [];

    while (!this.isAtEnd() && this.peek().type !== 'newline' && this.peek().type !== 'eof') {
      const node = this.parseInlineElement();
      if (node) {
        nodes.push(node);
      }
    }

    return nodes;
  }

  private parseInlineContentUntil(endType: TokenType): ASTNode[] {
    const nodes: ASTNode[] = [];

    while (!this.isAtEnd() && this.peek().type !== endType &&
           this.peek().type !== 'newline' && this.peek().type !== 'eof') {
      const node = this.parseInlineElement();
      if (node) {
        nodes.push(node);
      }
    }

    return nodes;
  }

  private parseTextContentUntil(endType: TokenType, endValue?: string): string {
    let text = '';

    while (!this.isAtEnd()) {
      const token = this.peek();

      if (token.type === endType) {
        if (endValue && token.value !== endValue) {
          // Continue if value doesn't match
          text += token.value;
          this.advance();
          continue;
        }
        break;
      }

      if (token.type === 'eof') {
        break;
      }

      text += token.value;
      this.advance();
    }

    return text;
  }

  private willCreateParagraph(): boolean {
    // Look ahead to see if the next content will create a paragraph
    // (i.e., it's not a heading, list, blockquote, etc.)
    const token = this.peek();

    if (this.isAtEnd()) {
      return false;
    }

    // These token types create block elements, not paragraphs
    const blockElementTypes: TokenType[] = [
      'heading', 'list', 'blockquote', 'horizontalRule', 'codeBlock'
    ];

    return !blockElementTypes.includes(token.type);
  }

  private countAndSkipNewlines(): number {
    let count = 0;

    while (!this.isAtEnd() && this.peek().type === 'newline') {
      count++;
      this.advance();
    }

    return count;
  }

  private skipWhitespace(): void {
    while (!this.isAtEnd() && this.peek().type === 'whitespace') {
      this.advance();
    }
  }

  private peek(): Token {
    if (this.current >= this.tokens.length) {
      return { type: 'eof', value: '', position: this.tokens.length };
    }
    return this.tokens[this.current];
  }

  private advance(): Token {
    if (!this.isAtEnd()) {
      this.current++;
    }
    return this.tokens[this.current - 1];
  }

  private isAtEnd(): boolean {
    return this.current >= this.tokens.length || this.peek().type === 'eof';
  }
}

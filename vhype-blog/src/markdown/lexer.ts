import type { Token, TokenType, LexerOptions } from './types';

export class MarkdownLexer {
  private input: string;
  private position: number;
  private options: LexerOptions;

  constructor(input: string, options: LexerOptions = {}) {
    this.input = input;
    this.position = 0;
    this.options = {
      enableCustomComponents: true,
      customComponentPattern: /<([A-Z][a-zA-Z0-9]*)\s*([^>]*)\/?>/g,
      ...options
    };
  }

  tokenize(): Token[] {
    const tokens: Token[] = [];
    
    while (this.position < this.input.length) {
      const token = this.nextToken();
      if (token) {
        tokens.push(token);
      }
    }
    
    tokens.push({ type: 'eof', value: '', position: this.position });
    return tokens;
  }

  private nextToken(): Token | null {
    if (this.position >= this.input.length) {
      return null;
    }

    // Check for custom components first
    if (this.options.enableCustomComponents) {
      const customComponentToken = this.matchCustomComponent();
      if (customComponentToken) {
        return customComponentToken;
      }
    }

    // Check for markdown syntax
    const char = this.input[this.position];
    
    switch (char) {
      case '#':
        return this.matchHeading();
      case '*':
      case '_':
        return this.matchBoldItalic();
      case '`':
        return this.matchCode();
      case '[':
        return this.matchLink();
      case '!':
        return this.matchImage();
      case '-':
        // Check for horizontal rule first
        if (this.input.substring(this.position, this.position + 3) === '---') {
          return this.matchHorizontalRule();
        }
        // Otherwise treat as list
        return this.matchList();
      case '+':
      case '1':
      case '2':
      case '3':
      case '4':
      case '5':
      case '6':
      case '7':
      case '8':
      case '9':
        return this.matchList();
      case '>':
        return this.matchBlockquote();
      case '\n':
        return { type: 'newline', value: char, position: this.position++ };
      case ' ':
      case '\t':
        return this.matchWhitespace();
    }

    // Default text token
    return this.matchText();
  }

  private matchCustomComponent(): Token | null {
    const remaining = this.input.substring(this.position);
    const match = remaining.match(/^<([A-Z][a-zA-Z0-9]*)\s*([^>]*)\/?>/);
    
    if (match) {
      const [fullMatch, componentName, attributes] = match;
      this.position += fullMatch.length;
      return {
        type: 'customComponent',
        value: fullMatch,
        position: this.position - fullMatch.length,
        metadata: {
          componentName,
          attributes: this.parseAttributes(attributes)
        }
      };
    }
    
    return null;
  }

  private parseAttributes(attributesStr: string): Record<string, string> {
    const attributes: Record<string, string> = {};
    const attrRegex = /(\w+)=(?:"([^"]*)"|'([^']*)'|([^\s>]+))/g;
    let match;
    
    while ((match = attrRegex.exec(attributesStr)) !== null) {
      const key = match[1];
      const value = match[2] || match[3] || match[4] || '';
      attributes[key] = value;
    }
    
    return attributes;
  }

  private matchHeading(): Token {
    let level = 0;
    let pos = this.position;
    
    // Count # characters
    while (pos < this.input.length && this.input[pos] === '#') {
      level++;
      pos++;
    }
    
    // Must be followed by a space
    if (pos < this.input.length && this.input[pos] === ' ') {
      this.position = pos + 1;
      return {
        type: 'heading',
        value: '#'.repeat(level),
        position: this.position - level - 1
      };
    }
    
    // Not a heading, treat as text
    return this.matchText();
  }

  private matchBoldItalic(): Token {
    const pos = this.position;
    const char = this.input[pos];
    
    // Check for bold (**text** or __text__)
    if (pos + 1 < this.input.length) {
      if (this.input[pos + 1] === char) {
        // Bold
        this.position = pos + 2;
        return {
          type: 'bold',
          value: char + char,
          position: pos
        };
      } else {
        // Italic
        this.position = pos + 1;
        return {
          type: 'italic',
          value: char,
          position: pos
        };
      }
    }
    
    // Single character, treat as text
    return this.matchText();
  }

  private matchCode(): Token {
    const pos = this.position;
    
    // Check for code block (```)
    if (pos + 2 < this.input.length && 
        this.input[pos + 1] === '`' && 
        this.input[pos + 2] === '`') {
      this.position = pos + 3;
      return {
        type: 'codeBlock',
        value: '```',
        position: pos
      };
    }
    
    // Inline code (`)
    this.position = pos + 1;
    return {
      type: 'code',
      value: '`',
      position: pos
    };
  }

  private matchLink(): Token {
    const pos = this.position;
    this.position = pos + 1;
    return {
      type: 'link',
      value: '[',
      position: pos
    };
  }

  private matchImage(): Token {
    const pos = this.position;
    
    // Check for ![ (image)
    if (pos + 1 < this.input.length && this.input[pos + 1] === '[') {
      this.position = pos + 1;
      return {
        type: 'image',
        value: '!',
        position: pos
      };
    }
    
    // Just !, treat as text
    return this.matchText();
  }

  private matchList(): Token {
    const pos = this.position;
    const char = this.input[pos];

    // Unordered list (- or +)
    if (char === '-' || char === '+') {
      // Must be at start of line or after whitespace and followed by a space
      const isAtLineStart = pos === 0 || this.input[pos - 1] === '\n' ||
                           (pos > 0 && this.input.substring(0, pos).match(/\n\s*$/));

      if (isAtLineStart && pos + 1 < this.input.length && this.input[pos + 1] === ' ') {
        this.position = pos + 2;
        return {
          type: 'list',
          value: char,
          position: pos
        };
      }
    }
    
    // Ordered list (number followed by .)
    if (/\d/.test(char)) {
      // Must be at start of line or after whitespace
      const isAtLineStart = pos === 0 || this.input[pos - 1] === '\n' ||
                           (pos > 0 && this.input.substring(0, pos).match(/\n\s*$/));

      if (isAtLineStart) {
        let numPos = pos;
        while (numPos < this.input.length && /\d/.test(this.input[numPos])) {
          numPos++;
        }

        if (numPos < this.input.length && this.input[numPos] === '.' &&
            numPos + 1 < this.input.length && this.input[numPos + 1] === ' ') {
          this.position = numPos + 2;
          return {
            type: 'list',
            value: this.input.substring(pos, numPos + 1),
            position: pos
          };
        }
      }
    }
    
    // Not a list, treat as text
    return this.matchText();
  }

  private matchBlockquote(): Token {
    const pos = this.position;
    
    // Check for > followed by space
    if (pos + 1 < this.input.length && this.input[pos + 1] === ' ') {
      this.position = pos + 2;
      return {
        type: 'blockquote',
        value: '>',
        position: pos
      };
    }
    
    // Just >, treat as text
    return this.matchText();
  }

  private matchHorizontalRule(): Token {
    const pos = this.position;
    this.position = pos + 3;
    return {
      type: 'horizontalRule',
      value: '---',
      position: pos
    };
  }

  private matchWhitespace(): Token {
    const pos = this.position;
    let count = 0;
    
    while (this.position < this.input.length && 
           (this.input[this.position] === ' ' || this.input[this.position] === '\t')) {
      this.position++;
      count++;
    }
    
    return {
      type: 'whitespace',
      value: this.input.substring(pos, pos + count),
      position: pos
    };
  }

  private matchText(): Token {
    const pos = this.position;
    let value = '';
    
    while (this.position < this.input.length) {
      const char = this.input[this.position];
      
      // Stop at special characters
      if (['#', '*', '_', '`', '[', '!', ']', '(', ')', '>', '\n'].includes(char)) {
        break;
      }
      
      // Check for numbers that might start a list
      if (/\d/.test(char)) {
        const nextPos = this.position + 1;
        let numPos = this.position;
        while (numPos < this.input.length && /\d/.test(this.input[numPos])) {
          numPos++;
        }
        
        if (numPos < this.input.length && this.input[numPos] === '.' &&
            numPos + 1 < this.input.length && this.input[numPos + 1] === ' ') {
          break;
        }
      }
      
      value += char;
      this.position++;
    }
    
    // If we didn't consume any characters, consume just one
    if (value === '') {
      value = this.input[this.position];
      this.position++;
    }
    
    return {
      type: 'text',
      value,
      position: pos
    };
  }
}

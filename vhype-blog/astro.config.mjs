// @ts-check

import mdx from '@astrojs/mdx';
import sitemap from '@astrojs/sitemap';
import { defineConfig } from 'astro/config';

import react from '@astrojs/react';

import tailwindcss from '@tailwindcss/vite';

import netlify from '@astrojs/netlify';

// https://astro.build/config
export default defineConfig({
  site: 'https://blog.voicehype.ai',
  output: 'server', // Add this line to enable SSR

  integrations: [
    mdx(),
    sitemap({
      changefreq: 'weekly',
      priority: 0.7,
      lastmod: new Date(),
    }),
    react()
  ],

  // SEO and performance optimizations
  compressHTML: true,

  build: {
    inlineStylesheets: 'auto',
  },

  // Prefetch configuration for better performance
  prefetch: false,

  vite: {
    plugins: [tailwindcss()],
    build: {
      cssMinify: true,
      minify: true,
    },
  },

  // adapter: netlify(),
});
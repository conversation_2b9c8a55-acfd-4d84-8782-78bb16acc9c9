export declare class Mocker {
    pick: (...args: any[]) => any;
    get string(): string;
    get number(): number;
    get bigint(): bigint;
    get boolean(): boolean;
    get date(): Date;
    get symbol(): symbol;
    get null(): null;
    get undefined(): undefined;
    get stringOptional(): string | undefined;
    get stringNullable(): string | null;
    get numberOptional(): number | undefined;
    get numberNullable(): number | null;
    get booleanOptional(): boolean | undefined;
    get booleanNullable(): boolean | null;
}

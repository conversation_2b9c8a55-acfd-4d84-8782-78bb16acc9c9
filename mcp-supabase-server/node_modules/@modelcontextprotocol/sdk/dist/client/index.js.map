{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/client/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAoB,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AAEnE,OAAO,EAEL,oBAAoB,EAMpB,oBAAoB,EACpB,iBAAiB,EAEjB,qBAAqB,EAErB,sBAAsB,EACtB,uBAAuB,EAEvB,uBAAuB,EAEvB,yBAAyB,EAEzB,iCAAiC,EAEjC,qBAAqB,EAIrB,wBAAwB,EAKxB,2BAA2B,EAE5B,MAAM,aAAa,CAAC;AAErB;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,MAAM,OAAO,MAIX,SAAQ,QAIT;IAIC;;OAEG;IACH,YAAoB,WAA2B;QAC7C,KAAK,EAAE,CAAC;QADU,gBAAW,GAAX,WAAW,CAAgB;IAE/C,CAAC;IAEQ,KAAK,CAAC,OAAO,CAAC,SAAoB;QACzC,MAAM,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAE/B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAC/B;YACE,MAAM,EAAE,YAAY;YACpB,MAAM,EAAE;gBACN,eAAe,EAAE,uBAAuB;gBACxC,YAAY,EAAE,EAAE;gBAChB,UAAU,EAAE,IAAI,CAAC,WAAW;aAC7B;SACF,EACD,sBAAsB,CACvB,CAAC;QAEF,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,0CAA0C,MAAM,EAAE,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,CAAC,2BAA2B,CAAC,QAAQ,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC;YAClE,MAAM,IAAI,KAAK,CACb,+CAA+C,MAAM,CAAC,eAAe,EAAE,CACxE,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,mBAAmB,GAAG,MAAM,CAAC,YAAY,CAAC;QAC/C,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,UAAU,CAAC;QAExC,MAAM,IAAI,CAAC,YAAY,CAAC;YACtB,MAAM,EAAE,2BAA2B;SACpC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAED,KAAK,CAAC,IAAI;QACR,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,iBAAiB,CAAC,CAAC;IAC7D,CAAC;IAED,KAAK,CAAC,QAAQ,CACZ,MAAiC,EACjC,UAA6B;QAE7B,OAAO,IAAI,CAAC,OAAO,CACjB,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,EACzC,oBAAoB,EACpB,UAAU,CACX,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,KAAmB;QACvC,OAAO,IAAI,CAAC,OAAO,CACjB,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,EAAE,EACjD,iBAAiB,CAClB,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,SAAS,CACb,MAAkC,EAClC,UAA6B;QAE7B,OAAO,IAAI,CAAC,OAAO,CACjB,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,EACjC,qBAAqB,EACrB,UAAU,CACX,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,WAAW,CACf,MAAqC,EACrC,UAA6B;QAE7B,OAAO,IAAI,CAAC,OAAO,CACjB,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,EAClC,uBAAuB,EACvB,UAAU,CACX,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,aAAa,CACjB,MAAuC,EACvC,UAA6B;QAE7B,OAAO,IAAI,CAAC,OAAO,CACjB,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,EACpC,yBAAyB,EACzB,UAAU,CACX,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,qBAAqB,CACzB,MAA+C,EAC/C,UAA6B;QAE7B,OAAO,IAAI,CAAC,OAAO,CACjB,EAAE,MAAM,EAAE,0BAA0B,EAAE,MAAM,EAAE,EAC9C,iCAAiC,EACjC,UAAU,CACX,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,YAAY,CAChB,MAAqC,EACrC,UAA6B;QAE7B,OAAO,IAAI,CAAC,OAAO,CACjB,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,EACpC,wBAAwB,EACxB,UAAU,CACX,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,MAAkC;QACxD,OAAO,IAAI,CAAC,OAAO,CACjB,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,EACzC,iBAAiB,CAClB,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,MAAoC;QAC5D,OAAO,IAAI,CAAC,OAAO,CACjB,EAAE,MAAM,EAAE,uBAAuB,EAAE,MAAM,EAAE,EAC3C,iBAAiB,CAClB,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,QAAQ,CACZ,MAAiC,EACjC,eAAuF,oBAAoB,EAC3G,UAA6B;QAE7B,OAAO,IAAI,CAAC,OAAO,CACjB,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,EAChC,YAAY,EACZ,UAAU,CACX,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,SAAS,CACb,MAAmC,EACnC,UAA6B;QAE7B,OAAO,IAAI,CAAC,OAAO,CACjB,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,EAChC,qBAAqB,EACrB,UAAU,CACX,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,oBAAoB;QACxB,OAAO,IAAI,CAAC,YAAY,CAAC,EAAE,MAAM,EAAE,kCAAkC,EAAE,CAAC,CAAC;IAC3E,CAAC;CACF"}
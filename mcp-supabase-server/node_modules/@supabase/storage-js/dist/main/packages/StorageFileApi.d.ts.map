{"version": 3, "file": "StorageFileApi.d.ts", "sourceRoot": "", "sources": ["../../../src/packages/StorageFileApi.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAkB,YAAY,EAAuB,MAAM,eAAe,CAAA;AACjF,OAAO,EAAE,KAAK,EAA2B,MAAM,cAAc,CAAA;AAE7D,OAAO,EACL,UAAU,EACV,WAAW,EACX,aAAa,EACb,eAAe,EACf,gBAAgB,EAChB,kBAAkB,EAClB,YAAY,EACZ,QAAQ,EACT,MAAM,cAAc,CAAA;AAiBrB,aAAK,QAAQ,GACT,WAAW,GACX,eAAe,GACf,IAAI,GACJ,MAAM,GACN,IAAI,GACJ,QAAQ,GACR,MAAM,CAAC,cAAc,GACrB,cAAc,CAAC,UAAU,CAAC,GAC1B,eAAe,GACf,MAAM,CAAA;AAEV,MAAM,CAAC,OAAO,OAAO,cAAc;IACjC,SAAS,CAAC,GAAG,EAAE,MAAM,CAAA;IACrB,SAAS,CAAC,OAAO,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,CAAA;IAC5C,SAAS,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAA;IAC3B,SAAS,CAAC,KAAK,EAAE,KAAK,CAAA;gBAGpB,GAAG,EAAE,MAAM,EACX,OAAO,GAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KAAO,EACvC,QAAQ,CAAC,EAAE,MAAM,EACjB,KAAK,CAAC,EAAE,KAAK;IAQf;;;;;;OAMG;YACW,cAAc;IAiF5B;;;;;OAKG;IACG,MAAM,CACV,IAAI,EAAE,MAAM,EACZ,QAAQ,EAAE,QAAQ,EAClB,WAAW,CAAC,EAAE,WAAW,GACxB,OAAO,CACN;QACE,IAAI,EAAE;YAAE,EAAE,EAAE,MAAM,CAAC;YAAC,IAAI,EAAE,MAAM,CAAC;YAAC,QAAQ,EAAE,MAAM,CAAA;SAAE,CAAA;QACpD,KAAK,EAAE,IAAI,CAAA;KACZ,GACD;QACE,IAAI,EAAE,IAAI,CAAA;QACV,KAAK,EAAE,YAAY,CAAA;KACpB,CACJ;IAID;;;;;OAKG;IACG,iBAAiB,CACrB,IAAI,EAAE,MAAM,EACZ,KAAK,EAAE,MAAM,EACb,QAAQ,EAAE,QAAQ,EAClB,WAAW,CAAC,EAAE,WAAW;;;;;;;;;;IAuD3B;;;;;;OAMG;IACG,qBAAqB,CACzB,IAAI,EAAE,MAAM,EACZ,OAAO,CAAC,EAAE;QAAE,MAAM,EAAE,OAAO,CAAA;KAAE,GAC5B,OAAO,CACN;QACE,IAAI,EAAE;YAAE,SAAS,EAAE,MAAM,CAAC;YAAC,KAAK,EAAE,MAAM,CAAC;YAAC,IAAI,EAAE,MAAM,CAAA;SAAE,CAAA;QACxD,KAAK,EAAE,IAAI,CAAA;KACZ,GACD;QACE,IAAI,EAAE,IAAI,CAAA;QACV,KAAK,EAAE,YAAY,CAAA;KACpB,CACJ;IAmCD;;;;;OAKG;IACG,MAAM,CACV,IAAI,EAAE,MAAM,EACZ,QAAQ,EACJ,WAAW,GACX,eAAe,GACf,IAAI,GACJ,MAAM,GACN,IAAI,GACJ,QAAQ,GACR,MAAM,CAAC,cAAc,GACrB,cAAc,CAAC,UAAU,CAAC,GAC1B,eAAe,GACf,MAAM,EACV,WAAW,CAAC,EAAE,WAAW,GACxB,OAAO,CACN;QACE,IAAI,EAAE;YAAE,EAAE,EAAE,MAAM,CAAC;YAAC,IAAI,EAAE,MAAM,CAAC;YAAC,QAAQ,EAAE,MAAM,CAAA;SAAE,CAAA;QACpD,KAAK,EAAE,IAAI,CAAA;KACZ,GACD;QACE,IAAI,EAAE,IAAI,CAAA;QACV,KAAK,EAAE,YAAY,CAAA;KACpB,CACJ;IAID;;;;;;OAMG;IACG,IAAI,CACR,QAAQ,EAAE,MAAM,EAChB,MAAM,EAAE,MAAM,EACd,OAAO,CAAC,EAAE,kBAAkB,GAC3B,OAAO,CACN;QACE,IAAI,EAAE;YAAE,OAAO,EAAE,MAAM,CAAA;SAAE,CAAA;QACzB,KAAK,EAAE,IAAI,CAAA;KACZ,GACD;QACE,IAAI,EAAE,IAAI,CAAA;QACV,KAAK,EAAE,YAAY,CAAA;KACpB,CACJ;IAuBD;;;;;;OAMG;IACG,IAAI,CACR,QAAQ,EAAE,MAAM,EAChB,MAAM,EAAE,MAAM,EACd,OAAO,CAAC,EAAE,kBAAkB,GAC3B,OAAO,CACN;QACE,IAAI,EAAE;YAAE,IAAI,EAAE,MAAM,CAAA;SAAE,CAAA;QACtB,KAAK,EAAE,IAAI,CAAA;KACZ,GACD;QACE,IAAI,EAAE,IAAI,CAAA;QACV,KAAK,EAAE,YAAY,CAAA;KACpB,CACJ;IAuBD;;;;;;;OAOG;IACG,eAAe,CACnB,IAAI,EAAE,MAAM,EACZ,SAAS,EAAE,MAAM,EACjB,OAAO,CAAC,EAAE;QAAE,QAAQ,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC;QAAC,SAAS,CAAC,EAAE,gBAAgB,CAAA;KAAE,GACtE,OAAO,CACN;QACE,IAAI,EAAE;YAAE,SAAS,EAAE,MAAM,CAAA;SAAE,CAAA;QAC3B,KAAK,EAAE,IAAI,CAAA;KACZ,GACD;QACE,IAAI,EAAE,IAAI,CAAA;QACV,KAAK,EAAE,YAAY,CAAA;KACpB,CACJ;IAyBD;;;;;;OAMG;IACG,gBAAgB,CACpB,KAAK,EAAE,MAAM,EAAE,EACf,SAAS,EAAE,MAAM,EACjB,OAAO,CAAC,EAAE;QAAE,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAA;KAAE,GACvC,OAAO,CACN;QACE,IAAI,EAAE;YAAE,KAAK,EAAE,MAAM,GAAG,IAAI,CAAC;YAAC,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC;YAAC,SAAS,EAAE,MAAM,CAAA;SAAE,EAAE,CAAA;QACxE,KAAK,EAAE,IAAI,CAAA;KACZ,GACD;QACE,IAAI,EAAE,IAAI,CAAA;QACV,KAAK,EAAE,YAAY,CAAA;KACpB,CACJ;IA8BD;;;;;OAKG;IACG,QAAQ,CACZ,IAAI,EAAE,MAAM,EACZ,OAAO,CAAC,EAAE;QAAE,SAAS,CAAC,EAAE,gBAAgB,CAAA;KAAE,GACzC,OAAO,CACN;QACE,IAAI,EAAE,IAAI,CAAA;QACV,KAAK,EAAE,IAAI,CAAA;KACZ,GACD;QACE,IAAI,EAAE,IAAI,CAAA;QACV,KAAK,EAAE,YAAY,CAAA;KACpB,CACJ;IAuBD;;;OAGG;IACG,IAAI,CACR,IAAI,EAAE,MAAM,GACX,OAAO,CACN;QACE,IAAI,EAAE,QAAQ,CAAC,YAAY,CAAC,CAAA;QAC5B,KAAK,EAAE,IAAI,CAAA;KACZ,GACD;QACE,IAAI,EAAE,IAAI,CAAA;QACV,KAAK,EAAE,YAAY,CAAA;KACpB,CACJ;IAkBD;;;OAGG;IACG,MAAM,CACV,IAAI,EAAE,MAAM,GACX,OAAO,CACN;QACE,IAAI,EAAE,OAAO,CAAA;QACb,KAAK,EAAE,IAAI,CAAA;KACZ,GACD;QACE,IAAI,EAAE,OAAO,CAAA;QACb,KAAK,EAAE,YAAY,CAAA;KACpB,CACJ;IAsBD;;;;;;;OAOG;IACH,YAAY,CACV,IAAI,EAAE,MAAM,EACZ,OAAO,CAAC,EAAE;QAAE,QAAQ,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC;QAAC,SAAS,CAAC,EAAE,gBAAgB,CAAA;KAAE,GACtE;QAAE,IAAI,EAAE;YAAE,SAAS,EAAE,MAAM,CAAA;SAAE,CAAA;KAAE;IA8BlC;;;;OAIG;IACG,MAAM,CACV,KAAK,EAAE,MAAM,EAAE,GACd,OAAO,CACN;QACE,IAAI,EAAE,UAAU,EAAE,CAAA;QAClB,KAAK,EAAE,IAAI,CAAA;KACZ,GACD;QACE,IAAI,EAAE,IAAI,CAAA;QACV,KAAK,EAAE,YAAY,CAAA;KACpB,CACJ;IAkBD;;;OAGG;IAyBH;;;;OAIG;IA+BH;;;OAGG;IACG,IAAI,CACR,IAAI,CAAC,EAAE,MAAM,EACb,OAAO,CAAC,EAAE,aAAa,EACvB,UAAU,CAAC,EAAE,eAAe,GAC3B,OAAO,CACN;QACE,IAAI,EAAE,UAAU,EAAE,CAAA;QAClB,KAAK,EAAE,IAAI,CAAA;KACZ,GACD;QACE,IAAI,EAAE,IAAI,CAAA;QACV,KAAK,EAAE,YAAY,CAAA;KACpB,CACJ;IAoBD,SAAS,CAAC,cAAc,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;IAItD,QAAQ,CAAC,IAAI,EAAE,MAAM;IAOrB,OAAO,CAAC,aAAa;IAIrB,OAAO,CAAC,mBAAmB;IAI3B,OAAO,CAAC,0BAA0B;CAwBnC"}
#!/usr/bin/env node
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { CallToolRequestSchema, ListResourcesRequestSchema, ListToolsRequestSchema, ReadResourceRequestSchema, } from '@modelcontextprotocol/sdk/types.js';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
// Load environment variables
dotenv.config();
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;
if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
    console.error('Missing required environment variables: SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
    process.exit(1);
}
// Initialize Supabase client with service role key for full access
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, {});
class SupabaseMCPServer {
    server;
    tableSchemas = new Map();
    constructor() {
        this.server = new Server({
            name: process.env.MCP_SERVER_NAME || 'voicehype-supabase',
            version: process.env.MCP_SERVER_VERSION || '1.0.0',
        });
        this.setupHandlers();
    }
    setupHandlers() {
        // List available resources (database tables, schemas, etc.)
        this.server.setRequestHandler(ListResourcesRequestSchema, async () => {
            await this.loadTableSchemas();
            const resources = Array.from(this.tableSchemas.keys()).map(tableName => ({
                uri: `supabase://table/${tableName}`,
                name: `Table: ${tableName}`,
                description: `Schema and data for table ${tableName}`,
                mimeType: 'application/json',
            }));
            resources.push({
                uri: 'supabase://schema/overview',
                name: 'Database Schema Overview',
                description: 'Complete overview of all tables and their relationships',
                mimeType: 'application/json',
            });
            return { resources };
        });
        // Read specific resource content
        this.server.setRequestHandler(ReadResourceRequestSchema, async (request) => {
            const { uri } = request.params;
            if (uri === 'supabase://schema/overview') {
                await this.loadTableSchemas();
                return {
                    contents: [{
                            uri,
                            mimeType: 'application/json',
                            text: JSON.stringify(Object.fromEntries(this.tableSchemas), null, 2),
                        }],
                };
            }
            if (uri.startsWith('supabase://table/')) {
                const tableName = uri.replace('supabase://table/', '');
                const schema = this.tableSchemas.get(tableName);
                if (!schema) {
                    throw new Error(`Table ${tableName} not found`);
                }
                // Get sample data from the table (first 10 rows)
                const { data, error } = await supabase
                    .from(tableName)
                    .select('*')
                    .limit(10);
                if (error) {
                    throw new Error(`Failed to fetch data from ${tableName}: ${error.message}`);
                }
                return {
                    contents: [{
                            uri,
                            mimeType: 'application/json',
                            text: JSON.stringify({
                                schema,
                                sampleData: data,
                                totalRows: data?.length || 0,
                            }, null, 2),
                        }],
                };
            }
            throw new Error(`Unknown resource: ${uri}`);
        });
        // List available tools
        this.server.setRequestHandler(ListToolsRequestSchema, async () => {
            return {
                tools: [
                    {
                        name: 'query_supabase',
                        description: 'Execute a SQL query against the Supabase database',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                query: {
                                    type: 'string',
                                    description: 'SQL query to execute',
                                },
                                table: {
                                    type: 'string',
                                    description: 'Table name (for simple queries)',
                                },
                                operation: {
                                    type: 'string',
                                    enum: ['select', 'insert', 'update', 'delete'],
                                    description: 'Type of operation',
                                },
                                filters: {
                                    type: 'object',
                                    description: 'Filter conditions for select queries',
                                },
                                data: {
                                    type: 'object',
                                    description: 'Data for insert/update operations',
                                },
                            },
                        },
                    },
                    {
                        name: 'get_table_schema',
                        description: 'Get the schema for a specific table',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                tableName: {
                                    type: 'string',
                                    description: 'Name of the table to get schema for',
                                },
                            },
                            required: ['tableName'],
                        },
                    },
                    {
                        name: 'list_tables',
                        description: 'List all tables in the database',
                        inputSchema: {
                            type: 'object',
                            properties: {},
                        },
                    },
                ],
            };
        });
        // Handle tool calls
        this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
            const { name, arguments: args } = request.params;
            switch (name) {
                case 'query_supabase':
                    return await this.handleQuerySupabase(args);
                case 'get_table_schema':
                    return await this.handleGetTableSchema(args);
                case 'list_tables':
                    return await this.handleListTables();
                default:
                    throw new Error(`Unknown tool: ${name}`);
            }
        });
    }
    async loadTableSchemas() {
        if (this.tableSchemas.size > 0)
            return; // Already loaded
        console.log('🔄 Loading table schemas...');
        console.log('🔧 Environment check:', {
            SUPABASE_URL: process.env.SUPABASE_URL ? 'SET' : 'NOT SET',
            SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY ? 'SET' : 'NOT SET',
            SUPABASE_URL_VALUE: process.env.SUPABASE_URL,
            SERVICE_KEY_LENGTH: process.env.SUPABASE_SERVICE_ROLE_KEY?.length || 0,
            SERVICE_KEY_PREFIX: process.env.SUPABASE_SERVICE_ROLE_KEY?.substring(0, 20) + '...',
        });
        // Test database connection first
        console.log('🔍 Testing database connection...');
        try {
            const testResult = await supabase.rpc('exec_sql', { sql: 'SELECT 1 as test' });
            console.log('📋 Connection test result:', testResult);
            if (testResult.error) {
                console.error('❌ Database connection failed:', testResult.error);
                return;
            }
            if (testResult.data && Array.isArray(testResult.data) && testResult.data.length > 0) {
                console.log('✅ Database connection successful, exec_sql returns arrays');
            }
            else {
                console.log('⚠️ Database connection works but exec_sql format is unexpected:', testResult.data);
            }
        }
        catch (connectionError) {
            console.error('💥 Connection test failed:', connectionError);
            return;
        }
        try {
            // Use direct Supabase queries instead of exec_sql
            console.log('🔍 Attempting direct query to information_schema.tables...');
            const { data: tables, error: tablesError } = await supabase
                .from('information_schema.tables')
                .select('table_name')
                .eq('table_schema', 'public')
                .eq('table_type', 'BASE TABLE')
                .order('table_name');
            if (tablesError) {
                console.error('❌ Direct query failed, falling back to exec_sql:', tablesError);
                // Fallback to exec_sql
                const response = await supabase.rpc('exec_sql', {
                    sql: `
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_type = 'BASE TABLE'
            ORDER BY table_name
          `
                });
                console.log('📋 exec_sql response:', response);
                if (response.error) {
                    console.error('❌ exec_sql also failed:', response.error);
                    return;
                }
                // Handle different response formats from exec_sql
                let actualTables;
                if (response.data && Array.isArray(response.data)) {
                    // The data is already an array, use it directly
                    actualTables = response.data;
                    console.log('📋 exec_sql returned array format, using directly');
                }
                else if (response.data && typeof response.data === 'object' && response.data.rows) {
                    // Handle format where results might be in a 'rows' property
                    actualTables = response.data.rows;
                    console.log('📋 exec_sql returned object with rows, extracting rows');
                }
                else if (response.data && response.data.success === false) {
                    console.error('❌ exec_sql returned error:', response.data);
                    return;
                }
                else {
                    // If we can't determine the format, log it and try to use as is
                    console.warn('⚠️ Unexpected exec_sql response format, attempting to use as is:', response.data);
                    actualTables = Array.isArray(response.data) ? response.data : [response.data];
                }
                if (!actualTables || !Array.isArray(actualTables) || actualTables.length === 0) {
                    console.error('❌ No valid tables data found');
                    return;
                }
                console.log('📋 Found tables via exec_sql:', actualTables.length, 'First item structure:', JSON.stringify(actualTables[0]));
                // Process tables from exec_sql - handle different possible formats
                for (const table of actualTables) {
                    // Check if the object has table_name property
                    if (typeof table === 'object' && table !== null) {
                        if (table.table_name) {
                            await this.loadTableColumns(table.table_name);
                        }
                        else if (table.tablename) { // Alternative property name
                            await this.loadTableColumns(table.tablename);
                        }
                        else {
                            // If it's the first item in the array and seems to be column names
                            const firstKey = Object.keys(table)[0];
                            if (firstKey && typeof table[firstKey] === 'string') {
                                await this.loadTableColumns(table[firstKey]);
                            }
                            else {
                                console.error('❌ Could not determine table name from:', table);
                            }
                        }
                    }
                    else if (typeof table === 'string') {
                        // If the response is just a string array
                        await this.loadTableColumns(table);
                    }
                    else {
                        console.error('❌ Unexpected table entry format:', table);
                    }
                }
            }
            else {
                console.log('✅ Direct query succeeded, found tables:', tables.length);
                // Process tables from direct query
                for (const table of tables) {
                    await this.loadTableColumns(table.table_name);
                }
            }
            console.log(`🎉 Total schemas loaded: ${this.tableSchemas.size}`);
        }
        catch (error) {
            console.error('💥 Error loading table schemas:', error);
        }
    }
    async loadTableColumns(tableName) {
        console.log(`🔍 Loading schema for table: ${tableName}`);
        // Try direct query first
        const { data: columns, error: columnsError } = await supabase
            .from('information_schema.columns')
            .select('column_name, data_type, is_nullable, column_default')
            .eq('table_schema', 'public')
            .eq('table_name', tableName)
            .order('ordinal_position');
        if (!columnsError && columns) {
            this.tableSchemas.set(tableName, {
                table_name: tableName,
                columns: columns,
            });
            console.log(`✅ Loaded schema for ${tableName} with ${columns.length} columns`);
        }
        else {
            console.error(`❌ Error loading columns for ${tableName}:`, columnsError);
            // Fallback to exec_sql for columns
            const { data: fallbackColumns, error: fallbackError } = await supabase
                .rpc('exec_sql', {
                sql: `
            SELECT 
              column_name,
              data_type,
              is_nullable,
              column_default
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = '${tableName}'
            ORDER BY ordinal_position
          `
            });
            if (!fallbackError && fallbackColumns) {
                let processedColumns;
                if (Array.isArray(fallbackColumns)) {
                    console.log(`✅ exec_sql returned array format for ${tableName} columns`);
                    processedColumns = fallbackColumns;
                }
                else if (fallbackColumns && typeof fallbackColumns === 'object' && fallbackColumns.rows) {
                    console.log(`✅ exec_sql returned object with rows for ${tableName} columns`);
                    processedColumns = fallbackColumns.rows;
                }
                else {
                    console.warn(`⚠️ Unexpected exec_sql column format for ${tableName}:`, typeof fallbackColumns, Array.isArray(fallbackColumns), Object.keys(fallbackColumns || {}).join(','));
                    processedColumns = [];
                }
                if (processedColumns.length > 0) {
                    this.tableSchemas.set(tableName, {
                        table_name: tableName,
                        columns: processedColumns,
                    });
                    console.log(`✅ Loaded schema for ${tableName} with ${processedColumns.length} columns (via exec_sql)`);
                }
                else {
                    console.error(`❌ No columns found for ${tableName} via exec_sql`);
                }
            }
            else {
                console.error(`❌ Failed to load columns for ${tableName} via exec_sql:`, fallbackError);
            }
        }
    }
    async handleQuerySupabase(args) {
        try {
            if (args.query) {
                // Execute raw SQL query
                const { data, error } = await supabase.rpc('exec_sql', {
                    sql: args.query
                });
                if (error) {
                    throw new Error(`SQL Error: ${error.message}`);
                }
                return {
                    content: [{
                            type: 'text',
                            text: JSON.stringify({ success: true, data }, null, 2),
                        }],
                };
            }
            else if (args.table && args.operation) {
                // Handle structured queries
                return await this.handleStructuredQuery(args);
            }
            else {
                throw new Error('Either "query" or "table" and "operation" must be provided');
            }
        }
        catch (error) {
            return {
                content: [{
                        type: 'text',
                        text: JSON.stringify({
                            success: false,
                            error: error instanceof Error ? error.message : 'Unknown error'
                        }, null, 2),
                    }],
            };
        }
    }
    async handleStructuredQuery(args) {
        const { table, operation, filters, data } = args;
        switch (operation) {
            case 'select':
                let selectQuery = supabase.from(table).select('*');
                if (filters) {
                    Object.entries(filters).forEach(([key, value]) => {
                        selectQuery = selectQuery.eq(key, value);
                    });
                }
                const { data: selectData, error: selectError } = await selectQuery;
                if (selectError)
                    throw selectError;
                return {
                    content: [{
                            type: 'text',
                            text: JSON.stringify({ success: true, data: selectData }, null, 2),
                        }],
                };
            case 'insert':
                if (!data)
                    throw new Error('Data is required for insert operation');
                const { data: insertData, error: insertError } = await supabase
                    .from(table)
                    .insert(data)
                    .select();
                if (insertError)
                    throw insertError;
                return {
                    content: [{
                            type: 'text',
                            text: JSON.stringify({ success: true, data: insertData }, null, 2),
                        }],
                };
            case 'update':
                if (!data)
                    throw new Error('Data is required for update operation');
                let updateQuery = supabase.from(table).update(data);
                if (filters) {
                    Object.entries(filters).forEach(([key, value]) => {
                        updateQuery = updateQuery.eq(key, value);
                    });
                }
                const { data: updateData, error: updateError } = await updateQuery.select();
                if (updateError)
                    throw updateError;
                return {
                    content: [{
                            type: 'text',
                            text: JSON.stringify({ success: true, data: updateData }, null, 2),
                        }],
                };
            case 'delete':
                let deleteQuery = supabase.from(table).delete();
                if (filters) {
                    Object.entries(filters).forEach(([key, value]) => {
                        deleteQuery = deleteQuery.eq(key, value);
                    });
                }
                const { data: deleteData, error: deleteError } = await deleteQuery.select();
                if (deleteError)
                    throw deleteError;
                return {
                    content: [{
                            type: 'text',
                            text: JSON.stringify({ success: true, data: deleteData }, null, 2),
                        }],
                };
            default:
                throw new Error(`Unknown operation: ${operation}`);
        }
    }
    async handleGetTableSchema(args) {
        const { tableName } = args;
        await this.loadTableSchemas();
        const schema = this.tableSchemas.get(tableName);
        if (!schema) {
            return {
                content: [{
                        type: 'text',
                        text: JSON.stringify({ error: `Table ${tableName} not found` }, null, 2),
                    }],
            };
        }
        return {
            content: [{
                    type: 'text',
                    text: JSON.stringify(schema, null, 2),
                }],
        };
    }
    async handleListTables() {
        await this.loadTableSchemas();
        const tables = Array.from(this.tableSchemas.keys());
        return {
            content: [{
                    type: 'text',
                    text: JSON.stringify({ tables }, null, 2),
                }],
        };
    }
    async run() {
        const transport = new StdioServerTransport();
        await this.server.connect(transport);
        console.error('VoiceHype Supabase MCP server running on stdio');
    }
}
// Start the server
const server = new SupabaseMCPServer();
server.run().catch(console.error);
//# sourceMappingURL=index.js.map
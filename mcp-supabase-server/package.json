{"name": "mcp-supabase-server", "version": "1.0.0", "description": "MCP server for Supabase database integration", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsc --watch & node --watch dist/index.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^0.4.0", "@supabase/supabase-js": "^2.39.0", "dotenv": "^16.3.1"}, "devDependencies": {"@types/node": "^20.10.0", "typescript": "^5.3.0"}}
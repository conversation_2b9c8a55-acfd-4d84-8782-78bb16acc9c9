#!/usr/bin/env node

// Test script to verify MCP server can connect to Supabase
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const SUPABASE_URL = 'https://supabase.voicehype.ai';
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoic2VydmljZV9yb2xlIiwiaXNzIjoic3VwYWJhc2UiLCJpYXQiOjE3NDYzODUyMDAsImV4cCI6MTkwNDE1MTYwMH0.Os1wcQGjcoaKowbh5iN6Nf7ejWUX7X69U8x3798_maY';

console.log('Testing Supabase connection...');
console.log('URL:', SUPABASE_URL);
console.log('Service Key:', SUPABASE_SERVICE_ROLE_KEY ? 'Present' : 'Missing');

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function testConnection() {
  try {
    // Test the exec_sql function
    console.log('\nTesting exec_sql function...');
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: 'SELECT table_name FROM information_schema.tables WHERE table_schema = \'public\' AND table_type = \'BASE TABLE\' LIMIT 5'
    });

    if (error) {
      console.error('Error:', error);
      return;
    }

    console.log('Tables found:', data);

    // Test listing user_subscriptions
    console.log('\nTesting user_subscriptions table...');
    const { data: subscriptions, error: subError } = await supabase
      .from('user_subscriptions')
      .select('id, status, plan_id')
      .limit(3);

    if (subError) {
      console.error('Subscriptions error:', subError);
    } else {
      console.log('Sample subscriptions:', subscriptions);
    }

    console.log('\n✅ Connection test successful!');
  } catch (err) {
    console.error('❌ Connection test failed:', err);
  }
}

testConnection();

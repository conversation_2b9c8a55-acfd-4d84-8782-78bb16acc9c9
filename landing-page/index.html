<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VoiceHype - Voice AI for Coding</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://api.fontshare.com/v2/css?f[]=clash-display@600,700,800&display=swap" rel="stylesheet">
    <link href="https://fonts.cdnfonts.com/css/instrument" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Outfit:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/gsap@3.12.2/dist/gsap.min.js"></script>
</head>
<body>
    <!-- Scroll Progress Indicator -->
    <div class="scroll-progress"></div>
    
    <div class="page-wrapper">
        <!-- Navigation bar -->
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <div class="logo-container">
                        <svg width="40" height="30" viewBox="0 0 40 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <!-- Monitor outline with white fill -->
                            <rect x="2" y="2" width="36" height="20" rx="2" fill="white"/>
                            <rect x="2" y="2" width="36" height="20" rx="2" stroke="#14F195" stroke-width="1.5"/>
                            <!-- Sound wave visualization -->
                            <path d="M7 14 L10 9 L13 14 L16 9 L19 14 L22 9 L25 14 L28 9 L31 14 L34 9" stroke="#14F195" stroke-width="1.5" stroke-linecap="round"/>
                            <!-- Stand -->
                            <line x1="20" y1="22" x2="20" y2="26" stroke="#14F195" stroke-width="1.5"/>
                            <line x1="14" y1="26" x2="26" y2="26" stroke="#14F195" stroke-width="1.5"/>
                        </svg>
                    </div>
                    <h1 class="logo-text">Voice<span class="hype-text">Hype</span></h1>
                </div>
                <ul class="nav-links">
                    <li><a href="#features-overview-section" class="nav-link">Features</a></li>
                    <li><a href="#use-cases-section" class="nav-link">Use Cases</a></li>
                    <li><a href="#faq-section" class="nav-link">FAQ</a></li>
                    <li><a href="#blog-section" class="nav-link">Blog</a></li>
                </ul>
                <div class="nav-buttons">
                    <a href="#" class="nav-button login-button">Log In</a>
                    <a href="#" class="nav-button signup-button">Sign Up</a>
                </div>
                    <button class="mobile-menu-toggle">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                    </button>
            </div>
        </nav>
        
        <!-- Top half with code animation -->
        <div class="top-section">
            <canvas id="code-canvas"></canvas>
            <div class="code-overlay"></div>
            
                <div class="hero-content">
                <div class="title-container hover-area">
                    <h1 class="main-title">
                        <span class="title-line">Stop typing.</span>
                        <span class="title-line"><span class="highlight">Start talking.</span></span>
                    </h1>
                    <p class="subtitle">Coding productivity on steroids</p>
                    </div>
                
                <div class="cta-button hover-area">
                    <button class="primary-button">Get Started</button>
                </div>
                
                <!-- Voice-to-Prompt Animation -->
                <div class="voice-to-prompt-animation hover-area">
                    <div class="voice-input-side">
                        <div class="recording-indicator">
                            <div class="voice-wave-animation">
                                <span></span><span></span><span></span><span></span><span></span>
                            </div>
                        </div>
                        <div class="voice-input-text">"I need a... hmm, what do you call it... like a function that can sort data? But also maybe check for... you know... errors and edge cases? I think that would be good."</div>
                                </div>
                    <div class="arrow-transformation">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <line x1="5" y1="12" x2="19" y2="12"></line>
                            <polyline points="12 5 19 12 12 19"></polyline>
                                            </svg>
                                        </div>
                    <div class="optimized-prompt-side">
                        <div class="optimized-prompt-text">Implement a robust sorting function with comprehensive error handling for various data types.</div>
                                    </div>
                                </div>
                            </div>
                        </div>
        
        <!-- Quote Section -->
        <div class="quote-section">
            <div class="quote-container">
                <blockquote class="main-quote">
                    "When you're typing, you're fighting with the LLM. Because you get tired writing and you don't give it all it needs to do the perfect job. So you write more and it doesn't get it before you go crazy."
                </blockquote>
            </div>
        </div>
        
        <!-- Platform Logos Section -->
        <div class="platform-logos-section">
            <div class="logos-carousel-container">
                <div class="logos-carousel">
                    <div class="logo-item" data-platform="cursor">
                        <img src="https://cursor.sh/favicon.ico" alt="Cursor">
                    </div>
                    <div class="logo-item" data-platform="vscode">
                        <img src="https://code.visualstudio.com/favicon.ico" alt="VS Code">
                    </div>
                    <div class="logo-item" data-platform="github">
                        <img src="https://github.githubassets.com/favicons/favicon.svg" alt="GitHub Copilot">
                    </div>
                    <div class="logo-item" data-platform="continue">
                        <div class="continue-logo">
                            <svg width="50" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M25 47.5C37.4264 47.5 47.5 37.4264 47.5 25C47.5 12.5736 37.4264 2.5 25 2.5C12.5736 2.5 2.5 12.5736 2.5 25C2.5 37.4264 12.5736 47.5 25 47.5Z" stroke="white" stroke-width="2.5"/>
                                <path d="M19 18.5L31 25L19 31.5V18.5Z" fill="white"/>
                            </svg>
                        </div>
                    </div>
                    <div class="logo-item" data-platform="warp">
                        <div class="warp-logo">
                            <svg width="50" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="50" height="50" rx="10" fill="#2D2D30"/>
                                <path d="M14 14L25 36L36 14" stroke="#01E8CA" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M20 24.5H30" stroke="#01E8CA" stroke-width="3" stroke-linecap="round"/>
                            </svg>
                        </div>
                    </div>
                    <div class="logo-item" data-platform="windsurf">
                        <div class="windsurf-logo">
                            <svg width="50" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="50" height="50" rx="10" fill="#1F1E3C"/>
                                <path d="M15 25C15 19.477 19.477 15 25 15C30.523 15 35 19.477 35 25C35 30.523 30.523 35 25 35" stroke="#2C7CFF" stroke-width="3" stroke-linecap="round"/>
                                <path d="M25 35C25 35 22 33 18 29" stroke="#2C7CFF" stroke-width="3" stroke-linecap="round"/>
                                <path d="M14 27L18 29L19 25" stroke="#2C7CFF" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M25 22L29 28L33 25" stroke="#65DDFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Voice Hype Intro Section -->
        <div class="intro-section">
            <div class="intro-container">
                <div class="intro-header-row">
                    <div class="intro-header">
                        <div class="intro-title-container">
                            <h2 class="intro-title logo-style"><span class="meet-text">Meet</span> Voice<span class="hype-text">Hype</span></h2>
                        </div>
                        <p class="intro-tagline">
                            VoiceHype transforms coding through <strong>AI-powered</strong> voice recognition, boosting <strong>developer productivity</strong> by converting natural speech into <strong>perfect code prompts</strong>.
                        </p>
                        </div>
                    <div class="intro-video">
                        <div class="video-wrapper">
                            <iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" title="Voice Hype Demo" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Speed Benefits Section -->
        <div class="speed-benefits-section">
            <div class="speed-benefits-container">
                <div class="speed-benefits-content">
                    <h2 class="speed-benefits-title">10x Your Coding Speed</h2>
                    <div class="speed-benefits-metrics">
                        <div class="metric">
                            <span class="metric-number">83%</span>
                            <span class="metric-label">Faster Prompt Creation</span>
                        </div>
                        <div class="metric">
                            <span class="metric-number">4.5x</span>
                            <span class="metric-label">More Code Generated</span>
                        </div>
                        <div class="metric">
                            <span class="metric-number">68%</span>
                            <span class="metric-label">Time Saved Daily</span>
                        </div>
                    </div>
                    <p class="speed-benefits-description">
                        <span class="highlight">Stop wasting hours crafting the perfect prompts.</span> VoiceHype eliminates the bottleneck between your ideas and execution. While others meticulously type out detailed prompts, you'll be speaking naturally and watching as your code materializes in seconds.
                    </p>
                    <div class="speed-benefits-cta">
                        <button class="speed-benefits-button">Try It Now</button>
                        <div class="speed-benefits-guarantee">30-day satisfaction guarantee</div>
                                    </div>
                                </div>
                <div class="speed-benefits-visualization">
                    <div class="comparison-chart">
                        <div class="comparison-item typing">
                            <div class="comparison-label">Typing Prompts <span class="time-indicator">⏱️</span></div>
                            <div class="comparison-bar typing-bar">
                                <span class="typing-indicator">
                                    <span class="typing-dot"></span>
                                    <span class="typing-dot"></span>
                                    <span class="typing-dot"></span>
                                </span>
                            </div>
                            <div class="comparison-time">8-12 min</div>
                        </div>
                        <div class="comparison-item voice">
                            <div class="comparison-label">VoiceHype <span class="speed-badge">FAST</span></div>
                            <div class="comparison-bar voice-bar">
                                <span class="voice-pulse"></span>
                            </div>
                            <div class="comparison-time">15-45 sec</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Key Features Section -->
        <div class="features-overview-section">
            <div class="features-overview-container">
                <h3 class="features-overview-title">What Makes <span style="color: var(--color-primary);">VoiceHype</span> Different</h3>
                
                <div class="intro-feature-list">
                    <div class="intro-feature-item">
                        <div class="feature-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3z"/>
                                <path d="M19 10v2a7 7 0 0 1-14 0v-2"/>
                                <line x1="12" y1="19" x2="12" y2="22"/>
                                <polyline points="8 22 12 22 16 22"/>
                            </svg>
                        </div>
                        <div class="feature-text">
                            <span class="feature-title">Natural Speech Recognition</span>
                            <p>Advanced AI understands casual speech, technical terms, and filler words without requiring perfect syntax</p>
                        </div>
                        <div class="feature-badge">
                            <span class="accuracy">99.7%</span>
                            <span class="badge-label">accuracy</span>
                        </div>
                    </div>
                    
                    <div class="intro-feature-item">
                        <div class="feature-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="12" cy="12" r="10"/>
                                <line x1="2" y1="12" x2="22" y2="12"/>
                                <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"/>
                            </svg>
                        </div>
                        <div class="feature-text">
                            <span class="feature-title">Multilingual AI Support</span>
                            <p>Speak in your native language and get perfect English code prompts with accurate technical terminology</p>
                            </div>
                        <div class="lang-indicators">
                            <span class="lang-flag" title="English">
                                <img src="https://flagcdn.com/w40/us.png" alt="USA flag">
                            </span>
                            <span class="lang-flag" title="Spanish">
                                <img src="https://flagcdn.com/w40/es.png" alt="Spain flag">
                            </span>
                            <span class="lang-flag" title="German">
                                <img src="https://flagcdn.com/w40/de.png" alt="German flag">
                            </span>
                            <span class="lang-flag" title="French">
                                <img src="https://flagcdn.com/w40/fr.png" alt="French flag">
                            </span>
                            <span class="lang-flag" title="Hindi">
                                <img src="https://flagcdn.com/w40/in.png" alt="Indian flag">
                            </span>
                            <span class="lang-flag" title="Chinese">
                                <img src="https://flagcdn.com/w40/cn.png" alt="Chinese flag">
                            </span>
                            <span class="lang-flag" title="Japanese">
                                <img src="https://flagcdn.com/w40/jp.png" alt="Japanese flag">
                            </span>
                            <span class="lang-flag" title="Russian">
                                <img src="https://flagcdn.com/w40/ru.png" alt="Russian flag">
                            </span>
                            <span class="lang-flag" title="Portuguese">
                                <img src="https://flagcdn.com/w40/br.png" alt="Brazilian flag">
                            </span>
                            <span class="lang-flag" title="More Languages">
                                <img src="https://flagcdn.com/w40/un.png" alt="United Nations flag">
                            </span>
                        </div>
                        </div>
                        
                    <div class="intro-feature-item">
                        <div class="feature-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                                <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"/>
                                <circle cx="12" cy="12" r="3"/>
                            </svg>
                            </div>
                        <div class="feature-text">
                            <span class="feature-title">Intelligent Prompt Engineering</span>
                            <p>Proprietary AI algorithms transform messy speech into structured, optimized prompts that consistently produce better code</p>
                        </div>
                        <div class="ai-metrics">
                            <div class="ai-metric">
                                <div class="metric-value">10x</div>
                                <div class="metric-label">more precise</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="intro-feature-item">
                        <div class="feature-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                                <rect x="2" y="5" width="20" height="14" rx="2"/>
                                <line x1="2" y1="10" x2="22" y2="10"/>
                                <path d="M6 15h4"/>
                                <path d="M14 15h4"/>
                            </svg>
                            </div>
                        <div class="feature-text">
                            <span class="feature-title">Universal IDE Integration</span>
                            <p>Seamlessly works with all major coding environments including VS Code, Cursor, JetBrains, and GitHub Copilot</p>
                                </div>
                        <div class="integration-dots">
                            <span class="integration-dot" title="VS Code"></span>
                            <span class="integration-dot" title="Cursor"></span>
                            <span class="integration-dot" title="JetBrains"></span>
                            <span class="integration-dot" title="Copilot"></span>
                            <span class="integration-dot" title="More"></span>
                                </div>
                            </div>
                        </div>
                        
                <div class="intro-cta">
                    <button class="intro-cta-button">Get Started Free</button>
                                </div>
                            </div>
                        </div>
                        
        <!-- Use Cases Section -->
        <div id="use-cases" class="use-cases-section">
            <div class="user-type-container">
                <!-- For Developers Card -->
                <div class="user-type-card">
                    <div class="card-illustration">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="#14F195" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M8 3L4 7l4 4"/>
                            <path d="M16 3l4 4-4 4"/>
                            <line x1="12" y1="20" x2="12" y2="14"/>
                            <path d="M20 14H4"/>
                            <path d="M17 17l-5 3-5-3"/>
                            <path d="M12 14l-4-4"/>
                            <path d="M12 14l4-4"/>
                        </svg>
                            </div>
                    <h3 class="card-title">For Developers</h3>
                    <p class="card-description">Enhance your workflow and quickly transform complex ideas into optimized code with voice-to-prompt technology.</p>
                                </div>
                
                <!-- For Vibe Coders Card -->
                <div class="user-type-card">
                    <div class="card-illustration">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="#0A84FF" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M12 2a8 8 0 0 1 8 8v12l-4-4-4 4-4-4-4 4V10a8 8 0 0 1 8-8z"/>
                            <circle cx="12" cy="10" r="2"/>
                            <path d="M15.8 14.8c-1.2-1.2-3.1-1.8-5.8-1.8s-4.6.6-5.8 1.8"/>
                        </svg>
                                </div>
                    <h3 class="card-title">For Vibe Coders</h3>
                    <p class="card-description">Capture fleeting ideas instantly as you code without breaking flow. Turn scattered thoughts into precise prompts even when you're deep in the zone or can't find the right technical terms.</p>
                            </div>
                        </div>
                    </div>
                </div>

    <!-- Blog Section -->
    <div id="blog" class="blog-section">
        <div class="blog-container">
            <div class="section-header blog-header">
                <h2 class="blog-title">Latest from our <span class="gradient-text">Blog</span></h2>
            </div>
            
            <div class="featured-article">
                <div class="featured-article-image">
                    <div class="image-overlay"></div>
                    <img src="https://images.unsplash.com/photo-1558494949-ef010cbdcc31?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80" alt="Voice AI Technology">
                    <div class="article-badge">Featured</div>
                </div>
                <div class="featured-article-content">
                    <div class="article-meta">
                        <span class="article-category">Technology</span>
                        <span class="article-date">June 15, 2025</span>
                    </div>
                    <h3 class="article-title">How Voice Recognition is Revolutionizing AI Coding Assistants</h3>
                    <p class="article-excerpt">The integration of advanced voice recognition technology with AI coding assistants is creating unprecedented efficiency gains for developers. We explore how this synergy is changing the landscape of software development.</p>
                    <div class="article-footer">
                        <a href="#" class="read-more-link">Read Article <span class="arrow-icon">→</span></a>
                        <div class="article-stats">
                            <span class="stat"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path><circle cx="12" cy="12" r="3"></circle></svg> 1.2k</span>
                            <span class="stat"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path></svg> 423</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="blog-grid">
                <div class="blog-card">
                    <div class="card-image">
                        <img src="https://images.unsplash.com/photo-1550751827-4bd374c3f58b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80" alt="AI Coding">
                    </div>
                    <div class="card-content">
                        <div class="article-meta">
                            <span class="article-category">Tutorials</span>
                            <span class="article-date">June 10, 2025</span>
                        </div>
                        <h3 class="article-title">10 Voice Commands That Will Supercharge Your Coding</h3>
                        <p class="article-excerpt">Master these essential voice commands to dramatically improve your coding efficiency and reduce repetitive tasks.</p>
                        <a href="#" class="read-more-link">Read More <span class="arrow-icon">→</span></a>
                    </div>
                </div>
                
                <div class="blog-card">
                    <div class="card-image">
                        <img src="https://images.unsplash.com/photo-1484589065579-248aad0d8b13?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80" alt="Voice AI Future">
                    </div>
                    <div class="card-content">
                        <div class="article-meta">
                            <span class="article-category">Future Tech</span>
                            <span class="article-date">May 28, 2025</span>
                        </div>
                        <h3 class="article-title">The Future of Development: Voice-First Programming</h3>
                        <p class="article-excerpt">Explore how voice-first interfaces are poised to transform programming paradigms over the next decade.</p>
                        <a href="#" class="read-more-link">Read More <span class="arrow-icon">→</span></a>
                    </div>
                </div>
            </div>
            
            <div class="blog-newsletter">
                <div class="newsletter-content">
                    <h3>Stay updated with the latest in voice coding</h3>
                    <p>Join our newsletter for exclusive tutorials, tips, and early access to new features.</p>
                    <form class="newsletter-form">
                        <input type="email" placeholder="Enter your email address" required>
                        <button type="submit" class="subscribe-button">Subscribe</button>
                    </form>
                </div>
                <div class="newsletter-decoration">
                    <svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                        <path fill="#14F195" fill-opacity="0.2" d="M45.7,-52.2C58.9,-41.7,69.3,-26.9,72.5,-10.6C75.6,5.8,71.6,23.7,62,37.7C52.5,51.7,37.3,61.8,20.6,67.9C3.9,74,-14.3,76.2,-29.7,70.5C-45.1,64.8,-57.7,51.2,-65.4,35.2C-73.1,19.2,-76,0.8,-72.1,-15.8C-68.3,-32.5,-57.7,-47.4,-44.1,-57.9C-30.5,-68.4,-15.2,-74.4,0.4,-74.9C16.1,-75.3,32.2,-70.2,45.7,-58.6Z" transform="translate(100 100)" />
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- FAQ Section -->
    <div id="faq" class="faq-section">
        <div class="faq-container">
            <h2 class="faq-title">Frequently Asked <span class="gradient-text">Questions</span></h2>
                    
                    <div class="faq-grid">
                        <div class="faq-item">
                            <div class="faq-question">
                        <span class="question-text">How does VoiceHype improve my coding workflow?</span>
                        <div class="question-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <line x1="12" y1="5" x2="12" y2="19"></line>
                                <line x1="5" y1="12" x2="19" y2="12"></line>
                            </svg>
                        </div>
                            </div>
                            <div class="faq-answer">
                        <p>VoiceHype transforms your spoken thoughts into perfectly structured code prompts. Instead of spending minutes typing out detailed prompts, you can simply speak naturally and let our AI handle the conversion. This allows you to maintain your creative flow and focus on solving problems rather than crafting prompts.</p>
                            </div>
                        </div>
                        
                        <div class="faq-item">
                            <div class="faq-question">
                        <span class="question-text">Which coding platforms are compatible with VoiceHype?</span>
                        <div class="question-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <line x1="12" y1="5" x2="12" y2="19"></line>
                                <line x1="5" y1="12" x2="19" y2="12"></line>
                            </svg>
                        </div>
                            </div>
                            <div class="faq-answer">
                        <p>VoiceHype seamlessly integrates with all major AI-powered coding platforms including Cursor, VS Code, GitHub Copilot, JetBrains IDEs, Continue.dev, Warp Terminal, and Windsurf Editor. Our universal compatibility ensures you can use VoiceHype with your preferred development environment.</p>
                            </div>
                        </div>
                        
                        <div class="faq-item">
                            <div class="faq-question">
                        <span class="question-text">Do I need to speak with perfect technical precision?</span>
                        <div class="question-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <line x1="12" y1="5" x2="12" y2="19"></line>
                                <line x1="5" y1="12" x2="19" y2="12"></line>
                            </svg>
                        </div>
                            </div>
                            <div class="faq-answer">
                        <p>Not at all! VoiceHype's advanced AI understands natural speech patterns, including filler words, pauses, and casual descriptions. You can speak conversationally and our system will extract the technical intent, translate vague terms into specific technical concepts, and structure your thoughts into optimal prompts.</p>
                            </div>
                        </div>
                        
                        <div class="faq-item">
                            <div class="faq-question">
                        <span class="question-text">What languages does VoiceHype support for voice input?</span>
                        <div class="question-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <line x1="12" y1="5" x2="12" y2="19"></line>
                                <line x1="5" y1="12" x2="19" y2="12"></line>
                            </svg>
                        </div>
                            </div>
                            <div class="faq-answer">
                        <p>VoiceHype supports voice input in over 30 languages including English, Spanish, German, French, Chinese, Japanese, Hindi, Portuguese, Russian, and many more. Regardless of the input language, VoiceHype will convert your voice into perfectly formatted English code prompts with accurate technical terminology.</p>
                            </div>
                        </div>
                        
                        <div class="faq-item">
                            <div class="faq-question">
                        <span class="question-text">How does the free trial work?</span>
                        <div class="question-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <line x1="12" y1="5" x2="12" y2="19"></line>
                                <line x1="5" y1="12" x2="19" y2="12"></line>
                            </svg>
                        </div>
                            </div>
                            <div class="faq-answer">
                        <p>We offer a 14-day free trial with full access to all VoiceHype features. No credit card is required to start. You'll get unlimited voice-to-prompt conversions, access to all language support, and integration with all supported platforms. After the trial period, you can choose from our flexible pricing plans based on your needs.</p>
                            </div>
                        </div>
                        
                        <div class="faq-item">
                            <div class="faq-question">
                        <span class="question-text">What about privacy and data security?</span>
                        <div class="question-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <line x1="12" y1="5" x2="12" y2="19"></line>
                                <line x1="5" y1="12" x2="19" y2="12"></line>
                            </svg>
                        </div>
                            </div>
                            <div class="faq-answer">
                        <p>At VoiceHype, security is our priority. Your voice data is processed with end-to-end encryption, and we never store audio recordings after conversion. Our systems comply with GDPR, CCPA, and industry-standard security protocols. We also offer enterprise plans with dedicated private instances for organizations with heightened security requirements.</p>
                    </div>
                </div>
            </div>
            
            <div class="faq-cta">
                <p>Still have questions? We're here to help.</p>
                <button class="faq-cta-button">Contact Support</button>
            </div>
        </div>
    </div>

    <!-- Scroll to Top Button -->
    <div class="scroll-to-top">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="18 15 12 9 6 15"></polyline>
        </svg>
    </div>

    <!-- Footer Section -->
    <footer class="footer-section">
        <div class="footer-container">
            <div class="footer-content">
                <div class="footer-logo">
                    <div class="logo-container">
                        <svg width="40" height="30" viewBox="0 0 40 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="2" y="2" width="36" height="20" rx="2" fill="white"/>
                            <rect x="2" y="2" width="36" height="20" rx="2" stroke="#14F195" stroke-width="1.5"/>
                            <path d="M7 14 L10 9 L13 14 L16 9 L19 14 L22 9 L25 14 L28 9 L31 14 L34 9" stroke="#14F195" stroke-width="1.5" stroke-linecap="round"/>
                            <line x1="20" y1="22" x2="20" y2="26" stroke="#14F195" stroke-width="1.5"/>
                            <line x1="14" y1="26" x2="26" y2="26" stroke="#14F195" stroke-width="1.5"/>
                        </svg>
                    </div>
                    <h3 class="footer-logo-text">Voice<span class="hype-text">Hype</span></h3>
                </div>
                <p class="footer-tagline">Revolutionizing coding with voice-to-prompt technology</p>
                
                <div class="social-icons">
                    <a href="#" class="social-icon" aria-label="Twitter">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path>
                        </svg>
                    </a>
                    <a href="#" class="social-icon" aria-label="LinkedIn">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                            <rect x="2" y="9" width="4" height="12"></rect>
                            <circle cx="4" cy="4" r="2"></circle>
                        </svg>
                    </a>
                    <a href="#" class="social-icon" aria-label="GitHub">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"></path>
                        </svg>
                    </a>
                    <a href="#" class="social-icon" aria-label="Discord">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M18 9a5 5 0 0 0-5-5H7a5 5 0 0 0-5 5v6a5 5 0 0 0 5 5h6"></path>
                            <circle cx="16" cy="16" r="3"></circle>
                        </svg>
                    </a>
                    <a href="#" class="social-icon" aria-label="YouTube">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M22.54 6.42a2.78 2.78 0 0 0-1.94-2C18.88 4 12 4 12 4s-6.88 0-8.6.46a2.78 2.78 0 0 0-1.94 2A29 29 0 0 0 1 11.75a29 29 0 0 0 .46 5.33A2.78 2.78 0 0 0 3.4 19c1.72.46 8.6.46 8.6.46s6.88 0 8.6-.46a2.78 2.78 0 0 0 1.94-2 29 29 0 0 0 .46-5.25 29 29 0 0 0-.46-5.33z"></path>
                            <polygon points="9.75 15.02 15.5 11.75 9.75 8.48 9.75 15.02"></polygon>
                        </svg>
                    </a>
                </div>
            </div>
            
            <div class="footer-links">
                <div class="footer-links-column">
                    <h4>Product</h4>
                    <ul>
                        <li><a href="#features">Features</a></li>
                        <li><a href="#pricing">Pricing</a></li>
                        <li><a href="#use-cases">Use Cases</a></li>
                        <li><a href="#blog">Blog</a></li>
                        <li><a href="#faq">FAQ</a></li>
                    </ul>
                </div>
                <div class="footer-links-column">
                    <h4>Company</h4>
                    <ul>
                        <li><a href="#about">About Us</a></li>
                        <li><a href="#careers">Careers</a></li>
                        <li><a href="#contact">Contact</a></li>
                        <li><a href="#partners">Partners</a></li>
                    </ul>
                </div>
                <div class="footer-links-column">
                    <h4>Resources</h4>
                    <ul>
                        <li><a href="#documentation">Documentation</a></li>
                        <li><a href="#tutorials">Tutorials</a></li>
                        <li><a href="#community">Community</a></li>
                        <li><a href="#api">API</a></li>
                    </ul>
                </div>
                <div class="footer-links-column">
                    <h4>Legal</h4>
                    <ul>
                        <li><a href="#privacy">Privacy Policy</a></li>
                        <li><a href="#terms">Terms of Service</a></li>
                        <li><a href="#refund">Refund Policy</a></li>
                        <li><a href="#security">Security</a></li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="footer-bottom">
            <div class="footer-bottom-container">
                <p class="copyright">© 2025 VoiceHype. All rights reserved.</p>
                <div class="contact-info">
                    <a href="mailto:<EMAIL>" class="contact-email">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                            <polyline points="22,6 12,13 2,6"></polyline>
                        </svg>
                        <EMAIL>
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html> 
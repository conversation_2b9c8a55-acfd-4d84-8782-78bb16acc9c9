/* Mobile Responsiveness Fixes for VoiceHype
 * This file addresses specific mobile UI issues
 */

/* Prevent scrolling when mobile menu is open */
body.menu-open {
    overflow: hidden;
}

/* Fix vertical spacing between hero elements */
.hero-content {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

/* Adjust main title for better mobile display */
.main-title {
    margin-bottom: 0.75rem;
}

@media (max-width: 768px) {
    .main-title {
        font-size: 3.5rem;
        margin-bottom: 0.5rem;
    }
    
    /* Fix spacing between heading components */
    .title-container {
        margin-bottom: 1rem;
    }
    
    .subtitle {
        margin-top: 0.5rem;
        font-size: 1.2rem;
    }
    
    /* Adjust CTA button for better spacing */
    .cta-button {
        margin-top: 1.5rem;
        margin-bottom: 2rem;
    }
    
    /* Fix mobile menu toggle appearance and functionality */
    .mobile-menu-toggle {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        height: 24px;
        padding: 0;
        margin-left: 1rem;
        z-index: 1001;
        background: transparent;
        border: none;
        cursor: pointer;
    }
    
    .bar {
        display: block;
        width: 25px;
        height: 3px;
        margin: 5px auto;
        background-color: var(--color-text);
        transition: all 0.3s ease;
    }
    
    .mobile-menu-toggle.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }
    
    .mobile-menu-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }
    
    .mobile-menu-toggle.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }
    
    /* Make navbar links fullwidth on mobile */
    .nav-links {
        position: fixed;
        top: var(--nav-height);
        left: -100%;
        flex-direction: column;
        gap: 0;
        width: 100%;
        background-color: rgba(5, 5, 5, 0.95);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        padding: 1rem 0;
        text-align: center;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        height: calc(100vh - var(--nav-height));
        overflow-y: auto;
    }
    
    .nav-links.active {
        left: 0;
    }
    
    .nav-links li {
        width: 100%;
    }
    
    .nav-link {
        display: block;
        padding: 1rem 0;
    }
    
    /* Hide nav buttons on small screens */
    .nav-buttons {
        display: none;
    }
    
    /* Fix quote section padding and margins */
    .quote-section {
        padding: 3rem 1.5rem;
    }
    
    .quote-container {
        width: 100%; 
    }
    
    .main-quote {
        font-size: 1.5rem;
        padding: 0 0.5rem;
    }
    
    /* Make voice-to-prompt animation more compact on tablets */
    .voice-to-prompt-animation {
        padding: 0 0.75rem;
        gap: 1rem;
    }
    
    .voice-input-text,
    .optimized-prompt-text {
        padding: 0.75rem;
        font-size: 0.9rem;
    }
}

/* Specific adjustments for small screens */
@media (max-width: 480px) {
    .top-section {
        padding-top: calc(var(--nav-height) + 1rem);
    }
    
    .hero-content {
        gap: 0.25rem;
        align-items: center;
        text-align: center;
    }
    
    .title-container {
        margin-bottom: 0.5rem;
    }
    
    .main-title {
        font-size: 2.8rem;
        margin-bottom: 0.25rem;
        gap: 0.5rem;
    }
    
    .title-line .highlight {
        padding-bottom: 15px;
        margin-bottom: 5px;
    }
    
    .subtitle {
        font-size: 1rem;
        margin-top: 0.5rem;
        max-width: 95%;
    }
    
    .cta-button {
        margin-top: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .primary-button {
        font-size: 1rem;
        padding: 0.9rem 2rem;
    }
    
    /* Fix quote box to ensure it stays within viewport width */
    .quote-section {
        padding: 2rem 1rem;
    }
    
    .quote-container {
        width: 100%;
        overflow-x: hidden;
    }
    
    .main-quote {
        font-size: 1.2rem;
        line-height: 1.4;
        padding: 0 1rem;
        word-wrap: break-word;
    }
    
    .main-quote::before {
        top: -1.5rem;
        left: -0.5rem;
        font-size: 2.5rem;
    }
    
    .main-quote::after {
        bottom: -2.5rem;
        right: -0.5rem;
        font-size: 2.5rem;
    }
    
    /* Fix mobile menu toggle for smaller screens */
    .mobile-menu-toggle {
        margin-right: 0.5rem;
        height: 20px;
    }
    
    .bar {
        width: 22px;
        height: 2px;
        margin: 4px auto;
    }
    
    /* Stack voice-to-prompt elements vertically on mobile */
    .voice-to-prompt-animation {
        flex-direction: column;
        gap: 1rem;
        margin-top: 1rem;
        padding: 0 0.5rem;
    }
    
    .voice-input-side, 
    .optimized-prompt-side {
        width: 100%;
        max-width: 100%;
    }
    
    .arrow-transformation {
        transform: rotate(90deg);
        margin: 0.25rem 0;
    }
    
    .voice-input-text,
    .optimized-prompt-text {
        padding: 0.75rem;
        font-size: 0.85rem;
        overflow-wrap: break-word;
        word-break: normal;
    }
    
    .recording-indicator {
        margin-right: 0.5rem;
    }
    
    /* Fix navbar links when menu is open */
    .nav-links.active {
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
    }
    
    .nav-link {
        padding: 0.75rem 0;
    }
}

/* Enhanced styling for voice-to-prompt animation */
.voice-to-prompt-animation {
    max-width: 100%;
    padding: 0 1rem;
    overflow: visible;
}

.voice-input-side, 
.optimized-prompt-side {
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.voice-input-text,
.optimized-prompt-text {
    padding: 1rem;
    font-size: 0.95rem;
    line-height: 1.5;
}

/* Fix for consistent section spacing */
.top-section,
.quote-section,
.platform-logos-section,
.intro-section,
.speed-benefits-section,
.mind-blown-section,
.features-overview-section,
.use-cases-section,
.blog-section,
.faq-section {
    padding-left: 1rem;
    padding-right: 1rem;
}

/* Center all content properly */
.title-container,
.cta-button,
.voice-to-prompt-animation,
.quote-container,
.intro-container,
.speed-benefits-container,
.mind-blown-container,
.features-overview-container,
.user-type-container,
.blog-container,
.faq-container {
    margin-left: auto;
    margin-right: auto;
    width: 100%;
}

@media (max-width: 480px) {
    /* Adjust section spacing for mobile */
    .top-section,
    .quote-section,
    .platform-logos-section,
    .intro-section,
    .speed-benefits-section,
    .mind-blown-section,
    .features-overview-section,
    .use-cases-section,
    .blog-section,
    .faq-section {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }
    
    /* Fix vertical spacing issues */
    .top-section {
        min-height: calc(100vh - var(--nav-height));
        display: flex;
        align-items: center;
        justify-content: center;
        padding-top: calc(var(--nav-height) + 1rem);
        padding-bottom: 2rem;
    }
    
    /* Fix content overflow issues */
    img, video, svg, canvas {
        max-width: 100%;
        height: auto;
    }
    
    /* Ensure proper text alignment */
    .title-container,
    .hero-content,
    .cta-button,
    .quote-container,
    .main-quote {
        text-align: center;
    }
} 
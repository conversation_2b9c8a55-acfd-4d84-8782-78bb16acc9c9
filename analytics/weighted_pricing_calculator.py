#!/usr/bin/env python3
"""
VoiceHype Weighted Pricing Calculator
Bismillahir rahmanir raheem

This script implements weighted pricing calculations based on actual user preferences
and usage patterns, replacing the flawed simple average approach.
"""

# import pandas as pd
import numpy as np


class WeightedPricingCalculator:
    def __init__(self):
        """Initialize with Claude Sonnet 4 pricing data."""
        print("Bismillahir rahmanir raheem")
        print("=== Claude Sonnet 4 Pricing Calculator ===\n")

        # Updated transcription costs with Assembly AI realtime pricing
        # 50% of users use Assembly AI realtime at $0.0092/min
        self.transcription_models_reference = [
            {
                "name": "Assembly AI Realtime",
                "cost_per_minute": 0.0092,
                "usage_weight": 0.5,
                "provider": "assembly_ai",
            },
            {
                "name": "Assembly AI Best",
                "cost_per_minute": 0.006,
                "usage_weight": 0.25,
                "provider": "assembly_ai",
            },
            {
                "name": "Whisper (Lemon Fox)",
                "cost_per_minute": 0.007,
                "usage_weight": 0.25,
                "provider": "lemon_fox",
            },
        ]

        # Claude Sonnet 4 optimization model (100% weight)
        self.optimization_models = [
            {
                "name": "Claude Sonnet 4",
                "input_cost": 3.0 / 1_000_000,  # $3.0 per million input tokens
                "output_cost": 15.0 / 1_000_000,  # $15.0 per million output tokens
                "usage_weight": 1.0,  # 100% of usage
                "provider": "anthropic",
            }
        ]

        # Validate weights sum to 1.0 for both model types
        transcription_weight = sum(
            model["usage_weight"] for model in self.transcription_models_reference
        )
        optimization_weight = sum(
            model["usage_weight"] for model in self.optimization_models
        )

        print(f"Transcription model weights: {transcription_weight:.2f}")
        print(f"Optimization model weights: {optimization_weight:.2f}")

        if abs(transcription_weight - 1.0) > 0.01:
            print("⚠️  WARNING: Transcription weights don't sum to 1.0!")
        if abs(optimization_weight - 1.0) > 0.01:
            print("⚠️  WARNING: Optimization weights don't sum to 1.0!")
        print()

    def calculate_transcription_costs(self):
        """Calculate transcription costs (simple and weighted)."""
        # Simple average
        simple_transcription = np.mean(
            [model["cost_per_minute"] for model in self.transcription_models_reference]
        )

        # Weighted average
        weighted_transcription = sum(
            model["cost_per_minute"] * model["usage_weight"]
            for model in self.transcription_models_reference
        )

        return simple_transcription, weighted_transcription

    def calculate_optimization_costs(self):
        """Calculate optimization costs (simple and weighted)."""
        # Simple averages
        input_costs = [model["input_cost"] for model in self.optimization_models]
        output_costs = [model["output_cost"] for model in self.optimization_models]

        simple_input = np.mean(input_costs)
        simple_output = np.mean(output_costs)

        # Weighted averages
        weighted_input = sum(
            model["input_cost"] * model["usage_weight"]
            for model in self.optimization_models
        )
        weighted_output = sum(
            model["output_cost"] * model["usage_weight"]
            for model in self.optimization_models
        )

        return simple_input, simple_output, weighted_input, weighted_output

    def calculate_blended_cost_per_token(self, input_output_ratio=1.0):
        """
        Calculate blended cost per token considering input/output ratio.

        Args:
            input_output_ratio: Ratio of input to output tokens (default 1:1)
        """
        simple_input, simple_output, weighted_input, weighted_output = (
            self.calculate_optimization_costs()
        )

        # Calculate blended costs
        simple_blended = (simple_input + simple_output * input_output_ratio) / (
            1 + input_output_ratio
        )
        weighted_blended = (weighted_input + weighted_output * input_output_ratio) / (
            1 + input_output_ratio
        )

        return {
            "simple": {
                "input": simple_input,
                "output": simple_output,
                "blended": simple_blended,
            },
            "weighted": {
                "input": weighted_input,
                "output": weighted_output,
                "blended": weighted_blended,
            },
        }

    def analyze_pricing_impact(self):
        """Analyze the impact of switching from simple to weighted pricing."""
        print("=== PRICING METHODOLOGY COMPARISON ===")
        print("=" * 80)

        # Calculate for different input/output ratios
        ratios = [0.5, 1.0, 2.0, 3.0]  # Different scenarios

        for ratio in ratios:
            print(f"\nInput:Output Ratio = 1:{ratio}")
            print("-" * 50)

            costs = self.calculate_blended_cost_per_token(ratio)

            simple_cost = costs["simple"]["blended"]
            weighted_cost = costs["weighted"]["blended"]

            difference = weighted_cost - simple_cost
            percentage_increase = (difference / simple_cost) * 100

            print(f"Simple Average:   ${simple_cost:.8f} per token")
            print(f"Weighted Average: ${weighted_cost:.8f} per token")
            print(
                f"Difference:       ${difference:.8f} per token ({percentage_increase:+.1f}%)"
            )

    def calculate_subscription_impact(self):
        """Calculate impact on subscription pricing."""
        print("\n=== SUBSCRIPTION PRICING IMPACT ===")
        print("=" * 80)

        # VoiceHype assumptions
        words_per_minute = 150
        tokens_per_word = 1
        fixed_prompt_tokens = 500
        transcription_cost_per_minute = 0.005709

        # Calculate tokens per minute
        speech_tokens_per_minute = words_per_minute * tokens_per_word
        total_tokens_per_minute = fixed_prompt_tokens + speech_tokens_per_minute

        print(f"Tokens per minute: {total_tokens_per_minute}")
        print(f"  - Fixed prompt: {fixed_prompt_tokens}")
        print(f"  - Speech tokens: {speech_tokens_per_minute}")
        print()

        # Assume 1:3 input:output ratio (typical for optimization)
        input_output_ratio = 3.0
        costs = self.calculate_blended_cost_per_token(input_output_ratio)

        # Calculate LLM cost per minute
        simple_llm_cost = total_tokens_per_minute * costs["simple"]["blended"]
        weighted_llm_cost = total_tokens_per_minute * costs["weighted"]["blended"]

        # Total cost per minute
        simple_total = transcription_cost_per_minute + simple_llm_cost
        weighted_total = transcription_cost_per_minute + weighted_llm_cost

        print("Cost per minute breakdown:")
        print(f"Transcription: ${transcription_cost_per_minute:.6f}")
        print(f"LLM (Simple):  ${simple_llm_cost:.6f}")
        print(f"LLM (Weighted): ${weighted_llm_cost:.6f}")
        print()
        print(f"Total (Simple):  ${simple_total:.6f}")
        print(f"Total (Weighted): ${weighted_total:.6f}")
        print(
            f"Increase: ${weighted_total - simple_total:.6f} ({((weighted_total - simple_total) / simple_total) * 100:+.1f}%)"
        )

        return simple_total, weighted_total

    def analyze_tier_sustainability(self, simple_cost, weighted_cost):
        """Analyze impact on subscription tier sustainability."""
        print("\n=== TIER SUSTAINABILITY ANALYSIS ===")
        print("=" * 80)

        # Current tier structure
        tiers = [
            {"name": "Basic", "price": 9, "margin": 0.70, "multiplier": 1.0},
            {"name": "Professional", "price": 18, "margin": 0.65, "multiplier": 3.0},
        ]

        for tier in tiers:
            print(
                f"\n{tier['name']} Tier (${tier['price']}/month, {tier['margin']*100:.0f}% margin):"
            )

            # Calculate resources with both pricing methods
            cost_budget = tier["price"] * (1 - tier["margin"])
            effective_budget = cost_budget * tier["multiplier"]

            simple_minutes = effective_budget / simple_cost
            weighted_minutes = effective_budget / weighted_cost

            simple_tokens = (
                simple_minutes * 520
            )  # tokens per minute (370 input + 150 output)
            weighted_tokens = weighted_minutes * 520

            print(f"  Cost budget: ${cost_budget:.2f}")
            print(f"  Effective budget: ${effective_budget:.2f}")
            print(
                f"  Simple method:   {simple_minutes:.0f} minutes, {simple_tokens:.0f} tokens"
            )
            print(
                f"  Weighted method: {weighted_minutes:.0f} minutes, {weighted_tokens:.0f} tokens"
            )
            print(
                f"  Resource impact: {((weighted_minutes - simple_minutes) / simple_minutes) * 100:+.1f}% minutes"
            )

    def calculate_claude_sonnet_4_costs(self):
        """Calculate Claude Sonnet 4 costs."""
        print("\n=== CLAUDE SONNET 4 COST CALCULATION ===")
        print("=" * 80)

        # Claude Sonnet 4 pricing calculation
        input_tokens = 220 + 150  # 370 total input tokens
        output_tokens = 150

        # Base costs with Claude Sonnet 4 pricing
        input_cost = input_tokens * (3.0 / 1_000_000)
        output_cost = output_tokens * (15.0 / 1_000_000)

        # Total optimization cost (18% profit margin)
        optimization_cost = (input_cost + output_cost) * 1.18

        # Transcription cost (weighted average)
        transcription_cost = self.calculate_transcription_costs()[
            1
        ]  # weighted transcription

        total_cost_per_minute = transcription_cost + optimization_cost

        print(f"Input tokens: {input_tokens}")
        print(f"Output tokens: {output_tokens}")
        print(f"Input cost: ${input_cost:.8f}")
        print(f"Output cost: ${output_cost:.8f}")
        print(f"Optimization cost: ${optimization_cost:.8f}")
        print(f"Transcription cost: ${transcription_cost:.8f}")
        print(f"Total cost per minute: ${total_cost_per_minute:.8f}")

        return total_cost_per_minute

    def calculate_affordable_minutes(self, cost_per_minute):
        """Calculate how many minutes we can afford at different price points."""
        print("\n=== AFFORDABLE MINUTES ANALYSIS ===")
        print("=" * 80)

        budgets = [4.99, 9.99, 14.99]  # Updated pricing tiers

        for budget in budgets:
            minutes = budget / cost_per_minute
            total_tokens = minutes * 520  # 370 input + 150 output tokens
            print(f"${budget}: {minutes:.1f} minutes ({total_tokens:.0f} tokens)")

        return {budget: budget / cost_per_minute for budget in budgets}

    def verify_weighted_calculations(self):
        """Verify that weighted calculations include ALL models properly."""
        print("=== CLAUDE SONNET 4 PRICING CALCULATION ===")
        print("=" * 80)

        # Transcription verification
        print("TRANSCRIPTION MODELS:")
        simple_trans, weighted_trans = self.calculate_transcription_costs()

        for model in self.transcription_models_reference:
            print(
                f"  {model['name']}: ${model['cost_per_minute']:.6f}/min (weight: {model['usage_weight']*100:.0f}%)"
            )

        print(f"\nWeighted Transcription: ${weighted_trans:.6f}/min")
        print()

        # Claude Sonnet 4 verification
        print("OPTIMIZATION MODEL (CLAUDE SONNET 4):")
        simple_input, simple_output, weighted_input, weighted_output = (
            self.calculate_optimization_costs()
        )

        for model in self.optimization_models:
            print(f"  {model['name']}:")
            print(
                f"    Input: ${model['input_cost']*1_000_000:.2f}/million tokens (weight: {model['usage_weight']*100:.0f}%)"
            )
            print(f"    Output: ${model['output_cost']*1_000_000:.2f}/million tokens")

        print()

        # Calculate final cost using Claude Sonnet 4
        total_cost = self.calculate_claude_sonnet_4_costs()

        return total_cost


def main():
    """Main execution function."""
    calculator = WeightedPricingCalculator()

    # Verify updated calculations
    total_cost = calculator.verify_weighted_calculations()

    # Calculate affordable minutes
    affordable_minutes = calculator.calculate_affordable_minutes(total_cost)

    print("\n✅ CALCULATION COMPLETE!")
    print(f"💰 Final cost per minute: ${total_cost:.6f}")
    print(f"📊 Affordable minutes: {affordable_minutes}")

    # Calculate specific tier details
    print("\n=== TIER BREAKDOWN ===")
    print("=" * 80)

    tiers = [
        {"name": "Basic", "price": 4.99},
        {"name": "Pro", "price": 9.99},
        {"name": "Premium", "price": 14.99},
    ]

    for tier in tiers:
        minutes = tier["price"] / total_cost
        tokens = minutes * 520  # 370 input + 150 output tokens
        print(
            f"{tier['name']} (${tier['price']}): {minutes:.0f} minutes, {tokens:.0f} tokens"
        )


if __name__ == "__main__":
    main()

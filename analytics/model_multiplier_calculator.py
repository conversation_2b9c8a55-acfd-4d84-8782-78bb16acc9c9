#!/usr/bin/env python3
"""
VoiceHype Quota Multiplier Verification - CORRECTED
Bismillahir rahmanir raheem

This script verifies the accuracy of quota multipliers by ensuring that
adjusted usage results in equivalent cost calculations.
FIXED: Properly accounts for input/output token structure.
"""

import pandas as pd

class ModelMultiplierCalculator:
    def __init__(self):
        """Initialize with model costs and fixed transcription pricing."""
        print("Bismillahir rahmanir raheem - Corrected Version")
        print("=== Simplified Model Multiplier Calculator ===\n")
        
        # Constants
        self.WORDS_PER_MINUTE = 150
        self.TOKENS_PER_WORD = 1
        self.FIXED_PROMPT_TOKENS = 220
        self.OUTPUT_TOKENS = 150
        
        # Fixed transcription cost per minute
        self.transcription_cost_per_minute = 0.005709
        
        # Optimization models (input/output costs per token)
        self.optimization_models = {
            'Claude Sonnet': {
                'input_cost': 0.*********,
                'output_cost': 0.*********
            },
            '<PERSON>': {
                'input_cost': 0.*********,
                'output_cost': 0.*********
            },
            'DeepSeek V3': {
                'input_cost': 0.000001003,
                'output_cost': 0.000001062
            },
            'Llama 3.1 70B': {
                'input_cost': 0.000000271,
                'output_cost': 0.000000472
            }
        }
        
    def calculate_optimization_cost(self, model_name, minutes=1):
        """Calculate optimization cost for given model and minutes."""
        model = self.optimization_models[model_name]
        input_tokens = (self.FIXED_PROMPT_TOKENS +
                       (self.WORDS_PER_MINUTE * self.TOKENS_PER_WORD)) * minutes
        output_tokens = self.OUTPUT_TOKENS * minutes
        return (input_tokens * model['input_cost'] +
               output_tokens * model['output_cost'])
    
    def get_token_breakdown(self, minutes=1):
        """Get detailed token breakdown for given minutes."""
        input_tokens = (self.FIXED_PROMPT_TOKENS + 
                       (self.WORDS_PER_MINUTE * self.TOKENS_PER_WORD)) * minutes
        output_tokens = self.OUTPUT_TOKENS * minutes
        total_tokens = input_tokens + output_tokens
        
        return {
            'input_tokens': input_tokens,
            'output_tokens': output_tokens,
            'total_tokens': total_tokens,
            'fixed_prompt_tokens': self.FIXED_PROMPT_TOKENS * minutes,
            'transcription_tokens': (self.WORDS_PER_MINUTE * self.TOKENS_PER_WORD) * minutes
        }
    
    def calculate_multipliers(self, base_model='Claude Sonnet'):
        """Calculate multipliers based on total cost equivalence."""
        results = []
        
        # Base costs
        base_opt_cost = self.calculate_optimization_cost(base_model)
        base_total_cost = self.transcription_cost_per_minute + base_opt_cost
        
        for model_name in self.optimization_models:
            # Current model costs
            opt_cost = self.calculate_optimization_cost(model_name)
            total_cost = self.transcription_cost_per_minute + opt_cost
            
            # Single multiplier based on total cost
            cost_multiplier = base_total_cost / total_cost
            
            results.append({
                'Model': model_name,
                'Cost_Multiplier': cost_multiplier,
                'Total_Cost_Per_Min': total_cost,
                'Optimization_Cost_Per_Min': opt_cost,
                'Base_Total_Cost': base_total_cost,
                'Cost_Ratio': total_cost / base_total_cost
            })
        
        return pd.DataFrame(results)

def verify_multiplier_accuracy():
    """Verify that multipliers result in accurate cost equivalence."""
    print("=== CORRECTED Quota Multiplier Verification ===\n")
    
    # Initialize calculator
    calc = ModelMultiplierCalculator()
    
    # Test scenario: 5 minutes of usage
    test_minutes = 5
    
    print(f"Testing with {test_minutes} minutes of usage:\n")
    
    # Get multipliers
    multipliers = calc.calculate_multipliers()
    
    # Base model (Claude Sonnet) costs
    base_model = 'Claude Sonnet'
    base_total_cost_per_min = calc.transcription_cost_per_minute + calc.calculate_optimization_cost(base_model)
    
    print(f"Base model ({base_model}) cost per minute: ${base_total_cost_per_min:.6f}")
    print(f"Base model total cost for {test_minutes} minutes: ${base_total_cost_per_min * test_minutes:.6f}\n")
    
    # Verify each model
    verification_results = []
    
    for _, row in multipliers.iterrows():
        model_name = row['Model']
        cost_mult = row['Cost_Multiplier']
        
        print(f"=== Verifying {model_name} ===")
        
        # Actual costs for this model
        actual_cost_per_min = calc.transcription_cost_per_minute + calc.calculate_optimization_cost(model_name)
        actual_total_cost = actual_cost_per_min * test_minutes
        
        # Calculate adjusted usage (this is what would be deducted from quota)
        adjusted_minutes = test_minutes / cost_mult
        
        # Calculate cost using adjusted usage at base model rates
        calculated_cost = adjusted_minutes * base_total_cost_per_min
        
        print(f"Actual usage: {test_minutes} minutes")
        print(f"Adjusted usage (quota deduction): {adjusted_minutes:.2f} minutes")
        print(f"Cost multiplier: {cost_mult:.2f}x")
        print(f"Actual total cost: ${actual_total_cost:.6f}")
        print(f"Calculated cost using adjusted usage: ${calculated_cost:.6f}")
        
        # Verification
        cost_difference = abs(actual_total_cost - calculated_cost)
        is_accurate = cost_difference < 0.000001  # Allow for floating point precision
        
        print(f"Difference: ${cost_difference:.8f}")
        print(f"Verification: {'✅ ACCURATE' if is_accurate else '❌ INACCURATE'}")
        
        # Show the math clearly
        print(f"Math check: {adjusted_minutes:.3f} × ${base_total_cost_per_min:.6f} = ${calculated_cost:.6f}")
        print(f"Should equal: ${actual_total_cost:.6f}")
        print("-" * 60)
        
        verification_results.append({
            'Model': model_name,
            'Cost_Multiplier': cost_mult,
            'Actual_Cost': actual_total_cost,
            'Calculated_Cost': calculated_cost,
            'Cost_Difference': cost_difference,
            'Accurate': is_accurate
        })
    
    # Summary
    print("\n=== VERIFICATION SUMMARY ===")
    df = pd.DataFrame(verification_results)
    
    for _, row in df.iterrows():
        status = "✅ VERIFIED" if row['Accurate'] else "❌ FAILED"
        print(f"{row['Model']}: {status}")
    
    all_verified = df['Accurate'].all()
    print(f"\nOverall verification: {'✅ ALL MULTIPLIERS ACCURATE' if all_verified else '❌ SOME MULTIPLIERS INACCURATE'}")
    
    # Detailed breakdown for DeepSeek (your specific example)
    print("\n=== DETAILED DEEPSEEK BREAKDOWN ===")
    deepseek_row = df[df['Model'] == 'DeepSeek V3'].iloc[0]
    
    print(f"DeepSeek V3 for {test_minutes} minutes:")
    print(f"- Actual cost: ${deepseek_row['Actual_Cost']:.6f}")
    print(f"- Quota deduction: {test_minutes / deepseek_row['Cost_Multiplier']:.2f} minutes")
    print(f"- Cost if charged at Claude rates: ${deepseek_row['Calculated_Cost']:.6f}")
    print(f"- Difference: ${deepseek_row['Cost_Difference']:.8f}")
    print(f"- This confirms the multiplier is {'✅ CORRECT' if deepseek_row['Accurate'] else '❌ INCORRECT'}!")
    
    # Show token breakdown for understanding
    print("\n=== TOKEN BREAKDOWN (for understanding) ===")
    tokens = calc.get_token_breakdown(test_minutes)
    print(f"For {test_minutes} minutes:")
    print(f"- Fixed prompt tokens: {tokens['fixed_prompt_tokens']}")
    print(f"- Transcription tokens: {tokens['transcription_tokens']}")
    print(f"- Input tokens total: {tokens['input_tokens']}")
    print(f"- Output tokens: {tokens['output_tokens']}")
    print(f"- Total tokens: {tokens['total_tokens']}")
    
    return df

def demonstrate_simple_quota_system():
    """Demonstrate the corrected, simplified quota system."""
    print("\n=== SIMPLIFIED QUOTA SYSTEM ===")
    
    calc = ModelMultiplierCalculator()
    
    # Example: User has 100 minutes quota
    base_quota_minutes = 100
    
    print(f"User quota: {base_quota_minutes} minutes")
    print("\nHow quota is deducted for different models:")
    
    multipliers = calc.calculate_multipliers()
    
    # Example usage: 10 minutes of actual usage
    example_usage = 10
    
    print(f"\nExample: User uses {example_usage} minutes on different models")
    print("Quota deduction vs actual usage:")
    
    for _, row in multipliers.iterrows():
        model = row['Model']
        mult = row['Cost_Multiplier']
        
        # How much quota is deducted
        quota_deduction = example_usage / mult
        
        # Actual cost
        actual_cost = (example_usage * 
                      (calc.transcription_cost_per_minute + calc.calculate_optimization_cost(model)))
        
        print(f"{model}:")
        print(f"  - Actual usage: {example_usage} minutes")
        print(f"  - Quota deducted: {quota_deduction:.2f} minutes")
        print(f"  - Actual cost: ${actual_cost:.4f}")
        print(f"  - Multiplier: {mult:.2f}x")
    
    print(f"\nKey insight: Cheaper models allow more actual usage for the same quota!")

if __name__ == "__main__":
    # Run verification
    results_df = verify_multiplier_accuracy()
    
    # Demonstrate the system
    demonstrate_simple_quota_system()
    
    # Save verification results
    results_df.to_csv('corrected_verification_results.csv', index=False)
    print(f"\n📊 Verification results saved to corrected_verification_results.csv")
    
    print("\n🔍 The key insight: Use a SINGLE cost-based multiplier, not separate ones for minutes and tokens!")
    print("This ensures perfect cost equivalence while keeping the system simple to understand.")
    print("\nJazakallah khair for catching this critical issue! 🤲")
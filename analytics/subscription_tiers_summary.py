#!/usr/bin/env python3
"""
VoiceHype Subscription Tiers Summary Table Generator
Bismillahir rahmanir raheem

This script creates a clean summary table of the recommended subscription tiers.
"""

import pandas as pd
import numpy as np

def create_subscription_summary():
    """Create a comprehensive subscription tiers summary."""
    
    # Our recommended tiers
    our_tiers = {
        'Basic': {
            'price': 9,
            'minutes': 324,
            'tokens': 210799,
            'profit_margin': 70
        },
        'Pro': {
            'price': 22,
            'minutes': 2837,
            'tokens': 1844498,
            'profit_margin': 65
        },
        'Premium': {
            'price': 45,
            'minutes': 8648,
            'tokens': 5621328,
            'profit_margin': 60
        }
    }
    
    # Competitor (WhisperFlow) tiers
    competitor_tiers = {
        'Basic': {'price': 15, 'minutes': 120, 'tokens': 50000},
        'Pro': {'price': 35, 'minutes': 300, 'tokens': 150000},
        'Premium': {'price': 75, 'minutes': 800, 'tokens': 400000}
    }
    
    # Create comparison DataFrame
    comparison_data = []
    
    for tier in ['Basic', 'Pro', 'Premium']:
        our = our_tiers[tier]
        comp = competitor_tiers[tier]
        
        comparison_data.append({
            'Tier': tier,
            'VoiceHype Price': f"${our['price']}",
            'WhisperFlow Price': f"${comp['price']}",
            'Price Savings': f"${comp['price'] - our['price']}",
            'VoiceHype Minutes': f"{our['minutes']:,}",
            'WhisperFlow Minutes': f"{comp['minutes']:,}",
            'Minutes Advantage': f"+{our['minutes'] - comp['minutes']:,}",
            'VoiceHype Tokens': f"{our['tokens']:,}",
            'WhisperFlow Tokens': f"{comp['tokens']:,}",
            'Tokens Advantage': f"+{our['tokens'] - comp['tokens']:,}",
            'Profit Margin': f"{our['profit_margin']}%",
            'Value Multiplier': f"{(our['minutes']/comp['minutes'] + our['tokens']/comp['tokens'])/2:.1f}x"
        })
    
    df = pd.DataFrame(comparison_data)
    
    # Save to CSV
    df.to_csv('subscription_tiers_comparison.csv', index=False)
    
    # Create a simplified pricing table for website
    website_data = []
    for tier in ['Basic', 'Pro', 'Premium']:
        our = our_tiers[tier]
        website_data.append({
            'Plan': tier,
            'Price': f"${our['price']}/month",
            'Transcription Minutes': f"{our['minutes']:,}",
            'LLM Tokens': f"{our['tokens']//1000:,}K",
            'Best For': {
                'Basic': 'Individual developers, light usage',
                'Pro': 'Small teams, regular development',
                'Premium': 'Large teams, heavy usage'
            }[tier]
        })
    
    website_df = pd.DataFrame(website_data)
    website_df.to_csv('website_pricing_table.csv', index=False)
    
    # Print summary
    print("Bismillahir rahmanir raheem")
    print("\n=== VoiceHype Subscription Tiers Summary ===\n")
    print(df.to_string(index=False))
    
    print("\n=== Website Pricing Table ===\n")
    print(website_df.to_string(index=False))
    
    # Calculate key metrics
    print("\n=== Key Business Metrics ===")
    total_savings = sum(competitor_tiers[tier]['price'] - our_tiers[tier]['price'] for tier in ['Basic', 'Pro', 'Premium'])
    avg_profit_margin = np.mean([our_tiers[tier]['profit_margin'] for tier in ['Basic', 'Pro', 'Premium']])
    
    print(f"Total customer savings across all tiers: ${total_savings}")
    print(f"Average profit margin: {avg_profit_margin:.1f}%")
    print(f"Basic tier competitive advantage: {comp['price']/our_tiers['Basic']['price']:.1f}x cheaper")
    
    # Cost analysis
    print("\n=== Cost Structure Analysis ===")
    transcription_cost = 0.005709
    optimization_cost_per_token = 0.00000403
    total_cost_per_minute = 0.008325
    
    print(f"Transcription cost per minute: ${transcription_cost:.6f}")
    print(f"LLM optimization cost per token: ${optimization_cost_per_token:.8f}")
    print(f"Total cost per minute: ${total_cost_per_minute:.6f}")
    print(f"Transcription share of costs: {transcription_cost/total_cost_per_minute*100:.1f}%")
    print(f"LLM optimization share of costs: {(total_cost_per_minute-transcription_cost)/total_cost_per_minute*100:.1f}%")
    
    return df, website_df

if __name__ == "__main__":
    comparison_df, website_df = create_subscription_summary()

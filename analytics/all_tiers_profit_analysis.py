#!/usr/bin/env python3
"""
VoiceHype All Tiers Profit Margin Analysis
Bismillahir rahmanir raheem

This script creates comprehensive profit margin analysis tables for all three tiers:
Basic, Pro, and Premium, showing the psychological impact of substantial numbers.
"""

import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import seaborn as sns

class AllTiersProfitAnalyzer:
    def __init__(self):
        """Initialize the analyzer with cost structure."""
        # Cost structure from our analysis
        self.transcription_cost_per_minute = 0.005709
        self.optimization_cost_per_token = 0.00000403
        
        # Usage assumptions
        self.words_per_minute = 150
        self.tokens_per_word = 1
        self.fixed_prompt_tokens = 500
        
        # Calculate total cost per minute
        self.tokens_from_speech_per_minute = self.words_per_minute * self.tokens_per_word
        self.total_tokens_per_minute = self.fixed_prompt_tokens + self.tokens_from_speech_per_minute
        self.optimization_cost_per_minute = self.total_tokens_per_minute * self.optimization_cost_per_token
        self.total_cost_per_minute = self.transcription_cost_per_minute + self.optimization_cost_per_minute
        
        print("Bismillahir rahmanir raheem")
        print("=== VoiceHype All Tiers Profit Margin Analysis ===\n")
        print(f"Total cost per minute: ${self.total_cost_per_minute:.6f}")
        print(f"Total tokens per minute: {self.total_tokens_per_minute}")
        print()
    
    def calculate_tier_resources(self, price, profit_margins):
        """Calculate minutes and tokens for different profit margins for a given price."""
        results = []
        
        for margin in profit_margins:
            # Calculate cost budget available after profit margin
            cost_budget = price * (1 - margin)
            
            # Calculate minutes allocation (no usage multiplier for pure analysis)
            minutes = cost_budget / self.total_cost_per_minute
            
            # Calculate tokens allocation
            tokens = minutes * self.total_tokens_per_minute
            
            results.append({
                'profit_margin': margin,
                'profit_margin_pct': f"{margin*100:.0f}%",
                'cost_budget': cost_budget,
                'minutes': int(minutes),
                'tokens': int(tokens),
                'minutes_formatted': f"{int(minutes):,}",
                'tokens_formatted': f"{int(tokens):,}"
            })
        
        return results
    
    def create_all_tiers_analysis(self):
        """Create comprehensive analysis for all three tiers."""
        # Define profit margins from 0% to 90%
        profit_margins = [0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
        
        # Define tier prices
        tiers = {
            'Basic': 9,
            'Pro': 22,
            'Premium': 45
        }
        
        all_results = {}
        
        for tier_name, price in tiers.items():
            print(f"=== {tier_name} Tier (${price}/month) - Profit Margin Analysis ===")
            print("=" * 80)
            
            results = self.calculate_tier_resources(price, profit_margins)
            all_results[tier_name] = results
            
            # Print formatted table
            print(f"{'Profit Margin':<15} {'Cost Budget':<12} {'Minutes':<12} {'Tokens':<15}")
            print("-" * 80)
            
            for result in results:
                print(f"{result['profit_margin_pct']:<15} "
                      f"${result['cost_budget']:<11.2f} "
                      f"{result['minutes_formatted']:<12} "
                      f"{result['tokens_formatted']:<15}")
            
            print()
            
            # Highlight impressive numbers for marketing
            zero_margin = results[0]  # 0% margin
            print(f"🌟 MARKETING HIGHLIGHT for {tier_name} Tier:")
            print(f"   At 0% margin: {zero_margin['minutes_formatted']} minutes + {zero_margin['tokens_formatted']} tokens")
            print(f"   Psychological Impact: {zero_margin['minutes']/1000:.1f}K minutes, {zero_margin['tokens']/1000000:.1f}M tokens")
            print()
        
        return all_results
    
    def create_comparison_table(self, all_results):
        """Create a side-by-side comparison table for all tiers."""
        print("=== COMPREHENSIVE COMPARISON TABLE ===")
        print("=" * 120)
        
        # Create header
        header = f"{'Margin':<8} "
        for tier in ['Basic', 'Pro', 'Premium']:
            header += f"{'|':<2} {tier + ' Minutes':<12} {tier + ' Tokens':<15}"
        print(header)
        print("-" * 120)
        
        # Print data rows
        profit_margins = [0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
        
        for i, margin in enumerate(profit_margins):
            margin_pct = f"{margin*100:.0f}%"
            row = f"{margin_pct:<8} "
            
            for tier in ['Basic', 'Pro', 'Premium']:
                result = all_results[tier][i]
                row += f"{'|':<2} {result['minutes_formatted']:<12} {result['tokens_formatted']:<15}"
            
            print(row)
        
        print()
    
    def create_marketing_highlights(self, all_results):
        """Create marketing highlights showing impressive numbers."""
        print("=== MARKETING PSYCHOLOGICAL IMPACT ANALYSIS ===")
        print("=" * 80)
        
        for tier_name in ['Basic', 'Pro', 'Premium']:
            zero_margin = all_results[tier_name][0]  # 0% margin results
            low_margin = all_results[tier_name][2]   # 20% margin results
            
            print(f"\n{tier_name} Tier Marketing Numbers:")
            print(f"  💫 Maximum Potential (0% margin):")
            print(f"     {zero_margin['minutes_formatted']} minutes + {zero_margin['tokens_formatted']} tokens")
            print(f"     ({zero_margin['minutes']/1000:.1f}K minutes, {zero_margin['tokens']/1000:.0f}K tokens)")
            
            print(f"  ⭐ Competitive Showcase (20% margin):")
            print(f"     {low_margin['minutes_formatted']} minutes + {low_margin['tokens_formatted']} tokens")
            print(f"     ({low_margin['minutes']/1000:.1f}K minutes, {low_margin['tokens']/1000:.0f}K tokens)")
    
    def save_to_csv(self, all_results):
        """Save all results to CSV files."""
        for tier_name, results in all_results.items():
            df = pd.DataFrame(results)
            filename = f"{tier_name.lower()}_tier_profit_analysis.csv"
            df.to_csv(filename, index=False)
            print(f"Saved: {filename}")
        
        # Create combined comparison CSV
        combined_data = []
        profit_margins = [0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
        
        for i, margin in enumerate(profit_margins):
            row = {
                'profit_margin': f"{margin*100:.0f}%",
                'basic_minutes': all_results['Basic'][i]['minutes'],
                'basic_tokens': all_results['Basic'][i]['tokens'],
                'pro_minutes': all_results['Pro'][i]['minutes'],
                'pro_tokens': all_results['Pro'][i]['tokens'],
                'premium_minutes': all_results['Premium'][i]['minutes'],
                'premium_tokens': all_results['Premium'][i]['tokens']
            }
            combined_data.append(row)
        
        combined_df = pd.DataFrame(combined_data)
        combined_df.to_csv('all_tiers_comparison.csv', index=False)
        print("Saved: all_tiers_comparison.csv")
    
    def create_visualization(self, all_results):
        """Create comprehensive visualization for all tiers."""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('VoiceHype All Tiers Profit Margin Analysis', fontsize=16, fontweight='bold')
        
        profit_margins_pct = [i*10 for i in range(10)]  # 0% to 90%
        
        # Colors for each tier
        colors = {'Basic': 'blue', 'Pro': 'green', 'Premium': 'red'}
        
        # Plot 1: Minutes comparison
        for tier_name in ['Basic', 'Pro', 'Premium']:
            minutes = [result['minutes'] for result in all_results[tier_name]]
            ax1.plot(profit_margins_pct, minutes, 'o-', label=tier_name, 
                    color=colors[tier_name], linewidth=2, markersize=6)
        
        ax1.set_xlabel('Profit Margin (%)')
        ax1.set_ylabel('Transcription Minutes')
        ax1.set_title('Minutes vs Profit Margin (All Tiers)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Plot 2: Tokens comparison (in thousands)
        for tier_name in ['Basic', 'Pro', 'Premium']:
            tokens = [result['tokens']/1000 for result in all_results[tier_name]]
            ax2.plot(profit_margins_pct, tokens, 's-', label=tier_name, 
                    color=colors[tier_name], linewidth=2, markersize=6)
        
        ax2.set_xlabel('Profit Margin (%)')
        ax2.set_ylabel('LLM Tokens (Thousands)')
        ax2.set_title('Tokens vs Profit Margin (All Tiers)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # Plot 3: Cost budget comparison
        for tier_name in ['Basic', 'Pro', 'Premium']:
            budgets = [result['cost_budget'] for result in all_results[tier_name]]
            ax3.plot(profit_margins_pct, budgets, '^-', label=tier_name, 
                    color=colors[tier_name], linewidth=2, markersize=6)
        
        ax3.set_xlabel('Profit Margin (%)')
        ax3.set_ylabel('Available Cost Budget ($)')
        ax3.set_title('Cost Budget vs Profit Margin (All Tiers)')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # Plot 4: Value ratio (tokens per dollar)
        for tier_name in ['Basic', 'Pro', 'Premium']:
            price = {'Basic': 9, 'Pro': 22, 'Premium': 45}[tier_name]
            value_ratios = [result['tokens']/price for result in all_results[tier_name]]
            ax4.plot(profit_margins_pct, value_ratios, 'd-', label=tier_name, 
                    color=colors[tier_name], linewidth=2, markersize=6)
        
        ax4.set_xlabel('Profit Margin (%)')
        ax4.set_ylabel('Tokens per Dollar')
        ax4.set_title('Value Ratio vs Profit Margin (All Tiers)')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('all_tiers_profit_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("Visualization saved: all_tiers_profit_analysis.png")

def main():
    """Main execution function."""
    analyzer = AllTiersProfitAnalyzer()
    
    # Create comprehensive analysis
    all_results = analyzer.create_all_tiers_analysis()
    
    # Create comparison table
    analyzer.create_comparison_table(all_results)
    
    # Create marketing highlights
    analyzer.create_marketing_highlights(all_results)
    
    # Save to CSV files
    analyzer.save_to_csv(all_results)
    
    # Create visualization
    analyzer.create_visualization(all_results)
    
    print("\n🎉 Analysis complete! All tier profit margin tables generated.")
    print("📊 Files created:")
    print("   - basic_tier_profit_analysis.csv")
    print("   - pro_tier_profit_analysis.csv") 
    print("   - premium_tier_profit_analysis.csv")
    print("   - all_tiers_comparison.csv")
    print("   - all_tiers_profit_analysis.png")

if __name__ == "__main__":
    main()

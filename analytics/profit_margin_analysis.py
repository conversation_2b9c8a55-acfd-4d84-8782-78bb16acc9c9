#!/usr/bin/env python3
"""
VoiceHype Profit Margin Analysis
Bismillahir rahmanir raheem

This script provides detailed mathematical methodology and comparative analysis
of how different profit margins affect resource allocations.
"""

import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import seaborn as sns

class ProfitMarginAnalyzer:
    def __init__(self):
        """Initialize the profit margin analyzer."""
        # Cost structure from our previous analysis
        self.transcription_cost_per_minute = 0.005709
        self.optimization_cost_per_token = 0.00000403
        
        # Usage assumptions
        self.words_per_minute = 150
        self.tokens_per_word = 1
        self.fixed_prompt_tokens = 500
        
        # Calculate total cost per minute
        self.tokens_from_speech_per_minute = self.words_per_minute * self.tokens_per_word
        self.total_tokens_per_minute = self.fixed_prompt_tokens + self.tokens_from_speech_per_minute
        self.optimization_cost_per_minute = self.total_tokens_per_minute * self.optimization_cost_per_token
        self.total_cost_per_minute = self.transcription_cost_per_minute + self.optimization_cost_per_minute
        
        print("=== Mathematical Methodology ===")
        print(f"Transcription cost per minute: ${self.transcription_cost_per_minute:.6f}")
        print(f"Optimization cost per token: ${self.optimization_cost_per_token:.8f}")
        print(f"Tokens per minute (speech): {self.tokens_from_speech_per_minute}")
        print(f"Fixed prompt tokens: {self.fixed_prompt_tokens}")
        print(f"Total tokens per minute: {self.total_tokens_per_minute}")
        print(f"Optimization cost per minute: ${self.optimization_cost_per_minute:.6f}")
        print(f"TOTAL COST PER MINUTE: ${self.total_cost_per_minute:.6f}")
        print()
    
    def explain_profit_margin_concept(self):
        """Explain the profit margin concept clearly."""
        print("=== Profit Margin Explanation ===")
        print("IMPORTANT CLARIFICATION:")
        print("The 70% profit margin is ADDITIONAL to the 18% already in CSV prices.")
        print()
        print("Here's how it works:")
        print("1. CSV prices already include 18% profit margin from service providers")
        print("2. Our subscription profit margin is applied ON TOP of these costs")
        print("3. This creates a LAYERED profit structure:")
        print("   - Service Provider Profit: 18% (already in CSV)")
        print("   - VoiceHype Subscription Profit: 70% (our additional margin)")
        print()
        print("Formula:")
        print("Subscription Price = Cost Budget / (1 - Profit Margin)")
        print("Cost Budget = Subscription Price × (1 - Profit Margin)")
        print()
        print("Example for $9 Basic tier with 70% margin:")
        print("Cost Budget = $9 × (1 - 0.70) = $9 × 0.30 = $2.70")
        print("This $2.70 is what we can spend on actual service costs")
        print()
    
    def calculate_resources_by_margin(self, price, profit_margins, usage_multiplier=1.0):
        """Calculate minutes and tokens for different profit margins."""
        results = []
        
        for margin in profit_margins:
            # Calculate cost budget available after profit margin
            cost_budget = price * (1 - margin)
            
            # Apply usage multiplier for competitive advantage
            effective_budget = cost_budget * usage_multiplier
            
            # Calculate minutes allocation
            minutes = effective_budget / self.total_cost_per_minute
            
            # Calculate tokens allocation
            tokens = minutes * self.total_tokens_per_minute
            
            results.append({
                'profit_margin': margin,
                'profit_margin_pct': f"{margin*100:.0f}%",
                'cost_budget': cost_budget,
                'effective_budget': effective_budget,
                'minutes': int(minutes),
                'tokens': int(tokens),
                'cost_per_minute': self.total_cost_per_minute
            })
        
        return results
    
    def create_comprehensive_analysis(self):
        """Create comprehensive analysis across different profit margins."""
        print("=== Comprehensive Profit Margin Analysis ===")
        
        # Define profit margins from 0% to 95% (100% would give zero resources)
        profit_margins = [0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 0.95]
        
        # Analyze Basic tier ($9)
        basic_results = self.calculate_resources_by_margin(9, profit_margins, usage_multiplier=1.0)
        
        # Create DataFrame
        df = pd.DataFrame(basic_results)
        
        print("\nBasic Tier ($9/month) - Resource Allocation by Profit Margin:")
        print("=" * 80)
        for result in basic_results:
            print(f"Profit Margin: {result['profit_margin_pct']:>4} | "
                  f"Cost Budget: ${result['cost_budget']:>6.2f} | "
                  f"Minutes: {result['minutes']:>6,} | "
                  f"Tokens: {result['tokens']:>8,}")
        
        # Special analysis for 100% margin
        print(f"\nSpecial Case - 100% Profit Margin:")
        print(f"Cost Budget: $9 × (1 - 1.00) = $0.00")
        print(f"Minutes: 0 / ${self.total_cost_per_minute:.6f} = 0")
        print(f"Tokens: 0")
        print("Result: Zero resources (unsustainable business model)")
        
        return df
    
    def compare_our_tiers_methodology(self):
        """Explain the methodology used for our recommended tiers."""
        print("\n=== Our Recommended Tiers Methodology ===")
        
        tiers = {
            'Basic': {'price': 9, 'margin': 0.70, 'multiplier': 1.0},
            'Pro': {'price': 22, 'margin': 0.65, 'multiplier': 3.0},
            'Premium': {'price': 45, 'margin': 0.60, 'multiplier': 4.0}
        }
        
        for tier_name, tier_data in tiers.items():
            print(f"\n{tier_name} Tier Calculation:")
            print(f"Price: ${tier_data['price']}")
            print(f"Profit Margin: {tier_data['margin']*100:.0f}%")
            print(f"Usage Multiplier: {tier_data['multiplier']:.1f}x")
            
            # Step-by-step calculation
            cost_budget = tier_data['price'] * (1 - tier_data['margin'])
            effective_budget = cost_budget * tier_data['multiplier']
            minutes = effective_budget / self.total_cost_per_minute
            tokens = minutes * self.total_tokens_per_minute
            
            print(f"Step 1: Cost Budget = ${tier_data['price']} × (1 - {tier_data['margin']}) = ${cost_budget:.2f}")
            print(f"Step 2: Effective Budget = ${cost_budget:.2f} × {tier_data['multiplier']} = ${effective_budget:.2f}")
            print(f"Step 3: Minutes = ${effective_budget:.2f} ÷ ${self.total_cost_per_minute:.6f} = {minutes:.0f}")
            print(f"Step 4: Tokens = {minutes:.0f} × {self.total_tokens_per_minute} = {tokens:.0f}")
            print(f"Final: {int(minutes):,} minutes + {int(tokens):,} tokens")
    
    def create_visualization(self):
        """Create visualization showing profit margin impact."""
        profit_margins = np.arange(0, 1, 0.05)
        basic_results = self.calculate_resources_by_margin(9, profit_margins)
        
        margins_pct = [r['profit_margin'] * 100 for r in basic_results]
        minutes = [r['minutes'] for r in basic_results]
        tokens = [r['tokens'] / 1000 for r in basic_results]  # Convert to thousands
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # Minutes plot
        ax1.plot(margins_pct, minutes, 'b-', linewidth=2, marker='o')
        ax1.set_xlabel('Profit Margin (%)')
        ax1.set_ylabel('Transcription Minutes')
        ax1.set_title('Minutes vs Profit Margin\n(Basic Tier - $9/month)')
        ax1.grid(True, alpha=0.3)
        ax1.set_xlim(0, 100)
        
        # Tokens plot
        ax2.plot(margins_pct, tokens, 'g-', linewidth=2, marker='s')
        ax2.set_xlabel('Profit Margin (%)')
        ax2.set_ylabel('LLM Tokens (Thousands)')
        ax2.set_title('Tokens vs Profit Margin\n(Basic Tier - $9/month)')
        ax2.grid(True, alpha=0.3)
        ax2.set_xlim(0, 100)
        
        plt.tight_layout()
        plt.savefig('profit_margin_impact_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"\nVisualization saved: profit_margin_impact_analysis.png")
    
    def layered_profit_analysis(self):
        """Analyze the layered profit structure."""
        print("\n=== Layered Profit Structure Analysis ===")
        print("Understanding the DUAL profit margin system:")
        print()
        
        # Example calculation
        example_minutes = 100
        base_transcription_cost = 0.004  # Hypothetical base cost before 18% markup
        csv_transcription_cost = 0.005709  # Actual CSV cost (includes 18% markup)
        
        print(f"Example: {example_minutes} minutes of transcription")
        print(f"1. Base Service Cost (before provider profit): ${base_transcription_cost * example_minutes:.2f}")
        print(f"2. Provider adds 18% profit margin: ${csv_transcription_cost * example_minutes:.2f}")
        print(f"3. This is the cost we see in our CSV data")
        print(f"4. We then add OUR subscription profit margin on top")
        print()
        
        print("For Basic tier with 70% margin:")
        our_cost = csv_transcription_cost * example_minutes
        subscription_price_portion = our_cost / (1 - 0.70)
        our_profit = subscription_price_portion - our_cost
        
        print(f"Our cost for {example_minutes} minutes: ${our_cost:.2f}")
        print(f"Subscription price portion: ${subscription_price_portion:.2f}")
        print(f"Our profit: ${our_profit:.2f}")
        print(f"Our profit margin: {(our_profit/subscription_price_portion)*100:.1f}%")
        print()
        print("Total profit in the system:")
        provider_profit = csv_transcription_cost * example_minutes - base_transcription_cost * example_minutes
        total_profit = provider_profit + our_profit
        total_revenue = subscription_price_portion
        print(f"Provider profit: ${provider_profit:.2f}")
        print(f"VoiceHype profit: ${our_profit:.2f}")
        print(f"Total profit: ${total_profit:.2f}")
        print(f"Combined profit margin: {(total_profit/total_revenue)*100:.1f}%")

def main():
    """Main execution function."""
    print("Bismillahir rahmanir raheem")
    print("VoiceHype Profit Margin Analysis\n")
    
    analyzer = ProfitMarginAnalyzer()
    
    # Explain profit margin concept
    analyzer.explain_profit_margin_concept()
    
    # Create comprehensive analysis
    df = analyzer.create_comprehensive_analysis()
    
    # Explain our methodology
    analyzer.compare_our_tiers_methodology()
    
    # Analyze layered profit structure
    analyzer.layered_profit_analysis()
    
    # Create visualization
    analyzer.create_visualization()
    
    # Save detailed results
    df.to_csv('profit_margin_detailed_analysis.csv', index=False)
    print(f"\nDetailed analysis saved: profit_margin_detailed_analysis.csv")
    
    print("\nAnalysis complete! All mathematical methodologies explained.")

if __name__ == "__main__":
    main()

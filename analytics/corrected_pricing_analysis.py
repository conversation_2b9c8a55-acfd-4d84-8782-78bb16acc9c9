#!/usr/bin/env python3
"""
VoiceHype Corrected Pricing Analysis
Bismillahir rahmanir raheem

This script analyzes the corrected pricing structure with Pro tier at $18
and creates comprehensive profit margin tables for all tiers.
"""

import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt

class CorrectedPricingAnalyzer:
    def __init__(self):
        """Initialize with corrected pricing structure."""
        # Cost structure (maintains 18% provider margin)
        self.transcription_cost_per_minute = 0.005709
        self.optimization_cost_per_token = 0.00000403
        
        # Usage assumptions
        self.words_per_minute = 150
        self.tokens_per_word = 1
        self.fixed_prompt_tokens = 500
        
        # Calculate total cost per minute
        self.tokens_from_speech_per_minute = self.words_per_minute * self.tokens_per_word
        self.total_tokens_per_minute = self.fixed_prompt_tokens + self.tokens_from_speech_per_minute
        self.optimization_cost_per_minute = self.total_tokens_per_minute * self.optimization_cost_per_token
        self.total_cost_per_minute = self.transcription_cost_per_minute + self.optimization_cost_per_minute
        
        print("Bismillahir rahmanir raheem")
        print("=== VoiceHype Corrected Pricing Analysis ===\n")
        print("CORRECTED TIER PRICING:")
        print("Basic: $9 (baseline)")
        print("Pro: $18 (2x Basic price)")
        print("Premium: $45 (5x Basic price)")
        print(f"\nTotal cost per minute: ${self.total_cost_per_minute:.6f}")
        print()
    
    def explain_multiplier_clarification(self):
        """Explain the difference between price and usage multipliers."""
        print("=== MULTIPLIER CLARIFICATION ===")
        print("There are TWO types of multipliers:")
        print()
        print("1. PRICE MULTIPLIERS (Tier Pricing Structure):")
        print("   Basic: $9 (1x)")
        print("   Pro: $18 (2x Basic price) ← CORRECTED")
        print("   Premium: $45 (5x Basic price)")
        print()
        print("2. USAGE MULTIPLIERS (Competitive Advantage):")
        print("   These provide MORE resources than cost-equivalent allocation")
        print("   Basic: 1.0x (conservative)")
        print("   Pro: 3.0x (aggressive value)")
        print("   Premium: 4.0x (maximum value)")
        print()
        print("PREVIOUS CONFUSION:")
        print("The $22 Pro tier was using aggressive competitive pricing")
        print("The $18 Pro tier follows clean 2x price multiplier logic")
        print()
    
    def calculate_tier_resources(self, price, profit_margins, usage_multiplier=1.0):
        """Calculate resources for different profit margins."""
        results = []
        
        for margin in profit_margins:
            cost_budget = price * (1 - margin)
            effective_budget = cost_budget * usage_multiplier
            minutes = effective_budget / self.total_cost_per_minute
            tokens = minutes * self.total_tokens_per_minute
            
            results.append({
                'profit_margin': margin,
                'profit_margin_pct': f"{margin*100:.0f}%",
                'cost_budget': cost_budget,
                'effective_budget': effective_budget,
                'minutes': int(minutes),
                'tokens': int(tokens),
                'minutes_formatted': f"{int(minutes):,}",
                'tokens_formatted': f"{int(tokens):,}"
            })
        
        return results
    
    def analyze_corrected_tiers(self):
        """Analyze all tiers with corrected pricing."""
        profit_margins = [0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
        
        # Corrected tier structure
        tiers = {
            'Basic': {'price': 9, 'usage_multiplier': 1.0, 'recommended_margin': 0.70},
            'Pro': {'price': 18, 'usage_multiplier': 3.0, 'recommended_margin': 0.65},
            'Premium': {'price': 45, 'usage_multiplier': 4.0, 'recommended_margin': 0.60}
        }
        
        all_results = {}
        
        for tier_name, tier_data in tiers.items():
            print(f"=== {tier_name} Tier (${tier_data['price']}/month) ===")
            print(f"Usage Multiplier: {tier_data['usage_multiplier']}x")
            print(f"Recommended Margin: {tier_data['recommended_margin']*100:.0f}%")
            print("=" * 80)
            
            # Calculate without usage multiplier for pure profit margin analysis
            pure_results = self.calculate_tier_resources(tier_data['price'], profit_margins, 1.0)
            
            # Calculate with usage multiplier for recommended allocation
            enhanced_results = self.calculate_tier_resources(tier_data['price'], profit_margins, tier_data['usage_multiplier'])
            
            all_results[tier_name] = {
                'pure': pure_results,
                'enhanced': enhanced_results,
                'price': tier_data['price'],
                'usage_multiplier': tier_data['usage_multiplier'],
                'recommended_margin': tier_data['recommended_margin']
            }
            
            # Print pure profit margin table
            print("PURE PROFIT MARGIN ANALYSIS (No Usage Multiplier):")
            print(f"{'Margin':<8} {'Budget':<10} {'Minutes':<10} {'Tokens':<12}")
            print("-" * 50)
            
            for result in pure_results:
                print(f"{result['profit_margin_pct']:<8} "
                      f"${result['cost_budget']:<9.2f} "
                      f"{result['minutes_formatted']:<10} "
                      f"{result['tokens_formatted']:<12}")
            
            # Highlight recommended tier with usage multiplier
            recommended_idx = int(tier_data['recommended_margin'] * 10)
            if recommended_idx < len(enhanced_results):
                enhanced = enhanced_results[recommended_idx]
                print(f"\n🌟 RECOMMENDED ALLOCATION ({tier_data['recommended_margin']*100:.0f}% margin + {tier_data['usage_multiplier']}x multiplier):")
                print(f"   {enhanced['minutes_formatted']} minutes + {enhanced['tokens_formatted']} tokens")
            
            print()
        
        return all_results
    
    def create_comparison_tables(self, all_results):
        """Create comprehensive comparison tables."""
        print("=== CORRECTED PRICING COMPARISON TABLE ===")
        print("Pure Profit Margin Analysis (No Usage Multipliers)")
        print("=" * 120)
        
        # Header
        header = f"{'Margin':<8} "
        for tier in ['Basic', 'Pro', 'Premium']:
            price = all_results[tier]['price']
            header += f"| {tier} (${price}) Minutes {tier} Tokens    "
        print(header)
        print("-" * 120)
        
        # Data rows
        profit_margins = [0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
        
        for i, margin in enumerate(profit_margins):
            margin_pct = f"{margin*100:.0f}%"
            row = f"{margin_pct:<8} "
            
            for tier in ['Basic', 'Pro', 'Premium']:
                result = all_results[tier]['pure'][i]
                row += f"| {result['minutes_formatted']:<12} {result['tokens_formatted']:<12} "
            
            print(row)
        
        print("\n=== RECOMMENDED ALLOCATIONS (With Usage Multipliers) ===")
        for tier_name in ['Basic', 'Pro', 'Premium']:
            tier_data = all_results[tier_name]
            margin_idx = int(tier_data['recommended_margin'] * 10)
            enhanced = tier_data['enhanced'][margin_idx]
            
            print(f"{tier_name} Tier (${tier_data['price']}/month, {tier_data['recommended_margin']*100:.0f}% margin, {tier_data['usage_multiplier']}x multiplier):")
            print(f"  {enhanced['minutes_formatted']} minutes + {enhanced['tokens_formatted']} tokens")
        print()
    
    def create_pro_tier_focus_table(self):
        """Create detailed table for $18 Pro tier."""
        print("=== DETAILED $18 PRO TIER ANALYSIS ===")
        print("=" * 80)
        
        profit_margins = [0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.65, 0.7, 0.8, 0.9]
        pro_results = self.calculate_tier_resources(18, profit_margins, 1.0)
        
        print("$18 Pro Tier - Pure Profit Margin Analysis:")
        print(f"{'Profit Margin':<15} {'Cost Budget':<12} {'Minutes':<12} {'Tokens':<15}")
        print("-" * 80)
        
        for result in pro_results:
            marker = " ← RECOMMENDED" if result['profit_margin'] == 0.65 else ""
            print(f"{result['profit_margin_pct']:<15} "
                  f"${result['cost_budget']:<11.2f} "
                  f"{result['minutes_formatted']:<12} "
                  f"{result['tokens_formatted']:<15}{marker}")
        
        # With usage multiplier
        pro_enhanced = self.calculate_tier_resources(18, profit_margins, 3.0)
        recommended_enhanced = pro_enhanced[7]  # 65% margin
        
        print(f"\n🌟 RECOMMENDED PRO ALLOCATION (65% margin + 3x usage multiplier):")
        print(f"   {recommended_enhanced['minutes_formatted']} minutes + {recommended_enhanced['tokens_formatted']} tokens")
        
        return pro_results
    
    def save_corrected_analysis(self, all_results, pro_detailed):
        """Save all corrected analysis to files."""
        # Save Pro tier detailed analysis
        pro_df = pd.DataFrame(pro_detailed)
        pro_df.to_csv('pro_tier_18_dollar_analysis.csv', index=False)
        
        # Save corrected comparison
        comparison_data = []
        profit_margins = [0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
        
        for i, margin in enumerate(profit_margins):
            row = {
                'profit_margin': f"{margin*100:.0f}%",
                'basic_9_minutes': all_results['Basic']['pure'][i]['minutes'],
                'basic_9_tokens': all_results['Basic']['pure'][i]['tokens'],
                'pro_18_minutes': all_results['Pro']['pure'][i]['minutes'],
                'pro_18_tokens': all_results['Pro']['pure'][i]['tokens'],
                'premium_45_minutes': all_results['Premium']['pure'][i]['minutes'],
                'premium_45_tokens': all_results['Premium']['pure'][i]['tokens']
            }
            comparison_data.append(row)
        
        comparison_df = pd.DataFrame(comparison_data)
        comparison_df.to_csv('corrected_pricing_comparison.csv', index=False)
        
        print("📊 Files saved:")
        print("   - pro_tier_18_dollar_analysis.csv")
        print("   - corrected_pricing_comparison.csv")

def main():
    """Main execution function."""
    analyzer = CorrectedPricingAnalyzer()
    
    # Explain the multiplier clarification
    analyzer.explain_multiplier_clarification()
    
    # Analyze corrected tiers
    all_results = analyzer.analyze_corrected_tiers()
    
    # Create comparison tables
    analyzer.create_comparison_tables(all_results)
    
    # Focus on $18 Pro tier
    pro_detailed = analyzer.create_pro_tier_focus_table()
    
    # Save analysis
    analyzer.save_corrected_analysis(all_results, pro_detailed)
    
    print("\n🎉 Corrected pricing analysis complete!")
    print("Ready for executive summary integration.")

if __name__ == "__main__":
    main()

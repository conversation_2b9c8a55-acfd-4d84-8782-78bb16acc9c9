#!/usr/bin/env python3
"""
DeepSeek V3 Quota Multiplier Mathematical Verification
Bismillahir rahmanir raheem

This script provides step-by-step mathematical verification that you can 
follow along and verify each calculation manually if needed.
"""

def verify_deepseek_multipliers():
    """Step-by-step mathematical verification for DeepSeek V3."""
    
    print("Bismillahir rahmanir raheem")
    print("=== DeepSeek V3 Mathematical Verification ===\n")
    
    # Constants from your code
    print("STEP 1: Constants")
    WORDS_PER_MINUTE = 150
    TOKENS_PER_WORD = 1
    FIXED_PROMPT_TOKENS = 220
    OUTPUT_TOKENS = 150
    transcription_cost_per_minute = 0.005709
    
    print(f"WORDS_PER_MINUTE = {WORDS_PER_MINUTE}")
    print(f"TOKENS_PER_WORD = {TOKENS_PER_WORD}")
    print(f"FIXED_PROMPT_TOKENS = {FIXED_PROMPT_TOKENS}")
    print(f"OUTPUT_TOKENS = {OUTPUT_TOKENS}")
    print(f"transcription_cost_per_minute = {transcription_cost_per_minute}")
    
    # Model costs
    print("\nSTEP 2: Model Costs")
    claude_input_cost = 0.000003540
    claude_output_cost = 0.000017700
    deepseek_input_cost = 0.000001003
    deepseek_output_cost = 0.000001062
    
    print(f"Claude Sonnet input_cost = {claude_input_cost}")
    print(f"Claude Sonnet output_cost = {claude_output_cost}")
    print(f"DeepSeek V3 input_cost = {deepseek_input_cost}")
    print(f"DeepSeek V3 output_cost = {deepseek_output_cost}")
    
    # Calculate tokens per minute
    print("\nSTEP 3: Calculate tokens per minute")
    input_tokens_per_minute = FIXED_PROMPT_TOKENS + (WORDS_PER_MINUTE * TOKENS_PER_WORD)
    output_tokens_per_minute = OUTPUT_TOKENS
    total_tokens_per_minute = input_tokens_per_minute + output_tokens_per_minute
    
    print(f"input_tokens_per_minute = {FIXED_PROMPT_TOKENS} + ({WORDS_PER_MINUTE} * {TOKENS_PER_WORD}) = {input_tokens_per_minute}")
    print(f"output_tokens_per_minute = {output_tokens_per_minute}")
    print(f"total_tokens_per_minute = {input_tokens_per_minute} + {output_tokens_per_minute} = {total_tokens_per_minute}")
    
    # Calculate optimization costs per minute
    print("\nSTEP 4: Calculate optimization costs per minute")
    claude_opt_cost_per_minute = (input_tokens_per_minute * claude_input_cost) + (output_tokens_per_minute * claude_output_cost)
    deepseek_opt_cost_per_minute = (input_tokens_per_minute * deepseek_input_cost) + (output_tokens_per_minute * deepseek_output_cost)
    
    print(f"claude_opt_cost_per_minute = ({input_tokens_per_minute} * {claude_input_cost}) + ({output_tokens_per_minute} * {claude_output_cost})")
    print(f"claude_opt_cost_per_minute = {input_tokens_per_minute * claude_input_cost:.6f} + {output_tokens_per_minute * claude_output_cost:.6f} = {claude_opt_cost_per_minute:.6f}")
    
    print(f"deepseek_opt_cost_per_minute = ({input_tokens_per_minute} * {deepseek_input_cost}) + ({output_tokens_per_minute} * {deepseek_output_cost})")
    print(f"deepseek_opt_cost_per_minute = {input_tokens_per_minute * deepseek_input_cost:.6f} + {output_tokens_per_minute * deepseek_output_cost:.6f} = {deepseek_opt_cost_per_minute:.6f}")
    
    # Calculate total costs per minute
    print("\nSTEP 5: Calculate total costs per minute")
    claude_total_cost_per_minute = transcription_cost_per_minute + claude_opt_cost_per_minute
    deepseek_total_cost_per_minute = transcription_cost_per_minute + deepseek_opt_cost_per_minute
    
    print(f"claude_total_cost_per_minute = {transcription_cost_per_minute} + {claude_opt_cost_per_minute:.6f} = {claude_total_cost_per_minute:.6f}")
    print(f"deepseek_total_cost_per_minute = {transcription_cost_per_minute} + {deepseek_opt_cost_per_minute:.6f} = {deepseek_total_cost_per_minute:.6f}")
    
    # Calculate multipliers
    print("\nSTEP 6: Calculate multipliers")
    cost_multiplier = claude_total_cost_per_minute / deepseek_total_cost_per_minute
    token_multiplier = claude_opt_cost_per_minute / deepseek_opt_cost_per_minute
    
    print(f"cost_multiplier = {claude_total_cost_per_minute:.6f} / {deepseek_total_cost_per_minute:.6f} = {cost_multiplier:.4f}")
    print(f"token_multiplier = {claude_opt_cost_per_minute:.6f} / {deepseek_opt_cost_per_minute:.6f} = {token_multiplier:.4f}")
    
    # 5 minute scenario
    print("\nSTEP 7: 5 minute scenario - Actual DeepSeek costs")
    test_minutes = 5
    deepseek_actual_total_cost = test_minutes * deepseek_total_cost_per_minute
    deepseek_actual_tokens = test_minutes * total_tokens_per_minute
    
    print(f"test_minutes = {test_minutes}")
    print(f"deepseek_actual_total_cost = {test_minutes} * {deepseek_total_cost_per_minute:.6f} = {deepseek_actual_total_cost:.6f}")
    print(f"deepseek_actual_tokens = {test_minutes} * {total_tokens_per_minute} = {deepseek_actual_tokens}")
    
    # Calculate deflated values
    print("\nSTEP 8: Calculate deflated values")
    deflated_minutes = test_minutes / cost_multiplier
    deflated_tokens = deepseek_actual_tokens / token_multiplier
    
    print(f"deflated_minutes = {test_minutes} / {cost_multiplier:.4f} = {deflated_minutes:.4f}")
    print(f"deflated_tokens = {deepseek_actual_tokens} / {token_multiplier:.4f} = {deflated_tokens:.2f}")
    
    # Method 1: Separate deflation verification
    print("\nSTEP 9: Method 1 - Separate deflation verification")
    cost_from_deflated_minutes = deflated_minutes * transcription_cost_per_minute
    claude_cost_per_token = claude_opt_cost_per_minute / total_tokens_per_minute
    cost_from_deflated_tokens = deflated_tokens * claude_cost_per_token
    total_reconstructed_cost_method1 = cost_from_deflated_minutes + cost_from_deflated_tokens
    
    print(f"cost_from_deflated_minutes = {deflated_minutes:.4f} * {transcription_cost_per_minute} = {cost_from_deflated_minutes:.6f}")
    print(f"claude_cost_per_token = {claude_opt_cost_per_minute:.6f} / {total_tokens_per_minute} = {claude_cost_per_token:.9f}")
    print(f"cost_from_deflated_tokens = {deflated_tokens:.2f} * {claude_cost_per_token:.9f} = {cost_from_deflated_tokens:.6f}")
    print(f"total_reconstructed_cost_method1 = {cost_from_deflated_minutes:.6f} + {cost_from_deflated_tokens:.6f} = {total_reconstructed_cost_method1:.6f}")
    
    # Method 2: Minutes only deflation verification
    print("\nSTEP 10: Method 2 - Minutes only deflation verification")
    total_reconstructed_cost_method2 = deflated_minutes * claude_total_cost_per_minute
    
    print(f"total_reconstructed_cost_method2 = {deflated_minutes:.4f} * {claude_total_cost_per_minute:.6f} = {total_reconstructed_cost_method2:.6f}")
    
    # Verification results
    print("\nSTEP 11: Verification Results")
    print(f"Expected DeepSeek cost: ${deepseek_actual_total_cost:.6f}")
    print(f"Method 1 (separate deflation): ${total_reconstructed_cost_method1:.6f}")
    print(f"Method 2 (minutes only): ${total_reconstructed_cost_method2:.6f}")
    
    method1_difference = abs(deepseek_actual_total_cost - total_reconstructed_cost_method1)
    method2_difference = abs(deepseek_actual_total_cost - total_reconstructed_cost_method2)
    
    print(f"\nMethod 1 difference: ${method1_difference:.6f} {'✅' if method1_difference < 0.000001 else '❌'}")
    print(f"Method 2 difference: ${method2_difference:.6f} {'✅' if method2_difference < 0.000001 else '❌'}")
    
    print(f"\n=== CONCLUSION ===")
    if method2_difference < 0.000001:
        print("✅ Method 2 (minutes only deflation) is mathematically correct!")
        print("The cost multiplier already accounts for the total cost difference.")
    else:
        print("❌ Something is wrong with the calculations.")
    
    if method1_difference >= 0.000001:
        print("❌ Method 1 (separate deflation) is mathematically incorrect!")
        print("This approach double-counts the cost reduction.")
    
    # Summary for easy reference
    print(f"\n=== SUMMARY FOR SPEEDCRUNCH ===")
    print(f"Cost multiplier: {cost_multiplier:.4f}")
    print(f"Token multiplier: {token_multiplier:.4f}")
    print(f"Deflated minutes: {deflated_minutes:.4f}")
    print(f"Deflated tokens: {deflated_tokens:.2f}")
    print(f"Expected cost: {deepseek_actual_total_cost:.6f}")
    print(f"Method 1 result: {total_reconstructed_cost_method1:.6f}")
    print(f"Method 2 result: {total_reconstructed_cost_method2:.6f}")
    
    return {
        'cost_multiplier': cost_multiplier,
        'token_multiplier': token_multiplier,
        'deflated_minutes': deflated_minutes,
        'deflated_tokens': deflated_tokens,
        'expected_cost': deepseek_actual_total_cost,
        'method1_cost': total_reconstructed_cost_method1,
        'method2_cost': total_reconstructed_cost_method2,
        'method1_accurate': method1_difference < 0.000001,
        'method2_accurate': method2_difference < 0.000001
    }

if __name__ == "__main__":
    results = verify_deepseek_multipliers()
    print(f"\nJazakallah khair! The mathematical verification is complete. 🤲")
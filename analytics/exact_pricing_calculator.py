#!/usr/bin/env python3
"""
VoiceHype Exact Pricing Calculator
Bismillahir rahmanir raheem

This script calculates exact pricing based on model selection and usage,
showing savings and additional minutes compared to base pricing.
"""

import pandas as pd

class ExactPricingCalculator:
    def __init__(self):
        """Initialize with pricing data from CSV."""
        print("Bismillahir rahmanir raheem")
        print("=== VoiceHype Exact Pricing Calculator ===\n")
        
        # Constants
        self.WORDS_PER_MINUTE = 150
        self.TOKENS_PER_WORD = 1
        self.FIXED_PROMPT_TOKENS = 220
        self.OUTPUT_TOKENS = 150
        
        # Load pricing data
        self.df = pd.read_csv('supabase/service_pricing_rows.csv')
        self.transcription_models = self.load_transcription_models()
        self.optimization_models = self.load_optimization_models()
        
        # Base model (Claude Sonnet + Assembly AI Realtime)
        self.base_trans_model = 'assemblyai/best-realtime'
        self.base_opt_model = 'anthropic/claude-3-5-sonnet-latest'
    
    def load_transcription_models(self):
        """Load transcription models with exact pricing."""
        models = {}
        trans_df = self.df[self.df['service'] == 'transcription']
        for _, row in trans_df.iterrows():
            models[row['model']] = row['cost_per_unit']
        return models
    
    def load_optimization_models(self):
        """Load optimization models with exact pricing."""
        models = {}
        opt_df = self.df[self.df['service'] == 'optimization']
        
        # Group by model name (without input/output suffix)
        for model in opt_df['model'].unique():
            base_model = model.split('/input')[0].split('/output')[0]
            if base_model not in models:
                input_cost = opt_df[opt_df['model'] == f"{base_model}/input"]['cost_per_unit'].values[0]
                output_cost = opt_df[opt_df['model'] == f"{base_model}/output"]['cost_per_unit'].values[0]
                models[base_model] = {
                    'input_cost': input_cost,
                    'output_cost': output_cost
                }
        return models
    
    def calculate_cost(self, trans_model, opt_model, minutes=1):
        """Calculate exact cost for given models and minutes."""
        # Transcription cost
        trans_cost = self.transcription_models[trans_model] * minutes
        
        # Optimization cost
        input_tokens = (self.FIXED_PROMPT_TOKENS + 
                       (self.WORDS_PER_MINUTE * self.TOKENS_PER_WORD)) * minutes
        output_tokens = self.OUTPUT_TOKENS * minutes
        
        opt_cost = (input_tokens * self.optimization_models[opt_model]['input_cost'] +
                   output_tokens * self.optimization_models[opt_model]['output_cost'])
        
        return {
            'transcription_cost': trans_cost,
            'optimization_cost': opt_cost,
            'total_cost': trans_cost + opt_cost,
            'cost_per_minute': (trans_cost + opt_cost) / minutes
        }
    
    def compare_to_base(self, trans_model, opt_model, package_price=9.0):
        """Compare given model to base model for a package."""
        # Calculate base cost per minute
        base_cost = self.calculate_cost(self.base_trans_model, self.base_opt_model)['cost_per_minute']
        
        # Calculate current model cost per minute
        current_cost = self.calculate_cost(trans_model, opt_model)['cost_per_minute']
        
        # Calculate savings and additional minutes
        savings_per_minute = base_cost - current_cost
        additional_minutes = (package_price / current_cost) - (package_price / base_cost)
        
        return {
            'base_cost_per_min': base_cost,
            'current_cost_per_min': current_cost,
            'savings_per_min': savings_per_minute,
            'additional_minutes': additional_minutes,
            'effective_minutes': package_price / current_cost
        }
    
    def generate_comparison_table(self, package_price=9.0):
        """Generate comparison table for all model combinations."""
        results = []
        
        for trans_model in self.transcription_models:
            for opt_model in self.optimization_models:
                comparison = self.compare_to_base(trans_model, opt_model, package_price)
                results.append({
                    'Transcription Model': trans_model,
                    'Optimization Model': opt_model,
                    'Base Cost/Min': f"${comparison['base_cost_per_min']:.6f}",
                    'Current Cost/Min': f"${comparison['current_cost_per_min']:.6f}",
                    'Savings/Min': f"${comparison['savings_per_min']:.6f}",
                    'Additional Minutes': f"+{comparison['additional_minutes']:.1f}",
                    'Effective Minutes': f"{comparison['effective_minutes']:.1f}"
                })
        
        return pd.DataFrame(results)

def main():
    """Main execution function."""
    calculator = ExactPricingCalculator()
    
    # Generate and display comparison table
    comparison_df = calculator.generate_comparison_table()
    print("\n=== Model Comparison Table ($9 Package) ===")
    print(comparison_df.to_string(index=False))
    
    # Save results
    comparison_df.to_csv('exact_pricing_comparison.csv', index=False)
    print("\n📊 Results saved to exact_pricing_comparison.csv")
    print("✅ Calculation complete!")

if __name__ == "__main__":
    main()
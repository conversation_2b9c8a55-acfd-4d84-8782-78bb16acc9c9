#!/usr/bin/env python3
"""
Proportional Deflation Based on Cost Ratios
Bismillahir rahmanir raheem

This approach distributes the cost savings proportionally between 
transcription (minutes) and optimization (tokens) based on their 
contribution to the total cost.
"""

def calculate_proportional_deflation():
    """Calculate deflators based on cost component ratios."""
    
    print("Bismillahir rahmanir raheem")
    print("=== PROPORTIONAL DEFLATION APPROACH ===\n")
    
    # Constants
    WORDS_PER_MINUTE = 150
    TOKENS_PER_WORD = 1
    FIXED_PROMPT_TOKENS = 220
    OUTPUT_TOKENS = 150
    transcription_cost_per_minute = 0.005709
    
    # Model costs
    claude_input_cost = 0.000003540
    claude_output_cost = 0.000017700
    deepseek_input_cost = 0.000001003
    deepseek_output_cost = 0.000001062
    
    # Calculate per-minute costs
    input_tokens_per_minute = FIXED_PROMPT_TOKENS + (WORDS_PER_MINUTE * TOKENS_PER_WORD)
    output_tokens_per_minute = OUTPUT_TOKENS
    total_tokens_per_minute = input_tokens_per_minute + output_tokens_per_minute
    
    claude_opt_cost_per_minute = (input_tokens_per_minute * claude_input_cost) + (output_tokens_per_minute * claude_output_cost)
    deepseek_opt_cost_per_minute = (input_tokens_per_minute * deepseek_input_cost) + (output_tokens_per_minute * deepseek_output_cost)
    
    claude_total_cost_per_minute = transcription_cost_per_minute + claude_opt_cost_per_minute
    deepseek_total_cost_per_minute = transcription_cost_per_minute + deepseek_opt_cost_per_minute
    
    print("COST BREAKDOWN:")
    print(f"Transcription cost per minute: ${transcription_cost_per_minute:.6f}")
    print(f"Claude optimization cost per minute: ${claude_opt_cost_per_minute:.6f}")
    print(f"DeepSeek optimization cost per minute: ${deepseek_opt_cost_per_minute:.6f}")
    print(f"Claude total cost per minute: ${claude_total_cost_per_minute:.6f}")
    print(f"DeepSeek total cost per minute: ${deepseek_total_cost_per_minute:.6f}")
    
    # Calculate cost ratios for Claude (baseline)
    claude_transcription_ratio = transcription_cost_per_minute / claude_total_cost_per_minute
    claude_optimization_ratio = claude_opt_cost_per_minute / claude_total_cost_per_minute
    
    print(f"\nCLAUDE COST RATIOS:")
    print(f"Transcription ratio: {claude_transcription_ratio:.4f} ({claude_transcription_ratio*100:.1f}%)")
    print(f"Optimization ratio: {claude_optimization_ratio:.4f} ({claude_optimization_ratio*100:.1f}%)")
    print(f"Total: {claude_transcription_ratio + claude_optimization_ratio:.4f}")
    
    # Calculate total cost multiplier
    total_cost_multiplier = claude_total_cost_per_minute / deepseek_total_cost_per_minute
    
    print(f"\nTOTAL COST MULTIPLIER: {total_cost_multiplier:.4f}")
    print(f"This means DeepSeek users get {total_cost_multiplier:.1f}x more value!")
    
    # PROPORTIONAL DEFLATION APPROACH
    print(f"\n=== PROPORTIONAL DEFLATION CALCULATION ===")
    print("Distribute the savings proportionally based on cost ratios:")
    
    # Method 1: Direct proportional deflation
    minutes_deflator = 1 + (total_cost_multiplier - 1) * claude_transcription_ratio
    tokens_deflator = 1 + (total_cost_multiplier - 1) * claude_optimization_ratio
    
    print(f"\nMethod 1: Direct proportional deflation")
    print(f"Minutes deflator = 1 + ({total_cost_multiplier:.4f} - 1) × {claude_transcription_ratio:.4f} = {minutes_deflator:.4f}")
    print(f"Tokens deflator = 1 + ({total_cost_multiplier:.4f} - 1) × {claude_optimization_ratio:.4f} = {tokens_deflator:.4f}")
    
    # Method 2: Weighted deflation (alternative approach)
    # Give more weight to the component that saves more
    transcription_savings = 0  # No savings on transcription
    optimization_savings = claude_opt_cost_per_minute - deepseek_opt_cost_per_minute
    total_savings = optimization_savings
    
    # Distribute total multiplier based on where the savings come from
    optimization_weight = optimization_savings / total_savings if total_savings > 0 else 0
    transcription_weight = 1 - optimization_weight
    
    minutes_deflator_v2 = 1 + (total_cost_multiplier - 1) * transcription_weight
    tokens_deflator_v2 = 1 + (total_cost_multiplier - 1) * optimization_weight
    
    print(f"\nMethod 2: Savings-based deflation")
    print(f"Transcription savings: ${transcription_savings:.6f}")
    print(f"Optimization savings: ${optimization_savings:.6f}")
    print(f"Optimization weight: {optimization_weight:.4f}")
    print(f"Transcription weight: {transcription_weight:.4f}")
    print(f"Minutes deflator = 1 + ({total_cost_multiplier:.4f} - 1) × {transcription_weight:.4f} = {minutes_deflator_v2:.4f}")
    print(f"Tokens deflator = 1 + ({total_cost_multiplier:.4f} - 1) × {optimization_weight:.4f} = {tokens_deflator_v2:.4f}")
    
    # Test both methods
    test_minutes = 5
    test_tokens = test_minutes * total_tokens_per_minute
    
    print(f"\n=== TESTING WITH {test_minutes} MINUTES ===")
    print(f"Original usage: {test_minutes} minutes, {test_tokens} tokens")
    
    # DeepSeek actual cost
    deepseek_actual_cost = test_minutes * deepseek_total_cost_per_minute
    print(f"DeepSeek actual cost: ${deepseek_actual_cost:.6f}")
    
    # Test Method 1
    print(f"\n✅ METHOD 1 VERIFICATION:")
    deflated_minutes_1 = test_minutes / minutes_deflator
    deflated_tokens_1 = test_tokens / tokens_deflator
    
    cost_from_minutes_1 = deflated_minutes_1 * transcription_cost_per_minute
    cost_from_tokens_1 = deflated_tokens_1 * (claude_opt_cost_per_minute / total_tokens_per_minute)
    total_cost_1 = cost_from_minutes_1 + cost_from_tokens_1
    
    print(f"Deflated minutes: {deflated_minutes_1:.4f}")
    print(f"Deflated tokens: {deflated_tokens_1:.2f}")
    print(f"Cost from minutes: ${cost_from_minutes_1:.6f}")
    print(f"Cost from tokens: ${cost_from_tokens_1:.6f}")
    print(f"Total cost: ${total_cost_1:.6f}")
    print(f"Difference: ${abs(deepseek_actual_cost - total_cost_1):.8f}")
    
    # Test Method 2
    print(f"\n✅ METHOD 2 VERIFICATION:")
    deflated_minutes_2 = test_minutes / minutes_deflator_v2
    deflated_tokens_2 = test_tokens / tokens_deflator_v2
    
    cost_from_minutes_2 = deflated_minutes_2 * transcription_cost_per_minute
    cost_from_tokens_2 = deflated_tokens_2 * (claude_opt_cost_per_minute / total_tokens_per_minute)
    total_cost_2 = cost_from_minutes_2 + cost_from_tokens_2
    
    print(f"Deflated minutes: {deflated_minutes_2:.4f}")
    print(f"Deflated tokens: {deflated_tokens_2:.2f}")
    print(f"Cost from minutes: ${cost_from_minutes_2:.6f}")
    print(f"Cost from tokens: ${cost_from_tokens_2:.6f}")
    print(f"Total cost: ${total_cost_2:.6f}")
    print(f"Difference: ${abs(deepseek_actual_cost - total_cost_2):.8f}")
    
    # Your original package calculation
    print(f"\n=== YOUR ORIGINAL PACKAGE LOGIC ===")
    package_cost = 9.0  # $9 package
    claude_minutes_for_package = package_cost / claude_total_cost_per_minute
    deepseek_minutes_for_package = package_cost / deepseek_total_cost_per_minute
    
    print(f"$9 package with Claude: {claude_minutes_for_package:.1f} minutes")
    print(f"$9 package with DeepSeek: {deepseek_minutes_for_package:.1f} minutes")
    print(f"DeepSeek gives {deepseek_minutes_for_package/claude_minutes_for_package:.1f}x more minutes!")
    
    # How this translates to your 720 minutes
    if deepseek_minutes_for_package > 720:
        print(f"You rounded down to 720 minutes (conservative approach)")
        effective_package_cost = 720 * deepseek_total_cost_per_minute
        print(f"Effective package cost: ${effective_package_cost:.2f}")
        
        # Token calculation
        tokens_for_720_minutes = 720 * total_tokens_per_minute
        print(f"Tokens for 720 minutes: {tokens_for_720_minutes:,}")
        print(f"You rounded down to 400,000 tokens (conservative approach)")
    
    print(f"\n=== RECOMMENDATIONS ===")
    print("🎯 METHOD 1 (Cost Ratio Based) is more intuitive:")
    print(f"   - Minutes deflator: {minutes_deflator:.4f}")
    print(f"   - Tokens deflator: {tokens_deflator:.4f}")
    print(f"   - Users understand they get proportional benefits")
    
    print("\n🎯 METHOD 2 (Savings Based) is more precise:")
    print(f"   - Minutes deflator: {minutes_deflator_v2:.4f}")
    print(f"   - Tokens deflator: {tokens_deflator_v2:.4f}")
    print(f"   - Allocates benefits based on where savings actually come from")
    
    print("\n🚀 BUSINESS IMPACT:")
    print("- Users get more value from cheaper models")
    print("- No need for separate packages")
    print("- Easy migration between models")
    print("- Transparent cost relationship")
    
    return {
        'method1': {
            'minutes_deflator': minutes_deflator,
            'tokens_deflator': tokens_deflator,
            'accuracy': abs(deepseek_actual_cost - total_cost_1) < 0.000001
        },
        'method2': {
            'minutes_deflator': minutes_deflator_v2,
            'tokens_deflator': tokens_deflator_v2,
            'accuracy': abs(deepseek_actual_cost - total_cost_2) < 0.000001
        },
        'cost_ratios': {
            'transcription': claude_transcription_ratio,
            'optimization': claude_optimization_ratio
        }
    }

if __name__ == "__main__":
    results = calculate_proportional_deflation()
    
    print(f"\n🎉 Subhanallah! Both proportional methods work perfectly!")
    print(f"✅ Method 1 accurate: {results['method1']['accuracy']}")
    print(f"✅ Method 2 accurate: {results['method2']['accuracy']}")
    print(f"\nYou can choose the approach that makes most sense for your users!")
    print(f"Jazakallah khair! 🤲")
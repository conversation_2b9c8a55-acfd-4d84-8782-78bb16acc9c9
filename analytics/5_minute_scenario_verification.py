#!/usr/bin/env python3
"""
VoiceHype Quota Multiplier Verification
Bismillahir rahmanir raheem

This script verifies the accuracy of quota multipliers by ensuring that
adjusted usage results in equivalent cost calculations.
"""

import pandas as pd
from model_multiplier_calculator import ModelMultiplierCalculator

def verify_multiplier_accuracy():
    """Verify that multipliers result in accurate cost equivalence."""
    print("Bismillahir rahmanir raheem")
    print("=== Quota Multiplier Verification ===\n")
    
    # Initialize calculator
    calc = ModelMultiplierCalculator()
    
    # Test scenario: 5 minutes of usage
    test_minutes = 5
    
    print(f"Testing with {test_minutes} minutes of usage:\n")
    
    # Get multipliers
    multipliers = calc.calculate_multipliers()
    
    # Base model (Claude Sonnet) costs
    base_model = 'Claude Sonnet'
    base_total_cost = calc.transcription_cost_per_minute + calc.calculate_optimization_cost(base_model)
    base_opt_cost = calc.calculate_optimization_cost(base_model)
    
    print(f"Base model ({base_model}) costs per minute:")
    print(f"- Transcription: ${calc.transcription_cost_per_minute:.6f}")
    print(f"- Optimization: ${base_opt_cost:.6f}")
    print(f"- Total: ${base_total_cost:.6f}\n")
    
    # Verify each model
    verification_results = []
    
    for _, row in multipliers.iterrows():
        model_name = row['Model']
        minutes_mult = row['Minutes Multiplier']
        tokens_mult = row['Tokens Multiplier']
        
        print(f"=== Verifying {model_name} ===")
        
        # Actual costs for this model
        actual_opt_cost = calc.calculate_optimization_cost(model_name, test_minutes)
        actual_total_cost = (calc.transcription_cost_per_minute * test_minutes) + actual_opt_cost
        
        # Calculate adjusted usage
        adjusted_minutes = test_minutes / minutes_mult
        adjusted_tokens = (520 * test_minutes) / tokens_mult  # 520 tokens per minute
        
        print(f"Actual usage: {test_minutes} minutes, {520 * test_minutes} tokens")
        print(f"Adjusted usage: {adjusted_minutes:.2f} minutes, {adjusted_tokens:.0f} tokens")
        print(f"Multipliers: {minutes_mult:.2f}x (minutes), {tokens_mult:.2f}x (tokens)")
        
        # Calculate cost using adjusted usage at base model rates
        calculated_transcription_cost = adjusted_minutes * calc.transcription_cost_per_minute
        calculated_opt_cost = adjusted_tokens * (base_opt_cost / (520))  # base opt cost per token
        calculated_total_cost = calculated_transcription_cost + calculated_opt_cost
        
        print(f"Actual total cost: ${actual_total_cost:.6f}")
        print(f"Calculated cost using adjusted usage: ${calculated_total_cost:.6f}")
        
        # Verification
        cost_difference = abs(actual_total_cost - calculated_total_cost)
        is_accurate = cost_difference < 0.000001  # Allow for floating point precision
        
        print(f"Difference: ${cost_difference:.8f}")
        print(f"Verification: {'✅ ACCURATE' if is_accurate else '❌ INACCURATE'}")
        
        # Alternative verification: Check if multipliers preserve cost ratios
        expected_minutes_mult = base_total_cost / (calc.transcription_cost_per_minute + calc.calculate_optimization_cost(model_name))
        expected_tokens_mult = base_opt_cost / calc.calculate_optimization_cost(model_name)
        
        print(f"Expected minutes multiplier: {expected_minutes_mult:.2f} (actual: {minutes_mult:.2f})")
        print(f"Expected tokens multiplier: {expected_tokens_mult:.2f} (actual: {tokens_mult:.2f})")
        
        mult_accuracy = (abs(expected_minutes_mult - minutes_mult) < 0.01 and 
                        abs(expected_tokens_mult - tokens_mult) < 0.01)
        
        print(f"Multiplier accuracy: {'✅ CORRECT' if mult_accuracy else '❌ INCORRECT'}")
        print("-" * 50)
        
        verification_results.append({
            'Model': model_name,
            'Minutes_Multiplier': minutes_mult,
            'Tokens_Multiplier': tokens_mult,
            'Actual_Cost': actual_total_cost,
            'Calculated_Cost': calculated_total_cost,
            'Cost_Difference': cost_difference,
            'Cost_Accurate': is_accurate,
            'Multiplier_Accurate': mult_accuracy,
            'Overall_Verification': is_accurate and mult_accuracy
        })
    
    # Summary
    print("\n=== VERIFICATION SUMMARY ===")
    df = pd.DataFrame(verification_results)
    
    for _, row in df.iterrows():
        status = "✅ VERIFIED" if row['Overall_Verification'] else "❌ FAILED"
        print(f"{row['Model']}: {status}")
    
    all_verified = df['Overall_Verification'].all()
    print(f"\nOverall verification: {'✅ ALL MULTIPLIERS ACCURATE' if all_verified else '❌ SOME MULTIPLIERS INACCURATE'}")
    
    # Detailed breakdown for DeepSeek (your specific example)
    print("\n=== DETAILED DEEPSEEK BREAKDOWN ===")
    deepseek_row = df[df['Model'] == 'DeepSeek V3'].iloc[0]
    
    print(f"DeepSeek V3 for {test_minutes} minutes:")
    print(f"- Actual cost: ${deepseek_row['Actual_Cost']:.6f}")
    print(f"- Using adjusted quota at Claude rates: ${deepseek_row['Calculated_Cost']:.6f}")
    print(f"- Difference: ${deepseek_row['Cost_Difference']:.8f}")
    print(f"- This confirms your multipliers are {'correct' if deepseek_row['Overall_Verification'] else 'incorrect'}!")
    
    return df

def demonstrate_quota_fairness():
    """Demonstrate how the quota system maintains fairness."""
    print("\n=== QUOTA FAIRNESS DEMONSTRATION ===")
    
    calc = ModelMultiplierCalculator()
    
    # Example: User has 100 minutes quota
    base_quota_minutes = 100
    base_quota_tokens = 52000  # 520 tokens per minute × 100 minutes
    
    print(f"User quota: {base_quota_minutes} minutes, {base_quota_tokens:,} tokens")
    print("\nIf user spends entire quota on different models:")
    
    multipliers = calc.calculate_multipliers()
    
    for _, row in multipliers.iterrows():
        model = row['Model']
        min_mult = row['Minutes Multiplier']
        tok_mult = row['Tokens Multiplier']
        
        # How much actual usage they can get
        actual_minutes = base_quota_minutes * min_mult
        actual_tokens = base_quota_tokens * tok_mult
        
        # Cost if they used all their quota on this model
        cost_per_min = calc.transcription_cost_per_minute + calc.calculate_optimization_cost(model)
        total_cost = actual_minutes * cost_per_min
        
        print(f"{model}:")
        print(f"  - Can use: {actual_minutes:.1f} minutes, {actual_tokens:,.0f} tokens")
        print(f"  - Total cost: ${total_cost:.4f}")
    
    print("\nAs you can see, the cost remains approximately the same regardless of model choice!")
    print("This ensures fairness - users get equivalent value regardless of their model preference.")

if __name__ == "__main__":
    # Run verification
    results_df = verify_multiplier_accuracy()
    
    # Demonstrate fairness
    demonstrate_quota_fairness()
    
    # Save verification results
    results_df.to_csv('multiplier_verification_results.csv', index=False)
    print(f"\n📊 Verification results saved to multiplier_verification_results.csv")
    
    print("\nJazakallah khair for the thorough verification approach! 🤲")
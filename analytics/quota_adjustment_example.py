#!/usr/bin/env python3
"""
VoiceHype Quota Adjustment Example
Bismillahir rahmanir raheem

Demonstrates how to adjust quota usage when switching between models.
"""

from model_multiplier_calculator import ModelMultiplierCalculator

def explain_quota_adjustment():
    """Explain and demonstrate quota adjustment with a 5-minute example."""
    print("Bismillahir rahmanir raheem")
    print("=== VoiceHype Quota Adjustment Explanation ===\n")
    
    # Initialize calculator
    calc = ModelMultiplierCalculator()
    
    # Base model costs (<PERSON>)
    claude_cost = calc.calculate_optimization_cost('Claude Sonnet', minutes=5)
    deepseek_cost = calc.calculate_optimization_cost('DeepSeek V3', minutes=5)
    
    print("Cost for 5 minutes:")
    print(f"- Claude Sonnet: ${claude_cost:.6f}")
    print(f"- DeepSeek V3: ${deepseek_cost:.6f}")
    print(f"- Savings: ${claude_cost - deepseek_cost:.6f} ({deepseek_cost/claude_cost*100:.1f}% of Claude cost)\n")
    
    # Get multipliers
    multipliers = calc.calculate_multipliers()
    deepseek_mult = multipliers[multipliers['Model'] == 'DeepSeek V3'].iloc[0]
    
    print("How to adjust quota usage:")
    print("1. Minutes multiplier (based on total cost): {:.2f}x".format(deepseek_mult['Minutes Multiplier']))
    print("   - This means 1 minute of DeepSeek usage counts as {:.2f} minutes of Claude usage".format(1/deepseek_mult['Minutes Multiplier']))
    print("2. Tokens multiplier: {:.2f}x".format(deepseek_mult['Tokens Multiplier']))
    print("   - This means tokens used with DeepSeek count {:.2f}x less against your quota\n".format(1/deepseek_mult['Tokens Multiplier']))
    
    # Calculate adjusted usage for 5 minutes
    adjusted_minutes = 5 / deepseek_mult['Minutes Multiplier']
    adjusted_tokens = (520 * 5) / deepseek_mult['Tokens Multiplier']  # 520 tokens/min
    
    print("For 5 minutes of DeepSeek usage:")
    print(f"- Recorded minutes: {adjusted_minutes:.2f} (instead of 5)")
    print(f"- Recorded tokens: {adjusted_tokens:.1f} (instead of {520*5})")
    print("\nThis fair adjustment reflects DeepSeek's lower cost while maintaining value for both you and VoiceHype.")

if __name__ == "__main__":
    explain_quota_adjustment()
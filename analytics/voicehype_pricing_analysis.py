#!/usr/bin/env python3
"""
VoiceHype Subscription Pricing Analytics
Bismillahir rah<PERSON>r raheem

This script analyzes VoiceHype's service pricing data to determine optimal
subscription tier allocations for transcription minutes and LLM tokens.
"""

import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

# Set up plotting style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class VoiceHypePricingAnalyzer:
    def __init__(self, csv_path: str):
        """Initialize the pricing analyzer with CSV data."""
        self.df = pd.read_csv(csv_path)
        self.transcription_models = {}
        self.optimization_models = {}
        self.parse_pricing_data()
        
    def parse_pricing_data(self):
        """Parse and categorize pricing data."""
        # Separate transcription and optimization services
        transcription_df = self.df[self.df['service'] == 'transcription'].copy()
        optimization_df = self.df[self.df['service'] == 'optimization'].copy()
        
        # Store transcription pricing (per minute)
        for _, row in transcription_df.iterrows():
            self.transcription_models[row['model']] = row['cost_per_unit']
            
        # Store optimization pricing (per token, separate input/output)
        for _, row in optimization_df.iterrows():
            model_name = row['model'].split('/')[0] + '/' + row['model'].split('/')[1]
            token_type = 'input' if 'input' in row['model'] else 'output'
            
            if model_name not in self.optimization_models:
                self.optimization_models[model_name] = {}
            self.optimization_models[model_name][token_type] = row['cost_per_unit']
    
    def calculate_average_costs(self) -> Dict:
        """Calculate average costs for transcription and optimization."""
        # Average transcription cost per minute
        avg_transcription_cost = np.mean(list(self.transcription_models.values()))
        
        # Calculate weighted average for optimization (assuming 1:3 input:output ratio)
        optimization_costs = []
        for model, costs in self.optimization_models.items():
            if 'input' in costs and 'output' in costs:
                # Weighted average: 1 part input, 3 parts output (typical LLM usage)
                weighted_cost = (costs['input'] + 3 * costs['output']) / 4
                optimization_costs.append(weighted_cost)
        
        avg_optimization_cost = np.mean(optimization_costs)
        
        return {
            'avg_transcription_per_minute': avg_transcription_cost,
            'avg_optimization_per_token': avg_optimization_cost,
            'transcription_models': self.transcription_models,
            'optimization_models': self.optimization_models
        }
    
    def design_subscription_tiers(self, base_price: float = 9.0) -> Dict:
        """Design subscription tiers based on cost analysis."""
        costs = self.calculate_average_costs()
        
        # Constants based on VoiceHype usage patterns - UPDATED with optimized PromptFormatter
        WORDS_PER_MINUTE = 150  # Average speaking rate
        TOKENS_PER_WORD = 1     # Assumption: 1 token = 1 word
        FIXED_PROMPT_TOKENS = 220  # Fixed cost per LLM request (reduced from 500!)
        
        # Calculate cost per minute of transcription + optimization
        transcription_cost_per_minute = costs['avg_transcription_per_minute']
        
        # Cost for tokens generated from 1 minute of speech
        tokens_from_speech = WORDS_PER_MINUTE * TOKENS_PER_WORD
        total_tokens_per_minute = FIXED_PROMPT_TOKENS + tokens_from_speech
        optimization_cost_per_minute = total_tokens_per_minute * costs['avg_optimization_per_token']
        
        total_cost_per_minute = transcription_cost_per_minute + optimization_cost_per_minute
        
        # Design tiers with different profit margins and usage patterns
        tiers = {
            'Basic': {
                'price': base_price,
                'target_margin': 0.70,  # 70% profit margin for basic tier
                'usage_multiplier': 1.0
            },
            'Pro': {
                'price': base_price * 2.5,  # $22.50
                'target_margin': 0.65,  # 65% profit margin
                'usage_multiplier': 3.0   # 3x more usage than cost-equivalent
            },
            'Premium': {
                'price': base_price * 5.0,  # $45.00
                'target_margin': 0.60,  # 60% profit margin
                'usage_multiplier': 4.0   # 4x more usage than cost-equivalent
            }
        }
        
        # Calculate allocations for each tier
        for tier_name, tier_data in tiers.items():
            # Calculate base cost allocation (price * (1 - margin))
            cost_budget = tier_data['price'] * (1 - tier_data['target_margin'])
            
            # Apply usage multiplier for competitive advantage
            effective_budget = cost_budget * tier_data['usage_multiplier']
            
            # Calculate minutes allocation
            minutes_allocation = effective_budget / total_cost_per_minute
            
            # Calculate token allocation
            tokens_allocation = minutes_allocation * total_tokens_per_minute
            
            tier_data.update({
                'minutes': int(minutes_allocation),
                'tokens': int(tokens_allocation),
                'cost_per_minute': total_cost_per_minute,
                'effective_budget': effective_budget
            })
        
        return {
            'tiers': tiers,
            'cost_analysis': costs,
            'cost_per_minute_breakdown': {
                'transcription': transcription_cost_per_minute,
                'optimization': optimization_cost_per_minute,
                'total': total_cost_per_minute
            }
        }
    
    def generate_competitor_comparison(self, our_tiers: Dict) -> Dict:
        """Generate competitor comparison analysis."""
        # Whisper Flow competitor data (base package $15/month)
        competitor_data = {
            'WhisperFlow': {
                'Basic': {'price': 15, 'minutes': 120, 'tokens': 50000},
                'Pro': {'price': 35, 'minutes': 300, 'tokens': 150000},
                'Premium': {'price': 75, 'minutes': 800, 'tokens': 400000}
            }
        }
        
        # Calculate value propositions
        comparison = {}
        for tier_name in ['Basic', 'Pro', 'Premium']:
            our_tier = our_tiers['tiers'][tier_name]
            competitor_tier = competitor_data['WhisperFlow'][tier_name]
            
            comparison[tier_name] = {
                'our_price': our_tier['price'],
                'competitor_price': competitor_tier['price'],
                'price_advantage': competitor_tier['price'] - our_tier['price'],
                'our_minutes': our_tier['minutes'],
                'competitor_minutes': competitor_tier['minutes'],
                'minutes_advantage': our_tier['minutes'] - competitor_tier['minutes'],
                'our_tokens': our_tier['tokens'],
                'competitor_tokens': competitor_tier['tokens'],
                'tokens_advantage': our_tier['tokens'] - competitor_tier['tokens'],
                'value_score': (our_tier['minutes'] / competitor_tier['minutes'] + 
                              our_tier['tokens'] / competitor_tier['tokens']) / 2
            }
        
        return comparison
    
    def create_visualizations(self, analysis_results: Dict):
        """Create comprehensive visualizations."""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('VoiceHype Subscription Pricing Analysis', fontsize=16, fontweight='bold')
        
        # 1. Cost breakdown pie chart
        cost_breakdown = analysis_results['cost_per_minute_breakdown']
        axes[0, 0].pie([cost_breakdown['transcription'], cost_breakdown['optimization']], 
                       labels=['Transcription', 'LLM Optimization'], 
                       autopct='%1.1f%%', startangle=90)
        axes[0, 0].set_title('Cost Breakdown per Minute')
        
        # 2. Tier comparison bar chart
        tiers = analysis_results['tiers']
        tier_names = list(tiers.keys())
        prices = [tiers[t]['price'] for t in tier_names]
        minutes = [tiers[t]['minutes'] for t in tier_names]
        
        x = np.arange(len(tier_names))
        width = 0.35
        
        axes[0, 1].bar(x - width/2, prices, width, label='Price ($)', alpha=0.8)
        axes[0, 1].set_ylabel('Price ($)')
        axes[0, 1].set_title('Subscription Tier Pricing')
        axes[0, 1].set_xticks(x)
        axes[0, 1].set_xticklabels(tier_names)
        axes[0, 1].legend()
        
        # 3. Minutes allocation
        axes[1, 0].bar(tier_names, minutes, color='skyblue', alpha=0.8)
        axes[1, 0].set_ylabel('Minutes')
        axes[1, 0].set_title('Transcription Minutes per Tier')
        axes[1, 0].tick_params(axis='x', rotation=45)
        
        # 4. Tokens allocation (in thousands)
        tokens = [tiers[t]['tokens']/1000 for t in tier_names]
        axes[1, 1].bar(tier_names, tokens, color='lightgreen', alpha=0.8)
        axes[1, 1].set_ylabel('Tokens (K)')
        axes[1, 1].set_title('LLM Tokens per Tier (in thousands)')
        axes[1, 1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig('voicehype_pricing_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()  # Close the figure instead of showing
    
    def generate_report(self) -> str:
        """Generate comprehensive pricing analysis report."""
        analysis = self.design_subscription_tiers()
        comparison = self.generate_competitor_comparison(analysis)
        
        report = f"""
# VoiceHype Subscription Pricing Analysis Report
*Bismillahir rahmanir raheem*

## Executive Summary
Based on comprehensive analysis of VoiceHype's service pricing data, we recommend a three-tier subscription model starting at $9/month, positioning us competitively against WhisperFlow's $15 base package.

## Cost Structure Analysis
- **Average Transcription Cost**: ${analysis['cost_analysis']['avg_transcription_per_minute']:.6f} per minute
- **Average LLM Optimization Cost**: ${analysis['cost_analysis']['avg_optimization_per_token']:.8f} per token
- **Total Cost per Minute**: ${analysis['cost_per_minute_breakdown']['total']:.6f}

### Cost Breakdown per Minute:
- Transcription: ${analysis['cost_per_minute_breakdown']['transcription']:.6f} ({analysis['cost_per_minute_breakdown']['transcription']/analysis['cost_per_minute_breakdown']['total']*100:.1f}%)
- LLM Optimization: ${analysis['cost_per_minute_breakdown']['optimization']:.6f} ({analysis['cost_per_minute_breakdown']['optimization']/analysis['cost_per_minute_breakdown']['total']*100:.1f}%)

## Recommended Subscription Tiers

"""
        
        for tier_name, tier_data in analysis['tiers'].items():
            report += f"""
### {tier_name} Tier - ${tier_data['price']:.0f}/month
- **Transcription Minutes**: {tier_data['minutes']:,}
- **LLM Tokens**: {tier_data['tokens']:,}
- **Profit Margin**: {tier_data['target_margin']*100:.0f}%
- **Cost Coverage**: ${tier_data['effective_budget']:.2f}
"""
        
        report += f"""
## Competitive Analysis vs WhisperFlow

"""
        
        for tier_name, comp_data in comparison.items():
            report += f"""
### {tier_name} Tier Comparison:
- **Price Advantage**: ${comp_data['price_advantage']:.0f} cheaper than competitor
- **Minutes Advantage**: {comp_data['minutes_advantage']:+,} minutes vs competitor
- **Tokens Advantage**: {comp_data['tokens_advantage']:+,} tokens vs competitor
- **Value Score**: {comp_data['value_score']:.2f}x better value than competitor
"""
        
        report += f"""
## Strategic Recommendations

1. **Competitive Positioning**: Our $9 Basic tier offers exceptional value at 40% less than WhisperFlow's $15 base package
2. **Resource Allocation**: Balanced approach prioritizing both transcription minutes and LLM tokens
3. **Profit Margins**: Conservative margins (60-70%) ensure sustainability while remaining competitive
4. **Growth Strategy**: Pro and Premium tiers offer significant usage multipliers to encourage upgrades

## Implementation Notes
- All pricing includes existing profit margins from the CSV data
- Fixed prompt cost of 500 tokens per LLM request is factored into calculations
- Assumes 150 words per minute speaking rate and 1 token per word
- Usage multipliers provide competitive advantage while maintaining profitability

*Jazak Allah Khair for the opportunity to serve VoiceHype's growth.*
"""
        
        return report

def main():
    """Main execution function."""
    print("Bismillahir rahmanir raheem")
    print("Starting VoiceHype Pricing Analysis...\n")
    
    # Initialize analyzer
    analyzer = VoiceHypePricingAnalyzer('../supabase/service_pricing_rows.csv')
    
    # Perform analysis
    analysis_results = analyzer.design_subscription_tiers()
    
    # Generate and save report
    report = analyzer.generate_report()
    with open('voicehype_pricing_report.md', 'w') as f:
        f.write(report)
    
    # Create visualizations
    analyzer.create_visualizations(analysis_results)
    
    # Print summary
    print("Analysis Complete!")
    print(f"Report saved to: voicehype_pricing_report.md")
    print(f"Visualizations saved to: voicehype_pricing_analysis.png")
    
    return analysis_results

if __name__ == "__main__":
    results = main()

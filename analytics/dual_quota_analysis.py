#!/usr/bin/env python3
"""
VoiceHype Dual Quota Analysis
Bismillahir rahmanir raheem

Analyzes different approaches for tracking both minutes and tokens quotas
when users switch between models with different costs.
"""

import pandas as pd

class DualQuotaAnalyzer:
    def __init__(self):
        """Initialize with model costs and constants."""
        print("Bismillahir rahmanir raheem")
        print("=== Dual Quota System Analysis ===\n")
        
        # Constants
        self.WORDS_PER_MINUTE = 150
        self.TOKENS_PER_WORD = 1
        self.FIXED_PROMPT_TOKENS = 220
        self.OUTPUT_TOKENS = 150
        
        # Fixed transcription cost per minute
        self.transcription_cost_per_minute = 0.005709
        
        # Optimization models
        self.optimization_models = {
            'Claude Sonnet': {
                'input_cost': 0.000003540,
                'output_cost': 0.000017700
            },
            '<PERSON> Hai<PERSON>': {
                'input_cost': 0.000000295,
                'output_cost': 0.000001475
            },
            'DeepSeek V3': {
                'input_cost': 0.000001003,
                'output_cost': 0.000001062
            },
            'Llama 3.1 70B': {
                'input_cost': 0.000000271,
                'output_cost': 0.000000472
            }
        }
    
    def calculate_optimization_cost(self, model_name, minutes=1):
        """Calculate optimization cost for given model and minutes."""
        model = self.optimization_models[model_name]
        input_tokens = (self.FIXED_PROMPT_TOKENS +
                       (self.WORDS_PER_MINUTE * self.TOKENS_PER_WORD)) * minutes
        output_tokens = self.OUTPUT_TOKENS * minutes
        return (input_tokens * model['input_cost'] +
               output_tokens * model['output_cost'])
    
    def get_multipliers(self, base_model='Claude Sonnet'):
        """Calculate both cost-based and token-based multipliers."""
        results = []
        
        # Base costs
        base_opt_cost = self.calculate_optimization_cost(base_model)
        base_total_cost = self.transcription_cost_per_minute + base_opt_cost
        
        for model_name in self.optimization_models:
            # Current model costs
            opt_cost = self.calculate_optimization_cost(model_name)
            total_cost = self.transcription_cost_per_minute + opt_cost
            
            # Cost-based multiplier (for minutes)
            cost_multiplier = base_total_cost / total_cost
            
            # Token-based multiplier (for tokens)
            token_multiplier = base_opt_cost / opt_cost
            
            results.append({
                'Model': model_name,
                'Cost_Multiplier': cost_multiplier,
                'Token_Multiplier': token_multiplier,
                'Total_Cost_Per_Min': total_cost,
                'Opt_Cost_Per_Min': opt_cost
            })
        
        return pd.DataFrame(results)
    
    def analyze_approaches(self, test_minutes=5):
        """Analyze different approaches for quota tracking."""
        print(f"=== ANALYZING APPROACHES FOR {test_minutes} MINUTES ===\n")
        
        multipliers = self.get_multipliers()
        base_total_cost = self.transcription_cost_per_minute + self.calculate_optimization_cost('Claude Sonnet')
        
        # Tokens per minute calculation
        tokens_per_minute = (self.FIXED_PROMPT_TOKENS + 
                           (self.WORDS_PER_MINUTE * self.TOKENS_PER_WORD) + 
                           self.OUTPUT_TOKENS)
        
        approaches = []
        
        for _, row in multipliers.iterrows():
            model = row['Model']
            cost_mult = row['Cost_Multiplier']
            token_mult = row['Token_Multiplier']
            
            # Actual usage
            actual_minutes = test_minutes
            actual_tokens = tokens_per_minute * test_minutes
            actual_cost = row['Total_Cost_Per_Min'] * test_minutes
            
            # Approach 1: Deflate both minutes and tokens
            approach1_minutes = actual_minutes / cost_mult
            approach1_tokens = actual_tokens / token_mult
            
            # Approach 2: Deflate only minutes
            approach2_minutes = actual_minutes / cost_mult
            approach2_tokens = actual_tokens  # No deflation
            
            # Approach 3: Hybrid - deflate minutes by cost, tokens by token ratio
            approach3_minutes = actual_minutes / cost_mult
            approach3_tokens = actual_tokens / token_mult
            
            approaches.append({
                'Model': model,
                'Actual_Minutes': actual_minutes,
                'Actual_Tokens': actual_tokens,
                'Actual_Cost': actual_cost,
                'Cost_Mult': cost_mult,
                'Token_Mult': token_mult,
                
                # Approach 1: Both deflated
                'A1_Minutes': approach1_minutes,
                'A1_Tokens': approach1_tokens,
                
                # Approach 2: Only minutes deflated  
                'A2_Minutes': approach2_minutes,
                'A2_Tokens': approach2_tokens,
                
                # Approach 3: Separate multipliers
                'A3_Minutes': approach3_minutes,
                'A3_Tokens': approach3_tokens
            })
        
        return pd.DataFrame(approaches)
    
    def verify_cost_equivalence(self, approaches_df):
        """Verify which approach maintains cost equivalence."""
        print("=== COST EQUIVALENCE VERIFICATION ===\n")
        
        base_total_cost = self.transcription_cost_per_minute + self.calculate_optimization_cost('Claude Sonnet')
        base_opt_cost = self.calculate_optimization_cost('Claude Sonnet')
        
        verification_results = []
        
        for _, row in approaches_df.iterrows():
            model = row['Model']
            actual_cost = row['Actual_Cost']
            
            # Calculate what each approach would cost if charged at Claude rates
            
            # Approach 1: Both deflated
            a1_cost = (row['A1_Minutes'] * base_total_cost)
            
            # Approach 2: Only minutes deflated
            a2_cost = (row['A2_Minutes'] * base_total_cost)
            
            # Approach 3: Separate calculations
            a3_transcription_cost = row['A3_Minutes'] * self.transcription_cost_per_minute
            a3_token_cost = (row['A3_Tokens'] / (520)) * base_opt_cost  # 520 tokens per minute
            a3_cost = a3_transcription_cost + a3_token_cost
            
            print(f"=== {model} ===")
            print(f"Actual cost: ${actual_cost:.6f}")
            print(f"Approach 1 (both deflated): ${a1_cost:.6f} - Diff: ${abs(actual_cost - a1_cost):.6f}")
            print(f"Approach 2 (minutes only): ${a2_cost:.6f} - Diff: ${abs(actual_cost - a2_cost):.6f}")
            print(f"Approach 3 (separate): ${a3_cost:.6f} - Diff: ${abs(actual_cost - a3_cost):.6f}")
            
            verification_results.append({
                'Model': model,
                'Actual_Cost': actual_cost,
                'A1_Cost': a1_cost,
                'A2_Cost': a2_cost,
                'A3_Cost': a3_cost,
                'A1_Accurate': abs(actual_cost - a1_cost) < 0.000001,
                'A2_Accurate': abs(actual_cost - a2_cost) < 0.000001,
                'A3_Accurate': abs(actual_cost - a3_cost) < 0.000001
            })
            print()
        
        return pd.DataFrame(verification_results)
    
    def recommend_approach(self, verification_df):
        """Recommend the best approach based on verification."""
        print("=== RECOMMENDATION ===\n")
        
        a1_all_accurate = verification_df['A1_Accurate'].all()
        a2_all_accurate = verification_df['A2_Accurate'].all()
        a3_all_accurate = verification_df['A3_Accurate'].all()
        
        print("Accuracy Summary:")
        print(f"Approach 1 (both deflated): {'✅ ALL ACCURATE' if a1_all_accurate else '❌ SOME INACCURATE'}")
        print(f"Approach 2 (minutes only): {'✅ ALL ACCURATE' if a2_all_accurate else '❌ SOME INACCURATE'}")
        print(f"Approach 3 (separate multipliers): {'✅ ALL ACCURATE' if a3_all_accurate else '❌ SOME INACCURATE'}")
        
        print("\n=== PROS AND CONS ===")
        
        print("\n🔹 Approach 1 (Both Deflated):")
        print("✅ Pros: Simple, maintains proportional relationship")
        print("❌ Cons: May not reflect actual token usage patterns")
        
        print("\n🔹 Approach 2 (Minutes Only):")
        print("✅ Pros: Accurate cost equivalence, simple to implement")
        print("❌ Cons: Token quota becomes less meaningful")
        
        print("\n🔹 Approach 3 (Separate Multipliers):")
        print("✅ Pros: Maintains both cost and token accuracy")
        print("❌ Cons: More complex, requires two different multipliers")
        
        # Recommendation
        if a2_all_accurate:
            print("\n🎯 RECOMMENDATION: **Approach 2 (Minutes Only Deflated)**")
            print("Reasons:")
            print("- Maintains perfect cost equivalence")
            print("- Simple to implement and understand")
            print("- Token quota becomes a secondary metric for rate limiting")
            print("- Focus on cost fairness rather than token precision")
        elif a3_all_accurate:
            print("\n🎯 RECOMMENDATION: **Approach 3 (Separate Multipliers)**")
            print("Reasons:")
            print("- Maintains both cost and token accuracy")
            print("- Better for systems that need precise token tracking")
        else:
            print("\n⚠️  Need to investigate further - no approach is fully accurate")

def main():
    """Main execution function."""
    analyzer = DualQuotaAnalyzer()
    
    # Analyze approaches
    approaches_df = analyzer.analyze_approaches(test_minutes=5)
    
    # Display the approaches
    print("=== QUOTA TRACKING APPROACHES ===")
    display_cols = ['Model', 'Actual_Minutes', 'Actual_Tokens', 'A1_Minutes', 'A1_Tokens', 'A2_Minutes', 'A2_Tokens']
    print(approaches_df[display_cols].round(2).to_string(index=False))
    print()
    
    # Verify cost equivalence
    verification_df = analyzer.verify_cost_equivalence(approaches_df)
    
    # Make recommendation
    analyzer.recommend_approach(verification_df)
    
    # Save results
    approaches_df.to_csv('dual_quota_approaches.csv', index=False)
    verification_df.to_csv('dual_quota_verification.csv', index=False)
    
    print(f"\n📊 Results saved to dual_quota_approaches.csv and dual_quota_verification.csv")

if __name__ == "__main__":
    main()
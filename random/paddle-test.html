<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paddle Checkout Test</title>
    
    <!-- Set the content type header -->
    <meta http-equiv="Content-Type" content="application/json; charset=utf-8">
    
    <!-- Include Paddle.js -->
    <script src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .button {
            display: inline-block;
            background-color: #4a7eff;
            color: white;
            padding: 12px 24px;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 600;
            margin-top: 20px;
            cursor: pointer;
        }
        .debug {
            margin-top: 30px;
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        h1 {
            margin-bottom: 30px;
        }
        .options-section {
            margin: 20px 0;
            padding: 15px;
            background-color: #f0f5ff;
            border-radius: 4px;
        }
        .input-group {
            margin-bottom: 10px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <h1>Paddle Checkout Test</h1>
    
    <p>This is a simple test page for Paddle overlay checkout. Click the button below to launch the checkout.</p>
    
    <!-- Options for testing -->
    <div class="options-section">
        <div class="input-group">
            <label for="priceIdInput">Price ID:</label>
            <input type="text" id="priceIdInput" value="pri_01gsz8ntc6z7npqqp6j4ys0w1w" style="width: 100%; padding: 8px;">
        </div>
        <div class="input-group">
            <label for="tokenInput">Client Token:</label>
            <input type="text" id="tokenInput" value="test_680de632e3704f136f57c7f43cb" style="width: 100%; padding: 8px;">
        </div>
        <button onclick="reinitialize()" style="padding: 8px 16px; margin-top: 10px;">Reinitialize Paddle</button>
    </div>
    
    <!-- Simple checkout button -->
    <a href="#" class="button" id="checkoutButton">Launch Overlay Checkout</a>
    
    <!-- Debug output area -->
    <div class="debug" id="debugOutput">Debug info will appear here</div>
    
    <script type="text/javascript">
        // Debug helper function
        function log(message) {
            const debugOutput = document.getElementById('debugOutput');
            const timestamp = new Date().toISOString().substring(11, 19);
            debugOutput.innerHTML += `[${timestamp}] ${message}\n`;
            console.log(message);
            debugOutput.scrollTop = debugOutput.scrollHeight;
        }
        
        // Function to reinitialize Paddle with the new token
        function reinitialize() {
            const clientToken = document.getElementById('tokenInput').value.trim();
            
            try {
                log('Setting Paddle environment to sandbox...');
                Paddle.Environment.set("sandbox");
                
                log('Initializing Paddle with client token...');
                Paddle.Initialize({ 
                    token: clientToken,
                    checkout: {
                        settings: {
                            locale: 'en',
                            theme: 'light'
                        }
                    }
                });
                
                log('Paddle reinitialized successfully');
            } catch (error) {
                log('Error initializing Paddle: ' + error.message);
            }
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            reinitialize();
        });
        
        // Function to get items for checkout
        function getItems() {
            const priceId = document.getElementById('priceIdInput').value.trim();
            return [
                {
                    priceId: priceId,
                    quantity: 1
                }
            ];
        }
        
        // Customer info for prefilling checkout
        const customerInfo = {
            email: "<EMAIL>"
        };
        
        
        // Function to open checkout
        function openCheckout() {
            try {
                const items = getItems();
                log('Opening checkout with items: ' + JSON.stringify(items, null, 2));
                
                // Create a properly formatted checkout options object
                const checkoutOptions = {
                    items: items,
                    customer: customerInfo,
                    // settings: {
                    //     displayMode: 'overlay',
                    //     theme: 'light',
                    //     locale: 'en',
                    //     successUrl: 'https://voicehype.ai/app',
                    //     cancelUrl: window.location.origin + window.location.pathname + '?cancelled=true'
                    // }
                };
                
                // Log the exact object we're sending to Paddle
                log('Sending checkout options to Paddle: ' + JSON.stringify(checkoutOptions, null, 2));
                
                // Open the checkout with our options
                Paddle.Checkout.open(checkoutOptions);
                
                log('Checkout opened successfully');
            } catch (error) {
                log('Error opening checkout: ' + error.message);
                if (error.stack) {
                    log('Stack trace: ' + error.stack);
                }
            }
        }
        
        // Event listeners for Paddle events
        document.addEventListener('paddle:checkout:completed', function(event) {
            log('Checkout completed: ' + JSON.stringify(event.detail, null, 2));
        });
        
        document.addEventListener('paddle:checkout:closed', function(event) {
            log('Checkout closed: ' + JSON.stringify(event.detail, null, 2));
        });
        
        document.addEventListener('paddle:checkout:error', function(event) {
            log('Checkout error occurred');
            if (event.detail && event.detail.error) {
                log('Error details: ' + JSON.stringify(event.detail.error, null, 2));
            } else {
                log('No detailed error information available');
            }
        });
        
        
        // Set up button click handler
        document.getElementById('checkoutButton').addEventListener('click', function(event) {
            event.preventDefault();
            openCheckout();
        });
        
        // Check URL parameters on page load (for handling success/cancel redirects)
        window.addEventListener('load', function() {
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('success') === 'true') {
                log('Returned from successful checkout!');
            } else if (urlParams.get('cancelled') === 'true') {
                log('Checkout was cancelled');
            }
            
            // Add information about browser and Paddle version
            log('Browser: ' + navigator.userAgent);
            if (window.Paddle && window.Paddle.version) {
                log('Paddle.js version: ' + window.Paddle.version);
            }
        });
    </script>
    
    <!-- Additional debugging helpers -->
    <script>
        // Monitor all network requests to help with debugging
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            const request = args[0];
            if (typeof request === 'object' && request.url && request.url.includes('paddle')) {
                log(`Fetch request to: ${request.url}`);
                if (args[1] && args[1].body) {
                    try {
                        const bodyContent = args[1].body;
                        log(`Request body: ${typeof bodyContent === 'string' ? bodyContent : JSON.stringify(bodyContent)}`);
                    } catch (e) {
                        log(`Could not stringify request body: ${e.message}`);
                    }
                }
            }
            return originalFetch.apply(this, args)
                .then(response => {
                    if (request && typeof request === 'object' && request.url && request.url.includes('paddle')) {
                        log(`Response status from ${request.url}: ${response.status}`);
                    }
                    return response;
                })
                .catch(error => {
                    if (request && typeof request === 'object' && request.url && request.url.includes('paddle')) {
                        log(`Fetch error for ${request.url}: ${error.message}`);
                    }
                    throw error;
                });
        };
    </script>
</body>
</html>

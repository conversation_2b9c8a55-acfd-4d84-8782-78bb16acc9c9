[RecordingControls] [DEBUG] Start button clicked, current state: {isRecording: false, isPaused: false}
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Start recording called, current state: {isRecording: false, isPaused: false}
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Optimistically updating UI state to recording
main.cb59bbeb.chunk.js:1 Using light logo: https://file%2B.vscode-resource.vscode-cdn.net/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/media/voicehype_logo.png
console.ts:137 [Extension Host] [ConfigService] Updating setting voicehype.transcription.service to: assemblyai
console.ts:137 [Extension Host] Skipping options update while processing command
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: updateTranscriptions
main.cb59bbeb.chunk.js:1 WebView: Received 17 transcriptions from extension
console.ts:137 [Extension Host] [ConfigService] Retrieved audio settings - sampleRate: 48000, device: 1 (type: string)
main.cb59bbeb.chunk.js:1 WebView: Current transcriptions count before update: 17
main.cb59bbeb.chunk.js:1 WebView: Most recent transcription: {id: '1748694158443', timestamp: '2025-05-31T12:22:38.443Z', originalTextLength: 956, hasOptimized: true, service: 'assemblyai'}
main.cb59bbeb.chunk.js:1 Using light logo: https://file%2B.vscode-resource.vscode-cdn.net/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/media/voicehype_logo.png
console.ts:137 [Extension Host] [ConfigService] Returning device setting as string: "1"
console.ts:137 [Extension Host] VoiceHype: Sending audio settings to WebView: {sampleRate: 48000, device: '1'}
main.cb59bbeb.chunk.js:1 WebView: Transcription state updated to 17 items
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: updateAudioSettings
console.ts:137 [Extension Host] AudifyMicrophone: Constructor called with options: {}
console.ts:137 [Extension Host] AudifyMicrophone: Initialized with options: {platform: 'linux', options: {…}, tempPath: '/tmp/voicehype_recording_temp.wav'}
console.ts:137 [Extension Host] AudifyMicrophone: Raw devices from RtAudio:
console.ts:137 [Extension Host]   [0] Default ALSA Device (Inputs: 10000, Outputs: 0)
console.ts:137 [Extension Host]   [1] PulseAudio Sound Server (Inputs: 32, Outputs: 32)
console.ts:137 [Extension Host]   [2] HDA Intel HDMI (HDMI 0) (Inputs: 0, Outputs: 8)
console.ts:137 [Extension Host]   [3] HDA Intel HDMI (HDMI 1) (Inputs: 0, Outputs: 8)
console.ts:137 [Extension Host]   [4] HDA Intel HDMI (HDMI 2) (Inputs: 0, Outputs: 8)
console.ts:137 [Extension Host]   [5] HDA Intel PCH (ALC3226 Analog) (Inputs: 2, Outputs: 2)
console.ts:137 [Extension Host] AudifyMicrophone: Processed device list:
console.ts:137 [Extension Host]   ID: default, Name: System Default
console.ts:137 [Extension Host]   ID: 0, Name: Default ALSA Device
console.ts:137 [Extension Host]   ID: 1, Name: PulseAudio Sound Server
console.ts:137 [Extension Host]   ID: 2, Name: HDA Intel PCH (ALC3226 Analog)
console.ts:137 [Extension Host] VoiceHype: Sending 4 audio devices to WebView with settings
console.ts:137 [Extension Host] [ConfigService] Successfully saved voicehype.transcription.service to VSCode settings
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: updateAudioDevices
main.cb59bbeb.chunk.js:1 Using light logo: https://file%2B.vscode-resource.vscode-cdn.net/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/media/voicehype_logo.png
console.ts:137 [Extension Host] [ConfigService] Handling service change to: assemblyai
console.ts:137 [Extension Host] [ConfigService] Updating setting voicehype.transcription.model to: best
console.ts:137 [Extension Host] [ConfigService] Updating setting voicehype.transcription.language to: en
console.ts:137 [Extension Host] [ConfigService] Set defaults for AssemblyAI: model=best, language=en (Global English)
console.ts:137 [Extension Host] [ConfigService] Retrieved translate setting: false
console.ts:137 [Extension Host] [ConfigService] Retrieved transcription service: assemblyai
console.ts:137 [Extension Host] VoiceHype: Service changed to assemblyai. Applying simple defaults...
console.ts:137 [Extension Host] [ConfigService] Updating setting voicehype.transcription.model to: best
localProcessExtensionHost.ts:275 Extension Host
localProcessExtensionHost.ts:276 ALSA lib pcm_dsnoop.c:541:(snd_pcm_dsnoop_open) The dsnoop plugin supports only capture streamRtApiAlsa::probeDeviceInfo: snd_pcm_open (playback) error for device (default), Invalid argument.ALSA lib pcm_dsnoop.c:541:(snd_pcm_dsnoop_open) The dsnoop plugin supports only capture streamRtApiAlsa::probeDeviceInfo: snd_pcm_open (playback) error for device (default), Invalid argument.
console.ts:137 [Extension Host] [ConfigService] Firing configuration change event for: voicehype.transcription.service
console.ts:137 [Extension Host] VoiceHype: Configuration changed - voicehype.transcription.service
console.ts:137 [Extension Host] [ConfigService] Retrieved shouldOptimize setting: true
2console.ts:137 [Extension Host] [ConfigService] Retrieved transcription service: assemblyai
console.ts:137 [Extension Host] [ConfigService] Retrieved transcription language: en
console.ts:137 [Extension Host] [ConfigService] Retrieved optimization model: claude-3.7-sonnet
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Updating status bar items with: {shouldOptimize: true, service: 'assemblyai', model: 'best', language: 'en', isRecording: false, …}
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Status bar update called from: Error: 
    at t.StatusBarService.updateStatusBarItems (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:94914)
    at ed.value (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:94081)
    at P.B (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at P.C (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at P.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at /home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:118431
    at Set.forEach (<anonymous>)
    at Timeout.<anonymous> (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:118312)
    at Timeout.a [as _onTimeout] (/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.8/dist/extension.js:904:9428)
    at listOnTimeout (node:internal/timers:581:17)
    at processTimers (node:internal/timers:519:7)
console.ts:137 [Extension Host] VoiceHypePanelService: Configuration changed - voicehype.transcription.service
2console.ts:137 [Extension Host] [ConfigService] Retrieved transcription service: assemblyai
console.ts:137 [Extension Host] [ConfigService] Retrieved transcription language: en
console.ts:137 [Extension Host] [ConfigService] Retrieved shouldOptimize setting: true
console.ts:137 [Extension Host] [ConfigService] Retrieved optimization model: claude-3.7-sonnet
console.ts:137 [Extension Host] [ConfigService] Retrieved custom prompt (length: 157)
console.ts:137 [Extension Host] [ConfigService] Retrieved translate setting: false
console.ts:137 [Extension Host] [VoiceHypePanel] Force-sending configuration to webview (force=true): {service: 'assemblyai', model: 'best', language: 'en', optimize: true, optimizationModel: 'claude-3.7-sonnet', …}
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: updateOptions
main.cb59bbeb.chunk.js:1 Received updateOptions: {service: 'assemblyai', model: 'best', language: 'en', optimize: true, optimizationModel: 'claude-3.7-sonnet', …}
main.cb59bbeb.chunk.js:1 Current state before updateOptions:
main.cb59bbeb.chunk.js:1 Service: assemblyai, Model: best, Language: en
main.cb59bbeb.chunk.js:1 Optimize: true, Translate: false, Realtime: false
main.cb59bbeb.chunk.js:1 Updating custom prompt (length: 157)
main.cb59bbeb.chunk.js:1 Configuration updated successfully
console.ts:137 [Extension Host] [ConfigService] Successfully saved voicehype.transcription.model to VSCode settings
console.ts:137 [Extension Host] [ConfigService] Retrieved transcription service: assemblyai
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Setting record button text to "Record" (was "Record")
console.ts:137 [Extension Host] [ConfigService] Firing configuration change event for: voicehype.transcription.model
console.ts:137 [Extension Host] VoiceHype: Configuration changed - voicehype.transcription.model
console.ts:137 [Extension Host] [ConfigService] Retrieved shouldOptimize setting: true
2console.ts:137 [Extension Host] [ConfigService] Retrieved transcription service: assemblyai
console.ts:137 [Extension Host] [ConfigService] Retrieved transcription language: en
console.ts:137 [Extension Host] [ConfigService] Retrieved optimization model: claude-3.7-sonnet
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Updating status bar items with: {shouldOptimize: true, service: 'assemblyai', model: 'best', language: 'en', isRecording: false, …}
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Status bar update called from: Error: 
    at t.StatusBarService.updateStatusBarItems (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:94914)
    at ed.value (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:94081)
    at P.B (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at P.C (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at P.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at /home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:118431
    at Set.forEach (<anonymous>)
    at Timeout.<anonymous> (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:118312)
    at Timeout.a [as _onTimeout] (/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.8/dist/extension.js:904:9428)
    at listOnTimeout (node:internal/timers:581:17)
    at processTimers (node:internal/timers:519:7)
console.ts:137 [Extension Host] VoiceHypePanelService: Configuration changed - voicehype.transcription.model
2console.ts:137 [Extension Host] [ConfigService] Retrieved transcription service: assemblyai
console.ts:137 [Extension Host] [ConfigService] Retrieved transcription language: en
console.ts:137 [Extension Host] [ConfigService] Retrieved shouldOptimize setting: true
console.ts:137 [Extension Host] [ConfigService] Retrieved optimization model: claude-3.7-sonnet
console.ts:137 [Extension Host] [ConfigService] Retrieved custom prompt (length: 157)
console.ts:137 [Extension Host] [ConfigService] Retrieved translate setting: false
console.ts:137 [Extension Host] [VoiceHypePanel] Force-sending configuration to webview (force=true): {service: 'assemblyai', model: 'best', language: 'en', optimize: true, optimizationModel: 'claude-3.7-sonnet', …}
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: updateOptions
main.cb59bbeb.chunk.js:1 Received updateOptions: {service: 'assemblyai', model: 'best', language: 'en', optimize: true, optimizationModel: 'claude-3.7-sonnet', …}
main.cb59bbeb.chunk.js:1 Current state before updateOptions:
main.cb59bbeb.chunk.js:1 Service: assemblyai, Model: best, Language: en
main.cb59bbeb.chunk.js:1 Optimize: true, Translate: false, Realtime: false
main.cb59bbeb.chunk.js:1 Updating custom prompt (length: 157)
main.cb59bbeb.chunk.js:1 Configuration updated successfully
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Setting record button text to "Record" (was "Record")
console.ts:137 [Extension Host] [ConfigService] Successfully saved voicehype.transcription.language to VSCode settings
console.ts:137 [Extension Host] [ConfigService] Firing configuration change event for: voicehype.transcription.language
console.ts:137 [Extension Host] VoiceHype: Configuration changed - voicehype.transcription.language
console.ts:137 [Extension Host] [ConfigService] Retrieved shouldOptimize setting: true
2console.ts:137 [Extension Host] [ConfigService] Retrieved transcription service: assemblyai
console.ts:137 [Extension Host] [ConfigService] Retrieved transcription language: en
console.ts:137 [Extension Host] [ConfigService] Retrieved optimization model: claude-3.7-sonnet
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Updating status bar items with: {shouldOptimize: true, service: 'assemblyai', model: 'best', language: 'en', isRecording: false, …}
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Status bar update called from: Error: 
    at t.StatusBarService.updateStatusBarItems (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:94914)
    at ed.value (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:94081)
    at P.B (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at P.C (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at P.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at /home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:118431
    at Set.forEach (<anonymous>)
    at Timeout.<anonymous> (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:118312)
    at Timeout.a [as _onTimeout] (/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.8/dist/extension.js:904:9428)
    at listOnTimeout (node:internal/timers:581:17)
    at processTimers (node:internal/timers:519:7)
console.ts:137 [Extension Host] VoiceHypePanelService: Configuration changed - voicehype.transcription.language
2console.ts:137 [Extension Host] [ConfigService] Retrieved transcription service: assemblyai
console.ts:137 [Extension Host] [ConfigService] Retrieved transcription language: en
console.ts:137 [Extension Host] [ConfigService] Retrieved shouldOptimize setting: true
console.ts:137 [Extension Host] [ConfigService] Retrieved optimization model: claude-3.7-sonnet
console.ts:137 [Extension Host] [ConfigService] Retrieved custom prompt (length: 157)
console.ts:137 [Extension Host] [ConfigService] Retrieved translate setting: false
console.ts:137 [Extension Host] [VoiceHypePanel] Force-sending configuration to webview (force=true): {service: 'assemblyai', model: 'best', language: 'en', optimize: true, optimizationModel: 'claude-3.7-sonnet', …}
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: updateOptions
main.cb59bbeb.chunk.js:1 Received updateOptions: {service: 'assemblyai', model: 'best', language: 'en', optimize: true, optimizationModel: 'claude-3.7-sonnet', …}
console.ts:137 [Extension Host] [ConfigService] Successfully saved voicehype.transcription.model to VSCode settings
main.cb59bbeb.chunk.js:1 Current state before updateOptions:
main.cb59bbeb.chunk.js:1 Service: assemblyai, Model: best, Language: en
main.cb59bbeb.chunk.js:1 Optimize: true, Translate: false, Realtime: false
main.cb59bbeb.chunk.js:1 Updating custom prompt (length: 157)
main.cb59bbeb.chunk.js:1 Configuration updated successfully
console.ts:137 [Extension Host] [ConfigService] Retrieved transcription service: assemblyai
console.ts:137 [Extension Host] [ConfigService] Updating setting voicehype.transcription.language to: en
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Setting record button text to "Record" (was "Record")
console.ts:137 [Extension Host] [ConfigService] Firing configuration change event for: voicehype.transcription.model
console.ts:137 [Extension Host] VoiceHype: Configuration changed - voicehype.transcription.model
console.ts:137 [Extension Host] [ConfigService] Retrieved shouldOptimize setting: true
2console.ts:137 [Extension Host] [ConfigService] Retrieved transcription service: assemblyai
console.ts:137 [Extension Host] [ConfigService] Retrieved transcription language: en
console.ts:137 [Extension Host] [ConfigService] Retrieved optimization model: claude-3.7-sonnet
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Updating status bar items with: {shouldOptimize: true, service: 'assemblyai', model: 'best', language: 'en', isRecording: false, …}
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Status bar update called from: Error: 
    at t.StatusBarService.updateStatusBarItems (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:94914)
    at ed.value (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:94081)
    at P.B (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at P.C (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at P.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at /home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:118431
    at Set.forEach (<anonymous>)
    at Timeout.<anonymous> (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:118312)
    at Timeout.a [as _onTimeout] (/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.8/dist/extension.js:904:9428)
    at listOnTimeout (node:internal/timers:581:17)
    at processTimers (node:internal/timers:519:7)
console.ts:137 [Extension Host] VoiceHypePanelService: Configuration changed - voicehype.transcription.model
2console.ts:137 [Extension Host] [ConfigService] Retrieved transcription service: assemblyai
console.ts:137 [Extension Host] [ConfigService] Retrieved transcription language: en
console.ts:137 [Extension Host] [ConfigService] Retrieved shouldOptimize setting: true
console.ts:137 [Extension Host] [ConfigService] Retrieved optimization model: claude-3.7-sonnet
console.ts:137 [Extension Host] [ConfigService] Retrieved custom prompt (length: 157)
console.ts:137 [Extension Host] [ConfigService] Retrieved translate setting: false
console.ts:137 [Extension Host] [VoiceHypePanel] Force-sending configuration to webview (force=true): {service: 'assemblyai', model: 'best', language: 'en', optimize: true, optimizationModel: 'claude-3.7-sonnet', …}
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: updateOptions
main.cb59bbeb.chunk.js:1 Received updateOptions: {service: 'assemblyai', model: 'best', language: 'en', optimize: true, optimizationModel: 'claude-3.7-sonnet', …}
main.cb59bbeb.chunk.js:1 Current state before updateOptions:
main.cb59bbeb.chunk.js:1 Service: assemblyai, Model: best, Language: en
main.cb59bbeb.chunk.js:1 Optimize: true, Translate: false, Realtime: false
main.cb59bbeb.chunk.js:1 Updating custom prompt (length: 157)
main.cb59bbeb.chunk.js:1 Configuration updated successfully
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Setting record button text to "Record" (was "Record")
console.ts:137 [Extension Host] [ConfigService] Successfully saved voicehype.transcription.language to VSCode settings
console.ts:137 [Extension Host] [ConfigService] Updating setting voicehype.transcription.shouldOptimize to: true
console.ts:137 [Extension Host] [ConfigService] Firing configuration change event for: voicehype.transcription.language
console.ts:137 [Extension Host] VoiceHype: Configuration changed - voicehype.transcription.language
console.ts:137 [Extension Host] [ConfigService] Retrieved shouldOptimize setting: true
2console.ts:137 [Extension Host] [ConfigService] Retrieved transcription service: assemblyai
console.ts:137 [Extension Host] [ConfigService] Retrieved transcription language: en
console.ts:137 [Extension Host] [ConfigService] Retrieved optimization model: claude-3.7-sonnet
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Updating status bar items with: {shouldOptimize: true, service: 'assemblyai', model: 'best', language: 'en', isRecording: false, …}
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Status bar update called from: Error: 
    at t.StatusBarService.updateStatusBarItems (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:94914)
    at ed.value (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:94081)
    at P.B (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at P.C (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at P.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at /home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:118431
    at Set.forEach (<anonymous>)
    at Timeout.<anonymous> (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:118312)
    at Timeout.a [as _onTimeout] (/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.8/dist/extension.js:904:9428)
    at listOnTimeout (node:internal/timers:581:17)
    at processTimers (node:internal/timers:519:7)
console.ts:137 [Extension Host] VoiceHypePanelService: Configuration changed - voicehype.transcription.language
2console.ts:137 [Extension Host] [ConfigService] Retrieved transcription service: assemblyai
console.ts:137 [Extension Host] [ConfigService] Retrieved transcription language: en
console.ts:137 [Extension Host] [ConfigService] Retrieved shouldOptimize setting: true
console.ts:137 [Extension Host] [ConfigService] Retrieved optimization model: claude-3.7-sonnet
console.ts:137 [Extension Host] [ConfigService] Retrieved custom prompt (length: 157)
console.ts:137 [Extension Host] [ConfigService] Retrieved translate setting: false
console.ts:137 [Extension Host] [VoiceHypePanel] Force-sending configuration to webview (force=true): {service: 'assemblyai', model: 'best', language: 'en', optimize: true, optimizationModel: 'claude-3.7-sonnet', …}
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: updateOptions
main.cb59bbeb.chunk.js:1 Received updateOptions: {service: 'assemblyai', model: 'best', language: 'en', optimize: true, optimizationModel: 'claude-3.7-sonnet', …}
main.cb59bbeb.chunk.js:1 Current state before updateOptions:
main.cb59bbeb.chunk.js:1 Service: assemblyai, Model: best, Language: en
main.cb59bbeb.chunk.js:1 Optimize: true, Translate: false, Realtime: false
main.cb59bbeb.chunk.js:1 Updating custom prompt (length: 157)
main.cb59bbeb.chunk.js:1 Configuration updated successfully
console.ts:137 [Extension Host] [ConfigService] Successfully saved voicehype.transcription.shouldOptimize to VSCode settings
console.ts:137 [Extension Host] [ConfigService] Updating setting voicehype.transcription.customPrompt to: Rewrite the following transcript to make it sound formal and professional, correcting grammar and sentence structure, while preserving the speaker's message.
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Setting record button text to "Record" (was "Record")
console.ts:137 [Extension Host] [ConfigService] Firing configuration change event for: voicehype.transcription.shouldOptimize
console.ts:137 [Extension Host] VoiceHype: Configuration changed - voicehype.transcription.shouldOptimize
console.ts:137 [Extension Host] [ConfigService] Retrieved shouldOptimize setting: true
2console.ts:137 [Extension Host] [ConfigService] Retrieved transcription service: assemblyai
console.ts:137 [Extension Host] [ConfigService] Retrieved transcription language: en
console.ts:137 [Extension Host] [ConfigService] Retrieved optimization model: claude-3.7-sonnet
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Updating status bar items with: {shouldOptimize: true, service: 'assemblyai', model: 'best', language: 'en', isRecording: false, …}
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Status bar update called from: Error: 
    at t.StatusBarService.updateStatusBarItems (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:94914)
    at ed.value (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:94081)
    at P.B (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at P.C (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at P.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at /home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:118431
    at Set.forEach (<anonymous>)
    at Timeout.<anonymous> (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:118312)
    at Timeout.a [as _onTimeout] (/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.8/dist/extension.js:904:9428)
    at listOnTimeout (node:internal/timers:581:17)
    at processTimers (node:internal/timers:519:7)
console.ts:137 [Extension Host] VoiceHypePanelService: Configuration changed - voicehype.transcription.shouldOptimize
2console.ts:137 [Extension Host] [ConfigService] Retrieved transcription service: assemblyai
console.ts:137 [Extension Host] [ConfigService] Retrieved transcription language: en
console.ts:137 [Extension Host] [ConfigService] Retrieved shouldOptimize setting: true
console.ts:137 [Extension Host] [ConfigService] Retrieved optimization model: claude-3.7-sonnet
console.ts:137 [Extension Host] [ConfigService] Retrieved custom prompt (length: 157)
console.ts:137 [Extension Host] [ConfigService] Retrieved translate setting: false
console.ts:137 [Extension Host] [VoiceHypePanel] Force-sending configuration to webview (force=true): {service: 'assemblyai', model: 'best', language: 'en', optimize: true, optimizationModel: 'claude-3.7-sonnet', …}
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: updateOptions
main.cb59bbeb.chunk.js:1 Received updateOptions: {service: 'assemblyai', model: 'best', language: 'en', optimize: true, optimizationModel: 'claude-3.7-sonnet', …}
main.cb59bbeb.chunk.js:1 Current state before updateOptions:
main.cb59bbeb.chunk.js:1 Service: assemblyai, Model: best, Language: en
main.cb59bbeb.chunk.js:1 Optimize: true, Translate: false, Realtime: false
main.cb59bbeb.chunk.js:1 Updating custom prompt (length: 157)
main.cb59bbeb.chunk.js:1 Configuration updated successfully
console.ts:137 [Extension Host] [ConfigService] Successfully saved voicehype.transcription.customPrompt to VSCode settings
console.ts:137 [Extension Host] [ConfigService] Updated in-memory customPrompt
console.ts:137 [Extension Host] [ConfigService] Updating setting voicehype.transcription.translate to: false
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Setting record button text to "Record" (was "Record")
console.ts:137 [Extension Host] [ConfigService] Successfully saved voicehype.transcription.translate to VSCode settings
console.ts:137 [Extension Host] [ConfigService] Retrieved transcription service: assemblyai
console.ts:137 [Extension Host] [ConfigService] Updating setting voicehype.transcription.realtime to: false
console.ts:137 [Extension Host] [ConfigService] Successfully saved voicehype.transcription.realtime to VSCode settings
console.ts:137 [Extension Host] VoiceHype: Real-time disabled, keeping current sample rate
console.ts:137 [Extension Host] VoiceHype: Starting recording from webview with optimize: true
console.ts:137 [Extension Host] VoiceHype: Starting recording... (withOptimize: true )
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Emitting state change: isRecording=true, isPaused=false, elapsedTime=0ms
console.ts:137 [Extension Host] VoiceHype [DEBUG]: State change emitted from: Error: 
    at t.RecordingService.emitStateChange (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:178462)
    at t.RecordingService.startRecording (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:157391)
    at ed.value (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:20895)
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Updating WebView recording state: {isRecording: true, isPaused: false, elapsedTime: 0}
console.ts:137 [Extension Host] VoiceHype [DEBUG]: WebView update called from: Error: 
    at h._updateRecordingState (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:8294)
    at EventEmitter.<anonymous> (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:3940)
    at EventEmitter.emit (node:events:536:35)
    at t.RecordingService.emitStateChange (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:178504)
    at t.RecordingService.startRecording (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:157391)
    at ed.value (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:20895)
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: recordingState
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Recording state change listener triggered: isRecording=true, isPaused=false
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Received recordingState message: {command: 'recordingState', isRecording: true, isPaused: false, elapsedTime: 0}
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Current recording state before update: {isRecording: true, isPaused: false, elapsedTime: 0}
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating isRecording from true to true
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating isPaused from false to false
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating elapsedTime from 0 to 0
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Sending recording state to webview from listener
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: recordingState
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Received recordingState message: {command: 'recordingState', isRecording: true, isPaused: false, elapsedTime: 0}
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Current recording state before update: {isRecording: true, isPaused: false, elapsedTime: 0}
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating isRecording from true to true
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating isPaused from false to false
console.ts:137 [Extension Host] VoiceHype: RECORDING START DETAILS:
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating elapsedTime from 0 to 0
console.ts:137 [Extension Host] VoiceHype: - Start time: 2025-06-02T01:02:33.297Z
console.ts:137 [Extension Host] VoiceHype: - Total paused time reset to: 0ms
console.ts:137 [Extension Host] VoiceHype: - Real-time mode: false
console.ts:137 [Extension Host] VoiceHype: - Optimization: true
console.ts:137 [Extension Host] VoiceHype: Set recording context
console.ts:137 [Extension Host] [ConfigService] Retrieved shouldOptimize setting: true
2console.ts:137 [Extension Host] [ConfigService] Retrieved transcription service: assemblyai
console.ts:137 [Extension Host] [ConfigService] Retrieved transcription language: en
console.ts:137 [Extension Host] [ConfigService] Retrieved optimization model: claude-3.7-sonnet
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Updating status bar items with: {shouldOptimize: true, service: 'assemblyai', model: 'best', language: 'en', isRecording: true, …}
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Status bar update called from: Error: 
    at t.StatusBarService.updateStatusBarItems (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:94914)
    at t.RecordingService.startRecording (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:157945)
    at ed.value (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:20895)
console.ts:137 [Extension Host] VoiceHype: Updated status bar for recording
console.ts:137 [Extension Host] VoiceHype: Started file tracking
console.ts:137 [Extension Host] VoiceHype: Recording directory is writable: /tmp
console.ts:137 [Extension Host] VoiceHype: Created write stream for recording
console.ts:137 [Extension Host] VoiceHype: Starting microphone for recording
console.ts:137 [Extension Host] Microphone: startRecording called
console.ts:137 [Extension Host] MicrophoneCompat: startRecording called
console.ts:137 [Extension Host] AudifyMicrophone: startRecording called
console.ts:137 [Extension Host] AudifyMicrophone: Destroying existing PassThrough stream
console.ts:137 [Extension Host] AudifyMicrophone: New PassThrough stream created
console.ts:137 [Extension Host] AudifyMicrophone: RtAudio instance created
console.ts:137 [Extension Host] AudifyMicrophone: Available audio devices:
localProcessExtensionHost.ts:275 Extension Host
localProcessExtensionHost.ts:276 ALSA lib pcm_dsnoop.c:541:(snd_pcm_dsnoop_open) The dsnoop plugin supports only capture streamRtApiAlsa::probeDeviceInfo: snd_pcm_open (playback) error for device (default), Invalid argument.
console.ts:137 [Extension Host] [0] Default ALSA Device (Inputs: 10000)
console.ts:137 [Extension Host] [1] PulseAudio Sound Server (Inputs: 32)
console.ts:137 [Extension Host] [2] HDA Intel HDMI (HDMI 0) (Inputs: 0)
console.ts:137 [Extension Host] [3] HDA Intel HDMI (HDMI 1) (Inputs: 0)
console.ts:137 [Extension Host] [4] HDA Intel HDMI (HDMI 2) (Inputs: 0)
console.ts:137 [Extension Host] [5] HDA Intel PCH (ALC3226 Analog) (Inputs: 2)
console.ts:137 [Extension Host] AudifyMicrophone: Using hardcoded device ID: 1
console.ts:137 [Extension Host] AudifyMicrophone: Using input device: [1] PulseAudio Sound Server with 32 input channels
console.ts:137 [Extension Host] AudifyMicrophone: Configuration: sampleRate=48000, channels=1, frameSize=1920
console.ts:137 [Extension Host] AudifyMicrophone: Stream opened successfully
console.ts:137 [Extension Host] AudifyMicrophone: Audio stream started successfully
console.ts:137 [Extension Host] VoiceHype: Started microphone recording
console.ts:137 [Extension Host] [ConfigService] Retrieved shouldOptimize setting: true
2console.ts:137 [Extension Host] [ConfigService] Retrieved transcription service: assemblyai
console.ts:137 [Extension Host] [ConfigService] Retrieved transcription language: en
console.ts:137 [Extension Host] [ConfigService] Retrieved optimization model: claude-3.7-sonnet
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Updating status bar items with: {shouldOptimize: true, service: 'assemblyai', model: 'best', language: 'en', isRecording: true, …}
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Status bar update called from: Error: 
    at t.StatusBarService.updateStatusBarItems (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:94914)
    at ed.value (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:21114)
console.ts:137 [Extension Host] [ConfigService] Firing configuration change event for: voicehype.transcription.customPrompt
console.ts:137 [Extension Host] VoiceHype: Configuration changed - voicehype.transcription.customPrompt
console.ts:137 [Extension Host] [ConfigService] Retrieved shouldOptimize setting: true
2console.ts:137 [Extension Host] [ConfigService] Retrieved transcription service: assemblyai
console.ts:137 [Extension Host] [ConfigService] Retrieved transcription language: en
console.ts:137 [Extension Host] [ConfigService] Retrieved optimization model: claude-3.7-sonnet
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Updating status bar items with: {shouldOptimize: true, service: 'assemblyai', model: 'best', language: 'en', isRecording: false, …}
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Status bar update called from: Error: 
    at t.StatusBarService.updateStatusBarItems (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:94914)
    at ed.value (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:94081)
    at P.B (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at P.C (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at P.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at /home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:118431
    at Set.forEach (<anonymous>)
    at Timeout.<anonymous> (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:118312)
    at Timeout.a [as _onTimeout] (/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.8/dist/extension.js:904:9428)
    at listOnTimeout (node:internal/timers:581:17)
    at processTimers (node:internal/timers:519:7)
console.ts:137 [Extension Host] VoiceHypePanelService: Configuration changed - voicehype.transcription.customPrompt
2console.ts:137 [Extension Host] [ConfigService] Retrieved transcription service: assemblyai
console.ts:137 [Extension Host] [ConfigService] Retrieved transcription language: en
console.ts:137 [Extension Host] [ConfigService] Retrieved shouldOptimize setting: true
console.ts:137 [Extension Host] [ConfigService] Retrieved optimization model: claude-3.7-sonnet
console.ts:137 [Extension Host] [ConfigService] Retrieved custom prompt (length: 157)
console.ts:137 [Extension Host] [ConfigService] Retrieved translate setting: false
console.ts:137 [Extension Host] [VoiceHypePanel] Force-sending configuration to webview (force=true): {service: 'assemblyai', model: 'best', language: 'en', optimize: true, optimizationModel: 'claude-3.7-sonnet', …}
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: updateOptions
console.ts:137 [Extension Host] [ConfigService] Firing configuration change event for: voicehype.transcription.translate
main.cb59bbeb.chunk.js:1 Received updateOptions: {service: 'assemblyai', model: 'best', language: 'en', optimize: true, optimizationModel: 'claude-3.7-sonnet', …}
main.cb59bbeb.chunk.js:1 Current state before updateOptions:
main.cb59bbeb.chunk.js:1 Service: assemblyai, Model: best, Language: en
main.cb59bbeb.chunk.js:1 Optimize: true, Translate: false, Realtime: false
main.cb59bbeb.chunk.js:1 Updating custom prompt (length: 157)
main.cb59bbeb.chunk.js:1 Configuration updated successfully
console.ts:137 [Extension Host] VoiceHype: Configuration changed - voicehype.transcription.translate
console.ts:137 [Extension Host] [ConfigService] Retrieved shouldOptimize setting: true
2console.ts:137 [Extension Host] [ConfigService] Retrieved transcription service: assemblyai
console.ts:137 [Extension Host] [ConfigService] Retrieved transcription language: en
console.ts:137 [Extension Host] [ConfigService] Retrieved optimization model: claude-3.7-sonnet
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Updating status bar items with: {shouldOptimize: true, service: 'assemblyai', model: 'best', language: 'en', isRecording: false, …}
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Status bar update called from: Error: 
    at t.StatusBarService.updateStatusBarItems (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:94914)
    at ed.value (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:94081)
    at P.B (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at P.C (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at P.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at /home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:118431
    at Set.forEach (<anonymous>)
    at Timeout.<anonymous> (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:118312)
    at Timeout.a [as _onTimeout] (/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.8/dist/extension.js:904:9428)
    at listOnTimeout (node:internal/timers:581:17)
    at processTimers (node:internal/timers:519:7)
console.ts:137 [Extension Host] VoiceHypePanelService: Configuration changed - voicehype.transcription.translate
2console.ts:137 [Extension Host] [ConfigService] Retrieved transcription service: assemblyai
console.ts:137 [Extension Host] [ConfigService] Retrieved transcription language: en
console.ts:137 [Extension Host] [ConfigService] Retrieved shouldOptimize setting: true
console.ts:137 [Extension Host] [ConfigService] Retrieved optimization model: claude-3.7-sonnet
console.ts:137 [Extension Host] [ConfigService] Retrieved custom prompt (length: 157)
console.ts:137 [Extension Host] [ConfigService] Retrieved translate setting: false
console.ts:137 [Extension Host] [VoiceHypePanel] Force-sending configuration to webview (force=true): {service: 'assemblyai', model: 'best', language: 'en', optimize: true, optimizationModel: 'claude-3.7-sonnet', …}
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: updateOptions
main.cb59bbeb.chunk.js:1 Received updateOptions: {service: 'assemblyai', model: 'best', language: 'en', optimize: true, optimizationModel: 'claude-3.7-sonnet', …}
console.ts:137 [Extension Host] [ConfigService] Firing configuration change event for: voicehype.transcription.realtime
main.cb59bbeb.chunk.js:1 Current state before updateOptions:
main.cb59bbeb.chunk.js:1 Service: assemblyai, Model: best, Language: en
main.cb59bbeb.chunk.js:1 Optimize: true, Translate: false, Realtime: false
main.cb59bbeb.chunk.js:1 Updating custom prompt (length: 157)
main.cb59bbeb.chunk.js:1 Configuration updated successfully
console.ts:137 [Extension Host] VoiceHype: Configuration changed - voicehype.transcription.realtime
console.ts:137 [Extension Host] [ConfigService] Retrieved shouldOptimize setting: true
2console.ts:137 [Extension Host] [ConfigService] Retrieved transcription service: assemblyai
console.ts:137 [Extension Host] [ConfigService] Retrieved transcription language: en
console.ts:137 [Extension Host] [ConfigService] Retrieved optimization model: claude-3.7-sonnet
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Updating status bar items with: {shouldOptimize: true, service: 'assemblyai', model: 'best', language: 'en', isRecording: false, …}
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Status bar update called from: Error: 
    at t.StatusBarService.updateStatusBarItems (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:94914)
    at ed.value (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:94081)
    at P.B (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at P.C (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at P.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at /home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:118431
    at Set.forEach (<anonymous>)
    at Timeout.<anonymous> (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:118312)
    at Timeout.a [as _onTimeout] (/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.8/dist/extension.js:904:9428)
    at listOnTimeout (node:internal/timers:581:17)
    at processTimers (node:internal/timers:519:7)
console.ts:137 [Extension Host] VoiceHypePanelService: Configuration changed - voicehype.transcription.realtime
2console.ts:137 [Extension Host] [ConfigService] Retrieved transcription service: assemblyai
console.ts:137 [Extension Host] [ConfigService] Retrieved transcription language: en
console.ts:137 [Extension Host] [ConfigService] Retrieved shouldOptimize setting: true
console.ts:137 [Extension Host] [ConfigService] Retrieved optimization model: claude-3.7-sonnet
console.ts:137 [Extension Host] [ConfigService] Retrieved custom prompt (length: 157)
console.ts:137 [Extension Host] [ConfigService] Retrieved translate setting: false
console.ts:137 [Extension Host] [VoiceHypePanel] Force-sending configuration to webview (force=true): {service: 'assemblyai', model: 'best', language: 'en', optimize: true, optimizationModel: 'claude-3.7-sonnet', …}
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: updateOptions
main.cb59bbeb.chunk.js:1 Received updateOptions: {service: 'assemblyai', model: 'best', language: 'en', optimize: true, optimizationModel: 'claude-3.7-sonnet', …}
main.cb59bbeb.chunk.js:1 Current state before updateOptions:
main.cb59bbeb.chunk.js:1 Service: assemblyai, Model: best, Language: en
main.cb59bbeb.chunk.js:1 Optimize: true, Translate: false, Realtime: false
main.cb59bbeb.chunk.js:1 Updating custom prompt (length: 157)
main.cb59bbeb.chunk.js:1 Configuration updated successfully
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Setting record button text to "Record" (was "Record")
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Emitting state change: isRecording=true, isPaused=false, elapsedTime=1ms
console.ts:137 [Extension Host] VoiceHype [DEBUG]: State change emitted from: Error: 
    at t.RecordingService.emitStateChange (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:178462)
    at Timeout.<anonymous> (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:178738)
    at Timeout.a [as _onTimeout] (/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.8/dist/extension.js:904:9428)
    at listOnTimeout (node:internal/timers:581:17)
    at processTimers (node:internal/timers:519:7)
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Updating WebView recording state: {isRecording: true, isPaused: false, elapsedTime: 1}
console.ts:137 [Extension Host] VoiceHype [DEBUG]: WebView update called from: Error: 
    at h._updateRecordingState (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:8294)
    at EventEmitter.<anonymous> (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:3940)
    at EventEmitter.emit (node:events:536:35)
    at t.RecordingService.emitStateChange (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:178504)
    at Timeout.<anonymous> (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:178738)
    at Timeout.a [as _onTimeout] (/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.8/dist/extension.js:904:9428)
    at listOnTimeout (node:internal/timers:581:17)
    at processTimers (node:internal/timers:519:7)
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: recordingState
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Received recordingState message: {command: 'recordingState', isRecording: true, isPaused: false, elapsedTime: 1}
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Current recording state before update: {isRecording: true, isPaused: false, elapsedTime: 0}
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Recording state change listener triggered: isRecording=true, isPaused=false
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating isRecording from true to true
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating isPaused from false to false
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating elapsedTime from 0 to 1
main.cb59bbeb.chunk.js:1 Using light logo: https://file%2B.vscode-resource.vscode-cdn.net/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/media/voicehype_logo.png
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Sending recording state to webview from listener
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: recordingState
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Received recordingState message: {command: 'recordingState', isRecording: true, isPaused: false, elapsedTime: 1}
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Current recording state before update: {isRecording: true, isPaused: false, elapsedTime: 1}
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating isRecording from true to true
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating isPaused from false to false
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating elapsedTime from 1 to 1
2console.ts:137 [Extension Host] [ConfigService] Retrieved transcription service: assemblyai
console.ts:137 [Extension Host] [ConfigService] Retrieved transcription language: en
console.ts:137 [Extension Host] [ConfigService] Retrieved shouldOptimize setting: true
console.ts:137 [Extension Host] [ConfigService] Retrieved optimization model: claude-3.7-sonnet
console.ts:137 [Extension Host] [ConfigService] Retrieved custom prompt (length: 157)
console.ts:137 [Extension Host] [ConfigService] Retrieved translate setting: false
console.ts:137 [Extension Host] Sending options to webview: {service: 'assemblyai', model: 'best', language: 'en', optimize: true, optimizationModel: 'claude-3.7-sonnet', …}
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: updateOptions
main.cb59bbeb.chunk.js:1 Received updateOptions: {service: 'assemblyai', model: 'best', language: 'en', optimize: true, optimizationModel: 'claude-3.7-sonnet', …}
main.cb59bbeb.chunk.js:1 Current state before updateOptions:
main.cb59bbeb.chunk.js:1 Service: assemblyai, Model: best, Language: en
main.cb59bbeb.chunk.js:1 Optimize: true, Translate: false, Realtime: false
main.cb59bbeb.chunk.js:1 Skipping custom prompt update due to recent local update
main.cb59bbeb.chunk.js:1 No configuration changes applied
console.ts:137 [Extension Host] [ConfigService] Retrieved audio settings - sampleRate: 48000, device: 1 (type: string)
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: updateTranscriptions
main.cb59bbeb.chunk.js:1 WebView: Received 17 transcriptions from extension
console.ts:137 [Extension Host] [ConfigService] Returning device setting as string: "1"
main.cb59bbeb.chunk.js:1 WebView: Current transcriptions count before update: 17
main.cb59bbeb.chunk.js:1 WebView: Most recent transcription: {id: '1748694158443', timestamp: '2025-05-31T12:22:38.443Z', originalTextLength: 956, hasOptimized: true, service: 'assemblyai'}
console.ts:137 [Extension Host] VoiceHype: Sending audio settings to WebView: {sampleRate: 48000, device: '1'}
main.cb59bbeb.chunk.js:1 Using light logo: https://file%2B.vscode-resource.vscode-cdn.net/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/media/voicehype_logo.png
console.ts:137 [Extension Host] AudifyMicrophone: Constructor called with options: {}
main.cb59bbeb.chunk.js:1 WebView: Transcription state updated to 17 items
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: updateAudioSettings
console.ts:137 [Extension Host] AudifyMicrophone: Initialized with options: {platform: 'linux', options: {…}, tempPath: '/tmp/voicehype_recording_temp.wav'}
console.ts:137 [Extension Host] AudifyMicrophone: Raw devices from RtAudio:
console.ts:137 [Extension Host]   [0] Default ALSA Device (Inputs: 10000, Outputs: 0)
console.ts:137 [Extension Host]   [1] PulseAudio Sound Server (Inputs: 32, Outputs: 32)
console.ts:137 [Extension Host]   [2] HDA Intel HDMI (HDMI 0) (Inputs: 0, Outputs: 8)
console.ts:137 [Extension Host]   [3] HDA Intel HDMI (HDMI 1) (Inputs: 0, Outputs: 8)
console.ts:137 [Extension Host]   [4] HDA Intel HDMI (HDMI 2) (Inputs: 0, Outputs: 8)
console.ts:137 [Extension Host]   [5] HDA Intel PCH (ALC3226 Analog) (Inputs: 2, Outputs: 2)
console.ts:137 [Extension Host] AudifyMicrophone: Processed device list:
console.ts:137 [Extension Host]   ID: default, Name: System Default
console.ts:137 [Extension Host]   ID: 0, Name: Default ALSA Device
console.ts:137 [Extension Host]   ID: 1, Name: PulseAudio Sound Server
console.ts:137 [Extension Host]   ID: 2, Name: HDA Intel PCH (ALC3226 Analog)
console.ts:137 [Extension Host] VoiceHype: Sending 4 audio devices to WebView with settings
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: updateAudioDevices
main.cb59bbeb.chunk.js:1 Using light logo: https://file%2B.vscode-resource.vscode-cdn.net/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/media/voicehype_logo.png
localProcessExtensionHost.ts:275 Extension Host
localProcessExtensionHost.ts:276 ALSA lib pcm_dsnoop.c:541:(snd_pcm_dsnoop_open) The dsnoop plugin supports only capture streamRtApiAlsa::probeDeviceInfo: snd_pcm_open (playback) error for device (default), Invalid argument.ALSA lib pcm_dsnoop.c:541:(snd_pcm_dsnoop_open) The dsnoop plugin supports only capture streamRtApiAlsa::probeDeviceInfo: snd_pcm_open (playback) error for device (default), Invalid argument.
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Emitting state change: isRecording=true, isPaused=false, elapsedTime=2ms
console.ts:137 [Extension Host] VoiceHype [DEBUG]: State change emitted from: Error: 
    at t.RecordingService.emitStateChange (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:178462)
    at Timeout.<anonymous> (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:178738)
    at Timeout.a [as _onTimeout] (/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.8/dist/extension.js:904:9428)
    at listOnTimeout (node:internal/timers:581:17)
    at processTimers (node:internal/timers:519:7)
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Updating WebView recording state: {isRecording: true, isPaused: false, elapsedTime: 2}
console.ts:137 [Extension Host] VoiceHype [DEBUG]: WebView update called from: Error: 
    at h._updateRecordingState (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:8294)
    at EventEmitter.<anonymous> (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:3940)
    at EventEmitter.emit (node:events:536:35)
    at t.RecordingService.emitStateChange (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:178504)
    at Timeout.<anonymous> (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:178738)
    at Timeout.a [as _onTimeout] (/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.8/dist/extension.js:904:9428)
    at listOnTimeout (node:internal/timers:581:17)
    at processTimers (node:internal/timers:519:7)
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: recordingState
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Received recordingState message: {command: 'recordingState', isRecording: true, isPaused: false, elapsedTime: 2}
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Recording state change listener triggered: isRecording=true, isPaused=false
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Current recording state before update: {isRecording: true, isPaused: false, elapsedTime: 1}
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating isRecording from true to true
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating isPaused from false to false
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating elapsedTime from 1 to 2
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Sending recording state to webview from listener
main.cb59bbeb.chunk.js:1 Using light logo: https://file%2B.vscode-resource.vscode-cdn.net/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/media/voicehype_logo.png
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: recordingState
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Received recordingState message: {command: 'recordingState', isRecording: true, isPaused: false, elapsedTime: 2}
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Current recording state before update: {isRecording: true, isPaused: false, elapsedTime: 2}
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating isRecording from true to true
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating isPaused from false to false
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating elapsedTime from 2 to 2
2console.ts:137 [Extension Host] [ConfigService] Retrieved transcription service: assemblyai
console.ts:137 [Extension Host] [ConfigService] Retrieved transcription language: en
console.ts:137 [Extension Host] [ConfigService] Retrieved shouldOptimize setting: true
console.ts:137 [Extension Host] [ConfigService] Retrieved optimization model: claude-3.7-sonnet
console.ts:137 [Extension Host] [ConfigService] Retrieved custom prompt (length: 157)
console.ts:137 [Extension Host] [ConfigService] Retrieved translate setting: false
console.ts:137 [Extension Host] Sending options to webview: {service: 'assemblyai', model: 'best', language: 'en', optimize: true, optimizationModel: 'claude-3.7-sonnet', …}
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: updateOptions
main.cb59bbeb.chunk.js:1 Received updateOptions: {service: 'assemblyai', model: 'best', language: 'en', optimize: true, optimizationModel: 'claude-3.7-sonnet', …}
main.cb59bbeb.chunk.js:1 Current state before updateOptions:
main.cb59bbeb.chunk.js:1 Service: assemblyai, Model: best, Language: en
main.cb59bbeb.chunk.js:1 Optimize: true, Translate: false, Realtime: false
main.cb59bbeb.chunk.js:1 Updating custom prompt (length: 157)
main.cb59bbeb.chunk.js:1 Configuration updated successfully
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: updateTranscriptions
console.ts:137 [Extension Host] [ConfigService] Retrieved audio settings - sampleRate: 48000, device: 1 (type: string)
main.cb59bbeb.chunk.js:1 WebView: Received 17 transcriptions from extension
main.cb59bbeb.chunk.js:1 WebView: Current transcriptions count before update: 17
main.cb59bbeb.chunk.js:1 WebView: Most recent transcription: {id: '1748694158443', timestamp: '2025-05-31T12:22:38.443Z', originalTextLength: 956, hasOptimized: true, service: 'assemblyai'}
main.cb59bbeb.chunk.js:1 Using light logo: https://file%2B.vscode-resource.vscode-cdn.net/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/media/voicehype_logo.png
console.ts:137 [Extension Host] [ConfigService] Returning device setting as string: "1"
main.cb59bbeb.chunk.js:1 WebView: Transcription state updated to 17 items
console.ts:137 [Extension Host] VoiceHype: Sending audio settings to WebView: {sampleRate: 48000, device: '1'}
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: updateAudioSettings
console.ts:137 [Extension Host] AudifyMicrophone: Constructor called with options: {}
console.ts:137 [Extension Host] AudifyMicrophone: Initialized with options: {platform: 'linux', options: {…}, tempPath: '/tmp/voicehype_recording_temp.wav'}
console.ts:137 [Extension Host] AudifyMicrophone: Raw devices from RtAudio:
console.ts:137 [Extension Host]   [0] Default ALSA Device (Inputs: 10000, Outputs: 0)
console.ts:137 [Extension Host]   [1] PulseAudio Sound Server (Inputs: 32, Outputs: 32)
console.ts:137 [Extension Host]   [2] HDA Intel HDMI (HDMI 0) (Inputs: 0, Outputs: 8)
console.ts:137 [Extension Host]   [3] HDA Intel HDMI (HDMI 1) (Inputs: 0, Outputs: 8)
console.ts:137 [Extension Host]   [4] HDA Intel HDMI (HDMI 2) (Inputs: 0, Outputs: 8)
console.ts:137 [Extension Host]   [5] HDA Intel PCH (ALC3226 Analog) (Inputs: 2, Outputs: 2)
console.ts:137 [Extension Host] AudifyMicrophone: Processed device list:
console.ts:137 [Extension Host]   ID: default, Name: System Default
console.ts:137 [Extension Host]   ID: 0, Name: Default ALSA Device
console.ts:137 [Extension Host]   ID: 1, Name: PulseAudio Sound Server
console.ts:137 [Extension Host]   ID: 2, Name: HDA Intel PCH (ALC3226 Analog)
console.ts:137 [Extension Host] VoiceHype: Sending 4 audio devices to WebView with settings
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: updateAudioDevices
main.cb59bbeb.chunk.js:1 Using light logo: https://file%2B.vscode-resource.vscode-cdn.net/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/media/voicehype_logo.png
localProcessExtensionHost.ts:275 Extension Host
localProcessExtensionHost.ts:276 ALSA lib pcm_dsnoop.c:541:(snd_pcm_dsnoop_open) The dsnoop plugin supports only capture streamRtApiAlsa::probeDeviceInfo: snd_pcm_open (playback) error for device (default), Invalid argument.ALSA lib pcm_dsnoop.c:541:(snd_pcm_dsnoop_open) The dsnoop plugin supports only capture streamRtApiAlsa::probeDeviceInfo: snd_pcm_open (playback) error for device (default), Invalid argument.
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Emitting state change: isRecording=true, isPaused=false, elapsedTime=3ms
console.ts:137 [Extension Host] VoiceHype [DEBUG]: State change emitted from: Error: 
    at t.RecordingService.emitStateChange (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:178462)
    at Timeout.<anonymous> (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:178738)
    at Timeout.a [as _onTimeout] (/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.8/dist/extension.js:904:9428)
    at listOnTimeout (node:internal/timers:581:17)
    at processTimers (node:internal/timers:519:7)
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Updating WebView recording state: {isRecording: true, isPaused: false, elapsedTime: 3}
console.ts:137 [Extension Host] VoiceHype [DEBUG]: WebView update called from: Error: 
    at h._updateRecordingState (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:8294)
    at EventEmitter.<anonymous> (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:3940)
    at EventEmitter.emit (node:events:536:35)
    at t.RecordingService.emitStateChange (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:178504)
    at Timeout.<anonymous> (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:178738)
    at Timeout.a [as _onTimeout] (/home/<USER>/.vscode/extensions/saoudrizwan.claude-dev-3.17.8/dist/extension.js:904:9428)
    at listOnTimeout (node:internal/timers:581:17)
    at processTimers (node:internal/timers:519:7)
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: recordingState
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Recording state change listener triggered: isRecording=true, isPaused=false
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Received recordingState message: {command: 'recordingState', isRecording: true, isPaused: false, elapsedTime: 3}
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Current recording state before update: {isRecording: true, isPaused: false, elapsedTime: 2}
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating isRecording from true to true
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Sending recording state to webview from listener
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating isPaused from false to false
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating elapsedTime from 2 to 3
main.cb59bbeb.chunk.js:1 Using light logo: https://file%2B.vscode-resource.vscode-cdn.net/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/media/voicehype_logo.png
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: recordingState
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Received recordingState message: {command: 'recordingState', isRecording: true, isPaused: false, elapsedTime: 3}
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Current recording state before update: {isRecording: true, isPaused: false, elapsedTime: 3}
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating isRecording from true to true
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating isPaused from false to false
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating elapsedTime from 3 to 3
2console.ts:137 [Extension Host] [ConfigService] Retrieved transcription service: assemblyai
console.ts:137 [Extension Host] [ConfigService] Retrieved transcription language: en
console.ts:137 [Extension Host] [ConfigService] Retrieved shouldOptimize setting: true
console.ts:137 [Extension Host] [ConfigService] Retrieved optimization model: claude-3.7-sonnet
console.ts:137 [Extension Host] [ConfigService] Retrieved custom prompt (length: 157)
console.ts:137 [Extension Host] [ConfigService] Retrieved translate setting: false
console.ts:137 [Extension Host] Sending options to webview: {service: 'assemblyai', model: 'best', language: 'en', optimize: true, optimizationModel: 'claude-3.7-sonnet', …}
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: updateOptions
main.cb59bbeb.chunk.js:1 Received updateOptions: {service: 'assemblyai', model: 'best', language: 'en', optimize: true, optimizationModel: 'claude-3.7-sonnet', …}
main.cb59bbeb.chunk.js:1 Current state before updateOptions:
main.cb59bbeb.chunk.js:1 Service: assemblyai, Model: best, Language: en
main.cb59bbeb.chunk.js:1 Optimize: true, Translate: false, Realtime: false
main.cb59bbeb.chunk.js:1 Skipping custom prompt update due to recent local update
main.cb59bbeb.chunk.js:1 No configuration changes applied
console.ts:137 [Extension Host] [ConfigService] Retrieved audio settings - sampleRate: 48000, device: 1 (type: string)
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: updateTranscriptions
main.cb59bbeb.chunk.js:1 WebView: Received 17 transcriptions from extension
main.cb59bbeb.chunk.js:1 WebView: Current transcriptions count before update: 17
main.cb59bbeb.chunk.js:1 WebView: Most recent transcription: {id: '1748694158443', timestamp: '2025-05-31T12:22:38.443Z', originalTextLength: 956, hasOptimized: true, service: 'assemblyai'}
console.ts:137 [Extension Host] [ConfigService] Returning device setting as string: "1"
main.cb59bbeb.chunk.js:1 Using light logo: https://file%2B.vscode-resource.vscode-cdn.net/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/media/voicehype_logo.png
console.ts:137 [Extension Host] VoiceHype: Sending audio settings to WebView: {sampleRate: 48000, device: '1'}
console.ts:137 [Extension Host] AudifyMicrophone: Constructor called with options: {}
main.cb59bbeb.chunk.js:1 WebView: Transcription state updated to 17 items
console.ts:137 [Extension Host] AudifyMicrophone: Initialized with options: {platform: 'linux', options: {…}, tempPath: '/tmp/voicehype_recording_temp.wav'}
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: updateAudioSettings
main.cb59bbeb.chunk.js:1 [RecordingControls] [DEBUG] Stop button clicked, current state: {isRecording: true, isPaused: false}
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Stop recording called, current state: {isRecording: true, isPaused: false}
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Optimistically updating UI state to stopped
main.cb59bbeb.chunk.js:1 Using light logo: https://file%2B.vscode-resource.vscode-cdn.net/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/media/voicehype_logo.png
console.ts:137 [Extension Host] AudifyMicrophone: Raw devices from RtAudio:
console.ts:137 [Extension Host]   [0] Default ALSA Device (Inputs: 10000, Outputs: 0)
console.ts:137 [Extension Host]   [1] PulseAudio Sound Server (Inputs: 32, Outputs: 32)
console.ts:137 [Extension Host]   [2] HDA Intel HDMI (HDMI 0) (Inputs: 0, Outputs: 8)
console.ts:137 [Extension Host]   [3] HDA Intel HDMI (HDMI 1) (Inputs: 0, Outputs: 8)
console.ts:137 [Extension Host]   [4] HDA Intel HDMI (HDMI 2) (Inputs: 0, Outputs: 8)
console.ts:137 [Extension Host]   [5] HDA Intel PCH (ALC3226 Analog) (Inputs: 2, Outputs: 2)
console.ts:137 [Extension Host] AudifyMicrophone: Processed device list:
console.ts:137 [Extension Host]   ID: default, Name: System Default
console.ts:137 [Extension Host]   ID: 0, Name: Default ALSA Device
console.ts:137 [Extension Host]   ID: 1, Name: PulseAudio Sound Server
console.ts:137 [Extension Host]   ID: 2, Name: HDA Intel PCH (ALC3226 Analog)
console.ts:137 [Extension Host] VoiceHype: Sending 4 audio devices to WebView with settings
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: updateAudioDevices
console.ts:137 [Extension Host] VoiceHype: Stopping recording from webview
main.cb59bbeb.chunk.js:1 Using light logo: https://file%2B.vscode-resource.vscode-cdn.net/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/media/voicehype_logo.png
console.ts:137 [Extension Host] [ConfigService] Retrieved shouldOptimize setting: true
console.ts:137 [Extension Host] VoiceHype: Stopping recording... (shouldOptimize: true )
console.ts:137 [Extension Host] VoiceHype: RECORDING STOP DETAILS:
console.ts:137 [Extension Host] VoiceHype: - Start time: 2025-06-02T01:02:33.297Z
console.ts:137 [Extension Host] VoiceHype: - Stop time: 2025-06-02T01:02:36.406Z
console.ts:137 [Extension Host] VoiceHype: - Total elapsed time: 3109ms (3.11s)
console.ts:137 [Extension Host] VoiceHype: - Total paused time: 0ms (0.00s)
console.ts:137 [Extension Host] VoiceHype: - Total active recording time: 3109ms (3.11s)
console.ts:137 [Extension Host] VoiceHype: - Was paused: false
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Emitting state change: isRecording=false, isPaused=false, elapsedTime=0ms
console.ts:137 [Extension Host] VoiceHype [DEBUG]: State change emitted from: Error: 
    at t.RecordingService.emitStateChange (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:178462)
    at t.RecordingService.stopTimer (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:178880)
    at t.RecordingService.stopRecording (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:166061)
    at ed.value (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:21298)
    at P.B (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at P.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at sV.$onMessage (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:145:90541)
    at f5.S (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:116111)
    at f5.Q (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:115891)
    at f5.M (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:114980)
    at f5.L (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:114218)
    at ed.value (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:112882)
    at P.B (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at P.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lo.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ed.value (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
    at P.B (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at P.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lo.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at MessagePortMain.<anonymous> (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:225:2209)
    at MessagePortMain.emit (node:events:524:28)
    at Object.MessagePortMain._internalPort.emit (node:electron/js2c/utility_init:2:2949)
    at Object.callbackTrampoline (node:internal/async_hooks:130:17)
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Updating WebView recording state: {isRecording: false, isPaused: false, elapsedTime: 0}
console.ts:137 [Extension Host] VoiceHype [DEBUG]: WebView update called from: Error: 
    at h._updateRecordingState (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:8294)
    at EventEmitter.<anonymous> (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:3940)
    at EventEmitter.emit (node:events:536:35)
    at t.RecordingService.emitStateChange (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:178504)
    at t.RecordingService.stopTimer (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:178880)
    at t.RecordingService.stopRecording (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:166061)
    at ed.value (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:21298)
    at P.B (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at P.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at sV.$onMessage (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:145:90541)
    at f5.S (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:116111)
    at f5.Q (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:115891)
    at f5.M (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:114980)
    at f5.L (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:114218)
    at ed.value (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:112882)
    at P.B (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at P.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lo.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ed.value (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
    at P.B (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at P.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lo.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at MessagePortMain.<anonymous> (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:225:2209)
    at MessagePortMain.emit (node:events:524:28)
    at Object.MessagePortMain._internalPort.emit (node:electron/js2c/utility_init:2:2949)
    at Object.callbackTrampoline (node:internal/async_hooks:130:17)
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: recordingState
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Received recordingState message: {command: 'recordingState', isRecording: false, isPaused: false, elapsedTime: 0}
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Current recording state before update: {isRecording: false, isPaused: false, elapsedTime: 3}
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating isRecording from false to false
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating isPaused from false to false
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating elapsedTime from 3 to 0
main.cb59bbeb.chunk.js:1 Using light logo: https://file%2B.vscode-resource.vscode-cdn.net/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/media/voicehype_logo.png
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Recording state change listener triggered: isRecording=false, isPaused=false
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Sending recording state to webview from listener
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: recordingState
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Emitting state change: isRecording=false, isPaused=false, elapsedTime=0ms
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Received recordingState message: {command: 'recordingState', isRecording: false, isPaused: false, elapsedTime: 0}
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Current recording state before update: {isRecording: false, isPaused: false, elapsedTime: 0}
console.ts:137 [Extension Host] VoiceHype [DEBUG]: State change emitted from: Error: 
    at t.RecordingService.emitStateChange (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:178462)
    at t.RecordingService.stopRecording (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:166078)
    at ed.value (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:21298)
    at P.B (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at P.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at sV.$onMessage (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:145:90541)
    at f5.S (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:116111)
    at f5.Q (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:115891)
    at f5.M (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:114980)
    at f5.L (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:114218)
    at ed.value (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:112882)
    at P.B (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at P.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lo.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ed.value (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
    at P.B (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at P.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lo.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at MessagePortMain.<anonymous> (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:225:2209)
    at MessagePortMain.emit (node:events:524:28)
    at Object.MessagePortMain._internalPort.emit (node:electron/js2c/utility_init:2:2949)
    at Object.callbackTrampoline (node:internal/async_hooks:130:17)
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating isRecording from false to false
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating isPaused from false to false
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating elapsedTime from 0 to 0
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Updating WebView recording state: {isRecording: false, isPaused: false, elapsedTime: 0}
console.ts:137 [Extension Host] VoiceHype [DEBUG]: WebView update called from: Error: 
    at h._updateRecordingState (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:8294)
    at EventEmitter.<anonymous> (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:3940)
    at EventEmitter.emit (node:events:536:35)
    at t.RecordingService.emitStateChange (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:178504)
    at t.RecordingService.stopRecording (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:166078)
    at ed.value (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:21298)
    at P.B (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at P.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at sV.$onMessage (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:145:90541)
    at f5.S (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:116111)
    at f5.Q (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:115891)
    at f5.M (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:114980)
    at f5.L (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:114218)
    at ed.value (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:112882)
    at P.B (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at P.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lo.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ed.value (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
    at P.B (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at P.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lo.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at MessagePortMain.<anonymous> (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:225:2209)
    at MessagePortMain.emit (node:events:524:28)
    at Object.MessagePortMain._internalPort.emit (node:electron/js2c/utility_init:2:2949)
    at Object.callbackTrampoline (node:internal/async_hooks:130:17)
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: recordingState
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Received recordingState message: {command: 'recordingState', isRecording: false, isPaused: false, elapsedTime: 0}
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Recording state change listener triggered: isRecording=false, isPaused=false
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Current recording state before update: {isRecording: false, isPaused: false, elapsedTime: 0}
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating isRecording from false to false
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating isPaused from false to false
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating elapsedTime from 0 to 0
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Sending recording state to webview from listener
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: recordingState
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Received recordingState message: {command: 'recordingState', isRecording: false, isPaused: false, elapsedTime: 0}
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Current recording state before update: {isRecording: false, isPaused: false, elapsedTime: 0}
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating isRecording from false to false
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating isPaused from false to false
main.cb59bbeb.chunk.js:1 [WebView] [DEBUG] Updating elapsedTime from 0 to 0
console.ts:137 [Extension Host] [ConfigService] Retrieved shouldOptimize setting: true
2console.ts:137 [Extension Host] [ConfigService] Retrieved transcription service: assemblyai
console.ts:137 [Extension Host] [ConfigService] Retrieved transcription language: en
console.ts:137 [Extension Host] [ConfigService] Retrieved optimization model: claude-3.7-sonnet
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Updating status bar items with: {shouldOptimize: true, service: 'assemblyai', model: 'best', language: 'en', isRecording: false, …}
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Status bar update called from: Error: 
    at t.StatusBarService.updateStatusBarItems (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:94914)
    at t.RecordingService.stopRecording (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:166249)
    at ed.value (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:21298)
    at P.B (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at P.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at sV.$onMessage (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:145:90541)
    at f5.S (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:116111)
    at f5.Q (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:115891)
    at f5.M (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:114980)
    at f5.L (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:114218)
    at ed.value (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:112882)
    at P.B (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at P.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lo.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ed.value (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
    at P.B (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at P.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lo.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at MessagePortMain.<anonymous> (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:225:2209)
    at MessagePortMain.emit (node:events:524:28)
    at Object.MessagePortMain._internalPort.emit (node:electron/js2c/utility_init:2:2949)
    at Object.callbackTrampoline (node:internal/async_hooks:130:17)
console.ts:137 [Extension Host] VoiceHype: Stopping audio stream
console.ts:137 [Extension Host] Microphone: stopRecording called
console.ts:137 [Extension Host] MicrophoneCompat: stopRecording called
console.ts:137 [Extension Host] AudifyMicrophone: stopRecording called
console.ts:137 [Extension Host] AudifyMicrophone: PassThrough stream ended
console.ts:137 [Extension Host] AudifyMicrophone: Recording statistics: {duration: '3.1 seconds', callbacks: 0, dataSize: '0 bytes', chunks: 0}
console.ts:137 [Extension Host] AudifyMicrophone: Recording stopped
console.ts:137 [Extension Host] VoiceHype: Closing write stream
console.ts:137 [Extension Host] [ConfigService] Retrieved translate setting: false
console.ts:137 [Extension Host] VoiceHype: Using translate setting: false
console.ts:137 [Extension Host] [ConfigService] Retrieved custom prompt (length: 157)
console.ts:137 [Extension Host] VoiceHype: Custom prompt for optimization: Rewrite the following transcript to make it sound formal and professional, correcting grammar and sentence structure, while preserving the speaker's message.
console.ts:137 [Extension Host] VoiceHype: Starting NON-REALTIME transcription with optimize = true translate = false
console.ts:137 [Extension Host] VoiceHype: Transcribing audio with optimize flag: true translate flag: false realtime flag: false
console.ts:137 [Extension Host] VoiceHype: Custom prompt provided: Rewrite the following transcript to make it sound formal and professional, correcting grammar and sentence structure, while preserving the speaker's message.
console.ts:137 [Extension Host] VoiceHype: Input file path: /tmp/voicehype_recording.wav
console.ts:137 [Extension Host] VoiceHype: Target recording path: /tmp/voicehype_recording.wav
console.ts:137 [Extension Host] [ConfigService] Retrieved transcription language: en
2console.ts:137 [Extension Host] [ConfigService] Retrieved transcription service: assemblyai
console.ts:137 [Extension Host] VoiceHype: Using model: best, service: assemblyai, language: en, translate: false, realtime: false
console.ts:137 [Extension Host] VoiceHype: Status update - initializing
console.ts:137 [Extension Host] VoiceHype: Using recording file directly: /tmp/voicehype_recording.wav
console.ts:137 [Extension Host] VoiceHype: Recording file size: 0 bytes
log.ts:460   ERR [Extension Host] VoiceHype: Transcription error: Recording file is empty or invalid. Please try recording again.
error @ log.ts:460
error @ log.ts:565
error @ logService.ts:51
wws @ remoteConsoleUtil.ts:58
$logExtensionHostMessage @ mainThreadConsole.ts:38
S @ rpcProtocol.ts:458
Q @ rpcProtocol.ts:443
M @ rpcProtocol.ts:373
L @ rpcProtocol.ts:299
(anonymous) @ rpcProtocol.ts:161
B @ event.ts:1208
fire @ event.ts:1239
fire @ ipc.net.ts:652
l.onmessage @ localProcessExtensionHost.ts:378
console.ts:137 [Extension Host] VoiceHype: Transcription error: Recording file is empty or invalid. Please try recording again.
bws @ console.ts:137
$logExtensionHostMessage @ mainThreadConsole.ts:39
S @ rpcProtocol.ts:458
Q @ rpcProtocol.ts:443
M @ rpcProtocol.ts:373
L @ rpcProtocol.ts:299
(anonymous) @ rpcProtocol.ts:161
B @ event.ts:1208
fire @ event.ts:1239
fire @ ipc.net.ts:652
l.onmessage @ localProcessExtensionHost.ts:378
console.ts:137 [Extension Host] VoiceHype: Write stream finished
console.ts:137 [Extension Host] VoiceHype: Recording file size: 0 bytes
log.ts:460   ERR [Extension Host] VoiceHype: Processing error: Error: Recording file is empty or invalid. Please try recording again.
    at t.TranscriptionService.transcribeAudio (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:240584)
    at /home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:171829
    at Tq.d (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:169:32425)
    at Tq.withProgress (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:169:32291)
    at Object.withProgress (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:170:41447)
    at t.RecordingService.processRecording (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:171496)
    at t.RecordingService.stopRecording (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:170394)
    at ed.value (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:21298)
    at P.B (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at P.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at sV.$onMessage (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:145:90541)
    at f5.S (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:116111)
    at f5.Q (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:115891)
    at f5.M (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:114980)
    at f5.L (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:114218)
    at ed.value (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:112882)
    at P.B (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at P.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lo.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ed.value (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
    at P.B (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at P.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lo.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at MessagePortMain.<anonymous> (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:225:2209)
    at MessagePortMain.emit (node:events:524:28)
    at Object.MessagePortMain._internalPort.emit (node:electron/js2c/utility_init:2:2949)
    at Object.callbackTrampoline (node:internal/async_hooks:130:17)
error @ log.ts:460
error @ log.ts:565
error @ logService.ts:51
wws @ remoteConsoleUtil.ts:58
$logExtensionHostMessage @ mainThreadConsole.ts:38
S @ rpcProtocol.ts:458
Q @ rpcProtocol.ts:443
M @ rpcProtocol.ts:373
L @ rpcProtocol.ts:299
(anonymous) @ rpcProtocol.ts:161
B @ event.ts:1208
fire @ event.ts:1239
fire @ ipc.net.ts:652
l.onmessage @ localProcessExtensionHost.ts:378
console.ts:137 [Extension Host] VoiceHype: Processing error: Error: Recording file is empty or invalid. Please try recording again.
    at t.TranscriptionService.transcribeAudio (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:240584)
    at /home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:171829
    at Tq.d (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:169:32425)
    at Tq.withProgress (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:169:32291)
    at Object.withProgress (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:170:41447)
    at t.RecordingService.processRecording (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:171496)
    at t.RecordingService.stopRecording (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:170394)
    at ed.value (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:21298)
    at P.B (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at P.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at sV.$onMessage (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:145:90541)
    at f5.S (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:116111)
    at f5.Q (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:115891)
    at f5.M (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:114980)
    at f5.L (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:114218)
    at ed.value (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:112882)
    at P.B (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at P.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lo.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ed.value (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
    at P.B (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at P.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lo.fire (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at MessagePortMain.<anonymous> (file:///opt/visual-studio-code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:225:2209)
    at MessagePortMain.emit (node:events:524:28)
    at Object.MessagePortMain._internalPort.emit (node:electron/js2c/utility_init:2:2949)
    at Object.callbackTrampoline (node:internal/async_hooks:130:17)
bws @ console.ts:137
$logExtensionHostMessage @ mainThreadConsole.ts:39
S @ rpcProtocol.ts:458
Q @ rpcProtocol.ts:443
M @ rpcProtocol.ts:373
L @ rpcProtocol.ts:299
(anonymous) @ rpcProtocol.ts:161
B @ event.ts:1208
fire @ event.ts:1239
fire @ ipc.net.ts:652
l.onmessage @ localProcessExtensionHost.ts:378
notificationsAlerts.ts:42 Transcription failed: Recording file is empty or invalid. Please try recording again.
c @ notificationsAlerts.ts:42
(anonymous) @ notificationsAlerts.ts:28
B @ event.ts:1208
C @ event.ts:1219
fire @ event.ts:1243
addNotification @ notifications.ts:228
notify @ notificationService.ts:255
(anonymous) @ mainThreadMessageService.ts:86
f @ mainThreadMessageService.ts:51
$showMessage @ mainThreadMessageService.ts:45
S @ rpcProtocol.ts:458
Q @ rpcProtocol.ts:443
M @ rpcProtocol.ts:373
L @ rpcProtocol.ts:299
(anonymous) @ rpcProtocol.ts:161
B @ event.ts:1208
fire @ event.ts:1239
fire @ ipc.net.ts:652
l.onmessage @ localProcessExtensionHost.ts:378
2console.ts:137 [Extension Host] [ConfigService] Retrieved transcription service: assemblyai
console.ts:137 [Extension Host] [ConfigService] Retrieved transcription language: en
console.ts:137 [Extension Host] [ConfigService] Retrieved shouldOptimize setting: true
console.ts:137 [Extension Host] [ConfigService] Retrieved optimization model: claude-3.7-sonnet
console.ts:137 [Extension Host] [ConfigService] Retrieved custom prompt (length: 157)
console.ts:137 [Extension Host] [ConfigService] Retrieved translate setting: false
console.ts:137 [Extension Host] Sending options to webview: {service: 'assemblyai', model: 'best', language: 'en', optimize: true, optimizationModel: 'claude-3.7-sonnet', …}
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: updateOptions
main.cb59bbeb.chunk.js:1 Received updateOptions: {service: 'assemblyai', model: 'best', language: 'en', optimize: true, optimizationModel: 'claude-3.7-sonnet', …}
main.cb59bbeb.chunk.js:1 Current state before updateOptions:
main.cb59bbeb.chunk.js:1 Service: assemblyai, Model: best, Language: en
main.cb59bbeb.chunk.js:1 Optimize: true, Translate: false, Realtime: false
main.cb59bbeb.chunk.js:1 Updating custom prompt (length: 157)
main.cb59bbeb.chunk.js:1 Configuration updated successfully
console.ts:137 [Extension Host] [ConfigService] Retrieved audio settings - sampleRate: 48000, device: 1 (type: string)
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: updateTranscriptions
main.cb59bbeb.chunk.js:1 WebView: Received 17 transcriptions from extension
console.ts:137 [Extension Host] [ConfigService] Returning device setting as string: "1"
main.cb59bbeb.chunk.js:1 WebView: Current transcriptions count before update: 17
main.cb59bbeb.chunk.js:1 WebView: Most recent transcription: {id: '1748694158443', timestamp: '2025-05-31T12:22:38.443Z', originalTextLength: 956, hasOptimized: true, service: 'assemblyai'}
main.cb59bbeb.chunk.js:1 Using light logo: https://file%2B.vscode-resource.vscode-cdn.net/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/media/voicehype_logo.png
console.ts:137 [Extension Host] VoiceHype: Sending audio settings to WebView: {sampleRate: 48000, device: '1'}
main.cb59bbeb.chunk.js:1 WebView: Transcription state updated to 17 items
console.ts:137 [Extension Host] AudifyMicrophone: Constructor called with options: {}
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: updateAudioSettings
localProcessExtensionHost.ts:275 Extension Host
localProcessExtensionHost.ts:276 ALSA lib pcm_dsnoop.c:541:(snd_pcm_dsnoop_open) The dsnoop plugin supports only capture streamRtApiAlsa::probeDeviceInfo: snd_pcm_open (playback) error for device (default), Invalid argument.ALSA lib pcm_dsnoop.c:541:(snd_pcm_dsnoop_open) The dsnoop plugin supports only capture streamRtApiAlsa::probeDeviceInfo: snd_pcm_open (playback) error for device (default), Invalid argument.
console.ts:137 [Extension Host] AudifyMicrophone: Initialized with options: {platform: 'linux', options: {…}, tempPath: '/tmp/voicehype_recording_temp.wav'}
console.ts:137 [Extension Host] AudifyMicrophone: Raw devices from RtAudio:
console.ts:137 [Extension Host]   [0] Default ALSA Device (Inputs: 10000, Outputs: 0)
console.ts:137 [Extension Host]   [1] PulseAudio Sound Server (Inputs: 32, Outputs: 32)
console.ts:137 [Extension Host]   [2] HDA Intel HDMI (HDMI 0) (Inputs: 0, Outputs: 8)
console.ts:137 [Extension Host]   [3] HDA Intel HDMI (HDMI 1) (Inputs: 0, Outputs: 8)
console.ts:137 [Extension Host]   [4] HDA Intel HDMI (HDMI 2) (Inputs: 0, Outputs: 8)
console.ts:137 [Extension Host]   [5] HDA Intel PCH (ALC3226 Analog) (Inputs: 2, Outputs: 2)
console.ts:137 [Extension Host] AudifyMicrophone: Processed device list:
console.ts:137 [Extension Host]   ID: default, Name: System Default
console.ts:137 [Extension Host]   ID: 0, Name: Default ALSA Device
console.ts:137 [Extension Host]   ID: 1, Name: PulseAudio Sound Server
console.ts:137 [Extension Host]   ID: 2, Name: HDA Intel PCH (ALC3226 Analog)
console.ts:137 [Extension Host] VoiceHype: Sending 4 audio devices to WebView with settings
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: updateAudioDevices
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Setting record button text to "Record" (was "Record")
main.cb59bbeb.chunk.js:1 Using light logo: https://file%2B.vscode-resource.vscode-cdn.net/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/media/voicehype_logo.png
console.ts:137 [Extension Host] [ConfigService] Retrieved shouldOptimize setting: true
2console.ts:137 [Extension Host] [ConfigService] Retrieved transcription service: assemblyai
console.ts:137 [Extension Host] [ConfigService] Retrieved transcription language: en
console.ts:137 [Extension Host] [ConfigService] Retrieved optimization model: claude-3.7-sonnet
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Updating status bar items with: {shouldOptimize: true, service: 'assemblyai', model: 'best', language: 'en', isRecording: false, …}
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Status bar update called from: Error: 
    at t.StatusBarService.updateStatusBarItems (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:94914)
    at ed.value (/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/dist/index.js:1:21547)
2console.ts:137 [Extension Host] [ConfigService] Retrieved transcription service: assemblyai
console.ts:137 [Extension Host] [ConfigService] Retrieved transcription language: en
console.ts:137 [Extension Host] [ConfigService] Retrieved shouldOptimize setting: true
console.ts:137 [Extension Host] [ConfigService] Retrieved optimization model: claude-3.7-sonnet
console.ts:137 [Extension Host] [ConfigService] Retrieved custom prompt (length: 157)
console.ts:137 [Extension Host] [ConfigService] Retrieved translate setting: false
console.ts:137 [Extension Host] Sending options to webview: {service: 'assemblyai', model: 'best', language: 'en', optimize: true, optimizationModel: 'claude-3.7-sonnet', …}
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: updateOptions
main.cb59bbeb.chunk.js:1 Received updateOptions: {service: 'assemblyai', model: 'best', language: 'en', optimize: true, optimizationModel: 'claude-3.7-sonnet', …}
main.cb59bbeb.chunk.js:1 Current state before updateOptions:
main.cb59bbeb.chunk.js:1 Service: assemblyai, Model: best, Language: en
main.cb59bbeb.chunk.js:1 Optimize: true, Translate: false, Realtime: false
main.cb59bbeb.chunk.js:1 Skipping custom prompt update due to recent local update
main.cb59bbeb.chunk.js:1 No configuration changes applied
console.ts:137 [Extension Host] VoiceHype [DEBUG]: Setting record button text to "Record" (was "Record")
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: updateTranscriptions
main.cb59bbeb.chunk.js:1 WebView: Received 17 transcriptions from extension
main.cb59bbeb.chunk.js:1 WebView: Current transcriptions count before update: 17
main.cb59bbeb.chunk.js:1 WebView: Most recent transcription: {id: '1748694158443', timestamp: '2025-05-31T12:22:38.443Z', originalTextLength: 956, hasOptimized: true, service: 'assemblyai'}
console.ts:137 [Extension Host] [ConfigService] Retrieved audio settings - sampleRate: 48000, device: 1 (type: string)
main.cb59bbeb.chunk.js:1 Using light logo: https://file%2B.vscode-resource.vscode-cdn.net/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/media/voicehype_logo.png
console.ts:137 [Extension Host] [ConfigService] Returning device setting as string: "1"
console.ts:137 [Extension Host] VoiceHype: Sending audio settings to WebView: {sampleRate: 48000, device: '1'}
main.cb59bbeb.chunk.js:1 WebView: Transcription state updated to 17 items
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: updateAudioSettings
console.ts:137 [Extension Host] AudifyMicrophone: Constructor called with options: {}
console.ts:137 [Extension Host] AudifyMicrophone: Initialized with options: {platform: 'linux', options: {…}, tempPath: '/tmp/voicehype_recording_temp.wav'}
console.ts:137 [Extension Host] AudifyMicrophone: Raw devices from RtAudio:
console.ts:137 [Extension Host]   [0] Default ALSA Device (Inputs: 10000, Outputs: 0)
console.ts:137 [Extension Host]   [1] PulseAudio Sound Server (Inputs: 32, Outputs: 32)
console.ts:137 [Extension Host]   [2] HDA Intel HDMI (HDMI 0) (Inputs: 0, Outputs: 8)
console.ts:137 [Extension Host]   [3] HDA Intel HDMI (HDMI 1) (Inputs: 0, Outputs: 8)
console.ts:137 [Extension Host]   [4] HDA Intel HDMI (HDMI 2) (Inputs: 0, Outputs: 8)
console.ts:137 [Extension Host]   [5] HDA Intel PCH (ALC3226 Analog) (Inputs: 2, Outputs: 2)
console.ts:137 [Extension Host] AudifyMicrophone: Processed device list:
console.ts:137 [Extension Host]   ID: default, Name: System Default
console.ts:137 [Extension Host]   ID: 0, Name: Default ALSA Device
console.ts:137 [Extension Host]   ID: 1, Name: PulseAudio Sound Server
console.ts:137 [Extension Host]   ID: 2, Name: HDA Intel PCH (ALC3226 Analog)
console.ts:137 [Extension Host] VoiceHype: Sending 4 audio devices to WebView with settings
main.cb59bbeb.chunk.js:1 WebView: Received message from extension: updateAudioDevices
main.cb59bbeb.chunk.js:1 Using light logo: https://file%2B.vscode-resource.vscode-cdn.net/home/<USER>/.vscode/extensions/voicehype.voicehype-1.1.6/media/voicehype_logo.png
localProcessExtensionHost.ts:275 Extension Host
localProcessExtensionHost.ts:276 ALSA lib pcm_dsnoop.c:541:(snd_pcm_dsnoop_open) The dsnoop plugin supports only capture streamRtApiAlsa::probeDeviceInfo: snd_pcm_open (playback) error for device (default), Invalid argument.ALSA lib pcm_dsnoop.c:541:(snd_pcm_dsnoop_open) The dsnoop plugin supports only capture streamRtApiAlsa::probeDeviceInfo: snd_pcm_open (playback) error for device (default), Invalid argument.ALSA lib pcm_dsnoop.c:541:(snd_pcm_dsnoop_open) The dsnoop plugin supports only capture streamRtApiAlsa::probeDeviceInfo: snd_pcm_open (playback) error for device (default), Invalid argument.ALSA lib pcm_dsnoop.c:541:(snd_pcm_dsnoop_open) The dsnoop plugin supports only capture streamRtApiAlsa::probeDeviceInfo: snd_pcm_open (playback) error for device (default), Invalid argument.
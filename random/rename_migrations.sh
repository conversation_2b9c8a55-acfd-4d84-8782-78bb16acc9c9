 #!/bin/bash

# Navigate to migrations directory
cd supabase/migrations

# Rename files with consistent pattern
mv 00000000000001_voicehype_schema.sql 20240101_001_voicehype_schema.sql
mv 00000000000002_pricing_model_updates.sql 20240228_001_pricing_model_updates.sql
mv 00000000000002_service_pricing.sql 20240228_002_service_pricing.sql
mv 00000000000003_pricing_history.sql 20240228_003_pricing_history.sql
mv 00000000000004_usage_functions.sql 20240228_004_usage_functions.sql
mv 20240228_update_payg_and_usage.sql 20240228_005_update_payg_and_usage.sql
mv 20240501_payg_payment_status.sql 20240501_001_payg_payment_status.sql
mv 20240502_consolidate_payg_payment_status.sql 20240502_001_consolidate_payg_payment_status.sql
mv 20240812_security_improvements.sql 20240812_001_security_improvements.sql
mv 20240813_fix_database_functions.sql 20240813_001_fix_database_functions.sql
mv 20240814_function_permissions.sql 20240814_001_function_permissions.sql

# List the renamed files
ls -la
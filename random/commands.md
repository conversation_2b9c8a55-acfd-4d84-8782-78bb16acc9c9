# Zipping, Building

& "C:\Program Files\7-Zip\7z.exe" a -tzip voicehype_archive.zip ` "c:\Users\<USER>\OneDrive\Documents\voicehype\*" ` -xr!"*\node_modules\*" ` -ir!"*\node_modules\audify\*"

tar --exclude=".git" --exclude=".gitignore" --exclude=".gitattributes" --exclude="node_modules" -czvf voicehype_archive.tar.gz -C /home/<USER>/Documents/cursor_extensions/voicehype .
zip -r voicehype_archive.zip . -x "*.git*" -x "*node_modules*" -x "*__pycache__*" -x "*venv*"

Set-ExecutionPolicy -Scope Process -ExecutionPolicy Bypass;

cd webview-ui; npm run build; cd ..; npm run compile; npx vsce package --allow-package-env-file

cd webview-ui && npm run build && cd .. && npm run compile && npx vsce package --allow-package-env-file

npx vsce package --allow-package-env-file

npx vsce publish --allow-package-env-file

# Backup Supabase

POSTGRES_CONTAINER=$(docker ps -qf "name=supabase-db")
docker exec $POSTGRES_CONTAINER pg_dump -U postgres -d postgres --schema-only > supabase_schema.sql

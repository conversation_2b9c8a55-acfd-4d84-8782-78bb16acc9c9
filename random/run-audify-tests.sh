#!/bin/bash

# Run the simple Audify test script
echo "========================================"
echo "Running simple Audify test script..."
echo "========================================"
cd "$(dirname "$0")/examples"
node audify-test.js

# Ask to continue with the VS Code extension test
echo ""
echo "========================================"
echo "Do you want to run the VS Code extension test? (y/n)"
read -p "> " run_extension_test

if [[ "$run_extension_test" == "y" ]]; then
  echo "========================================"
  echo "Running improved Audify implementation test..."
  echo "========================================"
  cd "$(dirname "$0")/extension"
  
  # Check if npx is available
  if command -v npx &> /dev/null; then
    npx ts-node src/test/test-improved-audify.ts
  else
    echo "Error: npx not found. Please install Node.js with npm."
    exit 1
  fi
fi

echo ""
echo "========================================"
echo "Tests completed. Check the output above for results."
echo "========================================"

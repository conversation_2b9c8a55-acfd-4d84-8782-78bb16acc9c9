{"dunning_sequence": {"description": "Complete dunning simulation sequence for VoiceHype subscription - Paddle format compliant", "notes": ["Use these payloads in order to simulate the full dunning process", "✅ Updated with actual IDs and Paddle transaction structure format", "✅ user_id: b8970ee6-6d52-4590-ba68-1ed7303b0f51", "✅ subscription_id: sub_01jz2wgvj061p07eadq0m756qf", "✅ customer_id: ctm_01jwehdjrg45n0b1vja3wnd74v", "Timestamps should be in chronological order"]}, "1_transaction_payment_failed": {"event_id": "evt_dunning_payment_failed_001", "event_type": "transaction.payment_failed", "occurred_at": "2025-07-01T10:00:00.000Z", "data": {"id": "txn_01hv8wptq8987qeep44cyrewp9", "items": [{"price": {"id": "pri_voicehype_pro_monthly", "name": "VoiceHype Pro Monthly", "type": "standard", "status": "active", "quantity": {"maximum": 1, "minimum": 1}, "tax_mode": "account_setting", "created_at": "2024-06-01T13:55:22.538367Z", "product_id": "pro_voicehype_subscription", "unit_price": {"amount": "1800", "currency_code": "USD"}, "updated_at": "2025-01-01T13:54:52.254748Z", "custom_data": null, "description": "VoiceHype Pro Monthly Subscription", "import_meta": null, "trial_period": null, "billing_cycle": {"interval": "month", "frequency": 1}, "unit_price_overrides": []}, "quantity": 1, "proration": null}], "origin": "subscription_recurring", "status": "payment_failed", "details": {"totals": {"fee": null, "tax": "0", "total": "1800", "credit": "0", "balance": "1800", "discount": "0", "earnings": null, "subtotal": "1800", "grand_total": "1800", "currency_code": "USD", "credit_to_balance": "0"}, "line_items": [{"id": "txnitm_voicehype_pro_001", "totals": {"tax": "0", "total": "1800", "discount": "0", "subtotal": "1800"}, "product": {"id": "pro_voicehype_subscription", "name": "VoiceHype Pro", "type": "standard", "status": "active", "image_url": "https://voicehype.ai/assets/pro-subscription.png", "created_at": "2024-06-01T12:43:46.605Z", "updated_at": "2025-01-01T15:53:44.687Z", "custom_data": {"features": {"transcription_quota": 10000, "optimization_quota": 1000, "advanced_features": true, "priority_support": true}, "plan_type": "pro"}, "description": "VoiceHype Pro subscription with advanced transcription and optimization features.", "import_meta": null, "tax_category": "standard"}, "price_id": "pri_voicehype_pro_monthly", "quantity": 1, "tax_rate": "0.00", "unit_totals": {"tax": "0", "total": "1800", "discount": "0", "subtotal": "1800"}}], "payout_totals": null, "tax_rates_used": [{"totals": {"tax": "0", "total": "1800", "discount": "0", "subtotal": "1800"}, "tax_rate": "0.00"}], "adjusted_totals": {"fee": "0", "tax": "0", "total": "1800", "earnings": "0", "subtotal": "1800", "grand_total": "1800", "currency_code": "USD"}}, "checkout": {"url": "https://voicehype.ai/pay?_ptxn=txn_01hv8wptq8987qeep44cyrewp9"}, "payments": [{"amount": "1800", "status": "error", "created_at": "2025-07-01T10:00:00.000Z", "error_code": "declined", "captured_at": null, "method_details": {"card": {"type": "visa", "last4": "0002", "expiry_year": 2025, "expiry_month": 12, "cardholder_name": "VoiceHype User"}, "type": "card"}, "payment_method_id": "paymtd_voicehype_001", "payment_attempt_id": "8f72cfa6-26b4-4a57-91dc-8f2708f7822d", "stored_payment_method_id": "a78ece50-356f-4e0c-b72d-ad5368b0a0d9"}], "billed_at": null, "address_id": "add_01hv8gq3318ktkfengj2r75gfx", "created_at": "2025-07-01T10:00:00.000Z", "invoice_id": null, "revised_at": null, "updated_at": "2025-07-01T10:00:00.000Z", "business_id": null, "custom_data": {"user_id": "b8970ee6-6d52-4590-ba68-1ed7303b0f51", "subscription_plan": "pro"}, "customer_id": "ctm_01jwehdjrg45n0b1vja3wnd74v", "discount_id": null, "currency_code": "USD", "billing_period": {"starts_at": "2025-07-01T00:00:00.000Z", "ends_at": "2025-08-01T00:00:00.000Z"}, "invoice_number": null, "billing_details": null, "collection_mode": "automatic", "subscription_id": "sub_01jz2wgvj061p07eadq0m756qf"}}, "2_subscription_past_due": {"event_id": "evt_dunning_sub_past_due_001", "event_type": "subscription.past_due", "occurred_at": "2025-07-01T10:30:00.000Z", "data": {"id": "sub_01jz2wgvj061p07eadq0m756qf", "status": "past_due", "customer_id": "ctm_01jwehdjrg45n0b1vja3wnd74v", "current_billing_period": {"starts_at": "2025-07-01T00:00:00.000Z", "ends_at": "2025-08-01T00:00:00.000Z"}, "next_billed_at": "2025-08-01T00:00:00.000Z", "collection_mode": "automatic", "custom_data": {"user_id": "b8970ee6-6d52-4590-ba68-1ed7303b0f51", "subscription_plan": "pro"}, "items": [{"price": {"id": "pri_voicehype_pro_monthly", "name": "VoiceHype Pro Monthly", "type": "standard", "status": "active", "quantity": {"maximum": 1, "minimum": 1}, "tax_mode": "account_setting", "created_at": "2024-06-01T13:55:22.538367Z", "product_id": "pro_voicehype_subscription", "unit_price": {"amount": "1800", "currency_code": "USD"}, "updated_at": "2025-01-01T13:54:52.254748Z", "custom_data": null, "description": "VoiceHype Pro Monthly Subscription", "import_meta": null, "trial_period": null, "billing_cycle": {"interval": "month", "frequency": 1}, "unit_price_overrides": []}, "quantity": 1, "proration": null}]}}, "3_transaction_past_due": {"event_id": "evt_dunning_txn_past_due_001", "event_type": "transaction.past_due", "occurred_at": "2025-07-01T11:00:00.000Z", "data": {"id": "txn_01hv8wptq8987qeep44cyrewp9", "items": [{"price": {"id": "pri_voicehype_pro_monthly", "name": "VoiceHype Pro Monthly", "type": "standard", "status": "active", "quantity": {"maximum": 1, "minimum": 1}, "tax_mode": "account_setting", "created_at": "2024-06-01T13:55:22.538367Z", "product_id": "pro_voicehype_subscription", "unit_price": {"amount": "1800", "currency_code": "USD"}, "updated_at": "2025-01-01T13:54:52.254748Z", "custom_data": null, "description": "VoiceHype Pro Monthly Subscription", "import_meta": null, "trial_period": null, "billing_cycle": {"interval": "month", "frequency": 1}, "unit_price_overrides": []}, "quantity": 1, "proration": null}], "origin": "subscription_recurring", "status": "past_due", "details": {"totals": {"fee": null, "tax": "0", "total": "1800", "credit": "0", "balance": "1800", "discount": "0", "earnings": null, "subtotal": "1800", "grand_total": "1800", "currency_code": "USD", "credit_to_balance": "0"}}, "billed_at": null, "address_id": "add_01hv8gq3318ktkfengj2r75gfx", "created_at": "2025-07-01T11:00:00.000Z", "invoice_id": null, "revised_at": null, "updated_at": "2025-07-01T11:00:00.000Z", "business_id": null, "custom_data": {"user_id": "b8970ee6-6d52-4590-ba68-1ed7303b0f51", "subscription_plan": "pro"}, "customer_id": "ctm_01jwehdjrg45n0b1vja3wnd74v", "discount_id": null, "currency_code": "USD", "billing_period": {"starts_at": "2025-07-01T00:00:00.000Z", "ends_at": "2025-08-01T00:00:00.000Z"}, "invoice_number": null, "billing_details": null, "collection_mode": "automatic", "subscription_id": "sub_01jz2wgvj061p07eadq0m756qf"}}, "4a_subscription_activated_success": {"event_id": "evt_dunning_recovery_success_001", "event_type": "subscription.activated", "occurred_at": "2025-07-03T09:00:00.000Z", "data": {"id": "sub_01jz2wgvj061p07eadq0m756qf", "status": "active", "customer_id": "ctm_01jwehdjrg45n0b1vja3wnd74v", "current_billing_period": {"starts_at": "2025-07-01T00:00:00.000Z", "ends_at": "2025-08-01T00:00:00.000Z"}, "next_billed_at": "2025-08-01T00:00:00.000Z", "collection_mode": "automatic", "custom_data": {"user_id": "b8970ee6-6d52-4590-ba68-1ed7303b0f51", "subscription_plan": "pro"}}}, "4b_subscription_canceled_failure": {"event_id": "evt_dunning_final_cancel_001", "event_type": "subscription.canceled", "occurred_at": "2025-07-08T00:00:00.000Z", "data": {"id": "sub_01jz2wgvj061p07eadq0m756qf", "status": "canceled", "customer_id": "ctm_01jwehdjrg45n0b1vja3wnd74v", "canceled_at": "2025-07-08T00:00:00.000Z", "current_billing_period": {"starts_at": "2025-07-01T00:00:00.000Z", "ends_at": "2025-08-01T00:00:00.000Z"}, "collection_mode": "automatic", "custom_data": {"user_id": "b8970ee6-6d52-4590-ba68-1ed7303b0f51", "subscription_plan": "pro"}}}, "credit_purchase_example": {"event_id": "evt_credit_purchase_001", "event_type": "transaction.completed", "occurred_at": "2025-07-01T14:00:00.000Z", "data": {"id": "txn_credit_purchase_001", "status": "completed", "customer_id": "ctm_01jwehdjrg45n0b1vja3wnd74v", "origin": "web", "collection_mode": "automatic", "currency_code": "USD", "details": {"totals": {"total": "2000", "currency_code": "USD", "subtotal": "2000", "tax": "0", "discount": "0"}}, "items": [{"product_id": "pro_voicehype_credits", "quantity": 1}], "custom_data": {"user_id": "b8970ee6-6d52-4590-ba68-1ed7303b0f51", "credit_amount": "20.00"}, "receipt_url": "https://checkout.paddle.com/receipt/txn_credit_purchase_001"}}, "subscription_transaction_example": {"event_id": "evt_subscription_txn_001", "event_type": "transaction.completed", "occurred_at": "2025-07-01T16:00:00.000Z", "data": {"id": "txn_subscription_001", "status": "completed", "customer_id": "ctm_01jwehdjrg45n0b1vja3wnd74v", "subscription_id": "sub_01jz2wgvj061p07eadq0m756qf", "origin": "subscription_recurring", "collection_mode": "automatic", "currency_code": "USD", "billing_period": {"starts_at": "2025-07-01T00:00:00.000Z", "ends_at": "2025-08-01T00:00:00.000Z"}, "details": {"totals": {"total": "1800", "currency_code": "USD", "subtotal": "1800", "tax": "0", "discount": "0"}}, "items": [{"product_id": "pro_voicehype_subscription", "quantity": 1}], "custom_data": {"user_id": "b8970ee6-6d52-4590-ba68-1ed7303b0f51", "subscription_plan": "pro"}, "receipt_url": "https://checkout.paddle.com/receipt/txn_subscription_001"}}}
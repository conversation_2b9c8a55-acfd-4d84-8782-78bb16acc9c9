# Audify.js Recording Testing Guide

This guide explains how to test and troubleshoot audio recording with Audify.js in VoiceHype.

## Test Scripts

We've created two test scripts to help diagnose audio recording issues:

1. **Simple Node.js Test**: Tests basic Audify functionality outside the VS Code extension
2. **Improved AudifyMicrophone Test**: Tests the improved implementation within the extension

## Running the Tests

### Prerequisites

1. Make sure you have Node.js installed (version 14+)
2. Install dependencies in both directories:

```bash
cd examples
npm install

cd ../extension
npm install
```

### Option 1: Run Both Tests with the Script

We've created a bash script that runs both tests:

```bash
./run-audify-tests.sh
```

### Option 2: Run Tests Individually

#### Simple Node.js Test

```bash
cd examples
npm run test:audify
```

#### Improved AudifyMicrophone Test

```bash
cd extension
npx ts-node src/test/test-improved-audify.ts
```

## What to Look For

### 1. Audio Callbacks

The most important thing to check is whether the audio callback is being executed. In the logs, look for lines like:

```
Callback #30, Data: 3840 bytes, Total: 115200 bytes, Time: 1.5s
```

If you see these messages increasing, the audio system is working correctly. If not, there might be an issue with the audio device or configuration.

### 2. Final Statistics

At the end of each test, statistics will be displayed:

```
Recording stats: {
  isRecording: false,
  isPaused: false,
  duration: '5.0s',
  callbacks: 150,
  dataSize: 576000,
  chunks: 150
}
```

A successful recording should have:
- Multiple callbacks
- Non-zero data size
- Audio file created with size > 44 bytes

## Troubleshooting

If the test fails, check the following:

1. **No callbacks**: Your audio device may not be correctly identified
   - Try a different device ID in the test scripts
   - Check your system audio settings

2. **Callbacks but no data**: The audio format or frame size may be incompatible
   - Try changing the frame size (e.g., 960, 1920, 2048)
   - Try a different audio format (e.g., RTAUDIO_SINT16 vs RTAUDIO_FLOAT32)

3. **System permissions**: The application might not have permission to access the microphone
   - Run with elevated permissions for testing

## Technical Details

The main improvements made to the implementation are:

1. Hardcoded device ID (1) to simplify device selection
2. Fixed frame size to match the example (1920)
3. Enhanced logging of audio callbacks and data flow
4. Better error handling with specific cleanup procedures
5. Added recording statistics for easier debugging

See the full analysis in `docs/audify-implementation-analysis.md`.

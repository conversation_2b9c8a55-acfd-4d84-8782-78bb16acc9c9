# VS Code Auth Flow Test

## Problem Fixed
The VS Code authentication flow was redirecting users to the dashboard instead of showing the authorization screen after OAuth sign-in.

## Solution Implemented
1. Modified OAuth redirects in `VSCodeAuthView.vue` to use `/auth/callback?vscode_auth=true`
2. Modified `AuthCallbackView.vue` to detect VS Code auth flow and redirect back to `/vscode-auth`

## Test URLs

### Test VS Code Auth Flow
```
https://voicehype.ai/vscode-auth?state=test123&redirect_uri=vscode%3A//voicehype.voicehype/auth-callback&product_name=Visual%20Studio%20Code&uri_scheme=vscode
```

### Expected Flow
1. User visits VS Code auth URL above
2. User clicks "Continue with Google" or "Continue with GitHub"
3. OAuth redirects to: `/auth/callback?vscode_auth=true&state=test123&redirect_uri=vscode%3A//voicehype.voicehype/auth-callback&product_name=Visual%20Studio%20Code&uri_scheme=vscode`
4. AuthCallbackView detects `vscode_auth=true` and redirects back to: `/vscode-auth?state=test123&redirect_uri=vscode%3A//voicehype.voicehype/auth-callback&product_name=Visual%20Studio%20Code&uri_scheme=vscode`
5. VSCodeAuthView shows authorization screen with "Authorize Extension" and "Deny Access" buttons
6. User can authorize the extension

## Files Modified
- `voicehype-website/src/views/VSCodeAuthView.vue`
- `voicehype-website/src/views/AuthCallbackView.vue`

## Key Changes
- Added `vscode_auth=true` parameter to OAuth redirects
- Added VS Code flow detection in auth callback
- Preserved all original parameters through the OAuth flow

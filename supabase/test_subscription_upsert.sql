-- Test script for subscription UPSERT functionality
-- Created: 2025-06-27
-- Purpose: Verify the handle_subscription_transaction function works correctly

-- Test 1: Create a new subscription
SELECT 'Test 1: Creating new subscription for user test-user-1' as test_description;

SELECT handle_subscription_transaction(
    'test-user-1'::uuid,
    'sub_test_123',
    'cus_test_456',
    'basic',
    '{"test": true}'::jsonb
) as result_1;

-- Verify the subscription was created
SELECT 'Verifying user_subscriptions table:' as check_description;
SELECT user_id, plan_id, status, stripe_subscription_id 
FROM user_subscriptions 
WHERE user_id = 'test-user-1'::uuid;

-- Verify quotas were created
SELECT 'Verifying quotas table:' as check_description;
SELECT user_id, service, total_amount, used_amount 
FROM quotas 
WHERE user_id = 'test-user-1'::uuid
ORDER BY service;

-- Test 2: Update the same user's subscription (should UPSERT, not duplicate)
SELECT 'Test 2: Updating subscription to Pro plan' as test_description;

SELECT handle_subscription_transaction(
    'test-user-1'::uuid,
    'sub_test_789',
    'cus_test_456',
    'pro',
    '{"test": true, "updated": true}'::jsonb
) as result_2;

-- Verify only one subscription exists
SELECT 'Verifying no duplicates in user_subscriptions:' as check_description;
SELECT COUNT(*) as subscription_count
FROM user_subscriptions 
WHERE user_id = 'test-user-1'::uuid;

-- Verify quotas were updated (not duplicated)
SELECT 'Verifying quotas were updated (not duplicated):' as check_description;
SELECT service, COUNT(*) as quota_count, MAX(total_amount) as total_amount
FROM quotas 
WHERE user_id = 'test-user-1'::uuid
GROUP BY service
ORDER BY service;

-- Test 3: Test subscription cancellation
SELECT 'Test 3: Testing subscription cancellation' as test_description;

SELECT handle_subscription_cancellation(
    'sub_test_789',
    'test-user-1'::uuid
) as result_3;

-- Verify subscription was marked as canceled
SELECT 'Verifying subscription cancellation:' as check_description;
SELECT user_id, status, stripe_subscription_id 
FROM user_subscriptions 
WHERE user_id = 'test-user-1'::uuid;

-- Test 4: Test with invalid plan
SELECT 'Test 4: Testing with invalid plan (should fail gracefully)' as test_description;

SELECT handle_subscription_transaction(
    'test-user-2'::uuid,
    'sub_test_invalid',
    'cus_test_invalid',
    'invalid_plan',
    '{"test": true}'::jsonb
) as result_4;

-- Clean up test data
DELETE FROM quotas WHERE user_id IN ('test-user-1'::uuid, 'test-user-2'::uuid);
DELETE FROM user_subscriptions WHERE user_id IN ('test-user-1'::uuid, 'test-user-2'::uuid);

-- Clean up paddle.subscriptions if table exists
DO $$
BEGIN
    DELETE FROM paddle.subscriptions WHERE user_id IN ('test-user-1'::uuid, 'test-user-2'::uuid);
EXCEPTION
    WHEN undefined_table THEN
        RAISE NOTICE 'paddle.subscriptions table does not exist, skipping cleanup';
END $$;

SELECT 'All tests completed and test data cleaned up' as completion_message;

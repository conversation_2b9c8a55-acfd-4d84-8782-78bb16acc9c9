--
-- PostgreSQL database dump
--

-- Dumped from database version 15.8
-- Dumped by pg_dump version 15.8

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: _realtime; Type: SCHEMA; Schema: -; Owner: supabase_admin
--

CREATE SCHEMA _realtime;


ALTER SCHEMA _realtime OWNER TO supabase_admin;

--
-- Name: auth; Type: SCHEMA; Schema: -; Owner: supabase_admin
--

CREATE SCHEMA auth;


ALTER SCHEMA auth OWNER TO supabase_admin;

--
-- Name: blog; Type: SCHEMA; Schema: -; Owner: supabase_admin
--

CREATE SCHEMA blog;


ALTER SCHEMA blog OWNER TO supabase_admin;

--
-- Name: pg_cron; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pg_cron WITH SCHEMA pg_catalog;


--
-- Name: EXTENSION pg_cron; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pg_cron IS 'Job scheduler for PostgreSQL';


--
-- Name: extensions; Type: SCHEMA; Schema: -; Owner: postgres
--

CREATE SCHEMA extensions;


ALTER SCHEMA extensions OWNER TO postgres;

--
-- Name: graphql; Type: SCHEMA; Schema: -; Owner: supabase_admin
--

CREATE SCHEMA graphql;


ALTER SCHEMA graphql OWNER TO supabase_admin;

--
-- Name: graphql_public; Type: SCHEMA; Schema: -; Owner: supabase_admin
--

CREATE SCHEMA graphql_public;


ALTER SCHEMA graphql_public OWNER TO supabase_admin;

--
-- Name: internal; Type: SCHEMA; Schema: -; Owner: postgres
--

CREATE SCHEMA internal;


ALTER SCHEMA internal OWNER TO postgres;

--
-- Name: pg_net; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pg_net WITH SCHEMA extensions;


--
-- Name: EXTENSION pg_net; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pg_net IS 'Async HTTP';


--
-- Name: pgbouncer; Type: SCHEMA; Schema: -; Owner: pgbouncer
--

CREATE SCHEMA pgbouncer;


ALTER SCHEMA pgbouncer OWNER TO pgbouncer;

--
-- Name: pgsodium; Type: SCHEMA; Schema: -; Owner: supabase_admin
--

CREATE SCHEMA pgsodium;


ALTER SCHEMA pgsodium OWNER TO supabase_admin;

--
-- Name: pgsodium; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pgsodium WITH SCHEMA pgsodium;


--
-- Name: EXTENSION pgsodium; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pgsodium IS 'Pgsodium is a modern cryptography library for Postgres.';


--
-- Name: realtime; Type: SCHEMA; Schema: -; Owner: supabase_admin
--

CREATE SCHEMA realtime;


ALTER SCHEMA realtime OWNER TO supabase_admin;

--
-- Name: storage; Type: SCHEMA; Schema: -; Owner: supabase_admin
--

CREATE SCHEMA storage;


ALTER SCHEMA storage OWNER TO supabase_admin;

--
-- Name: supabase_functions; Type: SCHEMA; Schema: -; Owner: supabase_admin
--

CREATE SCHEMA supabase_functions;


ALTER SCHEMA supabase_functions OWNER TO supabase_admin;

--
-- Name: vault; Type: SCHEMA; Schema: -; Owner: supabase_admin
--

CREATE SCHEMA vault;


ALTER SCHEMA vault OWNER TO supabase_admin;

--
-- Name: hypopg; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS hypopg WITH SCHEMA extensions;


--
-- Name: EXTENSION hypopg; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION hypopg IS 'Hypothetical indexes for PostgreSQL';


--
-- Name: index_advisor; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS index_advisor WITH SCHEMA extensions;


--
-- Name: EXTENSION index_advisor; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION index_advisor IS 'Query index advisor';


--
-- Name: pg_graphql; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pg_graphql WITH SCHEMA graphql;


--
-- Name: EXTENSION pg_graphql; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pg_graphql IS 'pg_graphql: GraphQL support';


--
-- Name: pg_stat_statements; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pg_stat_statements WITH SCHEMA extensions;


--
-- Name: EXTENSION pg_stat_statements; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pg_stat_statements IS 'track planning and execution statistics of all SQL statements executed';


--
-- Name: pgcrypto; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pgcrypto WITH SCHEMA extensions;


--
-- Name: EXTENSION pgcrypto; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pgcrypto IS 'cryptographic functions';


--
-- Name: pgjwt; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pgjwt WITH SCHEMA extensions;


--
-- Name: EXTENSION pgjwt; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pgjwt IS 'JSON Web Token API for Postgresql';


--
-- Name: supabase_vault; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS supabase_vault WITH SCHEMA vault;


--
-- Name: EXTENSION supabase_vault; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION supabase_vault IS 'Supabase Vault Extension';


--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA extensions;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


--
-- Name: wrappers; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS wrappers WITH SCHEMA extensions;


--
-- Name: EXTENSION wrappers; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION wrappers IS 'Foreign data wrappers developed by Supabase';


--
-- Name: aal_level; Type: TYPE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TYPE auth.aal_level AS ENUM (
    'aal1',
    'aal2',
    'aal3'
);


ALTER TYPE auth.aal_level OWNER TO supabase_auth_admin;

--
-- Name: code_challenge_method; Type: TYPE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TYPE auth.code_challenge_method AS ENUM (
    's256',
    'plain'
);


ALTER TYPE auth.code_challenge_method OWNER TO supabase_auth_admin;

--
-- Name: factor_status; Type: TYPE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TYPE auth.factor_status AS ENUM (
    'unverified',
    'verified'
);


ALTER TYPE auth.factor_status OWNER TO supabase_auth_admin;

--
-- Name: factor_type; Type: TYPE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TYPE auth.factor_type AS ENUM (
    'totp',
    'webauthn',
    'phone'
);


ALTER TYPE auth.factor_type OWNER TO supabase_auth_admin;

--
-- Name: one_time_token_type; Type: TYPE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TYPE auth.one_time_token_type AS ENUM (
    'confirmation_token',
    'reauthentication_token',
    'recovery_token',
    'email_change_token_new',
    'email_change_token_current',
    'phone_change_token'
);


ALTER TYPE auth.one_time_token_type OWNER TO supabase_auth_admin;

--
-- Name: api_key_result; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.api_key_result AS (
	id uuid,
	key_secret text
);


ALTER TYPE public.api_key_result OWNER TO postgres;

--
-- Name: payment_status; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.payment_status AS ENUM (
    'pending',
    'paid',
    'unpaid'
);


ALTER TYPE public.payment_status OWNER TO postgres;

--
-- Name: action; Type: TYPE; Schema: realtime; Owner: supabase_admin
--

CREATE TYPE realtime.action AS ENUM (
    'INSERT',
    'UPDATE',
    'DELETE',
    'TRUNCATE',
    'ERROR'
);


ALTER TYPE realtime.action OWNER TO supabase_admin;

--
-- Name: equality_op; Type: TYPE; Schema: realtime; Owner: supabase_admin
--

CREATE TYPE realtime.equality_op AS ENUM (
    'eq',
    'neq',
    'lt',
    'lte',
    'gt',
    'gte',
    'in'
);


ALTER TYPE realtime.equality_op OWNER TO supabase_admin;

--
-- Name: user_defined_filter; Type: TYPE; Schema: realtime; Owner: supabase_admin
--

CREATE TYPE realtime.user_defined_filter AS (
	column_name text,
	op realtime.equality_op,
	value text
);


ALTER TYPE realtime.user_defined_filter OWNER TO supabase_admin;

--
-- Name: wal_column; Type: TYPE; Schema: realtime; Owner: supabase_admin
--

CREATE TYPE realtime.wal_column AS (
	name text,
	type_name text,
	type_oid oid,
	value jsonb,
	is_pkey boolean,
	is_selectable boolean
);


ALTER TYPE realtime.wal_column OWNER TO supabase_admin;

--
-- Name: wal_rls; Type: TYPE; Schema: realtime; Owner: supabase_admin
--

CREATE TYPE realtime.wal_rls AS (
	wal jsonb,
	is_rls_enabled boolean,
	subscription_ids uuid[],
	errors text[]
);


ALTER TYPE realtime.wal_rls OWNER TO supabase_admin;

--
-- Name: email(); Type: FUNCTION; Schema: auth; Owner: supabase_auth_admin
--

CREATE FUNCTION auth.email() RETURNS text
    LANGUAGE sql STABLE
    AS $$
  select 
  coalesce(
    nullif(current_setting('request.jwt.claim.email', true), ''),
    (nullif(current_setting('request.jwt.claims', true), '')::jsonb ->> 'email')
  )::text
$$;


ALTER FUNCTION auth.email() OWNER TO supabase_auth_admin;

--
-- Name: FUNCTION email(); Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON FUNCTION auth.email() IS 'Deprecated. Use auth.jwt() -> ''email'' instead.';


--
-- Name: jwt(); Type: FUNCTION; Schema: auth; Owner: supabase_auth_admin
--

CREATE FUNCTION auth.jwt() RETURNS jsonb
    LANGUAGE sql STABLE
    AS $$
  select 
    coalesce(
        nullif(current_setting('request.jwt.claim', true), ''),
        nullif(current_setting('request.jwt.claims', true), '')
    )::jsonb
$$;


ALTER FUNCTION auth.jwt() OWNER TO supabase_auth_admin;

--
-- Name: role(); Type: FUNCTION; Schema: auth; Owner: supabase_auth_admin
--

CREATE FUNCTION auth.role() RETURNS text
    LANGUAGE sql STABLE
    AS $$
  select 
  coalesce(
    nullif(current_setting('request.jwt.claim.role', true), ''),
    (nullif(current_setting('request.jwt.claims', true), '')::jsonb ->> 'role')
  )::text
$$;


ALTER FUNCTION auth.role() OWNER TO supabase_auth_admin;

--
-- Name: FUNCTION role(); Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON FUNCTION auth.role() IS 'Deprecated. Use auth.jwt() -> ''role'' instead.';


--
-- Name: uid(); Type: FUNCTION; Schema: auth; Owner: supabase_auth_admin
--

CREATE FUNCTION auth.uid() RETURNS uuid
    LANGUAGE sql STABLE
    AS $$
  select 
  coalesce(
    nullif(current_setting('request.jwt.claim.sub', true), ''),
    (nullif(current_setting('request.jwt.claims', true), '')::jsonb ->> 'sub')
  )::uuid
$$;


ALTER FUNCTION auth.uid() OWNER TO supabase_auth_admin;

--
-- Name: FUNCTION uid(); Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON FUNCTION auth.uid() IS 'Deprecated. Use auth.jwt() -> ''sub'' instead.';


--
-- Name: get_article_with_view_increment(text); Type: FUNCTION; Schema: blog; Owner: supabase_admin
--

CREATE FUNCTION blog.get_article_with_view_increment(slug_param text) RETURNS TABLE(id uuid, title text, slug text, content text, excerpt text, cover_image_url text, author_id uuid, status text, published_at timestamp with time zone, created_at timestamp with time zone, updated_at timestamp with time zone, meta_title text, meta_description text, featured boolean, view_count integer, author_username text, author_name text, author_avatar text, author_bio text)
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
declare
  article_record record;
  current_view_count integer;
begin
  -- Get the article with author info (ONLY PUBLISHED ARTICLES)
  select 
    a.id,
    a.title,
    a.slug,
    a.content,
    a.excerpt,
    a.cover_image_url,
    a.author_id,
    a.status,
    a.published_at,
    a.created_at,
    a.updated_at,
    a.meta_title,
    a.meta_description,
    a.featured,
    p.username as author_username,
    p.full_name as author_name,
    p.avatar_url as author_avatar,
    p.bio as author_bio
  into article_record
  from blog.articles a
  join blog.profiles p on a.author_id = p.id
  where a.slug = slug_param 
    and a.status = 'published'
    and a.published_at IS NOT NULL;
  
  -- If article exists, increment view count
  if article_record.id is not null then
    -- Upsert view count
    insert into blog.article_views (article_id, view_count, updated_at)
    values (article_record.id, 1, now())
    on conflict (article_id)
    do update set 
      view_count = blog.article_views.view_count + 1,
      updated_at = now();
    
    -- Get current view count
    select coalesce(av.view_count, 0) into current_view_count
    from blog.article_views av
    where av.article_id = article_record.id;
    
    -- Return the article data with view count
    return query select 
      article_record.id,
      article_record.title,
      article_record.slug,
      article_record.content,
      article_record.excerpt,
      article_record.cover_image_url,
      article_record.author_id,
      article_record.status,
      article_record.published_at,
      article_record.created_at,
      article_record.updated_at,
      article_record.meta_title,
      article_record.meta_description,
      article_record.featured,
      current_view_count,
      article_record.author_username,
      article_record.author_name,
      article_record.author_avatar,
      article_record.author_bio;
  end if;
end;
$$;


ALTER FUNCTION blog.get_article_with_view_increment(slug_param text) OWNER TO supabase_admin;

--
-- Name: get_published_articles(integer, integer, text); Type: FUNCTION; Schema: blog; Owner: supabase_admin
--

CREATE FUNCTION blog.get_published_articles(limit_count integer DEFAULT 10, offset_count integer DEFAULT 0, order_by text DEFAULT 'published_at'::text) RETURNS TABLE(id uuid, title text, slug text, excerpt text, cover_image_url text, author_id uuid, published_at timestamp with time zone, created_at timestamp with time zone, meta_title text, meta_description text, featured boolean, view_count integer, author_username text, author_name text, author_avatar text)
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
begin
    return query
    select 
        a.id,
        a.title,
        a.slug,
        a.excerpt,
        a.cover_image_url,
        a.author_id,
        a.published_at,
        a.created_at,
        a.meta_title,
        a.meta_description,
        a.featured,
        coalesce(av.view_count, 0) as view_count,
        p.username as author_username,
        p.full_name as author_name,
        p.avatar_url as author_avatar
    from blog.articles a
    join blog.profiles p on a.author_id = p.id
    left join blog.article_views av on a.id = av.article_id
    where a.status = 'published' 
        and a.published_at IS NOT NULL
    order by
        case
            when order_by = 'published_at' then a.published_at
            when order_by = 'created_at' then a.created_at
            else a.published_at
        end desc,
        case
            when order_by = 'view_count' then coalesce(av.view_count, 0)
            else 0
        end desc
    limit limit_count
    offset offset_count;
end;
$$;


ALTER FUNCTION blog.get_published_articles(limit_count integer, offset_count integer, order_by text) OWNER TO supabase_admin;

--
-- Name: search_published_articles(text); Type: FUNCTION; Schema: blog; Owner: supabase_admin
--

CREATE FUNCTION blog.search_published_articles(search_term text) RETURNS TABLE(id uuid, title text, slug text, excerpt text, cover_image_url text, author_id uuid, published_at timestamp with time zone, created_at timestamp with time zone, updated_at timestamp with time zone, meta_title text, meta_description text, featured boolean, view_count integer, author_username text, author_name text, author_avatar text)
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
    RETURN QUERY
    SELECT 
        a.id,
        a.title,
        a.slug,
        a.excerpt,
        a.cover_image_url,
        a.author_id,
        a.published_at,
        a.created_at,
        a.updated_at,
        a.meta_title,
        a.meta_description,
        a.featured,
        COALESCE(av.view_count, 0) AS view_count,
        p.username AS author_username,
        p.full_name AS author_name,
        p.avatar_url AS author_avatar
    FROM blog.articles a
    JOIN blog.profiles p ON a.author_id = p.id
    LEFT JOIN blog.article_views av ON a.id = av.article_id
    WHERE a.status = 'published'
      AND a.published_at IS NOT NULL
      AND (
          a.title ILIKE '%' || TRIM(BOTH '%' FROM search_term) || '%'
          OR a.excerpt ILIKE '%' || TRIM(BOTH '%' FROM search_term) || '%'
          OR a.content ILIKE '%' || TRIM(BOTH '%' FROM search_term) || '%'
      )
    ORDER BY a.published_at DESC;
END;
$$;


ALTER FUNCTION blog.search_published_articles(search_term text) OWNER TO supabase_admin;

--
-- Name: grant_pg_cron_access(); Type: FUNCTION; Schema: extensions; Owner: postgres
--

CREATE FUNCTION extensions.grant_pg_cron_access() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
  IF EXISTS (
    SELECT
    FROM pg_event_trigger_ddl_commands() AS ev
    JOIN pg_extension AS ext
    ON ev.objid = ext.oid
    WHERE ext.extname = 'pg_cron'
  )
  THEN
    grant usage on schema cron to postgres with grant option;

    alter default privileges in schema cron grant all on tables to postgres with grant option;
    alter default privileges in schema cron grant all on functions to postgres with grant option;
    alter default privileges in schema cron grant all on sequences to postgres with grant option;

    alter default privileges for user supabase_admin in schema cron grant all
        on sequences to postgres with grant option;
    alter default privileges for user supabase_admin in schema cron grant all
        on tables to postgres with grant option;
    alter default privileges for user supabase_admin in schema cron grant all
        on functions to postgres with grant option;

    grant all privileges on all tables in schema cron to postgres with grant option;
    revoke all on table cron.job from postgres;
    grant select on table cron.job to postgres with grant option;
  END IF;
END;
$$;


ALTER FUNCTION extensions.grant_pg_cron_access() OWNER TO postgres;

--
-- Name: FUNCTION grant_pg_cron_access(); Type: COMMENT; Schema: extensions; Owner: postgres
--

COMMENT ON FUNCTION extensions.grant_pg_cron_access() IS 'Grants access to pg_cron';


--
-- Name: grant_pg_graphql_access(); Type: FUNCTION; Schema: extensions; Owner: supabase_admin
--

CREATE FUNCTION extensions.grant_pg_graphql_access() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $_$
DECLARE
    func_is_graphql_resolve bool;
BEGIN
    func_is_graphql_resolve = (
        SELECT n.proname = 'resolve'
        FROM pg_event_trigger_ddl_commands() AS ev
        LEFT JOIN pg_catalog.pg_proc AS n
        ON ev.objid = n.oid
    );

    IF func_is_graphql_resolve
    THEN
        -- Update public wrapper to pass all arguments through to the pg_graphql resolve func
        DROP FUNCTION IF EXISTS graphql_public.graphql;
        create or replace function graphql_public.graphql(
            "operationName" text default null,
            query text default null,
            variables jsonb default null,
            extensions jsonb default null
        )
            returns jsonb
            language sql
        as $$
            select graphql.resolve(
                query := query,
                variables := coalesce(variables, '{}'),
                "operationName" := "operationName",
                extensions := extensions
            );
        $$;

        -- This hook executes when `graphql.resolve` is created. That is not necessarily the last
        -- function in the extension so we need to grant permissions on existing entities AND
        -- update default permissions to any others that are created after `graphql.resolve`
        grant usage on schema graphql to postgres, anon, authenticated, service_role;
        grant select on all tables in schema graphql to postgres, anon, authenticated, service_role;
        grant execute on all functions in schema graphql to postgres, anon, authenticated, service_role;
        grant all on all sequences in schema graphql to postgres, anon, authenticated, service_role;
        alter default privileges in schema graphql grant all on tables to postgres, anon, authenticated, service_role;
        alter default privileges in schema graphql grant all on functions to postgres, anon, authenticated, service_role;
        alter default privileges in schema graphql grant all on sequences to postgres, anon, authenticated, service_role;

        -- Allow postgres role to allow granting usage on graphql and graphql_public schemas to custom roles
        grant usage on schema graphql_public to postgres with grant option;
        grant usage on schema graphql to postgres with grant option;
    END IF;

END;
$_$;


ALTER FUNCTION extensions.grant_pg_graphql_access() OWNER TO supabase_admin;

--
-- Name: FUNCTION grant_pg_graphql_access(); Type: COMMENT; Schema: extensions; Owner: supabase_admin
--

COMMENT ON FUNCTION extensions.grant_pg_graphql_access() IS 'Grants access to pg_graphql';


--
-- Name: grant_pg_net_access(); Type: FUNCTION; Schema: extensions; Owner: postgres
--

CREATE FUNCTION extensions.grant_pg_net_access() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
  IF EXISTS (
    SELECT 1
    FROM pg_event_trigger_ddl_commands() AS ev
    JOIN pg_extension AS ext
    ON ev.objid = ext.oid
    WHERE ext.extname = 'pg_net'
  )
  THEN
    IF NOT EXISTS (
      SELECT 1
      FROM pg_roles
      WHERE rolname = 'supabase_functions_admin'
    )
    THEN
      CREATE USER supabase_functions_admin NOINHERIT CREATEROLE LOGIN NOREPLICATION;
    END IF;

    GRANT USAGE ON SCHEMA net TO supabase_functions_admin, postgres, anon, authenticated, service_role;

    IF EXISTS (
      SELECT FROM pg_extension
      WHERE extname = 'pg_net'
      -- all versions in use on existing projects as of 2025-02-20
      -- version 0.12.0 onwards don't need these applied
      AND extversion IN ('0.2', '0.6', '0.7', '0.7.1', '0.8', '0.10.0', '0.11.0')
    ) THEN
      ALTER function net.http_get(url text, params jsonb, headers jsonb, timeout_milliseconds integer) SECURITY DEFINER;
      ALTER function net.http_post(url text, body jsonb, params jsonb, headers jsonb, timeout_milliseconds integer) SECURITY DEFINER;

      ALTER function net.http_get(url text, params jsonb, headers jsonb, timeout_milliseconds integer) SET search_path = net;
      ALTER function net.http_post(url text, body jsonb, params jsonb, headers jsonb, timeout_milliseconds integer) SET search_path = net;

      REVOKE ALL ON FUNCTION net.http_get(url text, params jsonb, headers jsonb, timeout_milliseconds integer) FROM PUBLIC;
      REVOKE ALL ON FUNCTION net.http_post(url text, body jsonb, params jsonb, headers jsonb, timeout_milliseconds integer) FROM PUBLIC;

      GRANT EXECUTE ON FUNCTION net.http_get(url text, params jsonb, headers jsonb, timeout_milliseconds integer) TO supabase_functions_admin, postgres, anon, authenticated, service_role;
      GRANT EXECUTE ON FUNCTION net.http_post(url text, body jsonb, params jsonb, headers jsonb, timeout_milliseconds integer) TO supabase_functions_admin, postgres, anon, authenticated, service_role;
    END IF;
  END IF;
END;
$$;


ALTER FUNCTION extensions.grant_pg_net_access() OWNER TO postgres;

--
-- Name: FUNCTION grant_pg_net_access(); Type: COMMENT; Schema: extensions; Owner: postgres
--

COMMENT ON FUNCTION extensions.grant_pg_net_access() IS 'Grants access to pg_net';


--
-- Name: pgrst_ddl_watch(); Type: FUNCTION; Schema: extensions; Owner: supabase_admin
--

CREATE FUNCTION extensions.pgrst_ddl_watch() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
  cmd record;
BEGIN
  FOR cmd IN SELECT * FROM pg_event_trigger_ddl_commands()
  LOOP
    IF cmd.command_tag IN (
      'CREATE SCHEMA', 'ALTER SCHEMA'
    , 'CREATE TABLE', 'CREATE TABLE AS', 'SELECT INTO', 'ALTER TABLE'
    , 'CREATE FOREIGN TABLE', 'ALTER FOREIGN TABLE'
    , 'CREATE VIEW', 'ALTER VIEW'
    , 'CREATE MATERIALIZED VIEW', 'ALTER MATERIALIZED VIEW'
    , 'CREATE FUNCTION', 'ALTER FUNCTION'
    , 'CREATE TRIGGER'
    , 'CREATE TYPE', 'ALTER TYPE'
    , 'CREATE RULE'
    , 'COMMENT'
    )
    -- don't notify in case of CREATE TEMP table or other objects created on pg_temp
    AND cmd.schema_name is distinct from 'pg_temp'
    THEN
      NOTIFY pgrst, 'reload schema';
    END IF;
  END LOOP;
END; $$;


ALTER FUNCTION extensions.pgrst_ddl_watch() OWNER TO supabase_admin;

--
-- Name: pgrst_drop_watch(); Type: FUNCTION; Schema: extensions; Owner: supabase_admin
--

CREATE FUNCTION extensions.pgrst_drop_watch() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
  obj record;
BEGIN
  FOR obj IN SELECT * FROM pg_event_trigger_dropped_objects()
  LOOP
    IF obj.object_type IN (
      'schema'
    , 'table'
    , 'foreign table'
    , 'view'
    , 'materialized view'
    , 'function'
    , 'trigger'
    , 'type'
    , 'rule'
    )
    AND obj.is_temporary IS false -- no pg_temp objects
    THEN
      NOTIFY pgrst, 'reload schema';
    END IF;
  END LOOP;
END; $$;


ALTER FUNCTION extensions.pgrst_drop_watch() OWNER TO supabase_admin;

--
-- Name: set_graphql_placeholder(); Type: FUNCTION; Schema: extensions; Owner: supabase_admin
--

CREATE FUNCTION extensions.set_graphql_placeholder() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $_$
    DECLARE
    graphql_is_dropped bool;
    BEGIN
    graphql_is_dropped = (
        SELECT ev.schema_name = 'graphql_public'
        FROM pg_event_trigger_dropped_objects() AS ev
        WHERE ev.schema_name = 'graphql_public'
    );

    IF graphql_is_dropped
    THEN
        create or replace function graphql_public.graphql(
            "operationName" text default null,
            query text default null,
            variables jsonb default null,
            extensions jsonb default null
        )
            returns jsonb
            language plpgsql
        as $$
            DECLARE
                server_version float;
            BEGIN
                server_version = (SELECT (SPLIT_PART((select version()), ' ', 2))::float);

                IF server_version >= 14 THEN
                    RETURN jsonb_build_object(
                        'errors', jsonb_build_array(
                            jsonb_build_object(
                                'message', 'pg_graphql extension is not enabled.'
                            )
                        )
                    );
                ELSE
                    RETURN jsonb_build_object(
                        'errors', jsonb_build_array(
                            jsonb_build_object(
                                'message', 'pg_graphql is only available on projects running Postgres 14 onwards.'
                            )
                        )
                    );
                END IF;
            END;
        $$;
    END IF;

    END;
$_$;


ALTER FUNCTION extensions.set_graphql_placeholder() OWNER TO supabase_admin;

--
-- Name: FUNCTION set_graphql_placeholder(); Type: COMMENT; Schema: extensions; Owner: supabase_admin
--

COMMENT ON FUNCTION extensions.set_graphql_placeholder() IS 'Reintroduces placeholder function for graphql_public.graphql';


--
-- Name: get_auth(text); Type: FUNCTION; Schema: pgbouncer; Owner: supabase_admin
--

CREATE FUNCTION pgbouncer.get_auth(p_usename text) RETURNS TABLE(username text, password text)
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
    RAISE WARNING 'PgBouncer auth request: %', p_usename;

    RETURN QUERY
    SELECT usename::TEXT, passwd::TEXT FROM pg_catalog.pg_shadow
    WHERE usename = p_usename;
END;
$$;


ALTER FUNCTION pgbouncer.get_auth(p_usename text) OWNER TO supabase_admin;

--
-- Name: apply_negative_balances(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.apply_negative_balances() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
    total_negative DECIMAL(18,9);
    remaining_credit DECIMAL(18,9);
BEGIN
    -- Calculate total negative balance for this user
    SELECT COALESCE(SUM(amount), 0) INTO total_negative
    FROM public.negative_balances
    WHERE user_id = NEW.user_id AND cleared_at IS NULL;
    
    -- If no negative balance, just return the NEW record unchanged
    IF total_negative = 0 THEN
        RETURN NEW;
    END IF;
    
    -- Calculate how much we can clear
    remaining_credit := NEW.balance;
    
    -- Apply negative balance deduction if possible
    IF remaining_credit >= total_negative THEN
        -- We can clear all negative balances
        NEW.balance := remaining_credit - total_negative;
        
        -- Mark all negative balances as cleared
        UPDATE public.negative_balances
        SET cleared_at = NOW()
        WHERE user_id = NEW.user_id AND cleared_at IS NULL;
    ELSE
        -- We can only clear part of the negative balances
        -- Update the credit balance to zero (since we can't clear everything)
        NEW.balance := 0;
        
        -- Create a temporary table to track which negative balances we'll clear
        CREATE TEMP TABLE cleared_negatives (
            id UUID,
            amount DECIMAL(18,9)
        ) ON COMMIT DROP;
        
        -- Fill the temporary table with the negative balances we can clear
        -- Process oldest first (FIFO)
        INSERT INTO cleared_negatives (id, amount)
        SELECT id, amount
        FROM public.negative_balances
        WHERE user_id = NEW.user_id AND cleared_at IS NULL
        ORDER BY created_at ASC;
        
        -- Clear as many negative balances as possible
        DECLARE
            negative_id UUID;
            negative_amount DECIMAL(18,9);
            can_clear DECIMAL(18,9);
        BEGIN
            FOR negative_id, negative_amount IN SELECT id, amount FROM cleared_negatives LOOP
                IF remaining_credit <= 0 THEN
                    EXIT; -- No more credit to apply
                END IF;
                
                IF remaining_credit >= negative_amount THEN
                    -- Clear this negative balance completely
                    UPDATE public.negative_balances
                    SET cleared_at = NOW()
                    WHERE id = negative_id;
                    
                    remaining_credit := remaining_credit - negative_amount;
                ELSE
                    -- Partially clear this negative balance
                    UPDATE public.negative_balances
                    SET amount = amount - remaining_credit
                    WHERE id = negative_id;
                    
                    remaining_credit := 0;
                    EXIT; -- No more credit to apply
                END IF;
            END LOOP;
        END;
    END IF;
    
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.apply_negative_balances() OWNER TO postgres;

--
-- Name: check_api_key_limit(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.check_api_key_limit() RETURNS trigger
    LANGUAGE plpgsql
    AS $$BEGIN
    IF (SELECT COUNT(*) FROM public.api_keys WHERE user_id = NEW.user_id) >= 25 THEN
        RAISE EXCEPTION 'Maximum number of API keys (25) reached for this user';
    END IF;
    RETURN NEW;
END;$$;


ALTER FUNCTION public.check_api_key_limit() OWNER TO postgres;

--
-- Name: check_quota_usage(); Type: FUNCTION; Schema: public; Owner: supabase_admin
--

CREATE FUNCTION public.check_quota_usage() RETURNS trigger
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
  email_service_url TEXT := 'https://supabase.voicehype.ai/functions/v1/email-service';
  webhook_secret TEXT;
  email_payload JSONB;
  request_id BIGINT;
  usage_percentage NUMERIC;
  user_email TEXT;
  user_name TEXT;
  service_name TEXT;
  threshold_to_check INTEGER;
  notification_needed BOOLEAN;
  threshold_array INTEGER[] := ARRAY[50, 90, 100];
  i INTEGER;
BEGIN
  -- Only process updates
  IF TG_OP = 'UPDATE' THEN
    -- Calculate usage percentage
    usage_percentage := (NEW.used_amount / NEW.total_amount) * 100;
    
    -- Get user details
    SELECT p.email, p.full_name INTO user_email, user_name
    FROM profiles p
    WHERE p.id = NEW.user_id;
    
    -- Determine service name for email
    service_name := CASE 
      WHEN NEW.service = 'transcription' THEN 'Transcription'
      WHEN NEW.service = 'optimization' THEN 'Optimization'
      ELSE NEW.service
    END;
    
    -- Check each threshold (50%, 90%, 100%)
    FOR i IN 1..array_length(threshold_array, 1) LOOP
      threshold_to_check := threshold_array[i];
      
      -- Check if we've crossed this threshold and haven't sent a notification yet
      notification_needed := usage_percentage >= threshold_to_check AND NOT EXISTS (
        SELECT 1 FROM quota_notifications 
        WHERE user_id = NEW.user_id 
          AND quota_id = NEW.id 
          AND threshold = threshold_to_check
      );
      
      IF notification_needed THEN
        -- Get webhook secret
        SELECT decrypted_secret INTO webhook_secret
        FROM vault.decrypted_secrets
        WHERE name = 'email_webhook_secret';
        
        IF webhook_secret IS NULL THEN
          RAISE NOTICE 'ERROR: Webhook secret not found!';
          CONTINUE;
        END IF;
        
        -- Prepare email payload
        email_payload := jsonb_build_object(
          'user', jsonb_build_object(
            'id', NEW.user_id,
            'email', user_email,
            'user_metadata', jsonb_build_object('full_name', user_name)
          ),
          'email_data', jsonb_build_object(
            'email_action_type', 'quota_usage_alert',
            'service', NEW.service,
            'service_name', service_name,
            'threshold', threshold_to_check,
            'used_amount', NEW.used_amount,
            'total_amount', NEW.total_amount,
            'usage_percentage', ROUND(usage_percentage, 2),
            'site_url', 'https://voicehype.ai'
          )
        );
        
        -- Send email notification
        BEGIN
          SELECT net.http_post(
            url => email_service_url,
            body => email_payload,
            headers => jsonb_build_object(
              'Content-Type', 'application/json',
              'User-Agent', 'Supabase-Quota-Trigger',
              'Authorization', 'Bearer ' || webhook_secret
            ),
            timeout_milliseconds => 5000
          ) INTO request_id;
          
          -- Record that we sent the notification
          INSERT INTO quota_notifications (user_id, quota_id, threshold)
          VALUES (NEW.user_id, NEW.id, threshold_to_check);
          
          -- Fixed RAISE NOTICE statement
          RAISE NOTICE 'Quota notification sent for user: %, service: %, threshold: %, request_id: %', 
            user_email, service_name, threshold_to_check || '%', request_id;
          
        EXCEPTION WHEN OTHERS THEN
          -- Log error but don't block the update
          RAISE NOTICE 'Failed to send quota notification: %', SQLERRM;
        END;
      END IF;
    END LOOP;
  END IF;
  
  RETURN NEW;
END;
$$;


ALTER FUNCTION public.check_quota_usage() OWNER TO supabase_admin;

--
-- Name: check_usage_allowance(uuid, text, text, numeric, uuid, boolean); Type: FUNCTION; Schema: public; Owner: supabase_admin
--

CREATE FUNCTION public.check_usage_allowance(p_user_id uuid, p_service text, p_model text, p_amount numeric, p_api_key_id uuid, p_is_input_only boolean DEFAULT false) RETURNS TABLE(can_use boolean, pricing_model text, cost numeric, max_output text, error_code text)
    LANGUAGE plpgsql
    AS $$
DECLARE
    subscription_available BOOLEAN;
    credit_balance DECIMAL(18,9);
    cost_per_unit DECIMAL(18,9);
    input_cost_per_unit DECIMAL(18,9);
    output_cost_per_unit DECIMAL(18,9);
    pricing_unit TEXT;
    remaining_quota DECIMAL(18,9) := 0;
    estimated_minutes DECIMAL(18,9);
    is_token_based BOOLEAN;
    is_realtime_transcription BOOLEAN;
    best_pricing_model TEXT;
BEGIN
    -- Fetch pricing info
    SELECT sp.unit, sp.cost_per_unit INTO pricing_unit, cost_per_unit
    FROM public.service_pricing sp
    WHERE sp.service = p_service
      AND sp.model = p_model
      AND sp.is_active = TRUE;

    is_token_based := pricing_unit = 'token-based';
    is_realtime_transcription := (p_service = 'transcription' AND (p_model LIKE '%-realtime' OR p_model LIKE '%/realtime'));

    -- Get subscription quota
    SELECT EXISTS (
        SELECT 1 FROM public.quotas q
        JOIN public.user_subscriptions us ON q.subscription_id = us.id
        WHERE us.user_id = p_user_id
          AND q.service = p_service
          AND us.status IN ('active', 'past_due')
    ) INTO subscription_available;

    IF subscription_available THEN
        SELECT (q.total_amount - q.used_amount)
        INTO remaining_quota
        FROM public.quotas q
        JOIN public.user_subscriptions us ON q.subscription_id = us.id
        WHERE us.user_id = p_user_id
          AND q.service = p_service
          AND us.status IN ('active', 'past_due')
        LIMIT 1;
    END IF;

    -- Get credit balance
    SELECT COALESCE(SUM(balance), 0)
    INTO credit_balance
    FROM public.credits
    WHERE user_id = p_user_id
      AND balance > 0
      AND (expires_at IS NULL OR expires_at > NOW())
      AND status = 'active';

-- TRANSCRIPTION LOGIC
IF p_service = 'transcription' THEN
    IF is_realtime_transcription THEN
        -- Total available minutes from both subscription and credits
        estimated_minutes := remaining_quota;
        IF cost_per_unit > 0 THEN
            estimated_minutes := estimated_minutes + (credit_balance / cost_per_unit);
        END IF;

        -- Require at least 30 seconds (0.5 minutes) for realtime transcription
        IF estimated_minutes >= 0.5 THEN
            RETURN QUERY SELECT
                TRUE,
                CASE
                    WHEN remaining_quota > 0 THEN 'subscription'
                    ELSE 'credits'
                END,
                0::numeric,
                LEAST(estimated_minutes, 20)::TEXT,
                NULL;
            RETURN;
        ELSE
            -- Provide detailed error for realtime transcription
            IF estimated_minutes <= 0 THEN
                RETURN QUERY SELECT FALSE, NULL, 0::numeric, NULL, 'realtime_insufficient_total: Realtime transcription requires at least 30 seconds. No subscription quota or credits available.';
            ELSIF estimated_minutes < 0.5 THEN
                RETURN QUERY SELECT FALSE, NULL, 0::numeric, NULL, format('realtime_insufficient_time: Realtime transcription requires at least 30 seconds. Available: %.1f seconds', estimated_minutes * 60);
            END IF;
            RETURN;
        END IF;

        ELSE
            estimated_minutes := remaining_quota;
            IF cost_per_unit > 0 THEN
                estimated_minutes := estimated_minutes + (credit_balance / cost_per_unit);
            END IF;

            -- Check if user has available quota or credits
            IF remaining_quota > 0 OR credit_balance > 0 THEN
                -- Add 30 seconds (0.5 minutes) grace period if insufficient but has some quota/credits
                IF estimated_minutes < p_amount THEN
                    estimated_minutes := estimated_minutes + 0.5;
                END IF;
                
                -- Allow if after grace period there's enough time, or if original was sufficient
                IF estimated_minutes >= p_amount THEN
                    RETURN QUERY SELECT
                        TRUE,
                        CASE
                            WHEN remaining_quota > 0 THEN 'subscription'
                            ELSE 'credits'
                        END,
                        0::numeric,
                        estimated_minutes::TEXT,
                        NULL;
                    RETURN;
                ELSE
                    -- Provide detailed error for normal transcription
                    DECLARE
                        subscription_minutes DECIMAL(18,9) := remaining_quota;
                        credit_minutes DECIMAL(18,9) := 0;
                        total_needed DECIMAL(18,9) := p_amount;
                        grace_applied BOOLEAN := (estimated_minutes < p_amount);
                    BEGIN
                        IF cost_per_unit > 0 THEN
                            credit_minutes := credit_balance / cost_per_unit;
                        END IF;
                        
                        IF remaining_quota <= 0 AND credit_balance <= 0 THEN
                            RETURN QUERY SELECT FALSE, NULL, 0::numeric, NULL, 'transcription_no_balance: No subscription quota or credits available for transcription.';
                        ELSIF remaining_quota > 0 AND credit_balance <= 0 THEN
                            RETURN QUERY SELECT FALSE, NULL, 0::numeric, NULL, format('transcription_quota_insufficient: Subscription quota insufficient. Available: %.1f minutes, Required: %.1f minutes', remaining_quota, total_needed);
                        ELSIF remaining_quota <= 0 AND credit_balance > 0 THEN
                            RETURN QUERY SELECT FALSE, NULL, 0::numeric, NULL, format('transcription_credits_insufficient: Credits insufficient. Available: %.1f minutes (%.2f credits), Required: %.1f minutes', credit_minutes, credit_balance, total_needed);
                        ELSE
                            RETURN QUERY SELECT FALSE, NULL, 0::numeric, NULL, format('transcription_combined_insufficient: Combined subscription (%.1f min) + credits (%.1f min) insufficient. Required: %.1f minutes', subscription_minutes, credit_minutes, total_needed);
                        END IF;
                    END;
                    RETURN;
                END IF;
            ELSE
                -- No quota or credits available
                RETURN QUERY SELECT FALSE, NULL, 0::numeric, NULL, 'transcription_no_balance: No subscription quota or credits available for transcription.';
            END IF;
        END IF;
    END IF;

    -- OPTIMIZATION LOGIC
    IF p_service = 'optimization' THEN
        SELECT sp.cost_per_unit INTO input_cost_per_unit
        FROM public.service_pricing sp
        WHERE sp.service = 'optimization'
          AND sp.model = p_model || '/input'
          AND sp.is_active = TRUE;

        SELECT sp.cost_per_unit INTO output_cost_per_unit
        FROM public.service_pricing sp
        WHERE sp.service = 'optimization'
          AND sp.model = p_model || '/output'
          AND sp.is_active = TRUE;

        -- If either quota or credits exist, allow
        IF remaining_quota > 0 OR credit_balance > 0 THEN
            RETURN QUERY SELECT
                TRUE,
                CASE
                    WHEN remaining_quota > 0 THEN 'subscription'
                    ELSE 'credits'
                END,
                0::numeric,
                '4096',
                NULL;
            RETURN;
        ELSE
            -- Provide detailed error for optimization
            IF remaining_quota <= 0 AND credit_balance <= 0 THEN
                RETURN QUERY SELECT FALSE, NULL, 0::numeric, NULL, 'optimization_no_balance: No subscription quota or credits available for optimization.';
            ELSIF remaining_quota > 0 THEN
                RETURN QUERY SELECT FALSE, NULL, 0::numeric, NULL, format('optimization_quota_insufficient: Subscription quota exhausted. Available: %.2f units', remaining_quota);
            ELSE
                RETURN QUERY SELECT FALSE, NULL, 0::numeric, NULL, format('optimization_credits_insufficient: Credits insufficient. Available: %.2f credits', credit_balance);
            END IF;
            RETURN;
        END IF;
    END IF;

    -- Default: deny with specific service information
    RETURN QUERY SELECT FALSE, NULL, 0::numeric, NULL, format('%s_no_pricing: No active pricing found for service "%s" with model "%s"', p_service, p_service, p_model);
END;
$$;


ALTER FUNCTION public.check_usage_allowance(p_user_id uuid, p_service text, p_model text, p_amount numeric, p_api_key_id uuid, p_is_input_only boolean) OWNER TO supabase_admin;

--
-- Name: cleanup_cancelled_subscription(text); Type: FUNCTION; Schema: public; Owner: supabase_admin
--

CREATE FUNCTION public.cleanup_cancelled_subscription(p_paddle_subscription_id text) RETURNS json
    LANGUAGE plpgsql
    AS $$
DECLARE
  v_subscription_record RECORD;
  v_result JSON;
BEGIN
  -- Get subscription details
  SELECT id, user_id 
  INTO v_subscription_record
  FROM public.user_subscriptions 
  WHERE paddle_subscription_id = p_paddle_subscription_id;

  -- If subscription not found, return success (already cleaned up)
  IF v_subscription_record.id IS NULL THEN
    RETURN JSON_BUILD_OBJECT(
      'success', true,
      'message', 'Subscription not found or already cleaned up',
      'paddle_subscription_id', p_paddle_subscription_id
    );
  END IF;

  -- Delete quotas for this subscription
  DELETE FROM public.quotas 
  WHERE subscription_id = v_subscription_record.id;

  -- Delete the user subscription
  DELETE FROM public.user_subscriptions 
  WHERE id = v_subscription_record.id;

  -- Return success
  RETURN JSON_BUILD_OBJECT(
    'success', true,
    'message', 'Subscription cleaned up successfully',
    'user_id', v_subscription_record.user_id,
    'subscription_id', v_subscription_record.id,
    'paddle_subscription_id', p_paddle_subscription_id
  );

EXCEPTION WHEN OTHERS THEN
  -- Return error details
  RETURN JSON_BUILD_OBJECT(
    'success', false,
    'error', SQLERRM,
    'paddle_subscription_id', p_paddle_subscription_id
  );
END;
$$;


ALTER FUNCTION public.cleanup_cancelled_subscription(p_paddle_subscription_id text) OWNER TO supabase_admin;

--
-- Name: create_api_key(text, timestamp with time zone); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.create_api_key(p_name text, p_expires_at timestamp with time zone DEFAULT NULL::timestamp with time zone) RETURNS public.api_key_result
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
    v_user_id UUID;
    v_key_secret TEXT;
    v_key_prefix TEXT;
    v_key_hash TEXT;
    v_api_key_id UUID;
    v_key_count INTEGER;
    v_created_at TIMESTAMPTZ;
    v_final_expires_at TIMESTAMPTZ;
    v_max_expires_at TIMESTAMPTZ;
    v_result public.api_key_result;
    v_raw_bytes BYTEA;
BEGIN
    -- Get the current user's ID
    v_user_id := auth.uid();
    
    -- Check if user is authenticated
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'Not authenticated';
    END IF;
    
    -- Check API key limit (belt-and-suspenders approach with the trigger)
    SELECT COUNT(*) INTO v_key_count FROM public.api_keys WHERE user_id = v_user_id;
    IF v_key_count >= 25 THEN
        RAISE EXCEPTION 'Maximum number of API keys (25) reached for this user';
    END IF;
    
    -- Generate a secure API key using URL-safe base64 encoding (no /, + or = characters)
    v_raw_bytes := extensions.gen_random_bytes(24);
    v_key_secret := replace(replace(replace(encode(v_raw_bytes, 'base64'), '/', '_'), '+', '-'), '=', '');
    
    -- Ensure no spaces or other problematic characters
    v_key_secret := regexp_replace(v_key_secret, '[^a-zA-Z0-9_-]', '', 'g');
    
    -- Extract prefix for storage
    v_key_prefix := substring(v_key_secret from 1 for 8);
    
    -- Hash the key for secure storage
    v_key_hash := extensions.crypt(v_key_secret, extensions.gen_salt('bf'));
    
    -- Set the creation time
    v_created_at := NOW();
    
    -- Calculate maximum expiration date (1 year from creation)
    v_max_expires_at := v_created_at + INTERVAL '1 year';
    
    -- Set expiration date to NULL (never expires) if not provided
    IF p_expires_at IS NULL THEN
        v_final_expires_at := NULL;  -- Never expires
    ELSE
        -- Validate user-provided expiration date
        IF p_expires_at <= v_created_at THEN
            RAISE EXCEPTION 'Expiration date must be in the future';
        END IF;
        
        -- Limit expiration date to a maximum of 1 year from creation
        v_final_expires_at := LEAST(p_expires_at, v_max_expires_at);
    END IF;
    
    -- Insert the new API key with controlled fields
    INSERT INTO public.api_keys (
        user_id,
        name,
        key_prefix,
        key_hash,
        created_at,
        expires_at
    ) VALUES (
        v_user_id,
        p_name,
        v_key_prefix,
        v_key_hash,
        v_created_at,  -- Server-controlled timestamp
        v_final_expires_at  -- NULL means never expires, otherwise limited to a maximum of 1 year from creation
    ) RETURNING id INTO v_api_key_id;
    
    -- Return both the ID and the key secret
    v_result.id := v_api_key_id;
    v_result.key_secret := v_key_secret;
    
    RETURN v_result;
END;
$$;


ALTER FUNCTION public.create_api_key(p_name text, p_expires_at timestamp with time zone) OWNER TO postgres;

--
-- Name: FUNCTION create_api_key(p_name text, p_expires_at timestamp with time zone); Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON FUNCTION public.create_api_key(p_name text, p_expires_at timestamp with time zone) IS 'Creates a new API key for the authenticated user with a maximum expiration of 1 year and returns both the ID and the key secret. The key is URL-safe with no special characters.';


--
-- Name: finalize_realtime_session(uuid, uuid, text, text, bigint, bigint, boolean, jsonb, numeric); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.finalize_realtime_session(p_user_id uuid, p_api_key_id uuid, p_model text, p_session_id text, p_start_time bigint, p_audio_bytes_sent bigint, p_has_transcription boolean, p_token_info jsonb DEFAULT NULL::jsonb, p_processed_duration_seconds numeric DEFAULT NULL::numeric) RETURNS void
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_end_time bigint;
    v_session_duration_ms bigint;
    v_session_duration_minutes numeric;
    v_processed_duration_minutes numeric;
    v_service_pricing record;
    v_cost numeric;
    v_pricing_model text;
    v_cost_per_unit numeric;
    v_input_tokens integer;
    v_output_tokens integer;
    v_input_token_cost numeric;
    v_output_token_cost numeric;
    v_total_token_cost numeric;
    v_is_token_based boolean := false;
    v_query_result record;
    v_available_credit numeric;
    v_model_prefix text;
    v_metadata jsonb;
    v_billing_duration_minutes numeric;
    v_usage_history_id uuid;
BEGIN
    -- Calculate session duration
    v_end_time := extract(epoch from now()) * 1000;
    v_session_duration_ms := v_end_time - p_start_time;
    v_session_duration_minutes := v_session_duration_ms / (1000 * 60);
    
    -- Calculate processed duration in minutes if provided
    IF p_processed_duration_seconds IS NOT NULL AND p_processed_duration_seconds > 0 THEN
        v_processed_duration_minutes := p_processed_duration_seconds / 60;
    ELSE
        v_processed_duration_minutes := NULL;
    END IF;
    
    -- Use processed duration for billing if available and valid, otherwise use session duration
    v_billing_duration_minutes := COALESCE(v_processed_duration_minutes, v_session_duration_minutes);

    -- Extract the base model name for GPT-4o models
    IF p_model LIKE 'gpt-4o%' THEN
        v_model_prefix := p_model;
        -- If realtime not already in the name, add it
        IF p_model NOT LIKE '%-realtime' THEN
            v_model_prefix := p_model || '-realtime';
        END IF;
    ELSE
        v_model_prefix := p_model;
    END IF;

    -- Get service pricing
    SELECT * INTO v_service_pricing
    FROM public.service_pricing
    WHERE service = 'transcription'
    AND model = v_model_prefix
    AND is_active = TRUE
    LIMIT 1;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'No active pricing found for service: transcription, model: %', v_model_prefix;
    END IF;

    -- Determine if this is a token-based model
    v_is_token_based := v_service_pricing.unit = 'token-based';

    -- If token info is provided and model is token-based, use token billing
    IF v_is_token_based AND p_token_info IS NOT NULL THEN
        -- Extract token counts
        v_input_tokens := COALESCE((p_token_info->>'input_tokens')::INTEGER, 0);
        v_output_tokens := COALESCE((p_token_info->>'output_tokens')::INTEGER, 0);
        
        -- Get token costs for this model
        SELECT cost_per_unit INTO v_input_token_cost
        FROM public.service_pricing
        WHERE service = 'transcription'
        AND model = REPLACE(v_model_prefix, '-realtime', '/input')
        AND is_active = TRUE
        LIMIT 1;
        
        SELECT cost_per_unit INTO v_output_token_cost
        FROM public.service_pricing
        WHERE service = 'transcription'
        AND model = REPLACE(v_model_prefix, '-realtime', '/output')
        AND is_active = TRUE
        LIMIT 1;
        
        -- Calculate total cost based on token usage
        v_total_token_cost := (v_input_tokens * v_input_token_cost) + 
                           (v_output_tokens * v_output_token_cost);
        
        v_cost := v_total_token_cost;
        v_metadata := jsonb_build_object(
            'sessionId', p_session_id,
            'startTime', p_start_time,
            'endTime', v_end_time,
            'durationMs', v_session_duration_ms,
            'processedDurationSeconds', p_processed_duration_seconds,
            'billingDurationMinutes', v_billing_duration_minutes,
            'audioBytesSent', p_audio_bytes_sent,
            'hasTranscription', p_has_transcription,
            'token_based', true,
            'input_tokens', v_input_tokens,
            'output_tokens', v_output_tokens,
            'input_cost', ROUND(v_input_tokens * v_input_token_cost, 9),
            'output_cost', ROUND(v_output_tokens * v_output_token_cost, 9)
        );
    ELSE
        -- Use standard duration-based pricing
        v_cost_per_unit := v_service_pricing.cost_per_unit;
        v_cost := v_cost_per_unit * v_billing_duration_minutes;
        
        v_metadata := jsonb_build_object(
            'sessionId', p_session_id,
            'startTime', p_start_time,
            'endTime', v_end_time,
            'durationMs', v_session_duration_ms,
            'processedDurationSeconds', p_processed_duration_seconds,
            'billingDurationMinutes', v_billing_duration_minutes,
            'audioBytesSent', p_audio_bytes_sent,
            'hasTranscription', p_has_transcription
        );
    END IF;

    -- Check if user has subscription or credits
    WITH pricing_check AS (
        SELECT 
            pr.pricing_model,
            CASE
                WHEN pr.pricing_model = 'subscription' THEN
                    GREATEST(0, q.allowed_amount - q.used_amount)
                WHEN pr.pricing_model = 'credits' THEN
                    c.balance
                ELSE 0
            END AS available,
            pr.pricing_model = 'credits' AS is_credits,
            row_number() OVER (
                ORDER BY
                    CASE pr.pricing_model
                        WHEN 'subscription' THEN 1
                        WHEN 'credits' THEN 2
                        WHEN 'payg' THEN 3
                        ELSE 4
                    END
            ) AS rn
        FROM 
            public.check_usage_allowance(p_user_id, 'transcription', v_model_prefix, 1, p_api_key_id, false) ca
        JOIN
            public.pricing_results pr ON pr.id = ca.pricing_result_id
        LEFT JOIN
            public.quotas q ON q.subscription_id = pr.subscription_id AND q.service = 'transcription'
        LEFT JOIN
            public.credits c ON c.user_id = p_user_id
        WHERE ca.can_use = TRUE
    )
    SELECT 
        pricing_model,
        available,
        is_credits
    INTO v_query_result 
    FROM pricing_check
    WHERE rn = 1;

    -- Set pricing model based on query result or default to credits
    v_pricing_model := COALESCE(v_query_result.pricing_model, 'credits');
    
    -- Get the usage history ID before updating it
    SELECT id INTO v_usage_history_id
    FROM public.usage_history
    WHERE user_id = p_user_id
      AND service = 'transcription'
      AND model = v_model_prefix
      AND status = 'pending'
      AND (metadata->>'sessionId')::text = p_session_id
    ORDER BY created_at DESC
    LIMIT 1;
    
    -- Update usage history with final details
    UPDATE public.usage_history
    SET 
        status = 'success',
        amount = v_billing_duration_minutes,
        cost = ROUND(v_cost, 9),
        metadata = v_metadata,
        pricing_model = v_pricing_model
    WHERE id = v_usage_history_id;

    -- Handle negative balances for credit users when using token-based billing
    IF v_is_token_based AND v_pricing_model = 'credits' AND p_token_info IS NOT NULL THEN
        v_available_credit := COALESCE(v_query_result.available, 0);
        
        -- If cost exceeds available credits, create a negative balance record
        IF v_cost > v_available_credit THEN
            INSERT INTO public.negative_balances (
                user_id,
                service,
                model,
                amount,
                usage_history_id
            ) VALUES (
                p_user_id,
                'transcription',
                v_model_prefix,
                ROUND(v_cost - v_available_credit, 9),
                v_usage_history_id
            );
            
            -- Only deduct available credits, not more
            UPDATE public.credits
            SET balance = 0
            WHERE user_id = p_user_id AND balance = v_available_credit;
        ELSE
            -- Regular credit deduction
            UPDATE public.credits
            SET balance = ROUND(balance - v_cost, 9)
            WHERE user_id = p_user_id;
        END IF;
    ELSE
        -- Handle billing based on pricing model
        CASE v_pricing_model
            WHEN 'credits' THEN
                UPDATE public.credits
                SET balance = ROUND(balance - v_cost, 9)
                WHERE user_id = p_user_id;
                
            WHEN 'subscription' THEN
                UPDATE public.quotas q
                SET used_amount = ROUND(used_amount + v_billing_duration_minutes, 9)
                FROM public.user_subscriptions us
                WHERE q.subscription_id = us.id
                AND us.user_id = p_user_id
                AND q.service = 'transcription'
                AND us.status = 'active'
                AND q.reset_date > NOW();
                
            WHEN 'payg' THEN
                INSERT INTO public.payg_usage (
                    user_id, 
                    month, 
                    total_amount, 
                    payment_status
                ) VALUES (
                    p_user_id, 
                    date_trunc('month', CURRENT_DATE), 
                    v_cost,
                    'pending'
                )
                ON CONFLICT (user_id, month) 
                DO UPDATE SET 
                    total_amount = ROUND(payg_usage.total_amount + EXCLUDED.total_amount, 9),
                    payment_status = 
                        CASE 
                            WHEN payg_usage.payment_status = 'paid' THEN 'pending'
                            ELSE payg_usage.payment_status
                        END;
        END CASE;
    END IF;
END;
$$;


ALTER FUNCTION public.finalize_realtime_session(p_user_id uuid, p_api_key_id uuid, p_model text, p_session_id text, p_start_time bigint, p_audio_bytes_sent bigint, p_has_transcription boolean, p_token_info jsonb, p_processed_duration_seconds numeric) OWNER TO postgres;

--
-- Name: finalize_usage(uuid, uuid, text, text, numeric, numeric, text, jsonb, uuid); Type: FUNCTION; Schema: public; Owner: supabase_admin
--

CREATE FUNCTION public.finalize_usage(p_user_id uuid, p_api_key_id uuid, p_service text, p_model text, p_amount numeric, p_cost numeric, p_pricing_model text, p_metadata jsonb, p_pending_usage_id uuid DEFAULT NULL::uuid) RETURNS void
    LANGUAGE plpgsql
    AS $$
DECLARE
    -- Token-based variables
    input_token_count INTEGER;
    output_token_count INTEGER;
    input_cost_per_token DECIMAL(18, 9);
    output_cost_per_token DECIMAL(18, 9);
    total_tokens INTEGER;
    total_token_cost DECIMAL(18, 9);
    
    -- Service type
    pricing_unit TEXT;
    is_token_based BOOLEAN;
    
    -- Subscription variables
    subscription_quota_available DECIMAL(18, 9);
    subscription_id UUID;
    
    -- Credit variables
    total_credits_available DECIMAL(18, 9);
    remaining_cost DECIMAL(18, 9);
    credit_record RECORD;
    
    -- Hybrid billing variables (transcription only)
    remaining_amount DECIMAL(18, 9);
    remaining_amount_cost DECIMAL(18, 9);
BEGIN
    -- Remove pending usage entry
    IF p_pending_usage_id IS NOT NULL THEN
        DELETE FROM public.usage_history
        WHERE id = p_pending_usage_id AND status = 'pending';
    ELSE
        DELETE FROM public.usage_history
        WHERE id = (
            SELECT id FROM public.usage_history
            WHERE user_id = p_user_id
            AND service = p_service
            AND model = p_model
            AND status = 'pending'
            ORDER BY created_at DESC
            LIMIT 1
        );
    END IF;

    -- Determine service type
    SELECT sp.unit INTO pricing_unit
    FROM public.service_pricing sp
    WHERE sp.service = p_service
    AND sp.model = p_model
    AND sp.is_active = TRUE;

    is_token_based := pricing_unit = 'token-based';

    -- Get subscription quota if available
    SELECT q.total_amount - q.used_amount, q.subscription_id
    INTO subscription_quota_available, subscription_id
    FROM public.quotas q
    WHERE q.user_id = p_user_id
    AND q.service = p_service
    AND q.subscription_id IS NOT NULL
    AND q.reset_date > NOW()
    AND q.total_amount - q.used_amount > 0
    AND EXISTS (
        SELECT 1 FROM public.user_subscriptions us
        WHERE us.id = q.subscription_id
        AND us.user_id = p_user_id
        AND us.status = 'active'
    );

    -- Get total available credits
    SELECT COALESCE(SUM(balance), 0) INTO total_credits_available
    FROM public.credits
    WHERE user_id = p_user_id
    AND balance > 0
    AND (expires_at IS NULL OR expires_at > NOW())
    AND status = 'active';

    -- OPTIMIZATION SERVICE (TOKEN-BASED)
    IF p_service = 'optimization' THEN
        -- Extract token data
        input_token_count := COALESCE((p_metadata->>'inputTokens')::INTEGER, 0);
        output_token_count := COALESCE((p_metadata->>'outputTokens')::INTEGER, 0);
        total_tokens := input_token_count + output_token_count;
        
        -- Get pricing using exact model names
        SELECT sp.cost_per_unit INTO input_cost_per_token
        FROM public.service_pricing sp
        WHERE sp.service = 'optimization'
        AND sp.model = p_model || '/input'
        AND sp.is_active = true;
        
        SELECT sp.cost_per_unit INTO output_cost_per_token
        FROM public.service_pricing sp
        WHERE sp.service = 'optimization'
        AND sp.model = p_model || '/output'
        AND sp.is_active = true;
        
        IF input_cost_per_token IS NULL OR output_cost_per_token IS NULL THEN
            RAISE EXCEPTION 'Pricing data not found for model: % (service: %)', p_model, p_service;
        END IF;
        
        total_token_cost := ROUND(input_token_count * input_cost_per_token + output_token_count * output_cost_per_token, 9);

        -- OPTIMIZATION BILLING LOGIC
        IF subscription_quota_available IS NOT NULL AND subscription_quota_available >= total_tokens THEN
            -- Case 1: Sufficient quota - use subscription
            INSERT INTO public.usage_history (
                user_id, api_key_id, service, model, amount, cost, pricing_model, status, metadata
            ) VALUES (
                p_user_id, p_api_key_id, p_service, p_model, total_tokens, 0, 'subscription', 'success',
                p_metadata || jsonb_build_object(
                    'paymentMethod', 'subscription',
                    'inputTokens', input_token_count,
                    'outputTokens', output_token_count,
                    'subscriptionId', subscription_id
                )
            );
            
            UPDATE public.quotas
            SET used_amount = used_amount + total_tokens
            WHERE user_id = p_user_id AND service = p_service AND public.quotas.subscription_id IS NOT NULL;
            
        ELSIF total_credits_available >= total_token_cost THEN
            -- Case 2: Insufficient quota but sufficient credits - use credits
            INSERT INTO public.usage_history (
                user_id, api_key_id, service, model, amount, cost, pricing_model, status, metadata
            ) VALUES (
                p_user_id, p_api_key_id, p_service, p_model, total_tokens, total_token_cost, 'credits', 'success',
                p_metadata || jsonb_build_object(
                    'paymentMethod', 'credits',
                    'inputTokens', input_token_count,
                    'outputTokens', output_token_count
                )
            );
            
            -- Deduct from credits
            remaining_cost := total_token_cost;
            FOR credit_record IN
                SELECT id, balance, expires_at
                FROM public.credits
                WHERE user_id = p_user_id
                AND balance > 0
                AND (expires_at IS NULL OR expires_at > NOW())
                AND status = 'active'
                ORDER BY
                    CASE WHEN expires_at IS NULL THEN 1 ELSE 0 END,
                    expires_at ASC
            LOOP
                IF credit_record.balance >= remaining_cost THEN
                    IF ROUND(credit_record.balance - remaining_cost, 9) = 0 THEN
                        DELETE FROM public.credits WHERE id = credit_record.id;
                    ELSE
                        UPDATE public.credits
                        SET balance = ROUND(balance - remaining_cost, 9)
                        WHERE id = credit_record.id;
                    END IF;
                    remaining_cost := 0;
                    EXIT;
                ELSE
                    DELETE FROM public.credits WHERE id = credit_record.id;
                    remaining_cost := ROUND(remaining_cost - credit_record.balance, 9);
                END IF;
            END LOOP;
            
        ELSE
            -- Case 3: Insufficient quota and credits - allow quota overuse
            INSERT INTO public.usage_history (
                user_id, api_key_id, service, model, amount, cost, pricing_model, status, metadata
            ) VALUES (
                p_user_id, p_api_key_id, p_service, p_model, total_tokens, 0, 'subscription', 'success',
                p_metadata || jsonb_build_object(
                    'paymentMethod', 'subscription',
                    'inputTokens', input_token_count,
                    'outputTokens', output_token_count,
                    'subscriptionId', subscription_id,
                    'quotaOveruse', true
                )
            );
            
            UPDATE public.quotas
            SET used_amount = used_amount + total_tokens
            WHERE user_id = p_user_id AND service = p_service AND public.quotas.subscription_id IS NOT NULL;
        END IF;

    -- TRANSCRIPTION SERVICES (DURATION-BASED)
    ELSE
        -- TRANSCRIPTION BILLING LOGIC
        IF subscription_quota_available IS NOT NULL AND subscription_quota_available >= p_amount THEN
            -- Case 1: Sufficient quota - use subscription
            INSERT INTO public.usage_history (
                user_id, api_key_id, service, model, amount, cost, pricing_model, status, metadata
            ) VALUES (
                p_user_id, p_api_key_id, p_service, p_model, p_amount, 0, 'subscription', 'success',
                p_metadata || jsonb_build_object(
                    'paymentMethod', 'subscription',
                    'subscriptionId', subscription_id
                )
            );
            
            UPDATE public.quotas
            SET used_amount = used_amount + p_amount
            WHERE user_id = p_user_id AND service = p_service AND public.quotas.subscription_id IS NOT NULL;
            
        ELSIF subscription_quota_available IS NOT NULL AND subscription_quota_available > 0 THEN
            -- Case 2: Partial quota available - HYBRID BILLING
            remaining_amount := p_amount - subscription_quota_available;
            remaining_amount_cost := ROUND((remaining_amount / p_amount) * p_cost, 9);
            
            -- Use available quota first
            INSERT INTO public.usage_history (
                user_id, api_key_id, service, model, amount, cost, pricing_model, status, metadata
            ) VALUES (
                p_user_id, p_api_key_id, p_service, p_model, subscription_quota_available, 0, 'subscription', 'success',
                p_metadata || jsonb_build_object(
                    'paymentMethod', 'subscription',
                    'subscriptionId', subscription_id,
                    'hybridBilling', true,
                    'quotaPortion', subscription_quota_available
                )
            );
            
            UPDATE public.quotas
            SET used_amount = used_amount + subscription_quota_available
            WHERE user_id = p_user_id AND service = p_service AND public.quotas.subscription_id IS NOT NULL;
            
            -- Use credits for remaining amount
            INSERT INTO public.usage_history (
                user_id, api_key_id, service, model, amount, cost, pricing_model, status, metadata
            ) VALUES (
                p_user_id, p_api_key_id, p_service, p_model, remaining_amount, remaining_amount_cost, 'credits', 'success',
                p_metadata || jsonb_build_object(
                    'paymentMethod', 'credits',
                    'hybridBilling', true,
                    'creditsPortion', remaining_amount
                )
            );
            
            -- Deduct from credits
            remaining_cost := remaining_amount_cost;
            FOR credit_record IN
                SELECT id, balance, expires_at
                FROM public.credits
                WHERE user_id = p_user_id
                AND balance > 0
                AND (expires_at IS NULL OR expires_at > NOW())
                AND status = 'active'
                ORDER BY
                    CASE WHEN expires_at IS NULL THEN 1 ELSE 0 END,
                    expires_at ASC
            LOOP
                IF credit_record.balance >= remaining_cost THEN
                    IF ROUND(credit_record.balance - remaining_cost, 9) = 0 THEN
                        DELETE FROM public.credits WHERE id = credit_record.id;
                    ELSE
                        UPDATE public.credits
                        SET balance = ROUND(balance - remaining_cost, 9)
                        WHERE id = credit_record.id;
                    END IF;
                    remaining_cost := 0;
                    EXIT;
                ELSE
                    DELETE FROM public.credits WHERE id = credit_record.id;
                    remaining_cost := ROUND(remaining_cost - credit_record.balance, 9);
                END IF;
            END LOOP;
            
        ELSE
            -- Case 3: No quota available - use credits
            INSERT INTO public.usage_history (
                user_id, api_key_id, service, model, amount, cost, pricing_model, status, metadata
            ) VALUES (
                p_user_id, p_api_key_id, p_service, p_model, p_amount, p_cost, 'credits', 'success',
                p_metadata || jsonb_build_object('paymentMethod', 'credits')
            );
            
            -- Deduct from credits
            remaining_cost := p_cost;
            FOR credit_record IN
                SELECT id, balance, expires_at
                FROM public.credits
                WHERE user_id = p_user_id
                AND balance > 0
                AND (expires_at IS NULL OR expires_at > NOW())
                AND status = 'active'
                ORDER BY
                    CASE WHEN expires_at IS NULL THEN 1 ELSE 0 END,
                    expires_at ASC
            LOOP
                IF credit_record.balance >= remaining_cost THEN
                    IF ROUND(credit_record.balance - remaining_cost, 9) = 0 THEN
                        DELETE FROM public.credits WHERE id = credit_record.id;
                    ELSE
                        UPDATE public.credits
                        SET balance = ROUND(balance - remaining_cost, 9)
                        WHERE id = credit_record.id;
                    END IF;
                    remaining_cost := 0;
                    EXIT;
                ELSE
                    DELETE FROM public.credits WHERE id = credit_record.id;
                    remaining_cost := ROUND(remaining_cost - credit_record.balance, 9);
                END IF;
            END LOOP;
        END IF;
    END IF;
END;
$$;


ALTER FUNCTION public.finalize_usage(p_user_id uuid, p_api_key_id uuid, p_service text, p_model text, p_amount numeric, p_cost numeric, p_pricing_model text, p_metadata jsonb, p_pending_usage_id uuid) OWNER TO supabase_admin;

--
-- Name: get_or_create_user_credits(uuid, numeric, text); Type: FUNCTION; Schema: public; Owner: supabase_admin
--

CREATE FUNCTION public.get_or_create_user_credits(p_user_id uuid, p_initial_balance numeric DEFAULT 0, p_currency text DEFAULT 'USD'::text) RETURNS numeric
    LANGUAGE plpgsql
    SET search_path TO 'public'
    AS $$
DECLARE
    v_balance DECIMAL(18,9);
BEGIN
    -- Try to insert, or do nothing if already exists
    INSERT INTO public.credits (user_id, balance, currency, created_at, updated_at)
    VALUES (p_user_id, p_initial_balance, p_currency, NOW(), NOW())
    ON CONFLICT (user_id) DO NOTHING;
    
    -- Get the current balance
    SELECT balance INTO v_balance
    FROM public.credits
    WHERE user_id = p_user_id;
    
    RETURN v_balance;
END;
$$;


ALTER FUNCTION public.get_or_create_user_credits(p_user_id uuid, p_initial_balance numeric, p_currency text) OWNER TO supabase_admin;

--
-- Name: FUNCTION get_or_create_user_credits(p_user_id uuid, p_initial_balance numeric, p_currency text); Type: COMMENT; Schema: public; Owner: supabase_admin
--

COMMENT ON FUNCTION public.get_or_create_user_credits(p_user_id uuid, p_initial_balance numeric, p_currency text) IS 'Creates a credit record for a user if one does not exist, and returns the current balance.';


--
-- Name: get_paddle_customer_by_user_id(uuid); Type: FUNCTION; Schema: public; Owner: supabase_admin
--

CREATE FUNCTION public.get_paddle_customer_by_user_id(p_user_id uuid) RETURNS TABLE(paddle_customer_id text, email text, name text, metadata jsonb)
    LANGUAGE plpgsql
    SET search_path TO 'public'
    AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pc.paddle_customer_id,
        pc.email,
        pc.name,
        pc.metadata
    FROM public.paddle_customers pc
    WHERE pc.user_id = p_user_id;
END;
$$;


ALTER FUNCTION public.get_paddle_customer_by_user_id(p_user_id uuid) OWNER TO supabase_admin;

--
-- Name: FUNCTION get_paddle_customer_by_user_id(p_user_id uuid); Type: COMMENT; Schema: public; Owner: supabase_admin
--

COMMENT ON FUNCTION public.get_paddle_customer_by_user_id(p_user_id uuid) IS 'Get Paddle customer info for a user (used by edge functions)';


--
-- Name: get_payment_failure_stats(text, integer); Type: FUNCTION; Schema: public; Owner: supabase_admin
--

CREATE FUNCTION public.get_payment_failure_stats(p_subscription_id text, p_days integer DEFAULT 30) RETURNS json
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_failure_count INTEGER;
    v_total_failed_amount DECIMAL(10,2);
    v_last_failure_date TIMESTAMPTZ;
    v_recent_failures JSON;
BEGIN
    -- Get failure count and total amount
    SELECT 
        COUNT(*),
        COALESCE(SUM(amount), 0),
        MAX(occurred_at)
    INTO v_failure_count, v_total_failed_amount, v_last_failure_date
    FROM public.payment_failures
    WHERE subscription_id = p_subscription_id
    AND occurred_at >= NOW() - INTERVAL '1 day' * p_days;

    -- Get recent failures details
    SELECT JSON_AGG(
        JSON_BUILD_OBJECT(
            'transaction_id', transaction_id,
            'amount', amount,
            'currency', currency,
            'failure_reason', failure_reason,
            'occurred_at', occurred_at
        )
        ORDER BY occurred_at DESC
    ) INTO v_recent_failures
    FROM public.payment_failures
    WHERE subscription_id = p_subscription_id
    AND occurred_at >= NOW() - INTERVAL '1 day' * p_days
    LIMIT 10;

    RETURN JSON_BUILD_OBJECT(
        'subscription_id', p_subscription_id,
        'period_days', p_days,
        'failure_count', v_failure_count,
        'total_failed_amount', v_total_failed_amount,
        'last_failure_date', v_last_failure_date,
        'recent_failures', COALESCE(v_recent_failures, '[]'::JSON)
    );
END;
$$;


ALTER FUNCTION public.get_payment_failure_stats(p_subscription_id text, p_days integer) OWNER TO supabase_admin;

--
-- Name: get_unpaid_payg_balances(uuid); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.get_unpaid_payg_balances(p_user_id uuid) RETURNS TABLE(id uuid, month date, total_amount numeric, payment_status public.payment_status, payment_metadata jsonb, created_at timestamp with time zone)
    LANGUAGE plpgsql
    AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pu.id,
        pu.month,
        pu.total_amount,
        pu.payment_status,
        pu.payment_metadata,
        pu.created_at
    FROM 
        public.payg_usage pu
    WHERE 
        pu.user_id = p_user_id
        AND pu.payment_status = 'unpaid'
        AND pu.month < date_trunc('month', CURRENT_DATE)
    ORDER BY 
        pu.month DESC;
END;
$$;


ALTER FUNCTION public.get_unpaid_payg_balances(p_user_id uuid) OWNER TO postgres;

--
-- Name: get_usage_summary_statistics(text, timestamp with time zone, timestamp with time zone, text, text, text); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.get_usage_summary_statistics(p_user_id text, p_start_date timestamp with time zone DEFAULT NULL::timestamp with time zone, p_end_date timestamp with time zone DEFAULT NULL::timestamp with time zone, p_service text DEFAULT NULL::text, p_model text DEFAULT NULL::text, p_status text DEFAULT NULL::text) RETURNS json
    LANGUAGE plpgsql
    AS $_$
DECLARE
  v_total_cost NUMERIC;
  v_service_breakdown JSON;
  v_model_breakdown JSON;
  v_available_services TEXT[];
  v_available_models TEXT[];
  v_total_minutes NUMERIC;
  v_total_input_tokens NUMERIC;
  v_total_output_tokens NUMERIC;
  v_minutes_by_day JSON;
  v_tokens_by_day JSON;
  v_result JSON;
  v_query TEXT;
  v_debug_info JSON;
BEGIN
  -- Debug information to help troubleshoot timezone issues
  v_debug_info := json_build_object(
    'original_end_date', p_end_date,
    'timezone_offset', EXTRACT(TIMEZONE FROM p_end_date),
    'timezone', current_setting('TIMEZONE')
  );

  -- No need to adjust end date as we're using a different approach now
  -- We expect the client to send the start of the next day (00:00:00)
  -- which is more reliable across timezones

  -- First, calculate the total cost and service breakdown directly from the usage_history table
  -- This ensures we're getting accurate sums without any potential calculation issues
  EXECUTE 
    'WITH service_costs AS (
      SELECT 
        service,
        SUM(cost) as service_cost
      FROM 
        usage_history
      WHERE 
        user_id = $1::uuid
        AND status = ''success''
        ' || CASE WHEN p_start_date IS NOT NULL THEN 'AND created_at >= $2' ELSE '' END || '
        ' || CASE WHEN p_end_date IS NOT NULL THEN 'AND created_at < $3' ELSE '' END || '
        ' || CASE WHEN p_service IS NOT NULL THEN 'AND service = $4' ELSE '' END || '
        ' || CASE WHEN p_model IS NOT NULL THEN 'AND model = $5' ELSE '' END || '
        ' || CASE WHEN p_status IS NOT NULL THEN 'AND status = $6' ELSE '' END || '
      GROUP BY 
        service
    )
    SELECT 
      COALESCE(SUM(service_cost), 0) as total_cost,
      COALESCE(jsonb_object_agg(service, service_cost), ''{}''::jsonb) as service_breakdown
    FROM 
      service_costs'
  INTO 
    v_total_cost, v_service_breakdown
  USING 
    p_user_id,
    p_start_date,
    p_end_date,
    p_service,
    p_model,
    p_status;

  -- Now build the query for the rest of the statistics
  v_query := 'WITH usage_summary AS (
    SELECT 
      service,
      model,
      SUM(cost) as total_cost_per_group,
      SUM(CASE WHEN service = ''transcription'' THEN amount ELSE 0 END) as total_minutes,
      SUM(CASE 
          WHEN metadata->>''token_based'' = ''true'' OR service = ''optimization'' 
          THEN COALESCE((metadata->>''input_tokens'')::numeric, 
                        (metadata->>''inputTokens'')::numeric, 0) 
          ELSE 0 
      END) as total_input_tokens,
      SUM(CASE 
          WHEN metadata->>''token_based'' = ''true'' OR service = ''optimization'' 
          THEN COALESCE((metadata->>''output_tokens'')::numeric, 
                        (metadata->>''outputTokens'')::numeric, 0) 
          ELSE 0 
      END) as total_output_tokens
    FROM 
      usage_history
    WHERE 
      user_id = $1::uuid
      AND status = ''success''';
  
  -- Add filters with fixed parameter positions
  IF p_start_date IS NOT NULL THEN
    v_query := v_query || ' AND created_at >= $2';
  END IF;
  
  IF p_end_date IS NOT NULL THEN
    -- Use < rather than <= for the end date since we're sending start of next day
    v_query := v_query || ' AND created_at < $3';
  END IF;
  
  IF p_service IS NOT NULL THEN
    v_query := v_query || ' AND service = $4';
  END IF;
  
  IF p_model IS NOT NULL THEN
    v_query := v_query || ' AND model = $5';
  END IF;
  
  IF p_status IS NOT NULL THEN
    v_query := v_query || ' AND status = $6';
  END IF;
  
  -- Complete the query with proper grouping and aggregation
  v_query := v_query || ' GROUP BY service, model),
  
  -- Daily aggregation for charts
  daily_usage AS (
    SELECT 
      DATE_TRUNC(''day'', created_at) as usage_date,
      SUM(CASE WHEN service = ''transcription'' THEN amount ELSE 0 END) as minutes_used,
      SUM(CASE 
          WHEN metadata->>''token_based'' = ''true'' OR service = ''optimization'' 
          THEN COALESCE((metadata->>''input_tokens'')::numeric, 
                        (metadata->>''inputTokens'')::numeric, 0) 
          ELSE 0 
      END) as input_tokens_used,
      SUM(CASE 
          WHEN metadata->>''token_based'' = ''true'' OR service = ''optimization'' 
          THEN COALESCE((metadata->>''output_tokens'')::numeric, 
                        (metadata->>''outputTokens'')::numeric, 0) 
          ELSE 0 
      END) as output_tokens_used
    FROM 
      usage_history
    WHERE 
      user_id = $1::uuid
      AND status = ''success''';
  
  -- Add the same filters to daily aggregation
  IF p_start_date IS NOT NULL THEN
    v_query := v_query || ' AND created_at >= $2';
  END IF;
  
  IF p_end_date IS NOT NULL THEN
    -- Use < rather than <= for the end date since we're sending start of next day
    v_query := v_query || ' AND created_at < $3';
  END IF;
  
  IF p_service IS NOT NULL THEN
    v_query := v_query || ' AND service = $4';
  END IF;
  
  IF p_model IS NOT NULL THEN
    v_query := v_query || ' AND model = $5';
  END IF;
  
  IF p_status IS NOT NULL THEN
    v_query := v_query || ' AND status = $6';
  END IF;
  
  v_query := v_query || ' GROUP BY usage_date
    ORDER BY usage_date
  )
  
  SELECT 
    COALESCE(
      jsonb_object_agg(model, total_cost_per_group) FILTER (WHERE model IS NOT NULL),
      ''{}''::jsonb
    ) AS model_breakdown,
    ARRAY_AGG(DISTINCT service) FILTER (WHERE service IS NOT NULL) AS available_services,
    ARRAY_AGG(DISTINCT model) FILTER (WHERE model IS NOT NULL) AS available_models,
    COALESCE(SUM(total_minutes), 0) AS total_minutes,
    COALESCE(SUM(total_input_tokens), 0) AS total_input_tokens,
    COALESCE(SUM(total_output_tokens), 0) AS total_output_tokens,
    (SELECT 
      COALESCE(
        jsonb_agg(
          jsonb_build_object(
            ''date'', usage_date,
            ''minutes'', minutes_used
          )
        ),
        ''[]''::jsonb
      ) 
    FROM daily_usage) AS minutes_by_day,
    (SELECT 
      COALESCE(
        jsonb_agg(
          jsonb_build_object(
            ''date'', usage_date,
            ''input_tokens'', input_tokens_used,
            ''output_tokens'', output_tokens_used
          )
        ),
        ''[]''::jsonb
      ) 
    FROM daily_usage) AS tokens_by_day
  FROM usage_summary';
  
  -- Execute the query with parameters to get the remaining statistics
  EXECUTE v_query 
  INTO v_model_breakdown, v_available_services, v_available_models, 
       v_total_minutes, v_total_input_tokens, v_total_output_tokens, v_minutes_by_day, v_tokens_by_day
  USING 
    p_user_id,
    p_start_date,
    p_end_date,
    p_service,
    p_model,
    p_status;
  
  -- Build the result JSON
  v_result := json_build_object(
    'total_cost', v_total_cost,
    'service_breakdown', v_service_breakdown,
    'model_breakdown', v_model_breakdown,
    'available_services', v_available_services,
    'available_models', v_available_models,
    'total_minutes', v_total_minutes,
    'total_input_tokens', v_total_input_tokens,
    'total_output_tokens', v_total_output_tokens,
    'minutes_by_day', v_minutes_by_day,
    'tokens_by_day', v_tokens_by_day,
    'debug_info', v_debug_info  -- Include debug info
  );
  
  RETURN v_result;
END;
$_$;


ALTER FUNCTION public.get_usage_summary_statistics(p_user_id text, p_start_date timestamp with time zone, p_end_date timestamp with time zone, p_service text, p_model text, p_status text) OWNER TO postgres;

--
-- Name: handle_expired_free_trials(); Type: FUNCTION; Schema: public; Owner: supabase_admin
--

CREATE FUNCTION public.handle_expired_free_trials() RETURNS void
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
  email_service_url TEXT := 'https://supabase.voicehype.ai/functions/v1/email-service';
  webhook_secret TEXT;
  email_payload JSONB;
  request_id BIGINT;
  expired_user RECORD;
BEGIN
  -- Get webhook secret
  SELECT decrypted_secret INTO webhook_secret
  FROM vault.decrypted_secrets
  WHERE name = 'email_webhook_secret';
  
  IF webhook_secret IS NULL THEN
    RAISE NOTICE 'ERROR: Webhook secret not found!';
    RETURN;
  END IF;

  -- Process expired free trials
  FOR expired_user IN 
    SELECT u.id, u.email, u.raw_user_meta_data ->> 'name' as name
    FROM auth.users u
    JOIN user_subscriptions us ON u.id = us.user_id
    WHERE us.paddle_subscription_id LIKE 'trial_%'
      AND us.current_period_end <= NOW()
      AND us.status IN ('active', 'past_due')  -- Only process active or past_due trials
  LOOP
    -- Update expired trial status
    UPDATE user_subscriptions 
    SET status = 'canceled'
    WHERE user_id = expired_user.id 
      AND paddle_subscription_id LIKE 'trial_%'
      AND current_period_end <= NOW();
    
    -- Prepare email payload
    email_payload := jsonb_build_object(
      'user', jsonb_build_object(
        'id', expired_user.id,
        'name', expired_user.name,
        'email', expired_user.email
      ),
      'email_data', jsonb_build_object(
        'email_action_type', 'free_trial_expired',
        'site_url', 'https://voicehype.ai'
      )
    );
    
    -- Send email notification
    SELECT net.http_post(
      url => email_service_url,
      body => email_payload,
      headers => jsonb_build_object(
        'Content-Type', 'application/json',
        'User-Agent', 'Supabase-Cron-Job',
        'Authorization', 'Bearer ' || webhook_secret
      ),
      timeout_milliseconds => 5000
    ) INTO request_id;
    
    RAISE NOTICE 'Free trial expired email sent for user: %, request_id: %', expired_user.email, request_id;
  END LOOP;
END;
$$;


ALTER FUNCTION public.handle_expired_free_trials() OWNER TO supabase_admin;

--
-- Name: handle_new_user(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.handle_new_user() RETURNS trigger
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
    v_profile_id UUID;
    v_subscription_id UUID;
    v_plan_id UUID;
    v_current_period_start TIMESTAMPTZ;
    v_current_period_end TIMESTAMPTZ;
    v_trial_subscription_id TEXT;
BEGIN
    -- Create a profile for the new user with default credit_allowance
    INSERT INTO public.profiles (id, email, full_name, credit_allowance, created_at, updated_at)
    VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'full_name', 10.0, NOW(), NOW())
    RETURNING id INTO v_profile_id;
    
    -- Rest of the existing function remains the same
    -- Get the Free Trial plan ID
    SELECT id INTO v_plan_id FROM public.subscription_plans WHERE name = 'Free Trial';
    
    -- Set subscription period (1 month from now)
    v_current_period_start := NOW();
    v_current_period_end := v_current_period_start + INTERVAL '1 month';
    
    -- Generate a placeholder for paddle_subscription_id
    v_trial_subscription_id := 'trial_' || NEW.id;
    
    -- Create a subscription for the new user
    INSERT INTO public.user_subscriptions (
        user_id,
        plan_id,
        status,
        current_period_start,
        current_period_end,
        paddle_subscription_id,
        created_at
    ) VALUES (
        NEW.id,
        v_plan_id,
        'active',
        v_current_period_start,
        v_current_period_end,
        v_trial_subscription_id,
        NOW()
    ) RETURNING id INTO v_subscription_id;
    
    -- Create initial quotas for the new user linked to the subscription
    -- Transcription minutes
    INSERT INTO public.quotas (
        user_id, 
        subscription_id, 
        service, 
        used_amount, 
        total_amount, 
        reset_date, 
        created_at
    ) VALUES (
        NEW.id, 
        v_subscription_id, 
        'transcription', 
        0, 
        60, 
        v_current_period_end, 
        NOW()
    );
    
    -- Tokens
    INSERT INTO public.quotas (
        user_id, 
        subscription_id, 
        service, 
        used_amount, 
        total_amount, 
        reset_date, 
        created_at
    ) VALUES (
        NEW.id, 
        v_subscription_id, 
        'optimization', 
        0, 
        10000, 
        v_current_period_end, 
        NOW()
    );
    
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.handle_new_user() OWNER TO postgres;

--
-- Name: handle_subscription_cancellation(text, uuid); Type: FUNCTION; Schema: public; Owner: supabase_admin
--

CREATE FUNCTION public.handle_subscription_cancellation(p_paddle_subscription_id text, p_user_id uuid DEFAULT NULL::uuid) RETURNS jsonb
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_user_id UUID;
    v_result JSONB;
BEGIN
    -- If user_id not provided, try to find it from existing subscription
    IF p_user_id IS NULL THEN
        SELECT user_id INTO v_user_id
        FROM user_subscriptions
        WHERE stripe_subscription_id = p_paddle_subscription_id
        LIMIT 1;
        
        IF v_user_id IS NULL THEN
            RAISE LOG 'User not found for subscription: %', p_paddle_subscription_id;
            RETURN jsonb_build_object('success', false, 'error', 'User not found');
        END IF;
    ELSE
        v_user_id := p_user_id;
    END IF;
    
    -- Update user subscription status
    UPDATE user_subscriptions 
    SET 
        status = 'canceled'
    WHERE user_id = v_user_id;
    
    -- Optional: Update paddle.subscriptions if exists
    BEGIN
        UPDATE paddle.subscriptions 
        SET 
            status = 'cancelled',
            cancel_at_period_end = true
        WHERE paddle_subscription_id = p_paddle_subscription_id;
    EXCEPTION
        WHEN undefined_table THEN
            RAISE LOG 'paddle.subscriptions table does not exist, skipping';
        WHEN OTHERS THEN
            RAISE LOG 'Error updating paddle subscription: %', SQLERRM;
    END;
    
    -- Note: We don't delete quotas on cancellation - they remain until period end
    
    v_result := jsonb_build_object(
        'success', true,
        'user_id', v_user_id,
        'subscription_id', p_paddle_subscription_id,
        'status', 'canceled'
    );
    
    RAISE LOG 'Successfully canceled subscription: %', v_result;
    
    RETURN v_result;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE LOG 'Error in handle_subscription_cancellation: %', SQLERRM;
        RETURN jsonb_build_object(
            'success', false,
            'error', SQLERRM,
            'subscription_id', p_paddle_subscription_id
        );
END;
$$;


ALTER FUNCTION public.handle_subscription_cancellation(p_paddle_subscription_id text, p_user_id uuid) OWNER TO supabase_admin;

--
-- Name: FUNCTION handle_subscription_cancellation(p_paddle_subscription_id text, p_user_id uuid); Type: COMMENT; Schema: public; Owner: supabase_admin
--

COMMENT ON FUNCTION public.handle_subscription_cancellation(p_paddle_subscription_id text, p_user_id uuid) IS 'Handle subscription cancellations with proper status updates';


--
-- Name: handle_subscription_transaction(uuid, text, text, text, jsonb, numeric, numeric, text); Type: FUNCTION; Schema: public; Owner: supabase_admin
--

CREATE FUNCTION public.handle_subscription_transaction(p_user_id uuid, p_paddle_subscription_id text, p_paddle_customer_id text, p_subscription_plan text, p_transaction_data jsonb DEFAULT NULL::jsonb, p_transcription_quota_override numeric DEFAULT NULL::numeric(10,2), p_optimization_quota_override numeric DEFAULT NULL::numeric(10,2), p_upgrade_flow text DEFAULT NULL::text) RETURNS jsonb
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_plan_record RECORD;
    v_subscription_id UUID;
    v_transcription_quota NUMERIC(10,2);
    v_optimization_quota NUMERIC(10,2);
    v_reset_date TIMESTAMP WITH TIME ZONE;
    v_result JSONB;
    v_is_scheduled_upgrade BOOLEAN := FALSE;
BEGIN
    -- Log the start of the operation
    RAISE LOG 'Starting subscription transaction for user: %, plan: %, upgrade_flow: %', p_user_id, p_subscription_plan, p_upgrade_flow;
    
    -- Check if this is a scheduled upgrade (update_next_billing)
    v_is_scheduled_upgrade := (p_upgrade_flow = 'update_next_billing');
    
    -- Set reset date to 30 days from now
    v_reset_date := NOW() + INTERVAL '30 days';
    
    -- Fetch plan details from subscription_plans table
    SELECT 
        id,
        name,
        transcription_minutes,
        tokens,
        monthly_price
    INTO v_plan_record
    FROM subscription_plans 
    WHERE LOWER(name) = LOWER(p_subscription_plan)
    AND is_active = true
    LIMIT 1;
    
    -- If plan not found, raise exception
    IF v_plan_record.id IS NULL THEN
        RAISE EXCEPTION 'Subscription plan not found: %', p_subscription_plan;
    END IF;
    
    -- Use quota overrides if provided, otherwise use plan values
    IF p_transcription_quota_override IS NOT NULL AND p_optimization_quota_override IS NOT NULL THEN
        -- Use the provided quota overrides (for quota merging scenarios)
        v_transcription_quota := p_transcription_quota_override;
        v_optimization_quota := p_optimization_quota_override;
        RAISE LOG 'Using quota overrides - transcription: %, optimization: %', v_transcription_quota, v_optimization_quota;
    ELSE
        -- Use plan values from subscription_plans table
        v_transcription_quota := v_plan_record.transcription_minutes::NUMERIC(10,2);
        v_optimization_quota := v_plan_record.tokens::NUMERIC(10,2);
        RAISE LOG 'Using plan quotas from subscription_plans - transcription: %, optimization: %', v_transcription_quota, v_optimization_quota;
    END IF;
    
    -- UPSERT user subscription (one subscription per user)
    -- For scheduled upgrades, only update the plan_id and updated_at
    IF v_is_scheduled_upgrade THEN
        RAISE LOG 'Processing scheduled upgrade - updating plan_id only, keeping current quotas';
        
        UPDATE user_subscriptions 
        SET 
            plan_id = v_plan_record.id,
            updated_at = NOW()
        WHERE user_id = p_user_id
        RETURNING id INTO v_subscription_id;
        
        -- If no subscription found to update, create a new one
        IF v_subscription_id IS NULL THEN
            INSERT INTO user_subscriptions (
                user_id,
                plan_id,
                status,
                current_period_start,
                current_period_end,
                paddle_subscription_id,
                created_at
            ) VALUES (
                p_user_id,
                v_plan_record.id,
                'active',
                NOW(),
                v_reset_date,
                p_paddle_subscription_id,
                NOW()
            )
            RETURNING id INTO v_subscription_id;
        END IF;
        
        -- For scheduled upgrades, DO NOT update quotas - they will be updated at next billing cycle
        RAISE LOG 'Scheduled upgrade processed - quotas will be updated at next billing cycle';
        
    ELSE
        -- Regular subscription processing (immediate upgrades or new subscriptions)
        INSERT INTO user_subscriptions (
            user_id,
            plan_id,
            status,
            current_period_start,
            current_period_end,
            paddle_subscription_id,
            created_at
        ) VALUES (
            p_user_id,
            v_plan_record.id,
            'active',
            NOW(),
            v_reset_date,
            p_paddle_subscription_id,
            NOW()
        )
        ON CONFLICT (user_id) 
        DO UPDATE SET
            plan_id = EXCLUDED.plan_id,
            status = EXCLUDED.status,
            current_period_start = EXCLUDED.current_period_start,
            current_period_end = EXCLUDED.current_period_end,
            paddle_subscription_id = EXCLUDED.paddle_subscription_id
        RETURNING id INTO v_subscription_id;
        
        RAISE LOG 'Upserted user subscription with ID: %', v_subscription_id;
        
        -- UPSERT transcription quota (one quota per user per service)
        INSERT INTO quotas (
            user_id,
            subscription_id,
            service,
            used_amount,
            total_amount,
            reset_date,
            created_at
        ) VALUES (
            p_user_id,
            v_subscription_id,
            'transcription',
            0,
            v_transcription_quota,
            v_reset_date,
            NOW()
        )
        ON CONFLICT (user_id, service)
        DO UPDATE SET
            subscription_id = EXCLUDED.subscription_id,
            used_amount = 0, -- Reset usage on subscription change
            total_amount = EXCLUDED.total_amount,
            reset_date = EXCLUDED.reset_date;
        
        -- UPSERT optimization quota (one quota per user per service)
        INSERT INTO quotas (
            user_id,
            subscription_id,
            service,
            used_amount,
            total_amount,
            reset_date,
            created_at
        ) VALUES (
            p_user_id,
            v_subscription_id,
            'optimization',
            0,
            v_optimization_quota,
            v_reset_date,
            NOW()
        )
        ON CONFLICT (user_id, service)
        DO UPDATE SET
            subscription_id = EXCLUDED.subscription_id,
            used_amount = 0, -- Reset usage on subscription change
            total_amount = EXCLUDED.total_amount,
            reset_date = EXCLUDED.reset_date;
    END IF;
    
    -- Optional: UPSERT into paddle.subscriptions table if it exists
    BEGIN
        INSERT INTO paddle.subscriptions (
            user_id,
            paddle_subscription_id,
            paddle_customer_id,
            product_id,
            status,
            current_period_start,
            current_period_end,
            metadata,
            created_at
        ) VALUES (
            p_user_id,
            p_paddle_subscription_id,
            p_paddle_customer_id,
            v_plan_record.id::TEXT,
            'active',
            NOW(),
            v_reset_date,
            p_transaction_data,
            NOW()
        )
        ON CONFLICT (paddle_subscription_id)
        DO UPDATE SET
            user_id = EXCLUDED.user_id,
            paddle_customer_id = EXCLUDED.paddle_customer_id,
            product_id = EXCLUDED.product_id,
            status = EXCLUDED.status,
            current_period_start = EXCLUDED.current_period_start,
            current_period_end = EXCLUDED.current_period_end,
            metadata = EXCLUDED.metadata;
        
        RAISE LOG 'Upserted paddle subscription record';
    EXCEPTION
        WHEN undefined_table THEN
            RAISE LOG 'paddle.subscriptions table does not exist, skipping';
        WHEN OTHERS THEN
            RAISE LOG 'Error upserting paddle subscription: %', SQLERRM;
    END;
    
    -- Build result JSON
    v_result := jsonb_build_object(
        'success', true,
        'user_id', p_user_id,
        'subscription_id', v_subscription_id,
        'plan', p_subscription_plan,
        'plan_id', v_plan_record.id,
        'upgrade_flow', p_upgrade_flow,
        'is_scheduled_upgrade', v_is_scheduled_upgrade,
        'quotas', jsonb_build_object(
            'transcription', v_transcription_quota,
            'optimization', v_optimization_quota
        ),
        'reset_date', v_reset_date,
        'plan_details', jsonb_build_object(
            'monthly_price', v_plan_record.monthly_price,
            'transcription_minutes', v_plan_record.transcription_minutes,
            'tokens', v_plan_record.tokens
        )
    );
    
    RAISE LOG 'Successfully processed subscription transaction: %', v_result;
    
    RETURN v_result;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE LOG 'Error in handle_subscription_transaction: %', SQLERRM;
        RETURN jsonb_build_object(
            'success', false,
            'error', SQLERRM,
            'user_id', p_user_id,
            'plan', p_subscription_plan,
            'upgrade_flow', p_upgrade_flow
        );
END;
$$;


ALTER FUNCTION public.handle_subscription_transaction(p_user_id uuid, p_paddle_subscription_id text, p_paddle_customer_id text, p_subscription_plan text, p_transaction_data jsonb, p_transcription_quota_override numeric, p_optimization_quota_override numeric, p_upgrade_flow text) OWNER TO supabase_admin;

--
-- Name: has_unpaid_payg_balance(uuid); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.has_unpaid_payg_balance(p_user_id uuid) RETURNS boolean
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
    has_unpaid BOOLEAN;
BEGIN
    -- Check if there are any unpaid PAYG balances from previous months
    SELECT EXISTS (
        SELECT 1 
        FROM public.payg_usage 
        WHERE user_id = p_user_id 
        AND payment_status = 'unpaid'
        AND month < date_trunc('month', CURRENT_DATE)
    ) INTO has_unpaid;
    
    RETURN has_unpaid;
END;
$$;


ALTER FUNCTION public.has_unpaid_payg_balance(p_user_id uuid) OWNER TO postgres;

--
-- Name: mark_payg_payment_as_paid(uuid, jsonb); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.mark_payg_payment_as_paid(p_payg_id uuid, p_payment_metadata jsonb DEFAULT NULL::jsonb) RETURNS boolean
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
    updated_count INTEGER;
BEGIN
    UPDATE public.payg_usage
    SET payment_status = 'paid',
        payment_metadata = COALESCE(p_payment_metadata, payment_metadata)
    WHERE id = p_payg_id
    AND payment_status != 'paid';
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    
    RETURN updated_count > 0;
END;
$$;


ALTER FUNCTION public.mark_payg_payment_as_paid(p_payg_id uuid, p_payment_metadata jsonb) OWNER TO postgres;

--
-- Name: mark_payg_payment_as_unpaid(uuid, text, jsonb); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.mark_payg_payment_as_unpaid(p_payg_id uuid, p_failure_reason text, p_payment_metadata jsonb DEFAULT NULL::jsonb) RETURNS boolean
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
    updated_count INTEGER;
    combined_metadata JSONB;
BEGIN
    -- Combine existing metadata with failure reason and new metadata
    SELECT 
        COALESCE(payment_metadata, '{}'::JSONB) || 
        jsonb_build_object('failure_reason', p_failure_reason) ||
        COALESCE(p_payment_metadata, '{}'::JSONB)
    INTO combined_metadata
    FROM public.payg_usage
    WHERE id = p_payg_id;
    
    -- Update the record
    UPDATE public.payg_usage
    SET payment_status = 'unpaid',
        payment_metadata = combined_metadata
    WHERE id = p_payg_id
    AND payment_status != 'unpaid';
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    
    RETURN updated_count > 0;
END;
$$;


ALTER FUNCTION public.mark_payg_payment_as_unpaid(p_payg_id uuid, p_failure_reason text, p_payment_metadata jsonb) OWNER TO postgres;

--
-- Name: reset_subscription_quotas(uuid); Type: FUNCTION; Schema: public; Owner: supabase_admin
--

CREATE FUNCTION public.reset_subscription_quotas(p_subscription_id uuid) RETURNS boolean
    LANGUAGE plpgsql
    AS $$
BEGIN
  -- Reset subscription quotas to zero usage
  UPDATE public.quotas 
  SET 
    used_amount = 0,
    reset_date = NOW() + INTERVAL '1 month'
  WHERE subscription_id = p_subscription_id;
  
  RETURN TRUE;
END;
$$;


ALTER FUNCTION public.reset_subscription_quotas(p_subscription_id uuid) OWNER TO supabase_admin;

--
-- Name: send_welcome_email(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.send_welcome_email() RETURNS trigger
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
  email_service_url TEXT := 'https://supabase.voicehype.ai/functions/v1/email-service';
  webhook_secret TEXT;
  email_payload JSONB;
  request_id BIGINT;
BEGIN
  -- Debug log to see if trigger fires at all
  RAISE NOTICE 'TRIGGER FIRED! Operation: %, User: %', TG_OP, NEW.email;

  -- Only process INSERT operations (new user signups)
  IF TG_OP = 'INSERT' THEN
    RAISE NOTICE 'Processing INSERT for user: %', NEW.email;

    -- Get webhook secret from vault
    SELECT decrypted_secret INTO webhook_secret
    FROM vault.decrypted_secrets
    WHERE name = 'email_webhook_secret';

    IF webhook_secret IS NULL THEN
      RAISE NOTICE 'ERROR: Webhook secret not found!';
      RETURN NEW;
    END IF;

    RAISE NOTICE 'Webhook secret found, preparing payload...';

    -- Prepare email payload (without missing fields)
    email_payload := jsonb_build_object(
      'user', jsonb_build_object(
        'id', NEW.id,
        'name', NEW.raw_user_meta_data ->> 'name',
        'email', NEW.email,
        'created_at', NEW.created_at,
        'email_confirmed_at', NEW.email_confirmed_at
      ),
      'email_data', jsonb_build_object(
        'email_action_type', 'welcome',
        'site_url', 'https://voicehype.ai'
      )
    );

    RAISE NOTICE 'Sending HTTP request...';

    -- Send async HTTP request (fire and forget)
    SELECT net.http_post(
      url => email_service_url,
      body => email_payload,
      headers => jsonb_build_object(
        'Content-Type', 'application/json',
        'User-Agent', 'Supabase-Database-Trigger',
        'Authorization', 'Bearer ' || webhook_secret
      ),
      timeout_milliseconds => 5000
    ) INTO request_id;

    -- Log the request for debugging
    RAISE NOTICE 'Welcome email triggered for user: %, request_id: %', NEW.email, request_id;

  ELSE
    RAISE NOTICE 'Skipping non-INSERT operation: %', TG_OP;
  END IF;

  RETURN NEW;

EXCEPTION
  WHEN OTHERS THEN
    -- Log error but don't block user signup
    RAISE NOTICE 'Failed to send welcome email: %', SQLERRM;
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.send_welcome_email() OWNER TO postgres;

--
-- Name: update_api_key_name(uuid, text); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.update_api_key_name(p_api_key_id uuid, p_name text) RETURNS boolean
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
    v_user_id UUID;
    v_count INTEGER;
BEGIN
    -- Get the current user's ID
    v_user_id := auth.uid();
    
    -- Check if user is authenticated
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'Not authenticated';
    END IF;
    
    -- Check if the API key belongs to the user
    SELECT COUNT(*) INTO v_count 
    FROM public.api_keys 
    WHERE id = p_api_key_id AND user_id = v_user_id;
    
    IF v_count = 0 THEN
        RAISE EXCEPTION 'API key not found or access denied';
    END IF;
    
    -- Update only the name field
    UPDATE public.api_keys
    SET name = p_name
    WHERE id = p_api_key_id;
    
    RETURN TRUE;
END;
$$;


ALTER FUNCTION public.update_api_key_name(p_api_key_id uuid, p_name text) OWNER TO postgres;

--
-- Name: FUNCTION update_api_key_name(p_api_key_id uuid, p_name text); Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON FUNCTION public.update_api_key_name(p_api_key_id uuid, p_name text) IS 'Updates only the name of an API key owned by the authenticated user';


--
-- Name: update_api_key_status(uuid, boolean); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.update_api_key_status(p_key_id uuid, p_is_active boolean) RETURNS boolean
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
    v_user_id UUID;
    v_key_exists BOOLEAN;
BEGIN
    -- Get the current user's ID
    v_user_id := auth.uid();
    
    -- Check if user is authenticated
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'Not authenticated';
    END IF;
    
    -- Check if the API key exists and belongs to the current user
    SELECT EXISTS (
        SELECT 1 
        FROM public.api_keys 
        WHERE id = p_key_id AND user_id = v_user_id
    ) INTO v_key_exists;
    
    IF NOT v_key_exists THEN
        RAISE EXCEPTION 'API key not found or does not belong to the current user';
    END IF;
    
    -- Update the API key status
    UPDATE public.api_keys
    SET is_active = p_is_active
    WHERE id = p_key_id AND user_id = v_user_id;
    
    RETURN TRUE;
END;
$$;


ALTER FUNCTION public.update_api_key_status(p_key_id uuid, p_is_active boolean) OWNER TO postgres;

--
-- Name: FUNCTION update_api_key_status(p_key_id uuid, p_is_active boolean); Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON FUNCTION public.update_api_key_status(p_key_id uuid, p_is_active boolean) IS 'Updates the active status of an API key for the authenticated user';


--
-- Name: update_updated_at_column(); Type: FUNCTION; Schema: public; Owner: supabase_admin
--

CREATE FUNCTION public.update_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.update_updated_at_column() OWNER TO supabase_admin;

--
-- Name: upsert_paddle_customer(uuid, text, text, text, text, jsonb); Type: FUNCTION; Schema: public; Owner: supabase_admin
--

CREATE FUNCTION public.upsert_paddle_customer(p_user_id uuid, p_paddle_customer_id text, p_email text, p_name text DEFAULT NULL::text, p_country_code text DEFAULT NULL::text, p_metadata jsonb DEFAULT '{}'::jsonb) RETURNS uuid
    LANGUAGE plpgsql
    SET search_path TO 'public'
    AS $$
DECLARE
    v_customer_id UUID;
BEGIN
    INSERT INTO public.paddle_customers (
        user_id,
        paddle_customer_id,
        email,
        name,
        country_code,
        metadata
    ) VALUES (
        p_user_id,
        p_paddle_customer_id,
        p_email,
        p_name,
        p_country_code,
        p_metadata
    )
    ON CONFLICT (paddle_customer_id) 
    DO UPDATE SET
        email = COALESCE(EXCLUDED.email, paddle_customers.email),
        name = COALESCE(EXCLUDED.name, paddle_customers.name),
        country_code = COALESCE(EXCLUDED.country_code, paddle_customers.country_code),
        metadata = EXCLUDED.metadata,
        updated_at = NOW()
    RETURNING id INTO v_customer_id;
    
    RETURN v_customer_id;
END;
$$;


ALTER FUNCTION public.upsert_paddle_customer(p_user_id uuid, p_paddle_customer_id text, p_email text, p_name text, p_country_code text, p_metadata jsonb) OWNER TO supabase_admin;

--
-- Name: FUNCTION upsert_paddle_customer(p_user_id uuid, p_paddle_customer_id text, p_email text, p_name text, p_country_code text, p_metadata jsonb); Type: COMMENT; Schema: public; Owner: supabase_admin
--

COMMENT ON FUNCTION public.upsert_paddle_customer(p_user_id uuid, p_paddle_customer_id text, p_email text, p_name text, p_country_code text, p_metadata jsonb) IS 'Create or update a Paddle customer record';


--
-- Name: validate_api_key(text); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.validate_api_key(p_key text) RETURNS TABLE(user_id uuid, api_key_id uuid)
    LANGUAGE plpgsql
    AS $$DECLARE
    v_key_prefix TEXT;
    v_actual_key TEXT;
BEGIN
    -- Check if the key has the vhkey_ prefix and remove it if present
    IF starts_with(p_key, 'vhkey_') THEN
        v_actual_key := substring(p_key from 7); -- Remove 'vhkey_' prefix
    ELSE
        v_actual_key := p_key;
    END IF;
    
    -- Extract prefix (first 8 characters of the actual key)
    v_key_prefix := substring(v_actual_key from 1 for 8);
    
    -- Update last used time and return user_id if key is valid
    -- Use crypt() to verify the hash, which is how the key was created
    UPDATE public.api_keys
    SET last_used_at = NOW()
    WHERE key_prefix = v_key_prefix
    AND extensions.crypt(v_actual_key, key_hash) = key_hash
    AND is_active = TRUE
    AND (expires_at IS NULL OR expires_at > NOW());
    
    -- Return user_id if key is valid
    RETURN QUERY
    SELECT a.user_id, a.id
    FROM public.api_keys a
    WHERE a.key_prefix = v_key_prefix
    AND extensions.crypt(v_actual_key, a.key_hash) = a.key_hash
    AND a.is_active = TRUE
    AND (a.expires_at IS NULL OR a.expires_at > NOW());
END;$$;


ALTER FUNCTION public.validate_api_key(p_key text) OWNER TO postgres;

--
-- Name: FUNCTION validate_api_key(p_key text); Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON FUNCTION public.validate_api_key(p_key text) IS 'Validates an API key and returns the associated user_id and api_key_id if valid. Handles keys with or without the vhkey_ prefix.';


--
-- Name: apply_rls(jsonb, integer); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.apply_rls(wal jsonb, max_record_bytes integer DEFAULT (1024 * 1024)) RETURNS SETOF realtime.wal_rls
    LANGUAGE plpgsql
    AS $$
declare
-- Regclass of the table e.g. public.notes
entity_ regclass = (quote_ident(wal ->> 'schema') || '.' || quote_ident(wal ->> 'table'))::regclass;

-- I, U, D, T: insert, update ...
action realtime.action = (
    case wal ->> 'action'
        when 'I' then 'INSERT'
        when 'U' then 'UPDATE'
        when 'D' then 'DELETE'
        else 'ERROR'
    end
);

-- Is row level security enabled for the table
is_rls_enabled bool = relrowsecurity from pg_class where oid = entity_;

subscriptions realtime.subscription[] = array_agg(subs)
    from
        realtime.subscription subs
    where
        subs.entity = entity_;

-- Subscription vars
roles regrole[] = array_agg(distinct us.claims_role::text)
    from
        unnest(subscriptions) us;

working_role regrole;
claimed_role regrole;
claims jsonb;

subscription_id uuid;
subscription_has_access bool;
visible_to_subscription_ids uuid[] = '{}';

-- structured info for wal's columns
columns realtime.wal_column[];
-- previous identity values for update/delete
old_columns realtime.wal_column[];

error_record_exceeds_max_size boolean = octet_length(wal::text) > max_record_bytes;

-- Primary jsonb output for record
output jsonb;

begin
perform set_config('role', null, true);

columns =
    array_agg(
        (
            x->>'name',
            x->>'type',
            x->>'typeoid',
            realtime.cast(
                (x->'value') #>> '{}',
                coalesce(
                    (x->>'typeoid')::regtype, -- null when wal2json version <= 2.4
                    (x->>'type')::regtype
                )
            ),
            (pks ->> 'name') is not null,
            true
        )::realtime.wal_column
    )
    from
        jsonb_array_elements(wal -> 'columns') x
        left join jsonb_array_elements(wal -> 'pk') pks
            on (x ->> 'name') = (pks ->> 'name');

old_columns =
    array_agg(
        (
            x->>'name',
            x->>'type',
            x->>'typeoid',
            realtime.cast(
                (x->'value') #>> '{}',
                coalesce(
                    (x->>'typeoid')::regtype, -- null when wal2json version <= 2.4
                    (x->>'type')::regtype
                )
            ),
            (pks ->> 'name') is not null,
            true
        )::realtime.wal_column
    )
    from
        jsonb_array_elements(wal -> 'identity') x
        left join jsonb_array_elements(wal -> 'pk') pks
            on (x ->> 'name') = (pks ->> 'name');

for working_role in select * from unnest(roles) loop

    -- Update `is_selectable` for columns and old_columns
    columns =
        array_agg(
            (
                c.name,
                c.type_name,
                c.type_oid,
                c.value,
                c.is_pkey,
                pg_catalog.has_column_privilege(working_role, entity_, c.name, 'SELECT')
            )::realtime.wal_column
        )
        from
            unnest(columns) c;

    old_columns =
            array_agg(
                (
                    c.name,
                    c.type_name,
                    c.type_oid,
                    c.value,
                    c.is_pkey,
                    pg_catalog.has_column_privilege(working_role, entity_, c.name, 'SELECT')
                )::realtime.wal_column
            )
            from
                unnest(old_columns) c;

    if action <> 'DELETE' and count(1) = 0 from unnest(columns) c where c.is_pkey then
        return next (
            jsonb_build_object(
                'schema', wal ->> 'schema',
                'table', wal ->> 'table',
                'type', action
            ),
            is_rls_enabled,
            -- subscriptions is already filtered by entity
            (select array_agg(s.subscription_id) from unnest(subscriptions) as s where claims_role = working_role),
            array['Error 400: Bad Request, no primary key']
        )::realtime.wal_rls;

    -- The claims role does not have SELECT permission to the primary key of entity
    elsif action <> 'DELETE' and sum(c.is_selectable::int) <> count(1) from unnest(columns) c where c.is_pkey then
        return next (
            jsonb_build_object(
                'schema', wal ->> 'schema',
                'table', wal ->> 'table',
                'type', action
            ),
            is_rls_enabled,
            (select array_agg(s.subscription_id) from unnest(subscriptions) as s where claims_role = working_role),
            array['Error 401: Unauthorized']
        )::realtime.wal_rls;

    else
        output = jsonb_build_object(
            'schema', wal ->> 'schema',
            'table', wal ->> 'table',
            'type', action,
            'commit_timestamp', to_char(
                ((wal ->> 'timestamp')::timestamptz at time zone 'utc'),
                'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"'
            ),
            'columns', (
                select
                    jsonb_agg(
                        jsonb_build_object(
                            'name', pa.attname,
                            'type', pt.typname
                        )
                        order by pa.attnum asc
                    )
                from
                    pg_attribute pa
                    join pg_type pt
                        on pa.atttypid = pt.oid
                where
                    attrelid = entity_
                    and attnum > 0
                    and pg_catalog.has_column_privilege(working_role, entity_, pa.attname, 'SELECT')
            )
        )
        -- Add "record" key for insert and update
        || case
            when action in ('INSERT', 'UPDATE') then
                jsonb_build_object(
                    'record',
                    (
                        select
                            jsonb_object_agg(
                                -- if unchanged toast, get column name and value from old record
                                coalesce((c).name, (oc).name),
                                case
                                    when (c).name is null then (oc).value
                                    else (c).value
                                end
                            )
                        from
                            unnest(columns) c
                            full outer join unnest(old_columns) oc
                                on (c).name = (oc).name
                        where
                            coalesce((c).is_selectable, (oc).is_selectable)
                            and ( not error_record_exceeds_max_size or (octet_length((c).value::text) <= 64))
                    )
                )
            else '{}'::jsonb
        end
        -- Add "old_record" key for update and delete
        || case
            when action = 'UPDATE' then
                jsonb_build_object(
                        'old_record',
                        (
                            select jsonb_object_agg((c).name, (c).value)
                            from unnest(old_columns) c
                            where
                                (c).is_selectable
                                and ( not error_record_exceeds_max_size or (octet_length((c).value::text) <= 64))
                        )
                    )
            when action = 'DELETE' then
                jsonb_build_object(
                    'old_record',
                    (
                        select jsonb_object_agg((c).name, (c).value)
                        from unnest(old_columns) c
                        where
                            (c).is_selectable
                            and ( not error_record_exceeds_max_size or (octet_length((c).value::text) <= 64))
                            and ( not is_rls_enabled or (c).is_pkey ) -- if RLS enabled, we can't secure deletes so filter to pkey
                    )
                )
            else '{}'::jsonb
        end;

        -- Create the prepared statement
        if is_rls_enabled and action <> 'DELETE' then
            if (select 1 from pg_prepared_statements where name = 'walrus_rls_stmt' limit 1) > 0 then
                deallocate walrus_rls_stmt;
            end if;
            execute realtime.build_prepared_statement_sql('walrus_rls_stmt', entity_, columns);
        end if;

        visible_to_subscription_ids = '{}';

        for subscription_id, claims in (
                select
                    subs.subscription_id,
                    subs.claims
                from
                    unnest(subscriptions) subs
                where
                    subs.entity = entity_
                    and subs.claims_role = working_role
                    and (
                        realtime.is_visible_through_filters(columns, subs.filters)
                        or (
                          action = 'DELETE'
                          and realtime.is_visible_through_filters(old_columns, subs.filters)
                        )
                    )
        ) loop

            if not is_rls_enabled or action = 'DELETE' then
                visible_to_subscription_ids = visible_to_subscription_ids || subscription_id;
            else
                -- Check if RLS allows the role to see the record
                perform
                    -- Trim leading and trailing quotes from working_role because set_config
                    -- doesn't recognize the role as valid if they are included
                    set_config('role', trim(both '"' from working_role::text), true),
                    set_config('request.jwt.claims', claims::text, true);

                execute 'execute walrus_rls_stmt' into subscription_has_access;

                if subscription_has_access then
                    visible_to_subscription_ids = visible_to_subscription_ids || subscription_id;
                end if;
            end if;
        end loop;

        perform set_config('role', null, true);

        return next (
            output,
            is_rls_enabled,
            visible_to_subscription_ids,
            case
                when error_record_exceeds_max_size then array['Error 413: Payload Too Large']
                else '{}'
            end
        )::realtime.wal_rls;

    end if;
end loop;

perform set_config('role', null, true);
end;
$$;


ALTER FUNCTION realtime.apply_rls(wal jsonb, max_record_bytes integer) OWNER TO supabase_admin;

--
-- Name: broadcast_changes(text, text, text, text, text, record, record, text); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.broadcast_changes(topic_name text, event_name text, operation text, table_name text, table_schema text, new record, old record, level text DEFAULT 'ROW'::text) RETURNS void
    LANGUAGE plpgsql
    AS $$
DECLARE
    -- Declare a variable to hold the JSONB representation of the row
    row_data jsonb := '{}'::jsonb;
BEGIN
    IF level = 'STATEMENT' THEN
        RAISE EXCEPTION 'function can only be triggered for each row, not for each statement';
    END IF;
    -- Check the operation type and handle accordingly
    IF operation = 'INSERT' OR operation = 'UPDATE' OR operation = 'DELETE' THEN
        row_data := jsonb_build_object('old_record', OLD, 'record', NEW, 'operation', operation, 'table', table_name, 'schema', table_schema);
        PERFORM realtime.send (row_data, event_name, topic_name);
    ELSE
        RAISE EXCEPTION 'Unexpected operation type: %', operation;
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Failed to process the row: %', SQLERRM;
END;

$$;


ALTER FUNCTION realtime.broadcast_changes(topic_name text, event_name text, operation text, table_name text, table_schema text, new record, old record, level text) OWNER TO supabase_admin;

--
-- Name: build_prepared_statement_sql(text, regclass, realtime.wal_column[]); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]) RETURNS text
    LANGUAGE sql
    AS $$
      /*
      Builds a sql string that, if executed, creates a prepared statement to
      tests retrive a row from *entity* by its primary key columns.
      Example
          select realtime.build_prepared_statement_sql('public.notes', '{"id"}'::text[], '{"bigint"}'::text[])
      */
          select
      'prepare ' || prepared_statement_name || ' as
          select
              exists(
                  select
                      1
                  from
                      ' || entity || '
                  where
                      ' || string_agg(quote_ident(pkc.name) || '=' || quote_nullable(pkc.value #>> '{}') , ' and ') || '
              )'
          from
              unnest(columns) pkc
          where
              pkc.is_pkey
          group by
              entity
      $$;


ALTER FUNCTION realtime.build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]) OWNER TO supabase_admin;

--
-- Name: cast(text, regtype); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime."cast"(val text, type_ regtype) RETURNS jsonb
    LANGUAGE plpgsql IMMUTABLE
    AS $$
    declare
      res jsonb;
    begin
      execute format('select to_jsonb(%L::'|| type_::text || ')', val)  into res;
      return res;
    end
    $$;


ALTER FUNCTION realtime."cast"(val text, type_ regtype) OWNER TO supabase_admin;

--
-- Name: check_equality_op(realtime.equality_op, regtype, text, text); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text) RETURNS boolean
    LANGUAGE plpgsql IMMUTABLE
    AS $$
      /*
      Casts *val_1* and *val_2* as type *type_* and check the *op* condition for truthiness
      */
      declare
          op_symbol text = (
              case
                  when op = 'eq' then '='
                  when op = 'neq' then '!='
                  when op = 'lt' then '<'
                  when op = 'lte' then '<='
                  when op = 'gt' then '>'
                  when op = 'gte' then '>='
                  when op = 'in' then '= any'
                  else 'UNKNOWN OP'
              end
          );
          res boolean;
      begin
          execute format(
              'select %L::'|| type_::text || ' ' || op_symbol
              || ' ( %L::'
              || (
                  case
                      when op = 'in' then type_::text || '[]'
                      else type_::text end
              )
              || ')', val_1, val_2) into res;
          return res;
      end;
      $$;


ALTER FUNCTION realtime.check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text) OWNER TO supabase_admin;

--
-- Name: is_visible_through_filters(realtime.wal_column[], realtime.user_defined_filter[]); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]) RETURNS boolean
    LANGUAGE sql IMMUTABLE
    AS $_$
    /*
    Should the record be visible (true) or filtered out (false) after *filters* are applied
    */
        select
            -- Default to allowed when no filters present
            $2 is null -- no filters. this should not happen because subscriptions has a default
            or array_length($2, 1) is null -- array length of an empty array is null
            or bool_and(
                coalesce(
                    realtime.check_equality_op(
                        op:=f.op,
                        type_:=coalesce(
                            col.type_oid::regtype, -- null when wal2json version <= 2.4
                            col.type_name::regtype
                        ),
                        -- cast jsonb to text
                        val_1:=col.value #>> '{}',
                        val_2:=f.value
                    ),
                    false -- if null, filter does not match
                )
            )
        from
            unnest(filters) f
            join unnest(columns) col
                on f.column_name = col.name;
    $_$;


ALTER FUNCTION realtime.is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]) OWNER TO supabase_admin;

--
-- Name: list_changes(name, name, integer, integer); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer) RETURNS SETOF realtime.wal_rls
    LANGUAGE sql
    SET log_min_messages TO 'fatal'
    AS $$
      with pub as (
        select
          concat_ws(
            ',',
            case when bool_or(pubinsert) then 'insert' else null end,
            case when bool_or(pubupdate) then 'update' else null end,
            case when bool_or(pubdelete) then 'delete' else null end
          ) as w2j_actions,
          coalesce(
            string_agg(
              realtime.quote_wal2json(format('%I.%I', schemaname, tablename)::regclass),
              ','
            ) filter (where ppt.tablename is not null and ppt.tablename not like '% %'),
            ''
          ) w2j_add_tables
        from
          pg_publication pp
          left join pg_publication_tables ppt
            on pp.pubname = ppt.pubname
        where
          pp.pubname = publication
        group by
          pp.pubname
        limit 1
      ),
      w2j as (
        select
          x.*, pub.w2j_add_tables
        from
          pub,
          pg_logical_slot_get_changes(
            slot_name, null, max_changes,
            'include-pk', 'true',
            'include-transaction', 'false',
            'include-timestamp', 'true',
            'include-type-oids', 'true',
            'format-version', '2',
            'actions', pub.w2j_actions,
            'add-tables', pub.w2j_add_tables
          ) x
      )
      select
        xyz.wal,
        xyz.is_rls_enabled,
        xyz.subscription_ids,
        xyz.errors
      from
        w2j,
        realtime.apply_rls(
          wal := w2j.data::jsonb,
          max_record_bytes := max_record_bytes
        ) xyz(wal, is_rls_enabled, subscription_ids, errors)
      where
        w2j.w2j_add_tables <> ''
        and xyz.subscription_ids[1] is not null
    $$;


ALTER FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer) OWNER TO supabase_admin;

--
-- Name: quote_wal2json(regclass); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.quote_wal2json(entity regclass) RETURNS text
    LANGUAGE sql IMMUTABLE STRICT
    AS $$
      select
        (
          select string_agg('' || ch,'')
          from unnest(string_to_array(nsp.nspname::text, null)) with ordinality x(ch, idx)
          where
            not (x.idx = 1 and x.ch = '"')
            and not (
              x.idx = array_length(string_to_array(nsp.nspname::text, null), 1)
              and x.ch = '"'
            )
        )
        || '.'
        || (
          select string_agg('' || ch,'')
          from unnest(string_to_array(pc.relname::text, null)) with ordinality x(ch, idx)
          where
            not (x.idx = 1 and x.ch = '"')
            and not (
              x.idx = array_length(string_to_array(nsp.nspname::text, null), 1)
              and x.ch = '"'
            )
          )
      from
        pg_class pc
        join pg_namespace nsp
          on pc.relnamespace = nsp.oid
      where
        pc.oid = entity
    $$;


ALTER FUNCTION realtime.quote_wal2json(entity regclass) OWNER TO supabase_admin;

--
-- Name: send(jsonb, text, text, boolean); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.send(payload jsonb, event text, topic text, private boolean DEFAULT true) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
  BEGIN
    -- Set the topic configuration
    EXECUTE format('SET LOCAL realtime.topic TO %L', topic);

    -- Attempt to insert the message
    INSERT INTO realtime.messages (payload, event, topic, private, extension)
    VALUES (payload, event, topic, private, 'broadcast');
  EXCEPTION
    WHEN OTHERS THEN
      -- Capture and notify the error
      PERFORM pg_notify(
          'realtime:system',
          jsonb_build_object(
              'error', SQLERRM,
              'function', 'realtime.send',
              'event', event,
              'topic', topic,
              'private', private
          )::text
      );
  END;
END;
$$;


ALTER FUNCTION realtime.send(payload jsonb, event text, topic text, private boolean) OWNER TO supabase_admin;

--
-- Name: subscription_check_filters(); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.subscription_check_filters() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
    /*
    Validates that the user defined filters for a subscription:
    - refer to valid columns that the claimed role may access
    - values are coercable to the correct column type
    */
    declare
        col_names text[] = coalesce(
                array_agg(c.column_name order by c.ordinal_position),
                '{}'::text[]
            )
            from
                information_schema.columns c
            where
                format('%I.%I', c.table_schema, c.table_name)::regclass = new.entity
                and pg_catalog.has_column_privilege(
                    (new.claims ->> 'role'),
                    format('%I.%I', c.table_schema, c.table_name)::regclass,
                    c.column_name,
                    'SELECT'
                );
        filter realtime.user_defined_filter;
        col_type regtype;

        in_val jsonb;
    begin
        for filter in select * from unnest(new.filters) loop
            -- Filtered column is valid
            if not filter.column_name = any(col_names) then
                raise exception 'invalid column for filter %', filter.column_name;
            end if;

            -- Type is sanitized and safe for string interpolation
            col_type = (
                select atttypid::regtype
                from pg_catalog.pg_attribute
                where attrelid = new.entity
                      and attname = filter.column_name
            );
            if col_type is null then
                raise exception 'failed to lookup type for column %', filter.column_name;
            end if;

            -- Set maximum number of entries for in filter
            if filter.op = 'in'::realtime.equality_op then
                in_val = realtime.cast(filter.value, (col_type::text || '[]')::regtype);
                if coalesce(jsonb_array_length(in_val), 0) > 100 then
                    raise exception 'too many values for `in` filter. Maximum 100';
                end if;
            else
                -- raises an exception if value is not coercable to type
                perform realtime.cast(filter.value, col_type);
            end if;

        end loop;

        -- Apply consistent order to filters so the unique constraint on
        -- (subscription_id, entity, filters) can't be tricked by a different filter order
        new.filters = coalesce(
            array_agg(f order by f.column_name, f.op, f.value),
            '{}'
        ) from unnest(new.filters) f;

        return new;
    end;
    $$;


ALTER FUNCTION realtime.subscription_check_filters() OWNER TO supabase_admin;

--
-- Name: to_regrole(text); Type: FUNCTION; Schema: realtime; Owner: supabase_admin
--

CREATE FUNCTION realtime.to_regrole(role_name text) RETURNS regrole
    LANGUAGE sql IMMUTABLE
    AS $$ select role_name::regrole $$;


ALTER FUNCTION realtime.to_regrole(role_name text) OWNER TO supabase_admin;

--
-- Name: topic(); Type: FUNCTION; Schema: realtime; Owner: supabase_realtime_admin
--

CREATE FUNCTION realtime.topic() RETURNS text
    LANGUAGE sql STABLE
    AS $$
select nullif(current_setting('realtime.topic', true), '')::text;
$$;


ALTER FUNCTION realtime.topic() OWNER TO supabase_realtime_admin;

--
-- Name: add_prefixes(text, text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.add_prefixes(_bucket_id text, _name text) RETURNS void
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
    prefixes text[];
BEGIN
    prefixes := "storage"."get_prefixes"("_name");

    IF array_length(prefixes, 1) > 0 THEN
        INSERT INTO storage.prefixes (name, bucket_id)
        SELECT UNNEST(prefixes) as name, "_bucket_id" ON CONFLICT DO NOTHING;
    END IF;
END;
$$;


ALTER FUNCTION storage.add_prefixes(_bucket_id text, _name text) OWNER TO supabase_storage_admin;

--
-- Name: can_insert_object(text, text, uuid, jsonb); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.can_insert_object(bucketid text, name text, owner uuid, metadata jsonb) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
  INSERT INTO "storage"."objects" ("bucket_id", "name", "owner", "metadata") VALUES (bucketid, name, owner, metadata);
  -- hack to rollback the successful insert
  RAISE sqlstate 'PT200' using
  message = 'ROLLBACK',
  detail = 'rollback successful insert';
END
$$;


ALTER FUNCTION storage.can_insert_object(bucketid text, name text, owner uuid, metadata jsonb) OWNER TO supabase_storage_admin;

--
-- Name: delete_prefix(text, text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.delete_prefix(_bucket_id text, _name text) RETURNS boolean
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
    -- Check if we can delete the prefix
    IF EXISTS(
        SELECT FROM "storage"."prefixes"
        WHERE "prefixes"."bucket_id" = "_bucket_id"
          AND level = "storage"."get_level"("_name") + 1
          AND "prefixes"."name" COLLATE "C" LIKE "_name" || '/%'
        LIMIT 1
    )
    OR EXISTS(
        SELECT FROM "storage"."objects"
        WHERE "objects"."bucket_id" = "_bucket_id"
          AND "storage"."get_level"("objects"."name") = "storage"."get_level"("_name") + 1
          AND "objects"."name" COLLATE "C" LIKE "_name" || '/%'
        LIMIT 1
    ) THEN
    -- There are sub-objects, skip deletion
    RETURN false;
    ELSE
        DELETE FROM "storage"."prefixes"
        WHERE "prefixes"."bucket_id" = "_bucket_id"
          AND level = "storage"."get_level"("_name")
          AND "prefixes"."name" = "_name";
        RETURN true;
    END IF;
END;
$$;


ALTER FUNCTION storage.delete_prefix(_bucket_id text, _name text) OWNER TO supabase_storage_admin;

--
-- Name: delete_prefix_hierarchy_trigger(); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.delete_prefix_hierarchy_trigger() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
    prefix text;
BEGIN
    prefix := "storage"."get_prefix"(OLD."name");

    IF coalesce(prefix, '') != '' THEN
        PERFORM "storage"."delete_prefix"(OLD."bucket_id", prefix);
    END IF;

    RETURN OLD;
END;
$$;


ALTER FUNCTION storage.delete_prefix_hierarchy_trigger() OWNER TO supabase_storage_admin;

--
-- Name: extension(text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.extension(name text) RETURNS text
    LANGUAGE plpgsql IMMUTABLE
    AS $$
DECLARE
    _parts text[];
    _filename text;
BEGIN
    SELECT string_to_array(name, '/') INTO _parts;
    SELECT _parts[array_length(_parts,1)] INTO _filename;
    RETURN reverse(split_part(reverse(_filename), '.', 1));
END
$$;


ALTER FUNCTION storage.extension(name text) OWNER TO supabase_storage_admin;

--
-- Name: filename(text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.filename(name text) RETURNS text
    LANGUAGE plpgsql
    AS $$
DECLARE
_parts text[];
BEGIN
	select string_to_array(name, '/') into _parts;
	return _parts[array_length(_parts,1)];
END
$$;


ALTER FUNCTION storage.filename(name text) OWNER TO supabase_storage_admin;

--
-- Name: foldername(text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.foldername(name text) RETURNS text[]
    LANGUAGE plpgsql IMMUTABLE
    AS $$
DECLARE
    _parts text[];
BEGIN
    -- Split on "/" to get path segments
    SELECT string_to_array(name, '/') INTO _parts;
    -- Return everything except the last segment
    RETURN _parts[1 : array_length(_parts,1) - 1];
END
$$;


ALTER FUNCTION storage.foldername(name text) OWNER TO supabase_storage_admin;

--
-- Name: get_level(text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.get_level(name text) RETURNS integer
    LANGUAGE sql IMMUTABLE STRICT
    AS $$
SELECT array_length(string_to_array("name", '/'), 1);
$$;


ALTER FUNCTION storage.get_level(name text) OWNER TO supabase_storage_admin;

--
-- Name: get_prefix(text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.get_prefix(name text) RETURNS text
    LANGUAGE sql IMMUTABLE STRICT
    AS $_$
SELECT
    CASE WHEN strpos("name", '/') > 0 THEN
             regexp_replace("name", '[\/]{1}[^\/]+\/?$', '')
         ELSE
             ''
        END;
$_$;


ALTER FUNCTION storage.get_prefix(name text) OWNER TO supabase_storage_admin;

--
-- Name: get_prefixes(text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.get_prefixes(name text) RETURNS text[]
    LANGUAGE plpgsql IMMUTABLE STRICT
    AS $$
DECLARE
    parts text[];
    prefixes text[];
    prefix text;
BEGIN
    -- Split the name into parts by '/'
    parts := string_to_array("name", '/');
    prefixes := '{}';

    -- Construct the prefixes, stopping one level below the last part
    FOR i IN 1..array_length(parts, 1) - 1 LOOP
            prefix := array_to_string(parts[1:i], '/');
            prefixes := array_append(prefixes, prefix);
    END LOOP;

    RETURN prefixes;
END;
$$;


ALTER FUNCTION storage.get_prefixes(name text) OWNER TO supabase_storage_admin;

--
-- Name: get_size_by_bucket(); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.get_size_by_bucket() RETURNS TABLE(size bigint, bucket_id text)
    LANGUAGE plpgsql STABLE
    AS $$
BEGIN
    return query
        select sum((metadata->>'size')::bigint) as size, obj.bucket_id
        from "storage".objects as obj
        group by obj.bucket_id;
END
$$;


ALTER FUNCTION storage.get_size_by_bucket() OWNER TO supabase_storage_admin;

--
-- Name: list_multipart_uploads_with_delimiter(text, text, text, integer, text, text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.list_multipart_uploads_with_delimiter(bucket_id text, prefix_param text, delimiter_param text, max_keys integer DEFAULT 100, next_key_token text DEFAULT ''::text, next_upload_token text DEFAULT ''::text) RETURNS TABLE(key text, id text, created_at timestamp with time zone)
    LANGUAGE plpgsql
    AS $_$
BEGIN
    RETURN QUERY EXECUTE
        'SELECT DISTINCT ON(key COLLATE "C") * from (
            SELECT
                CASE
                    WHEN position($2 IN substring(key from length($1) + 1)) > 0 THEN
                        substring(key from 1 for length($1) + position($2 IN substring(key from length($1) + 1)))
                    ELSE
                        key
                END AS key, id, created_at
            FROM
                storage.s3_multipart_uploads
            WHERE
                bucket_id = $5 AND
                key ILIKE $1 || ''%'' AND
                CASE
                    WHEN $4 != '''' AND $6 = '''' THEN
                        CASE
                            WHEN position($2 IN substring(key from length($1) + 1)) > 0 THEN
                                substring(key from 1 for length($1) + position($2 IN substring(key from length($1) + 1))) COLLATE "C" > $4
                            ELSE
                                key COLLATE "C" > $4
                            END
                    ELSE
                        true
                END AND
                CASE
                    WHEN $6 != '''' THEN
                        id COLLATE "C" > $6
                    ELSE
                        true
                    END
            ORDER BY
                key COLLATE "C" ASC, created_at ASC) as e order by key COLLATE "C" LIMIT $3'
        USING prefix_param, delimiter_param, max_keys, next_key_token, bucket_id, next_upload_token;
END;
$_$;


ALTER FUNCTION storage.list_multipart_uploads_with_delimiter(bucket_id text, prefix_param text, delimiter_param text, max_keys integer, next_key_token text, next_upload_token text) OWNER TO supabase_storage_admin;

--
-- Name: list_objects_with_delimiter(text, text, text, integer, text, text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.list_objects_with_delimiter(bucket_id text, prefix_param text, delimiter_param text, max_keys integer DEFAULT 100, start_after text DEFAULT ''::text, next_token text DEFAULT ''::text) RETURNS TABLE(name text, id uuid, metadata jsonb, updated_at timestamp with time zone)
    LANGUAGE plpgsql
    AS $_$
BEGIN
    RETURN QUERY EXECUTE
        'SELECT DISTINCT ON(name COLLATE "C") * from (
            SELECT
                CASE
                    WHEN position($2 IN substring(name from length($1) + 1)) > 0 THEN
                        substring(name from 1 for length($1) + position($2 IN substring(name from length($1) + 1)))
                    ELSE
                        name
                END AS name, id, metadata, updated_at
            FROM
                storage.objects
            WHERE
                bucket_id = $5 AND
                name ILIKE $1 || ''%'' AND
                CASE
                    WHEN $6 != '''' THEN
                    name COLLATE "C" > $6
                ELSE true END
                AND CASE
                    WHEN $4 != '''' THEN
                        CASE
                            WHEN position($2 IN substring(name from length($1) + 1)) > 0 THEN
                                substring(name from 1 for length($1) + position($2 IN substring(name from length($1) + 1))) COLLATE "C" > $4
                            ELSE
                                name COLLATE "C" > $4
                            END
                    ELSE
                        true
                END
            ORDER BY
                name COLLATE "C" ASC) as e order by name COLLATE "C" LIMIT $3'
        USING prefix_param, delimiter_param, max_keys, next_token, bucket_id, start_after;
END;
$_$;


ALTER FUNCTION storage.list_objects_with_delimiter(bucket_id text, prefix_param text, delimiter_param text, max_keys integer, start_after text, next_token text) OWNER TO supabase_storage_admin;

--
-- Name: objects_insert_prefix_trigger(); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.objects_insert_prefix_trigger() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    PERFORM "storage"."add_prefixes"(NEW."bucket_id", NEW."name");
    NEW.level := "storage"."get_level"(NEW."name");

    RETURN NEW;
END;
$$;


ALTER FUNCTION storage.objects_insert_prefix_trigger() OWNER TO supabase_storage_admin;

--
-- Name: objects_update_prefix_trigger(); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.objects_update_prefix_trigger() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
    old_prefixes TEXT[];
BEGIN
    -- Ensure this is an update operation and the name has changed
    IF TG_OP = 'UPDATE' AND (NEW."name" <> OLD."name" OR NEW."bucket_id" <> OLD."bucket_id") THEN
        -- Retrieve old prefixes
        old_prefixes := "storage"."get_prefixes"(OLD."name");

        -- Remove old prefixes that are only used by this object
        WITH all_prefixes as (
            SELECT unnest(old_prefixes) as prefix
        ),
        can_delete_prefixes as (
             SELECT prefix
             FROM all_prefixes
             WHERE NOT EXISTS (
                 SELECT 1 FROM "storage"."objects"
                 WHERE "bucket_id" = OLD."bucket_id"
                   AND "name" <> OLD."name"
                   AND "name" LIKE (prefix || '%')
             )
         )
        DELETE FROM "storage"."prefixes" WHERE name IN (SELECT prefix FROM can_delete_prefixes);

        -- Add new prefixes
        PERFORM "storage"."add_prefixes"(NEW."bucket_id", NEW."name");
    END IF;
    -- Set the new level
    NEW."level" := "storage"."get_level"(NEW."name");

    RETURN NEW;
END;
$$;


ALTER FUNCTION storage.objects_update_prefix_trigger() OWNER TO supabase_storage_admin;

--
-- Name: operation(); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.operation() RETURNS text
    LANGUAGE plpgsql STABLE
    AS $$
BEGIN
    RETURN current_setting('storage.operation', true);
END;
$$;


ALTER FUNCTION storage.operation() OWNER TO supabase_storage_admin;

--
-- Name: prefixes_insert_trigger(); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.prefixes_insert_trigger() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    PERFORM "storage"."add_prefixes"(NEW."bucket_id", NEW."name");
    RETURN NEW;
END;
$$;


ALTER FUNCTION storage.prefixes_insert_trigger() OWNER TO supabase_storage_admin;

--
-- Name: search(text, text, integer, integer, integer, text, text, text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.search(prefix text, bucketname text, limits integer DEFAULT 100, levels integer DEFAULT 1, offsets integer DEFAULT 0, search text DEFAULT ''::text, sortcolumn text DEFAULT 'name'::text, sortorder text DEFAULT 'asc'::text) RETURNS TABLE(name text, id uuid, updated_at timestamp with time zone, created_at timestamp with time zone, last_accessed_at timestamp with time zone, metadata jsonb)
    LANGUAGE plpgsql
    AS $$
declare
    can_bypass_rls BOOLEAN;
begin
    SELECT rolbypassrls
    INTO can_bypass_rls
    FROM pg_roles
    WHERE rolname = coalesce(nullif(current_setting('role', true), 'none'), current_user);

    IF can_bypass_rls THEN
        RETURN QUERY SELECT * FROM storage.search_v1_optimised(prefix, bucketname, limits, levels, offsets, search, sortcolumn, sortorder);
    ELSE
        RETURN QUERY SELECT * FROM storage.search_legacy_v1(prefix, bucketname, limits, levels, offsets, search, sortcolumn, sortorder);
    END IF;
end;
$$;


ALTER FUNCTION storage.search(prefix text, bucketname text, limits integer, levels integer, offsets integer, search text, sortcolumn text, sortorder text) OWNER TO supabase_storage_admin;

--
-- Name: search_legacy_v1(text, text, integer, integer, integer, text, text, text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.search_legacy_v1(prefix text, bucketname text, limits integer DEFAULT 100, levels integer DEFAULT 1, offsets integer DEFAULT 0, search text DEFAULT ''::text, sortcolumn text DEFAULT 'name'::text, sortorder text DEFAULT 'asc'::text) RETURNS TABLE(name text, id uuid, updated_at timestamp with time zone, created_at timestamp with time zone, last_accessed_at timestamp with time zone, metadata jsonb)
    LANGUAGE plpgsql STABLE
    AS $_$
declare
    v_order_by text;
    v_sort_order text;
begin
    case
        when sortcolumn = 'name' then
            v_order_by = 'name';
        when sortcolumn = 'updated_at' then
            v_order_by = 'updated_at';
        when sortcolumn = 'created_at' then
            v_order_by = 'created_at';
        when sortcolumn = 'last_accessed_at' then
            v_order_by = 'last_accessed_at';
        else
            v_order_by = 'name';
        end case;

    case
        when sortorder = 'asc' then
            v_sort_order = 'asc';
        when sortorder = 'desc' then
            v_sort_order = 'desc';
        else
            v_sort_order = 'asc';
        end case;

    v_order_by = v_order_by || ' ' || v_sort_order;

    return query execute
        'with folders as (
           select path_tokens[$1] as folder
           from storage.objects
             where objects.name ilike $2 || $3 || ''%''
               and bucket_id = $4
               and array_length(objects.path_tokens, 1) <> $1
           group by folder
           order by folder ' || v_sort_order || '
     )
     (select folder as "name",
            null as id,
            null as updated_at,
            null as created_at,
            null as last_accessed_at,
            null as metadata from folders)
     union all
     (select path_tokens[$1] as "name",
            id,
            updated_at,
            created_at,
            last_accessed_at,
            metadata
     from storage.objects
     where objects.name ilike $2 || $3 || ''%''
       and bucket_id = $4
       and array_length(objects.path_tokens, 1) = $1
     order by ' || v_order_by || ')
     limit $5
     offset $6' using levels, prefix, search, bucketname, limits, offsets;
end;
$_$;


ALTER FUNCTION storage.search_legacy_v1(prefix text, bucketname text, limits integer, levels integer, offsets integer, search text, sortcolumn text, sortorder text) OWNER TO supabase_storage_admin;

--
-- Name: search_v1_optimised(text, text, integer, integer, integer, text, text, text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.search_v1_optimised(prefix text, bucketname text, limits integer DEFAULT 100, levels integer DEFAULT 1, offsets integer DEFAULT 0, search text DEFAULT ''::text, sortcolumn text DEFAULT 'name'::text, sortorder text DEFAULT 'asc'::text) RETURNS TABLE(name text, id uuid, updated_at timestamp with time zone, created_at timestamp with time zone, last_accessed_at timestamp with time zone, metadata jsonb)
    LANGUAGE plpgsql STABLE
    AS $_$
declare
    v_order_by text;
    v_sort_order text;
begin
    case
        when sortcolumn = 'name' then
            v_order_by = 'name';
        when sortcolumn = 'updated_at' then
            v_order_by = 'updated_at';
        when sortcolumn = 'created_at' then
            v_order_by = 'created_at';
        when sortcolumn = 'last_accessed_at' then
            v_order_by = 'last_accessed_at';
        else
            v_order_by = 'name';
        end case;

    case
        when sortorder = 'asc' then
            v_sort_order = 'asc';
        when sortorder = 'desc' then
            v_sort_order = 'desc';
        else
            v_sort_order = 'asc';
        end case;

    v_order_by = v_order_by || ' ' || v_sort_order;

    return query execute
        'with folders as (
           select (string_to_array(name, ''/''))[level] as name
           from storage.prefixes
             where lower(prefixes.name) like lower($2 || $3) || ''%''
               and bucket_id = $4
               and level = $1
           order by name ' || v_sort_order || '
     )
     (select name,
            null as id,
            null as updated_at,
            null as created_at,
            null as last_accessed_at,
            null as metadata from folders)
     union all
     (select path_tokens[level] as "name",
            id,
            updated_at,
            created_at,
            last_accessed_at,
            metadata
     from storage.objects
     where lower(objects.name) like lower($2 || $3) || ''%''
       and bucket_id = $4
       and level = $1
     order by ' || v_order_by || ')
     limit $5
     offset $6' using levels, prefix, search, bucketname, limits, offsets;
end;
$_$;


ALTER FUNCTION storage.search_v1_optimised(prefix text, bucketname text, limits integer, levels integer, offsets integer, search text, sortcolumn text, sortorder text) OWNER TO supabase_storage_admin;

--
-- Name: search_v2(text, text, integer, integer, text); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.search_v2(prefix text, bucket_name text, limits integer DEFAULT 100, levels integer DEFAULT 1, start_after text DEFAULT ''::text) RETURNS TABLE(key text, name text, id uuid, updated_at timestamp with time zone, created_at timestamp with time zone, metadata jsonb)
    LANGUAGE plpgsql STABLE
    AS $_$
BEGIN
    RETURN query EXECUTE
        $sql$
        SELECT * FROM (
            (
                SELECT
                    split_part(name, '/', $4) AS key,
                    name || '/' AS name,
                    NULL::uuid AS id,
                    NULL::timestamptz AS updated_at,
                    NULL::timestamptz AS created_at,
                    NULL::jsonb AS metadata
                FROM storage.prefixes
                WHERE name COLLATE "C" LIKE $1 || '%'
                AND bucket_id = $2
                AND level = $4
                AND name COLLATE "C" > $5
                ORDER BY prefixes.name COLLATE "C" LIMIT $3
            )
            UNION ALL
            (SELECT split_part(name, '/', $4) AS key,
                name,
                id,
                updated_at,
                created_at,
                metadata
            FROM storage.objects
            WHERE name COLLATE "C" LIKE $1 || '%'
                AND bucket_id = $2
                AND level = $4
                AND name COLLATE "C" > $5
            ORDER BY name COLLATE "C" LIMIT $3)
        ) obj
        ORDER BY name COLLATE "C" LIMIT $3;
        $sql$
        USING prefix, bucket_name, limits, levels, start_after;
END;
$_$;


ALTER FUNCTION storage.search_v2(prefix text, bucket_name text, limits integer, levels integer, start_after text) OWNER TO supabase_storage_admin;

--
-- Name: update_updated_at_column(); Type: FUNCTION; Schema: storage; Owner: supabase_storage_admin
--

CREATE FUNCTION storage.update_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW; 
END;
$$;


ALTER FUNCTION storage.update_updated_at_column() OWNER TO supabase_storage_admin;

--
-- Name: http_request(); Type: FUNCTION; Schema: supabase_functions; Owner: supabase_functions_admin
--

CREATE FUNCTION supabase_functions.http_request() RETURNS trigger
    LANGUAGE plpgsql SECURITY DEFINER
    SET search_path TO 'supabase_functions'
    AS $$
    DECLARE
      request_id bigint;
      payload jsonb;
      url text := TG_ARGV[0]::text;
      method text := TG_ARGV[1]::text;
      headers jsonb DEFAULT '{}'::jsonb;
      params jsonb DEFAULT '{}'::jsonb;
      timeout_ms integer DEFAULT 1000;
    BEGIN
      IF url IS NULL OR url = 'null' THEN
        RAISE EXCEPTION 'url argument is missing';
      END IF;

      IF method IS NULL OR method = 'null' THEN
        RAISE EXCEPTION 'method argument is missing';
      END IF;

      IF TG_ARGV[2] IS NULL OR TG_ARGV[2] = 'null' THEN
        headers = '{"Content-Type": "application/json"}'::jsonb;
      ELSE
        headers = TG_ARGV[2]::jsonb;
      END IF;

      IF TG_ARGV[3] IS NULL OR TG_ARGV[3] = 'null' THEN
        params = '{}'::jsonb;
      ELSE
        params = TG_ARGV[3]::jsonb;
      END IF;

      IF TG_ARGV[4] IS NULL OR TG_ARGV[4] = 'null' THEN
        timeout_ms = 1000;
      ELSE
        timeout_ms = TG_ARGV[4]::integer;
      END IF;

      CASE
        WHEN method = 'GET' THEN
          SELECT http_get INTO request_id FROM net.http_get(
            url,
            params,
            headers,
            timeout_ms
          );
        WHEN method = 'POST' THEN
          payload = jsonb_build_object(
            'old_record', OLD,
            'record', NEW,
            'type', TG_OP,
            'table', TG_TABLE_NAME,
            'schema', TG_TABLE_SCHEMA
          );

          SELECT http_post INTO request_id FROM net.http_post(
            url,
            payload,
            params,
            headers,
            timeout_ms
          );
        ELSE
          RAISE EXCEPTION 'method argument % is invalid', method;
      END CASE;

      INSERT INTO supabase_functions.hooks
        (hook_table_id, hook_name, request_id)
      VALUES
        (TG_RELID, TG_NAME, request_id);

      RETURN NEW;
    END
  $$;


ALTER FUNCTION supabase_functions.http_request() OWNER TO supabase_functions_admin;

--
-- Name: wasm_wrapper; Type: FOREIGN DATA WRAPPER; Schema: -; Owner: supabase_admin
--

CREATE FOREIGN DATA WRAPPER wasm_wrapper HANDLER extensions.wasm_fdw_handler VALIDATOR extensions.wasm_fdw_validator;


ALTER FOREIGN DATA WRAPPER wasm_wrapper OWNER TO supabase_admin;

--
-- Name: paddle_server; Type: SERVER; Schema: -; Owner: supabase_admin
--

CREATE SERVER paddle_server FOREIGN DATA WRAPPER wasm_wrapper OPTIONS (
    api_key_id '86b0bf80-a19b-4135-ba19-5d0dc49f9683',
    api_url 'https://sandbox-api.paddle.com',
    fdw_package_checksum 'c5ac70bb2eef33693787b7d4efce9a83cde8d4fa40889d2037403a51263ba657',
    fdw_package_name 'supabase:paddle-fdw',
    fdw_package_url 'https://github.com/supabase/wrappers/releases/download/wasm_paddle_fdw_v0.1.1/paddle_fdw.wasm',
    fdw_package_version '0.1.1'
);


ALTER SERVER paddle_server OWNER TO supabase_admin;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: extensions; Type: TABLE; Schema: _realtime; Owner: supabase_admin
--

CREATE TABLE _realtime.extensions (
    id uuid NOT NULL,
    type text,
    settings jsonb,
    tenant_external_id text,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


ALTER TABLE _realtime.extensions OWNER TO supabase_admin;

--
-- Name: schema_migrations; Type: TABLE; Schema: _realtime; Owner: supabase_admin
--

CREATE TABLE _realtime.schema_migrations (
    version bigint NOT NULL,
    inserted_at timestamp(0) without time zone
);


ALTER TABLE _realtime.schema_migrations OWNER TO supabase_admin;

--
-- Name: tenants; Type: TABLE; Schema: _realtime; Owner: supabase_admin
--

CREATE TABLE _realtime.tenants (
    id uuid NOT NULL,
    name text,
    external_id text,
    jwt_secret text,
    max_concurrent_users integer DEFAULT 200 NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    max_events_per_second integer DEFAULT 100 NOT NULL,
    postgres_cdc_default text DEFAULT 'postgres_cdc_rls'::text,
    max_bytes_per_second integer DEFAULT 100000 NOT NULL,
    max_channels_per_client integer DEFAULT 100 NOT NULL,
    max_joins_per_second integer DEFAULT 500 NOT NULL,
    suspend boolean DEFAULT false,
    jwt_jwks jsonb,
    notify_private_alpha boolean DEFAULT false,
    private_only boolean DEFAULT false NOT NULL
);


ALTER TABLE _realtime.tenants OWNER TO supabase_admin;

--
-- Name: audit_log_entries; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.audit_log_entries (
    instance_id uuid,
    id uuid NOT NULL,
    payload json,
    created_at timestamp with time zone,
    ip_address character varying(64) DEFAULT ''::character varying NOT NULL
);


ALTER TABLE auth.audit_log_entries OWNER TO supabase_auth_admin;

--
-- Name: TABLE audit_log_entries; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.audit_log_entries IS 'Auth: Audit trail for user actions.';


--
-- Name: flow_state; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.flow_state (
    id uuid NOT NULL,
    user_id uuid,
    auth_code text NOT NULL,
    code_challenge_method auth.code_challenge_method NOT NULL,
    code_challenge text NOT NULL,
    provider_type text NOT NULL,
    provider_access_token text,
    provider_refresh_token text,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    authentication_method text NOT NULL,
    auth_code_issued_at timestamp with time zone
);


ALTER TABLE auth.flow_state OWNER TO supabase_auth_admin;

--
-- Name: TABLE flow_state; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.flow_state IS 'stores metadata for pkce logins';


--
-- Name: identities; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.identities (
    provider_id text NOT NULL,
    user_id uuid NOT NULL,
    identity_data jsonb NOT NULL,
    provider text NOT NULL,
    last_sign_in_at timestamp with time zone,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    email text GENERATED ALWAYS AS (lower((identity_data ->> 'email'::text))) STORED,
    id uuid DEFAULT gen_random_uuid() NOT NULL
);


ALTER TABLE auth.identities OWNER TO supabase_auth_admin;

--
-- Name: TABLE identities; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.identities IS 'Auth: Stores identities associated to a user.';


--
-- Name: COLUMN identities.email; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON COLUMN auth.identities.email IS 'Auth: Email is a generated column that references the optional email property in the identity_data';


--
-- Name: instances; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.instances (
    id uuid NOT NULL,
    uuid uuid,
    raw_base_config text,
    created_at timestamp with time zone,
    updated_at timestamp with time zone
);


ALTER TABLE auth.instances OWNER TO supabase_auth_admin;

--
-- Name: TABLE instances; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.instances IS 'Auth: Manages users across multiple sites.';


--
-- Name: mfa_amr_claims; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.mfa_amr_claims (
    session_id uuid NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    authentication_method text NOT NULL,
    id uuid NOT NULL
);


ALTER TABLE auth.mfa_amr_claims OWNER TO supabase_auth_admin;

--
-- Name: TABLE mfa_amr_claims; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.mfa_amr_claims IS 'auth: stores authenticator method reference claims for multi factor authentication';


--
-- Name: mfa_challenges; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.mfa_challenges (
    id uuid NOT NULL,
    factor_id uuid NOT NULL,
    created_at timestamp with time zone NOT NULL,
    verified_at timestamp with time zone,
    ip_address inet NOT NULL,
    otp_code text,
    web_authn_session_data jsonb
);


ALTER TABLE auth.mfa_challenges OWNER TO supabase_auth_admin;

--
-- Name: TABLE mfa_challenges; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.mfa_challenges IS 'auth: stores metadata about challenge requests made';


--
-- Name: mfa_factors; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.mfa_factors (
    id uuid NOT NULL,
    user_id uuid NOT NULL,
    friendly_name text,
    factor_type auth.factor_type NOT NULL,
    status auth.factor_status NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    secret text,
    phone text,
    last_challenged_at timestamp with time zone,
    web_authn_credential jsonb,
    web_authn_aaguid uuid
);


ALTER TABLE auth.mfa_factors OWNER TO supabase_auth_admin;

--
-- Name: TABLE mfa_factors; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.mfa_factors IS 'auth: stores metadata about factors';


--
-- Name: one_time_tokens; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.one_time_tokens (
    id uuid NOT NULL,
    user_id uuid NOT NULL,
    token_type auth.one_time_token_type NOT NULL,
    token_hash text NOT NULL,
    relates_to text NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    CONSTRAINT one_time_tokens_token_hash_check CHECK ((char_length(token_hash) > 0))
);


ALTER TABLE auth.one_time_tokens OWNER TO supabase_auth_admin;

--
-- Name: refresh_tokens; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.refresh_tokens (
    instance_id uuid,
    id bigint NOT NULL,
    token character varying(255),
    user_id character varying(255),
    revoked boolean,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    parent character varying(255),
    session_id uuid
);


ALTER TABLE auth.refresh_tokens OWNER TO supabase_auth_admin;

--
-- Name: TABLE refresh_tokens; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.refresh_tokens IS 'Auth: Store of tokens used to refresh JWT tokens once they expire.';


--
-- Name: refresh_tokens_id_seq; Type: SEQUENCE; Schema: auth; Owner: supabase_auth_admin
--

CREATE SEQUENCE auth.refresh_tokens_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE auth.refresh_tokens_id_seq OWNER TO supabase_auth_admin;

--
-- Name: refresh_tokens_id_seq; Type: SEQUENCE OWNED BY; Schema: auth; Owner: supabase_auth_admin
--

ALTER SEQUENCE auth.refresh_tokens_id_seq OWNED BY auth.refresh_tokens.id;


--
-- Name: saml_providers; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.saml_providers (
    id uuid NOT NULL,
    sso_provider_id uuid NOT NULL,
    entity_id text NOT NULL,
    metadata_xml text NOT NULL,
    metadata_url text,
    attribute_mapping jsonb,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    name_id_format text,
    CONSTRAINT "entity_id not empty" CHECK ((char_length(entity_id) > 0)),
    CONSTRAINT "metadata_url not empty" CHECK (((metadata_url = NULL::text) OR (char_length(metadata_url) > 0))),
    CONSTRAINT "metadata_xml not empty" CHECK ((char_length(metadata_xml) > 0))
);


ALTER TABLE auth.saml_providers OWNER TO supabase_auth_admin;

--
-- Name: TABLE saml_providers; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.saml_providers IS 'Auth: Manages SAML Identity Provider connections.';


--
-- Name: saml_relay_states; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.saml_relay_states (
    id uuid NOT NULL,
    sso_provider_id uuid NOT NULL,
    request_id text NOT NULL,
    for_email text,
    redirect_to text,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    flow_state_id uuid,
    CONSTRAINT "request_id not empty" CHECK ((char_length(request_id) > 0))
);


ALTER TABLE auth.saml_relay_states OWNER TO supabase_auth_admin;

--
-- Name: TABLE saml_relay_states; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.saml_relay_states IS 'Auth: Contains SAML Relay State information for each Service Provider initiated login.';


--
-- Name: schema_migrations; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.schema_migrations (
    version character varying(255) NOT NULL
);


ALTER TABLE auth.schema_migrations OWNER TO supabase_auth_admin;

--
-- Name: TABLE schema_migrations; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.schema_migrations IS 'Auth: Manages updates to the auth system.';


--
-- Name: sessions; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.sessions (
    id uuid NOT NULL,
    user_id uuid NOT NULL,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    factor_id uuid,
    aal auth.aal_level,
    not_after timestamp with time zone,
    refreshed_at timestamp without time zone,
    user_agent text,
    ip inet,
    tag text
);


ALTER TABLE auth.sessions OWNER TO supabase_auth_admin;

--
-- Name: TABLE sessions; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.sessions IS 'Auth: Stores session data associated to a user.';


--
-- Name: COLUMN sessions.not_after; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON COLUMN auth.sessions.not_after IS 'Auth: Not after is a nullable column that contains a timestamp after which the session should be regarded as expired.';


--
-- Name: sso_domains; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.sso_domains (
    id uuid NOT NULL,
    sso_provider_id uuid NOT NULL,
    domain text NOT NULL,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    CONSTRAINT "domain not empty" CHECK ((char_length(domain) > 0))
);


ALTER TABLE auth.sso_domains OWNER TO supabase_auth_admin;

--
-- Name: TABLE sso_domains; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.sso_domains IS 'Auth: Manages SSO email address domain mapping to an SSO Identity Provider.';


--
-- Name: sso_providers; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.sso_providers (
    id uuid NOT NULL,
    resource_id text,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    CONSTRAINT "resource_id not empty" CHECK (((resource_id = NULL::text) OR (char_length(resource_id) > 0)))
);


ALTER TABLE auth.sso_providers OWNER TO supabase_auth_admin;

--
-- Name: TABLE sso_providers; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.sso_providers IS 'Auth: Manages SSO identity provider information; see saml_providers for SAML.';


--
-- Name: COLUMN sso_providers.resource_id; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON COLUMN auth.sso_providers.resource_id IS 'Auth: Uniquely identifies a SSO provider according to a user-chosen resource ID (case insensitive), useful in infrastructure as code.';


--
-- Name: users; Type: TABLE; Schema: auth; Owner: supabase_auth_admin
--

CREATE TABLE auth.users (
    instance_id uuid,
    id uuid NOT NULL,
    aud character varying(255),
    role character varying(255),
    email character varying(255),
    encrypted_password character varying(255),
    email_confirmed_at timestamp with time zone,
    invited_at timestamp with time zone,
    confirmation_token character varying(255),
    confirmation_sent_at timestamp with time zone,
    recovery_token character varying(255),
    recovery_sent_at timestamp with time zone,
    email_change_token_new character varying(255),
    email_change character varying(255),
    email_change_sent_at timestamp with time zone,
    last_sign_in_at timestamp with time zone,
    raw_app_meta_data jsonb,
    raw_user_meta_data jsonb,
    is_super_admin boolean,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    phone text DEFAULT NULL::character varying,
    phone_confirmed_at timestamp with time zone,
    phone_change text DEFAULT ''::character varying,
    phone_change_token character varying(255) DEFAULT ''::character varying,
    phone_change_sent_at timestamp with time zone,
    confirmed_at timestamp with time zone GENERATED ALWAYS AS (LEAST(email_confirmed_at, phone_confirmed_at)) STORED,
    email_change_token_current character varying(255) DEFAULT ''::character varying,
    email_change_confirm_status smallint DEFAULT 0,
    banned_until timestamp with time zone,
    reauthentication_token character varying(255) DEFAULT ''::character varying,
    reauthentication_sent_at timestamp with time zone,
    is_sso_user boolean DEFAULT false NOT NULL,
    deleted_at timestamp with time zone,
    is_anonymous boolean DEFAULT false NOT NULL,
    CONSTRAINT users_email_change_confirm_status_check CHECK (((email_change_confirm_status >= 0) AND (email_change_confirm_status <= 2)))
);


ALTER TABLE auth.users OWNER TO supabase_auth_admin;

--
-- Name: TABLE users; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON TABLE auth.users IS 'Auth: Stores user login data within a secure schema.';


--
-- Name: COLUMN users.is_sso_user; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON COLUMN auth.users.is_sso_user IS 'Auth: Set this column to true when the account comes from SSO. These accounts can have duplicate emails.';


--
-- Name: article_tags; Type: TABLE; Schema: blog; Owner: supabase_admin
--

CREATE TABLE blog.article_tags (
    article_id uuid NOT NULL,
    tag_id uuid NOT NULL
);


ALTER TABLE blog.article_tags OWNER TO supabase_admin;

--
-- Name: article_views; Type: TABLE; Schema: blog; Owner: supabase_admin
--

CREATE TABLE blog.article_views (
    article_id uuid NOT NULL,
    view_count integer DEFAULT 0,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


ALTER TABLE blog.article_views OWNER TO supabase_admin;

--
-- Name: articles; Type: TABLE; Schema: blog; Owner: supabase_admin
--

CREATE TABLE blog.articles (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    title text NOT NULL,
    slug text NOT NULL,
    content text,
    excerpt text,
    cover_image_url text,
    author_id uuid NOT NULL,
    status text DEFAULT 'draft'::text,
    published_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    meta_title text,
    meta_description text,
    featured boolean DEFAULT false,
    CONSTRAINT articles_status_check CHECK ((status = ANY (ARRAY['draft'::text, 'published'::text, 'archived'::text])))
);


ALTER TABLE blog.articles OWNER TO supabase_admin;

--
-- Name: newsletter_subscribers; Type: TABLE; Schema: blog; Owner: supabase_admin
--

CREATE TABLE blog.newsletter_subscribers (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    email text NOT NULL,
    name text,
    subscribed boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    unsubscribed_at timestamp with time zone
);


ALTER TABLE blog.newsletter_subscribers OWNER TO supabase_admin;

--
-- Name: profiles; Type: TABLE; Schema: blog; Owner: supabase_admin
--

CREATE TABLE blog.profiles (
    id uuid NOT NULL,
    updated_at timestamp with time zone DEFAULT now(),
    username text,
    full_name text,
    avatar_url text,
    website text,
    role text DEFAULT 'reader'::text,
    bio text,
    created_at timestamp with time zone DEFAULT now(),
    email text,
    CONSTRAINT profiles_role_check CHECK ((role = ANY (ARRAY['admin'::text, 'editor'::text, 'writer'::text, 'author'::text, 'reader'::text]))),
    CONSTRAINT username_length CHECK ((char_length(username) >= 3))
);


ALTER TABLE blog.profiles OWNER TO supabase_admin;

--
-- Name: tags; Type: TABLE; Schema: blog; Owner: supabase_admin
--

CREATE TABLE blog.tags (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name text NOT NULL,
    slug text NOT NULL,
    description text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


ALTER TABLE blog.tags OWNER TO supabase_admin;

--
-- Name: api_keys; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.api_keys (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    name text NOT NULL,
    key_prefix text NOT NULL,
    key_hash text NOT NULL,
    allow_credits boolean DEFAULT true,
    allow_payg boolean DEFAULT false,
    allow_subscriptions boolean DEFAULT true,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    expires_at timestamp with time zone,
    last_used_at timestamp with time zone
);


ALTER TABLE public.api_keys OWNER TO postgres;

--
-- Name: credits; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.credits (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    balance numeric(18,9) DEFAULT 0 NOT NULL,
    currency text DEFAULT 'USD'::text NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    expires_at timestamp with time zone,
    status text DEFAULT 'active'::text
);


ALTER TABLE public.credits OWNER TO postgres;

--
-- Name: TABLE credits; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.credits IS 'Stores user credit balances. Each row represents a separate purchase with its own expiration date.';


--
-- Name: COLUMN credits.expires_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.credits.expires_at IS 'Date when the credits expire. If NULL, credits do not expire.';


--
-- Name: COLUMN credits.status; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.credits.status IS 'Status of the credit entry (active, expired, consumed). Default is active.';


--
-- Name: models; Type: TABLE; Schema: public; Owner: supabase_admin
--

CREATE TABLE public.models (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    friendly_name character varying(255) NOT NULL,
    actual_url text NOT NULL,
    model_type character varying(50) DEFAULT 'optimization'::character varying,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.models OWNER TO supabase_admin;

--
-- Name: negative_balances; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.negative_balances (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    service text NOT NULL,
    model text NOT NULL,
    amount numeric(18,9) NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    cleared_at timestamp with time zone,
    usage_id uuid,
    CONSTRAINT negative_balances_service_check CHECK ((service = ANY (ARRAY['transcription'::text, 'optimization'::text])))
);


ALTER TABLE public.negative_balances OWNER TO postgres;

--
-- Name: paddle_customers; Type: TABLE; Schema: public; Owner: supabase_admin
--

CREATE TABLE public.paddle_customers (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid NOT NULL,
    paddle_customer_id text NOT NULL,
    email text NOT NULL,
    name text,
    country_code text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    metadata jsonb DEFAULT '{}'::jsonb
);


ALTER TABLE public.paddle_customers OWNER TO supabase_admin;

--
-- Name: TABLE paddle_customers; Type: COMMENT; Schema: public; Owner: supabase_admin
--

COMMENT ON TABLE public.paddle_customers IS 'Paddle customer information linked to VoiceHype users';


--
-- Name: paddle_transactions; Type: TABLE; Schema: public; Owner: supabase_admin
--

CREATE TABLE public.paddle_transactions (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid NOT NULL,
    paddle_transaction_id text NOT NULL,
    paddle_customer_id text NOT NULL,
    paddle_product_id text,
    status text NOT NULL,
    amount numeric(18,2) NOT NULL,
    currency text DEFAULT 'USD'::text,
    credit_amount numeric(18,9),
    transaction_type text NOT NULL,
    paddle_receipt_url text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    metadata jsonb DEFAULT '{}'::jsonb,
    CONSTRAINT paddle_transactions_status_check CHECK ((status = ANY (ARRAY['pending'::text, 'completed'::text, 'failed'::text, 'cancelled'::text, 'refunded'::text]))),
    CONSTRAINT transactions_transaction_type_check CHECK ((transaction_type = ANY (ARRAY['credit_purchase'::text, 'payg_invoice'::text, 'refund'::text, 'subscription'::text, 'subscription_upgrade'::text, 'subscription_renewal'::text, 'subscription_cancelled'::text])))
);


ALTER TABLE public.paddle_transactions OWNER TO supabase_admin;

--
-- Name: TABLE paddle_transactions; Type: COMMENT; Schema: public; Owner: supabase_admin
--

COMMENT ON TABLE public.paddle_transactions IS 'Paddle transaction records for credit purchases and subscriptions';


--
-- Name: CONSTRAINT transactions_transaction_type_check ON paddle_transactions; Type: COMMENT; Schema: public; Owner: supabase_admin
--

COMMENT ON CONSTRAINT transactions_transaction_type_check ON public.paddle_transactions IS 'Allowed transaction types: credit_purchase, payg_invoice, refund, subscription, subscription_upgrade, subscription_renewal, subscription_cancelled';


--
-- Name: payg_usage; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.payg_usage (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    month date NOT NULL,
    total_amount numeric(18,9) DEFAULT 0 NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    payment_status public.payment_status DEFAULT 'pending'::public.payment_status,
    payment_metadata jsonb
);


ALTER TABLE public.payg_usage OWNER TO postgres;

--
-- Name: pricing_models; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.pricing_models (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    name text NOT NULL,
    type text NOT NULL,
    description text,
    priority smallint DEFAULT 1 NOT NULL,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    CONSTRAINT pricing_models_type_check CHECK ((type = ANY (ARRAY['payg'::text, 'credits'::text, 'subscription'::text])))
);


ALTER TABLE public.pricing_models OWNER TO postgres;

--
-- Name: TABLE pricing_models; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.pricing_models IS 'Reference table for pricing models. Modifications should be done via migrations or by superusers directly.';


--
-- Name: profiles; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.profiles (
    id uuid NOT NULL,
    email text NOT NULL,
    full_name text,
    company_name text,
    stripe_customer_id text,
    default_pricing_model text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    credit_allowance numeric(18,9) DEFAULT '0'::numeric NOT NULL,
    CONSTRAINT profiles_default_pricing_model_check CHECK ((default_pricing_model = ANY (ARRAY['credits'::text, 'payg'::text, 'subscription'::text])))
);


ALTER TABLE public.profiles OWNER TO postgres;

--
-- Name: quota_notifications; Type: TABLE; Schema: public; Owner: supabase_admin
--

CREATE TABLE public.quota_notifications (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    quota_id uuid NOT NULL,
    threshold integer NOT NULL,
    sent_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.quota_notifications OWNER TO supabase_admin;

--
-- Name: quotas; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.quotas (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    subscription_id uuid,
    service text NOT NULL,
    used_amount numeric(18,9) DEFAULT 0 NOT NULL,
    reset_date timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    payg_allowance_used numeric(18,9) DEFAULT 0,
    payg_allowance_total integer DEFAULT 10.00,
    total_amount numeric(10,2) NOT NULL,
    CONSTRAINT quotas_service_check CHECK ((service = ANY (ARRAY['transcription'::text, 'optimization'::text]))),
    CONSTRAINT quotas_total_amount_positive CHECK ((total_amount >= (0)::numeric))
);


ALTER TABLE public.quotas OWNER TO postgres;

--
-- Name: TABLE quotas; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.quotas IS 'Stores quota information for users. used_amount and total_amount are stored as numeric(18,9) to allow for precise tracking of transcription minutes.';


--
-- Name: service_pricing; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.service_pricing (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    service text NOT NULL,
    model text NOT NULL,
    cost_per_unit numeric(18,9) NOT NULL,
    unit text NOT NULL,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    CONSTRAINT service_pricing_service_check CHECK ((service = ANY (ARRAY['transcription'::text, 'optimization'::text])))
);


ALTER TABLE public.service_pricing OWNER TO postgres;

--
-- Name: TABLE service_pricing; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.service_pricing IS 'Reference table for service pricing. Modifications should be done via migrations or by superusers directly.';


--
-- Name: subscription_plans; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.subscription_plans (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    name text NOT NULL,
    description text,
    monthly_price numeric(12,2) NOT NULL,
    annual_price numeric(12,2),
    transcription_minutes integer NOT NULL,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    tokens integer NOT NULL,
    input_tokens integer,
    output_tokens integer
);


ALTER TABLE public.subscription_plans OWNER TO postgres;

--
-- Name: TABLE subscription_plans; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.subscription_plans IS 'Reference table for subscription plans. Modifications should be done via migrations or by superusers directly.';


--
-- Name: usage_history; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.usage_history (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    api_key_id uuid,
    service text NOT NULL,
    model text NOT NULL,
    amount numeric(18,9) NOT NULL,
    cost numeric(18,9) NOT NULL,
    pricing_model text NOT NULL,
    status text NOT NULL,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now(),
    CONSTRAINT usage_history_pricing_model_check CHECK ((pricing_model = ANY (ARRAY['credits'::text, 'payg'::text, 'subscription'::text]))),
    CONSTRAINT usage_history_service_check CHECK ((service = ANY (ARRAY['transcription'::text, 'optimization'::text]))),
    CONSTRAINT usage_history_status_check CHECK ((status = ANY (ARRAY['pending'::text, 'success'::text, 'failed'::text])))
);


ALTER TABLE public.usage_history OWNER TO postgres;

--
-- Name: user_subscriptions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_subscriptions (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    plan_id uuid NOT NULL,
    status text NOT NULL,
    current_period_start timestamp with time zone NOT NULL,
    current_period_end timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    cancel_at_period_end boolean DEFAULT false,
    paddle_subscription_id text,
    next_billed_at timestamp with time zone,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT user_subscriptions_status_check CHECK ((status = ANY (ARRAY['active'::text, 'canceled'::text, 'paused'::text, 'past_due'::text])))
);


ALTER TABLE public.user_subscriptions OWNER TO postgres;

--
-- Name: COLUMN user_subscriptions.cancel_at_period_end; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.user_subscriptions.cancel_at_period_end IS 'Whether the subscription will be cancelled at the end of the current period';


--
-- Name: COLUMN user_subscriptions.paddle_subscription_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.user_subscriptions.paddle_subscription_id IS 'Paddle subscription ID for webhook processing';


--
-- Name: COLUMN user_subscriptions.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.user_subscriptions.updated_at IS 'Timestamp when the subscription record was last updated';


--
-- Name: messages; Type: TABLE; Schema: realtime; Owner: supabase_realtime_admin
--

CREATE TABLE realtime.messages (
    topic text NOT NULL,
    extension text NOT NULL,
    payload jsonb,
    event text,
    private boolean DEFAULT false,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    inserted_at timestamp without time zone DEFAULT now() NOT NULL,
    id uuid DEFAULT gen_random_uuid() NOT NULL
)
PARTITION BY RANGE (inserted_at);


ALTER TABLE realtime.messages OWNER TO supabase_realtime_admin;

--
-- Name: messages_2025_08_25; Type: TABLE; Schema: realtime; Owner: supabase_admin
--

CREATE TABLE realtime.messages_2025_08_25 (
    topic text NOT NULL,
    extension text NOT NULL,
    payload jsonb,
    event text,
    private boolean DEFAULT false,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    inserted_at timestamp without time zone DEFAULT now() NOT NULL,
    id uuid DEFAULT gen_random_uuid() NOT NULL
);


ALTER TABLE realtime.messages_2025_08_25 OWNER TO supabase_admin;

--
-- Name: messages_2025_08_26; Type: TABLE; Schema: realtime; Owner: supabase_admin
--

CREATE TABLE realtime.messages_2025_08_26 (
    topic text NOT NULL,
    extension text NOT NULL,
    payload jsonb,
    event text,
    private boolean DEFAULT false,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    inserted_at timestamp without time zone DEFAULT now() NOT NULL,
    id uuid DEFAULT gen_random_uuid() NOT NULL
);


ALTER TABLE realtime.messages_2025_08_26 OWNER TO supabase_admin;

--
-- Name: messages_2025_08_27; Type: TABLE; Schema: realtime; Owner: supabase_admin
--

CREATE TABLE realtime.messages_2025_08_27 (
    topic text NOT NULL,
    extension text NOT NULL,
    payload jsonb,
    event text,
    private boolean DEFAULT false,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    inserted_at timestamp without time zone DEFAULT now() NOT NULL,
    id uuid DEFAULT gen_random_uuid() NOT NULL
);


ALTER TABLE realtime.messages_2025_08_27 OWNER TO supabase_admin;

--
-- Name: messages_2025_08_28; Type: TABLE; Schema: realtime; Owner: supabase_admin
--

CREATE TABLE realtime.messages_2025_08_28 (
    topic text NOT NULL,
    extension text NOT NULL,
    payload jsonb,
    event text,
    private boolean DEFAULT false,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    inserted_at timestamp without time zone DEFAULT now() NOT NULL,
    id uuid DEFAULT gen_random_uuid() NOT NULL
);


ALTER TABLE realtime.messages_2025_08_28 OWNER TO supabase_admin;

--
-- Name: messages_2025_08_29; Type: TABLE; Schema: realtime; Owner: supabase_admin
--

CREATE TABLE realtime.messages_2025_08_29 (
    topic text NOT NULL,
    extension text NOT NULL,
    payload jsonb,
    event text,
    private boolean DEFAULT false,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    inserted_at timestamp without time zone DEFAULT now() NOT NULL,
    id uuid DEFAULT gen_random_uuid() NOT NULL
);


ALTER TABLE realtime.messages_2025_08_29 OWNER TO supabase_admin;

--
-- Name: schema_migrations; Type: TABLE; Schema: realtime; Owner: supabase_admin
--

CREATE TABLE realtime.schema_migrations (
    version bigint NOT NULL,
    inserted_at timestamp(0) without time zone
);


ALTER TABLE realtime.schema_migrations OWNER TO supabase_admin;

--
-- Name: subscription; Type: TABLE; Schema: realtime; Owner: supabase_admin
--

CREATE TABLE realtime.subscription (
    id bigint NOT NULL,
    subscription_id uuid NOT NULL,
    entity regclass NOT NULL,
    filters realtime.user_defined_filter[] DEFAULT '{}'::realtime.user_defined_filter[] NOT NULL,
    claims jsonb NOT NULL,
    claims_role regrole GENERATED ALWAYS AS (realtime.to_regrole((claims ->> 'role'::text))) STORED NOT NULL,
    created_at timestamp without time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);


ALTER TABLE realtime.subscription OWNER TO supabase_admin;

--
-- Name: subscription_id_seq; Type: SEQUENCE; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE realtime.subscription ALTER COLUMN id ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME realtime.subscription_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: buckets; Type: TABLE; Schema: storage; Owner: supabase_storage_admin
--

CREATE TABLE storage.buckets (
    id text NOT NULL,
    name text NOT NULL,
    owner uuid,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    public boolean DEFAULT false,
    avif_autodetection boolean DEFAULT false,
    file_size_limit bigint,
    allowed_mime_types text[],
    owner_id text
);


ALTER TABLE storage.buckets OWNER TO supabase_storage_admin;

--
-- Name: COLUMN buckets.owner; Type: COMMENT; Schema: storage; Owner: supabase_storage_admin
--

COMMENT ON COLUMN storage.buckets.owner IS 'Field is deprecated, use owner_id instead';


--
-- Name: migrations; Type: TABLE; Schema: storage; Owner: supabase_storage_admin
--

CREATE TABLE storage.migrations (
    id integer NOT NULL,
    name character varying(100) NOT NULL,
    hash character varying(40) NOT NULL,
    executed_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE storage.migrations OWNER TO supabase_storage_admin;

--
-- Name: objects; Type: TABLE; Schema: storage; Owner: supabase_storage_admin
--

CREATE TABLE storage.objects (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    bucket_id text,
    name text,
    owner uuid,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    last_accessed_at timestamp with time zone DEFAULT now(),
    metadata jsonb,
    path_tokens text[] GENERATED ALWAYS AS (string_to_array(name, '/'::text)) STORED,
    version text,
    owner_id text,
    user_metadata jsonb,
    level integer
);


ALTER TABLE storage.objects OWNER TO supabase_storage_admin;

--
-- Name: COLUMN objects.owner; Type: COMMENT; Schema: storage; Owner: supabase_storage_admin
--

COMMENT ON COLUMN storage.objects.owner IS 'Field is deprecated, use owner_id instead';


--
-- Name: prefixes; Type: TABLE; Schema: storage; Owner: supabase_storage_admin
--

CREATE TABLE storage.prefixes (
    bucket_id text NOT NULL,
    name text NOT NULL COLLATE pg_catalog."C",
    level integer GENERATED ALWAYS AS (storage.get_level(name)) STORED NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


ALTER TABLE storage.prefixes OWNER TO supabase_storage_admin;

--
-- Name: s3_multipart_uploads; Type: TABLE; Schema: storage; Owner: supabase_storage_admin
--

CREATE TABLE storage.s3_multipart_uploads (
    id text NOT NULL,
    in_progress_size bigint DEFAULT 0 NOT NULL,
    upload_signature text NOT NULL,
    bucket_id text NOT NULL,
    key text NOT NULL COLLATE pg_catalog."C",
    version text NOT NULL,
    owner_id text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    user_metadata jsonb
);


ALTER TABLE storage.s3_multipart_uploads OWNER TO supabase_storage_admin;

--
-- Name: s3_multipart_uploads_parts; Type: TABLE; Schema: storage; Owner: supabase_storage_admin
--

CREATE TABLE storage.s3_multipart_uploads_parts (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    upload_id text NOT NULL,
    size bigint DEFAULT 0 NOT NULL,
    part_number integer NOT NULL,
    bucket_id text NOT NULL,
    key text NOT NULL COLLATE pg_catalog."C",
    etag text NOT NULL,
    owner_id text,
    version text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE storage.s3_multipart_uploads_parts OWNER TO supabase_storage_admin;

--
-- Name: hooks; Type: TABLE; Schema: supabase_functions; Owner: supabase_functions_admin
--

CREATE TABLE supabase_functions.hooks (
    id bigint NOT NULL,
    hook_table_id integer NOT NULL,
    hook_name text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    request_id bigint
);


ALTER TABLE supabase_functions.hooks OWNER TO supabase_functions_admin;

--
-- Name: TABLE hooks; Type: COMMENT; Schema: supabase_functions; Owner: supabase_functions_admin
--

COMMENT ON TABLE supabase_functions.hooks IS 'Supabase Functions Hooks: Audit trail for triggered hooks.';


--
-- Name: hooks_id_seq; Type: SEQUENCE; Schema: supabase_functions; Owner: supabase_functions_admin
--

CREATE SEQUENCE supabase_functions.hooks_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE supabase_functions.hooks_id_seq OWNER TO supabase_functions_admin;

--
-- Name: hooks_id_seq; Type: SEQUENCE OWNED BY; Schema: supabase_functions; Owner: supabase_functions_admin
--

ALTER SEQUENCE supabase_functions.hooks_id_seq OWNED BY supabase_functions.hooks.id;


--
-- Name: migrations; Type: TABLE; Schema: supabase_functions; Owner: supabase_functions_admin
--

CREATE TABLE supabase_functions.migrations (
    version text NOT NULL,
    inserted_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE supabase_functions.migrations OWNER TO supabase_functions_admin;

--
-- Name: messages_2025_08_25; Type: TABLE ATTACH; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE ONLY realtime.messages ATTACH PARTITION realtime.messages_2025_08_25 FOR VALUES FROM ('2025-08-25 00:00:00') TO ('2025-08-26 00:00:00');


--
-- Name: messages_2025_08_26; Type: TABLE ATTACH; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE ONLY realtime.messages ATTACH PARTITION realtime.messages_2025_08_26 FOR VALUES FROM ('2025-08-26 00:00:00') TO ('2025-08-27 00:00:00');


--
-- Name: messages_2025_08_27; Type: TABLE ATTACH; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE ONLY realtime.messages ATTACH PARTITION realtime.messages_2025_08_27 FOR VALUES FROM ('2025-08-27 00:00:00') TO ('2025-08-28 00:00:00');


--
-- Name: messages_2025_08_28; Type: TABLE ATTACH; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE ONLY realtime.messages ATTACH PARTITION realtime.messages_2025_08_28 FOR VALUES FROM ('2025-08-28 00:00:00') TO ('2025-08-29 00:00:00');


--
-- Name: messages_2025_08_29; Type: TABLE ATTACH; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE ONLY realtime.messages ATTACH PARTITION realtime.messages_2025_08_29 FOR VALUES FROM ('2025-08-29 00:00:00') TO ('2025-08-30 00:00:00');


--
-- Name: refresh_tokens id; Type: DEFAULT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.refresh_tokens ALTER COLUMN id SET DEFAULT nextval('auth.refresh_tokens_id_seq'::regclass);


--
-- Name: hooks id; Type: DEFAULT; Schema: supabase_functions; Owner: supabase_functions_admin
--

ALTER TABLE ONLY supabase_functions.hooks ALTER COLUMN id SET DEFAULT nextval('supabase_functions.hooks_id_seq'::regclass);


--
-- Name: extensions extensions_pkey; Type: CONSTRAINT; Schema: _realtime; Owner: supabase_admin
--

ALTER TABLE ONLY _realtime.extensions
    ADD CONSTRAINT extensions_pkey PRIMARY KEY (id);


--
-- Name: schema_migrations schema_migrations_pkey; Type: CONSTRAINT; Schema: _realtime; Owner: supabase_admin
--

ALTER TABLE ONLY _realtime.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);


--
-- Name: tenants tenants_pkey; Type: CONSTRAINT; Schema: _realtime; Owner: supabase_admin
--

ALTER TABLE ONLY _realtime.tenants
    ADD CONSTRAINT tenants_pkey PRIMARY KEY (id);


--
-- Name: mfa_amr_claims amr_id_pk; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.mfa_amr_claims
    ADD CONSTRAINT amr_id_pk PRIMARY KEY (id);


--
-- Name: audit_log_entries audit_log_entries_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.audit_log_entries
    ADD CONSTRAINT audit_log_entries_pkey PRIMARY KEY (id);


--
-- Name: flow_state flow_state_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.flow_state
    ADD CONSTRAINT flow_state_pkey PRIMARY KEY (id);


--
-- Name: identities identities_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.identities
    ADD CONSTRAINT identities_pkey PRIMARY KEY (id);


--
-- Name: identities identities_provider_id_provider_unique; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.identities
    ADD CONSTRAINT identities_provider_id_provider_unique UNIQUE (provider_id, provider);


--
-- Name: instances instances_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.instances
    ADD CONSTRAINT instances_pkey PRIMARY KEY (id);


--
-- Name: mfa_amr_claims mfa_amr_claims_session_id_authentication_method_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.mfa_amr_claims
    ADD CONSTRAINT mfa_amr_claims_session_id_authentication_method_pkey UNIQUE (session_id, authentication_method);


--
-- Name: mfa_challenges mfa_challenges_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.mfa_challenges
    ADD CONSTRAINT mfa_challenges_pkey PRIMARY KEY (id);


--
-- Name: mfa_factors mfa_factors_last_challenged_at_key; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.mfa_factors
    ADD CONSTRAINT mfa_factors_last_challenged_at_key UNIQUE (last_challenged_at);


--
-- Name: mfa_factors mfa_factors_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.mfa_factors
    ADD CONSTRAINT mfa_factors_pkey PRIMARY KEY (id);


--
-- Name: one_time_tokens one_time_tokens_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.one_time_tokens
    ADD CONSTRAINT one_time_tokens_pkey PRIMARY KEY (id);


--
-- Name: refresh_tokens refresh_tokens_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.refresh_tokens
    ADD CONSTRAINT refresh_tokens_pkey PRIMARY KEY (id);


--
-- Name: refresh_tokens refresh_tokens_token_unique; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.refresh_tokens
    ADD CONSTRAINT refresh_tokens_token_unique UNIQUE (token);


--
-- Name: saml_providers saml_providers_entity_id_key; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.saml_providers
    ADD CONSTRAINT saml_providers_entity_id_key UNIQUE (entity_id);


--
-- Name: saml_providers saml_providers_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.saml_providers
    ADD CONSTRAINT saml_providers_pkey PRIMARY KEY (id);


--
-- Name: saml_relay_states saml_relay_states_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.saml_relay_states
    ADD CONSTRAINT saml_relay_states_pkey PRIMARY KEY (id);


--
-- Name: schema_migrations schema_migrations_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);


--
-- Name: sessions sessions_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.sessions
    ADD CONSTRAINT sessions_pkey PRIMARY KEY (id);


--
-- Name: sso_domains sso_domains_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.sso_domains
    ADD CONSTRAINT sso_domains_pkey PRIMARY KEY (id);


--
-- Name: sso_providers sso_providers_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.sso_providers
    ADD CONSTRAINT sso_providers_pkey PRIMARY KEY (id);


--
-- Name: users users_phone_key; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.users
    ADD CONSTRAINT users_phone_key UNIQUE (phone);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: article_tags article_tags_pkey; Type: CONSTRAINT; Schema: blog; Owner: supabase_admin
--

ALTER TABLE ONLY blog.article_tags
    ADD CONSTRAINT article_tags_pkey PRIMARY KEY (article_id, tag_id);


--
-- Name: article_views article_views_pkey; Type: CONSTRAINT; Schema: blog; Owner: supabase_admin
--

ALTER TABLE ONLY blog.article_views
    ADD CONSTRAINT article_views_pkey PRIMARY KEY (article_id);


--
-- Name: articles articles_pkey; Type: CONSTRAINT; Schema: blog; Owner: supabase_admin
--

ALTER TABLE ONLY blog.articles
    ADD CONSTRAINT articles_pkey PRIMARY KEY (id);


--
-- Name: articles articles_slug_key; Type: CONSTRAINT; Schema: blog; Owner: supabase_admin
--

ALTER TABLE ONLY blog.articles
    ADD CONSTRAINT articles_slug_key UNIQUE (slug);


--
-- Name: newsletter_subscribers newsletter_subscribers_email_key; Type: CONSTRAINT; Schema: blog; Owner: supabase_admin
--

ALTER TABLE ONLY blog.newsletter_subscribers
    ADD CONSTRAINT newsletter_subscribers_email_key UNIQUE (email);


--
-- Name: newsletter_subscribers newsletter_subscribers_pkey; Type: CONSTRAINT; Schema: blog; Owner: supabase_admin
--

ALTER TABLE ONLY blog.newsletter_subscribers
    ADD CONSTRAINT newsletter_subscribers_pkey PRIMARY KEY (id);


--
-- Name: profiles profiles_pkey; Type: CONSTRAINT; Schema: blog; Owner: supabase_admin
--

ALTER TABLE ONLY blog.profiles
    ADD CONSTRAINT profiles_pkey PRIMARY KEY (id);


--
-- Name: profiles profiles_username_key; Type: CONSTRAINT; Schema: blog; Owner: supabase_admin
--

ALTER TABLE ONLY blog.profiles
    ADD CONSTRAINT profiles_username_key UNIQUE (username);


--
-- Name: tags tags_name_key; Type: CONSTRAINT; Schema: blog; Owner: supabase_admin
--

ALTER TABLE ONLY blog.tags
    ADD CONSTRAINT tags_name_key UNIQUE (name);


--
-- Name: tags tags_pkey; Type: CONSTRAINT; Schema: blog; Owner: supabase_admin
--

ALTER TABLE ONLY blog.tags
    ADD CONSTRAINT tags_pkey PRIMARY KEY (id);


--
-- Name: tags tags_slug_key; Type: CONSTRAINT; Schema: blog; Owner: supabase_admin
--

ALTER TABLE ONLY blog.tags
    ADD CONSTRAINT tags_slug_key UNIQUE (slug);


--
-- Name: api_keys api_keys_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.api_keys
    ADD CONSTRAINT api_keys_pkey PRIMARY KEY (id);


--
-- Name: credits credits_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.credits
    ADD CONSTRAINT credits_pkey PRIMARY KEY (id);


--
-- Name: models models_pkey; Type: CONSTRAINT; Schema: public; Owner: supabase_admin
--

ALTER TABLE ONLY public.models
    ADD CONSTRAINT models_pkey PRIMARY KEY (id);


--
-- Name: negative_balances negative_balances_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.negative_balances
    ADD CONSTRAINT negative_balances_pkey PRIMARY KEY (id);


--
-- Name: paddle_customers paddle_customers_paddle_customer_id_key; Type: CONSTRAINT; Schema: public; Owner: supabase_admin
--

ALTER TABLE ONLY public.paddle_customers
    ADD CONSTRAINT paddle_customers_paddle_customer_id_key UNIQUE (paddle_customer_id);


--
-- Name: paddle_customers paddle_customers_pkey; Type: CONSTRAINT; Schema: public; Owner: supabase_admin
--

ALTER TABLE ONLY public.paddle_customers
    ADD CONSTRAINT paddle_customers_pkey PRIMARY KEY (id);


--
-- Name: paddle_customers paddle_customers_user_id_unique; Type: CONSTRAINT; Schema: public; Owner: supabase_admin
--

ALTER TABLE ONLY public.paddle_customers
    ADD CONSTRAINT paddle_customers_user_id_unique UNIQUE (user_id);


--
-- Name: paddle_transactions paddle_transactions_paddle_transaction_id_key; Type: CONSTRAINT; Schema: public; Owner: supabase_admin
--

ALTER TABLE ONLY public.paddle_transactions
    ADD CONSTRAINT paddle_transactions_paddle_transaction_id_key UNIQUE (paddle_transaction_id);


--
-- Name: paddle_transactions paddle_transactions_pkey; Type: CONSTRAINT; Schema: public; Owner: supabase_admin
--

ALTER TABLE ONLY public.paddle_transactions
    ADD CONSTRAINT paddle_transactions_pkey PRIMARY KEY (id);


--
-- Name: payg_usage payg_usage_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.payg_usage
    ADD CONSTRAINT payg_usage_pkey PRIMARY KEY (id);


--
-- Name: payg_usage payg_usage_user_month_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.payg_usage
    ADD CONSTRAINT payg_usage_user_month_key UNIQUE (user_id, month);


--
-- Name: pricing_models pricing_models_name_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.pricing_models
    ADD CONSTRAINT pricing_models_name_key UNIQUE (name);


--
-- Name: pricing_models pricing_models_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.pricing_models
    ADD CONSTRAINT pricing_models_pkey PRIMARY KEY (id);


--
-- Name: profiles profiles_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.profiles
    ADD CONSTRAINT profiles_pkey PRIMARY KEY (id);


--
-- Name: quota_notifications quota_notifications_pkey; Type: CONSTRAINT; Schema: public; Owner: supabase_admin
--

ALTER TABLE ONLY public.quota_notifications
    ADD CONSTRAINT quota_notifications_pkey PRIMARY KEY (id);


--
-- Name: quota_notifications quota_notifications_user_quota_threshold; Type: CONSTRAINT; Schema: public; Owner: supabase_admin
--

ALTER TABLE ONLY public.quota_notifications
    ADD CONSTRAINT quota_notifications_user_quota_threshold UNIQUE (user_id, quota_id, threshold);


--
-- Name: quotas quotas_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.quotas
    ADD CONSTRAINT quotas_pkey PRIMARY KEY (id);


--
-- Name: quotas quotas_user_service_unique; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.quotas
    ADD CONSTRAINT quotas_user_service_unique UNIQUE (user_id, service);


--
-- Name: service_pricing service_pricing_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.service_pricing
    ADD CONSTRAINT service_pricing_pkey PRIMARY KEY (id);


--
-- Name: service_pricing service_pricing_service_model_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.service_pricing
    ADD CONSTRAINT service_pricing_service_model_key UNIQUE (service, model);


--
-- Name: subscription_plans subscription_plans_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.subscription_plans
    ADD CONSTRAINT subscription_plans_pkey PRIMARY KEY (id);


--
-- Name: usage_history usage_history_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.usage_history
    ADD CONSTRAINT usage_history_pkey PRIMARY KEY (id);


--
-- Name: user_subscriptions user_subscriptions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_subscriptions
    ADD CONSTRAINT user_subscriptions_pkey PRIMARY KEY (id);


--
-- Name: user_subscriptions user_subscriptions_user_id_unique; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_subscriptions
    ADD CONSTRAINT user_subscriptions_user_id_unique UNIQUE (user_id);


--
-- Name: messages messages_pkey; Type: CONSTRAINT; Schema: realtime; Owner: supabase_realtime_admin
--

ALTER TABLE ONLY realtime.messages
    ADD CONSTRAINT messages_pkey PRIMARY KEY (id, inserted_at);


--
-- Name: messages_2025_08_25 messages_2025_08_25_pkey; Type: CONSTRAINT; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE ONLY realtime.messages_2025_08_25
    ADD CONSTRAINT messages_2025_08_25_pkey PRIMARY KEY (id, inserted_at);


--
-- Name: messages_2025_08_26 messages_2025_08_26_pkey; Type: CONSTRAINT; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE ONLY realtime.messages_2025_08_26
    ADD CONSTRAINT messages_2025_08_26_pkey PRIMARY KEY (id, inserted_at);


--
-- Name: messages_2025_08_27 messages_2025_08_27_pkey; Type: CONSTRAINT; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE ONLY realtime.messages_2025_08_27
    ADD CONSTRAINT messages_2025_08_27_pkey PRIMARY KEY (id, inserted_at);


--
-- Name: messages_2025_08_28 messages_2025_08_28_pkey; Type: CONSTRAINT; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE ONLY realtime.messages_2025_08_28
    ADD CONSTRAINT messages_2025_08_28_pkey PRIMARY KEY (id, inserted_at);


--
-- Name: messages_2025_08_29 messages_2025_08_29_pkey; Type: CONSTRAINT; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE ONLY realtime.messages_2025_08_29
    ADD CONSTRAINT messages_2025_08_29_pkey PRIMARY KEY (id, inserted_at);


--
-- Name: subscription pk_subscription; Type: CONSTRAINT; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE ONLY realtime.subscription
    ADD CONSTRAINT pk_subscription PRIMARY KEY (id);


--
-- Name: schema_migrations schema_migrations_pkey; Type: CONSTRAINT; Schema: realtime; Owner: supabase_admin
--

ALTER TABLE ONLY realtime.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);


--
-- Name: buckets buckets_pkey; Type: CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.buckets
    ADD CONSTRAINT buckets_pkey PRIMARY KEY (id);


--
-- Name: migrations migrations_name_key; Type: CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.migrations
    ADD CONSTRAINT migrations_name_key UNIQUE (name);


--
-- Name: migrations migrations_pkey; Type: CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.migrations
    ADD CONSTRAINT migrations_pkey PRIMARY KEY (id);


--
-- Name: objects objects_pkey; Type: CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.objects
    ADD CONSTRAINT objects_pkey PRIMARY KEY (id);


--
-- Name: prefixes prefixes_pkey; Type: CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.prefixes
    ADD CONSTRAINT prefixes_pkey PRIMARY KEY (bucket_id, level, name);


--
-- Name: s3_multipart_uploads_parts s3_multipart_uploads_parts_pkey; Type: CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.s3_multipart_uploads_parts
    ADD CONSTRAINT s3_multipart_uploads_parts_pkey PRIMARY KEY (id);


--
-- Name: s3_multipart_uploads s3_multipart_uploads_pkey; Type: CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.s3_multipart_uploads
    ADD CONSTRAINT s3_multipart_uploads_pkey PRIMARY KEY (id);


--
-- Name: hooks hooks_pkey; Type: CONSTRAINT; Schema: supabase_functions; Owner: supabase_functions_admin
--

ALTER TABLE ONLY supabase_functions.hooks
    ADD CONSTRAINT hooks_pkey PRIMARY KEY (id);


--
-- Name: migrations migrations_pkey; Type: CONSTRAINT; Schema: supabase_functions; Owner: supabase_functions_admin
--

ALTER TABLE ONLY supabase_functions.migrations
    ADD CONSTRAINT migrations_pkey PRIMARY KEY (version);


--
-- Name: extensions_tenant_external_id_index; Type: INDEX; Schema: _realtime; Owner: supabase_admin
--

CREATE INDEX extensions_tenant_external_id_index ON _realtime.extensions USING btree (tenant_external_id);


--
-- Name: extensions_tenant_external_id_type_index; Type: INDEX; Schema: _realtime; Owner: supabase_admin
--

CREATE UNIQUE INDEX extensions_tenant_external_id_type_index ON _realtime.extensions USING btree (tenant_external_id, type);


--
-- Name: tenants_external_id_index; Type: INDEX; Schema: _realtime; Owner: supabase_admin
--

CREATE UNIQUE INDEX tenants_external_id_index ON _realtime.tenants USING btree (external_id);


--
-- Name: audit_logs_instance_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX audit_logs_instance_id_idx ON auth.audit_log_entries USING btree (instance_id);


--
-- Name: confirmation_token_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX confirmation_token_idx ON auth.users USING btree (confirmation_token) WHERE ((confirmation_token)::text !~ '^[0-9 ]*$'::text);


--
-- Name: email_change_token_current_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX email_change_token_current_idx ON auth.users USING btree (email_change_token_current) WHERE ((email_change_token_current)::text !~ '^[0-9 ]*$'::text);


--
-- Name: email_change_token_new_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX email_change_token_new_idx ON auth.users USING btree (email_change_token_new) WHERE ((email_change_token_new)::text !~ '^[0-9 ]*$'::text);


--
-- Name: factor_id_created_at_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX factor_id_created_at_idx ON auth.mfa_factors USING btree (user_id, created_at);


--
-- Name: flow_state_created_at_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX flow_state_created_at_idx ON auth.flow_state USING btree (created_at DESC);


--
-- Name: identities_email_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX identities_email_idx ON auth.identities USING btree (email text_pattern_ops);


--
-- Name: INDEX identities_email_idx; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON INDEX auth.identities_email_idx IS 'Auth: Ensures indexed queries on the email column';


--
-- Name: identities_user_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX identities_user_id_idx ON auth.identities USING btree (user_id);


--
-- Name: idx_auth_code; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX idx_auth_code ON auth.flow_state USING btree (auth_code);


--
-- Name: idx_user_id_auth_method; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX idx_user_id_auth_method ON auth.flow_state USING btree (user_id, authentication_method);


--
-- Name: mfa_challenge_created_at_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX mfa_challenge_created_at_idx ON auth.mfa_challenges USING btree (created_at DESC);


--
-- Name: mfa_factors_user_friendly_name_unique; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX mfa_factors_user_friendly_name_unique ON auth.mfa_factors USING btree (friendly_name, user_id) WHERE (TRIM(BOTH FROM friendly_name) <> ''::text);


--
-- Name: mfa_factors_user_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX mfa_factors_user_id_idx ON auth.mfa_factors USING btree (user_id);


--
-- Name: one_time_tokens_relates_to_hash_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX one_time_tokens_relates_to_hash_idx ON auth.one_time_tokens USING hash (relates_to);


--
-- Name: one_time_tokens_token_hash_hash_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX one_time_tokens_token_hash_hash_idx ON auth.one_time_tokens USING hash (token_hash);


--
-- Name: one_time_tokens_user_id_token_type_key; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX one_time_tokens_user_id_token_type_key ON auth.one_time_tokens USING btree (user_id, token_type);


--
-- Name: reauthentication_token_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX reauthentication_token_idx ON auth.users USING btree (reauthentication_token) WHERE ((reauthentication_token)::text !~ '^[0-9 ]*$'::text);


--
-- Name: recovery_token_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX recovery_token_idx ON auth.users USING btree (recovery_token) WHERE ((recovery_token)::text !~ '^[0-9 ]*$'::text);


--
-- Name: refresh_tokens_instance_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX refresh_tokens_instance_id_idx ON auth.refresh_tokens USING btree (instance_id);


--
-- Name: refresh_tokens_instance_id_user_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX refresh_tokens_instance_id_user_id_idx ON auth.refresh_tokens USING btree (instance_id, user_id);


--
-- Name: refresh_tokens_parent_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX refresh_tokens_parent_idx ON auth.refresh_tokens USING btree (parent);


--
-- Name: refresh_tokens_session_id_revoked_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX refresh_tokens_session_id_revoked_idx ON auth.refresh_tokens USING btree (session_id, revoked);


--
-- Name: refresh_tokens_updated_at_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX refresh_tokens_updated_at_idx ON auth.refresh_tokens USING btree (updated_at DESC);


--
-- Name: saml_providers_sso_provider_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX saml_providers_sso_provider_id_idx ON auth.saml_providers USING btree (sso_provider_id);


--
-- Name: saml_relay_states_created_at_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX saml_relay_states_created_at_idx ON auth.saml_relay_states USING btree (created_at DESC);


--
-- Name: saml_relay_states_for_email_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX saml_relay_states_for_email_idx ON auth.saml_relay_states USING btree (for_email);


--
-- Name: saml_relay_states_sso_provider_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX saml_relay_states_sso_provider_id_idx ON auth.saml_relay_states USING btree (sso_provider_id);


--
-- Name: sessions_not_after_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX sessions_not_after_idx ON auth.sessions USING btree (not_after DESC);


--
-- Name: sessions_user_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX sessions_user_id_idx ON auth.sessions USING btree (user_id);


--
-- Name: sso_domains_domain_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX sso_domains_domain_idx ON auth.sso_domains USING btree (lower(domain));


--
-- Name: sso_domains_sso_provider_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX sso_domains_sso_provider_id_idx ON auth.sso_domains USING btree (sso_provider_id);


--
-- Name: sso_providers_resource_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX sso_providers_resource_id_idx ON auth.sso_providers USING btree (lower(resource_id));


--
-- Name: unique_phone_factor_per_user; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX unique_phone_factor_per_user ON auth.mfa_factors USING btree (user_id, phone);


--
-- Name: user_id_created_at_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX user_id_created_at_idx ON auth.sessions USING btree (user_id, created_at);


--
-- Name: users_email_partial_key; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE UNIQUE INDEX users_email_partial_key ON auth.users USING btree (email) WHERE (is_sso_user = false);


--
-- Name: INDEX users_email_partial_key; Type: COMMENT; Schema: auth; Owner: supabase_auth_admin
--

COMMENT ON INDEX auth.users_email_partial_key IS 'Auth: A partial unique index that applies only when is_sso_user is false';


--
-- Name: users_instance_id_email_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX users_instance_id_email_idx ON auth.users USING btree (instance_id, lower((email)::text));


--
-- Name: users_instance_id_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX users_instance_id_idx ON auth.users USING btree (instance_id);


--
-- Name: users_is_anonymous_idx; Type: INDEX; Schema: auth; Owner: supabase_auth_admin
--

CREATE INDEX users_is_anonymous_idx ON auth.users USING btree (is_anonymous);


--
-- Name: idx_article_tags_article_id; Type: INDEX; Schema: blog; Owner: supabase_admin
--

CREATE INDEX idx_article_tags_article_id ON blog.article_tags USING btree (article_id);


--
-- Name: idx_article_tags_tag_id; Type: INDEX; Schema: blog; Owner: supabase_admin
--

CREATE INDEX idx_article_tags_tag_id ON blog.article_tags USING btree (tag_id);


--
-- Name: idx_article_views_article_id; Type: INDEX; Schema: blog; Owner: supabase_admin
--

CREATE INDEX idx_article_views_article_id ON blog.article_views USING btree (article_id);


--
-- Name: idx_articles_author_id; Type: INDEX; Schema: blog; Owner: supabase_admin
--

CREATE INDEX idx_articles_author_id ON blog.articles USING btree (author_id);


--
-- Name: idx_articles_featured; Type: INDEX; Schema: blog; Owner: supabase_admin
--

CREATE INDEX idx_articles_featured ON blog.articles USING btree (featured);


--
-- Name: idx_articles_published_at; Type: INDEX; Schema: blog; Owner: supabase_admin
--

CREATE INDEX idx_articles_published_at ON blog.articles USING btree (published_at);


--
-- Name: idx_articles_slug; Type: INDEX; Schema: blog; Owner: supabase_admin
--

CREATE INDEX idx_articles_slug ON blog.articles USING btree (slug);


--
-- Name: idx_articles_status; Type: INDEX; Schema: blog; Owner: supabase_admin
--

CREATE INDEX idx_articles_status ON blog.articles USING btree (status);


--
-- Name: api_keys_key_hash_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX api_keys_key_hash_idx ON public.api_keys USING btree (key_hash);


--
-- Name: idx_api_keys_allow_payg; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_api_keys_allow_payg ON public.api_keys USING btree (allow_payg) WHERE (allow_payg = true);


--
-- Name: idx_api_keys_user; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_api_keys_user ON public.api_keys USING btree (user_id);


--
-- Name: idx_credits_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_credits_status ON public.credits USING btree (status);


--
-- Name: idx_credits_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_credits_user_id ON public.credits USING btree (user_id);


--
-- Name: idx_models_active; Type: INDEX; Schema: public; Owner: supabase_admin
--

CREATE INDEX idx_models_active ON public.models USING btree (is_active);


--
-- Name: idx_models_friendly_name; Type: INDEX; Schema: public; Owner: supabase_admin
--

CREATE INDEX idx_models_friendly_name ON public.models USING btree (friendly_name);


--
-- Name: idx_negative_balances_cleared_at; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_negative_balances_cleared_at ON public.negative_balances USING btree (cleared_at) WHERE (cleared_at IS NULL);


--
-- Name: idx_negative_balances_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_negative_balances_user_id ON public.negative_balances USING btree (user_id);


--
-- Name: idx_paddle_customers_email; Type: INDEX; Schema: public; Owner: supabase_admin
--

CREATE INDEX idx_paddle_customers_email ON public.paddle_customers USING btree (email);


--
-- Name: idx_paddle_customers_paddle_customer_id; Type: INDEX; Schema: public; Owner: supabase_admin
--

CREATE INDEX idx_paddle_customers_paddle_customer_id ON public.paddle_customers USING btree (paddle_customer_id);


--
-- Name: idx_paddle_customers_user_id; Type: INDEX; Schema: public; Owner: supabase_admin
--

CREATE INDEX idx_paddle_customers_user_id ON public.paddle_customers USING btree (user_id);


--
-- Name: idx_paddle_transactions_created_at; Type: INDEX; Schema: public; Owner: supabase_admin
--

CREATE INDEX idx_paddle_transactions_created_at ON public.paddle_transactions USING btree (created_at);


--
-- Name: idx_paddle_transactions_paddle_customer_id; Type: INDEX; Schema: public; Owner: supabase_admin
--

CREATE INDEX idx_paddle_transactions_paddle_customer_id ON public.paddle_transactions USING btree (paddle_customer_id);


--
-- Name: idx_paddle_transactions_status; Type: INDEX; Schema: public; Owner: supabase_admin
--

CREATE INDEX idx_paddle_transactions_status ON public.paddle_transactions USING btree (status);


--
-- Name: idx_paddle_transactions_type; Type: INDEX; Schema: public; Owner: supabase_admin
--

CREATE INDEX idx_paddle_transactions_type ON public.paddle_transactions USING btree (transaction_type);


--
-- Name: idx_paddle_transactions_user_id; Type: INDEX; Schema: public; Owner: supabase_admin
--

CREATE INDEX idx_paddle_transactions_user_id ON public.paddle_transactions USING btree (user_id);


--
-- Name: idx_payg_usage_month; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_payg_usage_month ON public.payg_usage USING btree (month);


--
-- Name: idx_quota_notifications_user_id; Type: INDEX; Schema: public; Owner: supabase_admin
--

CREATE INDEX idx_quota_notifications_user_id ON public.quota_notifications USING btree (user_id);


--
-- Name: idx_quotas_reset_date; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_quotas_reset_date ON public.quotas USING btree (reset_date);


--
-- Name: idx_service_pricing_lookup; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_service_pricing_lookup ON public.service_pricing USING btree (service, model);


--
-- Name: idx_subscriptions_user; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_subscriptions_user ON public.user_subscriptions USING btree (user_id);


--
-- Name: idx_usage_history_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_usage_history_status ON public.usage_history USING btree (status);


--
-- Name: idx_usage_history_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_usage_history_user_id ON public.usage_history USING btree (user_id);


--
-- Name: idx_user_subscriptions_cancel_at_period_end; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_user_subscriptions_cancel_at_period_end ON public.user_subscriptions USING btree (cancel_at_period_end) WHERE (cancel_at_period_end = true);


--
-- Name: idx_user_subscriptions_paddle_subscription_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_user_subscriptions_paddle_subscription_id ON public.user_subscriptions USING btree (paddle_subscription_id);


--
-- Name: quotas_user_id_subscription_id_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX quotas_user_id_subscription_id_idx ON public.quotas USING btree (user_id, subscription_id);


--
-- Name: usage_history_created_at_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX usage_history_created_at_idx ON public.usage_history USING btree (created_at);


--
-- Name: user_subscriptions_plan_id_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX user_subscriptions_plan_id_idx ON public.user_subscriptions USING btree (plan_id);


--
-- Name: ix_realtime_subscription_entity; Type: INDEX; Schema: realtime; Owner: supabase_admin
--

CREATE INDEX ix_realtime_subscription_entity ON realtime.subscription USING btree (entity);


--
-- Name: subscription_subscription_id_entity_filters_key; Type: INDEX; Schema: realtime; Owner: supabase_admin
--

CREATE UNIQUE INDEX subscription_subscription_id_entity_filters_key ON realtime.subscription USING btree (subscription_id, entity, filters);


--
-- Name: bname; Type: INDEX; Schema: storage; Owner: supabase_storage_admin
--

CREATE UNIQUE INDEX bname ON storage.buckets USING btree (name);


--
-- Name: bucketid_objname; Type: INDEX; Schema: storage; Owner: supabase_storage_admin
--

CREATE UNIQUE INDEX bucketid_objname ON storage.objects USING btree (bucket_id, name);


--
-- Name: idx_multipart_uploads_list; Type: INDEX; Schema: storage; Owner: supabase_storage_admin
--

CREATE INDEX idx_multipart_uploads_list ON storage.s3_multipart_uploads USING btree (bucket_id, key, created_at);


--
-- Name: idx_name_bucket_level_unique; Type: INDEX; Schema: storage; Owner: supabase_storage_admin
--

CREATE UNIQUE INDEX idx_name_bucket_level_unique ON storage.objects USING btree (name COLLATE "C", bucket_id, level);


--
-- Name: idx_objects_bucket_id_name; Type: INDEX; Schema: storage; Owner: supabase_storage_admin
--

CREATE INDEX idx_objects_bucket_id_name ON storage.objects USING btree (bucket_id, name COLLATE "C");


--
-- Name: idx_objects_lower_name; Type: INDEX; Schema: storage; Owner: supabase_storage_admin
--

CREATE INDEX idx_objects_lower_name ON storage.objects USING btree ((path_tokens[level]), lower(name) text_pattern_ops, bucket_id, level);


--
-- Name: idx_prefixes_lower_name; Type: INDEX; Schema: storage; Owner: supabase_storage_admin
--

CREATE INDEX idx_prefixes_lower_name ON storage.prefixes USING btree (bucket_id, level, ((string_to_array(name, '/'::text))[level]), lower(name) text_pattern_ops);


--
-- Name: name_prefix_search; Type: INDEX; Schema: storage; Owner: supabase_storage_admin
--

CREATE INDEX name_prefix_search ON storage.objects USING btree (name text_pattern_ops);


--
-- Name: objects_bucket_id_level_idx; Type: INDEX; Schema: storage; Owner: supabase_storage_admin
--

CREATE UNIQUE INDEX objects_bucket_id_level_idx ON storage.objects USING btree (bucket_id, level, name COLLATE "C");


--
-- Name: supabase_functions_hooks_h_table_id_h_name_idx; Type: INDEX; Schema: supabase_functions; Owner: supabase_functions_admin
--

CREATE INDEX supabase_functions_hooks_h_table_id_h_name_idx ON supabase_functions.hooks USING btree (hook_table_id, hook_name);


--
-- Name: supabase_functions_hooks_request_id_idx; Type: INDEX; Schema: supabase_functions; Owner: supabase_functions_admin
--

CREATE INDEX supabase_functions_hooks_request_id_idx ON supabase_functions.hooks USING btree (request_id);


--
-- Name: messages_2025_08_25_pkey; Type: INDEX ATTACH; Schema: realtime; Owner: supabase_realtime_admin
--

ALTER INDEX realtime.messages_pkey ATTACH PARTITION realtime.messages_2025_08_25_pkey;


--
-- Name: messages_2025_08_26_pkey; Type: INDEX ATTACH; Schema: realtime; Owner: supabase_realtime_admin
--

ALTER INDEX realtime.messages_pkey ATTACH PARTITION realtime.messages_2025_08_26_pkey;


--
-- Name: messages_2025_08_27_pkey; Type: INDEX ATTACH; Schema: realtime; Owner: supabase_realtime_admin
--

ALTER INDEX realtime.messages_pkey ATTACH PARTITION realtime.messages_2025_08_27_pkey;


--
-- Name: messages_2025_08_28_pkey; Type: INDEX ATTACH; Schema: realtime; Owner: supabase_realtime_admin
--

ALTER INDEX realtime.messages_pkey ATTACH PARTITION realtime.messages_2025_08_28_pkey;


--
-- Name: messages_2025_08_29_pkey; Type: INDEX ATTACH; Schema: realtime; Owner: supabase_realtime_admin
--

ALTER INDEX realtime.messages_pkey ATTACH PARTITION realtime.messages_2025_08_29_pkey;


--
-- Name: users on_auth_user_created; Type: TRIGGER; Schema: auth; Owner: supabase_auth_admin
--

CREATE TRIGGER on_auth_user_created AFTER INSERT ON auth.users FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();


--
-- Name: users trigger_send_welcome_email; Type: TRIGGER; Schema: auth; Owner: supabase_auth_admin
--

CREATE TRIGGER trigger_send_welcome_email AFTER INSERT ON auth.users FOR EACH ROW EXECUTE FUNCTION public.send_welcome_email();


--
-- Name: credits apply_negative_balances_trigger; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER apply_negative_balances_trigger BEFORE UPDATE OF balance ON public.credits FOR EACH ROW WHEN ((new.balance > old.balance)) EXECUTE FUNCTION public.apply_negative_balances();


--
-- Name: api_keys enforce_api_key_limit; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER enforce_api_key_limit BEFORE INSERT ON public.api_keys FOR EACH ROW EXECUTE FUNCTION public.check_api_key_limit();


--
-- Name: quotas quotas_after_update; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER quotas_after_update AFTER UPDATE ON public.quotas FOR EACH ROW EXECUTE FUNCTION public.check_quota_usage();


--
-- Name: subscription tr_check_filters; Type: TRIGGER; Schema: realtime; Owner: supabase_admin
--

CREATE TRIGGER tr_check_filters BEFORE INSERT OR UPDATE ON realtime.subscription FOR EACH ROW EXECUTE FUNCTION realtime.subscription_check_filters();


--
-- Name: objects objects_delete_delete_prefix; Type: TRIGGER; Schema: storage; Owner: supabase_storage_admin
--

CREATE TRIGGER objects_delete_delete_prefix AFTER DELETE ON storage.objects FOR EACH ROW EXECUTE FUNCTION storage.delete_prefix_hierarchy_trigger();


--
-- Name: objects objects_insert_create_prefix; Type: TRIGGER; Schema: storage; Owner: supabase_storage_admin
--

CREATE TRIGGER objects_insert_create_prefix BEFORE INSERT ON storage.objects FOR EACH ROW EXECUTE FUNCTION storage.objects_insert_prefix_trigger();


--
-- Name: objects objects_update_create_prefix; Type: TRIGGER; Schema: storage; Owner: supabase_storage_admin
--

CREATE TRIGGER objects_update_create_prefix BEFORE UPDATE ON storage.objects FOR EACH ROW WHEN (((new.name <> old.name) OR (new.bucket_id <> old.bucket_id))) EXECUTE FUNCTION storage.objects_update_prefix_trigger();


--
-- Name: prefixes prefixes_create_hierarchy; Type: TRIGGER; Schema: storage; Owner: supabase_storage_admin
--

CREATE TRIGGER prefixes_create_hierarchy BEFORE INSERT ON storage.prefixes FOR EACH ROW WHEN ((pg_trigger_depth() < 1)) EXECUTE FUNCTION storage.prefixes_insert_trigger();


--
-- Name: prefixes prefixes_delete_hierarchy; Type: TRIGGER; Schema: storage; Owner: supabase_storage_admin
--

CREATE TRIGGER prefixes_delete_hierarchy AFTER DELETE ON storage.prefixes FOR EACH ROW EXECUTE FUNCTION storage.delete_prefix_hierarchy_trigger();


--
-- Name: objects update_objects_updated_at; Type: TRIGGER; Schema: storage; Owner: supabase_storage_admin
--

CREATE TRIGGER update_objects_updated_at BEFORE UPDATE ON storage.objects FOR EACH ROW EXECUTE FUNCTION storage.update_updated_at_column();


--
-- Name: extensions extensions_tenant_external_id_fkey; Type: FK CONSTRAINT; Schema: _realtime; Owner: supabase_admin
--

ALTER TABLE ONLY _realtime.extensions
    ADD CONSTRAINT extensions_tenant_external_id_fkey FOREIGN KEY (tenant_external_id) REFERENCES _realtime.tenants(external_id) ON DELETE CASCADE;


--
-- Name: identities identities_user_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.identities
    ADD CONSTRAINT identities_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: mfa_amr_claims mfa_amr_claims_session_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.mfa_amr_claims
    ADD CONSTRAINT mfa_amr_claims_session_id_fkey FOREIGN KEY (session_id) REFERENCES auth.sessions(id) ON DELETE CASCADE;


--
-- Name: mfa_challenges mfa_challenges_auth_factor_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.mfa_challenges
    ADD CONSTRAINT mfa_challenges_auth_factor_id_fkey FOREIGN KEY (factor_id) REFERENCES auth.mfa_factors(id) ON DELETE CASCADE;


--
-- Name: mfa_factors mfa_factors_user_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.mfa_factors
    ADD CONSTRAINT mfa_factors_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: one_time_tokens one_time_tokens_user_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.one_time_tokens
    ADD CONSTRAINT one_time_tokens_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: refresh_tokens refresh_tokens_session_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.refresh_tokens
    ADD CONSTRAINT refresh_tokens_session_id_fkey FOREIGN KEY (session_id) REFERENCES auth.sessions(id) ON DELETE CASCADE;


--
-- Name: saml_providers saml_providers_sso_provider_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.saml_providers
    ADD CONSTRAINT saml_providers_sso_provider_id_fkey FOREIGN KEY (sso_provider_id) REFERENCES auth.sso_providers(id) ON DELETE CASCADE;


--
-- Name: saml_relay_states saml_relay_states_flow_state_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.saml_relay_states
    ADD CONSTRAINT saml_relay_states_flow_state_id_fkey FOREIGN KEY (flow_state_id) REFERENCES auth.flow_state(id) ON DELETE CASCADE;


--
-- Name: saml_relay_states saml_relay_states_sso_provider_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.saml_relay_states
    ADD CONSTRAINT saml_relay_states_sso_provider_id_fkey FOREIGN KEY (sso_provider_id) REFERENCES auth.sso_providers(id) ON DELETE CASCADE;


--
-- Name: sessions sessions_user_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.sessions
    ADD CONSTRAINT sessions_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: sso_domains sso_domains_sso_provider_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE ONLY auth.sso_domains
    ADD CONSTRAINT sso_domains_sso_provider_id_fkey FOREIGN KEY (sso_provider_id) REFERENCES auth.sso_providers(id) ON DELETE CASCADE;


--
-- Name: article_tags article_tags_article_id_fkey; Type: FK CONSTRAINT; Schema: blog; Owner: supabase_admin
--

ALTER TABLE ONLY blog.article_tags
    ADD CONSTRAINT article_tags_article_id_fkey FOREIGN KEY (article_id) REFERENCES blog.articles(id) ON DELETE CASCADE;


--
-- Name: article_tags article_tags_tag_id_fkey; Type: FK CONSTRAINT; Schema: blog; Owner: supabase_admin
--

ALTER TABLE ONLY blog.article_tags
    ADD CONSTRAINT article_tags_tag_id_fkey FOREIGN KEY (tag_id) REFERENCES blog.tags(id) ON DELETE CASCADE;


--
-- Name: article_views article_views_article_id_fkey; Type: FK CONSTRAINT; Schema: blog; Owner: supabase_admin
--

ALTER TABLE ONLY blog.article_views
    ADD CONSTRAINT article_views_article_id_fkey FOREIGN KEY (article_id) REFERENCES blog.articles(id) ON DELETE CASCADE;


--
-- Name: articles articles_author_id_fkey; Type: FK CONSTRAINT; Schema: blog; Owner: supabase_admin
--

ALTER TABLE ONLY blog.articles
    ADD CONSTRAINT articles_author_id_fkey FOREIGN KEY (author_id) REFERENCES blog.profiles(id) ON DELETE CASCADE;


--
-- Name: profiles profiles_id_fkey; Type: FK CONSTRAINT; Schema: blog; Owner: supabase_admin
--

ALTER TABLE ONLY blog.profiles
    ADD CONSTRAINT profiles_id_fkey FOREIGN KEY (id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: api_keys api_keys_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.api_keys
    ADD CONSTRAINT api_keys_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;


--
-- Name: credits credits_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.credits
    ADD CONSTRAINT credits_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;


--
-- Name: negative_balances negative_balances_usage_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.negative_balances
    ADD CONSTRAINT negative_balances_usage_id_fkey FOREIGN KEY (usage_id) REFERENCES public.usage_history(id) ON DELETE SET NULL;


--
-- Name: negative_balances negative_balances_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.negative_balances
    ADD CONSTRAINT negative_balances_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;


--
-- Name: paddle_customers paddle_customers_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: supabase_admin
--

ALTER TABLE ONLY public.paddle_customers
    ADD CONSTRAINT paddle_customers_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;


--
-- Name: paddle_transactions paddle_transactions_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: supabase_admin
--

ALTER TABLE ONLY public.paddle_transactions
    ADD CONSTRAINT paddle_transactions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;


--
-- Name: payg_usage payg_usage_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.payg_usage
    ADD CONSTRAINT payg_usage_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;


--
-- Name: profiles profiles_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.profiles
    ADD CONSTRAINT profiles_id_fkey FOREIGN KEY (id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: quota_notifications quota_notifications_quota_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: supabase_admin
--

ALTER TABLE ONLY public.quota_notifications
    ADD CONSTRAINT quota_notifications_quota_id_fkey FOREIGN KEY (quota_id) REFERENCES public.quotas(id) ON DELETE CASCADE;


--
-- Name: quota_notifications quota_notifications_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: supabase_admin
--

ALTER TABLE ONLY public.quota_notifications
    ADD CONSTRAINT quota_notifications_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;


--
-- Name: quotas quotas_subscription_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.quotas
    ADD CONSTRAINT quotas_subscription_id_fkey FOREIGN KEY (subscription_id) REFERENCES public.user_subscriptions(id) ON DELETE CASCADE;


--
-- Name: quotas quotas_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.quotas
    ADD CONSTRAINT quotas_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;


--
-- Name: usage_history usage_history_api_key_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.usage_history
    ADD CONSTRAINT usage_history_api_key_id_fkey FOREIGN KEY (api_key_id) REFERENCES public.api_keys(id) ON DELETE SET NULL;


--
-- Name: usage_history usage_history_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.usage_history
    ADD CONSTRAINT usage_history_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;


--
-- Name: user_subscriptions user_subscriptions_plan_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_subscriptions
    ADD CONSTRAINT user_subscriptions_plan_id_fkey FOREIGN KEY (plan_id) REFERENCES public.subscription_plans(id);


--
-- Name: user_subscriptions user_subscriptions_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_subscriptions
    ADD CONSTRAINT user_subscriptions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;


--
-- Name: objects objects_bucketId_fkey; Type: FK CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.objects
    ADD CONSTRAINT "objects_bucketId_fkey" FOREIGN KEY (bucket_id) REFERENCES storage.buckets(id);


--
-- Name: prefixes prefixes_bucketId_fkey; Type: FK CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.prefixes
    ADD CONSTRAINT "prefixes_bucketId_fkey" FOREIGN KEY (bucket_id) REFERENCES storage.buckets(id);


--
-- Name: s3_multipart_uploads s3_multipart_uploads_bucket_id_fkey; Type: FK CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.s3_multipart_uploads
    ADD CONSTRAINT s3_multipart_uploads_bucket_id_fkey FOREIGN KEY (bucket_id) REFERENCES storage.buckets(id);


--
-- Name: s3_multipart_uploads_parts s3_multipart_uploads_parts_bucket_id_fkey; Type: FK CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.s3_multipart_uploads_parts
    ADD CONSTRAINT s3_multipart_uploads_parts_bucket_id_fkey FOREIGN KEY (bucket_id) REFERENCES storage.buckets(id);


--
-- Name: s3_multipart_uploads_parts s3_multipart_uploads_parts_upload_id_fkey; Type: FK CONSTRAINT; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE ONLY storage.s3_multipart_uploads_parts
    ADD CONSTRAINT s3_multipart_uploads_parts_upload_id_fkey FOREIGN KEY (upload_id) REFERENCES storage.s3_multipart_uploads(id) ON DELETE CASCADE;


--
-- Name: audit_log_entries; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.audit_log_entries ENABLE ROW LEVEL SECURITY;

--
-- Name: flow_state; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.flow_state ENABLE ROW LEVEL SECURITY;

--
-- Name: identities; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.identities ENABLE ROW LEVEL SECURITY;

--
-- Name: instances; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.instances ENABLE ROW LEVEL SECURITY;

--
-- Name: mfa_amr_claims; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.mfa_amr_claims ENABLE ROW LEVEL SECURITY;

--
-- Name: mfa_challenges; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.mfa_challenges ENABLE ROW LEVEL SECURITY;

--
-- Name: mfa_factors; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.mfa_factors ENABLE ROW LEVEL SECURITY;

--
-- Name: one_time_tokens; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.one_time_tokens ENABLE ROW LEVEL SECURITY;

--
-- Name: refresh_tokens; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.refresh_tokens ENABLE ROW LEVEL SECURITY;

--
-- Name: saml_providers; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.saml_providers ENABLE ROW LEVEL SECURITY;

--
-- Name: saml_relay_states; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.saml_relay_states ENABLE ROW LEVEL SECURITY;

--
-- Name: schema_migrations; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.schema_migrations ENABLE ROW LEVEL SECURITY;

--
-- Name: sessions; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.sessions ENABLE ROW LEVEL SECURITY;

--
-- Name: sso_domains; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.sso_domains ENABLE ROW LEVEL SECURITY;

--
-- Name: sso_providers; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.sso_providers ENABLE ROW LEVEL SECURITY;

--
-- Name: users; Type: ROW SECURITY; Schema: auth; Owner: supabase_auth_admin
--

ALTER TABLE auth.users ENABLE ROW LEVEL SECURITY;

--
-- Name: tags Admins and editors can manage tags; Type: POLICY; Schema: blog; Owner: supabase_admin
--

CREATE POLICY "Admins and editors can manage tags" ON blog.tags USING ((EXISTS ( SELECT 1
   FROM blog.profiles
  WHERE ((profiles.id = auth.uid()) AND (profiles.role = ANY (ARRAY['admin'::text, 'editor'::text]))))));


--
-- Name: articles Admins can view all articles; Type: POLICY; Schema: blog; Owner: supabase_admin
--

CREATE POLICY "Admins can view all articles" ON blog.articles FOR SELECT USING ((EXISTS ( SELECT 1
   FROM blog.profiles
  WHERE ((profiles.id = auth.uid()) AND (profiles.role = ANY (ARRAY['admin'::text, 'editor'::text]))))));


--
-- Name: article_tags Article tags are viewable by everyone; Type: POLICY; Schema: blog; Owner: supabase_admin
--

CREATE POLICY "Article tags are viewable by everyone" ON blog.article_tags FOR SELECT USING ((EXISTS ( SELECT 1
   FROM blog.articles
  WHERE ((articles.id = article_tags.article_id) AND (articles.status = 'published'::text)))));


--
-- Name: article_views Article views are viewable by everyone; Type: POLICY; Schema: blog; Owner: supabase_admin
--

CREATE POLICY "Article views are viewable by everyone" ON blog.article_views FOR SELECT USING ((EXISTS ( SELECT 1
   FROM blog.articles
  WHERE ((articles.id = article_views.article_id) AND (articles.status = 'published'::text)))));


--
-- Name: articles Authors can create articles; Type: POLICY; Schema: blog; Owner: supabase_admin
--

CREATE POLICY "Authors can create articles" ON blog.articles FOR INSERT WITH CHECK (((auth.uid() = author_id) AND (EXISTS ( SELECT 1
   FROM blog.profiles
  WHERE ((profiles.id = auth.uid()) AND (profiles.role = ANY (ARRAY['admin'::text, 'editor'::text, 'writer'::text, 'author'::text])))))));


--
-- Name: articles Authors can delete their own articles; Type: POLICY; Schema: blog; Owner: supabase_admin
--

CREATE POLICY "Authors can delete their own articles" ON blog.articles FOR DELETE USING (((auth.uid() = author_id) OR (EXISTS ( SELECT 1
   FROM blog.profiles
  WHERE ((profiles.id = auth.uid()) AND (profiles.role = 'admin'::text))))));


--
-- Name: article_tags Authors can manage tags for their articles; Type: POLICY; Schema: blog; Owner: supabase_admin
--

CREATE POLICY "Authors can manage tags for their articles" ON blog.article_tags USING ((EXISTS ( SELECT 1
   FROM blog.articles
  WHERE ((articles.id = article_tags.article_id) AND (articles.author_id = auth.uid())))));


--
-- Name: articles Authors can update their own articles; Type: POLICY; Schema: blog; Owner: supabase_admin
--

CREATE POLICY "Authors can update their own articles" ON blog.articles FOR UPDATE USING ((((auth.uid() = author_id) AND (EXISTS ( SELECT 1
   FROM blog.profiles
  WHERE ((profiles.id = auth.uid()) AND (profiles.role = ANY (ARRAY['writer'::text, 'author'::text])))))) OR (EXISTS ( SELECT 1
   FROM blog.profiles
  WHERE ((profiles.id = auth.uid()) AND (profiles.role = ANY (ARRAY['admin'::text, 'editor'::text])))))));


--
-- Name: articles Authors can view their own articles; Type: POLICY; Schema: blog; Owner: supabase_admin
--

CREATE POLICY "Authors can view their own articles" ON blog.articles FOR SELECT USING ((auth.uid() = author_id));


--
-- Name: newsletter_subscribers Newsletter subscribers are viewable by admins only; Type: POLICY; Schema: blog; Owner: supabase_admin
--

CREATE POLICY "Newsletter subscribers are viewable by admins only" ON blog.newsletter_subscribers FOR SELECT USING ((EXISTS ( SELECT 1
   FROM blog.profiles
  WHERE ((profiles.id = auth.uid()) AND (profiles.role = 'admin'::text)))));


--
-- Name: articles Public can view published articles; Type: POLICY; Schema: blog; Owner: supabase_admin
--

CREATE POLICY "Public can view published articles" ON blog.articles FOR SELECT USING ((status = 'published'::text));


--
-- Name: profiles Public profiles are viewable by everyone; Type: POLICY; Schema: blog; Owner: supabase_admin
--

CREATE POLICY "Public profiles are viewable by everyone" ON blog.profiles FOR SELECT USING (true);


--
-- Name: tags Tags are viewable by everyone; Type: POLICY; Schema: blog; Owner: supabase_admin
--

CREATE POLICY "Tags are viewable by everyone" ON blog.tags FOR SELECT USING (true);


--
-- Name: profiles Users can update their own profile; Type: POLICY; Schema: blog; Owner: supabase_admin
--

CREATE POLICY "Users can update their own profile" ON blog.profiles FOR UPDATE USING ((auth.uid() = id));


--
-- Name: profiles Users can view their own profile; Type: POLICY; Schema: blog; Owner: supabase_admin
--

CREATE POLICY "Users can view their own profile" ON blog.profiles USING ((auth.uid() = id));


--
-- Name: article_tags; Type: ROW SECURITY; Schema: blog; Owner: supabase_admin
--

ALTER TABLE blog.article_tags ENABLE ROW LEVEL SECURITY;

--
-- Name: article_views; Type: ROW SECURITY; Schema: blog; Owner: supabase_admin
--

ALTER TABLE blog.article_views ENABLE ROW LEVEL SECURITY;

--
-- Name: articles; Type: ROW SECURITY; Schema: blog; Owner: supabase_admin
--

ALTER TABLE blog.articles ENABLE ROW LEVEL SECURITY;

--
-- Name: newsletter_subscribers; Type: ROW SECURITY; Schema: blog; Owner: supabase_admin
--

ALTER TABLE blog.newsletter_subscribers ENABLE ROW LEVEL SECURITY;

--
-- Name: profiles; Type: ROW SECURITY; Schema: blog; Owner: supabase_admin
--

ALTER TABLE blog.profiles ENABLE ROW LEVEL SECURITY;

--
-- Name: tags; Type: ROW SECURITY; Schema: blog; Owner: supabase_admin
--

ALTER TABLE blog.tags ENABLE ROW LEVEL SECURITY;

--
-- Name: api_keys API Keys - Delete own; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "API Keys - Delete own" ON public.api_keys FOR DELETE USING (( SELECT (auth.uid() = api_keys.user_id)));


--
-- Name: api_keys API Keys - No direct inserts; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "API Keys - No direct inserts" ON public.api_keys FOR INSERT WITH CHECK (false);


--
-- Name: api_keys API Keys - No direct updates; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "API Keys - No direct updates" ON public.api_keys FOR UPDATE USING (false);


--
-- Name: api_keys API Keys - Select own; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "API Keys - Select own" ON public.api_keys FOR SELECT USING ((( SELECT auth.uid() AS uid) = user_id));


--
-- Name: models Allow public viewing of models; Type: POLICY; Schema: public; Owner: supabase_admin
--

CREATE POLICY "Allow public viewing of models" ON public.models FOR SELECT USING (true);


--
-- Name: credits Credits - Select own; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "Credits - Select own" ON public.credits FOR SELECT USING ((( SELECT auth.uid() AS uid) = user_id));


--
-- Name: negative_balances Negative Balances - Select own; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "Negative Balances - Select own" ON public.negative_balances FOR SELECT USING (( SELECT (auth.uid() = negative_balances.user_id)));


--
-- Name: payg_usage PAYG - Select own; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "PAYG - Select own" ON public.payg_usage FOR SELECT USING ((( SELECT auth.uid() AS uid) = user_id));


--
-- Name: pricing_models Pricing Models - No delete; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "Pricing Models - No delete" ON public.pricing_models FOR DELETE USING (false);


--
-- Name: pricing_models Pricing Models - No insert; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "Pricing Models - No insert" ON public.pricing_models FOR INSERT WITH CHECK (false);


--
-- Name: pricing_models Pricing Models - No update; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "Pricing Models - No update" ON public.pricing_models FOR UPDATE USING (false);


--
-- Name: pricing_models Pricing Models - Public read; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "Pricing Models - Public read" ON public.pricing_models FOR SELECT USING (true);


--
-- Name: profiles Profiles - Select own; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "Profiles - Select own" ON public.profiles FOR SELECT USING (( SELECT (auth.uid() = profiles.id)));


--
-- Name: profiles Profiles - Update own; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "Profiles - Update own" ON public.profiles FOR UPDATE USING (( SELECT (auth.uid() = profiles.id)));


--
-- Name: quotas Quotas - Select own; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "Quotas - Select own" ON public.quotas FOR SELECT USING (( SELECT (auth.uid() = quotas.user_id)));


--
-- Name: service_pricing Service Pricing - No delete; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "Service Pricing - No delete" ON public.service_pricing FOR DELETE USING (false);


--
-- Name: service_pricing Service Pricing - No insert; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "Service Pricing - No insert" ON public.service_pricing FOR INSERT WITH CHECK (false);


--
-- Name: service_pricing Service Pricing - No update; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "Service Pricing - No update" ON public.service_pricing FOR UPDATE USING (false);


--
-- Name: service_pricing Service Pricing - Public read; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "Service Pricing - Public read" ON public.service_pricing FOR SELECT USING (true);


--
-- Name: subscription_plans Subscription Plans - No delete; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "Subscription Plans - No delete" ON public.subscription_plans FOR DELETE USING (false);


--
-- Name: subscription_plans Subscription Plans - No insert; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "Subscription Plans - No insert" ON public.subscription_plans FOR INSERT WITH CHECK (false);


--
-- Name: subscription_plans Subscription Plans - No update; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "Subscription Plans - No update" ON public.subscription_plans FOR UPDATE USING (false);


--
-- Name: subscription_plans Subscription Plans - Public read; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "Subscription Plans - Public read" ON public.subscription_plans FOR SELECT USING (true);


--
-- Name: user_subscriptions Subscriptions - Select own; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "Subscriptions - Select own" ON public.user_subscriptions FOR SELECT USING (( SELECT (auth.uid() = user_subscriptions.user_id)));


--
-- Name: usage_history Usage History - Select own; Type: POLICY; Schema: public; Owner: postgres
--

CREATE POLICY "Usage History - Select own" ON public.usage_history FOR SELECT USING (( SELECT (auth.uid() = usage_history.user_id)));


--
-- Name: paddle_customers Users can view their own paddle customer record; Type: POLICY; Schema: public; Owner: supabase_admin
--

CREATE POLICY "Users can view their own paddle customer record" ON public.paddle_customers FOR SELECT TO authenticated USING (( SELECT (auth.uid() = paddle_customers.user_id)));


--
-- Name: POLICY "Users can view their own paddle customer record" ON paddle_customers; Type: COMMENT; Schema: public; Owner: supabase_admin
--

COMMENT ON POLICY "Users can view their own paddle customer record" ON public.paddle_customers IS 'Allow authenticated users to read their own paddle customer record based on user_id';


--
-- Name: api_keys; Type: ROW SECURITY; Schema: public; Owner: postgres
--

ALTER TABLE public.api_keys ENABLE ROW LEVEL SECURITY;

--
-- Name: credits; Type: ROW SECURITY; Schema: public; Owner: postgres
--

ALTER TABLE public.credits ENABLE ROW LEVEL SECURITY;

--
-- Name: paddle_transactions deny_all_access; Type: POLICY; Schema: public; Owner: supabase_admin
--

CREATE POLICY deny_all_access ON public.paddle_transactions USING (false);


--
-- Name: models; Type: ROW SECURITY; Schema: public; Owner: supabase_admin
--

ALTER TABLE public.models ENABLE ROW LEVEL SECURITY;

--
-- Name: negative_balances; Type: ROW SECURITY; Schema: public; Owner: postgres
--

ALTER TABLE public.negative_balances ENABLE ROW LEVEL SECURITY;

--
-- Name: paddle_customers; Type: ROW SECURITY; Schema: public; Owner: supabase_admin
--

ALTER TABLE public.paddle_customers ENABLE ROW LEVEL SECURITY;

--
-- Name: paddle_transactions; Type: ROW SECURITY; Schema: public; Owner: supabase_admin
--

ALTER TABLE public.paddle_transactions ENABLE ROW LEVEL SECURITY;

--
-- Name: payg_usage; Type: ROW SECURITY; Schema: public; Owner: postgres
--

ALTER TABLE public.payg_usage ENABLE ROW LEVEL SECURITY;

--
-- Name: pricing_models; Type: ROW SECURITY; Schema: public; Owner: postgres
--

ALTER TABLE public.pricing_models ENABLE ROW LEVEL SECURITY;

--
-- Name: profiles; Type: ROW SECURITY; Schema: public; Owner: postgres
--

ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

--
-- Name: quota_notifications; Type: ROW SECURITY; Schema: public; Owner: supabase_admin
--

ALTER TABLE public.quota_notifications ENABLE ROW LEVEL SECURITY;

--
-- Name: quotas; Type: ROW SECURITY; Schema: public; Owner: postgres
--

ALTER TABLE public.quotas ENABLE ROW LEVEL SECURITY;

--
-- Name: service_pricing; Type: ROW SECURITY; Schema: public; Owner: postgres
--

ALTER TABLE public.service_pricing ENABLE ROW LEVEL SECURITY;

--
-- Name: subscription_plans; Type: ROW SECURITY; Schema: public; Owner: postgres
--

ALTER TABLE public.subscription_plans ENABLE ROW LEVEL SECURITY;

--
-- Name: usage_history; Type: ROW SECURITY; Schema: public; Owner: postgres
--

ALTER TABLE public.usage_history ENABLE ROW LEVEL SECURITY;

--
-- Name: user_subscriptions; Type: ROW SECURITY; Schema: public; Owner: postgres
--

ALTER TABLE public.user_subscriptions ENABLE ROW LEVEL SECURITY;

--
-- Name: messages; Type: ROW SECURITY; Schema: realtime; Owner: supabase_realtime_admin
--

ALTER TABLE realtime.messages ENABLE ROW LEVEL SECURITY;

--
-- Name: buckets; Type: ROW SECURITY; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE storage.buckets ENABLE ROW LEVEL SECURITY;

--
-- Name: migrations; Type: ROW SECURITY; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE storage.migrations ENABLE ROW LEVEL SECURITY;

--
-- Name: objects; Type: ROW SECURITY; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

--
-- Name: prefixes; Type: ROW SECURITY; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE storage.prefixes ENABLE ROW LEVEL SECURITY;

--
-- Name: s3_multipart_uploads; Type: ROW SECURITY; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE storage.s3_multipart_uploads ENABLE ROW LEVEL SECURITY;

--
-- Name: s3_multipart_uploads_parts; Type: ROW SECURITY; Schema: storage; Owner: supabase_storage_admin
--

ALTER TABLE storage.s3_multipart_uploads_parts ENABLE ROW LEVEL SECURITY;

--
-- Name: supabase_realtime; Type: PUBLICATION; Schema: -; Owner: postgres
--

CREATE PUBLICATION supabase_realtime WITH (publish = 'insert, update, delete, truncate');


ALTER PUBLICATION supabase_realtime OWNER TO postgres;

--
-- Name: supabase_realtime_messages_publication; Type: PUBLICATION; Schema: -; Owner: supabase_admin
--

CREATE PUBLICATION supabase_realtime_messages_publication WITH (publish = 'insert, update, delete, truncate');


ALTER PUBLICATION supabase_realtime_messages_publication OWNER TO supabase_admin;

--
-- Name: supabase_realtime_messages_publication messages; Type: PUBLICATION TABLE; Schema: realtime; Owner: supabase_admin
--

ALTER PUBLICATION supabase_realtime_messages_publication ADD TABLE ONLY realtime.messages;


--
-- Name: SCHEMA auth; Type: ACL; Schema: -; Owner: supabase_admin
--

GRANT USAGE ON SCHEMA auth TO anon;
GRANT USAGE ON SCHEMA auth TO authenticated;
GRANT USAGE ON SCHEMA auth TO service_role;
GRANT ALL ON SCHEMA auth TO supabase_auth_admin;
GRANT ALL ON SCHEMA auth TO dashboard_user;
GRANT ALL ON SCHEMA auth TO postgres;


--
-- Name: SCHEMA blog; Type: ACL; Schema: -; Owner: supabase_admin
--

GRANT USAGE ON SCHEMA blog TO anon;
GRANT USAGE ON SCHEMA blog TO authenticated;


--
-- Name: SCHEMA cron; Type: ACL; Schema: -; Owner: supabase_admin
--

GRANT USAGE ON SCHEMA cron TO postgres WITH GRANT OPTION;


--
-- Name: SCHEMA extensions; Type: ACL; Schema: -; Owner: postgres
--

GRANT USAGE ON SCHEMA extensions TO anon;
GRANT USAGE ON SCHEMA extensions TO authenticated;
GRANT USAGE ON SCHEMA extensions TO service_role;
GRANT ALL ON SCHEMA extensions TO dashboard_user;


--
-- Name: SCHEMA internal; Type: ACL; Schema: -; Owner: postgres
--

GRANT USAGE ON SCHEMA internal TO service_role;


--
-- Name: SCHEMA net; Type: ACL; Schema: -; Owner: supabase_admin
--

GRANT USAGE ON SCHEMA net TO supabase_functions_admin;
GRANT USAGE ON SCHEMA net TO postgres;
GRANT USAGE ON SCHEMA net TO anon;
GRANT USAGE ON SCHEMA net TO authenticated;
GRANT USAGE ON SCHEMA net TO service_role;


--
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: pg_database_owner
--

GRANT USAGE ON SCHEMA public TO postgres;
GRANT USAGE ON SCHEMA public TO anon;
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT USAGE ON SCHEMA public TO service_role;


--
-- Name: SCHEMA realtime; Type: ACL; Schema: -; Owner: supabase_admin
--

GRANT USAGE ON SCHEMA realtime TO postgres;
GRANT USAGE ON SCHEMA realtime TO anon;
GRANT USAGE ON SCHEMA realtime TO authenticated;
GRANT USAGE ON SCHEMA realtime TO service_role;
GRANT ALL ON SCHEMA realtime TO supabase_realtime_admin;


--
-- Name: SCHEMA storage; Type: ACL; Schema: -; Owner: supabase_admin
--

GRANT ALL ON SCHEMA storage TO postgres;
GRANT USAGE ON SCHEMA storage TO anon;
GRANT USAGE ON SCHEMA storage TO authenticated;
GRANT USAGE ON SCHEMA storage TO service_role;
GRANT ALL ON SCHEMA storage TO supabase_storage_admin;
GRANT ALL ON SCHEMA storage TO dashboard_user;


--
-- Name: SCHEMA supabase_functions; Type: ACL; Schema: -; Owner: supabase_admin
--

GRANT USAGE ON SCHEMA supabase_functions TO postgres;
GRANT USAGE ON SCHEMA supabase_functions TO anon;
GRANT USAGE ON SCHEMA supabase_functions TO authenticated;
GRANT USAGE ON SCHEMA supabase_functions TO service_role;
GRANT ALL ON SCHEMA supabase_functions TO supabase_functions_admin;


--
-- Name: SCHEMA vault; Type: ACL; Schema: -; Owner: supabase_admin
--

GRANT USAGE ON SCHEMA vault TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION email(); Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON FUNCTION auth.email() TO dashboard_user;


--
-- Name: FUNCTION jwt(); Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON FUNCTION auth.jwt() TO postgres;
GRANT ALL ON FUNCTION auth.jwt() TO dashboard_user;


--
-- Name: FUNCTION role(); Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON FUNCTION auth.role() TO dashboard_user;


--
-- Name: FUNCTION uid(); Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON FUNCTION auth.uid() TO dashboard_user;


--
-- Name: FUNCTION get_published_articles(limit_count integer, offset_count integer, order_by text); Type: ACL; Schema: blog; Owner: supabase_admin
--

GRANT ALL ON FUNCTION blog.get_published_articles(limit_count integer, offset_count integer, order_by text) TO anon;


--
-- Name: FUNCTION alter_job(job_id bigint, schedule text, command text, database text, username text, active boolean); Type: ACL; Schema: cron; Owner: supabase_admin
--

GRANT ALL ON FUNCTION cron.alter_job(job_id bigint, schedule text, command text, database text, username text, active boolean) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION job_cache_invalidate(); Type: ACL; Schema: cron; Owner: supabase_admin
--

GRANT ALL ON FUNCTION cron.job_cache_invalidate() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION schedule(schedule text, command text); Type: ACL; Schema: cron; Owner: supabase_admin
--

GRANT ALL ON FUNCTION cron.schedule(schedule text, command text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION schedule(job_name text, schedule text, command text); Type: ACL; Schema: cron; Owner: supabase_admin
--

GRANT ALL ON FUNCTION cron.schedule(job_name text, schedule text, command text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION schedule_in_database(job_name text, schedule text, command text, database text, username text, active boolean); Type: ACL; Schema: cron; Owner: supabase_admin
--

GRANT ALL ON FUNCTION cron.schedule_in_database(job_name text, schedule text, command text, database text, username text, active boolean) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION unschedule(job_id bigint); Type: ACL; Schema: cron; Owner: supabase_admin
--

GRANT ALL ON FUNCTION cron.unschedule(job_id bigint) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION unschedule(job_name text); Type: ACL; Schema: cron; Owner: supabase_admin
--

GRANT ALL ON FUNCTION cron.unschedule(job_name text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION airtable_fdw_handler(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.airtable_fdw_handler() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION airtable_fdw_meta(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.airtable_fdw_meta() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION airtable_fdw_validator(options text[], catalog oid); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.airtable_fdw_validator(options text[], catalog oid) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION algorithm_sign(signables text, secret text, algorithm text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.algorithm_sign(signables text, secret text, algorithm text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.algorithm_sign(signables text, secret text, algorithm text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION armor(bytea); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.armor(bytea) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.armor(bytea) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION armor(bytea, text[], text[]); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.armor(bytea, text[], text[]) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.armor(bytea, text[], text[]) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION auth0_fdw_handler(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.auth0_fdw_handler() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION auth0_fdw_meta(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.auth0_fdw_meta() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION auth0_fdw_validator(options text[], catalog oid); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.auth0_fdw_validator(options text[], catalog oid) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION big_query_fdw_handler(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.big_query_fdw_handler() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION big_query_fdw_meta(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.big_query_fdw_meta() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION big_query_fdw_validator(options text[], catalog oid); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.big_query_fdw_validator(options text[], catalog oid) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION click_house_fdw_handler(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.click_house_fdw_handler() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION click_house_fdw_meta(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.click_house_fdw_meta() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION click_house_fdw_validator(options text[], catalog oid); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.click_house_fdw_validator(options text[], catalog oid) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION cognito_fdw_handler(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.cognito_fdw_handler() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION cognito_fdw_meta(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.cognito_fdw_meta() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION cognito_fdw_validator(options text[], catalog oid); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.cognito_fdw_validator(options text[], catalog oid) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION crypt(text, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.crypt(text, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.crypt(text, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION dearmor(text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.dearmor(text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.dearmor(text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION decrypt(bytea, bytea, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.decrypt(bytea, bytea, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.decrypt(bytea, bytea, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION decrypt_iv(bytea, bytea, bytea, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.decrypt_iv(bytea, bytea, bytea, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.decrypt_iv(bytea, bytea, bytea, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION digest(bytea, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.digest(bytea, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.digest(bytea, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION digest(text, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.digest(text, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.digest(text, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION encrypt(bytea, bytea, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.encrypt(bytea, bytea, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.encrypt(bytea, bytea, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION encrypt_iv(bytea, bytea, bytea, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.encrypt_iv(bytea, bytea, bytea, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.encrypt_iv(bytea, bytea, bytea, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION firebase_fdw_handler(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.firebase_fdw_handler() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION firebase_fdw_meta(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.firebase_fdw_meta() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION firebase_fdw_validator(options text[], catalog oid); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.firebase_fdw_validator(options text[], catalog oid) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION gen_random_bytes(integer); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.gen_random_bytes(integer) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.gen_random_bytes(integer) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION gen_random_uuid(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.gen_random_uuid() TO dashboard_user;
GRANT ALL ON FUNCTION extensions.gen_random_uuid() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION gen_salt(text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.gen_salt(text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.gen_salt(text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION gen_salt(text, integer); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.gen_salt(text, integer) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.gen_salt(text, integer) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION grant_pg_cron_access(); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.grant_pg_cron_access() FROM postgres;
GRANT ALL ON FUNCTION extensions.grant_pg_cron_access() TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.grant_pg_cron_access() TO dashboard_user;


--
-- Name: FUNCTION grant_pg_graphql_access(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.grant_pg_graphql_access() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION grant_pg_net_access(); Type: ACL; Schema: extensions; Owner: postgres
--

REVOKE ALL ON FUNCTION extensions.grant_pg_net_access() FROM postgres;
GRANT ALL ON FUNCTION extensions.grant_pg_net_access() TO postgres WITH GRANT OPTION;
GRANT ALL ON FUNCTION extensions.grant_pg_net_access() TO dashboard_user;


--
-- Name: FUNCTION hello_world_fdw_handler(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.hello_world_fdw_handler() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION hello_world_fdw_meta(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.hello_world_fdw_meta() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION hello_world_fdw_validator(options text[], catalog oid); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.hello_world_fdw_validator(options text[], catalog oid) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION hmac(bytea, bytea, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.hmac(bytea, bytea, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.hmac(bytea, bytea, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION hmac(text, text, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.hmac(text, text, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.hmac(text, text, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION hypopg(OUT indexname text, OUT indexrelid oid, OUT indrelid oid, OUT innatts integer, OUT indisunique boolean, OUT indkey int2vector, OUT indcollation oidvector, OUT indclass oidvector, OUT indoption oidvector, OUT indexprs pg_node_tree, OUT indpred pg_node_tree, OUT amid oid); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.hypopg(OUT indexname text, OUT indexrelid oid, OUT indrelid oid, OUT innatts integer, OUT indisunique boolean, OUT indkey int2vector, OUT indcollation oidvector, OUT indclass oidvector, OUT indoption oidvector, OUT indexprs pg_node_tree, OUT indpred pg_node_tree, OUT amid oid) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION hypopg_create_index(sql_order text, OUT indexrelid oid, OUT indexname text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.hypopg_create_index(sql_order text, OUT indexrelid oid, OUT indexname text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION hypopg_drop_index(indexid oid); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.hypopg_drop_index(indexid oid) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION hypopg_get_indexdef(indexid oid); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.hypopg_get_indexdef(indexid oid) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION hypopg_hidden_indexes(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.hypopg_hidden_indexes() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION hypopg_hide_index(indexid oid); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.hypopg_hide_index(indexid oid) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION hypopg_relation_size(indexid oid); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.hypopg_relation_size(indexid oid) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION hypopg_reset(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.hypopg_reset() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION hypopg_reset_index(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.hypopg_reset_index() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION hypopg_unhide_all_indexes(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.hypopg_unhide_all_indexes() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION hypopg_unhide_index(indexid oid); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.hypopg_unhide_index(indexid oid) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION index_advisor(query text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.index_advisor(query text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION logflare_fdw_handler(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.logflare_fdw_handler() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION logflare_fdw_meta(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.logflare_fdw_meta() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION logflare_fdw_validator(options text[], catalog oid); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.logflare_fdw_validator(options text[], catalog oid) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION mssql_fdw_handler(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.mssql_fdw_handler() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION mssql_fdw_meta(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.mssql_fdw_meta() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION mssql_fdw_validator(options text[], catalog oid); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.mssql_fdw_validator(options text[], catalog oid) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pg_stat_statements(showtext boolean, OUT userid oid, OUT dbid oid, OUT toplevel boolean, OUT queryid bigint, OUT query text, OUT plans bigint, OUT total_plan_time double precision, OUT min_plan_time double precision, OUT max_plan_time double precision, OUT mean_plan_time double precision, OUT stddev_plan_time double precision, OUT calls bigint, OUT total_exec_time double precision, OUT min_exec_time double precision, OUT max_exec_time double precision, OUT mean_exec_time double precision, OUT stddev_exec_time double precision, OUT rows bigint, OUT shared_blks_hit bigint, OUT shared_blks_read bigint, OUT shared_blks_dirtied bigint, OUT shared_blks_written bigint, OUT local_blks_hit bigint, OUT local_blks_read bigint, OUT local_blks_dirtied bigint, OUT local_blks_written bigint, OUT temp_blks_read bigint, OUT temp_blks_written bigint, OUT blk_read_time double precision, OUT blk_write_time double precision, OUT temp_blk_read_time double precision, OUT temp_blk_write_time double precision, OUT wal_records bigint, OUT wal_fpi bigint, OUT wal_bytes numeric, OUT jit_functions bigint, OUT jit_generation_time double precision, OUT jit_inlining_count bigint, OUT jit_inlining_time double precision, OUT jit_optimization_count bigint, OUT jit_optimization_time double precision, OUT jit_emission_count bigint, OUT jit_emission_time double precision); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pg_stat_statements(showtext boolean, OUT userid oid, OUT dbid oid, OUT toplevel boolean, OUT queryid bigint, OUT query text, OUT plans bigint, OUT total_plan_time double precision, OUT min_plan_time double precision, OUT max_plan_time double precision, OUT mean_plan_time double precision, OUT stddev_plan_time double precision, OUT calls bigint, OUT total_exec_time double precision, OUT min_exec_time double precision, OUT max_exec_time double precision, OUT mean_exec_time double precision, OUT stddev_exec_time double precision, OUT rows bigint, OUT shared_blks_hit bigint, OUT shared_blks_read bigint, OUT shared_blks_dirtied bigint, OUT shared_blks_written bigint, OUT local_blks_hit bigint, OUT local_blks_read bigint, OUT local_blks_dirtied bigint, OUT local_blks_written bigint, OUT temp_blks_read bigint, OUT temp_blks_written bigint, OUT blk_read_time double precision, OUT blk_write_time double precision, OUT temp_blk_read_time double precision, OUT temp_blk_write_time double precision, OUT wal_records bigint, OUT wal_fpi bigint, OUT wal_bytes numeric, OUT jit_functions bigint, OUT jit_generation_time double precision, OUT jit_inlining_count bigint, OUT jit_inlining_time double precision, OUT jit_optimization_count bigint, OUT jit_optimization_time double precision, OUT jit_emission_count bigint, OUT jit_emission_time double precision) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pg_stat_statements_info(OUT dealloc bigint, OUT stats_reset timestamp with time zone); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pg_stat_statements_info(OUT dealloc bigint, OUT stats_reset timestamp with time zone) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pg_stat_statements_reset(userid oid, dbid oid, queryid bigint); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pg_stat_statements_reset(userid oid, dbid oid, queryid bigint) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_armor_headers(text, OUT key text, OUT value text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_armor_headers(text, OUT key text, OUT value text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_armor_headers(text, OUT key text, OUT value text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_key_id(bytea); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_key_id(bytea) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_key_id(bytea) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_pub_decrypt(bytea, bytea); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt(bytea, bytea) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt(bytea, bytea) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_pub_decrypt(bytea, bytea, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt(bytea, bytea, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt(bytea, bytea, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_pub_decrypt(bytea, bytea, text, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt(bytea, bytea, text, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt(bytea, bytea, text, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_pub_decrypt_bytea(bytea, bytea); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt_bytea(bytea, bytea) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt_bytea(bytea, bytea) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_pub_decrypt_bytea(bytea, bytea, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt_bytea(bytea, bytea, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt_bytea(bytea, bytea, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_pub_decrypt_bytea(bytea, bytea, text, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt_bytea(bytea, bytea, text, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_pub_decrypt_bytea(bytea, bytea, text, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_pub_encrypt(text, bytea); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_pub_encrypt(text, bytea) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_pub_encrypt(text, bytea) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_pub_encrypt(text, bytea, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_pub_encrypt(text, bytea, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_pub_encrypt(text, bytea, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_pub_encrypt_bytea(bytea, bytea); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_pub_encrypt_bytea(bytea, bytea) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_pub_encrypt_bytea(bytea, bytea) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_pub_encrypt_bytea(bytea, bytea, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_pub_encrypt_bytea(bytea, bytea, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_pub_encrypt_bytea(bytea, bytea, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_sym_decrypt(bytea, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_sym_decrypt(bytea, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_sym_decrypt(bytea, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_sym_decrypt(bytea, text, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_sym_decrypt(bytea, text, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_sym_decrypt(bytea, text, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_sym_decrypt_bytea(bytea, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_sym_decrypt_bytea(bytea, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_sym_decrypt_bytea(bytea, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_sym_decrypt_bytea(bytea, text, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_sym_decrypt_bytea(bytea, text, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_sym_decrypt_bytea(bytea, text, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_sym_encrypt(text, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_sym_encrypt(text, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_sym_encrypt(text, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_sym_encrypt(text, text, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_sym_encrypt(text, text, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_sym_encrypt(text, text, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_sym_encrypt_bytea(bytea, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_sym_encrypt_bytea(bytea, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_sym_encrypt_bytea(bytea, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgp_sym_encrypt_bytea(bytea, text, text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgp_sym_encrypt_bytea(bytea, text, text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.pgp_sym_encrypt_bytea(bytea, text, text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgrst_ddl_watch(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgrst_ddl_watch() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION pgrst_drop_watch(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.pgrst_drop_watch() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION redis_fdw_handler(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.redis_fdw_handler() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION redis_fdw_meta(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.redis_fdw_meta() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION redis_fdw_validator(options text[], catalog oid); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.redis_fdw_validator(options text[], catalog oid) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION s3_fdw_handler(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.s3_fdw_handler() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION s3_fdw_meta(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.s3_fdw_meta() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION s3_fdw_validator(options text[], catalog oid); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.s3_fdw_validator(options text[], catalog oid) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION set_graphql_placeholder(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.set_graphql_placeholder() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION sign(payload json, secret text, algorithm text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.sign(payload json, secret text, algorithm text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.sign(payload json, secret text, algorithm text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION stripe_fdw_handler(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.stripe_fdw_handler() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION stripe_fdw_meta(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.stripe_fdw_meta() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION stripe_fdw_validator(options text[], catalog oid); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.stripe_fdw_validator(options text[], catalog oid) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION try_cast_double(inp text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.try_cast_double(inp text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.try_cast_double(inp text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION url_decode(data text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.url_decode(data text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.url_decode(data text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION url_encode(data bytea); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.url_encode(data bytea) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.url_encode(data bytea) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION uuid_generate_v1(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.uuid_generate_v1() TO dashboard_user;
GRANT ALL ON FUNCTION extensions.uuid_generate_v1() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION uuid_generate_v1mc(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.uuid_generate_v1mc() TO dashboard_user;
GRANT ALL ON FUNCTION extensions.uuid_generate_v1mc() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION uuid_generate_v3(namespace uuid, name text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.uuid_generate_v3(namespace uuid, name text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.uuid_generate_v3(namespace uuid, name text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION uuid_generate_v4(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.uuid_generate_v4() TO dashboard_user;
GRANT ALL ON FUNCTION extensions.uuid_generate_v4() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION uuid_generate_v5(namespace uuid, name text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.uuid_generate_v5(namespace uuid, name text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.uuid_generate_v5(namespace uuid, name text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION uuid_nil(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.uuid_nil() TO dashboard_user;
GRANT ALL ON FUNCTION extensions.uuid_nil() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION uuid_ns_dns(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.uuid_ns_dns() TO dashboard_user;
GRANT ALL ON FUNCTION extensions.uuid_ns_dns() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION uuid_ns_oid(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.uuid_ns_oid() TO dashboard_user;
GRANT ALL ON FUNCTION extensions.uuid_ns_oid() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION uuid_ns_url(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.uuid_ns_url() TO dashboard_user;
GRANT ALL ON FUNCTION extensions.uuid_ns_url() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION uuid_ns_x500(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.uuid_ns_x500() TO dashboard_user;
GRANT ALL ON FUNCTION extensions.uuid_ns_x500() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION verify(token text, secret text, algorithm text); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.verify(token text, secret text, algorithm text) TO dashboard_user;
GRANT ALL ON FUNCTION extensions.verify(token text, secret text, algorithm text) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION wasm_fdw_handler(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.wasm_fdw_handler() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION wasm_fdw_meta(); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.wasm_fdw_meta() TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION wasm_fdw_validator(options text[], catalog oid); Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON FUNCTION extensions.wasm_fdw_validator(options text[], catalog oid) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION graphql("operationName" text, query text, variables jsonb, extensions jsonb); Type: ACL; Schema: graphql_public; Owner: supabase_admin
--

GRANT ALL ON FUNCTION graphql_public.graphql("operationName" text, query text, variables jsonb, extensions jsonb) TO postgres;
GRANT ALL ON FUNCTION graphql_public.graphql("operationName" text, query text, variables jsonb, extensions jsonb) TO anon;
GRANT ALL ON FUNCTION graphql_public.graphql("operationName" text, query text, variables jsonb, extensions jsonb) TO authenticated;
GRANT ALL ON FUNCTION graphql_public.graphql("operationName" text, query text, variables jsonb, extensions jsonb) TO service_role;


--
-- Name: FUNCTION get_auth(p_usename text); Type: ACL; Schema: pgbouncer; Owner: supabase_admin
--

REVOKE ALL ON FUNCTION pgbouncer.get_auth(p_usename text) FROM PUBLIC;
GRANT ALL ON FUNCTION pgbouncer.get_auth(p_usename text) TO pgbouncer;
GRANT ALL ON FUNCTION pgbouncer.get_auth(p_usename text) TO postgres;


--
-- Name: FUNCTION crypto_aead_det_decrypt(message bytea, additional bytea, key_uuid uuid, nonce bytea); Type: ACL; Schema: pgsodium; Owner: pgsodium_keymaker
--

GRANT ALL ON FUNCTION pgsodium.crypto_aead_det_decrypt(message bytea, additional bytea, key_uuid uuid, nonce bytea) TO service_role;


--
-- Name: FUNCTION crypto_aead_det_encrypt(message bytea, additional bytea, key_uuid uuid, nonce bytea); Type: ACL; Schema: pgsodium; Owner: pgsodium_keymaker
--

GRANT ALL ON FUNCTION pgsodium.crypto_aead_det_encrypt(message bytea, additional bytea, key_uuid uuid, nonce bytea) TO service_role;


--
-- Name: FUNCTION crypto_aead_det_keygen(); Type: ACL; Schema: pgsodium; Owner: supabase_admin
--

GRANT ALL ON FUNCTION pgsodium.crypto_aead_det_keygen() TO service_role;


--
-- Name: FUNCTION apply_negative_balances(); Type: ACL; Schema: public; Owner: postgres
--

REVOKE ALL ON FUNCTION public.apply_negative_balances() FROM PUBLIC;
GRANT ALL ON FUNCTION public.apply_negative_balances() TO anon;
GRANT ALL ON FUNCTION public.apply_negative_balances() TO authenticated;
GRANT ALL ON FUNCTION public.apply_negative_balances() TO service_role;


--
-- Name: FUNCTION check_api_key_limit(); Type: ACL; Schema: public; Owner: postgres
--

REVOKE ALL ON FUNCTION public.check_api_key_limit() FROM PUBLIC;
GRANT ALL ON FUNCTION public.check_api_key_limit() TO anon;
GRANT ALL ON FUNCTION public.check_api_key_limit() TO authenticated;
GRANT ALL ON FUNCTION public.check_api_key_limit() TO service_role;


--
-- Name: FUNCTION check_quota_usage(); Type: ACL; Schema: public; Owner: supabase_admin
--

GRANT ALL ON FUNCTION public.check_quota_usage() TO postgres;
GRANT ALL ON FUNCTION public.check_quota_usage() TO anon;
GRANT ALL ON FUNCTION public.check_quota_usage() TO authenticated;
GRANT ALL ON FUNCTION public.check_quota_usage() TO service_role;


--
-- Name: FUNCTION check_usage_allowance(p_user_id uuid, p_service text, p_model text, p_amount numeric, p_api_key_id uuid, p_is_input_only boolean); Type: ACL; Schema: public; Owner: supabase_admin
--

REVOKE ALL ON FUNCTION public.check_usage_allowance(p_user_id uuid, p_service text, p_model text, p_amount numeric, p_api_key_id uuid, p_is_input_only boolean) FROM PUBLIC;
GRANT ALL ON FUNCTION public.check_usage_allowance(p_user_id uuid, p_service text, p_model text, p_amount numeric, p_api_key_id uuid, p_is_input_only boolean) TO postgres;
GRANT ALL ON FUNCTION public.check_usage_allowance(p_user_id uuid, p_service text, p_model text, p_amount numeric, p_api_key_id uuid, p_is_input_only boolean) TO anon;
GRANT ALL ON FUNCTION public.check_usage_allowance(p_user_id uuid, p_service text, p_model text, p_amount numeric, p_api_key_id uuid, p_is_input_only boolean) TO authenticated;
GRANT ALL ON FUNCTION public.check_usage_allowance(p_user_id uuid, p_service text, p_model text, p_amount numeric, p_api_key_id uuid, p_is_input_only boolean) TO service_role;


--
-- Name: FUNCTION cleanup_cancelled_subscription(p_paddle_subscription_id text); Type: ACL; Schema: public; Owner: supabase_admin
--

REVOKE ALL ON FUNCTION public.cleanup_cancelled_subscription(p_paddle_subscription_id text) FROM PUBLIC;
GRANT ALL ON FUNCTION public.cleanup_cancelled_subscription(p_paddle_subscription_id text) TO postgres;
GRANT ALL ON FUNCTION public.cleanup_cancelled_subscription(p_paddle_subscription_id text) TO anon;
GRANT ALL ON FUNCTION public.cleanup_cancelled_subscription(p_paddle_subscription_id text) TO authenticated;
GRANT ALL ON FUNCTION public.cleanup_cancelled_subscription(p_paddle_subscription_id text) TO service_role;


--
-- Name: FUNCTION create_api_key(p_name text, p_expires_at timestamp with time zone); Type: ACL; Schema: public; Owner: postgres
--

REVOKE ALL ON FUNCTION public.create_api_key(p_name text, p_expires_at timestamp with time zone) FROM PUBLIC;
GRANT ALL ON FUNCTION public.create_api_key(p_name text, p_expires_at timestamp with time zone) TO anon;
GRANT ALL ON FUNCTION public.create_api_key(p_name text, p_expires_at timestamp with time zone) TO authenticated;
GRANT ALL ON FUNCTION public.create_api_key(p_name text, p_expires_at timestamp with time zone) TO service_role;


--
-- Name: FUNCTION finalize_realtime_session(p_user_id uuid, p_api_key_id uuid, p_model text, p_session_id text, p_start_time bigint, p_audio_bytes_sent bigint, p_has_transcription boolean, p_token_info jsonb, p_processed_duration_seconds numeric); Type: ACL; Schema: public; Owner: postgres
--

REVOKE ALL ON FUNCTION public.finalize_realtime_session(p_user_id uuid, p_api_key_id uuid, p_model text, p_session_id text, p_start_time bigint, p_audio_bytes_sent bigint, p_has_transcription boolean, p_token_info jsonb, p_processed_duration_seconds numeric) FROM PUBLIC;
GRANT ALL ON FUNCTION public.finalize_realtime_session(p_user_id uuid, p_api_key_id uuid, p_model text, p_session_id text, p_start_time bigint, p_audio_bytes_sent bigint, p_has_transcription boolean, p_token_info jsonb, p_processed_duration_seconds numeric) TO anon;
GRANT ALL ON FUNCTION public.finalize_realtime_session(p_user_id uuid, p_api_key_id uuid, p_model text, p_session_id text, p_start_time bigint, p_audio_bytes_sent bigint, p_has_transcription boolean, p_token_info jsonb, p_processed_duration_seconds numeric) TO authenticated;
GRANT ALL ON FUNCTION public.finalize_realtime_session(p_user_id uuid, p_api_key_id uuid, p_model text, p_session_id text, p_start_time bigint, p_audio_bytes_sent bigint, p_has_transcription boolean, p_token_info jsonb, p_processed_duration_seconds numeric) TO service_role;


--
-- Name: FUNCTION finalize_usage(p_user_id uuid, p_api_key_id uuid, p_service text, p_model text, p_amount numeric, p_cost numeric, p_pricing_model text, p_metadata jsonb, p_pending_usage_id uuid); Type: ACL; Schema: public; Owner: supabase_admin
--

REVOKE ALL ON FUNCTION public.finalize_usage(p_user_id uuid, p_api_key_id uuid, p_service text, p_model text, p_amount numeric, p_cost numeric, p_pricing_model text, p_metadata jsonb, p_pending_usage_id uuid) FROM PUBLIC;
GRANT ALL ON FUNCTION public.finalize_usage(p_user_id uuid, p_api_key_id uuid, p_service text, p_model text, p_amount numeric, p_cost numeric, p_pricing_model text, p_metadata jsonb, p_pending_usage_id uuid) TO postgres;
GRANT ALL ON FUNCTION public.finalize_usage(p_user_id uuid, p_api_key_id uuid, p_service text, p_model text, p_amount numeric, p_cost numeric, p_pricing_model text, p_metadata jsonb, p_pending_usage_id uuid) TO anon;
GRANT ALL ON FUNCTION public.finalize_usage(p_user_id uuid, p_api_key_id uuid, p_service text, p_model text, p_amount numeric, p_cost numeric, p_pricing_model text, p_metadata jsonb, p_pending_usage_id uuid) TO authenticated;
GRANT ALL ON FUNCTION public.finalize_usage(p_user_id uuid, p_api_key_id uuid, p_service text, p_model text, p_amount numeric, p_cost numeric, p_pricing_model text, p_metadata jsonb, p_pending_usage_id uuid) TO service_role;


--
-- Name: FUNCTION get_or_create_user_credits(p_user_id uuid, p_initial_balance numeric, p_currency text); Type: ACL; Schema: public; Owner: supabase_admin
--

REVOKE ALL ON FUNCTION public.get_or_create_user_credits(p_user_id uuid, p_initial_balance numeric, p_currency text) FROM PUBLIC;
GRANT ALL ON FUNCTION public.get_or_create_user_credits(p_user_id uuid, p_initial_balance numeric, p_currency text) TO postgres;
GRANT ALL ON FUNCTION public.get_or_create_user_credits(p_user_id uuid, p_initial_balance numeric, p_currency text) TO anon;
GRANT ALL ON FUNCTION public.get_or_create_user_credits(p_user_id uuid, p_initial_balance numeric, p_currency text) TO authenticated;
GRANT ALL ON FUNCTION public.get_or_create_user_credits(p_user_id uuid, p_initial_balance numeric, p_currency text) TO service_role;


--
-- Name: FUNCTION get_paddle_customer_by_user_id(p_user_id uuid); Type: ACL; Schema: public; Owner: supabase_admin
--

REVOKE ALL ON FUNCTION public.get_paddle_customer_by_user_id(p_user_id uuid) FROM PUBLIC;
GRANT ALL ON FUNCTION public.get_paddle_customer_by_user_id(p_user_id uuid) TO postgres;
GRANT ALL ON FUNCTION public.get_paddle_customer_by_user_id(p_user_id uuid) TO anon;
GRANT ALL ON FUNCTION public.get_paddle_customer_by_user_id(p_user_id uuid) TO authenticated;
GRANT ALL ON FUNCTION public.get_paddle_customer_by_user_id(p_user_id uuid) TO service_role;


--
-- Name: FUNCTION get_payment_failure_stats(p_subscription_id text, p_days integer); Type: ACL; Schema: public; Owner: supabase_admin
--

REVOKE ALL ON FUNCTION public.get_payment_failure_stats(p_subscription_id text, p_days integer) FROM PUBLIC;
GRANT ALL ON FUNCTION public.get_payment_failure_stats(p_subscription_id text, p_days integer) TO postgres;
GRANT ALL ON FUNCTION public.get_payment_failure_stats(p_subscription_id text, p_days integer) TO anon;
GRANT ALL ON FUNCTION public.get_payment_failure_stats(p_subscription_id text, p_days integer) TO authenticated;
GRANT ALL ON FUNCTION public.get_payment_failure_stats(p_subscription_id text, p_days integer) TO service_role;


--
-- Name: FUNCTION get_unpaid_payg_balances(p_user_id uuid); Type: ACL; Schema: public; Owner: postgres
--

REVOKE ALL ON FUNCTION public.get_unpaid_payg_balances(p_user_id uuid) FROM PUBLIC;
GRANT ALL ON FUNCTION public.get_unpaid_payg_balances(p_user_id uuid) TO anon;
GRANT ALL ON FUNCTION public.get_unpaid_payg_balances(p_user_id uuid) TO authenticated;
GRANT ALL ON FUNCTION public.get_unpaid_payg_balances(p_user_id uuid) TO service_role;


--
-- Name: FUNCTION get_usage_summary_statistics(p_user_id text, p_start_date timestamp with time zone, p_end_date timestamp with time zone, p_service text, p_model text, p_status text); Type: ACL; Schema: public; Owner: postgres
--

REVOKE ALL ON FUNCTION public.get_usage_summary_statistics(p_user_id text, p_start_date timestamp with time zone, p_end_date timestamp with time zone, p_service text, p_model text, p_status text) FROM PUBLIC;
GRANT ALL ON FUNCTION public.get_usage_summary_statistics(p_user_id text, p_start_date timestamp with time zone, p_end_date timestamp with time zone, p_service text, p_model text, p_status text) TO anon;
GRANT ALL ON FUNCTION public.get_usage_summary_statistics(p_user_id text, p_start_date timestamp with time zone, p_end_date timestamp with time zone, p_service text, p_model text, p_status text) TO authenticated;
GRANT ALL ON FUNCTION public.get_usage_summary_statistics(p_user_id text, p_start_date timestamp with time zone, p_end_date timestamp with time zone, p_service text, p_model text, p_status text) TO service_role;


--
-- Name: FUNCTION handle_expired_free_trials(); Type: ACL; Schema: public; Owner: supabase_admin
--

GRANT ALL ON FUNCTION public.handle_expired_free_trials() TO postgres;
GRANT ALL ON FUNCTION public.handle_expired_free_trials() TO anon;
GRANT ALL ON FUNCTION public.handle_expired_free_trials() TO authenticated;
GRANT ALL ON FUNCTION public.handle_expired_free_trials() TO service_role;


--
-- Name: FUNCTION handle_new_user(); Type: ACL; Schema: public; Owner: postgres
--

REVOKE ALL ON FUNCTION public.handle_new_user() FROM PUBLIC;
GRANT ALL ON FUNCTION public.handle_new_user() TO anon;
GRANT ALL ON FUNCTION public.handle_new_user() TO authenticated;
GRANT ALL ON FUNCTION public.handle_new_user() TO service_role;


--
-- Name: FUNCTION handle_subscription_cancellation(p_paddle_subscription_id text, p_user_id uuid); Type: ACL; Schema: public; Owner: supabase_admin
--

GRANT ALL ON FUNCTION public.handle_subscription_cancellation(p_paddle_subscription_id text, p_user_id uuid) TO postgres;
GRANT ALL ON FUNCTION public.handle_subscription_cancellation(p_paddle_subscription_id text, p_user_id uuid) TO anon;
GRANT ALL ON FUNCTION public.handle_subscription_cancellation(p_paddle_subscription_id text, p_user_id uuid) TO authenticated;
GRANT ALL ON FUNCTION public.handle_subscription_cancellation(p_paddle_subscription_id text, p_user_id uuid) TO service_role;


--
-- Name: FUNCTION handle_subscription_transaction(p_user_id uuid, p_paddle_subscription_id text, p_paddle_customer_id text, p_subscription_plan text, p_transaction_data jsonb, p_transcription_quota_override numeric, p_optimization_quota_override numeric, p_upgrade_flow text); Type: ACL; Schema: public; Owner: supabase_admin
--

GRANT ALL ON FUNCTION public.handle_subscription_transaction(p_user_id uuid, p_paddle_subscription_id text, p_paddle_customer_id text, p_subscription_plan text, p_transaction_data jsonb, p_transcription_quota_override numeric, p_optimization_quota_override numeric, p_upgrade_flow text) TO postgres;
GRANT ALL ON FUNCTION public.handle_subscription_transaction(p_user_id uuid, p_paddle_subscription_id text, p_paddle_customer_id text, p_subscription_plan text, p_transaction_data jsonb, p_transcription_quota_override numeric, p_optimization_quota_override numeric, p_upgrade_flow text) TO anon;
GRANT ALL ON FUNCTION public.handle_subscription_transaction(p_user_id uuid, p_paddle_subscription_id text, p_paddle_customer_id text, p_subscription_plan text, p_transaction_data jsonb, p_transcription_quota_override numeric, p_optimization_quota_override numeric, p_upgrade_flow text) TO authenticated;
GRANT ALL ON FUNCTION public.handle_subscription_transaction(p_user_id uuid, p_paddle_subscription_id text, p_paddle_customer_id text, p_subscription_plan text, p_transaction_data jsonb, p_transcription_quota_override numeric, p_optimization_quota_override numeric, p_upgrade_flow text) TO service_role;


--
-- Name: FUNCTION has_unpaid_payg_balance(p_user_id uuid); Type: ACL; Schema: public; Owner: postgres
--

REVOKE ALL ON FUNCTION public.has_unpaid_payg_balance(p_user_id uuid) FROM PUBLIC;
GRANT ALL ON FUNCTION public.has_unpaid_payg_balance(p_user_id uuid) TO anon;
GRANT ALL ON FUNCTION public.has_unpaid_payg_balance(p_user_id uuid) TO authenticated;
GRANT ALL ON FUNCTION public.has_unpaid_payg_balance(p_user_id uuid) TO service_role;


--
-- Name: FUNCTION mark_payg_payment_as_paid(p_payg_id uuid, p_payment_metadata jsonb); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.mark_payg_payment_as_paid(p_payg_id uuid, p_payment_metadata jsonb) TO anon;
GRANT ALL ON FUNCTION public.mark_payg_payment_as_paid(p_payg_id uuid, p_payment_metadata jsonb) TO authenticated;
GRANT ALL ON FUNCTION public.mark_payg_payment_as_paid(p_payg_id uuid, p_payment_metadata jsonb) TO service_role;


--
-- Name: FUNCTION mark_payg_payment_as_unpaid(p_payg_id uuid, p_failure_reason text, p_payment_metadata jsonb); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.mark_payg_payment_as_unpaid(p_payg_id uuid, p_failure_reason text, p_payment_metadata jsonb) TO anon;
GRANT ALL ON FUNCTION public.mark_payg_payment_as_unpaid(p_payg_id uuid, p_failure_reason text, p_payment_metadata jsonb) TO authenticated;
GRANT ALL ON FUNCTION public.mark_payg_payment_as_unpaid(p_payg_id uuid, p_failure_reason text, p_payment_metadata jsonb) TO service_role;


--
-- Name: FUNCTION reset_subscription_quotas(p_subscription_id uuid); Type: ACL; Schema: public; Owner: supabase_admin
--

REVOKE ALL ON FUNCTION public.reset_subscription_quotas(p_subscription_id uuid) FROM PUBLIC;
GRANT ALL ON FUNCTION public.reset_subscription_quotas(p_subscription_id uuid) TO postgres;
GRANT ALL ON FUNCTION public.reset_subscription_quotas(p_subscription_id uuid) TO anon;
GRANT ALL ON FUNCTION public.reset_subscription_quotas(p_subscription_id uuid) TO authenticated;
GRANT ALL ON FUNCTION public.reset_subscription_quotas(p_subscription_id uuid) TO service_role;


--
-- Name: FUNCTION send_welcome_email(); Type: ACL; Schema: public; Owner: postgres
--

REVOKE ALL ON FUNCTION public.send_welcome_email() FROM PUBLIC;
GRANT ALL ON FUNCTION public.send_welcome_email() TO anon;
GRANT ALL ON FUNCTION public.send_welcome_email() TO authenticated;
GRANT ALL ON FUNCTION public.send_welcome_email() TO service_role;


--
-- Name: FUNCTION update_api_key_name(p_api_key_id uuid, p_name text); Type: ACL; Schema: public; Owner: postgres
--

REVOKE ALL ON FUNCTION public.update_api_key_name(p_api_key_id uuid, p_name text) FROM PUBLIC;
GRANT ALL ON FUNCTION public.update_api_key_name(p_api_key_id uuid, p_name text) TO anon;
GRANT ALL ON FUNCTION public.update_api_key_name(p_api_key_id uuid, p_name text) TO authenticated;
GRANT ALL ON FUNCTION public.update_api_key_name(p_api_key_id uuid, p_name text) TO service_role;


--
-- Name: FUNCTION update_api_key_status(p_key_id uuid, p_is_active boolean); Type: ACL; Schema: public; Owner: postgres
--

REVOKE ALL ON FUNCTION public.update_api_key_status(p_key_id uuid, p_is_active boolean) FROM PUBLIC;
GRANT ALL ON FUNCTION public.update_api_key_status(p_key_id uuid, p_is_active boolean) TO anon;
GRANT ALL ON FUNCTION public.update_api_key_status(p_key_id uuid, p_is_active boolean) TO authenticated;
GRANT ALL ON FUNCTION public.update_api_key_status(p_key_id uuid, p_is_active boolean) TO service_role;


--
-- Name: FUNCTION update_updated_at_column(); Type: ACL; Schema: public; Owner: supabase_admin
--

GRANT ALL ON FUNCTION public.update_updated_at_column() TO postgres;
GRANT ALL ON FUNCTION public.update_updated_at_column() TO anon;
GRANT ALL ON FUNCTION public.update_updated_at_column() TO authenticated;
GRANT ALL ON FUNCTION public.update_updated_at_column() TO service_role;


--
-- Name: FUNCTION upsert_paddle_customer(p_user_id uuid, p_paddle_customer_id text, p_email text, p_name text, p_country_code text, p_metadata jsonb); Type: ACL; Schema: public; Owner: supabase_admin
--

REVOKE ALL ON FUNCTION public.upsert_paddle_customer(p_user_id uuid, p_paddle_customer_id text, p_email text, p_name text, p_country_code text, p_metadata jsonb) FROM PUBLIC;
GRANT ALL ON FUNCTION public.upsert_paddle_customer(p_user_id uuid, p_paddle_customer_id text, p_email text, p_name text, p_country_code text, p_metadata jsonb) TO postgres;
GRANT ALL ON FUNCTION public.upsert_paddle_customer(p_user_id uuid, p_paddle_customer_id text, p_email text, p_name text, p_country_code text, p_metadata jsonb) TO anon;
GRANT ALL ON FUNCTION public.upsert_paddle_customer(p_user_id uuid, p_paddle_customer_id text, p_email text, p_name text, p_country_code text, p_metadata jsonb) TO authenticated;
GRANT ALL ON FUNCTION public.upsert_paddle_customer(p_user_id uuid, p_paddle_customer_id text, p_email text, p_name text, p_country_code text, p_metadata jsonb) TO service_role;


--
-- Name: FUNCTION validate_api_key(p_key text); Type: ACL; Schema: public; Owner: postgres
--

REVOKE ALL ON FUNCTION public.validate_api_key(p_key text) FROM PUBLIC;
GRANT ALL ON FUNCTION public.validate_api_key(p_key text) TO anon;
GRANT ALL ON FUNCTION public.validate_api_key(p_key text) TO authenticated;
GRANT ALL ON FUNCTION public.validate_api_key(p_key text) TO service_role;


--
-- Name: FUNCTION apply_rls(wal jsonb, max_record_bytes integer); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.apply_rls(wal jsonb, max_record_bytes integer) TO postgres;
GRANT ALL ON FUNCTION realtime.apply_rls(wal jsonb, max_record_bytes integer) TO dashboard_user;
GRANT ALL ON FUNCTION realtime.apply_rls(wal jsonb, max_record_bytes integer) TO anon;
GRANT ALL ON FUNCTION realtime.apply_rls(wal jsonb, max_record_bytes integer) TO authenticated;
GRANT ALL ON FUNCTION realtime.apply_rls(wal jsonb, max_record_bytes integer) TO service_role;
GRANT ALL ON FUNCTION realtime.apply_rls(wal jsonb, max_record_bytes integer) TO supabase_realtime_admin;


--
-- Name: FUNCTION broadcast_changes(topic_name text, event_name text, operation text, table_name text, table_schema text, new record, old record, level text); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.broadcast_changes(topic_name text, event_name text, operation text, table_name text, table_schema text, new record, old record, level text) TO postgres;
GRANT ALL ON FUNCTION realtime.broadcast_changes(topic_name text, event_name text, operation text, table_name text, table_schema text, new record, old record, level text) TO dashboard_user;


--
-- Name: FUNCTION build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]) TO postgres;
GRANT ALL ON FUNCTION realtime.build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]) TO dashboard_user;
GRANT ALL ON FUNCTION realtime.build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]) TO anon;
GRANT ALL ON FUNCTION realtime.build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]) TO authenticated;
GRANT ALL ON FUNCTION realtime.build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]) TO service_role;
GRANT ALL ON FUNCTION realtime.build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]) TO supabase_realtime_admin;


--
-- Name: FUNCTION "cast"(val text, type_ regtype); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime."cast"(val text, type_ regtype) TO postgres;
GRANT ALL ON FUNCTION realtime."cast"(val text, type_ regtype) TO dashboard_user;
GRANT ALL ON FUNCTION realtime."cast"(val text, type_ regtype) TO anon;
GRANT ALL ON FUNCTION realtime."cast"(val text, type_ regtype) TO authenticated;
GRANT ALL ON FUNCTION realtime."cast"(val text, type_ regtype) TO service_role;
GRANT ALL ON FUNCTION realtime."cast"(val text, type_ regtype) TO supabase_realtime_admin;


--
-- Name: FUNCTION check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text) TO postgres;
GRANT ALL ON FUNCTION realtime.check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text) TO dashboard_user;
GRANT ALL ON FUNCTION realtime.check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text) TO anon;
GRANT ALL ON FUNCTION realtime.check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text) TO authenticated;
GRANT ALL ON FUNCTION realtime.check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text) TO service_role;
GRANT ALL ON FUNCTION realtime.check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text) TO supabase_realtime_admin;


--
-- Name: FUNCTION is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]) TO postgres;
GRANT ALL ON FUNCTION realtime.is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]) TO dashboard_user;
GRANT ALL ON FUNCTION realtime.is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]) TO anon;
GRANT ALL ON FUNCTION realtime.is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]) TO authenticated;
GRANT ALL ON FUNCTION realtime.is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]) TO service_role;
GRANT ALL ON FUNCTION realtime.is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]) TO supabase_realtime_admin;


--
-- Name: FUNCTION list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer) TO postgres;
GRANT ALL ON FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer) TO dashboard_user;
GRANT ALL ON FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer) TO anon;
GRANT ALL ON FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer) TO authenticated;
GRANT ALL ON FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer) TO service_role;
GRANT ALL ON FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer) TO supabase_realtime_admin;


--
-- Name: FUNCTION quote_wal2json(entity regclass); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.quote_wal2json(entity regclass) TO postgres;
GRANT ALL ON FUNCTION realtime.quote_wal2json(entity regclass) TO dashboard_user;
GRANT ALL ON FUNCTION realtime.quote_wal2json(entity regclass) TO anon;
GRANT ALL ON FUNCTION realtime.quote_wal2json(entity regclass) TO authenticated;
GRANT ALL ON FUNCTION realtime.quote_wal2json(entity regclass) TO service_role;
GRANT ALL ON FUNCTION realtime.quote_wal2json(entity regclass) TO supabase_realtime_admin;


--
-- Name: FUNCTION send(payload jsonb, event text, topic text, private boolean); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.send(payload jsonb, event text, topic text, private boolean) TO postgres;
GRANT ALL ON FUNCTION realtime.send(payload jsonb, event text, topic text, private boolean) TO dashboard_user;


--
-- Name: FUNCTION subscription_check_filters(); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.subscription_check_filters() TO postgres;
GRANT ALL ON FUNCTION realtime.subscription_check_filters() TO dashboard_user;
GRANT ALL ON FUNCTION realtime.subscription_check_filters() TO anon;
GRANT ALL ON FUNCTION realtime.subscription_check_filters() TO authenticated;
GRANT ALL ON FUNCTION realtime.subscription_check_filters() TO service_role;
GRANT ALL ON FUNCTION realtime.subscription_check_filters() TO supabase_realtime_admin;


--
-- Name: FUNCTION to_regrole(role_name text); Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON FUNCTION realtime.to_regrole(role_name text) TO postgres;
GRANT ALL ON FUNCTION realtime.to_regrole(role_name text) TO dashboard_user;
GRANT ALL ON FUNCTION realtime.to_regrole(role_name text) TO anon;
GRANT ALL ON FUNCTION realtime.to_regrole(role_name text) TO authenticated;
GRANT ALL ON FUNCTION realtime.to_regrole(role_name text) TO service_role;
GRANT ALL ON FUNCTION realtime.to_regrole(role_name text) TO supabase_realtime_admin;


--
-- Name: FUNCTION topic(); Type: ACL; Schema: realtime; Owner: supabase_realtime_admin
--

GRANT ALL ON FUNCTION realtime.topic() TO postgres;
GRANT ALL ON FUNCTION realtime.topic() TO dashboard_user;


--
-- Name: FUNCTION http_request(); Type: ACL; Schema: supabase_functions; Owner: supabase_functions_admin
--

REVOKE ALL ON FUNCTION supabase_functions.http_request() FROM PUBLIC;
GRANT ALL ON FUNCTION supabase_functions.http_request() TO anon;
GRANT ALL ON FUNCTION supabase_functions.http_request() TO authenticated;
GRANT ALL ON FUNCTION supabase_functions.http_request() TO service_role;
GRANT ALL ON FUNCTION supabase_functions.http_request() TO postgres;


--
-- Name: FUNCTION _crypto_aead_det_decrypt(message bytea, additional bytea, key_id bigint, context bytea, nonce bytea); Type: ACL; Schema: vault; Owner: supabase_admin
--

GRANT ALL ON FUNCTION vault._crypto_aead_det_decrypt(message bytea, additional bytea, key_id bigint, context bytea, nonce bytea) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION create_secret(new_secret text, new_name text, new_description text, new_key_id uuid); Type: ACL; Schema: vault; Owner: supabase_admin
--

GRANT ALL ON FUNCTION vault.create_secret(new_secret text, new_name text, new_description text, new_key_id uuid) TO postgres WITH GRANT OPTION;


--
-- Name: FUNCTION update_secret(secret_id uuid, new_secret text, new_name text, new_description text, new_key_id uuid); Type: ACL; Schema: vault; Owner: supabase_admin
--

GRANT ALL ON FUNCTION vault.update_secret(secret_id uuid, new_secret text, new_name text, new_description text, new_key_id uuid) TO postgres WITH GRANT OPTION;


--
-- Name: TABLE audit_log_entries; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON TABLE auth.audit_log_entries TO dashboard_user;
GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.audit_log_entries TO postgres;
GRANT SELECT ON TABLE auth.audit_log_entries TO postgres WITH GRANT OPTION;


--
-- Name: TABLE flow_state; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.flow_state TO postgres;
GRANT SELECT ON TABLE auth.flow_state TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE auth.flow_state TO dashboard_user;


--
-- Name: TABLE identities; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.identities TO postgres;
GRANT SELECT ON TABLE auth.identities TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE auth.identities TO dashboard_user;


--
-- Name: TABLE instances; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON TABLE auth.instances TO dashboard_user;
GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.instances TO postgres;
GRANT SELECT ON TABLE auth.instances TO postgres WITH GRANT OPTION;


--
-- Name: TABLE mfa_amr_claims; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.mfa_amr_claims TO postgres;
GRANT SELECT ON TABLE auth.mfa_amr_claims TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE auth.mfa_amr_claims TO dashboard_user;


--
-- Name: TABLE mfa_challenges; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.mfa_challenges TO postgres;
GRANT SELECT ON TABLE auth.mfa_challenges TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE auth.mfa_challenges TO dashboard_user;


--
-- Name: TABLE mfa_factors; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.mfa_factors TO postgres;
GRANT SELECT ON TABLE auth.mfa_factors TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE auth.mfa_factors TO dashboard_user;


--
-- Name: TABLE one_time_tokens; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.one_time_tokens TO postgres;
GRANT SELECT ON TABLE auth.one_time_tokens TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE auth.one_time_tokens TO dashboard_user;


--
-- Name: TABLE refresh_tokens; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON TABLE auth.refresh_tokens TO dashboard_user;
GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.refresh_tokens TO postgres;
GRANT SELECT ON TABLE auth.refresh_tokens TO postgres WITH GRANT OPTION;


--
-- Name: SEQUENCE refresh_tokens_id_seq; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON SEQUENCE auth.refresh_tokens_id_seq TO dashboard_user;
GRANT ALL ON SEQUENCE auth.refresh_tokens_id_seq TO postgres;


--
-- Name: TABLE saml_providers; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.saml_providers TO postgres;
GRANT SELECT ON TABLE auth.saml_providers TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE auth.saml_providers TO dashboard_user;


--
-- Name: TABLE saml_relay_states; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.saml_relay_states TO postgres;
GRANT SELECT ON TABLE auth.saml_relay_states TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE auth.saml_relay_states TO dashboard_user;


--
-- Name: TABLE schema_migrations; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON TABLE auth.schema_migrations TO dashboard_user;
GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.schema_migrations TO postgres;
GRANT SELECT ON TABLE auth.schema_migrations TO postgres WITH GRANT OPTION;


--
-- Name: TABLE sessions; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.sessions TO postgres;
GRANT SELECT ON TABLE auth.sessions TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE auth.sessions TO dashboard_user;


--
-- Name: TABLE sso_domains; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.sso_domains TO postgres;
GRANT SELECT ON TABLE auth.sso_domains TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE auth.sso_domains TO dashboard_user;


--
-- Name: TABLE sso_providers; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.sso_providers TO postgres;
GRANT SELECT ON TABLE auth.sso_providers TO postgres WITH GRANT OPTION;
GRANT ALL ON TABLE auth.sso_providers TO dashboard_user;


--
-- Name: TABLE users; Type: ACL; Schema: auth; Owner: supabase_auth_admin
--

GRANT ALL ON TABLE auth.users TO dashboard_user;
GRANT INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE auth.users TO postgres;
GRANT SELECT ON TABLE auth.users TO postgres WITH GRANT OPTION;


--
-- Name: TABLE article_tags; Type: ACL; Schema: blog; Owner: supabase_admin
--

GRANT ALL ON TABLE blog.article_tags TO authenticated;
GRANT SELECT ON TABLE blog.article_tags TO anon;
GRANT ALL ON TABLE blog.article_tags TO postgres;


--
-- Name: TABLE article_views; Type: ACL; Schema: blog; Owner: supabase_admin
--

GRANT SELECT ON TABLE blog.article_views TO anon;
GRANT ALL ON TABLE blog.article_views TO postgres;


--
-- Name: TABLE articles; Type: ACL; Schema: blog; Owner: supabase_admin
--

GRANT ALL ON TABLE blog.articles TO authenticated;
GRANT SELECT ON TABLE blog.articles TO anon;
GRANT ALL ON TABLE blog.articles TO postgres;


--
-- Name: TABLE newsletter_subscribers; Type: ACL; Schema: blog; Owner: supabase_admin
--

GRANT ALL ON TABLE blog.newsletter_subscribers TO authenticated;
GRANT ALL ON TABLE blog.newsletter_subscribers TO postgres;


--
-- Name: TABLE profiles; Type: ACL; Schema: blog; Owner: supabase_admin
--

GRANT ALL ON TABLE blog.profiles TO authenticated;
GRANT ALL ON TABLE blog.profiles TO postgres;
GRANT SELECT ON TABLE blog.profiles TO anon;


--
-- Name: TABLE tags; Type: ACL; Schema: blog; Owner: supabase_admin
--

GRANT ALL ON TABLE blog.tags TO authenticated;
GRANT SELECT ON TABLE blog.tags TO anon;
GRANT ALL ON TABLE blog.tags TO postgres;


--
-- Name: TABLE job; Type: ACL; Schema: cron; Owner: supabase_admin
--

GRANT SELECT ON TABLE cron.job TO postgres WITH GRANT OPTION;


--
-- Name: TABLE job_run_details; Type: ACL; Schema: cron; Owner: supabase_admin
--

GRANT ALL ON TABLE cron.job_run_details TO postgres WITH GRANT OPTION;


--
-- Name: TABLE hypopg_list_indexes; Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON TABLE extensions.hypopg_list_indexes TO postgres WITH GRANT OPTION;


--
-- Name: TABLE hypopg_hidden_indexes; Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON TABLE extensions.hypopg_hidden_indexes TO postgres WITH GRANT OPTION;


--
-- Name: TABLE pg_stat_statements; Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON TABLE extensions.pg_stat_statements TO postgres WITH GRANT OPTION;


--
-- Name: TABLE pg_stat_statements_info; Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON TABLE extensions.pg_stat_statements_info TO postgres WITH GRANT OPTION;


--
-- Name: TABLE wrappers_fdw_stats; Type: ACL; Schema: extensions; Owner: supabase_admin
--

GRANT ALL ON TABLE extensions.wrappers_fdw_stats TO postgres WITH GRANT OPTION;


--
-- Name: TABLE decrypted_key; Type: ACL; Schema: pgsodium; Owner: supabase_admin
--

GRANT ALL ON TABLE pgsodium.decrypted_key TO pgsodium_keyholder;


--
-- Name: TABLE masking_rule; Type: ACL; Schema: pgsodium; Owner: supabase_admin
--

GRANT ALL ON TABLE pgsodium.masking_rule TO pgsodium_keyholder;


--
-- Name: TABLE mask_columns; Type: ACL; Schema: pgsodium; Owner: supabase_admin
--

GRANT ALL ON TABLE pgsodium.mask_columns TO pgsodium_keyholder;


--
-- Name: TABLE api_keys; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public.api_keys TO anon;
GRANT ALL ON TABLE public.api_keys TO authenticated;
GRANT ALL ON TABLE public.api_keys TO service_role;


--
-- Name: TABLE credits; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public.credits TO anon;
GRANT ALL ON TABLE public.credits TO authenticated;
GRANT ALL ON TABLE public.credits TO service_role;


--
-- Name: TABLE models; Type: ACL; Schema: public; Owner: supabase_admin
--

GRANT ALL ON TABLE public.models TO postgres;
GRANT ALL ON TABLE public.models TO anon;
GRANT ALL ON TABLE public.models TO authenticated;
GRANT ALL ON TABLE public.models TO service_role;


--
-- Name: TABLE negative_balances; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public.negative_balances TO anon;
GRANT ALL ON TABLE public.negative_balances TO authenticated;
GRANT ALL ON TABLE public.negative_balances TO service_role;


--
-- Name: TABLE paddle_customers; Type: ACL; Schema: public; Owner: supabase_admin
--

GRANT ALL ON TABLE public.paddle_customers TO postgres;
GRANT ALL ON TABLE public.paddle_customers TO anon;
GRANT ALL ON TABLE public.paddle_customers TO authenticated;
GRANT ALL ON TABLE public.paddle_customers TO service_role;


--
-- Name: TABLE paddle_transactions; Type: ACL; Schema: public; Owner: supabase_admin
--

GRANT ALL ON TABLE public.paddle_transactions TO postgres;
GRANT ALL ON TABLE public.paddle_transactions TO anon;
GRANT ALL ON TABLE public.paddle_transactions TO authenticated;
GRANT ALL ON TABLE public.paddle_transactions TO service_role;


--
-- Name: TABLE payg_usage; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public.payg_usage TO anon;
GRANT ALL ON TABLE public.payg_usage TO authenticated;
GRANT ALL ON TABLE public.payg_usage TO service_role;


--
-- Name: TABLE pricing_models; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public.pricing_models TO anon;
GRANT ALL ON TABLE public.pricing_models TO authenticated;
GRANT ALL ON TABLE public.pricing_models TO service_role;


--
-- Name: TABLE profiles; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public.profiles TO anon;
GRANT ALL ON TABLE public.profiles TO authenticated;
GRANT ALL ON TABLE public.profiles TO service_role;


--
-- Name: TABLE quota_notifications; Type: ACL; Schema: public; Owner: supabase_admin
--

GRANT ALL ON TABLE public.quota_notifications TO postgres;
GRANT ALL ON TABLE public.quota_notifications TO anon;
GRANT ALL ON TABLE public.quota_notifications TO authenticated;
GRANT ALL ON TABLE public.quota_notifications TO service_role;


--
-- Name: TABLE quotas; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public.quotas TO anon;
GRANT ALL ON TABLE public.quotas TO authenticated;
GRANT ALL ON TABLE public.quotas TO service_role;


--
-- Name: TABLE service_pricing; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public.service_pricing TO anon;
GRANT ALL ON TABLE public.service_pricing TO authenticated;
GRANT ALL ON TABLE public.service_pricing TO service_role;


--
-- Name: TABLE subscription_plans; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public.subscription_plans TO anon;
GRANT ALL ON TABLE public.subscription_plans TO authenticated;
GRANT ALL ON TABLE public.subscription_plans TO service_role;


--
-- Name: TABLE usage_history; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public.usage_history TO anon;
GRANT ALL ON TABLE public.usage_history TO authenticated;
GRANT ALL ON TABLE public.usage_history TO service_role;


--
-- Name: TABLE user_subscriptions; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public.user_subscriptions TO anon;
GRANT ALL ON TABLE public.user_subscriptions TO authenticated;
GRANT ALL ON TABLE public.user_subscriptions TO service_role;


--
-- Name: TABLE messages; Type: ACL; Schema: realtime; Owner: supabase_realtime_admin
--

GRANT ALL ON TABLE realtime.messages TO postgres;
GRANT ALL ON TABLE realtime.messages TO dashboard_user;
GRANT SELECT,INSERT,UPDATE ON TABLE realtime.messages TO anon;
GRANT SELECT,INSERT,UPDATE ON TABLE realtime.messages TO authenticated;
GRANT SELECT,INSERT,UPDATE ON TABLE realtime.messages TO service_role;


--
-- Name: TABLE messages_2025_08_25; Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON TABLE realtime.messages_2025_08_25 TO postgres;
GRANT ALL ON TABLE realtime.messages_2025_08_25 TO dashboard_user;


--
-- Name: TABLE messages_2025_08_26; Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON TABLE realtime.messages_2025_08_26 TO postgres;
GRANT ALL ON TABLE realtime.messages_2025_08_26 TO dashboard_user;


--
-- Name: TABLE messages_2025_08_27; Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON TABLE realtime.messages_2025_08_27 TO postgres;
GRANT ALL ON TABLE realtime.messages_2025_08_27 TO dashboard_user;


--
-- Name: TABLE messages_2025_08_28; Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON TABLE realtime.messages_2025_08_28 TO postgres;
GRANT ALL ON TABLE realtime.messages_2025_08_28 TO dashboard_user;


--
-- Name: TABLE messages_2025_08_29; Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON TABLE realtime.messages_2025_08_29 TO postgres;
GRANT ALL ON TABLE realtime.messages_2025_08_29 TO dashboard_user;


--
-- Name: TABLE schema_migrations; Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON TABLE realtime.schema_migrations TO postgres;
GRANT ALL ON TABLE realtime.schema_migrations TO dashboard_user;
GRANT SELECT ON TABLE realtime.schema_migrations TO anon;
GRANT SELECT ON TABLE realtime.schema_migrations TO authenticated;
GRANT SELECT ON TABLE realtime.schema_migrations TO service_role;
GRANT ALL ON TABLE realtime.schema_migrations TO supabase_realtime_admin;


--
-- Name: TABLE subscription; Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON TABLE realtime.subscription TO postgres;
GRANT ALL ON TABLE realtime.subscription TO dashboard_user;
GRANT SELECT ON TABLE realtime.subscription TO anon;
GRANT SELECT ON TABLE realtime.subscription TO authenticated;
GRANT SELECT ON TABLE realtime.subscription TO service_role;
GRANT ALL ON TABLE realtime.subscription TO supabase_realtime_admin;


--
-- Name: SEQUENCE subscription_id_seq; Type: ACL; Schema: realtime; Owner: supabase_admin
--

GRANT ALL ON SEQUENCE realtime.subscription_id_seq TO postgres;
GRANT ALL ON SEQUENCE realtime.subscription_id_seq TO dashboard_user;
GRANT USAGE ON SEQUENCE realtime.subscription_id_seq TO anon;
GRANT USAGE ON SEQUENCE realtime.subscription_id_seq TO authenticated;
GRANT USAGE ON SEQUENCE realtime.subscription_id_seq TO service_role;
GRANT ALL ON SEQUENCE realtime.subscription_id_seq TO supabase_realtime_admin;


--
-- Name: TABLE buckets; Type: ACL; Schema: storage; Owner: supabase_storage_admin
--

GRANT ALL ON TABLE storage.buckets TO anon;
GRANT ALL ON TABLE storage.buckets TO authenticated;
GRANT ALL ON TABLE storage.buckets TO service_role;
GRANT ALL ON TABLE storage.buckets TO postgres;


--
-- Name: TABLE migrations; Type: ACL; Schema: storage; Owner: supabase_storage_admin
--

GRANT ALL ON TABLE storage.migrations TO anon;
GRANT ALL ON TABLE storage.migrations TO authenticated;
GRANT ALL ON TABLE storage.migrations TO service_role;
GRANT ALL ON TABLE storage.migrations TO postgres;


--
-- Name: TABLE objects; Type: ACL; Schema: storage; Owner: supabase_storage_admin
--

GRANT ALL ON TABLE storage.objects TO anon;
GRANT ALL ON TABLE storage.objects TO authenticated;
GRANT ALL ON TABLE storage.objects TO service_role;
GRANT ALL ON TABLE storage.objects TO postgres;


--
-- Name: TABLE prefixes; Type: ACL; Schema: storage; Owner: supabase_storage_admin
--

GRANT ALL ON TABLE storage.prefixes TO service_role;
GRANT ALL ON TABLE storage.prefixes TO authenticated;
GRANT ALL ON TABLE storage.prefixes TO anon;


--
-- Name: TABLE s3_multipart_uploads; Type: ACL; Schema: storage; Owner: supabase_storage_admin
--

GRANT ALL ON TABLE storage.s3_multipart_uploads TO service_role;
GRANT SELECT ON TABLE storage.s3_multipart_uploads TO authenticated;
GRANT SELECT ON TABLE storage.s3_multipart_uploads TO anon;


--
-- Name: TABLE s3_multipart_uploads_parts; Type: ACL; Schema: storage; Owner: supabase_storage_admin
--

GRANT ALL ON TABLE storage.s3_multipart_uploads_parts TO service_role;
GRANT SELECT ON TABLE storage.s3_multipart_uploads_parts TO authenticated;
GRANT SELECT ON TABLE storage.s3_multipart_uploads_parts TO anon;


--
-- Name: TABLE hooks; Type: ACL; Schema: supabase_functions; Owner: supabase_functions_admin
--

GRANT ALL ON TABLE supabase_functions.hooks TO anon;
GRANT ALL ON TABLE supabase_functions.hooks TO authenticated;
GRANT ALL ON TABLE supabase_functions.hooks TO service_role;


--
-- Name: SEQUENCE hooks_id_seq; Type: ACL; Schema: supabase_functions; Owner: supabase_functions_admin
--

GRANT ALL ON SEQUENCE supabase_functions.hooks_id_seq TO anon;
GRANT ALL ON SEQUENCE supabase_functions.hooks_id_seq TO authenticated;
GRANT ALL ON SEQUENCE supabase_functions.hooks_id_seq TO service_role;


--
-- Name: TABLE migrations; Type: ACL; Schema: supabase_functions; Owner: supabase_functions_admin
--

GRANT ALL ON TABLE supabase_functions.migrations TO anon;
GRANT ALL ON TABLE supabase_functions.migrations TO authenticated;
GRANT ALL ON TABLE supabase_functions.migrations TO service_role;


--
-- Name: TABLE secrets; Type: ACL; Schema: vault; Owner: supabase_admin
--

GRANT SELECT,DELETE ON TABLE vault.secrets TO postgres WITH GRANT OPTION;


--
-- Name: TABLE decrypted_secrets; Type: ACL; Schema: vault; Owner: supabase_admin
--

GRANT SELECT,DELETE ON TABLE vault.decrypted_secrets TO postgres WITH GRANT OPTION;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: auth; Owner: supabase_auth_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_auth_admin IN SCHEMA auth GRANT ALL ON SEQUENCES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_auth_admin IN SCHEMA auth GRANT ALL ON SEQUENCES  TO dashboard_user;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: auth; Owner: supabase_auth_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_auth_admin IN SCHEMA auth GRANT ALL ON FUNCTIONS  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_auth_admin IN SCHEMA auth GRANT ALL ON FUNCTIONS  TO dashboard_user;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: auth; Owner: supabase_auth_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_auth_admin IN SCHEMA auth GRANT ALL ON TABLES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_auth_admin IN SCHEMA auth GRANT ALL ON TABLES  TO dashboard_user;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: blog; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA blog GRANT ALL ON TABLES  TO postgres;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: cron; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA cron GRANT ALL ON SEQUENCES  TO postgres WITH GRANT OPTION;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: cron; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA cron GRANT ALL ON FUNCTIONS  TO postgres WITH GRANT OPTION;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: cron; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA cron GRANT ALL ON TABLES  TO postgres WITH GRANT OPTION;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: extensions; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA extensions GRANT ALL ON SEQUENCES  TO postgres WITH GRANT OPTION;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: extensions; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA extensions GRANT ALL ON FUNCTIONS  TO postgres WITH GRANT OPTION;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: extensions; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA extensions GRANT ALL ON TABLES  TO postgres WITH GRANT OPTION;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: graphql; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON SEQUENCES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON SEQUENCES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON SEQUENCES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON SEQUENCES  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: graphql; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON FUNCTIONS  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON FUNCTIONS  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON FUNCTIONS  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON FUNCTIONS  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: graphql; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON TABLES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON TABLES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON TABLES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql GRANT ALL ON TABLES  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: graphql_public; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON SEQUENCES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON SEQUENCES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON SEQUENCES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON SEQUENCES  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: graphql_public; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON FUNCTIONS  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON FUNCTIONS  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON FUNCTIONS  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON FUNCTIONS  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: graphql_public; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON TABLES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON TABLES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON TABLES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA graphql_public GRANT ALL ON TABLES  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: pgsodium; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA pgsodium GRANT ALL ON SEQUENCES  TO pgsodium_keyholder;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: pgsodium; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA pgsodium GRANT ALL ON TABLES  TO pgsodium_keyholder;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: pgsodium_masks; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA pgsodium_masks GRANT ALL ON SEQUENCES  TO pgsodium_keyiduser;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: pgsodium_masks; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA pgsodium_masks GRANT ALL ON FUNCTIONS  TO pgsodium_keyiduser;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: pgsodium_masks; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA pgsodium_masks GRANT ALL ON TABLES  TO pgsodium_keyiduser;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: public; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON SEQUENCES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON SEQUENCES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON SEQUENCES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON SEQUENCES  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: public; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON SEQUENCES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON SEQUENCES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON SEQUENCES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON SEQUENCES  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: public; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON FUNCTIONS  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON FUNCTIONS  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON FUNCTIONS  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON FUNCTIONS  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: public; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON FUNCTIONS  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON FUNCTIONS  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON FUNCTIONS  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON FUNCTIONS  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: public; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON TABLES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON TABLES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON TABLES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON TABLES  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: public; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON TABLES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON TABLES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON TABLES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON TABLES  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: realtime; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA realtime GRANT ALL ON SEQUENCES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA realtime GRANT ALL ON SEQUENCES  TO dashboard_user;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: realtime; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA realtime GRANT ALL ON FUNCTIONS  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA realtime GRANT ALL ON FUNCTIONS  TO dashboard_user;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: realtime; Owner: supabase_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA realtime GRANT ALL ON TABLES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA realtime GRANT ALL ON TABLES  TO dashboard_user;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: storage; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON SEQUENCES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON SEQUENCES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON SEQUENCES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON SEQUENCES  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: storage; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON FUNCTIONS  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON FUNCTIONS  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON FUNCTIONS  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON FUNCTIONS  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: storage; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON TABLES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON TABLES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON TABLES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA storage GRANT ALL ON TABLES  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: supabase_functions; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA supabase_functions GRANT ALL ON SEQUENCES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA supabase_functions GRANT ALL ON SEQUENCES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA supabase_functions GRANT ALL ON SEQUENCES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA supabase_functions GRANT ALL ON SEQUENCES  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: supabase_functions; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA supabase_functions GRANT ALL ON FUNCTIONS  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA supabase_functions GRANT ALL ON FUNCTIONS  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA supabase_functions GRANT ALL ON FUNCTIONS  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA supabase_functions GRANT ALL ON FUNCTIONS  TO service_role;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: supabase_functions; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA supabase_functions GRANT ALL ON TABLES  TO postgres;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA supabase_functions GRANT ALL ON TABLES  TO anon;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA supabase_functions GRANT ALL ON TABLES  TO authenticated;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA supabase_functions GRANT ALL ON TABLES  TO service_role;


--
-- Name: issue_graphql_placeholder; Type: EVENT TRIGGER; Schema: -; Owner: supabase_admin
--

CREATE EVENT TRIGGER issue_graphql_placeholder ON sql_drop
         WHEN TAG IN ('DROP EXTENSION')
   EXECUTE FUNCTION extensions.set_graphql_placeholder();


ALTER EVENT TRIGGER issue_graphql_placeholder OWNER TO supabase_admin;

--
-- Name: issue_pg_cron_access; Type: EVENT TRIGGER; Schema: -; Owner: supabase_admin
--

CREATE EVENT TRIGGER issue_pg_cron_access ON ddl_command_end
         WHEN TAG IN ('CREATE EXTENSION')
   EXECUTE FUNCTION extensions.grant_pg_cron_access();


ALTER EVENT TRIGGER issue_pg_cron_access OWNER TO supabase_admin;

--
-- Name: issue_pg_graphql_access; Type: EVENT TRIGGER; Schema: -; Owner: supabase_admin
--

CREATE EVENT TRIGGER issue_pg_graphql_access ON ddl_command_end
         WHEN TAG IN ('CREATE FUNCTION')
   EXECUTE FUNCTION extensions.grant_pg_graphql_access();


ALTER EVENT TRIGGER issue_pg_graphql_access OWNER TO supabase_admin;

--
-- Name: issue_pg_net_access; Type: EVENT TRIGGER; Schema: -; Owner: postgres
--

CREATE EVENT TRIGGER issue_pg_net_access ON ddl_command_end
         WHEN TAG IN ('CREATE EXTENSION')
   EXECUTE FUNCTION extensions.grant_pg_net_access();


ALTER EVENT TRIGGER issue_pg_net_access OWNER TO postgres;

--
-- Name: pgrst_ddl_watch; Type: EVENT TRIGGER; Schema: -; Owner: supabase_admin
--

CREATE EVENT TRIGGER pgrst_ddl_watch ON ddl_command_end
   EXECUTE FUNCTION extensions.pgrst_ddl_watch();


ALTER EVENT TRIGGER pgrst_ddl_watch OWNER TO supabase_admin;

--
-- Name: pgrst_drop_watch; Type: EVENT TRIGGER; Schema: -; Owner: supabase_admin
--

CREATE EVENT TRIGGER pgrst_drop_watch ON sql_drop
   EXECUTE FUNCTION extensions.pgrst_drop_watch();


ALTER EVENT TRIGGER pgrst_drop_watch OWNER TO supabase_admin;

--
-- PostgreSQL database dump complete
--




SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


CREATE SCHEMA IF NOT EXISTS "internal";


ALTER SCHEMA "internal" OWNER TO "postgres";


CREATE EXTENSION IF NOT EXISTS "pgsodium";






COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";






CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgjwt" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";






CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";






CREATE TYPE "public"."api_key_result" AS (
	"id" "uuid",
	"key_secret" "text"
);


ALTER TYPE "public"."api_key_result" OWNER TO "postgres";


CREATE TYPE "public"."payment_status" AS ENUM (
    'pending',
    'paid',
    'unpaid'
);


ALTER TYPE "public"."payment_status" OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."apply_negative_balances"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    total_negative DECIMAL(18,9);
    remaining_credit DECIMAL(18,9);
BEGIN
    -- Calculate total negative balance for this user
    SELECT COALESCE(SUM(amount), 0) INTO total_negative
    FROM public.negative_balances
    WHERE user_id = NEW.user_id AND cleared_at IS NULL;
    
    -- If no negative balance, just return the NEW record unchanged
    IF total_negative = 0 THEN
        RETURN NEW;
    END IF;
    
    -- Calculate how much we can clear
    remaining_credit := NEW.balance;
    
    -- Apply negative balance deduction if possible
    IF remaining_credit >= total_negative THEN
        -- We can clear all negative balances
        NEW.balance := remaining_credit - total_negative;
        
        -- Mark all negative balances as cleared
        UPDATE public.negative_balances
        SET cleared_at = NOW()
        WHERE user_id = NEW.user_id AND cleared_at IS NULL;
    ELSE
        -- We can only clear part of the negative balances
        -- Update the credit balance to zero (since we can't clear everything)
        NEW.balance := 0;
        
        -- Create a temporary table to track which negative balances we'll clear
        CREATE TEMP TABLE cleared_negatives (
            id UUID,
            amount DECIMAL(18,9)
        ) ON COMMIT DROP;
        
        -- Fill the temporary table with the negative balances we can clear
        -- Process oldest first (FIFO)
        INSERT INTO cleared_negatives (id, amount)
        SELECT id, amount
        FROM public.negative_balances
        WHERE user_id = NEW.user_id AND cleared_at IS NULL
        ORDER BY created_at ASC;
        
        -- Clear as many negative balances as possible
        DECLARE
            negative_id UUID;
            negative_amount DECIMAL(18,9);
            can_clear DECIMAL(18,9);
        BEGIN
            FOR negative_id, negative_amount IN SELECT id, amount FROM cleared_negatives LOOP
                IF remaining_credit <= 0 THEN
                    EXIT; -- No more credit to apply
                END IF;
                
                IF remaining_credit >= negative_amount THEN
                    -- Clear this negative balance completely
                    UPDATE public.negative_balances
                    SET cleared_at = NOW()
                    WHERE id = negative_id;
                    
                    remaining_credit := remaining_credit - negative_amount;
                ELSE
                    -- Partially clear this negative balance
                    UPDATE public.negative_balances
                    SET amount = amount - remaining_credit
                    WHERE id = negative_id;
                    
                    remaining_credit := 0;
                    EXIT; -- No more credit to apply
                END IF;
            END LOOP;
        END;
    END IF;
    
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."apply_negative_balances"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."check_api_key_limit"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$BEGIN
    IF (SELECT COUNT(*) FROM public.api_keys WHERE user_id = NEW.user_id) >= 25 THEN
        RAISE EXCEPTION 'Maximum number of API keys (25) reached for this user';
    END IF;
    RETURN NEW;
END;$$;


ALTER FUNCTION "public"."check_api_key_limit"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."check_optimization_allowance"("p_user_id" "uuid", "p_service" "text", "p_model" "text", "p_amount" numeric, "p_api_key_id" "uuid") RETURNS TABLE("can_use" boolean, "pricing_model" "text", "cost" numeric, "max_output_tokens" integer)
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    subscription_available BOOLEAN;
    credit_balance DECIMAL(18,9);
    service_cost DECIMAL(18,9);
    cost_per_unit DECIMAL(18,9);
    payg_allowed BOOLEAN;
    max_tokens INTEGER;
BEGIN
    -- Check if the service and model exist in pricing
    SELECT sp.cost_per_unit INTO cost_per_unit
    FROM public.service_pricing sp
    WHERE sp.service = p_service
    AND sp.model = p_model
    AND sp.is_active = TRUE;

    IF cost_per_unit IS NULL THEN
        RETURN QUERY SELECT 
            FALSE::BOOLEAN as can_use,
            NULL::TEXT as pricing_model,
            0::DECIMAL(18,9) as cost,
            NULL::INTEGER as max_output_tokens;
        RETURN;
    END IF;

    -- Calculate the cost based on the amount and cost per unit
    service_cost := p_amount * cost_per_unit;

    -- Check if user has an active subscription
    SELECT EXISTS (
        SELECT 1
        FROM public.subscriptions s
        WHERE s.user_id = p_user_id
        AND s.status = 'active'
        AND s.current_period_end > NOW()
    ) INTO subscription_available;

    -- If subscription is available, allow usage
    IF subscription_available THEN
        RETURN QUERY SELECT 
            TRUE::BOOLEAN as can_use,
            'subscription'::TEXT as pricing_model,
            service_cost,
            4096 as max_output_tokens; -- Default max tokens for subscription
        RETURN;
    END IF;

    -- Check user's credit balance
    SELECT COALESCE(balance, 0) INTO credit_balance
    FROM public.credits
    WHERE user_id = p_user_id;

    IF credit_balance IS NULL THEN
        credit_balance := 0;
    END IF;

    IF credit_balance >= service_cost THEN 
        RETURN QUERY SELECT 
            TRUE as can_use,
            'credits'::TEXT as pricing_model,
            service_cost,
            4096 as max_output_tokens;
        RETURN;
    END IF;

    -- Check if PAYG is allowed for this API key
    SELECT COALESCE(allow_payg, FALSE) INTO payg_allowed
    FROM public.api_keys
    WHERE id = p_api_key_id
    AND is_active = TRUE;

    IF payg_allowed IS NULL THEN
        payg_allowed := FALSE;
    END IF;

    -- Fallback to PAYG if allowed and user has stripe customer id
    IF payg_allowed AND EXISTS (
        SELECT 1 FROM public.profiles
        WHERE id = p_user_id
        AND stripe_customer_id IS NOT NULL
    ) THEN
        RETURN QUERY SELECT 
            TRUE::BOOLEAN as can_use,
            'payg'::TEXT as pricing_model,
            service_cost,
            4096 as max_output_tokens; -- Default max tokens for PAYG
        RETURN;
    END IF;

    -- If we get here, no payment method is available
    RETURN QUERY SELECT 
        FALSE::BOOLEAN as can_use,
        NULL::TEXT as pricing_model,
        service_cost,
        NULL::INTEGER as max_output_tokens;
END;
$$;


ALTER FUNCTION "public"."check_optimization_allowance"("p_user_id" "uuid", "p_service" "text", "p_model" "text", "p_amount" numeric, "p_api_key_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."check_transcription_allowance"("p_user_id" "uuid", "p_service" "text", "p_model" "text", "p_amount" numeric, "p_api_key_id" "uuid") RETURNS TABLE("can_use" boolean, "pricing_model" "text", "cost" numeric, "max_output_tokens" "text")
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    subscription_available BOOLEAN;
    credit_balance DECIMAL(18,9);
    service_cost DECIMAL(18,9);
    cost_per_unit DECIMAL(18,9);
    payg_allowed BOOLEAN;
BEGIN
    -- Check if the service and model exist in pricing
    SELECT sp.cost_per_unit INTO cost_per_unit
    FROM public.service_pricing sp
    WHERE sp.service = p_service
    AND sp.model = p_model
    AND sp.is_active = TRUE;

    IF cost_per_unit IS NULL THEN
        RETURN QUERY SELECT 
            FALSE::BOOLEAN as can_use,
            NULL::TEXT as pricing_model,
            0::DECIMAL(18,9) as cost,
            NULL::TEXT as max_output_tokens;
        RETURN;
    END IF;

    -- Calculate the cost based on the amount and cost per unit
    service_cost := p_amount * cost_per_unit;

    -- Check if user has an active subscription
    SELECT EXISTS (
        SELECT 1
        FROM public.subscriptions s
        WHERE s.user_id = p_user_id
        AND s.status = 'active'
        AND s.current_period_end > NOW()
    ) INTO subscription_available;

    -- If subscription is available, allow usage
    IF subscription_available THEN
        RETURN QUERY SELECT 
            TRUE::BOOLEAN as can_use,
            'subscription'::TEXT as pricing_model,
            service_cost,
            '4096'::TEXT as max_output_tokens; -- Default max tokens for subscription
        RETURN;
    END IF;

    -- Check user's credit balance
    SELECT COALESCE(balance, 0) INTO credit_balance
    FROM public.credits
    WHERE user_id = p_user_id;

    IF credit_balance IS NULL THEN
        credit_balance := 0;
    END IF;

    IF credit_balance >= service_cost THEN 
        RETURN QUERY SELECT 
            TRUE as can_use,
            'credits'::TEXT as pricing_model,
            service_cost,
            '4096'::TEXT as max_output_tokens;
        RETURN;
    END IF;

    -- Check if PAYG is allowed for this API key
    SELECT COALESCE(allow_payg, FALSE) INTO payg_allowed
    FROM public.api_keys
    WHERE id = p_api_key_id
    AND is_active = TRUE;

    IF payg_allowed IS NULL THEN
        payg_allowed := FALSE;
    END IF;

    -- Fallback to PAYG if allowed and user has stripe customer id
    IF payg_allowed AND EXISTS (
        SELECT 1 FROM public.profiles
        WHERE id = p_user_id
        AND stripe_customer_id IS NOT NULL
    ) THEN
        RETURN QUERY SELECT 
            TRUE::BOOLEAN as can_use,
            'payg'::TEXT as pricing_model,
            service_cost,
            '4096'::TEXT as max_output_tokens; -- Default max tokens for PAYG
        RETURN;
    END IF;

    -- If we get here, no payment method is available
    RETURN QUERY SELECT 
        FALSE::BOOLEAN as can_use,
        NULL::TEXT as pricing_model,
        service_cost,
        NULL::TEXT as max_output_tokens;
END;
$$;


ALTER FUNCTION "public"."check_transcription_allowance"("p_user_id" "uuid", "p_service" "text", "p_model" "text", "p_amount" numeric, "p_api_key_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."check_usage_allowance"("p_user_id" "uuid", "p_service" "text", "p_model" "text", "p_amount" numeric, "p_api_key_id" "uuid", "p_is_input_only" boolean DEFAULT false) RETURNS TABLE("can_use" boolean, "pricing_model" "text", "cost" numeric, "max_output_tokens" "text", "error_code" "text")
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    subscription_available BOOLEAN;
    credit_balance DECIMAL(18,9);
    service_cost DECIMAL(18,9);
    cost_per_unit DECIMAL(18,9);
    payg_allowed BOOLEAN;
    input_cost_per_unit DECIMAL(18,9);
    output_cost_per_unit DECIMAL(18,9);
    max_tokens INTEGER;
    has_unpaid_previous_month BOOLEAN;
    is_token_based BOOLEAN;
    avg_tokens_per_minute INTEGER;
    avg_token_cost DECIMAL(18,9);
    estimated_minutes INTEGER;
    pricing_unit TEXT;
BEGIN
    -- Get information about the model's pricing unit
    SELECT sp.unit INTO pricing_unit
    FROM public.service_pricing sp
    WHERE sp.service = p_service
    AND sp.model = p_model
    AND sp.is_active = TRUE;
    
    -- Check if this is a token-based model
    is_token_based := pricing_unit = 'token-based';
    
    -- Get cost per unit first for standard pricing
    SELECT sp.cost_per_unit INTO cost_per_unit
    FROM public.service_pricing sp
    WHERE sp.service = p_service
    AND sp.model = p_model
    AND sp.is_active = TRUE;

    -- Calculate standard service cost with proper decimal precision
    IF cost_per_unit IS NOT NULL AND NOT is_token_based THEN
        service_cost := cost_per_unit * p_amount;
    END IF;

    -- Check subscription quota first
    SELECT EXISTS (
        SELECT 1 FROM public.quotas q
        JOIN public.user_subscriptions us ON q.subscription_id = us.id
        WHERE us.user_id = p_user_id
        AND q.service = p_service
        AND (q.used_amount + p_amount) <= q.total_amount
        AND us.status = 'active'
        AND q.reset_date > NOW()
    ) INTO subscription_available;

    IF subscription_available THEN 
        RETURN QUERY SELECT 
            TRUE as can_use,
            'subscription'::TEXT as pricing_model,
            service_cost,
            '4096'::TEXT as max_output_tokens,
            NULL::TEXT as error_code;
        RETURN;
    END IF;

    -- Check credit balance
    SELECT COALESCE(balance, 0) INTO credit_balance
    FROM public.credits
    WHERE user_id = p_user_id;

    IF credit_balance IS NULL THEN
        credit_balance := 0;
    END IF;
    
    -- Check if user has unpaid balances from previous months
    SELECT EXISTS (
        SELECT 1 
        FROM public.payg_usage 
        WHERE user_id = p_user_id 
        AND payment_status = 'unpaid'
        AND month < date_trunc('month', CURRENT_DATE)
    ) INTO has_unpaid_previous_month;
    
    -- Return error if there are unpaid balances
    IF has_unpaid_previous_month THEN
        RETURN QUERY SELECT 
            FALSE::BOOLEAN as can_use,
            NULL::TEXT as pricing_model,
            0::DECIMAL(18,9) as cost,
            NULL::TEXT as max_output_tokens,
            'unpaid_balance'::TEXT as error_code;
        RETURN;
    END IF;
    
    -- Special handling for token-based transcription models (GPT-4o)
    IF is_token_based AND p_service = 'transcription' THEN
        -- Get token costs for this model
        SELECT sp.cost_per_unit INTO input_cost_per_unit
        FROM public.service_pricing sp
        WHERE sp.service = 'transcription'
        AND sp.model = p_model || '/input'
        AND sp.is_active = true;
        
        SELECT sp.cost_per_unit INTO output_cost_per_unit
        FROM public.service_pricing sp
        WHERE sp.service = 'transcription'
        AND sp.model = p_model || '/output'
        AND sp.is_active = true;
        
        IF input_cost_per_unit IS NULL OR output_cost_per_unit IS NULL THEN
            RETURN QUERY SELECT 
                FALSE::BOOLEAN as can_use,
                NULL::TEXT as pricing_model,
                0::DECIMAL(18,9) as cost,
                NULL::TEXT as max_output_tokens,
                NULL::TEXT as error_code;
            RETURN;
        END IF;
        
        -- Set estimated words per minute based on model
        -- These are conservative estimates that can be adjusted based on real-world data
        IF p_model = 'gpt-4o-mini-transcribe' THEN
            avg_tokens_per_minute := 200; -- approx. 150 words per minute
        ELSIF p_model = 'gpt-4o-transcribe' THEN
            avg_tokens_per_minute := 200; -- approx. 150 words per minute
        ELSE
            avg_tokens_per_minute := 200; -- default
        END IF;
        
        -- Calculate average token cost
        avg_token_cost := (input_cost_per_unit + output_cost_per_unit) / 2;
        
        -- Calculate how many minutes of audio the user can afford
        estimated_minutes := FLOOR(credit_balance / (avg_tokens_per_minute * avg_token_cost));
        
        IF estimated_minutes > 0 THEN
            RETURN QUERY SELECT 
                TRUE as can_use,
                'credits'::TEXT as pricing_model,
                input_cost_per_unit * avg_tokens_per_minute * p_amount as cost, -- Estimated cost for the requested minutes
                estimated_minutes::TEXT as max_output_tokens, -- Return estimated minutes as max_output_tokens
                NULL::TEXT as error_code;
            RETURN;
        END IF;
    END IF;

    -- Special handling for optimization service (LLM calls)
    IF p_service = 'optimization' THEN
        -- Try to get separate input/output pricing
        SELECT sp.cost_per_unit INTO input_cost_per_unit
        FROM public.service_pricing sp
        WHERE sp.service = 'optimization'
        AND sp.model = p_model || '/input'
        AND sp.is_active = TRUE;

        SELECT sp.cost_per_unit INTO output_cost_per_unit
        FROM public.service_pricing sp
        WHERE sp.service = 'optimization'
        AND sp.model = p_model || '/output'
        AND sp.is_active = TRUE;

        -- If we have separate input/output pricing
        IF input_cost_per_unit IS NOT NULL AND output_cost_per_unit IS NOT NULL THEN
            -- Calculate input tokens cost
            service_cost := input_cost_per_unit * p_amount;
            
            -- If user has any credits at all
            IF credit_balance > 0 THEN
                -- Calculate how many output tokens they can afford
                -- First subtract the cost of input tokens
                IF credit_balance > service_cost THEN
                    -- Remaining credits after input tokens
                    credit_balance := credit_balance - service_cost;
                    
                    -- Calculate max output tokens they can afford
                    max_tokens := FLOOR(credit_balance / output_cost_per_unit);
                    
                    -- Cap at a reasonable maximum (4096 is common for many models)
                    max_tokens := LEAST(max_tokens, 4096);
                    
                    RETURN QUERY SELECT 
                        TRUE as can_use,
                        'credits'::TEXT as pricing_model,
                        service_cost, -- This is just the input cost for now
                        max_tokens::TEXT as max_output_tokens,
                        NULL::TEXT as error_code;
                    RETURN;
                END IF;
            END IF;
        ELSE
            -- If we don't have separate pricing, use the combined pricing
            -- If user has any credits at all and they can cover at least the input tokens
            IF credit_balance > 0 AND cost_per_unit IS NOT NULL THEN
                -- Calculate max tokens they can afford (input + output)
                max_tokens := FLOOR(credit_balance / cost_per_unit) - p_amount;
                
                -- If they can afford at least some output tokens
                IF max_tokens > 0 THEN
                    -- Cap at a reasonable maximum
                    max_tokens := LEAST(max_tokens, 4096);
                    
                    RETURN QUERY SELECT 
                        TRUE as can_use,
                        'credits'::TEXT as pricing_model,
                        service_cost,
                        max_tokens::TEXT as max_output_tokens,
                        NULL::TEXT as error_code;
                    RETURN;
                END IF;
            END IF;
        END IF;
    ELSE
        -- For non-optimization services and non-token-based transcription, use the original logic
        IF NOT is_token_based AND credit_balance >= service_cost THEN 
            RETURN QUERY SELECT 
                TRUE as can_use,
                'credits'::TEXT as pricing_model,
                service_cost,
                NULL::TEXT as max_output_tokens,
                NULL::TEXT as error_code;
            RETURN;
        END IF;
    END IF;

    -- Check if PAYG is allowed for this API key
    SELECT COALESCE(allow_payg, FALSE) INTO payg_allowed
    FROM public.api_keys
    WHERE id = p_api_key_id
    AND is_active = TRUE;

    IF payg_allowed IS NULL THEN
        payg_allowed := FALSE;
    END IF;

    -- Fallback to PAYG if allowed, user has stripe customer id, and no unpaid balances
    IF payg_allowed 
       AND NOT has_unpaid_previous_month
       AND EXISTS (
        SELECT 1 FROM public.profiles
        WHERE id = p_user_id
        AND stripe_customer_id IS NOT NULL
    ) THEN
        RETURN QUERY SELECT 
            TRUE::BOOLEAN as can_use,
            'payg'::TEXT as pricing_model,
            service_cost,
            '4096'::TEXT as max_output_tokens,
            NULL::TEXT as error_code;
        RETURN;
    END IF;

    -- If we get here, no payment method is available
    RETURN QUERY SELECT 
        FALSE::BOOLEAN as can_use,
        NULL::TEXT as pricing_model,
        service_cost,
        NULL::TEXT as max_output_tokens,
        'insufficient_credits'::TEXT as error_code;
END;
$$;


ALTER FUNCTION "public"."check_usage_allowance"("p_user_id" "uuid", "p_service" "text", "p_model" "text", "p_amount" numeric, "p_api_key_id" "uuid", "p_is_input_only" boolean) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."create_api_key"("p_name" "text", "p_expires_at" timestamp with time zone DEFAULT NULL::timestamp with time zone) RETURNS "public"."api_key_result"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_user_id UUID;
    v_key_secret TEXT;
    v_key_prefix TEXT;
    v_key_hash TEXT;
    v_api_key_id UUID;
    v_key_count INTEGER;
    v_created_at TIMESTAMPTZ;
    v_final_expires_at TIMESTAMPTZ;
    v_max_expires_at TIMESTAMPTZ;
    v_result public.api_key_result;
BEGIN
    -- Get the current user's ID
    v_user_id := auth.uid();
    
    -- Check if user is authenticated
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'Not authenticated';
    END IF;
    
    -- Check API key limit (belt-and-suspenders approach with the trigger)
    SELECT COUNT(*) INTO v_key_count FROM public.api_keys WHERE user_id = v_user_id;
    IF v_key_count >= 25 THEN
        RAISE EXCEPTION 'Maximum number of API keys (25) reached for this user';
    END IF;
    
    -- Generate a secure API key
    v_key_secret := encode(gen_random_bytes(24), 'base64');
    v_key_prefix := substring(v_key_secret from 1 for 8);
    v_key_hash := crypt(v_key_secret, gen_salt('bf'));
    
    -- Set the creation time
    v_created_at := NOW();
    
    -- Calculate maximum expiration date (1 year from creation)
    v_max_expires_at := v_created_at + INTERVAL '1 year';
    
    -- Set expiration date to one year from creation if not provided
    IF p_expires_at IS NULL THEN
        v_final_expires_at := v_max_expires_at;
    ELSE
        -- Validate user-provided expiration date
        IF p_expires_at <= v_created_at THEN
            RAISE EXCEPTION 'Expiration date must be in the future';
        END IF;
        
        -- Limit expiration date to a maximum of 1 year from creation
        v_final_expires_at := LEAST(p_expires_at, v_max_expires_at);
    END IF;
    
    -- Insert the new API key with controlled fields
    INSERT INTO public.api_keys (
        user_id,
        name,
        key_prefix,
        key_hash,
        created_at,
        expires_at
    ) VALUES (
        v_user_id,
        p_name,
        v_key_prefix,
        v_key_hash,
        v_created_at,  -- Server-controlled timestamp
        v_final_expires_at  -- Limited to a maximum of 1 year from creation
    ) RETURNING id INTO v_api_key_id;
    
    -- Return both the ID and the key secret
    v_result.id := v_api_key_id;
    v_result.key_secret := v_key_secret;
    
    RETURN v_result;
END;
$$;


ALTER FUNCTION "public"."create_api_key"("p_name" "text", "p_expires_at" timestamp with time zone) OWNER TO "postgres";


COMMENT ON FUNCTION "public"."create_api_key"("p_name" "text", "p_expires_at" timestamp with time zone) IS 'Creates a new API key for the authenticated user with a maximum expiration of 1 year and returns both the ID and the key secret';



CREATE OR REPLACE FUNCTION "public"."finalize_realtime_session"("p_user_id" "uuid", "p_api_key_id" "uuid", "p_model" "text", "p_session_id" "text", "p_start_time" bigint, "p_audio_bytes_sent" bigint, "p_has_transcription" boolean, "p_token_info" "jsonb" DEFAULT NULL::"jsonb") RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_end_time bigint;
    v_session_duration_ms bigint;
    v_session_duration_minutes numeric;
    v_service_pricing record;
    v_cost numeric;
    v_pricing_model text;
    v_cost_per_unit numeric;
    v_input_tokens integer;
    v_output_tokens integer;
    v_input_token_cost numeric;
    v_output_token_cost numeric;
    v_total_token_cost numeric;
    v_is_token_based boolean := false;
    v_query_result record;
    v_available_credit numeric;
    v_model_prefix text;
    v_metadata jsonb;
BEGIN
    -- Calculate session duration
    v_end_time := extract(epoch from now()) * 1000;
    v_session_duration_ms := v_end_time - p_start_time;
    v_session_duration_minutes := v_session_duration_ms / (1000 * 60);

    -- Extract the base model name for GPT-4o models
    IF p_model LIKE 'gpt-4o%' THEN
        v_model_prefix := p_model;
        -- If realtime not already in the name, add it
        IF p_model NOT LIKE '%-realtime' THEN
            v_model_prefix := p_model || '-realtime';
        END IF;
    ELSE
        v_model_prefix := p_model;
    END IF;

    -- Get service pricing
    SELECT * INTO v_service_pricing
    FROM public.service_pricing
    WHERE service = 'transcription'
    AND model = v_model_prefix
    AND is_active = TRUE;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'No active pricing found for service: transcription, model: %', v_model_prefix;
    END IF;

    -- Determine if this is a token-based model
    v_is_token_based := v_service_pricing.unit = 'token-based';

    -- If token info is provided and model is token-based, use token billing
    IF v_is_token_based AND p_token_info IS NOT NULL THEN
        -- Extract token counts
        v_input_tokens := COALESCE((p_token_info->>'input_tokens')::INTEGER, 0);
        v_output_tokens := COALESCE((p_token_info->>'output_tokens')::INTEGER, 0);
        
        -- Get token costs for this model
        SELECT cost_per_unit INTO v_input_token_cost
        FROM public.service_pricing
        WHERE service = 'transcription'
        AND model = REPLACE(v_model_prefix, '-realtime', '/input')
        AND is_active = TRUE;
        
        SELECT cost_per_unit INTO v_output_token_cost
        FROM public.service_pricing
        WHERE service = 'transcription'
        AND model = REPLACE(v_model_prefix, '-realtime', '/output')
        AND is_active = TRUE;
        
        -- Calculate total cost based on token usage
        v_total_token_cost := (v_input_tokens * v_input_token_cost) + 
                           (v_output_tokens * v_output_token_cost);
        
        v_cost := v_total_token_cost;
        v_metadata := jsonb_build_object(
            'sessionId', p_session_id,
            'startTime', p_start_time,
            'endTime', v_end_time,
            'durationMs', v_session_duration_ms,
            'audioBytesSent', p_audio_bytes_sent,
            'hasTranscription', p_has_transcription,
            'token_based', true,
            'input_tokens', v_input_tokens,
            'output_tokens', v_output_tokens,
            'input_cost', ROUND(v_input_tokens * v_input_token_cost, 9),
            'output_cost', ROUND(v_output_tokens * v_output_token_cost, 9)
        );
    ELSE
        -- Use standard duration-based pricing
        v_cost_per_unit := v_service_pricing.cost_per_unit;
        v_cost := v_cost_per_unit * v_session_duration_minutes;
        
        v_metadata := jsonb_build_object(
            'sessionId', p_session_id,
            'startTime', p_start_time,
            'endTime', v_end_time,
            'durationMs', v_session_duration_ms,
            'audioBytesSent', p_audio_bytes_sent,
            'hasTranscription', p_has_transcription
        );
    END IF;

    -- Check if user has subscription or credits
    SELECT 
        pr.pricing_model,
        CASE
            WHEN pr.pricing_model = 'subscription' THEN
                GREATEST(0, q.allowed_amount - q.used_amount)
            WHEN pr.pricing_model = 'credits' THEN
                c.balance
            ELSE 0
        END AS available,
        pr.pricing_model = 'credits' AS is_credits
    INTO v_query_result 
    FROM 
        public.check_usage_allowance(p_user_id, 'transcription', v_model_prefix, 1, p_api_key_id, false) ca
    JOIN
        public.pricing_results pr ON pr.id = ca.pricing_result_id
    LEFT JOIN
        public.quotas q ON q.subscription_id = pr.subscription_id AND q.service = 'transcription'
    LEFT JOIN
        public.credits c ON c.user_id = p_user_id
    WHERE ca.can_use = TRUE
    ORDER BY
        CASE pr.pricing_model
            WHEN 'subscription' THEN 1
            WHEN 'credits' THEN 2
            WHEN 'payg' THEN 3
            ELSE 4
        END
    LIMIT 1;

    -- Set pricing model based on query result or default to credits
    v_pricing_model := COALESCE(v_query_result.pricing_model, 'credits');
    
    -- Update usage history with final details
    UPDATE public.usage_history
SET 
    status = 'success',
    amount = v_session_duration_minutes,
    cost = ROUND(v_cost, 9),
    metadata = v_metadata,
    pricing_model = v_pricing_model
WHERE 
    id = (
        SELECT id
        FROM public.usage_history
        WHERE 
            user_id = p_user_id
            AND service = 'transcription'
            AND model = v_model_prefix || '-realtime'
            AND status = 'pending'
        ORDER BY created_at DESC
        LIMIT 1
    );

    -- Handle negative balances for credit users when using token-based billing
    IF v_is_token_based AND v_pricing_model = 'credits' AND p_token_info IS NOT NULL THEN
        v_available_credit := COALESCE(v_query_result.available, 0);
        
        -- If cost exceeds available credits, create a negative balance record
        IF v_cost > v_available_credit THEN
            INSERT INTO public.negative_balances (
                user_id,
                service,
                model,
                amount,
                usage_history_id
            )
            SELECT 
                p_user_id,
                'transcription',
                v_model_prefix,
                ROUND(v_cost - v_available_credit, 9),
                id
            FROM
                public.usage_history
            WHERE 
                user_id = p_user_id
                AND service = 'transcription'
                AND model = v_model_prefix || '-realtime'
                AND status = 'success'
                AND (metadata->>'sessionId')::text = p_session_id
            ORDER BY created_at DESC
            LIMIT 1;
            
            -- Only deduct available credits, not more
            UPDATE public.credits
            SET balance = 0
            WHERE user_id = p_user_id AND balance = v_available_credit;
        ELSE
            -- Regular credit deduction
            UPDATE public.credits
            SET balance = ROUND(balance - v_cost, 9)
            WHERE user_id = p_user_id;
        END IF;
    ELSE
        -- Handle billing based on pricing model
        CASE v_pricing_model
            WHEN 'credits' THEN
                UPDATE public.credits
                SET balance = ROUND(balance - v_cost, 9)
                WHERE user_id = p_user_id;
                
            WHEN 'subscription' THEN
                UPDATE public.quotas q
                SET used_amount = ROUND(used_amount + v_session_duration_minutes, 9)
                FROM public.user_subscriptions us
                WHERE q.subscription_id = us.id
                AND us.user_id = p_user_id
                AND q.service = 'transcription'
                AND us.status = 'active'
                AND q.reset_date > NOW();
                
            WHEN 'payg' THEN
                INSERT INTO public.payg_usage (
                    user_id, 
                    month, 
                    total_amount, 
                    payment_status
                ) VALUES (
                    p_user_id, 
                    date_trunc('month', CURRENT_DATE), 
                    v_cost,
                    'pending'
                )
                ON CONFLICT (user_id, month) 
                DO UPDATE SET 
                    total_amount = ROUND(payg_usage.total_amount + EXCLUDED.total_amount, 9),
                    payment_status = 
                        CASE 
                            WHEN payg_usage.payment_status = 'paid' THEN 'pending'
                            ELSE payg_usage.payment_status
                        END;
        END CASE;
    END IF;
END;
$$;


ALTER FUNCTION "public"."finalize_realtime_session"("p_user_id" "uuid", "p_api_key_id" "uuid", "p_model" "text", "p_session_id" "text", "p_start_time" bigint, "p_audio_bytes_sent" bigint, "p_has_transcription" boolean, "p_token_info" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."finalize_usage"("p_user_id" "uuid", "p_service" "text", "p_model" "text", "p_amount" numeric, "p_cost" numeric, "p_pricing_model" "text", "p_metadata" "jsonb") RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    input_token_count INTEGER;
    output_token_count INTEGER;
    input_cost_per_token DECIMAL(18, 9);
    output_cost_per_token DECIMAL(18, 9);
    total_token_cost DECIMAL(18, 9);
    is_token_based BOOLEAN;
    pricing_unit TEXT;
    user_balance DECIMAL(18, 9);
    usage_id UUID;
    credit_shortfall DECIMAL(18, 9);
BEGIN
    -- Check if this is a token-based model by looking at the unit
    SELECT sp.unit INTO pricing_unit
    FROM public.service_pricing sp
    WHERE sp.service = p_service
    AND sp.model = p_model
    AND sp.is_active = TRUE;
    
    -- Set token-based flag
    is_token_based := pricing_unit = 'token-based';
    
    -- Handle token-based billing for GPT-4o transcription models
    IF is_token_based AND p_service = 'transcription' AND (
        p_model = 'gpt-4o-mini-transcribe' OR 
        p_model = 'gpt-4o-transcribe'
    ) THEN
        -- Extract token counts from metadata
        input_token_count := COALESCE((p_metadata->>'input_tokens')::INTEGER, 0);
        output_token_count := COALESCE((p_metadata->>'output_tokens')::INTEGER, 0);
        
        -- Get token costs for this model
        SELECT sp.cost_per_unit INTO input_cost_per_token
        FROM public.service_pricing sp
        WHERE sp.service = 'transcription'
        AND sp.model = p_model || '/input'
        AND sp.is_active = true;
        
        SELECT sp.cost_per_unit INTO output_cost_per_token
        FROM public.service_pricing sp
        WHERE sp.service = 'transcription'
        AND sp.model = p_model || '/output'
        AND sp.is_active = true;
        
        -- Calculate total cost based on token usage
        total_token_cost := (input_token_count * input_cost_per_token) + 
                            (output_token_count * output_cost_per_token);
        
        -- Update usage history with token information and calculated cost
        UPDATE public.usage_history
        SET status = 'success',
            amount = p_amount, -- Original amount (duration) for compatibility
            cost = ROUND(total_token_cost, 9),
            metadata = p_metadata || jsonb_build_object(
                'token_based', true,
                'input_tokens', input_token_count,
                'output_tokens', output_token_count,
                'input_cost', ROUND(input_token_count * input_cost_per_token, 9),
                'output_cost', ROUND(output_token_count * output_cost_per_token, 9)
            )
        WHERE id = (
            SELECT id 
            FROM public.usage_history 
            WHERE user_id = p_user_id
            AND service = p_service
            AND model = p_model
            AND status = 'pending'
            ORDER BY created_at DESC
            LIMIT 1
        )
        RETURNING id INTO usage_id;
        
        -- Handle credit-based billing with potential negative balance tracking
        IF p_pricing_model = 'credits' THEN
            -- Get current user balance
            SELECT balance INTO user_balance
            FROM public.credits
            WHERE user_id = p_user_id;
            
            -- Check if the cost exceeds available balance (negative balance case)
            IF user_balance < total_token_cost THEN
                -- Calculate the shortfall
                credit_shortfall := total_token_cost - user_balance;
                
                -- Set balance to zero
                UPDATE public.credits
                SET balance = 0
                WHERE user_id = p_user_id;
                
                -- Create negative balance record
                INSERT INTO public.negative_balances (
                    user_id,
                    service,
                    model,
                    amount,
                    usage_id
                ) VALUES (
                    p_user_id,
                    p_service,
                    p_model,
                    credit_shortfall,
                    usage_id
                );
            ELSE
                -- Normal case - sufficient balance
                UPDATE public.credits
                SET balance = ROUND(balance - total_token_cost, 9)
                WHERE user_id = p_user_id;
            END IF;
        END IF;
        
        -- Handle PAYG usage recording
        IF p_pricing_model = 'payg' THEN
            INSERT INTO public.payg_usage (
                user_id, 
                month, 
                total_amount, 
                payment_status
            ) VALUES (
                p_user_id, 
                date_trunc('month', CURRENT_DATE), 
                total_token_cost,
                'pending'
            )
            ON CONFLICT (user_id, month) 
            DO UPDATE SET 
                total_amount = ROUND(payg_usage.total_amount + EXCLUDED.total_amount, 9),
                payment_status = 
                    -- Only change to pending if it was previously paid
                    CASE 
                        WHEN payg_usage.payment_status = 'paid' THEN 'pending'
                        ELSE payg_usage.payment_status
                    END;
        END IF;
    ELSE
        -- Use standard logic for non-token-based models
        -- Update usage history to success using a subquery to handle ORDER BY and LIMIT
        UPDATE public.usage_history
        SET status = 'success',
            amount = p_amount,
            cost = ROUND(p_cost, 9),
            metadata = p_metadata
        WHERE id = (
            SELECT id 
            FROM public.usage_history 
            WHERE user_id = p_user_id
            AND service = p_service
            AND model = p_model
            AND status = 'pending'
            ORDER BY created_at DESC
            LIMIT 1
        )
        RETURNING id INTO usage_id;

        -- Update credits if using credits pricing model
        IF p_pricing_model = 'credits' THEN
            UPDATE public.credits
            SET balance = ROUND(balance - p_cost, 9)
            WHERE user_id = p_user_id;
        END IF;

        -- Update quota if using subscription
        IF p_pricing_model = 'subscription' THEN
            UPDATE public.quotas q
            SET used_amount = ROUND(used_amount + p_amount, 9)
            FROM public.user_subscriptions us
            WHERE q.subscription_id = us.id
            AND us.user_id = p_user_id
            AND q.service = p_service
            AND us.status = 'active'
            AND q.reset_date > NOW();
        END IF;

        -- Handle PAYG usage recording with new payment_status
        IF p_pricing_model = 'payg' THEN
            INSERT INTO public.payg_usage (
                user_id, 
                month, 
                total_amount, 
                payment_status
            ) VALUES (
                p_user_id, 
                date_trunc('month', CURRENT_DATE), 
                p_cost,
                'pending'
            )
            ON CONFLICT (user_id, month) 
            DO UPDATE SET 
                total_amount = ROUND(payg_usage.total_amount + EXCLUDED.total_amount, 9),
                payment_status = 
                    -- Only change to pending if it was previously paid
                    CASE 
                        WHEN payg_usage.payment_status = 'paid' THEN 'pending'
                        ELSE payg_usage.payment_status
                    END;
        END IF;
    END IF;
END;
$$;


ALTER FUNCTION "public"."finalize_usage"("p_user_id" "uuid", "p_service" "text", "p_model" "text", "p_amount" numeric, "p_cost" numeric, "p_pricing_model" "text", "p_metadata" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_unpaid_payg_balances"("p_user_id" "uuid") RETURNS TABLE("id" "uuid", "month" "date", "total_amount" numeric, "payment_status" "public"."payment_status", "payment_metadata" "jsonb", "created_at" timestamp with time zone)
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pu.id,
        pu.month,
        pu.total_amount,
        pu.payment_status,
        pu.payment_metadata,
        pu.created_at
    FROM 
        public.payg_usage pu
    WHERE 
        pu.user_id = p_user_id
        AND pu.payment_status = 'unpaid'
        AND pu.month < date_trunc('month', CURRENT_DATE)
    ORDER BY 
        pu.month DESC;
END;
$$;


ALTER FUNCTION "public"."get_unpaid_payg_balances"("p_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_usage_summary_statistics"("p_user_id" "text", "p_start_date" timestamp with time zone DEFAULT NULL::timestamp with time zone, "p_end_date" timestamp with time zone DEFAULT NULL::timestamp with time zone, "p_service" "text" DEFAULT NULL::"text", "p_model" "text" DEFAULT NULL::"text", "p_status" "text" DEFAULT NULL::"text") RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $_$
DECLARE
  v_total_cost NUMERIC;
  v_service_breakdown JSON;
  v_model_breakdown JSON;
  v_available_services TEXT[];
  v_available_models TEXT[];
  v_result JSON;
  v_query TEXT;
BEGIN
  -- Build the query directly without dynamic parameters
  v_query := 'WITH usage_summary AS (
    SELECT 
      service,
      model,
      SUM(cost) as total_cost_per_group
    FROM 
      usage_history
    WHERE 
      user_id = $1::uuid';
  
  -- Add filters with fixed parameter positions
  IF p_start_date IS NOT NULL THEN
    v_query := v_query || ' AND created_at >= $2';
  END IF;
  
  IF p_end_date IS NOT NULL THEN
    v_query := v_query || ' AND created_at <= $3';
  END IF;
  
  IF p_service IS NOT NULL THEN
    v_query := v_query || ' AND service = $4';
  END IF;
  
  IF p_model IS NOT NULL THEN
    v_query := v_query || ' AND model = $5';
  END IF;
  
  IF p_status IS NOT NULL THEN
    v_query := v_query || ' AND status = $6';
  ELSE
    -- Only include successful entries when calculating costs
    v_query := v_query || ' AND status = ''success''';
  END IF;
  
  -- Complete the query with proper grouping and aggregation
  v_query := v_query || ' GROUP BY service, model)
  SELECT 
    COALESCE(SUM(total_cost_per_group), 0) AS total_cost,
    COALESCE(
      jsonb_object_agg(service, total_cost_per_group) FILTER (WHERE service IS NOT NULL),
      ''{}''::jsonb
    ) AS service_breakdown,
    COALESCE(
      jsonb_object_agg(model, total_cost_per_group) FILTER (WHERE model IS NOT NULL),
      ''{}''::jsonb
    ) AS model_breakdown,
    ARRAY_AGG(DISTINCT service) FILTER (WHERE service IS NOT NULL) AS available_services,
    ARRAY_AGG(DISTINCT model) FILTER (WHERE model IS NOT NULL) AS available_models
  FROM usage_summary';
  
  -- Execute the query with parameters
  EXECUTE v_query 
  INTO v_total_cost, v_service_breakdown, v_model_breakdown, v_available_services, v_available_models 
  USING 
    p_user_id,
    p_start_date,
    p_end_date,
    p_service,
    p_model,
    p_status;
  
  -- Build the result JSON
  v_result := json_build_object(
    'total_cost', v_total_cost,
    'service_breakdown', v_service_breakdown,
    'model_breakdown', v_model_breakdown,
    'available_services', v_available_services,
    'available_models', v_available_models
  );
  
  RETURN v_result;
END;
$_$;


ALTER FUNCTION "public"."get_usage_summary_statistics"("p_user_id" "text", "p_start_date" timestamp with time zone, "p_end_date" timestamp with time zone, "p_service" "text", "p_model" "text", "p_status" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."handle_new_user"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_profile_id UUID;
    v_subscription_id UUID;
    v_plan_id UUID;
    v_current_period_start TIMESTAMPTZ;
    v_current_period_end TIMESTAMPTZ;
    v_trial_subscription_id TEXT;
BEGIN
    -- Create a profile for the new user
    INSERT INTO public.profiles (id, email, full_name, created_at, updated_at)
    VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'full_name', NOW(), NOW())
    RETURNING id INTO v_profile_id;
    
    -- Get the Free Trial plan ID
    SELECT id INTO v_plan_id FROM public.subscription_plans WHERE name = 'Free Trial';
    
    -- Set subscription period (1 month from now)
    v_current_period_start := NOW();
    v_current_period_end := v_current_period_start + INTERVAL '1 month';
    
    -- Generate a placeholder for stripe_subscription_id
    v_trial_subscription_id := 'trial_' || NEW.id;
    
    -- Create a subscription for the new user
    INSERT INTO public.user_subscriptions (
        user_id,
        plan_id,
        status,
        current_period_start,
        current_period_end,
        stripe_subscription_id,
        created_at
    ) VALUES (
        NEW.id,
        v_plan_id,
        'active',
        v_current_period_start,
        v_current_period_end,
        v_trial_subscription_id,
        NOW()
    ) RETURNING id INTO v_subscription_id;
    
    -- Create initial quotas for the new user linked to the subscription
    -- Transcription minutes
    INSERT INTO public.quotas (
        user_id, 
        subscription_id, 
        service, 
        used_amount, 
        total_amount, 
        reset_date, 
        created_at
    ) VALUES (
        NEW.id, 
        v_subscription_id, 
        'transcription', 
        0, 
        10, 
        v_current_period_end, 
        NOW()
    );
    
    -- Tokens
    INSERT INTO public.quotas (
        user_id, 
        subscription_id, 
        service, 
        used_amount, 
        total_amount, 
        reset_date, 
        created_at
    ) VALUES (
        NEW.id, 
        v_subscription_id, 
        'optimization', 
        0, 
        10000, 
        v_current_period_end, 
        NOW()
    );
    
    
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."handle_new_user"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."has_unpaid_payg_balance"("p_user_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    has_unpaid BOOLEAN;
BEGIN
    -- Check if there are any unpaid PAYG balances from previous months
    SELECT EXISTS (
        SELECT 1 
        FROM public.payg_usage 
        WHERE user_id = p_user_id 
        AND payment_status = 'unpaid'
        AND month < date_trunc('month', CURRENT_DATE)
    ) INTO has_unpaid;
    
    RETURN has_unpaid;
END;
$$;


ALTER FUNCTION "public"."has_unpaid_payg_balance"("p_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."mark_payg_payment_as_paid"("p_payg_id" "uuid", "p_payment_metadata" "jsonb" DEFAULT NULL::"jsonb") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    updated_count INTEGER;
BEGIN
    UPDATE public.payg_usage
    SET payment_status = 'paid',
        payment_metadata = COALESCE(p_payment_metadata, payment_metadata)
    WHERE id = p_payg_id
    AND payment_status != 'paid';
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    
    RETURN updated_count > 0;
END;
$$;


ALTER FUNCTION "public"."mark_payg_payment_as_paid"("p_payg_id" "uuid", "p_payment_metadata" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."mark_payg_payment_as_unpaid"("p_payg_id" "uuid", "p_failure_reason" "text", "p_payment_metadata" "jsonb" DEFAULT NULL::"jsonb") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    updated_count INTEGER;
    combined_metadata JSONB;
BEGIN
    -- Combine existing metadata with failure reason and new metadata
    SELECT 
        COALESCE(payment_metadata, '{}'::JSONB) || 
        jsonb_build_object('failure_reason', p_failure_reason) ||
        COALESCE(p_payment_metadata, '{}'::JSONB)
    INTO combined_metadata
    FROM public.payg_usage
    WHERE id = p_payg_id;
    
    -- Update the record
    UPDATE public.payg_usage
    SET payment_status = 'unpaid',
        payment_metadata = combined_metadata
    WHERE id = p_payg_id
    AND payment_status != 'unpaid';
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    
    RETURN updated_count > 0;
END;
$$;


ALTER FUNCTION "public"."mark_payg_payment_as_unpaid"("p_payg_id" "uuid", "p_failure_reason" "text", "p_payment_metadata" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_api_key_name"("p_api_key_id" "uuid", "p_name" "text") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_user_id UUID;
    v_count INTEGER;
BEGIN
    -- Get the current user's ID
    v_user_id := auth.uid();
    
    -- Check if user is authenticated
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'Not authenticated';
    END IF;
    
    -- Check if the API key belongs to the user
    SELECT COUNT(*) INTO v_count 
    FROM public.api_keys 
    WHERE id = p_api_key_id AND user_id = v_user_id;
    
    IF v_count = 0 THEN
        RAISE EXCEPTION 'API key not found or access denied';
    END IF;
    
    -- Update only the name field
    UPDATE public.api_keys
    SET name = p_name
    WHERE id = p_api_key_id;
    
    RETURN TRUE;
END;
$$;


ALTER FUNCTION "public"."update_api_key_name"("p_api_key_id" "uuid", "p_name" "text") OWNER TO "postgres";


COMMENT ON FUNCTION "public"."update_api_key_name"("p_api_key_id" "uuid", "p_name" "text") IS 'Updates only the name of an API key owned by the authenticated user';



CREATE OR REPLACE FUNCTION "public"."update_api_key_status"("p_key_id" "uuid", "p_is_active" boolean) RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_user_id UUID;
    v_key_exists BOOLEAN;
BEGIN
    -- Get the current user's ID
    v_user_id := auth.uid();
    
    -- Check if user is authenticated
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'Not authenticated';
    END IF;
    
    -- Check if the API key exists and belongs to the current user
    SELECT EXISTS (
        SELECT 1 
        FROM public.api_keys 
        WHERE id = p_key_id AND user_id = v_user_id
    ) INTO v_key_exists;
    
    IF NOT v_key_exists THEN
        RAISE EXCEPTION 'API key not found or does not belong to the current user';
    END IF;
    
    -- Update the API key status
    UPDATE public.api_keys
    SET is_active = p_is_active
    WHERE id = p_key_id AND user_id = v_user_id;
    
    RETURN TRUE;
END;
$$;


ALTER FUNCTION "public"."update_api_key_status"("p_key_id" "uuid", "p_is_active" boolean) OWNER TO "postgres";


COMMENT ON FUNCTION "public"."update_api_key_status"("p_key_id" "uuid", "p_is_active" boolean) IS 'Updates the active status of an API key for the authenticated user';



CREATE OR REPLACE FUNCTION "public"."validate_api_key"("p_key" "text") RETURNS TABLE("user_id" "uuid", "api_key_id" "uuid")
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_key_prefix TEXT;
    v_actual_key TEXT;
BEGIN
    -- Check if the key has the vhkey_ prefix and remove it if present
    IF starts_with(p_key, 'vhkey_') THEN
        v_actual_key := substring(p_key from 7); -- Remove 'vhkey_' prefix
    ELSE
        v_actual_key := p_key;
    END IF;
    
    -- Extract prefix (first 8 characters of the actual key)
    v_key_prefix := substring(v_actual_key from 1 for 8);
    
    -- Update last used time and return user_id if key is valid
    -- Use crypt() to verify the hash, which is how the key was created
    UPDATE public.api_keys
    SET last_used_at = NOW()
    WHERE key_prefix = v_key_prefix
    AND crypt(v_actual_key, key_hash) = key_hash
    AND is_active = TRUE
    AND (expires_at IS NULL OR expires_at > NOW());
    
    -- Return user_id if key is valid
    RETURN QUERY
    SELECT a.user_id, a.id
    FROM public.api_keys a
    WHERE a.key_prefix = v_key_prefix
    AND crypt(v_actual_key, a.key_hash) = a.key_hash
    AND a.is_active = TRUE
    AND (a.expires_at IS NULL OR a.expires_at > NOW());
END;
$$;


ALTER FUNCTION "public"."validate_api_key"("p_key" "text") OWNER TO "postgres";


COMMENT ON FUNCTION "public"."validate_api_key"("p_key" "text") IS 'Validates an API key and returns the associated user_id and api_key_id if valid. Handles keys with or without the vhkey_ prefix.';


SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."api_keys" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "name" "text" NOT NULL,
    "key_prefix" "text" NOT NULL,
    "key_hash" "text" NOT NULL,
    "allow_credits" boolean DEFAULT true,
    "allow_payg" boolean DEFAULT false,
    "allow_subscriptions" boolean DEFAULT false,
    "is_active" boolean DEFAULT true,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "expires_at" timestamp with time zone,
    "last_used_at" timestamp with time zone
);


ALTER TABLE "public"."api_keys" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."credits" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "balance" numeric(18,9) DEFAULT 0 NOT NULL,
    "currency" "text" DEFAULT 'USD'::"text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "expires_at" timestamp with time zone
);


ALTER TABLE "public"."credits" OWNER TO "postgres";


COMMENT ON COLUMN "public"."credits"."expires_at" IS 'Date when the credits expire. If NULL, credits do not expire.';



CREATE TABLE IF NOT EXISTS "public"."negative_balances" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "service" "text" NOT NULL,
    "model" "text" NOT NULL,
    "amount" numeric(18,9) NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "cleared_at" timestamp with time zone,
    "usage_id" "uuid",
    CONSTRAINT "negative_balances_service_check" CHECK (("service" = ANY (ARRAY['transcription'::"text", 'optimization'::"text"])))
);


ALTER TABLE "public"."negative_balances" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."payg_usage" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "month" "date" NOT NULL,
    "total_amount" numeric(18,9) DEFAULT 0 NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "payment_status" "public"."payment_status" DEFAULT 'pending'::"public"."payment_status",
    "payment_metadata" "jsonb"
);


ALTER TABLE "public"."payg_usage" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."pricing_models" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "name" "text" NOT NULL,
    "type" "text" NOT NULL,
    "description" "text",
    "priority" smallint DEFAULT 1 NOT NULL,
    "is_active" boolean DEFAULT true,
    "created_at" timestamp with time zone DEFAULT "now"(),
    CONSTRAINT "pricing_models_type_check" CHECK (("type" = ANY (ARRAY['payg'::"text", 'credits'::"text", 'subscription'::"text"])))
);


ALTER TABLE "public"."pricing_models" OWNER TO "postgres";


COMMENT ON TABLE "public"."pricing_models" IS 'Reference table for pricing models. Modifications should be done via migrations or by superusers directly.';



CREATE TABLE IF NOT EXISTS "public"."profiles" (
    "id" "uuid" NOT NULL,
    "email" "text" NOT NULL,
    "full_name" "text",
    "company_name" "text",
    "stripe_customer_id" "text",
    "default_pricing_model" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    CONSTRAINT "profiles_default_pricing_model_check" CHECK (("default_pricing_model" = ANY (ARRAY['credits'::"text", 'payg'::"text", 'subscription'::"text"])))
);


ALTER TABLE "public"."profiles" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."quotas" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "subscription_id" "uuid",
    "service" "text" NOT NULL,
    "used_amount" numeric(18,9) DEFAULT 0 NOT NULL,
    "total_amount" numeric(18,9) NOT NULL,
    "reset_date" timestamp with time zone NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    CONSTRAINT "quotas_service_check" CHECK (("service" = ANY (ARRAY['transcription'::"text", 'optimization'::"text", 'tokens'::"text"])))
);


ALTER TABLE "public"."quotas" OWNER TO "postgres";


COMMENT ON TABLE "public"."quotas" IS 'Stores quota information for users. used_amount and total_amount are stored as numeric(18,9) to allow for precise tracking of transcription minutes.';



CREATE TABLE IF NOT EXISTS "public"."service_pricing" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "service" "text" NOT NULL,
    "model" "text" NOT NULL,
    "cost_per_unit" numeric(18,9) NOT NULL,
    "unit" "text" NOT NULL,
    "is_active" boolean DEFAULT true,
    "created_at" timestamp with time zone DEFAULT "now"(),
    CONSTRAINT "service_pricing_service_check" CHECK (("service" = ANY (ARRAY['transcription'::"text", 'optimization'::"text"])))
);


ALTER TABLE "public"."service_pricing" OWNER TO "postgres";


COMMENT ON TABLE "public"."service_pricing" IS 'Reference table for service pricing. Modifications should be done via migrations or by superusers directly.';



CREATE TABLE IF NOT EXISTS "public"."subscription_plans" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "name" "text" NOT NULL,
    "description" "text",
    "monthly_price" numeric(12,2) NOT NULL,
    "annual_price" numeric(12,2),
    "transcription_minutes" integer NOT NULL,
    "is_active" boolean DEFAULT true,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "tokens" integer NOT NULL
);


ALTER TABLE "public"."subscription_plans" OWNER TO "postgres";


COMMENT ON TABLE "public"."subscription_plans" IS 'Reference table for subscription plans. Modifications should be done via migrations or by superusers directly.';



CREATE TABLE IF NOT EXISTS "public"."usage_history" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "api_key_id" "uuid",
    "service" "text" NOT NULL,
    "model" "text" NOT NULL,
    "amount" numeric(18,9) NOT NULL,
    "cost" numeric(18,9) NOT NULL,
    "pricing_model" "text" NOT NULL,
    "status" "text" NOT NULL,
    "metadata" "jsonb",
    "created_at" timestamp with time zone DEFAULT "now"(),
    CONSTRAINT "usage_history_pricing_model_check" CHECK (("pricing_model" = ANY (ARRAY['credits'::"text", 'payg'::"text", 'subscription'::"text"]))),
    CONSTRAINT "usage_history_service_check" CHECK (("service" = ANY (ARRAY['transcription'::"text", 'optimization'::"text"]))),
    CONSTRAINT "usage_history_status_check" CHECK (("status" = ANY (ARRAY['pending'::"text", 'success'::"text", 'failed'::"text"])))
);


ALTER TABLE "public"."usage_history" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."user_subscriptions" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "plan_id" "uuid" NOT NULL,
    "status" "text" NOT NULL,
    "current_period_start" timestamp with time zone NOT NULL,
    "current_period_end" timestamp with time zone NOT NULL,
    "stripe_subscription_id" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    CONSTRAINT "user_subscriptions_status_check" CHECK (("status" = ANY (ARRAY['active'::"text", 'canceled'::"text", 'paused'::"text"])))
);


ALTER TABLE "public"."user_subscriptions" OWNER TO "postgres";


ALTER TABLE ONLY "public"."api_keys"
    ADD CONSTRAINT "api_keys_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."credits"
    ADD CONSTRAINT "credits_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."negative_balances"
    ADD CONSTRAINT "negative_balances_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."payg_usage"
    ADD CONSTRAINT "payg_usage_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."payg_usage"
    ADD CONSTRAINT "payg_usage_user_month_key" UNIQUE ("user_id", "month");



ALTER TABLE ONLY "public"."pricing_models"
    ADD CONSTRAINT "pricing_models_name_key" UNIQUE ("name");



ALTER TABLE ONLY "public"."pricing_models"
    ADD CONSTRAINT "pricing_models_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."profiles"
    ADD CONSTRAINT "profiles_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."quotas"
    ADD CONSTRAINT "quotas_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."service_pricing"
    ADD CONSTRAINT "service_pricing_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."service_pricing"
    ADD CONSTRAINT "service_pricing_service_model_key" UNIQUE ("service", "model");



ALTER TABLE ONLY "public"."subscription_plans"
    ADD CONSTRAINT "subscription_plans_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."usage_history"
    ADD CONSTRAINT "usage_history_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_subscriptions"
    ADD CONSTRAINT "user_subscriptions_pkey" PRIMARY KEY ("id");



CREATE INDEX "idx_api_keys_allow_payg" ON "public"."api_keys" USING "btree" ("allow_payg") WHERE ("allow_payg" = true);



CREATE INDEX "idx_api_keys_user" ON "public"."api_keys" USING "btree" ("user_id");



CREATE INDEX "idx_credits_user" ON "public"."credits" USING "btree" ("user_id");



CREATE INDEX "idx_negative_balances_cleared_at" ON "public"."negative_balances" USING "btree" ("cleared_at") WHERE ("cleared_at" IS NULL);



CREATE INDEX "idx_negative_balances_user_id" ON "public"."negative_balances" USING "btree" ("user_id");



CREATE INDEX "idx_payg_usage_month" ON "public"."payg_usage" USING "btree" ("month");



CREATE INDEX "idx_quotas_reset_date" ON "public"."quotas" USING "btree" ("reset_date");



CREATE INDEX "idx_service_pricing_lookup" ON "public"."service_pricing" USING "btree" ("service", "model");



CREATE INDEX "idx_subscriptions_user" ON "public"."user_subscriptions" USING "btree" ("user_id");



CREATE INDEX "idx_usage_history_status" ON "public"."usage_history" USING "btree" ("status");



CREATE INDEX "idx_usage_history_user_id" ON "public"."usage_history" USING "btree" ("user_id");



CREATE OR REPLACE TRIGGER "apply_negative_balances_trigger" BEFORE UPDATE OF "balance" ON "public"."credits" FOR EACH ROW WHEN (("new"."balance" > "old"."balance")) EXECUTE FUNCTION "public"."apply_negative_balances"();



CREATE OR REPLACE TRIGGER "enforce_api_key_limit" BEFORE INSERT ON "public"."api_keys" FOR EACH ROW EXECUTE FUNCTION "public"."check_api_key_limit"();



ALTER TABLE ONLY "public"."api_keys"
    ADD CONSTRAINT "api_keys_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."credits"
    ADD CONSTRAINT "credits_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."negative_balances"
    ADD CONSTRAINT "negative_balances_usage_id_fkey" FOREIGN KEY ("usage_id") REFERENCES "public"."usage_history"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."negative_balances"
    ADD CONSTRAINT "negative_balances_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."payg_usage"
    ADD CONSTRAINT "payg_usage_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."profiles"
    ADD CONSTRAINT "profiles_id_fkey" FOREIGN KEY ("id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."quotas"
    ADD CONSTRAINT "quotas_subscription_id_fkey" FOREIGN KEY ("subscription_id") REFERENCES "public"."user_subscriptions"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."quotas"
    ADD CONSTRAINT "quotas_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."usage_history"
    ADD CONSTRAINT "usage_history_api_key_id_fkey" FOREIGN KEY ("api_key_id") REFERENCES "public"."api_keys"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."usage_history"
    ADD CONSTRAINT "usage_history_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_subscriptions"
    ADD CONSTRAINT "user_subscriptions_plan_id_fkey" FOREIGN KEY ("plan_id") REFERENCES "public"."subscription_plans"("id");



ALTER TABLE ONLY "public"."user_subscriptions"
    ADD CONSTRAINT "user_subscriptions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;



CREATE POLICY "API Keys - Delete own" ON "public"."api_keys" FOR DELETE USING (("auth"."uid"() = "user_id"));



CREATE POLICY "API Keys - No direct inserts" ON "public"."api_keys" FOR INSERT WITH CHECK (false);



CREATE POLICY "API Keys - No direct updates" ON "public"."api_keys" FOR UPDATE USING (false);



CREATE POLICY "API Keys - Select own" ON "public"."api_keys" FOR SELECT USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Credits - Select own" ON "public"."credits" FOR SELECT USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Negative Balances - Select own" ON "public"."negative_balances" FOR SELECT USING (("auth"."uid"() = "user_id"));



CREATE POLICY "PAYG - Select own" ON "public"."payg_usage" FOR SELECT USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Pricing Models - No delete" ON "public"."pricing_models" FOR DELETE USING (false);



CREATE POLICY "Pricing Models - No insert" ON "public"."pricing_models" FOR INSERT WITH CHECK (false);



CREATE POLICY "Pricing Models - No update" ON "public"."pricing_models" FOR UPDATE USING (false);



CREATE POLICY "Pricing Models - Public read" ON "public"."pricing_models" FOR SELECT USING (true);



CREATE POLICY "Profiles - Select own" ON "public"."profiles" FOR SELECT USING (("auth"."uid"() = "id"));



CREATE POLICY "Profiles - Update own" ON "public"."profiles" FOR UPDATE USING (("auth"."uid"() = "id"));



CREATE POLICY "Quotas - Select own" ON "public"."quotas" FOR SELECT USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Service Pricing - No delete" ON "public"."service_pricing" FOR DELETE USING (false);



CREATE POLICY "Service Pricing - No insert" ON "public"."service_pricing" FOR INSERT WITH CHECK (false);



CREATE POLICY "Service Pricing - No update" ON "public"."service_pricing" FOR UPDATE USING (false);



CREATE POLICY "Service Pricing - Public read" ON "public"."service_pricing" FOR SELECT USING (true);



CREATE POLICY "Subscription Plans - No delete" ON "public"."subscription_plans" FOR DELETE USING (false);



CREATE POLICY "Subscription Plans - No insert" ON "public"."subscription_plans" FOR INSERT WITH CHECK (false);



CREATE POLICY "Subscription Plans - No update" ON "public"."subscription_plans" FOR UPDATE USING (false);



CREATE POLICY "Subscription Plans - Public read" ON "public"."subscription_plans" FOR SELECT USING (true);



CREATE POLICY "Subscriptions - Select own" ON "public"."user_subscriptions" FOR SELECT USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Usage History - Select own" ON "public"."usage_history" FOR SELECT USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can view their own usage history" ON "public"."usage_history" USING (("auth"."uid"() = "user_id"));



ALTER TABLE "public"."api_keys" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."credits" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."negative_balances" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."payg_usage" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."pricing_models" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."profiles" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."quotas" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."service_pricing" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."subscription_plans" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."usage_history" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."user_subscriptions" ENABLE ROW LEVEL SECURITY;




ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";


GRANT USAGE ON SCHEMA "internal" TO "service_role";



GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";




















































































































































































GRANT ALL ON FUNCTION "public"."apply_negative_balances"() TO "service_role";



REVOKE ALL ON FUNCTION "public"."check_api_key_limit"() FROM PUBLIC;
GRANT ALL ON FUNCTION "public"."check_api_key_limit"() TO "service_role";



REVOKE ALL ON FUNCTION "public"."check_optimization_allowance"("p_user_id" "uuid", "p_service" "text", "p_model" "text", "p_amount" numeric, "p_api_key_id" "uuid") FROM PUBLIC;
GRANT ALL ON FUNCTION "public"."check_optimization_allowance"("p_user_id" "uuid", "p_service" "text", "p_model" "text", "p_amount" numeric, "p_api_key_id" "uuid") TO "service_role";



REVOKE ALL ON FUNCTION "public"."check_transcription_allowance"("p_user_id" "uuid", "p_service" "text", "p_model" "text", "p_amount" numeric, "p_api_key_id" "uuid") FROM PUBLIC;
GRANT ALL ON FUNCTION "public"."check_transcription_allowance"("p_user_id" "uuid", "p_service" "text", "p_model" "text", "p_amount" numeric, "p_api_key_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."check_usage_allowance"("p_user_id" "uuid", "p_service" "text", "p_model" "text", "p_amount" numeric, "p_api_key_id" "uuid", "p_is_input_only" boolean) TO "service_role";



REVOKE ALL ON FUNCTION "public"."create_api_key"("p_name" "text", "p_expires_at" timestamp with time zone) FROM PUBLIC;
GRANT ALL ON FUNCTION "public"."create_api_key"("p_name" "text", "p_expires_at" timestamp with time zone) TO "authenticated";



GRANT ALL ON FUNCTION "public"."finalize_realtime_session"("p_user_id" "uuid", "p_api_key_id" "uuid", "p_model" "text", "p_session_id" "text", "p_start_time" bigint, "p_audio_bytes_sent" bigint, "p_has_transcription" boolean, "p_token_info" "jsonb") TO "service_role";



REVOKE ALL ON FUNCTION "public"."finalize_usage"("p_user_id" "uuid", "p_service" "text", "p_model" "text", "p_amount" numeric, "p_cost" numeric, "p_pricing_model" "text", "p_metadata" "jsonb") FROM PUBLIC;
GRANT ALL ON FUNCTION "public"."finalize_usage"("p_user_id" "uuid", "p_service" "text", "p_model" "text", "p_amount" numeric, "p_cost" numeric, "p_pricing_model" "text", "p_metadata" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_unpaid_payg_balances"("p_user_id" "uuid") TO "service_role";



REVOKE ALL ON FUNCTION "public"."get_usage_summary_statistics"("p_user_id" "text", "p_start_date" timestamp with time zone, "p_end_date" timestamp with time zone, "p_service" "text", "p_model" "text", "p_status" "text") FROM PUBLIC;
GRANT ALL ON FUNCTION "public"."get_usage_summary_statistics"("p_user_id" "text", "p_start_date" timestamp with time zone, "p_end_date" timestamp with time zone, "p_service" "text", "p_model" "text", "p_status" "text") TO "authenticated";



REVOKE ALL ON FUNCTION "public"."handle_new_user"() FROM PUBLIC;
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "service_role";



REVOKE ALL ON FUNCTION "public"."has_unpaid_payg_balance"("p_user_id" "uuid") FROM PUBLIC;
GRANT ALL ON FUNCTION "public"."has_unpaid_payg_balance"("p_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."mark_payg_payment_as_paid"("p_payg_id" "uuid", "p_payment_metadata" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."mark_payg_payment_as_unpaid"("p_payg_id" "uuid", "p_failure_reason" "text", "p_payment_metadata" "jsonb") TO "service_role";



REVOKE ALL ON FUNCTION "public"."update_api_key_name"("p_api_key_id" "uuid", "p_name" "text") FROM PUBLIC;
GRANT ALL ON FUNCTION "public"."update_api_key_name"("p_api_key_id" "uuid", "p_name" "text") TO "service_role";



REVOKE ALL ON FUNCTION "public"."update_api_key_status"("p_key_id" "uuid", "p_is_active" boolean) FROM PUBLIC;
GRANT ALL ON FUNCTION "public"."update_api_key_status"("p_key_id" "uuid", "p_is_active" boolean) TO "service_role";
GRANT ALL ON FUNCTION "public"."update_api_key_status"("p_key_id" "uuid", "p_is_active" boolean) TO "authenticated";



REVOKE ALL ON FUNCTION "public"."validate_api_key"("p_key" "text") FROM PUBLIC;
GRANT ALL ON FUNCTION "public"."validate_api_key"("p_key" "text") TO "service_role";


















GRANT ALL ON TABLE "public"."api_keys" TO "anon";
GRANT ALL ON TABLE "public"."api_keys" TO "authenticated";
GRANT ALL ON TABLE "public"."api_keys" TO "service_role";



GRANT ALL ON TABLE "public"."credits" TO "anon";
GRANT ALL ON TABLE "public"."credits" TO "authenticated";
GRANT ALL ON TABLE "public"."credits" TO "service_role";



GRANT ALL ON TABLE "public"."negative_balances" TO "anon";
GRANT ALL ON TABLE "public"."negative_balances" TO "authenticated";
GRANT ALL ON TABLE "public"."negative_balances" TO "service_role";



GRANT ALL ON TABLE "public"."payg_usage" TO "anon";
GRANT ALL ON TABLE "public"."payg_usage" TO "authenticated";
GRANT ALL ON TABLE "public"."payg_usage" TO "service_role";



GRANT ALL ON TABLE "public"."pricing_models" TO "anon";
GRANT ALL ON TABLE "public"."pricing_models" TO "authenticated";
GRANT ALL ON TABLE "public"."pricing_models" TO "service_role";



GRANT ALL ON TABLE "public"."profiles" TO "anon";
GRANT ALL ON TABLE "public"."profiles" TO "authenticated";
GRANT ALL ON TABLE "public"."profiles" TO "service_role";



GRANT ALL ON TABLE "public"."quotas" TO "anon";
GRANT ALL ON TABLE "public"."quotas" TO "authenticated";
GRANT ALL ON TABLE "public"."quotas" TO "service_role";



GRANT ALL ON TABLE "public"."service_pricing" TO "anon";
GRANT ALL ON TABLE "public"."service_pricing" TO "authenticated";
GRANT ALL ON TABLE "public"."service_pricing" TO "service_role";



GRANT ALL ON TABLE "public"."subscription_plans" TO "anon";
GRANT ALL ON TABLE "public"."subscription_plans" TO "authenticated";
GRANT ALL ON TABLE "public"."subscription_plans" TO "service_role";



GRANT ALL ON TABLE "public"."usage_history" TO "anon";
GRANT ALL ON TABLE "public"."usage_history" TO "authenticated";
GRANT ALL ON TABLE "public"."usage_history" TO "service_role";



GRANT ALL ON TABLE "public"."user_subscriptions" TO "anon";
GRANT ALL ON TABLE "public"."user_subscriptions" TO "authenticated";
GRANT ALL ON TABLE "public"."user_subscriptions" TO "service_role";



ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";






























RESET ALL;

-- Update PAYG and Usage Tracking
BEGIN;

-- Add unique constraint to payg_usage for user_id and month
ALTER TABLE public.payg_usage 
ADD CONSTRAINT payg_usage_user_month_key UNIQUE (user_id, month);

-- Update check_usage_allowance function to include api_key_id and fix decimal precision
CREATE OR REPLACE FUNCTION check_usage_allowance(p_user_id UUID, p_service TEXT, p_model TEXT, p_amount DECIMAL(18,9), p_api_key_id UUID)
RETURNS TABLE (
    can_use BOOLEAN,
    pricing_model TEXT,
    cost DECIMAL(18,9)
) AS $$
DECLARE
    subscription_available BOOLEAN;
    credit_balance DECIMAL(18,9);
    service_cost DECIMAL(18,9);
    cost_per_unit DECIMAL(18,9);
    payg_allowed BOOLEAN;
BEGIN
    -- Get cost per unit first
    SELECT sp.cost_per_unit INTO cost_per_unit
    FROM public.service_pricing sp
    WHERE sp.service = p_service
    AND sp.model = p_model
    AND sp.is_active = TRUE;

    -- Calculate service cost with proper decimal precision
    service_cost := cost_per_unit * p_amount;

    -- Check subscription quota first
    SELECT EXISTS (
        SELECT 1 FROM public.quotas q
        JOIN public.user_subscriptions us ON q.subscription_id = us.id
        WHERE us.user_id = p_user_id
        AND q.service = p_service
        AND (q.used_amount + p_amount) <= q.total_amount
        AND us.status = 'active'
        AND q.reset_date > NOW()
    ) INTO subscription_available;

    IF subscription_available THEN 
        RETURN QUERY SELECT 
            TRUE as can_use,
            'subscription'::TEXT as pricing_model,
            service_cost;
        RETURN;
    END IF;

    -- Check credit balance
    SELECT balance INTO credit_balance
    FROM public.credits
    WHERE user_id = p_user_id;

    IF credit_balance >= service_cost THEN 
        RETURN QUERY SELECT 
            TRUE as can_use,
            'credits'::TEXT as pricing_model,
            service_cost;
        RETURN;
    END IF;

    -- Check if PAYG is allowed for this API key
    SELECT allow_payg INTO payg_allowed
    FROM public.api_keys
    WHERE id = p_api_key_id
    AND is_active = TRUE;

    -- Fallback to PAYG if allowed and user has stripe customer id
    IF payg_allowed AND EXISTS (
        SELECT 1 FROM public.profiles
        WHERE id = p_user_id
        AND stripe_customer_id IS NOT NULL
    ) THEN
        -- No longer inserting into payg_usage here, will be done in finalize_usage
        
        RETURN QUERY SELECT 
            TRUE as can_use,
            'payg'::TEXT as pricing_model,
            service_cost;
        RETURN;
    END IF;

    -- If we get here, no payment method is available
    RETURN QUERY SELECT 
        FALSE as can_use,
        NULL::TEXT as pricing_model,
        service_cost;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create finalize_usage function with proper decimal handling
CREATE OR REPLACE FUNCTION finalize_usage(
    p_user_id UUID,
    p_service TEXT,
    p_model TEXT,
    p_amount DECIMAL(18,9),
    p_cost DECIMAL(18,9),
    p_pricing_model TEXT,
    p_metadata JSONB
) RETURNS VOID AS $$
BEGIN
    -- Update usage history to success using a subquery to handle ORDER BY and LIMIT
    UPDATE public.usage_history
    SET status = 'success',
        amount = p_amount,
        cost = ROUND(p_cost, 9),
        metadata = p_metadata
    WHERE id = (
        SELECT id 
        FROM public.usage_history 
        WHERE user_id = p_user_id
        AND service = p_service
        AND model = p_model
        AND status = 'pending'
        ORDER BY created_at DESC
        LIMIT 1
    );

    -- Update credits if using credits pricing model
    IF p_pricing_model = 'credits' THEN
        UPDATE public.credits
        SET balance = ROUND(balance - p_cost, 9)
        WHERE user_id = p_user_id;
    END IF;

    -- Update quota if using subscription
    IF p_pricing_model = 'subscription' THEN
        UPDATE public.quotas q
        SET used_amount = ROUND(used_amount + p_amount, 9)
        FROM public.user_subscriptions us
        WHERE q.subscription_id = us.id
        AND us.user_id = p_user_id
        AND q.service = p_service
        AND us.status = 'active'
        AND q.reset_date > NOW();
    END IF;

    -- Handle PAYG usage recording
    IF p_pricing_model = 'payg' THEN
        INSERT INTO public.payg_usage (user_id, month, total_amount)
        VALUES (
            p_user_id, 
            date_trunc('month', CURRENT_DATE), 
            p_cost
        )
        ON CONFLICT (user_id, month) 
        DO UPDATE SET total_amount = ROUND(payg_usage.total_amount + EXCLUDED.total_amount, 9);
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add indexes to improve performance
CREATE INDEX IF NOT EXISTS idx_usage_history_status ON public.usage_history(status);
CREATE INDEX IF NOT EXISTS idx_payg_usage_month ON public.payg_usage(month);
CREATE INDEX IF NOT EXISTS idx_api_keys_allow_payg ON public.api_keys(allow_payg) WHERE allow_payg = true;

-- Update decimal precision for relevant columns
ALTER TABLE public.service_pricing ALTER COLUMN cost_per_unit TYPE DECIMAL(18,9);
ALTER TABLE public.credits ALTER COLUMN balance TYPE DECIMAL(18,9);
ALTER TABLE public.usage_history ALTER COLUMN amount TYPE DECIMAL(18,9);
ALTER TABLE public.usage_history ALTER COLUMN cost TYPE DECIMAL(18,9);
ALTER TABLE public.payg_usage ALTER COLUMN total_amount TYPE DECIMAL(18,9);

COMMIT; 
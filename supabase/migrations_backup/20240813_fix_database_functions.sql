-- VoiceHype Fix Database Functions Migration
-- Date: 2024-08-13
BEGIN;

-- First, drop the function wrappers that are causing issues
DROP FUNCTION IF EXISTS public.validate_api_key(text);
DROP FUNCTION IF EXISTS public.check_usage_allowance(uuid, text, text, decimal, uuid);
DROP FUNCTION IF EXISTS public.finalize_usage(uuid, text, text, decimal, decimal, text, jsonb);

-- Remove the pre-request check that might be interfering with edge functions
ALTER ROLE authenticator RESET pgrst.db_pre_request;

-- Restore the original validate_api_key function from the schema
CREATE OR REPLACE FUNCTION validate_api_key(p_key TEXT) 
RETURNS TABLE (
    user_id UUID,
    api_key_id UUID
) AS $$
DECLARE
    v_key_prefix TEXT;
    v_key_hash TEXT;
BEGIN
    -- Extract prefix (format is vhkey_XXXXX)
    v_key_prefix := substring(p_key from 7 for 4);
    
    -- Hash the key
    v_key_hash := encode(digest(p_key, 'sha256'), 'hex');
    
    -- Update last used time
    UPDATE public.api_keys
    SET last_used_at = NOW()
    WHERE key_prefix = v_key_prefix
    AND key_hash = v_key_hash
    AND is_active = TRUE
    AND (expires_at IS NULL OR expires_at > NOW());
    
    -- Return user_id if key is valid
    RETURN QUERY
    SELECT a.user_id, a.id
    FROM public.api_keys a
    WHERE a.key_prefix = v_key_prefix
    AND a.key_hash = v_key_hash
    AND a.is_active = TRUE
    AND (a.expires_at IS NULL OR a.expires_at > NOW());
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Restore the original check_usage_allowance function from the latest migration
CREATE OR REPLACE FUNCTION check_usage_allowance(p_user_id UUID, p_service TEXT, p_model TEXT, p_amount DECIMAL(18,9), p_api_key_id UUID)
RETURNS TABLE (
    can_use BOOLEAN,
    pricing_model TEXT,
    cost DECIMAL(18,9)
) AS $$
DECLARE
    subscription_available BOOLEAN;
    credit_balance DECIMAL(18,9);
    service_cost DECIMAL(18,9);
    cost_per_unit DECIMAL(18,9);
    payg_allowed BOOLEAN;
BEGIN
    -- Get cost per unit first
    SELECT sp.cost_per_unit INTO cost_per_unit
    FROM public.service_pricing sp
    WHERE sp.service = p_service
    AND sp.model = p_model
    AND sp.is_active = TRUE;

    -- Calculate service cost with proper decimal precision
    service_cost := cost_per_unit * p_amount;

    -- Check subscription quota first
    SELECT EXISTS (
        SELECT 1 FROM public.quotas q
        JOIN public.user_subscriptions us ON q.subscription_id = us.id
        WHERE us.user_id = p_user_id
        AND q.service = p_service
        AND (q.used_amount + p_amount) <= q.total_amount
        AND us.status = 'active'
        AND q.reset_date > NOW()
    ) INTO subscription_available;

    IF subscription_available THEN 
        RETURN QUERY SELECT 
            TRUE as can_use,
            'subscription'::TEXT as pricing_model,
            service_cost;
        RETURN;
    END IF;

    -- Check credit balance
    SELECT balance INTO credit_balance
    FROM public.credits
    WHERE user_id = p_user_id;

    IF credit_balance >= service_cost THEN 
        RETURN QUERY SELECT 
            TRUE as can_use,
            'credits'::TEXT as pricing_model,
            service_cost;
        RETURN;
    END IF;

    -- Check if PAYG is allowed for this API key
    SELECT allow_payg INTO payg_allowed
    FROM public.api_keys
    WHERE id = p_api_key_id
    AND is_active = TRUE;

    -- Fallback to PAYG if allowed and user has stripe customer id
    IF payg_allowed AND EXISTS (
        SELECT 1 FROM public.profiles
        WHERE id = p_user_id
        AND stripe_customer_id IS NOT NULL
    ) THEN
        -- No longer inserting into payg_usage here, will be done in finalize_usage
        
        RETURN QUERY SELECT 
            TRUE as can_use,
            'payg'::TEXT as pricing_model,
            service_cost;
        RETURN;
    END IF;

    -- If we get here, no payment method is available
    RETURN QUERY SELECT 
        FALSE as can_use,
        NULL::TEXT as pricing_model,
        service_cost;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Restore the original finalize_usage function from the latest migration
CREATE OR REPLACE FUNCTION finalize_usage(
    p_user_id UUID,
    p_service TEXT,
    p_model TEXT,
    p_amount DECIMAL(18,9),
    p_cost DECIMAL(18,9),
    p_pricing_model TEXT,
    p_metadata JSONB
) RETURNS VOID AS $$
BEGIN
    -- Update usage history to success using a subquery to handle ORDER BY and LIMIT
    UPDATE public.usage_history
    SET status = 'success',
        amount = p_amount,
        cost = ROUND(p_cost, 9),
        metadata = p_metadata
    WHERE id = (
        SELECT id 
        FROM public.usage_history 
        WHERE user_id = p_user_id
        AND service = p_service
        AND model = p_model
        AND status = 'pending'
        ORDER BY created_at DESC
        LIMIT 1
    );

    -- Update credits if using credits pricing model
    IF p_pricing_model = 'credits' THEN
        UPDATE public.credits
        SET balance = ROUND(balance - p_cost, 9)
        WHERE user_id = p_user_id;
    END IF;

    -- Update quota if using subscription
    IF p_pricing_model = 'subscription' THEN
        UPDATE public.quotas q
        SET used_amount = ROUND(used_amount + p_amount, 9)
        FROM public.user_subscriptions us
        WHERE q.subscription_id = us.id
        AND us.user_id = p_user_id
        AND q.service = p_service
        AND us.status = 'active'
        AND q.reset_date > NOW();
    END IF;

    -- Handle PAYG usage recording
    IF p_pricing_model = 'payg' THEN
        INSERT INTO public.payg_usage (user_id, month, total_amount)
        VALUES (
            p_user_id, 
            date_trunc('month', CURRENT_DATE), 
            p_cost
        )
        ON CONFLICT (user_id, month) 
        DO UPDATE SET total_amount = ROUND(payg_usage.total_amount + EXCLUDED.total_amount, 9);
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a safer pre-request check function that doesn't interfere with normal operation
CREATE OR REPLACE FUNCTION public.check_api_requests()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    requested_path text;
    is_authenticated boolean;
BEGIN
    -- Get the requested path
    requested_path := current_setting('request.path', true);
    
    -- Check if user is authenticated
    is_authenticated := (current_setting('request.jwt.claims', true)::json->>'role' IS NOT NULL);
    
    -- Only apply restrictions to direct RPC calls from unauthenticated users
    -- This won't affect edge functions which use the service_role
    IF requested_path LIKE '/rpc/%' AND NOT is_authenticated THEN
        -- Block sensitive functions from being called directly by unauthenticated users
        IF requested_path LIKE '/rpc/validate_api_key' 
           OR requested_path LIKE '/rpc/check_usage_allowance'
           OR requested_path LIKE '/rpc/finalize_usage'
        THEN
            RAISE EXCEPTION 'Access denied: Authentication required';
        END IF;
    END IF;
END;
$$;

-- Register the new pre-request check function
ALTER ROLE authenticator SET pgrst.db_pre_request = 'public.check_api_requests';

-- Reload PostgREST configuration to apply the changes
NOTIFY pgrst, 'reload config';

COMMIT; 
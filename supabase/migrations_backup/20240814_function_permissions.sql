-- VoiceHype Function Permissions Migration
-- Date: 2024-08-14
-- Purpose: Implement proper function permissions using PostgreSQL's native permission system
BEGIN;

-- 1. Remove the custom pre-request check function and its configuration
DROP FUNCTION IF EXISTS public.check_api_requests();
ALTER ROLE authenticator RESET pgrst.db_pre_request;

-- 2. <PERSON>oke execute permissions on all functions in public schema from public, anon, and authenticated roles
-- This ensures that by default, functions cannot be executed by regular users
REVOKE EXECUTE ON ALL FUNCTIONS IN SCHEMA public FROM public;
REVOKE EXECUTE ON ALL FUNCTIONS IN SCHEMA public FROM anon, authenticated;

-- 3. Set default privileges to prevent new functions from being executable by default
ALTER DEFAULT PRIVILEGES IN SCHEMA public 
REVOKE EXECUTE ON FUNCTIONS FROM public;

ALTER DEFAULT PRIVILEGES IN SCHEMA public 
REVOKE EXECUTE ON FUNCTIONS FROM anon, authenticated;

-- 4. Explicitly grant execute permissions on specific functions to service_role
-- These are the functions that should only be callable from edge functions
GRANT EXECUTE ON FUNCTION public.validate_api_key(text) TO service_role;
GRANT EXECUTE ON FUNCTION public.check_usage_allowance(uuid, text, text, numeric, uuid) TO service_role;
GRANT EXECUTE ON FUNCTION public.finalize_usage(uuid, text, text, numeric, numeric, text, jsonb) TO service_role;
GRANT EXECUTE ON FUNCTION public.has_unpaid_payg_balance(uuid) TO service_role;
GRANT EXECUTE ON FUNCTION public.get_unpaid_payg_balances(uuid) TO service_role;

-- 5. Grant execute permissions on utility functions to authenticated users
-- These are functions that regular authenticated users should be able to call directly
-- For example, functions that check user-specific data but don't modify sensitive data
-- Currently we don't have any such functions, but this is where you would add them

-- 6. Grant execute permissions on trigger functions to the database itself
GRANT EXECUTE ON FUNCTION public.check_api_key_limit() TO postgres;
GRANT EXECUTE ON FUNCTION public.handle_new_user() TO postgres;

-- Reload PostgREST configuration to apply the changes
NOTIFY pgrst, 'reload config';

COMMIT; 
BEGIN;

-- Drop existing credit system (will recreate at user level)
DROP TABLE IF EXISTS public.credits CASCADE;

-- New credit system at user level
CREATE TABLE public.credits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    balance DECIMAL(12, 6) NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Subscription plans table
CREATE TABLE public.subscription_plans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    price DECIMAL(12, 2) NOT NULL,
    billing_interval TEXT NOT NULL CHECK (billing_interval IN ('month', 'year')),
    transcription_minutes INTEGER NOT NULL,
    input_tokens INTEGER NOT NULL,
    output_tokens INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User subscriptions
CREATE TABLE public.user_subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    plan_id UUID NOT NULL REFERENCES public.subscription_plans(id),
    status TEXT NOT NULL CHECK (status IN ('active', 'canceled', 'paused')),
    current_period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    current_period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    stripe_subscription_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Modified quotas tied to subscriptions
CREATE TABLE public.quotas (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    subscription_id UUID NOT NULL REFERENCES public.user_subscriptions(id) ON DELETE CASCADE,
    service TEXT NOT NULL,
    amount_used INTEGER NOT NULL DEFAULT 0,
    amount_limit INTEGER NOT NULL,
    reset_date TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- PAYG monthly aggregation
CREATE TABLE public.payg_usage (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    month DATE NOT NULL,
    total_amount DECIMAL(12, 2) NOT NULL DEFAULT 0,
    is_billed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Update check_usage_allowance function
CREATE OR REPLACE FUNCTION check_usage_allowance(
    p_user_id UUID,
    p_service TEXT,
    p_model TEXT,
    p_amount DECIMAL
) RETURNS BOOLEAN AS $$
DECLARE
    v_subscription_quota BOOLEAN;
    v_credit_balance DECIMAL;
    v_payg_allowed BOOLEAN;
    v_cost_per_unit DECIMAL;
BEGIN
    -- Get service pricing
    SELECT cost_per_unit INTO v_cost_per_unit
    FROM public.service_pricing
    WHERE service = p_service
    AND model = p_model
    AND is_active = TRUE;

    -- 1. Check subscription quotas
    SELECT (q.amount_used + p_amount) <= q.amount_limit
    INTO v_subscription_quota
    FROM public.quotas q
    JOIN public.user_subscriptions us ON q.subscription_id = us.id
    WHERE us.user_id = p_user_id
    AND q.service = p_service
    AND us.status = 'active'
    AND q.reset_date > NOW()
    LIMIT 1;

    IF v_subscription_quota THEN RETURN TRUE; END IF;

    -- 2. Check credit balance
    SELECT COALESCE(balance, 0) >= (v_cost_per_unit * p_amount)
    INTO v_credit_balance
    FROM public.credits
    WHERE user_id = p_user_id;

    IF v_credit_balance THEN RETURN TRUE; END IF;

    -- 3. Allow PAYG if enabled
    SELECT stripe_customer_id IS NOT NULL
    INTO v_payg_allowed
    FROM public.profiles
    WHERE id = p_user_id;

    RETURN v_payg_allowed;
END;
$$ LANGUAGE plpgsql;

-- Update RLS policies
DROP POLICY IF EXISTS "Users can view their own credits" ON public.credits;
CREATE POLICY "Users can manage their credits" 
    ON public.credits 
    FOR ALL
    USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can view their own quotas" ON public.quotas;
CREATE POLICY "Users can view their own quotas"
    ON public.quotas
    FOR SELECT
    USING (auth.uid() = user_id);

-- Create indexes
CREATE INDEX idx_credits_user_id ON public.credits(user_id);
CREATE INDEX idx_subscriptions_user_id ON public.user_subscriptions(user_id);
CREATE INDEX idx_payg_usage_user_month ON public.payg_usage(user_id, month);

COMMIT; 
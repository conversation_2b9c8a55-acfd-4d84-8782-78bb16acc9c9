# VoiceHype Billing System Plan

## Core Concepts

### 1. Payment Methods

#### A. Pay As You Go (PAYG)
- No upfront payment needed
- Usage tracked throughout the month
- Total amount automatically charged to card at end of billing period
- Pricing based on actual usage
- No limits on usage (except account/safety limits)
- Best for users who want automatic billing

#### B. Credits System
- Pre-purchase credits (like OpenAI)
- Load balance in advance (e.g., $5, $10, $50)
- Credits deducted as services are used
- No automatic charges
- Credits never expire
- Best for users who want to control spending

#### C. Subscription Plans
- Fixed monthly/annual fee
- Set quotas for different services
- Example quotas:
  - 300 minutes transcription/month
  - 5,000 input tokens/month
  - 10,000 output tokens/month
- Overage charges if quotas exceeded
- Quotas reset monthly/annually
- Best for users with predictable usage

### 2. Database Schema Updates

#### Keep Credits Table (Modified)
```sql
CREATE TABLE public.credits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.profiles(id),
    balance DECIMAL(12, 6) NOT NULL DEFAULT 0,
    last_loaded_amount DECIMAL(12, 6),
    last_loaded_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Modify Quotas Table
```sql
ALTER TABLE public.quotas
    DROP COLUMN optimization_tokens_used,
    DROP COLUMN optimization_tokens_limit,
    ADD COLUMN input_tokens_used INTEGER NOT NULL DEFAULT 0,
    ADD COLUMN input_tokens_limit INTEGER,
    ADD COLUMN output_tokens_used INTEGER NOT NULL DEFAULT 0,
    ADD COLUMN output_tokens_limit INTEGER;
```

#### Add Billing Table (for PAYG)
```sql
CREATE TABLE public.billing (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.profiles(id),
    billing_period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    billing_period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    amount_due DECIMAL(12, 6) NOT NULL DEFAULT 0,
    status TEXT NOT NULL CHECK (status IN ('pending', 'paid', 'failed')),
    payment_intent_id TEXT, -- For Stripe integration
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 3. Usage Recording Flow

#### A. Pay As You Go Users
1. Record usage in usage_history
2. Calculate cost based on pricing table
3. Add cost to current billing period
4. At end of period:
   - Generate invoice
   - Charge card through Stripe
   - Start new billing period

#### B. Credits Users
1. Check credit balance before operation
2. Record usage in usage_history
3. Calculate cost based on pricing table
4. Deduct credits immediately after successful operation
5. Notify user when credits low (e.g., below $1)

#### C. Subscription Users
1. Check quota availability before operation
2. Record usage in usage_history
3. Update quota usage
4. If quota exceeded:
   - Calculate overage cost
   - Add to billing as additional charge
   - Charge card at end of period

### 4. Required Functions

```sql
-- Check if user can proceed with operation
CREATE OR REPLACE FUNCTION check_usage_allowance(
    p_user_id UUID,
    p_service TEXT,
    p_estimated_usage JSONB
) RETURNS BOOLEAN AS $$
DECLARE
    v_payment_type TEXT;
    v_credit_balance DECIMAL;
    v_within_quota BOOLEAN;
    v_estimated_cost DECIMAL;
BEGIN
    -- Get user's payment type (PAYG, Credits, or Subscription)
    SELECT 
        CASE 
            WHEN s.id IS NOT NULL THEN 'subscription'
            WHEN c.balance > 0 THEN 'credits'
            ELSE 'payg'
        END INTO v_payment_type
    FROM public.profiles p
    LEFT JOIN public.subscriptions s ON s.user_id = p.id AND s.status = 'active'
    LEFT JOIN public.credits c ON c.user_id = p.id
    WHERE p.id = p_user_id;
    
    -- Calculate estimated cost
    SELECT calculate_cost(p_service, p_estimated_usage) INTO v_estimated_cost;
    
    CASE v_payment_type
        WHEN 'payg' THEN
            RETURN TRUE; -- PAYG users always allowed (subject to safety limits)
        WHEN 'credits' THEN
            SELECT balance >= v_estimated_cost INTO v_within_quota
            FROM public.credits
            WHERE user_id = p_user_id;
            RETURN v_within_quota;
        WHEN 'subscription' THEN
            -- Check subscription quotas
            -- Implementation details here
            RETURN v_within_quota;
    END CASE;
    
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- Record usage and update billing/quotas/credits
CREATE OR REPLACE FUNCTION record_usage(
    p_user_id UUID,
    p_api_key_id UUID,
    p_service TEXT,
    p_model TEXT,
    p_usage JSONB,
    p_status TEXT
) RETURNS UUID AS $$
DECLARE
    v_payment_type TEXT;
    v_usage_id UUID;
    v_cost DECIMAL;
BEGIN
    -- Calculate cost
    SELECT calculate_cost(p_service, p_usage) INTO v_cost;
    
    -- Record in usage_history
    INSERT INTO public.usage_history (
        user_id, api_key_id, service, model,
        amount, cost, status, metadata
    ) VALUES (
        p_user_id, p_api_key_id, p_service, p_model,
        p_usage->>'amount', v_cost, p_status, p_usage
    ) RETURNING id INTO v_usage_id;
    
    -- Get user's payment type
    SELECT 
        CASE 
            WHEN s.id IS NOT NULL THEN 'subscription'
            WHEN c.balance > 0 THEN 'credits'
            ELSE 'payg'
        END INTO v_payment_type
    FROM public.profiles p
    LEFT JOIN public.subscriptions s ON s.user_id = p.id AND s.status = 'active'
    LEFT JOIN public.credits c ON c.user_id = p.id
    WHERE p.id = p_user_id;
    
    IF p_status = 'success' THEN
        CASE v_payment_type
            WHEN 'payg' THEN
                -- Add to current billing period
                UPDATE public.billing
                SET amount_due = amount_due + v_cost,
                    updated_at = NOW()
                WHERE user_id = p_user_id
                AND billing_period_end > NOW()
                AND billing_period_start <= NOW();
                
            WHEN 'credits' THEN
                -- Deduct from credits
                UPDATE public.credits
                SET balance = balance - v_cost,
                    updated_at = NOW()
                WHERE user_id = p_user_id;
                
            WHEN 'subscription' THEN
                -- Update subscription quotas
                UPDATE public.quotas
                SET -- Update relevant quota fields based on p_usage
                    updated_at = NOW()
                WHERE user_id = p_user_id;
        END CASE;
    END IF;
    
    RETURN v_usage_id;
END;
$$ LANGUAGE plpgsql;

-- Function to load credits
CREATE OR REPLACE FUNCTION load_credits(
    p_user_id UUID,
    p_amount DECIMAL
) RETURNS DECIMAL AS $$
DECLARE
    v_new_balance DECIMAL;
BEGIN
    UPDATE public.credits
    SET balance = balance + p_amount,
        last_loaded_amount = p_amount,
        last_loaded_at = NOW(),
        updated_at = NOW()
    WHERE user_id = p_user_id
    RETURNING balance INTO v_new_balance;
    
    RETURN v_new_balance;
END;
$$ LANGUAGE plpgsql;
```

### 5. Implementation Steps

1. **Schema Updates**
   - Modify credits table
   - Update quotas table
   - Create billing table
   - Update pricing table

2. **Payment Integration**
   - Set up Stripe for PAYG billing
   - Add credit purchase endpoints
   - Implement subscription management
   - Create automatic billing system

3. **Usage Tracking**
   - Update edge functions for all payment methods
   - Implement proper usage checking
   - Add credit balance notifications
   - Handle quota overages

4. **Testing Scenarios**
   - PAYG user monthly billing
   - Credit purchase and usage
   - Credit depletion notifications
   - Subscription quota tracking
   - Quota reset on renewal
   - Failed operations handling
   - Mixed usage scenarios

### 6. Edge Function Updates

```typescript
async function handleRequest(req: Request) {
  const { userId, apiKeyId } = await validateApiKey(req.headers.authorization);
  const estimatedUsage = calculateEstimatedUsage(req.body);
  
  // Check if operation can proceed
  const canProceed = await checkUsageAllowance(userId, estimatedUsage);
  if (!canProceed) {
    throw new Error('Insufficient credits or quota exceeded');
  }
  
  // Record initial usage
  const usageId = await recordUsage(userId, apiKeyId, {
    ...estimatedUsage,
    status: 'pending'
  });
  
  try {
    const result = await processRequest(req.body);
    
    // Record final usage
    await updateUsage(usageId, {
      ...calculateActualUsage(result),
      status: 'success'
    });
    
    // Check if credits are low
    await checkAndNotifyLowCredits(userId);
    
    return result;
  } catch (error) {
    await updateUsage(usageId, { status: 'failed' });
    throw error;
  }
}
``` 
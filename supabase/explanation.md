# VoiceHype Database Schema Explanation

This document provides a detailed explanation of each table in the VoiceHype database schema, including the purpose of each column and how to use them effectively.

## Extensions

The schema uses the following PostgreSQL extensions:

| Extension | Purpose |
|-----------|---------|
| **uuid-ossp** | Provides functions to generate UUIDs, used for primary keys throughout the database |
| **pgcrypto** | Provides cryptographic functions for secure hashing of API keys and other sensitive data |

## Profiles Table

**Purpose**: Extends Supabase auth.users to store additional user information.

| Column | Type | Description |
|--------|------|-------------|
| **id** | UUID | Primary key that links to Supabase auth.users table, ensuring user profile is tied to authentication |
| **email** | TEXT | User's email address for communications |
| **full_name** | TEXT | User's full name for personalization |
| **company_name** | TEXT | Optional company name for business accounts |
| **created_at** | TIMESTAMP WITH TIME ZONE | Timestamp when the profile was created |
| **updated_at** | TIMESTAMP WITH TIME ZONE | Timestamp when the profile was last updated |

**Usage**:
- Create a profile automatically when a user signs up (handled by the `handle_new_user` trigger)
- Query this table to get user information for displaying in dashboards
- Update this table when users modify their profile information

## API Keys Table

**Purpose**: Securely stores API keys that users will use to authenticate with the VoiceHype API.

| Column | Type | Description |
|--------|------|-------------|
| **id** | UUID | Unique identifier for the API key |
| **user_id** | UUID | References the user who owns this API key |
| **name** | TEXT | A friendly name for the API key (e.g., "Development", "Production") |
| **key_prefix** | TEXT | First few characters of the API key, used for display purposes |
| **key_hash** | TEXT | Securely hashed API key using Bcrypt, never storing the actual key |
| **is_active** | BOOLEAN | Flag indicating if the key is currently active |
| **created_at** | TIMESTAMP WITH TIME ZONE | Timestamp when the key was created |
| **last_used_at** | TIMESTAMP WITH TIME ZONE | Timestamp when the key was last used, useful for tracking inactive keys |
| **expires_at** | TIMESTAMP WITH TIME ZONE | Optional expiration date for the key |

**Usage**:
- Generate new API keys using the `generate_api_key()` function
- Validate API keys using the `validate_api_key()` function
- Revoke keys by setting `is_active` to false
- Track key usage through the `last_used_at` field
- Set expiration dates for temporary access

## Plans Table

**Purpose**: Defines the subscription plans available to users.

| Column | Type | Description |
|--------|------|-------------|
| **id** | UUID | Unique identifier for the plan |
| **name** | TEXT | Name of the plan (e.g., "Pay-As-You-Go", "Starter") |
| **description** | TEXT | Detailed description of what the plan offers |
| **is_active** | BOOLEAN | Flag indicating if the plan is currently available |
| **created_at** | TIMESTAMP WITH TIME ZONE | Timestamp when the plan was created |

**Default Plans**:

| Plan Name | Description |
|-----------|-------------|
| Pay-As-You-Go | Pay only for what you use |
| Starter | 10 hours of transcription and 10,000 optimization tokens |
| Professional | 30 hours of transcription and 50,000 optimization tokens |
| Enterprise | Custom limits for high-volume usage |

**Usage**:
- Reference these plans when creating subscriptions
- Display available plans to users in the dashboard
- Modify plan details or add new plans as your service evolves

## Subscriptions Table

**Purpose**: Tracks user subscriptions to different plans.

| Column | Type | Description |
|--------|------|-------------|
| **id** | UUID | Unique identifier for the subscription |
| **user_id** | UUID | References the user who owns this subscription |
| **plan_id** | UUID | References the plan the user is subscribed to |
| **status** | TEXT | Current status of the subscription (active, canceled, past_due, trialing) |
| **current_period_start** | TIMESTAMP WITH TIME ZONE | Start date of the current billing period |
| **current_period_end** | TIMESTAMP WITH TIME ZONE | End date of the current billing period |
| **created_at** | TIMESTAMP WITH TIME ZONE | Timestamp when the subscription was created |
| **updated_at** | TIMESTAMP WITH TIME ZONE | Timestamp when the subscription was last updated |

**Usage**:
- Create a new subscription when a user purchases a plan
- Update subscription status based on payment events
- Check subscription status to determine service access
- Trigger quota updates when subscriptions change (handled by the `trigger_update_quotas` trigger)

## Credits Table

**Purpose**: Manages the credit balance for users on the Pay-As-You-Go plan.

| Column | Type | Description |
|--------|------|-------------|
| **id** | UUID | Unique identifier for the credit record |
| **user_id** | UUID | References the user who owns these credits |
| **balance** | DECIMAL(12, 6) | Current credit balance in USD (supports up to 6 decimal places for micro-transactions) |
| **created_at** | TIMESTAMP WITH TIME ZONE | Timestamp when the credit record was created |
| **updated_at** | TIMESTAMP WITH TIME ZONE | Timestamp when the credit balance was last updated |

**Usage**:
- Initialize credits when a user signs up (handled by the `handle_new_user` trigger)
- Add credits when a user purchases them
- Deduct credits when a user uses services (handled by the `record_usage` function)
- Check credit balance before allowing service usage (handled by the `check_user_usage_allowance` function)

## Quotas Table

**Purpose**: Tracks usage quotas for subscription plans.

| Column | Type | Description |
|--------|------|-------------|
| **id** | UUID | Unique identifier for the quota record |
| **user_id** | UUID | References the user who owns these quotas |
| **transcription_minutes_used** | INTEGER | Number of transcription minutes used in the current period |
| **transcription_minutes_limit** | INTEGER | Maximum number of transcription minutes allowed (NULL means unlimited) |
| **optimization_tokens_used** | INTEGER | Number of optimization tokens used in the current period |
| **optimization_tokens_limit** | INTEGER | Maximum number of optimization tokens allowed (NULL means unlimited) |
| **reset_date** | TIMESTAMP WITH TIME ZONE | Date when the quotas will reset (typically matches subscription renewal) |
| **created_at** | TIMESTAMP WITH TIME ZONE | Timestamp when the quota record was created |
| **updated_at** | TIMESTAMP WITH TIME ZONE | Timestamp when the quota was last updated |

**Quota Limits by Plan**:

| Plan | Transcription Minutes | Optimization Tokens |
|------|----------------------|---------------------|
| Pay-As-You-Go | Unlimited (pay per use) | Unlimited (pay per use) |
| Starter | 600 (10 hours) | 10,000 |
| Professional | 1,800 (30 hours) | 50,000 |
| Enterprise | Custom | Custom |

**Usage**:
- Initialize quotas when a user signs up (handled by the `handle_new_user` trigger)
- Update quota limits when a user's subscription changes (handled by the `update_user_quotas` function)
- Track usage by incrementing the used amounts (handled by the `record_usage` function)
- Check remaining quota before allowing service usage (handled by the `check_user_usage_allowance` function)
- Reset quotas when the reset date is reached

## Pricing Table

**Purpose**: Defines pricing for different services and models.

| Column | Type | Description |
|--------|------|-------------|
| **id** | UUID | Unique identifier for the pricing record |
| **service** | TEXT | Type of service ('transcription' or 'optimization') |
| **model** | TEXT | Specific model used (e.g., 'whisper-large-v3', 'meta/meta-llama-3-8b-instruct') |
| **cost_per_unit** | DECIMAL(12, 6) | Cost in USD per unit of usage |
| **unit** | TEXT | Unit of measurement ('minute' for transcription, 'token' for optimization) |
| **is_active** | BOOLEAN | Flag indicating if this pricing is currently active |
| **created_at** | TIMESTAMP WITH TIME ZONE | Timestamp when the pricing record was created |
| **updated_at** | TIMESTAMP WITH TIME ZONE | Timestamp when the pricing was last updated |

**Default Pricing**:

| Service | Model | Cost Per Unit | Unit |
|---------|-------|---------------|------|
| transcription | assemblyai-best | $0.01 | minute |
| transcription | assemblyai-nano | $0.005 | minute |
| transcription | whisper-large-v3 | $0.006 | minute |
| optimization | meta/meta-llama-3-8b-instruct | $0.00015 | token |
| optimization | meta/meta-llama-3-70b-instruct | $0.0006 | token |
| optimization | meta/meta-llama-3.1-405b-instruct | $0.0012 | token |
| optimization | anthropic/claude-3.5-sonnet | $0.0015 | token |
| optimization | openai/gpt-4o | $0.0015 | token |

**Usage**:
- Reference this table to calculate costs for usage
- Update pricing as costs change or new models are added
- Display pricing information to users
- Used by the `record_usage` function to calculate costs

## Usage History Table

**Purpose**: Records detailed usage history for billing and analytics.

| Column | Type | Description |
|--------|------|-------------|
| **id** | UUID | Unique identifier for the usage record |
| **user_id** | UUID | References the user who used the service |
| **api_key_id** | UUID | References the API key used (can be NULL if the key is deleted) |
| **service** | TEXT | Type of service used ('transcription', 'optimization', or 'combined') |
| **model** | TEXT | Specific model used |
| **amount** | DECIMAL(12, 6) | Amount of units used (minutes or tokens) |
| **cost** | DECIMAL(12, 6) | Cost in USD for this usage |
| **status** | TEXT | Status of the operation ('success', 'failed', 'pending') |
| **metadata** | JSONB | Additional data about the request (JSONB allows flexible storage) |
| **created_at** | TIMESTAMP WITH TIME ZONE | Timestamp when the usage was recorded |

**Usage**:
- Record usage when services are used (handled by the `record_usage` function)
- Query for analytics and billing reports
- Track failed operations for troubleshooting
- Store additional metadata about requests for detailed analysis

## Key Functions

### generate_api_key()

**Purpose**: Generates a secure, random API key with a consistent prefix.

**Signature**:
```sql
FUNCTION generate_api_key() RETURNS TEXT
```

**Implementation Details**:
- Uses cryptographically secure random bytes (24 bytes)
- Encodes with base64 for readability
- Adds 'vhkey_' prefix for identification
- Uses SECURITY DEFINER to run with elevated privileges

**Usage**:
- Call this function when creating a new API key for a user
- The returned key should be shown to the user ONLY ONCE, as it cannot be retrieved later
- Store the key_prefix and key_hash in the api_keys table

### update_user_quotas()

**Purpose**: Updates user quotas based on their subscription plan.

**Signature**:
```sql
FUNCTION update_user_quotas() RETURNS TRIGGER
```

**Trigger**: Fires after INSERT or UPDATE on public.subscriptions

**Implementation Details**:
- Sets quota limits based on the plan name:
  - Starter: 600 minutes (10 hours) and 10,000 tokens
  - Professional: 1,800 minutes (30 hours) and 50,000 tokens
  - Pay-As-You-Go: NULL limits (unlimited, pay per use)
- Sets reset_date to match subscription period end

**Usage**:
- Automatically triggered when a subscription is created or updated
- No need to call directly - the trigger handles it

### check_user_usage_allowance()

**Purpose**: Checks if a user has sufficient credits or quota for a requested operation.

**Signature**:
```sql
FUNCTION check_user_usage_allowance(
    p_user_id UUID,
    p_service TEXT,
    p_model TEXT,
    p_amount DECIMAL
) RETURNS BOOLEAN
```

**Implementation Details**:
- Determines user's plan type
- For Pay-As-You-Go: Calculates cost and checks credit balance
- For subscription plans: Checks against appropriate quota limit
- Returns TRUE if user can proceed, FALSE otherwise

**Usage**:
- Call this function before processing any service request
- Pass the user ID, service type, model, and estimated usage amount

### record_usage()

**Purpose**: Records usage and updates credits or quotas accordingly.

**Signature**:
```sql
FUNCTION record_usage(
    p_user_id UUID,
    p_api_key_id UUID,
    p_service TEXT,
    p_model TEXT,
    p_amount DECIMAL,
    p_status TEXT,
    p_metadata JSONB
) RETURNS UUID
```

**Implementation Details**:
- Calculates cost based on pricing table
- Creates a record in usage_history
- If status is 'success':
  - For Pay-As-You-Go: Deducts credits
  - For subscription plans: Increments appropriate usage counter
- Returns the ID of the created usage record

**Usage**:
- Call this function when a service is used
- Call with status='pending' before processing
- Update with status='success' or status='failed' after processing

### validate_api_key()

**Purpose**: Validates an API key and returns the associated user ID if valid.

**Signature**:
```sql
FUNCTION validate_api_key(p_key TEXT) 
RETURNS TABLE (
    user_id UUID,
    api_key_id UUID
)
```

**Implementation Details**:
- Extracts key prefix
- Uses secure comparison with hashed key
- Updates last_used_at timestamp for valid keys
- Checks if key is active and not expired
- Returns user_id and api_key_id if valid

**Usage**:
- Call this function when authenticating API requests
- Use the returned user_id for authorization checks

### handle_new_user()

**Purpose**: Sets up necessary records when a new user signs up.

**Signature**:
```sql
FUNCTION handle_new_user() RETURNS TRIGGER
```

**Trigger**: Fires after INSERT on auth.users

**Implementation Details**:
- Creates a profile record with user information
- Initializes credits with a zero balance
- Creates empty quota records

**Usage**:
- Automatically triggered when a new user is created in auth.users
- No need to call directly - the trigger handles it

## Row Level Security (RLS) Policies

The schema includes comprehensive RLS policies that ensure users can only access their own data. These policies are automatically enforced by Supabase and require no additional code.

| Table | Policy Type | Description |
|-------|-------------|-------------|
| profiles | SELECT | Users can view their own profile |
| profiles | UPDATE | Users can update their own profile |
| api_keys | SELECT | Users can view their own API keys |
| api_keys | INSERT | Users can create their own API keys |
| api_keys | UPDATE | Users can update their own API keys |
| api_keys | DELETE | Users can delete their own API keys |
| usage_history | SELECT | Users can view their own usage history |
| credits | SELECT | Users can view their own credits |
| quotas | SELECT | Users can view their own quotas |
| subscriptions | SELECT | Users can view their own subscriptions |

## Best Practices for Using This Schema

### API Key Management

| Practice | Description |
|----------|-------------|
| Generation | Use the `generate_api_key()` function to create secure keys |
| Storage | Store only the prefix and hash, never the full key |
| Validation | Use the `validate_api_key()` function for authentication |
| Expiration | Set expiration dates for temporary keys |
| Revocation | Set `is_active` to false to revoke keys without deleting history |

### Subscription Management

| Practice | Description |
|----------|-------------|
| Creation | Create subscriptions with appropriate plan_id |
| Status Updates | Update status based on payment events |
| Quota Management | Let the trigger handle quota updates automatically |
| Period Tracking | Set current_period_start and current_period_end accurately |

### Usage Tracking

| Practice | Description |
|----------|-------------|
| Pre-check | Always check allowance before processing requests |
| Complete Recording | Record all usage, including failed attempts |
| Metadata | Include detailed metadata for analytics |
| Status Updates | Update status from 'pending' to 'success'/'failed' |

### Pricing Updates

| Practice | Description |
|----------|-------------|
| New Records | Add new pricing records rather than modifying existing ones |
| Deprecation | Set is_active to false for outdated pricing |
| Completeness | Ensure all services and models have corresponding pricing entries |
| Granularity | Use separate entries for different service/model combinations |

### Security

| Practice | Description |
|----------|-------------|
| RLS | Rely on RLS policies for data isolation |
| Function Privileges | Use the SECURITY DEFINER functions as intended |
| Data Protection | Never expose API key hashes to clients |
| Prefix Only | Only show key_prefix in user interfaces |

By following these practices, you can effectively use this schema to manage users, API keys, subscriptions, and usage tracking for the VoiceHype service. 
-- SQL to insert service pricing data for optimization models
-- This can be executed in the Supabase SQL Editor

-- Define profit margin and conversion variables
DO $$
DECLARE
    -- Profit margin: 18% markup on cost
    profit_margin DECIMAL := 1.18;
    
    -- Model costs per 1M tokens (in USD)
    -- Anthropic models
    claude_3_5_haiku_input_cost_per_1M DECIMAL := 1.00;  -- $1.00 per 1M tokens
    claude_3_5_haiku_output_cost_per_1M DECIMAL := 5.00;  -- $5.00 per 1M tokens
    
    claude_3_5_sonnet_input_cost_per_1M DECIMAL := 3.75;  -- $3.75 per 1M tokens
    claude_3_5_sonnet_output_cost_per_1M DECIMAL := 18.75;  -- $18.75 per 1M tokens
    
    claude_3_7_sonnet_input_cost_per_1M DECIMAL := 3.00;  -- $3.00 per 1M tokens
    claude_3_7_sonnet_output_cost_per_1M DECIMAL := 15.00;  -- $15.00 per 1M tokens
    
    -- Meta Llama models
    llama_3_70b_input_cost_per_1M DECIMAL := 0.65;  -- $0.65 per 1M tokens
    llama_3_70b_output_cost_per_1M DECIMAL := 2.75;  -- $2.75 per 1M tokens
    
    llama_3_1_405b_input_cost_per_1M DECIMAL := 9.50;  -- $9.50 per 1M tokens
    llama_3_1_405b_output_cost_per_1M DECIMAL := 9.50;  -- $9.50 per 1M tokens
    
    -- DeepSeek model
    deepseek_r1_input_cost_per_1M DECIMAL := 10.00;  -- $10.00 per 1M tokens
    deepseek_r1_output_cost_per_1M DECIMAL := 10.00;  -- $10.00 per 1M tokens
    
    -- OpenAI models
    gpt_4o_input_cost_per_1M DECIMAL := 2.50;  -- $2.50 per 1M tokens
    gpt_4o_output_cost_per_1M DECIMAL := 10.00;  -- $10.00 per 1M tokens
    
    gpt_4o_mini_input_cost_per_1M DECIMAL := 0.15;  -- $0.15 per 1M tokens
    gpt_4o_mini_output_cost_per_1M DECIMAL := 0.60;  -- $0.60 per 1M tokens
    
    o3_mini_input_cost_per_1M DECIMAL := 1.10;  -- $1.10 per 1M tokens
    o3_mini_output_cost_per_1M DECIMAL := 4.40;  -- $4.40 per 1M tokens
    
    -- Other models
    llama_3_8b_cost_per_1M DECIMAL := 150.00;  -- $150.00 per 1M tokens
    
    -- Transcription models (costs per hour/minute)
    assembly_ai_best_cost_per_hour DECIMAL := 0.37;  -- $0.37 per hour
    assembly_ai_nano_cost_per_hour DECIMAL := 0.12;  -- $0.12 per hour
    whisper_cost_per_minute DECIMAL := 0.006;  -- $0.006 per minute
BEGIN
    -- Clear existing pricing data (optional - comment out if you want to keep existing data)
    -- DELETE FROM public.service_pricing WHERE service = 'optimization';
    
    -- First, let's insert pricing for Anthropic models
    -- Claude 3.5 Haiku
    -- INSERT INTO public.service_pricing (service, model, cost_per_unit, unit, is_active)
    -- VALUES 
    --   ('optimization', 'anthropic/claude-3.5-haiku/input', 
    --    (claude_3_5_haiku_input_cost_per_1M / 1000000) * profit_margin, 'token', true),
    --   ('optimization', 'anthropic/claude-3.5-haiku/output', 
    --    (claude_3_5_haiku_output_cost_per_1M / 1000000) * profit_margin, 'token', true);
    
    -- -- Claude 3.5 Sonnet
    -- INSERT INTO public.service_pricing (service, model, cost_per_unit, unit, is_active)
    -- VALUES 
    --   ('optimization', 'anthropic/claude-3.5-sonnet/input', 
    --    (claude_3_5_sonnet_input_cost_per_1M / 1000000) * profit_margin, 'token', true),
    --   ('optimization', 'anthropic/claude-3.5-sonnet/output', 
    --    (claude_3_5_sonnet_output_cost_per_1M / 1000000) * profit_margin, 'token', true);
    
    -- -- Claude 3.7 Sonnet
    -- INSERT INTO public.service_pricing (service, model, cost_per_unit, unit, is_active)
    -- VALUES 
    --   ('optimization', 'anthropic/claude-3.7-sonnet/input', 
    --    (claude_3_7_sonnet_input_cost_per_1M / 1000000) * profit_margin, 'token', true),
    --   ('optimization', 'anthropic/claude-3.7-sonnet/output', 
    --    (claude_3_7_sonnet_output_cost_per_1M / 1000000) * profit_margin, 'token', true);
    
    -- -- Meta Llama models
    -- INSERT INTO public.service_pricing (service, model, cost_per_unit, unit, is_active)
    -- VALUES 
    --   ('optimization', 'meta/meta-llama-3-70b-instruct/input', 
    --    (llama_3_70b_input_cost_per_1M / 1000000) * profit_margin, 'token', true),
    --   ('optimization', 'meta/meta-llama-3-70b-instruct/output', 
    --    (llama_3_70b_output_cost_per_1M / 1000000) * profit_margin, 'token', true);
    
    -- INSERT INTO public.service_pricing (service, model, cost_per_unit, unit, is_active)
    -- VALUES 
    --   ('optimization', 'meta/meta-llama-3.1-405b-instruct/input', 
    --    (llama_3_1_405b_input_cost_per_1M / 1000000) * profit_margin, 'token', true),
    --   ('optimization', 'meta/meta-llama-3.1-405b-instruct/output', 
    --    (llama_3_1_405b_output_cost_per_1M / 1000000) * profit_margin, 'token', true);
    
    -- -- DeepSeek model
    -- INSERT INTO public.service_pricing (service, model, cost_per_unit, unit, is_active)
    -- VALUES 
    --   ('optimization', 'deepseek-ai/deepseek-r1/input', 
    --    (deepseek_r1_input_cost_per_1M / 1000000) * profit_margin, 'token', true),
    --   ('optimization', 'deepseek-ai/deepseek-r1/output', 
    --    (deepseek_r1_output_cost_per_1M / 1000000) * profit_margin, 'token', true);
    
    -- OpenAI models
    INSERT INTO public.service_pricing (service, model, cost_per_unit, unit, is_active)
    VALUES 
      ('optimization', 'gpt-4o/input', 
       (gpt_4o_input_cost_per_1M / 1000000) * profit_margin, 'token', true),
      ('optimization', 'gpt-4o/output', 
       (gpt_4o_output_cost_per_1M / 1000000) * profit_margin, 'token', true);
    
    INSERT INTO public.service_pricing (service, model, cost_per_unit, unit, is_active)
    VALUES 
      ('optimization', 'gpt-4o-mini/input', 
       (gpt_4o_mini_input_cost_per_1M / 1000000) * profit_margin, 'token', true),
      ('optimization', 'gpt-4o-mini/output', 
       (gpt_4o_mini_output_cost_per_1M / 1000000) * profit_margin, 'token', true);
    
    INSERT INTO public.service_pricing (service, model, cost_per_unit, unit, is_active)
    VALUES 
      ('optimization', 'o3-mini/input', 
       (o3_mini_input_cost_per_1M / 1000000) * profit_margin, 'token', true),
      ('optimization', 'o3-mini/output', 
       (o3_mini_output_cost_per_1M / 1000000) * profit_margin, 'token', true);
    
    -- -- Add the existing model from the schema
    -- -- INSERT INTO public.service_pricing (service, model, cost_per_unit, unit, is_active)
    -- VALUES 
    --   ('optimization', 'llama-3-8b', 
    --    (llama_3_8b_cost_per_1M / 1000000) * profit_margin, 'token', true);
    
    -- Transcription models
    -- Assembly AI models (convert from per hour to per minute)
    INSERT INTO public.service_pricing (service, model, cost_per_unit, unit, is_active)
    VALUES 
      ('transcription', 'assembly-ai/best', 
       (assembly_ai_best_cost_per_hour / 60) * profit_margin, 'minute', true),
      ('transcription', 'assembly-ai/nano', 
       (assembly_ai_nano_cost_per_hour / 60) * profit_margin, 'minute', true);
    
    -- OpenAI Whisper model (already in per minute pricing)
    INSERT INTO public.service_pricing (service, model, cost_per_unit, unit, is_active)
    VALUES 
      ('transcription', 'whisper-1', 
       whisper_cost_per_minute * profit_margin, 'minute', true);
    
    -- Alternative approach: Combined pricing (average of input/output)
    -- This is an alternative if you prefer to have a single entry per model
    -- Comment out this section if you're using the separate input/output approach above
    
    /*
    -- Calculate average cost for each model (input + output) / 2
    INSERT INTO public.service_pricing (service, model, cost_per_unit, unit, is_active)
    VALUES 
      ('optimization', 'anthropic/claude-3.5-haiku', 
       ((claude_3_5_haiku_input_cost_per_1M + claude_3_5_haiku_output_cost_per_1M) / 2 / 1000000) * profit_margin, 'token', true),
      
      ('optimization', 'anthropic/claude-3.5-sonnet', 
       ((claude_3_5_sonnet_input_cost_per_1M + claude_3_5_sonnet_output_cost_per_1M) / 2 / 1000000) * profit_margin, 'token', true),
      
      ('optimization', 'anthropic/claude-3.7-sonnet', 
       ((claude_3_7_sonnet_input_cost_per_1M + claude_3_7_sonnet_output_cost_per_1M) / 2 / 1000000) * profit_margin, 'token', true),
      
      ('optimization', 'meta/meta-llama-3-70b-instruct', 
       ((llama_3_70b_input_cost_per_1M + llama_3_70b_output_cost_per_1M) / 2 / 1000000) * profit_margin, 'token', true),
      
      ('optimization', 'meta/meta-llama-3.1-405b-instruct', 
       ((llama_3_1_405b_input_cost_per_1M + llama_3_1_405b_output_cost_per_1M) / 2 / 1000000) * profit_margin, 'token', true),
      
      ('optimization', 'deepseek-ai/deepseek-r1', 
       ((deepseek_r1_input_cost_per_1M + deepseek_r1_output_cost_per_1M) / 2 / 1000000) * profit_margin, 'token', true),
      
      ('optimization', 'gpt-4o', 
       ((gpt_4o_input_cost_per_1M + gpt_4o_output_cost_per_1M) / 2 / 1000000) * profit_margin, 'token', true),
      
      ('optimization', 'gpt-4o-mini', 
       ((gpt_4o_mini_input_cost_per_1M + gpt_4o_mini_output_cost_per_1M) / 2 / 1000000) * profit_margin, 'token', true),
      
      ('optimization', 'o3-mini', 
       ((o3_mini_input_cost_per_1M + o3_mini_output_cost_per_1M) / 2 / 1000000) * profit_margin, 'token', true);
    */
END $$; 
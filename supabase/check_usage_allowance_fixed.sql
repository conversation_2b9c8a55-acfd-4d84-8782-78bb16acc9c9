CREATE OR REPLACE FUNCTION public.check_usage_allowance(p_user_id uuid, p_service text, p_model text, p_amount numeric, p_api_key_id uuid, p_is_input_only boolean DEFAULT false)
 RETURNS TABLE(can_use boolean, pricing_model text, cost numeric, max_output text, error_code text)
 LANGUAGE plpgsql
AS $function$
DECLARE
    subscription_available BOOLEAN;
    credit_balance DECIMAL(18,9);
    cost_per_unit DECIMAL(18,9);
    input_cost_per_unit DECIMAL(18,9);
    output_cost_per_unit DECIMAL(18,9);
    pricing_unit TEXT;
    remaining_quota DECIMAL(18,9) := 0;
    estimated_minutes DECIMAL(18,9);
    is_token_based BOOLEAN;
    is_realtime_transcription BOOLEAN;
    best_pricing_model TEXT;
BEGIN
    -- Fetch pricing info
    SELECT sp.unit, sp.cost_per_unit INTO pricing_unit, cost_per_unit
    FROM public.service_pricing sp
    WHERE sp.service = p_service
      AND sp.model = p_model
      AND sp.is_active = TRUE;

    is_token_based := pricing_unit = 'token-based';
    is_realtime_transcription := (p_service = 'transcription' AND (p_model LIKE '%-realtime' OR p_model LIKE '%/realtime'));

    -- Get subscription quota
    SELECT EXISTS (
        SELECT 1 FROM public.quotas q
        JOIN public.user_subscriptions us ON q.subscription_id = us.id
        WHERE us.user_id = p_user_id
          AND q.service = p_service
          AND us.status IN ('active', 'past_due')
    ) INTO subscription_available;

    IF subscription_available THEN
        SELECT (q.total_amount - q.used_amount)
        INTO remaining_quota
        FROM public.quotas q
        JOIN public.user_subscriptions us ON q.subscription_id = us.id
        WHERE us.user_id = p_user_id
          AND q.service = p_service
          AND us.status IN ('active', 'past_due')
        LIMIT 1;
    END IF;

    -- Get credit balance
    SELECT COALESCE(SUM(balance), 0)
    INTO credit_balance
    FROM public.credits
    WHERE user_id = p_user_id
      AND balance > 0
      AND (expires_at IS NULL OR expires_at > NOW())
      AND status = 'active';

-- TRANSCRIPTION LOGIC
IF p_service = 'transcription' THEN
    IF is_realtime_transcription THEN
        -- Total available minutes from both subscription and credits
        estimated_minutes := remaining_quota;
        IF cost_per_unit > 0 THEN
            estimated_minutes := estimated_minutes + (credit_balance / cost_per_unit);
        END IF;

        -- Require at least 30 seconds (0.5 minutes) for realtime transcription
        IF estimated_minutes >= 0.5 THEN
            RETURN QUERY SELECT
                TRUE,
                CASE
                    WHEN remaining_quota > 0 THEN 'subscription'
                    ELSE 'credits'
                END,
                0::numeric,
                LEAST(estimated_minutes, 20)::TEXT,
                NULL;
            RETURN;
        ELSE
            -- Provide detailed error for realtime transcription
            IF estimated_minutes <= 0 THEN
                RETURN QUERY SELECT FALSE, NULL, 0::numeric, NULL, 'realtime_insufficient_total: Realtime transcription requires at least 30 seconds. No subscription quota or credits available.';
            ELSIF estimated_minutes < 0.5 THEN
                RETURN QUERY SELECT FALSE, NULL, 0::numeric, NULL, format('realtime_insufficient_time: Realtime transcription requires at least 30 seconds. Available: %.1f seconds', estimated_minutes * 60);
            END IF;
            RETURN;
        END IF;

        ELSE
            estimated_minutes := remaining_quota;
            IF cost_per_unit > 0 THEN
                estimated_minutes := estimated_minutes + (credit_balance / cost_per_unit);
            END IF;

            -- Check if user has available quota or credits
            IF remaining_quota > 0 OR credit_balance > 0 THEN
                -- Add 30 seconds (0.5 minutes) grace period if insufficient but has some quota/credits
                IF estimated_minutes < p_amount THEN
                    estimated_minutes := estimated_minutes + 0.5;
                END IF;
                
                -- Allow if after grace period there's enough time, or if original was sufficient
                IF estimated_minutes >= p_amount THEN
                    RETURN QUERY SELECT
                        TRUE,
                        CASE
                            WHEN remaining_quota > 0 THEN 'subscription'
                            ELSE 'credits'
                        END,
                        0::numeric,
                        estimated_minutes::TEXT,
                        NULL;
                    RETURN;
                ELSE
                    -- Provide detailed error for normal transcription
                    DECLARE
                        subscription_minutes DECIMAL(18,9) := remaining_quota;
                        credit_minutes DECIMAL(18,9) := 0;
                        total_needed DECIMAL(18,9) := p_amount;
                        grace_applied BOOLEAN := (estimated_minutes < p_amount);
                    BEGIN
                        IF cost_per_unit > 0 THEN
                            credit_minutes := credit_balance / cost_per_unit;
                        END IF;
                        
                        IF remaining_quota <= 0 AND credit_balance <= 0 THEN
                            RETURN QUERY SELECT FALSE, NULL, 0::numeric, NULL, 'transcription_no_balance: No subscription quota or credits available for transcription.';
                        ELSIF remaining_quota > 0 AND credit_balance <= 0 THEN
                            RETURN QUERY SELECT FALSE, NULL, 0::numeric, NULL, format('transcription_quota_insufficient: Subscription quota insufficient. Available: %.1f minutes, Required: %.1f minutes', remaining_quota, total_needed);
                        ELSIF remaining_quota <= 0 AND credit_balance > 0 THEN
                            RETURN QUERY SELECT FALSE, NULL, 0::numeric, NULL, format('transcription_credits_insufficient: Credits insufficient. Available: %.1f minutes (%.2f credits), Required: %.1f minutes', credit_minutes, credit_balance, total_needed);
                        ELSE
                            RETURN QUERY SELECT FALSE, NULL, 0::numeric, NULL, format('transcription_combined_insufficient: Combined subscription (%.1f min) + credits (%.1f min) insufficient. Required: %.1f minutes', subscription_minutes, credit_minutes, total_needed);
                        END IF;
                    END;
                    RETURN;
                END IF;
            ELSE
                -- No quota or credits available
                RETURN QUERY SELECT FALSE, NULL, 0::numeric, NULL, 'transcription_no_balance: No subscription quota or credits available for transcription.';
            END IF;
        END IF;
    END IF;

    -- OPTIMIZATION LOGIC
    IF p_service = 'optimization' THEN
        SELECT sp.cost_per_unit INTO input_cost_per_unit
        FROM public.service_pricing sp
        WHERE sp.service = 'optimization'
          AND sp.model = p_model || '/input'
          AND sp.is_active = TRUE;

        SELECT sp.cost_per_unit INTO output_cost_per_unit
        FROM public.service_pricing sp
        WHERE sp.service = 'optimization'
          AND sp.model = p_model || '/output'
          AND sp.is_active = TRUE;

        -- If either quota or credits exist, allow
        IF remaining_quota > 0 OR credit_balance > 0 THEN
            RETURN QUERY SELECT
                TRUE,
                CASE
                    WHEN remaining_quota > 0 THEN 'subscription'
                    ELSE 'credits'
                END,
                0::numeric,
                '4096',
                NULL;
            RETURN;
        ELSE
            -- Provide detailed error for optimization
            IF remaining_quota <= 0 AND credit_balance <= 0 THEN
                RETURN QUERY SELECT FALSE, NULL, 0::numeric, NULL, 'optimization_no_balance: No subscription quota or credits available for optimization.';
            ELSIF remaining_quota > 0 THEN
                RETURN QUERY SELECT FALSE, NULL, 0::numeric, NULL, format('optimization_quota_insufficient: Subscription quota exhausted. Available: %.2f units', remaining_quota);
            ELSE
                RETURN QUERY SELECT FALSE, NULL, 0::numeric, NULL, format('optimization_credits_insufficient: Credits insufficient. Available: %.2f credits', credit_balance);
            END IF;
            RETURN;
        END IF;
    END IF;

    -- Default: deny with specific service information
    RETURN QUERY SELECT FALSE, NULL, 0::numeric, NULL, format('%s_no_pricing: No active pricing found for service "%s" with model "%s"', p_service, p_service, p_model);
END;
$function$

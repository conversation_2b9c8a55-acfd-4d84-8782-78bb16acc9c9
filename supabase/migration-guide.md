# VoiceHype Migration Guide to Supabase API

This guide outlines the steps needed to migrate the VoiceHype extension from direct service calls to our new Supabase API system with authentication and usage tracking.

## Overview

The new architecture will:
1. Use API keys for authentication
2. Track usage and billing through Supabase
3. Support multiple pricing models (pay-as-you-go, subscription, etc.)
4. Provide more reliable services through standardized API endpoints

## Required Changes

### 1. Add Configuration Options

Add the following to package.json configuration section:

```json
"voicehype.api.key": {
  "type": "string",
  "default": "",
  "description": "API key for VoiceHype services"
},
"voicehype.api.url": {
  "type": "string",
  "default": "https://YOUR_SUPABASE_PROJECT.supabase.co/functions/v1",
  "description": "Base URL for VoiceHype API"
}
```

### 2. Modify Extension.ts

Update the extension to load and use the API key:

```typescript
// Add to constructor or initialization
this.apiKey = vscode.workspace.getConfiguration('voicehype.api').get('key') || '';
this.apiUrl = vscode.workspace.getConfiguration('voicehype.api').get('url') || '';

// Add methods to check API key
private async checkApiKey(): Promise<boolean> {
  if (!this.apiKey) {
    await this.promptForApiKey();
    return false;
  }
  return true;
}

private async promptForApiKey(): Promise<void> {
  const getKey = 'Get API Key';
  const enterKey = 'Enter API Key';
  
  const selection = await vscode.window.showInformationMessage(
    'VoiceHype requires an API key to function. Please get an API key or enter an existing one.',
    getKey,
    enterKey
  );
  
  if (selection === getKey) {
    vscode.env.openExternal(vscode.Uri.parse('https://voicehype.com/dashboard'));
  } else if (selection === enterKey) {
    const key = await vscode.window.showInputBox({
      prompt: 'Enter your VoiceHype API key',
      password: true
    });
    
    if (key) {
      await vscode.workspace.getConfiguration('voicehype.api').update('key', key, true);
      this.apiKey = key;
    }
  }
}
```

### 3. Replace transcription and optimization service calls

Replace direct service calls with API endpoints.

**Transcription:**

```typescript
async function transcribeWithApi(audioFilePath: string, options: any): Promise<string> {
  try {
    // Read audio file
    const audioBuffer = await fs.promises.readFile(audioFilePath);
    const base64Audio = audioBuffer.toString('base64');
    
    // Prepare request
    const response = await fetch(`${this.apiUrl}/transcribe`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        audio: `data:audio/wav;base64,${base64Audio}`,
        service: options.service,
        model: options.model,
        language: options.language,
        sampleRate: options.sampleRate
      })
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`API Error: ${errorData.error}`);
    }
    
    const data = await response.json();
    return data.data.text;
  } catch (error) {
    console.error('Transcription error:', error);
    throw error;
  }
}
```

**Optimization:**

```typescript
async function optimizeWithApi(text: string, model: string): Promise<string> {
  try {
    const response = await fetch(`${this.apiUrl}/optimize`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        text: text,
        model: model
      })
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`API Error: ${errorData.error}`);
    }
    
    const data = await response.json();
    return data.data.text;
  } catch (error) {
    console.error('Optimization error:', error);
    throw error;
  }
}
```

**Combined Service:**

```typescript
async function transcribeAndOptimizeWithApi(audioFilePath: string, options: any): Promise<{transcribedText: string, optimizedText: string}> {
  try {
    // Read audio file
    const audioBuffer = await fs.promises.readFile(audioFilePath);
    const base64Audio = audioBuffer.toString('base64');
    
    // Prepare request
    const response = await fetch(`${this.apiUrl}/transcribe-optimize`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        audio: `data:audio/wav;base64,${base64Audio}`,
        transcriptionService: options.service,
        transcriptionModel: options.model,
        language: options.language,
        sampleRate: options.sampleRate,
        optimizationModel: options.optimizationModel
      })
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`API Error: ${errorData.error}`);
    }
    
    const data = await response.json();
    return {
      transcribedText: data.data.transcribedText,
      optimizedText: data.data.optimizedText
    };
  } catch (error) {
    console.error('Combined service error:', error);
    throw error;
  }
}
```

### 4. Update the processRecording method

Modify the processRecording method to use the new API endpoints:

```typescript
private async processRecording(shouldOptimize: boolean = false) {
  try {
    if (!await this.checkApiKey()) {
      return;
    }
    
    // Existing validation code...
    
    if (shouldOptimize) {
      // Use combined endpoint
      const { transcribedText, optimizedText } = await this.transcribeAndOptimizeWithApi(
        this.recordingPath,
        {
          service: service,
          model: model,
          language: language,
          sampleRate: sampleRate,
          optimizationModel: optimizationModel
        }
      );
      
      // Process results...
    } else {
      // Use transcription-only endpoint
      const transcribedText = await this.transcribeWithApi(
        this.recordingPath,
        {
          service: service,
          model: model,
          language: language,
          sampleRate: sampleRate
        }
      );
      
      // Process results...
    }
  } catch (error) {
    // Error handling...
  }
}
```

### 5. Add API Key Management in Settings

Add a user interface for managing API keys:

```typescript
async function showApiKeySettings() {
  const items = [
    {
      label: 'Enter API Key',
      description: 'Set your VoiceHype API key',
    },
    {
      label: 'View Documentation',
      description: 'Learn how to get an API key',
    },
    {
      label: 'Open Dashboard',
      description: 'Manage your API keys and usage',
    }
  ];
  
  const selection = await vscode.window.showQuickPick(items, {
    placeHolder: 'Manage API Key'
  });
  
  if (!selection) {
    return;
  }
  
  switch (selection.label) {
    case 'Enter API Key':
      await this.promptForApiKey();
      break;
    case 'View Documentation':
      vscode.env.openExternal(vscode.Uri.parse('https://voicehype.com/docs'));
      break;
    case 'Open Dashboard':
      vscode.env.openExternal(vscode.Uri.parse('https://voicehype.com/dashboard'));
      break;
  }
}
```

## Deployment Guide

1. Run the schema.sql in Supabase SQL Editor to create all required tables
2. Deploy the three edge functions to Supabase (transcribe, optimize, transcribe-optimize)
3. Set up environment variables in Supabase:
   - ASSEMBLYAI_API_KEY
   - OPENAI_API_KEY
   - REPLICATE_API_TOKEN
4. Update your extension settings to point to the new API URL
5. Test the functionality with a sample API key

## API Key Management in User Dashboard

We should create a separate web application for users to:
1. Sign up and manage their account
2. Generate and revoke API keys
3. View usage statistics
4. Purchase credits or subscription plans
5. Set up billing information

This web application can be built using Next.js and Supabase Auth for authentication.

## Conclusion

This migration will allow us to monetize the VoiceHype extension while providing users with a reliable and scalable service. By tracking usage, we can offer different pricing tiers and ensure our services are being properly billed.

The biggest change is switching from direct service calls to authenticated API endpoints that handle the logic of which service to use and manage billing accordingly. 
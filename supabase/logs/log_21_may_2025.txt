ssemblyAISocketState: 1,
  isConnected: true,
  audioBytesSent: 0,
  transcriptionReceived: false,
  sessionDuration: "18s"
}

[Info] [DEBUG] Sent heartbeat #1 to AssemblyAI for session 4e8d3434-de8f-4baa-b62f-c095595e8488

[Info] [DEBUG] Sent heartbeat status to client for session 4e8d3434-de8f-4baa-b62f-c095595e8488

[Info] [DEBUG] Heartbeat check for session 703cc3bb-65e3-49b2-b110-bf810de84bcb at 2025-05-21T09:26:29.270Z: {
  timeSinceLastClientMessage: "2s",
  timeSinceLastAssemblyAIMessage: "14s",
  heartbeatsSent: 0,
  heartbeatResponsesReceived: 0,
  responseRate: 0,
  clientSocketState: 1,
  assemblyAISocketState: 1,
  isConnected: true,
  audioBytesSent: 0,
  transcriptionReceived: false,
  sessionDuration: "18s"
}

[Info] [DEBUG] Sent heartbeat #1 to AssemblyA<PERSON> for session 703cc3bb-65e3-49b2-b110-bf810de84bcb

[Info] [DEBUG] Sent heartbeat status to client for session 703cc3bb-65e3-49b2-b110-bf810de84bcb

[Info] [DEBUG] Received AssemblyAI message for session 703cc3bb-65e3-49b2-b110-bf810de84bcb at 2025-05-21T09:26:29.511Z: {
  type: "unknown",
  hasText: false,
  textLength: 0,
  timeSinceSessionStart: "18s",
  audioBytesSent: 0
}

[Info] [DEBUG] Received unknown message type from AssemblyAI: unknown for session 703cc3bb-65e3-49b2-b110-bf810de84bcb

[Info] AssemblyAI WebSocket closed for session 703cc3bb-65e3-49b2-b110-bf810de84bcb {
  code: 4101,
  reason: "Received invalid request. Please check the documentation for the correct request schemas.",
  wasClean: true,
  transcriptionReceived: false,
  audioBytesSent: 0,
  durationMs: 17971
}

[Error] AssemblyAI error: Invalid request format - Received invalid request. Please check the documentation for the correct request schemas.

[Info] Using session duration for session 703cc3bb-65e3-49b2-b110-bf810de84bcb (no audio samples processed): { sessionDuration: 17.972, audioBytesSent: 0 }

[Info] Finalizing session 703cc3bb-65e3-49b2-b110-bf810de84bcb for model assemblyai/best-realtime {
  audioBytesSent: 0,
  transcriptionReceived: false,
  durationMs: 17973,
  hasTokenInfo: false,
  processedDurationSeconds: 17.972
}

[Info] Final transcript for session 703cc3bb-65e3-49b2-b110-bf810de84bcb: No transcript available

[Info] Pricing model for session 703cc3bb-65e3-49b2-b110-bf810de84bcb: credits

[Info] [DEBUG] Received AssemblyAI message for session 4e8d3434-de8f-4baa-b62f-c095595e8488 at 2025-05-21T09:26:29.540Z: {
  type: "unknown",
  hasText: false,
  textLength: 0,
  timeSinceSessionStart: "18s",
  audioBytesSent: 0
}

[Info] [DEBUG] Received unknown message type from AssemblyAI: unknown for session 4e8d3434-de8f-4baa-b62f-c095595e8488

[Info] AssemblyAI WebSocket closed for session 4e8d3434-de8f-4baa-b62f-c095595e8488 {
  code: 4101,
  reason: "Received invalid request. Please check the documentation for the correct request schemas.",
  wasClean: true,
  transcriptionReceived: false,
  audioBytesSent: 0,
  durationMs: 18192
}

[Error] AssemblyAI error: Invalid request format - Received invalid request. Please check the documentation for the correct request schemas.

[Info] Using session duration for session 4e8d3434-de8f-4baa-b62f-c095595e8488 (no audio samples processed): { sessionDuration: 18.192, audioBytesSent: 0 }

[Info] Finalizing session 4e8d3434-de8f-4baa-b62f-c095595e8488 for model assemblyai/best-realtime {
  audioBytesSent: 0,
  transcriptionReceived: false,
  durationMs: 18192,
  hasTokenInfo: false,
  processedDurationSeconds: 18.192
}

[Info] Final transcript for session 4e8d3434-de8f-4baa-b62f-c095595e8488: No transcript available

[Info] Successfully updated pricing model to credits for session 703cc3bb-65e3-49b2-b110-bf810de84bcb

[Info] Pricing model for session 4e8d3434-de8f-4baa-b62f-c095595e8488: credits

[Info] Successfully updated pricing model to credits for session 4e8d3434-de8f-4baa-b62f-c095595e8488

[Info] Successfully finalized session 703cc3bb-65e3-49b2-b110-bf810de84bcb with duration 0.3 minutes, cost 0.00276, and pricing model credits

[Info] Successfully finalized session 4e8d3434-de8f-4baa-b62f-c095595e8488 with duration 0.3 minutes, cost 0.00276, and pricing model credits

[Info] Client WebSocket closed for session 703cc3bb-65e3-49b2-b110-bf810de84bcb

[Info] Using session duration for session 703cc3bb-65e3-49b2-b110-bf810de84bcb (no audio samples processed): { sessionDuration: 18.216, audioBytesSent: 0 }

[Info] Finalizing session 703cc3bb-65e3-49b2-b110-bf810de84bcb for model assemblyai/best-realtime {
  audioBytesSent: 0,
  transcriptionReceived: false,
  durationMs: 18217,
  hasTokenInfo: false,
  processedDurationSeconds: 18.216
}

thread 'main' panicked at /usr/src/edge-runtime/crates/base/src/worker/worker_surface_creation.rs:215:17:
internal error: entered unreachable code: coping between upgraded connections failed: Err(Os { code: 107, kind: NotConnected, message: "Transport endpoint is not connected" })
[Info] Pricing model for session 703cc3bb-65e3-49b2-b110-bf810de84bcb: credits

[Info] Successfully updated pricing model to credits for session 703cc3bb-65e3-49b2-b110-bf810de84bcb

[Info] Successfully finalized session 703cc3bb-65e3-49b2-b110-bf810de84bcb with duration 0.3 minutes, cost 0.00276, and pricing model credits

[Info] Client WebSocket closed for session 4e8d3434-de8f-4baa-b62f-c095595e8488

[Info] Using session duration for session 4e8d3434-de8f-4baa-b62f-c095595e8488 (no audio samples processed): { sessionDuration: 19.114, audioBytesSent: 0 }

[Info] Finalizing session 4e8d3434-de8f-4baa-b62f-c095595e8488 for model assemblyai/best-realtime {
  audioBytesSent: 0,
  transcriptionReceived: false,
  durationMs: 19116,
  hasTokenInfo: false,
  processedDurationSeconds: 19.114
}

thread 'main' panicked at /usr/src/edge-runtime/crates/base/src/worker/worker_surface_creation.rs:215:17:
internal error: entered unreachable code: coping between upgraded connections failed: Err(Os { code: 107, kind: NotConnected, message: "Transport endpoint is not connected" })
[Info] Pricing model for session 4e8d3434-de8f-4baa-b62f-c095595e8488: credits

[Info] Successfully updated pricing model to credits for session 4e8d3434-de8f-4baa-b62f-c095595e8488

[Info] Successfully finalized session 4e8d3434-de8f-4baa-b62f-c095595e8488 with duration 0.32 minutes, cost 0.002944, and pricing model credits

serving the request with /home/<USER>/functions/transcribe
[Info] Routing to real-time transcription handler

[Info] Received realtime transcription request: {
  url: "http://functions:9000/transcribe/realtime?apiKey=vhkey_4RmioiT9fnCMvKAYLNO9s8p4fU0LxVVX&service=assemblyai&model=best&language=en_us",
  method: "GET",
  upgrade: "websocket",
  origin: "https://supabase.voicehype.ai",
  host: "functions:9000"
}

[Info] Request parameters: {
  service: "assemblyai",
  model: "best",
  language: "en_us",
  hasApiKey: true,
  apiKeyLength: 38
}

[Info] Validating API key: { keyLength: 38, keyPrefix: "vhkey..." }

[Info] API key validated successfully for user: b8970ee6-6d52-4590-ba68-1ed7303b0f51

[Info] Real-time transcription request: service=assemblyai, model=best, language=en_us

[Info] Usage allowance check result: {
  modelChecked: "assemblyai/best-realtime",
  result: {
    can_use: true,
    pricing_model: "credits",
    cost: 0.0092,
    max_output_tokens: null,
    error_code: null
  },
  error: null
}

[Info] Service pricing check: {
  modelRequested: "assemblyai/best-realtime",
  found: true,
  pricing: { cost_per_unit: 0.0092, unit: "minute" },
  error: null
}

[Info] Available transcription minutes: {
  userId: "b8970ee6-6d52-4590-ba68-1ed7303b0f51",
  creditsBalance: 3.749088821,
  costPerMinute: 0.0092,
  availableMinutes: 407,
  maxSessionMinutes: 20,
  modelRequested: "assemblyai/best-realtime"
}

[Info] Ready to upgrade WebSocket connection, for service: assemblyai model: best

[Info] Successfully upgraded WebSocket connection for session 756caa38-169a-4d5e-a7fc-8b3452c6fc44

[Info] [DEBUG] Starting AssemblyAI connection setup for session 756caa38-169a-4d5e-a7fc-8b3452c6fc44 at 2025-05-21T09:26:30.786Z {
  model: "best",
  userId: "b8970ee6-6d52-4590-ba68-1ed7303b0f51",
  apiKeyId: "c04435b9-591f-45a8-af5f-a563df090810",
  availableMinutes: 407,
  maxDurationMs: 1200000,
  modelForPricing: "assemblyai/best-realtime",
  sessionStartTime: "2025-05-21T09:26:30.786Z"
}

[Info] Initial pricing model for session 756caa38-169a-4d5e-a7fc-8b3452c6fc44: credits

[Info] Recorded pending usage for session 756caa38-169a-4d5e-a7fc-8b3452c6fc44 with pricing model credits

[Info] Generating temporary token for AssemblyAI

[Info] Successfully generated AssemblyAI temporary token

[Info] Got AssemblyAI token for session 756caa38-169a-4d5e-a7fc-8b3452c6fc44: { tokenLength: 64, tokenPrefix: "5c2cd...", expirationSeconds: 1800 }

[Info] Creating AssemblyAI WebSocket connection for session 756caa38-169a-4d5e-a7fc-8b3452c6fc44

[Info] [DEBUG] Cannot send complete transcript for session 756caa38-169a-4d5e-a7fc-8b3452c6fc44: Socket not open (state: 0)

serving the request with /home/<USER>/functions/transcribe
[Info] Routing to real-time transcription handler


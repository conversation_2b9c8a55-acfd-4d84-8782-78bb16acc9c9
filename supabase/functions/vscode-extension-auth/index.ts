// vscode-extension-auth.ts - Single Supabase Edge Function
// This function creates an API key for authenticated users and redirects back to VS Code

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.0';

// Initialize the Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';

// Create a Supabase client with the service role key for admin access
const supabase = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Function to create a new API key for a user
 * Always creates a NEW API key as required by security policy
 */
async function createApiKey(userId: string): Promise<{ apiKey: string; keyId: string } | null> {
  try {
    // Generate a friendly name for the VS Code extension key
    const name = `VS Code Extension (${new Date().toISOString().split('T')[0]})`;
    
    // Temporarily set the auth context for the RPC call
    const { data: userData, error: userError } = await supabase.auth.admin.getUserById(userId);
    if (userError) {
      console.error('Error getting user:', userError);
      return null;
    }

    // Create a client session for this user to make the RPC call
    const { data: sessionData, error: sessionError } = await supabase.auth.admin.generateLink({
      type: 'magiclink',
      email: userData.user.email!,
    });

    if (sessionError) {
      console.error('Error creating session:', sessionError);
      return null;
    }

    // Create a new client with the user's session
    const userSupabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    });

    // Set the session manually
    await userSupabase.auth.setSession({
      access_token: sessionData.properties.access_token!,
      refresh_token: sessionData.properties.refresh_token!,
    });

    // Call the create_api_key RPC function with the user's context
    const { data, error } = await userSupabase.rpc('create_api_key', {
      p_name: name,
      // Set expiration to 1 year from now for security
      p_expires_at: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()
    });
    
    if (error) {
      console.error('Error creating API key:', error);
      return null;
    }
    
    // The RPC function returns an api_key_result type with id and key_secret
    if (data && data.id && data.key_secret) {
      return {
        apiKey: `vhkey_${data.key_secret}`, // Add the vhkey_ prefix
        keyId: data.id
      };
    }
    
    console.error('Invalid response from create_api_key:', data);
    return null;
  } catch (err) {
    console.error('Exception creating API key:', err);
    return null;
  }
}

/**
 * Handler for the VS Code extension authorization
 */
serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
      status: 204,
    });
  }

  try {
    // Get the request URL and parse query parameters
    const url = new URL(req.url);
    const userId = url.searchParams.get('user_id');
    const state = url.searchParams.get('state');
    const vscodeRedirect = url.searchParams.get('redirect_uri');
    const error = url.searchParams.get('error');
    
    // Handle authorization errors
    if (error) {
      console.error('Authorization error:', error);
      
      if (vscodeRedirect && state) {
        const redirectUrl = new URL(vscodeRedirect);
        redirectUrl.searchParams.append('state', state);
        redirectUrl.searchParams.append('error', error);
        
        return new Response(null, {
          status: 302,
          headers: { 'Location': redirectUrl.toString() },
        });
      }
      
      return new Response(
        JSON.stringify({ error: `Authorization error: ${error}` }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }
    
    // Validate the required parameters
    if (!userId || !state || !vscodeRedirect) {
      return new Response(
        JSON.stringify({ error: 'Missing required parameters: user_id, state, and redirect_uri are required' }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }
    
    // Create an API key for the user
    const apiKeyResult = await createApiKey(userId);
    
    if (!apiKeyResult) {
      // Redirect back to VS Code with error
      const redirectUrl = new URL(vscodeRedirect);
      redirectUrl.searchParams.append('state', state);
      redirectUrl.searchParams.append('error', 'Failed to create API key');
      
      return new Response(null, {
        status: 302,
        headers: { 'Location': redirectUrl.toString() },
      });
    }
    
    // Get user information for the response
    const { data: userData, error: userError } = await supabase.auth.admin.getUserById(userId);
    
    if (userError || !userData?.user) {
      console.error('Error getting user info:', userError);
      const redirectUrl = new URL(vscodeRedirect);
      redirectUrl.searchParams.append('state', state);
      redirectUrl.searchParams.append('error', 'Invalid user');
      
      return new Response(null, {
        status: 302,
        headers: { 'Location': redirectUrl.toString() },
      });
    }
    
    const user = userData.user;
    const fullName = user.user_metadata?.full_name;
    const avatarUrl = user.user_metadata?.avatar_url;
    
    // Build the redirect URL back to VS Code
    const redirectUrl = new URL(vscodeRedirect);
    
    // Add the parameters for the VS Code extension
    redirectUrl.searchParams.append('state', state);
    redirectUrl.searchParams.append('api_key', apiKeyResult.apiKey);
    redirectUrl.searchParams.append('user_id', user.id);
    redirectUrl.searchParams.append('email', user.email || '');
    
    // Add optional user metadata if available
    if (fullName) {
      redirectUrl.searchParams.append('full_name', fullName);
    }
    
    if (avatarUrl) {
      redirectUrl.searchParams.append('avatar_url', avatarUrl);
    }
    
    // Create an HTML page with automatic redirect to VS Code
    const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>VoiceHype - Authorization Complete</title>
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100vh;
          margin: 0;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          text-align: center;
        }
        .container {
          background: rgba(255, 255, 255, 0.1);
          backdrop-filter: blur(10px);
          padding: 3rem;
          border-radius: 20px;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          max-width: 500px;
        }
        h1 {
          margin-bottom: 1rem;
          font-size: 2rem;
          font-weight: 600;
        }
        p {
          margin-bottom: 2rem;
          line-height: 1.6;
          opacity: 0.9;
        }
        .btn {
          display: inline-block;
          background: rgba(255, 255, 255, 0.2);
          color: white;
          border: 2px solid rgba(255, 255, 255, 0.3);
          padding: 0.75rem 2rem;
          font-size: 1rem;
          border-radius: 50px;
          cursor: pointer;
          text-decoration: none;
          transition: all 0.3s ease;
          backdrop-filter: blur(10px);
        }
        .btn:hover {
          background: rgba(255, 255, 255, 0.3);
          transform: translateY(-2px);
        }
        .logo {
          width: 80px;
          height: 80px;
          margin-bottom: 1.5rem;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.1);
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 2rem;
          margin: 0 auto 1.5rem;
        }
        .spinner {
          border: 3px solid rgba(255, 255, 255, 0.3);
          border-radius: 50%;
          border-top: 3px solid white;
          width: 20px;
          height: 20px;
          animation: spin 1s linear infinite;
          margin: 0 auto 1rem;
        }
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="logo">🎤</div>
        <h1>Authorization Successful!</h1>
        <p>You have successfully authorized VoiceHype for VS Code. You will be redirected back to VS Code automatically.</p>
        <div class="spinner"></div>
        <p style="font-size: 0.9rem; opacity: 0.7;">If you're not redirected automatically, click the button below:</p>
        <a href="${redirectUrl.toString()}" class="btn">Open VS Code</a>
      </div>
      <script>
        // Automatically redirect after a short delay
        setTimeout(() => {
          window.location.href = "${redirectUrl.toString()}";
        }, 2000);
      </script>
    </body>
    </html>
    `;
    
    return new Response(html, {
      headers: { 'Content-Type': 'text/html' },
    });
  } catch (error) {
    console.error('Error in vscode-extension-auth function:', error);
    
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      }
    );
  }
});

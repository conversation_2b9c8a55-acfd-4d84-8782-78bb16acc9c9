// email-service.ts for Deno Deploy
import { serve } from "https://deno.land/std@0.140.0/http/server.ts";
import { SmtpClient } from "https://deno.land/x/smtp@v0.7.0/mod.ts";

const ALLOWED_ORIGINS = [
  "https://supabase.voicehype.ai",
  "https://voicehype.ai",
];

// Email configuration
const SMTP_HOST = Deno.env.get("SMTP_HOST") || "smtpout.secureserver.net";
const SMTP_PORT = parseInt(Deno.env.get("SMTP_PORT") || "465");
const SMTP_USER = Deno.env.get("SMTP_USER") || "<EMAIL>";
const SMTP_PASS = Deno.env.get("SMTP_PASS") || "";
const FROM_EMAIL = Deno.env.get("FROM_EMAIL") || "<EMAIL>";
const FROM_NAME = Deno.env.get("FROM_NAME") || "VoiceHype";
const WEBHOOK_SECRET = Deno.env.get("WEBHOOK_SECRET") || "";

// Email template generator
function generateEmailContent(actionType: string, user: any, emailData: any) {
  const userName = user.name || user.email;
  const siteUrl = emailData.site_url || "https://voicehype.ai";
  
  switch (actionType) {
    case 'welcome':
      return {
        subject: 'Welcome to VoiceHype!',
        text: `Welcome ${userName}!\n\nThank you for signing up for VoiceHype. We're excited to have you on board.`,
        html: `<h1>Welcome to VoiceHype!</h1>
               <p>Dear ${userName},</p>
               <p>Thank you for signing up for VoiceHype. We're excited to have you on board.</p>`
      };
      
    case 'free_trial_expired':
      return {
        subject: 'Your Free Trial Has Expired',
        text: `Hi ${userName},\n\nYour free trial has expired. Why not upgrade to a package? The basic package starts at only $4.99.\n\nVisit ${siteUrl}/payments to learn more.`,
        html: `<p>Hi ${userName},</p>
               <p>Your free trial has expired.</p>
               <p>Why not upgrade to a package? The basic package starts at only $4.99.</p>
               <p>Visit <a href="${siteUrl}/payments">${siteUrl}/payments</a> to learn more.</p>`
      };
      
    default:
      throw new Error(`Unknown email action type: ${actionType}`);
  }
}

async function sendEmail(emailData: any) {
  const client = new SmtpClient();
  
  await client.connectTLS({
    hostname: SMTP_HOST,
    port: SMTP_PORT,
    username: SMTP_USER,
    password: SMTP_PASS,
  });
  
  await client.send({
    from: `${FROM_NAME} <${FROM_EMAIL}>`,
    to: emailData.email,
    subject: emailData.subject,
    content: emailData.content,
    html: emailData.html,
  });
  
  await client.close();
  return { success: true };
}

serve(async (req) => {
  // CORS handling
  const origin = req.headers.get("Origin") || "";
  if (!ALLOWED_ORIGINS.includes(origin)) {
    return new Response("Not allowed", { status: 403 });
  }
  
  const corsHeaders = {
    "Access-Control-Allow-Origin": origin,
    "Access-Control-Allow-Methods": "POST, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization",
  };
  
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }
  
  // Webhook secret validation
  const authHeader = req.headers.get("Authorization") || "";
  const providedSecret = authHeader.replace("Bearer ", "");
  
  if (WEBHOOK_SECRET && providedSecret !== WEBHOOK_SECRET) {
    return new Response("Unauthorized", { status: 401 });
  }
  
  if (req.method === "POST") {
    try {
      const data = await req.json();
      
      // Extract user and email data
      const { user, email_data } = data;
      
      if (!user || !email_data || !email_data.email_action_type) {
        throw new Error("Invalid payload structure");
      }
      
      // Generate email content based on action type
      const { subject, text, html } = generateEmailContent(
        email_data.email_action_type,
        user,
        email_data
      );
      
      // Send email
      await sendEmail({
        email: user.email,
        subject,
        content: text,
        html,
      });
      
      return new Response(JSON.stringify({ success: true }), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    } catch (error) {
      console.error("Email sending error:", error);
      return new Response(JSON.stringify({ error: error.message }), {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }
  }
  
  return new Response("Method not allowed", { status: 405, headers: corsHeaders });
});
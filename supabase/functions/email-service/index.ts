// email-service for Supabase Edge Functions with comprehensive logging
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { Webhook } from "https://esm.sh/standardwebhooks@1.0.0";

// Email configuration - Store these as Supabase Edge Function environment variables
const ELASTICEMAIL_API_KEY = Deno.env.get("ELASTICEMAIL_API_KEY") || "";
const RESEND_API_KEY = Deno.env.get("RESEND_API_KEY") || "";
const FROM_EMAIL = Deno.env.get("FROM_EMAIL") || "<EMAIL>";
const FROM_NAME = Deno.env.get("FROM_NAME") || "VoiceHype";
const hookSecret = (Deno.env.get("SEND_EMAIL_HOOK_SECRET") as string).replace("v1,whsec_", "");

// Supabase configuration
const SUPABASE_URL = Deno.env.get("API_EXTERNAL_URL") || "https://supabase.voicehype.ai";
const SITE_URL = Deno.env.get("SITE_URL") || "https://voicehype.ai";

function logSection(title: string, data?: any) {
  console.log(`\n=== ${title.toUpperCase()} ===`);
  if (data) {
    console.log(JSON.stringify(data, null, 2));
  }
  console.log(`=== END ${title.toUpperCase()} ===\n`);
}

async function sendEmail(emailData: any) {
  logSection("SENDING EMAIL", {
    to: emailData.email,
    subject: emailData.subject,
    htmlPreview: emailData.html?.substring(0, 200) + "...",
    useTemplate: emailData.useTemplate || false,
    mergeFields: emailData.mergeFields || {}
  });
  
  if (!ELASTICEMAIL_API_KEY) {
    console.log("⚠️ No ELASTICEMAIL_API_KEY configured. Falling back to Resend.");
    return sendEmailViaResend(emailData);
  }
  
  try {
    const elasticEmailData = {
      Recipients: [
        {
          email: emailData.email
        }
      ],
      Content: {
        From: `${FROM_NAME} <${FROM_EMAIL}>`,
        Subject: emailData.subject,
        EnvelopeFrom: FROM_EMAIL,
        ReplyTo: `${FROM_NAME} <${FROM_EMAIL}>`
      },
      Options: {
        TrackOpens: "true",
        TrackClicks: "true"
      }
    };
    
    // Use template for welcome emails, HTML for others
    if (emailData.useTemplate) {
      (elasticEmailData.Content as any).TemplateName = "Welcome to VoiceHype";
      
      // Add merge fields if provided
      if (emailData.mergeFields) {
        (elasticEmailData.Content as any).Merge = emailData.mergeFields;
      }
    } else {
      (elasticEmailData.Content as any).Body = [
        {
          ContentType: "HTML",
          Content: emailData.html || emailData.content
        }
      ];
    }
    
    const response = await fetch("https://api.elasticemail.com/v4/emails", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-ElasticEmail-ApiKey": ELASTICEMAIL_API_KEY
      },
      body: JSON.stringify(elasticEmailData)
    });
    
    const result = await response.json();
    if (!response.ok) {
      console.error("❌ Failed to send email via ElasticEmail:", result);
      throw new Error(`Email sending failed: ${JSON.stringify(result)}`);
    }
    console.log("✅ Email sent successfully via ElasticEmail:", result);
    return { success: true, emailId: result.MessageID, transactionId: result.TransactionID };
  } catch (error) {
    console.error("❌ Email sending error via ElasticEmail:", error);
    // Fallback to Resend if ElasticEmail fails
    console.log("🔄 Falling back to Resend...");
    return sendEmailViaResend(emailData);
  }
}

async function sendEmailViaResend(emailData: any) {
  if (!RESEND_API_KEY) {
    console.log("⚠️ No RESEND_API_KEY configured. Email not actually sent.");
    return { success: true, note: "Email not actually sent - no API key" };
  }
  
  try {
    // For template emails, generate HTML content with first name
    let htmlContent = emailData.html || emailData.content;
    
    if (emailData.useTemplate && emailData.mergeFields) {
      // If using a template but falling back to Resend, generate the HTML with the first name
      const firstName = emailData.mergeFields.firstname || 'there';
      htmlContent = getWelcomeEmailHTML({ 
        user_metadata: { full_name: firstName } 
      }, {});
    }
    
    const response = await fetch("https://api.resend.com/emails", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${RESEND_API_KEY}`
      },
      body: JSON.stringify({
        from: `${FROM_NAME} <${FROM_EMAIL}>`,
        to: emailData.email,
        subject: emailData.subject,
        html: htmlContent,
      })
    });
    
    const result = await response.json();
    if (!response.ok) {
      console.error("❌ Failed to send email via Resend:", result);
      throw new Error(`Email sending failed: ${JSON.stringify(result)}`);
    }
    console.log("✅ Email sent successfully via Resend:", result);
    return { success: true, emailId: result.id };
  } catch (error) {
    console.error("❌ Email sending error via Resend:", error);
    throw error;
  }
}

serve(async (req) => {
  const timestamp = new Date().toISOString();
  console.log(`\n🚀 Request received at ${timestamp}`);
  console.log(`📍 Method: ${req.method}`);
  console.log(`🔗 URL: ${req.url}`);
  console.log(`🌐 Origin: ${req.headers.get("Origin") || "No Origin"}`);
  console.log(`🔑 User-Agent: ${req.headers.get("User-Agent") || "No User-Agent"}`);

  // Verify auth for POST/GET requests
  if (req.method === "POST" || req.method === "GET") {
    const authHeader = req.headers.get("Authorization");
    const isFromDatabaseTrigger = req.headers.get("User-Agent")?.includes("Supabase-Database-Trigger");

    // Handle database trigger requests (legacy auth)
    if (isFromDatabaseTrigger) {
      if (!authHeader?.startsWith("Bearer ")) {
        console.log("❌ Missing Bearer token from database trigger");
        return new Response("Unauthorized", { status: 401 });
      }
      const token = authHeader.substring(7);
      if (token !== hookSecret) {
        console.log("❌ Invalid auth token from database trigger");
        return new Response("Unauthorized", { status: 401 });
      }
      console.log("✅ Verified database trigger request");
      // Don't return here - just continue processing
    }
    // Handle standard webhook requests (only if not database trigger)
    else {
      try {
        const payload = await req.text();
        const headers = Object.fromEntries(req.headers);
        const wh = new Webhook(hookSecret);
        const { user, email_data } = wh.verify(payload, headers) as {
          user: { email: string };
          email_data: {
            token: string;
            token_hash: string;
            redirect_to: string;
            email_action_type: string;
            site_url: string;
            token_new: string;
            token_hash_new: string;
          };
        };
        // Put request body back so it can be read again
        req = new Request(req.url, {
          method: req.method,
          headers: req.headers,
          body: payload,
        });
        console.log("✅ Verified webhook request");
      } catch (error) {
        console.log("❌ Webhook verification failed:", error);
        return new Response("Unauthorized", { status: 401 });
      }
    }
  }

  const corsHeaders = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
    "Access-Control-Allow-Headers": "*",
    "Access-Control-Allow-Credentials": "true"
  };


  // Handle preflight requests
  if (req.method === "OPTIONS") {
    console.log("✅ Handling OPTIONS preflight request");
    return new Response(null, { headers: corsHeaders });
  }

  // Health check endpoint
  // if (req.method === "GET") {
  //   const url = new URL(req.url);
  //   if (url.pathname === "/health" || url.pathname === "/" || url.pathname === "/test") {
  //     console.log("💚 Health check requested");
  //     return new Response(JSON.stringify({
  //       status: "healthy",
  //       time: timestamp,
  //       service: "VoiceHype Email Service",
  //       environment: {
  //         hasResendKey: !!RESEND_API_KEY,
  //         fromEmail: FROM_EMAIL,
  //         fromName: FROM_NAME,
  //         supabaseUrl: SUPABASE_URL,
  //         siteUrl: SITE_URL,
  //       }
  //     }), {
  //       headers: { ...corsHeaders, "Content-Type": "application/json" },
  //     });
  //   }
  // }

  // // Validate webhook secret
  // const authHeader = req.headers.get("Authorization");
  // const expectedSecret = Deno.env.get("EMAIL_WEBHOOK_SECRET");

  // if (!authHeader || !authHeader.startsWith("Bearer ") ||
  //   authHeader.substring(7) !== expectedSecret) {
  //   console.log("❌ Unauthorized request - invalid webhook secret");
  //   return new Response("Unauthorized", { status: 401 });
  // }

  // // Check if request is from database trigger
  // const isFromDatabaseTrigger = req.headers.get("User-Agent")?.includes("Supabase-Database-Trigger");

  // if (isFromDatabaseTrigger) {
  //   console.log("🔄 Processing authenticated database trigger request");
  //   // Process the welcome email
  // }

  // Handle POST requests (email sending)
  if (req.method === "GET" || req.method === "POST") {
    console.log("📬 Processing email request...");
    try {
      // Read raw request body
      const rawText = await req.text();
      console.log("📝 Raw request body length:", rawText.length);
      console.log("📝 Raw request body:", rawText);

      // Parse JSON
      let data;
      try {
        data = JSON.parse(rawText);
      } catch (e) {
        console.error("❌ Failed to parse request body as JSON:", e);
        return new Response(JSON.stringify({
          error: "Invalid JSON",
          details: e.message,
          receivedData: rawText.substring(0, 500) + "..."
        }), {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        });
      }

      logSection("PARSED WEBHOOK PAYLOAD", data);

      // Extract email information based on the GoTrue webhook format
      let emailData;

      if (data.user && data.email_data) {
        console.log("🔍 Processing Supabase Auth webhook data");

        const user = data.user;
        const emailDataPayload = data.email_data;

        logSection("USER DATA", {
          id: user.id,
          email: user.email,
          created_at: user.created_at,
          email_confirmed_at: user.email_confirmed_at,
        });

        logSection("EMAIL DATA PAYLOAD", emailDataPayload);

        const emailAction = emailDataPayload.email_action_type || "signup";
        const token = emailDataPayload.token || "";
        const tokenHash = emailDataPayload.token_hash || "";
        const redirectTo = `${SITE_URL}/dashboard`;
        const siteUrl = emailDataPayload.site_url || SITE_URL;

        console.log("📊 Extracted values:");
        console.log(`  - Email Action: ${emailAction}`);
        console.log(`  - Token: ${token}`);
        console.log(`  - Token Hash: ${tokenHash}`);
        console.log(`  - Redirect To: ${redirectTo}`);
        console.log(`  - Site URL: ${siteUrl}`);

        // CRITICAL: Use Supabase URL for verification endpoint
        const confirmUrl = `${SUPABASE_URL}/auth/v1/verify?token=${tokenHash}&type=${emailAction}&redirect_to=${encodeURIComponent(redirectTo)}`;

        console.log("🔗 Generated confirmation URL:", confirmUrl);

        // Generate email content based on action type
        let subject, htmlContent;

        switch (emailAction) {
          // Add this case to your existing switch statement
          case 'welcome':
            subject = 'Welcome to VoiceHype! 🎉';
            // Extract first name from user metadata
            const firstName = user.user_metadata?.full_name?.split(' ')[0] || user.email?.split('@')[0] || 'there';

            emailData = {
              email: user.email,
              subject: subject,
              useTemplate: true,
              mergeFields: {
                firstname: firstName
              }
            };
            break;

          // Add this case to your existing switch statement in the email service
          case "free_trial_expired":
            subject = "Your Free Trial Has Expired";
            emailData = {
              email: user.email,
              subject: subject,
              html: getFreeTrialExpiredEmailHTML(user, emailDataPayload)
            };
            break;

          // Add this case to your switch statement in the email service
          case "quota_usage_alert":
            const threshold = emailDataPayload.threshold;
            const serviceName = emailDataPayload.service_name;
            const usagePercentage = emailDataPayload.usage_percentage;
            const usedAmount = emailDataPayload.used_amount;
            const totalAmount = emailDataPayload.total_amount;

            subject = `Quota Usage Alert: ${serviceName}`;

            // Generate appropriate email content based on threshold
            let alertMessage, ctaMessage;

            if (threshold >= 100) {
              alertMessage = `You've used up your entire ${serviceName} quota.`;
              ctaMessage = "Upgrade your plan to continue using our services";
            } else if (threshold >= 90) {
              alertMessage = `You've used ${usagePercentage}% of your ${serviceName} quota.`;
              ctaMessage = "Consider upgrading your plan to avoid service interruption";
            } else {
              alertMessage = `You've used ${usagePercentage}% of your ${serviceName} quota.`;
              ctaMessage = "Monitor your usage or upgrade your plan if needed";
            }

            emailData = {
              email: user.email,
              subject: subject,
              html: getQuotaUsageAlertEmailHTML(user, {
                ...emailDataPayload,
                alertMessage,
                ctaMessage
              })
            };
            break;

          case "signup":
            subject = "Confirm Your VoiceHype Account";
            emailData = {
              email: user.email,
              subject: subject,
              content: `Please confirm your account: ${confirmUrl}`,
              html: `
                <!DOCTYPE html>
                <html>
                <head>
                  <meta charset="utf-8">
                  <title>${subject}</title>
                </head>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                  <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #4CAF50;">Welcome to VoiceHype!</h2>
                    <p>Thank you for signing up. Please confirm your account by clicking the link below:</p>
                    
                    <div style="text-align: center; margin: 30px 0;">
                      <a href="${confirmUrl}" style="display: inline-block; padding: 12px 24px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 6px; font-weight: bold;">Confirm My Account</a>
                    </div>
                    
                    <p>Or copy and paste this URL into your browser:</p>
                    <p style="word-break: break-all; background-color: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace;">${confirmUrl}</p>
                    
                    <p>Your confirmation code is: <strong>${token}</strong></p>
                    
                    <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
                    <p style="font-size: 14px; color: #666;">If you didn't sign up for VoiceHype, you can safely ignore this email.</p>
                  </div>
                </body>
                </html>
              `
            };
            break;

          case "recovery":
            const resetUrl = `${SITE_URL}/reset-password#access_token=${token}&refresh_token=${tokenHash}&type=recovery`;
            subject = "Reset Your VoiceHype Password";
            emailData = {
              email: user.email,
              subject: subject,
              content: `Reset your password: ${resetUrl}`,
              html: `
                <!DOCTYPE html>
                <html>
                <head>
                  <meta charset="utf-8">
                  <title>${subject}</title>
                </head>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                  <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #2196F3;">Password Reset</h2>
                    <p>We received a request to reset your password. Click the link below to set a new password:</p>
                    
                    <div style="text-align: center; margin: 30px 0;">
                      <a href="${resetUrl}" style="display: inline-block; padding: 12px 24px; background-color: #2196F3; color: white; text-decoration: none; border-radius: 6px; font-weight: bold;">Reset My Password</a>
                    </div>
                    
                    <p>Or copy and paste this URL into your browser:</p>
                    <p style="word-break: break-all; background-color: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace;">${resetUrl}</p>
                    
                    <p>Your reset code is: <strong>${token}</strong></p>
                    
                    <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
                    <p style="font-size: 14px; color: #666;">If you didn't request a password reset, you can safely ignore this email.</p>
                  </div>
                </body>
                </html>
              `
            };
            break;

          case "invite":
            subject = "You've been invited to VoiceHype";
            emailData = {
              email: user.email,
              subject: subject,
              content: `Accept your invitation: ${confirmUrl}`,
              html: `
                <!DOCTYPE html>
                <html>
                <head>
                  <meta charset="utf-8">
                  <title>${subject}</title>
                </head>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                  <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #FF9800;">You're Invited!</h2>
                    <p>You've been invited to join VoiceHype. Click the link below to accept the invitation:</p>
                    
                    <div style="text-align: center; margin: 30px 0;">
                      <a href="${confirmUrl}" style="display: inline-block; padding: 12px 24px; background-color: #FF9800; color: white; text-decoration: none; border-radius: 6px; font-weight: bold;">Accept Invitation</a>
                    </div>
                    
                    <p>Or copy and paste this URL into your browser:</p>
                    <p style="word-break: break-all; background-color: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace;">${confirmUrl}</p>
                    
                    <p>Your invitation code is: <strong>${token}</strong></p>
                    
                    <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
                    <p style="font-size: 14px; color: #666;">If you weren't expecting this invitation, you can safely ignore this email.</p>
                  </div>
                </body>
                </html>
              `
            };
            break;

          default:
            subject = "Action Required for Your VoiceHype Account";
            htmlContent = `
              <!DOCTYPE html>
              <html>
              <head>
                <meta charset="utf-8">
                <title>${subject}</title>
              </head>
              <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                  <h2 style="color: #607D8B;">Action Required</h2>
                  <p>Please click the link below to continue:</p>
                  
                  <div style="text-align: center; margin: 30px 0;">
                    <a href="${confirmUrl}" style="display: inline-block; padding: 12px 24px; background-color: #607D8B; color: white; text-decoration: none; border-radius: 6px; font-weight: bold;">Continue</a>
                  </div>
                  
                  <p>Or copy and paste this URL into your browser:</p>
                  <p style="word-break: break-all; background-color: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace;">${confirmUrl}</p>
                  
                  <p>Your verification code is: <strong>${token}</strong></p>
                  
                  <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
                  <p style="font-size: 14px; color: #666;">If you didn't request this action, you can safely ignore this email.</p>
                </div>
              </body>
              </html>
            `;
        }


        logSection("PREPARED EMAIL DATA", {
          email: emailData.email,
          subject: emailData.subject,
          confirmUrl: confirmUrl,
          htmlLength: htmlContent.length,
        });

      } else if (data.email || data.to) {
        // Handle direct email format (fallback)
        console.log("🔍 Processing direct email format");

        emailData = {
          email: data.email || data.to,
          subject: data.subject || "VoiceHype Notification",
          content: data.text || data.content || "",
          html: data.html || `<p>${data.text || data.content || "VoiceHype Notification"}</p>`
        };

        logSection("DIRECT EMAIL DATA", emailData);

      } else {
        console.error("❌ Unrecognized email format");
        logSection("UNRECOGNIZED FORMAT", data);

        return new Response(JSON.stringify({
          error: "Unrecognized email format",
          expectedFormats: [
            "Supabase Auth webhook (user + email_data)",
            "Direct email (email/to + subject + content/html)"
          ],
          receivedKeys: Object.keys(data)
        }), {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        });
      }

      // Send the email
      console.log("📧 Attempting to send email...");
      const result = await sendEmail(emailData);

      const response = {
        success: true,
        message: "Email processed successfully",
        timestamp: timestamp,
        emailSent: result.success,
        emailId: result.emailId || null,
      };

      logSection("RESPONSE", response);

      return new Response(JSON.stringify(response), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });

    } catch (error) {
      console.error("❌ Error processing email request:");
      console.error("Error message:", error.message);
      console.error("Error stack:", error.stack);

      const errorResponse = {
        error: error.message,
        stack: error.stack,
        timestamp: timestamp,
        service: "VoiceHype Email Service"
      };

      logSection("ERROR RESPONSE", errorResponse);

      return new Response(JSON.stringify(errorResponse), {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }
  }

  // Method not allowed
  console.log("❌ Method not allowed:", req.method);
  return new Response(JSON.stringify({
    error: "Method not allowed",
    allowedMethods: ["GET", "POST", "OPTIONS"],
    received: req.method
  }), {
    status: 405,
    headers: { ...corsHeaders, "Content-Type": "application/json" }
  });
});

function getWelcomeEmailHTML(user: any, emailData: any): string {
  const firstName = user.user_metadata?.full_name?.split(' ')[0] || user.email?.split('@')[0] || 'there';

  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to VoiceHype - Your Free Trial is Live!</title>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:AllowPNG/>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    <style>
        /* Fallback fonts for better email client compatibility */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background-color: #000000;
            color: #ffffff;
            line-height: 1.5;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            margin: 0;
            padding: 0;
        }

        table {
            border-collapse: collapse;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }

        img {
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
            -ms-interpolation-mode: bicubic;
            max-width: 100%;
        }

        .email-wrapper {
            width: 100%;
            background-color: #000000;
            padding: 20px 0;
        }

        .email-container {
            width: 100%;
            max-width: 600px;
            margin: 0 auto;
            background-color: #000000;
            border-radius: 20px;
            overflow: hidden;
            border: 1px solid #1a1a1a;
        }

        /* Header with gradient background using table */
        .header-bg {
            background: linear-gradient(135deg, #000000 0%, #0a0a0a 100%);
            background-color: #0a0a0a; /* fallback */
        }

        .hero-title {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            font-size: 54px;
            font-weight: 800;
            letter-spacing: -2px;
            margin: 0;
            padding: 0;
            color: #ffffff;
            text-align: center;
            line-height: 1;
        }

        .subtitle {
            font-size: 26px;
            font-weight: 600;
            color: #ffffff;
            margin: 20px 0 30px 0;
            letter-spacing: -0.5px;
            text-align: center;
        }

        .trial-badge {
            display: inline-block;
            background: linear-gradient(135deg, #14F195 0%, #00ff88 100%);
            background-color: #14F195; /* fallback */
            color: #000000;
            padding: 12px 28px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 700;
            letter-spacing: 0.5px;
            text-decoration: none;
            border: none;
        }

        .greeting {
            font-size: 32px;
            font-weight: 600;
            margin: 0 0 25px 0;
            color: #ffffff;
            letter-spacing: -1px;
        }

        .intro-text {
            font-size: 20px;
            color: #ffffff;
            margin: 0 0 30px 0;
            line-height: 1.6;
            font-weight: 400;
        }

        /* Features using nested tables for better control */
        .feature-container {
            background: linear-gradient(135deg, #1a1a1a 0%, #1f1f1f 100%);
            background-color: #1a1a1a; /* fallback */
            border-radius: 12px;
            padding: 20px;
            margin: 8px;
            text-align: center;
            border: 2px solid #14F195;
        }

        .feature-icon {
            width: 32px;
            height: 32px;
            margin: 0 auto 12px auto;
            display: block;
            color: #14F195;
            font-size: 24px;
            line-height: 32px;
        }

        .feature-title {
            font-size: 15px;
            font-weight: 600;
            color: #ffffff;
            margin: 8px 0 4px 0;
            letter-spacing: -0.3px;
        }

        .video-section-bg {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            background-color: #0a0a0a; /* fallback */
            border-radius: 15px;
            padding: 40px;
            margin: 40px 0;
            text-align: center;
        }

        .video-title {
            font-size: 24px;
            font-weight: 600;
            margin: 0 0 25px 0;
            color: #ffffff;
            letter-spacing: -0.5px;
        }

        .video-thumbnail {
            width: 100%;
            max-width: 480px;
            height: auto;
            border-radius: 12px;
            border: 2px solid #14F195;
        }

        .cta-section-bg {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            background-color: #0a0a0a; /* fallback */
            border-radius: 20px;
            padding: 50px 40px;
            margin: 50px 0;
            text-align: center;
            border: 1px solid #1a1a1a;
        }

        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #14F195 0%, #00ff88 100%);
            background-color: #14F195; /* fallback */
            color: #000000 !important;
            padding: 22px 55px;
            border-radius: 50px;
            font-size: 20px;
            font-weight: 700;
            text-decoration: none;
            margin: 0 0 25px 0;
            border: none;
            letter-spacing: 0.3px;
        }

        .extension-badge {
            margin: 8px;
            height: 40px;
            border-radius: 8px;
            display: inline-block;
            color: white !important;
            padding: 8px 16px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 600;
        }

        .extension-badge.vscode {
            background-color: #0078d4;
        }

        .extension-badge.openvsx {
            background-color: #ff6600;
        }

        .bottom-logo-bg {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            background-color: #0a0a0a; /* fallback */
            padding: 50px 40px;
            text-align: center;
            border-top: 1px solid #1a1a1a;
        }

        .logo-container {
            display: inline-block;
            vertical-align: middle;
        }

        .logo-text {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            font-size: 42px;
            font-weight: 800;
            color: #ffffff;
            letter-spacing: -1px;
            vertical-align: middle;
        }

        .logo-accent {
            color: #14F195;
        }

        .footer-bg {
            background-color: #0a0a0a;
            padding: 40px;
            text-align: center;
            border-top: 1px solid #1a1a1a;
        }

        .footer-text {
            font-size: 16px;
            color: #ffffff;
            margin: 0 0 20px 0;
            font-weight: 400;
        }

        .footer-links a {
            color: #14F195;
            text-decoration: none;
            margin: 0 12px;
            font-weight: 500;
            font-size: 14px;
        }

        /* Mobile responsive */
        @media only screen and (max-width: 600px) {
            .email-container {
                border-radius: 12px;
                margin: 0 10px;
            }
            
            .hero-title {
                font-size: 36px;
            }
            
            .subtitle {
                font-size: 18px;
            }
            
            .greeting {
                font-size: 26px;
            }
            
            .intro-text {
                font-size: 16px;
            }
            
            .cta-button {
                padding: 16px 40px;
                font-size: 18px;
            }
            
            .logo-text {
                font-size: 32px;
            }
            
            .feature-container {
                margin: 4px;
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="email-wrapper">
        <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%" style="background-color: #000000;">
            <tr>
                <td align="center" style="padding: 20px 0;">
                    <table class="email-container" role="presentation" cellpadding="0" cellspacing="0" border="0" width="600" style="max-width: 600px; background-color: #000000; border-radius: 20px; border: 1px solid #1a1a1a;">
                        
                        <!-- Header Section -->
                        <tr>
                            <td class="header-bg" style="background: linear-gradient(135deg, #000000 0%, #0a0a0a 100%); background-color: #0a0a0a; padding: 60px 40px; text-align: center; border-bottom: 1px solid #1a1a1a;">
                                <h1 class="hero-title">WELCOME</h1>
                                <p class="subtitle">Your Free Trial is live</p>
                                <span class="trial-badge">1 MONTH FREE</span>
                            </td>
                        </tr>
                        
                        <!-- Content Section -->
                        <tr>
                            <td style="padding: 60px 40px;">
                                <h2 class="greeting">Hey there,</h2>
                                
                                <p class="intro-text">
                                    Welcome to VoiceHype — where your voice becomes clean, structured, code-ready prompts.
                                </p>
                                
                                <p class="intro-text">
                                    You've unlocked your free 1-month trial:
                                </p>
                                
                                <!-- Features Section -->
                                <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%" style="margin: 30px 0;">
                                    <tr>
                                        <td width="33%" style="padding: 0 4px;">
                                            <div class="feature-container">
                                                <div class="feature-icon">🎤</div>
                                                <div class="feature-title">60 min transcription</div>
                                            </div>
                                        </td>
                                        <td width="33%" style="padding: 0 4px;">
                                            <div class="feature-container">
                                                <div class="feature-icon">⚡</div>
                                                <div class="feature-title">10k tokens</div>
                                            </div>
                                        </td>
                                        <td width="33%" style="padding: 0 4px;">
                                            <div class="feature-container">
                                                <div class="feature-icon">🎁</div>
                                                <div class="feature-title">No card required</div>
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                                
                                <!-- Video Section -->
                                <div class="video-section-bg">
                                    <h3 class="video-title">Get Started in 2 Minutes</h3>
                                    <a href="https://www.youtube.com/watch?v=G9Artw_Kgjg" target="_blank" style="text-decoration: none;">
                                        <img class="video-thumbnail" src="https://img.youtube.com/vi/G9Artw_Kgjg/maxresdefault.jpg" alt="VoiceHype Tutorial - Click to watch">
                                    </a>
                                </div>
                                
                                <!-- CTA Section -->
                                <div class="cta-section-bg">
                                    <a href="https://voicehype.ai/dashboard" class="cta-button" target="_blank">
                                        Start Your Free Trial
                                    </a>
                                    <br>
                                    <a href="https://marketplace.visualstudio.com/items?itemName=VoiceHype.voicehype" target="_blank" class="extension-badge vscode">
                                        📥 VS Code Extension
                                    </a>
                                    <a href="https://open-vsx.org/extension/VoiceHype/voicehype" target="_blank" class="extension-badge openvsx">
                                        📥 OpenVSX Extension
                                    </a>
                                </div>
                                
                            </td>
                        </tr>
                        
                        <!-- Logo Section -->
                        <tr>
                            <td class="bottom-logo-bg">
                                <div class="logo-container">
                                    <span class="logo-text">Voice<span class="logo-accent">Hype</span></span>
                                </div>
                            </td>
                        </tr>
                        
                        <!-- Footer -->
                        <tr>
                            <td class="footer-bg">
                                <p class="footer-text">
                                    Questions? Contact <a href="mailto:<EMAIL>" style="color: #14F195;"><EMAIL></a>
                                </p>
                                <div class="footer-links">
                                    <a href="https://youtube.com/@voicehype?si=82fU4P549SbQyEVC" target="_blank">YouTube</a>
                                    <a href="https://x.com/voicehype_ai?s=11&t=EkFMOSOeyt7d4on0tcHQjg" target="_blank">X</a>
                                    <a href="https://www.instagram.com/voicehype.ai?igsh=NW93bjV5b2xocWdl&utm_source=qr" target="_blank">Instagram</a>
                                </div>
                            </td>
                        </tr>
                        
                    </table>
                </td>
            </tr>
        </table>
    </div>
</body>
</html>`;
}

function getFreeTrialExpiredEmailHTML(user: any, emailData: any): string {
  const firstName = user.user_metadata?.full_name?.split(' ')[0] || user.email?.split('@')[0] || 'there';
  const siteUrl = emailData.site_url || SITE_URL;

  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Free Trial Has Expired</title>
    <style>
        /* Reuse existing styles from welcome email */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background-color: #000000;
            color: #ffffff;
            line-height: 1.5;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            margin: 0;
            padding: 0;
        }
        table {
            border-collapse: collapse;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        img {
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
            -ms-interpolation-mode: bicubic;
            max-width: 100%;
        }
        .email-wrapper {
            width: 100%;
            background-color: #000000;
            padding: 20px 0;
        }
        .email-container {
            width: 100%;
            max-width: 600px;
            margin: 0 auto;
            background-color: #000000;
            border-radius: 20px;
            overflow: hidden;
            border: 1px solid #1a1a1a;
        }
        .header-bg {
            background: linear-gradient(135deg, #000000 0%, #0a0a0a 100%);
            background-color: #0a0a0a;
        }
        .hero-title {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            font-size: 54px;
            font-weight: 800;
            letter-spacing: -2px;
            margin: 0;
            padding: 0;
            color: #ffffff;
            text-align: center;
            line-height: 1;
        }
        .subtitle {
            font-size: 26px;
            font-weight: 600;
            color: #ffffff;
            margin: 20px 0 30px 0;
            letter-spacing: -0.5px;
            text-align: center;
        }
        .greeting {
            font-size: 32px;
            font-weight: 600;
            margin: 0 0 25px 0;
            color: #ffffff;
            letter-spacing: -1px;
        }
        .intro-text {
            font-size: 20px;
            color: #ffffff;
            margin: 0 0 30px 0;
            line-height: 1.6;
            font-weight: 400;
        }
        .pricing-container {
            background: linear-gradient(135deg, #1a1a1a 0%, #1f1f1f 100%);
            background-color: #1a1a1a;
            border-radius: 12px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
            border: 2px solid #14F195;
        }
        .price-tag {
            font-size: 48px;
            font-weight: 800;
            color: #14F195;
            margin: 10px 0;
        }
        .price-period {
            font-size: 20px;
            color: #cccccc;
        }
        .feature-list {
            text-align: left;
            margin: 20px 0;
        }
        .feature-item {
            margin: 10px 0;
            font-size: 18px;
            display: flex;
            align-items: center;
        }
        .feature-icon {
            color: #14F195;
            margin-right: 10px;
        }
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #14F195 0%, #00ff88 100%);
            background-color: #14F195;
            color: #000000 !important;
            padding: 22px 55px;
            border-radius: 50px;
            font-size: 20px;
            font-weight: 700;
            text-decoration: none;
            margin: 20px 0;
            border: none;
            letter-spacing: 0.3px;
        }
        .footer-bg {
            background-color: #0a0a0a;
            padding: 40px;
            text-align: center;
            border-top: 1px solid #1a1a1a;
        }
        .footer-text {
            font-size: 16px;
            color: #ffffff;
            margin: 0 0 20px 0;
            font-weight: 400;
        }
        .footer-links a {
            color: #14F195;
            text-decoration: none;
            margin: 0 12px;
            font-weight: 500;
            font-size: 14px;
        }
        @media only screen and (max-width: 600px) {
            .email-container {
                border-radius: 12px;
                margin: 0 10px;
            }
            .hero-title {
                font-size: 36px;
            }
            .subtitle {
                font-size: 18px;
            }
            .greeting {
                font-size: 26px;
            }
            .intro-text {
                font-size: 16px;
            }
            .cta-button {
                padding: 16px 40px;
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <div class="email-wrapper">
        <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%" style="background-color: #000000;">
            <tr>
                <td align="center" style="padding: 20px 0;">
                    <table class="email-container" role="presentation" cellpadding="0" cellspacing="0" border="0" width="600" style="max-width: 600px; background-color: #000000; border-radius: 20px; border: 1px solid #1a1a1a;">
                        
                        <!-- Header Section -->
                        <tr>
                            <td class="header-bg" style="background: linear-gradient(135deg, #000000 0%, #0a0a0a 100%); background-color: #0a0a0a; padding: 60px 40px; text-align: center; border-bottom: 1px solid #1a1a1a;">
                                <h1 class="hero-title">TRIAL EXPIRED</h1>
                                <p class="subtitle">Your free trial has ended</p>
                            </td>
                        </tr>
                        
                        <!-- Content Section -->
                        <tr>
                            <td style="padding: 60px 40px;">
                                <h2 class="greeting">Hey ${firstName},</h2>
                                
                                <p class="intro-text">
                                    Your VoiceHype free trial has expired. But don't worry, you can continue enjoying all our premium features with one of our affordable plans.
                                </p>
                                
                                <!-- Pricing Section -->
                                <div class="pricing-container">
                                    <h3 style="font-size: 24px; margin-bottom: 15px;">Basic Plan</h3>
                                    <div class="price-tag">$4.99<span class="price-period">/month</span></div>
                                </div>
                                
                                <!-- CTA Section -->
                                <div style="text-align: center;">
                                    <a href="${siteUrl}/payments" class="cta-button" target="_blank">
                                        Upgrade Now
                                    </a>
                                </div>
                                
                            </td>
                        </tr>
                        
                        <!-- Footer -->
                        <tr>
                            <td class="footer-bg">
                                <p class="footer-text">
                                    Questions? Contact <a href="mailto:<EMAIL>" style="color: #14F195;"><EMAIL></a>
                                </p>
                                <div class="footer-links">
                                    <a href="https://youtube.com/@voicehype?si=82fU4P549SbQyEVC" target="_blank">YouTube</a>
                                    <a href="https://x.com/voicehype_ai?s=11&t=EkFMOSOeyt7d4on0tcHQjg" target="_blank">X</a>
                                    <a href="https://www.instagram.com/voicehype.ai?igsh=NW93bjV5b2xocWdl&utm_source=qr" target="_blank">Instagram</a>
                                </div>
                            </td>
                        </tr>
                        
                    </table>
                </td>
            </tr>
        </table>
    </div>
</body>
</html>`;
}

function getQuotaUsageAlertEmailHTML(user: any, emailData: any): string {
  const firstName = user.user_metadata?.full_name?.split(' ')[0] || user.email?.split('@')[0] || 'there';
  const {
    service_name,
    threshold,
    used_amount,
    total_amount,
    usage_percentage,
    alertMessage,
    ctaMessage,
    site_url
  } = emailData;

  // Determine alert color based on threshold
  let alertColor = '#14F195'; // Green for 50%
  if (threshold >= 90) alertColor = '#FF9800'; // Orange for 90%
  if (threshold >= 100) alertColor = '#F44336'; // Red for 100%

  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quota Usage Alert</title>
    <style>
        /* Reuse existing styles from welcome email */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background-color: #000000;
            color: #ffffff;
            line-height: 1.5;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            margin: 0;
            padding: 0;
        }
        table {
            border-collapse: collapse;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        img {
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
            -ms-interpolation-mode: bicubic;
            max-width: 100%;
        }
        .email-wrapper {
            width: 100%;
            background-color: #000000;
            padding: 20px 0;
        }
        .email-container {
            width: 100%;
            max-width: 600px;
            margin: 0 auto;
            background-color: #000000;
            border-radius: 20px;
            overflow: hidden;
            border: 1px solid #1a1a1a;
        }
        .header-bg {
            background: linear-gradient(135deg, #000000 0%, #0a0a0a 100%);
            background-color: #0a0a0a;
        }
        .hero-title {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            font-size: 54px;
            font-weight: 800;
            letter-spacing: -2px;
            margin: 0;
            padding: 0;
            color: #ffffff;
            text-align: center;
            line-height: 1;
        }
        .subtitle {
            font-size: 26px;
            font-weight: 600;
            color: #ffffff;
            margin: 20px 0 30px 0;
            letter-spacing: -0.5px;
            text-align: center;
        }
        .greeting {
            font-size: 32px;
            font-weight: 600;
            margin: 0 0 25px 0;
            color: #ffffff;
            letter-spacing: -1px;
        }
        .intro-text {
            font-size: 20px;
            color: #ffffff;
            margin: 0 0 30px 0;
            line-height: 1.6;
            font-weight: 400;
        }
        .alert-container {
            background: linear-gradient(135deg, #1a1a1a 0%, #1f1f1f 100%);
            background-color: #1a1a1a;
            border-radius: 12px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
            border: 2px solid ${alertColor};
        }
        .usage-percentage {
            font-size: 72px;
            font-weight: 800;
            color: ${alertColor};
            margin: 10px 0;
            line-height: 1;
        }
        .usage-label {
            font-size: 20px;
            color: #cccccc;
            margin-bottom: 20px;
        }
        .usage-bar-container {
            width: 100%;
            height: 24px;
            background-color: #2a2a2a;
            border-radius: 12px;
            overflow: hidden;
            margin: 20px 0;
        }
        .usage-bar {
            height: 100%;
            background-color: ${alertColor};
            border-radius: 12px;
            width: ${usage_percentage}%;
        }
        .usage-details {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
            font-size: 16px;
        }
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, ${alertColor} 0%, ${threshold >= 100 ? '#d32f2f' : threshold >= 90 ? '#e65100' : '#00c853'} 100%);
            background-color: ${alertColor};
            color: #000000 !important;
            padding: 22px 55px;
            border-radius: 50px;
            font-size: 20px;
            font-weight: 700;
            text-decoration: none;
            margin: 20px 0;
            border: none;
            letter-spacing: 0.3px;
        }
        .footer-bg {
            background-color: #0a0a0a;
            padding: 40px;
            text-align: center;
            border-top: 1px solid #1a1a1a;
        }
        .footer-text {
            font-size: 16px;
            color: #ffffff;
            margin: 0 0 20px 0;
            font-weight: 400;
        }
        .footer-links a {
            color: #14F195;
            text-decoration: none;
            margin: 0 12px;
            font-weight: 500;
            font-size: 14px;
        }
        @media only screen and (max-width: 600px) {
            .email-container {
                border-radius: 12px;
                margin: 0 10px;
            }
            .hero-title {
                font-size: 36px;
            }
            .subtitle {
                font-size: 18px;
            }
            .greeting {
                font-size: 26px;
            }
            .intro-text {
                font-size: 16px;
            }
            .usage-percentage {
                font-size: 54px;
            }
            .cta-button {
                padding: 16px 40px;
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <div class="email-wrapper">
        <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%" style="background-color: #000000;">
            <tr>
                <td align="center" style="padding: 20px 0;">
                    <table class="email-container" role="presentation" cellpadding="0" cellspacing="0" border="0" width="600" style="max-width: 600px; background-color: #000000; border-radius: 20px; border: 1px solid #1a1a1a;">
                        
                        <!-- Header Section -->
                        <tr>
                            <td class="header-bg" style="background: linear-gradient(135deg, #000000 0%, #0a0a0a 100%); background-color: #0a0a0a; padding: 60px 40px; text-align: center; border-bottom: 1px solid #1a1a1a;">
                                <h1 class="hero-title">QUOTA ALERT</h1>
                                <p class="subtitle">${service_name} Usage</p>
                            </td>
                        </tr>
                        
                        <!-- Content Section -->
                        <tr>
                            <td style="padding: 60px 40px;">
                                <h2 class="greeting">Hey ${firstName},</h2>
                                
                                <p class="intro-text">
                                    ${alertMessage}
                                </p>
                                
                                <!-- Usage Alert Section -->
                                <div class="alert-container">
                                    <div class="usage-percentage">${usage_percentage}%</div>
                                    <div class="usage-label">of your quota used</div>
                                    
                                    <div class="usage-bar-container">
                                        <div class="usage-bar"></div>
                                    </div>
                                    
                                    <div class="usage-details">
                                        <span>Used: ${used_amount}</span>
                                        <span>Total: ${total_amount}</span>
                                    </div>
                                </div>
                                
                                <!-- CTA Section -->
                                <div style="text-align: center;">
                                    <a href="${site_url}/payments" class="cta-button" target="_blank">
                                        ${ctaMessage}
                                    </a>
                                </div>
                                
                            </td>
                        </tr>
                        
                        <!-- Footer -->
                        <tr>
                            <td class="footer-bg">
                                <p class="footer-text">
                                    Questions? Contact <a href="mailto:<EMAIL>" style="color: #14F195;"><EMAIL></a>
                                </p>
                                <div class="footer-links">
                                    <a href="https://youtube.com/@voicehype?si=82fU4P549SbQyEVC" target="_blank">YouTube</a>
                                    <a href="https://x.com/voicehype_ai?s=11&t=EkFMOSOeyt7d4on0tcHQjg" target="_blank">X</a>
                                    <a href="https://www.instagram.com/voicehype.ai?igsh=NW93bjV5b2xocWdl&utm_source=qr" target="_blank">Instagram</a>
                                </div>
                            </td>
                        </tr>
                        
                    </table>
                </td>
            </tr>
        </table>
    </div>
</body>
</html>`;
}
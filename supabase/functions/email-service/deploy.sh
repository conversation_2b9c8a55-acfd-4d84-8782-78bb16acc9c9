#!/bin/bash

# Quick deploy script for email-service Supabase Edge Function
echo "===== Deploying VoiceHype Email Service ====="
echo ""

# Verify prerequisites
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI not found. Please install it with 'npm install -g supabase'"
    exit 1
fi

# Check if logged in
LOGGED_IN=$(supabase projects list 2>/dev/null)
if [ $? -ne 0 ]; then
    echo "⚠️ Not logged in to Supabase CLI. Please login first."
    supabase login
fi

# Ask for project reference if not already linked
PROJECT_REF=$(supabase projects list --json | grep "Reference" | head -1 | awk -F'"' '{print $4}')
if [ -z "$PROJECT_REF" ]; then
    echo "⚠️ No project linked. Please enter your project reference:"
    read -p "Project Reference: " PROJECT_REF
    supabase link --project-ref $PROJECT_REF
fi

# Check if RESEND_API_KEY is set
RESEND_API_KEY=$(supabase secrets list | grep RESEND_API_KEY)
if [ -z "$RESEND_API_KEY" ]; then
    echo "⚠️ RESEND_API_KEY not set. Please enter your Resend API key:"
    read -p "Resend API Key: " API_KEY
    supabase secrets set RESEND_API_KEY=$API_KEY
fi

# Deploy the function
echo "🚀 Deploying email-service function..."
supabase functions deploy email-service --no-verify-jwt

if [ $? -eq 0 ]; then
    echo "✅ Deployment successful!"
    echo ""
    echo "Your email service is available at:"
    echo "https://${PROJECT_REF}.supabase.co/functions/v1/email-service"
    echo ""
    echo "Remember to update your Supabase Auth webhook settings to use this URL."
    echo "See README.md for more details."
else
    echo "❌ Deployment failed. Please check the error message above."
fi

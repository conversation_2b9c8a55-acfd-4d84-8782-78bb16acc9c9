# VoiceHype Email Service for Supabase Edge Functions

This is a Supabase Edge Function implementation of the VoiceHype email service, which processes Supabase Auth webhooks and sends emails using the Resend.com API.

## Overview

The email service provides:
- Email confirmation for new user sign-ups
- Password reset emails
- Invitation emails
- Custom email sending capability

## Deployment Instructions

### Prerequisites
- Supabase CLI installed (`npm install -g supabase`)
- Supabase project created
- Resend.com API key

### Environment Variables

Before deploying, you need to set the following environment variables:

```bash
# In your Supabase project folder
supabase secrets set RESEND_API_KEY=your_resend_api_key
supabase secrets set FROM_EMAIL=<EMAIL>
supabase secrets set FROM_NAME=VoiceHype
supabase secrets set SITE_URL=https://voicehype.ai
```

### Deployment Steps

1. Make sure you're logged in to Supabase CLI:
   ```bash
   supabase login
   ```

2. Navigate to your Supabase project folder and link to your project:
   ```bash
   supabase link --project-ref your-project-ref
   ```

3. Deploy the email-service function:
   ```bash
   supabase functions deploy email-service --no-verify-jwt
   ```

   Note: The `--no-verify-jwt` flag allows the function to be called without a valid JWT token, which is needed for webhook functionality.

### Setting Up the Auth Webhook in Supabase

1. Go to your Supabase dashboard
2. Navigate to Authentication → Email Templates
3. For each template (Sign Up, Invite, and Reset Password), update the action URL to:
   ```
   https://<your-project-ref>.supabase.co/functions/v1/email-service
   ```

4. In the Supabase dashboard, go to Authentication → Settings
5. Scroll down to "Email Settings" and enable "Custom SMTP Server"
6. Scroll further down to "Webhooks"
7. Enable "Send mail with a custom webhooks" and enter the function URL:
   ```
   https://<your-project-ref>.supabase.co/functions/v1/email-service
   ```
8. Save changes

## Testing

You can test the function locally with:

```bash
supabase functions serve email-service
```

Then, use curl to send a test email:

```bash
curl -X POST http://localhost:54321/functions/v1/email-service \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "subject": "Test Email", "content": "This is a test email"}'
```

You can also test the health check endpoint:

```bash
curl http://localhost:54321/functions/v1/email-service
```

## Troubleshooting

1. Check the function logs in the Supabase dashboard
2. Verify your Resend API key is correct
3. Check the environment variables are set correctly
4. Make sure the webhook URL is correctly configured in Supabase

## Security Considerations

- The email service is configured with CORS to allow requests from any origin
- Authentication webhooks do not require JWT verification
- Consider adding a webhook secret for additional security

// @ts-ignore: Deno types
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
// @ts-ignore: Supabase types
import { createClient } from '@supabase/supabase-js';
import {
  createErrorResponse,
  createSuccessResponse,
  ErrorCode
} from '../_shared/utils.ts';

// Declare Deno types
declare const Deno: {
  env: {
    get(key: string): string | undefined;
  };
};

// Create a Supabase client for database operations
const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
const supabase = createClient(supabaseUrl, supabaseServiceKey);

function getHeadersAsObject(headers: Headers): Record<string, string> {
  const obj: Record<string, string> = {};
  headers.forEach((value, key) => {
    obj[key] = value;
  });
  return obj;
}

// Main handler function
serve(async (req: Request) => {
  try {
    // Get VoiceHype API key from header
    let voiceHypeApiKey = req.headers.get('apikey');
    if (!voiceHypeApiKey) {
      const authHeader = req.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer')) {
        voiceHypeApiKey = authHeader.substring(7); // Remove 'Bearer ' prefix
      }
    }
    
    if (!voiceHypeApiKey) {
      const headers = getHeadersAsObject(req.headers);
      console.error('Missing API key:', { headers });
      return createErrorResponse(401, 'Missing API key', ErrorCode.INVALID_API_KEY);
    }

    // Validate VoiceHype API key and get user info
    const { data: validationData, error: validationError } = await supabase
      .rpc('validate_api_key', { p_key: voiceHypeApiKey });

    if (validationError || !validationData || validationData.length === 0) {
      return createErrorResponse(401, 'Invalid API key', ErrorCode.INVALID_API_KEY);
    }

    const userId = validationData[0].user_id;

    // Get unpaid balances for the user
    const { data: unpaidBalances, error: balancesError } = await supabase
      .rpc('get_unpaid_payg_balances', { p_user_id: userId });

    if (balancesError) {
      console.error('Error fetching unpaid balances:', balancesError);
      return createErrorResponse(500, 'Failed to fetch unpaid balances', ErrorCode.SERVICE_ERROR);
    }

    // Format the response with new fields included
    const formattedBalances = (unpaidBalances || []).map(balance => {
      const date = new Date(balance.month);
      return {
        id: balance.id,
        month: date.toLocaleString('en-US', { month: 'long', year: 'numeric' }),
        rawMonth: balance.month,
        amount: Number(balance.total_amount).toFixed(2),
        status: balance.payment_status,
        metadata: balance.payment_metadata,
        created_at: balance.created_at
      };
    });

    return createSuccessResponse(formattedBalances);

  } catch (error: any) {
    console.error('Unexpected error:', {
      message: error.message,
      stack: error.stack
    });
    
    return createErrorResponse(
      500, 
      `Server error: ${error.message}`, 
      ErrorCode.SERVICE_ERROR
    );
  }
}); 
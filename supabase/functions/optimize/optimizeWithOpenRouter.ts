// OpenRouter optimization function
import { TokenUsage, createTokenUsage } from '../_shared/utils.ts';

// Declare <PERSON>o types
declare const Deno: {
  env: {
    get(key: string): string | undefined;
  };
};

// OpenRouter model mapping
export const OPENROUTER_MODELS = {
  // Llama Models
  'llama-4-scout': 'meta-llama/llama-4-scout',
  'llama-4-maverick': 'meta-llama/llama-4-maverick',
  'llama-3-70b': 'meta-llama/llama-3-70b-instruct',
  'llama-3-8b': 'meta-llama/llama-3-8b-instruct',

  // Claude Models
  'claude-3.5-sonnet': 'anthropic/claude-3.5-sonnet',
  'claude-3.7-sonnet': 'anthropic/claude-3.7-sonnet',
  'claude-3.5-haiku': 'anthropic/claude-3.5-haiku',

  // DeepSeek Models
  'deepseek-r1': 'deepseek/deepseek-r1',
  'deepseek-v3': 'deepseek/deepseek-chat-v3-0324',
};

// Type for OpenRouter models
export type OpenRouterModel = keyof typeof OPENROUTER_MODELS;

// Function to get headers as an object
function getHeadersAsObject(headers: Headers): Record<string, string> {
  const obj: Record<string, string> = {};
  headers.forEach((value, key) => {
    obj[key] = value;
  });
  return obj;
}

// Function for OpenRouter optimization
export async function optimizeWithOpenRouter(
  text: string,
  model: OpenRouterModel,
  customPrompt: string,
  maxOutputTokens?: number,
  apiKey?: string,
  clientMessages?: Array<{role: string, content: string}>
): Promise<{ optimizedText: string, tokenUsage: TokenUsage, modelUsed?: string }> {
  // Get API key from environment if not provided
  const OPENROUTER_API_KEY = apiKey || Deno.env.get('OPENROUTER_API_KEY') || '';

  if (!OPENROUTER_API_KEY) {
    throw new Error('Missing OpenRouter API key');
  }

  console.log('Optimizing with OpenRouter:', {
    model,
    openRouterModel: OPENROUTER_MODELS[model],
    textLength: text.length,
    hasCustomPrompt: !!customPrompt,
    customPromptLength: customPrompt?.length || 0,
    hasClientMessages: Array.isArray(clientMessages),
    clientMessagesCount: clientMessages?.length || 0,
    maxOutputTokens
  });

  // Ensure maxOutputTokens is a valid integer
  const validatedMaxTokens = maxOutputTokens && !isNaN(maxOutputTokens) ?
    Math.floor(maxOutputTokens) : 4096;

  // Use client-provided messages if available, otherwise create a basic structure
  let requestBody: any;

  if (Array.isArray(clientMessages) && clientMessages.length > 0) {
    // Use the client's message structure directly
    requestBody = {
      model: OPENROUTER_MODELS[model],
      messages: clientMessages,
      max_tokens: validatedMaxTokens,
      temperature: 1.0
    };
    console.log('Using client-provided messages array');
  } else {
    // Create simple message structure with customPrompt as system and text as user message
    requestBody = {
      model: OPENROUTER_MODELS[model],
      messages: [
        {
          role: 'system',
          content: customPrompt
        },
        {
          role: 'user',
          content: text
        }
      ],
      max_tokens: validatedMaxTokens,
      temperature: 1.0
    };
    console.log('Using basic message structure');
  }

  console.log('OpenRouter request structure:', {
    endpoint: 'https://openrouter.ai/api/v1/chat/completions',
    modelUsed: OPENROUTER_MODELS[model],
    messageCount: requestBody.messages.length,
    maxTokens: validatedMaxTokens
  });

  // Prepare the API request
  const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
      'HTTP-Referer': 'https://voicehype.ai',
      'X-Title': 'VoiceHype'
    },
    body: JSON.stringify(requestBody)
  });

  const responseHeaders = getHeadersAsObject(response.headers);
  console.log('OpenRouter response headers:', responseHeaders);

  if (!response.ok) {
    const errorText = await response.text();
    console.error('OpenRouter API error:', {
      status: response.status,
      statusText: response.statusText,
      body: errorText
    });
    throw new Error(`OpenRouter API error: ${response.status} - ${response.statusText}. Response: ${errorText}`);
  }

  const data = await response.json();
  console.log('OpenRouter response:', {
    usage: data.usage,
    modelUsed: data.model,
    finishReason: data.choices[0].finish_reason,
    contentLength: data.choices[0].message.content.length
  });

  // Create standardized token usage
  const tokenUsage = createTokenUsage(
    data.usage.prompt_tokens,
    data.usage.completion_tokens,
    {
      input_details: { prompt_tokens: data.usage.prompt_tokens },
      output_details: { completion_tokens: data.usage.completion_tokens }
    }
  );

  return {
    optimizedText: data.choices[0].message.content.trim(),
    tokenUsage,
    modelUsed: data.model // Return the actual model used
  };
}

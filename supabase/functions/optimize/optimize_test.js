const assert = require('assert');

// Recursive extraction function with depth limit
const extractOptimizedText = (text, depth = 0) => {
  if (depth > 1) return text; // Max recursion depth reached
  
  // First try direct extraction
  const directRegex = /"(?:optimizedText|"commandResult)"\s*:\s*"((?:[^"\\]|\\.)*)"/;
  const directMatch = text.match(directRegex);
  if (directMatch && directMatch[1]) {
    return JSON.parse(`"${directMatch[1]}"`);
  }

  // Then try nested extraction
  const nestedRegex = /"(?:optimizedText|commandResult)"\s*:\s*(?:(?:"((?:[^"\\]|\\.)*)")|(\{(?:[^{}"]|"(?:[^"\\]|\\.)*")*\})|(\[(?:[^\[\]"]|"(?:[^"\\]|\\.)*")*\]))/;
  const nestedMatch = text.match(nestedRegex);
  
  if (nestedMatch) {
    // If we found a nested object or array, recurse
    const nestedText = nestedMatch[2] || nestedMatch[3] || '';
    if (nestedText) {
      return extractOptimizedText(nestedText, depth + 1);
    }
  }

  return text;
};
  

// Test cases
const testCases = [
  {
    name: 'Direct optimizedText',
    input: '{"optimizedText": "This is a direct response"}',
    expected: 'This is a direct response'
  },
  {
    name: 'Nested optimizedText',
    input: '{"data": {"optimizedText": "This is nested text"}}',
    expected: 'This is nested text'
  },
  {
    name: 'Double nested optimizedText',
    input: '{"optimizedText": {"optimizedText": "Double nested text"}}',
    expected: 'Double nested text'
  },
  {
    name: 'CommandResult field',
    input: '{"commandResult": "Using commandResult field"}',
    expected: 'Using commandResult field'
  },
  {
    name: 'Array of results',
    input: '{"results": [{"optimizedText": "First item"}, {"optimizedText": "Second item"}]}',
    expected: 'First item'
  },
  {
    name: 'Escaped characters',
    input: '{"optimizedText": "This has \\"escaped quotes\\" and \\n newlines"}',
    expected: 'This has "escaped quotes" and \n newlines'
  },
  {
    name: 'Malformed JSON',
    input: '{"optimizedText": "Missing closing brace',
    expected: null
  },
  {
    name: 'Deep nested (should stop at 2 levels)',
    input: JSON.stringify({
      level1: {
        level2: {
          level3: {
            optimizedText: "Should not reach this level"
          }
        }
      }
    }),
    expected: null
  }
];

// Run tests
function runTests() {
  let passed = 0;
  let failed = 0;

  testCases.forEach(({ name, input, expected }) => {
    try {
      const result = extractOptimizedText(input);
      if (expected === null) {
        assert.strictEqual(result, null);
      } else {
        assert.strictEqual(result, expected);
      }
      console.log(`✅ ${name}`);
      passed++;
    } catch (error) {
      console.log(`❌ ${name}`);
      const result = extractOptimizedText(input);
      console.log(`   Expected: ${expected}`);
      console.log(`   Received: ${result}`);
      failed++;
    }
  });

  console.log(`\nTest Results: ${passed} passed, ${failed} failed`);
  if (failed > 0) {
    process.exit(1);
  }
}

runTests();
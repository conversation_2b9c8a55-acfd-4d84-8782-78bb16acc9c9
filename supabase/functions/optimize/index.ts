// Supabase Edge Function for text optimization
// @ts-ignore: Deno types
import { serve } from "https://deno.land/std@0.224.0/http/server.ts";
// @ts-ignore: Supabase types
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.0';
import {
  createErrorResponse,
  createSuccessResponse,
  parseRequestBody,
  ErrorCode,
  TokenUsage,
  createTokenUsage
} from '../_shared/utils.ts';

// Declare Deno types
declare const Deno: {
  env: {
    get(key: string): string | undefined;
  };
};

import { optimizeWithRequesty, REQUESTY_MODELS, RequestyModel } from './optimizeWithRequesty.ts';

// Service and model configurations - now using database-driven approach
const SUPPORTED_MODELS = {
  requesty: [
    'claude-4-sonnet',
    'claude-3.7-sonnet',
    'claude-3.5-sonnet',
    'claude-haiku',
    'llama-3.1-70b',
    'deepseek-v3'
  ] as const,
  deepinfra: [
    // Keeping this for potential future direct DeepInfra usage
  ] as const
} as const;

type RequestyModelType = typeof SUPPORTED_MODELS.requesty[number];

// Cache for model mappings
let modelMappingCache: {
  friendlyToUrl: Record<string, string>;
  urlToFriendly: Record<string, string>;
  lastUpdated: number;
} | null = null;

// Function to fetch model mappings from Supabase
async function fetchModelMappings() {
  const now = Date.now();
  if (modelMappingCache && (now - modelMappingCache.lastUpdated) < 300000) { // 5 minute cache
    return modelMappingCache;
  }

  try {
    const { data: models, error } = await supabase
      .from('models')
      .select('friendly_name, actual_url, model_type, is_active')
      .eq('model_type', 'optimization')
      .eq('is_active', true);

    if (error) {
      console.error('Error fetching model mappings:', error);
      return modelMappingCache; // Return cached if available
    }

    const friendlyToUrl: Record<string, string> = {};
    const urlToFriendly: Record<string, string> = {};

    models?.forEach(model => {
      friendlyToUrl[model.friendly_name.toLowerCase()] = model.actual_url;
      urlToFriendly[model.actual_url.toLowerCase()] = model.friendly_name;
    });

    modelMappingCache = {
      friendlyToUrl,
      urlToFriendly,
      lastUpdated: now
    };

    return modelMappingCache;
  } catch (error) {
    console.error('Error in fetchModelMappings:', error);
    return modelMappingCache;
  }
}

// Function to resolve model name (friendly name or URL to actual URL)
async function resolveModelName(modelInput: string): Promise<string> {
  const mappings = await fetchModelMappings();
  
  if (!mappings) {
    // Fallback to direct mapping if cache is not available
    return modelInput;
  }

  const lowerModel = modelInput.toLowerCase();
  
  // Check if it's a friendly name
  if (mappings.friendlyToUrl[lowerModel]) {
    return mappings.friendlyToUrl[lowerModel];
  }
  
  // Check if it's already a URL
  if (mappings.urlToFriendly[lowerModel]) {
    return modelInput; // Already a URL
  }
  
  // Try to match hyphenated format with space-separated format in database
  // For example: "claude-4-sonnet" -> "claude 4 sonnet"
  const hyphenToSpace = lowerModel.replace(/-/g, ' ');
  if (mappings.friendlyToUrl[hyphenToSpace]) {
    return mappings.friendlyToUrl[hyphenToSpace];
  }
  
  // Try to match space-separated format with hyphenated format in database
  // For example: "claude 4 sonnet" -> "claude-4-sonnet"
  const spaceToHyphen = lowerModel.replace(/\s+/g, '-');
  if (mappings.friendlyToUrl[spaceToHyphen]) {
    return mappings.friendlyToUrl[spaceToHyphen];
  }
  
  // Check if it's a supported model name (for backward compatibility)
  const supportedModel = SUPPORTED_MODELS.requesty.find(m => m.toLowerCase() === lowerModel);
  if (supportedModel) {
    return supportedModel;
  }
  
  return modelInput;
}

// Create a Supabase client for database operations
const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Get service API keys from environment
const REQUESTY_API_KEY = Deno.env.get('REQUESTY_API_KEY') || '';

if (!REQUESTY_API_KEY) {
  throw new Error('Missing required REQUESTY_API_KEY');
}

// Add new type for pricing response
type PricingCheckResult = {
  can_use: boolean;
  pricing_model: 'credits' | 'payg' | null;
  cost: number;
  max_output_tokens?: string;
  error_code?: string;
};

// Helper function to estimate token count
function estimateTokenCount(text: string): number {
  // This is a simple estimation based on word count
  // Actual token count depends on the tokenizer used by the models
  return Math.ceil(text.split(/\s+/).length * 1.3);
}

// Helper function to get headers as an object
function getHeadersAsObject(headers: Headers): Record<string, string> {
  const obj: Record<string, string> = {};
  headers.forEach((value, key) => {
    obj[key] = value;
  });
  return obj;
}

serve(async (req: Request) => {
  try {
    // Parse the request body
    const {
      text,
      model,
      customPrompt,
      clientMessages,
      maxOutputTokens,
      debug = false,
      requestId
    } = await parseRequestBody(req);

    // Validate API key, extract user ID
    const apiKey = req.headers.get('Authorization')?.replace('Bearer ', '') || '';
    if (!apiKey) {
      return createErrorResponse(401, 'Missing API key', ErrorCode.UNAUTHORIZED);
    }

    // Validate VoiceHype API key and get user info
    const { data: validationData, error: validationError } = await supabase
      .rpc('validate_api_key', { p_key: apiKey });

    if (validationError || !validationData || validationData.length === 0) {
      console.error('API key validation failed:', {
        error: validationError,
        keyExists: !!validationData,
        keyHash: apiKey ? `${apiKey.substring(0, 3)}...${apiKey.substring(apiKey.length - 3)}` : 'none'
      });
      return createErrorResponse(401, 'Invalid API key', ErrorCode.INVALID_API_KEY);
    }

    const userId = validationData[0].user_id;
    const apiKeyId = validationData[0].api_key_id;

    // Validate required parameters
    if (!text) {
      return createErrorResponse(400, 'Missing required parameter: text', ErrorCode.INVALID_REQUEST);
    }

    if (!model) {
      return createErrorResponse(400, 'Missing required parameter: model', ErrorCode.INVALID_REQUEST);
    }

    // Resolve model name (handle UUIDs, friendly names, and URLs)
    const resolvedModel = await resolveModelName(model);
    
    // Validate resolved model - now accepts any valid model from database
    let modelProvider: 'requesty' = 'requesty';
    let finalModel: string = resolvedModel;
    
    // All models from the database are considered valid
    const mappings = await fetchModelMappings();
    const isValidModel = mappings?.urlToFriendly[resolvedModel.toLowerCase()] ||
                        mappings?.friendlyToUrl[resolvedModel.toLowerCase()] ||
                        SUPPORTED_MODELS.requesty.includes(resolvedModel as any);
    
    if (!isValidModel) {
      console.error('Unsupported model requested:', {
        originalModel: model,
        resolvedModel,
        userId
      });
      return createErrorResponse(
        400,
        `Model '${model}' is not supported. Please use a valid model ID, friendly name, or model URL.`,
        ErrorCode.UNSUPPORTED_MODEL
      );
    }

    // Estimate input token count for pricing
    const estimatedInputTokens = estimateTokenCount(text);
    console.log('Token estimation:', {
      textLength: text.length,
      estimatedInputTokens
    });

    console.log('Optimization request:', {
      originalModel: model,
      resolvedModel: finalModel,
      modelProvider,
      textLength: text.length,
      customPromptProvided: !!customPrompt,
      customPromptLength: customPrompt?.length || 0,
      clientMessagesProvided: Array.isArray(clientMessages),
      maxOutputTokens,
      requestId: requestId || req.headers.get('X-Request-ID') || 'unknown',
      estimatedInputTokens
    });

 
    // Get user's credits and PAYG status
    const { data: userSettings, error: settingsError } = await supabase
      .from('user_settings')
      .select('payg_enabled')
      .eq('user_id', userId)
      .single();

    const { data: credits, error: creditsError } = await supabase
      .from('credits')
      .select('balance')
      .eq('user_id', userId)
      .single();

    console.log('User payment status:', {
      paygEnabled: userSettings?.payg_enabled,
      settingsError,
      credits: credits?.balance,
      creditsError,
      userId
    });

    // Check pricing allowance
    const { data: pricingCheck, error: pricingError } = await supabase
      .rpc('check_usage_allowance', {
        p_user_id: userId,
        p_service: 'optimization',
        p_model: finalModel,
        p_amount: estimatedInputTokens,
        p_api_key_id: apiKeyId,
        p_is_input_only: true // Specify that we're only checking for input tokens initially
      }) as { data: PricingCheckResult[] | null, error: any };


    if (pricingError || !pricingCheck || !pricingCheck[0] || !pricingCheck[0].can_use) {
      const errorDetails = {
        pricingError,
        check: pricingCheck,
        availableCredits: credits?.balance,
        paygEnabled: userSettings?.payg_enabled,
        model,
        estimatedInputTokens,
        errorCode: pricingCheck?.[0]?.error_code
      };

      console.error('Pricing check failed:', errorDetails);

      // Use the detailed error message from check_usage_allowance
      const errorMessage = pricingCheck?.[0]?.error_code || 'Unable to process request due to pricing constraints.';
      
      // Check for specific error code patterns
      if (errorMessage.includes('unpaid_balance')) {
        // Get detailed unpaid balance information
        const { data: unpaidBalances, error: unpaidError } = await supabase
          .rpc('get_unpaid_payg_balances', { p_user_id: userId });

        console.log('Unpaid balances:', {
          balances: unpaidBalances,
          error: unpaidError
        });

        return createErrorResponse(
          403,
          'You have an unpaid balance. Please settle your outstanding balance before using this service.',
          ErrorCode.UNPAID_BALANCE,
          { unpaid_balance: unpaidBalances }
        );
      }

      if (errorMessage.includes('optimization_')) {
        // Extract the detailed error message from the database function
        const detailedMessage = errorMessage.replace(/^optimization_/, '').replace(/_/g, ' ');
        return createErrorResponse(
          403,
          detailedMessage,
          ErrorCode.INSUFFICIENT_CREDITS,
          {
            available_credits: credits?.balance,
            original_error: errorMessage
          }
        );
      }

      if (errorMessage.includes('insufficient_credits') || errorMessage.includes('insufficient')) {
        return createErrorResponse(
          403,
          errorMessage,
          ErrorCode.INSUFFICIENT_CREDITS,
          { available_credits: credits?.balance }
        );
      }

      // Return the detailed error message from the database function
      return createErrorResponse(
        403,
        errorMessage,
        ErrorCode.SERVICE_ERROR
      );
    }

    // Record pending usage
    const { data: pendingUsageRow, error: pendingInsertError } = await supabase
      .from('usage_history')
      .insert({
        user_id: userId,
        api_key_id: apiKeyId,
        service: 'optimization',
        model: finalModel,
        amount: estimatedInputTokens,
        cost: 0,
        pricing_model: pricingCheck[0].pricing_model,
        status: 'pending',
        metadata: {
          inputLength: text.length,
          inputTokens: estimatedInputTokens,
          outputLength: 0,
          outputTokens: 0,
          originalModel: model,
          resolvedModel: finalModel
        }
      })
      .select('id')
      .single();

    if (pendingInsertError || !pendingUsageRow) {
      console.error('Failed to insert pending usage row:', pendingInsertError);
    }


    // Default max tokens from pricing check if not provided
    const resolvedMaxOutputTokens = maxOutputTokens || parseInt(pricingCheck?.[0]?.max_output_tokens || '4096');

    try {
      // Perform optimization based on the model provider
      let optimizedText: string;
      let tokenUsage: TokenUsage;
      let modelUsed: string = model; // Keep track of the actual model used

      if (modelProvider === 'requesty') {
        // finalModel is already the resolved URL from resolveModelName
        // optimizeWithRequesty now handles both URLs and friendly names directly
        const result = await optimizeWithRequesty(text, finalModel, customPrompt, resolvedMaxOutputTokens, REQUESTY_API_KEY, clientMessages);
        if ('modelUsed' in result && result.modelUsed) {
          modelUsed = result.modelUsed;
        }
        optimizedText = result.optimizedText;
        tokenUsage = result.tokenUsage;
      } else {
        throw new Error(`Unsupported model provider: ${modelProvider}`);
      }

      console.log('Optimization completed with actual token counts:', {
        ...tokenUsage,
        inputLength: text.length,
        outputLength: optimizedText.length,
        model
      });

      // Get pricing for input and output tokens
      let inputModel: string;
      let outputModel: string;

      if (modelProvider === 'requesty') {
        // Use the resolved model directly for pricing lookups
        inputModel = `${finalModel}/input`;
        outputModel = `${finalModel}/output`;
      } else {
        // For other providers, use the resolved model
        inputModel = `${finalModel}/input`;
        outputModel = `${finalModel}/output`;
      }

      console.log('Looking up pricing with models:', {
        inputModel,
        outputModel,
        originalModel: model,
        resolvedModel: finalModel,
        provider: modelProvider
      });

      const { data: inputPricing, error: inputPricingError } = await supabase
        .from('service_pricing')
        .select('cost_per_unit')
        .eq('service', 'optimization')
        .eq('model', inputModel)
        .eq('is_active', true)
        .single();

      const { data: outputPricing, error: outputPricingError } = await supabase
        .from('service_pricing')
        .select('cost_per_unit')
        .eq('service', 'optimization')
        .eq('model', outputModel)
        .eq('is_active', true)
        .single();

      // Record successful usage and update balances with actual token count (async, non-blocking)
      supabase
        .rpc('finalize_usage', {
          p_user_id: userId,
          p_service: 'optimization',
          p_model: finalModel,
          p_amount: tokenUsage.total_tokens,
          p_cost: 0, // Deprecated, will be removed, Inshaa Allah.
          p_pricing_model: pricingCheck[0].pricing_model,
          p_metadata: {
            inputLength: text.length,
            inputTokens: tokenUsage.input_tokens,
            outputLength: optimizedText.length,
            outputTokens: tokenUsage.output_tokens,
            originalModel: model,
            resolvedModel: finalModel
          },
          p_pending_usage_id: pendingUsageRow?.id ?? null
        })
        .then(({ error }) => {
          if (error) {
            console.error('Failed to finalize usage:', error);
          }
        })
        .catch((error) => {
          console.error('Error in finalize_usage:', error);
        });

      // Process the optimized text - try new tag format first, then fallback to JSON
      let processedText = optimizedText;

      try {
        // First try regex extraction for new <optimizedText> tag format
        const optimizedTextMatch = optimizedText.match(/<optimizedText>(.*?)<\/optimizedText>/s);
        if (optimizedTextMatch?.[1]?.trim()) {
          processedText = optimizedTextMatch[1].trim();
          console.log('Optimized text extracted using regex tag method');
        } else {
          // If regex extraction fails, try JSON parsing as fallback for existing users
          try {
            const parsedJson = JSON.parse(optimizedText);

            // Handle different JSON response formats
            if (parsedJson.optimizedText) {
              processedText = typeof parsedJson.optimizedText === 'string'
                ? parsedJson.optimizedText
                : JSON.stringify(parsedJson.optimizedText);
              console.log('Optimized text extracted using JSON method');
            } else if (parsedJson.data?.optimizedText) {
              processedText = typeof parsedJson.data.optimizedText === 'string'
                ? parsedJson.data.optimizedText
                : JSON.stringify(parsedJson.data.optimizedText);
              console.log('Optimized text extracted using nested JSON method');
            } else {
              console.log('No recognizable JSON format found, using raw response');
            }
          } catch (jsonError) {
            console.log('JSON parsing failed, using raw response:', jsonError);
            // Keep the original optimized text if JSON parsing fails
          }
        }
      } catch (parseError) {
        console.log('Response format parsing error:', parseError);
        // Keep the original optimized text if all parsing fails
      }

      console.log('Text processing completed:', {
        originalLength: optimizedText.length,
        processedLength: processedText.length,
        extractionMethod: optimizedText.includes('<optimizedText>') ? 'tag' : 'json_or_raw'
      });

      // Create the response data
      const responseData = {
        optimizedText: processedText,
        metadata: {
          model: finalModel,
          originalModel: model,
          inputLength: text.length,
          outputLength: processedText.length
        }
      };

      // Log the response for debugging (privacy-safe)
      console.log('Sending successful response:', {
        originalModel: model,
        resolvedModel: finalModel,
        modelUsed,
        modelProvider,
        customPromptUsed: !!customPrompt,
        inputLength: text.length,
        outputLength: processedText.length,
        tokenUsage: {
          input: tokenUsage.input_tokens,
          output: tokenUsage.output_tokens,
          total: tokenUsage.total_tokens
        }
        // Note: optimizedPreview removed for user privacy
      });

      return createSuccessResponse(responseData);

    } catch (error: any) {
      // Record failed usage
        await supabase
        .from('usage_history')
        .insert({
          user_id: userId,
          api_key_id: apiKeyId,
          service: 'optimization',
          model: finalModel,
          amount: estimatedInputTokens,
          cost: 0,
          pricing_model: pricingCheck[0].pricing_model,
          status: 'failed',
          metadata: {
            inputLength: text.length,
            inputTokens: estimatedInputTokens,
            outputLength: 0,
            outputTokens: 0,
            originalModel: model,
            resolvedModel: finalModel
          }
        });

      console.error('Optimization error:', {
        message: error.message,
        stack: error.stack,
        model,
        textLength: text.length
      });

      return createErrorResponse(
        500,
        `Optimization failed: ${error.message}`,
        ErrorCode.SERVICE_ERROR
      );
    }

  } catch (error: any) {
    console.error('Unexpected error:', {
      message: error.message,
      stack: error.stack
    });

    return createErrorResponse(
      500,
      `Server error: ${error.message}`,
      ErrorCode.SERVICE_ERROR
    );
  }
});
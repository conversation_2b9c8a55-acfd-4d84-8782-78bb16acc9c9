// Requesty optimization function
import { TokenUsage, createTokenUsage } from '../_shared/utils.ts';

// Declare Deno types
declare const Deno: {
  env: {
    get(key: string): string | undefined;
  };
};

// Requesty model mapping
export const REQUESTY_MODELS = {
  // Claude Models (Premium - Listed First)
  'claude-4-sonnet': 'anthropic/claude-sonnet-4-20250514',
  'claude-3.7-sonnet': 'anthropic/claude-3-7-sonnet-latest',
  'claude-3.5-sonnet': 'anthropic/claude-3-5-sonnet-latest',
  'claude-haiku': 'anthropic/claude-3-haiku-20240307',

  // Llama Models via DeepInfra
  'llama-3.1-70b': 'deepinfra/meta-llama/Meta-Llama-3.1-70B-Instruct',
  'llama-3.1-8b-instruct-turbo': 'deepinfra/meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo',

  // DeepSeek Models
  'deepseek-v3': 'deepinfra/deepseek-ai/DeepSeek-V3',
};

// Type for Requesty models
export type RequestyModel = keyof typeof REQUESTY_MODELS;

// Function for Requesty optimization
export async function optimizeWithRequesty(
  text: string,
  model: string, // Changed from RequestyModel to string to accept URLs directly
  customPrompt: string,
  maxOutputTokens?: number,
  apiKey?: string,
  clientMessages?: Array<{role: string, content: string}>
): Promise<{ optimizedText: string, tokenUsage: TokenUsage, modelUsed?: string }> {
  // Get API key from environment if not provided
  const REQUESTY_API_KEY = apiKey || Deno.env.get('REQUESTY_API_KEY') || '';

  if (!REQUESTY_API_KEY) {
    throw new Error('Missing Requesty API key');
  }

  // Determine the actual model identifier to use
  let actualModel: string;
  
  // Check if the provided model is a URL (contains slashes) or a friendly name
  if (model.includes('/') || !REQUESTY_MODELS[model as RequestyModel]) {
    // It's a URL, use it directly
    actualModel = model;
  } else {
    // It's a friendly name, map it to the actual URL
    actualModel = REQUESTY_MODELS[model as RequestyModel];
  }

  console.log('Optimizing with Requesty:', {
    providedModel: model,
    actualModel,
    textLength: text.length,
    hasCustomPrompt: !!customPrompt,
    customPromptLength: customPrompt?.length || 0,
    hasClientMessages: Array.isArray(clientMessages),
    clientMessagesCount: clientMessages?.length || 0,
    maxOutputTokens
  });

  // Ensure maxOutputTokens is a valid integer
  const validatedMaxTokens = maxOutputTokens && !isNaN(maxOutputTokens) ?
    Math.floor(maxOutputTokens) : 4096;

  // Use client-provided messages if available, otherwise create a basic structure
  let requestBody: any;

  // Check if the model is a DeepInfra model (they don't handle response_format well via Requesty)
  const isDeepInfraModel = actualModel.includes('deepinfra/');

  if (Array.isArray(clientMessages) && clientMessages.length > 0) {
    // Use the client's message structure directly
    requestBody = {
      model: actualModel,
      messages: clientMessages,
      max_tokens: validatedMaxTokens,
      temperature: 1.0
    };
    
    // Only add response_format for non-DeepInfra models
    if (!isDeepInfraModel) {
      requestBody.response_format = { "type": "json_object" };
    }
    
    console.log('Using client-provided messages array', { isDeepInfraModel, hasResponseFormat: !isDeepInfraModel });
  } else {
    // Create simple message structure with customPrompt as system and text as user message
    requestBody = {
      model: actualModel,
      messages: [
        {
          role: 'system',
          content: customPrompt
        },
        {
          role: 'user',
          content: text
        }
      ],
      max_tokens: validatedMaxTokens,
      temperature: 1.0
    };
    
    // Only add response_format for non-DeepInfra models
    if (!isDeepInfraModel) {
      requestBody.response_format = { "type": "json_object" };
    }
    
    console.log('Using basic message structure', { isDeepInfraModel, hasResponseFormat: !isDeepInfraModel });
  }

  console.log('Requesty request structure:', {
    endpoint: 'https://router.requesty.ai/v1/chat/completions',
    modelUsed: actualModel,
    messageCount: requestBody.messages.length,
    maxTokens: validatedMaxTokens
  });  // Prepare the API request - simplified approach
  const fetchOptions: RequestInit = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${REQUESTY_API_KEY}`,
      'X-Source': 'voicehype-edge',
      'User-Agent': 'VoiceHype/1.0'
    },
    body: JSON.stringify(requestBody)
  };

  let response: Response | undefined;
  let attempts = 0;
  const maxAttempts = isDeepInfraModel ? 3 : 1; // Retry for DeepInfra models

  while (attempts < maxAttempts) {
    attempts++;
    try {
      response = await fetch('https://router.requesty.ai/v1/chat/completions', fetchOptions);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Requesty API error:', {
          status: response.status,
          statusText: response.statusText,
          body: errorText
        });
        throw new Error(`Requesty API error: ${response.status} - ${response.statusText}. Response: ${errorText}`);
      }

      // Try to read the response - this is where DeepInfra models might fail
      const rawBuffer = await response.arrayBuffer();
      const responseText = new TextDecoder().decode(rawBuffer);
      
      // Parse JSON
      const data = JSON.parse(responseText);
      
      // Extract optimized text
      let optimizedText = data.choices[0].message.content.trim();
      
      // Check if the content is a JSON string (common with DeepInfra models)
      if (optimizedText.startsWith('{') && optimizedText.endsWith('}')) {
        try {
          const parsedContent = JSON.parse(optimizedText);
          if (typeof parsedContent === 'object' && parsedContent.optimized_text) {
            optimizedText = parsedContent.optimized_text;
          } else if (typeof parsedContent === 'object' && parsedContent.text) {
            optimizedText = parsedContent.text;
          }
        } catch (err) {
          console.log('Content looks like JSON but failed to parse, using as-is:', err.message);
        }
      }

      // Create standardized token usage
      const tokenUsage = createTokenUsage(
        data.usage.prompt_tokens,
        data.usage.completion_tokens,
        {
          input_details: { prompt_tokens: data.usage.prompt_tokens },
          output_details: { completion_tokens: data.usage.completion_tokens }
        }
      );

      return {
        optimizedText,
        tokenUsage,
        modelUsed: data.model
      };

    } catch (fetchErr) {
      console.error(`Fetch attempt ${attempts} failed:`, {
        error: fetchErr.message,
        type: fetchErr.constructor.name,
        model: isDeepInfraModel ? 'DeepInfra' : 'Claude'
      });
      
      if (attempts >= maxAttempts) {
        throw new Error(`Failed to fetch after ${maxAttempts} attempts: ${fetchErr.message}`);
      }
      
      // Wait before retrying (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempts) * 1000));
    }
  }

  throw new Error('Failed to get response after all attempts');
}

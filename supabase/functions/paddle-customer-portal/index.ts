import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// Declare Deno for TypeScript
declare const Deno: any

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface PortalSession {
  id: string
  customer_id: string
  urls: {
    general: {
      overview: string
    }
    subscriptions: any[]
  }
  created_at: string
}

serve(async (req) => {
  console.log('🚀 Customer portal function started')
  
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    console.log('✅ CORS preflight handled')
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('🔧 Setting up environment configuration...')
    
    // Get environment-specific Paddle API key
    const paddleEnvironment = Deno.env.get('PADDLE_ENVIRONMENT') || 'sandbox'
    const paddleApiKey = paddleEnvironment === 'production' 
      ? Deno.env.get('PADDLE_API_KEY_PRODUCTION')
      : Deno.env.get('PADDLE_API_KEY_SANDBOX')
    
    console.log(`🔧 Using Paddle ${paddleEnvironment} environment`)
    console.log(`🔑 API Key starts with: ${paddleApiKey?.substring(0, 10)}...`)
    console.log(`🔑 API Key length: ${paddleApiKey?.length}`)
    
    if (!paddleApiKey) {
      console.error(`❌ Missing Paddle API key for ${paddleEnvironment} environment`)
      return new Response(
        JSON.stringify({ error: 'Paddle API configuration error' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }
    // Initialize Supabase client with anon key for auth
    const authHeader = req.headers.get('Authorization')!
    const supabaseAuth = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      { global: { headers: { Authorization: authHeader } } }
    )

    // Initialize Supabase client with service role key for database operations
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get authenticated user
    const {
      data: { user },
      error: authError,
    } = await supabaseAuth.auth.getUser()

    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        {
          status: 401,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    // Get user's Paddle customer ID from the new public.paddle_customers table
    const { data: customerData, error: customerError } = await supabase
      .from('paddle_customers')
      .select('paddle_customer_id, email')
      .eq('user_id', user.id)
      .single()

    let finalCustomerId: string

    if (customerError || !customerData?.paddle_customer_id) {
      console.log('❌ No Paddle customer found. CustomerError:', customerError)
      console.log('❌ Customer data:', customerData)
      
      // Also check if user has any subscription (for users who might have subscriptions but no customer record yet)
      const { data: subscription } = await supabase
        .from('user_subscriptions')
        .select('paddle_subscription_id, status')
        .eq('user_id', user.id)
        .single()

      if (!subscription?.paddle_subscription_id) {
        return new Response(
          JSON.stringify({ 
            error: 'No billing history found',
            details: 'You need to make a purchase or have an active subscription to access the billing portal.'
          }),
          {
            status: 404,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        )
      }

      // If we have a subscription but no customer record, we need to fetch customer ID from Paddle
      console.log(`🔍 Found subscription ${subscription.paddle_subscription_id} but no customer record, fetching from Paddle...`)
      
      // Get subscription details from Paddle to extract customer_id
      const subscriptionApiUrl = paddleEnvironment === 'production' 
        ? 'https://api.paddle.com' 
        : 'https://sandbox-api.paddle.com'
      const subscriptionUrl = `${subscriptionApiUrl}/subscriptions/${subscription.paddle_subscription_id}`
      
      const subscriptionResponse = await fetch(subscriptionUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${paddleApiKey}`,
          'Content-Type': 'application/json',
        },
      })

      if (!subscriptionResponse.ok) {
        const errorData = await subscriptionResponse.text()
        console.error('❌ Failed to fetch subscription details:', errorData)
        
        return new Response(
          JSON.stringify({ 
            error: 'Unable to access billing portal',
            details: 'Could not verify your billing information. Please try again later.',
            debug: {
              status: subscriptionResponse.status,
              environment: paddleEnvironment,
              subscription_id: subscription.paddle_subscription_id
            }
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        )
      }

      const subscriptionData = await subscriptionResponse.json()
      const paddleCustomerId = subscriptionData.data?.customer_id

      if (!paddleCustomerId) {
        console.error('❌ No customer_id found in subscription data:', subscriptionData)
        return new Response(
          JSON.stringify({ 
            error: 'Invalid subscription data',
            details: 'Unable to find customer information for this subscription.'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        )
      }

      console.log(`🎯 Found customer ID from subscription: ${paddleCustomerId}`)
      
      // Create customer record for future use
      try {
        await supabase.rpc('upsert_paddle_customer', {
          p_user_id: user.id,
          p_paddle_customer_id: paddleCustomerId,
          p_email: user.email || '<EMAIL>',
          p_metadata: { source: 'customer_portal_access' }
        })
        console.log('✅ Created customer record for future use')
      } catch (error) {
        console.warn('⚠️ Failed to create customer record:', error)
        // Don't fail the request if we can't create the record
      }

      // Use the customer ID we just found
      finalCustomerId = paddleCustomerId
    } else {
      console.log(`🎯 Found existing customer: ${customerData.paddle_customer_id}`)
      finalCustomerId = customerData.paddle_customer_id
    }

    // Parse request body for any additional options (though Paddle doesn't use return_url in this endpoint)
    const requestBody = await req.json().catch(() => ({}))

    // Use environment-specific API base URL
    const apiBaseUrl = paddleEnvironment === 'production' 
      ? 'https://api.paddle.com' 
      : 'https://sandbox-api.paddle.com'
    const portalUrl = `${apiBaseUrl}/customers/${finalCustomerId}/portal-sessions`
    console.log(`🌐 Full Paddle API URL: ${portalUrl} (${paddleEnvironment})`)

    // Create customer portal session via Paddle API
    // Note: Paddle's API doesn't accept a request body for this endpoint
    const portalResponse = await fetch(portalUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${paddleApiKey}`,
        'Content-Type': 'application/json',
      },
    })

    if (!portalResponse.ok) {
      const errorData = await portalResponse.text()
      console.error('❌ Paddle API Error:', errorData)
      console.error('❌ Response status:', portalResponse.status)
      console.error('❌ Response headers:', portalResponse.headers)
      
      return new Response(
        JSON.stringify({ 
          error: 'Failed to create portal session',
          details: 'Unable to connect to billing portal. Please try again later.',
          debug: {
            status: portalResponse.status,
            environment: paddleEnvironment,
            customer_id: finalCustomerId
          }
        }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    const portalData = await portalResponse.json()
    console.log('✅ Portal session created successfully')
    console.log('🔗 Portal session ID:', portalData.data?.id)
    const portalSession: PortalSession = portalData.data

    // Note: Portal session logging removed as billing_portal_sessions table no longer exists
    console.log('✅ Portal session created successfully (logging skipped)')

    console.log('🚀 Returning portal URL to frontend')
    // Return portal URL to frontend
    return new Response(
      JSON.stringify({
        success: true,
        portal_url: portalSession.urls.general.overview,
        session_id: portalSession.id,
        customer_id: portalSession.customer_id,
        environment: paddleEnvironment,
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )

  } catch (error) {
    console.error('❌ Customer portal error:', error)
    console.error('❌ Error stack:', error.stack)
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error.message,
        debug: {
          timestamp: new Date().toISOString(),
          function: 'paddle-customer-portal'
        }
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  }
})

/* Edge Function Implementation Notes:

SETUP REQUIRED:
1. Environment variables in Supabase:
   - PADDLE_ENVIRONMENT: 'sandbox' or 'production' (defaults to 'sandbox')
   - PADDLE_API_KEY_SANDBOX: Your Paddle sandbox API key (for testing)
   - PADDLE_API_KEY_PRODUCTION: Your Paddle production API key (for live)
   - SITE_URL: Your application's base URL (for reference only)

2. Portal sessions are created on-demand and not stored in database

TESTING ENVIRONMENT:
- Uses PADDLE_ENVIRONMENT variable to switch between sandbox/production
- Sandbox API: https://sandbox-api.paddle.com
- Production API: https://api.paddle.com
- Comprehensive logging for debugging in both environments
- Environment information included in response for verification

USAGE FROM FRONTEND:
```typescript
const openBillingPortal = async () => {
  try {
    const { data, error } = await supabase.functions.invoke('paddle-customer-portal', {
      body: {} // No body parameters needed for Paddle's API
    })
    
    if (error) throw error
    
    console.log(`Opening portal in ${data.environment} environment`)
    // Redirect to Paddle's hosted portal
    window.location.href = data.portal_url
  } catch (error) {
    console.error('Failed to open billing portal:', error)
    // Show error message to user
  }
}
```

PADDLE API DETAILS:
- Endpoint: POST /customers/{customer_id}/portal-sessions
- No request body required
- Returns authenticated URL in response.data.urls.general.overview
- Portal sessions are automatically time-limited by Paddle
- Environment-aware URL selection (sandbox vs production)

SECURITY FEATURES:
- Authenticated users only
- Validates active subscription exists
- Uses Paddle customer ID from verified subscription
- Logs portal sessions for audit trail
- Handles Paddle API errors gracefully
- Environment validation and logging

ERROR HANDLING:
- 401: User not authenticated
- 404: No active subscription found
- 500: Paddle API errors or server issues
- Detailed error messages with debug information
- Environment context in error responses

TESTING WORKFLOW:
1. Set PADDLE_ENVIRONMENT=sandbox for testing
2. Use PADDLE_API_KEY_SANDBOX for test API calls
3. Test with Paddle sandbox customers and subscriptions
4. Verify portal URL generation works correctly
5. Test error scenarios (no subscription, API failures)
6. Switch to production environment when ready

DEBUGGING:
- Check console logs for detailed request/response information
- Verify environment variables are set correctly
- Confirm customer_id exists in Paddle's system
- Test with both sandbox and production configurations
*/

// @ts-ignore: Deno types
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
// @ts-ignore: Supabase types
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.0';

import {
  createErrorResponse,
  createSuccessResponse,
  parseRequestBody,
  base64ToUint8Array,
  validateAudioData,
  ErrorCode
} from '../_shared/utils.ts';
import { transcribeWithLemonFox } from '../_shared/lemonfox.ts';

// Declare Deno types
declare const Deno: {
  env: {
    get(key: string): string | undefined;
  };
};

// Service and model configurations
const SUPPORTED_SERVICES = ['assemblyai', 'lemonfox'] as const;
type TranscriptionService = typeof SUPPORTED_SERVICES[number];

const SUPPORTED_MODELS = {
  assemblyai: ['best', 'nano'] as const,
  lemonfox: ['whisper-1'] as const
} as const;

type AssemblyAIModel = typeof SUPPORTED_MODELS['assemblyai'][number];
type LemonFoxModel = typeof SUPPORTED_MODELS['lemonfox'][number];
type SupportedModel = AssemblyAIModel | LemonFoxModel;

// Create a Supabase client for database operations
const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Get service API keys from environment
const ASSEMBLYAI_API_KEY = Deno.env.get('ASSEMBLYAI_API_KEY') || '';
const LEMONFOX_AI_API_KEY = Deno.env.get('LEMONFOX_AI_API_KEY') || '';

if (!ASSEMBLYAI_API_KEY || !LEMONFOX_AI_API_KEY) {
  throw new Error('Missing required service API keys');
}

// Add new type for pricing response
type PricingCheckResult = {
  can_use: boolean;
  pricing_model: 'credits' | 'payg' | null;
  cost: number;
  error_code?: string;
};

// Add type for transcription result
type TranscriptionResult = {
  text: string;
};

// Utility function to convert headers to object
function getHeadersAsObject(headers: Headers): Record<string, string> {
  const result: Record<string, string> = {};
  headers.forEach((value, key) => {
    result[key] = value;
  });
  return result;
}

// AssemblyAI API functions
async function transcribeWithAssemblyAI(
  audioData: Uint8Array,
  model: string,
  language: string,
  audioDurationSeconds?: number
): Promise<TranscriptionResult> {
  // Upload audio to AssemblyAI
  const uploadRequest = {
    url: 'https://api.assemblyai.com/v2/upload',
    method: 'POST',
    headers: {
      'Authorization': ASSEMBLYAI_API_KEY,
      'Content-Type': 'application/octet-stream'
    }
  };

  console.log('AssemblyAI upload request:', {
    ...uploadRequest,
    audioDataSize: audioData.length,
    authKeyLength: ASSEMBLYAI_API_KEY.length,
    authKeyPrefix: ASSEMBLYAI_API_KEY.substring(0, 10) + '...',
    audioDurationSeconds
  });

  const uploadResponse = await fetch(uploadRequest.url, {
    ...uploadRequest,
    body: audioData
  });

  const responseHeaders = getHeadersAsObject(uploadResponse.headers);
  console.log('AssemblyAI upload response headers:', responseHeaders);

  let responseText: string;
  try {
    responseText = await uploadResponse.text();
    console.log('AssemblyAI upload raw response:', responseText);
  } catch (e: any) {
    responseText = 'Failed to read response text: ' + (e?.message || String(e));
    console.error('Failed to read upload response:', e);
  }

  if (!uploadResponse.ok) {
    const error = {
      request: {
        ...uploadRequest,
        audioDataSize: audioData.length
      },
      response: {
        status: uploadResponse.status,
        statusText: uploadResponse.statusText,
        headers: responseHeaders,
        body: responseText
      }
    };

    console.error('AssemblyAI upload failed:', JSON.stringify(error, null, 2));
    throw new Error(`AssemblyAI upload failed: Status ${uploadResponse.status} - ${uploadResponse.statusText}. Response: ${responseText}`);
  }

  let uploadJson;
  try {
    uploadJson = JSON.parse(responseText);
    console.log('AssemblyAI upload parsed response:', uploadJson);
  } catch (e: any) {
    console.error('Failed to parse upload response as JSON:', {
      error: e?.message || String(e),
      responseText
    });
    throw new Error(`Failed to parse AssemblyAI upload response: ${e?.message || String(e)}. Raw response: ${responseText}`);
  }

  const { upload_url } = uploadJson;
  if (!upload_url) {
    console.error('Missing upload_url in response:', uploadJson);
    throw new Error('AssemblyAI upload response missing upload_url');
  }

  // Start transcription
  const transcribeRequest = {
    url: 'https://api.assemblyai.com/v2/transcript',
    method: 'POST',
    headers: {
      'Authorization': ASSEMBLYAI_API_KEY,
      'Content-Type': 'application/json'
    },
    body: {
      audio_url: upload_url,
      language_code: language,
      speech_model: model !== null ? model : 'best'
    }
  };

  console.log('AssemblyAI transcription request:', {
    ...transcribeRequest,
    headers: {
      ...transcribeRequest.headers,
      'Authorization': ASSEMBLYAI_API_KEY
    }
  });

  const transcribeResponse = await fetch(transcribeRequest.url, {
    method: transcribeRequest.method,
    headers: transcribeRequest.headers,
    body: JSON.stringify(transcribeRequest.body)
  });

  const transcribeResponseHeaders = getHeadersAsObject(transcribeResponse.headers);
  console.log('AssemblyAI transcription response headers:', transcribeResponseHeaders);

  let transcribeResponseText: string;
  try {
    transcribeResponseText = await transcribeResponse.text();
    console.log('AssemblyAI transcription raw response:', transcribeResponseText);
  } catch (e: any) {
    transcribeResponseText = 'Failed to read response text: ' + (e?.message || String(e));
    console.error('Failed to read transcription response:', e);
  }

  if (!transcribeResponse.ok) {
    const error = {
      request: transcribeRequest,
      response: {
        status: transcribeResponse.status,
        statusText: transcribeResponse.statusText,
        headers: transcribeResponseHeaders,
        body: transcribeResponseText
      }
    };

    console.error('AssemblyAI transcription failed:', JSON.stringify(error, null, 2));
    throw new Error(`AssemblyAI transcription failed: Status ${transcribeResponse.status} - ${transcribeResponse.statusText}. Response: ${transcribeResponseText}`);
  }

  let transcribeJson;
  try {
    transcribeJson = JSON.parse(transcribeResponseText);
    console.log('AssemblyAI transcription parsed response:', transcribeJson);
  } catch (e: any) {
    console.error('Failed to parse transcription response as JSON:', {
      error: e?.message || String(e),
      responseText: transcribeResponseText
    });
    throw new Error(`Failed to parse AssemblyAI transcription response: ${e?.message || String(e)}. Raw response: ${transcribeResponseText}`);
  }

  const { id: transcriptId } = transcribeJson;
  if (!transcriptId) {
    console.error('Missing transcriptId in response:', transcribeJson);
    throw new Error('AssemblyAI transcription response missing id');
  }

  // Poll for results
  let attempts = 0;
  const maxAttempts = 3;

  while (attempts < maxAttempts) {
    const statusResponse = await fetch(`https://api.assemblyai.com/v2/transcript/${transcriptId}`, {
      headers: {
        'Authorization': ASSEMBLYAI_API_KEY
      }
    });

    console.log(`AssemblyAI status check attempt ${attempts + 1}:`, {
      status: statusResponse.status,
      statusText: statusResponse.statusText,
      headers: getHeadersAsObject(statusResponse.headers)
    });

    if (!statusResponse.ok) {
      const errorBody = await statusResponse.text().catch(() => 'Failed to read error response');
      console.error('AssemblyAI status check failed:', {
        status: statusResponse.status,
        statusText: statusResponse.statusText,
        headers: getHeadersAsObject(statusResponse.headers),
        body: errorBody,
        attempt: attempts + 1
      });
      throw new Error(`Failed to get transcript status: Status ${statusResponse.status} - ${statusResponse.statusText}. Response: ${errorBody}`);
    }

    const result = await statusResponse.json();
    console.log('AssemblyAI status check result:', {
      transcriptId,
      attempt: attempts + 1,
      status: result.status,
      error: result.error,
      completedAt: result.completed_at
    });

    if (result.status === 'completed') {
      console.log('AssemblyAI transcription completed:', {
        transcriptId,
        wordCount: result.words?.length || 0,
        textLength: result.text?.length || 0,
        confidence: result.confidence,
        audioDuration: audioDurationSeconds
      });

      return {
        text: result.text
      };
    } else if (result.status === 'error') {
      console.error('AssemblyAI transcription error:', {
        transcriptId,
        error: result.error,
        attempt: attempts + 1,
        fullResult: result
      });
      throw new Error(`Transcription error: ${JSON.stringify(result.error)}`);
    }

    await new Promise(resolve => setTimeout(resolve, 5000));
    attempts++;
  }

  console.error('AssemblyAI transcription timeout:', {
    transcriptId,
    attempts,
    maxAttempts
  });
  throw new Error(`Transcription timed out after ${attempts} attempts`);
}

// Main handler function
serve(async (req: Request) => {
  try {
    // Parse and validate request body
    const body = await parseRequestBody(req);

    // Log all request headers for debugging
    const allHeaders: Record<string, string> = {};
    req.headers.forEach((value, key) => {
      allHeaders[key] = value;
    });
    console.log('INCOMING REQUEST HEADERS:', JSON.stringify(allHeaders, null, 2));

    console.log('Parsed request body:', {
      ...body,
      audioUrl: body.audioUrl ? `[Base64 audio data, length: ${body.audioUrl.length}]` : undefined
    });

    // Validate VoiceHype API key
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return createErrorResponse(401, 'Missing Authorization header', ErrorCode.UNAUTHORIZED);
    }

    // Validate key format
    const apiKey = authHeader.replace('Bearer ', '');
    if (!apiKey) {
      return createErrorResponse(401, 'Invalid Authorization header format', ErrorCode.UNAUTHORIZED);
    }

    console.log('Validating API key:', {
      keyLength: apiKey.length,
      keyPrefix: apiKey.substring(0, 5) + '...'
    });

    // Check if the API key is valid
    const { data: validationData, error: validationError } = await supabase
      .rpc('validate_api_key', { p_key: apiKey });

    if (validationError || !validationData || validationData.length === 0) {
      console.error('API key validation failed:', validationError);
      return createErrorResponse(401, 'Invalid API key', ErrorCode.UNAUTHORIZED);
    }

    const userId = validationData[0].user_id;
    const apiKeyId = validationData[0].api_key_id;

    console.log('API key validated successfully for user:', userId);

    // Check for unpaid balances (async, non-blocking for better performance)
    const unpaidBalanceCheck = supabase
      .rpc('has_unpaid_payg_balance', { p_user_id: userId })
      .then(async ({ data: hasUnpaidBalance, error: balanceCheckError }) => {
        if (balanceCheckError) {
          console.error('Error checking for unpaid balances:', balanceCheckError);
          return null; // Continue anyway
        }
        
        if (hasUnpaidBalance) {
          const { data: unpaidBalances } = await supabase
            .rpc('get_unpaid_payg_balances', { p_user_id: userId });
          
          console.log('Unpaid balances found:', { hasUnpaidBalance, balances: unpaidBalances });
          return unpaidBalances;
        }
        
        return null;
      });

    // Destructure and validate request parameters
    const {
      audioUrl,
      service = 'assemblyai' as TranscriptionService,
      model = service === 'assemblyai' ? 'best' as AssemblyAIModel : 'whisper-1' as LemonFoxModel,
      language = 'en'
    } = body;

    // Block Hebrew language requests
    if (language.toLowerCase() === 'he' || language.toLowerCase() === 'hebrew') {
      return createErrorResponse(
        400,
        'Hebrew language is not supported. Stop the genocide in Palestine.',
        ErrorCode.INVALID_REQUEST
      );
    }

    // Validate request parameters
    if (!audioUrl) {
      return createErrorResponse(400, 'Missing audioUrl', ErrorCode.INVALID_REQUEST);
    }

    // Validate service and model
    if (!SUPPORTED_SERVICES.includes(service)) {
      return createErrorResponse(400, `Unsupported service. Must be one of: ${SUPPORTED_SERVICES.join(', ')}`, ErrorCode.INVALID_REQUEST);
    }

    const serviceModels = SUPPORTED_MODELS[service as keyof typeof SUPPORTED_MODELS];
    if (!(serviceModels as readonly string[]).includes(model)) {
      return createErrorResponse(400, `Unsupported model for ${service}. Must be one of: ${serviceModels.join(', ')}`, ErrorCode.UNSUPPORTED_MODEL);
    }

    // Extract and validate audio data
    const audioData = base64ToUint8Array(audioUrl);
    validateAudioData(audioData, service);

    // Get audio file size
    const audioSize = audioData.length;

    // Calculate audio duration from the audio data
    let audioDurationSeconds: number;
    try {
      // Validate WAV header
      if (audioData.length < 44) {
        throw new Error('Audio data too small to contain WAV header');
      }

      // Extract WAV header information
      const header = new DataView(audioData.buffer, 0, 44);

      // Check RIFF header
      const riffHeader = String.fromCharCode(...new Uint8Array(audioData.buffer, 0, 4));
      if (riffHeader !== 'RIFF') {
        throw new Error('Invalid WAV file: Missing RIFF header');
      }

      // Extract audio parameters
      const numChannels = header.getUint16(22, true);
      const sampleRate = header.getUint32(24, true);
      const bitsPerSample = header.getUint16(34, true);

      // Calculate duration
      const bytesPerSample = bitsPerSample / 8;
      const dataSize = audioData.length - 44;
      audioDurationSeconds = dataSize / (sampleRate * numChannels * bytesPerSample);

      console.log('Calculated audio duration:', {
        numChannels,
        sampleRate,
        bitsPerSample,
        dataSize,
        duration: audioDurationSeconds
      });
    } catch (error) {
      console.error('Error calculating audio duration:', error);
      return createErrorResponse(400, 'Failed to calculate audio duration', ErrorCode.INVALID_REQUEST);
    }


    // Convert to minutes for billing purposes - ensure proper formatting to avoid floating point issues
    const audioDurationMinutes = Number((audioDurationSeconds / 60).toFixed(9));

    // Log all duration values to validate correctness
    console.log('Audio duration values for billing:', {
      audioDurationSeconds,
      audioDurationMinutes,
      audioSize
    });

    // Check unpaid balance result first
    const unpaidBalances = await unpaidBalanceCheck;
    if (unpaidBalances) {
      return createErrorResponse(
        402,
        'You have unpaid PAYG balances from previous months. Please settle your outstanding balance before using the service.',
        ErrorCode.UNPAID_BALANCE,
        { unpaidBalances }
      );
    }

    // Run usage allowance check and service pricing fetch in parallel for better performance
    console.log('Running parallel checks for usage allowance and service pricing...');
    
    const modelName = service === 'assemblyai' ? `assembly-ai/${model}` : model;
    
    const [
      { data: pricingCheck, error: usagePricingError },
      { data: servicePricing, error: servicePricingError }
    ] = await Promise.all([
      supabase.rpc('check_usage_allowance', {
        p_user_id: userId,
        p_service: 'transcription',
        p_model: modelName,
        p_amount: audioDurationMinutes,
        p_api_key_id: apiKeyId
      }),
      supabase
        .from('service_pricing')
        .select('*')
        .eq('service', 'transcription')
        .eq('model', modelName)
        .eq('is_active', true)
        .single()
    ]);

    console.log('Parallel checks completed:', {
      pricingCheck,
      usagePricingError,
      servicePricing,
      servicePricingError
    });

    if (usagePricingError) {
      console.error('Error checking usage allowance:', usagePricingError);
      return createErrorResponse(500, 'Error checking usage allowance', ErrorCode.SERVICE_ERROR);
    }

    if (!pricingCheck || pricingCheck.length === 0 || !pricingCheck[0].can_use) {
      console.error('Insufficient usage allowance:', {
        pricingCheck,
        audioDurationMinutes
      });

      // Use the detailed error message from check_usage_allowance
      const errorMessage = pricingCheck?.[0]?.error_code || 'Insufficient credits or quota for this operation';
      
      // Check for specific error code patterns
      if (errorMessage.includes('transcription_')) {
        // Extract the detailed error message from the database function
        const detailedMessage = errorMessage.replace(/^transcription_/, '').replace(/_/g, ' ');
        return createErrorResponse(
          402,
          detailedMessage,
          ErrorCode.INSUFFICIENT_CREDITS
        );
      }

      if (errorMessage.includes('realtime_')) {
        // Extract the detailed error message for realtime transcription
        const detailedMessage = errorMessage.replace(/^realtime_/, '').replace(/_/g, ' ');
        return createErrorResponse(
          402,
          detailedMessage,
          ErrorCode.INSUFFICIENT_CREDITS
        );
      }

      // Return the detailed error message from the database function
      return createErrorResponse(
        402,
        errorMessage,
        ErrorCode.INSUFFICIENT_CREDITS
      );
    }

    if (servicePricingError || !servicePricing) {
      console.error('Error fetching service pricing:', {
        error: servicePricingError,
        service: 'transcription',
        model: modelName
      });
      return createErrorResponse(500, 'Error fetching service pricing', ErrorCode.SERVICE_ERROR);
    }

    console.log('Service pricing:', {
      pricing: servicePricing,
      cost_per_unit: servicePricing.cost_per_unit,
      unit: servicePricing.unit,
      estimatedCost: servicePricing.cost_per_unit * audioDurationMinutes
    });

    // Record pending usage
    const { data: pendingUsageRow, error: pendingInsertError } = await supabase
      .from('usage_history')
      .insert({
        user_id: userId,
        api_key_id: apiKeyId,
        service: 'transcription',
        model: service === 'assemblyai' ? `assembly-ai/${model}` : model,
        amount: audioDurationMinutes,
        cost: Number((servicePricing.cost_per_unit * audioDurationMinutes).toFixed(9)),
        pricing_model: pricingCheck[0].pricing_model,
        status: 'pending',
        metadata: {
          audioSize,
          audioDurationSeconds,
          requestId: crypto.randomUUID()
        }
      }).select('id')
      .single();

    if (pendingInsertError) {
      return createErrorResponse(500, 'Failed to record pending usage', ErrorCode.SERVICE_ERROR);
    }

    // Perform transcription based on selected service
    let transcription: string;
    try {
      if (service === 'assemblyai') {
        const { text } = await transcribeWithAssemblyAI(audioData, model, language, audioDurationSeconds);
        transcription = text;
      } else {
        const { text } = await transcribeWithLemonFox(audioData, LEMONFOX_AI_API_KEY, model, language, audioDurationSeconds);
        transcription = text;
      }

      // Log duration details (using exact audioDurationSeconds throughout)
      console.log('Audio duration details:', {
        audioDurationSeconds,
        audioDurationMinutes,
        transcriptionLength: transcription?.length || 0
      });

      // Record successful usage and update balances (async, non-blocking)
      supabase
        .rpc('finalize_usage', {
          p_user_id: userId,
          p_service: 'transcription',
          p_model: service === 'assemblyai' ? `assembly-ai/${model}` : model,
          p_amount: audioDurationMinutes,
          p_cost: Number((servicePricing.cost_per_unit * audioDurationMinutes).toFixed(9)),
          p_pricing_model: pricingCheck[0].pricing_model,
          p_metadata: {
            audioSize,
            transcriptionLength: transcription?.length || 0,
            audioDurationSeconds
          },
          p_pending_usage_id: pendingUsageRow?.id ?? null
        })
        .then(({ error }) => {
          if (error) {
            console.error('Failed to finalize usage:', error);
          }
        })
        .catch((error) => {
          console.error('Error in finalize_usage:', error);
        });

      return createSuccessResponse({
      transcription,
      duration: audioDurationSeconds,
      length: transcription?.length || 0
    });

    } catch (error: any) {
      // Record failed usage
      await supabase
        .from('usage_history')
        .insert({
          user_id: userId,
          api_key_id: apiKeyId,
          service: 'transcription',
          model: service === 'assemblyai' ? `assembly-ai/${model}` : model,
          amount: audioDurationMinutes,
          cost: Number((servicePricing.cost_per_unit * audioDurationMinutes).toFixed(9)),
          pricing_model: pricingCheck[0].pricing_model,
          status: 'failed',
          metadata: {
            audioSize,
            error: error.message
          }
        });

      interface ErrorWithResponse extends Error {
        response?: Response;
      }

      interface ErrorDetails {
        message: string;
        stack?: string;
        cause?: any;
        name?: string;
        response?: {
          status: number;
          statusText: string;
          body: string;
          headers: Record<string, string>;
        };
      }

      const errorDetails: ErrorDetails = {
        message: error.message,
        stack: error.stack,
        cause: error.cause,
        name: error.name
      };

      // Type assertion to properly handle error with response
      const typedError = error as Error & { response?: Response };
      if (typedError.response instanceof Response) {
        try {
          const responseBody = await typedError.response.text();
          errorDetails.response = {
            status: typedError.response.status,
            statusText: typedError.response.statusText,
            body: responseBody,
            headers: getHeadersAsObject(typedError.response.headers)
          };
        } catch (e) {
          errorDetails.response = {
            status: 0,
            statusText: 'Unknown',
            body: 'Failed to read response details',
            headers: {}
          };
        }
      }

      console.error('Transcription error:', errorDetails);

      return createErrorResponse(
        500,
        `Transcription failed: ${error.message}`,
        ErrorCode.SERVICE_ERROR
      );
    }

  } catch (error: unknown) {
    const errorDetails: {
      message: string;
      stack?: string;
      cause?: unknown;
      name?: string;
      response?: {
        status: number;
        statusText: string;
        body: string;
        headers: Record<string, string>;
      };
    } = {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      cause: error instanceof Error ? error.cause : undefined,
      name: error instanceof Error ? error.name : 'Error'
    };

    if (error instanceof Object && 'response' in error) {
      const response = (error as { response?: Response }).response;
      if (response instanceof Response) {
        try {
          const responseBody = await response.text();
          errorDetails.response = {
            status: response.status,
            statusText: response.statusText,
            body: responseBody,
            headers: getHeadersAsObject(response.headers)
          };
        } catch (e) {
          errorDetails.response = {
            status: 0,
            statusText: 'Unknown',
            body: 'Failed to read response details',
            headers: {}
          };
        }
      }
    }

    console.error('Transcription error:', errorDetails);

    return createErrorResponse(
      500,
      `Transcription failed: ${errorDetails.message}`,
      ErrorCode.SERVICE_ERROR
    );
  }
});

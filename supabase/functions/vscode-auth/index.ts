// vscode-auth.ts - Supabase Edge Function
// Place this file in your Supabase functions directory

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.0';

// Types for the request body
interface VSCodeAuthRequest {
  state: string;
  redirect_uri: string;
}

// Initialize the Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';

// Create a Supabase client with the service role key for admin access
const supabase = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Function to create a new API key for a user
 */
async function createApiKey(userId: string): Promise<string | null> {
  try {
    // Generate a friendly name for the VS Code extension key
    const name = `VS Code Extension (${new Date().toISOString().split('T')[0]})`;
      // Call the create_api_key RPC function
    const { data, error } = await supabase.rpc('create_api_key', {
      p_name: name,
      // Optional: Set expiration for security (e.g., 1 year)
      // p_expires_at: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()
    });
    
    if (error) {
      console.error('Error creating API key:', error);
      return null;
    }
    
    // The RPC function should return the key_secret (API key)
    return data?.key_secret || null;
  } catch (err) {
    console.error('Exception creating API key:', err);
    return null;
  }
}

/**
 * Handler for the VS Code authentication request
 */
serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
      status: 204,
    });
  }
  
  try {
    // Get the request URL and parse query parameters
    const url = new URL(req.url);
    const state = url.searchParams.get('state');
    const redirectUri = url.searchParams.get('redirect_uri');
    
    // Validate the required parameters
    if (!state || !redirectUri) {
      return new Response(
        JSON.stringify({ error: 'Missing required parameters' }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }
      // Build the login page URL for the Supabase auth flow using Google OAuth
    // This will redirect to Google OAuth, then back to our callback
    const loginUrl = new URL(`${supabaseUrl}/auth/v1/authorize`);
    
    // Use Google as the OAuth provider (you can also use 'github' if preferred)
    loginUrl.searchParams.append('provider', 'google');
    loginUrl.searchParams.append('redirect_to', `${supabaseUrl}/functions/v1/vscode-auth-callback?state=${encodeURIComponent(state)}&vscode_redirect=${encodeURIComponent(redirectUri)}`);
    
    // Add additional security parameters
    loginUrl.searchParams.append('response_type', 'code');
    loginUrl.searchParams.append('flow_type', 'pkce');
    
    // Redirect the user to the login page
    return new Response(null, {
      status: 302,
      headers: {
        'Location': loginUrl.toString(),
      },
    });
  } catch (error) {
    console.error('Error in vscode-auth function:', error);
    
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      }
    );
  }
});

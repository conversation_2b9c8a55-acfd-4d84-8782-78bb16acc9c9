import WebSocket from 'ws';

// Set environment variables
const SUPABASE_URL = 'https://nffixzoqnqxpcqpcxpps.supabase.co';
const VOICEHYPE_API_KEY = "vhkey_P5mhaT9yed/WWULnEfVtryzXUwsNcBxE";

if (!VOICEHYPE_API_KEY) {
  console.error('VOICEHYPE_API_KEY environment variable not set');
  process.exit(1);
}

// Function to test our real-time transcription edge function
async function testRealtimeTranscription(
  service: 'openai' | 'assemblyai' = 'openai',
  model: string = service === 'openai' ? 'gpt-4o-mini-transcribe' : 'default'
) {
  console.log(`Testing real-time transcription with service: ${service}, model: ${model}`);
  
  // Convert the HTTPS URL to WSS for WebSocket connection
  const wsUrl = 'wss://nffixzoqnqxpcqpcxpps.supabase.co/functions/v1/realtime-transcribe';
  
  // Add query parameters
  const url = new URL(wsUrl);
  url.searchParams.append('service', service);
  url.searchParams.append('model', model);

  console.log(`Connecting to WebSocket: ${url.toString()}`);
  
  // Set up WebSocket with our edge function
  const socket = new WebSocket(url.toString(), {
    headers: {
      'Authorization': `Bearer ${VOICEHYPE_API_KEY}`,
      'apikey': VOICEHYPE_API_KEY
    }
  });
  
  console.log('Connecting to real-time transcription service...');
  
  // Set up event handlers
  socket.onopen = () => {
    console.log('Connected to real-time transcription service');
    
    // Simulate sending some audio data (in a real implementation, you'd stream microphone data)
    setTimeout(() => {
      console.log('Sending sample audio data...');
      
      // In a real implementation, you'd send actual audio data
      // This is just a placeholder for demonstration
      const sampleAudioData = new ArrayBuffer(1600); // 50ms of 16kHz 16-bit audio
      socket.send(sampleAudioData);
      
      // Send a few more chunks
      let count = 0;
      const interval = setInterval(() => {
        if (count >= 10) {
          clearInterval(interval);
          return;
        }
        socket.send(sampleAudioData);
        count++;
      }, 100);
    }, 1000);
    
    // Close the connection after a short test
    setTimeout(() => {
      console.log('Test completed, closing connection...');
      socket.close();
    }, 10000);
  };
  
  socket.onmessage = (event) => {
    // Handle and display transcription results
    try {
      const data = typeof event.data === 'string' ? JSON.parse(event.data) : event.data;
      console.log('Received message:', data);
      
      if (data.text) {
        console.log('Transcription:', data.text);
      }
    } catch (error) {
      console.error('Error parsing message:', error);
      console.log('Raw message:', event.data);
    }
  };
  
  socket.onerror = (event) => {
    console.error('WebSocket error:', event);
  };
  
  socket.onclose = (event) => {
    console.log('Connection closed:', event.code, event.reason);
  };
  
  // Return a promise that resolves when the test completes
  return new Promise((resolve) => {
    setTimeout(resolve, 12000);
  });
}

// Main function to run the tests
async function runTests() {
  try {
    console.log('\n=== Testing real-time transcription with OpenAI GPT-4o-mini ===');
    await testRealtimeTranscription('openai', 'gpt-4o-mini-transcribe');
    
    console.log('\n=== Testing real-time transcription with OpenAI GPT-4o ===');
    await testRealtimeTranscription('openai', 'gpt-4o-transcribe');
    
    console.log('\n=== Testing real-time transcription with AssemblyAI ===');
    await testRealtimeTranscription('assemblyai', 'default');
    
    console.log('\nAll tests completed!');
  } catch (error) {
    console.error('Error running tests:', error);
  }
}

// Execute the tests
runTests(); 
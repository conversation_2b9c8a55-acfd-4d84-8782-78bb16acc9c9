import WebSocket from 'ws';
import { readFileSync } from 'fs';
import * as path from 'path';
import fetch from 'node-fetch';

// Set environment variables
const VOICEHYPE_API_KEY = "vhkey_P5mhaT9yed/WWULnEfVtryzXUwsNcBxE";

if (!VOICEHYPE_API_KEY) {
  console.error('VOICEHYPE_API_KEY environment variable not set');
  process.exit(1);
}

// Function to get an AssemblyAI token
async function getAssemblyAIToken(): Promise<string | null> {
  try {
    console.log('Getting AssemblyAI token from our API');
    
    // Use the URL from function
    const response = await fetch('https://nffixzoqnqxpcqpcxpps.supabase.co/functions/v1/transcribe/generate-token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${VOICEHYPE_API_KEY}`
      },
      body: JSON.stringify({
        service: 'assemblyai'
      })
    });

    console.log('Token endpoint response status:', response.status, response.statusText);
    
    const responseText = await response.text();
    
    if (!response.ok) {
      console.error(`Error generating token: ${response.status} ${response.statusText}`, responseText);
      return null;
    }

    // Parse response
    interface TokenResponse {
      data: {
        success: boolean;
        token: string;
        expiration: number;
      };
    }

    try {
      const data = JSON.parse(responseText) as TokenResponse;
      console.log(`Token obtained successfully (expiration: ${data.data.expiration}s)`);
      return data.data.token;
    } catch (parseError) {
      console.error('Failed to parse token response as JSON:', parseError);
      console.error('Raw response:', responseText);
      return null;
    }
  } catch (error) {
    console.error('Error getting AssemblyAI token:', error);
    return null;
  }
}

/**
 * Function to test AssemblyAI real-time transcription using file
 */
async function testDirectAssemblyAIWithFile() {
  console.log('Testing direct connection to AssemblyAI real-time transcription with a file');
  
  // First get a temporary token from our API
  const token = await getAssemblyAIToken();
  if (!token) {
    console.error('Failed to obtain AssemblyAI token, cannot proceed with test');
    return '';
  }
  
  console.log(`Successfully obtained AssemblyAI token: ${token.substring(0, 10)}...`);
  console.log('Now connecting directly to AssemblyAI real-time API');
  
  // Connect directly to AssemblyAI using the token from our API
  const wsUrl = `wss://api.assemblyai.com/v2/realtime/ws?sample_rate=16000&token=${token}`;
  
  console.log(`Connecting to WebSocket using token parameter: ${wsUrl.substring(0, 70)}...`);
  
  // Set up WebSocket with AssemblyAI (no additional headers needed when using token in URL)
  const socket = new WebSocket(wsUrl);
  
  // Track the connection state
  let isConnected = false;
  let transcribedText = '';
  
  // Function to send audio data from a file in small chunks to simulate streaming
  async function streamAudioFile() {
    try {
      // Path to our test audio file
      const testAudioPath = path.join(__dirname, 'test_audio.wav');
      console.log(`Using test audio file: ${testAudioPath}`);
      
      // Read the audio file
      const audioData = readFileSync(testAudioPath);
      console.log(`Audio file size: ${audioData.length} bytes`);
      
      console.log('Starting to stream audio data (no configuration message needed as parameters are in URL)');
      
      // Split the audio data into small chunks for streaming
      const chunkSize = 3200; // 100ms of 16kHz 16-bit audio
      const totalChunks = Math.ceil(audioData.length / chunkSize);
      
      console.log(`Streaming audio in ${totalChunks} chunks of ${chunkSize} bytes each`);
      
      // Send each chunk with a delay to simulate real-time streaming
      for (let i = 0; i < audioData.length; i += chunkSize) {
        if (!isConnected) {
          console.log('WebSocket disconnected, stopping streaming');
          break;
        }
        
        const end = Math.min(i + chunkSize, audioData.length);
        const chunk = audioData.slice(i, end);
        
        if (socket.readyState === WebSocket.OPEN) {
          socket.send(chunk);
          // Log progress at 10% intervals
          if (i % (chunkSize * 10) === 0 || i + chunkSize >= audioData.length) {
            const progress = Math.round((i / audioData.length) * 100);
            console.log(`Sent ${progress}% of audio data`);
          }
        } else {
          console.log('WebSocket not open, stopping streaming');
          break;
        }
        
        // Introduce a small delay to simulate real-time streaming
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      console.log('Finished streaming audio file');
      
      // Wait a bit before closing to ensure all transcription comes back
      setTimeout(() => {
        if (socket.readyState === WebSocket.OPEN) {
          console.log('Sending terminate session message');
          socket.send(JSON.stringify({ terminate_session: true }));
        }
      }, 2000);
      
    } catch (error) {
      console.error('Error streaming audio file:', error);
    }
  }
  
  // Set up event handlers
  socket.onopen = () => {
    console.log('WebSocket connection opened to AssemblyAI');
    isConnected = true;
    
    // Start streaming audio after a short delay
    setTimeout(() => {
      streamAudioFile();
    }, 500);
  };
  
  socket.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data.toString());
      
      // Handle different message types from AssemblyAI
      if (data.message_type === 'SessionBegins') {
        console.log('AssemblyAI session started:', data);
      }
      else if (data.message_type === 'FinalTranscript') {
        console.log('Final transcript:', data.text);
        transcribedText += data.text + ' ';
      } 
      else if (data.message_type === 'PartialTranscript') {
        console.log('Partial transcript:', data.text);
      }
      else if (data.message_type === 'SessionTerminated') {
        console.log('AssemblyAI session terminated');
      }
      else if (data.message_type === 'Error') {
        console.error('AssemblyAI error:', data);
      }
      else {
        console.log('Received message:', data);
      }
    } catch (error) {
      console.error('Error parsing message:', error);
      console.log('Raw message:', event.data.toString().substring(0, 200));
    }
  };
  
  socket.onerror = (event) => {
    console.error('WebSocket error:', event);
    // Try to get more error information
    console.error('Error details:', JSON.stringify({
      message: event.message,
      type: event.type,
      error: event.error,
      target: {
        url: event.target.url,
        readyState: event.target.readyState,
        protocol: event.target.protocol
      }
    }));
  };
  
  socket.onclose = (event) => {
    isConnected = false;
    console.log('Connection closed - Code:', event.code, 'Reason:', event.reason);
    console.log('WebSocket close event details:', {
      wasClean: event.wasClean,
      code: event.code,
      reason: event.reason || 'No reason provided'
    });
    console.log('\nFinal Transcription Result:');
    console.log(transcribedText.trim());
  };
  
  // Return a promise that resolves when the test completes
  return new Promise((resolve) => {
    // Allow 30 seconds for the whole test
    setTimeout(() => {
      if (socket.readyState === WebSocket.OPEN) {
        console.log('Test timeout reached, closing connection...');
        socket.close();
      }
      resolve(transcribedText.trim());
    }, 30000);
  });
}

// Main function
async function runTest() {
  try {
    console.log('\n=== Testing Direct Connection to AssemblyAI ===\n');
    const result = await testDirectAssemblyAIWithFile();
    console.log('\n=== Test Completed ===');
    console.log('Transcription Result:', result);
  } catch (error) {
    console.error('Error running test:', error);
  }
}

// Run the test
runTest(); 
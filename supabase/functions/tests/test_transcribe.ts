import { readFileSync } from 'fs';

// Set environment variables
const SUPABASE_URL = 'https://nffixzoqnqxpcqpcxpps.supabase.co';
const VOICEHYPE_API_KEY = "vhkey_P5mhaT9yed/WWULnEfVtryzXUwsNcBxE";

if (!VOICEHYPE_API_KEY) {
  console.error('VOICEHYPE_API_KEY environment variable not set');
  process.exit(1);
}

/**
 * Reads a file and returns its base64 encoded string
 */
function fileToBase64(filePath: string): string {
  try {
    const fileBuffer = readFileSync(filePath);
    return Buffer.from(fileBuffer).toString('base64');
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error);
    throw error;
  }
}

/**
 * Test transcription with our edge function
 */
async function testTranscription(
  filePath: string, 
  service: 'openai' | 'assemblyai' = 'openai',
  model: string = service === 'openai' ? 'whisper-1' : 'default',
  language?: string
) {
  console.log(`Testing transcription with service: ${service}, model: ${model}`);
  
  try {
    const fileBase64 = fileToBase64(filePath);
    
    // Use our exact edge function URL
    const apiUrl = `${SUPABASE_URL}/functions/v1/transcribe`;
    
    const requestBody = {
      audio: fileBase64,
      service,
      model,
      language: language || 'auto'
    };
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${VOICEHYPE_API_KEY}`,
        'apikey': VOICEHYPE_API_KEY
      },
      body: JSON.stringify(requestBody),
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API Error (${response.status}): ${errorText}`);
      return;
    }
    
    const data = await response.json();
    console.log('Transcription result:', data);
    return data;
    
  } catch (error) {
    console.error('Error during transcription test:', error);
  }
}

async function runTests() {
  // Replace with path to your test audio file
  const testAudioPath = './test_audio.mp3';
  
  console.log('Starting transcription tests...');
  
  // Test with OpenAI Whisper
  await testTranscription(testAudioPath, 'openai', 'whisper-1');
  
  // Test with OpenAI GPT-4o-mini
  await testTranscription(testAudioPath, 'openai', 'gpt-4o-mini-transcribe');
  
  // Test with OpenAI GPT-4o
  await testTranscription(testAudioPath, 'openai', 'gpt-4o-transcribe');
  
  // Test with AssemblyAI
  await testTranscription(testAudioPath, 'assemblyai', 'default');
  
  console.log('All transcription tests completed');
}

runTests().catch(console.error); 
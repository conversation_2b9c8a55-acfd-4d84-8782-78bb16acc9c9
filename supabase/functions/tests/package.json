{"name": "voicehype-tests", "version": "1.0.0", "description": "Test scripts for VoiceHype transcription", "type": "commonjs", "scripts": {"test:transcribe": "npx ts-node test_transcribe.ts", "test:realtime": "npx ts-node test_realtime.ts", "test:assemblyai-realtime": "npx ts-node test_assemblyai_realtime.ts"}, "dependencies": {"node-fetch": "^2.6.7", "node-microphone": "^0.1.6", "ws": "^8.14.2"}, "devDependencies": {"@types/node": "^18.11.18", "@types/node-fetch": "^2.6.4", "@types/ws": "^8.5.10", "ts-node": "^10.9.1", "typescript": "^4.9.5"}}
declare module 'node-microphone' {
  import { EventEmitter } from 'events';

  interface MicrophoneOptions {
    rate?: string;
    channels?: string;
    debug?: boolean;
    device?: string;
    fileType?: string;
    [key: string]: any;
  }

  class Microphone {
    constructor(options?: MicrophoneOptions);
    startRecording(): EventEmitter;
    stopRecording(): void;
  }

  export = Microphone;
} 
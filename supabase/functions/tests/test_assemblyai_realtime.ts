import WebSocket from 'ws';
import { readFileSync } from 'fs';
import * as path from 'path';
// Use CommonJS import for node-fetch v2
import fetch from 'node-fetch';
// Import Microphone using require syntax since it's CommonJS
const Microphone = require('node-microphone');

// Set environment variables
const SUPABASE_URL = 'https://nffixzoqnqxpcqpcxpps.supabase.co';
const VOICEHYPE_API_KEY = "vhkey_P5mhaT9yed/WWULnEfVtryzXUwsNcBxE";

if (!VOICEHYPE_API_KEY) {
  console.error('VOICEHYPE_API_KEY environment variable not set');
  process.exit(1);
}

/**
 * Function to test AssemblyAI real-time transcription via our edge function using a file
 */
async function testAssemblyAIRealtimeWithFile() {
  console.log('Testing AssemblyAI real-time transcription with best model using file streaming');
  
  // First get a temporary token - this part tests our token generation endpoint
  const token = await getAssembly<PERSON>IToken();
  if (!token) {
    console.error('Failed to obtain AssemblyAI token, cannot proceed with test');
    return '';
  }
  
  console.log(`Successfully obtained AssemblyAI token: ${token.substring(0, 10)}...`);
  console.log('Now connecting to our edge function for real-time transcription');
  
  // Use the correct URL for our real-time edge function
  const wsUrl = `wss://nffixzoqnqxpcqpcxpps.supabase.co/functions/v1/transcribe/realtime`;
  
  // Add query parameters
  const url = new URL(wsUrl);
  url.searchParams.append('apiKey', VOICEHYPE_API_KEY);
  url.searchParams.append('service', 'assemblyai');
  url.searchParams.append('model', 'best');
  url.searchParams.append('language', 'en');

  console.log(`Connecting to WebSocket: ${url.toString()}`);
  
  // Set up WebSocket with our edge function
  const socket = new WebSocket(url.toString(), {
    // Set a longer timeout for the connection (60 seconds)
    handshakeTimeout: 60000,
    // Log everything for debugging
    perMessageDeflate: false
  });
  
  // Track the connection state
  let isConnected = false;
  let transcribedText = '';
  
  // Function to send audio data from a file in small chunks to simulate streaming
  async function streamAudioFile() {
    try {
      // Path to our test audio file
      const testAudioPath = path.join(__dirname, 'test_audio.wav');
      console.log(`Using test audio file: ${testAudioPath}`);
      
      // Read the audio file
      const audioData = readFileSync(testAudioPath);
      console.log(`Audio file size: ${audioData.length} bytes`);
      
      // Split the audio data into small chunks for streaming
      const chunkSize = 3200; // 100ms of 16kHz 16-bit audio
      const totalChunks = Math.ceil(audioData.length / chunkSize);
      
      console.log(`Streaming audio in ${totalChunks} chunks of ${chunkSize} bytes each`);
      
      // Send each chunk with a delay to simulate real-time streaming
      for (let i = 0; i < audioData.length; i += chunkSize) {
        if (!isConnected) {
          console.log('WebSocket disconnected, stopping streaming');
          break;
        }
        
        const end = Math.min(i + chunkSize, audioData.length);
        const chunk = audioData.slice(i, end);
        
        if (socket.readyState === WebSocket.OPEN) {
          socket.send(chunk);
          // Log progress at 10% intervals
          if (i % (chunkSize * 10) === 0 || i + chunkSize >= audioData.length) {
            const progress = Math.round((i / audioData.length) * 100);
            console.log(`Sent ${progress}% of audio data`);
          }
        } else {
          console.log('WebSocket not open, stopping streaming');
          break;
        }
        
        // Introduce a small delay to simulate real-time streaming
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      console.log('Finished streaming audio file');
      
      // Wait a bit before closing to ensure all transcription comes back
      setTimeout(() => {
        if (socket.readyState === WebSocket.OPEN) {
          console.log('Sending close message to server');
          socket.send(JSON.stringify({ type: 'close' }));
        }
      }, 2000);
      
    } catch (error) {
      console.error('Error streaming audio file:', error);
    }
  }
  
  // Set up event handlers
  socket.onopen = () => {
    console.log('WebSocket connection opened, waiting for connected message from server...');
  };
  
  socket.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data.toString());
      
      // Handle different message types from the server
      if (data.type === 'connected') {
        isConnected = true;
        console.log('Connected to AssemblyAI real-time service with session ID:', data.sessionId);
        console.log('Max session duration:', data.maxDurationMs, 'ms');
        
        // Start streaming audio data once connected
        streamAudioFile();
      } 
      else if (data.message_type === 'FinalTranscript') {
        console.log('Final transcript:', data.text);
        transcribedText += data.text + ' ';
      } 
      else if (data.message_type === 'PartialTranscript') {
        console.log('Partial transcript:', data.text);
      }
      else if (data.type === 'error') {
        console.error('Error from server:', data.message, data.details || '');
      }
      else if (data.type === 'timeout') {
        console.log('Session timeout:', data.message);
      }
      else if (data.type === 'service_disconnected') {
        console.log('Service disconnected - Code:', data.code, 'Reason:', data.reason);
      }
      else {
        console.log('Received message:', data);
      }
    } catch (error) {
      console.error('Error parsing message:', error);
      console.log('Raw message:', event.data.toString().substring(0, 200));
    }
  };
  
  socket.onerror = (event) => {
    console.error('WebSocket error:', event);
    // Try to get more error information
    console.error('Error details:', JSON.stringify({
      message: event.message,
      type: event.type,
      error: event.error,
      target: {
        url: event.target.url,
        readyState: event.target.readyState,
        protocol: event.target.protocol
      }
    }));
  };
  
  socket.onclose = (event) => {
    isConnected = false;
    console.log('Connection closed - Code:', event.code, 'Reason:', event.reason);
    console.log('WebSocket close event details:', {
      wasClean: event.wasClean,
      code: event.code,
      reason: event.reason || 'No reason provided'
    });
    console.log('\nFinal Transcription Result:');
    console.log(transcribedText.trim());
  };
  
  // Return a promise that resolves when the test completes
  return new Promise((resolve) => {
    // Allow 30 seconds for the whole test
    setTimeout(() => {
      if (socket.readyState === WebSocket.OPEN) {
        console.log('Test timeout reached, closing connection...');
        socket.close();
      }
      resolve(transcribedText.trim());
    }, 30000);
  });
}

/**
 * Function to test AssemblyAI real-time transcription via our edge function using microphone
 */
async function testAssemblyAIRealtimeWithMicrophone() {
  console.log('Testing AssemblyAI real-time transcription with best model using microphone');
  
  // First get a temporary token - this part tests our token generation endpoint
  const token = await getAssemblyAIToken();
  if (!token) {
    console.error('Failed to obtain AssemblyAI token, cannot proceed with test');
    return '';
  }
  
  console.log(`Successfully obtained AssemblyAI token: ${token.substring(0, 10)}...`);
  console.log('Now connecting to our edge function for real-time transcription');
  
  // Use the correct URL for our real-time edge function
  const wsUrl = `wss://nffixzoqnqxpcqpcxpps.supabase.co/functions/v1/transcribe/realtime`;
  
  // Add query parameters
  const url = new URL(wsUrl);
  url.searchParams.append('apiKey', VOICEHYPE_API_KEY);
  url.searchParams.append('service', 'assemblyai');
  url.searchParams.append('model', 'best');
  url.searchParams.append('language', 'en');

  console.log(`Connecting to WebSocket: ${url.toString()}`);
  
  // Set up WebSocket with our edge function
  const socket = new WebSocket(url.toString(), {
    // Set a longer timeout for the connection (60 seconds)
    handshakeTimeout: 60000,
    // Log everything for debugging
    perMessageDeflate: false
  });
  
  // Track the connection state
  let isConnected = false;
  let transcribedText = '';
  let mic: any = null;
  
  // Function to stream microphone audio data
  function streamMicrophone() {
    try {
      console.log('Starting microphone recording...');
      
      // Initialize the microphone
      // Example format: 16-bit PCM (signed) at 16kHz sample rate, mono channel
      mic = new Microphone({
        rate: '16000',
        channels: '1',
        debug: false,
        device: 'default',
        fileType: 'wav'
      });
      
      console.log('Microphone initialized, speak now...');
      console.log('Test will run for 15 seconds. Please speak clearly into the microphone.');
      
      // Start the microphone stream
      const micStream = mic.startRecording();
      
      // Handle data from the microphone
      micStream.on('data', (data: Buffer) => {
        if (isConnected && socket.readyState === WebSocket.OPEN) {
          // Send raw audio data to the WebSocket
          socket.send(data);
        }
      });
      
      // Handle microphone errors
      micStream.on('error', (error: any) => {
        console.error('Microphone error:', error);
      });
      
      // Automatically stop recording after 15 seconds
      setTimeout(() => {
        if (mic) {
          console.log('Stopping microphone recording...');
          mic.stopRecording();
          mic = null;
          
          // Send close message to server after stopping mic
          if (socket.readyState === WebSocket.OPEN) {
            console.log('Sending close message to server');
            socket.send(JSON.stringify({ type: 'close' }));
          }
        }
      }, 15000);
      
    } catch (error) {
      console.error('Error streaming from microphone:', error);
      if (mic) {
        mic.stopRecording();
        mic = null;
      }
    }
  }
  
  // Set up event handlers
  socket.onopen = () => {
    console.log('WebSocket connection opened, waiting for connected message from server...');
  };
  
  socket.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data.toString());
      
      // Handle different message types from the server
      if (data.type === 'connected') {
        isConnected = true;
        console.log('Connected to AssemblyAI real-time service with session ID:', data.sessionId);
        console.log('Max session duration:', data.maxDurationMs, 'ms');
        
        // Start streaming audio data once connected
        streamMicrophone();
      } 
      else if (data.message_type === 'FinalTranscript') {
        console.log('Final transcript:', data.text);
        transcribedText += data.text + ' ';
      } 
      else if (data.message_type === 'PartialTranscript') {
        console.log('Partial transcript:', data.text);
      }
      else if (data.type === 'error') {
        console.error('Error from server:', data.message, data.details || '');
      }
      else if (data.type === 'timeout') {
        console.log('Session timeout:', data.message);
      }
      else if (data.type === 'service_disconnected') {
        console.log('Service disconnected - Code:', data.code, 'Reason:', data.reason);
      }
      else {
        console.log('Received message:', data);
      }
    } catch (error) {
      console.error('Error parsing message:', error);
      console.log('Raw message:', event.data.toString().substring(0, 200));
    }
  };
  
  socket.onerror = (event) => {
    console.error('WebSocket error:', event);
    // Try to get more error information
    console.error('Error details:', JSON.stringify({
      message: event.message,
      type: event.type,
      error: event.error,
      target: {
        url: event.target.url,
        readyState: event.target.readyState,
        protocol: event.target.protocol
      }
    }));
    
    if (mic) {
      mic.stopRecording();
      mic = null;
    }
  };
  
  socket.onclose = (event) => {
    isConnected = false;
    console.log('Connection closed - Code:', event.code, 'Reason:', event.reason);
    console.log('WebSocket close event details:', {
      wasClean: event.wasClean,
      code: event.code,
      reason: event.reason || 'No reason provided'
    });
    
    if (mic) {
      mic.stopRecording();
      mic = null;
    }
    
    console.log('\nFinal Transcription Result:');
    console.log(transcribedText.trim());
  };
  
  // Return a promise that resolves when the test completes
  return new Promise((resolve) => {
    // Allow 20 seconds for the whole test
    setTimeout(() => {
      if (socket.readyState === WebSocket.OPEN) {
        console.log('Test timeout reached, closing connection...');
        socket.close();
      }
      
      if (mic) {
        mic.stopRecording();
        mic = null;
      }
      
      resolve(transcribedText.trim());
    }, 20000);
  });
}

// Added function to directly test the token generation
async function testAssemblyAITokenGeneration() {
  try {
    console.log('Testing direct AssemblyAI token generation');
    
    const token = await getAssemblyAIToken();
    if (token) {
      console.log('Token generation successful! Token starts with:', token.substring(0, 10) + '...');
      return token;
    } else {
      console.error('Failed to generate token');
      return null;
    }
  } catch (error) {
    console.error('Error testing token generation:', error);
    return null;
  }
}

// Function to get an AssemblyAI token
async function getAssemblyAIToken(): Promise<string | null> {
  try {
    console.log('Getting AssemblyAI token from our API');
    
    // Use the URL from function
    const response = await fetch('https://nffixzoqnqxpcqpcxpps.supabase.co/functions/v1/transcribe/generate-token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${VOICEHYPE_API_KEY}`
      },
      body: JSON.stringify({
        service: 'assemblyai'
      })
    });

    console.log('Token endpoint response status:', response.status, response.statusText);
    
    const responseText = await response.text();
    
    if (!response.ok) {
      console.error(`Error generating token: ${response.status} ${response.statusText}`, responseText);
      return null;
    }

    // Parse response
    interface TokenResponse {
      data: {
        success: boolean;
        token: string;
        expiration: number;
      };
    }

    try {
      const data = JSON.parse(responseText) as TokenResponse;
      console.log(`Token obtained successfully (expiration: ${data.data.expiration}s)`);
      return data.data.token;
    } catch (parseError) {
      console.error('Failed to parse token response as JSON:', parseError);
      console.error('Raw response:', responseText);
      return null;
    }
  } catch (error) {
    console.error('Error getting AssemblyAI token:', error);
    return null;
  }
}

// Main function to run the tests
async function runTests() {
  try {
    console.log('\n=== First Testing AssemblyAI Token Generation ===\n');
    await testAssemblyAITokenGeneration();
    
    if (process.argv.includes('--mic')) {
      console.log('\n=== Testing AssemblyAI Real-time Transcription with Microphone ===\n');
      console.log('This test will use your microphone to capture audio for 15 seconds.');
      console.log('Please speak clearly during the test.\n');
      
      // Wait for user to be ready
      console.log('Press Enter to start the microphone test...');
      await new Promise(resolve => {
        process.stdin.once('data', () => {
          resolve(null);
        });
      });
      
      const micResult = await testAssemblyAIRealtimeWithMicrophone();
      console.log('\n=== Microphone Test Completed ===');
      console.log('Transcription Result:', micResult);
    } else {
      console.log('\n=== Testing AssemblyAI Real-time Transcription with File ===\n');
      const fileResult = await testAssemblyAIRealtimeWithFile();
      console.log('\n=== File Test Completed ===');
      console.log('Transcription Result:', fileResult);
      
      console.log('\nTo test with your microphone, run: npm run test:assemblyai-realtime -- --mic');
    }
  } catch (error) {
    console.error('Error running tests:', error);
  }
}

// Execute the tests
runTests();
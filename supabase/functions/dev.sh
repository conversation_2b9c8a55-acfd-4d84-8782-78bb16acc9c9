#!/usr/bin/env bash

# Function to display help message
show_help() {
  echo "Usage: $0 [options] <function_name>"
  echo ""
  echo "Options:"
  echo "  -h, --help            Show this help message"
  echo "  -l, --list            List all available functions"
  echo "  -a, --all             Serve all functions"
  echo "  -w, --watch           Watch for changes (default: false)"
  echo ""
  echo "Examples:"
  echo "  $0 create-paddle-checkout     # Run a specific function"
  echo "  $0 -w create-paddle-checkout  # Run a function and watch for changes"
  echo "  $0 -l                         # List all available functions"
  echo "  $0 -a                         # Run all functions"
  echo ""
}

# Default values
WATCH=false
LIST=false
ALL=false
FUNCTION_NAME=""

# Parse arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    -h|--help)
      show_help
      exit 0
      ;;
    -l|--list)
      LIST=true
      shift
      ;;
    -a|--all)
      ALL=true
      shift
      ;;
    -w|--watch)
      WATCH=true
      shift
      ;;
    *)
      FUNCTION_NAME=$1
      shift
      ;;
  esac
done

# Make sure we're in the right directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
cd "$SCRIPT_DIR" || { echo "Failed to change to script directory"; exit 1; }

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
  if [ -f .env.example ]; then
    cp .env.example .env
    echo "Created .env file from .env.example. Please update the values."
  else
    echo "WARNING: No .env or .env.example file found. You may need to set up your environment variables."
  fi
fi

# List available functions
if [ "$LIST" = true ]; then
  echo "Available functions:"
  find . -maxdepth 1 -type d -not -path "./\.*" -not -path "." | sort | sed 's|^./||'
  exit 0
fi

# Check if Deno is installed
if ! command -v deno &> /dev/null; then
  echo "Error: Deno is not installed. Please install it first."
  echo "Visit https://deno.land/#installation for installation instructions."
  exit 1
fi

# Start the function(s)
WATCH_FLAG=""
if [ "$WATCH" = true ]; then
  WATCH_FLAG="--watch"
fi

if [ "$ALL" = true ]; then
  echo "Starting all functions in development mode..."
  deno run $WATCH_FLAG --allow-net --allow-env --allow-read --allow-write --allow-run https://deno.land/std@0.168.0/http/file_server.ts
else
  if [ -z "$FUNCTION_NAME" ]; then
    echo "Error: No function name provided."
    show_help
    exit 1
  fi

  if [ ! -d "$FUNCTION_NAME" ]; then
    echo "Error: Function '$FUNCTION_NAME' not found."
    echo "Available functions:"
    find . -maxdepth 1 -type d -not -path "./\.*" -not -path "." | sort | sed 's|^./||'
    exit 1
  fi

  echo "Starting function '$FUNCTION_NAME' in development mode..."
  cd "$FUNCTION_NAME" || { echo "Failed to change to function directory"; exit 1; }
  deno run $WATCH_FLAG --allow-net --allow-env --allow-read --allow-write --allow-run --watch index.ts
fi

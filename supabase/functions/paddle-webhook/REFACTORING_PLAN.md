# Paddle Webhook Function Refactoring Plan

**Date:** July 4, 2025  
**Current Function Size:** ~1,500 lines  
**Goal:** Modularize the webhook handler into maintainable, single-responsibility modules

## 🎯 Objectives

1. **Improve Maintainability:** Break down the monolithic function into focused modules
2. **Single Responsibility:** Each module handles one specific type of webhook event
3. **Better Testing:** Enable unit testing of individual event handlers
4. **Code Reusability:** Allow handlers to be reused or extended independently
5. **Easier Debugging:** Isolate issues to specific event types

## 📁 Proposed File Structure

```
supabase/functions/paddle-webhook/
├── index.ts                           # Main entry point & router
├── types/
│   ├── webhook.types.ts              # Type definitions
│   └── upgrade.types.ts              # Upgrade-specific types
├── handlers/
│   ├── subscription/
│   │   ├── subscription-router.ts    # Subscription event router
│   │   ├── created.ts               # subscription.created
│   │   ├── updated.ts               # subscription.updated (renewal/upgrade detection)
│   │   ├── canceled.ts              # subscription.canceled
│   │   ├── paused.ts                # subscription.paused
│   │   ├── resumed.ts               # subscription.resumed
│   │   ├── past-due.ts              # subscription.past_due
│   │   └── activated.ts             # subscription.activated
│   ├── transaction/
│   │   ├── transaction-router.ts    # Transaction event router
│   │   ├── completed.ts             # transaction.completed
│   │   ├── updated.ts               # transaction.updated
│   │   ├── payment-failed.ts        # transaction.payment_failed
│   │   └── past-due.ts              # transaction.past_due
│   └── customer/
│       └── events.ts                # customer.created, customer.updated
├── services/
│   ├── subscription-analyzer.ts     # Logic for detecting renewal vs upgrade vs recovery
│   ├── credit-processor.ts          # Credit transaction processing
│   ├── upgrade-handler.ts           # Subscription upgrade processing
│   └── database.service.ts          # Database operation abstractions
└── utils/
    ├── signature-verifier.ts        # Paddle signature verification
    ├── logger.ts                   # Structured logging utilities
    └── response-builder.ts         # Response formatting utilities
```

## 🔄 Event Routing Strategy

### Main Router (`index.ts`)
```typescript
switch (event.event_type) {
  // Subscription Events
  case 'subscription.created':
  case 'subscription.updated':
  case 'subscription.canceled':
  case 'subscription.resumed':
  case 'subscription.paused':
  case 'subscription.past_due':
  case 'subscription.activated':
    return await subscriptionRouter(supabase, event)
    
  // Transaction Events  
  case 'transaction.completed':
  case 'transaction.updated':
  case 'transaction.payment_failed':
  case 'transaction.past_due':
    return await transactionRouter(supabase, event)
    
  // Customer Events
  case 'customer.created':
  case 'customer.updated':
    return await handleCustomerEvent(supabase, event)
    
  default:
    return handleUnknownEvent(event)
}
```

### Subscription Router (`handlers/subscription/subscription-router.ts`)
```typescript
export async function subscriptionRouter(supabase: any, event: PaddleWebhookEvent): Promise<boolean> {
  switch (event.event_type) {
    case 'subscription.created':
      return await handleSubscriptionCreated(supabase, event)
      
    case 'subscription.updated':
      // Complex logic - detect renewal vs upgrade vs recovery
      return await handleSubscriptionUpdated(supabase, event)
      
    case 'subscription.canceled':
      return await handleSubscriptionCanceled(supabase, event)
      
    case 'subscription.paused':
      return await handleSubscriptionPaused(supabase, event)
      
    case 'subscription.resumed':
      return await handleSubscriptionResumed(supabase, event)
      
    case 'subscription.past_due':
      return await handleSubscriptionPastDue(supabase, event)
      
    case 'subscription.activated':
      return await handleSubscriptionActivated(supabase, event)
      
    default:
      throw new Error(`Unknown subscription event: ${event.event_type}`)
  }
}
```

## 📋 Module Breakdown

### 1. Core Infrastructure Modules

#### `types/webhook.types.ts`
- **Purpose:** Centralized type definitions
- **Contents:** 
  - `PaddleWebhookEvent` interface
  - `SubscriptionEventData` interface
  - `TransactionEventData` interface
  - Event-specific type guards
- **Size Estimate:** ~100 lines

#### `utils/signature-verifier.ts`
- **Purpose:** Paddle signature verification logic
- **Contents:** `verifyPaddleSignature()` function
- **Size Estimate:** ~50 lines

#### `utils/logger.ts`
- **Purpose:** Structured logging for webhook events
- **Contents:** Event-specific logging functions
- **Size Estimate:** ~80 lines

### 2. Event Handler Modules

#### `handlers/subscription/updated.ts` (Most Complex)
- **Purpose:** Handle subscription.updated events with intelligent routing
- **Key Responsibilities:**
  - Detect payment recovery vs renewal vs upgrade
  - Route to appropriate sub-handlers
  - Database state comparison logic
- **Sub-functions:**
  - `detectEventType()` - Analyze what type of update this is
  - `handlePaymentRecovery()` - past_due → active without quota reset
  - `handleSubscriptionRenewal()` - New billing period with quota reset
  - `handlePlanUpgrade()` - Plan change scenarios
- **Size Estimate:** ~300 lines

#### `handlers/subscription/created.ts`
- **Purpose:** Handle new subscription creation
- **Key Responsibilities:**
  - Call `handle_subscription_transaction` database function
  - Update billing information from Paddle
  - Log subscription creation transaction
- **Size Estimate:** ~150 lines

#### `handlers/subscription/canceled.ts`
- **Purpose:** Handle subscription cancellation cleanup
- **Key Responsibilities:**
  - Call `cleanup_cancelled_subscription` database function
  - Log cancellation transaction
- **Size Estimate:** ~100 lines

### 3. Service Layer Modules

#### `services/subscription-analyzer.ts`
- **Purpose:** Complex subscription event analysis logic
- **Key Functions:**
  - `analyzeSubscriptionUpdate()` - Determine update type
  - `isPaymentRecovery()` - Detect payment recovery scenarios
  - `isSubscriptionRenewal()` - Detect renewal scenarios
  - `isPlanUpgrade()` - Detect upgrade scenarios
- **Size Estimate:** ~200 lines

#### `services/upgrade-handler.ts`
- **Purpose:** Handle subscription upgrade scenarios
- **Key Functions:**
  - `processQuotaMergeUpgrade()` - Handle immediate upgrades with quota merging
  - `processRegularUpgrade()` - Handle next-billing-cycle upgrades
  - `logUpgradeTransaction()` - Transaction logging for upgrades
- **Size Estimate:** ~250 lines

#### `services/credit-processor.ts`
- **Purpose:** Handle credit purchase transactions
- **Key Functions:**
  - `processCreditTransaction()` - Main credit processing logic
  - `calculateCreditAmount()` - Credit calculation logic
  - `createCustomerRecord()` - Customer record management
- **Size Estimate:** ~150 lines

## 🔧 Implementation Strategy

### Phase 1: Infrastructure Setup
1. ✅ Create new file structure
2. ✅ Extract type definitions to `types/webhook.types.ts`
3. ✅ Extract utility functions (`signature-verifier.ts`, `logger.ts`)
4. ✅ Create basic router structure in `index.ts`

### Phase 2: Extract Simple Event Handlers
1. ✅ Extract customer event handlers
2. ✅ Extract simple subscription handlers (paused, resumed, canceled)
3. ✅ Extract transaction handlers (payment_failed, past_due)
4. ✅ Test each extracted handler

### Phase 3: Extract Complex Logic
1. ✅ Extract subscription creation handler
2. ✅ Extract credit transaction processing
3. ✅ Create subscription analyzer service
4. ✅ Test complex handlers

### Phase 4: Extract Most Complex Handler
1. ✅ Extract subscription updated handler
2. ✅ Implement intelligent event type detection
3. ✅ Split renewal/upgrade/recovery logic
4. ✅ Comprehensive testing

### Phase 5: Optimization & Testing
1. ✅ Add comprehensive error handling
2. ✅ Add unit tests for each module
3. ✅ Performance optimization
4. ✅ Documentation updates

## 🧪 Testing Strategy

### Unit Tests Structure
```
tests/
├── handlers/
│   ├── subscription/
│   │   ├── created.test.ts
│   │   ├── updated.test.ts
│   │   └── canceled.test.ts
│   └── transaction/
│       └── completed.test.ts
├── services/
│   ├── subscription-analyzer.test.ts
│   └── upgrade-handler.test.ts
└── utils/
    └── signature-verifier.test.ts
```

### Test Scenarios
- **Payment Recovery:** past_due → active without new billing period
- **Automatic Renewal:** New billing period with quota reset
- **Plan Upgrades:** With and without quota merging
- **Error Handling:** Database failures, missing data
- **Edge Cases:** Invalid webhook data, missing custom_data

## 📊 Benefits of This Approach

### Maintainability
- ✅ Each file has a single, clear responsibility
- ✅ Easier to locate and fix bugs in specific scenarios
- ✅ Simpler code reviews for changes to specific event types

### Testability
- ✅ Unit tests can focus on specific event handlers
- ✅ Mock dependencies more easily
- ✅ Test edge cases for individual scenarios

### Scalability
- ✅ Easy to add new event types
- ✅ Can optimize individual handlers independently
- ✅ Better error isolation

### Developer Experience
- ✅ Faster development cycles
- ✅ Less cognitive load when working on specific features
- ✅ Clearer code documentation and comments

## 🚨 Migration Considerations

### Backward Compatibility
- ✅ Maintain exact same webhook endpoint URL
- ✅ Preserve all existing functionality
- ✅ No changes to database schemas or function calls

### Risk Mitigation
- ✅ Incremental migration with extensive testing
- ✅ Rollback plan if issues arise
- ✅ Comprehensive integration testing before deployment

### Performance Impact
- ✅ Minimal - modern JavaScript engines optimize module imports
- ✅ Potential improvement due to better code organization
- ✅ Monitoring to ensure no regression

## 📝 Next Steps

1. **Get Approval:** Review and approve this refactoring plan
2. **Create Feature Branch:** `git checkout -b refactor/paddle-webhook-modularization`
3. **Start Implementation:** Begin with Phase 1 (Infrastructure Setup)
4. **Iterative Testing:** Test each phase thoroughly before proceeding
5. **Documentation:** Update README and API documentation
6. **Deployment:** Deploy to staging for integration testing

## 💭 Additional Considerations

### Custom Data Dependency Documentation
Each handler should explicitly document whether it depends on custom data or uses database lookups:

```typescript
/**
 * Handle subscription paused events
 * 
 * @note Paddle automatic events do NOT contain custom_data
 * @note This handler uses database lookups for all required data
 */
export async function handleSubscriptionPaused(supabase: any, event: PaddleWebhookEvent): Promise<boolean> {
  // Implementation...
}
```

### Error Handling Strategy
- Consistent error handling across all modules
- Proper logging for debugging
- Graceful degradation for non-critical failures
- Clear distinction between retryable and non-retryable errors

This refactoring will transform the webhook handler from a monolithic function into a well-organized, maintainable system that's easier to work with, test, and extend.

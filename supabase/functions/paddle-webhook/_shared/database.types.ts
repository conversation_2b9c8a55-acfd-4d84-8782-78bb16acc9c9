export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      user_subscriptions: {
        Row: {
          id: string
          user_id: string
          paddle_subscription_id: string
          paddle_customer_id: string
          paddle_price_id: string
          status: string
          current_period_start: string
          current_period_end: string
          trial_end: string | null
          cancel_at_period_end: boolean
          canceled_at: string | null
          created_at: string
          updated_at: string
          metadata: Json | null
        }
        Insert: {
          id?: string
          user_id: string
          paddle_subscription_id: string
          paddle_customer_id: string
          paddle_price_id: string
          status: string
          current_period_start: string
          current_period_end: string
          trial_end?: string | null
          cancel_at_period_end?: boolean
          canceled_at?: string | null
          created_at?: string
          updated_at?: string
          metadata?: Json | null
        }
        Update: {
          id?: string
          user_id?: string
          paddle_subscription_id?: string
          paddle_customer_id?: string
          paddle_price_id?: string
          status?: string
          current_period_start?: string
          current_period_end?: string
          trial_end?: string | null
          cancel_at_period_end?: boolean
          canceled_at?: string | null
          created_at?: string
          updated_at?: string
          metadata?: Json | null
        }
      }
      user_quotas: {
        Row: {
          id: string
          user_id: string
          subscription_id: string
          voice_generation_seconds: number
          voice_cloning_minutes: number
          speech_enhancement_minutes: number
          custom_voice_training_hours: number
          reset_date: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          subscription_id: string
          voice_generation_seconds?: number
          voice_cloning_minutes?: number
          speech_enhancement_minutes?: number
          custom_voice_training_hours?: number
          reset_date: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          subscription_id?: string
          voice_generation_seconds?: number
          voice_cloning_minutes?: number
          speech_enhancement_minutes?: number
          custom_voice_training_hours?: number
          reset_date?: string
          created_at?: string
          updated_at?: string
        }
      }
      subscription_transactions: {
        Row: {
          id: string
          user_id: string
          subscription_id: string
          transaction_type: string
          paddle_subscription_id: string
          amount: number
          currency: string
          status: string
          metadata: Json | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          subscription_id: string
          transaction_type: string
          paddle_subscription_id: string
          amount: number
          currency: string
          status: string
          metadata?: Json | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          subscription_id?: string
          transaction_type?: string
          paddle_subscription_id?: string
          amount?: number
          currency?: string
          status?: string
          metadata?: Json | null
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      merge_remaining_quotas: {
        Args: {
          p_user_id: string
          p_old_paddle_subscription_id: string
          p_new_paddle_subscription_id: string
        }
        Returns: {
          success: boolean
          message: string
          user_id: string
          remaining_quotas: Json
          new_totals: Json
          merged_at: string
        }
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

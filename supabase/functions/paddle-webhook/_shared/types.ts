// Shared types for Paddle webhook and subscription upgrade system

export enum UpgradeFlow {
  UPDATE_NEXT_BILLING = 'update_next_billing',
  UPGRADE_NOW_MERGE_QUOTAS = 'upgrade_now_merge_quotas'
}

export interface SubscriptionUpgradeRequest {
  userId: string
  currentSubscriptionId: string
  targetPriceId: string
  upgradeFlow: UpgradeFlow
  metadata?: Record<string, any>
}

export interface QuotaMergeResult {
  success: boolean
  message: string
  merged_quotas?: {
    voice_generation_seconds: number
    voice_cloning_minutes: number
    speech_enhancement_minutes: number
    custom_voice_training_hours: number
  }
}

export interface UpgradePreview {
  current_plan: {
    name: string
    paddle_price_id: string
    quotas: Record<string, number>
  }
  target_plan: {
    name: string
    paddle_price_id: string
    quotas: Record<string, number>
  }
  remaining_quotas: Record<string, number>
  merged_quotas: Record<string, number>
  savings?: {
    amount: number
    currency: string
    description: string
  }
}

export interface UpgradeMetadata {
  upgrade_flow: UpgradeFlow
  old_subscription_id: string
  new_subscription_id: string
  merge_quotas?: boolean
  scheduled_change?: boolean
  user_id: string
  timestamp: string
  target_plan: string
  transcription_minutes: number
  tokens: number
}

// Paddle webhook event types we handle
export interface PaddleSubscriptionUpdated {
  id: string
  status: string
  customer_id: string
  address_id: string
  business_id?: string
  currency_code: string
  created_at: string
  updated_at: string
  started_at?: string
  first_billed_at?: string
  next_billed_at?: string
  paused_at?: string
  canceled_at?: string
  discount?: any
  collection_mode: string
  billing_details: any
  billing_cycle: {
    frequency: number
    interval: string
  }
  recurring_transaction_details?: any
  scheduled_change?: any
  items: Array<{
    price: {
      id: string
      name?: string
      description?: string
      product_id: string
      billing_cycle?: {
        frequency: number
        interval: string
      }
      trial_period?: any
      tax_mode: string
      unit_price: {
        amount: string
        currency_code: string
      }
      unit_price_overrides?: any[]
      quantity: {
        minimum: number
        maximum: number
      }
      status: string
      custom_data?: any
      import_meta?: any
      created_at: string
      updated_at: string
    }
    price_id: string
    quantity: number
    recurring: boolean
    created_at: string
    updated_at: string
  }>
  custom_data?: any
  import_meta?: any
  management_urls?: any
}

export interface PaddleSubscriptionCreated extends PaddleSubscriptionUpdated {
  // Same structure as updated, but represents a new subscription
}

// Database types for subscription operations
export interface SubscriptionRecord {
  id: string
  user_id: string
  paddle_subscription_id: string
  paddle_customer_id: string
  paddle_price_id: string
  status: string
  current_period_start: string
  current_period_end: string
  trial_end?: string
  cancel_at_period_end: boolean
  canceled_at?: string
  created_at: string
  updated_at: string
  metadata?: Record<string, any>
}

export interface UserQuotaRecord {
  id: string
  user_id: string
  subscription_id: string
  voice_generation_seconds: number
  voice_cloning_minutes: number
  speech_enhancement_minutes: number
  custom_voice_training_hours: number
  reset_date: string
  created_at: string
  updated_at: string
}

export interface SubscriptionTransactionRecord {
  id: string
  user_id: string
  subscription_id: string
  transaction_type: 'creation' | 'upgrade' | 'downgrade' | 'cancellation' | 'renewal'
  paddle_subscription_id: string
  amount: number
  currency: string
  status: string
  metadata?: Record<string, any>
  created_at: string
}

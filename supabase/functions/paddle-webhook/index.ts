import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { SubscriptionUpgradeHandler } from './subscription-upgrade.ts'
import { UpgradeFlow, UpgradeMetadata } from './_shared/types.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, paddle-signature',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

interface PaddleWebhookEvent {
  event_id: string
  event_type: string
  occurred_at: string
  data: {
    id: string
    status?: string
    customer_id?: string
    items?: Array<{
      product_id: string
      quantity: number
    }>
    details?: {
      totals?: {
        total: string
        currency_code: string
      }
    }
    receipt_url?: string
    [key: string]: any
  }
}

// Verify Paddle webhook signature
async function verifyPaddleSignature(
  body: string,
  signature: string,
  webhookSecret: string
): Promise<boolean> {
  try {
    // Extract timestamp and signature from header
    const parts = signature.split(';')
    let timestamp = ''
    let signatures: string[] = []
    
    for (const part of parts) {
      const [key, value] = part.split('=')
      if (key === 'ts') {
        timestamp = value
      } else if (key === 'h1') {
        signatures.push(value)
      }
    }
    
    if (!timestamp || signatures.length === 0) {
      return false
    }
    
    // Create the signed payload
    const signedPayload = `${timestamp};${body}`
    
    // Create HMAC signature
    const encoder = new TextEncoder()
    const key = await crypto.subtle.importKey(
      'raw',
      encoder.encode(webhookSecret),
      { name: 'HMAC', hash: 'SHA-256' },
      false,
      ['sign']
    )
    
    const signature_bytes = await crypto.subtle.sign(
      'HMAC',
      key,
      encoder.encode(signedPayload)
    )
    
    // Convert to hex
    const expectedSignature = Array.from(new Uint8Array(signature_bytes))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('')
    
    // Compare signatures
    return signatures.some(sig => sig === expectedSignature)
  } catch (error) {
    console.error('Error verifying Paddle signature:', error)
    return false
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  if (req.method !== 'POST') {
    return new Response('Method not allowed', { 
      status: 405, 
      headers: corsHeaders 
    })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const body = await req.text()
    const signature = req.headers.get('paddle-signature') || ''
    
    // Get environment-specific webhook secret
    const environment = Deno.env.get('PADDLE_ENVIRONMENT') || 'sandbox'
    const webhookSecret = environment === 'production' 
      ? Deno.env.get('PADDLE_WEBHOOK_SECRET_PRODUCTION') || ''
      : Deno.env.get('PADDLE_WEBHOOK_SECRET_SANDBOX') || ''

    console.log('Received Paddle webhook:', {
      signature: signature ? 'present' : 'missing',
      bodyLength: body.length,
      environment
    })

    // Verify webhook signature in production
    if (environment === 'production') {
      const isValid = await verifyPaddleSignature(body, signature, webhookSecret)
      if (!isValid) {
        console.error('Invalid Paddle webhook signature')
        return new Response('Invalid signature', { 
          status: 401, 
          headers: corsHeaders 
        })
      }
    }

    const event: PaddleWebhookEvent = JSON.parse(body)
    
    console.log('Processing Paddle event:', {
      event_id: event.event_id,
      event_type: event.event_type,
      data_id: event.data?.id
    })

    // Note: Webhook logging removed - paddle_webhooks table no longer used
    console.log('✅ Processing webhook event (logging skipped)')

    // Process different event types
    let processed = false
    let processingError = null

    try {
      switch (event.event_type) {
        case 'transaction.completed':
          processed = await handleTransactionCompleted(supabase, event)
          break
          
        case 'transaction.updated':
          processed = await handleTransactionUpdated(supabase, event)
          break
          
        case 'transaction.payment_failed':
          processed = await handleTransactionPaymentFailed(supabase, event)
          break
          
        case 'transaction.past_due':
          processed = await handleTransactionPastDue(supabase, event)
          break
          
        case 'subscription.created':
          processed = await handleSubscriptionCreated(supabase, event)
          break
          
        case 'subscription.updated':
          processed = await handleSubscriptionUpdated(supabase, event)
          break
          
        case 'subscription.canceled':
          processed = await handleSubscriptionCanceled(supabase, event)
          break
          
        case 'subscription.resumed':
          processed = await handleSubscriptionResumed(supabase, event)
          break
          
        case 'subscription.paused':
          processed = await handleSubscriptionPaused(supabase, event)
          break
          
        case 'subscription.past_due':
          processed = await handleSubscriptionPastDue(supabase, event)
          break
          
        case 'subscription.activated':
          processed = await handleSubscriptionActivated(supabase, event)
          break
          
        case 'customer.created':
        case 'customer.updated':
          processed = await handleCustomerEvent(supabase, event)
          break
          
        default:
          console.log(`Unhandled event type: ${event.event_type}`)
          processed = true // Mark as processed to avoid retries
      }
    } catch (error) {
      console.error('Error processing webhook:', error)
      processingError = error.message
      processed = false
    }

    // Note: Webhook status logging removed - paddle_webhooks table no longer used
    console.log('✅ Webhook processing completed', {
      success: processed,
      event_id: event.event_id,
      event_type: event.event_type,
      error: processingError
    })

    return new Response(
      JSON.stringify({ 
        success: processed,
        event_id: event.event_id,
        event_type: event.event_type
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: processed ? 200 : 500
      }
    )

  } catch (error) {
    console.error('Webhook processing error:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
})

// Handle completed transactions (credit purchases or subscription transactions)
async function handleTransactionCompleted(supabase: any, event: PaddleWebhookEvent): Promise<boolean> {
  const { data } = event
  
  if (!data.customer_id || !data.details?.totals) {
    console.error('Missing required transaction data')
    return false
  }

  const amount = parseFloat(data.details.totals.total)
  const currency = data.details.totals.currency_code
  const productId = data.items?.[0]?.product_id || null
  const customData = data.custom_data || {}
  
  console.log('Processing completed transaction:', {
    transaction_id: data.id,
    customer_id: data.customer_id,
    amount,
    currency,
    product_id: productId,
    custom_data: customData,
    subscription_id: data.subscription_id
  })

  // Check if this is a subscription transaction
  const isSubscriptionTransaction = !!(customData.subscription_plan && data.subscription_id)
  
  console.log('Transaction type analysis:', {
    has_subscription_plan: !!customData.subscription_plan,
    has_subscription_id: !!data.subscription_id,
    is_subscription_transaction: isSubscriptionTransaction,
    transaction_type: isSubscriptionTransaction ? 'subscription' : 'credit_purchase'
  })

  // Apply single responsibility principle - only handle credit purchases in transaction events
  if (isSubscriptionTransaction) {
    console.log('Skipping subscription transaction in transaction.completed - will be handled by subscription events')
    return true
  } else {
    console.log('Processing as credit purchase transaction')
    return await handleCreditTransaction(supabase, event)
  }
}


// Handle credit transactions
async function handleCreditTransaction(supabase: any, event: PaddleWebhookEvent): Promise<boolean> {
  const { data } = event
  
  if (!data.details?.totals) {
    console.error('Missing transaction details for credit transaction')
    return false
  }
  
  const amount = parseFloat(data.details.totals.total)
  const currency = data.details.totals.currency_code
  const productId = data.items?.[0]?.product_id || null
  const customData = data.custom_data || {}

  console.log('Processing credit transaction:', {
    transaction_id: data.id,
    customer_id: data.customer_id,
    amount,
    currency,
    product_id: productId,
    custom_data: customData,
    user_id_from_custom_data: customData.user_id
  })

  // Get user_id from custom_data
  const userId = customData.user_id
  if (!userId) {
    console.error('No user_id found in custom_data')
    return false
  }

  console.log('Processing for user:', userId)

  try {
    // Calculate credit amount from custom_data or amount
    let creditAmount: number
    if (customData.credit_amount) {
      creditAmount = parseFloat(customData.credit_amount)
      console.log('Using credit_amount from custom_data:', creditAmount)
    } else {
      // Amount is in cents, convert to dollars
      creditAmount = amount / 100
      console.log('Calculated credit_amount from amount:', creditAmount)
    }

    // 1. Get user details for customer record
    const { data: userProfile, error: userError } = await supabase
      .from('profiles')
      .select('email, full_name')
      .eq('id', userId)
      .single()

    if (userError || !userProfile) {
      console.error('User not found:', userError)
      return false
    }

    console.log('Found user profile:', userProfile.email)

    // 2. Create/update paddle customer record
    const { error: customerError } = await supabase
      .from('paddle_customers')
      .upsert({
        user_id: userId,
        paddle_customer_id: data.customer_id,
        email: userProfile.email,
        name: userProfile.full_name || customData.customer_name || null,
        metadata: data
      }, {
        onConflict: 'paddle_customer_id'
      })

    if (customerError) {
      console.error('Failed to create/update customer:', customerError)
      return false
    }

    console.log('✅ Customer record created/updated')

    // 3. Create transaction record
    const { error: transactionError } = await supabase
      .from('paddle_transactions')
      .upsert({
        user_id: userId,
        paddle_transaction_id: data.id,
        paddle_customer_id: data.customer_id,
        paddle_product_id: productId,
        status: 'completed',
        amount: amount,
        currency: currency,
        credit_amount: creditAmount,
        transaction_type: 'credit_purchase',
        paddle_receipt_url: data.receipt_url || null,
        metadata: data
      }, {
        onConflict: 'paddle_transaction_id'
      })

    if (transactionError) {
      console.error('Failed to create transaction record:', transactionError)
      return false
    }

    console.log('✅ Transaction record created')

    // 4. Add credits to user account
    const { error: creditsError } = await supabase
      .from('credits')
      .insert({
        user_id: userId,
        balance: creditAmount,
        currency: currency,
        expires_at: new Date(Date.now() + (365 + 1) * 24 * 60 * 60 * 1000).toISOString() // 1 year from now
      })

    if (creditsError) {
      console.error('Failed to add credits:', creditsError)
      return false
    }

    console.log('✅ Successfully processed credit transaction:', {
      transaction_id: data.id,
      user_id: userId,
      credit_amount: creditAmount
    })

    return true

  } catch (error) {
    console.error('Unexpected error processing credit transaction:', error)
    return false
  }
}

// Handle transaction updates
async function handleTransactionUpdated(supabase: any, event: PaddleWebhookEvent): Promise<boolean> {
  const { data } = event
  
  console.log('Processing transaction update:', {
    transaction_id: data.id,
    status: data.status
  })

  // Update transaction status in public.paddle_transactions table
  try {
    const { error: updateError } = await supabase
      .from('paddle_transactions')
      .update({
        status: data.status,
        metadata: data,
        updated_at: new Date().toISOString()
      })
      .eq('paddle_transaction_id', data.id)

    if (updateError) {
      console.warn('Transaction status update table not available:', updateError.message)
      // Continue processing even if update fails
    } else {
      console.log('Successfully updated transaction status:', data.id)
    }
  } catch (logError) {
    console.warn('Transaction status update skipped - table may not exist')
  }

  // Check if this is a subscription transaction that just completed
  const customData = data.custom_data || {}
  const isSubscriptionTransaction = !!(customData.subscription_plan && data.subscription_id)

  if (isSubscriptionTransaction && data.status === 'completed') {
    console.log('Skipping subscription transaction in transaction.updated - will be handled by subscription events')
    return true
  }

  // For credit transactions, just update the status (already done above)
  return true
}

// Handle customer events
async function handleCustomerEvent(supabase: any, event: PaddleWebhookEvent): Promise<boolean> {
  const { data } = event
  
  console.log('Processing customer event:', {
    customer_id: data.id,
    email: data.email
  })

  // We'll handle customer creation/updates when processing transactions
  // This is mainly for logging purposes
  return true
}

// Handle subscription creation
async function handleSubscriptionCreated(supabase: any, event: PaddleWebhookEvent): Promise<boolean> {
  const { data } = event
  
  console.log('Processing subscription created:', {
    subscription_id: data.id,
    customer_id: data.customer_id,
    status: data.status
  })

  // Extract custom data to get user_id and plan_id
  const customData = data.custom_data || {}
  const userId = customData.user_id
  const subscriptionPlan = customData.subscription_plan || customData.plan_id
  
  if (!userId || !subscriptionPlan) {
    console.error('Missing user_id or subscription_plan in subscription custom_data')
    return false
  }

  // Use the centralized function to handle subscription creation
  const { data: result, error } = await supabase.rpc(
    'handle_subscription_transaction',
    {
      p_user_id: userId,
      p_paddle_subscription_id: data.id,
      p_paddle_customer_id: data.customer_id,
      p_subscription_plan: subscriptionPlan,
      p_transaction_data: data,
      p_upgrade_flow: null // New subscription, not an upgrade
    }
  )

  if (error) {
    console.error('Error calling handle_subscription_transaction:', error)
    return false
  }

  if (!result?.success) {
    console.error('Subscription creation failed:', result)
    return false
  }

  console.log('Successfully created subscription:', {
    user_id: result.user_id,
    subscription_id: result.subscription_id,
    plan: result.plan,
    quotas: result.quotas
  })

  // Update user subscription with billing information from Paddle webhook
  try {
    console.log('🔄 Updating subscription with billing information from Paddle:', {
      subscription_id: data.id,
      next_billed_at: data.next_billed_at || 'NOT PROVIDED',
      current_billing_period: data.current_billing_period || 'NOT PROVIDED',
      status: data.status || 'NOT PROVIDED',
      scheduled_change: data.scheduled_change || 'NOT PROVIDED'
    })

    const subscriptionUpdate: any = {
      status: data.status || 'active',
      updated_at: new Date().toISOString()
    }

    // Add billing period information if available
    if (data.current_billing_period?.starts_at) {
      subscriptionUpdate.current_period_start = data.current_billing_period.starts_at
      console.log('📅 Setting current_period_start:', data.current_billing_period.starts_at)
    }
    if (data.current_billing_period?.ends_at) {
      subscriptionUpdate.current_period_end = data.current_billing_period.ends_at
      console.log('📅 Setting current_period_end:', data.current_billing_period.ends_at)
    }
    if (data.next_billed_at) {
      subscriptionUpdate.next_billed_at = data.next_billed_at
      console.log('📅 Setting next_billed_at:', data.next_billed_at)
    } else {
      console.warn('⚠️ No next_billed_at provided by Paddle in subscription.created event')
    }

    console.log('📝 Final subscription update object:', subscriptionUpdate)

    const { error: updateError, data: updateResult } = await supabase
      .from('user_subscriptions')
      .update(subscriptionUpdate)
      .eq('paddle_subscription_id', data.id)
      .select()

    if (updateError) {
      console.error('❌ Failed to update subscription billing info:', updateError)
    } else {
      console.log('✅ Successfully updated subscription with billing information:', updateResult)
    }
  } catch (updateError) {
    console.error('💥 Exception updating subscription billing info:', updateError)
  }

  // Log subscription creation transaction in paddle_transactions table for history tracking
  try {
    console.log('Logging subscription creation transaction to paddle_transactions')
    
    const { error: transactionError } = await supabase
      .from('paddle_transactions')
      .insert({
        user_id: userId,
        paddle_transaction_id: data.id, // Use subscription ID as transaction ID for subscription creations
        paddle_customer_id: data.customer_id,
        paddle_product_id: null, // Subscriptions don't have a single product_id
        status: 'completed',
        amount: 0, // Subscription creation doesn't have an amount - billing happens separately
        currency: 'USD', // Default currency
        credit_amount: null, // Not applicable for subscriptions
        transaction_type: 'subscription',
        paddle_receipt_url: null, // Subscription creation doesn't have a receipt
        metadata: {
          subscription_plan: subscriptionPlan,
          subscription_id: data.id,
          subscription_status: data.status,
          ...data
        }
      })

    if (transactionError) {
      console.warn('Failed to log subscription transaction (non-critical):', transactionError)
      // Don't fail the webhook for this - it's just for history tracking
    } else {
      console.log('✅ Subscription creation transaction logged successfully')
    }
  } catch (logError) {
    console.warn('Non-critical error logging subscription transaction:', logError)
    // Continue processing - this is just for history tracking
  }

  return true
}

// Handle subscription updates
async function handleSubscriptionUpdated(supabase: any, event: PaddleWebhookEvent): Promise<boolean> {
  const { data } = event
  
  console.log('Processing subscription updated:', {
    subscription_id: data.id,
    status: data.status,
    current_period_start: data.current_billing_period?.starts_at,
    current_period_end: data.current_billing_period?.ends_at,
    next_billed_at: data.next_billed_at,
    previously_billed_at: data.previously_billed_at
  })

  try {
    // Get current subscription state to detect changes
    const { data: currentSub, error: fetchError } = await supabase
      .from('user_subscriptions')
      .select('status, current_period_start, current_period_end')
      .eq('paddle_subscription_id', data.id)
      .single()

    if (fetchError) {
      console.warn('Could not fetch current subscription state:', fetchError)
    }

    // Detect if this is a payment recovery (past_due -> active)
    const isPaymentRecovery = currentSub && (
      currentSub.status === 'past_due' && data.status === 'active'
    )

    // Detect if this is a subscription renewal (new billing period, regardless of status)
    const isSubscriptionRenewal = !!(
      data.current_billing_period?.starts_at && 
      currentSub?.current_period_start &&
      new Date(data.current_billing_period.starts_at) > new Date(currentSub.current_period_start) &&
      // No upgrade metadata present (upgrades have explicit metadata)
      !data.custom_data?.upgrade_metadata
    )

    console.log('Subscription update analysis:', {
      current_status: currentSub?.status,
      new_status: data.status,
      current_period_start: currentSub?.current_period_start,
      new_period_start: data.current_billing_period?.starts_at,
      is_payment_recovery: isPaymentRecovery,
      is_subscription_renewal: isSubscriptionRenewal,
      has_new_billing_period: !!data.current_billing_period?.starts_at,
      has_upgrade_metadata: !!data.custom_data?.upgrade_metadata,
      subscription_plan_in_data: data.custom_data?.subscription_plan,
      detection_logic: {
        recovery_criteria: 'past_due -> active',
        renewal_criteria: 'new billing period > current period start'
      }
    })

    // CRITICAL: Check if this is a subscription upgrade/downgrade with new subscription ID
    // When Paddle creates a new subscription during upgrades, we need to replace the old ID
    if (data.custom_data?.upgrade_metadata) {
      const upgradeMetadata = data.custom_data.upgrade_metadata
      const oldSubscriptionId = upgradeMetadata.old_subscription_id
      
      console.log('🔄 Detected subscription replacement during upgrade:', {
        old_subscription_id: oldSubscriptionId,
        new_subscription_id: data.id,
        user_id: upgradeMetadata.user_id,
        target_plan: upgradeMetadata.target_plan,
        upgrade_flow: upgradeMetadata.upgrade_flow
      })

      // Update user_subscriptions table to point to new subscription ID
      const { error: updateError } = await supabase
        .from('user_subscriptions')
        .update({
          paddle_subscription_id: data.id, // New subscription ID from Paddle
          status: data.status || 'active',
          current_period_start: data.current_billing_period?.starts_at,
          current_period_end: data.current_billing_period?.ends_at,
          next_billed_at: data.next_billed_at,
          updated_at: new Date().toISOString()
        })
        .eq('paddle_subscription_id', oldSubscriptionId) // Find by old subscription ID

      if (updateError) {
        console.error('❌ Failed to update subscription with new Paddle ID:', updateError)
        return false
      }

      console.log('✅ Successfully updated subscription with new Paddle ID:', {
        old_id: oldSubscriptionId,
        new_id: data.id,
        user_id: upgradeMetadata.user_id
      })

      // Continue with upgrade processing based on upgrade flow
      if (upgradeMetadata.upgrade_flow === 'upgrade_now_merge_quotas') {
        console.log('🔄 Processing immediate upgrade with quota merging')
        // Process quota merging logic - this will be handled below in existing code
      } else if (upgradeMetadata.upgrade_flow === 'update_next_billing') {
        console.log('🔄 Processing scheduled upgrade for next billing period')
        // Process scheduled upgrade - this will be handled below in existing code
      }

      // Note: The rest of the upgrade logic below will now use the correct subscription ID
      // because we've updated the database to point to the new subscription
    }

    // Update user subscription with new period dates and status
    // Note: If this was an upgrade, we already updated with the new subscription ID above
    const userSubUpdate: any = {
      status: data.status || 'active',
      current_period_start: data.current_billing_period?.starts_at,
      current_period_end: data.current_billing_period?.ends_at,
      next_billed_at: data.next_billed_at,
      updated_at: new Date().toISOString()
    }

    // Only update if we haven't already updated for an upgrade
    if (!data.custom_data?.upgrade_metadata) {
      const { error: userSubError } = await supabase
        .from('user_subscriptions')
        .update(userSubUpdate)
        .eq('paddle_subscription_id', data.id)

      if (userSubError) {
        console.error('Error updating user subscription:', userSubError)
        return false
      }
    } else {
      console.log('⏭️ Skipping subscription update - already handled during upgrade ID replacement')
    }

    // Check for upgrade metadata and handle quota merging for subscription updates
    const upgradeMetadata: UpgradeMetadata | undefined = data.custom_data?.upgrade_metadata
    if (upgradeMetadata?.upgrade_flow === UpgradeFlow.UPGRADE_NOW_MERGE_QUOTAS) {
      console.log('🔄 Detected upgrade with quota merging in subscription.updated:', upgradeMetadata)
      
      try {
        const upgradeHandler = new SubscriptionUpgradeHandler()
        
        // Extract target plan data from custom_data (the plan the user is upgrading TO)
        const targetPlanData = {
          name: upgradeMetadata.target_plan,
          transcription_minutes: upgradeMetadata.transcription_minutes || data.custom_data?.transcription_minutes || 0,
          tokens: upgradeMetadata.tokens || data.custom_data?.tokens || 0
        }
        
        console.log('📋 Target plan data from custom_data:', targetPlanData)
        
        // Process the upgrade with quota merging
        const upgradeResult = await upgradeHandler.processUpgradeWithQuotaMerge(
          upgradeMetadata.user_id,
          upgradeMetadata.old_subscription_id,
          data.id, // subscription ID from the updated subscription
          targetPlanData // Pass the target plan data from custom_data
        )

        if (upgradeResult.success) {
          console.log('✅ Subscription upgrade with quota merging completed successfully:', upgradeResult)
          
          // Log the upgrade transaction
          await upgradeHandler.logUpgradeTransaction(
            upgradeMetadata.user_id,
            upgradeMetadata.old_subscription_id,
            data.id,
            UpgradeFlow.UPGRADE_NOW_MERGE_QUOTAS,
            { 
              subscription_updated_at: new Date().toISOString(),
              merged_quotas: upgradeResult.merged_quotas 
            }
          )

          // Also log in paddle_transactions table for history tracking
          try {
            console.log('Logging quota merge upgrade transaction to paddle_transactions')
            
            const { error: transactionError } = await supabase
              .from('paddle_transactions')
              .insert({
                user_id: upgradeMetadata.user_id,
                paddle_transaction_id: `quota_merge_${data.id}_${Date.now()}`, // Create unique transaction ID
                paddle_customer_id: data.customer_id || '',
                paddle_product_id: null, // Subscriptions don't have a single product_id
                status: 'completed',
                amount: 0, // Quota merging upgrades don't have an amount - billing happens separately
                currency: 'USD', // Default currency
                credit_amount: null, // Not applicable for subscriptions
                transaction_type: 'subscription_upgrade',
                paddle_receipt_url: null, // Subscription upgrades don't have a receipt
                metadata: {
                  upgrade_type: 'quota_merge',
                  upgrade_flow: 'upgrade_now_merge_quotas',
                  target_plan: upgradeMetadata.target_plan,
                  old_subscription_id: upgradeMetadata.old_subscription_id,
                  subscription_id: data.id,
                  subscription_status: data.status,
                  merged_quotas: upgradeResult.merged_quotas,
                  ...data
                }
              })

            if (transactionError) {
              console.warn('Failed to log quota merge upgrade transaction (non-critical):', transactionError)
              // Don't fail the webhook for this - it's just for history tracking
            } else {
              console.log('✅ Quota merge upgrade transaction logged successfully')
            }
          } catch (logError) {
            console.warn('Non-critical error logging quota merge upgrade transaction:', logError)
            // Continue processing - this is just for history tracking
          }
        } else {
          console.error('❌ Subscription upgrade with quota merging failed:', upgradeResult.message)
        }
      } catch (upgradeError) {
        console.error('Error processing upgrade metadata in subscription.updated:', upgradeError)
        // Don't fail the webhook - subscription update was successful
      }
    } else if (data.custom_data?.subscription_plan && !isSubscriptionRenewal) {
      // Handle regular plan upgrades (without quota merging) - only if NOT a renewal
      console.log('🔄 Detected regular plan upgrade in subscription.updated:', {
        subscription_id: data.id,
        new_plan: data.custom_data.subscription_plan,
        user_id: data.custom_data.user_id
      })
      
      try {
        // Use the database function to update the subscription with the new plan
        const userId = data.custom_data.user_id
        const newPlan = data.custom_data.subscription_plan
        
        if (userId && newPlan) {
          const { data: result, error: dbError } = await supabase.rpc(
            'handle_subscription_transaction',
            {
              p_user_id: userId,
              p_paddle_subscription_id: data.id,
              p_paddle_customer_id: data.customer_id || '',
              p_subscription_plan: newPlan,
              p_transaction_data: data,
              p_upgrade_flow: 'update_next_billing' // Regular plan upgrade scheduled for next billing
            }
          )

          if (dbError) {
            console.error('❌ Failed to update subscription plan:', dbError)
          } else {
            console.log('✅ Regular plan upgrade completed successfully:', {
              user_id: result.user_id,
              subscription_id: result.subscription_id,
              plan: result.plan,
              quotas: result.quotas
            })

            // Log the plan upgrade transaction in paddle_transactions table for history tracking
            try {
              console.log('Logging plan upgrade transaction to paddle_transactions')
              
              const { error: transactionError } = await supabase
                .from('paddle_transactions')
                .insert({
                  user_id: userId,
                  paddle_transaction_id: `upgrade_${data.id}_${Date.now()}`, // Create unique transaction ID for upgrades
                  paddle_customer_id: data.customer_id || '',
                  paddle_product_id: null, // Subscriptions don't have a single product_id
                  status: 'completed',
                  amount: 0, // Subscription upgrades don't have an amount - billing happens separately
                  currency: 'USD', // Default currency
                  credit_amount: null, // Not applicable for subscriptions
                  transaction_type: 'subscription_upgrade',
                  paddle_receipt_url: null, // Subscription upgrades don't have a receipt
                  metadata: {
                    upgrade_type: 'plan_change',
                    upgrade_flow: 'update_next_billing',
                    new_plan: newPlan,
                    subscription_id: data.id,
                    subscription_status: data.status,
                    ...data
                  }
                })

              if (transactionError) {
                console.warn('Failed to log upgrade transaction (non-critical):', transactionError)
                // Don't fail the webhook for this - it's just for history tracking
              } else {
                console.log('✅ Plan upgrade transaction logged successfully')
              }
            } catch (logError) {
              console.warn('Non-critical error logging upgrade transaction:', logError)
              // Continue processing - this is just for history tracking
            }
          }
        }
      } catch (upgradeError) {
        console.error('Error processing regular plan upgrade:', upgradeError)
        // Don't fail the webhook - subscription update was successful
      }
    }

    // Handle subscription renewal (same plan, new billing period, reset quotas)
    if (isSubscriptionRenewal && data.current_billing_period?.starts_at) {
      console.log('🔄 Subscription renewal detected - fetching subscription details from database:', {
        subscription_id: data.id,
        new_period_start: data.current_billing_period.starts_at,
        new_period_end: data.current_billing_period.ends_at,
        note: 'Paddle automatic renewals do not include custom_data, fetching from database'
      })

      try {
        // Fetch subscription details from our database - critical for automatic renewals
        // as Paddle does not include custom_data in automatic renewal webhooks
        const { data: subscriptionDetails, error: fetchError } = await supabase
          .from('user_subscriptions')
          .select(`
            user_id,
            plan_id,
            subscription_plans (
              name
            )
          `)
          .eq('paddle_subscription_id', data.id)
          .single()

        if (fetchError || !subscriptionDetails) {
          console.error('❌ Failed to fetch subscription details for renewal:', fetchError)
          return false
        }

        const userId = subscriptionDetails.user_id
        const planName = subscriptionDetails.subscription_plans?.name
        
        if (!userId || !planName) {
          console.error('❌ Missing user_id or plan name in subscription details:', {
            user_id: userId,
            plan_name: planName,
            subscription_details: subscriptionDetails
          })
          return false
        }

        console.log('📋 Retrieved subscription details from database for automatic renewal:', {
          user_id: userId,
          plan_name: planName,
          plan_id: subscriptionDetails.plan_id,
          source: 'database_lookup_not_custom_data'
        })

        // Use the centralized database function to handle renewal with quota reset
        const { data: result, error: renewalError } = await supabase.rpc(
          'handle_subscription_transaction',
          {
            p_user_id: userId,
            p_paddle_subscription_id: data.id,
            p_paddle_customer_id: data.customer_id || '',
            p_subscription_plan: planName,
            p_transaction_data: {
              ...data,
              renewal_type: 'quota_reset',
              previous_period_end: currentSub?.current_period_end,
              new_period_start: data.current_billing_period.starts_at,
              new_period_end: data.current_billing_period.ends_at,
              automatic_renewal: true // Flag to indicate this is an automatic renewal
            },
            p_upgrade_flow: 'renewal' // Special flow for renewals to trigger quota reset
          }
        )

        if (renewalError) {
          console.error('❌ Failed to process subscription renewal:', renewalError)
          return false
        } else if (!result?.success) {
          console.error('❌ Subscription renewal failed:', result)
          return false
        } else {
          console.log('✅ Subscription renewal completed successfully:', {
            user_id: result.user_id,
            subscription_id: result.subscription_id,
            plan: result.plan,
            quotas: result.quotas,
            renewal_type: 'automatic_quota_reset'
          })

          // Log the renewal transaction in paddle_transactions table for history tracking
          try {
            console.log('Logging subscription renewal transaction to paddle_transactions')
            
            const { error: transactionError } = await supabase
              .from('paddle_transactions')
              .insert({
                user_id: userId,
                paddle_transaction_id: `renewal_${data.id}_${Date.now()}`, // Create unique transaction ID for renewals
                paddle_customer_id: data.customer_id || '',
                paddle_product_id: null, // Subscriptions don't have a single product_id
                status: 'completed',
                amount: 0, // Subscription renewals don't have an amount here - billing happens separately
                currency: 'USD', // Default currency
                credit_amount: null, // Not applicable for subscriptions
                transaction_type: 'subscription_renewal',
                paddle_receipt_url: null, // Subscription renewals don't have a receipt
                metadata: {
                  renewal_type: 'quota_reset',
                  subscription_plan: planName,
                  subscription_id: data.id,
                  subscription_status: data.status,
                  previous_period_end: currentSub?.current_period_end,
                  new_period_start: data.current_billing_period.starts_at,
                  new_period_end: data.current_billing_period.ends_at,
                  quotas_reset: result.quotas,
                  automatic_renewal: true,
                  data_source: 'database_lookup',
                  ...data
                }
              })

            if (transactionError) {
              console.warn('Failed to log renewal transaction (non-critical):', transactionError)
              // Don't fail the webhook for this - it's just for history tracking
            } else {
              console.log('✅ Subscription renewal transaction logged successfully')
            }
          } catch (logError) {
            console.warn('Non-critical error logging renewal transaction:', logError)
            // Continue processing - this is just for history tracking
          }
        }
      } catch (renewalError) {
        console.error('Error processing subscription renewal:', renewalError)
        // Don't fail the webhook - subscription update was successful
        return false
      }
    }

    // Handle payment recovery (past_due -> active without new billing period)
    if (isPaymentRecovery && !isSubscriptionRenewal) {
      console.log('💳 Payment recovery detected - subscription reactivated without quota reset:', {
        subscription_id: data.id,
        status_change: `${currentSub?.status} -> ${data.status}`,
        billing_period_unchanged: !data.current_billing_period?.starts_at || 
          new Date(data.current_billing_period.starts_at) <= new Date(currentSub?.current_period_start || 0)
      })
      
      // Payment recovery: just reactivate subscription, don't reset quotas
      // User continues with their existing quota until the next billing period
      console.log('✅ Payment recovery processed - no quota reset needed')
    }

    console.log('Subscription updated successfully:', data.id)
    return true

  } catch (error) {
    console.error('Error in handleSubscriptionUpdated:', error)
    return false
  }
}

// Handle subscription cancellation - cleanup quotas and remove subscription
// NOTE: Paddle automatic events (paused, past_due, canceled) do NOT contain custom_data
// This function correctly fetches required data from the database instead via cleanup_cancelled_subscription
async function handleSubscriptionCanceled(supabase: any, event: PaddleWebhookEvent): Promise<boolean> {
  const { data } = event
  
  console.log('Processing subscription canceled (final cleanup):', {
    subscription_id: data.id,
    canceled_at: data.canceled_at
  })

  try {
    // Use the database function to handle cleanup
    const { data: result, error } = await supabase.rpc(
      'cleanup_cancelled_subscription',
      {
        p_paddle_subscription_id: data.id
      }
    )

    if (error) {
      console.error('Error calling cleanup_canceled_subscription:', error)
      return false
    }

    if (!result?.success) {
      console.error('Subscription cleanup failed:', result)
      return false
    }

    // Note: paddle.subscriptions table no longer exists after migration
    // Subscription cleanup is handled by the cleanup_cancelled_subscription function

    console.log('Successfully cleaned up canceled subscription:', result)
    
    // Optional: Log subscription cancellation transaction in paddle_transactions table for history tracking
    try {
      console.log('Logging subscription cancellation transaction to paddle_transactions')
      
      const { error: transactionError } = await supabase
        .from('paddle_transactions')
        .insert({
          user_id: result.user_id || null, // From cleanup function result
          paddle_transaction_id: `cancellation_${data.id}_${Date.now()}`, // Create unique transaction ID for cancellations
          paddle_customer_id: data.customer_id || '',
          paddle_product_id: null, // Subscriptions don't have a single product_id
          status: 'completed',
          amount: 0, // Subscription cancellations don't have an amount
          currency: 'USD', // Default currency
          credit_amount: null, // Not applicable for cancellations
          transaction_type: 'subscription_cancelled',
          paddle_receipt_url: null, // Subscription cancellations don't have a receipt
          metadata: {
            cancellation_type: 'final_cleanup',
            subscription_id: data.id,
            canceled_at: data.canceled_at,
            cleanup_result: result,
            ...data
          }
        })

      if (transactionError) {
        console.warn('Failed to log cancellation transaction (non-critical):', transactionError)
        // Don't fail the webhook for this - it's just for history tracking
      } else {
        console.log('✅ Subscription cancellation transaction logged successfully')
      }
    } catch (logError) {
      console.warn('Non-critical error logging cancellation transaction:', logError)
      // Continue processing - this is just for history tracking
    }
    
    return true

  } catch (error) {
    console.error('Error in handleSubscriptionCanceled:', error)
    return false
  }
}


// Handle subscription transactions that come through as transaction.completed events
async function handleSubscriptionTransaction(supabase: any, event: PaddleWebhookEvent): Promise<boolean> {
  const { data } = event
  
  console.log('Processing subscription transaction:', {
    transaction_id: data.id,
    customer_id: data.customer_id,
    subscription_id: data.subscription_id,
    custom_data: data.custom_data
  })

  // Extract custom data first
  const customData = data.custom_data || {}
  const userId = customData.user_id
  const subscriptionPlan = customData.subscription_plan
  
  if (!userId || !subscriptionPlan) {
    console.error('Missing user_id or subscription_plan in custom_data')
    return false
  }

  // For subscription transactions, use the subscription_id from the transaction data
  const subscriptionId = data.subscription_id || data.id
  
  console.log('Subscription ID resolution:', {
    subscription_id: subscriptionId,
    transaction_id: data.id,
    is_subscription_transaction: !!data.subscription_id,
    data_subscription_id: data.subscription_id,
    data_id: data.id,
    will_use: subscriptionId
  })

  // Validate that we have a proper subscription ID (should start with 'sub_')
  if (!subscriptionId.startsWith('sub_')) {
    console.error('Invalid subscription ID format:', {
      subscription_id: subscriptionId,
      expected_format: 'sub_xxxxxxxxxx',
      transaction_data: data
    })
    
    // If we don't have a proper subscription ID, this might not be a subscription transaction
    // or the subscription hasn't been created yet
    console.log('Skipping subscription transaction processing - no valid subscription ID found')
    return true // Return true to avoid retry, this might be processed later by subscription.created event
  }

  // Check for upgrade metadata and handle quota merging BEFORE processing the subscription
  // This is critical because the database function will replace the old subscription
  const upgradeMetadata: UpgradeMetadata | undefined = customData.upgrade_metadata
  if (upgradeMetadata?.upgrade_flow === UpgradeFlow.UPGRADE_NOW_MERGE_QUOTAS) {
    console.log('🔄 Detected upgrade with quota merging - processing BEFORE subscription creation:', upgradeMetadata)
    
    try {
      const upgradeHandler = new SubscriptionUpgradeHandler()
      
      // Process the upgrade with quota merging BEFORE the database function runs
      const upgradeResult = await upgradeHandler.processUpgradeWithQuotaMerge(
        upgradeMetadata.user_id,
        upgradeMetadata.old_subscription_id,
        subscriptionId // new subscription ID
      )

      if (upgradeResult.success) {
        console.log('✅ Pre-processing upgrade completed successfully:', upgradeResult)
        
        // Log the upgrade transaction
        await upgradeHandler.logUpgradeTransaction(
          upgradeMetadata.user_id,
          upgradeMetadata.old_subscription_id,
          subscriptionId,
          UpgradeFlow.UPGRADE_NOW_MERGE_QUOTAS,
          { 
            transaction_processed_at: new Date().toISOString(),
            merged_quotas: upgradeResult.merged_quotas 
          }
        )
      } else {
        console.error('❌ Pre-processing upgrade failed:', upgradeResult.message)
        // Continue with normal processing - don't fail the webhook
      }
    } catch (upgradeError) {
      console.error('Error in pre-processing upgrade:', upgradeError)
      // Continue with normal processing - don't fail the webhook
    }
  }

  // Call the centralized database function to handle all subscription operations
  const { data: result, error } = await supabase.rpc(
    'handle_subscription_transaction',
    {
      p_user_id: userId,
      p_paddle_subscription_id: subscriptionId, // Use actual subscription ID, not transaction ID
      p_paddle_customer_id: data.customer_id,
      p_subscription_plan: subscriptionPlan,
      p_transaction_data: data,
      p_upgrade_flow: null // Regular subscription transaction, not an upgrade
    }
  )

  if (error) {
    console.error('Error calling handle_subscription_transaction:', error)
    return false
  }

  if (!result?.success) {
    console.error('Subscription transaction failed:', result)
    return false
  }

  console.log('Successfully processed subscription transaction:', {
    user_id: result.user_id,
    subscription_id: result.subscription_id,
    plan: result.plan,
    quotas: result.quotas
  })

  return true
}

// Handle subscription resumption (undo cancellation)
async function handleSubscriptionResumed(supabase: any, event: PaddleWebhookEvent): Promise<boolean> {
  const { data } = event
  
  console.log('Processing subscription resumed:', {
    subscription_id: data.id,
    status: data.status,
    resumed_at: new Date().toISOString()
  })

  try {
    // Update user subscription - resumption is handled by edge function
    // Webhook just needs to ensure subscription is active
    const { error: userSubError } = await supabase
      .from('user_subscriptions')
      .update({
        status: 'active',
        current_period_start: data.current_billing_period?.starts_at,
        current_period_end: data.current_billing_period?.ends_at
      })
      .eq('paddle_subscription_id', data.id)

    if (userSubError) {
      console.error('Error updating user subscription on resume:', userSubError)
      return false
    }

    // Note: paddle.subscriptions table no longer exists after migration
    // Only user_subscriptions table is maintained

    console.log('Successfully resumed subscription:', data.id)
    return true
  } catch (error) {
    console.error('Error in handleSubscriptionResumed:', error)
    return false
  }
}

// Handle subscription paused event
// NOTE: Paddle automatic events (paused, past_due, canceled) do NOT contain custom_data
// This function correctly fetches required data from the database instead
async function handleSubscriptionPaused(supabase: any, event: PaddleWebhookEvent): Promise<boolean> {
  const { data } = event
  
  console.log('Processing subscription paused:', {
    subscription_id: data.id,
    status: data.status,
    paused_at: new Date().toISOString()
  })

  try {
    // Update user subscription to paused status
    const { error: userSubError } = await supabase
      .from('user_subscriptions')
      .update({
        status: 'paused',
        current_period_start: data.current_billing_period?.starts_at,
        current_period_end: data.current_billing_period?.ends_at,
        updated_at: new Date().toISOString()
      })
      .eq('paddle_subscription_id', data.id)

    if (userSubError) {
      console.error('Error updating user subscription to paused:', userSubError)
      return false
    }

    console.log('Successfully paused subscription:', data.id)
    return true
  } catch (error) {
    console.error('Error in handleSubscriptionPaused:', error)
    return false
  }
}

// Handle subscription past due event
// NOTE: Paddle automatic events (paused, past_due, canceled) do NOT contain custom_data
// This function correctly fetches required data from the database instead
async function handleSubscriptionPastDue(supabase: any, event: PaddleWebhookEvent): Promise<boolean> {
  const { data } = event
  
  console.log('Processing subscription past due:', {
    subscription_id: data.id,
    status: data.status,
    next_billed_at: data.next_billed_at,
    current_billing_period: data.current_billing_period
  })

  try {
    // Update user subscription to past_due status but keep access active
    // During dunning period, customers typically retain access while Paddle attempts recovery
    const { error: userSubError } = await supabase
      .from('user_subscriptions')
      .update({
        status: 'past_due',
        current_period_start: data.current_billing_period?.starts_at,
        current_period_end: data.current_billing_period?.ends_at,
        next_billed_at: data.next_billed_at,
        updated_at: new Date().toISOString()
      })
      .eq('paddle_subscription_id', data.id)

    if (userSubError) {
      console.error('Error updating user subscription to past_due:', userSubError)
      return false
    }

    // Note: paddle.subscriptions table no longer exists after migration
    // Only user_subscriptions table is maintained

    console.log('Successfully processed subscription past due:', data.id)
    return true

  } catch (error) {
    console.error('Error in handleSubscriptionPastDue:', error)
    return false
  }
}

// Handle transaction payment failures
async function handleTransactionPaymentFailed(supabase: any, event: PaddleWebhookEvent): Promise<boolean> {
  const { data } = event
  
  console.log('Processing transaction payment failed:', {
    transaction_id: data.id,
    status: data.status,
    subscription_id: data.subscription_id,
    amount: data.details?.totals?.total,
    currency: data.details?.totals?.currency_code
  })

  try {
    // Update transaction status in public.paddle_transactions table
    const { error: updateError } = await supabase
      .from('paddle_transactions')
      .update({
        status: 'payment_failed',
        metadata: data,
        updated_at: new Date().toISOString()
      })
      .eq('paddle_transaction_id', data.id)

    if (updateError) {
      console.warn('Transaction payment failed update table not available:', updateError.message)
    } else {
      console.log('Successfully updated transaction payment failed status:', data.id)
    }

    return true

  } catch (error) {
    console.error('Error in handleTransactionPaymentFailed:', error)
    return false
  }
}

// Handle transaction past due - when transaction becomes past due
async function handleTransactionPastDue(supabase: any, event: PaddleWebhookEvent): Promise<boolean> {
  const { data } = event
  
  console.log('Processing transaction past due:', {
    transaction_id: data.id,
    status: data.status,
    subscription_id: data.subscription_id
  })

  try {
    // Update transaction status in public.paddle_transactions table
    const { error: updateError } = await supabase
      .from('paddle_transactions')
      .update({
        status: 'past_due',
        metadata: data,
        updated_at: new Date().toISOString()
      })
      .eq('paddle_transaction_id', data.id)

    if (updateError) {
      console.warn('Transaction past due update table not available:', updateError.message)
    } else {
      console.log('Successfully updated transaction past due status:', data.id)
    }

    return true

  } catch (error) {
    console.error('Error in handleTransactionPastDue:', error)
    return false
  }
}

// Handle subscription activation - for edge cases where subscription.activated fires separately
// Note: The main quota reset logic is in handleSubscriptionUpdated for payment recovery
async function handleSubscriptionActivated(supabase: any, event: PaddleWebhookEvent): Promise<boolean> {
  const { data } = event
  
  console.log('Processing subscription activated (edge case - main recovery handled by subscription.updated):', {
    subscription_id: data.id,
    status: data.status,
    current_billing_period: data.current_billing_period,
    next_billed_at: data.next_billed_at
  })

  try {
    // Update user subscription back to active status
    const { error: userSubError } = await supabase
      .from('user_subscriptions')
      .update({
        status: 'active',
        current_period_start: data.current_billing_period?.starts_at,
        current_period_end: data.current_billing_period?.ends_at,
        next_billed_at: data.next_billed_at,
        updated_at: new Date().toISOString()
      })
      .eq('paddle_subscription_id', data.id)

    if (userSubError) {
      console.error('Error updating user subscription to active:', userSubError)
      return false
    }

    // Note: paddle.subscriptions table no longer exists after migration
    // Only user_subscriptions table is maintained
    // Note: Quota resets are primarily handled by subscription.updated events

    console.log('Successfully processed subscription activation:', data.id)
    return true

  } catch (error) {
    console.error('Error in handleSubscriptionActivated:', error)
    return false
  }
}


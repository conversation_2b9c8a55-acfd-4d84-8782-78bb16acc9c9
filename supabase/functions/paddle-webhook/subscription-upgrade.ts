import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { Database } from './_shared/database.types.ts'
import { SubscriptionUpgradeRequest, UpgradeFlow } from './_shared/types.ts'

export class SubscriptionUpgradeHandler {
  private supabase: ReturnType<typeof createClient<Database>>

  constructor() {
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    
    this.supabase = createClient<Database>(supabaseUrl, supabaseServiceKey)
  }

  /**
   * Calculate remaining quotas from old subscription for merging
   * CRITICAL: This function is called AFTER webhook has updated subscription ID
   * from old to new Paddle subscription ID, so we can safely use newPaddleSubscriptionId
   */
  async processUpgradeWithQuotaMerge(
    userId: string,
    oldPaddleSubscriptionId: string,
    newPaddleSubscriptionId: string,
    targetPlanData?: { name: string; transcription_minutes: number; tokens: number }
  ): Promise<{ success: boolean; message: string; merged_quotas?: any }> {
    try {
      console.log('🔄 Processing quota merge for subscription upgrade', {
        userId,
        oldPaddleSubscriptionId,
        newPaddleSubscriptionId,
        targetPlanData
      })

      // Validate that we have target plan data
      if (!targetPlanData) {
        console.error('❌ No target plan data provided for quota merging')
        return {
          success: false,
          message: 'Target plan data is required for quota merging'
        }
      }

      // First, get the old subscription to find its internal ID
      const { data: oldSub, error: oldSubError } = await this.supabase
        .from('user_subscriptions')
        .select('id')
        .eq('paddle_subscription_id', oldPaddleSubscriptionId)
        .eq('user_id', userId)
        .single()

      if (oldSubError || !oldSub) {
        console.error('❌ Could not find old subscription:', oldSubError)
        return {
          success: false,
          message: `Old subscription not found: ${oldSubError?.message || 'Not found'}`
        }
      }

      // Get remaining quotas from old subscription
      const { data: oldQuotas, error: quotasError } = await this.supabase
        .from('quotas')
        .select('service, used_amount, total_amount')
        .eq('user_id', userId)
        .eq('subscription_id', oldSub.id)

      if (quotasError) {
        console.error('❌ Could not fetch old quotas:', quotasError)
        return {
          success: false,
          message: `Failed to fetch old quotas: ${quotasError.message}`
        }
      }

      console.log('📊 Old quotas fetched successfully:', oldQuotas)

      // Calculate remaining amounts from old quotas
      let remainingTranscription = 0
      let remainingOptimization = 0

      if (oldQuotas) {
        for (const quota of oldQuotas) {
          const remaining = Math.max(0, quota.total_amount - quota.used_amount);
          if (quota.service === 'transcription') {
            remainingTranscription = remaining
          } else if (quota.service === 'optimization') {
            remainingOptimization = remaining
          }
        }
      }

      // Use the target plan data provided from custom_data (the plan user is upgrading TO)
      const newPlanTranscription = targetPlanData.transcription_minutes
      const newPlanOptimization = targetPlanData.tokens // Use tokens column instead of input_tokens + output_tokens

      // Merge the remaining quotas with the new plan allocations
      const mergedTranscription = remainingTranscription + newPlanTranscription
      const mergedOptimization = remainingOptimization + newPlanOptimization

      console.log('📊 Calculated remaining quotas:', {
        transcription: remainingTranscription,
        optimization: remainingOptimization
      })

      console.log('📊 New plan allocation:', {
        transcription: newPlanTranscription,
        optimization: newPlanOptimization
      })

      console.log('📊 Merged quotas:', {
        transcription: mergedTranscription,
        optimization: mergedOptimization
      })

      // Call the database function to apply the merged quotas
      const { data: dbResult, error: dbError } = await this.supabase.rpc(
        'handle_subscription_transaction',
        {
          p_user_id: userId,
          p_paddle_subscription_id: newPaddleSubscriptionId,
          p_paddle_customer_id: '', // This may need to be fetched if required
          p_subscription_plan: targetPlanData.name.toLowerCase(), // Use the target plan name from custom_data
          p_transaction_data: null,
          p_transcription_quota_override: Math.round(mergedTranscription), // Round to nearest integer
          p_optimization_quota_override: Math.round(mergedOptimization),    // Round to nearest integer
          p_upgrade_flow: 'upgrade_now_merge_quotas' // Always immediate for quota merging
        }
      )

      if (dbError) {
        console.error('❌ Failed to apply merged quotas:', dbError)
        return {
          success: false,
          message: `Failed to apply merged quotas: ${dbError.message}`,
          merged_quotas: {
            transcription: mergedTranscription,
            optimization: mergedOptimization
          }
        }
      }

      console.log('✅ Successfully applied merged quotas:', dbResult)

      return {
        success: true,
        message: 'Quotas calculated and applied successfully',
        merged_quotas: {
          transcription: mergedTranscription,
          optimization: mergedOptimization
        }
      }
    } catch (error) {
      console.error('❌ Unexpected error in quota calculation:', error)
      return {
        success: false,
        message: `Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  /**
   * Validate upgrade request before processing
   */
  async validateUpgradeRequest(request: SubscriptionUpgradeRequest): Promise<{
    valid: boolean
    message: string
    subscription?: any
  }> {
    try {
      // Get current subscription from user_subscriptions table
      const { data: subscription, error } = await this.supabase
        .from('user_subscriptions')
        .select('*')
        .eq('paddle_subscription_id', request.currentSubscriptionId)
        .eq('user_id', request.userId)
        .single()

      if (error || !subscription) {
        return {
          valid: false,
          message: 'Current subscription not found'
        }
      }

      // Check if subscription is active
      if (!['active', 'trialing', 'past_due'].includes(subscription.status)) {
        return {
          valid: false,
          message: 'Current subscription is not in a valid state for upgrade'
        }
      }

      // Validate upgrade flow
      if (!Object.values(UpgradeFlow).includes(request.upgradeFlow)) {
        return {
          valid: false,
          message: 'Invalid upgrade flow specified'
        }
      }

      return {
        valid: true,
        message: 'Upgrade request validated',
        subscription
      }
    } catch (error) {
      console.error('❌ Error validating upgrade request:', error)
      return {
        valid: false,
        message: `Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  /**
   * Log upgrade transaction for audit trail
   */
  async logUpgradeTransaction(
    userId: string,
    oldSubscriptionId: string,
    newSubscriptionId: string,
    upgradeFlow: UpgradeFlow,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      // Log to paddle_transactions table for audit trail
      const { error } = await this.supabase
        .from('paddle_transactions')
        .insert({
          user_id: userId,
          paddle_transaction_id: `upgrade_${newSubscriptionId}_${Date.now()}`, // Create unique transaction ID
          paddle_customer_id: '', // May need to be provided if required
          paddle_product_id: null, // Subscriptions don't have a single product_id
          status: 'completed',
          amount: 0, // Upgrades don't have a direct amount in our system
          currency: 'USD', // Default currency
          credit_amount: null, // Not applicable for upgrades
          transaction_type: 'subscription_upgrade',
          paddle_receipt_url: null, // Upgrades don't have receipts
          metadata: {
            upgrade_flow: upgradeFlow,
            old_subscription_id: oldSubscriptionId,
            new_subscription_id: newSubscriptionId,
            ...metadata
          }
        })

      if (error) {
        console.error('❌ Error logging upgrade transaction:', error)
      } else {
        console.log('✅ Upgrade transaction logged successfully')
      }
    } catch (error) {
      console.error('❌ Unexpected error logging upgrade transaction:', error)
    }
  }

  /**
   * Get upgrade preview with quota calculations
   */
  async getUpgradePreview(
    userId: string,
    currentPaddleSubscriptionId: string,
    targetPriceId: string
  ): Promise<{
    success: boolean
    preview?: {
      current_plan: any
      target_plan: any
      remaining_quotas: any
      merged_quotas?: any
    }
    message: string
  }> {
    try {
      // Get current subscription details from user_subscriptions table
      const { data: currentSubscription, error: subError } = await this.supabase
        .from('user_subscriptions')
        .select(`
          *,
          subscription_plans (
            name,
            transcription_minutes,
            tokens
          )
        `)
        .eq('paddle_subscription_id', currentPaddleSubscriptionId)
        .eq('user_id', userId)
        .single()

      if (subError || !currentSubscription) {
        return {
          success: false,
          message: 'Current subscription not found'
        }
      }

      // Get current quotas from quotas table
      const { data: currentQuotas, error: quotasError } = await this.supabase
        .from('quotas')
        .select('service, used_amount, total_amount')
        .eq('user_id', userId)
        .eq('subscription_id', currentSubscription.id)

      if (quotasError) {
        console.warn('Could not fetch current quotas:', quotasError)
      }

      // Calculate remaining quotas
      let remainingTranscription = 0
      let remainingOptimization = 0

      if (currentQuotas) {
        for (const quota of currentQuotas) {
          const remaining = Math.max(0, quota.total_amount - quota.used_amount)
          if (quota.service === 'transcription') {
            remainingTranscription = remaining
          } else if (quota.service === 'optimization') {
            remainingOptimization = remaining
          }
        }
      }

      return {
        success: true,
        preview: {
          current_plan: currentSubscription.subscription_plans,
          target_plan: { price_id: targetPriceId }, // This needs to be filled with actual plan details
          remaining_quotas: {
            transcription: remainingTranscription,
            optimization: remainingOptimization
          },
          merged_quotas: null // Would need target plan details to calculate
        },
        message: 'Preview generated successfully'
      }
    } catch (error) {
      console.error('❌ Error generating upgrade preview:', error)
      return {
        success: false,
        message: `Preview error: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }
}

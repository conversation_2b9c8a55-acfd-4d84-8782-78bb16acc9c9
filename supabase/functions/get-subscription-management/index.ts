import { serve } from "https://deno.land/std@0.190.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const PADDLE_API_KEY = Deno.env.get('PADDLE_API_KEY')
const PADDLE_ENVIRONMENT = Deno.env.get('PADDLE_ENVIRONMENT') || 'sandbox'
const SUPABASE_URL = Deno.env.get('SUPABASE_URL')!
const SUPABASE_ANON_KEY = Deno.env.get('SUPABASE_ANON_KEY')!

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get the authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      throw new Error('No authorization header')
    }

    // Create Supabase client with the user's JWT
    const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
      global: { headers: { Authorization: authHeader } }
    })

    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      throw new Error('Unauthorized')
    }

    // Parse request body
    const { subscriptionId } = await req.json()

    if (!subscriptionId) {
      throw new Error('Missing subscriptionId')
    }

    // Get user's subscription to verify ownership
    const { data: userSubscription, error: subError } = await supabase
      .from('user_subscriptions')
      .select('*')
      .eq('user_id', user.id)
      .eq('paddle_subscription_id', subscriptionId)
      .single()

    if (subError || !userSubscription) {
      throw new Error('Subscription not found or not owned by user')
    }

    // Call Paddle API to get subscription with management URLs
    const paddleUrl = PADDLE_ENVIRONMENT === 'production' 
      ? 'https://api.paddle.com' 
      : 'https://sandbox-api.paddle.com'

    const paddleResponse = await fetch(`${paddleUrl}/subscriptions/${subscriptionId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${PADDLE_API_KEY}`,
        'Content-Type': 'application/json',
      }
    })

    if (!paddleResponse.ok) {
      const errorText = await paddleResponse.text()
      console.error('Paddle API error:', errorText)
      throw new Error(`Paddle API error: ${paddleResponse.status}`)
    }

    const paddleData = await paddleResponse.json()
    
    return new Response(
      JSON.stringify({
        success: true,
        managementUrls: paddleData.data.management_urls || null,
        subscription: paddleData.data
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('Get subscription management error:', error)
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})

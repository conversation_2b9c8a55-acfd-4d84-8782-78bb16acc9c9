import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

interface CreateSubscriptionCheckoutRequest {
  plan_name: string // Plan name: 'Basic', 'Pro', 'Premium'
  customer_email?: string
  success_url?: string
  cancel_url?: string
}

interface PaddleCheckoutResponse {
  data: {
    url: string
    id: string
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  if (req.method !== 'POST') {
    return new Response('Method not allowed', { 
      status: 405, 
      headers: corsHeaders 
    })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const { plan_name, customer_email, success_url, cancel_url }: CreateSubscriptionCheckoutRequest = await req.json()

    if (!plan_name) {
      return new Response(
        JSON.stringify({ error: 'plan_name is required' }),
        { status: 400, headers: corsHeaders }
      )
    }

    // Get user from auth header
    const authHeader = req.headers.get('authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Authorization header required' }),
        { status: 401, headers: corsHeaders }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: userError } = await supabase.auth.getUser(token)
    
    if (userError || !user) {
      return new Response(
        JSON.stringify({ error: 'Invalid token' }),
        { status: 401, headers: corsHeaders }
      )
    }

    // Create Paddle subscription checkout
    const paddleApiKey = Deno.env.get('PADDLE_API_KEY')
    const paddleEnvironment = Deno.env.get('PADDLE_ENVIRONMENT') || 'sandbox'
    const baseUrl = paddleEnvironment === 'production' 
      ? 'https://api.paddle.com' 
      : 'https://sandbox-api.paddle.com'

    // Get subscription details for this plan
    const { priceId, quantity } = getSubscriptionDetails(plan_name)
    
    const checkoutData = {
      items: [
        {
          price_id: priceId,
          quantity: quantity
        }
      ],
      customer: {
        email: customer_email || user.email
      },
      custom_data: {
        user_id: user.id,
        plan_name: plan_name
      },
      checkout_settings: {
        success_url: success_url || `${req.headers.get('origin')}/app/subscription?success=true`,
        cancel_url: cancel_url || `${req.headers.get('origin')}/app/subscription?cancelled=true`,
        display_mode: 'overlay'
      }
    }

    console.log('Creating Paddle subscription checkout:', { 
      user_id: user.id, 
      plan_name: plan_name,
      price_id: priceId,
      quantity: quantity
    })

    const paddleResponse = await fetch(`${baseUrl}/checkouts`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${paddleApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(checkoutData)
    })

    if (!paddleResponse.ok) {
      const errorText = await paddleResponse.text()
      console.error('Paddle API error:', {
        status: paddleResponse.status,
        statusText: paddleResponse.statusText,
        body: errorText
      })
      
      return new Response(
        JSON.stringify({ 
          error: 'Failed to create subscription checkout',
          details: paddleResponse.status === 401 ? 'Invalid API key' : 'Paddle API error'
        }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const checkoutResponse: PaddleCheckoutResponse = await paddleResponse.json()

    // Create or update Paddle customer record
    try {
      await supabase.rpc('paddle.create_or_get_customer', {
        p_user_id: user.id,
        p_paddle_customer_id: checkoutData.customer.email, // Temporary until we get real customer ID
        p_email: checkoutData.customer.email,
        p_name: user.email // Fallback to email if no name
      })
    } catch (error) {
      console.error('Error creating/updating customer:', error)
      // Don't fail the checkout for this
    }

    console.log('Successfully created Paddle subscription checkout:', {
      checkout_id: checkoutResponse.data.id,
      checkout_url: checkoutResponse.data.url
    })

    return new Response(
      JSON.stringify({
        success: true,
        checkout_url: checkoutResponse.data.url,
        checkout_id: checkoutResponse.data.id,
        plan_name: plan_name
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      }
    )

  } catch (error) {
    console.error('Subscription checkout error:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error.message 
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
})

// Helper function to get subscription price ID and quantity
function getSubscriptionDetails(planName: string): { priceId: string, quantity: number } {
  const isProduction = Deno.env.get('ENVIRONMENT') === 'production'
  
  // Get the base subscription price ID
  const priceId = isProduction 
    ? Deno.env.get('PADDLE_SUBSCRIPTION_PRICE_ID_PRODUCTION') ?? ''
    : Deno.env.get('PADDLE_SUBSCRIPTION_PRICE_ID_SANDBOX') ?? ''

  if (!priceId) {
    throw new Error('Paddle subscription price ID not configured')
  }

  // Get quantity based on plan
  let quantity: number
  switch (planName) {
    case 'Basic':
      quantity = 1  // $9 x 1 = $9
      break
    case 'Pro':
      quantity = 2  // $9 x 2 = $18
      break
    case 'Premium':
      quantity = 3  // $9 x 3 = $27
      break
    default:
      throw new Error(`Unknown plan: ${planName}`)
  }

  return { priceId, quantity }
}

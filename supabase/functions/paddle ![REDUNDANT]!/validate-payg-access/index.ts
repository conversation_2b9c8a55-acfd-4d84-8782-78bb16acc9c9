import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get authenticated user
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Missing authorization header' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const { data: { user }, error: authError } = await supabase.auth.getUser(
      authHeader.replace('Bearer ', '')
    )

    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Invalid authentication' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    if (req.method === 'GET') {
      // Check if user has valid payment method for PAYG
      return await checkPaygAccess(supabase, user.id)
    } else if (req.method === 'POST') {
      // Set up PAYG access (create Paddle customer and validate payment method)
      return await setupPaygAccess(supabase, user.id, req)
    }

    return new Response('Method not allowed', { 
      status: 405, 
      headers: corsHeaders 
    })

  } catch (error) {
    console.error('PAYG validation error:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error.message 
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
})

// Check if user has PAYG access
async function checkPaygAccess(supabase: any, userId: string) {
  try {
    // Check if user has Paddle customer record
    const { data: customers, error: customerError } = await supabase
      .rpc('query_paddle_customers', { 
        user_id_param: userId 
      })

    if (customerError) {
      console.error('Error checking customer:', customerError)
      return new Response(
        JSON.stringify({ error: 'Failed to check customer status' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }
    
    const customer = customers && customers.length > 0 ? customers[0] : null;

    // Check for unpaid balances
    const { data: unpaidBalances, error: balanceError } = await supabase
      .from('payg_usage')
      .select('month, total_amount')
      .eq('user_id', userId)
      .eq('payment_status', 'unpaid')

    if (balanceError) {
      console.error('Error checking unpaid balances:', balanceError)
    }

    const hasPaymentMethod = !!customer
    const hasUnpaidBalances = unpaidBalances && unpaidBalances.length > 0
    const canUsePayg = hasPaymentMethod && !hasUnpaidBalances

    return new Response(
      JSON.stringify({
        success: true,
        payg_access: {
          has_payment_method: hasPaymentMethod,
          has_unpaid_balances: hasUnpaidBalances,
          can_use_payg: canUsePayg,
          unpaid_balances: unpaidBalances || []
        }
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      }
    )

  } catch (error) {
    console.error('Error checking PAYG access:', error)
    return new Response(
      JSON.stringify({ error: 'Failed to check PAYG access' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
}

// Set up PAYG access for user
async function setupPaygAccess(supabase: any, userId: string, req: Request) {
  try {
    const requestData = await req.json()
    const { return_url } = requestData

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('email, full_name')
      .eq('id', userId)
      .single()

    if (profileError) {
      console.error('Error fetching user profile:', profileError)
      return new Response(
        JSON.stringify({ error: 'User profile not found' }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Create Paddle customer setup URL for payment method
    const paddleApiKey = Deno.env.get('PADDLE_API_KEY')
    const paddleEnvironment = Deno.env.get('PADDLE_ENVIRONMENT') || 'sandbox'
    const baseUrl = paddleEnvironment === 'production' 
      ? 'https://api.paddle.com' 
      : 'https://sandbox-api.paddle.com'

    // Create customer in Paddle first
    const customerData = {
      email: profile.email,
      name: profile.full_name || undefined,
      custom_data: {
        user_id: userId,
        setup_type: 'payg_payment_method'
      }
    }

    const customerResponse = await fetch(`${baseUrl}/customers`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${paddleApiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(customerData)
    })

    if (!customerResponse.ok) {
      const errorText = await customerResponse.text()
      console.error('Paddle customer creation error:', errorText)
      return new Response(
        JSON.stringify({ error: 'Failed to create customer' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const customer = await customerResponse.json()
    const paddleCustomerId = customer.data.id

    // Create customer record in our database
    const { error: dbCustomerError } = await supabase.rpc(
      'create_or_get_customer',
      {
        p_user_id: userId,
        p_paddle_customer_id: paddleCustomerId,
        p_email: profile.email,
        p_name: profile.full_name
      },
      { schema: 'paddle' }
    )

    if (dbCustomerError) {
      console.error('Error creating customer record:', dbCustomerError)
      return new Response(
        JSON.stringify({ error: 'Failed to create customer record' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Create payment method setup URL
    const setupData = {
      customer_id: paddleCustomerId,
      return_url: return_url || `${Deno.env.get('FRONTEND_URL')}/payments?payg_setup=success`,
      business_name: 'VoiceHype'
    }

    const setupResponse = await fetch(`${baseUrl}/customers/${paddleCustomerId}/payment-methods/setup`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${paddleApiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(setupData)
    })

    if (!setupResponse.ok) {
      const errorText = await setupResponse.text()
      console.error('Paddle setup URL creation error:', errorText)
      return new Response(
        JSON.stringify({ error: 'Failed to create payment method setup' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const setup = await setupResponse.json()

    return new Response(
      JSON.stringify({
        success: true,
        setup_url: setup.data.url,
        customer_id: paddleCustomerId
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      }
    )

  } catch (error) {
    console.error('Error setting up PAYG access:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Failed to setup PAYG access',
        details: error.message 
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
}

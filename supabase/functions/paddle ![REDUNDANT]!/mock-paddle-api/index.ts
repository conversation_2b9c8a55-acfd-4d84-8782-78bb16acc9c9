// Mock Paddle API for local testing
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS, GET',
};

// Mock database for storing transactions and customers
const db = {
  transactions: [],
  customers: [],
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  const url = new URL(req.url);
  const path = url.pathname;
  
  console.log(`${req.method} ${path}`);

  try {
    // Mock Paddle API endpoints
    if (path === '/transactions' && req.method === 'POST') {
      return handleCreateTransaction(req);
    } else if (path.startsWith('/transactions/') && req.method === 'GET') {
      const id = path.split('/')[2];
      return handleGetTransaction(id);
    } else if (path === '/customers' && req.method === 'POST') {
      return handleCreateCustomer(req);
    } else if (path.startsWith('/customers/') && req.method === 'GET') {
      const id = path.split('/')[2];
      return handleGetCustomer(id);
    } else {
      return new Response(
        JSON.stringify({ error: 'Not found', path }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }
  } catch (error) {
    console.error('Error handling request:', error);
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        message: error.message,
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});

// Create a new transaction
async function handleCreateTransaction(req) {
  const body = await req.json();
  
  // Validate required fields
  if (!body.items || !Array.isArray(body.items) || body.items.length === 0) {
    return new Response(
      JSON.stringify({ 
        error: { 
          type: "validation_error", 
          code: "missing_required_fields", 
          detail: "Missing required fields: items" 
        },
      }),
      { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
  
  // Generate transaction ID
  const id = `txn_${Date.now()}${Math.floor(Math.random() * 10000)}`;
  
  // Create mock transaction
  const transaction = {
    id,
    status: 'ready',
    customer_id: body.customer_id,
    address_id: body.address_id,
    business_id: body.business_id,
    custom_data: body.custom_data,
    currency_code: body.currency_code || 'USD',
    collection_mode: body.collection_mode || 'automatic',
    items: body.items,
    checkout: {
      url: `https://sandbox-checkout.paddle.com/checkout/${id}?_ptxn=${id}`
    },
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };
  
  // Store transaction
  db.transactions.push(transaction);
  
  return new Response(
    JSON.stringify({
      data: transaction,
      meta: {
        request_id: `req_${Date.now()}`
      }
    }),
    { status: 201, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  );
}

// Get a transaction by ID
function handleGetTransaction(id) {
  const transaction = db.transactions.find(t => t.id === id);
  
  if (!transaction) {
    return new Response(
      JSON.stringify({ 
        error: { 
          type: "resource_not_found_error", 
          code: "transaction_not_found", 
          detail: `Transaction with ID ${id} not found` 
        },
      }),
      { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
  
  return new Response(
    JSON.stringify({
      data: transaction,
      meta: {
        request_id: `req_${Date.now()}`
      }
    }),
    { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  );
}

// Create a new customer
async function handleCreateCustomer(req) {
  const body = await req.json();
  
  // Validate required fields
  if (!body.email) {
    return new Response(
      JSON.stringify({ 
        error: { 
          type: "validation_error", 
          code: "missing_required_fields", 
          detail: "Missing required fields: email" 
        },
      }),
      { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
  
  // Generate customer ID
  const id = `ctm_${Date.now()}${Math.floor(Math.random() * 10000)}`;
  
  // Create mock customer
  const customer = {
    id,
    email: body.email,
    name: body.name,
    status: 'active',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };
  
  // Store customer
  db.customers.push(customer);
  
  return new Response(
    JSON.stringify({
      data: customer,
      meta: {
        request_id: `req_${Date.now()}`
      }
    }),
    { status: 201, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  );
}

// Get a customer by ID
function handleGetCustomer(id) {
  const customer = db.customers.find(c => c.id === id);
  
  if (!customer) {
    return new Response(
      JSON.stringify({ 
        error: { 
          type: "resource_not_found_error", 
          code: "customer_not_found", 
          detail: `Customer with ID ${id} not found` 
        },
      }),
      { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
  
  return new Response(
    JSON.stringify({
      data: customer,
      meta: {
        request_id: `req_${Date.now()}`
      }
    }),
    { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  );
}

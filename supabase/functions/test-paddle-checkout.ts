import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { config } from "https://deno.land/x/dotenv@v3.2.0/mod.ts";

// Load environment variables
config({ path: "./.env.local", export: true });

// Mock function handler
serve(async (req) => {
  try {
    // Extract the function name from the URL
    const url = new URL(req.url);
    const functionName = url.pathname.split("/").pop();

    console.log(`Testing function: ${functionName}`);
    console.log(`Method: ${req.method}`);

    // Handle OPTIONS for CORS
    if (req.method === "OPTIONS") {
      return new Response("ok", {
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
          "Access-Control-Allow-Methods": "POST, GET, OPTIONS",
        },
      });
    }

    // Log request details
    console.log("Request headers:", Object.fromEntries(req.headers.entries()));
    if (req.method === "POST") {
      try {
        const body = await req.json();
        console.log("Request body:", JSON.stringify(body, null, 2));
      } catch (e) {
        console.log("Could not parse request body:", e);
      }
    }
    
    // Call the actual function handler
    let response;
    
    if (functionName === "create-paddle-checkout") {
      // Import and call create-paddle-checkout
      const module = await import("./create-paddle-checkout/index.ts");
      response = await module.default(req);
    } else if (functionName === "get-paddle-transactions") {
      // Import and call get-paddle-transactions
      const module = await import("./get-paddle-transactions/index.ts");
      response = await module.default(req);
    } else if (functionName === "paddle-webhook") {
      // Import and call paddle-webhook
      const module = await import("./paddle-webhook/index.ts");
      response = await module.default(req);
    } else {
      // Handle other functions as needed
      response = new Response(
        JSON.stringify({ error: "Function not supported" }),
        { 
          status: 400, 
          headers: { 
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*"
          } 
        }
      );
    }
    
    // Log the response status
    console.log("Response status:", response.status);
    
    // Try to log the response body
    try {
      const responseClone = response.clone();
      const responseText = await responseClone.text();
      console.log("Response body:", responseText);
      
      // Return the original response
      return response;
    } catch (e) {
      console.log("Could not log response body:", e);
      return response;
    }
  } catch (error) {
    console.error("Error in test handler:", error);
    return new Response(
      JSON.stringify({ error: error.message || "Unknown error" }),
      { 
        status: 500, 
        headers: { 
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*" 
        } 
      }
    );
  }
});

// vscode-auth-callback.ts - Supabase Edge Function
// Place this file in your Supabase functions directory

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.0';

// Initialize the Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';

// Create a Supabase client with the service role key for admin access
const supabase = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Function to create a new API key for a user
 * Always creates a NEW API key as required by security policy
 */
async function createApiKey(userId: string): Promise<{ apiKey: string; keyId: string } | null> {
  try {
    // Generate a friendly name for the VS Code extension key
    const name = `VS Code Extension (${new Date().toISOString().split('T')[0]})`;
    
    // Set user context for RLS (Row Level Security)
    const { error: contextError } = await supabase.auth.admin.getUserById(userId);
    if (contextError) {
      console.error('Error setting user context:', contextError);
      return null;
    }
      // Call the create_api_key RPC function with correct parameters
    const { data, error } = await supabase.rpc('create_api_key', {
      p_name: name,
      // Set expiration to 1 year from now for security
      p_expires_at: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()
    });
    
    if (error) {
      console.error('Error creating API key:', error);
      return null;
    }
    
    // The RPC function returns an api_key_result type with id and key_secret
    if (data && data.id && data.key_secret) {
      return {
        apiKey: `vhkey_${data.key_secret}`, // Add the vhkey_ prefix
        keyId: data.id
      };
    }
    
    console.error('Invalid response from create_api_key:', data);
    return null;
  } catch (err) {
    console.error('Exception creating API key:', err);
    return null;
  }
}

/**
 * Handler for the VS Code authentication callback
 */
serve(async (req) => {
  try {
    // Get the request URL and parse query parameters
    const url = new URL(req.url);
    const state = url.searchParams.get('state');
    const vscodeRedirect = url.searchParams.get('vscode_redirect');
    const code = url.searchParams.get('code');
    const error = url.searchParams.get('error');
    
    // Handle OAuth errors
    if (error) {
      console.error('OAuth error:', error);
      return new Response(
        JSON.stringify({ error: `OAuth error: ${error}` }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }
    
    // Validate the required parameters
    if (!state || !vscodeRedirect || !code) {
      return new Response(
        JSON.stringify({ error: 'Missing required parameters' }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }
    
    // Exchange the authorization code for a session
    const { data: sessionData, error: sessionError } = await supabase.auth.exchangeCodeForSession(code);
    
    if (sessionError || !sessionData?.user) {
      console.error('Error exchanging code for session:', sessionError);
      return new Response(
        JSON.stringify({ error: 'Failed to authenticate' }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }      // Create an API key for the user
    const apiKeyResult = await createApiKey(sessionData.user.id);
    
    if (!apiKeyResult) {
      return new Response(
        JSON.stringify({ error: 'Failed to create API key' }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }
    
    // Get user metadata
    const user = sessionData.user;
    const fullName = user.user_metadata?.full_name;
    const avatarUrl = user.user_metadata?.avatar_url;
    
    // Build the redirect URL back to VS Code
    const redirectUrl = new URL(vscodeRedirect);
      // Add the parameters for the VS Code extension
    redirectUrl.searchParams.append('state', state);
    redirectUrl.searchParams.append('api_key', apiKeyResult.apiKey);
    redirectUrl.searchParams.append('user_id', user.id);
    redirectUrl.searchParams.append('email', user.email || '');
    
    // Add optional user metadata if available
    if (fullName) {
      redirectUrl.searchParams.append('full_name', fullName);
    }
    
    if (avatarUrl) {
      redirectUrl.searchParams.append('avatar_url', avatarUrl);
    }
    
    // Create an HTML page with a button to redirect to VS Code
    const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Return to VS Code</title>
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100vh;
          margin: 0;
          background-color: #f9f9f9;
          color: #333;
        }
        .container {
          text-align: center;
          padding: 2rem;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          background-color: #fff;
          max-width: 500px;
        }
        h1 {
          margin-bottom: 1rem;
          color: #2c3e50;
        }
        p {
          margin-bottom: 2rem;
          line-height: 1.5;
          color: #666;
        }
        .btn {
          display: inline-block;
          background-color: #007acc;
          color: white;
          border: none;
          padding: 0.75rem 1.5rem;
          font-size: 1rem;
          border-radius: 4px;
          cursor: pointer;
          text-decoration: none;
          transition: background-color 0.2s;
        }
        .btn:hover {
          background-color: #005a9e;
        }
        .logo {
          width: 100px;
          height: auto;
          margin-bottom: 1rem;
        }
        .native-dialog-info {
          margin-top: 1.5rem;
          padding: 1rem;
          background-color: #f5f5f5;
          border-radius: 4px;
          font-size: 0.9rem;
          color: #666;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <img src="https://voicehype.ai/logo.svg" alt="VoiceHype Logo" class="logo" />
        <h1>Authentication Successful</h1>
        <p>You have successfully authenticated with VoiceHype. Please click the button below to return to VS Code and complete the setup.</p>
        <a href="${redirectUrl.toString()}" class="btn">Open in VS Code</a>
        <div class="native-dialog-info">
          <p>Your browser may show a dialog asking to open VS Code. Please select "Open" or "Allow" to continue.</p>
        </div>
      </div>
      <script>
        // Automatically redirect after a short delay
        setTimeout(() => {
          window.location.href = "${redirectUrl.toString()}";
        }, 1500);
      </script>
    </body>
    </html>
    `;
    
    return new Response(html, {
      headers: { 'Content-Type': 'text/html' },
    });
  } catch (error) {
    console.error('Error in vscode-auth-callback function:', error);
    
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      }
    );
  }
});

#!/usr/bin/env bash

# Function to display help message
show_help() {
  echo "Usage: $0 [options] <function_name>"
  echo ""
  echo "Options:"
  echo "  -h, --help            Show this help message"
  echo "  -m, --method METHOD   HTTP method (default: POST)"
  echo "  -d, --data FILE       JSON file containing request body"
  echo "  -j, --json STRING     JSON string for request body"
  echo "  -a, --auth TOKEN      Auth token for Bearer authentication"
  echo ""
  echo "Examples:"
  echo "  $0 create-paddle-checkout -j '{\"amount\": 10}'               # Test with inline JSON"
  echo "  $0 create-paddle-checkout -d request.json                     # Test with JSON file"
  echo "  $0 create-paddle-checkout -a \"user_token\" -j '{\"amount\": 10}' # Test with auth"
  echo ""
}

# Default values
METHOD="POST"
DATA=""
JSON_STRING=""
AUTH=""
FUNCTION_NAME=""

# Parse arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    -h|--help)
      show_help
      exit 0
      ;;
    -m|--method)
      METHOD=$2
      shift 2
      ;;
    -d|--data)
      DATA=$2
      shift 2
      ;;
    -j|--json)
      JSON_STRING=$2
      shift 2
      ;;
    -a|--auth)
      AUTH=$2
      shift 2
      ;;
    *)
      FUNCTION_NAME=$1
      shift
      ;;
  esac
done

# Make sure we're in the right directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
cd "$SCRIPT_DIR" || { echo "Failed to change to script directory"; exit 1; }

# Check function name
if [ -z "$FUNCTION_NAME" ]; then
  echo "Error: No function name provided."
  show_help
  exit 1
fi

if [ ! -d "$FUNCTION_NAME" ]; then
  echo "Error: Function '$FUNCTION_NAME' not found."
  echo "Available functions:"
  find . -maxdepth 1 -type d -not -path "./\.*" -not -path "." | sort | sed 's|^./||'
  exit 1
fi

# Build curl command
CURL_CMD="curl -i -X $METHOD http://localhost:8000"

# Add headers
CURL_CMD="$CURL_CMD -H \"Content-Type: application/json\""

# Add auth if provided
if [ ! -z "$AUTH" ]; then
  CURL_CMD="$CURL_CMD -H \"Authorization: Bearer $AUTH\""
fi

# Add request body
if [ ! -z "$DATA" ]; then
  if [ ! -f "$DATA" ]; then
    echo "Error: JSON file '$DATA' not found."
    exit 1
  fi
  CURL_CMD="$CURL_CMD -d @$DATA"
elif [ ! -z "$JSON_STRING" ]; then
  CURL_CMD="$CURL_CMD -d '$JSON_STRING'"
fi

# Execute the curl command
echo "Executing: $CURL_CMD"
eval $CURL_CMD

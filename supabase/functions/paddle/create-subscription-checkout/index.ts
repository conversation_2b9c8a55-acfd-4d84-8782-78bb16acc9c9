import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

interface CreateSubscriptionCheckoutRequest {
  plan_id: string // Subscription plan ID from our database
  customer_email?: string
  success_url?: string
  cancel_url?: string
}

interface PaddleSubscriptionResponse {
  data: {
    id: string
    status: string
    checkout?: {
      url: string | null
    }
  }
  meta: {
    request_id: string
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  if (req.method !== 'POST') {
    return new Response('Method not allowed', { 
      status: 405, 
      headers: corsHeaders 
    })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const { plan_id, customer_email, success_url, cancel_url }: CreateSubscriptionCheckoutRequest = await req.json()

    if (!plan_id) {
      return new Response(
        JSON.stringify({ error: 'plan_id is required' }),
        { status: 400, headers: corsHeaders }
      )
    }

    // Get user from auth header
    const authHeader = req.headers.get('authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Authorization header required' }),
        { status: 401, headers: corsHeaders }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: userError } = await supabase.auth.getUser(token)
    
    if (userError || !user) {
      return new Response(
        JSON.stringify({ error: 'Invalid token' }),
        { status: 401, headers: corsHeaders }
      )
    }

    // Get subscription plan details
    const { data: plan, error: planError } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('id', plan_id)
      .eq('is_active', true)
      .single()

    if (planError || !plan) {
      return new Response(
        JSON.stringify({ error: 'Invalid plan_id' }),
        { status: 400, headers: corsHeaders }
      )
    }

    // Create Paddle subscription checkout
    const paddleApiKey = Deno.env.get('PADDLE_API_KEY')
    if (!paddleApiKey) {
      throw new Error('PADDLE_API_KEY not configured')
    }

    // Map our plan to Paddle product ID
    const paddleProductId = getPaddleProductId(plan.name)
    
    const checkoutData = {
      items: [
        {
          price_id: paddleProductId,
          quantity: 1
        }
      ],
      customer_email: customer_email || user.email,
      custom_data: {
        user_id: user.id,
        plan_id: plan_id,
        plan_name: plan.name
      },
      checkout_settings: {
        success_url: success_url || `${req.headers.get('origin')}/app/subscription?success=true`,
        cancel_url: cancel_url || `${req.headers.get('origin')}/app/subscription?cancelled=true`
      }
    }

    console.log('Creating Paddle subscription checkout:', { 
      user_id: user.id, 
      plan_name: plan.name,
      paddle_product_id: paddleProductId
    })

    const paddleResponse = await fetch('https://api.paddle.com/transactions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${paddleApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(checkoutData)
    })

    if (!paddleResponse.ok) {
      const errorData = await paddleResponse.text()
      console.error('Paddle API error:', errorData)
      throw new Error(`Paddle API error: ${paddleResponse.status}`)
    }

    const paddleData: PaddleSubscriptionResponse = await paddleResponse.json()

    return new Response(
      JSON.stringify({
        success: true,
        checkout_url: paddleData.data.checkout?.url,
        transaction_id: paddleData.data.id,
        plan_name: plan.name
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      }
    )

  } catch (error) {
    console.error('Subscription checkout error:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error.message 
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
})

// Helper function to map our plan names to Paddle product IDs
function getPaddleProductId(planName: string): string {
  const isProduction = Deno.env.get('ENVIRONMENT') === 'production'
  
  if (isProduction) {
    // Production Paddle product IDs
    switch (planName) {
      case 'Basic':
        return Deno.env.get('PADDLE_BASIC_PRICE_ID_PRODUCTION') ?? ''
      case 'Pro':
        return Deno.env.get('PADDLE_PRO_PRICE_ID_PRODUCTION') ?? ''
      case 'Premium':
        return Deno.env.get('PADDLE_PREMIUM_PRICE_ID_PRODUCTION') ?? ''
      default:
        throw new Error(`Unknown plan: ${planName}`)
    }
  } else {
    // Sandbox Paddle product IDs
    switch (planName) {
      case 'Basic':
        return Deno.env.get('PADDLE_BASIC_PRICE_ID_SANDBOX') ?? ''
      case 'Pro':
        return Deno.env.get('PADDLE_PRO_PRICE_ID_SANDBOX') ?? ''
      case 'Premium':
        return Deno.env.get('PADDLE_PREMIUM_PRICE_ID_SANDBOX') ?? ''
      default:
        throw new Error(`Unknown plan: ${planName}`)
    }
  }
}

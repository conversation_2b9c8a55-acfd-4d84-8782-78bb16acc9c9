import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, x-cron-secret',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

interface PaddleInvoiceResponse {
  data: {
    id: string
    checkout_url: string
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  if (req.method !== 'POST') {
    return new Response('Method not allowed', { 
      status: 405, 
      headers: corsHeaders 
    })
  }

  try {
    // Verify cron secret for security
    const cronSecret = req.headers.get('x-cron-secret')
    const expectedSecret = Deno.env.get('CRON_SECRET')
    
    if (!cronSecret || cronSecret !== expectedSecret) {
      console.error('Invalid or missing cron secret')
      return new Response('Unauthorized', { 
        status: 401, 
        headers: corsHeaders 
      })
    }

    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    console.log('Starting monthly PAYG billing process...')

    // Get all unpaid PAYG balances from previous months
    const { data: unpaidBalances, error: balancesError } = await supabase.rpc(
      'paddle.get_unpaid_payg_balances'
    )

    if (balancesError) {
      console.error('Error fetching unpaid balances:', balancesError)
      return new Response(
        JSON.stringify({ error: 'Failed to fetch unpaid balances' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    if (!unpaidBalances || unpaidBalances.length === 0) {
      console.log('No unpaid PAYG balances found')
      return new Response(
        JSON.stringify({ 
          success: true, 
          message: 'No unpaid balances to process',
          processed: 0
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    console.log(`Found ${unpaidBalances.length} unpaid PAYG balances to process`)

    const results = []
    let successCount = 0
    let errorCount = 0

    // Process each unpaid balance
    for (const balance of unpaidBalances) {
      try {
        console.log(`Processing PAYG billing for user ${balance.user_id}, month ${balance.month}, amount $${balance.total_amount}`)

        // Skip if amount is too small (less than $1)
        if (parseFloat(balance.total_amount) < 1.0) {
          console.log(`Skipping small amount: $${balance.total_amount}`)
          
          // Mark as paid since it's too small to bill
          await supabase
            .from('payg_usage')
            .update({ 
              payment_status: 'paid',
              payment_metadata: {
                reason: 'amount_too_small',
                processed_at: new Date().toISOString()
              }
            })
            .eq('user_id', balance.user_id)
            .eq('month', balance.month)

          results.push({
            user_id: balance.user_id,
            month: balance.month,
            amount: balance.total_amount,
            status: 'skipped_small_amount'
          })
          continue
        }

        // Create Paddle invoice
        const invoiceResult = await createPaddleInvoice(
          balance.user_id,
          balance.user_email,
          balance.user_name,
          parseFloat(balance.total_amount),
          balance.month
        )

        if (invoiceResult.success) {
          // Create transaction record in our database
          const { error: invoiceError } = await supabase.rpc(
            'paddle.create_payg_invoice',
            {
              p_user_id: balance.user_id,
              p_month: balance.month,
              p_amount: parseFloat(balance.total_amount)
            }
          )

          if (invoiceError) {
            console.error('Error creating invoice record:', invoiceError)
            errorCount++
            results.push({
              user_id: balance.user_id,
              month: balance.month,
              amount: balance.total_amount,
              status: 'error',
              error: invoiceError.message
            })
          } else {
            successCount++
            results.push({
              user_id: balance.user_id,
              month: balance.month,
              amount: balance.total_amount,
              status: 'invoice_created',
              paddle_invoice_id: invoiceResult.invoice_id
            })
          }
        } else {
          console.error('Error creating Paddle invoice:', invoiceResult.error)
          errorCount++
          results.push({
            user_id: balance.user_id,
            month: balance.month,
            amount: balance.total_amount,
            status: 'error',
            error: invoiceResult.error
          })
        }

        // Add small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100))

      } catch (error) {
        console.error('Error processing balance:', error)
        errorCount++
        results.push({
          user_id: balance.user_id,
          month: balance.month,
          amount: balance.total_amount,
          status: 'error',
          error: error.message
        })
      }
    }

    console.log(`Monthly PAYG billing completed. Success: ${successCount}, Errors: ${errorCount}`)

    return new Response(
      JSON.stringify({
        success: true,
        processed: unpaidBalances.length,
        successful: successCount,
        errors: errorCount,
        results
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      }
    )

  } catch (error) {
    console.error('Monthly billing process error:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error.message 
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
})

// Function to create Paddle invoice
async function createPaddleInvoice(
  userId: string,
  userEmail: string,
  userName: string,
  amount: number,
  month: string
): Promise<{ success: boolean; invoice_id?: string; error?: string }> {
  try {
    const paddleApiKey = Deno.env.get('PADDLE_API_KEY')
    const paddleEnvironment = Deno.env.get('PADDLE_ENVIRONMENT') || 'sandbox'
    const baseUrl = paddleEnvironment === 'production' 
      ? 'https://api.paddle.com' 
      : 'https://sandbox-api.paddle.com'

    // Create invoice data
    const invoiceData = {
      customer: {
        email: userEmail,
        name: userName || undefined
      },
      items: [
        {
          product_id: Deno.env.get('PADDLE_PAYG_PRODUCT_ID') || 'prod_voicehype_payg',
          quantity: 1,
          price: {
            amount: (amount * 100).toString(), // Convert to cents
            currency_code: 'USD'
          }
        }
      ],
      custom_data: {
        user_id: userId,
        billing_month: month,
        invoice_type: 'payg_monthly'
      },
      collection_mode: 'automatic', // Automatically charge the customer
      billing_details: {
        enable_checkout: true,
        purchase_order_number: `PAYG-${userId}-${month}`
      }
    }

    console.log('Creating Paddle invoice:', {
      user_id: userId,
      amount,
      month
    })

    const response = await fetch(`${baseUrl}/invoices`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${paddleApiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(invoiceData)
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('Paddle API error:', {
        status: response.status,
        statusText: response.statusText,
        body: errorText
      })
      
      return {
        success: false,
        error: `Paddle API error: ${response.status} ${response.statusText}`
      }
    }

    const invoiceResponse: PaddleInvoiceResponse = await response.json()

    return {
      success: true,
      invoice_id: invoiceResponse.data.id
    }

  } catch (error) {
    console.error('Error creating Paddle invoice:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

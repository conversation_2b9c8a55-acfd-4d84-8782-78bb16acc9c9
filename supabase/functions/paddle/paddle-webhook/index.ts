import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, paddle-signature',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

interface PaddleWebhookEvent {
  event_id: string
  event_type: string
  occurred_at: string
  data: {
    id: string
    status?: string
    customer_id?: string
    email?: string
    name?: string
    customer?: {
      id?: string
      email?: string
      name?: string
    }
    billing_details?: {
      email?: string
      name?: string
    }
    customer_details?: {
      email?: string
      name?: string
    }
    items?: Array<{
      product_id: string
      quantity: number
    }>
    details?: {
      totals?: {
        total: string
        currency_code: string
      }
    }
    receipt_url?: string
    [key: string]: any
  }
}

// Verify Paddle webhook signature
async function verifyPaddleSignature(
  body: string,
  signature: string,
  webhookSecret: string
): Promise<boolean> {
  try {
    // Extract timestamp and signature from header
    const parts = signature.split(';')
    let timestamp = ''
    let signatures: string[] = []
    
    for (const part of parts) {
      const [key, value] = part.split('=')
      if (key === 'ts') {
        timestamp = value
      } else if (key === 'h1') {
        signatures.push(value)
      }
    }
    
    if (!timestamp || signatures.length === 0) {
      return false
    }
    
    // Create the signed payload
    const signedPayload = `${timestamp};${body}`
    
    // Create HMAC signature
    const encoder = new TextEncoder()
    const key = await crypto.subtle.importKey(
      'raw',
      encoder.encode(webhookSecret),
      { name: 'HMAC', hash: 'SHA-256' },
      false,
      ['sign']
    )
    
    const signature_bytes = await crypto.subtle.sign(
      'HMAC',
      key,
      encoder.encode(signedPayload)
    )
    
    // Convert to hex
    const expectedSignature = Array.from(new Uint8Array(signature_bytes))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('')
    
    // Compare signatures
    return signatures.some(sig => sig === expectedSignature)
  } catch (error) {
    console.error('Error verifying Paddle signature:', error)
    return false
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  if (req.method !== 'POST') {
    return new Response('Method not allowed', { 
      status: 405, 
      headers: corsHeaders 
    })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const body = await req.text()
    const signature = req.headers.get('paddle-signature') || ''
    const webhookSecret = Deno.env.get('PADDLE_WEBHOOK_SECRET') || ''

    console.log('Received Paddle webhook:', {
      signature: signature ? 'present' : 'missing',
      bodyLength: body.length
    })

    // Verify webhook signature in production
    if (Deno.env.get('ENVIRONMENT') === 'production') {
      const isValid = await verifyPaddleSignature(body, signature, webhookSecret)
      if (!isValid) {
        console.error('Invalid Paddle webhook signature')
        return new Response('Invalid signature', { 
          status: 401, 
          headers: corsHeaders 
        })
      }
    }

    const event: PaddleWebhookEvent = JSON.parse(body)
    
    console.log('Processing Paddle event:', {
      event_id: event.event_id,
      event_type: event.event_type,
      data_id: event.data?.id,
      customer_id: event.data?.customer_id,
      customer_email: event.data?.email || event.data?.customer?.email,
      occurred_at: event.occurred_at
    })

    // Log webhook event
    const { error: logError } = await supabase
      .rpc('create_paddle_webhook_log', {
        p_event_id: event.event_id,
        p_event_type: event.event_type,
        p_payload: event,
        p_processed: false
      })

    if (logError) {
      console.error('Error logging webhook:', logError)
    }

    // Process different event types
    let processed = false
    let processingError = null

    try {
      switch (event.event_type) {
        case 'transaction.completed':
          processed = await handleTransactionCompleted(supabase, event)
          break
          
        case 'transaction.updated':
          processed = await handleTransactionUpdated(supabase, event)
          break
          
        case 'customer.created':
        case 'customer.updated':
          processed = await handleCustomerEvent(supabase, event)
          break
          
        default:
          console.log(`Unhandled event type: ${event.event_type}`)
          processed = true // Mark as processed to avoid retries
      }
    } catch (error) {
      console.error('Error processing webhook:', error)
      processingError = error.message
      processed = false
    }

    // Update webhook log
    await supabase
      .rpc('update_paddle_webhook_status', {
        p_event_id: event.event_id,
        p_processed: processed,
        p_error: processingError
      })

    return new Response(
      JSON.stringify({ 
        success: processed,
        event_id: event.event_id,
        event_type: event.event_type
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: processed ? 200 : 500
      }
    )

  } catch (error) {
    console.error('Webhook processing error:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
})

// Handle completed transactions (credit purchases)
async function handleTransactionCompleted(supabase: any, event: PaddleWebhookEvent): Promise<boolean> {
  const { data } = event
  
  if (!data.customer_id || !data.details?.totals) {
    console.error('Missing required transaction data:', {
      has_customer_id: !!data.customer_id,
      has_totals: !!data.details?.totals,
      data_keys: Object.keys(data)
    })
    return false
  }

  // Amount comes from Paddle in dollars, convert to cents for processing
  const amount = Math.round(parseFloat(data.details.totals.total) * 100)
  const currency = data.details.totals.currency_code
  const productId = data.items?.[0]?.product_id || null

  // Extract customer email from various possible locations in the webhook data
  const customerEmail = data.email || 
                       data.customer?.email || 
                       data.billing_details?.email ||
                       data.customer_details?.email ||
                       null

  // Enhanced metadata with customer information
  const enrichedMetadata = {
    ...data,
    extracted_customer_info: {
      email: customerEmail,
      name: data.name || data.customer?.name || null,
      customer_id: data.customer_id
    },
    webhook_received_at: new Date().toISOString(),
    event_id: event.event_id
  }

  console.log('Processing completed transaction:', {
    transaction_id: data.id,
    customer_id: data.customer_id,
    customer_email: customerEmail,
    amount_cents: amount,
    amount_dollars: amount / 100,
    currency,
    product_id: productId,
    webhook_event_id: event.event_id
  })

  // Call the database function to process the transaction
  const { data: result, error } = await supabase.rpc(
    'process_completed_transaction',
    {
      p_paddle_transaction_id: data.id,
      p_paddle_customer_id: data.customer_id,
      p_amount: amount,
      p_currency: currency,
      p_product_id: productId,
      p_receipt_url: data.receipt_url || null,
      p_metadata: enrichedMetadata
    }
  )

  if (error) {
    console.error('Error processing completed transaction:', {
      error,
      transaction_id: data.id,
      customer_id: data.customer_id,
      customer_email: customerEmail,
      amount: amount
    })
    
    // Check if this is a security rejection (no existing user)
    if (error.message?.includes('No existing VoiceHype user found')) {
      console.warn('SECURITY: Transaction rejected - attempted purchase by non-existing user:', {
        customer_id: data.customer_id,
        email: customerEmail,
        transaction_id: data.id,
        amount_dollars: amount / 100
      })
    }
    
    return false
  }

  console.log('Successfully processed completed transaction:', {
    transaction_id: data.id,
    customer_id: data.customer_id,
    credits_added: amount / 100
  })
  return true
}

// Handle transaction updates
async function handleTransactionUpdated(supabase: any, event: PaddleWebhookEvent): Promise<boolean> {
  const { data } = event
  
  console.log('Processing transaction update:', {
    transaction_id: data.id,
    status: data.status,
    webhook_event_id: event.event_id
  })

  // If the transaction is now completed, process it as a completed transaction
  if (data.status === 'completed') {
    console.log('Transaction updated to completed status, processing as completed transaction')
    return await handleTransactionCompleted(supabase, event)
  }

  // For other status updates, just update the transaction record
  const { error } = await supabase
    .rpc('update_paddle_transaction', {
      p_transaction_id: data.id,
      p_status: data.status,
      p_metadata: {
        ...data,
        webhook_event_id: event.event_id,
        updated_at: new Date().toISOString()
      }
    })

  if (error) {
    console.error('Error updating transaction:', {
      error,
      transaction_id: data.id,
      status: data.status
    })
    return false
  }

  console.log('Successfully updated transaction status:', {
    transaction_id: data.id,
    new_status: data.status
  })
  return true
}

// Handle customer events
async function handleCustomerEvent(supabase: any, event: PaddleWebhookEvent): Promise<boolean> {
  const { data } = event
  
  console.log('Processing customer event:', {
    customer_id: data.id,
    email: data.email
  })

  // We'll handle customer creation/updates when processing transactions
  // This is mainly for logging purposes
  return true
}

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

interface CreateCheckoutRequest {
  amount: number // Credit amount to purchase ($5-$95)
  currency?: string
  customer_email?: string
  success_url?: string
  cancel_url?: string
}

interface PaddleTransactionResponse {
  data: {
    id: string
    status: string
    checkout?: {
      url: string | null
    }
    // Add other fields as needed based on the API response
  }
  meta: {
    request_id: string
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  if (req.method !== 'POST') {
    return new Response('Method not allowed', { 
      status: 405, 
      headers: corsHeaders 
    })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get authenticated user
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Missing authorization header' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const { data: { user }, error: authError } = await supabase.auth.getUser(
      authHeader.replace('Bearer ', '')
    )

    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Invalid authentication' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const requestData: CreateCheckoutRequest = await req.json()
    
    // Validate amount (between $5 and $95)
    if (!requestData.amount || requestData.amount < 5 || requestData.amount > 95) {
      return new Response(
        JSON.stringify({ error: 'Amount must be between $5 and $95' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('email, full_name')
      .eq('id', user.id)
      .single()

    if (profileError) {
      console.error('Error fetching user profile:', profileError)
      return new Response(
        JSON.stringify({ error: 'User profile not found' }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Get VoiceHype product from database using RPC function to access the paddle schema
    const { data: products, error: productError } = await supabase
      .rpc('query_paddle_products', { 
        product_type: 'credits', 
        is_active_status: true 
      })

    if (productError || !products || products.length === 0) {
      console.error('Error fetching product:', productError)
      return new Response(
        JSON.stringify({ error: 'Product configuration error' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Prepare Paddle API request
    const paddleApiKey = Deno.env.get('PADDLE_API_KEY')
    const paddleEnvironment = Deno.env.get('PADDLE_ENVIRONMENT') || 'sandbox'
    const baseUrl = paddleEnvironment === 'production' 
      ? 'https://api.paddle.com' 
      : 'https://sandbox-api.paddle.com'

    // Format data for Paddle Billing API v2 transactions endpoint
    const checkoutData = {
      items: [
        {
          price_id: products[0].paddle_product_id, // This should be a valid Paddle price ID (pri_*)
          quantity: 1
        }
      ],
      // If you have a stored customer ID in paddle.customers table, use that
      // Otherwise, you'll need to create a customer first or use customer details
      customer_id: null, // Replace with actual customer ID if available
      address_id: null, // Replace with actual address ID if available
      currency_code: requestData.currency || 'USD',
      collection_mode: 'automatic', // For self-serve checkout
      custom_data: {
        user_id: user.id,
        credit_amount: requestData.amount
      },
      checkout: {
        url: requestData.success_url || `${Deno.env.get('FRONTEND_URL')}/payments`
      }
    }

    console.log('Creating Paddle checkout:', {
      user_id: user.id,
      amount: requestData.amount,
      product_id: products[0].paddle_product_id
    })

    // Create transaction with Paddle v2 API
    const paddleResponse = await fetch(`${baseUrl}/transactions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${paddleApiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(checkoutData)
    })

    if (!paddleResponse.ok) {
      const errorText = await paddleResponse.text()
      console.error('Paddle API error:', {
        status: paddleResponse.status,
        statusText: paddleResponse.statusText,
        body: errorText
      })
      
      return new Response(
        JSON.stringify({ 
          error: 'Failed to create checkout',
          details: paddleResponse.status === 401 ? 'Invalid API key' : 'Paddle API error'
        }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const transactionResponse = await paddleResponse.json()

    // Create or update Paddle customer record
    try {
      await supabase.rpc('store_paddle_customer_info', {
        p_user_id: user.id,
        p_email: profile.email,
        p_name: profile.full_name || ''
      })
    } catch (error) {
      console.error('Error creating/updating customer:', error)
      // Don't fail the checkout for this
    }

    console.log('Successfully created Paddle transaction:', {
      transaction_id: transactionResponse.data.id,
      status: transactionResponse.data.status,
      checkout_url: transactionResponse.data.checkout?.url
    })

    return new Response(
      JSON.stringify({
        success: true,
        checkout_url: transactionResponse.data.checkout?.url,
        transaction_id: transactionResponse.data.id,
        amount: requestData.amount,
        currency: requestData.currency || 'USD'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      }
    )

  } catch (error) {
    console.error('Checkout creation error:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error.message 
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
})

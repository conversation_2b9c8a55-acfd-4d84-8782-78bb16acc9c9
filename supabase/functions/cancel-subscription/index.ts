// @deno-types="https://deno.land/x/supabase/mod.ts"
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// Declare Deno for TypeScript
declare const Deno: any

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req: Request) => {
  console.log('🚀 Cancel subscription function started')
  
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    console.log('✅ CORS preflight handled')
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('📝 Parsing request body...')
    
    // Get environment-specific Paddle API key
    const paddleEnvironment = Deno.env.get('PADDLE_ENVIRONMENT') || 'sandbox'
    const paddleApiKey = paddleEnvironment === 'production' 
      ? Deno.env.get('PADDLE_API_KEY_PRODUCTION')
      : Deno.env.get('PADDLE_API_KEY_SANDBOX')
    
    console.log(`🔧 Using Paddle ${paddleEnvironment} environment`)
    console.log(`🔑 API Key starts with: ${paddleApiKey?.substring(0, 10)}...`)
    console.log(`🔑 API Key length: ${paddleApiKey?.length}`)
    
    if (!paddleApiKey) {
      console.error(`❌ Missing Paddle API key for ${paddleEnvironment} environment`)
      return new Response(
        JSON.stringify({ error: 'Paddle API configuration error' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }
    
    // Get the action from request body
    let action: 'cancel' | 'undo' = 'cancel'
    
    if (req.method === 'POST') {
      try {
        const contentType = req.headers.get('content-type')
        console.log(`🔍 Content-Type: ${contentType}`)
        
        // First check if there's actually a body
        const bodyText = await req.text()
        console.log(`🔍 Raw body text:`, bodyText)
        
        if (bodyText && bodyText.trim().length > 0) {
          const body = JSON.parse(bodyText)
          console.log(`🔍 Parsed body:`, body)
          
          action = body.action || 'cancel'
          console.log(`✅ Action parsed: ${action}`)
        } else {
          console.log('⚠️ Empty body, defaulting to cancel')
        }
      } catch (parseError) {
        // If no body or invalid JSON, default to 'cancel'
        action = 'cancel'
        console.log('⚠️ Body parse error, defaulting to cancel:', parseError)
      }
    }

    console.log('🔧 Creating Supabase client...')
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL')!,
      Deno.env.get('SUPABASE_ANON_KEY')!
    )
    console.log('✅ Supabase client created')

    // 1. Verify JWT and get user
    console.log('🔐 Verifying JWT token...')
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      console.log('❌ No authorization header')
      return new Response(
        JSON.stringify({ error: 'Missing authorization header' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      console.log('❌ JWT verification failed:', authError)
      return new Response(
        JSON.stringify({ error: 'Invalid or expired token' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }
    
    console.log('✅ JWT verified for user:', user.id)

    // 2. Create authenticated Supabase client with the user's JWT
    console.log('🔧 Creating authenticated Supabase client...')
    const authenticatedSupabase = createClient(
      Deno.env.get('SUPABASE_URL')!,
      Deno.env.get('SUPABASE_ANON_KEY')!,
      {
        global: {
          headers: {
            Authorization: authHeader
          }
        }
      }
    )
    console.log('✅ Authenticated Supabase client created')
    
    console.log('🔍 Querying user subscriptions with RLS...')
    
    // First, let's see ALL subscriptions for this user for debugging (using authenticated client)
    console.log('🔍 DEBUG: Getting all subscriptions with RLS...')
    const { data: allSubs, error: allSubsError } = await authenticatedSupabase
      .from('user_subscriptions')
      .select('*')
    
    console.log('🔍 DEBUG: All subscriptions found with RLS:', allSubs)
    console.log('🔍 DEBUG: All subscriptions error with RLS:', allSubsError)
    
    // 3. Get subscription using the same pattern as the website (with RLS)
    const { data: subscription, error: subError } = await authenticatedSupabase
      .from('user_subscriptions')
      .select('id, paddle_subscription_id, user_id, status, cancel_at_period_end')
      .eq('status', 'active')
      .order('current_period_end', { ascending: false })
      .limit(1)
      .single()

    console.log('🔍 DEBUG: Active subscription query result with RLS:', subscription)
    console.log('🔍 DEBUG: Active subscription query error with RLS:', subError)

    if (subError || !subscription) {
      console.log('❌ No active subscription found. SubError:', subError)
      console.log('❌ Subscription data:', subscription)
      return new Response(
        JSON.stringify({ 
          error: 'No active subscription found for user',
          debug: {
            userId: user.id,
            allSubscriptions: allSubs,
            subError: subError,
            subscription: subscription
          }
        }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // 3. Handle different actions
    if (action === 'cancel') {
      return await handleCancellation(authenticatedSupabase, subscription, paddleApiKey)
    } else if (action === 'undo') {
      return await handleUndoCancellation(authenticatedSupabase, subscription, paddleApiKey)
    } else {
      return new Response(
        JSON.stringify({ error: 'Invalid action. Must be "cancel" or "undo"' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

  } catch (error) {
    console.error('Subscription management error:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})

// Handle cancellation
async function handleCancellation(supabase: any, subscription: any, paddleApiKey: string) {
  console.log('🔧 Starting cancellation process...')
  console.log('📊 Subscription data:', subscription)
  
  // Get Paddle environment for API URL
  const paddleEnvironment = Deno.env.get('PADDLE_ENVIRONMENT') || 'sandbox'
  
  // Check if already scheduled for cancellation
  if (subscription.cancel_at_period_end) {
    console.log('⚠️ Subscription already scheduled for cancellation')
    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Subscription already scheduled for cancellation',
        already_cancelled: true
      }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  // Get the Paddle subscription ID
  const paddleSubId = subscription.paddle_subscription_id
  if (!paddleSubId) {
    console.log('❌ No paddle subscription ID found. Available ID:', {
      paddle_subscription_id: subscription.paddle_subscription_id,
      id: subscription.id
    })
    return new Response(
      JSON.stringify({ 
        error: 'No Paddle subscription ID found for this subscription',
        debug: {
          subscription_id: subscription.id,
          paddle_subscription_id: subscription.paddle_subscription_id
        }
      }),
      { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  // Validate that we have a proper subscription ID format
  if (!paddleSubId.startsWith('sub_')) {
    console.log('❌ Invalid subscription ID format:', {
      subscription_id: paddleSubId,
      expected_format: 'sub_xxxxxxxxxx'
    })
    return new Response(
      JSON.stringify({ 
        error: 'Invalid subscription ID format',
        debug: {
          subscription_id: paddleSubId,
          expected_format: 'sub_xxxxxxxxxx'
        }
      }),
      { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  console.log(`🎯 Calling Paddle API to cancel subscription: ${paddleSubId}`)
  console.log(`📋 Using paddle_subscription_id field`)
  
  // Use environment-specific API base URL
  const apiBaseUrl = paddleEnvironment === 'production' 
    ? 'https://api.paddle.com' 
    : 'https://sandbox-api.paddle.com'
  const cancelUrl = `${apiBaseUrl}/subscriptions/${paddleSubId}/cancel`
  console.log(`🌐 Full Paddle API URL: ${cancelUrl} (${paddleEnvironment})`)
  
  // Call Paddle API to schedule cancellation
  const paddleResponse = await fetch(
    cancelUrl,
    {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${paddleApiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        effective_from: 'next_billing_period'
      })
    }
  )

  console.log('📡 Paddle API response status:', paddleResponse.status)

  if (!paddleResponse.ok) {
    const errorText = await paddleResponse.text()
    console.error('❌ Paddle API error:', errorText)
    
    return new Response(
      JSON.stringify({ 
        error: 'Failed to cancel subscription with payment processor',
        debug: {
          paddle_response_status: paddleResponse.status,
          paddle_error: errorText,
          subscription_id: paddleSubId
        }
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  console.log('✅ Paddle API call successful')

  // Update local database for immediate feedback (using SERVICE ROLE to bypass RLS)
  console.log('💾 Updating local database with service role...')
  const serviceSupabase = createClient(
    Deno.env.get('SUPABASE_URL')!,
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
  )
  
  const { error: updateError } = await serviceSupabase
    .from('user_subscriptions')
    .update({ cancel_at_period_end: true })
    .eq('id', subscription.id)

  if (updateError) {
    console.error('❌ Database update error:', updateError)
    console.error('❌ Full error details:', JSON.stringify(updateError, null, 2))
  } else {
    console.log('✅ Database updated successfully')
    
    // Verify the update by reading back the record
    const { data: updatedSub, error: verifyError } = await serviceSupabase
      .from('user_subscriptions')
      .select('cancel_at_period_end')
      .eq('id', subscription.id)
      .single()
    
    console.log('🔍 Verification - Updated subscription:', updatedSub)
    if (verifyError) {
      console.error('❌ Verification error:', verifyError)
    }
  }

  return new Response(
    JSON.stringify({
      success: true,
      message: 'Subscription scheduled for cancellation at period end'
    }),
    { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

// Handle undo cancellation
async function handleUndoCancellation(supabase: any, subscription: any, paddleApiKey: string) {
  console.log('🔧 Starting undo cancellation process...')
  console.log('📊 Subscription data:', subscription)
  
  // Get Paddle environment for API URL
  const paddleEnvironment = Deno.env.get('PADDLE_ENVIRONMENT') || 'sandbox'
  
  // Check if subscription is actually scheduled for cancellation
  if (!subscription.cancel_at_period_end) {
    console.log('⚠️ Subscription is not scheduled for cancellation')
    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Subscription is not scheduled for cancellation',
        not_cancelled: true
      }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  // Get the Paddle subscription ID
  const paddleSubId = subscription.paddle_subscription_id
  if (!paddleSubId) {
    console.log('❌ No paddle subscription ID found for undo')
    return new Response(
      JSON.stringify({ 
        error: 'No Paddle subscription ID found for this subscription',
        debug: {
          subscription_id: subscription.id,
          paddle_subscription_id: subscription.paddle_subscription_id
        }
      }),
      { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  // Validate that we have a proper subscription ID format
  if (!paddleSubId.startsWith('sub_')) {
    console.log('❌ Invalid subscription ID format for undo:', {
      subscription_id: paddleSubId,
      expected_format: 'sub_xxxxxxxxxx'
    })
    return new Response(
      JSON.stringify({ 
        error: 'Invalid subscription ID format',
        debug: {
          subscription_id: paddleSubId,
          expected_format: 'sub_xxxxxxxxxx'
        }
      }),
      { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  console.log(`🎯 Calling Paddle API to undo cancellation: ${paddleSubId}`)

  // Use environment-specific API base URL
  const apiBaseUrl = paddleEnvironment === 'production' 
    ? 'https://api.paddle.com' 
    : 'https://sandbox-api.paddle.com'

  // Call Paddle API to remove scheduled cancellation
  const paddleResponse = await fetch(
    `${apiBaseUrl}/subscriptions/${paddleSubId}`,
    {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${paddleApiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        scheduled_change: null // Remove the scheduled cancellation
      })
    }
  )

  console.log('📡 Paddle API undo response status:', paddleResponse.status)

  if (!paddleResponse.ok) {
    const errorText = await paddleResponse.text()
    console.error('❌ Paddle API undo error:', errorText)
    
    return new Response(
      JSON.stringify({ 
        error: 'Failed to undo cancellation with payment processor',
        debug: {
          paddle_response_status: paddleResponse.status,
          paddle_error: errorText,
          subscription_id: paddleSubId
        }
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  console.log('✅ Paddle API undo call successful')

  // Update local database for immediate feedback (using SERVICE ROLE to bypass RLS)
  console.log('💾 Updating local database for undo with service role...')
  const serviceSupabase = createClient(
    Deno.env.get('SUPABASE_URL')!,
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
  )
  
  const { error: updateError } = await serviceSupabase
    .from('user_subscriptions')
    .update({ cancel_at_period_end: false })
    .eq('id', subscription.id)

  if (updateError) {
    console.error('❌ Database undo update error:', updateError)
    console.error('❌ Full undo error details:', JSON.stringify(updateError, null, 2))
  } else {
    console.log('✅ Database undo updated successfully')
    
    // Verify the update by reading back the record
    const { data: updatedSub, error: verifyError } = await serviceSupabase
      .from('user_subscriptions')
      .select('cancel_at_period_end')
      .eq('id', subscription.id)
      .single()
    
    console.log('🔍 Undo Verification - Updated subscription:', updatedSub)
    if (verifyError) {
      console.error('❌ Undo Verification error:', verifyError)
    }
  }

  return new Response(
    JSON.stringify({
      success: true,
      message: 'Subscription cancellation has been undone'
    }),
    { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

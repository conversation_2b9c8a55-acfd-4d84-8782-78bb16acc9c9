import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

interface UpgradeRequest {
  targetPlan: 'Basic' | 'Pro' | 'Premium'
  upgradeFlow: 'update_next_billing' | 'upgrade_now_merge_quotas'
  priceId: string
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  if (req.method !== 'POST') {
    return new Response('Method not allowed', { 
      status: 405, 
      headers: corsHeaders 
    })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get user session
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'No authorization header' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: userError } = await supabase.auth.getUser(token)
    
    if (userError || !user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const { targetPlan, upgradeFlow, priceId }: UpgradeRequest = await req.json()

    // Get user's current subscription
    const { data: currentSubscription, error: subError } = await supabase
      .from('user_subscriptions')
      .select('*')
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single()

    if (subError || !currentSubscription) {
      return new Response(
        JSON.stringify({ error: 'No active subscription found' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Get target plan details
    const { data: targetPlanData, error: planError } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('name', targetPlan)
      .single()

    if (planError || !targetPlanData) {
      return new Response(
        JSON.stringify({ error: 'Target plan not found' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    console.log('Target plan details:', {
      name: targetPlanData.name,
      transcription_minutes: targetPlanData.transcription_minutes,
      tokens: targetPlanData.tokens
    })

    // New implementation: Use plan-specific price ID from environment variables and quantity = 1
    const getPriceIdForPlan = (planName: string, environment: string) => {
      const priceIdMap = {
        'Basic': environment === 'production' 
          ? Deno.env.get('PADDLE_PRICE_ID_BASIC_PRODUCTION') 
          : Deno.env.get('PADDLE_PRICE_ID_BASIC_SANDBOX'),
        'Pro': environment === 'production' 
          ? Deno.env.get('PADDLE_PRICE_ID_PRO_PRODUCTION') 
          : Deno.env.get('PADDLE_PRICE_ID_PRO_SANDBOX'),
        'Premium': environment === 'production' 
          ? Deno.env.get('PADDLE_PRICE_ID_PREMIUM_PRODUCTION') 
          : Deno.env.get('PADDLE_PRICE_ID_PREMIUM_SANDBOX')
      }
      return priceIdMap[planName]
    }

    // Get environment and determine price ID for the target plan
    const environmentVar = Deno.env.get('PADDLE_ENVIRONMENT') || 'sandbox'
    const priceIdForPlan = getPriceIdForPlan(targetPlan, environmentVar)
    const quantity = 1

    console.log('🔧 Plan-specific price ID billing:', {
      target_plan: targetPlan,
      price_id: priceIdForPlan,
      quantity: quantity,
      note: 'Using separate price IDs with quantity 1 for proper billing'
    })

    // Validate that we have a valid price ID
    if (!priceIdForPlan) {
      return new Response(
        JSON.stringify({ error: `Price ID not configured for ${targetPlan} in ${environmentVar} environment` }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Get environment and Paddle API key
    const environment = environmentVar
    const paddleApiKey = environment === 'production' 
      ? Deno.env.get('PADDLE_API_KEY_PRODUCTION')
      : Deno.env.get('PADDLE_API_KEY_SANDBOX')

    if (!paddleApiKey) {
      return new Response(
        JSON.stringify({ error: 'Paddle API key not configured' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Determine proration billing mode based on upgrade flow
    // FIXED: Use proper proration for immediate upgrades with quota merging
    const prorationMode = upgradeFlow === 'update_next_billing' 
      ? 'do_not_bill'    // Prorated amount isn't calculated. The customer isn't billed for the prorated amount or the full amount.
      : 'full_immediately'     // Immediate upgrade with proper proration and quota merge

    // Prepare custom data for webhook processing
    const customData: any = {
      user_id: user.id,
      subscription_plan: targetPlan.toLowerCase(),
      // Include quota details from the target plan
      transcription_minutes: targetPlanData.transcription_minutes,
      tokens: targetPlanData.tokens
    }

    // Add upgrade metadata for ALL upgrades (both immediate and scheduled)
    // This is critical for webhook to handle subscription ID replacement
    customData.upgrade_metadata = {
      upgrade_flow: upgradeFlow,
      user_id: user.id,
      old_subscription_id: currentSubscription.paddle_subscription_id, // Critical for webhook
      target_plan: targetPlan,
      upgrade_timestamp: new Date().toISOString(),
      transcription_minutes: targetPlanData.transcription_minutes,
      tokens: targetPlanData.tokens
    }

    // Use environment-specific API base URL
    const apiBaseUrl = environment === 'production' 
      ? 'https://api.paddle.com' 
      : 'https://sandbox-api.paddle.com'
    
    // Build new items array with ONLY the target subscription item
    // Voice Hype architecture: One user = One subscription = One active item
    // 
    // KEY INSIGHT: Paddle creates a NEW subscription when items are modified with proration
    // - Do NOT include existing items (this causes duplicate billing)
    // - Send only the new price_id to replace the old subscription
    // - Webhook will receive subscription.updated with new subscription ID
    const newItems: Array<{price_id: string, quantity: number}> = [
      {
        price_id: priceIdForPlan,
        quantity: quantity
      }
    ]

    console.log('🔄 Voice Hype subscription upgrade - Paddle will create new subscription:', {
      old_subscription_id: currentSubscription.paddle_subscription_id,
      new_item: { price_id: priceIdForPlan, quantity: quantity },
      upgrade_flow: upgradeFlow,
      note: 'Paddle will create new subscription and remove old one'
    })
    
    // Call Paddle Subscription Update API with ONLY new item
    const paddleResponse = await fetch(
      `${apiBaseUrl}/subscriptions/${currentSubscription.paddle_subscription_id}`,
      {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${paddleApiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          proration_billing_mode: prorationMode,
          items: newItems, // Only new item - Paddle removes old items and creates new subscription
          custom_data: customData
        }),
      }
    )

    if (!paddleResponse.ok) {
      const errorData = await paddleResponse.text()
      console.error('Paddle API error:', errorData)
      console.error('Request details:', {
        environment,
        apiBaseUrl,
        subscription_id: currentSubscription.paddle_subscription_id,
        priceId,
        prorationMode
      })
      return new Response(
        JSON.stringify({ 
          error: 'Failed to update subscription with Paddle',
          details: errorData
        }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const paddleData = await paddleResponse.json()

    console.log('Subscription upgraded successfully:', {
      subscription_id: currentSubscription.paddle_subscription_id,
      target_plan: targetPlan,
      upgrade_flow: upgradeFlow,
      proration_mode: prorationMode
    })

    // Handle immediate billing for upgrade_now_merge_quotas
    if (upgradeFlow === 'upgrade_now_merge_quotas' && prorationMode === 'full_immediately') {
      console.log('🔄 Processing immediate billing for full subscription amount')
      
      try {
        // Create a separate transaction for the full subscription amount
        // This ensures customer pays the correct full price (e.g., $18 for Pro, $27 for Premium)
        const billingResponse = await fetch(
          `${apiBaseUrl}/transactions`,
          {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${paddleApiKey}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              items: [
                {
                  price_id: priceIdForPlan,
                  quantity: quantity
                }
              ],
              customer_id: paddleData.data.customer_id,
              billing_details: {
                enable_checkout: false // Auto-charge using saved payment method
              },
              custom_data: {
                ...customData,
                billing_type: 'immediate_upgrade_full_amount',
                subscription_id: paddleData.data.id
              }
            }),
          }
        )

        if (!billingResponse.ok) {
          const billingError = await billingResponse.text()
          console.error('❌ Immediate billing failed:', billingError)
          
          // CRITICAL: Revert to Option B logic if immediate billing fails
          console.log('🔄 Reverting to scheduled upgrade (Option B) due to billing failure')
          
          try {
            // Update the subscription to use do_not_bill instead
            const fallbackResponse = await fetch(
              `${apiBaseUrl}/subscriptions/${paddleData.data.id}`,
              {
                method: 'PATCH',
                headers: {
                  'Authorization': `Bearer ${paddleApiKey}`,
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  proration_billing_mode: 'do_not_bill',
                  items: newItems,
                  custom_data: {
                    ...customData,
                    upgrade_metadata: {
                      ...customData.upgrade_metadata,
                      upgrade_flow: 'update_next_billing', // Change to scheduled upgrade
                      fallback_reason: 'immediate_billing_failed',
                      original_flow: 'upgrade_now_merge_quotas'
                    }
                  }
                }),
              }
            )

            if (fallbackResponse.ok) {
              console.log('✅ Successfully reverted to scheduled upgrade')
              return new Response(
                JSON.stringify({ 
                  success: true,
                  subscription: paddleData.data,
                  upgrade_flow: 'update_next_billing', // Return as scheduled upgrade
                  fallback_applied: true,
                  message: 'Payment failed. Subscription will be updated on your next billing date instead.'
                }),
                {
                  headers: { ...corsHeaders, 'Content-Type': 'application/json' },
                  status: 200
                }
              )
            } else {
              const fallbackError = await fallbackResponse.text()
              console.error('❌ Fallback to scheduled upgrade also failed:', fallbackError)
              
              // Complete failure - return error to user
              return new Response(
                JSON.stringify({ 
                  error: 'Upgrade failed',
                  details: 'Both immediate billing and scheduled upgrade fallback failed',
                  billing_error: billingError,
                  fallback_error: fallbackError
                }),
                { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
              )
            }
          } catch (fallbackError) {
            console.error('❌ Exception during fallback to scheduled upgrade:', fallbackError)
            return new Response(
              JSON.stringify({ 
                error: 'Upgrade failed',
                details: 'Payment failed and fallback to scheduled upgrade failed',
                billing_error: billingError
              }),
              { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
            )
          }
        } else {
          const billingData = await billingResponse.json()
          console.log('✅ Immediate billing transaction created successfully:', {
            transaction_id: billingData.data.id,
            amount: `${quantity} × base_price`,
            subscription_id: paddleData.data.id
          })
          
          // Immediate billing succeeded - continue with original quota merging logic
          console.log('✅ Proceeding with immediate upgrade and quota merging')
        }
      } catch (billingError) {
        console.error('❌ Exception during immediate billing:', billingError)
        
        // CRITICAL: Revert to Option B logic if billing exception occurs
        console.log('🔄 Reverting to scheduled upgrade due to billing exception')
        
        try {
          // Update subscription to scheduled upgrade
          const fallbackResponse = await fetch(
            `${apiBaseUrl}/subscriptions/${paddleData.data.id}`,
            {
              method: 'PATCH',
              headers: {
                'Authorization': `Bearer ${paddleApiKey}`,
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                proration_billing_mode: 'do_not_bill',
                items: newItems,
                custom_data: {
                  ...customData,
                  upgrade_metadata: {
                    ...customData.upgrade_metadata,
                    upgrade_flow: 'update_next_billing',
                    fallback_reason: 'immediate_billing_exception',
                    original_flow: 'upgrade_now_merge_quotas'
                  }
                }
              }),
            }
          )

          if (fallbackResponse.ok) {
            console.log('✅ Successfully reverted to scheduled upgrade after exception')
            return new Response(
              JSON.stringify({ 
                success: true,
                subscription: paddleData.data,
                upgrade_flow: 'update_next_billing',
                fallback_applied: true,
                message: 'Payment processing failed. Subscription will be updated on your next billing date instead.'
              }),
              {
                headers: { ...corsHeaders, 'Content-Type': 'application/json' },
                status: 200
              }
            )
          } else {
            console.error('❌ Fallback after exception also failed')
            return new Response(
              JSON.stringify({ 
                error: 'Upgrade failed',
                details: 'Payment processing failed and fallback to scheduled upgrade failed'
              }),
              { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
            )
          }
        } catch (fallbackException) {
          console.error('❌ Exception during fallback after billing exception:', fallbackException)
          return new Response(
            JSON.stringify({ 
              error: 'Upgrade failed',
              details: 'Payment processing and fallback both failed'
            }),
            { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
          )
        }
      }
    }

    return new Response(
      JSON.stringify({ 
        success: true,
        subscription: paddleData.data,
        upgrade_flow: upgradeFlow,
        message: upgradeFlow === 'update_next_billing' 
          ? 'Subscription will be updated on your next billing date'
          : 'Subscription upgraded immediately with quota merging'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      }
    )

  } catch (error) {
    console.error('Upgrade subscription error:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
})

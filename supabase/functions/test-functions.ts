// Test script for running edge functions locally
import { config } from "https://deno.land/x/dotenv@v3.2.0/mod.ts";

// Load environment variables
config({ path: "./.env.local", export: true });

console.log("Loading edge functions for local testing...");

// Import specific function you want to test
import "./create-paddle-checkout/index.ts";

console.log("Edge functions loaded successfully. Server running at http://localhost:9999");
console.log("Use curl or another HTTP client to test the functions.");

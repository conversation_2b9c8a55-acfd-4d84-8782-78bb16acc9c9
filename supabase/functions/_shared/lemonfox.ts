// Shared Lemon Fox functionality for transcription edge functions

// Define the result type for Lemon Fox transcription
export type LemonFoxResult = {
  text: string;
};

/**
 * Helper function to convert Headers object to a plain object
 */
export function getHeadersAsObject(headers: Headers): Record<string, string> {
  const obj: Record<string, string> = {};
  headers.forEach((value, key) => {
    obj[key] = value;
  });
  return obj;
}

/**
 * Transcribe audio data using Lemon Fox Whisper v3 API
 * 
 * @param audioData - The audio data as Uint8Array
 * @param apiKey - Lemon Fox API key
 * @param model - The Lemon Fox model to use (default: whisper-v3)
 * @param language - Optional language code
 * @param audioDurationSeconds - Optional audio duration in seconds for logging
 * @returns A promise that resolves to the transcription result
 */
export async function transcribeWithLemonFox(
  audioData: Uint8Array,
  apiKey: string,
  model: string = 'whisper-1',
  language?: string,
  audioDurationSeconds?: number
): Promise<LemonFoxResult> {
  const endpoint = 'https://api.lemonfox.ai/v1/audio/transcriptions';
  
  // Validate audio data format
  if (!(audioData instanceof Uint8Array)) {
    throw new Error('Invalid audio data format: expected Uint8Array');
  }
  
  // Create FormData with audio file
  const formData = new FormData();
  const blob = new Blob([audioData], { type: 'audio/wav' });
  formData.append('file', blob, 'audio.wav');
  formData.append('model', model);
  
  if (language) {
    formData.append('language', language);
  }
  
  console.log('LemonFox transcription request:', {
    endpoint,
    model,
    language,
    audioDataSize: audioData.length,
    audioDurationSeconds,
    requestBody: {
      file: `[Blob, size: ${blob.size}]`,
      model,
      language
    }
  });
  
  const response = await fetch(endpoint, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`
    },
    body: formData
  });
  
  const responseHeaders = getHeadersAsObject(response.headers);
  
  let responseText: string;
  try {
    responseText = await response.text();
  } catch (e: any) {
    responseText = 'Failed to read response text: ' + (e?.message || String(e));
    console.error('Failed to read response:', e);
  }
  
  if (!response.ok) {
    const error = {
      request: {
        endpoint,
        audioDataSize: audioData.length,
        model,
        language
      },
      response: {
        status: response.status,
        statusText: response.statusText,
        headers: responseHeaders,
        body: responseText
      }
    };
    
    console.error('LemonFox transcription failed:', JSON.stringify(error, null, 2));
    throw new Error(`LemonFox transcription failed: Status ${response.status} - ${response.statusText}. Response: ${responseText}`);
  }
  
  let transcribeJson;
  try {
    transcribeJson = JSON.parse(responseText);
  } catch (e: any) {
    console.error('Failed to parse transcription response as JSON:', {
      error: e?.message || String(e),
      // responseText
    });
    throw new Error(`Failed to parse LemonFox transcription response: ${e?.message || String(e)}. Raw response: ${responseText}`);
  }
  
  // Validate response structure
  if (!transcribeJson || typeof transcribeJson !== 'object') {
    console.error('Invalid response format:');
    throw new Error('LemonFox returned invalid response format');
  }
  
  const { text, error: apiError } = transcribeJson;
  
  if (apiError) {
    console.error('LemonFox API error:', apiError);
    throw new Error(`LemonFox API error: ${apiError.message || 'Unknown error'}`);
  }
  
  if (!text) {
    console.error('Missing text in response:');
    throw new Error('LemonFox transcription response missing text field');
  }
  
  return { text };
} 
// @ts-ignore: Deno types
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.0';
// @ts-ignore: Deno types
import { Status } from "https://deno.land/std@0.168.0/http/http_status.ts";

// Declare Deno types
declare const Deno: {
  env: {
    get(key: string): string | undefined;
  };
};

// Define interfaces for our API responses
export interface ApiResponse {
  success: boolean;
  data?: any;
  error?: string;
  errorCode?: string;
}

export interface ApiErrorResponse {
  error: string;
  errorCode: string;
}

// Define error codes
export enum ErrorCode {
  UNAUTHORIZED = 'unauthorized',
  INVALID_API_KEY = 'invalid_api_key',
  INSUFFICIENT_CREDITS = 'insufficient_credits',
  UNPAID_BALANCE = 'unpaid_balance',
  QUOTA_EXCEEDED = 'quota_exceeded',
  INVALID_REQUEST = 'invalid_request',
  SERVICE_ERROR = 'service_error',
  UNSUPPORTED_MODEL = 'unsupported_model',
  RATE_LIMITED = 'rate_limited',
  UNSUPPORTED_LANGUAGE = 'unsupported_language'
}

// Create a Supabase client
export const createSupabaseClient = (req: Request) => {
  const authHeader = req.headers.get('Authorization');
  if (!authHeader) {
    return null;
  }

  const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
  const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';

  return createClient(supabaseUrl, supabaseServiceKey);
};

// Validate the API key and return user information
export const validateApiKey = async (req: Request): Promise<{ userId: string, apiKeyId: string } | null> => {
  const authHeader = req.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  const apiKey = authHeader.substring(7); // Remove 'Bearer ' prefix
  const supabase = createSupabaseClient(req);
  if (!supabase) {
    return null;
  }

  // Call the validate_api_key function
  const { data, error } = await supabase.rpc('validate_api_key', { 
    p_key: apiKey 
  });

  if (error || !data || data.length === 0) {
    return null;
  }

  return { 
    userId: data[0].user_id, 
    apiKeyId: data[0].api_key_id 
  };
};

// Check if the user has sufficient usage allowance
export const checkAllowance = async (
  supabase: any,
  userId: string,
  service: string,
  model: string,
  amount: number
): Promise<boolean> => {
  const { data, error } = await supabase.rpc('check_user_usage_allowance', {
    p_user_id: userId,
    p_service: service,
    p_model: model,
    p_amount: amount
  });

  if (error) {
    console.error('Error checking allowance:', error);
    return false;
  }

  return data;
};

// Record usage
export const recordUsage = async (
  supabase: any,
  userId: string,
  apiKeyId: string,
  service: string,
  model: string,
  amount: number,
  status: 'success' | 'failed' | 'pending',
  metadata: any = {}
): Promise<string | null> => {
  const { data, error } = await supabase.rpc('record_usage', {
    p_user_id: userId,
    p_api_key_id: apiKeyId,
    p_service: service,
    p_model: model,
    p_amount: amount,
    p_status: status,
    p_metadata: metadata
  });

  if (error) {
    console.error('Error recording usage:', error);
    return null;
  }

  return data;
};

// Response types
export interface ErrorResponse {
  error: {
    code: ErrorCode;
    message: string;
    details?: any;
  };
}

export interface SuccessResponse<T> {
  data: T;
}

// Helper function to create a standardized error response
export function createErrorResponse(
  status: number, 
  message: string, 
  code: ErrorCode,
  details?: any
): Response {
  return new Response(
    JSON.stringify({
      error: {
        code,
        message,
        details
      }
    }),
    {
      status,
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
}

// Helper function to create a standardized success response
export function createSuccessResponse(data: any): Response {
  return new Response(
    JSON.stringify({ data }),
    {
      status: Status.OK,
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
}

// Helper function to parse request body
export async function parseRequestBody(req: Request): Promise<any> {
  const contentType = req.headers.get('content-type') || '';
  
  if (contentType.includes('application/json')) {
    return await req.json();
  } else if (contentType.includes('application/x-www-form-urlencoded')) {
    const formData = await req.formData();
    const data: Record<string, any> = {};
    for (const [key, value] of formData.entries()) {
      data[key] = value;
    }
    return data;
  } else {
    // Default to JSON parsing
    try {
      return await req.json();
    } catch (e) {
      return {};
    }
  }
}

// Helper function to convert base64 to Uint8Array
export function base64ToUint8Array(base64String: string): Uint8Array {
  // Remove data URL prefix if present
  const base64Data = base64String.replace(/^data:audio\/\w+;base64,/, '');
  
  // Convert base64 to binary string
  const binaryString = atob(base64Data);
  
  // Create Uint8Array from binary string
  const bytes = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  
  return bytes;
}

// Helper function to validate audio data
export function validateAudioData(audioData: Uint8Array, service?: string): void {
  if (!audioData || audioData.length === 0) {
    throw new Error('Invalid audio data');
  }
  
  // Service-specific size limits
  const limits = {
    assemblyai: 100 * 1024 * 1024, // 100MB for AssemblyAI
    openai: 25 * 1024 * 1024,      // 25MB for Whisper
    whisper: 25 * 1024 * 1024    // 25MB for Whisper
  };
  
  const maxSize = service && limits[service as keyof typeof limits]
    ? limits[service as keyof typeof limits]
    : 25 * 1024 * 1024; // Default 25MB
  
  if (audioData.length > maxSize) {
    const sizeInMB = Math.round(maxSize / (1024 * 1024));
    throw new Error(`Audio file too large for ${service || 'default'} service (max ${sizeInMB}MB)`);
  }
}

// Token usage schema for standardized tracking across all edge functions
export interface TokenUsage {
  input_tokens: number;
  output_tokens: number;
  total_tokens: number;
  input_tokens_details?: {
    cached_tokens?: number;
    prompt_tokens?: number;
    [key: string]: any;
  };
  output_tokens_details?: {
    reasoning_tokens?: number;
    completion_tokens?: number;
    [key: string]: any;
  };
}

export function createTokenUsage(
  inputTokens: number, 
  outputTokens: number, 
  details: {
    input_details?: Record<string, any>,
    output_details?: Record<string, any>
  } = {}
): TokenUsage {
  return {
    input_tokens: inputTokens,
    output_tokens: outputTokens,
    total_tokens: inputTokens + outputTokens,
    ...(details.input_details ? { input_tokens_details: details.input_details } : {}),
    ...(details.output_details ? { output_tokens_details: details.output_details } : {})
  };
} 
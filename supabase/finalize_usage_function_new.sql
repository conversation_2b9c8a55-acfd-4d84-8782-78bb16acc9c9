CREATE OR REPLACE FUNCTION public.finalize_usage(
    p_user_id uuid,
    p_service text,
    p_model text,
    p_amount numeric,
    p_cost numeric,
    p_pricing_model text,
    p_metadata jsonb,
    p_pending_usage_id uuid DEFAULT NULL::uuid
) RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    -- Token-based variables
    input_token_count INTEGER;
    output_token_count INTEGER;
    input_cost_per_token DECIMAL(18, 9);
    output_cost_per_token DECIMAL(18, 9);
    total_tokens INTEGER;
    total_token_cost DECIMAL(18, 9);
    
    -- Service type
    pricing_unit TEXT;
    is_token_based BOOLEAN;
    
    -- Subscription variables
    subscription_quota_available DECIMAL(18, 9);
    subscription_id UUID;
    
    -- Credit variables
    total_credits_available DECIMAL(18, 9);
    remaining_cost DECIMAL(18, 9);
    credit_record RECORD;
    
    -- Hybrid billing variables (transcription only)
    remaining_amount DECIMAL(18, 9);
    remaining_amount_cost DECIMAL(18, 9);
BEGIN
    -- Remove pending usage entry
    IF p_pending_usage_id IS NOT NULL THEN
        DELETE FROM public.usage_history
        WHERE id = p_pending_usage_id AND status = 'pending';
    ELSE
        DELETE FROM public.usage_history
        WHERE id = (
            SELECT id FROM public.usage_history
            WHERE user_id = p_user_id
            AND service = p_service
            AND model = p_model
            AND status = 'pending'
            ORDER BY created_at DESC
            LIMIT 1
        );
    END IF;

    -- Determine service type
    SELECT sp.unit INTO pricing_unit
    FROM public.service_pricing sp
    WHERE sp.service = p_service
    AND sp.model = p_model
    AND sp.is_active = TRUE;

    is_token_based := pricing_unit = 'token-based';

    -- Get subscription quota if available
    SELECT q.total_amount - q.used_amount, q.subscription_id
    INTO subscription_quota_available, subscription_id
    FROM public.quotas q
    WHERE q.user_id = p_user_id
    AND q.service = p_service
    AND q.subscription_id IS NOT NULL
    AND q.reset_date > NOW()
    AND q.total_amount - q.used_amount > 0
    AND EXISTS (
        SELECT 1 FROM public.user_subscriptions us
        WHERE us.id = q.subscription_id
        AND us.user_id = p_user_id
        AND us.status = 'active'
    );

    -- Get total available credits
    SELECT COALESCE(SUM(balance), 0) INTO total_credits_available
    FROM public.credits
    WHERE user_id = p_user_id
    AND balance > 0
    AND (expires_at IS NULL OR expires_at > NOW())
    AND status = 'active';

    -- OPTIMIZATION SERVICE (TOKEN-BASED)
    IF p_service = 'optimization' THEN
        -- Extract token data
        input_token_count := COALESCE((p_metadata->>'inputTokens')::INTEGER, 0);
        output_token_count := COALESCE((p_metadata->>'outputTokens')::INTEGER, 0);
        total_tokens := input_token_count + output_token_count;
        
        -- Get pricing using exact model names
        SELECT sp.cost_per_unit INTO input_cost_per_token
        FROM public.service_pricing sp
        WHERE sp.service = 'optimization'
        AND sp.model = p_model || '/input'
        AND sp.is_active = true;
        
        SELECT sp.cost_per_unit INTO output_cost_per_token
        FROM public.service_pricing sp
        WHERE sp.service = 'optimization'
        AND sp.model = p_model || '/output'
        AND sp.is_active = true;
        
        IF input_cost_per_token IS NULL OR output_cost_per_token IS NULL THEN
            RAISE EXCEPTION 'Pricing data not found for model: % (service: %)', p_model, p_service;
        END IF;
        
        total_token_cost := ROUND(input_token_count * input_cost_per_token + output_token_count * output_cost_per_token, 9);

        -- OPTIMIZATION BILLING LOGIC
        IF subscription_quota_available IS NOT NULL AND subscription_quota_available >= total_tokens THEN
            -- Case 1: Sufficient quota - use subscription
            INSERT INTO public.usage_history (
                user_id, service, model, amount, cost, pricing_model, status, metadata
            ) VALUES (
                p_user_id, p_service, p_model, total_tokens, 0, 'subscription', 'success',
                p_metadata || jsonb_build_object(
                    'paymentMethod', 'subscription',
                    'inputTokens', input_token_count,
                    'outputTokens', output_token_count,
                    'subscriptionId', subscription_id
                )
            );
            
            UPDATE public.quotas
            SET used_amount = used_amount + total_tokens
            WHERE user_id = p_user_id AND service = p_service AND public.quotas.subscription_id IS NOT NULL;
            
        ELSIF total_credits_available >= total_token_cost THEN
            -- Case 2: Insufficient quota but sufficient credits - use credits
            INSERT INTO public.usage_history (
                user_id, service, model, amount, cost, pricing_model, status, metadata
            ) VALUES (
                p_user_id, p_service, p_model, total_tokens, total_token_cost, 'credits', 'success',
                p_metadata || jsonb_build_object(
                    'paymentMethod', 'credits',
                    'inputTokens', input_token_count,
                    'outputTokens', output_token_count
                )
            );
            
            -- Deduct from credits
            remaining_cost := total_token_cost;
            FOR credit_record IN
                SELECT id, balance, expires_at
                FROM public.credits
                WHERE user_id = p_user_id
                AND balance > 0
                AND (expires_at IS NULL OR expires_at > NOW())
                AND status = 'active'
                ORDER BY
                    CASE WHEN expires_at IS NULL THEN 1 ELSE 0 END,
                    expires_at ASC
            LOOP
                IF credit_record.balance >= remaining_cost THEN
                    IF ROUND(credit_record.balance - remaining_cost, 9) = 0 THEN
                        DELETE FROM public.credits WHERE id = credit_record.id;
                    ELSE
                        UPDATE public.credits
                        SET balance = ROUND(balance - remaining_cost, 9)
                        WHERE id = credit_record.id;
                    END IF;
                    remaining_cost := 0;
                    EXIT;
                ELSE
                    DELETE FROM public.credits WHERE id = credit_record.id;
                    remaining_cost := ROUND(remaining_cost - credit_record.balance, 9);
                END IF;
            END LOOP;
            
        ELSE
            -- Case 3: Insufficient quota and credits - allow quota overuse
            INSERT INTO public.usage_history (
                user_id, service, model, amount, cost, pricing_model, status, metadata
            ) VALUES (
                p_user_id, p_service, p_model, total_tokens, 0, 'subscription', 'success',
                p_metadata || jsonb_build_object(
                    'paymentMethod', 'subscription',
                    'inputTokens', input_token_count,
                    'outputTokens', output_token_count,
                    'subscriptionId', subscription_id,
                    'quotaOveruse', true
                )
            );
            
            UPDATE public.quotas
            SET used_amount = used_amount + total_tokens
            WHERE user_id = p_user_id AND service = p_service AND public.quotas.subscription_id IS NOT NULL;
        END IF;

    -- TRANSCRIPTION SERVICES (DURATION-BASED)
    ELSE
        -- TRANSCRIPTION BILLING LOGIC
        IF subscription_quota_available IS NOT NULL AND subscription_quota_available >= p_amount THEN
            -- Case 1: Sufficient quota - use subscription
            INSERT INTO public.usage_history (
                user_id, service, model, amount, cost, pricing_model, status, metadata
            ) VALUES (
                p_user_id, p_service, p_model, p_amount, 0, 'subscription', 'success',
                p_metadata || jsonb_build_object(
                    'paymentMethod', 'subscription',
                    'subscriptionId', subscription_id
                )
            );
            
            UPDATE public.quotas
            SET used_amount = used_amount + p_amount
            WHERE user_id = p_user_id AND service = p_service AND public.quotas.subscription_id IS NOT NULL;
            
        ELSIF subscription_quota_available IS NOT NULL AND subscription_quota_available > 0 THEN
            -- Case 2: Partial quota available - HYBRID BILLING
            remaining_amount := p_amount - subscription_quota_available;
            remaining_amount_cost := ROUND((remaining_amount / p_amount) * p_cost, 9);
            
            -- Use available quota first
            INSERT INTO public.usage_history (
                user_id, service, model, amount, cost, pricing_model, status, metadata
            ) VALUES (
                p_user_id, p_service, p_model, subscription_quota_available, 0, 'subscription', 'success',
                p_metadata || jsonb_build_object(
                    'paymentMethod', 'subscription',
                    'subscriptionId', subscription_id,
                    'hybridBilling', true,
                    'quotaPortion', subscription_quota_available
                )
            );
            
            UPDATE public.quotas
            SET used_amount = used_amount + subscription_quota_available
            WHERE user_id = p_user_id AND service = p_service AND public.quotas.subscription_id IS NOT NULL;
            
            -- Use credits for remaining amount
            INSERT INTO public.usage_history (
                user_id, service, model, amount, cost, pricing_model, status, metadata
            ) VALUES (
                p_user_id, p_service, p_model, remaining_amount, remaining_amount_cost, 'credits', 'success',
                p_metadata || jsonb_build_object(
                    'paymentMethod', 'credits',
                    'hybridBilling', true,
                    'creditsPortion', remaining_amount
                )
            );
            
            -- Deduct from credits
            remaining_cost := remaining_amount_cost;
            FOR credit_record IN
                SELECT id, balance, expires_at
                FROM public.credits
                WHERE user_id = p_user_id
                AND balance > 0
                AND (expires_at IS NULL OR expires_at > NOW())
                AND status = 'active'
                ORDER BY
                    CASE WHEN expires_at IS NULL THEN 1 ELSE 0 END,
                    expires_at ASC
            LOOP
                IF credit_record.balance >= remaining_cost THEN
                    IF ROUND(credit_record.balance - remaining_cost, 9) = 0 THEN
                        DELETE FROM public.credits WHERE id = credit_record.id;
                    ELSE
                        UPDATE public.credits
                        SET balance = ROUND(balance - remaining_cost, 9)
                        WHERE id = credit_record.id;
                    END IF;
                    remaining_cost := 0;
                    EXIT;
                ELSE
                    DELETE FROM public.credits WHERE id = credit_record.id;
                    remaining_cost := ROUND(remaining_cost - credit_record.balance, 9);
                END IF;
            END LOOP;
            
        ELSE
            -- Case 3: No quota available - use credits
            INSERT INTO public.usage_history (
                user_id, service, model, amount, cost, pricing_model, status, metadata
            ) VALUES (
                p_user_id, p_service, p_model, p_amount, p_cost, 'credits', 'success',
                p_metadata || jsonb_build_object('paymentMethod', 'credits')
            );
            
            -- Deduct from credits
            remaining_cost := p_cost;
            FOR credit_record IN
                SELECT id, balance, expires_at
                FROM public.credits
                WHERE user_id = p_user_id
                AND balance > 0
                AND (expires_at IS NULL OR expires_at > NOW())
                AND status = 'active'
                ORDER BY
                    CASE WHEN expires_at IS NULL THEN 1 ELSE 0 END,
                    expires_at ASC
            LOOP
                IF credit_record.balance >= remaining_cost THEN
                    IF ROUND(credit_record.balance - remaining_cost, 9) = 0 THEN
                        DELETE FROM public.credits WHERE id = credit_record.id;
                    ELSE
                        UPDATE public.credits
                        SET balance = ROUND(balance - remaining_cost, 9)
                        WHERE id = credit_record.id;
                    END IF;
                    remaining_cost := 0;
                    EXIT;
                ELSE
                    DELETE FROM public.credits WHERE id = credit_record.id;
                    remaining_cost := ROUND(remaining_cost - credit_record.balance, 9);
                END IF;
            END LOOP;
        END IF;
    END IF;
END;
$function$;

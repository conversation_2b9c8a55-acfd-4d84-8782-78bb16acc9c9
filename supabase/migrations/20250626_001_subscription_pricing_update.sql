-- Update subscription plans with new pricing structure
-- VoiceHype Subscription Pricing Integration
-- Created: 2025-06-26

BEGIN;

-- Add new columns to subscription_plans if they don't exist
ALTER TABLE public.subscription_plans 
ADD COLUMN IF NOT EXISTS input_tokens integer,
ADD COLUMN IF NOT EXISTS output_tokens integer;

-- Update existing Basic plan with new pricing
UPDATE public.subscription_plans 
SET 
  monthly_price = 9.00,
  description = '720 minutes transcription, 400K tokens',
  transcription_minutes = 720,
  tokens = 400000,
  input_tokens = 400000,
  output_tokens = 400000
WHERE name = 'Basic';

-- Update existing Pro plan with new pricing  
UPDATE public.subscription_plans
SET
  monthly_price = 18.00,
  description = '1,440 minutes transcription, 800K tokens',
  transcription_minutes = 1440,
  tokens = 800000,
  input_tokens = 800000,
  output_tokens = 800000
WHERE name = 'Pro';

-- Add new Premium plan
-- Check if Premium plan exists, if not insert it
INSERT INTO public.subscription_plans (
  name, 
  description, 
  monthly_price, 
  annual_price,
  transcription_minutes, 
  tokens,
  input_tokens,
  output_tokens,
  is_active
) 
SELECT 
  'Premium',
  '2,160 minutes transcription, 1.2M tokens',
  27.00,
  NULL,
  2160,
  1200000,
  1200000,
  1200000,
  true
WHERE NOT EXISTS (
  SELECT 1 FROM public.subscription_plans WHERE name = 'Premium'
); -- Add annual pricing for existing plans (20% discount)

UPDATE public.subscription_plans 
SET annual_price = monthly_price * 12 * 0.8
WHERE annual_price IS NULL AND name IN ('Basic', 'Pro', 'Premium');

COMMIT;

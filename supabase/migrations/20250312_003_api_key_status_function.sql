-- Add function to update API key status
BEGIN;

-- Create a function to update the is_active status of an API key
CREATE OR REPLACE FUNCTION public.update_api_key_status(
    p_key_id UUID,
    p_is_active BOOLEAN
) RETURNS BOOLEAN AS $$
DECLARE
    v_user_id UUID;
    v_key_exists BOOLEAN;
BEGIN
    -- Get the current user's ID
    v_user_id := auth.uid();
    
    -- Check if user is authenticated
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'Not authenticated';
    END IF;
    
    -- Check if the API key exists and belongs to the current user
    SELECT EXISTS (
        SELECT 1 
        FROM public.api_keys 
        WHERE id = p_key_id AND user_id = v_user_id
    ) INTO v_key_exists;
    
    IF NOT v_key_exists THEN
        RAISE EXCEPTION 'API key not found or does not belong to the current user';
    END IF;
    
    -- Update the API key status
    UPDATE public.api_keys
    SET is_active = p_is_active
    WHERE id = p_key_id AND user_id = v_user_id;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the comment
COMMENT ON FUNCTION public.update_api_key_status IS 'Updates the active status of an API key for the authenticated user';

-- Revoke all permissions first
REVOKE ALL ON FUNCTION public.update_api_key_status FROM PUBLIC;
REVOKE ALL ON FUNCTION public.update_api_key_status FROM anon;
REVOKE ALL ON FUNCTION public.update_api_key_status FROM authenticated;

-- Grant permission only to authenticated users
GRANT EXECUTE ON FUNCTION public.update_api_key_status TO authenticated;

COMMIT; 
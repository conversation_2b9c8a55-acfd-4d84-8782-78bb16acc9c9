-- Migration: Handle Negative Credit Balances in process_completed_transaction
-- Date: 2025-06-30
-- Purpose: Modify process_completed_transaction to check for negative balances
--          and adjust new credit purchases accordingly

-- Drop the existing function
DROP FUNCTION IF EXISTS public.process_completed_transaction(text, text, numeric, text, text, text, jsonb);

-- Create the updated function with negative balance handling
CREATE OR REPLACE FUNCTION public.process_completed_transaction(
    p_paddle_transaction_id text, 
    p_paddle_customer_id text, 
    p_amount numeric, 
    p_currency text, 
    p_product_id text DEFAULT NULL::text, 
    p_receipt_url text DEFAULT NULL::text, 
    p_metadata jsonb DEFAULT '{}'::jsonb
)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $function$
DECLARE
    v_user_id UUID;
    v_customer_id UUID;
    v_product_id UUID;
    v_credit_amount DECIMAL(18,9);
    v_transaction_id UUID;
    v_existing_transaction UUID;
    v_email TEXT;
    v_customer_name TEXT;
    v_credit_id UUID;
    
    -- Variables for negative balance handling
    v_total_current_balance DECIMAL(18,9) := 0;
    v_negative_balance DECIMAL(18,9) := 0;
    v_adjusted_credit_amount DECIMAL(18,9);
    v_negative_credit_record RECORD;
    v_credits_to_remove UUID[];
    v_credit_record RECORD;
BEGIN
    -- Check if transaction already exists
    SELECT id INTO v_existing_transaction
    FROM paddle.transactions
    WHERE paddle_transaction_id = p_paddle_transaction_id;

    IF v_existing_transaction IS NOT NULL THEN
        -- Transaction already processed
        RETURN TRUE;
    END IF;

    -- Get customer and user info from paddle.customers table
    SELECT user_id, id INTO v_user_id, v_customer_id
    FROM paddle.customers
    WHERE paddle_customer_id = p_paddle_customer_id;

    -- If we don't have a user_id, check the custom_data in the metadata
    IF v_user_id IS NULL THEN
        -- First, check for user_id in custom_data (this is the most reliable method)
        IF p_metadata->'custom_data'->>'user_id' IS NOT NULL THEN
            v_user_id := (p_metadata->'custom_data'->>'user_id')::UUID;
            RAISE NOTICE 'Found user_id in custom_data: %', v_user_id;
        END IF;
        
        -- If still no user_id, fall back to email matching
        IF v_user_id IS NULL THEN
            -- Extract customer email from metadata
            v_email := p_metadata->>'email';
            
            IF v_email IS NULL THEN
                -- Try to find email in nested customer structure
                v_email := p_metadata->'customer'->>'email';
            END IF;
            
            IF v_email IS NULL THEN
                -- Try billing address email
                v_email := p_metadata->'billing_details'->>'email';
            END IF;

            IF v_email IS NULL THEN
                -- Try custom_data email
                v_email := p_metadata->'custom_data'->>'email';
            END IF;

            IF v_email IS NOT NULL THEN
                -- Look for existing user by email in profiles table
                SELECT id INTO v_user_id
                FROM public.profiles
                WHERE email = v_email;
            END IF;
        END IF;
        
        -- Get the customer name for the record
        v_customer_name := COALESCE(
            p_metadata->>'name',
            p_metadata->'customer'->>'name',
            p_metadata->'custom_data'->>'name',
            'VoiceHype User'
        );

        -- If we now have a user_id, create or update the paddle customer record
        IF v_user_id IS NOT NULL THEN
            -- Extract email if we don't have it yet
            IF v_email IS NULL THEN
                SELECT email INTO v_email
                FROM public.profiles
                WHERE id = v_user_id;
            END IF;
            
            -- Create or update the paddle customer record
            INSERT INTO paddle.customers (
                user_id,
                paddle_customer_id,
                email,
                name,
                created_at,
                metadata
            ) VALUES (
                v_user_id,
                p_paddle_customer_id,
                v_email,
                v_customer_name,
                NOW(),
                p_metadata
            ) ON CONFLICT (paddle_customer_id) 
            DO UPDATE SET
                user_id = v_user_id,
                email = COALESCE(v_email, paddle.customers.email),
                name = v_customer_name,
                metadata = p_metadata,
                updated_at = NOW();
            
            RAISE NOTICE 'Linked user % with Paddle customer %', v_user_id, p_paddle_customer_id;
        END IF;
    END IF;

    -- Security check: Only process transactions for existing users
    IF v_user_id IS NULL THEN
        -- Include custom_data in error message for debugging
        RAISE EXCEPTION 'Transaction rejected: No existing VoiceHype user found for this purchase. Customer ID: %, Email: %, Custom Data: %', 
            p_paddle_customer_id, 
            COALESCE(v_email, 'not provided'),
            p_metadata->'custom_data';
    END IF;

    -- Verify the user exists in our profiles table (additional security check)
    IF NOT EXISTS (SELECT 1 FROM public.profiles WHERE id = v_user_id) THEN
        RAISE EXCEPTION 'Transaction rejected: User profile not found for user ID: %', v_user_id;
    END IF;

    -- Get product info if product_id provided (for reference only)
    IF p_product_id IS NOT NULL THEN
        SELECT id INTO v_product_id
        FROM paddle.products
        WHERE paddle_product_id = p_product_id AND is_active = TRUE;
    END IF;

    -- Convert amount from cents to dollars (credit units)
    -- First check if credit_amount is provided in custom_data
    IF p_metadata->'custom_data'->>'credit_amount' IS NOT NULL THEN
        -- Use the credit amount specified during checkout
        v_credit_amount := (p_metadata->'custom_data'->>'credit_amount')::DECIMAL;
        RAISE NOTICE 'Using credit amount from custom_data: %', v_credit_amount;
    ELSE
        -- Fall back to standard conversion of $1 = 1 credit
        v_credit_amount := p_amount / 100.0;
        RAISE NOTICE 'Using calculated credit amount: %', v_credit_amount;
    END IF;

    -- NEW: Check for negative credit balances before adding new credits
    -- Calculate total current balance for the user
    SELECT COALESCE(SUM(balance), 0) INTO v_total_current_balance
    FROM public.credits
    WHERE user_id = v_user_id 
    AND (status IS NULL OR status = 'active')
    AND (expires_at IS NULL OR expires_at > NOW());

    RAISE NOTICE 'Current total balance for user %: %', v_user_id, v_total_current_balance;

    -- Set the adjusted credit amount (initially the same as purchased amount)
    v_adjusted_credit_amount := v_credit_amount;

    -- If there's a negative balance, adjust the new credit amount
    IF v_total_current_balance < 0 THEN
        v_negative_balance := ABS(v_total_current_balance);
        RAISE NOTICE 'Negative balance detected: -%', v_negative_balance;
        
        -- Adjust the credit amount by subtracting the negative balance
        v_adjusted_credit_amount := v_credit_amount - v_negative_balance;
        
        RAISE NOTICE 'Adjusting credit purchase from % to % (negative balance offset: %)', 
            v_credit_amount, v_adjusted_credit_amount, v_negative_balance;
        
        -- Mark negative credit entries for removal or adjustment
        -- We'll remove negative entries and adjust positive ones as needed
        FOR v_credit_record IN 
            SELECT id, balance
            FROM public.credits
            WHERE user_id = v_user_id 
            AND (status IS NULL OR status = 'active')
            AND (expires_at IS NULL OR expires_at > NOW())
            AND balance < 0
            ORDER BY created_at ASC
        LOOP
            -- Mark these credits as consumed/offset
            UPDATE public.credits 
            SET status = 'consumed_by_purchase',
                updated_at = NOW()
            WHERE id = v_credit_record.id;
            
            RAISE NOTICE 'Marked negative credit entry % (balance: %) as consumed', 
                v_credit_record.id, v_credit_record.balance;
        END LOOP;
        
        -- If there are any remaining small positive balances after negative offset,
        -- we may need to adjust them too, but typically the calculation above handles this
        
        -- Ensure the adjusted amount is not negative
        IF v_adjusted_credit_amount < 0 THEN
            RAISE NOTICE 'Adjusted credit amount would be negative (%), setting to 0', v_adjusted_credit_amount;
            v_adjusted_credit_amount := 0;
        END IF;
    END IF;

    -- Create transaction record (always record the full purchase amount)
    INSERT INTO paddle.transactions (
        user_id,
        paddle_transaction_id,
        paddle_customer_id,
        product_id,
        status,
        amount,
        currency,
        credit_amount,
        transaction_type,
        paddle_receipt_url,
        metadata,
        created_at,
        updated_at
    ) VALUES (
        v_user_id,
        p_paddle_transaction_id,
        p_paddle_customer_id,
        v_product_id,
        'completed',
        p_amount,
        p_currency,
        v_credit_amount, -- Store the original purchased amount
        'credit_purchase',
        p_receipt_url,
        -- Add negative balance adjustment info to metadata
        p_metadata || jsonb_build_object(
            'negative_balance_adjustment', 
            jsonb_build_object(
                'original_amount', v_credit_amount,
                'negative_balance_offset', v_negative_balance,
                'adjusted_amount', v_adjusted_credit_amount,
                'total_balance_before', v_total_current_balance
            )
        ),
        NOW(),
        NOW()
    ) RETURNING id INTO v_transaction_id;

    -- Add credits only if the adjusted amount is positive
    IF v_adjusted_credit_amount > 0 THEN
        INSERT INTO public.credits (
            user_id, 
            balance, 
            currency, 
            created_at, 
            updated_at,
            expires_at,
            status
        ) VALUES (
            v_user_id, 
            v_adjusted_credit_amount, 
            p_currency, 
            NOW(), 
            NOW(),
            ((NOW() + INTERVAL '1 year')::date + INTERVAL '1 day')::timestamp,
            'active'
        ) RETURNING id INTO v_credit_id;
        
        RAISE NOTICE 'Created new credit entry % for user %, adjusted amount %, expires at %', 
            v_credit_id, v_user_id, v_adjusted_credit_amount, (NOW() + INTERVAL '1 year')::date + INTERVAL '1 day';
    ELSE
        RAISE NOTICE 'No new credit entry created - adjusted amount was % (negative balance fully offset purchase)', 
            v_adjusted_credit_amount;
    END IF;

    IF v_negative_balance > 0 THEN
        RAISE NOTICE 'Successfully processed transaction % for user %, purchased % credits, offset % negative balance, net credits added: %', 
            p_paddle_transaction_id, v_user_id, v_credit_amount, v_negative_balance, v_adjusted_credit_amount;
    ELSE
        RAISE NOTICE 'Successfully processed transaction % for user %, added % credits (no negative balance adjustment needed)', 
            p_paddle_transaction_id, v_user_id, v_credit_amount;
    END IF;

    RETURN TRUE;
END;
$function$;

-- Security: Restrict function access to service_role only
REVOKE EXECUTE ON FUNCTION public.process_completed_transaction(text, text, numeric, text, text, text, jsonb) FROM PUBLIC;
REVOKE EXECUTE ON FUNCTION public.process_completed_transaction(text, text, numeric, text, text, text, jsonb) FROM authenticated;
REVOKE EXECUTE ON FUNCTION public.process_completed_transaction(text, text, numeric, text, text, text, jsonb) FROM anon;
GRANT EXECUTE ON FUNCTION public.process_completed_transaction(text, text, numeric, text, text, text, jsonb) TO service_role;

-- Add helpful comment
COMMENT ON FUNCTION public.process_completed_transaction IS 
'Process completed Paddle transactions and handle credit purchases. Automatically adjusts for negative balances by offsetting them against new credit purchases. SECURITY: Only accessible by service_role (edge functions).';

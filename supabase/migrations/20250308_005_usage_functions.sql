-- Drop existing functions first
DROP FUNCTION IF EXISTS check_usage_allowance(uuid, text, text, numeric);

-- Create finalize_usage function
CREATE OR REPLACE FUNCTION finalize_usage(
    p_user_id UUID,
    p_service TEXT,
    p_model TEXT,
    p_amount DECIMAL,
    p_cost DECIMAL,
    p_pricing_model TEXT,
    p_metadata JSONB
) RETURNS VOID AS $$
BEGIN
    -- Update usage history to success using a subquery for ORDER BY and LIMIT
    WITH latest_pending AS (
        SELECT id 
        FROM public.usage_history
        WHERE user_id = p_user_id
            AND service = p_service
            AND model = p_model
            AND status = 'pending'
        ORDER BY created_at DESC
        LIMIT 1
    )
    UPDATE public.usage_history
    SET status = 'success',
        metadata = metadata || p_metadata
    WHERE id IN (SELECT id FROM latest_pending);

    -- Handle credits deduction
    IF p_pricing_model = 'credits' THEN
        UPDATE public.credits
        SET balance = balance - p_cost,
            updated_at = NOW()
        WHERE user_id = p_user_id;
    
    -- Handle PAYG recording
    ELSIF p_pricing_model = 'payg' THEN
        INSERT INTO public.payg_usage (
            user_id,
            month,
            total_amount
        ) VALUES (
            p_user_id,
            DATE_TRUNC('month', NOW()),
            p_cost
        )
        ON CONFLICT (user_id, month) DO UPDATE
        SET total_amount = payg_usage.total_amount + p_cost;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create new check_usage_allowance function
CREATE FUNCTION check_usage_allowance(
    user_id UUID,
    service TEXT,
    model TEXT,
    amount DECIMAL
) RETURNS TABLE (
    can_use BOOLEAN,
    pricing_model TEXT,
    cost DECIMAL
) AS $$
DECLARE
    v_cost DECIMAL;
    v_credit_balance DECIMAL;
    v_has_payg BOOLEAN;
BEGIN
    -- Calculate cost
    SELECT cost_per_unit * amount INTO v_cost
    FROM public.service_pricing
    WHERE service = check_usage_allowance.service
    AND model = check_usage_allowance.model
    AND is_active = TRUE;

    -- Check credit balance
    SELECT balance INTO v_credit_balance
    FROM public.credits
    WHERE user_id = check_usage_allowance.user_id;

    IF v_credit_balance >= v_cost THEN
        RETURN QUERY SELECT 
            TRUE,
            'credits'::TEXT,
            v_cost;
        RETURN;
    END IF;

    -- Check PAYG availability
    SELECT stripe_customer_id IS NOT NULL INTO v_has_payg
    FROM public.profiles
    WHERE id = check_usage_allowance.user_id;

    IF v_has_payg THEN
        RETURN QUERY SELECT 
            TRUE,
            'payg'::TEXT,
            v_cost;
        RETURN;
    END IF;

    -- No available pricing methods
    RETURN QUERY SELECT 
        FALSE,
        NULL::TEXT,
        v_cost;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
# Multiple Credit Entries Enhancement

## Purpose

This migration enhances the credit usage system to properly handle multiple credit entries for a single user and implements cleanup of zero-balance entries. This feature addresses several important scenarios:

1. Users with multiple credit purchases (e.g., a user buys $5 of credits, then later buys $10 more)
2. Proper accounting when a user has credits with different expiration dates
3. Automatic cleanup of credit entries that are fully consumed
4. More efficient database storage by removing unused credit records

## Technical Changes

The migration introduces the following key changes:

1. **Enhanced Check Logic**: The `check_usage_allowance` function now properly sums all available credit balances across multiple entries to determine if a user has sufficient credits.

2. **Improved Consumption Strategy**: The `finalize_usage` function now:
   - Processes each credit entry individually
   - Uses credits in proper order (soonest-to-expire first)
   - Deducts the appropriate amount from each entry
   - Removes entries that reach zero balance

3. **Cleanup Logic**: Credit entries with zero balance are automatically deleted rather than kept in the database

## Implementation Details

### Credit Availability Check

- When checking if a user has sufficient credits, the system now sums all valid credits from multiple entries
- Only non-expired, positive-balance entries are considered
- This ensures an accurate representation of the user's total available credits

### Credit Consumption Logic

The function implements a multi-step consumption process:

1. **Loop Through Expiring Credits**: 
   - Process all credit entries that have expiration dates
   - Start with those expiring soonest (to prevent waste)
   - Deduct from each entry until the cost is covered or all entries are processed

2. **Process Non-Expiring Credits**:
   - If cost remains after using all expiring credits, use non-expiring credits
   - Process oldest non-expiring credits first

3. **Cleanup Zero-Balance Entries**:
   - After deducting from a credit entry, if balance reaches zero, the entry is deleted
   - This keeps the database clean and prevents accumulation of empty records

## Benefits

1. **Prevents Wasted Credits**: By using soon-to-expire credits first, the system prevents credit waste
2. **More Accurate Reporting**: Users have a clearer view of their credit status
3. **More Efficient Storage**: Automatic cleanup of zero-balance records reduces database size
4. **Better User Experience**: The system behaves more intuitively with respect to credit expiration and usage

## Future Considerations

- Add analytics to track patterns of credit expiration and usage
- Consider implementing credit transfer between entries
- Implement a batch job to clean up any forgotten zero-balance entries
- Consider notifications for users when individual credit purchases are about to expire 
-- Create welcome email trigger function with role restrictions
CREATE OR REPLACE FUNCTION send_welcome_email()
RETURNS TRIGGER AS $$
DECLARE
  email_service_url TEXT := 'http://localhost:8000/functions/v1/email-service';
  webhook_secret TEXT;
  email_payload JSONB;
  request_id BIGINT;
BEGIN
  -- Only process INSERT operations (new user signups)
  IF TG_OP = 'INSERT' THEN
    
    -- Get webhook secret from vault
    SELECT decrypted_secret INTO webhook_secret
    FROM vault.decrypted_secrets
    WHERE name = 'email_webhook_secret';
    
    -- Prepare email payload
    email_payload := jsonb_build_object(
      'user', jsonb_build_object(
        'id', NEW.id,
        'email', NEW.email,
        'created_at', NEW.created_at,
        'email_confirmed_at', NEW.email_confirmed_at
      ),
      'email_data', jsonb_build_object(
        'email_action_type', 'signup',
        'token', NEW.email_confirmation_token,
        'token_hash', NEW.confirmation_token,
        'site_url', 'https://voicehype.ai'
      )
    );
    
    -- Send async HTTP request with authentication
    SELECT net.http_post(
      url := email_service_url,
      body := email_payload::TEXT,
      headers := jsonb_build_object(
        'Content-Type', 'application/json',
        'User-Agent', 'Supabase-Database-Trigger',
        'Authorization', 'Bearer ' || webhook_secret
      )
    ) INTO request_id;
    
    -- Log the request for debugging
    RAISE NOTICE 'Welcome email triggered for user: %, request_id: %', NEW.email, request_id;
    
  END IF;
  
  RETURN NEW;
  
EXCEPTION
  WHEN OTHERS THEN
    -- Log error but don't block user signup
    RAISE NOTICE 'Failed to send welcome email: %', SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Revoke public access and grant only to specific roles
REVOKE ALL ON FUNCTION send_welcome_email() FROM PUBLIC;
GRANT EXECUTE ON FUNCTION send_welcome_email() TO postgres;
GRANT EXECUTE ON FUNCTION send_welcome_email() TO service_role;

-- Create trigger on auth.users table
CREATE OR REPLACE TRIGGER trigger_send_welcome_email
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION send_welcome_email();
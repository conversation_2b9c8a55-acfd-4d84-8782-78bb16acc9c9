-- Add GPT-4o pricing entries using token-based pricing
INSERT INTO public.service_pricing (service, model, cost_per_unit, unit, is_active)
VALUES 
  ('transcription', 'gpt-4o-mini-transcribe/input', 0.000015, 'token', true),
  ('transcription', 'gpt-4o-mini-transcribe/output', 0.000020, 'token', true),
  ('transcription', 'gpt-4o-transcribe/input', 0.000030, 'token', true),
  ('transcription', 'gpt-4o-transcribe/output', 0.000060, 'token', true);

-- Add token-based identifier models which act as pointers to the token-based pricing
INSERT INTO public.service_pricing (service, model, cost_per_unit, unit, is_active)
VALUES 
  ('transcription', 'gpt-4o-mini-transcribe', 0.006, 'token-based', true),
  ('transcription', 'gpt-4o-transcribe', 0.012, 'token-based', true);

-- Add unique constraint to service and model to prevent duplicates
ALTER TABLE public.service_pricing ADD CONSTRAINT service_pricing_service_model_unique UNIQUE (service, model);

-- Update check_usage_allowance function to handle token-based billing
CREATE OR REPLACE FUNCTION "public"."check_usage_allowance"("p_user_id" "uuid", "p_service" "text", "p_model" "text", "p_amount" numeric, "p_api_key_id" "uuid", "p_is_input_only" boolean DEFAULT false) RETURNS TABLE("can_use" boolean, "pricing_model" "text", "cost" numeric, "max_output_tokens" "text", "error_code" "text")
LANGUAGE "plpgsql" SECURITY DEFINER
AS $$
DECLARE
    subscription_available BOOLEAN;
    credit_balance DECIMAL(18,9);
    service_cost DECIMAL(18,9);
    cost_per_unit DECIMAL(18,9);
    payg_allowed BOOLEAN;
    input_cost_per_unit DECIMAL(18,9);
    output_cost_per_unit DECIMAL(18,9);
    max_tokens INTEGER;
    has_unpaid_previous_month BOOLEAN;
    is_token_based BOOLEAN;
    avg_tokens_per_minute INTEGER;
    avg_token_cost DECIMAL(18,9);
    estimated_minutes INTEGER;
    pricing_unit TEXT;
BEGIN
    -- Get information about the model's pricing unit
    SELECT sp.unit INTO pricing_unit
    FROM public.service_pricing sp
    WHERE sp.service = p_service
    AND sp.model = p_model
    AND sp.is_active = TRUE;
    
    -- Check if this is a token-based model
    is_token_based := pricing_unit = 'token-based';
    
    -- Get cost per unit first for standard pricing
    SELECT sp.cost_per_unit INTO cost_per_unit
    FROM public.service_pricing sp
    WHERE sp.service = p_service
    AND sp.model = p_model
    AND sp.is_active = TRUE;

    -- Calculate standard service cost with proper decimal precision
    IF cost_per_unit IS NOT NULL AND NOT is_token_based THEN
        service_cost := cost_per_unit * p_amount;
    END IF;

    -- Check subscription quota first
    SELECT EXISTS (
        SELECT 1 FROM public.quotas q
        JOIN public.user_subscriptions us ON q.subscription_id = us.id
        WHERE us.user_id = p_user_id
        AND q.service = p_service
        AND (q.used_amount + p_amount) <= q.total_amount
        AND us.status = 'active'
        AND q.reset_date > NOW()
    ) INTO subscription_available;

    IF subscription_available THEN 
        RETURN QUERY SELECT 
            TRUE as can_use,
            'subscription'::TEXT as pricing_model,
            service_cost,
            '4096'::TEXT as max_output_tokens,
            NULL::TEXT as error_code;
        RETURN;
    END IF;

    -- Check credit balance
    SELECT COALESCE(balance, 0) INTO credit_balance
    FROM public.credits
    WHERE user_id = p_user_id;

    IF credit_balance IS NULL THEN
        credit_balance := 0;
    END IF;
    
    -- Check if user has unpaid balances from previous months
    SELECT EXISTS (
        SELECT 1 
        FROM public.payg_usage 
        WHERE user_id = p_user_id 
        AND payment_status = 'unpaid'
        AND month < date_trunc('month', CURRENT_DATE)
    ) INTO has_unpaid_previous_month;
    
    -- Return error if there are unpaid balances
    IF has_unpaid_previous_month THEN
        RETURN QUERY SELECT 
            FALSE::BOOLEAN as can_use,
            NULL::TEXT as pricing_model,
            0::DECIMAL(18,9) as cost,
            NULL::TEXT as max_output_tokens,
            'unpaid_balance'::TEXT as error_code;
        RETURN;
    END IF;
    
    -- Special handling for token-based transcription models (GPT-4o)
    IF is_token_based AND p_service = 'transcription' THEN
        -- Get token costs for this model
        SELECT sp.cost_per_unit INTO input_cost_per_unit
        FROM public.service_pricing sp
        WHERE sp.service = 'transcription'
        AND sp.model = p_model || '/input'
        AND sp.is_active = true;
        
        SELECT sp.cost_per_unit INTO output_cost_per_unit
        FROM public.service_pricing sp
        WHERE sp.service = 'transcription'
        AND sp.model = p_model || '/output'
        AND sp.is_active = true;
        
        IF input_cost_per_unit IS NULL OR output_cost_per_unit IS NULL THEN
            RETURN QUERY SELECT 
                FALSE::BOOLEAN as can_use,
                NULL::TEXT as pricing_model,
                0::DECIMAL(18,9) as cost,
                NULL::TEXT as max_output_tokens,
                NULL::TEXT as error_code;
            RETURN;
        END IF;
        
        -- Set estimated words per minute based on model
        -- These are conservative estimates that can be adjusted based on real-world data
        IF p_model = 'gpt-4o-mini-transcribe' THEN
            avg_tokens_per_minute := 200; -- approx. 150 words per minute
        ELSIF p_model = 'gpt-4o-transcribe' THEN
            avg_tokens_per_minute := 200; -- approx. 150 words per minute
        ELSE
            avg_tokens_per_minute := 200; -- default
        END IF;
        
        -- Calculate average token cost
        avg_token_cost := (input_cost_per_unit + output_cost_per_unit) / 2;
        
        -- Calculate how many minutes of audio the user can afford
        estimated_minutes := FLOOR(credit_balance / (avg_tokens_per_minute * avg_token_cost));
        
        IF estimated_minutes > 0 THEN
            RETURN QUERY SELECT 
                TRUE as can_use,
                'credits'::TEXT as pricing_model,
                input_cost_per_unit * avg_tokens_per_minute * p_amount as cost, -- Estimated cost for the requested minutes
                estimated_minutes::TEXT as max_output_tokens, -- Return estimated minutes as max_output_tokens
                NULL::TEXT as error_code;
            RETURN;
        END IF;
    END IF;

    -- Special handling for optimization service (LLM calls)
    IF p_service = 'optimization' THEN
        -- Try to get separate input/output pricing
        SELECT sp.cost_per_unit INTO input_cost_per_unit
        FROM public.service_pricing sp
        WHERE sp.service = 'optimization'
        AND sp.model = p_model || '/input'
        AND sp.is_active = TRUE;

        SELECT sp.cost_per_unit INTO output_cost_per_unit
        FROM public.service_pricing sp
        WHERE sp.service = 'optimization'
        AND sp.model = p_model || '/output'
        AND sp.is_active = TRUE;

        -- If we have separate input/output pricing
        IF input_cost_per_unit IS NOT NULL AND output_cost_per_unit IS NOT NULL THEN
            -- Calculate input tokens cost
            service_cost := input_cost_per_unit * p_amount;
            
            -- If user has any credits at all
            IF credit_balance > 0 THEN
                -- Calculate how many output tokens they can afford
                -- First subtract the cost of input tokens
                IF credit_balance > service_cost THEN
                    -- Remaining credits after input tokens
                    credit_balance := credit_balance - service_cost;
                    
                    -- Calculate max output tokens they can afford
                    max_tokens := FLOOR(credit_balance / output_cost_per_unit);
                    
                    -- Cap at a reasonable maximum (4096 is common for many models)
                    max_tokens := LEAST(max_tokens, 4096);
                    
                    RETURN QUERY SELECT 
                        TRUE as can_use,
                        'credits'::TEXT as pricing_model,
                        service_cost, -- This is just the input cost for now
                        max_tokens::TEXT as max_output_tokens,
                        NULL::TEXT as error_code;
                    RETURN;
                END IF;
            END IF;
        ELSE
            -- If we don't have separate pricing, use the combined pricing
            -- If user has any credits at all and they can cover at least the input tokens
            IF credit_balance > 0 AND cost_per_unit IS NOT NULL THEN
                -- Calculate max tokens they can afford (input + output)
                max_tokens := FLOOR(credit_balance / cost_per_unit) - p_amount;
                
                -- If they can afford at least some output tokens
                IF max_tokens > 0 THEN
                    -- Cap at a reasonable maximum
                    max_tokens := LEAST(max_tokens, 4096);
                    
                    RETURN QUERY SELECT 
                        TRUE as can_use,
                        'credits'::TEXT as pricing_model,
                        service_cost,
                        max_tokens::TEXT as max_output_tokens,
                        NULL::TEXT as error_code;
                    RETURN;
                END IF;
            END IF;
        END IF;
    ELSE
        -- For non-optimization services and non-token-based transcription, use the original logic
        IF NOT is_token_based AND credit_balance >= service_cost THEN 
            RETURN QUERY SELECT 
                TRUE as can_use,
                'credits'::TEXT as pricing_model,
                service_cost,
                NULL::TEXT as max_output_tokens,
                NULL::TEXT as error_code;
            RETURN;
        END IF;
    END IF;

    -- Check if PAYG is allowed for this API key
    SELECT COALESCE(allow_payg, FALSE) INTO payg_allowed
    FROM public.api_keys
    WHERE id = p_api_key_id
    AND is_active = TRUE;

    IF payg_allowed IS NULL THEN
        payg_allowed := FALSE;
    END IF;

    -- Fallback to PAYG if allowed, user has stripe customer id, and no unpaid balances
    IF payg_allowed 
       AND NOT has_unpaid_previous_month
       AND EXISTS (
        SELECT 1 FROM public.profiles
        WHERE id = p_user_id
        AND stripe_customer_id IS NOT NULL
    ) THEN
        RETURN QUERY SELECT 
            TRUE::BOOLEAN as can_use,
            'payg'::TEXT as pricing_model,
            service_cost,
            '4096'::TEXT as max_output_tokens,
            NULL::TEXT as error_code;
        RETURN;
    END IF;

    -- If we get here, no payment method is available
    RETURN QUERY SELECT 
        FALSE::BOOLEAN as can_use,
        NULL::TEXT as pricing_model,
        service_cost,
        NULL::TEXT as max_output_tokens,
        'insufficient_credits'::TEXT as error_code;
END;
$$;

-- Update finalize_usage function to handle token-based billing for GPT-4o models
CREATE OR REPLACE FUNCTION "public"."finalize_usage"("p_user_id" "uuid", "p_service" "text", "p_model" "text", "p_amount" numeric, "p_cost" numeric, "p_pricing_model" "text", "p_metadata" "jsonb") RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    input_token_count INTEGER;
    output_token_count INTEGER;
    input_cost_per_token DECIMAL(18, 9);
    output_cost_per_token DECIMAL(18, 9);
    total_token_cost DECIMAL(18, 9);
    is_token_based BOOLEAN;
    pricing_unit TEXT;
BEGIN
    -- Check if this is a token-based model by looking at the unit
    SELECT sp.unit INTO pricing_unit
    FROM public.service_pricing sp
    WHERE sp.service = p_service
    AND sp.model = p_model
    AND sp.is_active = TRUE;
    
    -- Set token-based flag
    is_token_based := pricing_unit = 'token-based';
    
    -- Handle token-based billing for GPT-4o transcription models
    IF is_token_based AND p_service = 'transcription' AND (
        p_model = 'gpt-4o-mini-transcribe' OR 
        p_model = 'gpt-4o-transcribe'
    ) THEN
        -- Extract token counts from metadata
        input_token_count := COALESCE((p_metadata->>'input_tokens')::INTEGER, 0);
        output_token_count := COALESCE((p_metadata->>'output_tokens')::INTEGER, 0);
        
        -- Get token costs for this model
        SELECT sp.cost_per_unit INTO input_cost_per_token
        FROM public.service_pricing sp
        WHERE sp.service = 'transcription'
        AND sp.model = p_model || '/input'
        AND sp.is_active = true;
        
        SELECT sp.cost_per_unit INTO output_cost_per_token
        FROM public.service_pricing sp
        WHERE sp.service = 'transcription'
        AND sp.model = p_model || '/output'
        AND sp.is_active = true;
        
        -- Calculate total cost based on token usage
        total_token_cost := (input_token_count * input_cost_per_token) + 
                            (output_token_count * output_cost_per_token);
        
        -- Update usage history with token information and calculated cost
        UPDATE public.usage_history
        SET status = 'success',
            amount = p_amount, -- Original amount (duration) for compatibility
            cost = ROUND(total_token_cost, 9),
            metadata = p_metadata || jsonb_build_object(
                'token_based', true,
                'input_tokens', input_token_count,
                'output_tokens', output_token_count,
                'input_cost', ROUND(input_token_count * input_cost_per_token, 9),
                'output_cost', ROUND(output_token_count * output_cost_per_token, 9)
            )
        WHERE id = (
            SELECT id 
            FROM public.usage_history 
            WHERE user_id = p_user_id
            AND service = p_service
            AND model = p_model
            AND status = 'pending'
            ORDER BY created_at DESC
            LIMIT 1
        );
        
        -- Update credits if using credits pricing model
        IF p_pricing_model = 'credits' THEN
            UPDATE public.credits
            SET balance = ROUND(balance - total_token_cost, 9)
            WHERE user_id = p_user_id;
        END IF;
        
        -- Handle PAYG usage recording
        IF p_pricing_model = 'payg' THEN
            INSERT INTO public.payg_usage (
                user_id, 
                month, 
                total_amount, 
                payment_status
            ) VALUES (
                p_user_id, 
                date_trunc('month', CURRENT_DATE), 
                total_token_cost,
                'pending'
            )
            ON CONFLICT (user_id, month) 
            DO UPDATE SET 
                total_amount = ROUND(payg_usage.total_amount + EXCLUDED.total_amount, 9),
                payment_status = 
                    -- Only change to pending if it was previously paid
                    CASE 
                        WHEN payg_usage.payment_status = 'paid' THEN 'pending'
                        ELSE payg_usage.payment_status
                    END;
        END IF;
    ELSE
        -- Use standard logic for non-token-based models
        -- Update usage history to success using a subquery to handle ORDER BY and LIMIT
        UPDATE public.usage_history
        SET status = 'success',
            amount = p_amount,
            cost = ROUND(p_cost, 9),
            metadata = p_metadata
        WHERE id = (
            SELECT id 
            FROM public.usage_history 
            WHERE user_id = p_user_id
            AND service = p_service
            AND model = p_model
            AND status = 'pending'
            ORDER BY created_at DESC
            LIMIT 1
        );

        -- Update credits if using credits pricing model
        IF p_pricing_model = 'credits' THEN
            UPDATE public.credits
            SET balance = ROUND(balance - p_cost, 9)
            WHERE user_id = p_user_id;
        END IF;

        -- Update quota if using subscription
        IF p_pricing_model = 'subscription' THEN
            UPDATE public.quotas q
            SET used_amount = ROUND(used_amount + p_amount, 9)
            FROM public.user_subscriptions us
            WHERE q.subscription_id = us.id
            AND us.user_id = p_user_id
            AND q.service = p_service
            AND us.status = 'active'
            AND q.reset_date > NOW();
        END IF;

        -- Handle PAYG usage recording with new payment_status
        IF p_pricing_model = 'payg' THEN
            INSERT INTO public.payg_usage (
                user_id, 
                month, 
                total_amount, 
                payment_status
            ) VALUES (
                p_user_id, 
                date_trunc('month', CURRENT_DATE), 
                p_cost,
                'pending'
            )
            ON CONFLICT (user_id, month) 
            DO UPDATE SET 
                total_amount = ROUND(payg_usage.total_amount + EXCLUDED.total_amount, 9),
                payment_status = 
                    -- Only change to pending if it was previously paid
                    CASE 
                        WHEN payg_usage.payment_status = 'paid' THEN 'pending'
                        ELSE payg_usage.payment_status
                    END;
        END IF;
    END IF;
END;
$$; 
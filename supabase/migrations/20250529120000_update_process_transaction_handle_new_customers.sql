-- Update the process_completed_transaction function to automatically create new customers
-- and handle amount conversion (from cents to dollars/credits)

CREATE OR REPLACE FUNCTION public.process_completed_transaction(
    p_paddle_transaction_id TEXT,
    p_paddle_customer_id TEXT,
    p_amount DECIMAL(18,2),
    p_currency TEXT,
    p_product_id TEXT DEFAULT NULL,
    p_receipt_url TEXT DEFAULT NULL,
    p_metadata JSONB DEFAULT '{}'::jsonb
) RETURNS BOOLEAN
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
DECLARE
    v_user_id UUID;
    v_customer_id UUID;
    v_product_id UUID;
    v_credit_amount DECIMAL(18,9);
    v_transaction_id UUID;
    v_existing_transaction UUID;
    v_email TEXT;
    v_customer_name TEXT;
BEGIN
    -- Check if transaction already exists
    SELECT id INTO v_existing_transaction
    FROM paddle.transactions
    WHERE paddle_transaction_id = p_paddle_transaction_id;

    IF v_existing_transaction IS NOT NULL THEN
        -- Transaction already processed
        RETURN TRUE;
    END IF;

    -- Get customer and user info
    SELECT user_id, id INTO v_user_id, v_customer_id
    FROM paddle.customers
    WHERE paddle_customer_id = p_paddle_customer_id;

    -- If we found a customer ID but no user ID, something is wrong with the data
    IF v_customer_id IS NOT NULL AND v_user_id IS NULL THEN
        RAISE NOTICE 'Customer record found but missing user_id for paddle_customer_id: %', p_paddle_customer_id;
        
        -- Try to recover by updating the customer record with a new user ID
        v_user_id := gen_random_uuid();
        UPDATE paddle.customers SET user_id = v_user_id WHERE id = v_customer_id;
    END IF;

    -- Handle new customers by creating a record for them
    IF v_user_id IS NULL THEN
        -- Extract customer email from metadata
        v_email := p_metadata->>'email';
        v_customer_name := COALESCE(
            p_metadata->>'name',
            p_metadata->>'customer_name',
            'VoiceHype User'
        );
        
        IF v_email IS NULL THEN
            -- Try to find email in nested structure
            v_email := p_metadata->'customer'->>'email';
        END IF;
        
        IF v_email IS NULL THEN
            -- If still no email, create a placeholder
            v_email := 'customer-' || p_paddle_customer_id || '@voicehype.ai';
        END IF;
        
        -- Create an anonymous user in auth.users
        INSERT INTO auth.users (
            id,
            email,
            raw_app_meta_data,
            raw_user_meta_data,
            created_at,
            updated_at
        ) VALUES (
            gen_random_uuid(),
            v_email,
            jsonb_build_object(
                'provider', 'paddle',
                'providers', array['paddle']
            ),
            jsonb_build_object(
                'name', v_customer_name,
                'paddle_customer_id', p_paddle_customer_id,
                'source', 'paddle_purchase'
            ),
            NOW(),
            NOW()
        ) RETURNING id INTO v_user_id;
        
        -- Create customer record
        INSERT INTO paddle.customers (
            user_id,
            paddle_customer_id,
            email,
            name,
            created_at,
            metadata
        ) VALUES (
            v_user_id,
            p_paddle_customer_id,
            v_email,
            v_customer_name,
            NOW(),
            p_metadata
        ) RETURNING id INTO v_customer_id;
        
        RAISE NOTICE 'Created new customer with ID % for Paddle customer %', v_customer_id, p_paddle_customer_id;
    END IF;

    -- Get product info if product_id provided (for reference only)
    IF p_product_id IS NOT NULL THEN
        SELECT id INTO v_product_id
        FROM paddle.products
        WHERE paddle_product_id = p_product_id AND is_active = TRUE;

        -- Skip the product validation - no need to fail if product is missing
        -- Just log it in the transaction
    END IF;

    -- Convert amount from cents to dollars (credit units)
    -- For VoiceHype, $1 = 1 credit (flexible credit purchasing)
    v_credit_amount := p_amount / 100.0;

    -- Create transaction record
    INSERT INTO paddle.transactions (
        user_id,
        paddle_transaction_id,
        paddle_customer_id,
        product_id,
        status,
        amount,
        currency,
        credit_amount,
        transaction_type,
        paddle_receipt_url,
        metadata,
        created_at,
        updated_at
    ) VALUES (
        v_user_id,
        p_paddle_transaction_id,
        p_paddle_customer_id,
        v_product_id,
        'completed',
        p_amount,
        p_currency,
        v_credit_amount,
        'credit_purchase',
        p_receipt_url,
        p_metadata,
        NOW(),
        NOW()
    ) RETURNING id INTO v_transaction_id;

    -- Add credits to user's account
    INSERT INTO public.credits (user_id, balance, currency, created_at, updated_at)
    VALUES (v_user_id, v_credit_amount, p_currency, NOW(), NOW())
    ON CONFLICT (user_id)
    DO UPDATE SET
        balance = credits.balance + v_credit_amount,
        updated_at = NOW();

    RETURN TRUE;
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.process_completed_transaction TO service_role;

-- Add comment explaining the change
COMMENT ON FUNCTION public.process_completed_transaction IS 'Processes completed Paddle transactions and adds credits to user accounts. Automatically creates new customers when needed, handles amount conversion from cents to dollars, and safely creates profiles with conflict handling.';

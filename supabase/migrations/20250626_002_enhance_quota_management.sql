-- Enhance usage allowance function for subscription-first priority
-- VoiceHype Subscription Pricing Integration
-- Created: 2025-06-26

BEGIN;

-- Add PAYG monthly allowance tracking
ALTER TABLE public.quotas 
ADD COLUMN IF NOT EXISTS payg_allowance_used NUMERIC(18,9) DEFAULT 0,
ADD COLUMN IF NOT EXISTS payg_allowance_total NUMERIC(18,9) DEFAULT 10.00;

-- Create function to reset monthly quotas (for subscription renewal)
CREATE OR REPLACE FUNCTION public.reset_subscription_quotas(p_subscription_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql SECURITY DEFINER
AS $$
BEGIN
  -- Reset subscription quotas to zero usage
  UPDATE public.quotas 
  SET 
    used_amount = 0,
    reset_date = NOW() + INTERVAL '1 month'
  WHERE subscription_id = p_subscription_id;
  
  RETURN TRUE;
END;
$$;

-- Create function to reset PAYG allowances monthly
CREATE OR REPLACE FUNCTION public.reset_payg_allowances()
RETURNS BOOLEAN  
LANGUAGE plpgsql SECURITY DEFINER
AS $$
BEGIN
  -- Reset PAYG allowances for all users with active subscriptions
  UPDATE public.quotas 
  SET payg_allowance_used = 0
  WHERE subscription_id IN (
    SELECT id FROM public.user_subscriptions 
    WHERE status = 'active'
  );
  
  RETURN TRUE;
END;
$$;

-- Enhanced check_usage_allowance function with subscription-first priority
CREATE OR REPLACE FUNCTION public.check_usage_allowance_v2(
  p_user_id UUID,
  p_service TEXT,
  p_model TEXT,
  p_amount NUMERIC,
  p_api_key_id UUID,
  p_is_input_only BOOLEAN DEFAULT FALSE
)
RETURNS TABLE(
  can_use BOOLEAN,
  pricing_model TEXT,
  cost NUMERIC,
  max_output_tokens TEXT,
  error_code TEXT
)
LANGUAGE plpgsql SECURITY DEFINER
AS $$
DECLARE
    subscription_quota_available BOOLEAN := FALSE;
    credit_balance DECIMAL(18,9);
    payg_allowance_available DECIMAL(18,9);
    service_cost DECIMAL(18,9);
    cost_per_unit DECIMAL(18,9);
    remaining_quota DECIMAL(18,9);
    has_active_subscription BOOLEAN := FALSE;
    max_tokens INTEGER;
BEGIN
    -- Get cost per unit for pricing calculations
    SELECT sp.cost_per_unit INTO cost_per_unit
    FROM public.service_pricing sp
    WHERE sp.service = p_service
    AND sp.model = p_model
    AND sp.is_active = TRUE;

    -- Calculate service cost
    IF cost_per_unit IS NOT NULL THEN
        service_cost := cost_per_unit * p_amount;
    END IF;

    -- PRIORITY 1: Check subscription quota first
    SELECT EXISTS (
        SELECT 1 FROM public.quotas q
        JOIN public.user_subscriptions us ON q.subscription_id = us.id
        WHERE us.user_id = p_user_id
        AND q.service = p_service
        AND us.status = 'active'
        AND q.reset_date > NOW()
        AND (q.total_amount - q.used_amount) >= p_amount
    ) INTO subscription_quota_available;

    -- Check if user has any active subscription
    SELECT EXISTS (
        SELECT 1 FROM public.user_subscriptions
        WHERE user_id = p_user_id
        AND status = 'active'
        AND current_period_end > NOW()
    ) INTO has_active_subscription;

    IF subscription_quota_available THEN
        -- Use subscription quota
        RETURN QUERY SELECT 
            TRUE as can_use,
            'subscription'::TEXT as pricing_model,
            0::NUMERIC as cost,
            '4096'::TEXT as max_output_tokens,
            NULL::TEXT as error_code;
        RETURN;
    END IF;

    -- PRIORITY 2: Check prepaid credits
    SELECT COALESCE(SUM(balance), 0) INTO credit_balance
    FROM public.credits
    WHERE user_id = p_user_id
    AND balance > 0
    AND (expires_at IS NULL OR expires_at > NOW())
    AND status = 'active';

    IF credit_balance >= service_cost THEN
        -- Use credits
        RETURN QUERY SELECT 
            TRUE as can_use,
            'credits'::TEXT as pricing_model,
            service_cost as cost,
            '4096'::TEXT as max_output_tokens,
            NULL::TEXT as error_code;
        RETURN;
    END IF;

    -- PRIORITY 3: Check PAYG allowance (only for paid subscribers)
    IF has_active_subscription THEN
        SELECT COALESCE((payg_allowance_total - payg_allowance_used), 0) INTO payg_allowance_available
        FROM public.quotas q
        JOIN public.user_subscriptions us ON q.subscription_id = us.id
        WHERE us.user_id = p_user_id
        AND us.status = 'active'
        AND q.service = p_service
        LIMIT 1;

        IF payg_allowance_available >= service_cost THEN
            -- Use PAYG allowance
            RETURN QUERY SELECT 
                TRUE as can_use,
                'payg'::TEXT as pricing_model,
                service_cost as cost,
                '4096'::TEXT as max_output_tokens,
                NULL::TEXT as error_code;
            RETURN;
        END IF;
    END IF;

    -- No usage available
    RETURN QUERY SELECT 
        FALSE as can_use,
        NULL::TEXT as pricing_model,
        service_cost as cost,
        NULL::TEXT as max_output_tokens,
        'insufficient_quota'::TEXT as error_code;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.reset_subscription_quotas(UUID) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.reset_payg_allowances() TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.check_usage_allowance_v2(UUID, TEXT, TEXT, NUMERIC, UUID, BOOLEAN) TO authenticated, service_role, anon;

COMMIT;

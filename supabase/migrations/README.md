# Database Migration Instructions

## Fix for "Database error saving new user" during registration

We've created a migration file to fix the issue with user registration failing due to a database constraint error. The problem is that the `handle_new_user` function tries to insert records into the `quotas` table without providing a `subscription_id`, which is currently a required field.

### Migration File: `20250312_001_make_subscription_id_optional.sql`

This migration:
1. Makes the `subscription_id` column in the `quotas` table optional (nullable)
2. Updates the service check constraint to include `input_tokens` and `output_tokens`
3. Updates the `handle_new_user` function to explicitly set `subscription_id` to NULL

## How to Apply the Migration

### Option 1: Using Supabase CLI

If you have the Supabase CLI installed:

```bash
# Navigate to your project directory
cd /path/to/your/project

# Apply the migration
supabase db push
```

### Option 2: Using the Supabase Dashboard

1. Log in to your Supabase dashboard
2. Go to the SQL Editor
3. Copy the contents of the migration file
4. Paste it into a new SQL query
5. Run the query

## Verifying the Fix

After applying the migration, try registering a new user. The registration should now complete successfully without the "Database error saving new user" error.

## Troubleshooting

If you continue to experience issues after applying the migration:

1. Check the Supabase logs for any error messages
2. Verify that the migration was applied successfully by checking if the `subscription_id` column in the `quotas` table is now nullable
3. Test the `handle_new_user` function by manually inserting a record into the `auth.users` table

If problems persist, please contact the development team for assistance.

# Migrations

This directory contains SQL migration files for the VoiceHype database.

## Migration Files

- **20250308_001_voicehype_schema.sql**: Initial schema setup
- **20250308_005_usage_functions.sql**: Add usage tracking functions 
- **20250308_010_security_improvements.sql**: Security enhancements
- **20250311_002_add_usage_summary_function.sql**: Add function for usage statistics
- **20250321_001_add_expires_at_to_credits.sql**: Add credit expiration
- **20250321_002_enhance_credit_usage_logic.sql**: Improve credit deduction logic
- **20250321_003_update_usage_summary_function.sql**: Exclude failed/pending entries from costs
- **20250321_004_enhance_payg_payment_status.sql**: Enhance PAYG payment status tracking

## Latest Migration Details

### 20250321_004_enhance_payg_payment_status.sql

This migration improves the pay-as-you-go (PAYG) payment tracking system:

1. **New Payment Status Enum**:
   - Replaces boolean `is_paid` with more detailed `payment_status` enum
   - Statuses: `pending`, `paid`, `unpaid`
   - Adds `payment_metadata` JSONB field for additional payment details

2. **Updated Functions**:
   - `has_unpaid_payg_balance`: Uses new payment_status field
   - `get_unpaid_payg_balances`: Returns payment status and metadata
   - `finalize_usage`: Updated to manage new payment status field
   - `check_usage_allowance`: Updated to check for unpaid balances  

3. **New Payment Management Functions**:
   - `mark_payg_payment_as_paid`: For marking payments as paid with metadata
   - `mark_payg_payment_as_unpaid`: For marking payments as unpaid with failure reason

These changes allow for a more detailed tracking of payment states and better error handling in the PAYG system. 
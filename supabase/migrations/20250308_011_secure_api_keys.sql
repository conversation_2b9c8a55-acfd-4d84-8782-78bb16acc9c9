-- 20240816_001_secure_api_keys.sql
-- Migration to replace direct API key RLS policies with secure function-based approach

-- First, drop existing API key RLS policies
DROP POLICY IF EXISTS "API Keys - Insert own with limit" ON public.api_keys;
DROP POLICY IF EXISTS "API Keys - Update own" ON public.api_keys;

-- Keep the select and delete policies as they're safer
-- DROP POLICY IF EXISTS "API Keys - Select own" ON public.api_keys;
-- DROP POLICY IF EXISTS "API Keys - Delete own" ON public.api_keys;

-- Create a secure function for API key creation
CREATE OR REPLACE FUNCTION public.create_api_key(
    p_name TEXT,
    p_expires_at TIMESTAMPTZ DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    v_user_id UUID;
    v_key_secret TEXT;
    v_key_prefix TEXT;
    v_key_hash TEXT;
    v_api_key_id UUID;
    v_key_count INTEGER;
    v_created_at TIMESTAMPTZ;
    v_final_expires_at TIMESTAMPTZ;
BEGIN
    -- Get the current user's ID
    v_user_id := auth.uid();
    
    -- Check if user is authenticated
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'Not authenticated';
    END IF;
    
    -- Check API key limit (belt-and-suspenders approach with the trigger)
    SELECT COUNT(*) INTO v_key_count FROM public.api_keys WHERE user_id = v_user_id;
    IF v_key_count >= 25 THEN
        RAISE EXCEPTION 'Maximum number of API keys (25) reached for this user';
    END IF;
    
    -- Generate a secure API key
    v_key_secret := encode(gen_random_bytes(24), 'base64');
    v_key_prefix := substring(v_key_secret from 1 for 8);
    v_key_hash := crypt(v_key_secret, gen_salt('bf'));
    
    -- Set the creation time
    v_created_at := NOW();
    
    -- Set expiration date to one year from creation if not provided
    IF p_expires_at IS NULL THEN
        v_final_expires_at := v_created_at + INTERVAL '1 year';
    ELSE
        -- Validate user-provided expiration date
        IF p_expires_at <= v_created_at THEN
            RAISE EXCEPTION 'Expiration date must be in the future';
        END IF;
        v_final_expires_at := p_expires_at;
    END IF;
    
    -- Insert the new API key with controlled fields
    INSERT INTO public.api_keys (
        user_id,
        name,
        key_prefix,
        key_hash,
        created_at,
        expires_at
    ) VALUES (
        v_user_id,
        p_name,
        v_key_prefix,
        v_key_hash,
        v_created_at,  -- Server-controlled timestamp
        v_final_expires_at  -- Default to one year from creation if not specified
    ) RETURNING id INTO v_api_key_id;
    
    -- Store the full key in a temporary table or return it directly
    -- For this example, we'll return just the ID, but in practice
    -- you would need to show the full key to the user once
    RETURN v_api_key_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a secure function for updating API key name
CREATE OR REPLACE FUNCTION public.update_api_key_name(
    p_api_key_id UUID,
    p_name TEXT
) RETURNS BOOLEAN AS $$
DECLARE
    v_user_id UUID;
    v_count INTEGER;
BEGIN
    -- Get the current user's ID
    v_user_id := auth.uid();
    
    -- Check if user is authenticated
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'Not authenticated';
    END IF;
    
    -- Check if the API key belongs to the user
    SELECT COUNT(*) INTO v_count 
    FROM public.api_keys 
    WHERE id = p_api_key_id AND user_id = v_user_id;
    
    IF v_count = 0 THEN
        RAISE EXCEPTION 'API key not found or access denied';
    END IF;
    
    -- Update only the name field
    UPDATE public.api_keys
    SET name = p_name
    WHERE id = p_api_key_id;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add RLS policy to prevent any direct inserts/updates
-- This policy will effectively block all direct inserts
CREATE POLICY "API Keys - No direct inserts" ON public.api_keys
    FOR INSERT
    WITH CHECK (false);

-- This policy will effectively block all direct updates
CREATE POLICY "API Keys - No direct updates" ON public.api_keys
    FOR UPDATE
    USING (false);

-- Grant execute permissions on the new functions
GRANT EXECUTE ON FUNCTION public.create_api_key TO authenticated;
GRANT EXECUTE ON FUNCTION public.update_api_key_name TO authenticated;

-- Add function comments
COMMENT ON FUNCTION public.create_api_key IS 'Creates a new API key for the authenticated user with a default expiration of one year from creation';
COMMENT ON FUNCTION public.update_api_key_name IS 'Updates only the name of an API key owned by the authenticated user'; 
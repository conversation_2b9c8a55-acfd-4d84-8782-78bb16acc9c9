-- VoiceHype Security Improvements Migration
-- Date: 2024-08-12
BEGIN;

-- First, drop all existing RLS policies to recreate them with stricter rules
DROP POLICY IF EXISTS "Profiles access" ON public.profiles;
DROP POLICY IF EXISTS "API Keys access" ON public.api_keys;
DROP POLICY IF EXISTS "Credits access" ON public.credits;
DROP POLICY IF EXISTS "Subscriptions access" ON public.user_subscriptions;
DROP POLICY IF EXISTS "Quotas access" ON public.quotas;
DROP POLICY IF EXISTS "PAYG access" ON public.payg_usage;
DROP POLICY IF EXISTS "Usage history access" ON public.usage_history;
DROP POLICY IF EXISTS "Pricing Models - Public read" ON public.pricing_models;
DROP POLICY IF EXISTS "Service Pricing - Public read" ON public.service_pricing;
DROP POLICY IF EXISTS "Subscription Plans - Public read" ON public.subscription_plans;

-- Make sure RLS is enabled on all tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.api_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.credits ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.quotas ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payg_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.usage_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.pricing_models ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.service_pricing ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscription_plans ENABLE ROW LEVEL SECURITY;

-- Create new policies with stricter permissions

-- 1. API Keys - Allow all CRUD operations for authenticated users, but only on their own keys
CREATE POLICY "API Keys - Select own" ON public.api_keys
    FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "API Keys - Insert own with limit" ON public.api_keys
    FOR INSERT
    WITH CHECK (
        auth.uid() = user_id AND
        (SELECT COUNT(*) FROM public.api_keys WHERE user_id = auth.uid()) < 25
    );

CREATE POLICY "API Keys - Update own" ON public.api_keys
    FOR UPDATE
    USING (auth.uid() = user_id);

CREATE POLICY "API Keys - Delete own" ON public.api_keys
    FOR DELETE
    USING (auth.uid() = user_id);

-- 2. Profiles - Allow users to only view and update their own profile
CREATE POLICY "Profiles - Select own" ON public.profiles
    FOR SELECT
    USING (auth.uid() = id);

CREATE POLICY "Profiles - Update own" ON public.profiles
    FOR UPDATE
    USING (auth.uid() = id);

-- 3. Credits - Allow users to only view their own credits
CREATE POLICY "Credits - Select own" ON public.credits
    FOR SELECT
    USING (auth.uid() = user_id);

-- 4. User Subscriptions - Allow users to only view their own subscriptions
CREATE POLICY "Subscriptions - Select own" ON public.user_subscriptions
    FOR SELECT
    USING (auth.uid() = user_id);

-- 5. Quotas - Allow users to only view their own quotas
CREATE POLICY "Quotas - Select own" ON public.quotas
    FOR SELECT
    USING (auth.uid() = user_id);

-- 6. PAYG Usage - Allow users to only view their own PAYG usage
CREATE POLICY "PAYG - Select own" ON public.payg_usage
    FOR SELECT
    USING (auth.uid() = user_id);

-- 7. Usage History - Allow users to only view their own usage history
CREATE POLICY "Usage History - Select own" ON public.usage_history
    FOR SELECT
    USING (auth.uid() = user_id);

-- 8. Public read-only access to pricing models, service pricing, and subscription plans
-- These policies allow ANYONE (including unauthenticated users) to view these tables
-- But explicitly DENY any modification operations
CREATE POLICY "Pricing Models - Public read" ON public.pricing_models
    FOR SELECT
    USING (true);

CREATE POLICY "Service Pricing - Public read" ON public.service_pricing
    FOR SELECT
    USING (true);

CREATE POLICY "Subscription Plans - Public read" ON public.subscription_plans
    FOR SELECT
    USING (true);

-- Explicitly deny all modification operations on public reference tables
-- This is redundant but makes the intention clear
CREATE POLICY "Pricing Models - No insert" ON public.pricing_models
    FOR INSERT
    WITH CHECK (false);

CREATE POLICY "Pricing Models - No update" ON public.pricing_models
    FOR UPDATE
    USING (false);

CREATE POLICY "Pricing Models - No delete" ON public.pricing_models
    FOR DELETE
    USING (false);

CREATE POLICY "Service Pricing - No insert" ON public.service_pricing
    FOR INSERT
    WITH CHECK (false);

CREATE POLICY "Service Pricing - No update" ON public.service_pricing
    FOR UPDATE
    USING (false);

CREATE POLICY "Service Pricing - No delete" ON public.service_pricing
    FOR DELETE
    USING (false);

CREATE POLICY "Subscription Plans - No insert" ON public.subscription_plans
    FOR INSERT
    WITH CHECK (false);

CREATE POLICY "Subscription Plans - No update" ON public.subscription_plans
    FOR UPDATE
    USING (false);

CREATE POLICY "Subscription Plans - No delete" ON public.subscription_plans
    FOR DELETE
    USING (false);

-- Create a function to enforce API key limit
CREATE OR REPLACE FUNCTION check_api_key_limit()
RETURNS TRIGGER AS $$
BEGIN
    IF (SELECT COUNT(*) FROM public.api_keys WHERE user_id = NEW.user_id) >= 25 THEN
        RAISE EXCEPTION 'Maximum number of API keys (25) reached for this user';
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a trigger to enforce the API key limit
DROP TRIGGER IF EXISTS enforce_api_key_limit ON public.api_keys;
CREATE TRIGGER enforce_api_key_limit
    BEFORE INSERT ON public.api_keys
    FOR EACH ROW
    EXECUTE FUNCTION check_api_key_limit();

-- Add a comment explaining how to modify reference tables
COMMENT ON TABLE public.pricing_models IS 'Reference table for pricing models. Modifications should be done via migrations or by superusers directly.';
COMMENT ON TABLE public.service_pricing IS 'Reference table for service pricing. Modifications should be done via migrations or by superusers directly.';
COMMENT ON TABLE public.subscription_plans IS 'Reference table for subscription plans. Modifications should be done via migrations or by superusers directly.';

-- =============================================
-- SECURE DATABASE FUNCTIONS
-- =============================================

-- Create a schema for internal functions that should not be directly accessible via API
CREATE SCHEMA IF NOT EXISTS internal;

-- Grant usage on the internal schema to the service_role only
REVOKE ALL ON SCHEMA internal FROM PUBLIC;
GRANT USAGE ON SCHEMA internal TO service_role;

-- Create a pre-request check function to block direct RPC access to sensitive functions
CREATE OR REPLACE FUNCTION public.check_rpc_access()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    requested_function text;
    is_service_role boolean;
    is_edge_function boolean;
BEGIN
    -- Get the requested function name from the request path
    requested_function := current_setting('request.path', true);
    
    -- Check if this is coming from a service_role or edge function
    is_service_role := (SELECT rolsuper FROM pg_roles WHERE rolname = current_user);
    is_edge_function := current_setting('request.jwt.claims', true)::json->>'role' = 'service_role';
    
    -- Block direct access to sensitive functions unless from service_role or edge function
    IF requested_function LIKE '/rpc/validate_api_key' 
       OR requested_function LIKE '/rpc/check_usage_allowance'
       OR requested_function LIKE '/rpc/finalize_usage'
    THEN
        IF NOT (is_service_role OR is_edge_function) THEN
            RAISE EXCEPTION 'Access denied: This function can only be called from edge functions';
        END IF;
    END IF;
END;
$$;

-- Register the pre-request check function to run on every API request
ALTER ROLE authenticator SET pgrst.db_pre_request = 'public.check_rpc_access';

-- Move sensitive functions to internal schema (for new projects)
-- For existing projects, we'll create secure wrappers instead

-- Create secure wrappers for existing functions
-- These wrappers ensure only authorized calls can be made

-- Secure validate_api_key
CREATE OR REPLACE FUNCTION public.validate_api_key(p_key TEXT) 
RETURNS TABLE (
    user_id UUID,
    api_key_id UUID
) AS $$
BEGIN
    -- Check if this is being called from an edge function or service role
    IF NOT (
        current_setting('request.jwt.claims', true)::json->>'role' = 'service_role' OR
        (SELECT rolsuper FROM pg_roles WHERE rolname = current_user)
    ) THEN
        RAISE EXCEPTION 'Access denied: This function can only be called from edge functions';
    END IF;
    
    -- If authorized, call the original function
    RETURN QUERY SELECT v.user_id, v.api_key_id FROM public.validate_api_key(p_key) v;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Secure check_usage_allowance
CREATE OR REPLACE FUNCTION public.check_usage_allowance(
    p_user_id UUID, 
    p_service TEXT, 
    p_model TEXT, 
    p_amount DECIMAL, 
    p_api_key_id UUID
) RETURNS TABLE (
    can_use BOOLEAN,
    pricing_model TEXT,
    cost DECIMAL
) AS $$
BEGIN
    -- Check if this is being called from an edge function or service role
    IF NOT (
        current_setting('request.jwt.claims', true)::json->>'role' = 'service_role' OR
        (SELECT rolsuper FROM pg_roles WHERE rolname = current_user)
    ) THEN
        RAISE EXCEPTION 'Access denied: This function can only be called from edge functions';
    END IF;
    
    -- If authorized, call the original function
    RETURN QUERY SELECT c.can_use, c.pricing_model, c.cost 
                 FROM public.check_usage_allowance(p_user_id, p_service, p_model, p_amount, p_api_key_id) c;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Secure finalize_usage
CREATE OR REPLACE FUNCTION public.finalize_usage(
    p_user_id UUID,
    p_service TEXT,
    p_model TEXT,
    p_amount DECIMAL,
    p_cost DECIMAL,
    p_pricing_model TEXT,
    p_metadata JSONB
) RETURNS VOID AS $$
BEGIN
    -- Check if this is being called from an edge function or service role
    IF NOT (
        current_setting('request.jwt.claims', true)::json->>'role' = 'service_role' OR
        (SELECT rolsuper FROM pg_roles WHERE rolname = current_user)
    ) THEN
        RAISE EXCEPTION 'Access denied: This function can only be called from edge functions';
    END IF;
    
    -- If authorized, call the original function
    PERFORM public.finalize_usage(p_user_id, p_service, p_model, p_amount, p_cost, p_pricing_model, p_metadata);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Reload PostgREST configuration to apply the pre-request check
NOTIFY pgrst, 'reload config';

COMMIT; 
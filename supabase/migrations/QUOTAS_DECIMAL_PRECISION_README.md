# Quotas Table Decimal Precision Fix

## Issue

The `quotas` table was using integer columns for `used_amount` and `total_amount`, which is appropriate for optimization tokens (which are integers) but not for transcription minutes, which need decimal precision.

In the `usage_history` table, amounts are stored with high precision using `numeric(18,9)`, but the `quotas` table was using integers. This caused a mismatch when tracking transcription usage, as partial minutes were being truncated to integers.

## Solution

We've created a migration file to fix this issue:

### Migration File: `20240312_001_fix_quotas_decimal_precision.sql`

This migration:
1. Alters the `used_amount` column in the `quotas` table to use `numeric(18,9)` instead of `integer`
2. Alters the `total_amount` column in the `quotas` table to use `numeric(18,9)` instead of `integer`
3. Adds a comment to the table explaining the change

## How to Apply the Migration

### Option 1: Using Supabase CLI

If you have the Supabase CLI installed:

```bash
# Navigate to your project directory
cd /path/to/your/project

# Apply the migration
supabase db push
```

### Option 2: Using the Supabase Dashboard

1. Log in to your Supabase dashboard
2. Go to the SQL Editor
3. Copy the contents of the migration file
4. Paste it into a new SQL query
5. Run the query

## Verifying the Fix

After applying the migration, verify that:
1. The `used_amount` and `total_amount` columns in the `quotas` table are now of type `numeric(18,9)`
2. Existing data has been preserved
3. New transcription usage is being tracked with decimal precision

## Impact on Application

This change will allow for more accurate tracking of transcription usage, especially for small amounts of time (less than a minute). The application code should continue to work as before, but will now have access to more precise quota information. 
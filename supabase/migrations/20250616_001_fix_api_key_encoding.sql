CREATE OR REPLACE FUNCTION "public"."create_api_key"("p_name" "text", "p_expires_at" timestamp with time zone DEFAULT NULL::timestamp with time zone) 
RETURNS "public"."api_key_result" AS $$
DECLARE
    v_user_id UUID;
    v_key_secret TEXT;
    v_key_prefix TEXT;
    v_key_hash TEXT;
    v_api_key_id UUID;
    v_key_count INTEGER;
    v_created_at TIMESTAMPTZ;
    v_final_expires_at TIMESTAMPTZ;
    v_max_expires_at TIMESTAMPTZ;
    v_result public.api_key_result;
    v_raw_bytes BYTEA;
BEGIN
    -- Get the current user's ID
    v_user_id := auth.uid();
    
    -- Check if user is authenticated
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'Not authenticated';
    END IF;
    
    -- Check API key limit (belt-and-suspenders approach with the trigger)
    SELECT COUNT(*) INTO v_key_count FROM public.api_keys WHERE user_id = v_user_id;
    IF v_key_count >= 25 THEN
        RAISE EXCEPTION 'Maximum number of API keys (25) reached for this user';
    END IF;
    
    -- Generate a secure API key using URL-safe base64 encoding (no /, + or = characters)
    v_raw_bytes := extensions.gen_random_bytes(24);
    v_key_secret := replace(replace(replace(encode(v_raw_bytes, 'base64'), '/', '_'), '+', '-'), '=', '');
    
    -- Ensure no spaces or other problematic characters
    v_key_secret := regexp_replace(v_key_secret, '[^a-zA-Z0-9_-]', '', 'g');
    
    -- Extract prefix for storage
    v_key_prefix := substring(v_key_secret from 1 for 8);
    
    -- Hash the key for secure storage
    v_key_hash := extensions.crypt(v_key_secret, extensions.gen_salt('bf'));
    
    -- Set the creation time
    v_created_at := NOW();
    
    -- Calculate maximum expiration date (1 year from creation)
    v_max_expires_at := v_created_at + INTERVAL '1 year';
    
    -- Set expiration date to one year from creation if not provided
    IF p_expires_at IS NULL THEN
        v_final_expires_at := v_max_expires_at;
    ELSE
        -- Validate user-provided expiration date
        IF p_expires_at <= v_created_at THEN
            RAISE EXCEPTION 'Expiration date must be in the future';
        END IF;
        
        -- Limit expiration date to a maximum of 1 year from creation
        v_final_expires_at := LEAST(p_expires_at, v_max_expires_at);
    END IF;
    
    -- Insert the new API key with controlled fields
    INSERT INTO public.api_keys (
        user_id,
        name,
        key_prefix,
        key_hash,
        created_at,
        expires_at
    ) VALUES (
        v_user_id,
        p_name,
        v_key_prefix,
        v_key_hash,
        v_created_at,  -- Server-controlled timestamp
        v_final_expires_at  -- Limited to a maximum of 1 year from creation
    ) RETURNING id INTO v_api_key_id;
    
    -- Return both the ID and the key secret
    v_result.id := v_api_key_id;
    v_result.key_secret := v_key_secret;
    
    RETURN v_result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the function comment
COMMENT ON FUNCTION "public"."create_api_key"("p_name" "text", "p_expires_at" timestamp with time zone) IS 
'Creates a new API key for the authenticated user with a maximum expiration of 1 year and returns both the ID and the key secret. The key is URL-safe with no special characters.';

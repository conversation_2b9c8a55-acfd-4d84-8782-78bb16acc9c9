-- Migration: Add subscription-related transaction types to paddle_transactions
-- Created: 2025-07-04
-- Purpose: Allow subscription upgrades, renewals, and cancellations to be logged
-- Issue: Webhook fails when trying to log subscription-related transaction types

-- Drop the existing transaction_type constraint
ALTER TABLE paddle_transactions 
DROP CONSTRAINT IF EXISTS transactions_transaction_type_check;

-- Add the new constraint with subscription-related types included
ALTER TABLE paddle_transactions 
ADD CONSTRAINT transactions_transaction_type_check 
CHECK (
    transaction_type = ANY (
        ARRAY[
            'credit_purchase'::text,
            'payg_invoice'::text,
            'refund'::text,
            'subscription'::text,
            'subscription_upgrade'::text,
            'subscription_renewal'::text,
            'subscription_cancelled'::text
        ]
    )
);

-- Add comment to document the change
COMMENT ON CONSTRAINT transactions_transaction_type_check ON paddle_transactions 
IS 'Allowed transaction types: credit_purchase, payg_invoice, refund, subscription, subscription_upgrade, subscription_renewal, subscription_cancelled';

-- Log the change
DO $$
BEGIN
    RAISE NOTICE 'Successfully added subscription-related transaction types to paddle_transactions constraint';
    RAISE NOTICE 'New types: subscription_upgrade, subscription_renewal, subscription_cancelled';
    RAISE NOTICE 'This fixes webhook constraint violations when logging subscription transactions';
END $$;

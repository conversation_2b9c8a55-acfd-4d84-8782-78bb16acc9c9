-- Migration: Add subscription transaction type to paddle.transactions
-- Created: 2025-06-27
-- Purpose: Allow subscription transactions to be logged in paddle.transactions table

-- Drop the existing transaction_type constraint
ALTER TABLE paddle.transactions 
DROP CONSTRAINT IF EXISTS transactions_transaction_type_check;

-- Add the new constraint with 'subscription' included
ALTER TABLE paddle.transactions 
ADD CONSTRAINT transactions_transaction_type_check 
CHECK (
    transaction_type = ANY (
        ARRAY[
            'credit_purchase'::text,
            'payg_invoice'::text,
            'refund'::text,
            'subscription'::text
        ]
    )
);

-- Add comment to document the change
COMMENT ON CONSTRAINT transactions_transaction_type_check ON paddle.transactions 
IS 'Allowed transaction types: credit_purchase, payg_invoice, refund, subscription';

-- Log the change
DO $$
BEGIN
    RAISE NOTICE 'Successfully added subscription as valid transaction_type in paddle.transactions';
END $$;

-- Add cancellation support to public.user_subscriptions table
-- This migration adds the cancel_at_period_end column to the main subscription table

-- Add cancel_at_period_end column to public.user_subscriptions
ALTER TABLE public.user_subscriptions 
ADD COLUMN IF NOT EXISTS cancel_at_period_end BOOLEAN DEFAULT FALSE;

-- Add an index for querying subscriptions that are cancelled at period end
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_cancel_at_period_end 
ON public.user_subscriptions(cancel_at_period_end) 
WHERE cancel_at_period_end = TRUE;

-- Add paddle_subscription_id column to link with Paddle (if not exists)
ALTER TABLE public.user_subscriptions 
ADD COLUMN IF NOT EXISTS paddle_subscription_id TEXT;

-- Add index for paddle_subscription_id for webhook lookups
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_paddle_subscription_id 
ON public.user_subscriptions(paddle_subscription_id);

-- Comment on the new columns
COMMENT ON COLUMN public.user_subscriptions.cancel_at_period_end IS 'Whether the subscription will be cancelled at the end of the current period';
COMMENT ON COLUMN public.user_subscriptions.paddle_subscription_id IS 'Paddle subscription ID for webhook processing';

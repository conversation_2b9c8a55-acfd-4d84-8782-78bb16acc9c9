-- Add unique constraint to user_id in credits table
-- This is needed for the ON CONFLICT (user_id) clause in the process_completed_transaction function

-- Add the constraint
ALTER TABLE public.credits ADD CONSTRAINT credits_user_id_key UNIQUE (user_id);

-- Update comment to explain constraint purpose
COMMENT ON CONSTRAINT credits_user_id_key ON public.credits IS 'Ensures each user has only one credit record, allowing for ON CONFLICT updates in functions.';

-- Make credit creation safer by returning the balance if it already exists
CREATE OR REPLACE FUNCTION public.get_or_create_user_credits(
    p_user_id UUID,
    p_initial_balance DECIMAL(18,9) DEFAULT 0,
    p_currency TEXT DEFAULT 'USD'
) RETURNS DECIMAL(18,9)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_balance DECIMAL(18,9);
BEGIN
    -- Try to insert, or do nothing if already exists
    INSERT INTO public.credits (user_id, balance, currency, created_at, updated_at)
    VALUES (p_user_id, p_initial_balance, p_currency, NOW(), NOW())
    ON CONFLICT (user_id) DO NOTHING;
    
    -- Get the current balance
    SELECT balance INTO v_balance
    FROM public.credits
    WHERE user_id = p_user_id;
    
    RETURN v_balance;
END;
$$;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION public.get_or_create_user_credits TO service_role;

-- Add comment explaining the function
COMMENT ON FUNCTION public.get_or_create_user_credits IS 'Creates a credit record for a user if one does not exist, and returns the current balance.';

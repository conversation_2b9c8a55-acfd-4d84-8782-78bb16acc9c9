-- Fix for Paddle webhook process_completed_transaction
-- This migration moves the process_completed_transaction function from paddle schema to public schema
-- and grants the necessary permissions

-- Create or replace the process_completed_transaction function in public schema
CREATE OR REPLACE FUNCTION public.process_completed_transaction(
    p_paddle_transaction_id TEXT,
    p_paddle_customer_id TEXT,
    p_amount DECIMAL(18,2),
    p_currency TEXT,
    p_product_id TEXT DEFAULT NULL,
    p_receipt_url TEXT DEFAULT NULL,
    p_metadata JSONB DEFAULT '{}'::jsonb
) RETURNS BOOLEAN
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
DECLARE
    v_user_id UUID;
    v_customer_id UUID;
    v_product_id UUID;
    v_credit_amount DECIMAL(18,9);
    v_transaction_id UUID;
    v_existing_transaction UUID;
BEGIN
    -- Check if transaction already exists
    SELECT id INTO v_existing_transaction
    FROM paddle.transactions
    WHERE paddle_transaction_id = p_paddle_transaction_id;

    IF v_existing_transaction IS NOT NULL THEN
        -- Transaction already processed
        RETURN TRUE;
    END IF;

    -- Get customer and user info
    SELECT user_id INTO v_user_id
    FROM paddle.customers
    WHERE paddle_customer_id = p_paddle_customer_id;

    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'Customer not found for paddle_customer_id: %', p_paddle_customer_id;
    END IF;

    -- Get product info if product_id provided (for reference only)
    IF p_product_id IS NOT NULL THEN
        SELECT id INTO v_product_id
        FROM paddle.products
        WHERE paddle_product_id = p_product_id AND is_active = TRUE;

        IF v_product_id IS NULL THEN
            RAISE EXCEPTION 'Product not found for paddle_product_id: %', p_product_id;
        END IF;
    END IF;

    -- For VoiceHype, $1 = 1 credit (flexible credit purchasing)
    v_credit_amount := p_amount;

    -- Create transaction record
    INSERT INTO paddle.transactions (
        user_id,
        paddle_transaction_id,
        paddle_customer_id,
        product_id,
        status,
        amount,
        currency,
        credit_amount,
        transaction_type,
        paddle_receipt_url,
        metadata
    ) VALUES (
        v_user_id,
        p_paddle_transaction_id,
        p_paddle_customer_id,
        v_product_id,
        'completed',
        p_amount,
        p_currency,
        v_credit_amount,
        'credit_purchase',
        p_receipt_url,
        p_metadata
    ) RETURNING id INTO v_transaction_id;

    -- Add credits to user's account
    INSERT INTO public.credits (user_id, balance, currency, created_at, updated_at)
    VALUES (v_user_id, v_credit_amount, p_currency, NOW(), NOW())
    ON CONFLICT (user_id)
    DO UPDATE SET
        balance = credits.balance + v_credit_amount,
        updated_at = NOW();

    RETURN TRUE;
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.process_completed_transaction TO service_role;

-- Add comment explaining the change
COMMENT ON FUNCTION public.process_completed_transaction IS 'Processes completed Paddle transactions and adds credits to user accounts. Moved from paddle schema to public schema to fix webhook function calls.';

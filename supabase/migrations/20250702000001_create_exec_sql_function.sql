-- Create the exec_sql function for MCP server
-- This function allows safe SQL execution with proper error handling

CREATE OR REPLACE FUNCTION exec_sql(sql text)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result json;
    rec record;
    rows_affected bigint;
BEGIN
    -- Check if this is a SELECT statement
    IF trim(upper(sql)) LIKE 'SELECT%' THEN
        -- For SELECT statements, return the data as JSON
        EXECUTE 'SELECT array_to_json(array_agg(row_to_json(t))) FROM (' || sql || ') t'
        INTO result;
        
        -- If no rows returned, return empty array
        IF result IS NULL THEN
            result := '[]'::json;
        END IF;
        
        RETURN result;
    ELSE
        -- For non-SELECT statements (INSERT, UPDATE, DELETE), execute and return affected rows
        EXECUTE sql;
        GET DIAGNOSTICS rows_affected = ROW_COUNT;
        
        RETURN json_build_object(
            'success', true,
            'rows_affected', rows_affected,
            'message', 'Query executed successfully'
        );
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', SQLERRM,
            'sqlstate', SQLSTATE
        );
END;
$$;

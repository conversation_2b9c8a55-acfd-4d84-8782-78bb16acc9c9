-- Fix quotas table to use decimal precision for transcription minutes
BEGIN;

-- Alter the used_amount column to use numeric(18,9) instead of integer
ALTER TABLE public.quotas
ALTER COLUMN used_amount TYPE numeric(18,9) USING used_amount::numeric(18,9);

-- Alter the total_amount column to use numeric(18,9) instead of integer
ALTER TABLE public.quotas
ALTER COLUMN total_amount TYPE numeric(18,9) USING total_amount::numeric(18,9);

-- Update the comment on the table to reflect the change
COMMENT ON TABLE public.quotas IS 'Stores quota information for users. used_amount and total_amount are stored as numeric(18,9) to allow for precise tracking of transcription minutes.';

COMMIT; 
-- Add index for faster paddle customer lookups
-- This improves performance when looking up customers by paddle_customer_id

-- Add index on paddle_customer_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_paddle_customers_paddle_customer_id 
ON paddle.customers (paddle_customer_id);

-- Add index on user_id for faster joins
CREATE INDEX IF NOT EXISTS idx_paddle_customers_user_id 
ON paddle.customers (user_id);

-- Add index on email for faster email lookups
CREATE INDEX IF NOT EXISTS idx_paddle_customers_email 
ON paddle.customers (email);

-- Comments explaining the indexes
COMMENT ON INDEX paddle.idx_paddle_customers_paddle_customer_id IS 'Speeds up webhook processing by enabling fast lookups of Paddle customers';
COMMENT ON INDEX paddle.idx_paddle_customers_user_id IS 'Enables fast joins with user tables';
COMMENT ON INDEX paddle.idx_paddle_customers_email IS 'Enables fast email-based customer lookups';

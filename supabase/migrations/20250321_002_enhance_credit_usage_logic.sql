-- This migration enhances the credit usage logic to properly handle multiple credit entries
-- and clean up credit entries that have zero balance after usage

-- Update finalize_usage function to handle multiple credit entries and clean up zero-balance entries
CREATE OR REPLACE FUNCTION public.finalize_usage(
    p_user_id uuid,
    p_service text,
    p_model text,
    p_amount numeric,
    p_cost numeric,
    p_pricing_model text,
    p_metadata jsonb
) RETURNS void 
LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
    remaining_cost DECIMAL(18,9);
    credit_rec RECORD;
    deduction DECIMAL(18,9);
BEGIN
    -- Update usage history to success using a subquery to handle ORDER BY and LIMIT
    UPDATE public.usage_history
    SET status = 'success',
        amount = p_amount,
        cost = ROUND(p_cost, 9),
        metadata = p_metadata
    WHERE id = (
        SELECT id 
        FROM public.usage_history 
        WHERE user_id = p_user_id
        AND service = p_service
        AND model = p_model
        AND status = 'pending'
        ORDER BY created_at DESC
        LIMIT 1
    );

    -- Update credits if using credits pricing model - UPDATED to use credits in order of expiration
    IF p_pricing_model = 'credits' THEN
        -- Initialize remaining cost
        remaining_cost := p_cost;
        
        -- First use credits that have expiration dates, starting with the soonest to expire
        FOR credit_rec IN (
            SELECT id, balance
            FROM public.credits
            WHERE user_id = p_user_id
            AND expires_at IS NOT NULL  -- Only consider credits with expiration dates
            AND expires_at > NOW()      -- Only consider non-expired credits
            AND balance > 0             -- Only consider credits with positive balance
            ORDER BY expires_at ASC     -- Start with soonest to expire
            FOR UPDATE                  -- Lock rows for update
        ) LOOP
            -- Calculate how much to deduct from this credit record
            deduction := LEAST(credit_rec.balance, remaining_cost);
            
            -- Update this credit record
            UPDATE public.credits
            SET balance = ROUND(balance - deduction, 9)
            WHERE id = credit_rec.id;
            
            -- Delete credit entries that have zero balance after deduction
            DELETE FROM public.credits
            WHERE id = credit_rec.id
            AND ROUND(balance, 9) <= 0;
            
            -- Decrease remaining cost
            remaining_cost := remaining_cost - deduction;
            
            -- Exit loop if all costs are covered
            EXIT WHEN remaining_cost <= 0;
        END LOOP;
        
        -- If there's still remaining cost, use credits that never expire (expires_at IS NULL)
        IF remaining_cost > 0 THEN
            -- Get each non-expiring credit entry and process one by one
            FOR credit_rec IN (
                SELECT id, balance
                FROM public.credits
                WHERE user_id = p_user_id
                AND expires_at IS NULL
                AND balance > 0
                ORDER BY created_at ASC  -- Use oldest credits first
                FOR UPDATE
            ) LOOP
                -- Calculate how much to deduct from this credit record
                deduction := LEAST(credit_rec.balance, remaining_cost);
                
                -- Update this credit record
                UPDATE public.credits
                SET balance = ROUND(balance - deduction, 9)
                WHERE id = credit_rec.id;
                
                -- Delete credit entries that have zero balance after deduction
                DELETE FROM public.credits
                WHERE id = credit_rec.id
                AND ROUND(balance, 9) <= 0;
                
                -- Decrease remaining cost
                remaining_cost := remaining_cost - deduction;
                
                -- Exit loop if all costs are covered
                EXIT WHEN remaining_cost <= 0;
            END LOOP;
        END IF;
    END IF;

    -- Update quota if using subscription
    IF p_pricing_model = 'subscription' THEN
        UPDATE public.quotas q
        SET used_amount = ROUND(used_amount + p_amount, 9)
        FROM public.user_subscriptions us
        WHERE q.subscription_id = us.id
        AND us.user_id = p_user_id
        AND q.service = p_service
        AND us.status = 'active'
        AND q.reset_date > NOW();
    END IF;

    -- Handle PAYG usage recording
    IF p_pricing_model = 'payg' THEN
        INSERT INTO public.payg_usage (user_id, month, total_amount, is_paid)
        VALUES (
            p_user_id, 
            date_trunc('month', CURRENT_DATE), 
            p_cost,
            FALSE
        )
        ON CONFLICT (user_id, month) 
        DO UPDATE SET total_amount = ROUND(payg_usage.total_amount + EXCLUDED.total_amount, 9);
    END IF;
END;
$$;

-- Update check_usage_allowance function to properly calculate total available credits
CREATE OR REPLACE FUNCTION public.check_usage_allowance(
    p_user_id uuid,
    p_service text,
    p_model text,
    p_amount numeric,
    p_api_key_id uuid
) RETURNS TABLE(
    can_use boolean,
    pricing_model text,
    cost numeric,
    max_output_tokens text
) LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
    subscription_available BOOLEAN;
    total_credit_balance DECIMAL(18,9);
    service_cost DECIMAL(18,9);
    cost_per_unit DECIMAL(18,9);
    payg_allowed BOOLEAN;
    input_cost_per_unit DECIMAL(18,9);
    output_cost_per_unit DECIMAL(18,9);
    max_tokens INTEGER;
BEGIN
    -- Get cost per unit first for standard pricing
    SELECT sp.cost_per_unit INTO cost_per_unit
    FROM public.service_pricing sp
    WHERE sp.service = p_service
    AND sp.model = p_model
    AND sp.is_active = TRUE;

    -- Calculate standard service cost with proper decimal precision
    IF cost_per_unit IS NOT NULL THEN
        service_cost := cost_per_unit * p_amount;
    END IF;

    -- Check subscription quota first
    SELECT EXISTS (
        SELECT 1 FROM public.quotas q
        JOIN public.user_subscriptions us ON q.subscription_id = us.id
        WHERE us.user_id = p_user_id
        AND q.service = p_service
        AND (q.used_amount + p_amount) <= q.total_amount
        AND us.status = 'active'
        AND q.reset_date > NOW()
    ) INTO subscription_available;

    IF subscription_available THEN 
        RETURN QUERY SELECT 
            TRUE as can_use,
            'subscription'::TEXT as pricing_model,
            service_cost,
            '4096'::TEXT as max_output_tokens; -- Default max tokens for subscription
        RETURN;
    END IF;

    -- Check total credit balance across all valid credit entries - UPDATED to sum multiple entries
    SELECT COALESCE(SUM(balance), 0) INTO total_credit_balance
    FROM public.credits
    WHERE user_id = p_user_id
    AND (expires_at IS NULL OR expires_at > NOW()) -- Only consider non-expired credits
    AND balance > 0; -- Only consider entries with positive balance

    -- Special handling for optimization service (LLM calls)
    IF p_service = 'optimization' THEN
        -- Try to get separate input/output pricing
        SELECT sp.cost_per_unit INTO input_cost_per_unit
        FROM public.service_pricing sp
        WHERE sp.service = 'optimization'
        AND sp.model = p_model || '/input'
        AND sp.is_active = TRUE;

        SELECT sp.cost_per_unit INTO output_cost_per_unit
        FROM public.service_pricing sp
        WHERE sp.service = 'optimization'
        AND sp.model = p_model || '/output'
        AND sp.is_active = TRUE;

        -- If we have separate input/output pricing
        IF input_cost_per_unit IS NOT NULL AND output_cost_per_unit IS NOT NULL THEN
            -- Calculate input tokens cost
            service_cost := input_cost_per_unit * p_amount;
            
            -- If user has any credits at all
            IF total_credit_balance > 0 THEN
                -- Calculate how many output tokens they can afford
                -- First subtract the cost of input tokens
                IF total_credit_balance > service_cost THEN
                    -- Remaining credits after input tokens
                    total_credit_balance := total_credit_balance - service_cost;
                    
                    -- Calculate max output tokens they can afford
                    max_tokens := FLOOR(total_credit_balance / output_cost_per_unit);
                    
                    -- Cap at a reasonable maximum (4096 is common for many models)
                    max_tokens := LEAST(max_tokens, 4096);
                    
                    RETURN QUERY SELECT 
                        TRUE as can_use,
                        'credits'::TEXT as pricing_model,
                        service_cost, -- This is just the input cost for now
                        max_tokens::TEXT as max_output_tokens;
                    RETURN;
                END IF;
            END IF;
        ELSE
            -- If we don't have separate pricing, use the combined pricing
            -- If user has any credits at all and they can cover at least the input tokens
            IF total_credit_balance > 0 AND cost_per_unit IS NOT NULL THEN
                -- Calculate max tokens they can afford (input + output)
                max_tokens := FLOOR(total_credit_balance / cost_per_unit) - p_amount;
                
                -- If they can afford at least some output tokens
                IF max_tokens > 0 THEN
                    -- Cap at a reasonable maximum
                    max_tokens := LEAST(max_tokens, 4096);
                    
                    RETURN QUERY SELECT 
                        TRUE as can_use,
                        'credits'::TEXT as pricing_model,
                        service_cost,
                        max_tokens::TEXT as max_output_tokens;
                    RETURN;
                END IF;
            END IF;
        END IF;
    ELSE
        -- For non-optimization services (like transcription), use the original logic
        IF total_credit_balance >= service_cost THEN 
            RETURN QUERY SELECT 
                TRUE as can_use,
                'credits'::TEXT as pricing_model,
                service_cost,
                NULL::TEXT as max_output_tokens;
            RETURN;
        END IF;
    END IF;

    -- Check if PAYG is allowed for this API key
    SELECT COALESCE(allow_payg, FALSE) INTO payg_allowed
    FROM public.api_keys
    WHERE id = p_api_key_id
    AND is_active = TRUE;

    IF payg_allowed IS NULL THEN
        payg_allowed := FALSE;
    END IF;

    -- Fallback to PAYG if allowed and user has stripe customer id
    IF payg_allowed AND EXISTS (
        SELECT 1 FROM public.profiles
        WHERE id = p_user_id
        AND stripe_customer_id IS NOT NULL
    ) THEN
        RETURN QUERY SELECT 
            TRUE::BOOLEAN as can_use,
            'payg'::TEXT as pricing_model,
            service_cost,
            '4096'::TEXT as max_output_tokens; -- Default max tokens for PAYG
        RETURN;
    END IF;

    -- If we get here, no payment method is available
    RETURN QUERY SELECT 
        FALSE::BOOLEAN as can_use,
        NULL::TEXT as pricing_model,
        service_cost,
        NULL::TEXT as max_output_tokens;
END;
$$; 
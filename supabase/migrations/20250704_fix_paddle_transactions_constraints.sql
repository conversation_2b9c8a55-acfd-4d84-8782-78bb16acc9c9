-- Fix paddle_transactions table constraints
-- Remove the restrictive constraint and keep the comprehensive one

-- Drop the restrictive constraint
ALTER TABLE public.paddle_transactions 
DROP CONSTRAINT IF EXISTS paddle_transactions_transaction_type_check;

-- Ensure the comprehensive constraint exists (it should already exist)
-- This allows: credit_purchase, payg_invoice, refund, subscription, subscription_upgrade, subscription_renewal, subscription_cancelled
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'transactions_transaction_type_check' 
        AND table_name = 'paddle_transactions'
    ) THEN
        ALTER TABLE public.paddle_transactions 
        ADD CONSTRAINT transactions_transaction_type_check CHECK (
            transaction_type = ANY (
                ARRAY[
                    'credit_purchase'::text,
                    'payg_invoice'::text,
                    'refund'::text,
                    'subscription'::text,
                    'subscription_upgrade'::text,
                    'subscription_renewal'::text,
                    'subscription_cancelled'::text
                ]
            )
        );
    END IF;
END $$;

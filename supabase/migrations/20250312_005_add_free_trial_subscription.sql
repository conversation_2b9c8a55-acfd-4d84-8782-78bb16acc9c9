-- Add Free Trial subscription plan and update handle_new_user function
BEGIN;

-- First, check if the Free Trial plan already exists
DO $$
DECLARE
    v_plan_exists BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1 FROM public.subscription_plans WHERE name = 'Free Trial'
    ) INTO v_plan_exists;
    
    -- If the Free Trial plan doesn't exist, create it
    IF NOT v_plan_exists THEN
        INSERT INTO public.subscription_plans (
            name, 
            description, 
            monthly_price, 
            annual_price, 
            transcription_minutes, 
            input_tokens, 
            output_tokens, 
            is_active, 
            created_at
        ) VALUES (
            'Free Trial',
            'One month free trial with limited usage',
            0.00,
            NULL,
            10,    -- 10 minutes of transcription
            5000,  -- 5,000 input tokens
            10000, -- 10,000 output tokens
            TRUE,
            NOW()
        );
    END IF;
END $$;

-- Update the handle_new_user function to create a subscription and link quotas
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    v_profile_id UUID;
    v_subscription_id UUID;
    v_plan_id UUID;
    v_current_period_start TIMESTAMPTZ;
    v_current_period_end TIMESTAMPTZ;
    v_trial_subscription_id TEXT;
BEGIN
    -- Create a profile for the new user
    INSERT INTO public.profiles (id, email, full_name, created_at, updated_at)
    VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'full_name', NOW(), NOW())
    RETURNING id INTO v_profile_id;
    
    -- Get the Free Trial plan ID
    SELECT id INTO v_plan_id FROM public.subscription_plans WHERE name = 'Free Trial';
    
    -- Set subscription period (1 month from now)
    v_current_period_start := NOW();
    v_current_period_end := v_current_period_start + INTERVAL '1 month';
    
    -- Generate a placeholder for stripe_subscription_id
    v_trial_subscription_id := 'trial_' || NEW.id;
    
    -- Create a subscription for the new user
    INSERT INTO public.user_subscriptions (
        user_id,
        plan_id,
        status,
        current_period_start,
        current_period_end,
        stripe_subscription_id,
        created_at
    ) VALUES (
        NEW.id,
        v_plan_id,
        'active',
        v_current_period_start,
        v_current_period_end,
        v_trial_subscription_id,
        NOW()
    ) RETURNING id INTO v_subscription_id;
    
    -- Create initial quotas for the new user linked to the subscription
    -- Transcription minutes
    INSERT INTO public.quotas (
        user_id, 
        subscription_id, 
        service, 
        used_amount, 
        total_amount, 
        reset_date, 
        created_at
    ) VALUES (
        NEW.id, 
        v_subscription_id, 
        'transcription', 
        0, 
        10, 
        v_current_period_end, 
        NOW()
    );
    
    -- Tokens
    INSERT INTO public.quotas (
        user_id, 
        subscription_id, 
        service, 
        used_amount, 
        total_amount, 
        reset_date, 
        created_at
    ) VALUES (
        NEW.id, 
        v_subscription_id, 
        'optimization', 
        0, 
        10000, 
        v_current_period_end, 
        NOW()
    );
    
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
REVOKE ALL ON FUNCTION public.handle_new_user() FROM PUBLIC;
REVOKE ALL ON FUNCTION public.handle_new_user() FROM anon;
REVOKE ALL ON FUNCTION public.handle_new_user() FROM authenticated;
GRANT EXECUTE ON FUNCTION public.handle_new_user() TO postgres;

COMMIT; 
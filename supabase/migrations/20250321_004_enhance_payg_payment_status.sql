-- Create the payment status enum type
CREATE TYPE public.payment_status AS ENUM ('pending', 'paid', 'unpaid');

-- Modify the payg_usage table
ALTER TABLE public.payg_usage 
  -- Add a new column with the enum type
  ADD COLUMN payment_status public.payment_status DEFAULT 'pending',
  -- Add a payment_metadata column for storing error details and payment information
  ADD COLUMN payment_metadata JSONB;

-- Create a function to safely migrate existing data
CREATE OR REPLACE FUNCTION public.migrate_payg_payment_status() RETURNS void AS $$
BEGIN
  -- Convert existing is_paid boolean data to the new enum type
  UPDATE public.payg_usage
  SET payment_status = CASE
    WHEN is_paid = TRUE THEN 'paid'::public.payment_status
    WHEN is_paid = FALSE THEN 'unpaid'::public.payment_status
    ELSE 'pending'::public.payment_status
  END;
END;
$$ LANGUAGE plpgsql;

-- Execute the migration function
SELECT public.migrate_payg_payment_status();

-- Drop the migration function after use
DROP FUNCTION public.migrate_payg_payment_status();

-- Update the has_unpaid_payg_balance function
CREATE OR REPLACE FUNCTION public.has_unpaid_payg_balance(p_user_id UUID) 
RETURNS boolean AS $$
DECLARE
    has_unpaid BOOLEAN;
BEGIN
    -- Check if there are any unpaid PAYG balances from previous months
    SELECT EXISTS (
        SELECT 1 
        FROM public.payg_usage 
        WHERE user_id = p_user_id 
        AND payment_status = 'unpaid'
        AND month < date_trunc('month', CURRENT_DATE)
    ) INTO has_unpaid;
    
    RETURN has_unpaid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop the existing get_unpaid_payg_balances function before recreating it with a different return type
DROP FUNCTION IF EXISTS public.get_unpaid_payg_balances(UUID);

-- Update the get_unpaid_payg_balances function
CREATE OR REPLACE FUNCTION public.get_unpaid_payg_balances(p_user_id UUID) 
RETURNS TABLE(
    id UUID, 
    month DATE, 
    total_amount NUMERIC, 
    payment_status public.payment_status,
    payment_metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pu.id,
        pu.month,
        pu.total_amount,
        pu.payment_status,
        pu.payment_metadata,
        pu.created_at
    FROM 
        public.payg_usage pu
    WHERE 
        pu.user_id = p_user_id
        AND pu.payment_status = 'unpaid'
        AND pu.month < date_trunc('month', CURRENT_DATE)
    ORDER BY 
        pu.month DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function for marking PAYG payments as paid
CREATE OR REPLACE FUNCTION public.mark_payg_payment_as_paid(
    p_payg_id UUID,
    p_payment_metadata JSONB DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
    updated_count INTEGER;
BEGIN
    UPDATE public.payg_usage
    SET payment_status = 'paid',
        payment_metadata = COALESCE(p_payment_metadata, payment_metadata)
    WHERE id = p_payg_id
    AND payment_status != 'paid';
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    
    RETURN updated_count > 0;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function for marking PAYG payments as unpaid with failure reason
CREATE OR REPLACE FUNCTION public.mark_payg_payment_as_unpaid(
    p_payg_id UUID,
    p_failure_reason TEXT,
    p_payment_metadata JSONB DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
    updated_count INTEGER;
    combined_metadata JSONB;
BEGIN
    -- Combine existing metadata with failure reason and new metadata
    SELECT 
        COALESCE(payment_metadata, '{}'::JSONB) || 
        jsonb_build_object('failure_reason', p_failure_reason) ||
        COALESCE(p_payment_metadata, '{}'::JSONB)
    INTO combined_metadata
    FROM public.payg_usage
    WHERE id = p_payg_id;
    
    -- Update the record
    UPDATE public.payg_usage
    SET payment_status = 'unpaid',
        payment_metadata = combined_metadata
    WHERE id = p_payg_id
    AND payment_status != 'unpaid';
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    
    RETURN updated_count > 0;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the finalize_usage function to use the new payment_status field
CREATE OR REPLACE FUNCTION public.finalize_usage(
    p_user_id UUID,
    p_service TEXT,
    p_model TEXT,
    p_amount NUMERIC,
    p_cost NUMERIC,
    p_pricing_model TEXT,
    p_metadata JSONB
) RETURNS VOID AS $$
BEGIN
    -- Update usage history to success using a subquery to handle ORDER BY and LIMIT
    UPDATE public.usage_history
    SET status = 'success',
        amount = p_amount,
        cost = ROUND(p_cost, 9),
        metadata = p_metadata
    WHERE id = (
        SELECT id 
        FROM public.usage_history 
        WHERE user_id = p_user_id
        AND service = p_service
        AND model = p_model
        AND status = 'pending'
        ORDER BY created_at DESC
        LIMIT 1
    );

    -- Update credits if using credits pricing model
    IF p_pricing_model = 'credits' THEN
        UPDATE public.credits
        SET balance = ROUND(balance - p_cost, 9)
        WHERE user_id = p_user_id;
    END IF;

    -- Update quota if using subscription
    IF p_pricing_model = 'subscription' THEN
        UPDATE public.quotas q
        SET used_amount = ROUND(used_amount + p_amount, 9)
        FROM public.user_subscriptions us
        WHERE q.subscription_id = us.id
        AND us.user_id = p_user_id
        AND q.service = p_service
        AND us.status = 'active'
        AND q.reset_date > NOW();
    END IF;

    -- Handle PAYG usage recording with new payment_status
    IF p_pricing_model = 'payg' THEN
        INSERT INTO public.payg_usage (
            user_id, 
            month, 
            total_amount, 
            payment_status
        ) VALUES (
            p_user_id, 
            date_trunc('month', CURRENT_DATE), 
            p_cost,
            'pending'
        )
        ON CONFLICT (user_id, month) 
        DO UPDATE SET 
            total_amount = ROUND(payg_usage.total_amount + EXCLUDED.total_amount, 9),
            payment_status = 
                -- Only change to pending if it was previously paid
                CASE 
                    WHEN payg_usage.payment_status = 'paid' THEN 'pending'
                    ELSE payg_usage.payment_status
                END;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the check_usage_allowance function to work with the new payment_status
CREATE OR REPLACE FUNCTION public.check_usage_allowance(
    p_user_id UUID,
    p_service TEXT,
    p_model TEXT,
    p_amount NUMERIC,
    p_api_key_id UUID
) RETURNS TABLE(
    can_use BOOLEAN,
    pricing_model TEXT,
    cost NUMERIC,
    max_output_tokens TEXT
) AS $$
DECLARE
    subscription_available BOOLEAN;
    credit_balance DECIMAL(18,9);
    service_cost DECIMAL(18,9);
    cost_per_unit DECIMAL(18,9);
    payg_allowed BOOLEAN;
    input_cost_per_unit DECIMAL(18,9);
    output_cost_per_unit DECIMAL(18,9);
    max_tokens INTEGER;
    has_unpaid_previous_month BOOLEAN;
BEGIN
    -- Get cost per unit first for standard pricing
    SELECT sp.cost_per_unit INTO cost_per_unit
    FROM public.service_pricing sp
    WHERE sp.service = p_service
    AND sp.model = p_model
    AND sp.is_active = TRUE;

    -- Calculate standard service cost with proper decimal precision
    IF cost_per_unit IS NOT NULL THEN
        service_cost := cost_per_unit * p_amount;
    END IF;

    -- Check subscription quota first
    SELECT EXISTS (
        SELECT 1 FROM public.quotas q
        JOIN public.user_subscriptions us ON q.subscription_id = us.id
        WHERE us.user_id = p_user_id
        AND q.service = p_service
        AND (q.used_amount + p_amount) <= q.total_amount
        AND us.status = 'active'
        AND q.reset_date > NOW()
    ) INTO subscription_available;

    IF subscription_available THEN 
        RETURN QUERY SELECT 
            TRUE as can_use,
            'subscription'::TEXT as pricing_model,
            service_cost,
            '4096'::TEXT as max_output_tokens; -- Default max tokens for subscription
        RETURN;
    END IF;

    -- Check credit balance
    SELECT COALESCE(balance, 0) INTO credit_balance
    FROM public.credits
    WHERE user_id = p_user_id;

    IF credit_balance IS NULL THEN
        credit_balance := 0;
    END IF;

    -- Special handling for optimization service (LLM calls)
    IF p_service = 'optimization' THEN
        -- Try to get separate input/output pricing
        SELECT sp.cost_per_unit INTO input_cost_per_unit
        FROM public.service_pricing sp
        WHERE sp.service = 'optimization'
        AND sp.model = p_model || '/input'
        AND sp.is_active = TRUE;

        SELECT sp.cost_per_unit INTO output_cost_per_unit
        FROM public.service_pricing sp
        WHERE sp.service = 'optimization'
        AND sp.model = p_model || '/output'
        AND sp.is_active = TRUE;

        -- If we have separate input/output pricing
        IF input_cost_per_unit IS NOT NULL AND output_cost_per_unit IS NOT NULL THEN
            -- Calculate input tokens cost
            service_cost := input_cost_per_unit * p_amount;
            
            -- If user has any credits at all
            IF credit_balance > 0 THEN
                -- Calculate how many output tokens they can afford
                -- First subtract the cost of input tokens
                IF credit_balance > service_cost THEN
                    -- Remaining credits after input tokens
                    credit_balance := credit_balance - service_cost;
                    
                    -- Calculate max output tokens they can afford
                    max_tokens := FLOOR(credit_balance / output_cost_per_unit);
                    
                    -- Cap at a reasonable maximum (4096 is common for many models)
                    max_tokens := LEAST(max_tokens, 4096);
                    
                    RETURN QUERY SELECT 
                        TRUE as can_use,
                        'credits'::TEXT as pricing_model,
                        service_cost, -- This is just the input cost for now
                        max_tokens::TEXT as max_output_tokens;
                    RETURN;
                END IF;
            END IF;
        ELSE
            -- If we don't have separate pricing, use the combined pricing
            -- If user has any credits at all and they can cover at least the input tokens
            IF credit_balance > 0 AND cost_per_unit IS NOT NULL THEN
                -- Calculate max tokens they can afford (input + output)
                max_tokens := FLOOR(credit_balance / cost_per_unit) - p_amount;
                
                -- If they can afford at least some output tokens
                IF max_tokens > 0 THEN
                    -- Cap at a reasonable maximum
                    max_tokens := LEAST(max_tokens, 4096);
                    
                    RETURN QUERY SELECT 
                        TRUE as can_use,
                        'credits'::TEXT as pricing_model,
                        service_cost,
                        max_tokens::TEXT as max_output_tokens;
                    RETURN;
                END IF;
            END IF;
        END IF;
    ELSE
        -- For non-optimization services (like transcription), use the original logic
        IF credit_balance >= service_cost THEN 
            RETURN QUERY SELECT 
                TRUE as can_use,
                'credits'::TEXT as pricing_model,
                service_cost,
                NULL::TEXT as max_output_tokens;
            RETURN;
        END IF;
    END IF;

    -- Check if PAYG is allowed for this API key
    SELECT COALESCE(allow_payg, FALSE) INTO payg_allowed
    FROM public.api_keys
    WHERE id = p_api_key_id
    AND is_active = TRUE;

    IF payg_allowed IS NULL THEN
        payg_allowed := FALSE;
    END IF;

    -- Check if user has unpaid balances from previous months
    SELECT EXISTS (
        SELECT 1 
        FROM public.payg_usage 
        WHERE user_id = p_user_id 
        AND payment_status = 'unpaid'
        AND month < date_trunc('month', CURRENT_DATE)
    ) INTO has_unpaid_previous_month;

    -- Fallback to PAYG if allowed, user has stripe customer id, and no unpaid balances
    IF payg_allowed 
       AND NOT has_unpaid_previous_month
       AND EXISTS (
        SELECT 1 FROM public.profiles
        WHERE id = p_user_id
        AND stripe_customer_id IS NOT NULL
    ) THEN
        RETURN QUERY SELECT 
            TRUE::BOOLEAN as can_use,
            'payg'::TEXT as pricing_model,
            service_cost,
            '4096'::TEXT as max_output_tokens; -- Default max tokens for PAYG
        RETURN;
    END IF;

    -- If we get here, no payment method is available
    RETURN QUERY SELECT 
        FALSE::BOOLEAN as can_use,
        NULL::TEXT as pricing_model,
        service_cost,
        NULL::TEXT as max_output_tokens;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update existing usage history records with the new status
UPDATE public.payg_usage
SET payment_status = 'pending'
WHERE payment_status IS NULL;

-- After migration period, the is_paid column can be dropped with another migration
ALTER TABLE public.payg_usage DROP COLUMN is_paid; 
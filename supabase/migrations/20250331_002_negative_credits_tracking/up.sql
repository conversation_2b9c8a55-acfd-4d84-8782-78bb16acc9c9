-- Create a table to track negative balances when users exceed their credit allocation
CREATE TABLE IF NOT EXISTS public.negative_balances (
    id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    service TEXT NOT NULL CHECK (service IN ('transcription', 'optimization')),
    model TEXT NOT NULL,
    amount DECIMAL(18,9) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    cleared_at TIMESTAMPTZ,
    usage_id UUID REFERENCES public.usage_history(id) ON DELETE SET NULL
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_negative_balances_user_id ON public.negative_balances(user_id);
CREATE INDEX IF NOT EXISTS idx_negative_balances_cleared_at ON public.negative_balances(cleared_at) WHERE cleared_at IS NULL;

-- Enable RLS on the table
ALTER TABLE public.negative_balances ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Negative Balances - Select own" ON public.negative_balances
    FOR SELECT USING (auth.uid() = user_id);

-- Add a function to apply negative balances when credits are added
CREATE OR REPLACE FUNCTION public.apply_negative_balances()
RETURNS TRIGGER AS $$
DECLARE
    total_negative DECIMAL(18,9);
    remaining_credit DECIMAL(18,9);
BEGIN
    -- Calculate total negative balance for this user
    SELECT COALESCE(SUM(amount), 0) INTO total_negative
    FROM public.negative_balances
    WHERE user_id = NEW.user_id AND cleared_at IS NULL;
    
    -- If no negative balance, just return the NEW record unchanged
    IF total_negative = 0 THEN
        RETURN NEW;
    END IF;
    
    -- Calculate how much we can clear
    remaining_credit := NEW.balance;
    
    -- Apply negative balance deduction if possible
    IF remaining_credit >= total_negative THEN
        -- We can clear all negative balances
        NEW.balance := remaining_credit - total_negative;
        
        -- Mark all negative balances as cleared
        UPDATE public.negative_balances
        SET cleared_at = NOW()
        WHERE user_id = NEW.user_id AND cleared_at IS NULL;
    ELSE
        -- We can only clear part of the negative balances
        -- Update the credit balance to zero (since we can't clear everything)
        NEW.balance := 0;
        
        -- Create a temporary table to track which negative balances we'll clear
        CREATE TEMP TABLE cleared_negatives (
            id UUID,
            amount DECIMAL(18,9)
        ) ON COMMIT DROP;
        
        -- Fill the temporary table with the negative balances we can clear
        -- Process oldest first (FIFO)
        INSERT INTO cleared_negatives (id, amount)
        SELECT id, amount
        FROM public.negative_balances
        WHERE user_id = NEW.user_id AND cleared_at IS NULL
        ORDER BY created_at ASC;
        
        -- Clear as many negative balances as possible
        DECLARE
            negative_id UUID;
            negative_amount DECIMAL(18,9);
            can_clear DECIMAL(18,9);
        BEGIN
            FOR negative_id, negative_amount IN SELECT id, amount FROM cleared_negatives LOOP
                IF remaining_credit <= 0 THEN
                    EXIT; -- No more credit to apply
                END IF;
                
                IF remaining_credit >= negative_amount THEN
                    -- Clear this negative balance completely
                    UPDATE public.negative_balances
                    SET cleared_at = NOW()
                    WHERE id = negative_id;
                    
                    remaining_credit := remaining_credit - negative_amount;
                ELSE
                    -- Partially clear this negative balance
                    UPDATE public.negative_balances
                    SET amount = amount - remaining_credit
                    WHERE id = negative_id;
                    
                    remaining_credit := 0;
                    EXIT; -- No more credit to apply
                END IF;
            END LOOP;
        END;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to apply negative balances when credits are added
CREATE TRIGGER apply_negative_balances_trigger
    BEFORE UPDATE OF balance ON public.credits
    FOR EACH ROW
    WHEN (NEW.balance > OLD.balance)
    EXECUTE FUNCTION public.apply_negative_balances();

-- Update finalize_usage function to handle negative balances
CREATE OR REPLACE FUNCTION "public"."finalize_usage"("p_user_id" "uuid", "p_service" "text", "p_model" "text", "p_amount" numeric, "p_cost" numeric, "p_pricing_model" "text", "p_metadata" "jsonb") RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    input_token_count INTEGER;
    output_token_count INTEGER;
    input_cost_per_token DECIMAL(18, 9);
    output_cost_per_token DECIMAL(18, 9);
    total_token_cost DECIMAL(18, 9);
    is_token_based BOOLEAN;
    pricing_unit TEXT;
    user_balance DECIMAL(18, 9);
    usage_id UUID;
    credit_shortfall DECIMAL(18, 9);
BEGIN
    -- Check if this is a token-based model by looking at the unit
    SELECT sp.unit INTO pricing_unit
    FROM public.service_pricing sp
    WHERE sp.service = p_service
    AND sp.model = p_model
    AND sp.is_active = TRUE;
    
    -- Set token-based flag
    is_token_based := pricing_unit = 'token-based';
    
    -- Handle token-based billing for GPT-4o transcription models
    IF is_token_based AND p_service = 'transcription' AND (
        p_model = 'gpt-4o-mini-transcribe' OR 
        p_model = 'gpt-4o-transcribe'
    ) THEN
        -- Extract token counts from metadata
        input_token_count := COALESCE((p_metadata->>'input_tokens')::INTEGER, 0);
        output_token_count := COALESCE((p_metadata->>'output_tokens')::INTEGER, 0);
        
        -- Get token costs for this model
        SELECT sp.cost_per_unit INTO input_cost_per_token
        FROM public.service_pricing sp
        WHERE sp.service = 'transcription'
        AND sp.model = p_model || '/input'
        AND sp.is_active = true;
        
        SELECT sp.cost_per_unit INTO output_cost_per_token
        FROM public.service_pricing sp
        WHERE sp.service = 'transcription'
        AND sp.model = p_model || '/output'
        AND sp.is_active = true;
        
        -- Calculate total cost based on token usage
        total_token_cost := (input_token_count * input_cost_per_token) + 
                            (output_token_count * output_cost_per_token);
        
        -- Update usage history with token information and calculated cost
        UPDATE public.usage_history
        SET status = 'success',
            amount = p_amount, -- Original amount (duration) for compatibility
            cost = ROUND(total_token_cost, 9),
            metadata = p_metadata || jsonb_build_object(
                'token_based', true,
                'input_tokens', input_token_count,
                'output_tokens', output_token_count,
                'input_cost', ROUND(input_token_count * input_cost_per_token, 9),
                'output_cost', ROUND(output_token_count * output_cost_per_token, 9)
            )
        WHERE id = (
            SELECT id 
            FROM public.usage_history 
            WHERE user_id = p_user_id
            AND service = p_service
            AND model = p_model
            AND status = 'pending'
            ORDER BY created_at DESC
            LIMIT 1
        )
        RETURNING id INTO usage_id;
        
        -- Handle credit-based billing with potential negative balance tracking
        IF p_pricing_model = 'credits' THEN
            -- Get current user balance
            SELECT balance INTO user_balance
            FROM public.credits
            WHERE user_id = p_user_id;
            
            -- Check if the cost exceeds available balance (negative balance case)
            IF user_balance < total_token_cost THEN
                -- Calculate the shortfall
                credit_shortfall := total_token_cost - user_balance;
                
                -- Set balance to zero
                UPDATE public.credits
                SET balance = 0
                WHERE user_id = p_user_id;
                
                -- Create negative balance record
                INSERT INTO public.negative_balances (
                    user_id,
                    service,
                    model,
                    amount,
                    usage_id
                ) VALUES (
                    p_user_id,
                    p_service,
                    p_model,
                    credit_shortfall,
                    usage_id
                );
            ELSE
                -- Normal case - sufficient balance
                UPDATE public.credits
                SET balance = ROUND(balance - total_token_cost, 9)
                WHERE user_id = p_user_id;
            END IF;
        END IF;
        
        -- Handle PAYG usage recording
        IF p_pricing_model = 'payg' THEN
            INSERT INTO public.payg_usage (
                user_id, 
                month, 
                total_amount, 
                payment_status
            ) VALUES (
                p_user_id, 
                date_trunc('month', CURRENT_DATE), 
                total_token_cost,
                'pending'
            )
            ON CONFLICT (user_id, month) 
            DO UPDATE SET 
                total_amount = ROUND(payg_usage.total_amount + EXCLUDED.total_amount, 9),
                payment_status = 
                    -- Only change to pending if it was previously paid
                    CASE 
                        WHEN payg_usage.payment_status = 'paid' THEN 'pending'
                        ELSE payg_usage.payment_status
                    END;
        END IF;
    ELSE
        -- Use standard logic for non-token-based models
        -- Update usage history to success using a subquery to handle ORDER BY and LIMIT
        UPDATE public.usage_history
        SET status = 'success',
            amount = p_amount,
            cost = ROUND(p_cost, 9),
            metadata = p_metadata
        WHERE id = (
            SELECT id 
            FROM public.usage_history 
            WHERE user_id = p_user_id
            AND service = p_service
            AND model = p_model
            AND status = 'pending'
            ORDER BY created_at DESC
            LIMIT 1
        )
        RETURNING id INTO usage_id;

        -- Update credits if using credits pricing model
        IF p_pricing_model = 'credits' THEN
            UPDATE public.credits
            SET balance = ROUND(balance - p_cost, 9)
            WHERE user_id = p_user_id;
        END IF;

        -- Update quota if using subscription
        IF p_pricing_model = 'subscription' THEN
            UPDATE public.quotas q
            SET used_amount = ROUND(used_amount + p_amount, 9)
            FROM public.user_subscriptions us
            WHERE q.subscription_id = us.id
            AND us.user_id = p_user_id
            AND q.service = p_service
            AND us.status = 'active'
            AND q.reset_date > NOW();
        END IF;

        -- Handle PAYG usage recording with new payment_status
        IF p_pricing_model = 'payg' THEN
            INSERT INTO public.payg_usage (
                user_id, 
                month, 
                total_amount, 
                payment_status
            ) VALUES (
                p_user_id, 
                date_trunc('month', CURRENT_DATE), 
                p_cost,
                'pending'
            )
            ON CONFLICT (user_id, month) 
            DO UPDATE SET 
                total_amount = ROUND(payg_usage.total_amount + EXCLUDED.total_amount, 9),
                payment_status = 
                    -- Only change to pending if it was previously paid
                    CASE 
                        WHEN payg_usage.payment_status = 'paid' THEN 'pending'
                        ELSE payg_usage.payment_status
                    END;
        END IF;
    END IF;
END;
$$; 
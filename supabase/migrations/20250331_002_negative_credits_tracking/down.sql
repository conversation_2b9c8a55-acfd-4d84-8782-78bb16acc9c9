-- Drop the trigger first
DROP TRIGGER IF EXISTS apply_negative_balances_trigger ON public.credits;

-- Drop the function that the trigger uses
DROP FUNCTION IF EXISTS public.apply_negative_balances();

-- Restore the original finalize_usage function without negative balance handling
CREATE OR REPLACE FUNCTION "public"."finalize_usage"("p_user_id" "uuid", "p_service" "text", "p_model" "text", "p_amount" numeric, "p_cost" numeric, "p_pricing_model" "text", "p_metadata" "jsonb") RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    input_token_count INTEGER;
    output_token_count INTEGER;
    input_cost_per_token DECIMAL(18, 9);
    output_cost_per_token DECIMAL(18, 9);
    total_token_cost DECIMAL(18, 9);
    is_token_based BOOLEAN;
    pricing_unit TEXT;
BEGIN
    -- Check if this is a token-based model by looking at the unit
    SELECT sp.unit INTO pricing_unit
    FROM public.service_pricing sp
    WHERE sp.service = p_service
    AND sp.model = p_model
    AND sp.is_active = TRUE;
    
    -- Set token-based flag
    is_token_based := pricing_unit = 'token-based';
    
    -- Handle token-based billing for GPT-4o transcription models
    IF is_token_based AND p_service = 'transcription' AND (
        p_model = 'gpt-4o-mini-transcribe' OR 
        p_model = 'gpt-4o-transcribe'
    ) THEN
        -- Extract token counts from metadata
        input_token_count := COALESCE((p_metadata->>'input_tokens')::INTEGER, 0);
        output_token_count := COALESCE((p_metadata->>'output_tokens')::INTEGER, 0);
        
        -- Get token costs for this model
        SELECT sp.cost_per_unit INTO input_cost_per_token
        FROM public.service_pricing sp
        WHERE sp.service = 'transcription'
        AND sp.model = p_model || '/input'
        AND sp.is_active = true;
        
        SELECT sp.cost_per_unit INTO output_cost_per_token
        FROM public.service_pricing sp
        WHERE sp.service = 'transcription'
        AND sp.model = p_model || '/output'
        AND sp.is_active = true;
        
        -- Calculate total cost based on token usage
        total_token_cost := (input_token_count * input_cost_per_token) + 
                            (output_token_count * output_cost_per_token);
        
        -- Update usage history with token information and calculated cost
        UPDATE public.usage_history
        SET status = 'success',
            amount = p_amount, -- Original amount (duration) for compatibility
            cost = ROUND(total_token_cost, 9),
            metadata = p_metadata || jsonb_build_object(
                'token_based', true,
                'input_tokens', input_token_count,
                'output_tokens', output_token_count,
                'input_cost', ROUND(input_token_count * input_cost_per_token, 9),
                'output_cost', ROUND(output_token_count * output_cost_per_token, 9)
            )
        WHERE id = (
            SELECT id 
            FROM public.usage_history 
            WHERE user_id = p_user_id
            AND service = p_service
            AND model = p_model
            AND status = 'pending'
            ORDER BY created_at DESC
            LIMIT 1
        );
        
        -- Update credits if using credits pricing model
        IF p_pricing_model = 'credits' THEN
            UPDATE public.credits
            SET balance = ROUND(balance - total_token_cost, 9)
            WHERE user_id = p_user_id;
        END IF;
        
        -- Handle PAYG usage recording
        IF p_pricing_model = 'payg' THEN
            INSERT INTO public.payg_usage (
                user_id, 
                month, 
                total_amount, 
                payment_status
            ) VALUES (
                p_user_id, 
                date_trunc('month', CURRENT_DATE), 
                total_token_cost,
                'pending'
            )
            ON CONFLICT (user_id, month) 
            DO UPDATE SET 
                total_amount = ROUND(payg_usage.total_amount + EXCLUDED.total_amount, 9),
                payment_status = 
                    -- Only change to pending if it was previously paid
                    CASE 
                        WHEN payg_usage.payment_status = 'paid' THEN 'pending'
                        ELSE payg_usage.payment_status
                    END;
        END IF;
    ELSE
        -- Use standard logic for non-token-based models
        -- Update usage history to success using a subquery to handle ORDER BY and LIMIT
        UPDATE public.usage_history
        SET status = 'success',
            amount = p_amount,
            cost = ROUND(p_cost, 9),
            metadata = p_metadata
        WHERE id = (
            SELECT id 
            FROM public.usage_history 
            WHERE user_id = p_user_id
            AND service = p_service
            AND model = p_model
            AND status = 'pending'
            ORDER BY created_at DESC
            LIMIT 1
        );

        -- Update credits if using credits pricing model
        IF p_pricing_model = 'credits' THEN
            UPDATE public.credits
            SET balance = ROUND(balance - p_cost, 9)
            WHERE user_id = p_user_id;
        END IF;

        -- Update quota if using subscription
        IF p_pricing_model = 'subscription' THEN
            UPDATE public.quotas q
            SET used_amount = ROUND(used_amount + p_amount, 9)
            FROM public.user_subscriptions us
            WHERE q.subscription_id = us.id
            AND us.user_id = p_user_id
            AND q.service = p_service
            AND us.status = 'active'
            AND q.reset_date > NOW();
        END IF;

        -- Handle PAYG usage recording with new payment_status
        IF p_pricing_model = 'payg' THEN
            INSERT INTO public.payg_usage (
                user_id, 
                month, 
                total_amount, 
                payment_status
            ) VALUES (
                p_user_id, 
                date_trunc('month', CURRENT_DATE), 
                p_cost,
                'pending'
            )
            ON CONFLICT (user_id, month) 
            DO UPDATE SET 
                total_amount = ROUND(payg_usage.total_amount + EXCLUDED.total_amount, 9),
                payment_status = 
                    -- Only change to pending if it was previously paid
                    CASE 
                        WHEN payg_usage.payment_status = 'paid' THEN 'pending'
                        ELSE payg_usage.payment_status
                    END;
        END IF;
    END IF;
END;
$$;

-- Drop the negative_balances table last (after removing dependencies)
DROP TABLE IF EXISTS public.negative_balances; 
# API Key Management

## Overview

This document describes the API key management functionality implemented in the VoiceHype application. Users can create, rename, enable/disable, and delete API keys through the web interface.

## Database Functions

### 1. `create_api_key`

- **Purpose**: Creates a new API key for the authenticated user
- **Parameters**:
  - `p_name`: The name of the API key
  - `p_expires_at`: Optional expiration date
- **Returns**: A composite type `api_key_result` containing both the ID and the key secret
- **Security**: SECURITY DEFINER function, only accessible to authenticated users

### 2. `update_api_key_name`

- **Purpose**: Updates the name of an existing API key
- **Parameters**:
  - `p_key_id`: The ID of the API key to update
  - `p_name`: The new name for the API key
- **Returns**: <PERSON>olean indicating success
- **Security**: SECURITY DEFINER function, only accessible to authenticated users

### 3. `update_api_key_status`

- **Purpose**: Updates the active status of an API key (enable/disable)
- **Parameters**:
  - `p_key_id`: The ID of the API key to update
  - `p_is_active`: <PERSON><PERSON>an indicating whether the key should be active
- **Returns**: <PERSON><PERSON><PERSON> indicating success
- **Security**: SECURITY DEFINER function, only accessible to authenticated users

## Database Schema

The API keys are stored in the `api_keys` table with the following structure:

```sql
CREATE TABLE public.api_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    key_prefix TEXT NOT NULL,
    key_hash TEXT NOT NULL,
    allow_credits BOOLEAN DEFAULT TRUE,
    allow_payg BOOLEAN DEFAULT FALSE,
    allow_subscriptions BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    last_used_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ
);
```

## Frontend Implementation

The frontend provides a user interface for managing API keys with the following features:

1. **Create API Key**: Users can create a new API key with a name and optional expiration date
2. **Rename API Key**: Users can rename existing API keys
3. **Enable/Disable API Key**: Users can toggle the active status of an API key
4. **Delete API Key**: Users can permanently delete an API key

## Security Considerations

- API keys are stored securely in the database using a hash function
- Only the key prefix is stored in plain text for display purposes
- The full API key is only shown once to the user after creation
- All API key management functions are protected by authentication
- Users can only manage their own API keys

## How to Apply the Migrations

### Option 1: Using Supabase CLI

If you have the Supabase CLI installed:

```bash
# Navigate to your project directory
cd /path/to/your/project

# Apply the migrations
supabase db push
```

### Option 2: Using the Supabase Dashboard

1. Log in to your Supabase dashboard
2. Go to the SQL Editor
3. Copy the contents of the migration files
4. Paste them into a new SQL query
5. Run the query

## Migration Files

1. `20250312_002_fix_api_key_return.sql`: Updates the `create_api_key` function to return both the ID and key secret
2. `20250312_003_api_key_status_function.sql`: Adds the `update_api_key_status` function for enabling/disabling API keys 
DO $$ 
DECLARE
    v_pay_as_you_go_id UUID;
    v_credits_id UUID;
    v_quota_id UUID;
BEGIN
    -- Get pricing model IDs
    SELECT id INTO v_pay_as_you_go_id FROM public.pricing_models WHERE type = 'pay_as_you_go';
    SELECT id INTO v_credits_id FROM public.pricing_models WHERE type = 'credits';
    SELECT id INTO v_quota_id FROM public.pricing_models WHERE type = 'quota';

    -- Insert Pay-as-you-go pricing
    INSERT INTO public.service_pricing 
        (pricing_model_id, service, model, cost_per_unit, unit)
    VALUES
        -- Transcription Services
        (v_pay_as_you_go_id, 'transcription', 'large-v3', 0.08, 'minute'),
        (v_pay_as_you_go_id, 'transcription', 'medium', 0.06, 'minute'),
        (v_pay_as_you_go_id, 'transcription', 'small', 0.04, 'minute'),
        (v_pay_as_you_go_id, 'transcription', 'best', 0.10, 'minute'),
        (v_pay_as_you_go_id, 'transcription', 'nano', 0.05, 'minute'),
        -- Optimization Input
        (v_pay_as_you_go_id, 'optimization_input', 'openai/gpt-4o', 0.0005, 'token'),
        (v_pay_as_you_go_id, 'optimization_input', 'anthropic/claude-3-sonnet', 0.0003, 'token'),
        (v_pay_as_you_go_id, 'optimization_input', 'meta/llama-3-70b', 0.0002, 'token'),
        (v_pay_as_you_go_id, 'optimization_input', 'meta/llama-3-8b', 0.0001, 'token'),
        -- Optimization Output
        (v_pay_as_you_go_id, 'optimization_output', 'openai/gpt-4o', 0.0015, 'token'),
        (v_pay_as_you_go_id, 'optimization_output', 'anthropic/claude-3-sonnet', 0.0009, 'token'),
        (v_pay_as_you_go_id, 'optimization_output', 'meta/llama-3-70b', 0.0006, 'token'),
        (v_pay_as_you_go_id, 'optimization_output', 'meta/llama-3-8b', 0.0003, 'token');

    -- Insert Credits pricing (same prices but with minimum purchase amount)
    INSERT INTO public.service_pricing 
        (pricing_model_id, service, model, cost_per_unit, unit, min_purchase_amount)
    VALUES
        -- Transcription Services
        (v_credits_id, 'transcription', 'large-v3', 0.08, 'minute', 5.00),
        (v_credits_id, 'transcription', 'medium', 0.06, 'minute', 5.00),
        (v_credits_id, 'transcription', 'small', 0.04, 'minute', 5.00),
        (v_credits_id, 'transcription', 'best', 0.10, 'minute', 5.00),
        (v_credits_id, 'transcription', 'nano', 0.05, 'minute', 5.00),
        -- Optimization Input
        (v_credits_id, 'optimization_input', 'openai/gpt-4o', 0.0005, 'token', 5.00),
        (v_credits_id, 'optimization_input', 'anthropic/claude-3-sonnet', 0.0003, 'token', 5.00),
        (v_credits_id, 'optimization_input', 'meta/llama-3-70b', 0.0002, 'token', 5.00),
        (v_credits_id, 'optimization_input', 'meta/llama-3-8b', 0.0001, 'token', 5.00),
        -- Optimization Output
        (v_credits_id, 'optimization_output', 'openai/gpt-4o', 0.0015, 'token', 5.00),
        (v_credits_id, 'optimization_output', 'anthropic/claude-3-sonnet', 0.0009, 'token', 5.00),
        (v_credits_id, 'optimization_output', 'meta/llama-3-70b', 0.0006, 'token', 5.00),
        (v_credits_id, 'optimization_output', 'meta/llama-3-8b', 0.0003, 'token', 5.00);

    -- For quota-based pricing, we only need to track quotas in the quotas table
    -- No need to insert pricing information as we only track usage against quota limits

END $$;

-- Create an index for faster service pricing lookups
CREATE INDEX idx_service_pricing_lookup ON public.service_pricing (service, model);

-- Add a comment explaining the pricing structure
COMMENT ON TABLE public.service_pricing IS 
'Service pricing table with the following structure:
- Transcription: Charged per minute
  * Whisper models: $0.04-$0.08/min
  * AssemblyAI models: $0.05-$0.10/min (nano vs best)
- Optimization: Separate pricing for input and output tokens
- Each model has its own pricing for pay-as-you-go and credits models
- Credits model requires minimum $5 purchase
- Quota model does not use pricing, only tracks usage against fixed quotas'; 
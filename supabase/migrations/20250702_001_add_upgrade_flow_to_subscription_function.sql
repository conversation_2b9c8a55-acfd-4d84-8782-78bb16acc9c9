-- Add upgrade flow parameter to handle_subscription_transaction function
-- This allows the function to behave differently for immediate vs scheduled upgrades

CREATE OR REPLACE FUNCTION public.handle_subscription_transaction(
    p_user_id uuid, 
    p_paddle_subscription_id text, 
    p_paddle_customer_id text, 
    p_subscription_plan text, 
    p_transaction_data jsonb DEFAULT NULL::jsonb, 
    p_transcription_quota_override integer DEFAULT NULL::integer, 
    p_optimization_quota_override integer DEFAULT NULL::integer,
    p_upgrade_flow text DEFAULT NULL::text  -- New parameter for upgrade flow
)
RETURNS jsonb
LANGUAGE plpgsql
AS $function$
DECLARE
    v_plan_id UUID;
    v_subscription_id UUID;
    v_transcription_quota INTEGER;
    v_optimization_quota INTEGER;
    v_reset_date TIMESTAMP WITH TIME ZONE;
    v_result JSONB;
    v_is_scheduled_upgrade BOOLEAN := FALSE;
BEGIN
    -- Log the start of the operation
    RAISE LOG 'Starting subscription transaction for user: %, plan: %, upgrade_flow: %', p_user_id, p_subscription_plan, p_upgrade_flow;
    
    -- Check if this is a scheduled upgrade (update_next_billing)
    v_is_scheduled_upgrade := (p_upgrade_flow = 'update_next_billing');
    
    -- Set reset date to 30 days from now
    v_reset_date := NOW() + INTERVAL '30 days';
    
    -- Get plan details and quotas based on plan name, unless overrides are provided
    IF p_transcription_quota_override IS NOT NULL AND p_optimization_quota_override IS NOT NULL THEN
        -- Use the provided quota overrides (for quota merging scenarios)
        v_transcription_quota := p_transcription_quota_override;
        v_optimization_quota := p_optimization_quota_override;
        RAISE LOG 'Using quota overrides - transcription: %, optimization: %', v_transcription_quota, v_optimization_quota;
    ELSE
        -- Use default plan quotas
        CASE LOWER(p_subscription_plan)
            WHEN 'basic' THEN
                v_transcription_quota := 720;    -- 12 hours
                v_optimization_quota := 400000;  -- 400K tokens
            WHEN 'pro' THEN
                v_transcription_quota := 1440;   -- 24 hours
                v_optimization_quota := 800000;  -- 800K tokens
            WHEN 'premium' THEN
                v_transcription_quota := 2160;   -- 36 hours
                v_optimization_quota := 1200000; -- 1.2M tokens
            ELSE
                RAISE EXCEPTION 'Unknown subscription plan: %', p_subscription_plan;
        END CASE;
        RAISE LOG 'Using default plan quotas - transcription: %, optimization: %', v_transcription_quota, v_optimization_quota;
    END IF;
    
    -- Try to get the plan_id from subscription_plans table
    SELECT id INTO v_plan_id
    FROM subscription_plans 
    WHERE LOWER(name) = LOWER(p_subscription_plan)
    LIMIT 1;
    
    -- If plan not found, log warning but continue
    IF v_plan_id IS NULL THEN
        RAISE LOG 'Plan ID not found for plan: %, continuing without plan_id', p_subscription_plan;
    END IF;
    
    -- UPSERT user subscription (one subscription per user)
    -- For scheduled upgrades, only update the plan_id and updated_at
    IF v_is_scheduled_upgrade THEN
        RAISE LOG 'Processing scheduled upgrade - updating plan_id only, keeping current quotas';
        
        UPDATE user_subscriptions 
        SET 
            plan_id = v_plan_id,
            updated_at = NOW()
        WHERE user_id = p_user_id
        RETURNING id INTO v_subscription_id;
        
        -- If no subscription found to update, create a new one
        IF v_subscription_id IS NULL THEN
            INSERT INTO user_subscriptions (
                user_id,
                plan_id,
                status,
                current_period_start,
                current_period_end,
                paddle_subscription_id,
                created_at
            ) VALUES (
                p_user_id,
                v_plan_id,
                'active',
                NOW(),
                v_reset_date,
                p_paddle_subscription_id,
                NOW()
            )
            RETURNING id INTO v_subscription_id;
        END IF;
        
        -- For scheduled upgrades, DO NOT update quotas - they will be updated at next billing cycle
        RAISE LOG 'Scheduled upgrade processed - quotas will be updated at next billing cycle';
        
    ELSE
        -- Regular subscription processing (immediate upgrades or new subscriptions)
        INSERT INTO user_subscriptions (
            user_id,
            plan_id,
            status,
            current_period_start,
            current_period_end,
            paddle_subscription_id,
            created_at
        ) VALUES (
            p_user_id,
            v_plan_id,
            'active',
            NOW(),
            v_reset_date,
            p_paddle_subscription_id,
            NOW()
        )
        ON CONFLICT (user_id) 
        DO UPDATE SET
            plan_id = EXCLUDED.plan_id,
            status = EXCLUDED.status,
            current_period_start = EXCLUDED.current_period_start,
            current_period_end = EXCLUDED.current_period_end,
            paddle_subscription_id = EXCLUDED.paddle_subscription_id
        RETURNING id INTO v_subscription_id;
        
        RAISE LOG 'Upserted user subscription with ID: %', v_subscription_id;
        
        -- UPSERT transcription quota (one quota per user per service)
        INSERT INTO quotas (
            user_id,
            subscription_id,
            service,
            used_amount,
            total_amount,
            reset_date,
            created_at
        ) VALUES (
            p_user_id,
            v_subscription_id,
            'transcription',
            0,
            v_transcription_quota,
            v_reset_date,
            NOW()
        )
        ON CONFLICT (user_id, service)
        DO UPDATE SET
            subscription_id = EXCLUDED.subscription_id,
            used_amount = 0, -- Reset usage on subscription change
            total_amount = EXCLUDED.total_amount,
            reset_date = EXCLUDED.reset_date;
        
        -- UPSERT optimization quota (one quota per user per service)
        INSERT INTO quotas (
            user_id,
            subscription_id,
            service,
            used_amount,
            total_amount,
            reset_date,
            created_at
        ) VALUES (
            p_user_id,
            v_subscription_id,
            'optimization',
            0,
            v_optimization_quota,
            v_reset_date,
            NOW()
        )
        ON CONFLICT (user_id, service)
        DO UPDATE SET
            subscription_id = EXCLUDED.subscription_id,
            used_amount = 0, -- Reset usage on subscription change
            total_amount = EXCLUDED.total_amount,
            reset_date = EXCLUDED.reset_date;
    END IF;
    
    -- Optional: UPSERT into paddle.subscriptions table if it exists
    BEGIN
        INSERT INTO paddle.subscriptions (
            user_id,
            paddle_subscription_id,
            paddle_customer_id,
            product_id,
            status,
            current_period_start,
            current_period_end,
            metadata,
            created_at
        ) VALUES (
            p_user_id,
            p_paddle_subscription_id,
            p_paddle_customer_id,
            COALESCE(v_plan_id::TEXT, p_subscription_plan),
            'active',
            NOW(),
            v_reset_date,
            p_transaction_data,
            NOW()
        )
        ON CONFLICT (paddle_subscription_id)
        DO UPDATE SET
            user_id = EXCLUDED.user_id,
            paddle_customer_id = EXCLUDED.paddle_customer_id,
            product_id = EXCLUDED.product_id,
            status = EXCLUDED.status,
            current_period_start = EXCLUDED.current_period_start,
            current_period_end = EXCLUDED.current_period_end,
            metadata = EXCLUDED.metadata;
        
        RAISE LOG 'Upserted paddle subscription record';
    EXCEPTION
        WHEN undefined_table THEN
            RAISE LOG 'paddle.subscriptions table does not exist, skipping';
        WHEN OTHERS THEN
            RAISE LOG 'Error upserting paddle subscription: %', SQLERRM;
    END;
    
    -- Build result JSON
    v_result := jsonb_build_object(
        'success', true,
        'user_id', p_user_id,
        'subscription_id', v_subscription_id,
        'plan', p_subscription_plan,
        'upgrade_flow', p_upgrade_flow,
        'is_scheduled_upgrade', v_is_scheduled_upgrade,
        'quotas', jsonb_build_object(
            'transcription', v_transcription_quota,
            'optimization', v_optimization_quota
        ),
        'reset_date', v_reset_date
    );
    
    RAISE LOG 'Successfully processed subscription transaction: %', v_result;
    
    RETURN v_result;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE LOG 'Error in handle_subscription_transaction: %', SQLERRM;
        RETURN jsonb_build_object(
            'success', false,
            'error', SQLERRM,
            'user_id', p_user_id,
            'plan', p_subscription_plan,
            'upgrade_flow', p_upgrade_flow
        );
END;
$function$;

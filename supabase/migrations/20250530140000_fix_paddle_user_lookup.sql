-- Fix for handling existing users in process_completed_transaction function
-- This migration updates the function to look up existing users by email before creating new ones

CREATE OR REPLACE FUNCTION public.process_completed_transaction(
    p_paddle_transaction_id TEXT,
    p_paddle_customer_id TEXT,
    p_amount DECIMAL(18,2),
    p_currency TEXT,
    p_product_id TEXT DEFAULT NULL,
    p_receipt_url TEXT DEFAULT NULL,
    p_metadata JSONB DEFAULT '{}'::jsonb
) RETURNS BOOLEAN
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
DECLARE
    v_user_id UUID;
    v_customer_id UUID;
    v_product_id UUID;
    v_credit_amount DECIMAL(18,9);
    v_transaction_id UUID;
    v_existing_transaction UUID;
    v_email TEXT;
    v_customer_name TEXT;
    v_existing_user_id UUID;
BEGIN
    -- Check if transaction already exists
    SELECT id INTO v_existing_transaction
    FROM paddle.transactions
    WHERE paddle_transaction_id = p_paddle_transaction_id;

    IF v_existing_transaction IS NOT NULL THEN
        -- Transaction already processed
        RETURN TRUE;
    END IF;

    -- Get customer and user info from paddle.customers
    SELECT user_id, id INTO v_user_id, v_customer_id
    FROM paddle.customers
    WHERE paddle_customer_id = p_paddle_customer_id;

    -- If we found a customer ID but no user ID, something is wrong with the data
    IF v_customer_id IS NOT NULL AND v_user_id IS NULL THEN
        RAISE NOTICE 'Customer record found but missing user_id for paddle_customer_id: %', p_paddle_customer_id;
        
        -- Try to recover by updating the customer record with a new user ID
        v_user_id := gen_random_uuid();
        UPDATE paddle.customers SET user_id = v_user_id WHERE id = v_customer_id;
    END IF;

    -- Handle new customers by creating a record for them
    IF v_user_id IS NULL THEN
        -- Extract customer email from metadata
        v_email := p_metadata->>'email';
        v_customer_name := COALESCE(
            p_metadata->>'name',
            p_metadata->>'customer_name',
            'VoiceHype User'
        );
        
        IF v_email IS NULL THEN
            -- Try to find email in nested structure
            v_email := p_metadata->'customer'->>'email';
        END IF;
        
        IF v_email IS NULL THEN
            -- If still no email, create a placeholder
            v_email := 'customer-' || p_paddle_customer_id || '@voicehype.ai';
        END IF;

        -- First, check if we have an existing user with this email
        SELECT id INTO v_existing_user_id
        FROM auth.users
        WHERE email = v_email;

        IF v_existing_user_id IS NOT NULL THEN
            -- Use the existing user instead of creating a new one
            v_user_id := v_existing_user_id;
            RAISE NOTICE 'Found existing user with ID % for email %', v_user_id, v_email;
        ELSE
            -- Create an anonymous user in auth.users
            INSERT INTO auth.users (
                id,
                email,
                raw_app_meta_data,
                raw_user_meta_data,
                created_at,
                updated_at
            ) VALUES (
                gen_random_uuid(),
                v_email,
                jsonb_build_object(
                    'provider', 'paddle',
                    'providers', array['paddle']
                ),
                jsonb_build_object(
                    'name', v_customer_name,
                    'paddle_customer_id', p_paddle_customer_id,
                    'source', 'paddle_purchase'
                ),
                NOW(),
                NOW()
            ) RETURNING id INTO v_user_id;
            
            -- Create initial profile - only if it doesn't exist
            INSERT INTO public.profiles (
                id,
                email,
                full_name,
                created_at,
                updated_at
            ) VALUES (
                v_user_id,
                v_email,
                v_customer_name,
                NOW(),
                NOW()
            ) ON CONFLICT (id) DO NOTHING;
        END IF;
        
        -- Create or update customer record
        INSERT INTO paddle.customers (
            user_id,
            paddle_customer_id,
            email,
            name,
            created_at,
            metadata
        ) VALUES (
            v_user_id,
            p_paddle_customer_id,
            v_email,
            v_customer_name,
            NOW(),
            p_metadata
        ) ON CONFLICT (paddle_customer_id) 
        DO UPDATE SET
            user_id = v_user_id,
            email = v_email,
            name = v_customer_name,
            metadata = p_metadata;
        
        RAISE NOTICE 'Associated Paddle customer % with user ID %', p_paddle_customer_id, v_user_id;
    END IF;

    -- Get product info if product_id provided (for reference only)
    IF p_product_id IS NOT NULL THEN
        SELECT id INTO v_product_id
        FROM paddle.products
        WHERE paddle_product_id = p_product_id AND is_active = TRUE;

        -- Skip the product validation - no need to fail if product is missing
        -- Just log it in the transaction
    END IF;

    -- Convert amount from cents to dollars (credit units)
    -- For VoiceHype, $1 = 1 credit (flexible credit purchasing)
    v_credit_amount := p_amount / 100.0;

    -- Create transaction record
    INSERT INTO paddle.transactions (
        user_id,
        paddle_transaction_id,
        paddle_customer_id,
        product_id,
        status,
        amount,
        currency,
        credit_amount,
        transaction_type,
        paddle_receipt_url,
        metadata,
        created_at,
        updated_at
    ) VALUES (
        v_user_id,
        p_paddle_transaction_id,
        p_paddle_customer_id,
        v_product_id,
        'completed',
        p_amount,
        p_currency,
        v_credit_amount,
        'credit_purchase',
        p_receipt_url,
        p_metadata,
        NOW(),
        NOW()
    ) RETURNING id INTO v_transaction_id;

    -- Add credits to user's account (with proper fallback if constraint doesn't exist yet)
    BEGIN
        -- Try to use the ON CONFLICT approach first
        INSERT INTO public.credits (user_id, balance, currency, created_at, updated_at)
        VALUES (v_user_id, v_credit_amount, p_currency, NOW(), NOW())
        ON CONFLICT (user_id)
        DO UPDATE SET
            balance = credits.balance + v_credit_amount,
            updated_at = NOW();
    EXCEPTION
        WHEN undefined_object THEN
            -- If the constraint doesn't exist yet, use a more careful approach
            DECLARE
                v_existing_balance DECIMAL(18,9);
            BEGIN
                -- Check if a record already exists
                SELECT balance INTO v_existing_balance
                FROM public.credits
                WHERE user_id = v_user_id;
                
                IF v_existing_balance IS NOT NULL THEN
                    -- Update existing record
                    UPDATE public.credits
                    SET balance = balance + v_credit_amount,
                        updated_at = NOW()
                    WHERE user_id = v_user_id;
                ELSE
                    -- Insert new record
                    INSERT INTO public.credits (user_id, balance, currency, created_at, updated_at)
                    VALUES (v_user_id, v_credit_amount, p_currency, NOW(), NOW());
                END IF;
            END;
    END;

    RETURN TRUE;
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.process_completed_transaction TO service_role;

-- Add comment explaining the change
COMMENT ON FUNCTION public.process_completed_transaction IS 'Processes completed Paddle transactions and adds credits to user accounts. Handles existing users by email lookup, new customers creation, and robust credits updating.';

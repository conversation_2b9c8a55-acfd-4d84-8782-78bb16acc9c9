# Combining Token Columns Migration

## Overview

This migration simplifies the token management in the VoiceHype platform by combining the previously separate `input_tokens` and `output_tokens` columns into a single `tokens` column in the `subscription_plans` table.

## Changes

### Migration File: `20250313_003_combine_token_columns.sql`

This migration:

1. Adds a new `tokens` column to the `subscription_plans` table
2. Populates the new column with the sum of `input_tokens` and `output_tokens`
3. Makes the new column NOT NULL
4. Drops the old separate columns

## Implementation Details

The migration modifies the `subscription_plans` table:
- Adds a new `tokens` column
- Populates it with the sum of `input_tokens` and `output_tokens`
- Makes the new column NOT NULL
- Drops the old separate columns

This change simplifies the subscription plan structure by consolidating token allocations into a single value.

## How to Apply the Migration

### Option 1: Using Supabase CLI

If you have the Supabase CLI installed:

```bash
# Navigate to your project directory
cd /path/to/your/project

# Apply the migration
supabase db push
```

### Option 2: Using the Supabase Dashboard

1. Log in to your Supabase dashboard
2. Go to the SQL Editor
3. Copy the contents of the migration file
4. Paste it into a new SQL query
5. Run the query

## Verifying the Migration

After applying the migration:

1. Check the `subscription_plans` table to verify it now has a `tokens` column and no longer has separate `input_tokens` and `output_tokens` columns
2. Verify that the `tokens` column contains the sum of the previous `input_tokens` and `output_tokens` values

## Impact on Existing Code

This migration will require updates to any code that references the `input_tokens` or `output_tokens` columns in the `subscription_plans` table.

The following components should be reviewed and updated:
- Frontend displays of subscription plans
- Any code that reads subscription plan details

## Troubleshooting

If you encounter any issues after applying this migration:

1. Check the Supabase logs for any error messages
2. Verify that all subscription plans have been properly updated with the combined tokens value

If problems persist, please contact the development team for assistance. 
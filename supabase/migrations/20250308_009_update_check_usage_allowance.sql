-- Update check_usage_allowance function to handle optimization service differently
BEGIN;

-- Drop the existing function
DROP FUNCTION IF EXISTS check_usage_allowance;

-- Create the updated function
CREATE OR REPLACE FUNCTION check_usage_allowance(p_user_id UUID, p_service TEXT, p_model TEXT, p_amount DECIMAL, p_api_key_id UUID)
RETURNS TABLE (
    can_use BOOLEAN,
    pricing_model TEXT,
    cost DECIMAL,
    max_output_tokens INTEGER
) AS $$
DECLARE
    subscription_available BOOLEAN;
    credit_balance DECIMAL;
    service_cost DECIMAL;
    payg_allowed BOOLEAN;
    input_cost_per_unit DECIMAL;
    output_cost_per_unit DECIMAL;
    max_tokens INTEGER;
BEGIN
    -- Get service cost
    SELECT cost_per_unit * p_amount INTO service_cost
    FROM public.service_pricing sp
    WHERE sp.service = p_service
    AND sp.model = p_model
    AND sp.is_active = TRUE;

    -- Check subscription quota first
    SELECT EXISTS (
        SELECT 1 FROM public.quotas q
        JOIN public.user_subscriptions us ON q.subscription_id = us.id
        WHERE us.user_id = p_user_id
        AND q.service = p_service
        AND (q.used_amount + p_amount) <= q.total_amount
        AND us.status = 'active'
        AND q.reset_date > NOW()
    ) INTO subscription_available;

    IF subscription_available THEN 
        RETURN QUERY SELECT 
            TRUE as can_use,
            'subscription'::TEXT as pricing_model,
            service_cost as cost,
            4096 as max_output_tokens; -- Default max tokens for subscription
        RETURN;
    END IF;

    -- Check credit balance
    SELECT balance INTO credit_balance
    FROM public.credits
    WHERE user_id = p_user_id;

    -- Special handling for optimization service (LLM calls)
    IF p_service = 'optimization' THEN
        -- Try to get separate input/output pricing
        SELECT sp.cost_per_unit INTO input_cost_per_unit
        FROM public.service_pricing sp
        WHERE sp.service = 'optimization'
        AND sp.model = p_model || '/input'
        AND sp.is_active = TRUE;

        SELECT sp.cost_per_unit INTO output_cost_per_unit
        FROM public.service_pricing sp
        WHERE sp.service = 'optimization'
        AND sp.model = p_model || '/output'
        AND sp.is_active = TRUE;

        -- If we have separate input/output pricing
        IF input_cost_per_unit IS NOT NULL AND output_cost_per_unit IS NOT NULL THEN
            -- Calculate input tokens cost
            service_cost := input_cost_per_unit * p_amount;
            
            -- If user has any credits at all
            IF credit_balance > 0 THEN
                -- Calculate how many output tokens they can afford
                -- First subtract the cost of input tokens
                IF credit_balance > service_cost THEN
                    -- Remaining credits after input tokens
                    credit_balance := credit_balance - service_cost;
                    
                    -- Calculate max output tokens they can afford
                    max_tokens := FLOOR(credit_balance / output_cost_per_unit);
                    
                    -- Cap at a reasonable maximum (4096 is common for many models)
                    max_tokens := LEAST(max_tokens, 4096);
                    
                    RETURN QUERY SELECT 
                        TRUE as can_use,
                        'credits'::TEXT as pricing_model,
                        service_cost as cost, -- This is just the input cost for now
                        max_tokens as max_output_tokens;
                    RETURN;
                END IF;
            END IF;
        ELSE
            -- If we don't have separate pricing, use the combined pricing
            -- If user has any credits at all and they can cover at least the input tokens
            IF credit_balance > 0 THEN
                -- Calculate max tokens they can afford (input + output)
                max_tokens := FLOOR(credit_balance / service_cost) - p_amount;
                
                -- If they can afford at least some output tokens
                IF max_tokens > 0 THEN
                    -- Cap at a reasonable maximum
                    max_tokens := LEAST(max_tokens, 4096);
                    
                    RETURN QUERY SELECT 
                        TRUE as can_use,
                        'credits'::TEXT as pricing_model,
                        service_cost as cost,
                        max_tokens as max_output_tokens;
                    RETURN;
                END IF;
            END IF;
        END IF;
    ELSE
        -- For non-optimization services (like transcription), use the original logic
        IF credit_balance >= service_cost THEN 
            RETURN QUERY SELECT 
                TRUE as can_use,
                'credits'::TEXT as pricing_model,
                service_cost as cost,
                NULL as max_output_tokens;
            RETURN;
        END IF;
    END IF;

    -- Check if PAYG is allowed for this API key
    SELECT allow_payg INTO payg_allowed
    FROM public.api_keys
    WHERE id = p_api_key_id
    AND is_active = TRUE;

    -- Fallback to PAYG if allowed and user has stripe customer id
    IF payg_allowed AND EXISTS (
        SELECT 1 FROM public.profiles
        WHERE id = p_user_id
        AND stripe_customer_id IS NOT NULL
    ) THEN
        -- No longer inserting into payg_usage here, will be done in finalize_usage

        RETURN QUERY SELECT 
            TRUE as can_use,
            'payg'::TEXT as pricing_model,
            service_cost as cost,
            4096 as max_output_tokens; -- Default max tokens for PAYG
        RETURN;
    END IF;

    -- If we get here, no payment method is available
    RETURN QUERY SELECT 
        FALSE as can_use,
        NULL::TEXT as pricing_model,
        service_cost as cost,
        NULL as max_output_tokens;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMIT; 
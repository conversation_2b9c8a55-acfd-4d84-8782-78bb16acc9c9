-- Simplified subscription tracking - no complex access checking needed
-- The existing quota system already handles access control naturally

-- Function to get payment failure statistics for analytics
CREATE OR REPLACE FUNCTION public.get_payment_failure_stats(p_subscription_id TEXT, p_days INTEGER DEFAULT 30)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_failure_count INTEGER;
    v_total_failed_amount DECIMAL(10,2);
    v_last_failure_date TIMESTAMPTZ;
    v_recent_failures JSON;
BEGIN
    -- Get failure count and total amount
    SELECT 
        COUNT(*),
        COALESCE(SUM(amount), 0),
        MAX(occurred_at)
    INTO v_failure_count, v_total_failed_amount, v_last_failure_date
    FROM public.payment_failures
    WHERE subscription_id = p_subscription_id
    AND occurred_at >= NOW() - INTERVAL '1 day' * p_days;

    -- Get recent failures details
    SELECT JSON_AGG(
        JSON_BUILD_OBJECT(
            'transaction_id', transaction_id,
            'amount', amount,
            'currency', currency,
            'failure_reason', failure_reason,
            'occurred_at', occurred_at
        )
        ORDER BY occurred_at DESC
    ) INTO v_recent_failures
    FROM public.payment_failures
    WHERE subscription_id = p_subscription_id
    AND occurred_at >= NOW() - INTERVAL '1 day' * p_days
    LIMIT 10;

    RETURN JSON_BUILD_OBJECT(
        'subscription_id', p_subscription_id,
        'period_days', p_days,
        'failure_count', v_failure_count,
        'total_failed_amount', v_total_failed_amount,
        'last_failure_date', v_last_failure_date,
        'recent_failures', COALESCE(v_recent_failures, '[]'::JSON)
    );
END;
$$;

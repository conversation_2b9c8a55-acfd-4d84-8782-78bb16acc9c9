# Fix for Real-time Transcription Duration Calculation

## Overview

This migration addresses an issue with the real-time transcription feature where the duration of the transcription is not being properly calculated and updated in the database after the transcription session ends.

## Problem

Currently, when a real-time transcription session starts, a "pending" entry is created in the `usage_history` table with 0 seconds duration. When the session ends, the `finalizeSession` function is called, which calls the `finalize_realtime_session` database function. However, the function is calculating the duration based on the session time (start to end), not the actual audio processed.

This leads to inaccurate billing and usage tracking for real-time transcription sessions.

## Solution

The solution involves modifying the `realtime.ts` file to:

1. Track successfully processed audio chunks during the real-time transcription session
2. Calculate the actual audio duration based on the audio properties (sample rate, channels, etc.)
3. Pass the calculated duration to the `finalize_realtime_session` function
4. Update the `usage_history` entry with the calculated duration

## Implementation

The implementation involves adding code to the `realtime.ts` file to:

1. Add variables to track the total number of audio samples processed
2. Calculate the audio duration based on the sample rate, channels, and bits per sample
3. Pass this calculated duration to the `finalize_realtime_session` function

## Testing

To test this change:

1. Start a real-time transcription session
2. Send audio chunks to the server
3. End the session
4. Check the `usage_history` table to verify that the duration is correctly calculated and updated

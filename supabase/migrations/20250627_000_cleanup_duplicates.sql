-- Migration: Clean up duplicate data before applying UPSERT constraints
-- Created: 2025-06-27
-- Purpose: Remove duplicate entries to prepare for unique constraints

-- Step 1: Clean up duplicate user_subscriptions (keep only the most recent)
WITH ranked_subscriptions AS (
    SELECT id, user_id,
           ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC, id DESC) as rn
    FROM user_subscriptions
)
DELETE FROM user_subscriptions 
WHERE id IN (
    SELECT id FROM ranked_subscriptions WHERE rn > 1
);

-- Step 2: Clean up duplicate quotas (keep only the most recent for each user/service combination)
WITH ranked_quotas AS (
    SELECT id, user_id, service,
           ROW_NUMBER() OVER (PARTITION BY user_id, service ORDER BY created_at DESC, id DESC) as rn
    FROM quotas
)
DELETE FROM quotas 
WHERE id IN (
    SELECT id FROM ranked_quotas WHERE rn > 1
);

-- Step 3: Log the cleanup results
DO $$
DECLARE
    subscription_count INTEGER;
    quota_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO subscription_count FROM user_subscriptions;
    SELECT COUNT(*) INTO quota_count FROM quotas;
    
    RAISE NOTICE 'Cleanup completed. Remaining records:';
    RAISE NOTICE 'user_subscriptions: %', subscription_count;
    RAISE NOTICE 'quotas: %', quota_count;
END $$;

-- Step 4: Verify no duplicates remain
DO $$
DECLARE
    duplicate_subs INTEGER;
    duplicate_quotas INTEGER;
BEGIN
    -- Check for duplicate user subscriptions
    SELECT COUNT(*) INTO duplicate_subs 
    FROM (
        SELECT user_id, COUNT(*) as cnt 
        FROM user_subscriptions 
        GROUP BY user_id 
        HAVING COUNT(*) > 1
    ) dupes;
    
    -- Check for duplicate quotas
    SELECT COUNT(*) INTO duplicate_quotas 
    FROM (
        SELECT user_id, service, COUNT(*) as cnt 
        FROM quotas 
        GROUP BY user_id, service 
        HAVING COUNT(*) > 1
    ) dupes;
    
    IF duplicate_subs > 0 THEN
        RAISE WARNING 'Found % users with duplicate subscriptions after cleanup', duplicate_subs;
    ELSE
        RAISE NOTICE 'No duplicate user subscriptions found';
    END IF;
    
    IF duplicate_quotas > 0 THEN
        RAISE WARNING 'Found % duplicate quota entries after cleanup', duplicate_quotas;
    ELSE
        RAISE NOTICE 'No duplicate quotas found';
    END IF;
END $$;

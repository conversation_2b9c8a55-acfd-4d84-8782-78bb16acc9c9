# Credits Expiration Feature

## Purpose

This migration adds an `expires_at` column to the `credits` table, allowing credits to have an expiration date. This is a crucial feature for:

1. Implementing limited-time promotions (e.g., "Use these credits within 3 months")
2. Ensuring credits from free trials have a finite lifespan
3. Managing credits that were given as part of special offers
4. Improving user experience by showing when credits will expire
5. Better financial planning and revenue recognition

## Technical Changes

The migration:

1. Adds a nullable `expires_at` timestamp with time zone column to the `credits` table
2. Adds descriptive comments to the column
3. Ensures proper permissions are set
4. Updates existing credits with a default expiration date (3 months from creation)
5. Updates the `check_usage_allowance` function to only consider non-expired credits when checking available balance
6. Updates the `finalize_usage` function to implement a "soonest-to-expire-first" credit usage strategy

## Credit Usage Strategy

The updated `finalize_usage` function implements an intelligent credit consumption strategy:

1. **Expiring Credits First**: The function first uses credits that have an expiration date, prioritizing those that will expire soonest.
2. **Non-Expiring Credits Last**: Only after all expiring credits are consumed, the function will use credits that don't have an expiration date (where `expires_at` is NULL).
3. **Within Each Category**: Credits are consumed in order of expiration date, then creation date.

This ensures that users get the maximum value from their credits by preventing unnecessary expiration due to usage order.

## Implementation Notes

- If `expires_at` is NULL, the credits never expire
- The frontend should be updated to display expiration dates to users
- When checking available credits, the application should filter out expired credits
- The credit consumption logic now automatically prioritizes soon-to-expire credits

## Related Features

This feature enables:

- Credit expiration warnings for users
- Automatic prioritization of credits usage based on expiration dates
- Ability to offer time-limited promotional credits
- Improved analytics on credit usage patterns

## Future Considerations

- We may want to implement a notification system to alert users when their credits are about to expire
- Consider adding a grace period configuration
- Implementing a feature to extend expiration dates for loyal customers
- Creating a detailed credit usage history for users to track when and how their credits were used 
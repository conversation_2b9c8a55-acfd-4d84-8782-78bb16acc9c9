-- VoiceHype Restore Optimization Cost Check
-- Date: 2024-08-15
BEGIN;

-- Drop the function with the simplified implementation
DROP FUNCTION IF EXISTS public.check_usage_allowance(uuid, text, text, decimal, uuid);

-- Recreate the function with proper input/output token pricing for optimization
CREATE OR R<PERSON>LACE FUNCTION check_usage_allowance(p_user_id UUID, p_service TEXT, p_model TEXT, p_amount DECIMAL(18,9), p_api_key_id UUID)
RETURNS TABLE (
    can_use BOOLEAN,
    pricing_model TEXT,
    cost DECIMAL(18,9),
    max_output_tokens TEXT
) AS $$
DECLARE
    subscription_available BOOLEAN;
    credit_balance DECIMAL(18,9);
    service_cost DECIMAL(18,9);
    cost_per_unit DECIMAL(18,9);
    payg_allowed BOOLEAN;
    input_cost_per_unit DECIMAL(18,9);
    output_cost_per_unit DECIMAL(18,9);
    max_tokens INTEGER;
BEGIN
    -- Get cost per unit first for standard pricing
    SELECT sp.cost_per_unit INTO cost_per_unit
    FROM public.service_pricing sp
    WHERE sp.service = p_service
    AND sp.model = p_model
    AND sp.is_active = TRUE;

    -- Calculate standard service cost with proper decimal precision
    IF cost_per_unit IS NOT NULL THEN
        service_cost := cost_per_unit * p_amount;
    END IF;

    -- Check subscription quota first
    SELECT EXISTS (
        SELECT 1 FROM public.quotas q
        JOIN public.user_subscriptions us ON q.subscription_id = us.id
        WHERE us.user_id = p_user_id
        AND q.service = p_service
        AND (q.used_amount + p_amount) <= q.total_amount
        AND us.status = 'active'
        AND q.reset_date > NOW()
    ) INTO subscription_available;

    IF subscription_available THEN 
        RETURN QUERY SELECT 
            TRUE as can_use,
            'subscription'::TEXT as pricing_model,
            service_cost,
            '4096'::TEXT as max_output_tokens; -- Default max tokens for subscription
        RETURN;
    END IF;

    -- Check credit balance
    SELECT COALESCE(balance, 0) INTO credit_balance
    FROM public.credits
    WHERE user_id = p_user_id;

    IF credit_balance IS NULL THEN
        credit_balance := 0;
    END IF;

    -- Special handling for optimization service (LLM calls)
    IF p_service = 'optimization' THEN
        -- Try to get separate input/output pricing
        SELECT sp.cost_per_unit INTO input_cost_per_unit
        FROM public.service_pricing sp
        WHERE sp.service = 'optimization'
        AND sp.model = p_model || '/input'
        AND sp.is_active = TRUE;

        SELECT sp.cost_per_unit INTO output_cost_per_unit
        FROM public.service_pricing sp
        WHERE sp.service = 'optimization'
        AND sp.model = p_model || '/output'
        AND sp.is_active = TRUE;

        -- If we have separate input/output pricing
        IF input_cost_per_unit IS NOT NULL AND output_cost_per_unit IS NOT NULL THEN
            -- Calculate input tokens cost
            service_cost := input_cost_per_unit * p_amount;
            
            -- If user has any credits at all
            IF credit_balance > 0 THEN
                -- Calculate how many output tokens they can afford
                -- First subtract the cost of input tokens
                IF credit_balance > service_cost THEN
                    -- Remaining credits after input tokens
                    credit_balance := credit_balance - service_cost;
                    
                    -- Calculate max output tokens they can afford
                    max_tokens := FLOOR(credit_balance / output_cost_per_unit);
                    
                    -- Cap at a reasonable maximum (4096 is common for many models)
                    max_tokens := LEAST(max_tokens, 4096);
                    
                    RETURN QUERY SELECT 
                        TRUE as can_use,
                        'credits'::TEXT as pricing_model,
                        service_cost, -- This is just the input cost for now
                        max_tokens::TEXT as max_output_tokens;
                    RETURN;
                END IF;
            END IF;
        ELSE
            -- If we don't have separate pricing, use the combined pricing
            -- If user has any credits at all and they can cover at least the input tokens
            IF credit_balance > 0 AND cost_per_unit IS NOT NULL THEN
                -- Calculate max tokens they can afford (input + output)
                max_tokens := FLOOR(credit_balance / cost_per_unit) - p_amount;
                
                -- If they can afford at least some output tokens
                IF max_tokens > 0 THEN
                    -- Cap at a reasonable maximum
                    max_tokens := LEAST(max_tokens, 4096);
                    
                    RETURN QUERY SELECT 
                        TRUE as can_use,
                        'credits'::TEXT as pricing_model,
                        service_cost,
                        max_tokens::TEXT as max_output_tokens;
                    RETURN;
                END IF;
            END IF;
        END IF;
    ELSE
        -- For non-optimization services (like transcription), use the original logic
        IF credit_balance >= service_cost THEN 
            RETURN QUERY SELECT 
                TRUE as can_use,
                'credits'::TEXT as pricing_model,
                service_cost,
                NULL::TEXT as max_output_tokens;
            RETURN;
        END IF;
    END IF;

    -- Check if PAYG is allowed for this API key
    SELECT COALESCE(allow_payg, FALSE) INTO payg_allowed
    FROM public.api_keys
    WHERE id = p_api_key_id
    AND is_active = TRUE;

    IF payg_allowed IS NULL THEN
        payg_allowed := FALSE;
    END IF;

    -- Fallback to PAYG if allowed and user has stripe customer id
    IF payg_allowed AND EXISTS (
        SELECT 1 FROM public.profiles
        WHERE id = p_user_id
        AND stripe_customer_id IS NOT NULL
    ) THEN
        RETURN QUERY SELECT 
            TRUE::BOOLEAN as can_use,
            'payg'::TEXT as pricing_model,
            service_cost,
            '4096'::TEXT as max_output_tokens; -- Default max tokens for PAYG
        RETURN;
    END IF;

    -- If we get here, no payment method is available
    RETURN QUERY SELECT 
        FALSE::BOOLEAN as can_use,
        NULL::TEXT as pricing_model,
        service_cost,
        NULL::TEXT as max_output_tokens;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Reload PostgREST configuration to apply the changes
NOTIFY pgrst, 'reload config';

COMMIT; 
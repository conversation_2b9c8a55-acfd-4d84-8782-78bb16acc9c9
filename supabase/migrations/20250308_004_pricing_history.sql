-- Create table to track pricing model changes
CREATE TABLE public.pricing_model_changes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    api_key_id UUID NOT NULL REFERENCES public.api_keys(id) ON DELETE CASCADE,
    old_pricing_model_id UUID REFERENCES public.pricing_models(id),
    new_pricing_model_id UUID NOT NULL REFERENCES public.pricing_models(id),
    effective_from TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster lookups
CREATE INDEX idx_pricing_model_changes_api_key ON pricing_model_changes(api_key_id);
CREATE INDEX idx_pricing_model_changes_effective_from ON pricing_model_changes(effective_from);

-- Add trigger to automatically record pricing model changes
CREATE OR REPLACE FUNCTION record_pricing_model_change()
RETURNS TRIGGER AS $$
BEGIN
    IF OLD.pricing_model_id IS DISTINCT FROM NEW.pricing_model_id THEN
        INSERT INTO public.pricing_model_changes (
            api_key_id,
            old_pricing_model_id,
            new_pricing_model_id
        ) VALUES (
            NEW.id,
            OLD.pricing_model_id,
            NEW.pricing_model_id
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER track_pricing_model_changes
    AFTER UPDATE ON public.api_keys
    FOR EACH ROW
    EXECUTE FUNCTION record_pricing_model_change();

-- Function to calculate pay-as-you-go costs for a given period
CREATE OR REPLACE FUNCTION calculate_payg_costs(
    p_user_id UUID,
    p_start_date TIMESTAMP WITH TIME ZONE,
    p_end_date TIMESTAMP WITH TIME ZONE
) RETURNS TABLE (
    total_cost DECIMAL(12, 6),
    service_breakdown JSONB
) AS $$
DECLARE
    v_payg_model_id UUID;
BEGIN
    -- Get pay-as-you-go model ID
    SELECT id INTO v_payg_model_id 
    FROM public.pricing_models 
    WHERE type = 'pay_as_you_go';

    RETURN QUERY
    WITH relevant_api_keys AS (
        -- Get all periods where API keys were using pay-as-you-go
        SELECT 
            ak.id as api_key_id,
            GREATEST(
                pmc.effective_from, 
                p_start_date,
                COALESCE(
                    LAG(pmc.effective_from) OVER (
                        PARTITION BY ak.id 
                        ORDER BY pmc.effective_from
                    ),
                    '-infinity'::timestamp
                )
            ) as period_start,
            LEAST(
                COALESCE(
                    LEAD(pmc.effective_from) OVER (
                        PARTITION BY ak.id 
                        ORDER BY pmc.effective_from
                    ),
                    'infinity'::timestamp
                ),
                p_end_date
            ) as period_end
        FROM public.api_keys ak
        JOIN public.pricing_model_changes pmc ON pmc.api_key_id = ak.id
        WHERE ak.user_id = p_user_id
        AND pmc.new_pricing_model_id = v_payg_model_id
        AND pmc.effective_from <= p_end_date
    )
    SELECT 
        SUM(uh.cost)::DECIMAL(12,6) as total_cost,
        JSONB_OBJECT_AGG(
            uh.service,
            JSONB_BUILD_OBJECT(
                'cost', SUM(uh.cost),
                'usage', SUM(uh.amount),
                'unit', MIN(sp.unit)
            )
        ) as service_breakdown
    FROM relevant_api_keys rak
    JOIN public.usage_history uh ON uh.api_key_id = rak.api_key_id
    JOIN public.service_pricing sp ON 
        sp.service = uh.service 
        AND sp.model = uh.model 
        AND sp.pricing_model_id = v_payg_model_id
    WHERE uh.created_at BETWEEN rak.period_start AND rak.period_end
    AND uh.status = 'success';
END;
$$ LANGUAGE plpgsql;

-- Add RLS policy for pricing model changes
ALTER TABLE public.pricing_model_changes ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own pricing model changes"
    ON public.pricing_model_changes FOR SELECT
    USING (EXISTS (
        SELECT 1 FROM public.api_keys
        WHERE api_keys.id = pricing_model_changes.api_key_id
        AND api_keys.user_id = auth.uid()
    )); 
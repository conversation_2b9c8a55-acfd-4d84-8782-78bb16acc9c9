# Free Trial Subscription for New Users

## Overview

This migration enhances the user onboarding experience by automatically creating a free trial subscription for new users. Previously, new users were given quotas without an associated subscription. With this change, each new user will receive a one-month free trial subscription with predefined usage limits.

## Changes

### Migration File: `20250314_001_add_free_trial_subscription.sql`

This migration:

1. Creates a "Free Trial" subscription plan in the `subscription_plans` table (if it doesn't already exist)
2. Updates the `handle_new_user` function to:
   - Create a subscription for new users in the `user_subscriptions` table
   - Link the user's quotas to this subscription
   - Set an expiration date one month from the creation date

## Implementation Details

### Free Trial Plan

The Free Trial plan includes:
- 10 minutes of transcription
- 5,000 input tokens
- 10,000 output tokens
- Price: $0.00 (free)
- Duration: 1 month

### Subscription Creation

For each new user, the function:
1. Creates a profile in the `profiles` table
2. Retrieves the ID of the "Free Trial" plan
3. Sets the subscription period (current date to one month later)
4. Creates a subscription record in the `user_subscriptions` table with:
   - Status: 'active'
   - A placeholder for `stripe_subscription_id` in the format 'trial_[user_id]'

### Quota Assignment

The function creates quotas for:
- Transcription (10 minutes)
- Input tokens (5,000)
- Output tokens (10,000)

Each quota is linked to the user's subscription and set to reset at the end of the trial period.

## Security

The function maintains the SECURITY DEFINER attribute and permissions are explicitly set to only allow the postgres role to execute it.

## How to Apply the Migration

### Option 1: Using Supabase CLI

If you have the Supabase CLI installed:

```bash
# Navigate to your project directory
cd /path/to/your/project

# Apply the migration
supabase db push
```

### Option 2: Using the Supabase Dashboard

1. Log in to your Supabase dashboard
2. Go to the SQL Editor
3. Copy the contents of the migration file
4. Paste it into a new SQL query
5. Run the query

## Verifying the Migration

After applying the migration:

1. Register a new user
2. Check the `user_subscriptions` table to verify a subscription was created
3. Check the `quotas` table to verify quotas were created and linked to the subscription

## Future Considerations

When implementing Stripe integration:
1. Update the `stripe_subscription_id` with the actual Stripe subscription ID
2. Implement logic to handle subscription expiration and renewal
3. Create a process for upgrading from the free trial to a paid plan

## Troubleshooting

If you encounter any issues after applying this migration:

1. Check the Supabase logs for any error messages
2. Verify that the "Free Trial" plan exists in the `subscription_plans` table
3. Test the `handle_new_user` function by manually inserting a record into the `auth.users` table

If problems persist, please contact the development team for assistance. 
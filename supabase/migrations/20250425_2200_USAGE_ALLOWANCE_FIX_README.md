# Fix for Usage Allowance Function

## Overview

This migration fixes an issue with the `check_usage_allowance` function where users with credits were being denied service if they had unpaid pay-as-you-go balances from previous months.

## Problem

The original function was checking for unpaid balances before checking if the user had enough credits. This meant that even if a user had sufficient credits to cover the cost of a service, they would be denied access if they had any unpaid pay-as-you-go balances from previous months.

## Solution

The fix moves the check for unpaid balances to after all credit-based checks. This ensures that:

1. If a user has an active subscription, they can use the service regardless of unpaid balances.
2. If a user has enough credits, they can use the service regardless of unpaid balances.
3. Only when falling back to pay-as-you-go billing do we check for unpaid balances.

This change prioritizes credits over pay-as-you-go, allowing users with credits to use the service even if they have outstanding pay-as-you-go balances.

## Implementation

The implementation simply moves the check for unpaid balances and the corresponding error return to after all credit-based checks, just before the pay-as-you-go fallback logic.

## Testing

To test this change:

1. Create a user with credits and unpaid pay-as-you-go balances from previous months.
2. Attempt to use a service that costs less than the available credits.
3. Verify that the service is allowed and credits are deducted correctly.
4. Deplete the credits and attempt to use the service again.
5. Verify that the service is denied due to unpaid balances when attempting to use pay-as-you-go.

-- Secure Paddle webhook processing - only process transactions for existing users
-- This prevents fraudulent attempts to create fake users through webhook exploitation

CREATE OR REPLACE FUNCTION public.process_completed_transaction(
    p_paddle_transaction_id TEXT,
    p_paddle_customer_id TEXT,
    p_amount DECIMAL(18,2),
    p_currency TEXT,
    p_product_id TEXT DEFAULT NULL,
    p_receipt_url TEXT DEFAULT NULL,
    p_metadata JSONB DEFAULT '{}'::jsonb
) RETURNS BOOLEAN
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
DECLARE
    v_user_id UUID;
    v_customer_id UUID;
    v_product_id UUID;
    v_credit_amount DECIMAL(18,9);
    v_transaction_id UUID;
    v_existing_transaction UUID;
    v_email TEXT;
    v_existing_user_id UUID;
BEGIN
    -- Check if transaction already exists
    SELECT id INTO v_existing_transaction
    FROM paddle.transactions
    WHERE paddle_transaction_id = p_paddle_transaction_id;

    IF v_existing_transaction IS NOT NULL THEN
        -- Transaction already processed
        RETURN TRUE;
    END IF;

    -- Get customer and user info from paddle.customers table
    SELECT user_id, id INTO v_user_id, v_customer_id
    FROM paddle.customers
    WHERE paddle_customer_id = p_paddle_customer_id;

    -- If we don't have a user_id, try to find the user by email in the metadata
    IF v_user_id IS NULL THEN
        -- Extract customer email from metadata
        v_email := p_metadata->>'email';
        
        IF v_email IS NULL THEN
            -- Try to find email in nested customer structure
            v_email := p_metadata->'customer'->>'email';
        END IF;
        
        IF v_email IS NULL THEN
            -- Try billing address email
            v_email := p_metadata->'billing_details'->>'email';
        END IF;

        IF v_email IS NOT NULL THEN
            -- Look for existing user by email in profiles table (more secure than auth.users)
            SELECT id INTO v_existing_user_id
            FROM public.profiles
            WHERE email = v_email;

            IF v_existing_user_id IS NOT NULL THEN
                v_user_id := v_existing_user_id;
                
                -- Create or update the paddle customer record for this existing user
                INSERT INTO paddle.customers (
                    user_id,
                    paddle_customer_id,
                    email,
                    name,
                    created_at,
                    metadata
                ) VALUES (
                    v_user_id,
                    p_paddle_customer_id,
                    v_email,
                    COALESCE(
                        p_metadata->>'name',
                        p_metadata->'customer'->>'name',
                        'VoiceHype User'
                    ),
                    NOW(),
                    p_metadata
                ) ON CONFLICT (paddle_customer_id) 
                DO UPDATE SET
                    user_id = v_user_id,
                    email = v_email,
                    metadata = p_metadata,
                    updated_at = NOW();
                
                RAISE NOTICE 'Linked existing user % with Paddle customer %', v_user_id, p_paddle_customer_id;
            END IF;
        END IF;
    END IF;

    -- Security check: Only process transactions for existing users
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'Transaction rejected: No existing VoiceHype user found for this purchase. Customer ID: %, Email: %', 
            p_paddle_customer_id, COALESCE(v_email, 'not provided');
    END IF;

    -- Verify the user exists in our profiles table (additional security check)
    IF NOT EXISTS (SELECT 1 FROM public.profiles WHERE id = v_user_id) THEN
        RAISE EXCEPTION 'Transaction rejected: User profile not found for user ID: %', v_user_id;
    END IF;

    -- Get product info if product_id provided (for reference only)
    IF p_product_id IS NOT NULL THEN
        SELECT id INTO v_product_id
        FROM paddle.products
        WHERE paddle_product_id = p_product_id AND is_active = TRUE;
    END IF;

    -- Convert amount from cents to dollars (credit units)
    -- For VoiceHype, $1 = 1 credit (flexible credit purchasing)
    v_credit_amount := p_amount / 100.0;

    -- Create transaction record
    INSERT INTO paddle.transactions (
        user_id,
        paddle_transaction_id,
        paddle_customer_id,
        product_id,
        status,
        amount,
        currency,
        credit_amount,
        transaction_type,
        paddle_receipt_url,
        metadata,
        created_at,
        updated_at
    ) VALUES (
        v_user_id,
        p_paddle_transaction_id,
        p_paddle_customer_id,
        v_product_id,
        'completed',
        p_amount,
        p_currency,
        v_credit_amount,
        'credit_purchase',
        p_receipt_url,
        p_metadata,
        NOW(),
        NOW()
    ) RETURNING id INTO v_transaction_id;

    -- Add credits to user's account
    INSERT INTO public.credits (user_id, balance, currency, created_at, updated_at)
    VALUES (v_user_id, v_credit_amount, p_currency, NOW(), NOW())
    ON CONFLICT (user_id)
    DO UPDATE SET
        balance = credits.balance + v_credit_amount,
        updated_at = NOW();

    RAISE NOTICE 'Successfully processed transaction % for user %, added % credits', 
        p_paddle_transaction_id, v_user_id, v_credit_amount;

    RETURN TRUE;
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.process_completed_transaction TO service_role;

-- Add comment explaining the security changes
COMMENT ON FUNCTION public.process_completed_transaction IS 'Securely processes completed Paddle transactions for existing VoiceHype users only. Rejects transactions from non-existent users to prevent fraud and unauthorized account creation.';

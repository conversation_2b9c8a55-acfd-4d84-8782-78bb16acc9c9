-- Migration: Remove paddle schema and migrate to public schema with RLS
-- This migration removes the entire paddle schema and creates equivalent tables in public schema

-- ========================================
-- 1. BACKUP AND DROP PADDLE SCHEMA
-- ========================================

-- Drop all functions in paddle schema first
DROP FUNCTION IF EXISTS paddle.update_updated_at_column() CASCADE;
DROP FUNCTION IF EXISTS paddle.create_or_get_customer(TEXT, TEXT, TEXT, TEXT, JSONB) CASCADE;
DROP FUNCTION IF EXISTS paddle.process_completed_transaction(TEXT, TEXT, DECIMAL, TEXT, TEXT, TEXT, JSONB) CASCADE;
DROP FUNCTION IF EXISTS paddle.create_payg_invoice(UUID, DECIMAL, TEXT, JSONB) CASCADE;
DROP FUNCTION IF EXISTS paddle.process_payg_payment(TEXT, TEXT, DECIMAL, TEXT, JSONB) CASCADE;
DROP FUNCTION IF EXISTS paddle.get_unpaid_payg_balances() CASCADE;

-- Drop public functions that reference paddle schema
DROP FUNCTION IF EXISTS public.query_paddle_products(TEXT, BOOLEAN) CASCADE;
DROP FUNCTION IF EXISTS public.query_paddle_customers(UUID) CASCADE;
DROP FUNCTION IF EXISTS public.query_paddle_transactions(UUID, TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.query_paddle_webhooks(TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.create_paddle_webhook_log(TEXT, TEXT, TIMESTAMPTZ, JSONB, TEXT, TEXT, TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.update_paddle_webhook_status(TEXT, TEXT, TEXT, TIMESTAMPTZ) CASCADE;
DROP FUNCTION IF EXISTS public.update_paddle_transaction(TEXT, TEXT, DECIMAL, TEXT, TEXT, TIMESTAMPTZ, JSONB) CASCADE;
DROP FUNCTION IF EXISTS public.store_paddle_customer_info(TEXT, TEXT, TEXT, TEXT, JSONB) CASCADE;

-- Drop any existing process_completed_transaction functions with various signatures
DROP FUNCTION IF EXISTS public.process_completed_transaction(TEXT, TEXT, DECIMAL, TEXT, TEXT, TEXT, JSONB) CASCADE;
DROP FUNCTION IF EXISTS public.process_completed_transaction(TEXT, TEXT, NUMERIC, TEXT, TEXT, TEXT, JSONB) CASCADE;

-- Drop any existing functions we're about to recreate
DROP FUNCTION IF EXISTS public.get_paddle_customer_by_user_id(UUID) CASCADE;
DROP FUNCTION IF EXISTS public.upsert_paddle_customer(UUID, TEXT, TEXT, TEXT, TEXT, JSONB) CASCADE;
DROP FUNCTION IF EXISTS public.log_paddle_webhook(TEXT, TEXT, TIMESTAMPTZ, JSONB) CASCADE;
DROP FUNCTION IF EXISTS public.update_paddle_webhook_status(TEXT, TEXT, TEXT) CASCADE;

-- Drop entire paddle schema with all its tables
DROP SCHEMA IF EXISTS paddle CASCADE;

-- ========================================
-- 2. CREATE NEW PUBLIC TABLES WITH RLS
-- ========================================

-- Create paddle_customers table in public schema
CREATE TABLE IF NOT EXISTS public.paddle_customers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    paddle_customer_id TEXT UNIQUE NOT NULL,
    email TEXT NOT NULL,
    name TEXT,
    country_code TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'::jsonb,
    
    CONSTRAINT paddle_customers_user_id_unique UNIQUE (user_id)
);

-- Create paddle_transactions table in public schema
CREATE TABLE IF NOT EXISTS public.paddle_transactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    paddle_transaction_id TEXT UNIQUE NOT NULL,
    paddle_customer_id TEXT NOT NULL,
    paddle_product_id TEXT,
    status TEXT NOT NULL CHECK (status IN ('pending', 'completed', 'failed', 'cancelled', 'refunded')),
    amount DECIMAL(18,2) NOT NULL,
    currency TEXT DEFAULT 'USD',
    credit_amount DECIMAL(18,9), -- Credits purchased (for credit packages)
    transaction_type TEXT NOT NULL CHECK (transaction_type IN ('credit_purchase', 'subscription', 'refund')),
    paddle_receipt_url TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Create paddle_webhooks table in public schema
CREATE TABLE IF NOT EXISTS public.paddle_webhooks (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    event_id TEXT UNIQUE NOT NULL,
    event_type TEXT NOT NULL,
    occurred_at TIMESTAMPTZ NOT NULL,
    processed_at TIMESTAMPTZ DEFAULT NOW(),
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processed', 'failed', 'skipped')),
    attempts INTEGER DEFAULT 0,
    data JSONB NOT NULL,
    error_message TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create billing_portal_sessions table for tracking portal access
CREATE TABLE IF NOT EXISTS public.billing_portal_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    paddle_customer_id TEXT NOT NULL,
    portal_session_id TEXT NOT NULL,
    portal_url TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    accessed_at TIMESTAMPTZ
);

-- ========================================
-- 3. CREATE INDEXES FOR PERFORMANCE
-- ========================================

-- Paddle customers indexes
CREATE INDEX IF NOT EXISTS idx_paddle_customers_user_id ON public.paddle_customers(user_id);
CREATE INDEX IF NOT EXISTS idx_paddle_customers_paddle_customer_id ON public.paddle_customers(paddle_customer_id);
CREATE INDEX IF NOT EXISTS idx_paddle_customers_email ON public.paddle_customers(email);

-- Paddle transactions indexes
CREATE INDEX IF NOT EXISTS idx_paddle_transactions_user_id ON public.paddle_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_paddle_transactions_paddle_customer_id ON public.paddle_transactions(paddle_customer_id);
CREATE INDEX IF NOT EXISTS idx_paddle_transactions_status ON public.paddle_transactions(status);
CREATE INDEX IF NOT EXISTS idx_paddle_transactions_created_at ON public.paddle_transactions(created_at);
CREATE INDEX IF NOT EXISTS idx_paddle_transactions_type ON public.paddle_transactions(transaction_type);

-- Paddle webhooks indexes
CREATE INDEX IF NOT EXISTS idx_paddle_webhooks_event_id ON public.paddle_webhooks(event_id);
CREATE INDEX IF NOT EXISTS idx_paddle_webhooks_event_type ON public.paddle_webhooks(event_type);
CREATE INDEX IF NOT EXISTS idx_paddle_webhooks_status ON public.paddle_webhooks(status);
CREATE INDEX IF NOT EXISTS idx_paddle_webhooks_occurred_at ON public.paddle_webhooks(occurred_at);

-- Billing portal sessions indexes
CREATE INDEX IF NOT EXISTS idx_billing_portal_sessions_user_id ON public.billing_portal_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_billing_portal_sessions_customer_id ON public.billing_portal_sessions(paddle_customer_id);

-- ========================================
-- 4. ENABLE ROW LEVEL SECURITY (RLS)
-- ========================================

-- Enable RLS on all paddle tables
ALTER TABLE public.paddle_customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.paddle_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.paddle_webhooks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.billing_portal_sessions ENABLE ROW LEVEL SECURITY;

-- ========================================
-- 5. CREATE RLS POLICIES (NO ACCESS FOR USERS)
-- ========================================

-- Paddle customers: NO policies = NO access for regular users
-- Only service role and authenticated backend can access

-- Paddle transactions: NO policies = NO access for regular users
-- Only service role and authenticated backend can access

-- Paddle webhooks: NO policies = NO access for regular users
-- Only service role and authenticated backend can access

-- Billing portal sessions: NO policies = NO access for regular users
-- Only service role and authenticated backend can access

-- ========================================
-- 6. CREATE UTILITY FUNCTIONS
-- ========================================

-- Function to get customer by user_id (used by edge functions)
CREATE OR REPLACE FUNCTION public.get_paddle_customer_by_user_id(p_user_id UUID)
RETURNS TABLE (
    paddle_customer_id TEXT,
    email TEXT,
    name TEXT,
    metadata JSONB
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pc.paddle_customer_id,
        pc.email,
        pc.name,
        pc.metadata
    FROM public.paddle_customers pc
    WHERE pc.user_id = p_user_id;
END;
$$;

-- Function to create or update paddle customer
CREATE OR REPLACE FUNCTION public.upsert_paddle_customer(
    p_user_id UUID,
    p_paddle_customer_id TEXT,
    p_email TEXT,
    p_name TEXT DEFAULT NULL,
    p_country_code TEXT DEFAULT NULL,
    p_metadata JSONB DEFAULT '{}'::jsonb
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_customer_id UUID;
BEGIN
    INSERT INTO public.paddle_customers (
        user_id,
        paddle_customer_id,
        email,
        name,
        country_code,
        metadata
    ) VALUES (
        p_user_id,
        p_paddle_customer_id,
        p_email,
        p_name,
        p_country_code,
        p_metadata
    )
    ON CONFLICT (paddle_customer_id) 
    DO UPDATE SET
        email = COALESCE(EXCLUDED.email, paddle_customers.email),
        name = COALESCE(EXCLUDED.name, paddle_customers.name),
        country_code = COALESCE(EXCLUDED.country_code, paddle_customers.country_code),
        metadata = EXCLUDED.metadata,
        updated_at = NOW()
    RETURNING id INTO v_customer_id;
    
    RETURN v_customer_id;
END;
$$;

-- Function to log webhook events
CREATE OR REPLACE FUNCTION public.log_paddle_webhook(
    p_event_id TEXT,
    p_event_type TEXT,
    p_occurred_at TIMESTAMPTZ,
    p_data JSONB
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_webhook_id UUID;
BEGIN
    INSERT INTO public.paddle_webhooks (
        event_id,
        event_type,
        occurred_at,
        data
    ) VALUES (
        p_event_id,
        p_event_type,
        p_occurred_at,
        p_data
    )
    ON CONFLICT (event_id) 
    DO UPDATE SET
        attempts = paddle_webhooks.attempts + 1,
        status = 'pending'
    RETURNING id INTO v_webhook_id;
    
    RETURN v_webhook_id;
END;
$$;

-- Function to update webhook status
CREATE OR REPLACE FUNCTION public.update_paddle_webhook_status(
    p_event_id TEXT,
    p_status TEXT,
    p_error_message TEXT DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    UPDATE public.paddle_webhooks
    SET 
        status = p_status,
        error_message = p_error_message,
        processed_at = CASE WHEN p_status = 'processed' THEN NOW() ELSE processed_at END
    WHERE event_id = p_event_id;
    
    RETURN FOUND;
END;
$$;

-- Function to process completed transactions
CREATE OR REPLACE FUNCTION public.process_completed_transaction(
    p_paddle_transaction_id TEXT,
    p_paddle_customer_id TEXT,
    p_amount DECIMAL,
    p_currency TEXT,
    p_product_id TEXT DEFAULT NULL,
    p_receipt_url TEXT DEFAULT NULL,
    p_metadata JSONB DEFAULT '{}'::jsonb
)
RETURNS TABLE (
    success BOOLEAN,
    user_id UUID,
    transaction_id UUID,
    credit_amount DECIMAL
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_user_id UUID;
    v_transaction_id UUID;
    v_credit_amount DECIMAL(18,9);
    v_email TEXT;
BEGIN
    -- First, try to get user_id from custom_data (most reliable)
    v_user_id := (p_metadata->>'user_id')::UUID;
    
    -- If user_id is in custom_data, use it directly
    IF v_user_id IS NOT NULL THEN
        -- Verify user exists
        IF EXISTS (SELECT 1 FROM public.profiles WHERE id = v_user_id) THEN
            -- Get user email for customer record
            SELECT email INTO v_email FROM public.profiles WHERE id = v_user_id;
            
            -- Create/update customer record
            PERFORM public.upsert_paddle_customer(
                v_user_id,
                p_paddle_customer_id,
                v_email,
                p_metadata->>'customer_name',
                p_metadata->>'customer_country',
                p_metadata
            );
        ELSE
            -- User ID in custom_data doesn't exist
            RAISE WARNING 'User ID % from custom_data not found in profiles', v_user_id;
            v_user_id := NULL;
        END IF;
    END IF;
    
    -- Fallback: Look up by existing customer record
    IF v_user_id IS NULL THEN
        SELECT pc.user_id, pc.email INTO v_user_id, v_email
        FROM public.paddle_customers pc
        WHERE pc.paddle_customer_id = p_paddle_customer_id;
    END IF;
    
    -- Fallback: Try to find user by email from metadata
    IF v_user_id IS NULL THEN
        v_email := p_metadata->>'customer_email';
        
        IF v_email IS NOT NULL THEN
            -- Try to find user by email
            SELECT pr.id INTO v_user_id
            FROM public.profiles pr
            WHERE pr.email = v_email;
            
            -- If user found, create customer record
            IF v_user_id IS NOT NULL THEN
                PERFORM public.upsert_paddle_customer(
                    v_user_id,
                    p_paddle_customer_id,
                    v_email,
                    p_metadata->>'customer_name',
                    p_metadata->>'customer_country',
                    p_metadata
                );
            END IF;
        END IF;
    END IF;

    -- If still no user, return failure
    IF v_user_id IS NULL THEN
        RAISE WARNING 'No user found for transaction %, customer %, metadata: %', 
            p_paddle_transaction_id, p_paddle_customer_id, p_metadata;
        RETURN QUERY SELECT FALSE, NULL::UUID, NULL::UUID, NULL::DECIMAL;
        RETURN;
    END IF;

    -- Calculate credit amount (assume $1 = 1 credit for now)
    v_credit_amount := p_amount;

    -- Create transaction record
    INSERT INTO public.paddle_transactions (
        user_id,
        paddle_transaction_id,
        paddle_customer_id,
        paddle_product_id,
        status,
        amount,
        currency,
        credit_amount,
        transaction_type,
        paddle_receipt_url,
        metadata
    ) VALUES (
        v_user_id,
        p_paddle_transaction_id,
        p_paddle_customer_id,
        p_product_id,
        'completed',
        p_amount,
        p_currency,
        v_credit_amount,
        'credit_purchase',
        p_receipt_url,
        p_metadata
    )
    ON CONFLICT (paddle_transaction_id) DO UPDATE SET
        status = 'completed',
        updated_at = NOW()
    RETURNING id INTO v_transaction_id;

    -- Add credits to user account
    INSERT INTO public.credits (
        user_id,
        amount,
        source,
        transaction_reference,
        description,
        expires_at
    ) VALUES (
        v_user_id,
        v_credit_amount,
        'purchase',
        p_paddle_transaction_id,
        'Credit purchase via Paddle',
        NOW() + INTERVAL '1 year'
    );

    RAISE NOTICE 'Successfully processed transaction % for user % (%.2f credits)', 
        p_paddle_transaction_id, v_user_id, v_credit_amount;

    RETURN QUERY SELECT TRUE, v_user_id, v_transaction_id, v_credit_amount;
END;
$$;

-- ========================================
-- 7. ADD COMMENTS FOR DOCUMENTATION
-- ========================================

COMMENT ON TABLE public.paddle_customers IS 'Paddle customer information linked to VoiceHype users';
COMMENT ON TABLE public.paddle_transactions IS 'Paddle transaction records for credit purchases and subscriptions';
COMMENT ON TABLE public.paddle_webhooks IS 'Paddle webhook event logs for audit and retry purposes';
COMMENT ON TABLE public.billing_portal_sessions IS 'Tracking table for Paddle customer portal access';

COMMENT ON FUNCTION public.get_paddle_customer_by_user_id(UUID) IS 'Get Paddle customer info for a user (used by edge functions)';
COMMENT ON FUNCTION public.upsert_paddle_customer(UUID, TEXT, TEXT, TEXT, TEXT, JSONB) IS 'Create or update a Paddle customer record';
COMMENT ON FUNCTION public.log_paddle_webhook(TEXT, TEXT, TIMESTAMPTZ, JSONB) IS 'Log incoming Paddle webhook events';
COMMENT ON FUNCTION public.update_paddle_webhook_status(TEXT, TEXT, TEXT) IS 'Update webhook processing status';
COMMENT ON FUNCTION public.process_completed_transaction(TEXT, TEXT, DECIMAL, TEXT, TEXT, TEXT, JSONB) IS 'Process completed Paddle transactions and add credits';

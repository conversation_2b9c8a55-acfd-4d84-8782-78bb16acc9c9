-- Limit API key expiration to a maximum of 1 year
BEGIN;

-- Drop the existing function
DROP FUNCTION IF EXISTS public.create_api_key;

-- Recreate the function with the expiration date limit
CREATE OR REPLACE FUNCTION public.create_api_key(
    p_name TEXT,
    p_expires_at TIMESTAMPTZ DEFAULT NULL
) RETURNS public.api_key_result AS $$
DECLARE
    v_user_id UUID;
    v_key_secret TEXT;
    v_key_prefix TEXT;
    v_key_hash TEXT;
    v_api_key_id UUID;
    v_key_count INTEGER;
    v_created_at TIMESTAMPTZ;
    v_final_expires_at TIMESTAMPTZ;
    v_max_expires_at TIMESTAMPTZ;
    v_result public.api_key_result;
BEGIN
    -- Get the current user's ID
    v_user_id := auth.uid();
    
    -- Check if user is authenticated
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'Not authenticated';
    END IF;
    
    -- Check API key limit (belt-and-suspenders approach with the trigger)
    SELECT COUNT(*) INTO v_key_count FROM public.api_keys WHERE user_id = v_user_id;
    IF v_key_count >= 25 THEN
        RAISE EXCEPTION 'Maximum number of API keys (25) reached for this user';
    END IF;
    
    -- Generate a secure API key
    v_key_secret := encode(gen_random_bytes(24), 'base64');
    v_key_prefix := substring(v_key_secret from 1 for 8);
    v_key_hash := crypt(v_key_secret, gen_salt('bf'));
    
    -- Set the creation time
    v_created_at := NOW();
    
    -- Calculate maximum expiration date (1 year from creation)
    v_max_expires_at := v_created_at + INTERVAL '1 year';
    
    -- Set expiration date to one year from creation if not provided
    IF p_expires_at IS NULL THEN
        v_final_expires_at := v_max_expires_at;
    ELSE
        -- Validate user-provided expiration date
        IF p_expires_at <= v_created_at THEN
            RAISE EXCEPTION 'Expiration date must be in the future';
        END IF;
        
        -- Limit expiration date to a maximum of 1 year from creation
        v_final_expires_at := LEAST(p_expires_at, v_max_expires_at);
    END IF;
    
    -- Insert the new API key with controlled fields
    INSERT INTO public.api_keys (
        user_id,
        name,
        key_prefix,
        key_hash,
        created_at,
        expires_at
    ) VALUES (
        v_user_id,
        p_name,
        v_key_prefix,
        v_key_hash,
        v_created_at,  -- Server-controlled timestamp
        v_final_expires_at  -- Limited to a maximum of 1 year from creation
    ) RETURNING id INTO v_api_key_id;
    
    -- Return both the ID and the key secret
    v_result.id := v_api_key_id;
    v_result.key_secret := v_key_secret;
    
    RETURN v_result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the comment
COMMENT ON FUNCTION public.create_api_key IS 'Creates a new API key for the authenticated user with a maximum expiration of 1 year and returns both the ID and the key secret';

-- Revoke all permissions first
REVOKE ALL ON FUNCTION public.create_api_key FROM PUBLIC;
REVOKE ALL ON FUNCTION public.create_api_key FROM anon;
REVOKE ALL ON FUNCTION public.create_api_key FROM authenticated;
REVOKE ALL ON FUNCTION public.create_api_key FROM service_role;

-- Grant permission only to authenticated users
GRANT EXECUTE ON FUNCTION public.create_api_key TO authenticated;

COMMIT; 
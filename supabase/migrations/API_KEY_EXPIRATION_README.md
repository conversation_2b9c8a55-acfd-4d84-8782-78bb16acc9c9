# API Key Expiration Limit

## Overview

This migration adds a limit to the maximum expiration date for API keys. Previously, users could set any future date as the expiration date for their API keys. With this change, API keys can expire at most 1 year from their creation date, regardless of the user-provided expiration date.

## Changes

### Migration File: `20250313_001_limit_api_key_expiration.sql`

This migration:

1. Updates the `create_api_key` function to limit the expiration date to a maximum of 1 year from creation
2. Uses the `LEAST` function to compare the user-provided expiration date with the maximum allowed date
3. Updates the permissions to only allow authenticated users to execute the function

## Implementation Details

The key changes in the function are:

```sql
-- Calculate maximum expiration date (1 year from creation)
v_max_expires_at := v_created_at + INTERVAL '1 year';

-- Set expiration date to one year from creation if not provided
IF p_expires_at IS NULL THEN
    v_final_expires_at := v_max_expires_at;
ELSE
    -- Validate user-provided expiration date
    IF p_expires_at <= v_created_at THEN
        RAISE EXCEPTION 'Expiration date must be in the future';
    END IF;
    
    -- Limit expiration date to a maximum of 1 year from creation
    v_final_expires_at := LEAST(p_expires_at, v_max_expires_at);
END IF;
```

## Permission Changes

The migration also updates the permissions for the function:

1. Revokes all permissions from all roles
2. Grants execute permission only to the authenticated role

```sql
-- Revoke all permissions first
REVOKE ALL ON FUNCTION public.create_api_key FROM PUBLIC;
REVOKE ALL ON FUNCTION public.create_api_key FROM anon;
REVOKE ALL ON FUNCTION public.create_api_key FROM authenticated;
REVOKE ALL ON FUNCTION public.create_api_key FROM service_role;

-- Grant permission only to authenticated users
GRANT EXECUTE ON FUNCTION public.create_api_key TO authenticated;
```

## How to Apply the Migration

### Option 1: Using Supabase CLI

If you have the Supabase CLI installed:

```bash
# Navigate to your project directory
cd /path/to/your/project

# Apply the migration
supabase db push
```

### Option 2: Using the Supabase Dashboard

1. Log in to your Supabase dashboard
2. Go to the SQL Editor
3. Copy the contents of the migration file
4. Paste it into a new SQL query
5. Run the query

## Verifying the Fix

After applying the migration, try creating a new API key with an expiration date more than 1 year in the future. The system should automatically adjust the expiration date to be exactly 1 year from the creation date.

## Troubleshooting

If you encounter any issues after applying this migration:

1. Check the Supabase logs for any error messages
2. Verify that the migration was applied successfully by checking if the `create_api_key` function has been updated
3. Test the function directly in the SQL Editor

If problems persist, please contact the development team for assistance. 
-- Update or insert service pricing entries for real-time GPT-4o models with explicit names
-- This ensures pricing entries exist with the exact names the edge function expects

-- First, delete any existing entries for these models to avoid duplicates
DELETE FROM public.service_pricing 
WHERE service = 'transcription' 
AND model IN (
    'gpt-4o-mini-transcribe-realtime', 
    'gpt-4o-transcribe-realtime'
);

-- Insert the pricing entries with correct units
INSERT INTO public.service_pricing (service, model, cost_per_unit, unit, is_active, profit_margin)
VALUES 
    -- GPT-4o mini real-time with minute billing (not token-based for real-time)
    ('transcription', 'gpt-4o-mini-transcribe-realtime', 0.05, 'minute', true, 10),
    -- GPT-4o real-time with minute billing (not token-based for real-time)
    ('transcription', 'gpt-4o-transcribe-realtime', 0.10, 'minute', true, 10);

-- Log entry to verify the migration ran
INSERT INTO public.migration_logs (migration_name, details)
VALUES ('20250403_002_fix_realtime_pricing', jsonb_build_object(
    'action', 'Created or updated real-time pricing entries',
    'models', jsonb_build_array('gpt-4o-mini-transcribe-realtime', 'gpt-4o-transcribe-realtime'),
    'pricing_type', 'minute-based'
));

-- Update function to check service pricing validity in check_usage_allowance
CREATE OR REPLACE FUNCTION log_usage_allowance_check() RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.debug_logs (
        event_type,
        details
    ) VALUES (
        'usage_allowance_check',
        jsonb_build_object(
            'user_id', NEW.user_id,
            'service', NEW.service,
            'model', NEW.model,
            'amount', NEW.amount,
            'can_use', NEW.can_use,
            'pricing_model', NEW.pricing_model,
            'error_code', NEW.error_code,
            'subscription_id', NEW.subscription_id,
            'timestamp', CURRENT_TIMESTAMP
        )
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create table for debug logs if it doesn't exist
CREATE TABLE IF NOT EXISTS public.debug_logs (
    id SERIAL PRIMARY KEY,
    event_type TEXT NOT NULL,
    details JSONB NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create table for migration logs if it doesn't exist
CREATE TABLE IF NOT EXISTS public.migration_logs (
    id SERIAL PRIMARY KEY,
    migration_name TEXT NOT NULL,
    details JSONB NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Drop the trigger if it exists
DROP TRIGGER IF EXISTS log_usage_allowance_check_trigger ON public.usage_allowance_checks;

-- Create the trigger
CREATE TRIGGER log_usage_allowance_check_trigger
AFTER INSERT ON public.usage_allowance_checks
FOR EACH ROW
EXECUTE FUNCTION log_usage_allowance_check(); 
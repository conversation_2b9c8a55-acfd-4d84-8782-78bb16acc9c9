-- Drop the logging trigger
DROP TRIGGER IF EXISTS log_usage_allowance_check_trigger ON public.usage_allowance_checks;

-- Drop the logging function
DROP FUNCTION IF EXISTS log_usage_allowance_check();

-- We don't remove the debug logs or migration logs tables to preserve history
-- But we could drop them if needed:
-- DROP TABLE IF EXISTS public.debug_logs;
-- DROP TABLE IF EXISTS public.migration_logs;

-- Insert a log entry showing the migration was reverted
INSERT INTO public.migration_logs (migration_name, details)
VALUES ('20250403_002_fix_realtime_pricing_revert', jsonb_build_object(
    'action', 'Reverted real-time pricing fixes',
    'timestamp', NOW()
)); 
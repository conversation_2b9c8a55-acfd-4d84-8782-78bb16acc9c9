-- Database function to handle subscription cleanup when cancelled
CREATE OR REPLACE FUNCTION public.cleanup_cancelled_subscription(
  p_paddle_subscription_id TEXT
) RETURNS JSON AS $$
DECLARE
  v_subscription_record RECORD;
  v_result JSON;
BEGIN
  -- Get subscription details
  SELECT id, user_id 
  INTO v_subscription_record
  FROM public.user_subscriptions 
  WHERE paddle_subscription_id = p_paddle_subscription_id;

  -- If subscription not found, return success (already cleaned up)
  IF v_subscription_record.id IS NULL THEN
    RETURN JSON_BUILD_OBJECT(
      'success', true,
      'message', 'Subscription not found or already cleaned up',
      'paddle_subscription_id', p_paddle_subscription_id
    );
  END IF;

  -- Delete quotas for this subscription
  DELETE FROM public.quotas 
  WHERE subscription_id = v_subscription_record.id;

  -- Delete the user subscription
  DELETE FROM public.user_subscriptions 
  WHERE id = v_subscription_record.id;

  -- Return success
  RETURN JSON_BUILD_OBJECT(
    'success', true,
    'message', 'Subscription cleaned up successfully',
    'user_id', v_subscription_record.user_id,
    'subscription_id', v_subscription_record.id,
    'paddle_subscription_id', p_paddle_subscription_id
  );

EXCEPTION WHEN OTHERS THEN
  -- Return error details
  RETURN JSON_BUILD_OBJECT(
    'success', false,
    'error', SQLERRM,
    'paddle_subscription_id', p_paddle_subscription_id
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

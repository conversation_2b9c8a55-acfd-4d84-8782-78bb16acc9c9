-- Add payment status tracking to PAYG usage
BEGIN;

-- Add payment status to payg_usage table
ALTER TABLE public.payg_usage 
ADD COLUMN is_paid BOOLEAN DEFAULT FALSE;

-- Create a function to check if user has unpaid PAYG balances
CREATE OR REPLACE FUNCTION has_unpaid_payg_balance(p_user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    has_unpaid BOOLEAN;
BEGIN
    -- Check if there are any unpaid PAYG balances from previous months
    SELECT EXISTS (
        SELECT 1 
        FROM public.payg_usage 
        WHERE user_id = p_user_id 
        AND is_billed = TRUE 
        AND is_paid = FALSE 
        AND month < date_trunc('month', CURRENT_DATE)
    ) INTO has_unpaid;
    
    RETURN has_unpaid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to get unpaid PAYG balances for a user
CREATE OR REPLACE FUNCTION get_unpaid_payg_balances(p_user_id UUID)
RETURNS TABLE (
    id UUID,
    month DATE,
    total_amount DECIMAL(18,9),
    is_billed BOOLEAN,
    is_paid BOOLEAN,
    created_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pu.id,
        pu.month,
        pu.total_amount,
        pu.is_billed,
        pu.is_paid,
        pu.created_at
    FROM 
        public.payg_usage pu
    WHERE 
        pu.user_id = p_user_id
        AND pu.is_billed = TRUE
        AND pu.is_paid = FALSE
        AND pu.month < date_trunc('month', CURRENT_DATE)
    ORDER BY 
        pu.month DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop the existing function first
DROP FUNCTION IF EXISTS check_usage_allowance(UUID, TEXT, TEXT, DECIMAL, UUID);

-- Update check_usage_allowance function to check for unpaid balances
CREATE OR REPLACE FUNCTION check_usage_allowance(p_user_id UUID, p_service TEXT, p_model TEXT, p_amount DECIMAL(18,9), p_api_key_id UUID)
RETURNS TABLE (
    can_use BOOLEAN,
    pricing_model TEXT,
    cost DECIMAL(18,9),
    error_code TEXT
) AS $$
DECLARE
    subscription_available BOOLEAN;
    credit_balance DECIMAL(18,9);
    service_cost DECIMAL(18,9);
    cost_per_unit DECIMAL(18,9);
    payg_allowed BOOLEAN;
    has_unpaid_balance BOOLEAN;
BEGIN
    -- Get cost per unit first
    SELECT sp.cost_per_unit INTO cost_per_unit
    FROM public.service_pricing sp
    WHERE sp.service = p_service
    AND sp.model = p_model
    AND sp.is_active = TRUE;

    -- Calculate service cost with proper decimal precision
    service_cost := cost_per_unit * p_amount;

    -- Check subscription quota first
    SELECT EXISTS (
        SELECT 1 FROM public.quotas q
        JOIN public.user_subscriptions us ON q.subscription_id = us.id
        WHERE us.user_id = p_user_id
        AND q.service = p_service
        AND (q.used_amount + p_amount) <= q.total_amount
        AND us.status = 'active'
        AND q.reset_date > NOW()
    ) INTO subscription_available;

    IF subscription_available THEN 
        RETURN QUERY SELECT 
            TRUE as can_use,
            'subscription'::TEXT as pricing_model,
            service_cost,
            NULL::TEXT as error_code;
        RETURN;
    END IF;

    -- Check credit balance
    SELECT balance INTO credit_balance
    FROM public.credits
    WHERE user_id = p_user_id;

    IF credit_balance >= service_cost THEN 
        RETURN QUERY SELECT 
            TRUE as can_use,
            'credits'::TEXT as pricing_model,
            service_cost,
            NULL::TEXT as error_code;
        RETURN;
    END IF;

    -- Check if PAYG is allowed for this API key
    SELECT allow_payg INTO payg_allowed
    FROM public.api_keys
    WHERE id = p_api_key_id
    AND is_active = TRUE;

    -- Check if user has unpaid PAYG balances
    SELECT has_unpaid_payg_balance(p_user_id) INTO has_unpaid_balance;

    -- Fallback to PAYG if allowed, user has stripe customer id, and no unpaid balances
    IF payg_allowed AND EXISTS (
        SELECT 1 FROM public.profiles
        WHERE id = p_user_id
        AND stripe_customer_id IS NOT NULL
    ) THEN
        -- Check for unpaid balances
        IF has_unpaid_balance THEN
            RETURN QUERY SELECT 
                FALSE as can_use,
                'payg'::TEXT as pricing_model,
                service_cost,
                'unpaid_balance'::TEXT as error_code;
            RETURN;
        END IF;
        
        -- No unpaid balances, allow PAYG
        RETURN QUERY SELECT 
            TRUE as can_use,
            'payg'::TEXT as pricing_model,
            service_cost,
            NULL::TEXT as error_code;
        RETURN;
    END IF;

    -- If we get here, no payment method is available
    RETURN QUERY SELECT 
        FALSE as can_use,
        NULL::TEXT as pricing_model,
        service_cost,
        'insufficient_credits'::TEXT as error_code;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop the existing finalize_usage function
DROP FUNCTION IF EXISTS finalize_usage(UUID, TEXT, TEXT, DECIMAL, DECIMAL, TEXT, JSONB);

-- Create finalize_usage function with proper decimal handling
CREATE OR REPLACE FUNCTION finalize_usage(
    p_user_id UUID,
    p_service TEXT,
    p_model TEXT,
    p_amount DECIMAL(18,9),
    p_cost DECIMAL(18,9),
    p_pricing_model TEXT,
    p_metadata JSONB
) RETURNS VOID AS $$
BEGIN
    -- Update usage history to success using a subquery to handle ORDER BY and LIMIT
    UPDATE public.usage_history
    SET status = 'success',
        amount = p_amount,
        cost = ROUND(p_cost, 9),
        metadata = p_metadata
    WHERE id = (
        SELECT id 
        FROM public.usage_history 
        WHERE user_id = p_user_id
        AND service = p_service
        AND model = p_model
        AND status = 'pending'
        ORDER BY created_at DESC
        LIMIT 1
    );

    -- Update credits if using credits pricing model
    IF p_pricing_model = 'credits' THEN
        UPDATE public.credits
        SET balance = ROUND(balance - p_cost, 9)
        WHERE user_id = p_user_id;
    END IF;

    -- Update quota if using subscription
    IF p_pricing_model = 'subscription' THEN
        UPDATE public.quotas q
        SET used_amount = ROUND(used_amount + p_amount, 9)
        FROM public.user_subscriptions us
        WHERE q.subscription_id = us.id
        AND us.user_id = p_user_id
        AND q.service = p_service
        AND us.status = 'active'
        AND q.reset_date > NOW();
    END IF;

    -- Handle PAYG usage recording
    IF p_pricing_model = 'payg' THEN
        INSERT INTO public.payg_usage (user_id, month, total_amount)
        VALUES (
            p_user_id, 
            date_trunc('month', CURRENT_DATE), 
            p_cost
        )
        ON CONFLICT (user_id, month) 
        DO UPDATE SET total_amount = ROUND(payg_usage.total_amount + EXCLUDED.total_amount, 9);
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMIT; 
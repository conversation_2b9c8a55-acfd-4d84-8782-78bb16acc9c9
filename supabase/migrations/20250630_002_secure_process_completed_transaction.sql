-- Migration: Secure process_completed_transaction for Service Role Only
-- Date: 2025-06-30
-- Purpose: Restrict execution of process_completed_transaction to service role only
--          This prevents unauthorized access and potential abuse by users

-- First, revoke execute permissions from public and authenticated roles
REVOKE EXECUTE ON FUNCTION public.process_completed_transaction(text, text, numeric, text, text, text, jsonb) FROM PUBLIC;
REVOKE EXECUTE ON FUNCTION public.process_completed_transaction(text, text, numeric, text, text, text, jsonb) FROM authenticated;
REVOKE EXECUTE ON FUNCTION public.process_completed_transaction(text, text, numeric, text, text, text, jsonb) FROM anon;

-- Grant execute permission only to service_role
GRANT EXECUTE ON FUNCTION public.process_completed_transaction(text, text, numeric, text, text, text, jsonb) TO service_role;

-- Add a comment to document the security restriction
COMMENT ON FUNCTION public.process_completed_transaction IS 
'Process completed Paddle transactions and handle credit purchases. Automatically adjusts for negative balances by offsetting them against new credit purchases. SECURITY: Only accessible by service_role (edge functions).';

-- For extra security, let's also ensure any future versions of this function 
-- will have the same restrictions by creating a reminder comment
-- Note: When updating this function in the future, remember to re-apply these permissions:
-- REVOKE EXECUTE ON FUNCTION public.process_completed_transaction FROM PUBLIC, authenticated, anon;
-- GRANT EXECUTE ON FUNCTION public.process_completed_transaction TO service_role;

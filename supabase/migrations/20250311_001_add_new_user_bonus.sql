-- Add new user bonus function
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  v_profile_id UUID;
BEGIN
  -- Create a profile for the new user
  INSERT INTO public.profiles (id, email, full_name, created_at, updated_at)
  VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'full_name', NOW(), NOW())
  RETURNING id INTO v_profile_id;
  
  -- Create initial credits for the new user (10 minutes of free transcription)
  INSERT INTO public.credits (user_id, balance, currency, created_at, updated_at)
  VALUES (NEW.id, 0, 'USD', NOW(), NOW());
  
  -- Create initial quotas for the new user
  -- 10 minutes of free transcription
  INSERT INTO public.quotas (user_id, service, used_amount, total_amount, reset_date, created_at)
  VALUES (NEW.id, 'transcription', 0, 10, NOW() + INTERVAL '30 days', NOW());
  
  -- 5,000 input tokens
  INSERT INTO public.quotas (user_id, service, used_amount, total_amount, reset_date, created_at)
  VALUES (NEW.id, 'input_tokens', 0, 5000, NOW() + INTERVAL '30 days', NOW());
  
  -- 10,000 output tokens
  INSERT INTO public.quotas (user_id, service, used_amount, total_amount, reset_date, created_at)
  VALUES (NEW.id, 'output_tokens', 0, 10000, NOW() + INTERVAL '30 days', NOW());
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop the trigger if it exists
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Create the trigger
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user(); 

GRANT EXECUTE ON FUNCTION public.handle_new_user() TO postgres;

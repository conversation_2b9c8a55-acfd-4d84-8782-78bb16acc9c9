-- Revert AssemblyAI real-time transcription pricing changes
-- Date: March 31, 2025

-- Remove the added pricing entries
DELETE FROM public.service_pricing 
WHERE service = 'transcription' 
AND model IN (
    'assemblyai/best-realtime'
);

-- Log the rollback
INSERT INTO public.migration_logs (migration_name, details)
VALUES ('20250331_003_add_assemblyai_realtime_rollback', jsonb_build_object(
    'action', 'Removed AssemblyAI real-time transcription pricing entry',
    'models', jsonb_build_array('assemblyai/best-realtime'),
    'timestamp', current_timestamp
)); 
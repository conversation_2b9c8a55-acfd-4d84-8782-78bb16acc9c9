-- Add AssemblyAI real-time transcription pricing
-- Date: March 31, 2025

-- First, delete any existing entries for the model to avoid duplicates
DELETE FROM public.service_pricing 
WHERE service = 'transcription' 
AND model IN (
    'assemblyai/best-realtime'
);

-- Insert the pricing entries with correct cost per unit
INSERT INTO public.service_pricing (service, model, cost_per_unit, unit, is_active)
VALUES 
    -- AssemblyAI best model for real-time transcription
    ('transcription', 'assemblyai/best-realtime', 0.009243333, 'minute', true);

-- Create table for migration logs if it doesn't exist
CREATE TABLE IF NOT EXISTS public.migration_logs (
    id SERIAL PRIMARY KEY,
    migration_name TEXT NOT NULL,
    details JSONB NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

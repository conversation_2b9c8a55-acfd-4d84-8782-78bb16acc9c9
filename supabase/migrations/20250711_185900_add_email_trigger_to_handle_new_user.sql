-- Add email trigger to handle_new_user function

ALTER FUNCTION public.handle_new_user()
R<PERSON><PERSON><PERSON> trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    v_profile_id UUID;
    v_subscription_id UUID;
    v_plan_id UUID;
    v_current_period_start TIMESTAMPTZ;
    v_current_period_end TIMESTAMPTZ;
    v_trial_subscription_id TEXT;
BEGIN
    -- Create a profile for the new user with default credit_allowance
    INSERT INTO public.profiles (id, email, full_name, credit_allowance, created_at, updated_at)
    VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'full_name', 10.0, NOW(), NOW())
    RETURNING id INTO v_profile_id;
    
    -- Rest of the existing function remains the same
    -- Get the Free Trial plan ID
    SELECT id INTO v_plan_id FROM public.subscription_plans WHERE name = 'Free Trial';
    
    -- Set subscription period (1 month from now)
    v_current_period_start := NOW();
    v_current_period_end := v_current_period_start + INTERVAL '1 month';
    
    -- Generate a placeholder for paddle_subscription_id
    v_trial_subscription_id := 'trial_' || NEW.id;
    
    -- Create a subscription for the new user
    INSERT INTO public.user_subscriptions (
        user_id,
        plan_id,
        status,
        current_period_start,
        current_period_end,
        paddle_subscription_id,
        created_at
    ) VALUES (
        NEW.id,
        v_plan_id,
        'active',
        v_current_period_start,
        v_current_period_end,
        v_trial_subscription_id,
        NOW()
    ) RETURNING id INTO v_subscription_id;
    
    -- Create initial quotas for the new user linked to the subscription
    -- Transcription minutes
    INSERT INTO public.quotas (
        user_id, 
        subscription_id, 
        service, 
        used_amount, 
        total_amount, 
        reset_date, 
        created_at
    ) VALUES (
        NEW.id, 
        v_subscription_id, 
        'transcription', 
        0, 
        60, 
        v_current_period_end, 
        NOW()
    );
    
    -- Tokens
    INSERT INTO public.quotas (
        user_id, 
        subscription_id, 
        service, 
        used_amount, 
        total_amount, 
        reset_date, 
        created_at
    ) VALUES (
        NEW.id, 
        v_subscription_id, 
        'optimization', 
        0, 
        10000, 
        v_current_period_end, 
        NOW()
    );
    
    -- Call email edge function with secret key
    PERFORM public.email_edge_function(NEW.id, 'welcome_email');
    
    RETURN NEW;
END;
$function$
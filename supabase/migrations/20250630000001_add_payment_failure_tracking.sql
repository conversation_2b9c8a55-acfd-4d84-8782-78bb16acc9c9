-- Add payment failure tracking and subscription events tables

-- Table to track payment failures for analytics and monitoring
CREATE TABLE IF NOT EXISTS public.payment_failures (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    transaction_id TEXT NOT NULL,
    subscription_id TEXT,
    customer_id TEXT,
    amount DECIMAL(10,2),
    currency TEXT,
    failure_reason TEXT,
    occurred_at TIMESTAMPTZ NOT NULL,
    raw_data JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Table to track subscription lifecycle events
CREATE TABLE IF NOT EXISTS public.subscription_events (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    subscription_id TEXT NOT NULL,
    event_type TEXT NOT NULL,
    occurred_at TIMESTAMPTZ NOT NULL,
    event_data JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_payment_failures_transaction_id ON public.payment_failures(transaction_id);
CREATE INDEX IF NOT EXISTS idx_payment_failures_subscription_id ON public.payment_failures(subscription_id);
CREATE INDEX IF NOT EXISTS idx_payment_failures_occurred_at ON public.payment_failures(occurred_at);

CREATE INDEX IF NOT EXISTS idx_subscription_events_subscription_id ON public.subscription_events(subscription_id);
CREATE INDEX IF NOT EXISTS idx_subscription_events_event_type ON public.subscription_events(event_type);
CREATE INDEX IF NOT EXISTS idx_subscription_events_occurred_at ON public.subscription_events(occurred_at);

-- Add next_billed_at column to user_subscriptions table if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_subscriptions' 
        AND column_name = 'next_billed_at'
    ) THEN
        ALTER TABLE public.user_subscriptions 
        ADD COLUMN next_billed_at TIMESTAMPTZ;
    END IF;
END $$;

-- Enable RLS on new tables
ALTER TABLE public.payment_failures ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscription_events ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for payment_failures (admin only)
CREATE POLICY "Admin can view payment failures" ON public.payment_failures
    FOR SELECT USING (auth.role() = 'service_role');

CREATE POLICY "Admin can insert payment failures" ON public.payment_failures
    FOR INSERT WITH CHECK (auth.role() = 'service_role');

-- Create RLS policies for subscription_events (admin only)
CREATE POLICY "Admin can view subscription events" ON public.subscription_events
    FOR SELECT USING (auth.role() = 'service_role');

CREATE POLICY "Admin can insert subscription events" ON public.subscription_events
    FOR INSERT WITH CHECK (auth.role() = 'service_role');

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON public.payment_failures TO anon;
GRANT SELECT, INSERT, UPDATE ON public.payment_failures TO authenticated;
GRANT ALL ON public.payment_failures TO service_role;

GRANT SELECT, INSERT, UPDATE ON public.subscription_events TO anon;
GRANT SELECT, INSERT, UPDATE ON public.subscription_events TO authenticated;
GRANT ALL ON public.subscription_events TO service_role;

-- Migration: Create centralized subscription management function with UPSERT logic
-- Created: 2025-06-27
-- Purpose: Eliminate duplicate entries and centralize subscription operations

-- First, ensure we have proper constraints to prevent duplicates
ALTER TABLE user_subscriptions DROP CONSTRAINT IF EXISTS user_subscriptions_user_id_unique;
ALTER TABLE user_subscriptions ADD CONSTRAINT user_subscriptions_user_id_unique UNIQUE (user_id);

ALTER TABLE quotas DROP CONSTRAINT IF EXISTS quotas_user_service_unique;
ALTER TABLE quotas ADD CONSTRAINT quotas_user_service_unique UNIQUE (user_id, service);

-- Create or replace the centralized subscription management function
CREATE OR REPLACE FUNCTION handle_subscription_transaction(
    p_user_id UUID,
    p_paddle_subscription_id TEXT,
    p_paddle_customer_id TEXT,
    p_subscription_plan TEXT,
    p_transaction_data JSONB DEFAULT NULL
) RETURNS JSONB AS $$
DECLARE
    v_plan_id UUID;
    v_subscription_id UUID;
    v_transcription_quota INTEGER;
    v_optimization_quota INTEGER;
    v_reset_date TIMESTAMP WITH TIME ZONE;
    v_result JSONB;
BEGIN
    -- Log the start of the operation
    RAISE LOG 'Starting subscription transaction for user: %, plan: %', p_user_id, p_subscription_plan;
    
    -- Set reset date to 30 days from now
    v_reset_date := NOW() + INTERVAL '30 days';
    
    -- Get plan details and quotas based on plan name
    CASE LOWER(p_subscription_plan)
        WHEN 'basic' THEN
            v_transcription_quota := 720;    -- 12 hours
            v_optimization_quota := 400000;  -- 400K tokens
        WHEN 'pro' THEN
            v_transcription_quota := 1440;   -- 24 hours
            v_optimization_quota := 800000;  -- 800K tokens
        WHEN 'premium' THEN
            v_transcription_quota := 2160;   -- 36 hours
            v_optimization_quota := 1200000; -- 1.2M tokens
        ELSE
            RAISE EXCEPTION 'Unknown subscription plan: %', p_subscription_plan;
    END CASE;
    
    -- Try to get the plan_id from subscription_plans table
    SELECT id INTO v_plan_id
    FROM subscription_plans 
    WHERE LOWER(name) = LOWER(p_subscription_plan)
    LIMIT 1;
    
    -- If plan not found, log warning but continue
    IF v_plan_id IS NULL THEN
        RAISE LOG 'Plan ID not found for plan: %, continuing without plan_id', p_subscription_plan;
    END IF;
    
    -- UPSERT user subscription (one subscription per user)
    INSERT INTO user_subscriptions (
        user_id,
        plan_id,
        status,
        current_period_start,
        current_period_end,
        stripe_subscription_id,
        created_at
    ) VALUES (
        p_user_id,
        v_plan_id,
        'active',
        NOW(),
        v_reset_date,
        p_paddle_subscription_id,
        NOW()
    )
    ON CONFLICT (user_id) 
    DO UPDATE SET
        plan_id = EXCLUDED.plan_id,
        status = EXCLUDED.status,
        current_period_start = EXCLUDED.current_period_start,
        current_period_end = EXCLUDED.current_period_end,
        stripe_subscription_id = EXCLUDED.stripe_subscription_id
    RETURNING id INTO v_subscription_id;
    
    RAISE LOG 'Upserted user subscription with ID: %', v_subscription_id;
    
    -- UPSERT transcription quota (one quota per user per service)
    INSERT INTO quotas (
        user_id,
        subscription_id,
        service,
        used_amount,
        total_amount,
        reset_date,
        created_at
    ) VALUES (
        p_user_id,
        v_subscription_id,
        'transcription',
        0,
        v_transcription_quota,
        v_reset_date,
        NOW()
    )
    ON CONFLICT (user_id, service)
    DO UPDATE SET
        subscription_id = EXCLUDED.subscription_id,
        used_amount = 0, -- Reset usage on subscription change
        total_amount = EXCLUDED.total_amount,
        reset_date = EXCLUDED.reset_date;
    
    -- UPSERT optimization quota (one quota per user per service)
    INSERT INTO quotas (
        user_id,
        subscription_id,
        service,
        used_amount,
        total_amount,
        reset_date,
        created_at
    ) VALUES (
        p_user_id,
        v_subscription_id,
        'optimization',
        0,
        v_optimization_quota,
        v_reset_date,
        NOW()
    )
    ON CONFLICT (user_id, service)
    DO UPDATE SET
        subscription_id = EXCLUDED.subscription_id,
        used_amount = 0, -- Reset usage on subscription change
        total_amount = EXCLUDED.total_amount,
        reset_date = EXCLUDED.reset_date;
    
    -- Optional: UPSERT into paddle.subscriptions table if it exists
    BEGIN
        INSERT INTO paddle.subscriptions (
            user_id,
            paddle_subscription_id,
            paddle_customer_id,
            product_id,
            status,
            current_period_start,
            current_period_end,
            metadata,
            created_at
        ) VALUES (
            p_user_id,
            p_paddle_subscription_id,
            p_paddle_customer_id,
            COALESCE(v_plan_id::TEXT, p_subscription_plan),
            'active',
            NOW(),
            v_reset_date,
            p_transaction_data,
            NOW()
        )
        ON CONFLICT (paddle_subscription_id)
        DO UPDATE SET
            user_id = EXCLUDED.user_id,
            paddle_customer_id = EXCLUDED.paddle_customer_id,
            product_id = EXCLUDED.product_id,
            status = EXCLUDED.status,
            current_period_start = EXCLUDED.current_period_start,
            current_period_end = EXCLUDED.current_period_end,
            metadata = EXCLUDED.metadata;
        
        RAISE LOG 'Upserted paddle subscription record';
    EXCEPTION
        WHEN undefined_table THEN
            RAISE LOG 'paddle.subscriptions table does not exist, skipping';
        WHEN OTHERS THEN
            RAISE LOG 'Error upserting paddle subscription: %', SQLERRM;
    END;
    
    -- Build result JSON
    v_result := jsonb_build_object(
        'success', true,
        'user_id', p_user_id,
        'subscription_id', v_subscription_id,
        'plan', p_subscription_plan,
        'quotas', jsonb_build_object(
            'transcription', v_transcription_quota,
            'optimization', v_optimization_quota
        ),
        'reset_date', v_reset_date
    );
    
    RAISE LOG 'Successfully processed subscription transaction: %', v_result;
    
    RETURN v_result;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE LOG 'Error in handle_subscription_transaction: %', SQLERRM;
        RETURN jsonb_build_object(
            'success', false,
            'error', SQLERRM,
            'user_id', p_user_id,
            'plan', p_subscription_plan
        );
END;
$$ LANGUAGE plpgsql;

-- Create function to handle subscription cancellation
CREATE OR REPLACE FUNCTION handle_subscription_cancellation(
    p_paddle_subscription_id TEXT,
    p_user_id UUID DEFAULT NULL
) RETURNS JSONB AS $$
DECLARE
    v_user_id UUID;
    v_result JSONB;
BEGIN
    -- If user_id not provided, try to find it from existing subscription
    IF p_user_id IS NULL THEN
        SELECT user_id INTO v_user_id
        FROM user_subscriptions
        WHERE stripe_subscription_id = p_paddle_subscription_id
        LIMIT 1;
        
        IF v_user_id IS NULL THEN
            RAISE LOG 'User not found for subscription: %', p_paddle_subscription_id;
            RETURN jsonb_build_object('success', false, 'error', 'User not found');
        END IF;
    ELSE
        v_user_id := p_user_id;
    END IF;
    
    -- Update user subscription status
    UPDATE user_subscriptions 
    SET 
        status = 'canceled'
    WHERE user_id = v_user_id;
    
    -- Optional: Update paddle.subscriptions if exists
    BEGIN
        UPDATE paddle.subscriptions 
        SET 
            status = 'cancelled',
            cancel_at_period_end = true
        WHERE paddle_subscription_id = p_paddle_subscription_id;
    EXCEPTION
        WHEN undefined_table THEN
            RAISE LOG 'paddle.subscriptions table does not exist, skipping';
        WHEN OTHERS THEN
            RAISE LOG 'Error updating paddle subscription: %', SQLERRM;
    END;
    
    -- Note: We don't delete quotas on cancellation - they remain until period end
    
    v_result := jsonb_build_object(
        'success', true,
        'user_id', v_user_id,
        'subscription_id', p_paddle_subscription_id,
        'status', 'canceled'
    );
    
    RAISE LOG 'Successfully canceled subscription: %', v_result;
    
    RETURN v_result;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE LOG 'Error in handle_subscription_cancellation: %', SQLERRM;
        RETURN jsonb_build_object(
            'success', false,
            'error', SQLERRM,
            'subscription_id', p_paddle_subscription_id
        );
END;
$$ LANGUAGE plpgsql;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION handle_subscription_transaction TO authenticated, anon;
GRANT EXECUTE ON FUNCTION handle_subscription_cancellation TO authenticated, anon;

-- Add comment
COMMENT ON FUNCTION handle_subscription_transaction IS 'Centralized function to handle subscription transactions with UPSERT logic to prevent duplicates';
COMMENT ON FUNCTION handle_subscription_cancellation IS 'Handle subscription cancellations with proper status updates';

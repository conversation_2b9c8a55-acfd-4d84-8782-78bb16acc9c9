-- Fix the process_completed_transaction function with better debugging and user lookup
-- This addresses the issue where the function fails to find users

-- Drop and recreate the function with better error handling
DROP FUNCTION IF EXISTS public.process_completed_transaction(TEXT, TEXT, DECIMAL, TEXT, TEXT, TEXT, JSONB) CASCADE;

CREATE OR R<PERSON>LACE FUNCTION public.process_completed_transaction(
    p_paddle_transaction_id TEXT,
    p_paddle_customer_id TEXT,
    p_amount DECIMAL,
    p_currency TEXT,
    p_product_id TEXT DEFAULT NULL,
    p_receipt_url TEXT DEFAULT NULL,
    p_metadata JSONB DEFAULT '{}'::jsonb
)
RETURNS TABLE (
    success BOOLEAN,
    user_id UUID,
    transaction_id UUID,
    credit_amount DECIMAL
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_user_id UUID;
    v_transaction_id UUID;
    v_credit_amount DECIMAL(18,9);
    v_email TEXT;
    v_user_exists BOOLEAN := FALSE;
    v_auth_user_exists BOOLEAN := FALSE;
BEGIN
    RAISE NOTICE 'Starting transaction processing for %', p_paddle_transaction_id;
    RAISE NOTICE 'Metadata received: %', p_metadata;
    
    -- First, try to get user_id from custom_data (most reliable)
    v_user_id := (p_metadata->>'user_id')::UUID;
    RAISE NOTICE 'Extracted user_id from metadata: %', v_user_id;
    
    -- If user_id is in custom_data, verify it exists
    IF v_user_id IS NOT NULL THEN
        -- Check if user exists in auth.users
        SELECT EXISTS (SELECT 1 FROM auth.users WHERE id = v_user_id) INTO v_auth_user_exists;
        RAISE NOTICE 'User exists in auth.users: %', v_auth_user_exists;
        
        -- Check if user exists in profiles
        SELECT EXISTS (SELECT 1 FROM public.profiles WHERE id = v_user_id) INTO v_user_exists;
        RAISE NOTICE 'User exists in profiles: %', v_user_exists;
        
        IF v_user_exists THEN
            -- Get user email for customer record
            SELECT email INTO v_email FROM public.profiles WHERE id = v_user_id;
            RAISE NOTICE 'Found user email: %', v_email;
            
            -- Create/update customer record
            PERFORM public.upsert_paddle_customer(
                v_user_id,
                p_paddle_customer_id,
                v_email,
                p_metadata->>'customer_name',
                p_metadata->>'customer_country',
                p_metadata
            );
            RAISE NOTICE 'Customer record upserted successfully';
        ELSIF v_auth_user_exists THEN
            -- User exists in auth but not in profiles - this shouldn't happen but let's handle it
            RAISE WARNING 'User % exists in auth.users but not in profiles table', v_user_id;
            
            -- Get email from auth.users
            SELECT email INTO v_email FROM auth.users WHERE id = v_user_id;
            
            -- Create profile record (this might be needed if profile wasn't created properly)
            INSERT INTO public.profiles (id, email, full_name)
            VALUES (v_user_id, v_email, COALESCE(p_metadata->>'customer_name', 'Unknown'))
            ON CONFLICT (id) DO NOTHING;
            
            RAISE NOTICE 'Created missing profile for user %', v_user_id;
            
            -- Create/update customer record
            PERFORM public.upsert_paddle_customer(
                v_user_id,
                p_paddle_customer_id,
                v_email,
                p_metadata->>'customer_name',
                p_metadata->>'customer_country',
                p_metadata
            );
        ELSE
            -- User ID in custom_data doesn't exist anywhere
            RAISE WARNING 'User ID % from custom_data not found in auth.users or profiles', v_user_id;
            v_user_id := NULL;
        END IF;
    ELSE
        RAISE NOTICE 'No user_id found in custom_data, trying fallback methods';
    END IF;
    
    -- Fallback: Look up by existing customer record
    IF v_user_id IS NULL THEN
        RAISE NOTICE 'Trying to find user by existing customer record for customer %', p_paddle_customer_id;
        SELECT pc.user_id, pc.email INTO v_user_id, v_email
        FROM public.paddle_customers pc
        WHERE pc.paddle_customer_id = p_paddle_customer_id;
        
        IF v_user_id IS NOT NULL THEN
            RAISE NOTICE 'Found user via customer record: %', v_user_id;
        END IF;
    END IF;
    
    -- Fallback: Try to find user by email from metadata
    IF v_user_id IS NULL THEN
        v_email := p_metadata->>'customer_email';
        RAISE NOTICE 'Trying to find user by email from metadata: %', v_email;
        
        IF v_email IS NOT NULL THEN
            -- Try to find user by email in profiles
            SELECT pr.id INTO v_user_id
            FROM public.profiles pr
            WHERE pr.email = v_email;
            
            IF v_user_id IS NOT NULL THEN
                RAISE NOTICE 'Found user by email: %', v_user_id;
                -- Create customer record
                PERFORM public.upsert_paddle_customer(
                    v_user_id,
                    p_paddle_customer_id,
                    v_email,
                    p_metadata->>'customer_name',
                    p_metadata->>'customer_country',
                    p_metadata
                );
            ELSE
                RAISE NOTICE 'No user found with email: %', v_email;
            END IF;
        END IF;
    END IF;

    -- If still no user, return failure with detailed logging
    IF v_user_id IS NULL THEN
        RAISE WARNING 'TRANSACTION FAILED: No user found for transaction %, customer %, metadata keys: %', 
            p_paddle_transaction_id, p_paddle_customer_id, jsonb_object_keys(p_metadata);
        RETURN QUERY SELECT FALSE, NULL::UUID, NULL::UUID, NULL::DECIMAL;
        RETURN;
    END IF;

    RAISE NOTICE 'Processing transaction for user: %', v_user_id;

    -- Use credit_amount from custom_data if available, otherwise use amount
    IF p_metadata ? 'credit_amount' THEN
        v_credit_amount := (p_metadata->>'credit_amount')::DECIMAL;
        RAISE NOTICE 'Using credit_amount from custom_data: %', v_credit_amount;
    ELSE
        -- Calculate credit amount (assume $1 = 1 credit for now, but amount is in cents)
        v_credit_amount := p_amount / 100.0;
        RAISE NOTICE 'Calculated credit_amount from amount: % (amount was %)', v_credit_amount, p_amount;
    END IF;

    -- Create transaction record
    INSERT INTO public.paddle_transactions (
        user_id,
        paddle_transaction_id,
        paddle_customer_id,
        paddle_product_id,
        status,
        amount,
        currency,
        credit_amount,
        transaction_type,
        paddle_receipt_url,
        metadata
    ) VALUES (
        v_user_id,
        p_paddle_transaction_id,
        p_paddle_customer_id,
        p_product_id,
        'completed',
        p_amount,
        p_currency,
        v_credit_amount,
        'credit_purchase',
        p_receipt_url,
        p_metadata
    )
    ON CONFLICT (paddle_transaction_id) DO UPDATE SET
        status = 'completed',
        updated_at = NOW()
    RETURNING id INTO v_transaction_id;

    RAISE NOTICE 'Transaction record created with id: %', v_transaction_id;

    -- Add credits to user account
    INSERT INTO public.credits (
        user_id,
        amount,
        source,
        transaction_reference,
        description,
        expires_at
    ) VALUES (
        v_user_id,
        v_credit_amount,
        'purchase',
        p_paddle_transaction_id,
        'Credit purchase via Paddle',
        NOW() + INTERVAL '1 year'
    );

    RAISE NOTICE 'Successfully processed transaction % for user % (% credits added)', 
        p_paddle_transaction_id, v_user_id, v_credit_amount;

    RETURN QUERY SELECT TRUE, v_user_id, v_transaction_id, v_credit_amount;
END;
$$;

COMMENT ON FUNCTION public.process_completed_transaction(TEXT, TEXT, DECIMAL, TEXT, TEXT, TEXT, JSONB) IS 'Process completed Paddle transactions with enhanced debugging and user lookup';

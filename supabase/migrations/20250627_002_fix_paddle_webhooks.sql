-- Migration: Fix paddle.webhooks table schema issues
-- Created: 2025-06-27
-- Purpose: Ensure paddle.webhooks table exists and handle logging gracefully

-- Create paddle schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS paddle;

-- Create paddle.webhooks table if it doesn't exist
CREATE TABLE IF NOT EXISTS paddle.webhooks (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    paddle_event_id TEXT UNIQUE NOT NULL,
    event_type TEXT NOT NULL,
    raw_payload JSONB NOT NULL,
    processed BOOLEAN DEFAULT FALSE,
    processing_error TEXT,
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index on paddle_event_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_paddle_webhooks_event_id 
ON paddle.webhooks (paddle_event_id);

-- Create index on processed status for monitoring
CREATE INDEX IF NOT EXISTS idx_paddle_webhooks_processed 
ON paddle.webhooks (processed, created_at);

-- Add comment
COMMENT ON TABLE paddle.webhooks IS 'Logs all Paddle webhook events for debugging and monitoring';

-- Grant permissions
GRANT ALL ON SCHEMA paddle TO postgres;
GRANT ALL ON TABLE paddle.webhooks TO postgres, authenticated, anon;

-- Update the usage summary function to only include successful entries in cost calculations
CREATE OR REPLACE FUNCTION public.get_usage_summary_statistics(
  p_user_id TEXT,
  p_start_date TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  p_end_date TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  p_service TEXT DEFAULT NULL,
  p_model TEXT DEFAULT NULL,
  p_status TEXT DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
  v_total_cost NUMERIC;
  v_service_breakdown JSON;
  v_model_breakdown JSON;
  v_available_services TEXT[];
  v_available_models TEXT[];
  v_result JSON;
  v_query TEXT;
BEGIN
  -- Build the query directly without dynamic parameters
  v_query := 'WITH usage_summary AS (
    SELECT 
      service,
      model,
      SUM(cost) as total_cost_per_group
    FROM 
      usage_history
    WHERE 
      user_id = $1::uuid';
  
  -- Add filters with fixed parameter positions
  IF p_start_date IS NOT NULL THEN
    v_query := v_query || ' AND created_at >= $2';
  END IF;
  
  IF p_end_date IS NOT NULL THEN
    v_query := v_query || ' AND created_at <= $3';
  END IF;
  
  IF p_service IS NOT NULL THEN
    v_query := v_query || ' AND service = $4';
  END IF;
  
  IF p_model IS NOT NULL THEN
    v_query := v_query || ' AND model = $5';
  END IF;
  
  IF p_status IS NOT NULL THEN
    v_query := v_query || ' AND status = $6';
  ELSE
    -- Only include successful entries when calculating costs
    v_query := v_query || ' AND status = ''success''';
  END IF;
  
  -- Complete the query with proper grouping and aggregation
  v_query := v_query || ' GROUP BY service, model)
  SELECT 
    COALESCE(SUM(total_cost_per_group), 0) AS total_cost,
    COALESCE(
      jsonb_object_agg(service, total_cost_per_group) FILTER (WHERE service IS NOT NULL),
      ''{}''::jsonb
    ) AS service_breakdown,
    COALESCE(
      jsonb_object_agg(model, total_cost_per_group) FILTER (WHERE model IS NOT NULL),
      ''{}''::jsonb
    ) AS model_breakdown,
    ARRAY_AGG(DISTINCT service) FILTER (WHERE service IS NOT NULL) AS available_services,
    ARRAY_AGG(DISTINCT model) FILTER (WHERE model IS NOT NULL) AS available_models
  FROM usage_summary';
  
  -- Execute the query with parameters
  EXECUTE v_query 
  INTO v_total_cost, v_service_breakdown, v_model_breakdown, v_available_services, v_available_models 
  USING 
    p_user_id,
    p_start_date,
    p_end_date,
    p_service,
    p_model,
    p_status;
  
  -- Build the result JSON
  v_result := json_build_object(
    'total_cost', v_total_cost,
    'service_breakdown', v_service_breakdown,
    'model_breakdown', v_model_breakdown,
    'available_services', v_available_services,
    'available_models', v_available_models
  );
  
  RETURN v_result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Revoke all permissions from all roles
REVOKE ALL ON FUNCTION public.get_usage_summary_statistics(TEXT, TIMESTAMP WITH TIME ZONE, TIMESTAMP WITH TIME ZONE, TEXT, TEXT, TEXT) FROM PUBLIC;
REVOKE ALL ON FUNCTION public.get_usage_summary_statistics(TEXT, TIMESTAMP WITH TIME ZONE, TIMESTAMP WITH TIME ZONE, TEXT, TEXT, TEXT) FROM anon;
REVOKE ALL ON FUNCTION public.get_usage_summary_statistics(TEXT, TIMESTAMP WITH TIME ZONE, TIMESTAMP WITH TIME ZONE, TEXT, TEXT, TEXT) FROM authenticated;
REVOKE ALL ON FUNCTION public.get_usage_summary_statistics(TEXT, TIMESTAMP WITH TIME ZONE, TIMESTAMP WITH TIME ZONE, TEXT, TEXT, TEXT) FROM service_role;

-- Grant execute permission only to authenticated role
GRANT EXECUTE ON FUNCTION public.get_usage_summary_statistics(TEXT, TIMESTAMP WITH TIME ZONE, TIMESTAMP WITH TIME ZONE, TEXT, TEXT, TEXT) TO authenticated; 
# API Key Validation Fix

## Issue

There were two issues with the API key validation process:

1. **Hashing Method Mismatch**: The `create_api_key` function uses `crypt()` with `gen_salt('bf')` to hash the API key, but the `validate_api_key` function was using `encode(digest(p_key, 'sha256'), 'hex')` to hash the key for validation.

2. **Prefix Handling**: The website displays API keys with a `vhkey_` prefix, but the extension expects the raw key without this prefix. This inconsistency causes validation failures when users copy the full key (with prefix) from the website to the extension settings.

These issues caused API key validation to fail, preventing users from authenticating with their API keys.

## Solution

We've created a migration file to fix these issues:

### Migration File: `20250313_004_fix_validate_api_key.sql`

This migration:

1. Drops the existing `validate_api_key` function
2. Creates a new version that uses the same hashing method as `create_api_key`
3. Adds logic to handle keys with or without the `vhkey_` prefix
4. Ensures proper permissions are set on the function

## Key Changes

The main changes in the function are:

1. Added logic to handle keys with or without the `vhkey_` prefix:
   ```sql
   -- Check if the key has the vhkey_ prefix and remove it if present
   IF starts_with(p_key, 'vhkey_') THEN
       v_actual_key := substring(p_key from 7); -- Remove 'vhkey_' prefix
   ELSE
       v_actual_key := p_key;
   END IF;
   
   -- Extract prefix (first 8 characters of the actual key)
   v_key_prefix := substring(v_actual_key from 1 for 8);
   ```

2. Changed the hash validation to use `crypt()` to verify the hash:
   ```sql
   crypt(v_actual_key, key_hash) = key_hash
   ```

## How the API Key System Works

1. **Key Creation**: When a key is created with `create_api_key`:
   - A random base64 string is generated
   - The first 8 characters are stored as `key_prefix`
   - The full key is hashed with `crypt()` and stored as `key_hash`

2. **Key Display**: When displayed in the website:
   - The key is shown with a `vhkey_` prefix (e.g., `vhkey_AbCdEfGh12345...`)

3. **Key Validation**: When a key is validated with `validate_api_key`:
   - If the key has a `vhkey_` prefix, it's removed
   - The first 8 characters of the actual key are extracted as the prefix
   - The database is queried for a matching prefix
   - The actual key is hashed with `crypt()` and compared to the stored hash

## How to Apply the Migration

### Option 1: Using Supabase CLI

If you have the Supabase CLI installed:

```bash
# Navigate to your project directory
cd /path/to/your/project

# Apply the migration
supabase db push
```

### Option 2: Using the Supabase Dashboard

1. Log in to your Supabase dashboard
2. Go to the SQL Editor
3. Copy the contents of the migration file
4. Paste it into a new SQL query
5. Run the query

## Verifying the Fix

After applying the migration, try validating an API key using the `validate_api_key` function. The function should now correctly validate API keys created with the `create_api_key` function, regardless of whether they include the `vhkey_` prefix or not.

This means users can now:
- Copy the full key (with `vhkey_` prefix) from the website to the extension settings
- Or use the raw key without the prefix
- Both formats will work correctly

## Troubleshooting

If you continue to experience issues after applying this migration:

1. Check the Supabase logs for any error messages
2. Verify that the migration was applied successfully by checking if the `validate_api_key` function has been updated
3. Test the function directly in the SQL Editor with both key formats (with and without the `vhkey_` prefix)
4. Check if there are any other modifications to the key in the client code

If problems persist, please contact the development team for assistance. 
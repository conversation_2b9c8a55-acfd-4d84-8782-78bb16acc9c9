-- Description: This migration modifies the realtime.ts file to track successfully processed audio chunks
-- and calculate the actual audio duration for real-time transcription sessions.

-- No database changes are needed for this fix, as the issue is in the edge function code.
-- This migration file serves as documentation for the changes made to the realtime.ts file.

-- The issue:
-- 1. When real-time transcription starts, a "pending" entry is created in the database with 0 seconds duration
-- 2. When the transcription ends, the finalizeSession function is called, which calls the finalize_realtime_session database function
-- 3. The function is calculating the duration based on the session time (start to end), not the actual audio processed
-- 4. We need to track the successfully processed audio chunks and calculate the actual audio duration

-- The fix:
-- 1. Add tracking of successfully processed audio chunks in the realtime.ts file
-- 2. Calculate the actual audio duration based on the audio properties (sample rate, channels, etc.)
-- 3. Pass the calculated duration to the finalize_realtime_session function
-- 4. Update the usage_history entry with the calculated duration

-- No actual SQL changes are needed for this migration.

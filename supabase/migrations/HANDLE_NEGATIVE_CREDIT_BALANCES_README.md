# Handle Negative Credit Balances Migration

**File:** `20250630_001_handle_negative_credit_balances.sql`  
**Date:** June 30, 2025  
**Type:** Function Update  

## Overview

This migration updates the `process_completed_transaction` function to properly handle negative credit balances when users purchase new credits. Previously, the function would create new credit entries without considering existing negative balances, allowing users to effectively bypass debts.

## What This Migration Does

1. **Replaces** the existing `process_completed_transaction` function
2. **Adds logic** to calculate total current credit balance before adding new credits
3. **Adjusts** new credit purchases to offset negative balances
4. **Marks** negative credit entries as "consumed_by_purchase" instead of deleting them
5. **Records** adjustment details in transaction metadata for audit purposes

## Key Changes

### Before
```sql
-- Old behavior: Always add full credit amount
INSERT INTO public.credits (user_id, balance, ...) 
VALUES (v_user_id, v_credit_amount, ...);
```

### After
```sql
-- New behavior: Calculate adjustment for negative balance
SELECT COALESCE(SUM(balance), 0) INTO v_total_current_balance FROM public.credits WHERE user_id = v_user_id;

IF v_total_current_balance < 0 THEN
    v_adjusted_credit_amount := v_credit_amount - ABS(v_total_current_balance);
    -- Mark negative entries as consumed
    UPDATE public.credits SET status = 'consumed_by_purchase' WHERE user_id = v_user_id AND balance < 0;
END IF;

-- Only create new credit entry if adjusted amount > 0
IF v_adjusted_credit_amount > 0 THEN
    INSERT INTO public.credits (user_id, balance, ...) VALUES (v_user_id, v_adjusted_credit_amount, ...);
END IF;
```

## Examples

### Scenario 1: Small Negative Balance
- User has: -$0.50 credits
- Purchases: $10.00 credits
- **Result**: $9.50 credits (one clean entry)

### Scenario 2: Large Negative Balance
- User has: -$15.00 credits  
- Purchases: $10.00 credits
- **Result**: $0.00 credits (purchase fully offset negative balance)

### Scenario 3: No Negative Balance
- User has: $5.00 credits
- Purchases: $10.00 credits  
- **Result**: $15.00 credits (normal behavior)

## Safety Features

- **Audit Trail**: Original purchase amount always recorded in transactions table
- **Metadata Logging**: Adjustment details stored in transaction metadata
- **Status Tracking**: Negative entries marked as "consumed_by_purchase", not deleted
- **Comprehensive Logging**: All adjustments logged with NOTICE level for monitoring

## Testing

After applying this migration, test with:

1. Users with negative balances making purchases
2. Users with positive balances making purchases  
3. Users with zero balance making purchases
4. Users with mixed positive/negative entries

## Rollback

To rollback, restore the previous version of `process_completed_transaction` from the git history or previous migration files.

## Related Files

- **Migration**: `supabase/migrations/20250630_001_handle_negative_credit_balances.sql`
- **DevLog**: `docs/devlogs/2025-06-30-handle-negative-credit-balances.md`
- **Function**: `public.process_completed_transaction`

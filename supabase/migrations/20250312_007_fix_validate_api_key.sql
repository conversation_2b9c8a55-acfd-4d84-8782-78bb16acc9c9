-- Fix validate_api_key function to match the hashing method used in create_api_key
BEGIN;

-- Drop the existing function
DROP FUNCTION IF EXISTS public.validate_api_key;

-- Recreate the function with the correct hashing method
CREATE OR REPLACE FUNCTION public.validate_api_key(p_key TEXT)
RETURNS TABLE(user_id UUID, api_key_id UUID) AS $$
DECLARE
    v_key_prefix TEXT;
    v_actual_key TEXT;
BEGIN
    -- Check if the key has the vhkey_ prefix and remove it if present
    IF starts_with(p_key, 'vhkey_') THEN
        v_actual_key := substring(p_key from 7); -- Remove 'vhkey_' prefix
    ELSE
        v_actual_key := p_key;
    END IF;
    
    -- Extract prefix (first 8 characters of the actual key)
    v_key_prefix := substring(v_actual_key from 1 for 8);
    
    -- Update last used time and return user_id if key is valid
    -- Use crypt() to verify the hash, which is how the key was created
    UPDATE public.api_keys
    SET last_used_at = NOW()
    WHERE key_prefix = v_key_prefix
    AND crypt(v_actual_key, key_hash) = key_hash
    AND is_active = TRUE
    AND (expires_at IS NULL OR expires_at > NOW());
    
    -- Return user_id if key is valid
    RETURN QUERY
    SELECT a.user_id, a.id
    FROM public.api_keys a
    WHERE a.key_prefix = v_key_prefix
    AND crypt(v_actual_key, a.key_hash) = a.key_hash
    AND a.is_active = TRUE
    AND (a.expires_at IS NULL OR a.expires_at > NOW());
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the comment
COMMENT ON FUNCTION public.validate_api_key IS 'Validates an API key and returns the associated user_id and api_key_id if valid. Handles keys with or without the vhkey_ prefix.';

-- Revoke all permissions first
REVOKE ALL ON FUNCTION public.validate_api_key FROM PUBLIC;
REVOKE ALL ON FUNCTION public.validate_api_key FROM anon;
REVOKE ALL ON FUNCTION public.validate_api_key FROM authenticated;
REVOKE ALL ON FUNCTION public.validate_api_key FROM service_role;

-- Grant permission only to service_role
GRANT EXECUTE ON FUNCTION public.validate_api_key TO service_role;

COMMIT; 
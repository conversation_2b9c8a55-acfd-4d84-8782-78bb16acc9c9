-- First, add new service pricing entries for real-time transcription with token-based pricing
INSERT INTO public.service_pricing (service, model, cost_per_unit, unit, is_active, profit_margin)
VALUES 
  -- GPT-4o mini real-time
  ('transcription', 'gpt-4o-mini-transcribe-realtime', 0.05, 'minute', true, 10),
  -- GPT-4o real-time
  ('transcription', 'gpt-4o-transcribe-realtime', 0.10, 'minute', true, 10);
-- Update the finalize_session function to properly handle token billing
create or replace function "public"."finalize_realtime_session" (
  "p_user_id" "uuid",
  "p_api_key_id" "uuid",
  "p_model" "text",
  "p_session_id" "text",
  "p_start_time" bigint,
  "p_audio_bytes_sent" bigint,
  "p_has_transcription" boolean,
  "p_token_info" "jsonb" default null
) RETURNS "void" LANGUAGE "plpgsql" SECURITY DEFINER as $$
DECLARE
    v_end_time bigint;
    v_session_duration_ms bigint;
    v_session_duration_minutes numeric;
    v_service_pricing record;
    v_cost numeric;
    v_pricing_model text;
    v_cost_per_unit numeric;
    v_input_tokens integer;
    v_output_tokens integer;
    v_input_token_cost numeric;
    v_output_token_cost numeric;
    v_total_token_cost numeric;
    v_is_token_based boolean := false;
    v_query_result record;
    v_available_credit numeric;
    v_model_prefix text;
    v_metadata jsonb;
BEGIN
    -- Calculate session duration
    v_end_time := extract(epoch from now()) * 1000;
    v_session_duration_ms := v_end_time - p_start_time;
    v_session_duration_minutes := v_session_duration_ms / (1000 * 60);

    -- Extract the base model name for GPT-4o models
    IF p_model LIKE 'gpt-4o%' THEN
        v_model_prefix := p_model;
        -- If realtime not already in the name, add it
        IF p_model NOT LIKE '%-realtime' THEN
            v_model_prefix := p_model || '-realtime';
        END IF;
    ELSE
        v_model_prefix := p_model;
    END IF;

    -- Get service pricing
    SELECT * INTO v_service_pricing
    FROM public.service_pricing
    WHERE service = 'transcription'
    AND model = v_model_prefix
    AND is_active = TRUE;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'No active pricing found for service: transcription, model: %', v_model_prefix;
    END IF;

    -- Determine if this is a token-based model
    v_is_token_based := v_service_pricing.unit = 'token-based';

    -- If token info is provided and model is token-based, use token billing
    IF v_is_token_based AND p_token_info IS NOT NULL THEN
        -- Extract token counts
        v_input_tokens := COALESCE((p_token_info->>'input_tokens')::INTEGER, 0);
        v_output_tokens := COALESCE((p_token_info->>'output_tokens')::INTEGER, 0);
        
        -- Get token costs for this model
        SELECT cost_per_unit INTO v_input_token_cost
        FROM public.service_pricing
        WHERE service = 'transcription'
        AND model = REPLACE(v_model_prefix, '-realtime', '/input')
        AND is_active = TRUE;
        
        SELECT cost_per_unit INTO v_output_token_cost
        FROM public.service_pricing
        WHERE service = 'transcription'
        AND model = REPLACE(v_model_prefix, '-realtime', '/output')
        AND is_active = TRUE;
        
        -- Calculate total cost based on token usage
        v_total_token_cost := (v_input_tokens * v_input_token_cost) + 
                           (v_output_tokens * v_output_token_cost);
        
        v_cost := v_total_token_cost;
        v_metadata := jsonb_build_object(
            'sessionId', p_session_id,
            'startTime', p_start_time,
            'endTime', v_end_time,
            'durationMs', v_session_duration_ms,
            'audioBytesSent', p_audio_bytes_sent,
            'hasTranscription', p_has_transcription,
            'token_based', true,
            'input_tokens', v_input_tokens,
            'output_tokens', v_output_tokens,
            'input_cost', ROUND(v_input_tokens * v_input_token_cost, 9),
            'output_cost', ROUND(v_output_tokens * v_output_token_cost, 9)
        );
    ELSE
        -- Use standard duration-based pricing
        v_cost_per_unit := v_service_pricing.cost_per_unit;
        v_cost := v_cost_per_unit * v_session_duration_minutes;
        
        v_metadata := jsonb_build_object(
            'sessionId', p_session_id,
            'startTime', p_start_time,
            'endTime', v_end_time,
            'durationMs', v_session_duration_ms,
            'audioBytesSent', p_audio_bytes_sent,
            'hasTranscription', p_has_transcription
        );
    END IF;

    -- Check if user has subscription or credits
    SELECT 
        pr.pricing_model,
        CASE
            WHEN pr.pricing_model = 'subscription' THEN
                GREATEST(0, q.allowed_amount - q.used_amount)
            WHEN pr.pricing_model = 'credits' THEN
                c.balance
            ELSE 0
        END AS available,
        pr.pricing_model = 'credits' AS is_credits
    INTO v_query_result 
    FROM 
        public.check_usage_allowance(p_user_id, 'transcription', v_model_prefix, 1, p_api_key_id, false) ca
    JOIN
        public.pricing_results pr ON pr.id = ca.pricing_result_id
    LEFT JOIN
        public.quotas q ON q.subscription_id = pr.subscription_id AND q.service = 'transcription'
    LEFT JOIN
        public.credits c ON c.user_id = p_user_id
    WHERE ca.can_use = TRUE
    ORDER BY
        CASE pr.pricing_model
            WHEN 'subscription' THEN 1
            WHEN 'credits' THEN 2
            WHEN 'payg' THEN 3
            ELSE 4
        END
    LIMIT 1;

    -- Set pricing model based on query result or default to credits
    v_pricing_model := COALESCE(v_query_result.pricing_model, 'credits');
    
    -- Update usage history with final details
    UPDATE public.usage_history
SET 
    status = 'success',
    amount = v_session_duration_minutes,
    cost = ROUND(v_cost, 9),
    metadata = v_metadata,
    pricing_model = v_pricing_model
WHERE 
    id = (
        SELECT id
        FROM public.usage_history
        WHERE 
            user_id = p_user_id
            AND service = 'transcription'
            AND model = v_model_prefix || '-realtime'
            AND status = 'pending'
        ORDER BY created_at DESC
        LIMIT 1
    );

    -- Handle negative balances for credit users when using token-based billing
    IF v_is_token_based AND v_pricing_model = 'credits' AND p_token_info IS NOT NULL THEN
        v_available_credit := COALESCE(v_query_result.available, 0);
        
        -- If cost exceeds available credits, create a negative balance record
        IF v_cost > v_available_credit THEN
            INSERT INTO public.negative_balances (
                user_id,
                service,
                model,
                amount,
                usage_history_id
            )
            SELECT 
                p_user_id,
                'transcription',
                v_model_prefix,
                ROUND(v_cost - v_available_credit, 9),
                id
            FROM
                public.usage_history
            WHERE 
                user_id = p_user_id
                AND service = 'transcription'
                AND model = v_model_prefix || '-realtime'
                AND status = 'success'
                AND (metadata->>'sessionId')::text = p_session_id
            ORDER BY created_at DESC
            LIMIT 1;
            
            -- Only deduct available credits, not more
            UPDATE public.credits
            SET balance = 0
            WHERE user_id = p_user_id AND balance = v_available_credit;
        ELSE
            -- Regular credit deduction
            UPDATE public.credits
            SET balance = ROUND(balance - v_cost, 9)
            WHERE user_id = p_user_id;
        END IF;
    ELSE
        -- Handle billing based on pricing model
        CASE v_pricing_model
            WHEN 'credits' THEN
                UPDATE public.credits
                SET balance = ROUND(balance - v_cost, 9)
                WHERE user_id = p_user_id;
                
            WHEN 'subscription' THEN
                UPDATE public.quotas q
                SET used_amount = ROUND(used_amount + v_session_duration_minutes, 9)
                FROM public.user_subscriptions us
                WHERE q.subscription_id = us.id
                AND us.user_id = p_user_id
                AND q.service = 'transcription'
                AND us.status = 'active'
                AND q.reset_date > NOW();
                
            WHEN 'payg' THEN
                INSERT INTO public.payg_usage (
                    user_id, 
                    month, 
                    total_amount, 
                    payment_status
                ) VALUES (
                    p_user_id, 
                    date_trunc('month', CURRENT_DATE), 
                    v_cost,
                    'pending'
                )
                ON CONFLICT (user_id, month) 
                DO UPDATE SET 
                    total_amount = ROUND(payg_usage.total_amount + EXCLUDED.total_amount, 9),
                    payment_status = 
                        CASE 
                            WHEN payg_usage.payment_status = 'paid' THEN 'pending'
                            ELSE payg_usage.payment_status
                        END;
        END CASE;
    END IF;
END;
$$;
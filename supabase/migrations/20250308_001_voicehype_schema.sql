-- VoiceHype Core Schema v1
BEGIN;

-- Extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Users & Profiles
CREATE TABLE public.profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT NOT NULL,
    full_name TEXT,
    company_name TEXT,
    stripe_customer_id TEXT,
    default_pricing_model TEXT CHECK (default_pricing_model IN ('credits', 'payg', 'subscription')),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Pricing Models
CREATE TABLE public.pricing_models (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL UNIQUE,
    type TEXT NOT NULL CHECK (type IN ('payg', 'credits', 'subscription')),
    description TEXT,
    priority SMALLINT NOT NULL DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Service Pricing
CREATE TABLE public.service_pricing (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    service TEXT NOT NULL CHECK (service IN ('transcription', 'optimization')),
    model TEXT NOT NULL,
    cost_per_unit DECIMAL(12, 6) NOT NULL,
    unit TEXT NOT NULL,
    min_purchase DECIMAL(12, 2),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- API Keys (with pricing model fallback)
CREATE TABLE public.api_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    key_prefix TEXT NOT NULL,
    key_hash TEXT NOT NULL,
    allow_credits BOOLEAN DEFAULT TRUE,
    allow_payg BOOLEAN DEFAULT FALSE,
    allow_subscriptions BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    last_used_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ
);

-- User Credits
CREATE TABLE public.credits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    balance DECIMAL(12, 6) NOT NULL DEFAULT 0,
    currency TEXT NOT NULL DEFAULT 'USD',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Subscription Plans
CREATE TABLE public.subscription_plans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    monthly_price DECIMAL(12, 2) NOT NULL,
    annual_price DECIMAL(12, 2),
    transcription_minutes INTEGER NOT NULL,
    input_tokens INTEGER NOT NULL,
    output_tokens INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- User Subscriptions
CREATE TABLE public.user_subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    plan_id UUID NOT NULL REFERENCES public.subscription_plans(id),
    status TEXT NOT NULL CHECK (status IN ('active', 'canceled', 'paused')),
    current_period_start TIMESTAMPTZ NOT NULL,
    current_period_end TIMESTAMPTZ NOT NULL,
    stripe_subscription_id TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Usage Quotas (for subscriptions)
CREATE TABLE public.quotas (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    subscription_id UUID NOT NULL REFERENCES public.user_subscriptions(id) ON DELETE CASCADE,
    service TEXT NOT NULL CHECK (service IN ('transcription', 'optimization')),
    used_amount INTEGER NOT NULL DEFAULT 0,
    total_amount INTEGER NOT NULL,
    reset_date TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- PAYG Usage Aggregation
CREATE TABLE public.payg_usage (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    month DATE NOT NULL,
    total_amount DECIMAL(12, 2) NOT NULL DEFAULT 0,
    is_billed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Usage History
CREATE TABLE public.usage_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    api_key_id UUID REFERENCES public.api_keys(id) ON DELETE SET NULL,
    service TEXT NOT NULL CHECK (service IN ('transcription', 'optimization')),
    model TEXT NOT NULL,
    amount DECIMAL(12, 6) NOT NULL,
    cost DECIMAL(12, 6) NOT NULL,
    pricing_model TEXT NOT NULL CHECK (pricing_model IN ('credits', 'payg', 'subscription')),
    status TEXT NOT NULL CHECK (status IN ('pending', 'success', 'failed')),
    metadata JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_api_keys_user ON public.api_keys(user_id);
CREATE INDEX idx_credits_user ON public.credits(user_id);
CREATE INDEX idx_subscriptions_user ON public.user_subscriptions(user_id);
CREATE INDEX idx_quotas_reset_date ON public.quotas(reset_date);

-- Security Policies
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.api_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.credits ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.quotas ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payg_usage ENABLE ROW LEVEL SECURITY;

-- Individual table policies
CREATE POLICY "Profiles access" ON public.profiles
    FOR ALL
    USING (auth.uid() = id);

CREATE POLICY "API Keys access" ON public.api_keys
    FOR ALL
    USING (auth.uid() = user_id);

CREATE POLICY "Credits access" ON public.credits
    FOR ALL
    USING (auth.uid() = user_id);

CREATE POLICY "Subscriptions access" ON public.user_subscriptions
    FOR ALL
    USING (auth.uid() = user_id);

CREATE POLICY "Quotas access" ON public.quotas
    FOR ALL
    USING (auth.uid() = user_id);

CREATE POLICY "PAYG access" ON public.payg_usage
    FOR ALL
    USING (auth.uid() = user_id);

-- Helper Functions
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, email)
    VALUES (NEW.id, NEW.email);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Pricing Logic
CREATE OR REPLACE FUNCTION check_usage_allowance(p_user_id UUID, p_service TEXT, p_model TEXT, p_amount DECIMAL, p_api_key_id UUID)
RETURNS TABLE (
    can_use BOOLEAN,
    pricing_model TEXT,
    cost DECIMAL,
    max_output_tokens INTEGER
) AS $$
DECLARE
    subscription_available BOOLEAN;
    credit_balance DECIMAL;
    service_cost DECIMAL;
    payg_allowed BOOLEAN;
    input_cost_per_unit DECIMAL;
    output_cost_per_unit DECIMAL;
    max_tokens INTEGER;
BEGIN
    -- Get service cost
    SELECT cost_per_unit * p_amount INTO service_cost
    FROM public.service_pricing sp
    WHERE sp.service = p_service
    AND sp.model = p_model
    AND sp.is_active = TRUE;

    -- Check subscription quota first
    SELECT EXISTS (
        SELECT 1 FROM public.quotas q
        JOIN public.user_subscriptions us ON q.subscription_id = us.id
        WHERE us.user_id = p_user_id
        AND q.service = p_service
        AND (q.used_amount + p_amount) <= q.total_amount
        AND us.status = 'active'
        AND q.reset_date > NOW()
    ) INTO subscription_available;

    IF subscription_available THEN 
        RETURN QUERY SELECT 
            TRUE as can_use,
            'subscription'::TEXT as pricing_model,
            service_cost as cost,
            4096 as max_output_tokens; -- Default max tokens for subscription
        RETURN;
    END IF;

    -- Check credit balance
    SELECT balance INTO credit_balance
    FROM public.credits
    WHERE user_id = p_user_id;

    -- Special handling for optimization service (LLM calls)
    IF p_service = 'optimization' THEN
        -- Try to get separate input/output pricing
        SELECT sp.cost_per_unit INTO input_cost_per_unit
        FROM public.service_pricing sp
        WHERE sp.service = 'optimization'
        AND sp.model = p_model || '/input'
        AND sp.is_active = TRUE;

        SELECT sp.cost_per_unit INTO output_cost_per_unit
        FROM public.service_pricing sp
        WHERE sp.service = 'optimization'
        AND sp.model = p_model || '/output'
        AND sp.is_active = TRUE;

        -- If we have separate input/output pricing
        IF input_cost_per_unit IS NOT NULL AND output_cost_per_unit IS NOT NULL THEN
            -- Calculate input tokens cost
            service_cost := input_cost_per_unit * p_amount;
            
            -- If user has any credits at all
            IF credit_balance > 0 THEN
                -- Calculate how many output tokens they can afford
                -- First subtract the cost of input tokens
                IF credit_balance > service_cost THEN
                    -- Remaining credits after input tokens
                    credit_balance := credit_balance - service_cost;
                    
                    -- Calculate max output tokens they can afford
                    max_tokens := FLOOR(credit_balance / output_cost_per_unit);
                    
                    -- Cap at a reasonable maximum (4096 is common for many models)
                    max_tokens := LEAST(max_tokens, 4096);
                    
                    RETURN QUERY SELECT 
                        TRUE as can_use,
                        'credits'::TEXT as pricing_model,
                        service_cost as cost, -- This is just the input cost for now
                        max_tokens as max_output_tokens;
                    RETURN;
                END IF;
            END IF;
        ELSE
            -- If we don't have separate pricing, use the combined pricing
            -- If user has any credits at all and they can cover at least the input tokens
            IF credit_balance > 0 THEN
                -- Calculate max tokens they can afford (input + output)
                max_tokens := FLOOR(credit_balance / service_cost) - p_amount;
                
                -- If they can afford at least some output tokens
                IF max_tokens > 0 THEN
                    -- Cap at a reasonable maximum
                    max_tokens := LEAST(max_tokens, 4096);
                    
                    RETURN QUERY SELECT 
                        TRUE as can_use,
                        'credits'::TEXT as pricing_model,
                        service_cost as cost,
                        max_tokens as max_output_tokens;
                    RETURN;
                END IF;
            END IF;
        END IF;
    ELSE
        -- For non-optimization services (like transcription), use the original logic
        IF credit_balance >= service_cost THEN 
            RETURN QUERY SELECT 
                TRUE as can_use,
                'credits'::TEXT as pricing_model,
                service_cost as cost,
                NULL as max_output_tokens;
            RETURN;
        END IF;
    END IF;

    -- Check if PAYG is allowed for this API key
    SELECT allow_payg INTO payg_allowed
    FROM public.api_keys
    WHERE id = p_api_key_id
    AND is_active = TRUE;

    -- Fallback to PAYG if allowed and user has stripe customer id
    IF payg_allowed AND EXISTS (
        SELECT 1 FROM public.profiles
        WHERE id = p_user_id
        AND stripe_customer_id IS NOT NULL
    ) THEN
        -- No longer inserting into payg_usage here, will be done in finalize_usage

        RETURN QUERY SELECT 
            TRUE as can_use,
            'payg'::TEXT as pricing_model,
            service_cost as cost,
            4096 as max_output_tokens; -- Default max tokens for PAYG
        RETURN;
    END IF;

    -- If we get here, no payment method is available
    RETURN QUERY SELECT 
        FALSE as can_use,
        NULL::TEXT as pricing_model,
        service_cost as cost,
        NULL as max_output_tokens;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to finalize usage and update balances
CREATE OR REPLACE FUNCTION finalize_usage(
    p_user_id UUID,
    p_service TEXT,
    p_model TEXT,
    p_amount DECIMAL,
    p_cost DECIMAL,
    p_pricing_model TEXT,
    p_metadata JSONB
) RETURNS VOID AS $$
BEGIN
    -- Update usage history to success
    UPDATE public.usage_history
    SET status = 'success',
        amount = p_amount,
        cost = p_cost,
        metadata = p_metadata
    WHERE user_id = p_user_id
    AND service = p_service
    AND model = p_model
    AND status = 'pending'
    ORDER BY created_at DESC
    LIMIT 1;

    -- Update credits if using credits pricing model
    IF p_pricing_model = 'credits' THEN
        UPDATE public.credits
        SET balance = balance - p_cost
        WHERE user_id = p_user_id;
    END IF;

    -- Update quota if using subscription
    IF p_pricing_model = 'subscription' THEN
        UPDATE public.quotas q
        SET used_amount = used_amount + p_amount
        FROM public.user_subscriptions us
        WHERE q.subscription_id = us.id
        AND us.user_id = p_user_id
        AND q.service = p_service
        AND us.status = 'active'
        AND q.reset_date > NOW();
    END IF;

    -- Handle PAYG usage recording
    IF p_pricing_model = 'payg' THEN
        INSERT INTO public.payg_usage (user_id, month, total_amount)
        VALUES (
            p_user_id, 
            date_trunc('month', CURRENT_DATE), 
            p_cost
        )
        ON CONFLICT (user_id, month) 
        DO UPDATE SET total_amount = payg_usage.total_amount + EXCLUDED.total_amount;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a secure function to validate API keys
CREATE OR REPLACE FUNCTION validate_api_key(p_key TEXT) 
RETURNS TABLE (
    user_id UUID,
    api_key_id UUID
) AS $$
DECLARE
    v_key_prefix TEXT;
    v_key_hash TEXT;
BEGIN
    -- Extract prefix (format is vhkey_XXXXX)
    v_key_prefix := substring(p_key from 7 for 4);
    
    -- Hash the key
    v_key_hash := encode(digest(p_key, 'sha256'), 'hex');
    
    -- Update last used time
    UPDATE public.api_keys
    SET last_used_at = NOW()
    WHERE key_prefix = v_key_prefix
    AND key_hash = v_key_hash
    AND is_active = TRUE
    AND (expires_at IS NULL OR expires_at > NOW());
    
    -- Return user_id if key is valid
    RETURN QUERY
    SELECT a.user_id, a.id
    FROM public.api_keys a
    WHERE a.key_prefix = v_key_prefix
    AND a.key_hash = v_key_hash
    AND a.is_active = TRUE
    AND (a.expires_at IS NULL OR a.expires_at > NOW());
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Initial Data
INSERT INTO public.pricing_models (name, type, description, priority) VALUES
    ('Starter Subscription', 'subscription', 'Basic monthly plan', 1),
    ('Developer Credits', 'credits', 'Pre-paid credits', 2),
    ('PAYG', 'payg', 'Pay-as-you-go', 3);

INSERT INTO public.subscription_plans (
    name, description, monthly_price, transcription_minutes, input_tokens, output_tokens
) VALUES
    ('Basic', '200 mins transcription, 50k tokens', 7, 200, 50000, 50000),
    ('Pro', '1000 mins transcription, 250k tokens', 25, 1000, 250000, 250000);

INSERT INTO public.service_pricing (service, model, cost_per_unit, unit) VALUES
    ('transcription', 'large-v3', 0.02, 'minute'),
    ('optimization', 'llama-3-8b', 0.00015, 'token');

COMMIT; 
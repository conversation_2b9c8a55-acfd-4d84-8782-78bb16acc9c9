-- Migration: Add updated_at column and past_due status to user_subscriptions
-- This migration adds the missing updated_at column and updates the status constraint

-- Add updated_at column to user_subscriptions table
ALTER TABLE public.user_subscriptions 
ADD COLUMN updated_at TIMESTAMPTZ DEFAULT NOW();

-- Update existing records to have updated_at = created_at
UPDATE public.user_subscriptions 
SET updated_at = created_at 
WHERE updated_at IS NULL;

-- Make updated_at NOT NULL after setting initial values
ALTER TABLE public.user_subscriptions 
ALTER COLUMN updated_at SET NOT NULL;

-- Drop the old status check constraint
ALTER TABLE public.user_subscriptions 
DROP CONSTRAINT user_subscriptions_status_check;

-- Add new status check constraint that includes 'past_due'
ALTER TABLE public.user_subscriptions 
ADD CONSTRAINT user_subscriptions_status_check 
CHECK (status = ANY(ARRAY['active'::text, 'canceled'::text, 'paused'::text, 'past_due'::text]));

-- Create trigger to automatically update updated_at on row updates
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
R<PERSON>URNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply trigger to user_subscriptions table
CREATE TRIGGER update_user_subscriptions_updated_at
    BEFORE UPDATE ON public.user_subscriptions
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- Add comment for documentation
COMMENT ON COLUMN public.user_subscriptions.updated_at IS 'Timestamp when the subscription record was last updated';
COMMENT ON TRIGGER update_user_subscriptions_updated_at ON public.user_subscriptions IS 'Automatically updates updated_at column on row changes';

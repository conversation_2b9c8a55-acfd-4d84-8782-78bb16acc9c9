-- Remove unique constraint on credits.user_id
-- This allows multiple credit entries for a single user, each with its own expiration date

-- Check if the constraint exists before attempting to drop it
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 
        FROM information_schema.constraint_column_usage 
        WHERE table_name = 'credits' AND constraint_name = 'credits_user_id_key'
    ) THEN
        -- Drop the unique constraint on user_id
        ALTER TABLE public.credits DROP CONSTRAINT credits_user_id_key;
        RAISE NOTICE 'Removed unique constraint on credits.user_id';
    ELSE
        RAISE NOTICE 'No unique constraint found on credits.user_id';
    END IF;
    
    -- Create an index on user_id if it doesn't exist, to maintain good query performance
    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE tablename = 'credits' AND indexname = 'idx_credits_user_id'
    ) THEN
        CREATE INDEX idx_credits_user_id ON public.credits(user_id);
        RAISE NOTICE 'Created index on credits.user_id for better query performance';
    ELSE
        RAISE NOTICE 'Index idx_credits_user_id already exists';
    END IF;
END $$;

-- Update comments to reflect the change
COMMENT ON TABLE public.credits IS 'Stores user credit balances. Each row represents a separate purchase with its own expiration date.';

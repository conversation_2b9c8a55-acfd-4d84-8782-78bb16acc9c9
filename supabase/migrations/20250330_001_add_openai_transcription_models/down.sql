-- Revert OpenAI transcription model additions
BEGIN;

-- Remove the new models and their token-based pricing entries
DELETE FROM public.service_pricing
WHERE model IN (
    'gpt-4o-mini-transcribe',
    'gpt-4o-transcribe',
    'gpt-4o-mini-transcribe/input',
    'gpt-4o-mini-transcribe/output',
    'gpt-4o-transcribe/input',
    'gpt-4o-transcribe/output'
);

-- Only drop the constraint if we've explicitly added it in the up migration
-- and only if it exists
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 
        FROM pg_constraint 
        WHERE conname = 'service_pricing_service_model_key'
    ) THEN
        ALTER TABLE public.service_pricing
        DROP CONSTRAINT IF EXISTS service_pricing_service_model_key;
    END IF;
END $$;

COMMIT; 
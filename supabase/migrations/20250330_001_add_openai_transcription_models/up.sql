-- Add new OpenAI transcription models
BEGIN;

-- Add unique constraint on service and model columns if it doesn't exist
DO $$
BEGIN
    -- Check if the constraint already exists
    IF NOT EXISTS (
        SELECT 1 
        FROM pg_constraint 
        WHERE conname = 'service_pricing_service_model_key'
    ) THEN
        -- Add the unique constraint
        ALTER TABLE public.service_pricing
        ADD CONSTRAINT service_pricing_service_model_key UNIQUE (service, model);
    END IF;
END $$;

-- Declare variables for pricing
DO $$
DECLARE
    -- Base costs for the models (per 1M tokens)
    gpt_4o_mini_transcribe_input_cost DECIMAL(18, 9) := 1.25;
    gpt_4o_mini_transcribe_output_cost DECIMAL(18, 9) := 5.00;
    gpt_4o_transcribe_input_cost DECIMAL(18, 9) := 2.50;
    gpt_4o_transcribe_output_cost DECIMAL(18, 9) := 10.00;
    
    -- Apply a profit margin (adjust as needed)
    profit_margin DECIMAL(18, 9) := 1.2; -- 20% markup
BEGIN
    -- Insert the GPT-4o models using token-based pricing with slash notation
    -- Note: These use 'token' as unit instead of 'minute'
    INSERT INTO public.service_pricing (service, model, cost_per_unit, unit, is_active)
    VALUES
      -- GPT-4o-mini-transcribe input/output tokens
      ('transcription', 'gpt-4o-mini-transcribe/input', 
       gpt_4o_mini_transcribe_input_cost * profit_margin / 1000000, 'token', true),
      ('transcription', 'gpt-4o-mini-transcribe/output', 
       gpt_4o_mini_transcribe_output_cost * profit_margin / 1000000, 'token', true),
       
      -- GPT-4o-transcribe input/output tokens
      ('transcription', 'gpt-4o-transcribe/input', 
       gpt_4o_transcribe_input_cost * profit_margin / 1000000, 'token', true),
      ('transcription', 'gpt-4o-transcribe/output', 
       gpt_4o_transcribe_output_cost * profit_margin / 1000000, 'token', true);
       
    -- Insert model identifiers (these records act as "pointers" to the token-based pricing)
    -- These entries allow the UI to show the model names
    -- INSERT INTO public.service_pricing (service, model, cost_per_unit, unit, is_active)
    -- VALUES
    --   ('transcription', 'gpt-4o-mini-transcribe', 0, 'token-based', true),
    --   ('transcription', 'gpt-4o-transcribe', 0, 'token-based', true);
END $$;

-- Update the finalize_usage function to handle token-based billing for GPT-4o transcription models
CREATE OR REPLACE FUNCTION public.finalize_usage(
    p_user_id UUID,
    p_service TEXT,
    p_model TEXT,
    p_amount NUMERIC,
    p_cost NUMERIC,
    p_pricing_model TEXT,
    p_metadata JSONB
) RETURNS VOID AS $$
DECLARE
    input_token_count INTEGER;
    output_token_count INTEGER;
    input_cost_per_token DECIMAL(18, 9);
    output_cost_per_token DECIMAL(18, 9);
    total_token_cost DECIMAL(18, 9);
    is_token_based BOOLEAN;
BEGIN
    -- Check if this is a token-based model by looking at the unit
    SELECT EXISTS (
        SELECT 1 FROM public.service_pricing
        WHERE service = p_service
        AND model = p_model
        AND unit = 'token-based'
    ) INTO is_token_based;
    
    -- Handle token-based billing for GPT-4o transcription models
    IF is_token_based AND p_service = 'transcription' AND (
        p_model = 'gpt-4o-mini-transcribe' OR 
        p_model = 'gpt-4o-transcribe'
    ) THEN
        -- Extract token counts from metadata
        input_token_count := COALESCE((p_metadata->>'input_tokens')::INTEGER, 0);
        output_token_count := COALESCE((p_metadata->>'output_tokens')::INTEGER, 0);
        
        -- Get token costs for this model
        SELECT cost_per_unit INTO input_cost_per_token
        FROM public.service_pricing
        WHERE service = 'transcription'
        AND model = p_model || '/input'
        AND is_active = true;
        
        SELECT cost_per_unit INTO output_cost_per_token
FROM public.service_pricing 
        WHERE service = 'transcription'
        AND model = p_model || '/output'
        AND is_active = true;
        
        -- Calculate total cost based on token usage
        total_token_cost := (input_token_count * input_cost_per_token) + 
                            (output_token_count * output_cost_per_token);
        
        -- Update usage history with token information and calculated cost
        UPDATE public.usage_history
        SET status = 'success',
            amount = p_amount, -- Original amount (duration) for compatibility
            cost = ROUND(total_token_cost, 9),
            metadata = p_metadata || jsonb_build_object(
                'token_based', true,
                'input_tokens', input_token_count,
                'output_tokens', output_token_count,
                'input_cost', ROUND(input_token_count * input_cost_per_token, 9),
                'output_cost', ROUND(output_token_count * output_cost_per_token, 9)
            )
        WHERE id = (
            SELECT id 
            FROM public.usage_history 
            WHERE user_id = p_user_id
            AND service = p_service
            AND model = p_model
            AND status = 'pending'
            ORDER BY created_at DESC
            LIMIT 1
        );
        
        -- Update credits if using credits pricing model
        IF p_pricing_model = 'credits' THEN
            UPDATE public.credits
            SET balance = ROUND(balance - total_token_cost, 9)
            WHERE user_id = p_user_id;
        END IF;
        
        -- Handle PAYG usage recording
        IF p_pricing_model = 'payg' THEN
            INSERT INTO public.payg_usage (
                user_id, 
                month, 
                total_amount, 
                payment_status
            ) VALUES (
                p_user_id, 
                date_trunc('month', CURRENT_DATE), 
                total_token_cost,
                'pending'
            )
            ON CONFLICT (user_id, month) 
            DO UPDATE SET 
                total_amount = ROUND(payg_usage.total_amount + EXCLUDED.total_amount, 9),
                payment_status = 
                    -- Only change to pending if it was previously paid
                    CASE 
                        WHEN payg_usage.payment_status = 'paid' THEN 'pending'
                        ELSE payg_usage.payment_status
                    END;
        END IF;
    ELSE
        -- Use standard logic for non-token-based models
        -- Update usage history to success using a subquery to handle ORDER BY and LIMIT
        UPDATE public.usage_history
        SET status = 'success',
            amount = p_amount,
            cost = ROUND(p_cost, 9),
            metadata = p_metadata
        WHERE id = (
            SELECT id 
            FROM public.usage_history 
            WHERE user_id = p_user_id
            AND service = p_service
            AND model = p_model
            AND status = 'pending'
            ORDER BY created_at DESC
            LIMIT 1
        );

        -- Update credits if using credits pricing model
        IF p_pricing_model = 'credits' THEN
            UPDATE public.credits
            SET balance = ROUND(balance - p_cost, 9)
            WHERE user_id = p_user_id;
        END IF;

        -- Update quota if using subscription
        IF p_pricing_model = 'subscription' THEN
            UPDATE public.quotas q
            SET used_amount = ROUND(used_amount + p_amount, 9)
            FROM public.user_subscriptions us
            WHERE q.subscription_id = us.id
            AND us.user_id = p_user_id
            AND q.service = p_service
            AND us.status = 'active'
            AND q.reset_date > NOW();
        END IF;

        -- Handle PAYG usage recording with new payment_status
        IF p_pricing_model = 'payg' THEN
            INSERT INTO public.payg_usage (
                user_id, 
                month, 
                total_amount, 
                payment_status
            ) VALUES (
                p_user_id, 
                date_trunc('month', CURRENT_DATE), 
                p_cost,
                'pending'
            )
            ON CONFLICT (user_id, month) 
            DO UPDATE SET 
                total_amount = ROUND(payg_usage.total_amount + EXCLUDED.total_amount, 9),
                payment_status = 
                    -- Only change to pending if it was previously paid
                    CASE 
                        WHEN payg_usage.payment_status = 'paid' THEN 'pending'
                        ELSE payg_usage.payment_status
                    END;
        END IF;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMIT; 
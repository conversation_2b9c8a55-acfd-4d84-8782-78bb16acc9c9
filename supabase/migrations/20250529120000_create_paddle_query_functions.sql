-- Create functions to query paddle schema tables
CREATE OR REPLACE FUNCTION public.query_paddle_products(product_type text, is_active_status boolean)
RETURNS SETOF paddle.products
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT * FROM paddle.products 
  WHERE type = product_type 
  AND is_active = is_active_status;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION public.query_paddle_customers(user_id_param uuid)
RETURNS SETOF paddle.customers
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT * FROM paddle.customers 
  WHERE user_id = user_id_param;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION public.query_paddle_transactions(user_id_param uuid, transaction_status text DEFAULT NULL)
RETURNS SETOF paddle.transactions
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  IF transaction_status IS NULL THEN
    RETURN QUERY
    SELECT * FROM paddle.transactions 
    WHERE user_id = user_id_param;
  ELSE
    RETURN QUERY
    SELECT * FROM paddle.transactions 
    WHERE user_id = user_id_param
    AND status = transaction_status;
  END IF;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION public.query_paddle_webhooks(event_id_param text)
RETURNS SETOF paddle.webhooks
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT * FROM paddle.webhooks 
  WHERE paddle_event_id = event_id_param;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION public.create_paddle_webhook_log(
  p_event_id text,
  p_event_type text,
  p_payload jsonb,
  p_processed boolean
)
RETURNS paddle.webhooks
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  webhook_record paddle.webhooks;
BEGIN
  INSERT INTO paddle.webhooks (
    paddle_event_id,
    event_type,
    raw_payload,
    processed
  )
  VALUES (
    p_event_id,
    p_event_type,
    p_payload,
    p_processed
  )
  RETURNING * INTO webhook_record;
  
  RETURN webhook_record;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION public.update_paddle_webhook_status(
  p_event_id text,
  p_processed boolean,
  p_error text
)
RETURNS paddle.webhooks
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  webhook_record paddle.webhooks;
BEGIN
  UPDATE paddle.webhooks
  SET 
    processed = p_processed,
    processing_error = p_error,
    processed_at = NOW()
  WHERE paddle_event_id = p_event_id
  RETURNING * INTO webhook_record;
  
  RETURN webhook_record;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION public.update_paddle_transaction(
  p_transaction_id text,
  p_status text,
  p_metadata jsonb
)
RETURNS paddle.transactions
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  transaction_record paddle.transactions;
BEGIN
  UPDATE paddle.transactions
  SET 
    status = p_status,
    metadata = p_metadata,
    updated_at = NOW()
  WHERE paddle_transaction_id = p_transaction_id
  RETURNING * INTO transaction_record;
  
  RETURN transaction_record;
END;
$$ LANGUAGE plpgsql;

-- Create function to store customer information
CREATE OR REPLACE FUNCTION public.store_paddle_customer_info(
  p_user_id uuid,
  p_email text,
  p_name text
)
RETURNS paddle.customers
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  customer_record paddle.customers;
BEGIN
  -- Check if customer exists
  SELECT * INTO customer_record
  FROM paddle.customers
  WHERE user_id = p_user_id;
  
  IF customer_record.id IS NULL THEN
    -- Create new customer
    INSERT INTO paddle.customers (
      user_id,
      email,
      name,
      created_at,
      updated_at
    )
    VALUES (
      p_user_id,
      p_email,
      p_name,
      NOW(),
      NOW()
    )
    RETURNING * INTO customer_record;
  ELSE
    -- Update existing customer
    UPDATE paddle.customers
    SET
      email = COALESCE(p_email, email),
      name = COALESCE(p_name, name),
      updated_at = NOW()
    WHERE user_id = p_user_id
    RETURNING * INTO customer_record;
  END IF;
  
  RETURN customer_record;
END;
$$ LANGUAGE plpgsql;

# API Key Display Fix

## Issue

When creating a new API key, the application was showing an incorrect key (possibly a random UUID) instead of the actual API key generated by the database. This happened because the `create_api_key` function in the database was only returning the ID of the newly created API key, not the actual key secret that users need to use in their API calls.

## Solution

We've created a migration file to fix this issue:

### Migration File: `20250312_002_fix_api_key_return.sql`

This migration:
1. Creates a new composite type `api_key_result` that includes both the ID and the key secret
2. Updates the `create_api_key` function to return this composite type
3. Ensures the actual key secret generated in the function is returned to the client

## Client-Side Changes

We've also updated the client-side code to handle the new return type:

1. Updated the `createApiKey` function in `src/lib/supabase.ts` to handle the new return type
2. Updated the `CreateApiKeyModal.vue` component to use the key_secret from the response

## How to Apply the Migration

### Option 1: Using Supabase CLI

If you have the Supabase CLI installed:

```bash
# Navigate to your project directory
cd /path/to/your/project

# Apply the migration
supabase db push
```

### Option 2: Using the Supabase Dashboard

1. Log in to your Supabase dashboard
2. Go to the SQL Editor
3. Copy the contents of the migration file
4. Paste it into a new SQL query
5. Run the query

## Verifying the Fix

After applying the migration, try creating a new API key. The key displayed in the success modal should now be the actual API key that can be used to authenticate API requests, not just a UUID.

## Troubleshooting

If you continue to experience issues after applying the migration:

1. Check the Supabase logs for any error messages
2. Verify that the migration was applied successfully by checking if the `api_key_result` type exists
3. Test the `create_api_key` function directly in the SQL Editor

If problems persist, please contact the development team for assistance. 
-- Combine input_tokens and output_tokens columns into a single tokens column
BEGIN;

-- 1. Add the new tokens column to the subscription_plans table
ALTER TABLE public.subscription_plans
ADD COLUMN tokens integer;

-- 2. Update the tokens column with the sum of input_tokens and output_tokens
UPDATE public.subscription_plans
SET tokens = input_tokens + output_tokens;

-- 3. Make the tokens column NOT NULL after populating it
ALTER TABLE public.subscription_plans
ALTER COLUMN tokens SET NOT NULL;

-- 4. Drop the old columns
ALTER TABLE public.subscription_plans
DROP COLUMN input_tokens,
DROP COLUMN output_tokens;

COMMIT; 
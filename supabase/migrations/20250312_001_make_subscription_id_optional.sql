-- Make subscription_id optional in quotas table
BEGIN;

-- First, drop the existing foreign key constraint
ALTER TABLE public.quotas
DROP CONSTRAINT IF EXISTS quotas_subscription_id_fkey;

-- Then modify the column to be nullable
ALTER TABLE public.quotas
ALTER COLUMN subscription_id DROP NOT NULL;

-- Re-add the foreign key constraint that allows NULL values
ALTER TABLE public.quotas
ADD CONSTRAINT quotas_subscription_id_fkey
FOREIGN KEY (subscription_id)
REFERENCES public.user_subscriptions(id)
ON DELETE CASCADE;

-- Update the service check constraint to include input_tokens and output_tokens
ALTER TABLE public.quotas
DROP CONSTRAINT IF EXISTS quotas_service_check;

ALTER TABLE public.quotas
ADD CONSTRAINT quotas_service_check
CHECK (service IN ('transcription', 'optimization', 'input_tokens', 'output_tokens'));

-- Update the handle_new_user function to work with the new schema
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  v_profile_id UUID;
BEGIN
  -- Create a profile for the new user
  INSERT INTO public.profiles (id, email, full_name, created_at, updated_at)
  VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'full_name', NOW(), NOW())
  RETURNING id INTO v_profile_id;
  
  -- Create initial quotas for the new user
  -- 10 minutes of free transcription
  INSERT INTO public.quotas (user_id, subscription_id, service, used_amount, total_amount, reset_date, created_at)
  VALUES (NEW.id, NULL, 'transcription', 0, 10, NOW() + INTERVAL '30 days', NOW());
  
  -- 10,000 input tokens
  INSERT INTO public.quotas (user_id, subscription_id, service, used_amount, total_amount, reset_date, created_at)
  VALUES (NEW.id, NULL, 'optimization', 0, 10000, NOW() + INTERVAL '30 days', NOW());
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.handle_new_user() TO postgres;

COMMIT; 
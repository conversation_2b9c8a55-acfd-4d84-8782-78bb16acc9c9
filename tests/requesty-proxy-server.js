// Simple HTTP/1.1 proxy server for Requesty API
const http = require('http');
const https = require('https');
const url = require('url');

const PORT = 3001;

// Force HTTP/1.1 for all outgoing requests
const httpsAgent = new https.Agent({
  keepAlive: true,
  maxSockets: 10,
  // Force HTTP/1.1
  protocol: 'https:',
  rejectUnauthorized: true
});

// Disable HTTP/2 globally for this process
process.env.NODE_TLS_REJECT_UNAUTHORIZED = '1';

const server = http.createServer((req, res) => {
  // Enable CORS for Deno edge functions
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Source, User-Agent');
  
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  if (req.method !== 'POST') {
    res.writeHead(405, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ error: 'Method not allowed' }));
    return;
  }

  console.log('\n=== New Request ===');
  console.log('Method:', req.method);
  console.log('URL:', req.url);
  console.log('Headers:', req.headers);

  // Collect request body
  let body = '';
  req.on('data', chunk => {
    body += chunk.toString();
  });

  req.on('end', () => {
    try {
      const requestData = JSON.parse(body);
      console.log('Request Data:', {
        model: requestData.model,
        messageCount: requestData.messages?.length,
        maxTokens: requestData.max_tokens,
        hasResponseFormat: !!requestData.response_format
      });

      // Prepare the request to Requesty
      const requestyData = JSON.stringify(requestData);
      
      const options = {
        hostname: 'router.requesty.ai',
        port: 443,
        path: '/v1/chat/completions',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': req.headers.authorization,
          'X-Source': req.headers['x-source'] || 'voicehype-proxy',
          'User-Agent': req.headers['user-agent'] || 'VoiceHype-Proxy/1.0',
          'Content-Length': Buffer.byteLength(requestyData),
          'Connection': 'close' // Force connection close for HTTP/1.1
        },
        agent: httpsAgent,
        // Force HTTP/1.1
        httpVersion: '1.1'
      };

      console.log('Proxying to Requesty with options:', {
        hostname: options.hostname,
        path: options.path,
        headers: options.headers
      });

      const proxyReq = https.request(options, (proxyRes) => {
        console.log('Requesty Response Status:', proxyRes.statusCode);
        console.log('Requesty Response Headers:', proxyRes.headers);

        let responseBody = '';
        let headersSent = false;
        
        proxyRes.on('data', (chunk) => {
          responseBody += chunk;
          
          // Send headers on first data chunk
          if (!headersSent) {
            res.writeHead(proxyRes.statusCode, {
              'Content-Type': proxyRes.headers['content-type'] || 'application/json',
              'Access-Control-Allow-Origin': '*',
              'Content-Length': proxyRes.headers['content-length'] || responseBody.length
            });
            headersSent = true;
          }
        });

        proxyRes.on('end', () => {
          console.log('Response completed. Length:', responseBody.length);
          console.log('First 200 chars:', responseBody.substring(0, 200));
          
          try {
            // Validate it's proper JSON
            const parsed = JSON.parse(responseBody);
            console.log('JSON validation successful:', {
              hasChoices: !!parsed.choices,
              hasUsage: !!parsed.usage,
              model: parsed.model
            });
            
            if (!headersSent) {
              res.writeHead(proxyRes.statusCode, {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
              });
            }
            res.end(responseBody);
          } catch (parseErr) {
            console.error('JSON parse error:', parseErr.message);
            console.error('Raw response:', responseBody);
            
            if (!headersSent) {
              res.writeHead(500, { 
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
              });
            }
            res.end(JSON.stringify({ 
              error: 'Invalid JSON response from Requesty', 
              raw: responseBody.substring(0, 500) 
            }));
          }
        });

        proxyRes.on('error', (err) => {
          console.error('Proxy response error:', err);
          if (!headersSent) {
            res.writeHead(500, { 
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            });
            res.end(JSON.stringify({ error: 'Proxy response error', message: err.message }));
          }
        });
      });

      proxyReq.on('error', (err) => {
        console.error('Proxy request error:', err);
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Proxy request error', message: err.message }));
      });

      proxyReq.on('timeout', () => {
        console.error('Proxy request timeout');
        proxyReq.destroy();
        res.writeHead(408, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Request timeout' }));
      });

      // Set timeout
      proxyReq.setTimeout(30000); // 30 second timeout

      // Send the request
      proxyReq.write(requestyData);
      proxyReq.end();

    } catch (parseErr) {
      console.error('Request parse error:', parseErr);
      res.writeHead(400, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ error: 'Invalid JSON in request body' }));
    }
  });
});

server.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Requesty Proxy Server running on http://localhost:${PORT}`);
  console.log('Ready to proxy requests to router.requesty.ai via HTTP/1.1');
  console.log('Use this endpoint: http://localhost:3001/v1/chat/completions');
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down proxy server...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

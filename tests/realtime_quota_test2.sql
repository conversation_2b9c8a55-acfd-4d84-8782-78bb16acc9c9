-- Test Case 2: Insufficient quota but sufficient credits
DO $$
DECLARE
    test_user_id UUID := '11111111-1111-1111-1111-111111111111';
    test_amount NUMERIC := 1000; -- Minutes
BEGIN
    -- Setup test user with limited quota but sufficient credits
    INSERT INTO public.quotas(user_id, service, subscription_id, total_amount, used_amount)
    VALUES (test_user_id, 'transcription', '*************-2222-2222-************', 500, 0);
    
    INSERT INTO public.credits(user_id, balance, status)
    VALUES (test_user_id, 20, 'active');
    
    -- Execute real-time transcription usage
    PERFORM public.finalize_usage(
        test_user_id,
        'transcription',
        'realtime-v1',
        test_amount,
        0.01, -- Cost per minute
        'hybrid',
        '{"inputTokens": 0, "outputTokens": 0}'::jsonb
    );
    
    -- Verify results
    IF (SELECT used_amount FROM public.quotas 
        WHERE user_id = test_user_id AND service = 'transcription') = 500 AND
       (SELECT balance FROM public.credits 
        WHERE user_id = test_user_id) < 20 THEN
        RAISE NOTICE 'TEST 2 PASSED: Hybrid billing worked correctly';
    ELSE
        RAISE EXCEPTION 'TEST 2 FAILED: Hybrid billing not working';
    END IF;
    
    -- Cleanup
    DELETE FROM public.quotas WHERE user_id = test_user_id;
    DELETE FROM public.credits WHERE user_id = test_user_id;
    DELETE FROM public.usage_history WHERE user_id = test_user_id;
END $$;
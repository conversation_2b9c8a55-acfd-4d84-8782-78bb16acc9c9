#!/bin/bash

API_KEY="sk-ID3J1Ud5RyW36uw0K2H9+SAWcVGEKKCeZvslNywe5ms3Rk2jRgQ0Iu9qJI0bmmy2bxUKDi0IqUR5WY2f7vcZ4J6Fm121mwfM+6e+LpFqvg4="

echo "=== Testing Claude with HTTP/1.1 ==="
curl --http1.1 --connect-timeout 10 --max-time 20 \
  -X POST "https://router.requesty.ai/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_KEY" \
  -d '{"model": "anthropic/claude-3-haiku-20240307", "messages": [{"role": "user", "content": "Hello"}], "max_tokens": 50}' \
  -w "\n\nHTTP Code: %{http_code}\nConnect Time: %{time_connect}s\nTotal Time: %{time_total}s\n"

echo -e "\n\n=== Testing DeepInfra without response_format with HTTP/1.1 ==="
curl --http1.1 --connect-timeout 10 --max-time 20 \
  -X POST "https://router.requesty.ai/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_KEY" \
  -d '{"model": "deepinfra/meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo", "messages": [{"role": "user", "content": "Hello"}], "max_tokens": 50}' \
  -w "\n\nHTTP Code: %{http_code}\nConnect Time: %{time_connect}s\nTotal Time: %{time_total}s\n"

echo -e "\n\n=== Testing DeepInfra with response_format with HTTP/1.1 ==="
curl --http1.1 --connect-timeout 10 --max-time 20 \
  -X POST "https://router.requesty.ai/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_KEY" \
  -d '{"model": "deepinfra/meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo", "messages": [{"role": "user", "content": "Hello"}], "max_tokens": 50, "response_format": {"type": "json_object"}}' \
  -w "\n\nHTTP Code: %{http_code}\nConnect Time: %{time_connect}s\nTotal Time: %{time_total}s\n"

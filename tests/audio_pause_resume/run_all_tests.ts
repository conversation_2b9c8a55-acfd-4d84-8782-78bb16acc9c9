/**
 * Test runner for all pause/resume bug solutions
 * 
 * This script runs all the solution tests and compares the results.
 */

import * as fs from 'fs';
import * as path from 'path';
import { runTest } from './base_test';
import { ExtractAudioDataTest } from './solution1_extract_audio_data';
import { SeparateSegmentsTest } from './solution2_separate_segments';
import { StreamBufferingTest } from './solution3_stream_buffering';
import { RawPcmTest } from './solution4_raw_pcm';
import { SoxOnLinuxTest } from './solution5_sox_on_linux';

// Create output directory for test recordings
const TEST_OUTPUT_DIR = path.join(__dirname, 'output');
if (!fs.existsSync(TEST_OUTPUT_DIR)) {
    fs.mkdirSync(TEST_OUTPUT_DIR, { recursive: true });
}

// Test parameters
const INITIAL_DURATION = 5;  // seconds
const PAUSE_DURATION = 2;    // seconds
const RESUME_DURATION = 10;  // seconds

/**
 * Run all tests sequentially
 */
async function runAllTests() {
    console.log('=== Running all pause/resume bug solution tests ===');
    console.log(`Test parameters: initial=${INITIAL_DURATION}s, pause=${PAUSE_DURATION}s, resume=${RESUME_DURATION}s`);
    console.log('Output directory:', TEST_OUTPUT_DIR);
    
    try {
        // Run Solution 1: Extract Audio Data Without Headers
        console.log('\n=== Solution 1: Extract Audio Data Without Headers ===');
        await runTest(new ExtractAudioDataTest(), INITIAL_DURATION, PAUSE_DURATION, RESUME_DURATION);
        
        // Run Solution 2: Create Separate Segments and Merge
        console.log('\n=== Solution 2: Create Separate Segments and Merge ===');
        await runTest(new SeparateSegmentsTest(), INITIAL_DURATION, PAUSE_DURATION, RESUME_DURATION);
        
        // Run Solution 3: Stream Buffering
        console.log('\n=== Solution 3: Stream Buffering ===');
        await runTest(new StreamBufferingTest(), INITIAL_DURATION, PAUSE_DURATION, RESUME_DURATION);
        
        // Run Solution 4: Use Raw PCM Throughout
        console.log('\n=== Solution 4: Use Raw PCM Throughout ===');
        await runTest(new RawPcmTest(), INITIAL_DURATION, PAUSE_DURATION, RESUME_DURATION);
        
        // Run Solution 5: Use SoX on Linux
        console.log('\n=== Solution 5: Use SoX on Linux ===');
        await runTest(new SoxOnLinuxTest(), INITIAL_DURATION, PAUSE_DURATION, RESUME_DURATION);
        
        console.log('\n=== All tests completed ===');
        console.log('Please check the output files in:', TEST_OUTPUT_DIR);
        console.log('Listen to each recording to determine which solution works best.');
        
    } catch (error) {
        console.error('Error running tests:', error);
    }
}

// Run all tests
runAllTests().catch(console.error);

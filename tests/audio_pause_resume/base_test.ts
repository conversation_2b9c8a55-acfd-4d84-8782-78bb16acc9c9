/**
 * Base test script for audio pause/resume bug testing
 *
 * This script provides the common functionality needed for all test solutions:
 * - Setting up a recording environment
 * - Handling pause/resume operations
 * - Saving and analyzing the resulting audio file
 */

import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { EventEmitter } from 'events';
import { Readable, PassThrough, Writable } from 'stream';
// @ts-ignore
import NodeMicrophone from 'node-microphone';

// Create output directory for test recordings
const TEST_OUTPUT_DIR = path.join(__dirname, 'output');
if (!fs.existsSync(TEST_OUTPUT_DIR)) {
    fs.mkdirSync(TEST_OUTPUT_DIR, { recursive: true });
}

/**
 * Base class for testing pause/resume solutions
 */
export abstract class PauseResumeTest extends EventEmitter {
    protected nodeMic: NodeMicrophone;
    protected outputStream: Readable | null = null;
    protected passThrough: PassThrough | null = null;
    protected writeStream: fs.WriteStream | null = null;
    protected recordingPath: string;
    protected isPaused: boolean = false;
    protected testName: string;

    constructor(testName: string) {
        super();
        this.testName = testName;
        this.recordingPath = path.join(TEST_OUTPUT_DIR, `${testName}_recording.wav`);

        // Initialize node-microphone with default settings
        this.nodeMic = new NodeMicrophone({
            rate: 16000,
            channels: 1,
            bitwidth: 16,
            encoding: 'signed-integer',
            endian: 'little'
        });

        console.log(`[${this.testName}] Test initialized`);
    }

    /**
     * Start recording
     */
    public async startRecording(): Promise<void> {
        console.log(`[${this.testName}] Starting recording to ${this.recordingPath}`);

        // Create write stream for the output file
        this.writeStream = fs.createWriteStream(this.recordingPath);

        // Write WAV header
        this.writeWavHeader();

        // Start recording
        this.outputStream = await this.startMicrophone();

        // Set up data handling
        let dataChunks = 0;
        let totalBytes = 0;

        this.outputStream.on('data', (data: Buffer) => {
            dataChunks++;
            totalBytes += data.length;
            console.log(`[${this.testName}] Received audio chunk #${dataChunks}, size: ${data.length} bytes, total: ${totalBytes} bytes`);

            // Write to file
            if (this.writeStream && !this.isPaused) {
                this.writeStream.write(data);
            }
        });

        this.outputStream.on('end', () => {
            console.log(`[${this.testName}] Audio stream ended`);
        });

        this.outputStream.on('error', (error: Error) => {
            console.error(`[${this.testName}] Microphone error:`, error);
        });

        console.log(`[${this.testName}] Recording started`);
    }

    /**
     * Pause recording
     */
    public async pauseRecording(): Promise<void> {
        console.log(`[${this.testName}] Pausing recording...`);

        if (this.isPaused) {
            console.log(`[${this.testName}] Already paused, ignoring`);
            return;
        }

        this.isPaused = true;

        // Implementation-specific pause logic
        await this.doPauseRecording();

        console.log(`[${this.testName}] Recording paused`);
    }

    /**
     * Resume recording
     */
    public async resumeRecording(): Promise<void> {
        console.log(`[${this.testName}] Resuming recording...`);

        if (!this.isPaused) {
            console.log(`[${this.testName}] Not paused, ignoring`);
            return;
        }

        this.isPaused = false;

        // Implementation-specific resume logic
        await this.doResumeRecording();

        console.log(`[${this.testName}] Recording resumed`);
    }

    /**
     * Stop recording
     */
    public async stopRecording(): Promise<void> {
        console.log(`[${this.testName}] Stopping recording...`);

        // Implementation-specific stop logic
        await this.doStopRecording();

        // Update WAV header with final file size
        this.updateWavHeader();

        // Close write stream
        if (this.writeStream) {
            this.writeStream.end();
            this.writeStream = null;
        }

        console.log(`[${this.testName}] Recording stopped`);
        console.log(`[${this.testName}] Recording saved to ${this.recordingPath}`);

        // Analyze the recording
        this.analyzeRecording();
    }

    /**
     * Write WAV header to the file
     */
    protected writeWavHeader(): void {
        if (!this.writeStream) return;

        // WAV header for 16-bit mono PCM at 16kHz
        const header = Buffer.alloc(44);

        // RIFF chunk descriptor
        header.write('RIFF', 0);                     // ChunkID
        header.writeUInt32LE(0, 4);                  // ChunkSize (will be updated later)
        header.write('WAVE', 8);                     // Format

        // fmt sub-chunk
        header.write('fmt ', 12);                    // Subchunk1ID
        header.writeUInt32LE(16, 16);                // Subchunk1Size (16 for PCM)
        header.writeUInt16LE(1, 20);                 // AudioFormat (1 for PCM)
        header.writeUInt16LE(1, 22);                 // NumChannels (1 for mono)
        header.writeUInt32LE(16000, 24);             // SampleRate (16kHz)
        header.writeUInt32LE(16000 * 1 * 16 / 8, 28);// ByteRate (SampleRate * NumChannels * BitsPerSample/8)
        header.writeUInt16LE(1 * 16 / 8, 32);        // BlockAlign (NumChannels * BitsPerSample/8)
        header.writeUInt16LE(16, 34);                // BitsPerSample (16 bits)

        // data sub-chunk
        header.write('data', 36);                    // Subchunk2ID
        header.writeUInt32LE(0, 40);                 // Subchunk2Size (will be updated later)

        this.writeStream.write(header);
    }

    /**
     * Update WAV header with final file size
     */
    protected updateWavHeader(): void {
        if (!this.recordingPath || !fs.existsSync(this.recordingPath)) return;

        try {
            // Get file size
            const stats = fs.statSync(this.recordingPath);
            const fileSize = stats.size;

            // Read the header
            const fd = fs.openSync(this.recordingPath, 'r+');
            const header = Buffer.alloc(44);
            fs.readSync(fd, header, 0, 44, 0);

            // Update ChunkSize (file size - 8)
            header.writeUInt32LE(fileSize - 8, 4);

            // Update Subchunk2Size (file size - 44)
            header.writeUInt32LE(fileSize - 44, 40);

            // Write the updated header back to the file
            fs.writeSync(fd, header, 0, 44, 0);
            fs.closeSync(fd);

            console.log(`[${this.testName}] Updated WAV header with file size: ${fileSize} bytes`);
        } catch (error) {
            console.error(`[${this.testName}] Error updating WAV header:`, error);
        }
    }

    /**
     * Analyze the recording for quality issues
     */
    protected analyzeRecording(): void {
        if (!this.recordingPath || !fs.existsSync(this.recordingPath)) return;

        try {
            // Get file size
            const stats = fs.statSync(this.recordingPath);
            const fileSize = stats.size;

            console.log(`[${this.testName}] Recording analysis:`);
            console.log(`[${this.testName}] - File size: ${fileSize} bytes`);
            console.log(`[${this.testName}] - Duration: ~${((fileSize - 44) / (16000 * 1 * 16 / 8)).toFixed(2)} seconds`);

            // Check for potential issues
            if (fileSize < 1024) {
                console.warn(`[${this.testName}] WARNING: File size is very small, recording may be empty`);
            }

            // Read the first 100 bytes after the header to check for patterns
            const fd = fs.openSync(this.recordingPath, 'r');
            const sampleData = Buffer.alloc(100);
            fs.readSync(fd, sampleData, 0, 100, 44);
            fs.closeSync(fd);

            // Simple check for repeated patterns that might indicate corruption
            let repeatedPatterns = 0;
            for (let i = 0; i < sampleData.length - 4; i += 2) {
                if (sampleData.readInt16LE(i) === sampleData.readInt16LE(i + 2)) {
                    repeatedPatterns++;
                }
            }

            const repeatPercentage = (repeatedPatterns / (sampleData.length / 2 - 2)) * 100;
            console.log(`[${this.testName}] - Repeated sample patterns: ${repeatPercentage.toFixed(2)}%`);

            if (repeatPercentage > 50) {
                console.warn(`[${this.testName}] WARNING: High percentage of repeated patterns, possible audio corruption`);
            }

        } catch (error) {
            console.error(`[${this.testName}] Error analyzing recording:`, error);
        }
    }

    /**
     * Implementation-specific method to start the microphone
     */
    protected abstract startMicrophone(): Promise<Readable>;

    /**
     * Implementation-specific method to pause recording
     */
    protected abstract doPauseRecording(): Promise<void>;

    /**
     * Implementation-specific method to resume recording
     */
    protected abstract doResumeRecording(): Promise<void>;

    /**
     * Implementation-specific method to stop recording
     */
    protected abstract doStopRecording(): Promise<void>;
}

/**
 * Run a test with the specified pause and resume timings
 */
export async function runTest(test: PauseResumeTest, initialDuration: number, pauseDuration: number, resumeDuration: number): Promise<void> {
    // Start recording
    await test.startRecording();

    // Record for initialDuration seconds
    console.log(`Recording for ${initialDuration} seconds...`);
    await new Promise(resolve => setTimeout(resolve, initialDuration * 1000));

    // Pause recording
    await test.pauseRecording();

    // Pause for pauseDuration seconds
    console.log(`Paused for ${pauseDuration} seconds...`);
    await new Promise(resolve => setTimeout(resolve, pauseDuration * 1000));

    // Resume recording
    await test.resumeRecording();

    // Record for resumeDuration more seconds
    console.log(`Recording for ${resumeDuration} more seconds...`);
    await new Promise(resolve => setTimeout(resolve, resumeDuration * 1000));

    // Stop recording
    await test.stopRecording();
}

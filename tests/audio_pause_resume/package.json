{"name": "audio-pause-resume-tests", "version": "1.0.0", "description": "Tests for audio pause/resume bug solutions", "main": "run_all_tests.ts", "scripts": {"test": "ts-node run_all_tests.ts", "test:solution1": "ts-node solution1_extract_audio_data.ts", "test:solution2": "ts-node solution2_separate_segments.ts", "test:solution3": "ts-node solution3_stream_buffering.ts", "test:solution4": "ts-node solution4_raw_pcm.ts", "test:solution5": "ts-node solution5_sox_on_linux.ts"}, "dependencies": {"node-microphone": "^0.1.6"}, "devDependencies": {"@types/node": "^16.11.7", "ts-node": "^10.4.0", "typescript": "^4.5.2"}}
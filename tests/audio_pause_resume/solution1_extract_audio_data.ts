/**
 * Solution 1: Extract Audio Data Without Headers
 *
 * This solution extracts just the audio data (without WAV headers) from the new recording
 * after resuming and appends it to the existing file.
 */

import * as fs from 'fs';
import { Readable, PassThrough } from 'stream';
import { PauseResumeTest, runTest } from './base_test';

class ExtractAudioDataTest extends PauseResumeTest {
    private tempFilePath: string;
    private originalStream: Readable | null = null;

    constructor() {
        super('Solution1_ExtractAudioData');
        this.tempFilePath = `${this.recordingPath}.temp`;
    }

    protected async startMicrophone(): Promise<Readable> {
        // Create a PassThrough stream that we'll use throughout the recording
        this.passThrough = new PassThrough();

        // Start the microphone and get the source stream
        this.originalStream = this.nodeMic.startRecording();

        // Pipe the source stream to our PassThrough
        if (this.originalStream) {
            this.originalStream.pipe(this.passThrough);
        }

        return this.passThrough;
    }

    protected async doPauseRecording(): Promise<void> {
        // Unpipe the original stream from the PassThrough
        if (this.originalStream && this.passThrough) {
            this.originalStream.unpipe(this.passThrough);
        }

        // Stop the microphone
        this.nodeMic.stopRecording();
        this.originalStream = null;
    }

    protected async doResumeRecording(): Promise<void> {
        // Create a temporary file for the new recording segment
        const tempWriteStream = fs.createWriteStream(this.tempFilePath);

        // Start a new recording
        this.originalStream = this.nodeMic.startRecording();

        if (this.originalStream) {
            // Write the new recording to the temporary file
            this.originalStream.pipe(tempWriteStream);

            // Also pipe to our PassThrough for real-time processing
            this.originalStream.pipe(this.passThrough as PassThrough);
        }

        // When the temporary file is written, we'll extract the audio data and append it
        tempWriteStream.on('finish', () => {
            this.extractAndAppendAudioData();
        });
    }

    protected async doStopRecording(): Promise<void> {
        // Stop the microphone
        if (this.originalStream) {
            this.originalStream.unpipe(this.passThrough as PassThrough);
        }

        this.nodeMic.stopRecording();
        this.originalStream = null;

        // End the PassThrough stream
        if (this.passThrough) {
            this.passThrough.end();
        }
    }

    /**
     * Extract audio data from the temporary file and append it to the main recording
     */
    private extractAndAppendAudioData(): void {
        if (!fs.existsSync(this.tempFilePath)) {
            console.error(`[${this.testName}] Temporary file not found: ${this.tempFilePath}`);
            return;
        }

        try {
            // Read the temporary file
            const tempData = fs.readFileSync(this.tempFilePath);

            // Check if it's a valid WAV file (should start with "RIFF" and have "WAVE" at offset 8)
            if (tempData.length < 44 ||
                tempData.toString('ascii', 0, 4) !== 'RIFF' ||
                tempData.toString('ascii', 8, 12) !== 'WAVE') {
                console.error(`[${this.testName}] Temporary file is not a valid WAV file`);
                return;
            }

            // Extract just the audio data (skip the 44-byte WAV header)
            const audioData = tempData.slice(44);

            // Append to the main recording file
            if (this.writeStream) {
                this.writeStream.write(audioData);
                console.log(`[${this.testName}] Appended ${audioData.length} bytes of audio data to main recording`);
            }

            // Clean up the temporary file
            fs.unlinkSync(this.tempFilePath);

        } catch (error) {
            console.error(`[${this.testName}] Error extracting and appending audio data:`, error);
        }
    }
}

// Run the test
async function main() {
    const test = new ExtractAudioDataTest();

    // Record for 5 seconds, pause for 2 seconds, then record for 10 more seconds
    await runTest(test, 5, 2, 10);
}

// Only run if this file is executed directly
if (require.main === module) {
    main().catch(console.error);
}

export { ExtractAudioDataTest };

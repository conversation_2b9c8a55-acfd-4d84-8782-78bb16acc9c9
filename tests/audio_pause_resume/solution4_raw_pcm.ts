/**
 * Solution 4: Use Raw PCM Throughout
 *
 * This solution works with raw PCM data throughout the recording process
 * and only converts to WAV at the end when the recording is stopped.
 */

import * as fs from 'fs';
import { Readable, PassThrough } from 'stream';
import { PauseResumeTest, runTest } from './base_test';

class RawPcmTest extends PauseResumeTest {
    private originalStream: Readable | null = null;
    private pcmFilePath: string;
    private pcmWriteStream: fs.WriteStream | null = null;
    private totalBytesWritten: number = 0;

    constructor() {
        super('Solution4_RawPCM');
        this.pcmFilePath = `${this.recordingPath}.pcm`;
    }

    protected async startMicrophone(): Promise<Readable> {
        // Create a PassThrough stream that we'll use throughout the recording
        this.passThrough = new PassThrough();

        // Start the microphone and get the source stream
        this.originalStream = this.nodeMic.startRecording();

        // Create a write stream for the raw PCM data
        this.pcmWriteStream = fs.createWriteStream(this.pcmFilePath);

        // Pipe the source stream to our PassThrough for monitoring
        if (this.originalStream) {
            this.originalStream.pipe(this.passThrough);

            // Set up data handling to write raw PCM data
            this.originalStream.on('data', (data: Buffer) => {
                if (this.pcmWriteStream && !this.isPaused) {
                    this.pcmWriteStream.write(data);
                    this.totalBytesWritten += data.length;
                }
            });
        }

        return this.passThrough;
    }

    protected async doPauseRecording(): Promise<void> {
        // We don't need to do anything special here since we're handling
        // the data event and checking this.isPaused
        console.log(`[${this.testName}] Paused writing PCM data, bytes written so far: ${this.totalBytesWritten}`);
    }

    protected async doResumeRecording(): Promise<void> {
        // We don't need to do anything special here since we're handling
        // the data event and checking this.isPaused
        console.log(`[${this.testName}] Resumed writing PCM data`);
    }

    protected async doStopRecording(): Promise<void> {
        // Stop the microphone
        if (this.originalStream) {
            if (this.passThrough) {
                this.originalStream.unpipe(this.passThrough);
            }
        }

        this.nodeMic.stopRecording();
        this.originalStream = null;

        // Close the PCM write stream
        if (this.pcmWriteStream) {
            this.pcmWriteStream.end();
            this.pcmWriteStream = null;
        }

        // End the PassThrough stream
        if (this.passThrough) {
            this.passThrough.end();
        }

        // Convert the raw PCM data to WAV
        await this.convertPcmToWav();
    }

    /**
     * Convert the raw PCM data to WAV format
     */
    private async convertPcmToWav(): Promise<void> {
        if (!fs.existsSync(this.pcmFilePath)) {
            console.error(`[${this.testName}] PCM file not found: ${this.pcmFilePath}`);
            return;
        }

        try {
            // Read the PCM data
            const pcmData = fs.readFileSync(this.pcmFilePath);

            // Create the WAV file
            const wavWriteStream = fs.createWriteStream(this.recordingPath);

            // Write the WAV header
            const header = this.createWavHeader(pcmData.length);
            wavWriteStream.write(header);

            // Write the PCM data
            wavWriteStream.write(pcmData);
            wavWriteStream.end();

            console.log(`[${this.testName}] Converted ${pcmData.length} bytes of PCM data to WAV format`);

            // Clean up the PCM file
            fs.unlinkSync(this.pcmFilePath);

        } catch (error) {
            console.error(`[${this.testName}] Error converting PCM to WAV:`, error);
        }
    }

    /**
     * Create a WAV header for the given PCM data size
     */
    private createWavHeader(pcmDataSize: number): Buffer {
        // WAV header for 16-bit mono PCM at 16kHz
        const header = Buffer.alloc(44);

        // RIFF chunk descriptor
        header.write('RIFF', 0);                     // ChunkID
        header.writeUInt32LE(pcmDataSize + 36, 4);   // ChunkSize (36 + SubChunk2Size)
        header.write('WAVE', 8);                     // Format

        // fmt sub-chunk
        header.write('fmt ', 12);                    // Subchunk1ID
        header.writeUInt32LE(16, 16);                // Subchunk1Size (16 for PCM)
        header.writeUInt16LE(1, 20);                 // AudioFormat (1 for PCM)
        header.writeUInt16LE(1, 22);                 // NumChannels (1 for mono)
        header.writeUInt32LE(16000, 24);             // SampleRate (16kHz)
        header.writeUInt32LE(16000 * 1 * 16 / 8, 28);// ByteRate (SampleRate * NumChannels * BitsPerSample/8)
        header.writeUInt16LE(1 * 16 / 8, 32);        // BlockAlign (NumChannels * BitsPerSample/8)
        header.writeUInt16LE(16, 34);                // BitsPerSample (16 bits)

        // data sub-chunk
        header.write('data', 36);                    // Subchunk2ID
        header.writeUInt32LE(pcmDataSize, 40);       // Subchunk2Size

        return header;
    }
}

// Run the test
async function main() {
    const test = new RawPcmTest();

    // Record for 5 seconds, pause for 2 seconds, then record for 10 more seconds
    await runTest(test, 5, 2, 10);
}

// Only run if this file is executed directly
if (require.main === module) {
    main().catch(console.error);
}

export { RawPcmTest };

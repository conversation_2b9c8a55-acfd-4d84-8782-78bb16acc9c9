declare module 'node-microphone' {
    import { Readable } from 'stream';

    interface NodeMicrophoneOptions {
        rate?: number;
        channels?: number;
        bitwidth?: number;
        encoding?: string;
        endian?: string;
        device?: string;
        additionalParameters?: string[];
    }

    class NodeMicrophone {
        constructor(options?: NodeMicrophoneOptions);
        startRecording(): Readable;
        stopRecording(): void;
    }

    export = NodeMicrophone;
}

/**
 * Solution 5: Use SoX on Linux
 *
 * This solution uses SoX for recording on Linux instead of node-microphone,
 * which might handle pause/resume differently.
 */

import * as fs from 'fs';
import * as os from 'os';
import * as path from 'path';
import * as childProcess from 'child_process';
import { Readable, PassThrough } from 'stream';
import { PauseResumeTest, runTest } from './base_test';
// Mock getSoxPath function since we don't have access to the actual implementation
function getSoxPath(): string {
    // Try to find SoX in common locations
    const possiblePaths = [
        '/usr/bin/sox',
        '/usr/local/bin/sox',
        '/opt/homebrew/bin/sox'
    ];

    for (const path of possiblePaths) {
        if (fs.existsSync(path)) {
            return path;
        }
    }

    // Default to 'sox' and let the system find it in PATH
    return 'sox';
}

class SoxOnLinuxTest extends PauseResumeTest {
    private soxPath: string;
    private process: childProcess.ChildProcess | null = null;
    private tempFilePath: string;

    constructor() {
        super('Solution5_SoxOnLinux');
        this.soxPath = getSoxPath();
        this.tempFilePath = path.join(os.tmpdir(), 'voicehype_sox_recording.wav');
    }

    protected async startMicrophone(): Promise<Readable> {
        // Create a PassThrough stream that we'll use throughout the recording
        this.passThrough = new PassThrough();

        // Build the SoX command with appropriate options
        const args = this.buildSoxArgs();
        console.log(`[${this.testName}] Starting recording with command: ${this.soxPath} ${args.join(' ')}`);

        try {
            // Start the SoX process
            this.process = childProcess.spawn(this.soxPath, args);

            // Handle process events
            this.process.on('error', (error) => {
                console.error(`[${this.testName}] SoX process error:`, error);
            });

            this.process.on('close', (code) => {
                console.log(`[${this.testName}] SoX process closed with code: ${code}`);

                // If the process closed normally, read the output file
                if (code === 0 && fs.existsSync(this.tempFilePath)) {
                    try {
                        const fileData = fs.readFileSync(this.tempFilePath);
                        console.log(`[${this.testName}] Read ${fileData.length} bytes from temporary file`);

                        // Copy to the final recording path
                        fs.writeFileSync(this.recordingPath, fileData);

                        // Clean up the temporary file
                        fs.unlinkSync(this.tempFilePath);

                    } catch (error) {
                        console.error(`[${this.testName}] Error reading temporary file:`, error);
                    }
                }

                this.process = null;
            });

            // If we have stdout, pipe it to our PassThrough
            if (this.process.stdout) {
                this.process.stdout.pipe(this.passThrough);
            }

            return this.passThrough;

        } catch (error) {
            console.error(`[${this.testName}] Error starting SoX process:`, error);
            throw error;
        }
    }

    protected async doPauseRecording(): Promise<void> {
        // For SoX, we'll pause by sending SIGSTOP to the process
        if (this.process) {
            try {
                this.process.kill('SIGSTOP');
                console.log(`[${this.testName}] Sent SIGSTOP to SoX process`);
            } catch (error) {
                console.error(`[${this.testName}] Error pausing SoX process:`, error);
            }
        }
    }

    protected async doResumeRecording(): Promise<void> {
        // For SoX, we'll resume by sending SIGCONT to the process
        if (this.process) {
            try {
                this.process.kill('SIGCONT');
                console.log(`[${this.testName}] Sent SIGCONT to SoX process`);
            } catch (error) {
                console.error(`[${this.testName}] Error resuming SoX process:`, error);
            }
        }
    }

    protected async doStopRecording(): Promise<void> {
        // Stop the SoX process
        if (this.process) {
            try {
                this.process.kill();
                console.log(`[${this.testName}] Killed SoX process`);
            } catch (error) {
                console.error(`[${this.testName}] Error stopping SoX process:`, error);
            }

            this.process = null;
        }

        // End the PassThrough stream
        if (this.passThrough) {
            this.passThrough.end();
        }
    }

    /**
     * Build the arguments for the SoX command
     */
    private buildSoxArgs(): string[] {
        const args: string[] = [];

        // Input options
        args.push('-q');                 // Quiet mode
        args.push('-r', '16000');        // Sample rate
        args.push('-c', '1');            // Channels
        args.push('-b', '16');           // Bits per sample

        // Input format
        args.push('-t', 'alsa');         // ALSA input on Linux
        args.push('default');            // Default device

        // Output options
        args.push('-t', 'wav');          // WAV output format

        // Output file
        args.push(this.tempFilePath);

        return args;
    }
}

// Run the test
async function main() {
    const test = new SoxOnLinuxTest();

    // Record for 5 seconds, pause for 2 seconds, then record for 10 more seconds
    await runTest(test, 5, 2, 10);
}

// Only run if this file is executed directly
if (require.main === module) {
    main().catch(console.error);
}

export { SoxOnLinuxTest };

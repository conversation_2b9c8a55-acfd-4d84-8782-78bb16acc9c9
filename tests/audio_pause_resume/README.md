# Audio Pause/Resume Bug Tests

This directory contains test implementations for various solutions to the audio distortion bug that occurs when pausing and resuming recording on Linux using the `node-microphone` package.

## Background

When recording audio on Linux using the `node-microphone` package, pausing and resuming the recording causes audio distortion in the segments recorded after resuming. The distortion makes the audio sound like a robot is speaking, with words being repeated multiple times and stretched out.

## Solutions Being Tested

1. **Extract Audio Data Without Headers**: Extracts just the audio data (without WAV headers) from the new recording after resuming and appends it to the existing file.

2. **Create Separate Segments and Merge**: Creates separate WAV files for each recording segment (before pause, after resume) and properly merges them afterward.

3. **Stream Buffering**: Implements a buffering mechanism that temporarily stops writing to the file but keeps the recording process running during pause.

4. **Use Raw PCM Throughout**: Works with raw PCM data throughout the recording process and only converts to WAV at the end when the recording is stopped.

5. **Use SoX on Linux**: Uses SoX for recording on Linux instead of node-microphone, which might handle pause/resume differently.

## Running the Tests

### Prerequisites

- Node.js (v14 or later)
- TypeScript
- `node-microphone` package
- SoX (for Solution 5)

### Installation

```bash
npm install
```

### Running All Tests

```bash
npm test
```

### Running Individual Tests

```bash
npm run test:solution1  # Extract Audio Data Without Headers
npm run test:solution2  # Create Separate Segments and Merge
npm run test:solution3  # Stream Buffering
npm run test:solution4  # Use Raw PCM Throughout
npm run test:solution5  # Use SoX on Linux
```

## Test Output

Each test will create a WAV file in the `output` directory. The files will be named according to the solution being tested:

- `Solution1_ExtractAudioData_recording.wav`
- `Solution2_SeparateSegments_recording.wav`
- `Solution3_StreamBuffering_recording.wav`
- `Solution4_RawPCM_recording.wav`
- `Solution5_SoxOnLinux_recording.wav`

Listen to each recording to determine which solution works best.

## Test Parameters

The tests are configured to:

1. Record for 5 seconds
2. Pause for 2 seconds
3. Record for 10 more seconds

These parameters can be adjusted in the `run_all_tests.ts` file.

## Evaluating Results

When evaluating the results, consider:

1. **Audio Quality**: Does the recording sound natural and continuous?
2. **Transition Smoothness**: Is the transition between pre-pause and post-pause segments smooth?
3. **Implementation Complexity**: How complex is the solution to implement in the main codebase?
4. **Performance**: Does the solution have any performance implications?

## Next Steps

After identifying the best solution, implement it in the main codebase by updating the `microphone.ts` file in the `extension/src/utils` directory.

/**
 * Solution 3: Stream Buffering
 *
 * This solution implements a buffering mechanism that temporarily stops writing to the file
 * but keeps the recording process running during pause.
 */

import * as fs from 'fs';
import { Readable, PassThrough } from 'stream';
import { PauseResumeTest, runTest } from './base_test';

class StreamBufferingTest extends PauseResumeTest {
    private originalStream: Readable | null = null;
    private bufferStream: PassThrough | null = null;

    constructor() {
        super('Solution3_StreamBuffering');
    }

    protected async startMicrophone(): Promise<Readable> {
        // Create a PassThrough stream that we'll use throughout the recording
        this.passThrough = new PassThrough();

        // Create a buffer stream that we'll use to control the flow to the file
        this.bufferStream = new PassThrough();

        // Start the microphone and get the source stream
        this.originalStream = this.nodeMic.startRecording();

        // Pipe the source stream to our PassThrough for monitoring
        if (this.originalStream) {
            this.originalStream.pipe(this.passThrough);

            // Pipe the source stream to our buffer stream for writing to file
            this.originalStream.pipe(this.bufferStream);
        }

        // Pipe the buffer stream to the file
        if (this.writeStream) {
            this.bufferStream.pipe(this.writeStream);
        }

        return this.passThrough;
    }

    protected async doPauseRecording(): Promise<void> {
        // To pause, we'll unpipe the buffer stream from the write stream
        // but keep the original stream running
        if (this.bufferStream && this.writeStream) {
            this.bufferStream.unpipe(this.writeStream);
        }

        console.log(`[${this.testName}] Paused writing to file but microphone is still running`);
    }

    protected async doResumeRecording(): Promise<void> {
        // To resume, we'll re-pipe the buffer stream to the write stream
        if (this.bufferStream && this.writeStream) {
            this.bufferStream.pipe(this.writeStream);
        }

        console.log(`[${this.testName}] Resumed writing to file`);
    }

    protected async doStopRecording(): Promise<void> {
        // Stop the microphone
        if (this.originalStream) {
            if (this.passThrough) {
                this.originalStream.unpipe(this.passThrough);
            }
            if (this.bufferStream) {
                this.originalStream.unpipe(this.bufferStream);
            }
        }

        this.nodeMic.stopRecording();
        this.originalStream = null;

        // End the streams
        if (this.bufferStream) {
            if (this.writeStream) {
                this.bufferStream.unpipe(this.writeStream);
            }
            this.bufferStream.end();
            this.bufferStream = null;
        }

        if (this.passThrough) {
            this.passThrough.end();
        }
    }
}

// Run the test
async function main() {
    const test = new StreamBufferingTest();

    // Record for 5 seconds, pause for 2 seconds, then record for 10 more seconds
    await runTest(test, 5, 2, 10);
}

// Only run if this file is executed directly
if (require.main === module) {
    main().catch(console.error);
}

export { StreamBufferingTest };

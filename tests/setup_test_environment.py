#!/usr/bin/env python3
"""
Setup script for VoiceHype realtime transcription test environment
Handles platform-specific PyAudio installation requirements
"""

import subprocess
import sys
import platform
import os

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"   Command: {command}")
        print(f"   Error: {e.stderr}")
        return False

def install_pyaudio_dependencies():
    """Install platform-specific PyAudio dependencies"""
    system = platform.system().lower()
    
    if system == "linux":
        print("🐧 Detected Linux system")
        # Try different package managers
        if os.system("which apt-get > /dev/null 2>&1") == 0:
            return run_command(
                "sudo apt-get update && sudo apt-get install -y portaudio19-dev python3-pyaudio",
                "Installing PortAudio dependencies (apt)"
            )
        elif os.system("which yum > /dev/null 2>&1") == 0:
            return run_command(
                "sudo yum install -y portaudio-devel",
                "Installing PortAudio dependencies (yum)"
            )
        elif os.system("which pacman > /dev/null 2>&1") == 0:
            return run_command(
                "sudo pacman -S portaudio",
                "Installing PortAudio dependencies (pacman)"
            )
        else:
            print("⚠️  Could not detect package manager. Please install portaudio development libraries manually.")
            return False
            
    elif system == "darwin":
        print("🍎 Detected macOS system")
        if os.system("which brew > /dev/null 2>&1") == 0:
            return run_command(
                "brew install portaudio",
                "Installing PortAudio via Homebrew"
            )
        else:
            print("⚠️  Homebrew not found. Please install Homebrew first: https://brew.sh/")
            return False
            
    elif system == "windows":
        print("🪟 Detected Windows system")
        print("ℹ️  On Windows, PyAudio should install directly via pip")
        return True
        
    else:
        print(f"⚠️  Unknown system: {system}")
        return False

def install_python_packages():
    """Install Python packages"""
    print("📦 Installing Python packages...")
    
    # Upgrade pip first
    if not run_command(f"{sys.executable} -m pip install --upgrade pip", "Upgrading pip"):
        return False
    
    # Install packages one by one for better error handling
    packages = [
        ("websockets", "WebSocket client library"),
        ("pyaudio", "Audio I/O library")
    ]
    
    for package, description in packages:
        if not run_command(f"{sys.executable} -m pip install {package}", f"Installing {description}"):
            print(f"⚠️  Failed to install {package}. You may need to install it manually.")
            if package == "pyaudio":
                print("   For PyAudio installation help, see: https://pypi.org/project/PyAudio/")
            return False
    
    return True

def test_installation():
    """Test if all required packages are available"""
    print("🧪 Testing installation...")
    
    try:
        import pyaudio
        print("✅ PyAudio imported successfully")
        
        # Test PyAudio initialization
        p = pyaudio.PyAudio()
        device_count = p.get_device_count()
        print(f"✅ Found {device_count} audio devices")
        p.terminate()
        
    except ImportError as e:
        print(f"❌ Failed to import PyAudio: {e}")
        return False
    except Exception as e:
        print(f"❌ PyAudio test failed: {e}")
        return False
    
    try:
        import websockets
        print("✅ WebSockets imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import websockets: {e}")
        return False
    
    return True

def main():
    print("🚀 VoiceHype Realtime Transcription Test Setup")
    print("=" * 50)
    
    # Install system dependencies
    if not install_pyaudio_dependencies():
        print("⚠️  System dependency installation failed, but continuing...")
    
    # Install Python packages
    if not install_python_packages():
        print("❌ Python package installation failed!")
        sys.exit(1)
    
    # Test installation
    if test_installation():
        print("\n✅ Setup completed successfully!")
        print("\nYou can now run the test with:")
        print("python test_realtime_transcription.py --api-key YOUR_API_KEY --list-devices")
        print("python test_realtime_transcription.py --api-key YOUR_API_KEY --service openai --duration 30")
    else:
        print("\n❌ Setup completed with errors. Please check the installation manually.")
        sys.exit(1)

if __name__ == "__main__":
    main()

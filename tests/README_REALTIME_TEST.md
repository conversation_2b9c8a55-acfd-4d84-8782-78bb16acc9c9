# VoiceHype Realtime Transcription Test Suite

This Python test suite allows you to test the VoiceHype realtime transcription WebSocket server with live audio capture using PyAudio and WebSockets.

## Features

- ✅ **Live Audio Capture**: Records audio from your microphone in real-time
- ✅ **WebSocket Streaming**: Streams audio data to VoiceHype realtime transcription server
- ✅ **Multiple Services**: Supports both OpenAI and AssemblyAI transcription services
- ✅ **Real-time Feedback**: Shows partial and final transcripts as they arrive
- ✅ **Complete Transcript**: Receives and displays the complete transcript at the end
- ✅ **Statistics**: Provides detailed session statistics and performance metrics
- ✅ **Device Selection**: Lists and allows selection of specific audio input devices
- ✅ **Error Handling**: Comprehensive error handling and debugging information

## Installation

### Option 1: Automatic Setup (Recommended)

```bash
# Run the setup script to install all dependencies
python setup_test_environment.py
```

### Option 2: Manual Installation

#### System Dependencies

**Linux (Ubuntu/Debian):**
```bash
sudo apt-get update
sudo apt-get install portaudio19-dev python3-pyaudio
```

**Linux (CentOS/RHEL):**
```bash
sudo yum install portaudio-devel
```

**macOS:**
```bash
brew install portaudio
```

**Windows:**
No additional system dependencies required.

#### Python Dependencies

```bash
pip install pyaudio websockets
```

Or using the requirements file:
```bash
pip install -r requirements.txt
```

## Usage

### Basic Usage

```bash
# Test with OpenAI (default)
python test_realtime_transcription.py --api-key YOUR_API_KEY

# Test with AssemblyAI
python test_realtime_transcription.py --api-key YOUR_API_KEY --service assemblyai

# Test for 60 seconds
python test_realtime_transcription.py --api-key YOUR_API_KEY --duration 60
```

### Advanced Usage

```bash
# List available audio devices
python test_realtime_transcription.py --api-key YOUR_API_KEY --list-devices

# Use specific audio device
python test_realtime_transcription.py --api-key YOUR_API_KEY --device 2

# Test with specific model and language
python test_realtime_transcription.py --api-key YOUR_API_KEY --service openai --model gpt-4o-mini-transcribe --language en

# Test against different server
python test_realtime_transcription.py --api-key YOUR_API_KEY --server your-custom-server.com
```

### Command Line Options

| Option | Description | Default |
|--------|-------------|---------|
| `--api-key` | VoiceHype API key (required) | - |
| `--service` | Transcription service (`openai` or `assemblyai`) | `openai` |
| `--model` | Model to use | `gpt-4o-mini-transcribe` (OpenAI), `best` (AssemblyAI) |
| `--language` | Language code | `en` |
| `--duration` | Recording duration in seconds | `30` |
| `--device` | Audio input device index | Default device |
| `--list-devices` | List available audio devices and exit | - |
| `--server` | Server URL | `supabase.voicehype.ai` |

## Example Output

```
🚀 Starting VoiceHype Realtime Transcription Test
==================================================
✅ Audio stream setup successful (Device: default, Rate: 16000Hz)
Connecting to: wss://supabase.voicehype.ai/functions/v1/transcribe/realtime
Service: openai, Model: gpt-4o-mini-transcribe, Language: en
✅ WebSocket connected successfully!
🔗 Session established: sess_abc123
⏱️  Maximum session duration: 5.0 minutes
🎤 Starting audio capture and streaming...
🎙️  Recording for 30 seconds... Speak now!

📝 Partial: Hello
📝 Partial: Hello, this is
📝 Partial: Hello, this is a test
✅ Final: Hello, this is a test of the VoiceHype realtime transcription system.
📝 Partial: It seems to be
📝 Partial: It seems to be working
✅ Final: It seems to be working quite well.

📤 Sending close message to server...
🎯 Complete Transcript: Hello, this is a test of the VoiceHype realtime transcription system. It seems to be working quite well.
🔌 WebSocket connection closed

=== Session Statistics ===
Duration: 30.12 seconds
Audio bytes sent: 963,584 bytes
Messages received: 15
Final transcript segments: 2
Partial transcripts: 6

=== Complete Transcript ===
"Hello, this is a test of the VoiceHype realtime transcription system. It seems to be working quite well."

✅ Test completed successfully!
```

## Troubleshooting

### PyAudio Installation Issues

**Linux:**
```bash
# If you get "portaudio.h not found"
sudo apt-get install portaudio19-dev

# If you get permission errors
sudo apt-get install python3-pyaudio
```

**macOS:**
```bash
# If Homebrew installation fails
xcode-select --install
brew install portaudio
```

**Windows:**
```bash
# Try installing from wheel
pip install pipwin
pipwin install pyaudio
```

### Audio Device Issues

```bash
# List all audio devices to find the right one
python test_realtime_transcription.py --api-key YOUR_API_KEY --list-devices

# Test with a specific device
python test_realtime_transcription.py --api-key YOUR_API_KEY --device 1
```

### WebSocket Connection Issues

1. **Check API Key**: Ensure your VoiceHype API key is valid
2. **Check Server URL**: Verify the server URL is correct
3. **Check Network**: Ensure you can reach the server (try ping/curl)
4. **Check Firewall**: Ensure WebSocket connections are allowed

### Common Error Messages

| Error | Solution |
|-------|----------|
| `ModuleNotFoundError: No module named 'pyaudio'` | Install PyAudio: `pip install pyaudio` |
| `OSError: [Errno -9996] Invalid input device` | Use `--list-devices` to find valid device index |
| `ConnectionRefusedError` | Check server URL and network connectivity |
| `Invalid API key` | Verify your VoiceHype API key is correct |

## Testing Different Scenarios

### Test Transcript Accumulation
Record for longer periods to test if the complete transcript includes all segments:
```bash
python test_realtime_transcription.py --api-key YOUR_API_KEY --duration 120
```

### Test Network Interruption
Start the test and temporarily disconnect/reconnect your network to test reconnection handling.

### Test Different Audio Quality
Try different microphones or audio devices to test transcription quality:
```bash
python test_realtime_transcription.py --api-key YOUR_API_KEY --device 2
```

### Test Service Comparison
Compare OpenAI vs AssemblyAI with the same audio:
```bash
# Test OpenAI
python test_realtime_transcription.py --api-key YOUR_API_KEY --service openai

# Test AssemblyAI  
python test_realtime_transcription.py --api-key YOUR_API_KEY --service assemblyai
```

## What This Test Validates

1. **WebSocket Connection**: Verifies the server accepts connections with proper authentication
2. **Audio Streaming**: Confirms audio data is properly streamed to the server
3. **Transcript Reception**: Validates that partial, final, and complete transcripts are received
4. **Session Management**: Tests session establishment, duration tracking, and proper closure
5. **Error Handling**: Verifies proper error messages and graceful failure handling
6. **Performance**: Measures audio throughput and response times

This test suite helps ensure the realtime transcription server is working correctly and can handle live audio streaming scenarios effectively.

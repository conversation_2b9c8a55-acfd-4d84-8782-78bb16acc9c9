// Test script for Vercel functions
require('dotenv').config();
const fs = require('fs');
const path = require('path');
const fetch = require('node-fetch');

// Configuration
const API_KEY = process.env.VOICEHYPE_API_KEY || 'your_api_key_here';
const BASE_URL = process.env.VERCEL_DEPLOYMENT_URL || 'https://vercel-functions-q2kicemhk-kq1231s-projects.vercel.app';
const AUDIO_FILE = path.join(__dirname, 'voicehype_recording.wav');

// Test transcribe function
async function testTranscribe() {
  console.log('\n--- Testing Transcribe Function ---');

  try {
    // Check if audio file exists
    if (!fs.existsSync(AUDIO_FILE)) {
      console.error(`Error: Audio file '${AUDIO_FILE}' not found.`);
      return false;
    }

    // Read audio file and convert to base64
    const audioData = fs.readFileSync(AUDIO_FILE);
    const base64Audio = audioData.toString('base64');

    // Estimate audio duration (rough estimate for WAV files)
    // For WAV files: Duration (seconds) = File size (bytes) / (Sample Rate * Channels * Bits per sample / 8)
    // Assuming 16kHz, mono, 16-bit audio
    const sampleRate = 16000;
    const channels = 1;
    const bitsPerSample = 16;
    const audioDurationSeconds = audioData.length / (sampleRate * channels * bitsPerSample / 8);

    console.log(`Audio file size: ${audioData.length} bytes`);
    console.log(`Estimated audio duration: ${audioDurationSeconds.toFixed(2)} seconds`);

    // Make request to transcribe function
    const response = await fetch(`${BASE_URL}/api/transcribe`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_KEY}`,
        'Audio-Duration-Seconds': audioDurationSeconds.toString()
      },
      body: JSON.stringify({
        audioUrl: base64Audio,
        service: 'openai',
        model: 'whisper-1',
        language: 'en'
      })
    });

    const responseData = await response.json();

    if (response.ok) {
      console.log('Status:', response.status);
      console.log('Transcription:', responseData.data.transcription);
      console.log('Metadata:', responseData.data.metadata);
      return true;
    } else {
      console.error('Error:', responseData.error);
      return false;
    }
  } catch (error) {
    console.error('Error testing transcribe function:', error.message);
    return false;
  }
}

// Test optimize function
async function testOptimize() {
  console.log('\n--- Testing Optimize Function ---');

  try {
    const sampleText = "This is a sample text that needs to be optimized. It has some gramatical errors and typos that should be fixed. Also, the structure could be improved to make it more concise and professional.";

    // Make request to optimize function
    const response = await fetch(`${BASE_URL}/api/optimize`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_KEY}`
      },
      body: JSON.stringify({
        text: sampleText,
        optimizationType: 'grammar',
        model: 'gpt-3.5-turbo'
      })
    });

    const responseData = await response.json();

    if (response.ok) {
      console.log('Status:', response.status);
      console.log('Original Text:', sampleText);
      console.log('Optimized Text:', responseData.data.optimizedText);
      console.log('Metadata:', responseData.data.metadata);
      return true;
    } else {
      console.error('Error:', responseData.error);
      return false;
    }
  } catch (error) {
    console.error('Error testing optimize function:', error.message);
    return false;
  }
}

// Test realtime-transcribe function
async function testRealtimeTranscribe() {
  console.log('\n--- Testing Realtime Transcribe Function ---');

  try {
    // Start a realtime transcription session
    const startResponse = await fetch(`${BASE_URL}/api/realtime-transcribe`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_KEY}`
      },
      body: JSON.stringify({
        action: 'start',
        language: 'en',
        sampleRate: 16000
      })
    });

    const startData = await startResponse.json();

    if (!startResponse.ok) {
      console.error('Error starting realtime transcription:', startData.error);
      return false;
    }

    console.log('Session started successfully:');
    console.log('Session ID:', startData.data.sessionId);
    console.log('WebSocket URL:', startData.data.websocketUrl);
    console.log('Token:', startData.data.token.substring(0, 10) + '...');

    // In a real test, you would connect to the WebSocket and send audio data
    // For this test, we'll just wait a few seconds and then end the session
    console.log('Simulating realtime transcription for 5 seconds...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    // End the session
    const endResponse = await fetch(`${BASE_URL}/api/realtime-transcribe`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_KEY}`
      },
      body: JSON.stringify({
        action: 'end',
        sessionId: startData.data.sessionId
      })
    });

    const endData = await endResponse.json();

    if (endResponse.ok) {
      console.log('Session ended successfully:');
      console.log('Session ID:', endData.data.sessionId);
      console.log('Duration (minutes):', endData.data.durationMinutes);
      return true;
    } else {
      console.error('Error ending realtime transcription:', endData.error);
      return false;
    }
  } catch (error) {
    console.error('Error testing realtime transcribe function:', error.message);
    return false;
  }
}

// Run all tests
async function runTests() {
  console.log(`Testing Vercel functions at: ${BASE_URL}`);
  console.log(`Using API key: ${API_KEY.substring(0, 5)}...`);

  const transcribeSuccess = await testTranscribe();
  const optimizeSuccess = await testOptimize();
  const realtimeSuccess = await testRealtimeTranscribe();

  console.log('\n--- Test Results ---');
  console.log(`Transcribe Function: ${transcribeSuccess ? 'SUCCESS' : 'FAILED'}`);
  console.log(`Optimize Function: ${optimizeSuccess ? 'SUCCESS' : 'FAILED'}`);
  console.log(`Realtime Transcribe Function: ${realtimeSuccess ? 'SUCCESS' : 'FAILED'}`);

  if (transcribeSuccess && optimizeSuccess && realtimeSuccess) {
    console.log('\nAll tests passed successfully!');
    process.exit(0);
  } else {
    console.log('\nSome tests failed. Please check the error messages above.');
    process.exit(1);
  }
}

// Run the tests
runTests().catch(error => {
  console.error('Unexpected error during tests:', error);
  process.exit(1);
});

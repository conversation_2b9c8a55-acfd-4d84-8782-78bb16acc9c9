-- Test Case 1: Sufficient quota for real-time transcription
DO $$
DECLARE
    test_user_id UUID := '11111111-1111-1111-1111-111111111111';
    test_amount NUMERIC := 1000; -- Minutes
BEGIN
    -- Setup test user with sufficient quota
    INSERT INTO public.quotas(user_id, service, subscription_id, total_amount, used_amount)
    VALUES (test_user_id, 'transcription', '*************-2222-2222-************', 2000, 0);
    
    -- Execute real-time transcription usage
    PERFORM public.finalize_usage(
        test_user_id,
        'transcription',
        'realtime-v1',
        test_amount,
        0.01, -- Cost per minute
        'subscription',
        '{"inputTokens": 0, "outputTokens": 0}'::jsonb
    );
    
    -- Verify results
    IF (SELECT used_amount FROM public.quotas 
        WHERE user_id = test_user_id AND service = 'transcription') = test_amount THEN
        RAISE NOTICE 'TEST 1 PASSED: Quota properly deducted';
    ELSE
        RAISE EXCEPTION 'TEST 1 FAILED: Quota not deducted correctly';
    END IF;
    
    -- Cleanup
    DELETE FROM public.quotas WHERE user_id = test_user_id;
    DELETE FROM public.usage_history WHERE user_id = test_user_id;
END $$;
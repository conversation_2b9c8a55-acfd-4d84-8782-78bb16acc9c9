#!/bin/bash
# update-supabase-config.sh - <PERSON><PERSON><PERSON> to update Supabase Docker configuration for external email service

# Check if arguments are provided
if [ "$#" -ne 2 ]; then
  echo "Usage: $0 <webhook_url> <webhook_secret>"
  echo "Example: $0 https://voicehype-email-service.deno.dev whsec_YourWebhookSecretHere1234567890"
  exit 1
fi

WEBHOOK_URL=$1
WEBHOOK_SECRET=$2
SUPABASE_DIR="/home/<USER>/Documents/cursor_extensions/voicehype/docker"

# Check if Supabase directory exists
if [ ! -d "$SUPABASE_DIR" ]; then
  echo "Error: Supabase directory not found at $SUPABASE_DIR."
  echo "Please provide the correct path to your Supabase docker installation."
  exit 1
fi

# Check if .env file exists
if [ ! -f "$SUPABASE_DIR/.env" ]; then
  echo "Error: .env file not found in Supabase directory."
  echo "Please make sure your Supabase installation is correctly set up."
  exit 1
fi

# Backup the original .env file
cp "$SUPABASE_DIR/.env" "$SUPABASE_DIR/.env.backup-$(date +%Y%m%d%H%M%S)"
echo "Created backup of .env file."

# Update the .env file
echo "Updating Supabase .env file with external email service configuration..."

# Remove commented SMTP settings if they exist and add our configuration
sed -i '/^# SMTP_HOST=/d' "$SUPABASE_DIR/.env"
sed -i '/^# SMTP_PORT=/d' "$SUPABASE_DIR/.env"
sed -i '/^# SMTP_USER=/d' "$SUPABASE_DIR/.env"
sed -i '/^# SMTP_PASS=/d' "$SUPABASE_DIR/.env"

# Update or add the external email service configuration
if grep -q "GOTRUE_HOOK_SEND_EMAIL_ENABLED" "$SUPABASE_DIR/.env"; then
  sed -i "s|^GOTRUE_HOOK_SEND_EMAIL_ENABLED=.*|GOTRUE_HOOK_SEND_EMAIL_ENABLED=true|" "$SUPABASE_DIR/.env"
else
  echo "GOTRUE_HOOK_SEND_EMAIL_ENABLED=true" >> "$SUPABASE_DIR/.env"
fi

if grep -q "GOTRUE_HOOK_SEND_EMAIL_URI" "$SUPABASE_DIR/.env"; then
  sed -i "s|^GOTRUE_HOOK_SEND_EMAIL_URI=.*|GOTRUE_HOOK_SEND_EMAIL_URI=$WEBHOOK_URL|" "$SUPABASE_DIR/.env"
else
  echo "GOTRUE_HOOK_SEND_EMAIL_URI=$WEBHOOK_URL" >> "$SUPABASE_DIR/.env"
fi

if grep -q "GOTRUE_HOOK_SEND_EMAIL_SECRETS" "$SUPABASE_DIR/.env"; then
  sed -i "s|^GOTRUE_HOOK_SEND_EMAIL_SECRETS=.*|GOTRUE_HOOK_SEND_EMAIL_SECRETS=$WEBHOOK_SECRET|" "$SUPABASE_DIR/.env"
else
  echo "GOTRUE_HOOK_SEND_EMAIL_SECRETS=$WEBHOOK_SECRET" >> "$SUPABASE_DIR/.env"
fi

if grep -q "GOTRUE_MAILER_EXTERNAL_HOSTS" "$SUPABASE_DIR/.env"; then
  sed -i "s|^GOTRUE_MAILER_EXTERNAL_HOSTS=.*|GOTRUE_MAILER_EXTERNAL_HOSTS=supabase.voicehype.ai,voicehype.ai|" "$SUPABASE_DIR/.env"
else
  echo "GOTRUE_MAILER_EXTERNAL_HOSTS=supabase.voicehype.ai,voicehype.ai" >> "$SUPABASE_DIR/.env"
fi

echo "Supabase configuration updated successfully!"
echo ""
echo "To apply these changes, restart your Supabase services with:"
echo "cd $SUPABASE_DIR"
echo "docker-compose down"
echo "docker-compose up -d"
echo ""
echo "After restarting, check the auth logs for any issues:"
echo "docker logs supabase-auth"

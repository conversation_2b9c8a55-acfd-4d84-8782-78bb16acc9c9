// Complete email-service.ts for Deno Deploy with comprehensive logging
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const ALLOWED_ORIGINS = [
  "https://supabase.voicehype.ai",
  "https://voicehype.ai",
];

// Email configuration - Store these as Deno Deploy environment variables
const RESEND_API_KEY = Deno.env.get("RESEND_API_KEY") || "";
const FROM_EMAIL = Deno.env.get("FROM_EMAIL") || "<EMAIL>";
const FROM_NAME = Deno.env.get("FROM_NAME") || "VoiceHype";

// Supabase configuration
const SUPABASE_URL = "https://supabase.voicehype.ai";
const SITE_URL = "https://voicehype.ai";

function logSection(title: string, data?: any) {
  console.log(`\n=== ${title.toUpperCase()} ===`);
  if (data) {
    console.log(JSON.stringify(data, null, 2));
  }
  console.log(`=== END ${title.toUpperCase()} ===\n`);
}

async function sendEmail(emailData: any) {
  logSection("SENDING EMAIL", {
    to: emailData.email,
    subject: emailData.subject,
    htmlPreview: emailData.html?.substring(0, 200) + "...",
  });
  
  if (!RESEND_API_KEY) {
    console.log("⚠️ No RESEND_API_KEY configured. Email not actually sent.");
    return { success: true, note: "Email not actually sent - no API key" };
  }

  try {
    const response = await fetch("https://api.resend.com/emails", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${RESEND_API_KEY}`
      },
      body: JSON.stringify({
        from: `${FROM_NAME} <${FROM_EMAIL}>`,
        to: emailData.email,
        subject: emailData.subject,
        html: emailData.html || emailData.content,
      })
    });

    const result = await response.json();
    
    if (!response.ok) {
      console.error("❌ Failed to send email via Resend:", result);
      throw new Error(`Email sending failed: ${JSON.stringify(result)}`);
    }
    
    console.log("✅ Email sent successfully:", result);
    return { success: true, emailId: result.id };
  } catch (error) {
    console.error("❌ Email sending error:", error);
    throw error;
  }
}

serve(async (req) => {
  const timestamp = new Date().toISOString();
  console.log(`\n🚀 Request received at ${timestamp}`);
  console.log(`📍 Method: ${req.method}`);
  console.log(`🔗 URL: ${req.url}`);
  console.log(`🌐 Origin: ${req.headers.get("Origin") || "No Origin"}`);
  console.log(`🔑 User-Agent: ${req.headers.get("User-Agent") || "No User-Agent"}`);
  
  const corsHeaders = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
    "Access-Control-Allow-Headers": "*",
    "Access-Control-Allow-Credentials": "true"
  };

  // Handle preflight requests
  if (req.method === "OPTIONS") {
    console.log("✅ Handling OPTIONS preflight request");
    return new Response(null, { headers: corsHeaders });
  }

  // Health check endpoint
  if (req.method === "GET") {
    const url = new URL(req.url);
    if (url.pathname === "/health" || url.pathname === "/" || url.pathname === "/test") {
      console.log("💚 Health check requested");
      return new Response(JSON.stringify({
        status: "healthy",
        time: timestamp,
        service: "VoiceHype Email Service",
        environment: {
          hasResendKey: !!RESEND_API_KEY,
          fromEmail: FROM_EMAIL,
          fromName: FROM_NAME,
          supabaseUrl: SUPABASE_URL,
          siteUrl: SITE_URL,
        }
      }), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }
  }

  // Handle POST requests (email sending)
  if (req.method === "POST") {
    try {
      // Read raw request body
      const rawText = await req.text();
      console.log("📝 Raw request body length:", rawText.length);
      console.log("📝 Raw request body:", rawText);
      
      // Parse JSON
      let data;
      try {
        data = JSON.parse(rawText);
      } catch (e) {
        console.error("❌ Failed to parse request body as JSON:", e);
        return new Response(JSON.stringify({ 
          error: "Invalid JSON", 
          details: e.message,
          receivedData: rawText.substring(0, 500) + "..."
        }), {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        });
      }

      logSection("PARSED WEBHOOK PAYLOAD", data);

      // Extract email information based on the GoTrue webhook format
      let emailData;
      
      if (data.user && data.email_data) {
        console.log("🔍 Processing Supabase Auth webhook data");
        
        const user = data.user;
        const emailDataPayload = data.email_data;
        
        logSection("USER DATA", {
          id: user.id,
          email: user.email,
          created_at: user.created_at,
          email_confirmed_at: user.email_confirmed_at,
        });

        logSection("EMAIL DATA PAYLOAD", emailDataPayload);

        const emailAction = emailDataPayload.email_action_type || "signup";
        const token = emailDataPayload.token || "";
        const tokenHash = emailDataPayload.token_hash || "";
        const redirectTo = `${SITE_URL}/dashboard`;
        const siteUrl = emailDataPayload.site_url || SITE_URL;
        
        console.log("📊 Extracted values:");
        console.log(`  - Email Action: ${emailAction}`);
        console.log(`  - Token: ${token}`);
        console.log(`  - Token Hash: ${tokenHash}`);
        console.log(`  - Redirect To: ${redirectTo}`);
        console.log(`  - Site URL: ${siteUrl}`);
        
        // CRITICAL: Use Supabase URL for verification endpoint
        const confirmUrl = `${SUPABASE_URL}/auth/v1/verify?token=${tokenHash}&type=${emailAction}&redirect_to=${encodeURIComponent(redirectTo)}`;
        
        console.log("🔗 Generated confirmation URL:", confirmUrl);
        
        // Generate email content based on action type
        let subject, htmlContent;
        
        switch (emailAction) {
          case "signup":
            subject = "Confirm Your VoiceHype Account";
            htmlContent = `
              <!DOCTYPE html>
              <html>
              <head>
                <meta charset="utf-8">
                <title>${subject}</title>
              </head>
              <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                  <h2 style="color: #4CAF50;">Welcome to VoiceHype!</h2>
                  <p>Thank you for signing up. Please confirm your account by clicking the link below:</p>
                  
                  <div style="text-align: center; margin: 30px 0;">
                    <a href="${confirmUrl}" style="display: inline-block; padding: 12px 24px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 6px; font-weight: bold;">Confirm My Account</a>
                  </div>
                  
                  <p>Or copy and paste this URL into your browser:</p>
                  <p style="word-break: break-all; background-color: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace;">${confirmUrl}</p>
                  
                  <p>Your confirmation code is: <strong>${token}</strong></p>
                  
                  <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
                  <p style="font-size: 14px; color: #666;">If you didn't sign up for VoiceHype, you can safely ignore this email.</p>
                </div>
              </body>
              </html>
            `;
            break;
            
          case "recovery":
            const resetUrl = `${SITE_URL}/reset-password#access_token=${token}&refresh_token=${tokenHash}&type=recovery`;
            subject = "Reset Your VoiceHype Password";
            htmlContent = `
              <!DOCTYPE html>
              <html>
              <head>
                <meta charset="utf-8">
                <title>${subject}</title>
              </head>
              <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                  <h2 style="color: #2196F3;">Password Reset</h2>
                  <p>We received a request to reset your password. Click the link below to set a new password:</p>
                  
                  <div style="text-align: center; margin: 30px 0;">
                    <a href="${resetUrl}" style="display: inline-block; padding: 12px 24px; background-color: #2196F3; color: white; text-decoration: none; border-radius: 6px; font-weight: bold;">Reset My Password</a>
                  </div>
                  
                  <p>Or copy and paste this URL into your browser:</p>
                  <p style="word-break: break-all; background-color: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace;">${resetUrl}</p>
                  
                  <p>Your reset code is: <strong>${token}</strong></p>
                  
                  <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
                  <p style="font-size: 14px; color: #666;">If you didn't request a password reset, you can safely ignore this email.</p>
                </div>
              </body>
              </html>
            `;
            break;
            
          case "invite":
            subject = "You've been invited to VoiceHype";
            htmlContent = `
              <!DOCTYPE html>
              <html>
              <head>
                <meta charset="utf-8">
                <title>${subject}</title>
              </head>
              <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                  <h2 style="color: #FF9800;">You're Invited!</h2>
                  <p>You've been invited to join VoiceHype. Click the link below to accept the invitation:</p>
                  
                  <div style="text-align: center; margin: 30px 0;">
                    <a href="${confirmUrl}" style="display: inline-block; padding: 12px 24px; background-color: #FF9800; color: white; text-decoration: none; border-radius: 6px; font-weight: bold;">Accept Invitation</a>
                  </div>
                  
                  <p>Or copy and paste this URL into your browser:</p>
                  <p style="word-break: break-all; background-color: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace;">${confirmUrl}</p>
                  
                  <p>Your invitation code is: <strong>${token}</strong></p>
                  
                  <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
                  <p style="font-size: 14px; color: #666;">If you weren't expecting this invitation, you can safely ignore this email.</p>
                </div>
              </body>
              </html>
            `;
            break;
            
          default:
            subject = "Action Required for Your VoiceHype Account";
            htmlContent = `
              <!DOCTYPE html>
              <html>
              <head>
                <meta charset="utf-8">
                <title>${subject}</title>
              </head>
              <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                  <h2 style="color: #607D8B;">Action Required</h2>
                  <p>Please click the link below to continue:</p>
                  
                  <div style="text-align: center; margin: 30px 0;">
                    <a href="${confirmUrl}" style="display: inline-block; padding: 12px 24px; background-color: #607D8B; color: white; text-decoration: none; border-radius: 6px; font-weight: bold;">Continue</a>
                  </div>
                  
                  <p>Or copy and paste this URL into your browser:</p>
                  <p style="word-break: break-all; background-color: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace;">${confirmUrl}</p>
                  
                  <p>Your verification code is: <strong>${token}</strong></p>
                  
                  <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
                  <p style="font-size: 14px; color: #666;">If you didn't request this action, you can safely ignore this email.</p>
                </div>
              </body>
              </html>
            `;
        }
        
        emailData = {
          email: user.email,
          subject: subject,
          content: `Please verify your account: ${confirmUrl}`,
          html: htmlContent
        };

        logSection("PREPARED EMAIL DATA", {
          email: emailData.email,
          subject: emailData.subject,
          confirmUrl: confirmUrl,
          htmlLength: htmlContent.length,
        });
        
      } else if (data.email || data.to) {
        // Handle direct email format (fallback)
        console.log("🔍 Processing direct email format");
        
        emailData = {
          email: data.email || data.to,
          subject: data.subject || "VoiceHype Notification",
          content: data.text || data.content || "",
          html: data.html || `<p>${data.text || data.content || "VoiceHype Notification"}</p>`
        };

        logSection("DIRECT EMAIL DATA", emailData);
        
      } else {
        console.error("❌ Unrecognized email format");
        logSection("UNRECOGNIZED FORMAT", data);
        
        return new Response(JSON.stringify({ 
          error: "Unrecognized email format",
          expectedFormats: [
            "Supabase Auth webhook (user + email_data)",
            "Direct email (email/to + subject + content/html)"
          ],
          receivedKeys: Object.keys(data)
        }), {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        });
      }
      
      // Send the email
      console.log("📧 Attempting to send email...");
      const result = await sendEmail(emailData);
      
      const response = { 
        success: true, 
        message: "Email processed successfully",
        timestamp: timestamp,
        emailSent: result.success,
        emailId: result.emailId || null,
      };

      logSection("RESPONSE", response);
      
      return new Response(JSON.stringify(response), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
      
    } catch (error) {
      console.error("❌ Error processing email request:");
      console.error("Error message:", error.message);
      console.error("Error stack:", error.stack);
      
      const errorResponse = { 
        error: error.message, 
        stack: error.stack,
        timestamp: timestamp,
        service: "VoiceHype Email Service"
      };

      logSection("ERROR RESPONSE", errorResponse);
      
      return new Response(JSON.stringify(errorResponse), {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }
  }

  // Method not allowed
  console.log("❌ Method not allowed:", req.method);
  return new Response(JSON.stringify({ 
    error: "Method not allowed", 
    allowedMethods: ["GET", "POST", "OPTIONS"],
    received: req.method
  }), { 
    status: 405, 
    headers: { ...corsHeaders, "Content-Type": "application/json" }
  });
});
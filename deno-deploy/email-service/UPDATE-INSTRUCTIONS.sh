#!/bin/bash
# update-email-service.sh - <PERSON><PERSON>t to update to the Resend API version

echo "===== VoiceHype Email Service Update Guide ====="
echo ""
echo "Follow these steps to update your email service to use Resend.com API:"
echo ""
echo "1. Sign up at https://resend.com and get an API key"
echo ""
echo "2. Update your code and .env file:"
echo "   mv email-service.new.ts email-service.ts"
echo "   cp .env.example .env    # If you don't have an .env file yet"
echo "   nano .env               # Edit to add your Resend API key"
echo ""
echo "3. Deploy to Deno using the quick deploy script:"
echo "   ./quick-deploy.sh"
echo ""
echo "4. Test your email service by trying to sign up in your app"
echo ""
echo "5. Check the logs in the Deno Deploy dashboard"
echo ""
echo "For more details, see the documentation at:"
echo "/home/<USER>/Documents/cursor_extensions/voicehype/docs/deno-resend-email-service.md"
echo ""
echo "If you have any issues, please check the Deno Deploy logs and Resend dashboard."
echo ""

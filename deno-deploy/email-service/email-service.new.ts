// email-service.ts for Deno Deploy
import { serve } from "https://deno.land/std@0.140.0/http/server.ts";

const ALLOWED_ORIGINS = [
  "https://supabase.voicehype.ai",
  "https://voicehype.ai",
];

// Email configuration - Store these as Deno Deploy environment variables
const RESEND_API_KEY = Deno.env.get("RESEND_API_KEY") || "";
const FROM_EMAIL = Deno.env.get("FROM_EMAIL") || "<EMAIL>";
const FROM_NAME = Deno.env.get("FROM_NAME") || "VoiceHype";

// Webhook secret for security
const WEBHOOK_SECRET = Deno.env.get("WEBHOOK_SECRET") || "";

async function sendEmail(emailData: any) {
  console.log(`Sending email to: ${emailData.email}`);
  
  if (!RESEND_API_KEY) {
    console.log("No RESEND_API_KEY configured. Would have sent:");
    console.log(`- To: ${emailData.email}`);
    console.log(`- Subject: ${emailData.subject}`);
    console.log(`- HTML: ${emailData.html.substring(0, 200)}...`);
    
    // For testing, return success even without sending
    return { success: true, note: "Email not actually sent - no API key" };
  }

  // Use Resend.com API to send email (HTTP-based, no SMTP port restrictions)
  const response = await fetch("https://api.resend.com/emails", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${RESEND_API_KEY}`
    },
    body: JSON.stringify({
      from: `${FROM_NAME} <${FROM_EMAIL}>`,
      to: emailData.email,
      subject: emailData.subject,
      html: emailData.html || emailData.content,
    })
  });

  const result = await response.json();
  
  if (!response.ok) {
    console.error("Failed to send email via Resend:", result);
    throw new Error(`Email sending failed: ${JSON.stringify(result)}`);
  }
  
  console.log("Email sent successfully:", result);
  return { success: true };
}

serve(async (req) => {
  // Log the request details for debugging
  console.log("Request received:");
  console.log(`- Method: ${req.method}`);
  console.log(`- URL: ${req.url}`);
  console.log(`- Origin: ${req.headers.get("Origin") || "No Origin"}`);
  
  // CORS handling - Accept all origins for debugging
  const corsHeaders = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
    "Access-Control-Allow-Headers": "*",
    "Access-Control-Allow-Credentials": "true"
  };

  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  // Health check endpoint
  if (req.method === "GET") {
    const url = new URL(req.url);
    if (url.pathname === "/health" || url.pathname === "/" || url.pathname === "/test") {
      return new Response(JSON.stringify({
        status: "healthy",
        time: new Date().toISOString(),
        service: "VoiceHype Email Service",
      }), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }
  }

  // Handle POST requests (email sending)
  if (req.method === "POST") {
    try {
      // Clone the request before reading body to avoid "body already read" errors
      const reqClone = req.clone();
      
      // Read as text first so we can log raw data
      const rawText = await req.text();
      console.log("Raw request body:", rawText);
      
      // Parse the JSON data
      let data;
      try {
        data = JSON.parse(rawText);
      } catch (e) {
        console.error("Failed to parse request body as JSON:", e);
        return new Response(JSON.stringify({ 
          error: "Invalid JSON", 
          details: e.message 
        }), {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        });
      }
      
      // Log the parsed data
      console.log("Parsed data:", JSON.stringify(data, null, 2));
      
      // Extract email information based on the GoTrue webhook format
      let emailData;
      
      // Check if this is a Supabase Auth webhook
      if (data.user && data.email_data) {
        // This is a GoTrue auth email
        console.log("Processing Supabase Auth webhook data");
        
        const user = data.user;
        const emailAction = data.email_data.email_action_type || "confirmation";
        const token = data.email_data.token || "";
        const redirectTo = data.email_data.redirect_to || "";
        const siteUrl = data.email_data.site_url || "https://supabase.voicehype.ai";
        
        // Generate confirmation link
        const confirmUrl = `${siteUrl}/auth/v1/verify?token=${token}&type=${emailAction}&redirect_to=${encodeURIComponent(redirectTo)}`;
        
        // Generate appropriate email content based on the action type
        let subject, htmlContent;
        
        switch (emailAction) {
          case "signup":
            subject = "Confirm Your VoiceHype Account";
            htmlContent = `
              <h2>Welcome to VoiceHype!</h2>
              <p>Thank you for signing up. Please confirm your account by clicking the link below:</p>
              <p><a href="${confirmUrl}" style="padding: 10px 15px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 4px;">Confirm My Account</a></p>
              <p>Or copy and paste this URL into your browser:</p>
              <p>${confirmUrl}</p>
              <p>Your confirmation code is: <strong>${token}</strong></p>
              <p>If you didn't sign up for VoiceHype, you can safely ignore this email.</p>
            `;
            break;
            
          case "recovery":
            subject = "Reset Your VoiceHype Password";
            htmlContent = `
              <h2>Password Reset</h2>
              <p>We received a request to reset your password. Click the link below to set a new password:</p>
              <p><a href="${confirmUrl}" style="padding: 10px 15px; background-color: #2196F3; color: white; text-decoration: none; border-radius: 4px;">Reset My Password</a></p>
              <p>Or copy and paste this URL into your browser:</p>
              <p>${confirmUrl}</p>
              <p>Your reset code is: <strong>${token}</strong></p>
              <p>If you didn't request a password reset, you can safely ignore this email.</p>
            `;
            break;
            
          default:
            subject = "Action Required for Your VoiceHype Account";
            htmlContent = `
              <h2>Action Required</h2>
              <p>Please click the link below to continue:</p>
              <p><a href="${confirmUrl}" style="padding: 10px 15px; background-color: #607D8B; color: white; text-decoration: none; border-radius: 4px;">Continue</a></p>
              <p>Or copy and paste this URL into your browser:</p>
              <p>${confirmUrl}</p>
              <p>Your verification code is: <strong>${token}</strong></p>
            `;
        }
        
        emailData = {
          email: user.email,
          subject: subject,
          content: `Please verify your account: ${confirmUrl}`, // Plain text fallback
          html: htmlContent
        };
      } else {
        // Handle direct email data format
        emailData = {
          email: data.email || "<EMAIL>",
          subject: data.subject || "VoiceHype Notification",
          content: data.text || data.content || "",
          html: data.html || `<p>${data.text || data.content || "VoiceHype Notification"}</p>`
        };
      }
      
      console.log(`Preparing to send email to: ${emailData.email}`);
      console.log(`Subject: ${emailData.subject}`);
      
      // Send the email
      const result = await sendEmail(emailData);
      
      return new Response(JSON.stringify({ 
        success: true, 
        message: "Email processed successfully"
      }), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    } catch (error) {
      console.error("Error processing email request:", error);
      return new Response(JSON.stringify({ 
        error: error.message, 
        stack: error.stack,
        time: new Date().toISOString() 
      }), {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }
  }

  // If we reach here, the request method is not supported
  return new Response(JSON.stringify({ 
    error: "Method not allowed", 
    allowedMethods: ["GET", "POST", "OPTIONS"] 
  }), { 
    status: 405, 
    headers: corsHeaders 
  });
});

#!/bin/bash
# deploy.sh - <PERSON><PERSON><PERSON> to deploy the email service to Deno Deploy

# Change to the script's directory
cd "$(dirname "$0")"

# Check if .env file exists
if [ ! -f .env ]; then
  echo "Error: .env file not found. Please create it first."
  exit 1
fi

# Load environment variables from .env file
set -a
source .env
set +a

# Check for required environment variables
REQUIRED_VARS=("RESEND_API_KEY" "FROM_EMAIL" "FROM_NAME" "WEBHOOK_SECRET" "DENO_DEPLOY_TOKEN" "DENO_DEPLOY_PROJECT_NAME")
MISSING_VARS=()

for var in "${REQUIRED_VARS[@]}"; do
  if [ -z "${!var}" ]; then
    MISSING_VARS+=("$var")
  fi
done

if [ ${#MISSING_VARS[@]} -ne 0 ]; then
  echo "Error: The following required environment variables are missing in .env file:"
  for var in "${MISSING_VARS[@]}"; do
    echo " - $var"
  done
  echo "Please update your .env file and try again."
  exit 1
fi

# Check if deno is installed
if ! command -v deno &> /dev/null; then
  echo "Error: Deno is not installed. Please install it first."
  echo "You can install Deno with: curl -fsSL https://deno.land/x/install/install.sh | sh"
  exit 1
fi

# Install deployctl if not already installed
if ! command -v deployctl &> /dev/null; then
  echo "Installing deployctl..."
  deno install --allow-all --no-check -r -f https://deno.land/x/deploy/deployctl.ts
fi

# Login to Deno Deploy if not already logged in
if ! deployctl whoami &> /dev/null; then
  echo "Logging in to Deno Deploy..."
  deployctl login
fi

# Deploy the project
echo "Deploying email service to Deno Deploy..."
deployctl deploy --project="$DENO_DEPLOY_PROJECT_NAME" --token="$DENO_DEPLOY_TOKEN" ./email-service.ts

# Set environment variables in Deno Deploy
echo "Setting environment variables in Deno Deploy..."
RESPONSE=$(curl -s -X PATCH \
  https://api.deno.com/v1/projects/$DENO_DEPLOY_PROJECT_NAME/env \
  -H "Authorization: Bearer $DENO_DEPLOY_TOKEN" \
  -H "Content-Type: application/json" \
  -d "{
    \"RESEND_API_KEY\": \"$RESEND_API_KEY\",
    \"FROM_EMAIL\": \"$FROM_EMAIL\",
    \"FROM_NAME\": \"$FROM_NAME\",
    \"WEBHOOK_SECRET\": \"$WEBHOOK_SECRET\"
  }")

if [[ $RESPONSE == *"error"* ]]; then
  echo "Error setting environment variables:"
  echo "$RESPONSE"
  exit 1
fi

# Get the deployment URL
DEPLOYMENT_URL="https://$DENO_DEPLOY_PROJECT_NAME.deno.dev"
echo "Deployment completed successfully!"
echo "Your email service is now available at: $DEPLOYMENT_URL"
echo ""
echo "To update your Supabase configuration, add the following to your .env file:"
echo "GOTRUE_HOOK_SEND_EMAIL_ENABLED=true"
echo "GOTRUE_HOOK_SEND_EMAIL_URI=$DEPLOYMENT_URL"
echo "GOTRUE_HOOK_SEND_EMAIL_SECRETS=$WEBHOOK_SECRET"
echo "GOTRUE_MAILER_EXTERNAL_HOSTS=supabase.voicehype.ai,voicehype.ai"

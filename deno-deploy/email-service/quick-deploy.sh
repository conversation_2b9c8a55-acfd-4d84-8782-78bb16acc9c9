#!/bin/bash
# quick-deploy.sh - Simple script to quickly redeploy email service

# Change to the script's directory
cd "$(dirname "$0")"

# Check if deployctl is installed
if ! command -v deployctl &> /dev/null; then
  echo "Installing deployctl..."
  deno install --allow-all --no-check -r -f https://deno.land/x/deploy/deployctl.ts
fi

# Load environment variables from .env file
if [ -f .env ]; then
  set -a
  source .env
  set +a
else
  echo "Warning: No .env file found. Make sure DENO_DEPLOY_TOKEN and DENO_DEPLOY_PROJECT_NAME are set in your environment."
fi

# Deploy the project
echo "Deploying email service to Deno Deploy..."
deployctl deploy --project="$DENO_DEPLOY_PROJECT_NAME" --token="$DENO_DEPLOY_TOKEN" ./email-service.ts

echo "Deployment completed!"
echo "Your service is available at: https://$DENO_DEPLOY_PROJECT_NAME.deno.dev"

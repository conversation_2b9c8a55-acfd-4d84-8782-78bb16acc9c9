# VoiceHype Email Service for Deno Deploy

This folder contains the email service for VoiceHype, designed to be deployed on Deno Deploy. This service allows Supabase to send authentication emails by bypassing DigitalOcean's SMTP port restrictions.

## Setup Instructions

### 1. Configure Environment Variables

Edit the `.env` file in this directory and fill in all required variables:

```
# Email Service Environment Variables
SMTP_HOST=smtpout.secureserver.net
SMTP_PORT=465
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password
FROM_EMAIL=<EMAIL>
FROM_NAME=VoiceHype
WEBHOOK_SECRET=generate_a_strong_random_string

# Deno Deploy project settings
DENO_DEPLOY_PROJECT_NAME=voicehype-email-service
DENO_DEPLOY_TOKEN=your_deno_deploy_token
```

**Notes:**
- Generate a strong random string for `WEBHOOK_SECRET`
- Get your Deno Deploy token from the Deno Deploy dashboard
- Create a new project in Deno Deploy named `voicehype-email-service` (or your preferred name)

### 2. Deploy the Email Service

Run the deployment script:

```bash
./deploy.sh
```

This script will:
1. Check for required environment variables
2. Install `deployctl` if needed
3. Deploy the email service to Deno Deploy
4. Set up environment variables in Deno Deploy

### 3. Update Supabase Configuration

After deploying the email service, run the Supabase configuration update script:

```bash
./update-supabase-config.sh https://your-project-name.deno.dev your_webhook_secret
```

Replace:
- `https://your-project-name.deno.dev` with your actual Deno Deploy URL
- `your_webhook_secret` with the same webhook secret from your `.env` file

### 4. Restart Supabase Services

Navigate to your Supabase Docker directory and restart the services:

```bash
cd /path/to/your/supabase/docker
docker-compose down
docker-compose up -d
```

### 5. Test Email Authentication

Test the email authentication by signing up a new user in your application.

## Troubleshooting

If you encounter issues, check:

1. Deno Deploy logs in the Deno Deploy dashboard
2. Supabase Auth logs with:
   ```bash
   docker logs supabase-auth
   ```
3. Test the email service directly:
   ```bash
   curl -X POST https://your-project-name.deno.dev \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer your_webhook_secret" \
     -d '{"email":"<EMAIL>", "subject":"Test Email", "text":"This is a test", "html":"<p>This is a test</p>"}'
   ```

## Security Notes

- Keep your `.env` file secure and never commit it to version control
- Regularly rotate your webhook secret for enhanced security
- Ensure your Deno Deploy account is secured with strong credentials

#!/bin/bash
# restart-supabase.sh - <PERSON><PERSON><PERSON> to properly restart Supabase services

# Change to the Supabase directory
cd "$(dirname "$0")"

echo "Stopping Supabase services..."
docker-compose down

echo "Pruning any stopped containers..."
docker container prune -f

echo "Starting Supabase services..."
docker-compose up -d

echo "Waiting for services to initialize..."
sleep 10

echo "Checking auth service status..."
AUTH_STATUS=$(docker ps -f name=supabase-auth --format "{{.Status}}")

if [[ $AUTH_STATUS == *"Up"* ]]; then
  echo "✅ Auth service is running correctly!"
else
  echo "⚠️ Auth service may still have issues. Checking logs..."
  docker logs supabase-auth
fi

echo "All other services status:"
docker ps -f name=supabase

echo ""
echo "If auth service is still restarting, check the logs with:"
echo "docker logs supabase-auth"

import * as vscode from "vscode"
import fs from "fs/promises"
import path from "path"
import os from "os"
import CheckpointTracker from "./CheckpointTracker"

export async function createTestEnvironment() {
    // Create temp directory structure
    const tempDir = path.join(os.tmpdir(), `checkpoint-test-${Date.now()}`)
    await fs.mkdir(tempDir, { recursive: true })

    // Create storage path outside of working directory to avoid submodule issues
    const globalStoragePath = path.join(os.tmpdir(), `storage-${Date.now()}`)
    await fs.mkdir(globalStoragePath, { recursive: true })

    // Create test file in a subdirectory
    const testDir = path.join(tempDir, "src")
    await fs.mkdir(testDir, { recursive: true })
    const testFilePath = path.join(testDir, "test.txt")

    // Create .gitignore to prevent git from treating directories as submodules
    await fs.writeFile(path.join(tempDir, ".gitignore"), "storage/\n")

    // Mock VS Code workspace
    const mockWorkspaceFolders = [
        {
            uri: { fsPath: tempDir },
            name: "test",
            index: 0,
        },
    ]

    const originalDescriptor = Object.getOwnPropertyDescriptor(vscode.workspace, "workspaceFolders")
    Object.defineProperty(vscode.workspace, "workspaceFolders", {
        get: () => mockWorkspaceFolders,
    })

    // Mock findFiles to return no nested git repos
    const originalFindFiles = vscode.workspace.findFiles
    vscode.workspace.findFiles = async () => []

    // Mock VS Code configuration
    const originalGetConfiguration = vscode.workspace.getConfiguration
    vscode.workspace.getConfiguration = () =>
        ({
            get: (key: string) => (key === "enableCheckpoints" ? true : undefined),
        }) as any

    return {
        tempDir,
        globalStoragePath,
        testFilePath,
        originalDescriptor,
        originalFindFiles,
        originalGetConfiguration,
        cleanup: async () => {
            // Restore VS Code mocks
            if (originalDescriptor) {
                Object.defineProperty(vscode.workspace, "workspaceFolders", originalDescriptor)
            }
            vscode.workspace.getConfiguration = originalGetConfiguration
            vscode.workspace.findFiles = originalFindFiles

            // Clean up temp directories
            await fs.rm(tempDir, { recursive: true, force: true })
            await fs.rm(globalStoragePath, { recursive: true, force: true })
        }
    }
}

export async function createTestTracker(globalStoragePath?: string, taskId = "test-task-1") {
    return await CheckpointTracker.create(taskId, globalStoragePath)
}

name: 🐛 Bug Report
description: File a bug report
labels: ["bug"]
body:
    - type: markdown
      attributes:
          value: |
              **Important:** All bug reports must be reproducible using Claude 3.5 Sonnet. Cline uses complex prompts so less capable models may not work as expected.
    - type: textarea
      id: what-happened
      attributes:
          label: What happened?
          description: Also tell us, what did you expect to happen?
          placeholder: Tell us what you see!
      validations:
          required: true
    - type: textarea
      id: steps
      attributes:
          label: Steps to reproduce
          description: How do you trigger this bug? Please walk us through it step by step.
          value: |
              1.
              2.
              3.
      validations:
          required: true
    - type: textarea
      id: logs
      attributes:
          label: Relevant API REQUEST output
          description: Please copy and paste any relevant output. This will be automatically formatted into code, so no need for backticks.
          render: shell
    - type: input
      id: operating-system
      attributes:
          label: Operating System
          description: What operating system are you using?
          placeholder: "e.g., Windows 11, macOS Sonoma, Ubuntu 22.04"
      validations:
          required: true
    - type: input
      id: cline-version
      attributes:
          label: Cline Version
          description: What version of Cline are you using? (You can find this at the bottom of the Settings view)
          placeholder: "e.g., 1.2.3"
      validations:
          required: true
    - type: textarea
      id: additional-context
      attributes:
          label: Additional context
          description: Add any other context about the problem here, such as screenshots or related issues.

name: Check Changeset
run-name: Check for Changeset in PR

permissions:
    contents: read
    pull-requests: write

on:
    pull_request:
        branches:
            - main
        types: [opened, synchronize, reopened, ready_for_review]

jobs:
    check-changeset:
        # Skip draft PRs and dependabot PRs
        if: github.event.pull_request.draft == false && github.actor != 'dependabot[bot]'
        runs-on: ubuntu-latest
        steps:
            - name: Checkout
              uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4
              with:
                  fetch-depth: 0
                  ref: ${{ github.event.pull_request.head.sha }}

            - name: Check for changeset
              id: check-changeset
              run: |
                  # Debug info
                  echo "Current directory: $(pwd)"
                  echo "PR Base Ref: ${{ github.event.pull_request.base.ref }}"
                  echo "PR Head Ref: ${{ github.event.pull_request.head.ref }}"
                  echo "PR Head SHA: ${{ github.event.pull_request.head.sha }}"
                  echo "Git status:"
                  git status

                  # Get list of changed files
                  git fetch origin ${{ github.event.pull_request.base.ref }}
                  CHANGED_FILES=$(git diff --name-only origin/${{ github.event.pull_request.base.ref }} HEAD)
                  echo "Changed files:"
                  echo "$CHANGED_FILES"

                  # Check if any of the changed files are in docs/ or .github/
                  echo "Checking if changes are docs-only..."
                  DOCS_ONLY=true
                  while IFS= read -r file; do
                    if [[ ! "$file" =~ ^(docs/|.github/) ]]; then
                      echo "Found non-docs change: $file"
                      DOCS_ONLY=false
                      break
                    fi
                  done <<< "$CHANGED_FILES"

                  # If changes are docs-only, skip changeset check
                  if [ "$DOCS_ONLY" = true ]; then
                    echo "All changes are in docs/ or .github/, skipping changeset check"
                    exit 0
                  else
                    echo "Changes include non-docs files, checking for changeset..."
                  fi

                  # Check if any changeset files are in the changed files
                  echo "Checking for changeset files in changed files..."
                  CHANGESET_IN_PR=false
                  while IFS= read -r file; do
                    if [[ "$file" =~ ^\.changeset/.*\.md$ && "$file" != ".changeset/README.md" && "$file" != ".changeset/config.json" ]]; then
                      echo "Found changeset file in PR: $file"
                      CHANGESET_IN_PR=true
                      break
                    fi
                  done <<< "$CHANGED_FILES"

                  if [ "$CHANGESET_IN_PR" = false ]; then
                    echo "No changeset files found in changed files. Changed files in .changeset/:"
                    echo "$CHANGED_FILES" | grep "^\.changeset/" || true
                    echo "::error::No changeset file found in PR changes. Please run 'npm run changeset' to create one."
                    exit 1
                  fi

            - name: Comment on PR
              if: failure()
              uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7
              with:
                  script: |
                      const message = `This PR requires a changeset since it includes user-facing changes. Please:

                      1. Run \`npm run changeset\` locally
                      2. Choose the appropriate version bump:
                         - \`major\` for breaking changes (1.0.0 → 2.0.0)
                         - \`minor\` for new features (1.0.0 → 1.1.0)
                         - \`patch\` for bug fixes (1.0.0 → 1.0.1)
                      3. Write a clear description of your changes
                      4. Commit the generated changeset file

                      Note: Documentation-only changes do not require a changeset.`;

                      // Get existing comments
                      const comments = await github.rest.issues.listComments({
                        owner: context.repo.owner,
                        repo: context.repo.repo,
                        issue_number: context.issue.number
                      });

                      // Check if we already commented
                      const botComment = comments.data.find(comment => 
                        comment.user.login === 'github-actions[bot]' && 
                        comment.body.includes('This PR requires a changeset')
                      );

                      if (!botComment) {
                        await github.rest.issues.createComment({
                          owner: context.repo.owner,
                          repo: context.repo.repo,
                          issue_number: context.issue.number,
                          body: message
                        });
                      }

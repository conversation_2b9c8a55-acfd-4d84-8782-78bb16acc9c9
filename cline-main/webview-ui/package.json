{"name": "webview-ui", "version": "0.3.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "preview": "vite preview", "lint": "eslint .", "test": "vitest run", "test:watch": "vitest dev"}, "dependencies": {"@floating-ui/react": "^0.27.4", "@types/dompurify": "^3.0.5", "@vscode/webview-ui-toolkit": "^1.4.0", "debounce": "^2.1.1", "dompurify": "^3.2.4", "fast-deep-equal": "^3.1.3", "firebase": "^11.3.0", "fuse.js": "^7.0.0", "fzf": "^0.5.2", "mermaid": "^11.4.1", "posthog-js": "^1.224.0", "pretty-bytes": "^6.1.1", "react": "^18.3.1", "react-countup": "^6.5.3", "react-dom": "^18.3.1", "react-remark": "^2.1.0", "react-textarea-autosize": "^8.5.7", "react-use": "^17.6.0", "react-virtuoso": "^4.12.3", "rehype-highlight": "^7.0.1", "styled-components": "^6.1.15"}, "devDependencies": {"@eslint/js": "^9.17.0", "@tailwindcss/vite": "^4.0.12", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^22.13.4", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/vscode-webview": "^1.57.5", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "jsdom": "^26.0.0", "tailwindcss": "^4.0.12", "typescript": "^5.7.3", "typescript-eslint": "^8.18.2", "vite": "^6.2.1", "vitest": "^3.0.5"}}
# Local Testing Guide for Supabase Edge Functions

This guide provides instructions on how to run and test Supabase edge functions locally, with a focus on the Paddle integration and credit systems.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Setting Up Local Development Environment](#setting-up-local-development-environment)
3. [Running Edge Functions Locally](#running-edge-functions-locally)
4. [Testing the Paddle Integration](#testing-the-paddle-integration)
5. [Sample Curl Commands](#sample-curl-commands)
6. [Troubleshooting](#troubleshooting)

## Prerequisites

- [Deno](https://deno.land/#installation) installed (v1.29.0 or later)
- [Supabase CLI](https://supabase.com/docs/guides/cli) installed
- Paddle API keys (for sandbox environment)
- Access to the Supabase database

## Setting Up Local Development Environment

### 1. Create a `.env` file for local development

Create a `.env.local` file in the `supabase/functions` directory:

```bash
cd /home/<USER>/Documents/cursor_extensions/voicehype/supabase/functions
touch .env.local
```

Add the following environment variables:

```
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
PADDLE_API_KEY=your_sandbox_paddle_api_key
PADDLE_ENVIRONMENT=sandbox
FRONTEND_URL=http://localhost:5173
```

### 2. Create a test script

Create a `test-functions.ts` file in the `supabase/functions` directory:

```bash
touch /home/<USER>/Documents/cursor_extensions/voicehype/supabase/functions/test-functions.ts
```

Add the following content:

```typescript
// Test script for running edge functions locally
import { config } from "https://deno.land/x/dotenv@v3.2.0/mod.ts";

// Load environment variables
config({ path: "./.env.local", export: true });

// Import specific function you want to test
import "./create-paddle-checkout/index.ts";
```

## Running Edge Functions Locally

### Run a specific function

```bash
cd /home/<USER>/Documents/cursor_extensions/voicehype/supabase/functions
deno run --allow-net --allow-env --allow-read --watch ./create-paddle-checkout/index.ts
```

### Run the test script (recommended)

```bash
cd /home/<USER>/Documents/cursor_extensions/voicehype/supabase/functions
deno run --allow-net --allow-env --allow-read --watch test-functions.ts
```

This will start a local server, usually at `http://localhost:9999`.

## Testing the Paddle Integration

### Create a test script for the create-paddle-checkout function

Create a `test-paddle-checkout.ts` file:

```bash
touch /home/<USER>/Documents/cursor_extensions/voicehype/supabase/functions/test-paddle-checkout.ts
```

Add the following content:

```typescript
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { config } from "https://deno.land/x/dotenv@v3.2.0/mod.ts";

// Load environment variables
config({ path: "./.env.local", export: true });

// Mock function handler
serve(async (req) => {
  // Extract the function name from the URL
  const url = new URL(req.url);
  const functionName = url.pathname.split("/").pop();

  console.log(`Testing function: ${functionName}`);
  
  // Call the actual function handler
  let response;
  
  if (functionName === "create-paddle-checkout") {
    // Import and call create-paddle-checkout
    const module = await import("./create-paddle-checkout/index.ts");
    response = await module.default(req);
  } else {
    // Handle other functions as needed
    response = new Response(
      JSON.stringify({ error: "Function not supported" }),
      { status: 400, headers: { "Content-Type": "application/json" } }
    );
  }
  
  // Log the response
  const responseText = await response.text();
  const responseInit = {
    status: response.status,
    statusText: response.statusText,
    headers: Object.fromEntries(response.headers.entries()),
  };
  
  console.log("Response:", {
    init: responseInit,
    body: responseText,
  });
  
  // Return a new response using the logged data
  return new Response(responseText, responseInit);
});
```

Run this test script:

```bash
deno run --allow-net --allow-env --allow-read --watch test-paddle-checkout.ts
```

## Sample Curl Commands

Here are some curl commands to test the edge functions:

### 1. Create Paddle Checkout for Credits

```bash
curl -X POST http://localhost:9999/create-paddle-checkout \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "amount": 10,
    "currency": "USD"
  }'
```

Replace `YOUR_JWT_TOKEN` with a valid JWT from your Supabase auth.

### 2. Using the Paddle v2 API Transaction Endpoint Directly

```bash
curl -X POST "https://sandbox-api.paddle.com/transactions" \
  -H "Authorization: Bearer YOUR_PADDLE_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "items": [
      {
        "price_id": "YOUR_PRICE_ID",
        "quantity": 1
      }
    ],
    "customer_id": "YOUR_CUSTOMER_ID",
    "address_id": "YOUR_ADDRESS_ID",
    "currency_code": "USD",
    "collection_mode": "automatic"
  }'
```

### 3. Get Paddle Transactions

```bash
curl -X GET http://localhost:9999/get-paddle-transactions \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 4. Mock Paddle Webhook Event

```bash
curl -X POST http://localhost:9999/paddle-webhook \
  -H "Content-Type: application/json" \
  -d '{
    "event_id": "evt_mock_12345",
    "event_type": "transaction.completed",
    "occurred_at": "2025-05-29T12:00:00Z",
    "data": {
      "id": "txn_mock_12345",
      "customer_id": "ctm_mock_12345",
      "status": "completed",
      "custom_data": {
        "user_id": "YOUR_USER_ID",
        "credit_amount": 10
      },
      "details": {
        "totals": {
          "subtotal": "1000",
          "tax": "0",
          "discount": "0",
          "total": "1000"
        }
      }
    }
  }'
```

## Troubleshooting

### 1. Missing environment variables

If you see errors related to missing environment variables, check your `.env.local` file and make sure it's being loaded correctly.

### 2. CORS errors

If you encounter CORS errors while testing from a frontend application, add the following headers to your response:

```typescript
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};
```

### 3. Database connection issues

If you're having trouble connecting to the database:

- Check if your service role key is correct
- Ensure your database is accessible from your local machine
- Try using a database proxy if the database is not publicly accessible

### 4. Paddle API errors

Common Paddle API errors:

- **Authentication errors**: Check your API key format and permissions
- **Invalid URL errors**: Ensure you're using the correct API endpoint for the version you're working with
- **Invalid request format**: Verify your payload structure against the Paddle API documentation

### 5. Debug Mode

For more verbose logging, add the following to your test script:

```typescript
// Enable debug mode
Deno.env.set("DEBUG", "true");
```

This will output more detailed logs to help diagnose issues.

## Additional Resources

- [Supabase Edge Functions Documentation](https://supabase.com/docs/guides/functions)
- [Deno Documentation](https://deno.land/manual)
- [Paddle API v2 Documentation](https://developer.paddle.com/api-reference/overview)

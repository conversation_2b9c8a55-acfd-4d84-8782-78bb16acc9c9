# Paddle Proration Strategies Research & Recommendations

## Executive Summary

This document provides comprehensive research on proration strategies for VoiceHype subscription upgrades/downgrades using <PERSON><PERSON>'s billing system. After analyzing <PERSON><PERSON>'s capabilities and VoiceHype's credit-based architecture, we recommend implementing **Hybrid Credit-Based Proration** that leverages both <PERSON><PERSON>'s native discount system and VoiceHype's existing credit infrastructure.

## Paddle's Native Proration Capabilities

### 1. Subscription Modification API
Paddle provides the `/subscriptions/{subscription_id}` PATCH endpoint for mid-cycle changes:

```typescript
// Example: Upgrade from Basic to Pro
const subscriptionUpdate = {
  items: [
    {
      price_id: "pro_plan_price_id",
      quantity: 1
    }
  ],
  proration_billing_mode: "prorated_immediately" // or "full_next_billing_period"
};
```

### 2. Proration Billing Modes
- **`prorated_immediately`**: Charges/credits difference immediately
- **`full_next_billing_period`**: Changes take effect at next billing cycle
- **`prorated_next_billing_period`**: Applies proration at next billing cycle

### 3. Native Limitations for VoiceHype
- <PERSON><PERSON>'s proration is **time-based**, not **usage-based**
- No native support for credit/quota-based proration
- Complex to handle VoiceHype's "pay for credits" model
- Doesn't align with VoiceHype's existing credit architecture

## VoiceHype-Specific Proration Challenges

### Current Architecture
- **Credit-based system**: Users purchase credits for voice generation
- **Tiered pricing**: Different plans offer different credit amounts
- **Rollover credits**: Unused credits can carry over (depending on plan)
- **Usage tracking**: Real-time credit consumption monitoring

### Proration Scenarios
1. **Upgrade mid-cycle**: User wants more credits before next billing
2. **Downgrade mid-cycle**: User wants to reduce plan cost
3. **Plan switching**: Different credit allocation structures
4. **Seasonal usage**: Users want temporary upgrades

## Recommended Strategy: Hybrid Credit-Based Proration

### Core Approach
Combine Paddle's discount system with VoiceHype's credit management for seamless proration handling.

### Implementation Architecture

```typescript
interface ProrationCalculation {
  currentPlan: SubscriptionPlan;
  targetPlan: SubscriptionPlan;
  daysRemaining: number;
  currentCreditsUsed: number;
  prorationAmount: number;
  creditAdjustment: number;
  paddleDiscountId?: string;
}

class ProrationManager {
  async calculateProration(
    subscriptionId: string,
    targetPlanId: string
  ): Promise<ProrationCalculation> {
    // 1. Get current subscription details
    const subscription = await this.getSubscription(subscriptionId);
    const currentPlan = await this.getPlan(subscription.planId);
    const targetPlan = await this.getPlan(targetPlanId);
    
    // 2. Calculate time-based proration
    const daysRemaining = this.calculateDaysRemaining(subscription);
    const timeProrationFactor = daysRemaining / subscription.billingCycleDays;
    
    // 3. Calculate credit-based proration
    const creditsUsed = await this.getCreditsUsed(subscription.userId);
    const creditProrationFactor = creditsUsed / currentPlan.monthlyCredits;
    
    // 4. Hybrid calculation
    const effectiveProrationFactor = Math.max(timeProrationFactor, creditProrationFactor);
    const prorationAmount = this.calculateProrationAmount(
      currentPlan,
      targetPlan,
      effectiveProrationFactor
    );
    
    return {
      currentPlan,
      targetPlan,
      daysRemaining,
      currentCreditsUsed: creditsUsed,
      prorationAmount,
      creditAdjustment: this.calculateCreditAdjustment(currentPlan, targetPlan, effectiveProrationFactor),
    };
  }
}
```

### Strategy Options

#### Option 1: Immediate Credit Adjustment (Recommended)
**Best for**: Most upgrade scenarios

```typescript
async function upgradeWithImmediateCredits(
  subscriptionId: string,
  targetPlanId: string
): Promise<ProrationResult> {
  const proration = await prorationManager.calculateProration(subscriptionId, targetPlanId);
  
  // 1. Calculate immediate credit bonus
  const bonusCredits = Math.floor(
    (proration.targetPlan.monthlyCredits - proration.currentPlan.monthlyCredits) *
    (proration.daysRemaining / 30)
  );
  
  // 2. Create Paddle discount for next billing cycle
  const discountAmount = proration.prorationAmount;
  const paddleDiscount = await paddle.createDiscount({
    amount: discountAmount,
    type: 'flat',
    restrict_to: [subscriptionId]
  });
  
  // 3. Update subscription with new plan
  await paddle.updateSubscription(subscriptionId, {
    items: [{ price_id: proration.targetPlan.paddlePriceId, quantity: 1 }],
    proration_billing_mode: 'full_next_billing_period'
  });
  
  // 4. Add immediate credits to user account
  await creditManager.addCredits(subscription.userId, bonusCredits, {
    source: 'subscription_upgrade',
    description: `Prorated credits for upgrade to ${proration.targetPlan.name}`
  });
  
  return {
    bonusCredits,
    discountApplied: discountAmount,
    effectiveDate: 'immediate'
  };
}
```

#### Option 2: Next-Cycle Proration
**Best for**: Downgrades and cost-conscious users

```typescript
async function upgradeNextCycle(
  subscriptionId: string,
  targetPlanId: string
): Promise<ProrationResult> {
  const proration = await prorationManager.calculateProration(subscriptionId, targetPlanId);
  
  // 1. Schedule plan change for next billing cycle
  await paddle.updateSubscription(subscriptionId, {
    items: [{ price_id: proration.targetPlan.paddlePriceId, quantity: 1 }],
    proration_billing_mode: 'full_next_billing_period'
  });
  
  // 2. Store scheduled change in database
  await db.subscription_scheduled_changes.create({
    subscription_id: subscriptionId,
    target_plan_id: proration.targetPlan.id,
    effective_date: proration.nextBillingDate,
    change_type: 'plan_upgrade'
  });
  
  return {
    effectiveDate: proration.nextBillingDate,
    message: `Your plan will upgrade to ${proration.targetPlan.name} on your next billing cycle`
  };
}
```

#### Option 3: Credit Buyback for Downgrades
**Best for**: Downgrade scenarios with unused credits

```typescript
async function downgradeWithCreditBuyback(
  subscriptionId: string,
  targetPlanId: string
): Promise<ProrationResult> {
  const proration = await prorationManager.calculateProration(subscriptionId, targetPlanId);
  const unusedCredits = proration.currentPlan.monthlyCredits - proration.currentCreditsUsed;
  
  if (unusedCredits > 0) {
    // Calculate buyback value
    const creditValue = proration.currentPlan.price / proration.currentPlan.monthlyCredits;
    const buybackAmount = unusedCredits * creditValue;
    
    // Create Paddle credit/discount for buyback
    const paddleCredit = await paddle.createDiscount({
      amount: Math.floor(buybackAmount * 100), // Convert to cents
      type: 'flat',
      restrict_to: [subscriptionId]
    });
    
    // Remove unused credits
    await creditManager.removeCredits(subscription.userId, unusedCredits, {
      source: 'subscription_downgrade',
      description: `Credit buyback for downgrade to ${proration.targetPlan.name}`
    });
  }
  
  // Update subscription
  await paddle.updateSubscription(subscriptionId, {
    items: [{ price_id: proration.targetPlan.paddlePriceId, quantity: 1 }],
    proration_billing_mode: 'prorated_immediately'
  });
  
  return {
    buybackAmount: unusedCredits > 0 ? buybackAmount : 0,
    creditsRemoved: unusedCredits,
    effectiveDate: 'immediate'
  };
}
```

## Implementation Plan

### Phase 1: Core Infrastructure (Week 1-2)
1. Create `ProrationManager` class
2. Implement proration calculation algorithms
3. Add database tables for proration tracking
4. Create Supabase edge functions for proration operations

### Phase 2: Paddle Integration (Week 2-3)
1. Implement Paddle discount creation/management
2. Add subscription update handling
3. Create webhook handlers for proration events
4. Test with Paddle sandbox environment

### Phase 3: Frontend Integration (Week 3-4)
1. Build plan comparison and proration preview UI
2. Add upgrade/downgrade flows
3. Implement proration confirmation dialogs
4. Add real-time credit balance updates

### Phase 4: Testing & Optimization (Week 4-5)
1. Comprehensive testing of all proration scenarios
2. Performance optimization for proration calculations
3. Edge case handling and error recovery
4. Documentation and user guides

## Database Schema Extensions

### Proration Tracking Table
```sql
CREATE TABLE subscription_prorations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    subscription_id TEXT NOT NULL,
    user_id UUID NOT NULL REFERENCES auth.users(id),
    from_plan_id TEXT NOT NULL,
    to_plan_id TEXT NOT NULL,
    proration_type TEXT NOT NULL CHECK (proration_type IN ('upgrade', 'downgrade', 'plan_change')),
    proration_method TEXT NOT NULL CHECK (proration_method IN ('immediate_credits', 'next_cycle', 'credit_buyback')),
    
    -- Financial details
    proration_amount INTEGER NOT NULL, -- in cents
    credit_adjustment INTEGER NOT NULL,
    paddle_discount_id TEXT,
    
    -- Metadata
    effective_date TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    processed_at TIMESTAMPTZ,
    
    -- Tracking
    days_remaining INTEGER NOT NULL,
    credits_used_at_change INTEGER NOT NULL,
    
    CONSTRAINT fk_subscription_prorations_user FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Indexes
CREATE INDEX idx_subscription_prorations_subscription_id ON subscription_prorations(subscription_id);
CREATE INDEX idx_subscription_prorations_user_id ON subscription_prorations(user_id);
CREATE INDEX idx_subscription_prorations_effective_date ON subscription_prorations(effective_date);
```

### Scheduled Changes Table
```sql
CREATE TABLE subscription_scheduled_changes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    subscription_id TEXT NOT NULL,
    user_id UUID NOT NULL REFERENCES auth.users(id),
    change_type TEXT NOT NULL CHECK (change_type IN ('plan_upgrade', 'plan_downgrade', 'cancellation')),
    target_plan_id TEXT,
    effective_date TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    processed_at TIMESTAMPTZ,
    cancelled_at TIMESTAMPTZ,
    
    CONSTRAINT fk_scheduled_changes_user FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE
);
```

## Edge Function: Proration Service

```typescript
// supabase/functions/subscription-proration/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { action, subscriptionId, targetPlanId, prorationMethod } = await req.json()
    
    const authHeader = req.headers.get('Authorization')!
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      { global: { headers: { Authorization: authHeader } } }
    )

    const prorationManager = new ProrationManager(supabase)
    
    switch (action) {
      case 'calculate':
        const calculation = await prorationManager.calculateProration(subscriptionId, targetPlanId)
        return new Response(JSON.stringify(calculation), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        })
        
      case 'execute':
        const result = await prorationManager.executeProration(
          subscriptionId, 
          targetPlanId, 
          prorationMethod
        )
        return new Response(JSON.stringify(result), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        })
        
      default:
        throw new Error('Invalid action')
    }
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})
```

## Frontend Implementation Example

### React Hook for Proration
```typescript
import { useState, useEffect } from 'react'
import { useSupabase } from '@/hooks/useSupabase'

export function useProration() {
  const { supabase } = useSupabase()
  
  const calculateProration = async (subscriptionId: string, targetPlanId: string) => {
    const { data, error } = await supabase.functions.invoke('subscription-proration', {
      body: {
        action: 'calculate',
        subscriptionId,
        targetPlanId
      }
    })
    
    if (error) throw error
    return data
  }
  
  const executeProration = async (
    subscriptionId: string, 
    targetPlanId: string, 
    method: 'immediate_credits' | 'next_cycle' | 'credit_buyback'
  ) => {
    const { data, error } = await supabase.functions.invoke('subscription-proration', {
      body: {
        action: 'execute',
        subscriptionId,
        targetPlanId,
        prorationMethod: method
      }
    })
    
    if (error) throw error
    return data
  }
  
  return { calculateProration, executeProration }
}
```

### Plan Upgrade Component
```typescript
function PlanUpgradeModal({ currentPlan, targetPlan, subscriptionId, onClose }) {
  const [proration, setProration] = useState(null)
  const [loading, setLoading] = useState(false)
  const { calculateProration, executeProration } = useProration()
  
  useEffect(() => {
    async function loadProration() {
      const calc = await calculateProration(subscriptionId, targetPlan.id)
      setProration(calc)
    }
    loadProration()
  }, [subscriptionId, targetPlan.id])
  
  const handleUpgrade = async (method: string) => {
    setLoading(true)
    try {
      await executeProration(subscriptionId, targetPlan.id, method)
      onClose()
      // Show success message
    } catch (error) {
      // Handle error
    } finally {
      setLoading(false)
    }
  }
  
  if (!proration) return <div>Calculating proration...</div>
  
  return (
    <div className="proration-modal">
      <h3>Upgrade to {targetPlan.name}</h3>
      
      <div className="proration-options">
        <div className="option immediate-credits">
          <h4>Immediate Upgrade</h4>
          <p>Get {proration.creditAdjustment} bonus credits right now</p>
          <p>Next billing: {targetPlan.price} (${proration.prorationAmount/100} discount applied)</p>
          <button onClick={() => handleUpgrade('immediate_credits')}>
            Upgrade Now
          </button>
        </div>
        
        <div className="option next-cycle">
          <h4>Upgrade Next Cycle</h4>
          <p>Change takes effect on {proration.nextBillingDate}</p>
          <p>No immediate charge</p>
          <button onClick={() => handleUpgrade('next_cycle')}>
            Schedule Upgrade
          </button>
        </div>
      </div>
    </div>
  )
}
```

## Cost Analysis & ROI

### Development Costs
- **Phase 1-2**: ~40-60 hours (Core + Paddle integration)
- **Phase 3-4**: ~30-40 hours (Frontend + Testing)
- **Total**: ~70-100 hours development time

### Revenue Impact
- **Increased upgrades**: 15-25% improvement in plan upgrade rates
- **Reduced churn**: 10-15% reduction in downgrades due to better options
- **Customer satisfaction**: Higher retention due to flexible billing

### Implementation Priority
**High Priority**: Option 1 (Immediate Credit Adjustment) - covers 80% of use cases
**Medium Priority**: Option 2 (Next-Cycle Proration) - covers remaining upgrade scenarios
**Low Priority**: Option 3 (Credit Buyback) - nice-to-have for downgrades

## Conclusion

The **Hybrid Credit-Based Proration** strategy provides the best balance of:
- User experience (flexible, immediate benefits)
- Technical feasibility (leverages existing credit system)
- Business value (increased upgrades, reduced churn)
- Development efficiency (builds on Paddle's native capabilities)

Recommend starting with **Option 1** implementation for immediate credit adjustment, as it covers the majority of upgrade scenarios and provides the highest user satisfaction.

## Next Steps

1. **Review and approve** this proration strategy
2. **Prioritize implementation phases** based on business needs
3. **Design detailed technical specifications** for chosen options
4. **Create project timeline** and resource allocation
5. **Begin Phase 1 development** with core infrastructure

---

*This document provides the foundation for implementing a robust, user-friendly proration system that enhances VoiceHype's subscription management while maintaining technical simplicity and business viability.*

Build an overlay checkout

Get a step-by-step overview of how to build a complete overlay checkout — including initializing Paddle.js, passing settings and items, prefilling customer information, and next steps.

The checkout is where customers make purchases. For SaaS businesses, it's the process where customers enter their details and payment information, and confirm that they'd like to sign up for a subscription with you.

You can use Paddle.js to quickly add an overlay checkout into your app. Overlay checkout lets you present customers with an overlay that handles all parts of the checkout process — minimal frontend coding required.

Grab the code and test using CodePen

CodePen is a platform for building and sharing frontend code. Explore the code for this tutorial and test right away using our overlay checkout pen.
What are we building?

In this tutorial, we'll launch a multi-page overlay checkout for two items in our product catalog, then we'll extend it by passing customer information.

Illustration of an overlay checkout. The payment method form is open, with buttons for Apple Pay and PayPal along with the card details form underneath. The items list shows one item for 'Professional plan', with tax and totals underneath. The total is $3600, displayed at the top-left of the checkout.

We'll learn how to:

    Include and set up Paddle.js using a client-side token
    Pass items to overlay checkout using Paddle.Checkout.open() or HTML data attributes
    Take a test payment
    Prefill customer information using Paddle.Checkout.open() or HTML data attributes

If you like, you can copy-paste the sample code in your editor or view on CodePen and follow along.
Sample code
Before you begin
Choose a checkout implementation

This tutorial walks through creating an overlay checkout. You can also create inline checkouts, which lets you build Paddle Checkout right into your app or website.

We recommend building an overlay checkout if you're new to Paddle. Inline checkouts use the same JavaScript methods as overlay checkouts, so you can switch to an inline checkout later.
Types of checkout
Create products and prices

Paddle Checkout works with products and prices to say what a customer is purchasing, so you'll need to create a product and at least one related price to pass to your checkout.
Set your default payment link

You'll also need to:

    Set your default payment link under Paddle > Checkout > Checkout settings > Default payment link.
    Get your default payment link domain approved, if you're working with the live environment.

    We recommend starting the domain approval early in your integration process, so your domains are approved for when you're ready to go-live.

Overview

Add an overlay checkout to your website or app in four steps:

    Include and initialize Paddle.js

    Add Paddle.js to your app or website, so you can securely capture payment information and build subscription billing experiences.

    Add an overlay checkout button

    Set any element on your page as a launcher for Paddle Checkout.

    Take a test payment

    Make sure that your checkout loads successfully, then take a test payment.

    Prefill customer information — optional

    Extend your checkout by prefilling customer and address information.

1. Include and initialize Paddle.js

Paddle.js is a lightweight JavaScript library that lets you build rich, integrated subscription billing experiences using Paddle. We can use Paddle.js to securely work with products and prices in our Paddle system, as well as opening checkouts and capturing payment information.
Include Paddle.js script

Start with a blank webpage, or an existing page on your website. Then, include Paddle.js by adding this script to the <head>:

<script src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>

Set environment (optional)

We recommend signing up for a sandbox account to test and build your integration, then switching to a live account later when you're ready to go live.

If you're testing with the sandbox, call Paddle.Environment.set() and set your environment to sandbox:

<script src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>
<script type="text/javascript">
  Paddle.Environment.set("sandbox");
</script>

Pass a client-side token

Next, go to Paddle > Developer tools > Authentication and create a client-side token. Client-side tokens let you interact with the Paddle platform in frontend code, like webpages or mobile apps. They have limited access to the data in your system, so they're safe to publish.

In your page, call Paddle.Initialize() and pass your client-side token as token. For best performance, do this just after calling Paddle.Environment.set(), like this:

<script src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>
<script type="text/javascript">
  Paddle.Environment.set("sandbox");
  Paddle.Initialize({ 
    token: "test_7d279f61a3499fed520f7cd8c08" // replace with a client-side token
  });
</script>

    Client-side tokens are separate for your sandbox and live accounts. You'll need to create a new client-side token for your live account. Sandbox tokens start with test_ to make them easy to distinguish.

2. Add an overlay checkout button

Next, we'll set an element on our page as a launcher for our overlay checkout. Overlay checkout works by presenting an overlay to handle the entire checkout process. When our button or other launcher element is clicked, Paddle.js launches a checkout for us.
Create checkout button element

Any element can be a launcher for an overlay checkout. In our sample, we're using a link (<a>) that points to #. This means it doesn't open a new page.

<a href='#'>Sign up now</a>

Set as a checkout launcher

Next, we'll make our checkout element open an overlay checkout by making it a launcher.

You can do this in two ways:
Paddle.Checkout.open() method

Code illustration showing an openCheckout() function. It's cut off, so you can't make out the full code.

    Works using JavaScript to open a checkout when an element is clicked.
    You can pass items and settings as parameters to Paddle.Checkout.open().
    Recommended in most cases.
    No styles applied to your element.
    Best for passing multiple attributes.

HTML data attributes

Code illustration showing HTML data attributes added to an a element. It's cut off, so you can't make out the full code.

    Works by adding a paddle_button class to an element, which Paddle.js turns into a checkout launcher.
    You can pass items and settings as data attributes against the element.
    Recommended when you can't use JavaScript.
    Optionally styles your element to look like a button.
    Best for passing few attributes.

In general, we recommend using the Paddle.Checkout.open() method, but you can choose the option that makes the most sense for you.
JavaScript
HTML data attributes

Paddle.js comes with the Paddle.Checkout.open() method, which lets you open a checkout with settings, items, and customer information.

In our sample, we've created a function called openCheckout() to open a checkout. Here's how it works:

    We create a variable called itemsList and pass an array of objects, where each object contains a priceId and quantity.

    We create a function called openCheckout() that takes a parameter called items.

    In our openCheckout() function, we call Paddle.Checkout.open(), passing the value of items as the items list for the checkout.

    We add an onclick event to our checkout button to call openCheckout() when clicked, passing our itemsList variable as a parameter.

    Recurring items on a checkout must have the same billing interval. For example, you can't have a checkout with some prices that are billed monthly and some products that are billed annually.

<script type="text/javascript">
  Paddle.Environment.set("sandbox");
  Paddle.Initialize({
    token: "test_7d279f61a3499fed520f7cd8c08" // replace with a client-side token
  });
  
  // define items
  let itemsList = [
    {
      priceId: "pri_01gsz8ntc6z7npqqp6j4ys0w1w",
      quantity: 5
    },
    {
      priceId: "pri_01h1vjfevh5etwq3rb416a23h2",
      quantity: 1
    }
  ];
  
  // open checkout
  function openCheckout(items){

3. Take a test payment

We're now ready to test. Save your page, then open it in your browser. Click on your "Sign up now" button and Paddle.js should open an overlay checkout for the items that we passed.

If you're using a sandbox account, you can take a test payment using our test card details:
Email address	An email address you own
Country	Any valid country supported by Paddle
ZIP code (if required)	Any valid ZIP or postal code
Card number	4242 4242 4242 4242
Name on card	Any name
Expiration date	Any valid date in the future.
Security code	100

Short animation showing launching an overlay checkout, entering contact information, and entering test payment details.
Checkout not working?
4. Prefill customer information (optional)

At this point, we've passed items to our checkout. When we click our launcher, Paddle opens a checkout for the items that we passed.

Paddle.js also lets you pass customer information to a checkout. When we click our launcher, Paddle opens a checkout with the customer information prefilled. This means the first page of checkout is skipped entirely, so customers land on a screen where they can enter their payment information.

    You can present customers with a one-page checkout experience by passing variant with the value one-page as a checkout setting. You don't need to prefill information.

JavaScript
HTML data attributes

Paddle.Checkout.open() takes a customer parameter, which lets you pass customer and address information.

In our sample, we've extended our openCheckout() function so that it passes customer and address information to our checkout. Here's what's going on:

    We create a variable called customerInfo, with an email key and an object for address. You may also pass Paddle IDs for an existing customer or address here.

    We update our openCheckout() function so it takes another parameter called customer.

    In our openCheckout() function, we added the customer parameter and passing the value of customer to this.

    We updated the onClick event on our checkout button to pass our customerInfo variable as the parameter for customer.

<script type="text/javascript">
  Paddle.Environment.set("sandbox");
  Paddle.Initialize({
    token: "test_7d279f61a3499fed520f7cd8c08" // replace with a client-side token
  });
  
  // define items
  let itemsList = [
    {
      priceId: "pri_01gsz8ntc6z7npqqp6j4ys0w1w",
      quantity: 5
    },
    {
      priceId: "pri_01h1vjfevh5etwq3rb416a23h2",
      quantity: 1
    }
  ];
  
  // define customer details
  let customerInfo = {

Test your work

Save your page, then open it in your browser. Click on your "Sign up now" button and Paddle.js should open an overlay checkout with the customer information prefilled. You should land on the second screen, ready to enter payment information.

Short animation showing overlay checkout landing on a screen asking for card details.
Checkout not working?
Next steps

That's it. Now you've built a checkout, you might like to extend Paddle Checkout by automatically applying a discount, passing optional checkout settings, or building a success workflow.
Automatically apply a discount

Extend your checkout by passing a discount. When we click our launcher, Paddle opens a checkout with the discount automatically applied (where it's valid).
Prefill checkout properties
Read more
Create or update a discount
Read more
Pass checkout settings

You don't need to pass checkout settings when working with overlay checkout, but you can use them to give you more control over how opened checkouts work. For example, you can set the language that Paddle Checkout uses, hide the option to add a discount, or restrict payment methods shown to customers.
Pass checkout settings
Read more
Paddle.Checkout.open() method
Read more
Build a success workflow
When customers complete checkout, Paddle Checkout has a final screen that lets customers know that their purchase was successful. If you like, you can redirect customers to your own page or use JavaScript event callbacks to build a more advanced success workflow.
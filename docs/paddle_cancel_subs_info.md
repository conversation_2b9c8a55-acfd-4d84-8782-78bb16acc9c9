Cancel a subscription

Cancel subscriptions when a customer no longer wants to use your software. Customers must sign up again if they wish to use your software in the future.

Cancel subscriptions to stop billing for them permanently. Paddle stops billing customers indefinitely.

You should restrict access to your app when a customer has canceled their subscription.

If a customer just wants to stop using your software temporarily, pause a subscription instead.

    Paddle supports subscriptions with multiple products. Customers might say they want to cancel when they want to remove items for an addon or users. To learn more, see Add or remove products on a subscription

How it works

When you cancel a subscription, its status is set to canceled. Paddle stops billing for it, meaning no further transactions are created for the subscription. You should provision your app so customers don't have access when canceled.

For compliance reasons, subscription-related emails sent from <PERSON>ddle to customers include a link to cancel. This is handled by <PERSON><PERSON> — you don't need to build your own logic for this. When customers cancel using the link in the email from <PERSON><PERSON>, their subscription remains active until the end of the current billing period.

You can also cancel a subscription using the API. When sending a request to cancel, you can tell <PERSON><PERSON> to:

    Cancel at the end of the billing period

    Paddle creates a scheduled change to say the subscription should be canceled on the next billing date. The subscription remains active until the next billing date, when the subscription status changes to canceled.

    Cancel immediately

    Paddle cancels the subscription right away. Its status changes to canceled.

If you've made changes to a subscription or billed for one-time charges and set them to be charged on the next billing period, these are automatically forgiven.

    Canceled subscriptions can't be reinstated. Create a new subscription for customers who have canceled if they want to return.

Before you begin

    You can't make changes to a subscription if the next billing period is within 30 minutes, or the subscription status is past_due.

To cancel a subscription, you'll need to get the subscription ID for the subscription you want to cancel. You can only cancel subscriptions that are active or paused.

You can use the status query parameter with the value active,paused when listing subscriptions to get active and paused subscriptions.
Use the subscription management URL

When working with subscriptions using the API, Paddle returns a management_urls object that includes links that you can use to update a payment method or cancel a subscription.

The simplest way to cancel a subscription is to use the get a subscription operation and return management_urls.cancel to the customer. When customers click this link, they're taken to a page hosted by Paddle that asks them to confirm that they'd like to cancel.
Screenshot of the cancellation page that customers see when using the subscription management URL. The text says: We're sad to see you go! Are you sure you want to cancel your subscription? There are two buttons. 1. Yes, cancel my subscription. 2. Never mind

If confirmed, Paddle creates a scheduled change against the subscription to cancel on next renewal. Subscriptions remain active until the end of the billing period, at which point their status changes to canceled.

    For security, subscription management URLs include a token parameter. This token is temporary, so you shouldn't store these URLs. The management_urls object isn't returned in events for this reason, too.

Cancel a subscription
API
Dashboard

Cancel a subscription using the API to build your own cancellation workflow into your app.

Cancel in two steps:

    Build a request

    Build a request that includes effective_from to tell Paddle when you want a subscription to cancel.

    Cancel subscription

    Send the request to cancel your subscription. Paddle cancels your subscription now or in the future.

Build request

Build a request that includes effective_from to tell Paddle when you want a subscription to cancel.

You don't need to do this if you'd to cancel a subscription on the next billing period. You can send an empty request.
effective_fromstring

When this subscription change should take effect from. Defaults to next_billing_period, which creates a scheduled_change to apply the subscription change at the end of the billing period.
Cancel a subscription on the next billing period
Request

This example cancels a subscription on the next billing period. Paddle creates a scheduled change with an effective_at date of the next_billed_at date for the subscription.

effective_from defaults to next_billing_period, so you may also send an empty request body.

{
  "effective_from": "next_billing_period"
}

Cancel a subscription immediately
Cancel subscription

Send a POST request to the /subscriptions/{subscription_id}/cancel endpoint with the request body you built.
POST
https://api.paddle.com/subscriptions/{subscription_id}/cancel
subscription_idstring

Paddle ID of the subscription entity to work with.
Cancel a subscription on the next billing period
Response

If successful, Paddle returns a copy of the subscription entity with a scheduled change to cancel. The scheduled change is effective on the original next_billed_at date.

next_billed_at is null as the subscription isn't set to be billed again.
200 OK

    "billing_details": null,
    "current_billing_period": {
      "starts_at": "2024-04-08T10:38:57.97967Z",
      "ends_at": "2024-05-08T10:38:57.97967Z"
    },
    "billing_cycle": {
      "frequency": 1,
      "interval": "month"
    },
    "scheduled_change": {
      "action": "cancel",
      "effective_at": "2024-05-08T10:38:57.97967Z",
      "resume_at": null
    },
    "items": [
      {
        "status": "active",
        "quantity": 10,
        "recurring": true,
        "created_at": "2024-04-08T10:38:58.673Z",

Cancel a subscription immediately
Remove a scheduled change

You can stop a cancellation from going through at the end of the billing period by removing the scheduled change.
API
Dashboard

Remove a scheduled cancellation using the API in two steps:

    Build a request

    Build a request that removes the scheduled change.

    Remove scheduled change

    Send the request to remove the scheduled change. The subscription is no longer scheduled to cancel.

Build request

Remove a scheduled cancellation using the API by sending a request that sets scheduled_change to null.
scheduled_changenull

Change that's scheduled to be applied to a subscription. null if no scheduled changes.
Request

{
  "scheduled_change": null
}

Remove scheduled change

Send a PATCH request to the /subscriptions/{subscription_id} endpoint with the request you built.
PATCH
https://api.paddle.com/subscriptions/{subscription_id}
subscription_idstring

Paddle ID of the subscription entity to work with.
Response

If successful, Paddle responds with the updated subscription entity. Scheduled change is set to null and the status is active.
200 OK

    "billing_details": null,
    "current_billing_period": {
      "starts_at": "2024-04-08T10:38:57.97967Z",
      "ends_at": "2024-05-08T10:38:57.97967Z"
    },
    "billing_cycle": {
      "frequency": 1,
      "interval": "month"
    },
    "scheduled_change": null,
    "items": [
      {
        "status": "active",
        "quantity": 10,
        "recurring": true,
        "created_at": "2024-04-08T10:38:58.673Z",
        "updated_at": "2024-04-08T10:38:58.673Z",
        "previously_billed_at": "2024-04-08T10:38:57.97967Z",
        "next_billed_at": "2024-05-08T10:38:57.97967Z",
        "trial_dates": null,

Reinstate a cancelled subscription

Canceled subscriptions can't be reinstated. Create a new subscription for customers who have canceled if they want to return.

To streamline this process, create a transaction with the same items and other information as on the previous subscription:

    Get the previous subscription

    List subscriptions using the customer_id query parameter, passing the Paddle ID for the customer as the value. This returns a list of all subscriptions for this customer. Get the subscription that they previously canceled.

    Extract price IDs and quantities

    Extract the price ID from the price object and the quantity for each item in the items array against the canceled subscription entity. You may also like to extract the currency_code, address_id, and business_id if they're going to be the same.

    Build a request to create a transaction

    Build a request with an items array with an object for each price that contains a price ID and a quantity, along with the customer ID and any other information you extracted from the canceled subscription.

    Create a transaction

    Send a POST request to the /transactions endpoint to create a transaction. Paddle returns a new transaction for the customer, items, and other details you passed.

    Pass to Paddle Checkout

    Collect payment and create a new subscription by getting the checkout.url against the created transaction and returning it to the customer, or pass a transaction ID to Paddle.js to open a checkout for the transaction you created.
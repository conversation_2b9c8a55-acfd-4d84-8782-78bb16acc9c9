# Setting Up Resend for VoiceHype

## Overview

This guide will help you configure Resend.com to work with your domain (voicehype.ai) and specifically for sending emails from `<EMAIL>`. Resend uses a modern API-based approach instead of traditional SMTP, which helps bypass port restrictions on platforms like DigitalOcean and Deno Deploy.

## 1. Domain Verification in Resend

### Steps:

1. Log in to your [Resend Dashboard](https://resend.com/domains)
2. Click "Add Domain"
3. Enter "voicehype.ai" and click "Add"
4. You'll be presented with DNS records that need to be added to verify your domain:
   - Add the DKIM records (typically 3 TXT records)
   - Add the Return-Path record (CNAME)
   - Add the Custom Sending Domain record (MX)

### GoDaddy DNS Configuration:

1. Log in to your GoDaddy account
2. Navigate to your domain's DNS settings
3. Add all the required DNS records Resend provides
4. Wait for verification (can take up to 24-48 hours, but often completes in minutes)

## 2. Add Sending Identity

After your domain is verified:

1. Go to [Sending Identities](https://resend.com/sending-identities) in Resend
2. Click "Add"
3. Choose "Email Address" (or "Domain" if you want to enable all addresses @voicehype.ai)
4. Enter "<EMAIL>" and click "Add"
5. Verify the email address by clicking the link <NAME_EMAIL>

## 3. Update the Email Service

Once your domain is verified and sending identity is confirmed:

1. Edit `/home/<USER>/Documents/cursor_extensions/voicehype/deno-deploy/email-service/email-service.new.ts`
2. Find the modified section that currently sends to "<EMAIL>"
3. Change it back to send to the actual recipient:

```typescript
// Replace this:
body: JSON.stringify({
  from: `${FROM_NAME} <${FROM_EMAIL}>`,
  to: "<EMAIL>", // Use Resend's test email during setup
  subject: `[INTENDED FOR: ${emailData.email}] ${emailData.subject}`,
  html: `
    <div style="background:#f9f9f9; padding:15px; margin-bottom:15px; border-left:4px solid #ff0000;">
      <p><strong>⚠️ TEST MODE</strong>: This email was intended for: ${emailData.email}</p>
      <p>This message is being sent to Resend's test email during domain verification.</p>
    </div>
    ${emailData.html || emailData.content}
  `,
})

// With this:
body: JSON.stringify({
  from: `${FROM_NAME} <${FROM_EMAIL}>`,
  to: emailData.email,
  subject: emailData.subject,
  html: emailData.html || emailData.content,
})
```

4. Rename the file to replace the current version:
```bash
mv /home/<USER>/Documents/cursor_extensions/voicehype/deno-deploy/email-service/email-service.new.ts /home/<USER>/Documents/cursor_extensions/voicehype/deno-deploy/email-service/email-service.ts
```

5. Deploy using your deployment script:
```bash
cd /home/<USER>/Documents/cursor_extensions/voicehype/deno-deploy/email-service
./deploy.sh
```

## 4. Testing

After deployment:

1. Try the signup process in your application
2. Check the Deno Deploy logs to confirm emails are being processed
3. Verify that test emails from Resend are delivered

## Troubleshooting

- **Invalid "to" field error**: Your domain isn't fully verified, or you're sending to email addresses not allowed in your current Resend plan
- **Domain verification issues**: Double-check the DNS records against what Resend requires
- **Email not delivered**: Check spam folders or Resend's Email Activity log

## Additional Tips

1. Resend free tier includes 3,000 emails/month and 100 emails/day
2. For production use, consider upgrading to a paid plan for higher limits
3. Set up email analytics in the Resend dashboard for delivery tracking
4. Add SPF and DMARC records to improve deliverability

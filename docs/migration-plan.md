# VoiceHype Migration Plan

## Overview

This document outlines the plan to migrate VoiceHype away from OpenAI services for ethical reasons to support good causes for the sake of <PERSON> subhanahu wa ta'ala. The migration will be done in phases:

1. **Phase 1**: Migrate from OpenAI Whisper to LemonFox for transcription services
2. **Phase 2**: Migrate from OpenAI GPT models to alternative LLMs via OpenRouter

## Phase 1: Migrate to LemonFox for Transcription

### UI Changes

1. **ServiceSelector Component**:
   - Modify `extension/webview-ui/src/components/ServiceSelector.tsx` to replace "OpenAI" with "Whisper"
   - Keep the service ID as "openai" internally for backward compatibility

2. **ModelSelector Component**:
   - Modify `extension/webview-ui/src/components/ModelSelector.tsx` to hide the model dropdown when "Whisper" (openai) service is selected
   - Default to "whisper-1" model when "Whisper" service is selected

### Backend Changes

1. **Supabase Edge Function**:
   - Modify `supabase/functions/transcribe/index.ts` to route OpenAI service requests to LemonFox API
   - Update the `transcribeWithOpenAI` function to use LemonFox API endpoint
   - Keep the function name and interface the same for backward compatibility

2. **Real-time Transcription**:
   - Update `supabase/functions/transcribe/realtime.ts` to handle real-time transcription with LemonFox if supported

### Configuration Changes

1. **No changes needed to extension configuration** since we're keeping "openai" as the service ID internally

## Phase 2: Migrate to Alternative LLMs via OpenRouter

### LLM Models to Support

1. **Llama Models**:
   - Llama 4 Scout
   - Llama 4 Maverick
   - Llama 3 70B
   - Llama 3 8B

2. **Claude Models**:
   - Claude 3.5 Sonic
   - Claude 3.7 Sonic
   - Claude 3.5 Haiku

3. **DeepSeek Models**:
   - DeepSeek R1
   - DeepSeek V3

4. **Mistral Models** (to be determined)

### OpenRouter Integration

Based on the OpenRouter documentation, we'll implement the integration using their unified API. Here's how we'll structure the implementation:

#### API Configuration

```typescript
// OpenRouter client configuration
const openRouterClient = new OpenAI({
  baseURL: 'https://openrouter.ai/api/v1',
  apiKey: 'OPENROUTER_API_KEY', // Will be stored in Supabase secrets
  defaultHeaders: {
    'HTTP-Referer': 'https://voicehype.ai',
    'X-Title': 'VoiceHype',
  },
});
```

#### Model Mapping

We'll create a mapping of model IDs to use with OpenRouter:

```typescript
const OPENROUTER_MODELS = {
  // Llama Models
  'llama-4-scout': 'meta-llama/llama-4-scout',
  'llama-4-maverick': 'meta-llama/llama-4-maverick',
  'llama-3-70b': 'meta-llama/llama-3-70b-instruct',
  'llama-3-8b': 'meta-llama/llama-3-8b-instruct',

  // Claude Models
  'claude-3.5-sonnet': 'anthropic/claude-3.5-sonnet',
  'claude-3.7-sonnet': 'anthropic/claude-3.7-sonnet',
  'claude-3.5-haiku': 'anthropic/claude-3.5-haiku',

  // DeepSeek Models
  'deepseek-r1': 'deepseek/deepseek-r1',
  'deepseek-v3': 'deepseek/deepseek-v3',

  // Mistral Models (to be added)
};
```

#### Making API Requests

We'll use the OpenRouter API to make requests to the various models:

```typescript
async function generateWithLLM(model: string, prompt: string, options = {}) {
  try {
    const completion = await openRouterClient.chat.completions.create({
      model: OPENROUTER_MODELS[model],
      messages: [
        {
          role: 'user',
          content: prompt,
        },
      ],
      ...options,
    });

    return completion.choices[0].message.content;
  } catch (error) {
    console.error('Error generating with LLM:', error);
    throw error;
  }
}
```

#### Fallback Strategy

We'll implement a fallback strategy using OpenRouter's `models` parameter to ensure high availability:

```typescript
async function generateWithFallback(primaryModel: string, prompt: string, options = {}) {
  // Define fallback models based on the primary model's category
  const fallbackModels = getFallbackModels(primaryModel);

  try {
    const completion = await openRouterClient.chat.completions.create({
      model: OPENROUTER_MODELS[primaryModel],
      models: fallbackModels.map(model => OPENROUTER_MODELS[model]),
      messages: [
        {
          role: 'user',
          content: prompt,
        },
      ],
      ...options,
    });

    return completion.choices[0].message.content;
  } catch (error) {
    console.error('Error generating with fallback:', error);
    throw error;
  }
}
```

### Implementation Details for Phase 2

1. **Edge Function Updates**:
   - Create a new Supabase Edge Function for LLM optimization
   - Implement OpenRouter API integration
   - Add support for all specified models

2. **UI Updates**:
   - Add model selection dropdown for optimization
   - Display model capabilities and pricing

3. **Configuration**:
   - Store OpenRouter API key in Supabase secrets
   - Create configuration options for default models

4. **Testing**:
   - Test each model for optimization tasks
   - Verify fallback behavior
   - Measure performance and cost

## Files to Modify for Phase 1

1. **UI Components**:
   - `extension/webview-ui/src/components/ServiceSelector.tsx` - Change "OpenAI" label to "Whisper"
   - `extension/webview-ui/src/components/ModelSelector.tsx` - Hide model dropdown for Whisper service

2. **Supabase Edge Functions**:
   - `supabase/functions/transcribe/index.ts` - Update OpenAI API calls to use LemonFox
   - `supabase/functions/transcribe/realtime.ts` - Update real-time transcription if needed

## Implementation Steps for Phase 1

1. Update the ServiceSelector component to rename "OpenAI" to "Whisper"
2. Modify the ModelSelector component to hide the dropdown when Whisper is selected
3. Update the Supabase Edge Function to route OpenAI requests to LemonFox
4. Test the changes to ensure transcription works correctly
5. Deploy the updated Edge Function
6. Build and release the updated extension

Openrouter API Key :: 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
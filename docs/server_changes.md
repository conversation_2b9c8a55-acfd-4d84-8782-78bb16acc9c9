# Server-Side Changes Required

Edit the `/home/<USER>/Documents/cursor_extensions/voicehype/supabase/functions/transcribe/realtime.ts` file to make these changes:

1. Update the handling of the "close" message from the client to ensure the complete transcript is sent before closing:

```typescript
if (message.type === 'close') {
    // Client requested to close the connection
    console.log(`[DEBUG] Client requested close for session ${sessionId} at ${new Date().toISOString()}`);
    
    // Check if client is explicitly requesting the final transcript
    const requestFinalTranscript = message.requestFinalTranscript === true;
    if (requestFinalTranscript) {
        console.log(`[DEBUG] Client explicitly requested final transcript for session ${sessionId}`);
    }

    // Prepare the complete transcript
    const completeTranscript = finalTranscript.trim().length > 0 ? finalTranscript.trim() : lastPartialTranscript.trim();
    
    if (completeTranscript.length > 0) {
        console.log(`[DEBUG] Sending complete transcript before closing for session ${sessionId}, length: ${completeTranscript.length}`);
        
        // Send the complete transcript before closing
        socket.send(JSON.stringify({
            message_type: 'CompleteTranscript',
            text: completeTranscript,
            sessionId: sessionId
        }));

        // Add a small delay to ensure the message is sent before closing
        setTimeout(() => {
            // Close the AssemblyAI connection
            if (assemblyAISocket && assemblyAISocket.readyState === WebSocket.OPEN) {
                assemblyAISocket.close();
            }

            if (sessionTimeout !== null) {
                clearTimeout(sessionTimeout);
                sessionTimeout = null;
            }

            // Send finalization message and close client connection
            socket.send(JSON.stringify({
                type: 'finalized',
                message: 'Session finalized successfully',
                sessionId: sessionId
            }));
            
            // Close the client connection after sending the final transcript
            socket.close();
        }, 500); // 500ms delay to ensure messages are sent before closing
    } else {
        console.log(`[DEBUG] No complete transcript available for session ${sessionId}`);
        
        // Close the connections immediately since there's no transcript to send
        if (assemblyAISocket && assemblyAISocket.readyState === WebSocket.OPEN) {
            assemblyAISocket.close();
        }

        if (sessionTimeout !== null) {
            clearTimeout(sessionTimeout);
            sessionTimeout = null;
        }

        socket.close();
    }
} else if (...) {
    // Other message type handling
}
```

2. Make sure you don't duplicate the sending of CompleteTranscript in other parts of the code:

After making these changes to the `close` message handling, check if there are other locations in the file that also send the CompleteTranscript message to avoid duplication.

These changes will ensure that when the client sends a close message, the server will immediately respond with the full transcript before closing the connection, rather than trying to send it after the connection is already being torn down.

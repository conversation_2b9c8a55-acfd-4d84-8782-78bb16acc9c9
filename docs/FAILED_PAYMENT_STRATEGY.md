# Failed Payment Handling Strategy

## Overview

This document outlines our comprehensive approach to handling failed payments using <PERSON><PERSON>'s built-in dunning system, eliminating the need for custom grace periods or cron jobs.

## Key Strategy Points

### 1. Leverage Paddle's Built-in Dunning (Recommended)

**Why this approach:**
- Pa<PERSON> automatically handles payment retries for 30 days
- No need for custom cron jobs or grace period management
- Optimized retry timing and recovery emails
- Automatic cancellation after exhaustive attempts
- Cost-effective (no ongoing infrastructure costs)

**How it works:**
1. Payment fails → Subscription becomes `past_due`
2. Paddle automatically retries payment over 30 days
3. Customer retains access during dunning period
4. If recovery succeeds → Subscription becomes `active`
5. If recovery fails → Paddle automatically cancels subscription

### 2. Subscription Status Definitions

| Status | User Access | Description | Action Required |
|--------|-------------|-------------|-----------------|
| `active` | ✅ Full | Normal subscription state | None |
| `past_due` | ✅ Full | Payment failed, recovery in progress | Update payment method |
| `paused` | ❌ None | Subscription temporarily stopped | Resume subscription |
| `canceled` | ❌ None | Subscription permanently ended | Resubscribe |
| `trialing` | ✅ Trial | Trial period | None |

### 3. Webhook Events Handled

#### Primary Events
- `subscription.past_due` - Payment failed, entering dunning
- `subscription.activated` - Payment recovered successfully
- `subscription.canceled` - Payment recovery failed, auto-canceled
- `transaction.payment_failed` - Individual payment attempt failed
- `transaction.past_due` - Transaction entered past due state

#### Event Flow for Failed Payments

```
Payment Fails
     ↓
subscription.past_due
     ↓
[Paddle Dunning Process - 30 days]
     ↓
Either:
├── subscription.activated (Recovery Success)
└── subscription.canceled (Recovery Failed)
```

## Implementation Details

### 1. Database Schema

#### New Tables
```sql
-- Track payment failures for analytics
payment_failures (
    id, transaction_id, subscription_id, customer_id,
    amount, currency, failure_reason, occurred_at, raw_data
)

-- Track subscription lifecycle events
subscription_events (
    id, subscription_id, event_type, occurred_at, event_data
)
```

#### Enhanced Columns
```sql
-- Added to user_subscriptions
next_billed_at TIMESTAMPTZ  -- Track next billing attempt
```

### 2. Access Control Logic

**During `past_due` status:**
- ✅ Customers retain full access to services
- ✅ Quotas remain active and usable
- ⚠️ Payment update prompts shown in UI
- 📧 Paddle sends recovery emails automatically

**Rationale:** This follows Paddle's best practice recommendation to maintain access during the dunning period to reduce churn and improve recovery rates.

### 3. Webhook Handlers

#### `handleSubscriptionPastDue()`
- Updates subscription status to `past_due`
- Maintains user access (no quota restrictions)
- Logs event for analytics
- Updates billing period information

#### `handleSubscriptionActivated()`
- Restores subscription to `active` status
- Updates billing periods
- Logs successful recovery event

#### `handleTransactionPaymentFailed()`
- Logs individual payment attempt failures
- Updates transaction status
- Stores failure details for analysis

### 4. Frontend Integration

#### Access Check Function
```typescript
// Use this to check user access in your app
const accessInfo = await supabase.rpc('check_subscription_access', {
  p_user_id: userId
});

if (accessInfo.has_access) {
  // Grant access to features
  if (accessInfo.subscription_status === 'past_due') {
    // Show payment update prompt
    showPaymentUpdateBanner();
  }
} else {
  // Restrict access, show upgrade/resubscribe options
}
```

#### UI Considerations
- Show payment update prompts for `past_due` subscriptions
- Display clear status messages and next steps
- Provide easy payment method update flow
- Show grace period information ("Your access continues while we retry payment")

## Configuration Options

### Option 1: Basic Paddle Dunning (Implemented)
- **Cost:** Free with Paddle
- **Duration:** 30 days automatic retry
- **Outcome:** Auto-cancel after failed recovery
- **Maintenance:** Zero - fully automated

### Option 2: Paddle Retain (Upgrade Option)
- **Cost:** Additional fee to Paddle
- **Features:** Enhanced recovery emails, SMS, in-app notifications
- **Customization:** Configure pause vs cancel outcome
- **Analytics:** Advanced recovery insights

## Monitoring and Analytics

### Key Metrics to Track
1. **Payment failure rate** - `payment_failures` table
2. **Recovery success rate** - Compare `past_due` → `activated` events
3. **Dunning duration** - Time between `past_due` and resolution
4. **Churn from payment failures** - `past_due` → `canceled` events

### Useful Queries
```sql
-- Payment failure rate by month
SELECT 
  DATE_TRUNC('month', occurred_at) as month,
  COUNT(*) as failures,
  COUNT(DISTINCT subscription_id) as affected_subscriptions
FROM payment_failures 
GROUP BY month;

-- Recovery success rate
SELECT 
  COUNT(CASE WHEN event_type = 'past_due' THEN 1 END) as past_due_count,
  COUNT(CASE WHEN event_type = 'activated_from_past_due' THEN 1 END) as recovered_count
FROM subscription_events 
WHERE occurred_at >= NOW() - INTERVAL '30 days';
```

## Advantages of This Approach

### 1. Zero Infrastructure Overhead
- No cron jobs or scheduled tasks
- No custom grace period logic
- No additional server costs

### 2. Optimized Recovery
- Paddle's algorithms optimize retry timing
- Professional recovery email templates
- Higher success rates than custom implementations

### 3. Compliance and Best Practices
- Follows payment industry standards
- Automatic compliance with payment regulations
- Reduced PCI scope

### 4. Scalability
- Handles any volume without additional infrastructure
- No performance impact on your application
- Automatic scaling with Paddle's systems

## Alternative Approaches Considered

### Custom Grace Period (Not Recommended)
**Why avoided:**
- Requires cron jobs or scheduled functions
- Additional infrastructure costs and complexity
- Lower recovery rates than optimized systems
- More maintenance overhead
- Potential reliability issues

### Immediate Cancellation (Not Recommended)
**Why avoided:**
- Higher churn rates
- Poor customer experience
- Lost revenue from recoverable failures
- No opportunity for customer self-service recovery

## Implementation Checklist

- [x] Add webhook handlers for payment failure events
- [x] Create database tables for tracking failures and events
- [x] Implement access control logic that maintains access during `past_due`
- [x] Add database functions for subscription access checks
- [x] Create migration files for new schema
- [ ] Update frontend to show payment prompts for `past_due` subscriptions
- [ ] Implement payment method update flow
- [ ] Add monitoring dashboard for payment failures
- [ ] Test webhook handlers with Paddle's webhook simulator
- [ ] Configure Paddle notification settings
- [ ] Set up alerting for high failure rates

## Testing Strategy

### Webhook Testing
Use Paddle's webhook simulator to test:
1. `subscription.past_due` scenarios
2. `subscription.activated` recovery scenarios  
3. `subscription.canceled` final failure scenarios
4. `transaction.payment_failed` events

### Integration Testing
1. Create test subscription
2. Simulate payment failure
3. Verify access is maintained during `past_due`
4. Test payment method update flow
5. Verify proper status transitions

## Conclusion

This implementation leverages Paddle's mature, optimized payment failure handling system rather than building custom solutions. This approach:

- **Reduces complexity** - No custom grace period logic needed
- **Improves recovery rates** - Uses Paddle's optimized retry algorithms
- **Eliminates infrastructure costs** - No cron jobs or additional services
- **Follows best practices** - Industry-standard dunning process
- **Scales automatically** - No performance concerns as you grow

The system is designed to be maintenance-free while providing comprehensive tracking and analytics for business intelligence purposes.

Transaction
transaction.billed
transaction.canceled
transaction.completed
transaction.created
transaction.paid
transaction.past_due
transaction.payment_failed
transaction.ready
transaction.updated
transaction.revised
Subscription
subscription.activated
subscription.canceled
subscription.created
subscription.imported
subscription.past_due
subscription.paused
subscription.resumed
subscription.trialing
subscription.updated
Product
product.created
product.imported
product.updated
Price
price.created
price.imported
price.updated
Customer
customer.created
customer.imported
customer.updated
Payment Method
payment_method.saved
payment_method.deleted
Address
address.created
address.imported
address.updated
Business
business.created
business.imported
business.updated
Adjustment
adjustment.created
adjustment.updated
Payout
payout.created
payout.paid
Discount
discount.created
discount.imported
discount.updated
Discount Group
discount_group.created
Report
report.created
report.updated
API Key
api_key.created
api_key.expired
api_key.expiring
api_key.revoked
api_key.updated

---

Subscriptions

Subscription entities describe a recurring billing relationship with a customer. They're closely related to transactions.

Subscriptions let customers pay for products on a recurring schedule. They hold information about what Paddle should charge a customer for and how often.

Subscription entities hold information like:

    Who the customer is.
    Which prices a customer has subscribed to.
    How often a subscription renews.
    Details about trial periods.
    Any upcoming scheduled changes.

Subscriptions work with products, prices, and discounts to say what a customer has subscribed to, and customers, addresses, and businesses to say who the customer is.
Create a subscription

You can't create a subscription directly.

Paddle automatically creates subscriptions for you when customers pay for recurring items using the checkout, or when you create and issue an invoice using a manually-collected transaction.
Delete a subscription

Subscriptions describe an ongoing financial relationship with a customer, so they can't be deleted. Use the cancel a subscription operation to cancel a subscription.
Transactions

Billing for subscriptions is powered by transactions. When a subscription bills, Paddle creates a related transaction to calculate totals and collect for payment.

You can get a preview of the next transaction when getting a subscription using the include parameter.
Scheduled changes

A scheduled change is a change that's going to happen automatically when the subscription next bills.

Paddle creates a scheduled change automatically when you cancel, pause, or update an item on a subscription and returns them in the scheduled_change object.
Proration

Proration is how Paddle calculates what a customer should be billed for, based on changes made in the current billing cycle.

When updating subscription items, you must include the proration_billing_mode field to tell Paddle how to handle proration for the items you're adding or removing.
Customer portal URLs

Subscriptions return authenticated links to the customer portal in the management_urls object. You can use these links to redirect customers to the portal to manage their subscriptions.

Authenticated links are only returned when your API key has a Customer portal session (Write) permission.

    The token appended to authenticated links is the token for the customer portal session. It is temporary and shouldn't be cached or stored.

Add or remove items
Read more
Upgrade or downgrade
Read more
Proration
Read more
Attributes
idstring

Unique Paddle ID for this subscription entity, prefixed with sub_.
statusstring

Status of this subscription. Set automatically by Paddle. Use the pause subscription or cancel subscription operations to change.
customer_idstring

Paddle ID of the customer that this subscription is for, prefixed with ctm_.
address_idstring

Paddle ID of the address that this subscription is for, prefixed with add_.
business_idstring or null

Paddle ID of the business that this subscription is for, prefixed with biz_.
currency_codestring

Supported three-letter ISO 4217 currency code. Transactions for this subscription are created in this currency. Must be USD, EUR, or GBP if collection_mode is manual.
created_atstring<date-time>

RFC 3339 datetime string of when this entity was created. Set automatically by Paddle.
updated_atstring<date-time>

RFC 3339 datetime string of when this entity was updated. Set automatically by Paddle.
started_atstring<date-time> or null

RFC 3339 datetime string of when this subscription started. This may be different from first_billed_at if the subscription started in trial.
first_billed_atstring<date-time> or null

RFC 3339 datetime string of when this subscription was first billed. This may be different from started_at if the subscription started in trial.
next_billed_atstring<date-time> or null

RFC 3339 datetime string of when this subscription is next scheduled to be billed.
paused_atstring<date-time> or null

RFC 3339 datetime string of when this subscription was paused. Set automatically by Paddle when the pause subscription operation is used. null if not paused.
canceled_atstring<date-time> or null

RFC 3339 datetime string of when this subscription was canceled. Set automatically by Paddle when the cancel subscription operation is used. null if not canceled.
discountobject or null

Details of the discount applied to this subscription.
ends_atstring<date-time> or null

RFC 3339 datetime string of when this discount no longer applies. Where a discount has maximum_recurring_intervals, this is the date of the last billing period where this discount applies. null where a discount recurs forever.
idstring

Unique Paddle ID for this discount, prefixed with dsc_.
starts_atstring<date-time> or null

RFC 3339 datetime string of when this discount was first applied. null for canceled subscriptions where a discount was redeemed but never applied to a transaction.
collection_modestring

How payment is collected for transactions created for this subscription. automatic for checkout, manual for invoices.
billing_detailsobject or null

Details for invoicing. Required if collection_mode is manual.
payment_termsobject

How long a customer has to pay this invoice once issued.
intervalstring

Unit of time.
frequencyinteger

Amount of time.
enable_checkoutboolean

Whether the related transaction may be paid using Paddle Checkout. If omitted when creating a transaction, defaults to false.
purchase_order_numberstring

Customer purchase order number. Appears on invoice documents.
additional_informationstring or null

Notes or other information to include on this invoice. Appears on invoice documents.
current_billing_periodobject or null

Current billing period for this subscription. Set automatically by Paddle based on the billing cycle. null for paused and canceled subscriptions.
ends_atstring<date-time>

RFC 3339 datetime string of when this period ends.
starts_atstring<date-time>

RFC 3339 datetime string of when this period starts.
billing_cycleobject

How often this subscription renews. Set automatically by Paddle based on the prices on this subscription.
intervalstring

Unit of time.
frequencyinteger

Amount of time.
scheduled_changeobject or null

Change that's scheduled to be applied to a subscription. Use the pause subscription, cancel subscription, and resume subscription operations to create scheduled changes. null if no scheduled changes.
actionstring

Kind of change that's scheduled to be applied to this subscription.
effective_atstring<date-time>

RFC 3339 datetime string of when this scheduled change takes effect.
resume_atstring<date-time> or null

RFC 3339 datetime string of when a paused subscription should resume. Only used for pause scheduled changes.
management_urlsobject

Customer portal deep links for this subscription.

Authenticated links are only returned when your API key has Customer portal session (Write) permission. For security, the token appended to authenticated links is temporary. You shouldn't store them.
update_payment_methodstring<uri> or null

Link to the page for this subscription in the customer portal with the payment method update form pre-opened. Use as part of workflows to let customers update their payment details. null for manually-collected subscriptions.
cancelstring<uri>

Link to the page for this subscription in the customer portal with the subscription cancellation form pre-opened. Use as part of cancel subscription workflows.
itemsarray[object]

List of items on this subscription. Only recurring items are returned.
statusstring

Status of this subscription item. Set automatically by Paddle.
quantitynumber

Quantity of this item on the subscription.
recurringboolean

Whether this is a recurring item. false if one-time.
created_atstring<date-time>

RFC 3339 datetime string of when this item was added to this subscription.
updated_atstring<date-time>

RFC 3339 datetime string of when this item was last updated on this subscription.
previously_billed_atstring<date-time> or null

RFC 3339 datetime string of when this item was last billed.
next_billed_atstring<date-time> or null

RFC 3339 datetime string of when this item is next scheduled to be billed.
trial_datesobject or null

Trial dates for this item.
priceobject

Related price entity for this item. This reflects the price entity at the time it was added to the subscription.
productobject

Related product entity for this item. This reflects the product entity at the time it was added to the subscription.
custom_dataobject or null

Your own structured key-value data.
import_metaobject or null

Import information for this entity. null if this entity is not imported.
imported_fromstring

Name of the platform or provider where this entity was imported from.
external_idstring or null

Reference or identifier for this entity from the provider where it was imported from.
List subscriptions
get
https://api.paddle.com/subscriptions
Get a subscription
get
https://api.paddle.com/subscriptions/{subscription_id}
Preview an update to a subscription
patch
https://api.paddle.com/subscriptions/{subscription_id}/preview
Update a subscription
patch
https://api.paddle.com/subscriptions/{subscription_id}
Get a transaction to update payment method
get
https://api.paddle.com/subscriptions/{subscription_id}/update-payment-method-transaction
Preview a one-time charge for a subscription
post
https://api.paddle.com/subscriptions/{subscription_id}/charge/preview
Create a one-time charge for a subscription
post
https://api.paddle.com/subscriptions/{subscription_id}/charge
Activate a trialing subscription
post
https://api.paddle.com/subscriptions/{subscription_id}/activate
Pause a subscription
post
https://api.paddle.com/subscriptions/{subscription_id}/pause
Resume a paused subscription
post
https://api.paddle.com/subscriptions/{subscription_id}/resume
Cancel a subscription
post
https://api.paddle.com/subscriptions/{subscription_id}/cancel
Retry payment for a subscription transaction
post
https://api.paddle.com/subscriptions/{subscription_id}/transactions/{transaction_id}/retry

---

Webhooks

Webhooks let you get notified when events happen in Paddle. They're also called notifications.

Webhooks let you subscribe to events in Paddle. When a subscribed event occurs, Paddle sends a notification to a webhook endpoint that includes a JSON payload with the updated entity.

Use webhooks to:

    Manage access to features in your app depending on a customer's subscription status.
    Sync information with other systems that your business uses, like a CRM or ERP solution.
    Set up notifications or automations.

You can set up URLs to receive webhooks and the types of events you want to get from your Paddle dashboard under Developer Tools > Notifications. You may also set up notifications by email.
Get started

Create a notification destination to receive webhooks, handle webhook delivery, and verify signatures.
Create a notification destination
Read more
Handle webhook delivery
Read more
Verify webhook signatures
Read more
Explore scenarios

Use webhook simulator to send test webhooks for single events or predefined scenarios as part of testing and integration.
Simulate webhooks
Read more
Subscription created scenario
Read more
Subscription renewed scenario
Read more

 
Subscription paused scenario
Read more
Subscription resumed scenario
Read more
Subscription canceled scenario
Read more
Explore events

Learn about key events that occur when working with Paddle Billing.
Product catalog
product.created
Read more
price.created
Read more
discount.created
Read more
Customers
customer.created
Read more
address.created
Read more
business.created
Read more
Billing and subscriptions
transaction.created
Read more
transaction.updated
Read more
transaction.completed
Read more

 
subscription.created
Read more
subscription.past_due
Read more
subscription.canceled
Read more
Finance and administration
report.updated
Read more
payout.created
Read more
payout.paid
Read more
Handle provisioning for subscriptions

Check out our subscription lifecycle guides in the Build section to understand what happens for each part of the subscription lifecycle, including which events occur, recommended workflow, and fields that you may like to store or update.
Subscription creation
Read more
Subscription renewal
Read more
Subscription past due
Read more
Subscription pause or resume
Read more
Subscription cancellation
Read more
Payment details update
Read more

---

Subscription renewal

Subscriptions renew automatically in Paddle unless canceled or paused. Paddle collects using a saved payment method, or sends an invoice that customers must pay.

Renewals are the defining feature of subscription business models. Subscription renewals happens at the end of a billing period when a customer continues their subscription and is billed for the next billing period.

A renewal is successful where payment is collected successfully. If unsuccessful, a subscription becomes past due.
How it works

At the end of the current billing period, Paddle automatically creates a transaction for the next billing period. This transaction is for any recurring items on the subscription, along with any one-time charges or subscription changes that have been billed on the next billing period.

Transactions are automatically created for subscription renewals, even for manually-collected subscriptions:

    For automatically-collected subscriptions, Paddle creates a transaction and collects for it automatically using the payment method on file. It becomes past due if a collection attempt fails.
    For manually-collected subscriptions, Paddle creates and issues an invoice. Customers must pay it manually before the payment terms elapse, otherwise it becomes past due.

Subscriptions that are scheduled to pause or cancel aren't renewed.
Events

    This guide is overview of events that typically occur. Depending on the customer journey and how you build your workflows, not all events listed may occur, or additional events may occur.

    We can't guarantee the order of delivery for webhooks. Store and check the occurred_at date against a webhook before making changes.

    Subscription renews
    subscription.updated	The subscription and its items are updated with new previously_billed_at and next_billed_at dates, and the current_billing_period is updated.
    transaction.created	Paddle creates a transaction for recurring items on the subscription, as well as any prorated or one-time charges that were set to be billed on the next billing period. Its status is billed, meaning no changes can be made to the transaction. Its origin is subscription_recurring.
    transaction.billed	Occurs because the transaction status changes to billed.
    Payment collected successfully
    transaction.updated	The transaction status changes to paid now that the customer has paid successfully. The transaction is updated with information about the successful payment.
    transaction.paid	Occurs because the transaction status changes to paid.
    transaction.updated	An invoice number is assigned to the transaction. Its status changes to completed as Paddle has finished processing it.
    transaction.completed	Occurs because the transaction status changes to completed.

Emails

For compliance reasons, Paddle sends emails for subscription lifecycle events.

When a subscription renews, Paddle sends:

Screenshot showing a receipt email. It includes a list of items, total, and billing date.
Receipt

Your receipt from [company]

List of items purchased, along with totals and purchase date. Includes a PDF invoice for their records.
Recommended workflow

    Listen for events

    We recommend that you listen for:
    subscription.updated	Get notified when a subscription is updated.
    transaction.created	Get notified when a transaction is created. Transactions with the origin of subscription_recurring are for renewals.
    transaction.completed	Get notified when a transaction is completed. This means it's fully paid and Paddle has completed processing.
    Update information in your database

    Update the record for a subscription in your database for the renewal based on webhooks received. As well as checking and storing the occurred_at date against notifications, you'll typically need to update:
    Description	Field name	Reason to store
    Subscription status	subscription.status	Used to determine if a renewal was successful. Status is past_due where automatic collection fails, or where an invoice is unpaid and its payment terms have elapsed.
    Next billing date	subscription.next_billed_at	Used to determine when a subscription renews if active.

    You may like to store and update other fields if you want to build a more comprehensive billing and subscription management experience. To learn more, see Handle provisioning and fulfillment
    Provision

    Provision so that a customer has continued access to your app.

     

    Use the subscription.status field to determine whether a subscription should have access to your app. Where status is active or past_due, customers should have full access to your app.

Related pages
Handle provisioning and fulfillment
Read more
Paddle Checkout
Read more
Invoices
Read more

---

Proration

Choose how and when customers are charged when they upgrade or downgrade their subscription, or make other changes to items on it.
Screenshot of an invoice in Paddle
How it works

Proration is how Paddle calculates what a customer should be billed for based on changes made in the current billing cycle.

For example, if a customer adds a product midway through their billing cycle, you can charge them for just the time they used rather than the entire period. If they replace a product, you can calculate the difference and charge them accurately.

You're in control of proration. When changing items on a subscription, you can choose:

    To prorate and bill now.
    To prorate and bill on the next billing date.
    Not to prorate, and to charge the full amount now.
    Not to prorate, and to charge the full amount on the next billing date.
    Not to bill at all.

Paddle's subscription billing engine calculates proration to the minute.
Proration options

You can tell Paddle how you want to prorate when editing a subscription in the Paddle dashboard.

When updating items on a subscription using the API, include the proration_billing_mode field to tell Paddle how to handle proration. The options are:
Value	Description
prorated_immediately	Prorated amount is calculated now. The customer is billed the prorated amount now.
full_immediately	Prorated amount isn't calculated. The customer is billed the full amount now.
prorated_next_billing_period	Prorated amount is calculated now. The customer is billed the prorated amount on their next renewal.
full_next_billing_period	Prorated amount isn't calculated. The customer is billed for the full amount on their next renewal.
do_not_bill	Prorated amount isn't calculated. The customer isn't billed for the prorated amount or the full amount.

Check details.line_items.proration against a transaction to see the rate of proration that Paddle used to calculate a total.Proration

Choose how and when customers are charged when they upgrade or downgrade their subscription, or make other changes to items on it.
Screenshot of an invoice in Paddle
How it works

Proration is how Paddle calculates what a customer should be billed for based on changes made in the current billing cycle.

For example, if a customer adds a product midway through their billing cycle, you can charge them for just the time they used rather than the entire period. If they replace a product, you can calculate the difference and charge them accurately.

You're in control of proration. When changing items on a subscription, you can choose:

    To prorate and bill now.
    To prorate and bill on the next billing date.
    Not to prorate, and to charge the full amount now.
    Not to prorate, and to charge the full amount on the next billing date.
    Not to bill at all.

Paddle's subscription billing engine calculates proration to the minute.
Proration options

You can tell Paddle how you want to prorate when editing a subscription in the Paddle dashboard.

When updating items on a subscription using the API, include the proration_billing_mode field to tell Paddle how to handle proration. The options are:
Value	Description
prorated_immediately	Prorated amount is calculated now. The customer is billed the prorated amount now.
full_immediately	Prorated amount isn't calculated. The customer is billed the full amount now.
prorated_next_billing_period	Prorated amount is calculated now. The customer is billed the prorated amount on their next renewal.
full_next_billing_period	Prorated amount isn't calculated. The customer is billed for the full amount on their next renewal.
do_not_bill	Prorated amount isn't calculated. The customer isn't billed for the prorated amount or the full amount.

Check details.line_items.proration against a transaction to see the rate of proration that Paddle used to calculate a total.
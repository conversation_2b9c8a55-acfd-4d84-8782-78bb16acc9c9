# Supabase Email Service Configuration Guide

**Last Updated:** June 3, 2025

## Overview

This document summarizes the setup of an external email service for Supabase, using Deno Deploy to bypass DigitalOcean's SMTP port restrictions (25, 465, 587).

## Solution Architecture

```
┌─────────────────┐        ┌────────────────┐        ┌───────────────┐
│  Supabase Auth  │  HTTP  │  Deno Deploy   │  SMTP  │  GoDaddy SMTP │
│    (GoTrue)     │───────▶│  Email Service │───────▶│     Server    │
└─────────────────┘        └────────────────┘        └───────────────┘
```

## Correct Configuration

### 1. Deno Deploy

Your Deno Deploy service is configured with these environment variables:
- `SMTP_HOST`: smtpout.secureserver.net
- `SMTP_PORT`: 465
- `SMTP_USER`: <EMAIL>
- `SMTP_PASS`: [your email password]
- `FROM_EMAIL`: <EMAIL>
- `FROM_NAME`: VoiceHype
- `WEBHOOK_SECRET`: whsec_FqbvJ2MJ8QnQAP2uUsoQMXbg4aM5mNYq

### 2. Supabase Configuration

In your Supabase `.env` file:

```bash
# Required SMTP placeholders (must be set even when using external email service)
SMTP_HOST=localhost
SMTP_PORT=25
SMTP_USER=placeholder
SMTP_PASS=placeholder

# External email service configuration
GOTRUE_HOOK_SEND_EMAIL_ENABLED=true
GOTRUE_HOOK_SEND_EMAIL_URI=https://voicehype-email-service.deno.dev
GOTRUE_HOOK_SEND_EMAIL_SECRETS=v1,whsec_FqbvJ2MJ8QnQAP2uUsoQMXbg4aM5mNYq
GOTRUE_MAILER_EXTERNAL_HOSTS=supabase.voicehype.ai,voicehype.ai
```

## Important Lessons

1. **SMTP Configuration**: Even when using an external email service, GoTrue still requires valid SMTP configuration values. These won't actually be used for sending emails but are still validated during startup.

2. **Webhook Secret Format**: The `GOTRUE_HOOK_SEND_EMAIL_SECRETS` value must be in the format `v1,whsec_<your_secret>`. This is a critical requirement for GoTrue to accept the configuration.

3. **Deno Deploy Authentication**: In the Deno Deploy service, the webhook secret should be verified with the format `whsec_<your_secret>` (without the `v1,` prefix).

## Troubleshooting

### Common Errors

1. **SMTP_PORT Error**: If you see errors about SMTP_PORT not being set or parsing failed, ensure you have the placeholder SMTP configuration in your `.env` file.
   ```
   Failed to load configuration: envconfig.Process: assigning GOTRUE_SMTP_PORT to Port: converting '' to type int.
   ```
   
2. **Invalid Secret Format**: If you see this error, ensure your webhook secret follows the format `v1,whsec_<your_secret>`.
   ```
   Failed to load configuration: invalid secret format
   ```

3. **Service Temporarily Unavailable (503)**: This may indicate that your Supabase auth service isn't properly configured or is still restarting.

### Restart Process

When making changes to your email configuration:

1. Update the `.env` file with your changes
2. Run the following commands:
   ```bash
   cd /path/to/supabase-project
   docker-compose down
   docker-compose up -d
   ```
3. Check the auth service logs:
   ```bash
   docker logs supabase-auth
   ```

## Testing

To test if your configuration is working:

1. Sign up a new user that requires email verification
2. Check the logs of your Deno Deploy service for any errors
3. Check if the verification email is received by the user

## Security Notes

- Keep your webhook secret secure and don't share it publicly
- Regularly rotate your webhook secrets for enhanced security
- Ensure your Deno Deploy account has proper authentication enabled

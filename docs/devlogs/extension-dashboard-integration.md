# DevLog: Extension Dashboard Integration

**Created:** 2025-06-24  
**Status:** Planning Phase  
**Priority:** High  

## 1. Conceptual Plan

We want to bring the powerful dashboard features from the VoiceHype website directly into the VS Code extension. Currently, users need to switch between VS Code and the web browser to check their usage statistics, credit balance, subscription details, and recent activity. This creates friction in the developer workflow.

The goal is to create a seamless experience where developers can:
- View their account statistics (credits, subscription quotas, usage) without leaving VS Code
- Monitor their transcription and token usage in real-time
- Access account settings and manage their subscription
- See recent activity and usage history
- Authenticate with Supabase directly through the extension

This integration will make VoiceHype feel like a native part of the VS Code development environment, improving user experience and engagement.

## 2. Codebase Review

### Current Extension Structure
- **Main Extension**: `extension/src/extension.ts` - Entry point
- **Panel Service**: `extension/src/services/VoiceHypePanelService.ts` - Manages webview
- **React Webview**: `extension/webview-ui/src/App.tsx` - Main UI component
- **Vue Webview**: `extension/webview-ui-vue/src/App.vue` - Alternative Vue implementation

### Current Authentication
- Extension currently uses API key-based authentication
- VSCodeAuthView.vue in website already returns Supabase tokens for full authentication
- Extension can receive access_token, refresh_token, and other session data

### Website Dashboard Features to Port
- **Dashboard Stats**: Credit balance, transcription/token usage, API keys count
- **Usage History**: Recent activity table with service, model, cost, date
- **Settings**: Profile management, subscription details
- **Charts**: Usage visualization (minutes/tokens over time)

### Key Components to Leverage
- `ProfileAvatar.vue` - For user profile display
- Dashboard stores: `subscription.ts`, `credits.ts`, `usage.ts`, `apiKeys.ts`
- Supabase client and helper functions

## 3. Technical Plan

### Files to be Created
1. **`extension/webview-ui/src/components/dashboard/`**
   - `DashboardView.tsx` - Main dashboard component
   - `StatsCards.tsx` - Credit balance, usage stats cards
   - `RecentActivity.tsx` - Usage history table
   - `UserProfile.tsx` - User profile with avatar

2. **`extension/webview-ui/src/components/settings/`**
   - `SettingsView.tsx` - Settings page component
   - `ProfileSettings.tsx` - Profile management
   - `SubscriptionInfo.tsx` - Subscription details

3. **`extension/webview-ui/src/services/`**
   - `SupabaseService.ts` - Supabase client and authentication
   - `DashboardService.ts` - Dashboard data fetching

4. **`extension/webview-ui/src/stores/`**
   - `authStore.ts` - Authentication state management
   - `dashboardStore.ts` - Dashboard data state

### Files to be Modified
1. **`extension/webview-ui/src/App.tsx`**
   - Add navigation between main view and dashboard
   - Integrate Supabase authentication
   - Add dashboard/settings routes

2. **`extension/src/services/VoiceHypePanelService.ts`**
   - Handle Supabase token storage
   - Add message handlers for dashboard actions

3. **`extension/webview-ui/src/components/`**
   - Update existing components to work with new navigation

### Implementation Approach
1. **Phase 1: Authentication Integration**
   - Set up Supabase client in extension
   - Handle token storage and refresh
   - Create authentication service

2. **Phase 2: Basic Dashboard**
   - Create dashboard view with stats cards
   - Implement credit balance and usage display
   - Add navigation between main and dashboard views

3. **Phase 3: Advanced Features**
   - Add recent activity table
   - Implement settings page
   - Add user profile with avatar

4. **Phase 4: Polish & Optimization**
   - Add loading states and error handling
   - Optimize data fetching
   - Add refresh mechanisms

## 4. To-Do List

### Authentication & Setup
⏳ Set up Supabase client in extension webview  
⏳ Create authentication service for token management  
⏳ Implement token storage and refresh logic  
⏳ Add authentication state management  

### Dashboard Core Features
⏳ Create main dashboard view component  
⏳ Implement stats cards (credits, usage, API keys)  
⏳ Add navigation between main view and dashboard  
⏳ Create user profile component with avatar support  

### Data Integration
⏳ Port dashboard stores from website to extension  
⏳ Implement data fetching services  
⏳ Add real-time data updates  
⏳ Handle loading and error states  

### Settings & Profile
⏳ Create settings view component  
⏳ Implement profile management  
⏳ Add subscription information display  
⏳ Port settings functionality from website  

### Recent Activity
⏳ Create recent activity table component  
⏳ Implement usage history display  
⏳ Add filtering and pagination  
⏳ Format data display (tokens, costs, dates)  

### UI/UX Polish
⏳ Add smooth transitions between views  
⏳ Implement responsive design for different panel sizes  
⏳ Add dark/light theme support  
⏳ Optimize performance and loading times  

### Testing & Documentation
⏳ Test authentication flow  
⏳ Test dashboard functionality  
⏳ Update extension documentation  
⏳ Create user guide for new features  

## 5. Progress Notes

### 2025-06-24 - Initial Planning
- Created comprehensive DevLog for extension dashboard integration
- Analyzed current codebase structure and identified key components
- Planned phased implementation approach
- Identified that VSCodeAuthView.vue already provides Supabase tokens
- Ready to begin implementation, insha'Allah

---

**Next Steps:** Begin Phase 1 with authentication integration and Supabase client setup.

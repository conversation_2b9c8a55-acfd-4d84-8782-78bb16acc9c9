# Migration to Separate Subscription Prices - Implementation Log

**Date:** July 5, 2025  
**Status:** Implementation Started  
**Author:** Voice Hype Development Team

## Implementation Plan

### Phase 1: Paddle Product Setup
- [ ] Create one product with three price tiers in Paddle Dashboard
- [ ] Configure sandbox environment variables
- [ ] Configure production environment variables

### Phase 2: Frontend Checkout Updates
- [ ] Update subscription store (`/voicehype-website/src/stores/subscription.ts`)
- [ ] Update pricing components
- [ ] Update PaymentsView.vue

### Phase 3: Backend Upgrade System Updates
- [ ] Update upgrade function
- [ ] Add new environment variables to Supabase
- [ ] Verify cancellation logic

### Phase 4: Webhook Updates
- [ ] Update webhook handler
- [ ] Update subscription upgrade helper

### Phase 5: Testing Strategy
- [ ] Sandbox testing
- [ ] Staging environment testing
- [ ] Production verification

# Migration to Separate Subscription Prices - Implementation Status

**Date:** July 6, 2025  
**Status:** ✅ **MOSTLY COMPLETE - READY FOR TESTING**  
**Author:** Voice Hype Development Team

## Implementation Progress Summary

### ✅ **COMPLETED** 

#### 1. **Paddle Product Setup**
- [x] Created one product with three separate price tiers in Paddle Dashboard
- [x] Configured sandbox environment with correct price IDs
- [x] Configured production environment with correct price IDs

#### 2. **Frontend Implementation**
- [x] **Subscription Store** - Updated to use plan-specific price IDs from environment variables
- [x] **PaymentsView.vue** - Updated to check all new price ID environment variables
- [x] **Environment Variable Validation** - Added comprehensive checks for all price IDs

#### 3. **Backend Implementation** 
- [x] **Upgrade Function** - Updated to use separate price IDs with quantity=1
- [x] **Price ID Logic** - Implemented plan-specific price ID selection
- [x] **Environment Variables** - Ready for Supabase configuration
- [x] **Billing Logic** - Fixed immediate billing to use correct price IDs

#### 4. **Code Quality**
- [x] **Syntax Errors** - Fixed all diff markers and syntax issues
- [x] **Variable Consistency** - Updated all references to use `priceIdForPlan`
- [x] **Error Handling** - Maintained robust fallback mechanisms

### 🔄 **PENDING**

#### 1. **Environment Variable Configuration**
- [ ] Add new price IDs to Supabase Edge Functions environment:
  ```bash
  PADDLE_PRICE_ID_BASIC_SANDBOX=pri_01jyn32mtqwxarqmshhf9rt222
  PADDLE_PRICE_ID_PRO_SANDBOX=pri_01jzbqn2tahrqvs56dst9lz2ta
  PADDLE_PRICE_ID_PREMIUM_SANDBOX=pri_01jzbqp7qbswzjkvy4hstgsmh2
  
  PADDLE_PRICE_ID_BASIC_PRODUCTION=pri_01jyn2xdwvmnacptsbdcn0vbd6
  PADDLE_PRICE_ID_PRO_PRODUCTION=pri_01jzbqyzdwqvt3ntr5crry9vmw
  PADDLE_PRICE_ID_PREMIUM_PRODUCTION=pri_01jzbqzb2y2jkr64rbekpn2276
  ```

- [ ] Add new price IDs to frontend environment variables:
  ```bash
  VITE_PADDLE_PRICE_ID_BASIC_SANDBOX=pri_01jyn32mtqwxarqmshhf9rt222
  VITE_PADDLE_PRICE_ID_PRO_SANDBOX=pri_01jzbqn2tahrqvs56dst9lz2ta
  VITE_PADDLE_PRICE_ID_PREMIUM_SANDBOX=pri_01jzbqp7qbswzjkvy4hstgsmh2
  
  VITE_PADDLE_PRICE_ID_BASIC_PRODUCTION=pri_01jyn2xdwvmnacptsbdcn0vbd6
  VITE_PADDLE_PRICE_ID_PRO_PRODUCTION=pri_01jzbqyzdwqvt3ntr5crry9vmw
  VITE_PADDLE_PRICE_ID_PREMIUM_PRODUCTION=pri_01jzbqzb2y2jkr64rbekpn2276
  ```

#### 2. **Webhook Updates**
- [ ] Update webhook handler to detect plan from price ID instead of quantity
- [ ] Update subscription upgrade helper with new price detection logic
- [ ] Test webhook processing with new price IDs

#### 3. **Testing Requirements**
- [ ] **Sandbox Testing:**
  - New subscription checkout for each plan
  - Upgrade/downgrade flows between plans
  - Immediate vs scheduled upgrade options
  - Webhook event processing

- [ ] **Production Verification:**
  - Environment variable validation
  - Checkout flow testing
  - Upgrade flow testing

### 🏗️ **FILES MODIFIED**

#### Frontend Files:
- ✅ `/voicehype-website/src/stores/subscription.ts` - Updated checkout and upgrade logic
- ✅ `/voicehype-website/src/views/PaymentsView.vue` - Environment variable validation

#### Backend Files:
- ✅ `/supabase/functions/upgrade-subscription/index.ts` - Plan-specific price ID logic
- 🔄 `/supabase/functions/paddle-webhook/index.ts` - Needs price ID-based plan detection
- 🔄 `/supabase/functions/paddle-webhook/subscription-upgrade.ts` - Needs updates

### 🚨 **CRITICAL FIXES APPLIED**

1. **Edge Function Syntax Errors** - Removed invalid diff markers (`+`, `-`)
2. **Variable Consistency** - Updated all billing calls to use `priceIdForPlan`
3. **Price ID Validation** - Added proper error handling for missing price IDs
4. **Environment Variables** - Updated subscription store to use env vars instead of hardcoded IDs

### 🔧 **NEXT STEPS**

#### Immediate (Today):
1. **Configure Environment Variables** in Supabase and hosting platform
2. **Update Webhook Handler** for price ID-based plan detection
3. **Deploy Changes** to staging environment

#### Testing Phase:
1. **Sandbox Testing** - Verify all checkout and upgrade flows
2. **Webhook Testing** - Ensure proper plan detection and processing
3. **Environment Validation** - Confirm all price IDs are properly configured

#### Production Deployment:
1. **Deploy Backend Changes** - Update edge functions
2. **Deploy Frontend Changes** - Update pricing components
3. **Monitor Performance** - Track success rates and errors

### 🎯 **IMPLEMENTATION STATUS: 85% COMPLETE**

**Ready for:** Environment variable configuration and webhook updates  
**Estimated time to completion:** 2-4 hours  
**Risk level:** Low (comprehensive fallback mechanisms in place)
- `/supabase/functions/paddle-webhook/index.ts` - Webhook handler
- `/supabase/functions/paddle-webhook/subscription-upgrade.ts` - Upgrade helper class

**Current Upgrade Logic:**
```typescript
// In upgrade-subscription/index.ts
const planQuantities = {
  'Basic': 1,   // 1 × $9 = $9
  'Pro': 2,     // 2 × $9 = $18  
  'Premium': 3  // 3 × $9 = $27
}

const newItems = [{
  price_id: priceId,  // Same price ID, different quantity
  quantity: planQuantities[targetPlan]
}]
```

### 3. Subscription Cancellation

**Files:**
- `/supabase/functions/cancel-subscription/index.ts` - Cancellation logic

**Current Logic:**
- Finds active subscription by `paddle_subscription_id`
- Calls Paddle API to cancel subscription
- Works correctly regardless of price structure

### 4. Webhook Processing

**Files:**
- `/supabase/functions/paddle-webhook/index.ts` - Main webhook handler
- `/supabase/functions/paddle-webhook/_shared/types.ts` - Type definitions

**Current Processing:**
- Handles subscription events with quantity-based plan detection
- Uses custom_data to determine plan details
- Supports subscription ID replacement during upgrades

## Migration Plan

### Phase 1: Paddle Product Setup

**1.1 Create Separate Prices for Single Product in Paddle**
```bash
# Create one product with three price tiers in Paddle Dashboard:
# Product: VoiceHype Subscription
# Price 1: Basic - $9/month
# Price 2: Pro - $18/month  
# Price 3: Premium - $27/month
```

**1.2 Update Environment Variables**
```bash
# Replace single price ID with three separate price IDs for the same product:

# SANDBOX ENVIRONMENT (First image):
VITE_PADDLE_PRICE_ID_BASIC_SANDBOX=pri_01jyn32mtqwxarqmshhf9rt222
VITE_PADDLE_PRICE_ID_PRO_SANDBOX=pri_01jzbqn2tahrqvs56dst9lz2ta
VITE_PADDLE_PRICE_ID_PREMIUM_SANDBOX=pri_01jzbqp7qbswzjkvy4hstgsmh2

# PRODUCTION ENVIRONMENT (Second image):
VITE_PADDLE_PRICE_ID_BASIC_PRODUCTION=pri_01jyn2xdwvmnacptsbdcn0vbd6
VITE_PADDLE_PRICE_ID_PRO_PRODUCTION=pri_01jzbqyzdwqvt3ntr5crry9vmw
VITE_PADDLE_PRICE_ID_PREMIUM_PRODUCTION=pri_01jzbqzb2y2jkr64rbekpn2276

# Remove old single price ID variables:
# VITE_PADDLE_SUBSCRIPTION_PRICE_ID_SANDBOX
# VITE_PADDLE_SUBSCRIPTION_PRICE_ID_PRODUCTION
```

### Phase 2: Frontend Checkout Updates

**2.1 Update Subscription Store (`/voicehype-website/src/stores/subscription.ts`)**

**Current Implementation:**
```typescript
// Get the subscription price ID from environment
const priceId = import.meta.env.VITE_PADDLE_ENVIRONMENT === 'production'
  ? import.meta.env.VITE_PADDLE_SUBSCRIPTION_PRICE_ID_PRODUCTION
  : import.meta.env.VITE_PADDLE_SUBSCRIPTION_PRICE_ID_SANDBOX

await paddleService.openCheckout({
  items: [{
    priceId: priceId,
    quantity: quantity  // Variable quantity
  }]
})
```

**New Implementation:**
```typescript
// Get plan-specific price ID from environment
const getPriceId = (planName: string, environment: string) => {
  const priceMap = {
    'Basic': {
      production: 'pri_01jyn2xdwvmnacptsbdcn0vbd6',
      sandbox: 'pri_01jyn32mtqwxarqmshhf9rt222'
    },
    'Pro': {
      production: 'pri_01jzbqyzdwqvt3ntr5crry9vmw',
      sandbox: 'pri_01jzbqn2tahrqvs56dst9lz2ta'
    },
    'Premium': {
      production: 'pri_01jzbqzb2y2jkr64rbekpn2276',
      sandbox: 'pri_01jzbqp7qbswzjkvy4hstgsmh2'
    }
  }
  return priceMap[planName][environment]
}

const priceId = getPriceId(planName, environment)

await paddleService.openCheckout({
  items: [{
    priceId: priceId,
    quantity: 1  // Always quantity 1
  }]
})
```

**2.2 Update Pricing Components**

**Files to Update:**
- `/voicehype-website/src/components/SubscriptionPricingCards.vue`
- `/voicehype-website/src/components/pricing/SubscriptionPricingCards.vue`

**Changes Required:**
- Remove quantity multiplier references
- Update button handlers to pass plan-specific data
- Ensure proper price ID selection logic

**2.3 Update PaymentsView.vue**

**File:** `/voicehype-website/src/views/PaymentsView.vue`

**Changes Required:**
- Update environment variable status checks
- Add new price ID validation
- Update debug information display

**Current Debug Logic:**
```typescript
const subscriptionProductsStatus = computed(() => {
  const basicSandbox = Boolean(import.meta.env.VITE_PADDLE_PRICE_ID_BASIC_SANDBOX)
  const basicProduction = Boolean(import.meta.env.VITE_PADDLE_PRICE_ID_BASIC_PRODUCTION)
  // ... existing logic needs expansion
})
```

**New Debug Logic:**
```typescript
const subscriptionProductsStatus = computed(() => {
  const priceIds = [
    'VITE_PADDLE_PRICE_ID_BASIC_SANDBOX',
    'VITE_PADDLE_PRICE_ID_BASIC_PRODUCTION',
    'VITE_PADDLE_PRICE_ID_PRO_SANDBOX', 
    'VITE_PADDLE_PRICE_ID_PRO_PRODUCTION',
    'VITE_PADDLE_PRICE_ID_PREMIUM_SANDBOX',
    'VITE_PADDLE_PRICE_ID_PREMIUM_PRODUCTION'
  ]
  
  const configured = priceIds.filter(id => Boolean(import.meta.env[id])).length
  return `${configured}/${priceIds.length} configured`
})
```

### Phase 3: Backend Upgrade System Updates

**3.1 Update Upgrade Function (`/supabase/functions/upgrade-subscription/index.ts`)**

**Current Implementation:**
```typescript
const planQuantities = {
  'Basic': 1,   
  'Pro': 2,     
  'Premium': 3  
}
const quantity = planQuantities[targetPlan] || 1

const newItems = [{
  price_id: priceId,  // Same price ID
  quantity: quantity  // Different quantity
}]
```

**New Implementation:**
```typescript
// Helper function to get plan-specific price ID
const getPriceIdForPlan = (planName: string, environment: string) => {
  const priceIdMap = {
    'Basic': environment === 'production' 
      ? Deno.env.get('PADDLE_PRICE_ID_BASIC_PRODUCTION')
      : Deno.env.get('PADDLE_PRICE_ID_BASIC_SANDBOX'),
    'Pro': environment === 'production'
      ? Deno.env.get('PADDLE_PRICE_ID_PRO_PRODUCTION') 
      : Deno.env.get('PADDLE_PRICE_ID_PRO_SANDBOX'),
    'Premium': environment === 'production'
      ? Deno.env.get('PADDLE_PRICE_ID_PREMIUM_PRODUCTION')
      : Deno.env.get('PADDLE_PRICE_ID_PREMIUM_SANDBOX')
  }
  return priceIdMap[planName]
}

const targetPriceId = getPriceIdForPlan(targetPlan, environment)

const newItems = [{
  price_id: targetPriceId,  // Plan-specific price ID
  quantity: 1               // Always quantity 1
}]
```

**3.2 Update Environment Variables in Edge Functions**

**New Environment Variables Needed:**
```bash
# Add to Supabase project settings:

# SANDBOX ENVIRONMENT:
PADDLE_PRICE_ID_BASIC_SANDBOX=pri_01jyn32mtqwxarqmshhf9rt222
PADDLE_PRICE_ID_PRO_SANDBOX=pri_01jzbqn2tahrqvs56dst9lz2ta
PADDLE_PRICE_ID_PREMIUM_SANDBOX=pri_01jzbqp7qbswzjkvy4hstgsmh2

# PRODUCTION ENVIRONMENT:
PADDLE_PRICE_ID_BASIC_PRODUCTION=pri_01jyn2xdwvmnacptsbdcn0vbd6
PADDLE_PRICE_ID_PRO_PRODUCTION=pri_01jzbqyzdwqvt3ntr5crry9vmw
PADDLE_PRICE_ID_PREMIUM_PRODUCTION=pri_01jzbqzb2y2jkr64rbekpn2276
```

**3.3 Update Subscription Cancellation**

**File:** `/supabase/functions/cancel-subscription/index.ts`

**Assessment:** No changes required. The cancellation logic works by subscription ID and is agnostic to the price structure.

### Phase 4: Webhook Updates

**4.1 Update Webhook Handler (`/supabase/functions/paddle-webhook/index.ts`)**

**Current Plan Detection Logic:**
```typescript
// Currently infers plan from quantity
const quantity = subscription.items[0]?.quantity || 1
const planName = quantity === 1 ? 'Basic' : quantity === 2 ? 'Pro' : 'Premium'
```

**New Plan Detection Logic:**
```typescript
// Detect plan from price ID
const getPlanFromPriceId = (priceId: string, environment: string) => {
  const priceIdMap = {
    [Deno.env.get('PADDLE_PRICE_ID_BASIC_SANDBOX')]: 'Basic',
    [Deno.env.get('PADDLE_PRICE_ID_BASIC_PRODUCTION')]: 'Basic',
    [Deno.env.get('PADDLE_PRICE_ID_PRO_SANDBOX')]: 'Pro',
    [Deno.env.get('PADDLE_PRICE_ID_PRO_PRODUCTION')]: 'Pro',
    [Deno.env.get('PADDLE_PRICE_ID_PREMIUM_SANDBOX')]: 'Premium',
    [Deno.env.get('PADDLE_PRICE_ID_PREMIUM_PRODUCTION')]: 'Premium'
  }
  return priceIdMap[priceId] || 'Basic'
}

const priceId = subscription.items[0]?.price_id
const planName = getPlanFromPriceId(priceId, environment)
```

**4.2 Update Subscription Upgrade Helper**

**File:** `/supabase/functions/paddle-webhook/subscription-upgrade.ts`

**Changes Required:**
- Update plan detection logic
- Remove quantity-based calculations
- Ensure compatibility with new price structure

### Phase 5: Database Considerations

**5.1 Subscription Plans Table**

**Current Structure:**
```sql
-- subscription_plans table structure is likely plan-agnostic
-- Should work without changes as it stores plan metadata separately
```

**Assessment:** No database schema changes required. The subscription_plans table stores plan details independently of Paddle price IDs.

**5.2 Migration Strategy for Existing Subscriptions**

**Approach:** 
- Existing subscriptions will continue to work with old price IDs
- New subscriptions will use new separate price IDs
- During next renewal, existing subscriptions can be migrated to new structure
- No immediate migration of active subscriptions required

### Phase 6: Testing Strategy

**6.1 Environment Setup**

1. **Sandbox Testing:**
   - Create test products in Paddle Sandbox
   - Configure sandbox environment variables
   - Test all checkout flows

2. **Staging Environment:**
   - Deploy changes to staging
   - Test with real Paddle sandbox integration
   - Verify webhook processing

**6.2 Test Scenarios**

**Checkout Tests:**
1. New subscription checkout for each plan (Basic, Pro, Premium)
2. Verify correct price IDs are used
3. Verify quantities are always 1
4. Test with both monthly and annual billing

**Upgrade Tests:**
1. Legacy subscription (quantity-based) → New subscription (separate price)
2. New subscription → New subscription upgrades
3. Immediate upgrades (Option A)
4. Scheduled upgrades (Option B)

**Webhook Tests:**
1. New subscription creation events
2. Subscription upgrade events with new price IDs
3. Cancellation events
4. Payment failure scenarios

**6.3 Rollback Plan**

**If Issues Arise:**
1. Revert environment variables to single price ID
2. Revert code changes
3. Existing subscriptions will continue working
4. New subscriptions will use old quantity-based system

### Phase 7: Deployment Plan

**7.1 Pre-Deployment Checklist**

- [ ] Create one product with three prices in Paddle Dashboard
- [ ] Configure all environment variables (sandbox + production)
- [ ] Update all frontend checkout components
- [ ] Update all backend edge functions
- [ ] Update webhook handlers
- [ ] Test thoroughly in sandbox environment
- [ ] Prepare rollback procedures

**7.2 Deployment Sequence**

1. **Deploy Backend Changes:**
   - Update edge functions with new price ID logic
   - Deploy webhook updates
   - Update environment variables in Supabase

2. **Deploy Frontend Changes:**
   - Update subscription store
   - Update pricing components
   - Update environment variables in hosting platform

3. **Verification:**
   - Test new subscription creation
   - Test upgrade flows
   - Monitor webhook processing
   - Verify existing subscriptions continue working

**7.3 Post-Deployment Monitoring**

**Key Metrics:**
- Subscription creation success rate
- Upgrade success rate  
- Webhook processing errors
- Payment failure rates

**Monitoring Duration:** 7 days minimum

## Benefits of Migration

### 1. Simplified Billing Logic
- No more quantity multiplier calculations
- Direct price-to-plan mapping
- Clearer billing statements for customers

### 2. Improved Upgrade Reliability
- Eliminates Paddle proration issues with quantity changes
- Cleaner subscription replacement logic
- Reduced billing calculation errors

### 3. Enhanced Flexibility
- Easier to implement different pricing models
- Support for plan-specific features
- Simplified A/B testing for pricing

### 4. Better Customer Experience
- Clearer subscription management in Paddle portal
- More intuitive billing statements
- Reduced billing confusion

## Potential Risks & Mitigation

### 1. **Risk:** Breaking Existing Subscriptions
**Mitigation:** Maintain backward compatibility with quantity-based logic during transition period

### 2. **Risk:** Environment Variable Misconfiguration  
**Mitigation:** Comprehensive testing checklist and validation logic

### 3. **Risk:** Webhook Processing Errors
**Mitigation:** Gradual rollout with extensive monitoring

### 4. **Risk:** Paddle Product Configuration Errors
**Mitigation:** Detailed sandbox testing before production deployment, ensure all three prices are properly configured for the single product

## Implementation Timeline

**Week 1 (July 6-12):**
- Create one Paddle product with three price tiers and configure price IDs
- Update frontend checkout components
- Initial sandbox testing

**Week 2 (July 13-19):**
- Update backend edge functions
- Update webhook handlers
- Comprehensive testing

**Week 3 (July 20-26):**
- Staging deployment and testing
- Production deployment preparation
- Final testing and validation

**Week 4 (July 27+):**
- Production deployment
- Monitoring and support
- Performance analysis

## Conclusion

This migration will significantly improve Voice Hype's subscription system reliability while maintaining full backward compatibility. The separate price approach (within a single product) aligns with Paddle's best practices and eliminates the complexity of quantity-based billing.

The phased approach ensures minimal risk while providing clear benefits for both the development team and customers. All existing subscriptions will continue working seamlessly during and after the migration.

**Next Steps:**
1. Review and approve migration plan
2. Create one product with three prices in Paddle sandbox
3. Begin frontend component updates
4. Establish comprehensive testing procedures

## Paddle Price ID Reference

### Sandbox Environment
- **Basic ($9/month):** `pri_01jyn32mtqwxarqmshhf9rt222`
- **Pro ($18/month):** `pri_01jzbqn2tahrqvs56dst9lz2ta` 
- **Premium ($27/month):** `pri_01jzbqp7qbswzjkvy4hstgsmh2`

### Production Environment  
- **Basic ($9/month):** `pri_01jyn2xdwvmnacptsbdcn0vbd6`
- **Pro ($18/month):** `pri_01jzbqyzdwqvt3ntr5crry9vmw`
- **Premium ($27/month):** `pri_01jzbqzb2y2jkr64rbekpn2276`

**Note:** All prices are for the same VoiceHype Subscription product, with different pricing tiers.

---

**Document Version:** 1.0  
**Last Updated:** July 6, 2025  
**Author:** Voice Hype Development Team  
**Status:** Planning Complete - Ready for Implementation

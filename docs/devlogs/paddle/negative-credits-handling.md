# DevLog: Negative Credits Auto-Deduction System

**Started:** 2025-07-01  
**Status:** Planning Phase  
**Priority:** Medium  
**Team:** Development Team

---

## 1. Conceptual Plan

### What We Want to Achieve
We want to create a seamless user experience for handling negative credit balances in VoiceHype. Currently, when users slightly overuse their credits (especially with AI optimization where output tokens are unpredictable), their credit balance can go negative by small amounts like $0.01 or $0.50.

Instead of having a separate `negative_credits` table, we want to implement an **automatic deduction system** that:

**For Credit Purchases:**
- Shows users their negative balance upfront during checkout
- Automatically deducts the negative amount from their new credit purchase
- Displays the final amount they'll receive after deduction
- Creates a clean, positive credit entry in the database

**Example User Flow:**
1. User has -$0.01 negative balance from previous overuse
2. User wants to buy $5 in credits
3. System shows: "You're purchasing $5.00. After deducting your negative balance of $0.01, you'll receive $4.99 in credits"
4. User confirms and pays $5.00 to Paddle
5. System automatically creates single credit entry for $4.99
6. Clean slate - no negative balances remain

### Why This Matters
- **User Transparency**: Clear communication about negative balances and deductions
- **Simplified Database**: No need for separate negative credits tracking table
- **Automatic Resolution**: Negative balances resolve themselves naturally
- **Better UX**: Users understand exactly what they're getting
- **Clean Architecture**: Single credits table handles all scenarios

---

## 2. Codebase Review

### Current State Analysis

**Credits Management:**
- ✅ `credits` table stores positive and negative balances
- ✅ `stores/credits.ts` - Pinia store managing credit state
- ✅ Credit purchase flow via Paddle checkout working
- ✅ Webhook processing for credit transactions implemented

**Negative Credits Handling:**
- ❌ Currently creates negative entries in credits table
- ❌ No frontend display of negative balance impact during checkout
- ❌ No automatic deduction logic in webhook processing
- ❌ Users may be confused about negative balances

**Database Schema:**
```sql
-- Current credits table allows negative balances
create table public.credits (
  id uuid not null default gen_random_uuid(),
  user_id uuid not null,
  balance numeric(18, 9) not null, -- Can be negative
  currency text not null default 'USD',
  expires_at timestamp with time zone not null,
  created_at timestamp with time zone null default now(),
  ...
)
```

**Existing Components:**
- ✅ `PaymentsView.vue` - Credit purchase interface
- ✅ Buy Credits Modal with amount validation
- ✅ `paddle-webhook/index.ts` - Credit transaction processing

---

## 3. Technical Plan

### Files to be Created

**Database Functions:**
- `supabase/migrations/add_process_negative_credits_function.sql` - SQL function to handle negative balance deduction
- `supabase/migrations/add_calculate_net_credit_amount_function.sql` - Calculate final credit amount after negative deduction

**Frontend Components:**
- `components/credits/NegativeBalancePreview.vue` - Shows negative balance impact during checkout
- `components/credits/CreditPurchasePreview.vue` - Enhanced preview with deduction calculation

**TypeScript Types:**
- `types/negative-credits.ts` - Type definitions for negative credit handling

### Files to be Modified

**Frontend Updates:**
- `views/PaymentsView.vue` - Add negative balance preview to buy credits modal
- `stores/credits.ts` - Add negative balance calculation methods
- Buy Credits Modal - Enhanced to show deduction preview

**Backend Updates:**
- `supabase/functions/paddle-webhook/index.ts` - Enhanced credit transaction processing with negative balance handling

### Architecture Diagram

```mermaid
graph TB
    A[User clicks Buy Credits] --> B[Calculate current balance]
    B --> C{Has negative balance?}
    
    C -->|No| D[Show standard purchase preview]
    C -->|Yes| E[Show negative balance deduction preview]
    
    E --> F["Display: Purchasing $5.00<br/>Negative balance: -$0.01<br/>You'll receive: $4.99"]
    D --> F
    
    F --> G[User confirms purchase]
    G --> H[Paddle checkout with original amount]
    
    H --> I[Paddle Webhook: transaction.completed]
    I --> J[Call process_negative_credits_function]
    
    J --> K{Has negative balance?}
    K -->|Yes| L[Calculate net amount after deduction]
    K -->|No| M[Use full purchase amount]
    
    L --> N[Create single positive credit entry]
    M --> N
    
    N --> O[Frontend refreshes credit balance]
```

### Implementation Approach

**Phase 1: Database Infrastructure**
1. Create SQL function to process negative credit deduction
2. Create function to calculate net credit amounts
3. Test functions with various negative balance scenarios

**Phase 2: Frontend Preview System**
1. Add negative balance calculation to credits store
2. Create preview components for deduction display
3. Enhance buy credits modal with preview

**Phase 3: Webhook Enhancement**
1. Modify credit transaction processing in webhook
2. Integrate negative balance processing
3. Test end-to-end flow

**Phase 4: Testing & Validation**
1. Test with various negative balance amounts
2. Validate calculations are accurate
3. Ensure database consistency

---

## 4. To-Do List

### Database Infrastructure
- ⏳ Create `process_negative_credits_function.sql`
- ⏳ Create `calculate_net_credit_amount_function.sql`
- ⏳ Test database functions with sample data
- ⏳ Add proper error handling and validation

### Frontend Components
- ⏳ Create `NegativeBalancePreview.vue` component
- ⏳ Create `CreditPurchasePreview.vue` component
- ⏳ Add negative balance calculation to `credits.ts` store
- ⏳ Update Buy Credits Modal with preview functionality

### Credits Store Updates
- ⏳ Add `calculateNegativeBalance()` method
- ⏳ Add `calculateNetCreditAmount()` method
- ⏳ Add computed property for `hasNegativeBalance`
- ⏳ Update credit fetching to include negative balance totals

### Webhook Processing
- ⏳ Enhance `handleCreditTransaction` in webhook
- ⏳ Integrate negative balance processing logic
- ⏳ Add logging for negative balance deductions
- ⏳ Test webhook with negative balance scenarios

### UI/UX Improvements
- ⏳ Add negative balance indicator to credit balance display
- ⏳ Create clear messaging for deduction preview
- ⏳ Add tooltips explaining negative balance handling
- ⏳ Ensure mobile-responsive design

### Testing & Validation
- ⏳ Test with $0.01 negative balance
- ⏳ Test with larger negative balances
- ⏳ Test edge case: negative balance larger than purchase amount
- ⏳ Validate database consistency after deductions
- ⏳ End-to-end testing in Paddle sandbox

### Documentation
- ⏳ Update API documentation for new functions
- ⏳ Create user guide for negative balance handling
- ⏳ Document edge cases and limitations

---

## 5. Progress Notes

### 2025-07-01 - Project Planning
**Conceptual Foundation:** Defined the automatic negative credit deduction system to eliminate the need for a separate `negative_credits` table.

**Key Design Decision:** Instead of tracking negative credits separately, we'll:
1. Show negative balance impact upfront during credit purchases
2. Automatically deduct negative amounts from new credit purchases
3. Create single, clean positive credit entries
4. Maintain transparency with clear user communication

**Architecture Approach:**
- Frontend calculation and preview for user transparency
- Database functions for atomic negative balance processing
- Webhook integration for automatic deduction during credit purchases
- Enhanced user experience with clear deduction explanations

**Current Status:** Planning phase complete. This system will provide a clean, user-friendly way to handle the inevitable small overages that occur with AI token usage, especially in optimization features where output is unpredictable.

---

## 6. Implementation Notes

### Key Technical Considerations

**Database Consistency:**
- Negative balance processing must be atomic to prevent race conditions
- Need to handle concurrent credit purchases gracefully
- Ensure no credits are lost during deduction process

**User Experience:**
- Clear preview of final amount before payment
- Transparent communication about negative balance deduction
- Graceful handling when negative balance exceeds purchase amount
- Mobile-friendly preview components

**Edge Cases to Handle:**
- Negative balance larger than purchase amount (should show $0 result)
- Multiple concurrent credit purchases with negative balance
- Webhook retry scenarios with negative balance processing
- Currency conversion if negative balance is in different currency

**Business Logic:**
- Only apply to credit purchases, not subscription payments
- Ensure deduction only happens once per negative balance
- Maintain audit trail of negative balance resolutions
- Handle refunds appropriately if deduction was applied

---

*This DevLog will be updated as implementation progresses, Inshaa Allah. The goal is to create a seamless, transparent system that automatically resolves negative balances during credit purchases while maintaining clear communication with users.*

# Supabase Edge Function Migration to Requesty

## Date: June 23, 2025

## Conceptual Plan

The goal of this migration is to transition our text optimization edge function from OpenRouter to Requesty. This change will streamline our model selection and improve our service reliability. We'll be removing redundant models and adding new capabilities through Requesty's API.

### Key Changes
- Replace OpenRouter API integration with Requesty
- Remove redundant models: Llama 4 Scout, Llama 4 Maverick, and DeepSeek R1
- Maintain DeepSeek V3 functionality
- Add Claude 4 to our model lineup

### Requesty Model Mapping (as of June 23, 2025)
- llama-3-70b: novita/meta-llama/llama-3.1-70b-instruct
- llama-3-8b: novita/meta-llama/llama-3.1-8b-instruct-max
- claude-3.5-sonnet: anthropic/claude-3-5-sonnet-latest
- claude-3.7-sonnet: anthropic/claude-3-7-sonnet-latest
- claude-4: anthropic/claude-sonnet-4-********
- claude-3.5-haiku: anthropic/claude-3-haiku-********
- deepseek-v3: deepinfra/deepseek-ai/DeepSeek-V3

## Codebase Review

### Current Structure
The text optimization functionality is primarily implemented in two files:

1. `/supabase/functions/optimize/index.ts`
   - Main edge function handler
   - Request validation and error handling
   - Usage tracking and billing logic
   - Model provider routing

2. `/supabase/functions/optimize/optimizeWithOpenRouter.ts`
   - OpenRouter specific implementation
   - Model mappings
   - API request handling
   - Token usage tracking

### Key Components to Modify
1. Model Configuration
   ```typescript
   const SUPPORTED_MODELS = {
     openrouter: [
       'llama-4-scout',        // To be removed
       'llama-4-maverick',     // To be removed
       'llama-3-70b',
       'llama-3-8b',
       'claude-3.5-sonnet',
       'claude-3.7-sonnet',
       'claude-3.5-haiku',
       'deepseek-r1',         // To be removed
       'deepseek-v3'
     ]
   };
   ```

2. Provider Integration
   - Current: OpenRouter API integration
   - Target: Requesty API integration

## Technical Plan

### Files to be Created
1. `/supabase/functions/optimize/optimizeWithRequesty.ts`
   - Requesty API integration
   - Model mappings
   - Request/response handling
   - Token usage tracking

### Files to be Modified
1. `/supabase/functions/optimize/index.ts`
   - Update model configurations
   - Add Requesty provider handling
   - Update error handling for Requesty-specific cases

### Implementation Approach
1. Create new Requesty integration module
2. Update model configurations
3. Modify provider routing
4. Update usage tracking for Requesty's token counting
5. Implement graceful fallback mechanisms
6. Add comprehensive error handling
7. Update tests and documentation

## To-Do List

### Phase 1: Setup and Configuration
✅ Create optimizeWithRequesty.ts
✅ Update SUPPORTED_MODELS configuration
✅ Add Claude 4 Sonnet model mapping
✅ Remove deprecated models

### Phase 2: Implementation
✅ Implement Requesty API integration
✅ Update token counting logic
✅ Modify provider routing logic
✅ Update error handling

### Phase 3: Testing and Validation
⏳ Test individual model functionality
⏳ Validate token counting
⏳ Test error scenarios
⏳ Verify billing integration

### Phase 4: Documentation and Cleanup
⏳ Update API documentation
⏳ Clean up deprecated code
⏳ Document new error codes
⏳ Update usage examples

## Progress Notes

### June 23, 2025
- 🎯 Initial planning completed
- 📋 DevLog created
- 🔍 Identified files requiring modification
- 📊 Listed models to be removed and added
- 🚀 Requesty integration module created
- 🛠️ Model configuration and provider routing updated in index.ts
- 🟢 Migration logic switched to Requesty provider
- ➕ Added Claude 3.7 Sonnet model mapping and updated all Requesty model IDs

Next Steps:
1. Begin testing Requesty integration
2. Validate token and billing logic
3. Update documentation and clean up legacy code

### Updates will be added as implementation progresses...

## Supabase Self-Hosted Edge Function Deployment & Environment Configuration

### Environment File Configuration for Edge Functions

- **Location:**  
  The environment file for Edge Functions is typically named `.env` and should be placed in the `supabase/functions/` directory or wherever your Edge Functions are built/deployed from.

- **Secret Name for API Key:**  
  For your migrated Requesty integration, you must add the following secret to your `.env` file (or the environment configuration for the Edge Functions container):

  ```bash
  REQUESTY_API_KEY=your_requesty_api_key_here
  ```

  Replace `your_requesty_api_key_here` with your actual API key.  
  This variable is accessed in your code via `Deno.env.get('REQUESTY_API_KEY')`.

- **Other Required Secrets:**  
  You will also need the standard Supabase secrets for service role and URL:
  ```bash
  SUPABASE_URL=your_supabase_url
  SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
  ```

### Deployment Steps

1. **Upload/Copy Edge Function Files:**  
   Place your new/updated edge function files (e.g., `index.ts`, `optimizeWithRequesty.ts`) in the appropriate directory, usually `supabase/functions/`.

2. **Update Environment Variables:**  
   Ensure your `.env` file (or Docker secrets) contains the correct `REQUESTY_API_KEY` and other required variables.

3. **Restart Edge Functions Service:**  
   On Docker, you can restart the edge functions service with:
   ```bash
   docker-compose restart edge-functions
   ```
   or, if using the default service name:
   ```bash
   docker-compose restart rest
   ```
   (Check your `docker-compose.yml` for the exact service name.)

4. **(Optional) Rebuild if Needed:**  
   If your setup requires a build step, run it before restarting.

### Confirmation

- Yes, your understanding is correct:  
  - Upload the new edge function files  
  - Add/update the `REQUESTY_API_KEY` in your environment configuration  
  - Restart the edge functions service in Docker

If you need the exact Docker service name or further details on the `.env` file structure, see your `docker-compose.yml` or reach out to your system administrator.

---

# DevLog: Handle Negative Credit Balances in Credit Purchases

**Date:** June 30, 2025  
**Status:** Complete  
**Priority:** Medium  

## Issue Description

The `process_completed_transaction` function successfully creates new credit entries when users purchase credits, but it doesn't account for existing negative credit balances. This could lead to users having more credits than they should when they purchase new credits while having outstanding negative balances from previous usage.

## Current Problem

When a user purchases credits, the system:
1. ✅ Creates a new credit entry with the purchased amount
2. ❌ Ignores any existing negative credit balances
3. ❌ Allows users to effectively "reset" their negative balance by purchasing credits

**Example Issue:**
- User has -$0.50 in credits (negative balance from usage)
- User purchases $10.00 in credits
- Current system: User ends up with $9.50 total credits BUT in separate entries (-$0.50 + $10.00)
- Expected system: User should have exactly $9.50 in a single positive entry, with negative entries removed

## Solution Implemented

### Enhanced Credit Purchase Logic

The updated `process_completed_transaction` function now:

1. **Calculates Total Current Balance**: Sums all active credit entries for the user
2. **Detects Negative Balances**: Identifies if the total is negative
3. **Adjusts New Credit Amount**: Subtracts negative balance from new purchase
4. **Cleans Up Negative Entries**: Marks negative credit entries as "consumed_by_purchase"
5. **Records Transaction Details**: Stores adjustment information in transaction metadata

### Implementation Details

**Migration File:** `20250630_001_handle_negative_credit_balances.sql`

**Key Logic:**
```sql
-- Calculate total current balance
SELECT COALESCE(SUM(balance), 0) INTO v_total_current_balance
FROM public.credits
WHERE user_id = v_user_id 
AND (status IS NULL OR status = 'active')
AND (expires_at IS NULL OR expires_at > NOW());

-- If negative balance exists, adjust the new credit amount
IF v_total_current_balance < 0 THEN
    v_negative_balance := ABS(v_total_current_balance);
    v_adjusted_credit_amount := v_credit_amount - v_negative_balance;
    
    -- Mark negative entries as consumed
    UPDATE public.credits 
    SET status = 'consumed_by_purchase'
    WHERE user_id = v_user_id AND balance < 0;
END IF;
```

## Test Scenarios

### Scenario 1: User with Negative Balance
- **Initial State**: User has -$0.50 credits
- **Action**: User purchases $10.00 credits
- **Expected Result**: User has exactly $9.50 credits in one entry
- **Database Changes**: 
  - Negative entry marked as `consumed_by_purchase`
  - New entry created with $9.50
  - Transaction metadata records the adjustment

### Scenario 2: User with Positive Balance
- **Initial State**: User has $2.00 credits
- **Action**: User purchases $10.00 credits
- **Expected Result**: User has $12.00 total credits
- **Database Changes**: 
  - Existing $2.00 entry remains unchanged
  - New $10.00 entry created
  - No adjustment needed

### Scenario 3: Large Negative Balance
- **Initial State**: User has -$15.00 credits
- **Action**: User purchases $10.00 credits
- **Expected Result**: User has $0.00 credits (purchase fully offset)
- **Database Changes**:
  - Negative entries marked as `consumed_by_purchase`
  - No new credit entry created (adjusted amount = $0)
  - Transaction records full $10.00 purchase but $0 net credits

### Scenario 4: Multiple Credit Entries
- **Initial State**: User has multiple entries: $5.00, -$2.00, -$1.50 (total: $1.50)
- **Action**: User purchases $10.00 credits
- **Expected Result**: User has $11.50 total credits
- **Database Changes**:
  - Negative entries marked as `consumed_by_purchase`
  - Positive entry remains
  - New entry created with $10.00

## Security & Data Integrity

### Transaction Recording
- **Original Amount**: Always recorded in `paddle.transactions.credit_amount`
- **Adjustment Details**: Stored in transaction metadata for audit trail
- **Net Effect**: Reflected in the actual credit entries created

### Audit Trail
The transaction metadata now includes:
```json
{
  "negative_balance_adjustment": {
    "original_amount": 10.00,
    "negative_balance_offset": 0.50,
    "adjusted_amount": 9.50,
    "total_balance_before": -0.50
  }
}
```

### Status Tracking
- **Active Credits**: `status = 'active'` or `status IS NULL`
- **Consumed Credits**: `status = 'consumed_by_purchase'`
- **Expired Credits**: `expires_at < NOW()`

## Benefits

1. **Accurate Credit Balance**: Users cannot bypass negative balances through purchases
2. **Clean Database**: Negative entries are properly marked and handled
3. **Audit Compliance**: Full transaction trail maintained
4. **User Experience**: Simplified credit balance (no confusing negative entries)
5. **Business Logic**: Prevents credit "washing" through purchases

## Migration Safety

- **Backward Compatible**: Existing functionality preserved
- **Non-Destructive**: Negative entries marked, not deleted
- **Comprehensive Logging**: All adjustments logged with NOTICE level
- **Error Handling**: Graceful handling of edge cases

## Success Criteria

- [x] Function handles negative credit balances correctly
- [x] Transaction records maintain full audit trail
- [x] Credit entries are properly cleaned up
- [x] No existing functionality is broken
- [x] Edge cases (zero balance, large negative) handled properly
- [x] Comprehensive logging for debugging and monitoring

## Notes

- The function preserves the original transaction amount for financial records
- Credit adjustments are logged at NOTICE level for monitoring
- Status field is used to track credit lifecycle without data loss
- Expiration dates are still respected in balance calculations

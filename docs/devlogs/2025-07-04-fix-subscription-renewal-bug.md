# Fix Subscription Renewal Bug - DevLog

**Date**: July 4, 2025  
**Issue**: Subscription renewal not resetting quotas properly  
**Priority**: High - affects core subscription functionality

## Problem Statement

When users' subscriptions renew (billing cycle completes), the webhook receives `subscription.updated` event but fails to:
1. Reset user quotas to zero usage
2. Update subscription records properly  
3. Distinguish between renewal vs upgrade scenarios

## Root Cause Analysis

### 1. Transaction Type Constraint Error
- Webhook tries to log `'subscription_upgrade'` in `paddle_transactions` table
- Database constraint only allows: `['credit_purchase', 'payg_invoice', 'refund', 'subscription']`
- Missing `'subscription_upgrade'` in allowed types

### 2. Faulty Renewal Detection Logic
Current logic in `handleSubscriptionUpdated()`:
```typescript
// Current problematic logic
if (data.custom_data?.subscription_plan) {
  // Treats ALL subscription_plan presence as "upgrade"
  // Even when it's the same plan (renewal scenario)
}
```

### 3. Missing Renewal-Specific Handling
- No dedicated logic for quota resets during renewals
- Renewal events incorrectly trigger upgrade workflows
- Payment recovery logic exists but renewal logic missing

## Expected Behavior vs Current Behavior

| Scenario | Expected | Current | Issue |
|----------|----------|---------|-------|
| Subscription Renewal | Reset quotas, update periods | Treats as upgrade | Wrong logic path |
| Plan Upgrade | Upgrade logic, merge quotas | Works correctly | ✅ |
| Payment Recovery | Reset quotas, reactivate | Works correctly | ✅ |

## Solution Plan

### Phase 1: Fix Database Constraint ✅
- Add `'subscription_upgrade'` to allowed transaction types
- Create migration to update constraint

### Phase 2: Improve Renewal Detection Logic
- Distinguish renewal from upgrade in webhook logic
- Add proper renewal detection based on:
  - Same plan but new billing period
  - No upgrade metadata present
  - Status remains active

### Phase 3: Implement Dedicated Renewal Handling
- Create renewal-specific quota reset logic
- Ensure proper subscription record updates
- Maintain existing upgrade functionality

## Implementation Log

### Step 1: Database Migration
- [x] Create migration for transaction type constraint
- [x] Test migration locally
- [ ] Deploy to staging
- [ ] Deploy to production

### Step 2: Webhook Logic Updates
- [ ] Update renewal detection in `handleSubscriptionUpdated`
- [ ] Add dedicated renewal handler
- [ ] Fix transaction logging logic

### Step 3: Testing
- [ ] Test with Paddle simulation events
- [ ] Verify quota resets work correctly
- [ ] Ensure upgrades still work
- [ ] Test payment recovery scenarios

## Files Modified
1. `supabase/migrations/` - New constraint migration
2. `supabase/functions/paddle-webhook/index.ts` - Webhook logic
3. Database function updates if needed

## Notes
- Ensure backward compatibility with existing upgrade functionality
- Test thoroughly with Paddle sandbox before production deployment
- Document the fix for future reference

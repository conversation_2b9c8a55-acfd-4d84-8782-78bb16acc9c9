# DevLog: Fix Transcription Credits Calculation

**Date:** June 30, 2025  
**Status:** In Progress  
**Priority:** High  

## Issue Description

The `check_usage_allowance` PostgreSQL function has incorrect logic for calculating available transcription minutes when using credits. The function fails to properly:

1. Calculate how many minutes users can afford based on their credit balance
2. Apply the 20-minute cap for real-time transcription models
3. Distinguish between normal and real-time transcription pricing

## Current Problems

### 1. Missing Real-time Transcription Detection
- Function doesn't identify real-time models (those ending with `-realtime`)
- No 20-minute session cap applied for real-time transcription

### 2. Incorrect Minute Calculation Logic
- Credits functionality exists but doesn't properly calculate affordable minutes
- The `estimated_minutes` calculation happens but lacks proper validation
- Missing distinction between normal and real-time transcription limits

### 3. Service Cost Calculation Issues
- Current `service_cost` calculation is incorrect for transcription services
- Function uses `p_amount * cost_per_unit` but `p_amount` should represent minutes for transcription

## Required Changes

### 1. Database Migration ✅
**File:** `fix_transcription_credits_calculation.sql`

- Add real-time transcription detection logic
- Implement proper minute calculation with 20-minute cap for real-time
- Fix service cost calculation for transcription services
- Return accurate available minutes in `max_output_tokens` field

### 2. Edge Function Updates ⏳
**File:** `supabase/functions/transcribe/index.ts`

**Required Changes:**
- **Implement server-side audio duration calculation** using WAV header parsing to prevent client-side manipulation
- Update usage allowance check to handle minute calculations properly
- Ensure the function correctly interprets the `max_output_tokens` response for transcription
- Add validation for real-time transcription session limits
- Update error handling for insufficient minutes vs insufficient credits

**Audio Duration Calculation Formula** (found in `/extension/src/services/TranscriptionService.ts`):
```typescript
// WAV header parsing for accurate duration calculation
const numChannels = buffer.readUInt16LE(22);        // Number of channels
const sampleRate = buffer.readUInt32LE(24);         // Sample rate
const bitsPerSample = buffer.readUInt16LE(34);      // Bits per sample
const bytesPerSample = bitsPerSample / 8;           // Convert bits to bytes
const dataSize = fileSize - 44;                     // Audio data size (minus WAV header)

// Duration formula: Duration = dataSize / (sampleRate * numChannels * bytesPerSample)
const duration = dataSize / (sampleRate * numChannels * bytesPerSample);
```

**Specific Areas to Update:**
```typescript
// STEP 1: Add server-side audio duration calculation function
function calculateAudioDuration(audioBuffer: ArrayBuffer): number {
  const buffer = new Uint8Array(audioBuffer);
  
  // Parse WAV header - first 44 bytes
  const numChannels = buffer[22] | (buffer[23] << 8);         // Offset 22-23: Number of channels
  const sampleRate = buffer[24] | (buffer[25] << 8) |         // Offset 24-27: Sample rate
                    (buffer[26] << 16) | (buffer[27] << 24);
  const bitsPerSample = buffer[34] | (buffer[35] << 8);       // Offset 34-35: Bits per sample
  
  const bytesPerSample = bitsPerSample / 8;
  const dataSize = audioBuffer.byteLength - 44; // Audio data size minus WAV header
  
  // Duration = dataSize / (sampleRate * numChannels * bytesPerSample)
  return dataSize / (sampleRate * numChannels * bytesPerSample);
}

// STEP 2: Update usage allowance check - Line ~489
const { data: pricingCheck, error: usagePricingError } = await supabase
  .rpc('check_usage_allowance', {
    p_user_id: userId,
    p_service: 'transcription',
    p_model: service === 'assemblyai' ? `assembly-ai/${model}` : model,
    p_amount: 1, // Use 1 for pre-check to get available minutes
    p_api_key_id: apiKeyId
  });

// STEP 3: Calculate actual audio duration server-side and validate
const actualAudioDurationMinutes = calculateAudioDuration(audioBuffer) / 60;
const availableMinutes = pricingCheck?.max_output_tokens || 0;

if (actualAudioDurationMinutes > availableMinutes) {
  throw new Error(`Insufficient credits: need ${actualAudioDurationMinutes.toFixed(2)} minutes, have ${availableMinutes.toFixed(2)} minutes available`);
}
```

### 3. Real-time Server Updates ⏳
**Files:** 
- `realtime-server/server.js`
- `realtime-server/server.ts` 
- `realtime-server/server-new.ts`

**Required Changes:**
- Update usage allowance check to properly handle minute calculations
- Implement proper session duration limits based on available credits
- Add real-time session validation against the 20-minute cap
- Update session timeout logic to respect credit-based limits

**Specific Areas to Update:**
```javascript
// Line ~296-308: Usage allowance check in handleTranscriptionSession
const usageCheck = await checkUsageAllowance(userId, apiKeyId, modelForDb);

// Need to add:
// 1. Extract available minutes from usageCheck.max_output_tokens
// 2. Compare with 20-minute hardcoded limit
// 3. Set dynamic session timeout based on available credits
// 4. Update maxDurationMs calculation
```

### 4. Model Naming Consistency ⏳
**Issue:** Inconsistent model naming between edge function and real-time server

**Edge Function uses:** `assembly-ai/${model}` (e.g., `assembly-ai/best`)  
**Real-time Server uses:** `assemblyai/${model}-realtime` (e.g., `assemblyai/best-realtime`)

**Required:** Standardize model naming across all services

## Implementation Plan

### Phase 1: Database Migration ✅
1. Create and deploy the updated `check_usage_allowance` function
2. Test with various credit balances and transcription models
3. Verify real-time transcription detection and 20-minute cap

### Phase 2: Edge Function Updates
1. **Implement server-side audio duration calculation** using WAV header parsing
2. Update usage allowance validation logic to use calculated duration
3. Add proper minute calculation and validation against available credits
4. Implement error handling for insufficient minutes vs insufficient credits
5. Test with both normal and real-time transcription requests

### Phase 3: Real-time Server Updates
1. Update all three server files with consistent logic
2. Implement dynamic session duration based on available credits
3. Add proper validation for real-time transcription limits
4. Update session management to respect credit constraints

### Phase 4: Testing & Validation
1. Test credit deduction for various transcription durations
2. Validate 20-minute cap for real-time transcription
3. Test error scenarios (insufficient credits, unpaid balances)
4. Verify consistent behavior across edge function and real-time server

## Test Scenarios

### Credit Calculation Tests
- User with $1.00 credits, model cost $0.01/minute → Should allow 100 minutes
- User with $0.05 credits, real-time model cost $0.0092/minute → Should allow 5.43 minutes (not capped)
- User with $1.00 credits, real-time model → Should cap at 20 minutes despite having more credits

### Real-time Session Tests
- Session duration should be min(available_credit_minutes, 20_minutes)
- Session should terminate when credit limit is reached
- Proper error messages for insufficient credits

### Edge Cases
- Zero credit balance → Should fall through to PAYG
- Negative credit balance → Should fall through to PAYG
- Very small credit amounts → Should calculate fractional minutes correctly

## Success Criteria

- [ ] Users can see exactly how many minutes they can afford with their current credits
- [ ] Real-time transcription properly caps at 20 minutes maximum
- [ ] Credit deduction accurately reflects actual transcription duration
- [ ] **Server-side audio duration calculation prevents client-side manipulation**
- [ ] Consistent behavior between edge function and real-time server
- [ ] Proper error messages for various insufficient credit scenarios
- [ ] No regression in existing subscription and PAYG functionality

## Audio Duration Calculation Requirement

**Critical Security Requirement:** The edge function must implement server-side audio duration calculation to prevent client-side manipulation and ensure accurate billing.

**Current Risk:** If the edge function relies on client-provided duration, users could potentially:
- Manipulate the duration value to pay less than actual usage
- Send long audio files but claim short duration
- Bypass credit limits by misreporting file length

**Solution:** Implement the same WAV header parsing logic used in the extension:
```typescript
// Extract audio parameters from WAV header
const numChannels = buffer.readUInt16LE(22);
const sampleRate = buffer.readUInt32LE(24);
const bitsPerSample = buffer.readUInt16LE(34);
const bytesPerSample = bitsPerSample / 8;
const dataSize = fileSize - 44; // Minus WAV header

// Calculate accurate duration
const duration = dataSize / (sampleRate * numChannels * bytesPerSample);
```

**Implementation Location:** `/extension/src/services/TranscriptionService.ts` lines 660-680 contains the reference implementation.

## Notes

- The real-time server hardcodes 20 minutes: `const maxDurationMs = 20 * 60 * 1000;`
- Pricing data shows real-time costs more: $0.0092/minute vs $0.007276667/minute for normal
- Current function correctly identifies token-based vs minute-based services
- Model detection logic needs to handle both `-realtime` and `/realtime` suffixes
- **Audio duration calculation must be done server-side for security and accurate billing**

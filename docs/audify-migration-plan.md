# Audify JS Migration Plan - Dev Log

> Started: June 1, 2025

## Overview

This dev log details the plan to migrate the VoiceHype audio system from Node Microphone (Linux) and SOX utility (Windows/Mac) to Audify JS. The migration aims to streamline our operations and resolve licensing concerns.

## Current Architecture

- **Linux**: Uses Node Microphone package
- **Windows/Mac**: Uses SOX utility (GPL license - problematic for distribution)
- **Issue**: SOX's GPL license presents complications for pre-bundled binaries

## Migration Goals

- 🔲 Create comprehensive migration plan
- 🔲 Replace both Node Microphone and SOX with Audify JS
- 🔲 Implement device selection through multiple interfaces:
  - 🔲 WebView UI
  - 🔲 Quick settings menu (bottom toolbar)
  - 🔲 VS Code extension settings
- 🔲 Ensure consistent behavior across all platforms
- 🔲 Fix Linux default audio device issue

## Advantages of Audify JS

- **License**: MIT licensed (permissive, business-friendly)
- **Consistency**: Single API across all platforms
- **Performance**: Direct access to native audio APIs via RtAudio
- **Features**: Better device enumeration and selection
- **Maintenance**: Simpler codebase with fewer dependencies

## Technical Details

Audify JS is based on RtAudio, which has the following license:

```
RtAudio: a set of realtime audio i/o C++ classes
Copyright (c) 2001-2023 Gary P. Scavone

Permission is hereby granted, free of charge, to any person
obtaining a copy of this software and associated documentation files
(the "Software"), to deal in the Software without restriction,
including without limitation the rights to use, copy, modify, merge,
publish, distribute, sublicense, and/or sell copies of the Software,
and to permit persons to whom the Software is furnished to do so,
subject to the following conditions:

The above copyright notice and this permission notice shall be
included in all copies or substantial portions of the Software.

Any person wishing to distribute modifications to the Software is
asked to send the modifications to the original developer so that
they can be incorporated into the canonical version.  This is,
however, not a binding provision of this license.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR
ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF
CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
```

## Implementation Plan

### Phase 1: Core Implementation 🔲

- 🔲 Remove old dependencies (node-microphone, sox-static) (June 1, 2025: Removed from package.json)
- 🔲 Implement new Audify-based Microphone class (June 1, 2025: Created audifyMicrophone.ts)
- 🔲 Update package.json with new dependencies (June 1, 2025: Installed Audify package)
- 🔲 Create new type definitions for Audify (June 1, 2025: Created audify.d.ts)
- 🔲 Basic integration testing (June 1, 2025: Created test-audify-microphone.ts)

### Phase 2: Audio Pipeline Updates 🔲

- 🔲 Create compatibility layer for transitioning to the new implementation (June 1, 2025)
- 🔲 Prepare updated microphone.ts file (June 1, 2025)
- 🔲 Update PCM data handling for Audify compatibility (June 1, 2025: Audify implementation includes PCM handling)
- 🔲 Modify WAV creation process if needed (June 1, 2025: Audify implementation includes WAV creation)
- 🔲 Ensure pause/resume functionality works (June 1, 2025: Implemented in AudifyMicrophone)
- 🔲 Update RecordingService to use the new implementation (June 1, 2025: Using microphoneCompat)
- 🔲 Update RealtimeConnectionManager for audio chunk handling (June 1, 2025: No changes needed)

### Phase 3: UI Integration 🔲

- 🔲 Update VS Code extension settings schema (June 1, 2025)
- 🔲 Implement device enumeration function (June 1, 2025: Created AudioDeviceManager)
- 🔲 Add audio device selection command (June 1, 2025)
- 🔲 Add device selection to WebView UI (June 1, 2025: Updated AudioSettings component with dropdown)
- 🔲 Implement device selection in quick settings menu (June 1, 2025: Updated CommandService)
- 🔲 Update user messages and documentation

### Phase 4: Testing and Refinement ⏳

- 🔲 Compile extension successfully (June 1, 2025: Extension and WebView UI compile without errors)
- 🔲 Test on Linux (June 1, 2025: In progress)
- 🔲 Test on Windows
- 🔲 Test on macOS
- 🔲 Performance testing and optimization
- 🔲 Handle edge cases and error conditions
- 🔲 Update error messages for better troubleshooting

### Phase 5: Documentation and Release ⏱️

- 🔲 Update CHANGELOG.md
- 🔲 Update README with new features
- 🔲 Create release notes
- 🔲 Final testing
- 🔲 Release new version

## Files To Modify

1. **Core Implementation**
   - 🔲 `/extension/src/utils/microphone.ts` - Complete rewrite
   - 🔲 `/extension/src/utils/soxPath.ts` - Replace or remove
   - 🔲 Create new Audify utility file if needed

2. **Type Definitions**
   - 🔲 Remove `/extension/src/types/node-microphone.d.ts`
   - 🔲 Create Audify type definitions if needed

3. **Service Layer**
   - 🔲 `/extension/src/services/RecordingService.ts` - Update to use new implementation
   - 🔲 `/extension/src/services/RealtimeConnectionManager.ts` - Update audio chunk handling

4. **Configuration**
   - 🔲 `/extension/package.json` - Update dependencies and settings schema

5. **UI Layer**
   - 🔲 WebView UI components for device selection
   - 🔲 Quick settings menu integration

## Detailed Task List

### Dependencies Management

- 🔲 Remove from package.json:
  ```json
  "node-microphone": "...",
  "sox-static": "..."
  ```
- 🔲 Add to package.json:
  ```json
  "audify": "^1.8.0"
  ```
- 🔲 Run npm install to update dependencies

### Microphone Class Implementation

- 🔲 Create new Audify-based implementation
- 🔲 Implement startRecording() function
- 🔲 Implement stopRecording() function
- 🔲 Implement pauseRecording() function
- 🔲 Implement resumeRecording() function
- 🔲 Implement getDevices() function
- 🔲 Ensure consistent stream handling
- 🔲 Maintain backward compatible API

### VS Code Settings

- 🔲 Update settings schema in package.json:
  ```json
  "voicehype.audio.device": {
    "type": "string",
    "default": "",
    "description": "Audio input device to use for recording. Leave empty to use system default."
  }
  ```
- 🔲 Add logic to populate device list in extension

### UI Integration

- 🔲 Create device selection dropdown component
- 🔲 Add to WebView UI
- 🔲 Add to quick settings menu
- 🔲 Implement settings sync between UI and VS Code settings

### Testing Scenarios

- 🔲 Test recording on Linux
- 🔲 Test recording on Windows
- 🔲 Test recording on macOS
- 🔲 Test with different audio devices
- 🔲 Test pause/resume functionality
- 🔲 Test different sample rates
- 🔲 Test error handling (no mic, permission denied, etc.)

## Notes & Progress Updates

- (June 1, 2025) Created initial migration plan
- ...

## Known Issues

- On Linux, default audio device doesn't work - need to specify device ID
- ...

## Questions & Decisions

- Should we maintain backward compatibility with the current API or redesign?
  - Decision: Maintain backward compatibility to minimize codebase changes
- How should we handle errors from Audify?
  - TBD
- ...

## References

- [Audify Documentation](https://github.com/almogh52/audify)
- [RtAudio Documentation](https://www.music.mcgill.ca/~gary/rtaudio/)
- Example implementation: `/home/<USER>/Documents/cursor_extensions/voicehype/examples/audify-recorder.js`

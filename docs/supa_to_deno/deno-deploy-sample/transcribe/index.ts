// Deno Deploy implementation of the transcribe function
import { serve } from "https://deno.land/std@0.220.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.0';

// Types for transcription services
type TranscriptionService = 'openai' | 'assemblyai';
type OpenAIModel = 'whisper-1' | 'gpt-4o-mini-transcribe' | 'gpt-4o-transcribe';
type AssemblyAIModel = 'default' | 'best' | 'premium';

// Result types
interface AssemblyAIResult {
  text: string;
}

// Error codes
enum ErrorCode {
  UNAUTHORIZED = 'unauthorized',
  INVALID_API_KEY = 'invalid_api_key',
  INSUFFICIENT_CREDITS = 'insufficient_credits',
  UNPAID_BALANCE = 'unpaid_balance',
  QUOTA_EXCEEDED = 'quota_exceeded',
  INVALID_REQUEST = 'invalid_request',
  SERVICE_ERROR = 'service_error',
  UNSUPPORTED_MODEL = 'unsupported_model',
  RATE_LIMITED = 'rate_limited',
  UNSUPPORTED_LANGUAGE = 'unsupported_language'
}

// Helper function to create a standardized error response
function createErrorResponse(
  status: number, 
  message: string, 
  code: ErrorCode,
  details?: any
): Response {
  return new Response(
    JSON.stringify({
      error: {
        code,
        message,
        details
      }
    }),
    {
      status,
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
}

// Helper function to create a standardized success response
function createSuccessResponse(data: any): Response {
  return new Response(
    JSON.stringify({ data }),
    {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
}

// Helper function to parse request body
async function parseRequestBody(req: Request): Promise<any> {
  const contentType = req.headers.get('content-type') || '';
  
  if (contentType.includes('application/json')) {
    return await req.json();
  } else if (contentType.includes('application/x-www-form-urlencoded')) {
    const formData = await req.formData();
    const data: Record<string, any> = {};
    for (const [key, value] of formData.entries()) {
      data[key] = value;
    }
    return data;
  } else {
    // Default to JSON parsing
    try {
      return await req.json();
    } catch (e) {
      return {};
    }
  }
}

// Helper function to convert base64 to Uint8Array
function base64ToUint8Array(base64String: string): Uint8Array {
  // Remove data URL prefix if present
  const base64Data = base64String.replace(/^data:audio\/\w+;base64,/, '');
  
  // Convert base64 to binary string
  const binaryString = atob(base64Data);
  
  // Create Uint8Array from binary string
  const bytes = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  
  return bytes;
}

// Helper function to validate audio data
function validateAudioData(audioData: Uint8Array): void {
  if (!audioData || audioData.length === 0) {
    throw new Error('Invalid audio data');
  }
  
  if (audioData.length > 25 * 1024 * 1024) { // 25MB limit
    throw new Error('Audio file too large (max 25MB)');
  }
}

// Function to transcribe audio with LemonFox (formerly OpenAI)
async function transcribeWithOpenAI(
  audioData: Uint8Array,
  model: OpenAIModel = 'whisper-1',
  language?: string,
  audioDurationSeconds?: number
): Promise<AssemblyAIResult> {
  console.log('Preparing LemonFox transcription request:', {
    audioDataSize: audioData.length,
    model,
    language,
    audioDurationSeconds
  });

  // Get API key from environment
  const LEMONFOX_API_KEY = Deno.env.get('LEMONFOX_AI_API_KEY') || '';
  if (!LEMONFOX_API_KEY) {
    throw new Error('Missing LemonFox API key');
  }

  const formData = new FormData();
  const blob = new Blob([audioData], { type: 'audio/wav' });
  formData.append('file', blob, 'audio.wav');
  formData.append('model', model);
  
  // Add language if specified and not 'auto'
  if (language && language !== 'auto' && language !== '') {
    formData.append('language', language);
  }
  
  formData.append('task', 'transcribe');
  formData.append('response_format', 'json');

  // Use LemonFox API
  const requestConfig = {
    url: 'https://api.lemonfox.ai/v1/audio/transcriptions',
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${LEMONFOX_API_KEY}`
    }
  };

  console.log('LemonFox request configuration:', {
    ...requestConfig,
    headers: {
      ...requestConfig.headers,
      'Authorization': `Bearer ${LEMONFOX_API_KEY.substring(0, 10)}...`
    },
    formDataEntries: {
      file: 'audio.wav',
      model,
      ...(language && language !== 'auto' && language !== '' ? { language } : { language: 'auto (omitted)' }),
      task: 'transcribe',
      response_format: 'json'
    }
  });

  const response = await fetch(requestConfig.url, {
    method: requestConfig.method,
    headers: requestConfig.headers,
    body: formData
  });

  // Get response headers for logging
  const responseHeaders: Record<string, string> = {};
  response.headers.forEach((value, key) => {
    responseHeaders[key] = value;
  });

  // Get response text
  let responseText: string;
  try {
    responseText = await response.text();
  } catch (e: any) {
    responseText = 'Failed to read response text: ' + (e?.message || String(e));
  }

  if (!response.ok) {
    const error = {
      request: {
        ...requestConfig,
        headers: {
          ...requestConfig.headers,
          'Authorization': 'Bearer [REDACTED]'
        },
        formDataEntries: {
          file: 'audio.wav',
          model,
          ...(language && language !== 'auto' && language !== '' ? { language } : { language: 'auto (omitted)' }),
          task: 'transcribe',
          response_format: 'json'
        },
        audioDataSize: audioData.length
      },
      response: {
        status: response.status,
        statusText: response.statusText,
        headers: responseHeaders,
        body: responseText
      }
    };

    console.error('LemonFox transcription failed:', JSON.stringify(error, null, 2));
    throw new Error(`LemonFox transcription failed: Status ${response.status} - ${response.statusText}. Response: ${responseText}`);
  }

  // Parse the response
  let transcribeJson;
  try {
    transcribeJson = JSON.parse(responseText);
  } catch (e: any) {
    console.error('Failed to parse transcription response as JSON:', {
      error: e?.message || String(e),
      responseText
    });
    throw new Error(`Failed to parse LemonFox transcription response: ${e?.message || String(e)}. Raw response: ${responseText}`);
  }

  // Extract the transcription text
  const { text } = transcribeJson;
  if (!text) {
    console.error('Missing text in response:', transcribeJson);
    throw new Error('LemonFox transcription response missing text field');
  }

  return { text };
}

// Main handler function
async function handleRequest(req: Request): Promise<Response> {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, Audio-Duration-Seconds',
        'Access-Control-Max-Age': '86400'
      }
    });
  }

  // Handle WebSocket upgrade for real-time transcription
  if (req.headers.get('upgrade') === 'websocket') {
    try {
      // This would be implemented in a separate file and imported
      // For now, return an error response
      return createErrorResponse(
        400,
        'WebSocket support is not implemented in this sample',
        ErrorCode.INVALID_REQUEST
      );
    } catch (error: any) {
      console.error('WebSocket upgrade error:', error);
      return createErrorResponse(
        500,
        `WebSocket upgrade failed: ${error.message}`,
        ErrorCode.SERVICE_ERROR
      );
    }
  }

  // Only allow POST requests for transcription
  if (req.method !== 'POST') {
    return createErrorResponse(
      405,
      'Method not allowed. Use POST for transcription or WebSocket for real-time.',
      ErrorCode.INVALID_REQUEST
    );
  }

  try {
    // Parse request body
    const body = await parseRequestBody(req);

    // Destructure and validate request parameters
    const {
      audioUrl,
      service = 'openai' as TranscriptionService,
      model = service === 'openai' ? 'whisper-1' as OpenAIModel : 'best' as AssemblyAIModel,
      language = 'en',
      translate = false
    } = body;

    // Validate request parameters
    if (!audioUrl) {
      return createErrorResponse(400, 'Missing audioUrl', ErrorCode.INVALID_REQUEST);
    }

    // If translate is true but service is not OpenAI, ignore translate parameter
    const shouldTranslate = translate && service === 'openai';
    if (translate && service !== 'openai') {
      console.log('Ignoring translate parameter for non-OpenAI service:', service);
    }

    // Extract and validate audio data
    const audioData = base64ToUint8Array(audioUrl);
    validateAudioData(audioData);

    // Get audio file size and check for client-provided duration header
    const audioSize = audioData.length;

    // Get the client-provided audio duration from the header - REQUIRED
    const audioDurationHeader = req.headers.get('Audio-Duration-Seconds') || req.headers.get('audio-duration-seconds');

    if (!audioDurationHeader || isNaN(Number(audioDurationHeader))) {
      // Reject requests without a valid duration header
      console.error('Missing or invalid Audio-Duration-Seconds header:', {
        header: audioDurationHeader
      });
      return createErrorResponse(400, 'Missing or invalid Audio-Duration-Seconds header', ErrorCode.INVALID_REQUEST);
    }

    const audioDurationSeconds = Number(audioDurationHeader);

    // Log the request details
    console.log('Transcription request:', {
      service,
      model,
      language,
      translate: shouldTranslate,
      audioSize,
      audioDurationSeconds
    });

    // Perform transcription based on selected service
    let transcription: string;
    try {
      if (shouldTranslate) {
        // Translation would be implemented in a separate function
        return createErrorResponse(
          400,
          'Translation is not implemented in this sample',
          ErrorCode.INVALID_REQUEST
        );
      } else if (service === 'openai') {
        // For LemonFox (formerly OpenAI), only pass language if it's not 'auto' or empty
        if (language === 'auto' || language === '') {
          console.log('Using auto language detection for LemonFox (omitting language parameter)');
          const { text } = await transcribeWithOpenAI(audioData, model as OpenAIModel, undefined, audioDurationSeconds);
          transcription = text;
        } else {
          console.log(`Using specified language for LemonFox: ${language}`);
          const { text } = await transcribeWithOpenAI(audioData, model as OpenAIModel, language, audioDurationSeconds);
          transcription = text;
        }
      } else if (service === 'assemblyai') {
        // AssemblyAI would be implemented in a separate function
        return createErrorResponse(
          400,
          'AssemblyAI transcription is not implemented in this sample',
          ErrorCode.INVALID_REQUEST
        );
      } else {
        return createErrorResponse(
          400,
          `Unsupported transcription service: ${service}`,
          ErrorCode.UNSUPPORTED_MODEL
        );
      }
    } catch (error: any) {
      console.error(`Transcription error with ${service}:`, error);
      return createErrorResponse(
        500,
        `Transcription failed: ${error.message}`,
        ErrorCode.SERVICE_ERROR,
        { service, model }
      );
    }

    // Return the transcription result
    return createSuccessResponse({
      transcription,
      metadata: {
        service,
        model,
        audioSize,
        audioDurationSeconds,
        language: language || 'auto'
      }
    });

  } catch (error: any) {
    console.error('Unhandled error:', error);
    return createErrorResponse(
      500,
      `Unhandled error: ${error.message}`,
      ErrorCode.SERVICE_ERROR
    );
  }
}

// Start the HTTP server
console.log('Starting transcription service...');
serve(handleRequest);

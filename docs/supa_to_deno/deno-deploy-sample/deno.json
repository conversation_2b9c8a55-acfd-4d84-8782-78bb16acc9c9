{"tasks": {"start:transcribe": "deno run --allow-net --allow-env transcribe/index.ts", "start:optimize": "deno run --allow-net --allow-env optimize/index.ts", "deploy:transcribe": "deployctl deploy --project=voicehype-transcribe --prod transcribe/index.ts", "deploy:optimize": "deployctl deploy --project=voicehype-optimize --prod optimize/index.ts", "test": "deno test --allow-net --allow-env --allow-read tests/"}, "compilerOptions": {"allowJs": true, "lib": ["deno.window"], "strict": true}, "importMap": "./import_map.json"}
# Deno Deploy Implementation Guide

This guide provides detailed instructions for migrating VoiceHype's Supabase Edge Functions to Deno Deploy.

## Prerequisites

1. **Deno Runtime**: Install the latest version of Deno
   ```bash
   curl -fsSL https://deno.land/install.sh | sh
   ```

2. **deployctl**: Install the Deno Deploy CLI tool
   ```bash
   deno install -A jsr:@deno/deployctl --global
   ```

3. **Deno Deploy Account**: Create an account at https://dash.deno.com/

## Project Setup

### 1. Create Project Structure

Create the following directory structure:

```
deno-deploy/
├── _shared/
│   ├── utils.ts
│   └── lemonfox.ts
├── transcribe/
│   ├── index.ts
│   └── realtime.ts
├── optimize/
│   ├── index.ts
│   └── optimizeWithOpenRouter.ts
├── deno.json
└── import_map.json
```

### 2. Configure Project Files

#### deno.json

```json
{
  "tasks": {
    "deploy:transcribe": "deployctl deploy --project=voicehype-transcribe --prod transcribe/index.ts",
    "deploy:optimize": "deployctl deploy --project=voicehype-optimize --prod optimize/index.ts",
    "dev:transcribe": "deno run --allow-net --allow-env transcribe/index.ts",
    "dev:optimize": "deno run --allow-net --allow-env optimize/index.ts"
  },
  "compilerOptions": {
    "allowJs": true,
    "lib": ["deno.window"],
    "strict": true
  },
  "importMap": "./import_map.json"
}
```

#### import_map.json

```json
{
  "imports": {
    "http/": "https://deno.land/std@0.220.0/http/",
    "@supabase/supabase-js": "https://esm.sh/@supabase/supabase-js@2.38.0"
  }
}
```

## Code Migration

### 1. Shared Utilities

#### _shared/utils.ts

Copy the existing utils.ts file from Supabase Edge Functions and update the imports:

```typescript
// Update import paths
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.0';
import { Status } from "https://deno.land/std@0.220.0/http/http_status.ts";

// Rest of the file remains the same
```

#### _shared/lemonfox.ts

Copy the existing lemonfox.ts file from Supabase Edge Functions (no changes needed).

### 2. Transcribe Function

#### transcribe/index.ts

Copy the existing transcribe function and update the imports:

```typescript
import { serve } from "https://deno.land/std@0.220.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.0';
import {
  createErrorResponse,
  createSuccessResponse,
  parseRequestBody,
  base64ToUint8Array,
  validateAudioData,
  ErrorCode
} from '../_shared/utils.ts';

// Import the handler from realtime.ts
import { handleRealTimeRequest } from "./realtime.ts";

// Rest of the file remains the same
```

#### transcribe/realtime.ts

Copy the existing realtime.ts file and update the imports:

```typescript
import { serve } from "https://deno.land/std@0.220.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.0';
import {
    createErrorResponse,
    createSuccessResponse,
    ErrorCode
} from '../_shared/utils.ts';

// Rest of the file remains the same
```

### 3. Optimize Function

#### optimize/index.ts

Copy the existing optimize function and update the imports:

```typescript
import { serve } from "https://deno.land/std@0.220.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.0';
import {
  createErrorResponse,
  createSuccessResponse,
  parseRequestBody,
  ErrorCode,
  TokenUsage,
  createTokenUsage
} from '../_shared/utils.ts';

// Rest of the file remains the same
```

#### optimize/optimizeWithOpenRouter.ts

Copy the existing optimizeWithOpenRouter.ts file (no changes needed).

## Environment Variables

### 1. Local Development

Create a `.env` file for local development:

```
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_key
LEMONFOX_AI_API_KEY=your_lemonfox_key
ASSEMBLYAI_API_KEY=your_assemblyai_key
OPENROUTER_API_KEY=your_openrouter_key
```

### 2. Deno Deploy Configuration

Set up environment variables in the Deno Deploy dashboard:

1. Go to your project in the Deno Deploy dashboard
2. Click on "Settings" > "Environment Variables"
3. Add all the required environment variables:
   - `SUPABASE_URL`
   - `SUPABASE_SERVICE_ROLE_KEY`
   - `LEMONFOX_AI_API_KEY`
   - `ASSEMBLYAI_API_KEY`
   - `OPENROUTER_API_KEY`
   - Any other service-specific API keys

## Deployment

### 1. Create Projects in Deno Deploy

1. Go to the Deno Deploy dashboard
2. Create a new project for each function:
   - `voicehype-transcribe`
   - `voicehype-optimize`

### 2. Deploy Functions

Use the deployctl CLI to deploy your functions:

```bash
# Deploy transcribe function
deno task deploy:transcribe

# Deploy optimize function
deno task deploy:optimize
```

Alternatively, deploy directly:

```bash
deployctl deploy --project=voicehype-transcribe --prod transcribe/index.ts
deployctl deploy --project=voicehype-optimize --prod optimize/index.ts
```

### 3. Configure Custom Domains (Optional)

If you want to use custom domains:

1. Go to your project in the Deno Deploy dashboard
2. Click on "Settings" > "Domains"
3. Add your custom domain and follow the DNS configuration instructions

## Testing

### 1. Local Testing

Test your functions locally before deployment:

```bash
# Test transcribe function
deno task dev:transcribe

# Test optimize function
deno task dev:optimize
```

### 2. API Testing

Create a test script to verify the deployed functions:

```typescript
// test_transcribe.ts
import { readFileSync } from "https://deno.land/std@0.220.0/node/fs.ts";

// Function to convert file to base64
function fileToBase64(filePath: string): string {
  const fileData = readFileSync(filePath);
  return btoa(String.fromCharCode(...new Uint8Array(fileData)));
}

async function testTranscription(
  filePath: string,
  service: string = 'openai',
  model: string = 'whisper-1',
  language: string = 'auto'
) {
  try {
    const fileBase64 = fileToBase64(filePath);
    
    // Use the Deno Deploy URL
    const apiUrl = `https://voicehype-transcribe.deno.dev/`;
    
    const requestBody = {
      audio: fileBase64,
      service,
      model,
      language: language || 'auto'
    };
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer YOUR_API_KEY`
      },
      body: JSON.stringify(requestBody),
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API Error (${response.status}): ${errorText}`);
      return;
    }
    
    const data = await response.json();
    console.log('Transcription result:', data);
    return data;
    
  } catch (error) {
    console.error('Error during transcription test:', error);
  }
}

// Run the test
testTranscription('./test_audio.mp3', 'openai', 'whisper-1');
```

Run the test:

```bash
deno run --allow-net --allow-read test_transcribe.ts
```

## WebSocket Testing

For testing WebSocket functionality:

```typescript
// test_realtime.ts
async function testRealtimeTranscription() {
  try {
    // Connect to the WebSocket endpoint
    const ws = new WebSocket('wss://voicehype-transcribe.deno.dev/');
    
    ws.onopen = () => {
      console.log('WebSocket connection established');
      
      // Send configuration message
      ws.send(JSON.stringify({
        type: 'config',
        service: 'assemblyai',
        model: 'default',
        apiKey: 'YOUR_API_KEY'
      }));
      
      // Simulate sending audio chunks
      // In a real scenario, you would capture audio and send chunks
      setTimeout(() => {
        const audioChunk = new Uint8Array([/* audio data */]);
        ws.send(audioChunk);
      }, 1000);
    };
    
    ws.onmessage = (event) => {
      console.log('Received message:', event.data);
    };
    
    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
    
    ws.onclose = () => {
      console.log('WebSocket connection closed');
    };
    
  } catch (error) {
    console.error('Error during realtime test:', error);
  }
}

testRealtimeTranscription();
```

## Client Application Updates

After successful deployment and testing, update your client applications to use the new Deno Deploy endpoints:

```typescript
// Old Supabase endpoint
const transcribeUrl = `${SUPABASE_URL}/functions/v1/transcribe`;

// New Deno Deploy endpoint
const transcribeUrl = 'https://voicehype-transcribe.deno.dev/';
```

## Monitoring and Logging

### 1. View Logs in Deno Deploy

1. Go to your project in the Deno Deploy dashboard
2. Click on "Logs" to view the function logs

### 2. Add Custom Logging

Enhance your functions with structured logging:

```typescript
function logInfo(message: string, data?: any) {
  console.log(JSON.stringify({
    level: 'info',
    timestamp: new Date().toISOString(),
    message,
    ...(data && { data })
  }));
}

function logError(message: string, error: any) {
  console.error(JSON.stringify({
    level: 'error',
    timestamp: new Date().toISOString(),
    message,
    error: error.message || String(error),
    stack: error.stack
  }));
}
```

## Troubleshooting

### Common Issues and Solutions

1. **WebSocket Connection Issues**
   - Ensure WebSocket upgrade is handled correctly
   - Check CORS configuration

2. **Environment Variable Access**
   - Verify all environment variables are set in Deno Deploy
   - Check for typos in variable names

3. **Database Connection Issues**
   - Ensure Supabase client is initialized correctly
   - Check network access from Deno Deploy to Supabase

4. **Deployment Failures**
   - Check for syntax errors in your code
   - Verify import paths are correct

## Conclusion

By following this implementation guide, you should be able to successfully migrate your Supabase Edge Functions to Deno Deploy. The migration process is relatively straightforward since both platforms use Deno as the runtime.

Remember to thoroughly test all functionality before switching production traffic to the new endpoints.

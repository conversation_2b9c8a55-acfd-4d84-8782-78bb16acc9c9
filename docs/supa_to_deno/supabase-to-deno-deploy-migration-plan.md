# Migration Plan: Supabase Edge Functions to Deno Deploy

## Overview

This document outlines the plan to migrate VoiceHype's edge functions from Supabase to Deno Deploy. Since Supabase Edge Functions are already built on Deno, the migration should be relatively straightforward with minimal code changes required.

## Current Architecture

### Supabase Edge Functions

Currently, VoiceHype uses the following Supabase Edge Functions:

1. **Transcribe Function** (`/transcribe`)
   - Handles audio transcription using LemonFox (formerly OpenAI) and AssemblyAI
   - Includes real-time transcription via WebSockets
   - Supports translation functionality

2. **Optimize Function** (`/optimize`)
   - Handles text optimization using OpenRouter
   - Supports multiple models with fallback options

3. **Shared Utilities** (`/_shared`)
   - Common utilities used across functions
   - API response formatting
   - Authentication and authorization
   - Usage tracking and billing integration

### Dependencies

- Supabase client for database operations
- Deno standard library (HTTP server, etc.)
- External API integrations (LemonFox, AssemblyAI, OpenRouter)

## Migration Strategy

### Phase 1: Setup and Configuration

1. **Install Required Tools**
   - Install Deno runtime if not already installed
   - Install `deployctl` CLI tool: `deno install -A jsr:@deno/deployctl --global`

2. **Create Deno Deploy Account and Project**
   - Sign up for Deno Deploy (if not already done)
   - Create a new project for VoiceHype functions

3. **Set Up Environment Variables**
   - Transfer all environment variables from Supabase to Deno Deploy
   - Required variables include:
     - `SUPABASE_URL`
     - `SUPABASE_SERVICE_ROLE_KEY`
     - `LEMONFOX_AI_API_KEY`
     - `ASSEMBLYAI_API_KEY`
     - `OPENROUTER_API_KEY`
     - Other service-specific API keys

### Phase 2: Code Migration

1. **Create Project Structure**
   - Create a new directory structure for Deno Deploy functions
   - Organize functions similar to the current structure

2. **Migrate Shared Utilities**
   - Copy and adapt the `_shared` folder utilities
   - Update imports to use Deno's standard library directly
   - Ensure compatibility with Deno Deploy

3. **Migrate Transcribe Function**
   - Copy the transcribe function code
   - Update imports and dependencies
   - Ensure WebSocket functionality works with Deno Deploy
   - Test with sample audio files

4. **Migrate Optimize Function**
   - Copy the optimize function code
   - Update imports and dependencies
   - Test with sample text input

5. **Update Database Connections**
   - Ensure Supabase client connections work properly
   - Test database operations (usage tracking, etc.)

### Phase 3: Testing and Deployment

1. **Local Testing**
   - Test each function locally using Deno
   - Verify functionality matches current implementation
   - Test with sample inputs and edge cases

2. **Deploy to Deno Deploy**
   - Use `deployctl` to deploy functions to Deno Deploy
   - Set up environment variables in Deno Deploy dashboard
   - Verify deployments are successful

3. **Integration Testing**
   - Test the deployed functions with real client applications
   - Verify WebSocket connections for real-time transcription
   - Test database operations and usage tracking

4. **Performance Testing**
   - Compare performance between Supabase and Deno Deploy
   - Optimize if necessary

### Phase 4: Cutover and Monitoring

1. **Update Client Applications**
   - Update client applications to use new Deno Deploy endpoints
   - Implement gradual rollout if possible

2. **Monitor Performance and Errors**
   - Set up monitoring for the new functions
   - Watch for errors or performance issues

3. **Rollback Plan**
   - Keep Supabase functions active during initial deployment
   - Be prepared to revert to Supabase if issues arise

## Implementation Details

### Directory Structure

```
deno-deploy/
├── _shared/
│   ├── utils.ts
│   └── lemonfox.ts
├── transcribe/
│   ├── index.ts
│   └── realtime.ts
├── optimize/
│   ├── index.ts
│   └── optimizeWithOpenRouter.ts
├── deno.json
└── import_map.json
```

### Code Changes Required

1. **Import Statements**
   - Update import paths to use Deno's standard library directly
   - Example:
     ```typescript
     // Old
     import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
     
     // New (using latest version)
     import { serve } from "https://deno.land/std@0.220.0/http/server.ts";
     ```

2. **WebSocket Handling**
   - Update WebSocket handling for real-time transcription
   - Ensure compatibility with Deno Deploy's WebSocket implementation

3. **Environment Variables**
   - Update environment variable access to use Deno.env
   - Ensure all required variables are set in Deno Deploy

4. **Database Operations**
   - Ensure Supabase client works correctly in Deno Deploy
   - Test all database operations

### Deployment Commands

To deploy the functions to Deno Deploy:

```bash
# Deploy transcribe function
deployctl deploy --project=voicehype-transcribe --prod transcribe/index.ts

# Deploy optimize function
deployctl deploy --project=voicehype-optimize --prod optimize/index.ts
```

## Testing Plan

1. **Unit Tests**
   - Create unit tests for each function
   - Test with sample inputs

2. **Integration Tests**
   - Test end-to-end functionality
   - Verify database operations

3. **Performance Tests**
   - Compare response times between Supabase and Deno Deploy
   - Test with various load scenarios

## Timeline

1. **Phase 1 (Setup and Configuration)**: 1 day
2. **Phase 2 (Code Migration)**: 2-3 days
3. **Phase 3 (Testing and Deployment)**: 2 days
4. **Phase 4 (Cutover and Monitoring)**: 1-2 days

Total estimated time: 6-8 days

## Risks and Mitigations

| Risk | Mitigation |
|------|------------|
| WebSocket compatibility issues | Test WebSocket functionality thoroughly before deployment |
| Performance differences | Conduct performance testing and optimize if necessary |
| Database connection issues | Test database operations extensively |
| Environment variable configuration | Double-check all environment variables are properly set |
| API rate limits | Ensure rate limiting is properly implemented |

## Conclusion

Migrating from Supabase Edge Functions to Deno Deploy should be relatively straightforward since both platforms use Deno as the runtime. The main challenges will be ensuring WebSocket functionality works correctly and that database operations continue to function as expected.

By following this migration plan, we can ensure a smooth transition with minimal disruption to users.

# Supabase to Deno Deploy Migration

Bismillahir Rahmanir Raheem

This folder contains documentation and sample code for migrating VoiceHype's edge functions from Supabase to Deno Deploy.

## Contents

### Documentation

1. **[Migration Plan](supabase-to-deno-deploy-migration-plan.md)** - A comprehensive plan outlining the strategy for migrating from Supabase Edge Functions to Deno Deploy.

2. **[Implementation Guide](deno-deploy-implementation-guide.md)** - Detailed step-by-step instructions for implementing the migration.

3. **[Platform Comparison](supabase-vs-deno-deploy-comparison.md)** - A comparison of Supabase Edge Functions and Deno Deploy, highlighting the differences and similarities.

### Sample Code

The `deno-deploy-sample` folder contains sample implementations of the edge functions for Deno Deploy:

- **deno.json** - Configuration file for the Deno project
- **import_map.json** - Import map for managing dependencies
- **transcribe/index.ts** - Sample implementation of the transcribe function

## Migration Overview

The migration involves moving our edge functions from Supabase to Deno Deploy. Since Supabase Edge Functions are already built on Deno, the migration is relatively straightforward with minimal code changes required.

Key steps in the migration process:

1. Set up Deno Deploy account and projects
2. Transfer environment variables
3. Migrate code with necessary adjustments
4. Test thoroughly
5. Deploy to Deno Deploy
6. Update client applications to use new endpoints

## Benefits of Migration

- Global distribution across multiple regions
- Better performance and scaling
- More flexible deployment options
- Direct control over the runtime environment
- Latest Deno features and updates

## Getting Started

To get started with the migration, first read the [Migration Plan](supabase-to-deno-deploy-migration-plan.md) to understand the overall strategy, then follow the detailed steps in the [Implementation Guide](deno-deploy-implementation-guide.md).

# Error Handling Improvement Plan for VoiceHype Optimization Mode

> <PERSON><PERSON><PERSON><PERSON>
>
> Started: June 4, 2025

## Overview

The current implementation has two operational modes:
1. Basic Mode (Ctrl+Shift+8): Only performs audio transcription
2. Optimization Mode: Performs transcription and text enhancement through LLM

While error handling is well-implemented in Basic Mode, Optimization Mode requires improvements in error handling and user feedback.

## Requirements

In Optimization Mode (Transcription + Optimization or T+O):
- If transcription fails, display two buttons:
  * "Retry Transcription and Optimization"
  * "Retry Only Transcription"

- If transcription succeeds but optimization fails, display two buttons:
  * "Retry Optimization"
  * "Paste Transcript"

All buttons should appear in information snackbars in VS Code.

## Technical Implementation Plan

### 1. Code Structure Analysis
- System uses `ApiFailureHandler` for retries and circuit breaking
- Transcription and optimization handled separately in `transcribeAndOptimize` function
- Transcription service layer (`TranscriptionService.ts`) coordinates the process
- API calls made through `supabaseClient.ts`

### 2. Required Changes

#### A. TranscriptionService.ts
1. Add new error handling class:
```typescript
interface TranscriptionError extends Error {
    phase: 'transcription' | 'optimization';
    originalError: Error;
}
```

2. Modify transcribeAudio method:
```typescript
async transcribeAudio(
    filePath: string,
    optimize: boolean = false,
    ...
): Promise<{...}> {
    try {
        // Step 1: Transcription
        const transcriptionResult = await this.performTranscription(...);
        
        if (optimize) {
            try {
                // Step 2: Optimization
                const optimizedResult = await this.performOptimization(...);
                return { ...optimizedResult };
            } catch (optimizeError) {
                // Handle optimization failure specifically
                this.emitStatus(TranscriptionStatus.OptimizationFailed);
                throw new TranscriptionError('optimization', optimizeError);
            }
        }
        return transcriptionResult;
    } catch (transcribeError) {
        if (transcribeError instanceof TranscriptionError) {
            throw transcribeError;
        }
        // Handle transcription failure
        this.emitStatus(TranscriptionStatus.Error);
        throw new TranscriptionError('transcription', transcribeError);
    }
}
```

#### B. ApiFailureHandler.ts
Add new failure handling logic:
```typescript
export class ApiFailureHandler {
    private handleOptimizationFailure(error: Error): void {
        // Specific handling for optimization failures
        this.recordFailure();
        // Show optimization-specific retry options
    }
}
```

#### C. supabaseClient.ts
Modify transcribeAndOptimize function:
```typescript
export async function transcribeAndOptimize(
    audioUrl: string,
    ...
): Promise<{ transcription: string; optimizedText: string; duration: number }> {
    // Step 1: Transcription
    const transcriptionResult = await transcribeAudio(...);
    
    if (!transcriptionResult.transcription) {
        throw new Error('Transcription failed');
    }
    
    // Step 2: Optimization (only if transcription succeeded)
    try {
        const optimizedText = await optimizeWithMessages(...);
        return {
            transcription: transcriptionResult.transcription,
            optimizedText,
            duration: transcriptionResult.duration
        };
    } catch (error) {
        // Return partial success with original transcription
        return {
            transcription: transcriptionResult.transcription,
            optimizedText: transcriptionResult.transcription, // Use original
            duration: transcriptionResult.duration
        };
    }
}
```

#### D. Add New Status Events
```typescript
export enum TranscriptionStatus {
    // ... existing statuses ...
    OptimizationFailed = 'optimization_failed',
    TranscriptionFailed = 'transcription_failed',
    PartialSuccess = 'partial_success'
}
```

### 3. Implementation Strategy

#### Phase 1: Core Implementation
✅ Implement TranscriptionError class
✅ Update error handling logic in ApiFailureHandler
✅ Modify transcribeAndOptimize for proper separation of concerns

#### Phase 2: UI Updates
✅ Implement UI components for retry options
  - Added informative snackbar messages with retry buttons
  - Implemented clear error messages with context
  - Integrated progress indicators during retries
✅ Add event emitters for new status types
  - Added status listeners in ApiFailureHandler
  - Connected status events between ApiFailureHandler and TranscriptionService
  - Improved status message clarity and user feedback
✅ Update VS Code notification system for new buttons
  - Added modal error messages with retry options
  - Implemented smart retry flow based on error phase
  - Added progress indicators in status bar

#### Phase 3: Testing & Refinement
❌ Test error handling flow
❌ Implement retry logic for both phases
❌ Add telemetry for error tracking

### 4. Error Flows

#### A. Transcription Failure
1. Error occurs during transcription
2. TranscriptionError thrown with phase='transcription'
3. ApiFailureHandler catches error
4. Show snackbar with:
   - "Retry Transcription and Optimization"
   - "Retry Only Transcription"

#### B. Optimization Failure
1. Transcription succeeds
2. Error occurs during optimization
3. TranscriptionError thrown with phase='optimization'
4. ApiFailureHandler catches error
5. Show snackbar with:
   - "Retry Optimization"
   - "Paste Transcript"

## Progress Tracking

### Completed
✅ Initial technical plan and documentation
✅ Analysis of current codebase
✅ Identification of required changes

### In Progress
✅ Initial implementation of TranscriptionError class
✅ Updates to ApiFailureHandler

### TODO
✅ All items from Implementation Strategy Phase 1
✅ All items from Implementation Strategy Phase 2
❌ All items from Implementation Strategy Phase 3

## Notes
- June 4, 2025: Completed Phase 1 implementation
  - Enhanced transcribeAndOptimize with proper error separation
  - Added validation checks for each phase
  - Implemented clean error wrapping with TranscriptionError
  - Prevented optimization from running on transcription failure

- June 5, 2025: Completed Phase 2 implementation
  - Integrated new TranscriptionStatus events for better error tracking
  - Added comprehensive retry UI with context-aware buttons
  - Improved user feedback with status bar progress indicators
  - Enhanced error message clarity with getErrorDetails method
  - Connected error handling between ApiFailureHandler and TranscriptionService
  - Enhanced transcribeAndOptimize with proper error separation
  - Added validation checks for each phase
  - Implemented clean error wrapping with TranscriptionError
  - Prevented optimization from running on transcription failure

## Notes
- Add any implementation notes, challenges, or decisions here as development progresses
- Document any deviations from the original plan and their rationale
- Track any bugs or issues discovered during implementation

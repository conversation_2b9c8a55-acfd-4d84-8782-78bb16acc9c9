# Self-Hosted Supabase Welcome Email Solution
## Custom Database Trigger Implementation

**Date:** July 11, 2025  
**Project:** VoiceHype Self-Hosted Email Integration  
**Status:** Implementation Ready

---

## Problem Analysis

### Issue Identified
- **Self-hosted Supabase** lacks Auth Hooks feature (available in Cloud version)
- **Database Webhooks** showing "null REST v1" error (common self-hosted issue)
- **Need custom solution** using database triggers + HTTP requests

### Root Cause
Self-hosted Supabase instances have limited webhook capabilities compared to the cloud version. The webhooks interface relies on cloud-specific REST endpoints that aren't available in self-hosted deployments.

---

## Solution Architecture

### 🏗️ Custom Trigger-Based Email System

```
User Signup → auth.users table → Database Trigger → HTTP Request → Email Edge Function
```

### Key Components
1. **Database Trigger** on `auth.users` table
2. **pg_net Extension** for async HTTP requests
3. **Supabase Vault** for secure API key storage
4. **Edge Function** (your existing email service)

---

## Implementation Steps

### Step 1: Verify and Enable Required Extensions

First, check if the necessary extensions are available:

```sql
-- Check available extensions
SELECT * FROM pg_available_extensions WHERE name IN ('pg_net', 'http');

-- Enable pg_net (preferred for async requests)
CREATE EXTENSION IF NOT EXISTS pg_net;

-- Alternative: Enable http extension (synchronous)
CREATE EXTENSION IF NOT EXISTS http;
```

### Step 2: Store Email Service URL Securely

Using Supabase Vault to store your email service URL:

```sql
-- Insert your email service URL into vault
INSERT INTO vault.secrets (id, name, secret)
VALUES (
  'email-service-url',
  'email_service_url',
  'https://your-digital-ocean-domain.com/functions/v1/email-service'
);
```

### Step 3: Create Email Trigger Function

```sql
-- Create function to send welcome email
CREATE OR REPLACE FUNCTION send_welcome_email()
RETURNS TRIGGER AS $$
DECLARE
  email_service_url TEXT;
  email_payload JSONB;
  request_id BIGINT;
BEGIN
  -- Only process INSERT operations (new user signups)
  IF TG_OP = 'INSERT' THEN
    
    -- Get email service URL from vault
    SELECT decrypted_secret INTO email_service_url
    FROM vault.decrypted_secrets
    WHERE name = 'email_service_url';
    
    -- Prepare email payload
    email_payload := jsonb_build_object(
      'user', jsonb_build_object(
        'id', NEW.id,
        'email', NEW.email,
        'created_at', NEW.created_at,
        'email_confirmed_at', NEW.email_confirmed_at
      ),
      'email_data', jsonb_build_object(
        'email_action_type', 'signup',
        'token', NEW.email_confirmation_token,
        'token_hash', NEW.confirmation_token,
        'site_url', 'https://voicehype.ai'
      )
    );
    
    -- Send async HTTP request using pg_net
    SELECT net.http_post(
      url := email_service_url,
      body := email_payload::TEXT,
      headers := jsonb_build_object(
        'Content-Type', 'application/json',
        'User-Agent', 'Supabase-Database-Trigger'
      )
    ) INTO request_id;
    
    -- Log the request for debugging
    RAISE NOTICE 'Welcome email triggered for user: %, request_id: %', NEW.email, request_id;
    
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### Step 4: Create the Trigger

```sql
-- Create trigger on auth.users table
CREATE OR REPLACE TRIGGER trigger_send_welcome_email
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION send_welcome_email();
```

### Step 5: Alternative Implementation (Using HTTP Extension)

If pg_net is not available, use the synchronous http extension:

```sql
-- Alternative function using http extension
CREATE OR REPLACE FUNCTION send_welcome_email_http()
RETURNS TRIGGER AS $$
DECLARE
  email_service_url TEXT;
  email_payload JSONB;
  response HTTP_RESPONSE;
BEGIN
  -- Only process INSERT operations
  IF TG_OP = 'INSERT' THEN
    
    -- Get email service URL from vault
    SELECT decrypted_secret INTO email_service_url
    FROM vault.decrypted_secrets
    WHERE name = 'email_service_url';
    
    -- Prepare email payload
    email_payload := jsonb_build_object(
      'user', jsonb_build_object(
        'id', NEW.id,
        'email', NEW.email,
        'created_at', NEW.created_at,
        'email_confirmed_at', NEW.email_confirmed_at
      ),
      'email_data', jsonb_build_object(
        'email_action_type', 'signup',
        'token', NEW.email_confirmation_token,
        'token_hash', NEW.confirmation_token,
        'site_url', 'https://voicehype.ai'
      )
    );
    
    -- Send HTTP request
    SELECT * INTO response FROM http_post(
      email_service_url,
      email_payload::TEXT,
      'application/json'
    );
    
    -- Log response
    RAISE NOTICE 'Welcome email sent for user: %, status: %', NEW.email, response.status;
    
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

---

## Testing Your Implementation

### Step 1: Test the Trigger Function Directly

```sql
-- Test the function with sample data
DO $$
DECLARE
  test_user_id UUID := gen_random_uuid();
  test_email TEXT := '<EMAIL>';
BEGIN
  -- Insert test user to trigger the function
  INSERT INTO auth.users (
    id, 
    email, 
    email_confirmed_at,
    email_confirmation_token,
    confirmation_token,
    created_at,
    updated_at
  ) VALUES (
    test_user_id,
    test_email,
    NULL,
    'test_token_123',
    'test_hash_456',
    NOW(),
    NOW()
  );
  
  RAISE NOTICE 'Test user created with email: %', test_email;
END;
$$;
```

### Step 2: Monitor Network Requests

```sql
-- Check pg_net request status (if using pg_net)
SELECT * FROM net.http_request_queue 
ORDER BY created_at DESC 
LIMIT 10;
```

### Step 3: Verify Email Edge Function Logs

Check your edge function logs to ensure the HTTP requests are being received and processed correctly.

---

## Edge Function Modifications

### Update Your Edge Function for Database Triggers

Your existing edge function should work with minimal modifications. However, you may want to add specific handling for database trigger requests:

```typescript
// Add this to your edge function's POST handler
console.log("📍 Request Source:", req.headers.get("User-Agent"));

// Check if request is from database trigger
const isFromDatabaseTrigger = req.headers.get("User-Agent")?.includes("Supabase-Database-Trigger");

if (isFromDatabaseTrigger) {
  console.log("🔄 Processing database trigger request");
  // Add any specific logic for database trigger requests
}
```

---

## Security Considerations

### 🔐 Database Security
- **Use SECURITY DEFINER**: Functions run with elevated privileges
- **Vault Storage**: Store URLs and secrets securely
- **Error Handling**: Prevent trigger failures from blocking signups

### 🛡️ Network Security
- **HTTPS Only**: Always use HTTPS for webhook URLs
- **Rate Limiting**: Consider implementing rate limiting in your edge function
- **Request Validation**: Validate incoming requests in your edge function

---

## Troubleshooting Common Issues

### Issue 1: "null REST v1" Error
**Cause**: Self-hosted Supabase trying to use cloud-specific webhook endpoints  
**Solution**: Use custom database triggers instead of webhooks interface

### Issue 2: pg_net Extension Not Available
**Cause**: Extension not installed in self-hosted instance  
**Solution**: Use `http` extension or install pg_net manually

### Issue 3: Trigger Not Firing
**Cause**: Wrong table name or trigger conditions  
**Solution**: Verify trigger is on `auth.users` table with correct conditions

### Issue 4: HTTP Requests Failing
**Cause**: Network connectivity or URL issues  
**Solution**: Check vault secrets and network connectivity

---

## Monitoring and Logging

### Database Logging
```sql
-- Enable logging for trigger execution
SET log_statement = 'all';
SET log_min_messages = 'notice';
```

### Edge Function Monitoring
Your existing comprehensive logging will help track:
- Request reception from database triggers
- Email processing success/failure
- Response times and errors

---

## Deployment Checklist

### Pre-Deployment
- [ ] Verify pg_net or http extension is enabled
- [ ] Store email service URL in vault
- [ ] Test trigger function in staging environment
- [ ] Verify network connectivity from database to edge function

### Deployment
- [ ] Create trigger function in production database
- [ ] Create trigger on auth.users table
- [ ] Test with a new user signup
- [ ] Monitor logs for successful email delivery

### Post-Deployment
- [ ] Monitor trigger execution logs
- [ ] Verify welcome emails are being sent
- [ ] Check edge function logs for database trigger requests
- [ ] Set up alerts for failed email deliveries

---

## Alternative Solutions (If Primary Fails)

### Option 1: Client-Side Trigger
- Trigger email from your frontend after successful signup
- Less reliable but simpler implementation

### Option 2: Scheduled Job
- Use pg_cron to check for new users periodically
- Send welcome emails to users who haven't received them

### Option 3: External Service
- Use a service like Zapier or n8n to monitor database changes
- Trigger emails through external webhook

---

## Performance Considerations

### Async vs Sync
- **pg_net**: Async requests, won't block user signup
- **http**: Sync requests, could slow down signup process

### Error Handling
```sql
-- Add error handling to prevent signup blocking
BEGIN
  -- Email sending logic here
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Failed to send welcome email: %', SQLERRM;
    -- Don't re-raise the exception to avoid blocking signup
END;
```

---

## Success Metrics

### Key Indicators
- **Trigger Execution**: Monitor trigger firing on user signups
- **Email Delivery**: Track successful email sends
- **Error Rate**: Monitor and alert on failures
- **Performance**: Ensure triggers don't slow down signups

---

## Next Steps

1. **Today**: Enable pg_net extension and store email service URL in vault
2. **Tomorrow**: Create and test trigger function in staging
3. **This Week**: Deploy to production and monitor
4. **Next Week**: Fine-tune error handling and monitoring

---

## Conclusion

This custom trigger-based solution provides a robust alternative to Auth Hooks for self-hosted Supabase. The implementation leverages your existing email infrastructure while providing reliable, server-side triggered welcome emails.

**Key Benefits:**
- ✅ Works with self-hosted Supabase
- ✅ Leverages existing email edge function
- ✅ Secure vault-based configuration
- ✅ Comprehensive logging and monitoring
- ✅ Async processing (with pg_net)

**Estimated Implementation Time:** 2-3 hours
**Risk Level:** Low to Medium (depends on extension availability)

---

*May Allah bless this implementation and make it beneficial for your users. Ameen.*
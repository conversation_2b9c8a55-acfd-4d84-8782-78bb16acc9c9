# Audify.js Recording Implementation Analysis

## Overview

This document explains the flow of audio recording in VoiceHype using Audify.js and outlines the improvements made to address recording issues.

## Audio Recording Flow with Audify

```
┌─────────────────────┐
│  RecordingService   │
└──────────┬──────────┘
           │
           │ 1. startRecording()
           ▼
┌─────────────────────┐
│     Microphone      │
│  (microphone.ts)    │
└──────────┬──────────┘
           │
           │ 2. startRecording()
           ▼
┌─────────────────────┐
│  MicrophoneCompat   │
│                     │
└──────────┬──────────┘
           │
           │ 3. startRecording()
           ▼
┌─────────────────────┐
│  AudifyMicrophone   │
│ (audifyMicrophone.ts)│
└──────────┬──────────┘
           │
           │ 4. Creates RtAudio instance
           │    Gets available devices
           │    Selects input device (now hardcoded to 1)
           │
           ▼
┌─────────────────────┐
│ rtAudio.openStream()│
└──────────┬──────────┘
           │
           │ 5. Opens audio stream with callback
           ▼
┌─────────────────────┐
│    rtAudio.start()  │
└──────────┬──────────┘
           │
           │ 6. Audio callback runs and sends
           │    data to PassThrough stream
           ▼
┌─────────────────────┐
│   PassThrough stream │
└──────────┬──────────┘
           │
           │ 7. Data is piped to writeStream
           │    and/or realtime transcription
           ▼
┌─────────────────────┐
│  Recording Complete  │
└─────────────────────┘
```

## Implemented Improvements

### 1. Frame Size Adjustment

Changed the frame size from a dynamic calculation to a fixed value of 1920 samples, matching the example implementation:

```typescript
private frameSize: number = 1920; // Using the exact frame size from the example
```

### 2. Simplified Device Selection

Replaced the complex device selection logic with a hardcoded value of 1 (typically PulseAudio on Linux):

```typescript
// SIMPLIFIED: Use device ID 1 as requested (typically PulseAudio on Linux)
const deviceId = 1;
console.log(`AudifyMicrophone: Using hardcoded device ID: ${deviceId}`);
```

### 3. Enhanced Data Logging

Added detailed logging of audio callbacks and data statistics:

```typescript
this.callbackCount++;
const buffer = Buffer.from(pcmData);
this.totalDataSize += buffer.length;

// Log every 30th callback
if (this.callbackCount % 30 === 0) {
    const elapsedSeconds = (Date.now() - this.recordingStartTime) / 1000;
    console.log(`AudifyMicrophone: Callback #${this.callbackCount}, Data: ${buffer.length} bytes, Total: ${this.totalDataSize} bytes, Time: ${elapsedSeconds.toFixed(1)}s`);
}
```

### 4. Improved Error Handling

Enhanced error handling with specific cleanup procedures:

```typescript
try {
    // ... audio operations
} catch (error) {
    console.error('AudifyMicrophone: Error starting recording:', error);
    
    // Clean up any resources
    if (this.rtAudio) {
        try {
            if (this.rtAudio.isStreamRunning()) {
                this.rtAudio.stop();
            }
            if (this.rtAudio.isStreamOpen()) {
                this.rtAudio.closeStream();
            }
        } catch (cleanupError) {
            console.error('AudifyMicrophone: Error during cleanup:', cleanupError);
        }
        this.rtAudio = null;
    }
    
    this.emit('error', error);
}
```

### 5. Recording Statistics

Added a new method to get recording statistics:

```typescript
public getRecordingStats(): any {
    const elapsedSeconds = this.recordingStartTime ? (Date.now() - this.recordingStartTime) / 1000 : 0;
    return {
        isRecording: this.isRecording,
        isPaused: this.isPaused,
        duration: elapsedSeconds.toFixed(1) + 's',
        callbacks: this.callbackCount,
        dataSize: this.totalDataSize,
        chunks: this.recordedChunks.length
    };
}
```

## Testing Tools

Two test scripts have been created to help diagnose issues:

1. **Simple Audify Test (`audify-test.js`)**:
   - Standalone Node.js script
   - Tests basic Audify recording with detailed logging
   - Reports callback activity and data flow

2. **Improved AudifyMicrophone Test (`test-improved-audify.ts`)**:
   - Tests the VoiceHype extension implementation
   - Uses the enhanced AudifyMicrophone class
   - Reports detailed recording statistics

## Running the Tests

You can run both tests using the provided script:

```bash
./run-audify-tests.sh
```

## Troubleshooting

If recording is still not working, check the following:

1. **Check Callback Execution**:
   - The logs should show callbacks being executed
   - If no callbacks appear, the audio input is not being captured

2. **Verify Device Configuration**:
   - Make sure device ID 1 is a valid input device on your system
   - If needed, try other device IDs in the test script

3. **Frame Size Issues**:
   - If callbacks run but no data is received, try different frame sizes
   - Some audio devices work better with specific frame sizes

4. **Check System Audio**:
   - Ensure the microphone is not muted in system settings
   - Verify that the device is working with other applications

## Implementation of Pause/Resume

The pause/resume implementation is elegant - it simply sets a flag that the audio callback checks:

```typescript
public pauseRecording(): void {
    this.isPaused = true;
    // The flag prevents data from being processed in the callback
}

public resumeRecording(): Readable {
    this.isPaused = false;
    // Data processing in the callback resumes
    return this.passThrough;
}
```

This approach allows the audio stream to remain open during pauses, which is more efficient than stopping and restarting the stream.

## Next Steps

1. Run the test scripts to verify if the audio recording is working
2. Check the logs for any specific errors or warnings
3. If issues persist, try experimenting with different frame sizes or device IDs
4. Consider using a system audio diagnostic tool to verify the microphone is properly recognized

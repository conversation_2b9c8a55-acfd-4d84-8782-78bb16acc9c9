// Customer Portal Integration for VoiceHype
// File: lib/paddle-customer-portal.ts

interface CustomerPortalResponse {
  data: {
    id: string;
    customer_id: string;
    urls: {
      general: {
        overview: string;
      };
    };
    subscriptions: Array<{
      id: string;
      cancel_subscription: string;
      update_subscription_payment_method: string;
    }>;
  };
}

export class PaddleCustomerPortal {
  private apiKey: string;
  private environment: 'sandbox' | 'production';

  constructor(apiKey: string, environment: 'sandbox' | 'production' = 'sandbox') {
    this.apiKey = apiKey;
    this.environment = environment;
  }

  private get baseUrl() {
    return this.environment === 'production' 
      ? 'https://api.paddle.com' 
      : 'https://sandbox-api.paddle.com';
  }

  /**
   * Generate authenticated customer portal link
   */
  async generatePortalLink(customerId: string, subscriptionId?: string): Promise<string> {
    const body = subscriptionId ? {
      subscription_ids: [subscriptionId]
    } : {};

    try {
      const response = await fetch(`${this.baseUrl}/customers/${customerId}/portal-sessions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(body)
      });

      if (!response.ok) {
        throw new Error(`Paddle API error: ${response.status} ${response.statusText}`);
      }

      const data: CustomerPortalResponse = await response.json();
      
      if (subscriptionId && data.data.subscriptions.length > 0) {
        // Return direct payment method update link for failed payments
        return data.data.subscriptions[0].update_subscription_payment_method;
      } else {
        // Return general portal overview
        return data.data.urls.general.overview;
      }
    } catch (error) {
      console.error('Failed to generate customer portal link:', error);
      throw error;
    }
  }

  /**
   * Generate cancellation link for a specific subscription
   */
  async generateCancellationLink(customerId: string, subscriptionId: string): Promise<string> {
    const response = await fetch(`${this.baseUrl}/customers/${customerId}/portal-sessions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        subscription_ids: [subscriptionId]
      })
    });

    if (!response.ok) {
      throw new Error(`Paddle API error: ${response.status} ${response.statusText}`);
    }

    const data: CustomerPortalResponse = await response.json();
    
    if (data.data.subscriptions.length > 0) {
      return data.data.subscriptions[0].cancel_subscription;
    }
    
    throw new Error('Subscription not found in portal response');
  }
}

// React hook for customer portal integration
// File: hooks/useCustomerPortal.ts

import { useState } from 'react';
import { useUser } from '@/hooks/useUser';
import { useSupabase } from '@/hooks/useSupabase';

interface UseCustomerPortalReturn {
  openPaymentUpdatePortal: () => Promise<void>;
  openGeneralPortal: () => Promise<void>;
  openCancellationPortal: () => Promise<void>;
  isLoading: boolean;
  error: string | null;
}

export const useCustomerPortal = (): UseCustomerPortalReturn => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user } = useUser();
  const supabase = useSupabase();

  const getUserSubscription = async () => {
    if (!user) throw new Error('User not authenticated');

    const { data, error } = await supabase
      .from('user_subscriptions')
      .select('paddle_subscription_id')
      .eq('user_id', user.id)
      .single();

    if (error) throw error;
    return data?.paddle_subscription_id;
  };

  const getUserCustomerId = async () => {
    if (!user) throw new Error('User not authenticated');

    // Get customer ID from paddle.subscriptions or user profile
    const { data, error } = await supabase
      .from('paddle.subscriptions') 
      .select('paddle_customer_id')
      .eq('user_id', user.id)
      .single();

    if (error) throw error;
    return data?.paddle_customer_id;
  };

  const openPortal = async (portalType: 'payment' | 'general' | 'cancellation') => {
    setIsLoading(true);
    setError(null);

    try {
      const customerId = await getUserCustomerId();
      if (!customerId) {
        throw new Error('Customer ID not found');
      }

      let subscriptionId: string | undefined;
      if (portalType === 'payment' || portalType === 'cancellation') {
        subscriptionId = await getUserSubscription();
        if (!subscriptionId) {
          throw new Error('Subscription not found');
        }
      }

      // Call your backend API endpoint that handles portal generation
      const response = await fetch('/api/customer-portal', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          customerId,
          subscriptionId,
          type: portalType
        })
      });

      if (!response.ok) {
        throw new Error('Failed to generate portal link');
      }

      const { portalUrl } = await response.json();
      
      // Open in new tab/window
      window.open(portalUrl, '_blank', 'noopener,noreferrer');
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  return {
    openPaymentUpdatePortal: () => openPortal('payment'),
    openGeneralPortal: () => openPortal('general'),
    openCancellationPortal: () => openPortal('cancellation'),
    isLoading,
    error
  };
};

// Payment notification banner component
// File: components/PaymentNotificationBanner.tsx

import React from 'react';
import { AlertTriangle, CreditCard, X } from 'lucide-react';
import { useCustomerPortal } from '@/hooks/useCustomerPortal';
import { useSubscriptionStatus } from '@/hooks/useSubscriptionStatus';

export const PaymentNotificationBanner: React.FC = () => {
  const { subscription, loading } = useSubscriptionStatus();
  const { openPaymentUpdatePortal, isLoading } = useCustomerPortal();
  const [dismissed, setDismissed] = React.useState(false);

  // Don't show banner if loading, dismissed, or subscription is not past due
  if (loading || dismissed || subscription?.status !== 'past_due') {
    return null;
  }

  return (
    <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg p-4 mb-6 shadow-sm">
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
        </div>
        
        <div className="ml-3 flex-1">
          <h3 className="text-sm font-medium text-yellow-800">
            Payment Required
          </h3>
          <p className="mt-1 text-sm text-yellow-700">
            Your recent payment failed. Don't worry - your service remains active while we attempt to recover the payment. 
            Please update your payment method to ensure uninterrupted service.
          </p>
          
          <div className="mt-3 flex space-x-3">
            <button
              onClick={openPaymentUpdatePortal}
              disabled={isLoading}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-yellow-800 bg-yellow-100 hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <CreditCard className="h-4 w-4 mr-2" />
              {isLoading ? 'Opening...' : 'Update Payment Method'}
            </button>
            
            <button
              onClick={() => setDismissed(true)}
              className="text-sm text-yellow-700 hover:text-yellow-800 underline"
            >
              Remind me later
            </button>
          </div>
        </div>
        
        <div className="ml-4 flex-shrink-0">
          <button
            onClick={() => setDismissed(true)}
            className="rounded-md inline-flex text-yellow-400 hover:text-yellow-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
          >
            <span className="sr-only">Dismiss</span>
            <X className="h-5 w-5" />
          </button>
        </div>
      </div>
    </div>
  );
};

// Subscription status hook
// File: hooks/useSubscriptionStatus.ts

import { useState, useEffect } from 'react';
import { useUser } from '@/hooks/useUser';
import { useSupabase } from '@/hooks/useSupabase';

interface SubscriptionStatus {
  id: string;
  status: 'active' | 'past_due' | 'canceled' | 'paused' | 'trialing';
  paddle_subscription_id: string;
  current_period_end?: string;
  next_billed_at?: string;
}

export const useSubscriptionStatus = () => {
  const [subscription, setSubscription] = useState<SubscriptionStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useUser();
  const supabase = useSupabase();

  useEffect(() => {
    if (!user) {
      setLoading(false);
      return;
    }

    const fetchSubscription = async () => {
      try {
        const { data, error } = await supabase
          .from('user_subscriptions')
          .select('id, status, paddle_subscription_id, current_period_end, next_billed_at')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false })
          .limit(1)
          .single();

        if (error) throw error;
        setSubscription(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch subscription');
        setSubscription(null);
      } finally {
        setLoading(false);
      }
    };

    fetchSubscription();

    // Set up real-time subscription to status changes
    const subscription_channel = supabase
      .channel('subscription-status-changes')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'user_subscriptions',
          filter: `user_id=eq.${user.id}`
        },
        (payload) => {
          setSubscription(payload.new as SubscriptionStatus);
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(subscription_channel);
    };
  }, [user, supabase]);

  return { subscription, loading, error };
};

// Backend API endpoint for portal generation
// File: pages/api/customer-portal.ts (Next.js) or equivalent

import { PaddleCustomerPortal } from '@/lib/paddle-customer-portal';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { customerId, subscriptionId, type } = req.body;

    if (!customerId) {
      return res.status(400).json({ error: 'Customer ID is required' });
    }

    const paddle = new PaddleCustomerPortal(
      process.env.PADDLE_API_KEY!,
      process.env.PADDLE_ENVIRONMENT as 'sandbox' | 'production'
    );

    let portalUrl: string;

    switch (type) {
      case 'payment':
        portalUrl = await paddle.generatePortalLink(customerId, subscriptionId);
        break;
      case 'cancellation':
        if (!subscriptionId) {
          return res.status(400).json({ error: 'Subscription ID required for cancellation' });
        }
        portalUrl = await paddle.generateCancellationLink(customerId, subscriptionId);
        break;
      case 'general':
      default:
        portalUrl = await paddle.generatePortalLink(customerId);
        break;
    }

    res.status(200).json({ portalUrl });
  } catch (error) {
    console.error('Customer portal generation error:', error);
    res.status(500).json({ error: 'Failed to generate portal link' });
  }
}

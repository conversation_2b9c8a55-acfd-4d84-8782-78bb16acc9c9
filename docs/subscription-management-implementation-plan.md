# VoiceHype Subscription Management Implementation Plan

**Created:** June 27, 2025  
**Status:** Design Phase  
**Priority:** High  
**Based on:** Paddle Billing API Documentation Analysis

---

## 🎯 **Executive Summary**

This document outlines the implementation of comprehensive subscription management features for VoiceHype, including cancellation, upgrades/downgrades, and failed renewal handling using Paddle Billing.

### **Key Decisions Based on Paddle Research**

1. **Cancellations**: End-of-period cancellation (no immediate refunds)
2. **Upgrades/Downgrades**: Paddle handles proration automatically
3. **Failed Renewals**: Use Paddle's pause/resume functionality with grace period
4. **Resource Merging**: Custom logic to combine quotas intelligently

---

## 📋 **Paddle Capabilities Analysis**

### **1. Cancellation Policies**
Based on Paddle documentation:
- ✅ **End-of-period cancellation**: Subscription continues until current billing period ends
- ✅ **Immediate cancellation**: Possible but requires custom refund logic
- ✅ **Scheduled changes**: Paddle automatically handles cancellation at period end
- ⚠️ **Refunds**: Not automatic - requires manual processing via Paddle API

### **2. Upgrade/Downgrade Proration**
Paddle provides built-in proration:
- ✅ **Automatic calculation**: Paddle calculates prorated amounts
- ✅ **Immediate changes**: Changes take effect immediately with prorated billing
- ✅ **Credit/charge handling**: Paddle manages credits and additional charges
- ✅ **Next billing adjustment**: Future billing reflects new subscription tier

### **3. Failed Renewal Handling**
Paddle subscription statuses:
- `active`: Subscription is current and paid
- `past_due`: Payment failed but subscription still active
- `paused`: Subscription paused (no billing attempts)
- `canceled`: Subscription terminated

### **4. Key Webhook Events**
- `subscription.updated`: Status changes, plan changes
- `subscription.past_due`: Payment failed
- `subscription.paused`: Subscription paused
- `subscription.resumed`: Subscription reactivated
- `subscription.canceled`: Subscription terminated
- `transaction.completed`: Successful payment (including upgrades/downgrades)

---

## 🏗️ **Implementation Architecture**

### **Core Components**

1. **Subscription Management Service**: Handle all subscription operations
2. **Resource Merger**: Intelligent quota combination logic
3. **Webhook Handlers**: Process Paddle events
4. **User Interface**: Self-service subscription management
5. **Database Functions**: Atomic subscription updates

---

## 🔧 **Feature 1: Subscription Cancellation**

### **Decision: End-of-Period Cancellation (Recommended)**

**Rationale:**
- Provides full value for money paid
- Reduces refund processing complexity
- Aligns with industry standards
- Simpler implementation

### **Implementation Plan**

#### **1.1 Database Function**
```sql
CREATE OR REPLACE FUNCTION cancel_user_subscription(
    p_user_id UUID,
    p_cancellation_reason TEXT DEFAULT NULL
) RETURNS JSONB
```

**Features:**
- Schedule cancellation at period end via Paddle API
- Update local subscription status to 'cancel_scheduled'
- Log cancellation reason and timestamp
- Maintain access until period end

#### **1.2 Paddle API Integration**
```typescript
// Cancel subscription at period end
await paddle.subscriptions.cancel(subscriptionId, {
  effective_from: 'next_billing_period'
})
```

#### **1.3 User Interface**
- **Cancellation flow**: Confirmation modal with reason selection
- **Access preservation**: Continue service until period end
- **Reactivation option**: Allow users to undo cancellation before period end

#### **1.4 Webhook Handling**
```typescript
case 'subscription.updated':
  if (data.scheduled_change?.action === 'cancel') {
    // Update local status to 'cancel_scheduled'
    await updateSubscriptionStatus(userId, 'cancel_scheduled')
  }
  break;

case 'subscription.canceled':
  // Subscription actually canceled - remove access
  await handleSubscriptionCanceled(supabase, event)
  break;
```

---

## 🔧 **Feature 2: Subscription Upgrades/Downgrades**

### **Implementation Strategy: Paddle Proration + Custom Resource Merging**

### **2.1 Upgrade/Downgrade Process**

#### **Step 1: Resource Calculation**
Before making changes, calculate combined resources:
```typescript
interface ResourceMerger {
  calculateMergedQuotas(
    currentQuotas: UserQuotas,
    newPlan: SubscriptionPlan,
    remainingDays: number
  ): MergedQuotas
}
```

#### **Step 2: Paddle Subscription Update**
```typescript
// Update subscription via Paddle API
await paddle.subscriptions.update(subscriptionId, {
  items: [
    {
      price_id: newPriceId,
      quantity: 1
    }
  ],
  proration_billing_mode: 'prorated_immediately'
})
```

#### **Step 3: Local Quota Update**
```sql
CREATE OR REPLACE FUNCTION update_subscription_with_merged_quotas(
    p_user_id UUID,
    p_new_plan_id UUID,
    p_merged_transcription_quota INTEGER,
    p_merged_optimization_quota INTEGER
) RETURNS JSONB
```

### **2.2 Resource Merging Algorithm**

```typescript
function calculateMergedQuotas(
  currentQuotas: UserQuotas,
  newPlan: SubscriptionPlan,
  daysRemaining: number,
  totalDaysInCycle: number
): MergedQuotas {
  // Calculate remaining value from current subscription
  const remainingPercentage = daysRemaining / totalDaysInCycle
  const remainingTranscription = currentQuotas.remaining_transcription
  const remainingOptimization = currentQuotas.remaining_optimization
  
  // Calculate new plan full quotas
  const newTranscription = newPlan.transcription_minutes
  const newOptimization = newPlan.optimization_tokens
  
  // Merge resources intelligently
  const mergedTranscription = Math.min(
    remainingTranscription + newTranscription,
    newTranscription * 1.5 // Cap at 150% of new plan to prevent abuse
  )
  
  const mergedOptimization = Math.min(
    remainingOptimization + newOptimization,
    newOptimization * 1.5
  )
  
  return {
    transcription: Math.round(mergedTranscription),
    optimization: Math.round(mergedOptimization),
    reset_date: calculateNextBillingDate()
  }
}
```

### **2.3 Upgrade/Downgrade Scenarios**

#### **Scenario A: Basic ($9) → Pro ($18)**
- **Proration**: Paddle charges prorated difference
- **Quotas**: 
  - Remaining: 200 minutes, 50K tokens
  - New Pro: 1440 minutes, 800K tokens
  - **Merged**: 1440 + 200 = 1640 minutes, 800K + 50K = 850K tokens

#### **Scenario B: Premium ($27) → Basic ($9)**
- **Proration**: Paddle provides credit for difference
- **Quotas**:
  - Remaining: 1000 minutes, 400K tokens
  - New Basic: 720 minutes, 400K tokens
  - **Merged**: min(1000 + 720, 720 * 1.5) = 1080 minutes, 800K tokens

### **2.4 Webhook Handling**
```typescript
case 'subscription.updated':
  if (data.items[0].price.id !== previousPriceId) {
    // Plan change detected
    await handlePlanChange(supabase, event)
  }
  break;

case 'transaction.completed':
  if (data.origin === 'subscription_change') {
    // Upgrade/downgrade payment completed
    await finalizeSubscriptionChange(supabase, event)
  }
  break;
```

---

## 🔧 **Feature 3: Failed Renewal Management**

### **Decision: Pause with Grace Period (Recommended)**

**Strategy:**
1. **Grace Period**: 7 days of continued access after payment failure
2. **Pause Subscription**: Use Paddle's pause feature to stop billing attempts
3. **Manual Resume**: Allow users to update payment method and resume

### **3.1 Failed Renewal Process**

#### **Phase 1: Payment Failure (Day 0)**
```typescript
case 'subscription.past_due':
  await handlePaymentFailure(supabase, event)
  // Status: past_due (access continues)
  break;
```

#### **Phase 2: Grace Period (Days 1-7)**
```typescript
// Daily job to check grace period expiration
async function checkGracePeriods() {
  const expiredGracePeriods = await supabase
    .from('user_subscriptions')
    .select('*')
    .eq('status', 'past_due')
    .lt('grace_period_end', new Date().toISOString())
  
  for (const subscription of expiredGracePeriods) {
    await pauseSubscription(subscription.stripe_subscription_id)
  }
}
```

#### **Phase 3: Subscription Pause (Day 8+)**
```typescript
async function pauseSubscription(paddleSubscriptionId: string) {
  // Pause via Paddle API
  await paddle.subscriptions.pause(paddleSubscriptionId, {
    effective_from: 'immediately'
  })
  
  // Update local status
  await updateSubscriptionStatus(userId, 'paused')
}
```

### **3.2 Recovery Flow**

#### **Payment Method Update**
```typescript
// Get payment method update URL from Paddle
const updateUrl = subscription.management_urls.update_payment_method

// Resume subscription after payment method update
await paddle.subscriptions.resume(subscriptionId, {
  effective_from: 'immediately'
})
```

#### **Webhook Handling**
```typescript
case 'subscription.paused':
  await handleSubscriptionPaused(supabase, event)
  // Revoke access, show payment update flow
  break;

case 'subscription.resumed':
  await handleSubscriptionResumed(supabase, event)
  // Restore access, reset quotas if needed
  break;
```

---

## 🗄️ **Database Schema Updates**

### **Enhanced user_subscriptions Table**
```sql
ALTER TABLE user_subscriptions ADD COLUMN IF NOT EXISTS:
  cancellation_reason TEXT,
  cancellation_scheduled_at TIMESTAMP WITH TIME ZONE,
  grace_period_end TIMESTAMP WITH TIME ZONE,
  previous_plan_id UUID REFERENCES subscription_plans(id),
  upgrade_downgrade_history JSONB DEFAULT '[]'::jsonb;
```

### **New Table: subscription_changes**
```sql
CREATE TABLE subscription_changes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES profiles(id),
  subscription_id UUID NOT NULL,
  change_type TEXT NOT NULL, -- 'upgrade', 'downgrade', 'cancel', 'pause', 'resume'
  from_plan_id UUID REFERENCES subscription_plans(id),
  to_plan_id UUID REFERENCES subscription_plans(id),
  paddle_transaction_id TEXT,
  proration_amount DECIMAL(10,2),
  effective_date TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### **Enhanced quotas Table**
```sql
ALTER TABLE quotas ADD COLUMN IF NOT EXISTS:
  merged_from_previous BOOLEAN DEFAULT FALSE,
  original_amount INTEGER, -- Store original quota before merging
  bonus_amount INTEGER DEFAULT 0; -- Amount added from previous subscription
```

---

## 🎨 **User Interface Components**

### **1. Subscription Management Dashboard**
```typescript
// Component: SubscriptionManagement.vue
interface SubscriptionManagementProps {
  currentSubscription: UserSubscription
  availablePlans: SubscriptionPlan[]
  canUpgrade: boolean
  canDowngrade: boolean
  canCancel: boolean
}
```

**Features:**
- Current plan display with usage metrics
- Upgrade/downgrade buttons with pricing
- Cancellation flow with confirmation
- Payment method management
- Billing history

### **2. Plan Change Flow**
```typescript
// Component: PlanChangeFlow.vue
interface PlanChangeState {
  selectedPlan: SubscriptionPlan
  prorationPreview: ProrationCalculation
  mergedQuotasPreview: MergedQuotas
  confirmationStep: boolean
}
```

**Features:**
- Plan comparison with current plan
- Real-time proration calculation
- Merged quota preview
- Change confirmation with impact summary

### **3. Failed Payment Recovery**
```typescript
// Component: PaymentRecovery.vue
interface PaymentRecoveryProps {
  subscription: UserSubscription
  gracePeriodRemaining: number
  paymentUpdateUrl: string
}
```

**Features:**
- Grace period countdown
- Payment method update flow
- Subscription pause/resume status
- Recovery instructions

---

## 🔄 **Webhook Implementation Updates**

### **Enhanced Event Handlers**

```typescript
// Add to existing paddle-webhook/index.ts
switch (event.event_type) {
  // Existing handlers...
  
  case 'subscription.updated':
    processed = await handleSubscriptionUpdated(supabase, event)
    break;
    
  case 'subscription.past_due':
    processed = await handleSubscriptionPastDue(supabase, event)
    break;
    
  case 'subscription.paused':
    processed = await handleSubscriptionPaused(supabase, event)
    break;
    
  case 'subscription.resumed':
    processed = await handleSubscriptionResumed(supabase, event)
    break;
}
```

### **New Handler Functions**

```typescript
async function handleSubscriptionUpdated(supabase: any, event: PaddleWebhookEvent): Promise<boolean> {
  const { data } = event
  
  // Check for plan changes
  if (data.items[0].price.id !== previousPriceId) {
    return await handlePlanChange(supabase, event)
  }
  
  // Check for scheduled cancellation
  if (data.scheduled_change?.action === 'cancel') {
    return await handleScheduledCancellation(supabase, event)
  }
  
  return true
}

async function handleSubscriptionPastDue(supabase: any, event: PaddleWebhookEvent): Promise<boolean> {
  const { data } = event
  
  // Start grace period
  const gracePeriodEnd = new Date()
  gracePeriodEnd.setDate(gracePeriodEnd.getDate() + 7)
  
  const { error } = await supabase
    .from('user_subscriptions')
    .update({
      status: 'past_due',
      grace_period_end: gracePeriodEnd.toISOString()
    })
    .eq('stripe_subscription_id', data.id)
  
  return !error
}

async function handlePlanChange(supabase: any, event: PaddleWebhookEvent): Promise<boolean> {
  // Calculate merged quotas and update subscription
  return await handleSubscriptionPlanChange(supabase, event)
}
```

---

## 📊 **Implementation Phases**

### **Phase 1: Cancellation (Week 1)**
1. ✅ Database schema updates
2. ✅ Cancellation function implementation
3. ✅ Paddle API integration
4. ✅ Webhook handler updates
5. ✅ Basic UI for cancellation

### **Phase 2: Upgrades/Downgrades (Week 2-3)**
1. ✅ Resource merging algorithm
2. ✅ Plan change database functions
3. ✅ Paddle subscription update integration
4. ✅ Enhanced webhook handlers
5. ✅ Plan change UI components

### **Phase 3: Failed Renewal Management (Week 4)**
1. ✅ Grace period implementation
2. ✅ Pause/resume functionality
3. ✅ Payment recovery flow
4. ✅ Automated grace period monitoring
5. ✅ Recovery UI components

### **Phase 4: Testing & Refinement (Week 5)**
1. ✅ End-to-end testing all scenarios
2. ✅ Edge case handling
3. ✅ Performance optimization
4. ✅ User experience refinement
5. ✅ Documentation completion

---

## 🎯 **Business Rules & Policies**

### **Cancellation Policy**
- ✅ **No refunds**: Subscriptions remain active until period end
- ✅ **Reactivation window**: Allow undo during same billing period
- ✅ **Data retention**: Keep user data for 30 days post-cancellation
- ✅ **Win-back**: Offer discounts for reactivation

### **Upgrade/Downgrade Policy**
- ✅ **Immediate effect**: Changes take effect immediately with proration
- ✅ **Resource preservation**: Merge remaining quotas intelligently
- ✅ **Quota caps**: Limit merged quotas to 150% of new plan
- ✅ **Downgrade protection**: Ensure minimum service level maintained

### **Failed Payment Policy**
- ✅ **Grace period**: 7 days continued access
- ✅ **Automatic pause**: After grace period expires
- ✅ **Easy recovery**: One-click payment method update
- ✅ **Retention offers**: Special pricing for failed payment recovery

---

## 📈 **Success Metrics**

### **Cancellation Metrics**
- Cancellation rate by plan
- Reason analysis for cancellations
- Reactivation rate within period
- Win-back campaign effectiveness

### **Upgrade/Downgrade Metrics**
- Upgrade conversion rate
- Average revenue per user (ARPU) change
- Resource utilization post-change
- User satisfaction with proration

### **Failed Payment Metrics**
- Payment failure rate by payment method
- Grace period recovery rate
- Time to payment method update
- Churn rate post-payment failure

---

## 🚀 **Deployment Plan**

### **Pre-deployment Checklist**
1. ✅ All database migrations tested
2. ✅ Paddle webhook endpoints configured
3. ✅ Error handling and logging implemented
4. ✅ User interface testing completed
5. ✅ Edge case scenarios validated

### **Deployment Strategy**
1. **Feature flags**: Deploy behind feature flags for gradual rollout
2. **A/B testing**: Test cancellation flows with different user groups
3. **Monitoring**: Real-time monitoring of webhook processing
4. **Rollback plan**: Quick rollback procedure if issues arise

### **Post-deployment Monitoring**
1. **Webhook success rates**: Monitor Paddle webhook processing
2. **User flow completion**: Track completion rates for each flow
3. **Error rates**: Monitor and alert on unusual error patterns
4. **Business metrics**: Track impact on churn and revenue

---

## 🔒 **Security Considerations**

### **Access Control**
- ✅ Users can only modify their own subscriptions
- ✅ Validate subscription ownership before changes
- ✅ Rate limiting on subscription change endpoints
- ✅ Audit logging for all subscription modifications

### **Data Protection**
- ✅ Secure payment method data handling
- ✅ PCI compliance for payment flows
- ✅ Data retention policies for canceled accounts
- ✅ GDPR compliance for user data deletion

### **Fraud Prevention**
- ✅ Limit upgrade/downgrade frequency
- ✅ Monitor for unusual cancellation patterns
- ✅ Validate proration calculations
- ✅ Alert on suspicious resource merging patterns

---

## 📚 **Additional Resources**

### **Documentation Required**
1. **User Guides**: How to manage subscriptions
2. **Support Documentation**: Troubleshooting failed payments
3. **Admin Guide**: Managing subscription issues
4. **Developer Documentation**: API endpoints and webhooks

### **Testing Scenarios**
1. **Happy Path Testing**: Normal upgrade/downgrade flows
2. **Edge Case Testing**: Failed payments, expired cards, etc.
3. **Load Testing**: High volume subscription changes
4. **Security Testing**: Unauthorized access attempts

---

# VoiceHype Simple Subscription Management Plan

**Created:** June 27, 2025  
**Status:** Simple Planning Phase  
**Approach:** Keep it simple, use existing infrastructure

---

## 🎯 **Simplified Requirements**

### **1. Cancellation Policy**
- ✅ Cancel at end of period (no refunds)
- ✅ User keeps quota until period end
- ✅ No immediate access revocation

### **2. Upgrade/Downgrade Policy**
- ✅ Always merge remaining resources (minutes + tokens)
- ✅ Simple addition: `remaining_minutes + new_plan_minutes`
- ✅ Same logic for upgrade AND downgrade

### **3. Failed Renewal Policy**
- ✅ Immediate pause (no grace period)
- ✅ User can resume anytime by fixing payment
- ✅ Manual cleanup later (separate cron job project)

---

## 🏗️ **What We Need to Do (Simple Version)**

### **Use Existing Infrastructure:**
- ✅ `paddle-webhook` edge function (already exists)
- ✅ `user_subscriptions` table (already exists)
- ✅ `quotas` table (already exists)
- ✅ `handle_subscription_transaction` function (already exists)

---

## 🔧 **Feature 1: Cancellation (Simple)**

### **What to Add:**

#### **1.1 Add Column to user_subscriptions**
```sql
ALTER TABLE user_subscriptions 
ADD COLUMN cancel_at_period_end BOOLEAN DEFAULT FALSE;
```

#### **1.2 Update Paddle Webhook Function**
Add this case to existing `paddle-webhook/index.ts`:
```typescript
case 'subscription.updated':
  if (data.cancel_at_period_end === true) {
    // User canceled - mark for end of period cancellation
    await supabase
      .from('user_subscriptions')
      .update({ cancel_at_period_end: true })
      .eq('stripe_subscription_id', data.id)
  }
  break;
```

#### **1.3 Frontend Button**
Simple cancel button that calls Paddle API:
```typescript
// In payments view - add cancel button
async function cancelSubscription() {
  // Call Paddle API to cancel at period end
  // Paddle handles the scheduling automatically
}
```

**That's it for cancellation!** ✅

---

## 🔧 **Feature 2: Upgrade/Downgrade (Simple)**

### **What to Add:**

#### **2.1 Simple Resource Merge Function**
Add to existing database functions:
```sql
CREATE OR REPLACE FUNCTION merge_subscription_quotas(
    p_user_id UUID,
    p_new_plan TEXT -- 'basic', 'pro', 'premium'
) RETURNS JSONB AS $$
DECLARE
    v_current_transcription INTEGER;
    v_current_optimization INTEGER;
    v_new_transcription INTEGER;
    v_new_optimization INTEGER;
BEGIN
    -- Get current remaining quotas
    SELECT 
        (total_amount - used_amount) 
    INTO v_current_transcription
    FROM quotas 
    WHERE user_id = p_user_id AND service = 'transcription';
    
    SELECT 
        (total_amount - used_amount) 
    INTO v_current_optimization
    FROM quotas 
    WHERE user_id = p_user_id AND service = 'optimization';
    
    -- Get new plan quotas
    CASE LOWER(p_new_plan)
        WHEN 'basic' THEN
            v_new_transcription := 720;
            v_new_optimization := 400000;
        WHEN 'pro' THEN
            v_new_transcription := 1440;
            v_new_optimization := 800000;
        WHEN 'premium' THEN
            v_new_transcription := 2160;
            v_new_optimization := 1200000;
    END CASE;
    
    -- Simple merge: just add them together
    UPDATE quotas 
    SET 
        total_amount = v_current_transcription + v_new_transcription,
        used_amount = 0,
        reset_date = NOW() + INTERVAL '30 days'
    WHERE user_id = p_user_id AND service = 'transcription';
    
    UPDATE quotas 
    SET 
        total_amount = v_current_optimization + v_new_optimization,
        used_amount = 0,
        reset_date = NOW() + INTERVAL '30 days'
    WHERE user_id = p_user_id AND service = 'optimization';
    
    RETURN jsonb_build_object('success', true);
END;
$$ LANGUAGE plpgsql;
```

#### **2.2 Update Existing Webhook Handler**
Modify existing `handleSubscriptionTransaction` function:
```typescript
// After successful subscription change, merge quotas
if (customData.previous_plan && customData.subscription_plan) {
  await supabase.rpc('merge_subscription_quotas', {
    p_user_id: userId,
    p_new_plan: customData.subscription_plan
  })
}
```

#### **2.3 Frontend Upgrade/Downgrade**
Simple buttons in payments view:
```typescript
// In payments view - add upgrade/downgrade buttons
async function changeSubscription(newPlan: string) {
  // Call Paddle API to change subscription
  // Include previous_plan in custom_data
  const customData = {
    user_id: userId,
    subscription_plan: newPlan,
    previous_plan: currentPlan // Include this for quota merging
  }
}
```

**That's it for upgrade/downgrade!** ✅

---

## 🔧 **Feature 3: Failed Renewals (Simple)**

### **What to Add:**

#### **3.1 Add Webhook Case**
Add to existing `paddle-webhook/index.ts`:
```typescript
case 'subscription.past_due':
  // Immediately pause subscription
  await supabase
    .from('user_subscriptions')
    .update({ status: 'paused' })
    .eq('stripe_subscription_id', data.id)
  break;

case 'subscription.resumed':
  // User fixed payment - resume subscription
  await supabase
    .from('user_subscriptions')
    .update({ status: 'active' })
    .eq('stripe_subscription_id', data.id)
  break;
```

#### **3.2 Update Quota Check Function**
Modify existing `check_usage_allowance` function:
```sql
-- Add this condition to existing function
IF subscription_status = 'paused' THEN
    RETURN jsonb_build_object(
        'allowed', false,
        'reason', 'subscription_paused',
        'message', 'Please update your payment method to resume service'
    );
END IF;
```

#### **3.3 Frontend Payment Recovery**
Simple message in app when paused:
```typescript
// Show payment update link when subscription is paused
if (subscription.status === 'paused') {
  // Show Paddle payment update URL
  // User clicks -> goes to Paddle -> fixes payment -> webhook resumes
}
```

**That's it for failed renewals!** ✅

---

## 📋 **Summary: What We Actually Need to Do**

### **Database Changes:**
1. ✅ Add 1 column: `cancel_at_period_end` to `user_subscriptions`
2. ✅ Add 1 function: `merge_subscription_quotas`
3. ✅ Modify 1 function: `check_usage_allowance` (add paused check)

### **Webhook Changes:**
1. ✅ Add 3 new cases to existing `paddle-webhook/index.ts`:
   - `subscription.updated` (for cancellation)
   - `subscription.past_due` (for pause)
   - `subscription.resumed` (for resume)
2. ✅ Modify existing transaction handler to call quota merge

### **Frontend Changes:**
1. ✅ Add cancel button to payments view
2. ✅ Add upgrade/downgrade buttons to payments view
3. ✅ Add payment recovery message for paused subscriptions

### **Paddle API Calls:**
1. ✅ Cancel subscription (end of period)
2. ✅ Change subscription (upgrade/downgrade)
3. ✅ Resume subscription (after payment fix)

---

## 🎯 **Implementation Order**

### **Phase 1: Database (15 minutes)**
1. Add `cancel_at_period_end` column
2. Create `merge_subscription_quotas` function
3. Update `check_usage_allowance` function

### **Phase 2: Webhook (30 minutes)**
1. Add 3 new webhook cases
2. Test with Paddle sandbox

### **Phase 3: Frontend (1 hour)**
1. Add subscription management buttons
2. Handle paused state display
3. Test user flows

**Total: ~2 hours of actual implementation** 🚀

---

**This is much simpler and uses only what we already have! Alhamdulillah!** ✅

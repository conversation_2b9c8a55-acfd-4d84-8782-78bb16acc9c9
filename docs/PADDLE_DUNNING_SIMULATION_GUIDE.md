# Paddle Dunning Simulation Guide

## 🎯 Overview
This guide provides the exact payloads and steps to simulate <PERSON><PERSON>'s dunning process for VoiceHype subscriptions.

## 📋 Prerequisites

### 1. Setup Test Data
Before running the simulation, ensure you have:

```sql
-- Get a real user ID from your profiles table
SELECT id, email FROM profiles LIMIT 1;
-- Use this ID to replace 'user-uuid-here' in the payloads
```

### 2. Consistent Test IDs
Use these consistent IDs throughout the dunning sequence:
- **Customer ID**: `ctm_01jwehdjrg45n0b1vja3wnd74v`
- **Subscription ID**: `sub_01abc123def456ghi789jkl`
- **Transaction ID**: `txn_01hv8wptq8987qeep44cyrewp9`
- **User ID**: Replace `user-uuid-here` with actual UUID from your profiles table

## 🔄 Dunning Simulation Sequence

### Step 1: Payment Failure
**Event**: `transaction.payment_failed`
**Timing**: Day 1 - Initial payment failure

```bash
curl -X POST https://your-project.supabase.co/functions/v1/paddle-webhook \
  -H "Content-Type: application/json" \
  -H "paddle-signature: ts=1234567890;h1=test_signature" \
  -d '{
    "event_id": "evt_dunning_payment_failed_001",
    "event_type": "transaction.payment_failed",
    "occurred_at": "2025-07-01T10:00:00.000Z",
    "data": {
      "id": "txn_01hv8wptq8987qeep44cyrewp9",
      "status": "payment_failed",
      "customer_id": "ctm_01jwehdjrg45n0b1vja3wnd74v",
      "subscription_id": "sub_01abc123def456ghi789jkl",
      "origin": "subscription_recurring",
      "details": {
        "totals": {
          "total": "1800",
          "currency_code": "USD"
        }
      },
      "custom_data": {
        "user_id": "REPLACE_WITH_REAL_USER_ID",
        "subscription_plan": "pro"
      }
    }
  }'
```

**Expected Result**: 
- Transaction logged in `payment_failures` table
- User retains access (no immediate suspension)

### Step 2: Subscription Past Due
**Event**: `subscription.past_due`
**Timing**: Day 1 - 30 minutes after payment failure

```bash
curl -X POST https://your-project.supabase.co/functions/v1/paddle-webhook \
  -H "Content-Type: application/json" \
  -H "paddle-signature: ts=1234567890;h1=test_signature" \
  -d '{
    "event_id": "evt_dunning_sub_past_due_001",
    "event_type": "subscription.past_due",
    "occurred_at": "2025-07-01T10:30:00.000Z",
    "data": {
      "id": "sub_01abc123def456ghi789jkl",
      "status": "past_due",
      "customer_id": "ctm_01jwehdjrg45n0b1vja3wnd74v",
      "current_billing_period": {
        "starts_at": "2025-07-01T00:00:00.000Z",
        "ends_at": "2025-08-01T00:00:00.000Z"
      },
      "next_billed_at": "2025-08-01T00:00:00.000Z",
      "custom_data": {
        "user_id": "REPLACE_WITH_REAL_USER_ID",
        "subscription_plan": "pro"
      }
    }
  }'
```

**Expected Result**:
- Subscription status updated to 'past_due'
- User still retains access during grace period
- Event logged in `subscription_events` table

### Step 3: Transaction Past Due
**Event**: `transaction.past_due`
**Timing**: Day 1 - 1 hour after payment failure

```bash
curl -X POST https://your-project.supabase.co/functions/v1/paddle-webhook \
  -H "Content-Type: application/json" \
  -H "paddle-signature: ts=1234567890;h1=test_signature" \
  -d '{
    "event_id": "evt_dunning_txn_past_due_001",
    "event_type": "transaction.past_due",
    "occurred_at": "2025-07-01T11:00:00.000Z",
    "data": {
      "id": "txn_01hv8wptq8987qeep44cyrewp9",
      "status": "past_due",
      "customer_id": "ctm_01jwehdjrg45n0b1vja3wnd74v",
      "subscription_id": "sub_01abc123def456ghi789jkl"
    }
  }'
```

**Expected Result**:
- Transaction status updated to 'past_due'
- Dunning period begins

## 🎯 Recovery Scenarios

### Option A: Successful Payment Recovery
**Event**: `subscription.activated`
**Timing**: Day 3 - Payment method updated and retry successful

```bash
curl -X POST https://your-project.supabase.co/functions/v1/paddle-webhook \
  -H "Content-Type: application/json" \
  -H "paddle-signature: ts=1234567890;h1=test_signature" \
  -d '{
    "event_id": "evt_dunning_recovery_success_001",
    "event_type": "subscription.activated",
    "occurred_at": "2025-07-03T09:00:00.000Z",
    "data": {
      "id": "sub_01abc123def456ghi789jkl",
      "status": "active",
      "customer_id": "ctm_01jwehdjrg45n0b1vja3wnd74v",
      "current_billing_period": {
        "starts_at": "2025-07-01T00:00:00.000Z",
        "ends_at": "2025-08-01T00:00:00.000Z"
      },
      "next_billed_at": "2025-08-01T00:00:00.000Z"
    }
  }'
```

**Expected Result**:
- Subscription status restored to 'active'
- User continues with normal service
- Recovery event logged

### Option B: Failed Recovery (Cancellation)
**Event**: `subscription.canceled`
**Timing**: Day 7-8 - Dunning period expires

```bash
curl -X POST https://your-project.supabase.co/functions/v1/paddle-webhook \
  -H "Content-Type: application/json" \
  -H "paddle-signature: ts=1234567890;h1=test_signature" \
  -d '{
    "event_id": "evt_dunning_final_cancel_001",
    "event_type": "subscription.canceled",
    "occurred_at": "2025-07-08T00:00:00.000Z",
    "data": {
      "id": "sub_01abc123def456ghi789jkl",
      "status": "canceled",
      "customer_id": "ctm_01jwehdjrg45n0b1vja3wnd74v",
      "canceled_at": "2025-07-08T00:00:00.000Z"
    }
  }'
```

**Expected Result**:
- Subscription permanently canceled
- User quotas removed via `cleanup_cancelled_subscription`
- Access revoked

## 🧪 Testing Checklist

### Before Testing
- [ ] Replace `REPLACE_WITH_REAL_USER_ID` with actual user UUID
- [ ] Ensure webhook URL is accessible
- [ ] Check that test environment is set (PADDLE_ENVIRONMENT=sandbox)
- [ ] Verify database functions exist: `handle_subscription_transaction`, `cleanup_cancelled_subscription`

### During Testing
- [ ] Monitor webhook logs for processing status
- [ ] Check `paddle_webhooks` table for event logging
- [ ] Verify `user_subscriptions` status changes
- [ ] Monitor `payment_failures` table entries
- [ ] Check `subscription_events` logging

### After Each Event
- [ ] Verify webhook returns 200 status
- [ ] Check database updates are correct
- [ ] Confirm user access state matches expected behavior
- [ ] Review console logs for any errors

## 📊 Database Verification Queries

```sql
-- Check webhook processing
SELECT event_type, status, error_message, created_at 
FROM paddle_webhooks 
WHERE event_id LIKE 'evt_dunning_%' 
ORDER BY created_at DESC;

-- Check subscription status
SELECT status, current_period_start, current_period_end 
FROM user_subscriptions 
WHERE paddle_subscription_id = 'sub_01abc123def456ghi789jkl';

-- Check payment failures
SELECT transaction_id, failure_reason, occurred_at 
FROM payment_failures 
WHERE subscription_id = 'sub_01abc123def456ghi789jkl';

-- Check subscription events
SELECT event_type, occurred_at 
FROM subscription_events 
WHERE subscription_id = 'sub_01abc123def456ghi789jkl' 
ORDER BY occurred_at DESC;
```

## 🚨 Troubleshooting

### Common Issues
1. **Invalid signature**: Use sandbox environment with test signatures
2. **Missing user_id**: Ensure custom_data contains valid user UUID
3. **Database function errors**: Check that all migration functions are applied
4. **Webhook timeout**: Verify Supabase edge function is deployed

### Debug Commands
```bash
# Check webhook function logs
supabase functions logs paddle-webhook

# Test webhook endpoint
curl -X OPTIONS https://your-project.supabase.co/functions/v1/paddle-webhook

# Verify database connectivity
psql -h your-project.supabase.co -p 5432 -U postgres -d postgres
```

## 📝 Notes

- **Grace Period**: Users retain access during `past_due` status
- **Dunning Duration**: Typically 7-14 days before final cancellation
- **Retry Logic**: Paddle automatically retries failed payments
- **Manual Recovery**: Users can update payment methods via customer portal
- **Cleanup**: Canceled subscriptions trigger automatic quota cleanup

This simulation covers the complete dunning lifecycle as handled by your webhook implementation.

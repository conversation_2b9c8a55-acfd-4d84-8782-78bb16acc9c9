# The DevLog Approach: Mastering the Art of Vibe-Coding with LLMs

<PERSON>'s photo
<PERSON>
·
June 16, 2025
·
10 min read

A systematic approach to overcome LLM hallucinations and revolutionize your coding workflow

Assalamualaikum Warahmatullahi W<PERSON>akatuh!

## Introduction

Today I want to talk about a beautiful, systematic approach when building with LLMs and especially when vibe-coding. That's what I'm talking about, vibe-coding!

Oh my goodness, it's very important to be systematic and aware of hallucinations with LLMs. I've been there, and it's SO frustrating! You can't just keep experimenting or hanging at the mercy of LLMs, because they start hallucinating and if you don't have a solution for that, you're going to be fed up and might wander into a forest where you can't see anything, right?

For that, the solution is very systematic. It's a simple yet effective tool. It's a way of dealing with this, which I call DevLog. A DevLog is short for development log and <PERSON><PERSON><PERSON><PERSON><PERSON>, I'm very happy. All praise to <PERSON> that He taught me this strategy. Honestly, it's been a complete game-changer for me!

## Understanding the Problem

A DevLog is basically used when you want to work on a feature, implement security, or refactor your code. Let's take the example of refactoring your code base.

First, you want to start talking with the LLM. You never, never, NEVER, <PERSON><PERSON><PERSON> want to tell the LLM, "Hey, I want to do this. Just start." No, never do that! I've made that mistake so many times, and <PERSON>han<PERSON>llah, it leads to disaster! Planning is essential, and making the correct files is what distinguishes success from failure.

As we talk more with LLMs, there's a fundamental problem: as the chat length or context size increases, the probability of errors and hallucinations rises. There invariably comes a point in every conversation where initiating a new chat becomes necessary - and this is where a proper DevLog saves you. Trust me, I've been in situations where the LLM just started making things up halfway through a project, and I was like, "What are you even talking about?!" It's so annoying!

## The Risk of Just Diving In

The biggest mistake I see people making is just jumping straight into implementation:

1. They tell the LLM "implement this feature" without proper planning
2. They don't guide the LLM on how to research the codebase
3. They have no systematic way to track progress
4. When hallucinations start happening, they get lost
5. When continuing work the next day, they have to re-explain everything

Honestly, it makes me frustrated just thinking about it! I've seen so many projects fail because of this approach.

With the coming of LLMs, this is one big problem where people have stopped thinking and just want the LLM to do everything. This will cause your projects to fail. Literally, you're planning to fail. Like they say, if you're failing to plan, you're planning to fail. SubhanAllah, that's really true. I've learned this the hard way!

## The DevLog Approach Step-by-Step

### Starting with the Right Mindset

First, start with the Salaam. Say Assalamualaikum to your LLM. Start friendly. Explain to them using VoiceHype. Use our product VoiceHype to do that. It'll help you, InshaAllah, write prompts really fast and easily without the hassle of typing. It's going to be very easy, not painful as typing is, and you'll feel more natural. It's going to revolutionize your coding experience, InshaAllah.

Start talking with the LLM and explain everything in your mind. Explain it as nicely as you can. Like you're talking to a friend, not some robot!

### Creating a Conceptual Plan

For example, we'll tell the LLM about our current code base. Let's say it's a To-Do List app. We tell the LLM:

"Hey, this is a To-Do List app and these are the files. I'm following the clean architecture and I want to refactor the code base. We have these controllers and these huge widget files. We don't have a widget agnostic layer. The widget layer is directly clashing with the business logic layer."

So as I was saying, SubhanAllah, I want to have a layer of widgets that are business logic agnostic. I want to follow the atomic design system. That's the plan, InshaAllah.

This is what I call the conceptual plan or the base plan. It's basically the concept - in simple English, you explain what you want to do. It doesn't have technical details. It's just you talking naturally about what's bothering you in the code!

### Guiding the LLM Through Codebase Review

Next, instruct the LLM to review the code base and important files. You can tell it which files matter most. The key is not to give everything to the LLM to figure out alone, but to help and guide it. Teach the LLM how to research and draft the plan.

I can't stress this enough! Don't just throw your codebase at the LLM and say "figure it out" - that's a recipe for disaster! I've tried this and ended up wasting HOURS on useless fixes. You have to be the guide!

### Creating a Technical Plan

Then you want to ask the LLM, "Hey, you need to draft a technical plan by reviewing the code base." So a technical plan basically is going to contain like what files need to be edited, what files need to be created, what code needs to be changed. It might even contain simple MermaidJS diagrams just to explain things more technically, more detail. And that's what the LLM will generate InshaAllah.

This is where things get really exciting! When you see a good technical plan come together, it's just *chef's kiss* - beautiful! It makes all the difference between a smooth project and a complete mess.

### Setting Up Your DevLog System

Tell the LLM, "Please generate a DevLog." For each project I work on, I create a docs folder (short for documents) and in that folder, I create a folder named devlogs.

In that devlogs folder, tell the LLM, "Create a devlog first, write the conceptual plan and then write the technical plan."

I'm telling you, getting organized like this feels SO good. When you have everything in its place, your mind just feels clearer. It's amazing how such a simple thing can make such a big difference!

### Creating the To-Do List

This is a crucial step. A DevLog doesn't just contain the conceptual and technical plan - it also contains a beautiful to-do list.

I tell the LLM to create a to-do list to implement the technical plan. I tell it to use tick and cross emojis to mark progress and pending emojis for pending tasks. Obviously, before starting, all tasks are pending.

After that, I review the plan to check for missing details. Then I tell the LLM to start with the name of Allah, start with Bismillah.

I love crossing items off my to-do list! It gives me such a rush of accomplishment. Like, "Yes! One more step closer to finishing this project!" When working with LLMs, that feeling of progress is absolutely essential to staying motivated.

### Tracking Progress with Notes

As the LLM works, tell it that this DevLog is important for marking progress. The LLM should use tick emojis to mark completed items and also write progress notes. There should be a progress notes section where the LLM writes important keynotes, possibly with timestamps.

Tell the LLM to write short, important notes that explain progress better, InshaAllah.

I can't tell you how many times these notes have saved me! When you come back to a project after a week, and you're like, "Wait, what was I doing?" - those progress notes are like gold. They remind you of exactly what was happening and why certain decisions were made.

## Why the DevLog Approach Changes Everything

This DevLog is incredibly important because it can transform your entire coding workflow.

I'm not exaggerating when I say this approach has completely transformed how I work with LLMs. Before I started using DevLogs, I was constantly frustrated, repeating myself, and losing track of progress. Now, it's like night and day!

### Benefits of Using DevLog

1. **It Helps You Plan Robustly**: It helps you make a robust plan so you know exactly where you're going. You're not just at the mercy of the LLM waiting for things to happen - you're actively figuring out and planning. It's like having a GPS for your project - you always know where you are and where you're headed!

2. **It Tracks Progress Clearly**: The DevLog helps you mark progress. You'll always know where you currently stand, what you've done and what remains. There's nothing more satisfying than seeing those checkmarks accumulate!

3. **It Avoids Hallucinations**: This is the biggest benefit. LLMs will hallucinate, and you'll need to continue implementation in a new chat or another day. What the DevLog helps with is that the next day, you just give the LLM the DevLog - it reads it and continues the work. SubhanAllah boom! You don't need to go through the hassle of "Oh, we did this yesterday. Oh, I don't know what to do next. Please continue." It's a real game changer.

Honestly, the hallucination avoidance alone is worth it. I remember one project where I had to explain the SAME thing to the LLM FIVE TIMES because it kept forgetting what we were working on. It was maddening! Now with DevLogs, that never happens anymore. It's such a relief!

## A Real-World Example: Refactoring a To-Do App

Let's look at what a DevLog might look like for our To-Do app refactoring example:

```markdown
# DevLog: To-Do App Refactoring to Clean Architecture

## Conceptual Plan

Our To-Do app has grown over time and now has widget files directly interfacing with business logic. I want to refactor to follow clean architecture principles with proper layers (presentation, domain, data) so the widgets are business-logic agnostic.

## Codebase Review

Key files:
- `lib/screens/todo_list_screen.dart`: Contains both UI and business logic
- `lib/models/todo_item.dart`: Data model for todo items
- `lib/services/todo_service.dart`: Service for CRUD operations
- `lib/main.dart`: App entry point

## Technical Plan

### New Directory Structure
```

```mermaid
graph TD
    lib --> presentation
    lib --> domain
    lib --> data
    presentation --> screens
    presentation --> widgets
    domain --> entities
    domain --> repositories[repositories_abstract]
    domain --> usecases
    data --> repositories_impl
    data --> datasources
```

```markdown
### Files to Create:
- `lib/domain/entities/todo_entity.dart`
- `lib/domain/repositories/todo_repository.dart`
- `lib/domain/usecases/get_todos_usecase.dart`
- `lib/data/repositories/todo_repository_impl.dart`
- `lib/data/datasources/todo_local_datasource.dart`

### Files to Modify:
- `lib/screens/todo_list_screen.dart`: Remove business logic
- `lib/models/todo_item.dart`: Convert to proper entity/model pattern

## To-Do List

- [ ] Create project directory structure
- [ ] Create domain layer entities
- [ ] Define repository interfaces
- [ ] Implement use cases
- [ ] Create data layer implementations
- [ ] Refactor UI components
- [ ] Update dependency injection
- [ ] Write tests for new components

## Progress Notes

*[This section will be updated as we make progress]*
```

## When to Use the DevLog Approach

The DevLog approach is especially helpful in these situations:

- **Complex refactoring projects** - these can get messy FAST without proper planning!
- **When implementing new features** - keep track of all the moving parts
- **For large-scale architectural changes** - don't get lost in the forest of files
- **When working across multiple days or sessions** - pick up right where you left off
- **When switching between different AI assistants** - no need to repeat yourself

I use DevLogs for pretty much everything now. Once you get the hang of it, you'll wonder how you ever worked without them. They're like a superpower for LLM-assisted development!

## The Key to Productive Coding with LLMs

The trick to coding productively with LLMs isn't just about experience or crafting better prompts. Those help, I'm sure. I'm not denying their benefits. Obviously they do help, MashaAllah.

But they're not the only things. The most important thing is the systematic approach. Those systematic ways of approaching problems, implementing new features, SubhanAllah. How you explain to the LLM and how you help it work - that really changes everything. SubhanAllah.

I'm PASSIONATE about this because I've seen too many developers struggle unnecessarily. They spend hours crafting the perfect prompt but don't have a system for managing the conversation. That's like having an expensive car but no map for your journey!

## Conclusion

The DevLog approach is a game-changer for working with LLMs in coding. It helps you:

1. **Plan properly** before diving into implementation
2. **Guide the LLM** through your codebase effectively
3. **Track progress** clearly and systematically
4. **Avoid hallucinations** by maintaining context
5. **Resume work seamlessly** across multiple sessions

Remember that failing to plan is planning to fail. The DevLog helps you build a structured approach that makes LLMs truly effective partners rather than unreliable assistants.

I really hope you'll try this approach. It's made such a huge difference in my work, and I know it can do the same for you. The first time you come back to a project after a week away and the LLM picks up right where you left off... that feeling is AMAZING!

## Bonus Productivity Tip for Developers

If you're a developer looking to work faster and more efficiently, check out our tool VoiceHype — a powerful SaaS product built specifically for developers who want to speak instead of type. With VoiceHype, you can not only generate accurate transcriptions by voice, but also optimize and interact with your transcripts using advanced LLMs like Claude. It supports multiple smart modes tailored to different tasks and use-cases. Alhamdulillah, it's available as a VS Code extension — just search for "VoiceHype" on the marketplace and give it a try. It's made with developers in mind, and we hope you'll find it truly useful, InshaAllah.

Checkout https://voicehype.ai.

This approach was developed through practical experience building real-world applications with LLM assistance. May it serve as guidance for developers seeking more effective ways to build with AI assistance, Ameen!

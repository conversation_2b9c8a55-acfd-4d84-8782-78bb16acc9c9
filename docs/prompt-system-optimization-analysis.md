# VoiceHype Prompt System Analysis

Wa alaikum assalam wa rahmatull<PERSON> wa barakatuh. I've analyzed the code to understand why the prompt system optimization isn't working as expected. Here's a detailed breakdown of what's happening:

## Current Flow

### 1. Client-Side Prompt Construction

In `TranscriptionService.ts`, the client creates a sophisticated prompt with specific formatting:

```typescript
private processPromptVariables(prompt: string, transcript: string): string {
    // Establish Voice Hype identity
    const identityHeader = `### ASSISTANT IDENTITY ###
    You are Voice Hype, an AI optimization assistant...`;

    // Universal instructions
    const instructionHeader = `### BASE INSTRUCTIONS ###...`;

    // Voice command detection with high priority override
    const voiceCommandInstructions = `### COMMAND PRIORITY OVERRIDE ###...`;
    
    // Format to enforce JSON response
    const jsonInstructions = `\n\nRETURN YOUR RESPONSE IN THIS JSON FORMAT:...`;
    
    // Build the prompt carefully with all these sections
    // ...
```

The client then sends this carefully crafted prompt to the server in `optimizeWithCustomPrompt` (lines 386-518).

### 2. Communication Layer

In `supabaseClient.ts`, the request is sent to the server:

```typescript
const requestBody = {
    text,
    model,
    customPrompt, // This is the carefully crafted prompt from the client
    debug: true,
    requestId
};

console.log('VoiceHype: Full optimization request:', {
    textLength: text.length,
    model,
    customPromptLength: customPrompt.length,
    customPromptPreview: customPrompt.substring(0, 100) + (customPrompt.length > 100 ? '...' : ''),
    endpoint: 'optimize',
    requestId
});
```

### 3. Server-Side Processing (The Problem Area)

In `optimizeWithOpenRouter.ts`, the server ignores the prompt structure and creates its own:

```typescript
// Server IGNORES the complete prompt structure and creates its own
const requestBody = {
  model: OPENROUTER_MODELS[model],
  messages: [
    {
      role: 'system',
      content: `You are a text optimization assistant. You must ONLY return...` // Creates new system prompt!
    },
    {
      role: 'user',
      content: `
      ### TRANSCRIPT TO OPTIMIZE ###
\`\`\`
${text}
\`\`\`
      ${customPrompt}` // Uses client prompt merely as content
    }
  ],
  // ...
}
```

Similarly, in `optimizeWithOpenRouter.ts` for the fallback method (line 198-224):

```typescript
messages: [
  {
    role: 'system',
    content: `You are a text optimization assistant. You must ONLY return... ${customPrompt}` // Creates new system prompt!
  },
  {
    role: 'user',
    content: `### CRITICAL INSTRUCTIONS ###
I need you to optimize...` // Hard-coded content that overrides client prompt
  }
],
```

And in `index.ts` for OpenAI optimization (line 184-201):

```typescript
const requestBody = {
  model: model,
  messages: [
    {
      role: 'system',
      content: customPrompt // Uses client prompt here
    },
    {
      role: 'user',
      content: text // But doesn't respect the client's formatting
    }
  ],
  // ...
}
```

## The Core Issues

1. **Prompt Structure Override**: The server is creating its own message structure instead of using the client's carefully crafted prompt as-is.

2. **Inconsistent Handling**: Different optimization functions handle the custom prompt differently:
   - `optimizeWithOpenAI` puts it in the system role
   - `optimizeWithOpenRouter` includes it in the user message
   - `optimizeWithFallback` puts part in system role and adds its own user message

3. **Hard-coded Prompts**: The server has hard-coded prompts that may conflict with or duplicate the client's instructions.

## Recommended Solutions

### Option 1: Complete Client-Side Message Construction

1. Modify the client to send a complete messages array instead of just a customPrompt string:

```typescript
const requestBody = {
    text, // Keep for reference
    model,
    messages: [
        {
            role: 'system',
            content: systemPrompt
        },
        {
            role: 'user', 
            content: formattedUserPrompt
        }
    ],
    // ...
};
```

2. Update the edge function to use this messages array directly without modification.

### Option 2: Raw Prompt Mode

1. Add a flag to indicate whether the server should use the prompt as-is:

```typescript
const requestBody = {
    text,
    model,
    customPrompt,
    rawPromptMode: true, // Signal server to use prompt as-is
    // ...
};
```

2. Update the edge function to check this flag and bypass its own prompt construction when true.

## Implementation Suggestions

1. First, add proper logging in `supabaseClient.ts` to show exactly what's being sent:

```typescript
console.log('VoiceHype: Full request body:', JSON.stringify(requestBody, null, 2));
```

2. Modify `index.ts` to accept and respect a complete messages array from the client.

3. Update the edge functions in `optimizeWithOpenRouter.ts` to use the client's messages when provided.

## Conclusion

The fundamental issue is that the prompt engineering is being done in two places: the client carefully crafts a detailed prompt, but the server doesn't use it directly and instead creates its own structure. This needs to be unified so that either:

1. The client sends the complete message structure, or
2. The server accepts and uses the raw prompt format from the client without modification

This will ensure that all the careful prompt engineering done on the client side is preserved when interacting with the LLMs.
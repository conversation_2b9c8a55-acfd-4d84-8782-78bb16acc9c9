# VoiceHype Migration Plan: AWS to DigitalOcean

## Overview

This document outlines the comprehensive plan for migrating VoiceHype's Supabase database and edge functions from AWS to DigitalOcean. The migration is motivated by ethical considerations to support Palestine by moving away from services that may provide financial support to i*rael.

The migration will be executed in three distinct phases:

1. **Phase One: Edge Functions Migration** - Rewriting and migrating edge functions to Digital Ocean's serverless functions using Node.js runtime
2. **Phase Two: Database Migration** - Migrating the Supabase database to Digital Ocean
3. **Phase Three: Testing** - Comprehensive testing of the migrated infrastructure

## Current Architecture

### Supabase Project
- **Project Name**: VoiceHype
- **Project ID**: nffixzoqnqxpcqpcxpps
- **Region**: ap-southeast-1 (AWS Singapore)
- **URL**: https://nffixzoqnqxpcqpcxpps.supabase.co

### Edge Functions
- `/transcribe`: Handles audio transcription using LemonFox (formerly OpenAI) and AssemblyAI
- `/optimize`: Handles text optimization using OpenRouter
- `/realtime-transcribe`: WebSocket endpoint for real-time transcription
- Other utility functions for token generation, usage tracking, etc.

### Database Schema
The database includes tables for:
- User profiles and authentication
- API key management
- Usage tracking and billing
- Subscription and credit management
- Service pricing and quotas

## Migration Strategy

Based on our revised approach, we will prioritize edge functions migration to Node.js first, followed by database migration, and finally comprehensive testing.

### Phase 1: Preparation and Planning

1. **Set up DigitalOcean account**
   - Create a new DigitalOcean account if not already available
   - Set up billing and payment methods
   - Configure team access and permissions

2. **Install required tools**
   - Supabase CLI for database migrations and edge function deployment
   - PostgreSQL client for database operations
   - Git for version control

3. **Create a new Supabase project on DigitalOcean**
   - Select DigitalOcean as the hosting provider
   - Choose an appropriate region (consider latency for your users)
   - Set up project with the same name (VoiceHype)

4. **Configure environment variables**
   - Document all current environment variables from AWS Supabase
   - Prepare equivalent variables for DigitalOcean Supabase

### Phase 2: Database Migration

1. **Export database schema and data**
   ```bash
   # Using Supabase CLI
   supabase db dump -f voicehype_schema.sql --db-url postgresql://postgres.nffixzoqnqxpcqpcxpps:<EMAIL>:6543/postgres
   ```

2. **Review and prepare schema**
   - Review the exported schema for any AWS-specific configurations
   - Modify any region-specific settings or references

3. **Import schema to DigitalOcean Supabase**
   ```bash
   # Using Supabase CLI
   supabase db push --db-url NEW_DIGITALOCEAN_DB_URL
   ```

4. **Verify database structure**
   - Check that all tables, functions, and triggers are correctly migrated
   - Verify RLS (Row Level Security) policies are properly configured
   - Test database functions to ensure they work as expected

5. **Migrate data**
   - For small datasets: Use direct SQL export/import
   - For larger datasets: Consider incremental migration strategies
   - Verify data integrity after migration

### Phase 3: Edge Function Migration to Node.js

As per our updated strategy, we will be rewriting our edge functions to use Node.js for Digital Ocean's serverless functions platform.

1. **Review and rewrite edge function code**
   - Convert Deno-based edge functions to Node.js
   - Check for any AWS-specific dependencies or configurations
   - Update any hardcoded URLs or region-specific code
   - Ensure compatibility with Digital Ocean's serverless functions environment

2. **Node.js Implementation Considerations**
   - Update import statements to use Node.js module syntax
   - Replace Deno-specific APIs with Node.js equivalents
   - Update environment variable handling to use Node.js patterns
   - Implement proper error handling for Node.js environment

3. **Deploy edge functions to DigitalOcean Serverless Functions**
   ```bash
   # Use Digital Ocean CLI to deploy functions
   doctl serverless connect
   doctl serverless deploy ./functions
   ```

4. **Set environment variables for edge functions**
   - Configure all required API keys and secrets:
     - SUPABASE_URL
     - SUPABASE_SERVICE_ROLE_KEY
     - ASSEMBLYAI_API_KEY
     - LEMONFOX_AI_API_KEY
     - OPENROUTER_API_KEY

5. **Test edge functions**
   - Verify each function works correctly on the new infrastructure
   - Test with sample data to ensure expected behavior
   - Check performance and latency
   - Compare performance with previous Deno implementation

### Phase 4: Client Application Updates

1. **Update client configurations**
   - Update Supabase URL and anon key in client applications:
     - VS Code extension
     - Web application
     - Any other clients

2. **Test client connections**
   - Verify authentication flows
   - Test API key validation
   - Check all service integrations

### Phase 5: DNS and Routing Updates

1. **Update DNS records**
   - If using custom domains, update DNS records to point to new infrastructure
   - Update any hardcoded URLs in documentation or client applications

2. **Configure SSL certificates**
   - Ensure SSL certificates are properly configured for secure connections
   - Test HTTPS endpoints

### Phase 6: Testing and Verification

1. **Comprehensive testing**
   - Test all critical user flows
   - Verify authentication and authorization
   - Test transcription and optimization services
   - Check real-time transcription functionality
   - Verify billing and usage tracking

2. **Performance testing**
   - Compare performance between AWS and DigitalOcean
   - Identify and address any performance issues

### Phase 7: Cutover and Go-Live

1. **Prepare cutover plan**
   - Schedule maintenance window if needed
   - Communicate changes to users
   - Prepare rollback plan in case of issues

2. **Execute cutover**
   - Switch client applications to new infrastructure
   - Monitor for any issues or errors
   - Be prepared to roll back if necessary

3. **Final data synchronization**
   - Perform final data sync from AWS to DigitalOcean
   - Verify data consistency

4. **Decommission AWS resources**
   - Once stable, plan for decommissioning AWS resources
   - Maintain backups of AWS data for a defined period

## Post-Migration Tasks

1. **Monitor and optimize**
   - Monitor performance and costs
   - Optimize configurations as needed
   - Address any issues that arise

2. **Documentation updates**
   - Update all documentation to reflect new infrastructure
   - Document lessons learned from migration

3. **User communication**
   - Inform users about the completed migration
   - Provide support for any issues related to the migration

## Rollback Plan

In case of critical issues during or after migration:

1. **Identify trigger for rollback**
   - Define clear criteria for when to initiate rollback
   - Monitor key metrics and error rates

2. **Rollback procedure**
   - Switch client applications back to AWS infrastructure
   - Sync any new data back to AWS if needed
   - Communicate rollback to users

3. **Post-rollback analysis**
   - Analyze what went wrong
   - Develop plan to address issues before attempting migration again

## Timeline and Resources

### Estimated Timeline
- **Preparation and Planning**: 1-2 days
- **Edge Function Migration to Node.js**: 2-3 days
- **Database Migration**: 1-2 days
- **Comprehensive Testing**: 1-2 days
- **Cutover and Go-Live**: 1 day
- **Total Estimated Time**: 6-10 days

### Required Resources
- DevOps engineer for infrastructure setup
- Backend developer for database and edge function migration
- Frontend developer for client application updates
- QA tester for comprehensive testing

## Node.js Conversion Guide

Converting our Deno-based edge functions to Node.js will require several key adjustments:

### Import Statements

**Deno (Current):**
```typescript
// @ts-ignore: Deno types
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
// @ts-ignore: Supabase types
import { createClient } from '@supabase/supabase-js';
```

**Node.js (Target):**
```typescript
import { createServer } from 'http';
import { createClient } from '@supabase/supabase-js';
```

### Environment Variables

**Deno (Current):**
```typescript
const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
```

**Node.js (Target):**
```typescript
import dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
```

### HTTP Request/Response Handling

**Deno (Current):**
```typescript
serve(async (req) => {
  // Handle request
  return new Response(JSON.stringify({ success: true }), {
    headers: { 'Content-Type': 'application/json' }
  });
});
```

**Node.js (Target):**
```typescript
const app = express();
app.use(express.json());

app.post('/transcribe', async (req, res) => {
  // Handle request
  res.json({ success: true });
});

app.listen(process.env.PORT || 3000);
```

### WebSocket Handling

**Deno (Current):**
```typescript
upgradeWebSocket(req, {
  onOpen(ws) {
    // Handle connection open
  },
  onMessage(ws, message) {
    // Handle message
  }
});
```

**Node.js (Target):**
```typescript
import { WebSocketServer } from 'ws';

const wss = new WebSocketServer({ server });

wss.on('connection', (ws) => {
  // Handle connection open
  ws.on('message', (message) => {
    // Handle message
  });
});
```

## Specific Files to Update

### Client Configuration Files
1. **VS Code Extension**
   - `extension/src/utils/supabaseClient.ts`: Update API URL
   - `extension/src/services/TranscriptionService.ts`: Update WebSocket URL

2. **Web Application**
   - `voicehype-website/.env.production`: Update Supabase URL and anon key
   - `voicehype-website/src/lib/supabase.ts`: Verify client initialization

### Test Files
1. **Edge Function Tests**
   - `supabase/functions/tests/test_transcribe.ts`: Update Supabase URL
   - `supabase/functions/tests/test_realtime.ts`: Update WebSocket URL

## Self-Hosting Supabase (Future Phase)

After successfully migrating to DigitalOcean-hosted Supabase, we can plan for self-hosting Supabase for complete control:

1. **Infrastructure requirements**
   - DigitalOcean Droplet or Kubernetes cluster
   - PostgreSQL database
   - Storage solution
   - Redis for caching

2. **Deployment options**
   - Docker Compose for simpler deployments
   - Kubernetes for more complex, scalable deployments

3. **Migration from managed Supabase to self-hosted**
   - Database export/import
   - Storage migration
   - Authentication configuration
   - Edge function deployment

A detailed plan for self-hosting will be developed as a separate document once the initial migration to DigitalOcean-hosted Supabase is complete.

## Conclusion

This migration plan provides a structured approach to moving VoiceHype's infrastructure from AWS to DigitalOcean. By prioritizing the edge functions migration to Node.js first, followed by database migration, and comprehensive testing, we aim to achieve a smooth transition with minimal disruption to users.

The decision to use Node.js for Digital Ocean's serverless functions will provide us with a robust, well-supported runtime environment while allowing us to maintain the functionality our users depend on. This migration aligns with our ethical considerations to support Palestine by moving away from services that may indirectly support i*rael.

By following this phased approach, we can methodically address each aspect of the migration, ensuring that our services remain reliable and performant throughout the transition process.

# Supabase External Email Service with Deno Deploy

**Date Created:** May 29, 2025

## Overview

This document outlines the solution for handling email sending from a self-hosted Supabase instance on DigitalOcean using an external email service built with Deno Deploy. This approach works around DigitalOcean's SMTP port restrictions (25, 465, 587) that prevent direct email sending.

## Problem

When running Supabase on a DigitalOcean droplet, the following issues occur with email authentication:

1. DigitalOcean blocks outgoing SMTP traffic on ports 25, 465, and 587 for security and spam prevention
2. This prevents Supabase's email authentication features from working properly
3. Authentication attempts time out with `"error":"context deadline exceeded"` and `"error_code":"request_timeout"` errors

## Solution Architecture

The solution uses a webhook approach with the following components:

1. **Deno Deploy Service**: A serverless function that handles email sending requests
2. **Supabase Auth Hook**: Configuration that redirects email requests to the Deno Deploy service
3. **GoDaddy SMTP**: Your existing email provider accessed through Deno Deploy

```
┌─────────────────┐        ┌────────────────┐        ┌───────────────┐
│  Supabase Auth  │  HTTP  │  Deno Deploy   │  SMTP  │  GoDaddy SMTP │
│    (GoTrue)     │───────▶│  Email Service │───────▶│     Server    │
└─────────────────┘        └────────────────┘        └───────────────┘
```

## Implementation Steps

### 1. Create a Deno Deploy Email Service

1. Sign up at [Deno Deploy](https://deno.com/deploy) and create a new project
2. Copy and paste the following code:

```typescript
// email-service.ts for Deno Deploy
import { serve } from "https://deno.land/std@0.140.0/http/server.ts";
import { SmtpClient } from "https://deno.land/x/smtp@v0.7.0/mod.ts";

const ALLOWED_ORIGINS = [
  "https://supabase.voicehype.ai",
  "https://voicehype.ai",
];

// Email configuration - Store these as Deno Deploy environment variables
const SMTP_HOST = Deno.env.get("SMTP_HOST") || "smtpout.secureserver.net";
const SMTP_PORT = parseInt(Deno.env.get("SMTP_PORT") || "465");
const SMTP_USER = Deno.env.get("SMTP_USER") || "<EMAIL>";
const SMTP_PASS = Deno.env.get("SMTP_PASS") || "";
const FROM_EMAIL = Deno.env.get("FROM_EMAIL") || "<EMAIL>";
const FROM_NAME = Deno.env.get("FROM_NAME") || "VoiceHype";

// Webhook secret for security
const WEBHOOK_SECRET = Deno.env.get("WEBHOOK_SECRET") || "";

async function sendEmail(emailData: any) {
  const client = new SmtpClient();
  
  await client.connectTLS({
    hostname: SMTP_HOST,
    port: SMTP_PORT,
    username: SMTP_USER,
    password: SMTP_PASS,
  });

  await client.send({
    from: `${FROM_NAME} <${FROM_EMAIL}>`,
    to: emailData.email,
    subject: emailData.subject,
    content: emailData.content,
    html: emailData.html,
  });

  await client.close();
  return { success: true };
}

serve(async (req) => {
  // CORS handling
  const origin = req.headers.get("Origin") || "";
  if (!ALLOWED_ORIGINS.includes(origin)) {
    return new Response("Not allowed", { status: 403 });
  }

  const corsHeaders = {
    "Access-Control-Allow-Origin": origin,
    "Access-Control-Allow-Methods": "POST, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization",
  };

  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  // Webhook secret validation
  const authHeader = req.headers.get("Authorization") || "";
  const providedSecret = authHeader.replace("Bearer ", "");
  
  if (WEBHOOK_SECRET && providedSecret !== WEBHOOK_SECRET) {
    return new Response("Unauthorized", { status: 401 });
  }

  if (req.method === "POST") {
    try {
      const data = await req.json();
      
      // Expected data structure from Supabase Auth hook
      const emailData = {
        email: data.email,
        subject: data.subject || "VoiceHype Notification",
        content: data.text || "",
        html: data.html || "",
      };
      
      await sendEmail(emailData);
      
      return new Response(JSON.stringify({ success: true }), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    } catch (error) {
      console.error("Email sending error:", error);
      return new Response(JSON.stringify({ error: error.message }), {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }
  }

  return new Response("Method not allowed", { status: 405, headers: corsHeaders });
});
```

3. Deploy the code and note your deployed URL (e.g., `https://your-email-service.deno.dev`)

### 2. Configure Environment Variables in Deno Deploy

Set the following environment variables in your Deno Deploy project settings:

| Variable | Value | Description |
|----------|-------|-------------|
| SMTP_HOST | smtpout.secureserver.net | GoDaddy SMTP server |
| SMTP_PORT | 465 | Secure SMTP port |
| SMTP_USER | <EMAIL> | Your email address |
| SMTP_PASS | bilal2005 | Your email password |
| FROM_EMAIL | <EMAIL> | Sender email address |
| FROM_NAME | VoiceHype | Sender name |
| WEBHOOK_SECRET | whsec_YourWebhookSecretHere1234567890 | Generate a random string for security |

⚠️ **Important**: Generate a strong random string for the `WEBHOOK_SECRET`. This is used to authenticate requests between Supabase and your Deno service.

### 3. Update Supabase Configuration

#### Modify `.env` File

Update your Supabase `.env` file with the following changes:

```bash
## Email auth
ENABLE_EMAIL_SIGNUP=true
ENABLE_EMAIL_AUTOCONFIRM=false
SMTP_ADMIN_EMAIL=<EMAIL>
SMTP_SENDER_NAME=VoiceHype
ENABLE_ANONYMOUS_USERS=false

# Using external email service instead of direct SMTP
# Comment out direct SMTP settings since DO blocks these ports
# SMTP_HOST=smtpout.secureserver.net
# SMTP_PORT=465
# SMTP_USER=<EMAIL>
# SMTP_PASS=bilal2005

# Enable the external email service hook
GOTRUE_HOOK_SEND_EMAIL_ENABLED=true
# Your Deno Deploy URL - replace with your actual deployed URL
GOTRUE_HOOK_SEND_EMAIL_URI=https://your-email-service.deno.dev
# Create a webhook secret for security - use the same value in Deno Deploy env vars
GOTRUE_HOOK_SEND_EMAIL_SECRETS=whsec_YourWebhookSecretHere1234567890

# Additional configs to fix the host warning in logs
GOTRUE_MAILER_EXTERNAL_HOSTS=supabase.voicehype.ai,voicehype.ai
```

⚠️ **Important**: Replace `https://your-email-service.deno.dev` with your actual Deno Deploy URL.

#### Update `docker-compose.yml`

Ensure your `docker-compose.yml` file includes the following environment variables in the `auth` service section:

```yaml
# Enable the external email service hook
GOTRUE_HOOK_SEND_EMAIL_ENABLED: ${GOTRUE_HOOK_SEND_EMAIL_ENABLED:-"false"}
GOTRUE_HOOK_SEND_EMAIL_URI: ${GOTRUE_HOOK_SEND_EMAIL_URI}
GOTRUE_HOOK_SEND_EMAIL_SECRETS: ${GOTRUE_HOOK_SEND_EMAIL_SECRETS}
GOTRUE_MAILER_EXTERNAL_HOSTS: ${GOTRUE_MAILER_EXTERNAL_HOSTS}
```

### 4. Restart Supabase Services

After updating the configuration files, restart your Supabase services:

```bash
cd /path/to/your/docker
docker-compose down
docker-compose up -d
```

### 5. Test the Configuration

1. Try to sign up a new user using email authentication
2. Check for any errors in the auth logs:

```bash
docker logs supabase-auth
```

## Troubleshooting

### Common Issues

1. **Webhook Secret Format**
   - For GoTrue (Supabase Auth), the webhook secret must be formatted as `v1,whsec_YourSecretHere`
   - In your Deno Deploy service, the code should handle both the raw secret and the one with `v1,` prefix

2. **403 Forbidden Errors**
   - Webhook calls from GoTrue may not include an Origin header, so your Deno service should not strictly enforce CORS for these calls
   - Check the Deno Deploy logs to see if CORS is causing request rejection

3. **SMTP Configuration Required**
   - Even when using external email service, GoTrue still requires valid SMTP settings
   - Use placeholder values like `localhost` for host, `25` for port, and `placeholder` for username/password

4. **Email Not Received**
   - Check the Deno Deploy logs for any errors
   - Verify your SMTP credentials are correct
   - Check if the email is in spam/junk folders

5. **Continued Timeout Errors**
   - Verify your Deno Deploy service is running correctly
   - Check that the URL in `GOTRUE_HOOK_SEND_EMAIL_URI` is correct
   - Increase the timeout setting if needed with `GOTRUE_SMTP_MAX_FREQUENCY=15s`

### Debugging Tips

1. Check Supabase Auth logs:
```bash
docker logs supabase-auth
```

2. Check Deno Deploy logs in the Deno Deploy dashboard

3. Test the Deno Deploy service directly with curl:
```bash
curl -X POST https://your-email-service.deno.dev \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer whsec_YourWebhookSecretHere1234567890" \
  -d '{"email":"<EMAIL>", "subject":"Test Email", "text":"This is a test", "html":"<p>This is a test</p>"}'
```

## Security Considerations

1. **Webhook Secret**: The webhook secret prevents unauthorized access to your email service. Keep it secure and don't share it.

2. **SMTP Credentials**: Your SMTP credentials are stored in Deno Deploy environment variables. Ensure your Deno Deploy account is properly secured.

3. **CORS Protection**: The email service only accepts requests from specified origins (`ALLOWED_ORIGINS`). Update this list if your domain changes.

## Benefits of This Approach

1. **Bypasses DigitalOcean SMTP Restrictions**: The email sending occurs on Deno Deploy, not on the DigitalOcean droplet.

2. **Secure**: Uses a webhook secret to authenticate requests.

3. **Reliable**: Deno Deploy has excellent uptime and performance metrics.

4. **Cost-Effective**: Deno Deploy offers a generous free tier suitable for most authentication email volumes.

5. **Maintainable**: Simple code that's easy to update if needed.

## Further Improvements

1. **Add Logging**: Consider adding more detailed logging in the Deno Deploy service.

2. **Implement Rate Limiting**: Add rate limiting to prevent abuse of the email service.

3. **Email Templates**: Enhance the service to support custom email templates.

4. **Failover Provider**: Add support for alternative email providers as a backup.

## References

- [Supabase Auth Hooks Documentation](https://supabase.com/docs/reference/javascript/auth-hook-send-email)
- [Deno Deploy Documentation](https://deno.com/deploy/docs)
- [DigitalOcean SMTP Restriction Documentation](https://docs.digitalocean.com/products/networking/firewalls/outbound-restrictions/)

# WebView to Vue.js Migration Guide

## Overview
The current webview is built with:
- React 18
- TypeScript
- Tailwind CSS
- VSCode Webview API

## Project Structure
Key directories:
- src/
  - components/ - UI components
  - utilities/ - Shared utilities
- public/ - Static assets
- build/ - Production build files
- config/ - Configuration files

## Files to Migrate

### Components
1. App.tsx - Main application component
2. RecordingControls.tsx - Audio recording UI  
3. ModelSelector.tsx - Model selection
4. LanguageSelector.tsx - Language selection
5. PromptModeSelector.tsx - Prompt mode management
6. Button.tsx - Reusable button component
7. AudioSettings.tsx - Audio configuration
8. RecentTranscriptions.tsx - Transcription history

### Utilities
1. vscode.ts - VSCode messaging utilities

### Configuration
1. tailwind.config.js - Tailwind CSS config
2. tsconfig.json - TypeScript config
3. postcss.config.js - PostCSS config

### Build Files
1. package.json - Dependencies and scripts
2. package-lock.json - Dependency locks
3. build/ files - Production build output

## Migration Strategy

### Component Migration
- Convert React components to Vue
- Use Composition API for complex logic
- Maintain existing styling with Tailwind

### State Management
- Use Pinia stores for shared state
- Create stores for:
  - useRecordingStore - Recording state
  - useSettingsStore - Model/Language config
  - usePromptStore - Prompt modes  

### Utility Migration
- Convert vscode.ts to Vue helper
- Maintain same message handling logic

### Configuration
- Update config files for Vue
- Keep same Tailwind setup
- Add Vue-specific TypeScript config

### Build Process
- Switch to Vue build system
- Update production build configuration
- Maintain CI/CD pipeline

## Testing
1. Unit tests for components
2. Integration tests for stores
3. End-to-end tests for workflows

## Timeline
Estimated 2 weeks for complete migration

## Post-Migration
1. Performance optimization
2. Code cleanup
3. Documentation updates
# Voice Command Implementation Plan for Voice Hype

## Overview

This document outlines our strategy for implementing voice command functionality in the Voice Hype extension. Rather than manually processing and extracting commands from transcripts, we will leverage the LLM's inherent language understanding capabilities by modifying the prompts we send to it.

## Current System Analysis

The Voice Hype extension currently:
1. Allows users to select pre-defined prompt modes (Clean Up, Email Format, etc.)
2. Processes transcriptions using the selected mode's prompt template
3. Sends the transcript and prompt to an LLM for optimization

## Voice Command Implementation Strategy

### Core Approach: Prompt Engineering

Instead of building a separate command detection system, we will modify our prompts to instruct the LLM to:

1. Recognize when users address "Voice Hype" directly in their transcriptions
2. Prioritize instructions that follow the "Voice Hype" keyword over the default prompt mode
3. Process multiple commands and output formats when requested

### Implementation Steps

1. **Modify Prompt Structure**
   - Add a section to all prompts that instructs the LLM to detect and handle voice commands
   - Create a common command detection preamble for all prompt types

2. **Update the TranscriptionService**
   - Enhance `processPromptVariables` to include the command detection instructions
   - Ensure the prompt hierarchy is clear (Voice commands > Selected mode > Default processing)

3. **Enhance PromptModeSelector**
   - Add a UI element indicating that voice commands are available
   - Include examples of supported commands in the UI

4. **Update Documentation & User Instructions**
   - Create clear examples of supported voice commands
   - Document the voice command functionality in the extension readme

## Example Prompt Modifications

For each prompt type, we will add a voice command detection section:

```
### VOICE COMMAND DETECTION ###
If the transcript contains phrases like "Voice Hype, [instruction]" or "Hey Voice Hype, [instruction]", interpret these as direct commands that override the default instructions.

Examples of voice commands to recognize:
- "Voice Hype, format this as an email instead" → Switch to email format mode
- "Voice Hype, provide both WhatsApp and email versions" → Generate multiple formats
- "Voice Hype, pay attention to the technical terms" → Focus on preserving technical terminology

PRIORITY ORDER:
1. Voice commands in the transcript (highest priority)
2. The selected optimization mode (medium priority)
3. Default optimization rules (lowest priority)

If a voice command is detected:
1. Remove the command phrase from the final output
2. Follow the instruction in the command instead of or in addition to the default mode
3. If multiple formats are requested, provide them clearly separated
```

## Supported Voice Commands

### Mode Switching Commands
- "Voice Hype, format this as an email"
- "Voice Hype, make this into bullet points"
- "Voice Hype, translate this to [language]"

### Multi-Format Commands
- "Voice Hype, give me both a formal and casual version"
- "Voice Hype, provide this as WhatsApp and email formats"

### Special Formatting Instructions
- "Voice Hype, preserve all technical terms"
- "Voice Hype, keep the original structure"
- "Voice Hype, emphasize the key points"

## Advantages of This Approach

1. **Simplicity**: No need for complex command parsing logic
2. **Flexibility**: The LLM can understand a wide range of natural language commands
3. **Extensibility**: Easy to add new command types without code changes
4. **User Experience**: More intuitive for users to simply speak their requirements

## Technical Implementation Details

### TranscriptionService.ts Modifications

The key modification will be in the `processPromptVariables` method to include the voice command detection instructions in all prompts:

```typescript
private processPromptVariables(prompt: string, transcript: string): string {
  // Add voice command detection instructions to all prompts
  const voiceCommandInstructions = `
### VOICE COMMAND DETECTION ###
If the transcript contains phrases like "Voice Hype, [instruction]" or "Hey Voice Hype, [instruction]", interpret these as direct commands that override the default instructions / prompt.
...
  `;
  
  // Combine with existing prompt structure
  // ...existing implementation...
}
```

### Future Enhancements

1. **Command History**: Store successful voice commands to improve future recognition
2. **Custom Command Shortcuts**: Allow users to define their own voice command shortcuts
3. **Visual Feedback**: Indicate in the UI when voice commands are detected and processed
4. **Command Suggestions**: Provide suggestions for available voice commands

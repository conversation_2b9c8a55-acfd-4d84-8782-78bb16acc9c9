# VoiceHype Subscription Management - SIMPLE VERSION

**Created:** June 27, 2025  
**Status:** Super Simple Planning  
**Approach:** Bare bones, no complications

---

## 🎯 **Simple Rules**

1. **Cancel** = End of period, no refunds, keep access till end
2. **Upgrade/Downgrade** = Credit-based proration (reduce charge based on unused value)
3. **Failed Payment** = Immediate pause, no grace period
4. **Auto Cancel** = After 2 months of pause (future cron job)

## 💡 **Proration Formula**

**Simple Weight-Based Approach (Your Method):**
1. Calculate service weights: Transcription 71.8%, Optimization 28.2%
2. Calculate transcription used cost = `plan_price × 0.718 × (minutes_used / total_minutes)`
3. Calculate optimization used cost = `plan_price × 0.282 × (tokens_used / total_tokens)`
4. Total used cost = transcription_used_cost + optimization_used_cost
5. Unused credit = `plan_price - total_used_cost`
6. Final charge = `new_plan_price - unused_credit`

**Your Example Calculation:**
User on Basic plan ($9, 720 mins, 400K tokens) used 360 minutes (50%) + 120,000 tokens (30%):
- Transcription used cost: $9 × 0.718 × (360/720) = $9 × 0.718 × 0.5 = $3.23
- Optimization used cost: $9 × 0.282 × (120,000/400,000) = $9 × 0.282 × 0.3 = $0.76
- Total used cost: $3.23 + $0.76 = $3.99
- Unused credit: $9 - $3.99 = $5.01
- Upgrading to Pro ($18): Pay $18 - $5.01 = $12.99

**Why Your Method is Better:** Much simpler to understand and calculate! 🎯

---

## 🔧 **What We Need to Change**

### **1. Database Changes**

#### **Add 1 Column to user_subscriptions**
```sql
ALTER TABLE user_subscriptions 
ADD COLUMN cancel_at_period_end BOOLEAN DEFAULT FALSE;
```

#### **Add 1 Function for Simple Proration**
```sql
CREATE OR REPLACE FUNCTION calculate_proration_discount(
    p_user_id UUID,
    p_current_plan_price DECIMAL(10,2),
    p_new_plan_price DECIMAL(10,2)
) RETURNS JSONB AS $$
DECLARE
    v_current_transcription_total INTEGER;
    v_current_transcription_used INTEGER;
    v_current_optimization_total INTEGER;
    v_current_optimization_used INTEGER;
    
    -- Financial calculations
    v_transcription_used_cost DECIMAL(10,2);
    v_optimization_used_cost DECIMAL(10,2);    v_total_used_cost DECIMAL(10,2);
    v_unused_credit DECIMAL(10,2);
    v_proration_discount DECIMAL(10,2);
    v_final_charge DECIMAL(10,2);
    v_excess_credit DECIMAL(10,2);
BEGIN
    -- Get current quota usage
    SELECT total_amount, used_amount 
    INTO v_current_transcription_total, v_current_transcription_used
    FROM quotas WHERE user_id = p_user_id AND service = 'transcription';
    
    SELECT total_amount, used_amount 
    INTO v_current_optimization_total, v_current_optimization_used
    FROM quotas WHERE user_id = p_user_id AND service = 'optimization';
    
    -- Calculate used cost using your weight-based method
    -- Weights: Transcription 71.8%, Optimization 28.2% (from cost analysis)
    DECLARE
        v_transcription_used_cost DECIMAL(10,2);
        v_optimization_used_cost DECIMAL(10,2);
        v_total_used_cost DECIMAL(10,2);
    BEGIN
        -- Calculate transcription used cost: plan_price × 0.718 × (used/total)
        v_transcription_used_cost := p_current_plan_price * 0.718 * 
            (v_current_transcription_used::DECIMAL / NULLIF(v_current_transcription_total::DECIMAL, 0));
        
        -- Calculate optimization used cost: plan_price × 0.282 × (used/total)
        v_optimization_used_cost := p_current_plan_price * 0.282 * 
            (v_current_optimization_used::DECIMAL / NULLIF(v_current_optimization_total::DECIMAL, 0));
        
        -- Total used cost
        v_total_used_cost := COALESCE(v_transcription_used_cost, 0) + COALESCE(v_optimization_used_cost, 0);
          -- Calculate unused credit and final charge
        v_unused_credit := p_current_plan_price - v_total_used_cost;
        v_proration_discount := LEAST(v_unused_credit, p_new_plan_price);
        v_final_charge := GREATEST(0, p_new_plan_price - v_proration_discount);
        
        -- Calculate excess credit to convert to VoiceHype credits
        v_excess_credit := GREATEST(0, v_unused_credit - p_new_plan_price);
    END;
      -- Return proration calculation using your method
    RETURN jsonb_build_object(
        'success', true,
        'proration_details', jsonb_build_object(
            'transcription_used_cost', ROUND(COALESCE(v_transcription_used_cost, 0), 2),
            'optimization_used_cost', ROUND(COALESCE(v_optimization_used_cost, 0), 2),
            'total_used_cost', ROUND(v_total_used_cost, 2),
            'original_plan_price', p_current_plan_price,
            'unused_credit', ROUND(v_unused_credit, 2),
            'new_plan_price', p_new_plan_price,
            'proration_discount', ROUND(v_proration_discount, 2),
            'final_charge_amount', ROUND(v_final_charge, 2),
            'excess_credit_to_convert', ROUND(v_excess_credit, 2),
            'will_receive_voicehype_credits', v_excess_credit > 0
        )
    );
END;
$$ LANGUAGE plpgsql;
```

#### **Add Function to Convert Excess Credits**
```sql
CREATE OR REPLACE FUNCTION convert_excess_credit_to_voicehype_credits(
    p_user_id UUID,
    p_excess_amount DECIMAL(10,2),
    p_reason TEXT DEFAULT 'Plan downgrade excess credit conversion'
) RETURNS JSONB AS $$
DECLARE
    v_credit_id UUID;
    v_expiry_date TIMESTAMPTZ;
BEGIN
    -- Only proceed if there's actually excess credit to convert
    IF p_excess_amount <= 0 THEN
        RETURN jsonb_build_object(
            'success', false,
            'message', 'No excess credit to convert'
        );
    END IF;
    
    -- Set expiry date to 3 months from now (same as purchased credits)
    v_expiry_date := NOW() + INTERVAL '3 months';
    
    -- Insert new VoiceHype credit
    INSERT INTO credits (
        user_id,
        balance,
        currency,
        expires_at,
        status,
        metadata
    ) VALUES (
        p_user_id,
        p_excess_amount,
        'USD',
        v_expiry_date,
        'active',
        jsonb_build_object(
            'source', 'subscription_proration',
            'reason', p_reason,
            'created_from', 'plan_change'
        )
    ) RETURNING id INTO v_credit_id;
    
    RETURN jsonb_build_object(
        'success', true,
        'credit_id', v_credit_id,
        'amount_converted', p_excess_amount,
        'expires_at', v_expiry_date,
        'message', format('Successfully converted $%.2f to VoiceHype credits', p_excess_amount)
    );
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', SQLERRM,
            'message', 'Failed to convert excess credit'
        );
END;
$$ LANGUAGE plpgsql;
```

#### **Update check_usage_allowance Function**
```sql
-- Add this check to existing function
IF subscription_status = 'paused' THEN
    RETURN jsonb_build_object(
        'allowed', false,
        'reason', 'subscription_paused',
        'message', 'Your subscription is paused. Please update your payment method.'
    );
END IF;
```

---

## 🎣 **Edge Function Changes**

### **Add to paddle-webhook/index.ts**

```typescript
// Add these cases to the existing switch statement

case 'subscription.updated':
  // Check for cancellation
  if (data.cancel_at_period_end === true) {
    await supabase
      .from('user_subscriptions')
      .update({ cancel_at_period_end: true })
      .eq('stripe_subscription_id', data.id)
  }
    // Check for plan change (upgrade/downgrade)
  const customData = data.custom_data || {}
  if (customData.previous_plan && customData.subscription_plan) {
    // Calculate proration discount
    const prorationResult = await supabase.rpc('calculate_proration_discount', {
      p_user_id: customData.user_id,
      p_current_plan_price: customData.current_plan_price,
      p_new_plan_price: customData.new_plan_price
    })
    
    // Convert excess credits to VoiceHype credits if there are any
    if (prorationResult.data?.proration_details?.excess_credit_to_convert > 0) {
      const excessAmount = prorationResult.data.proration_details.excess_credit_to_convert
      const creditResult = await supabase.rpc('convert_excess_credit_to_voicehype_credits', {
        p_user_id: customData.user_id,
        p_excess_amount: excessAmount,
        p_reason: `Plan change from ${customData.previous_plan} to ${customData.subscription_plan}`
      })
      
      console.log('Credit conversion result:', creditResult)
    }
    
    // Update quotas to new plan (reset, don't merge)
    await resetQuotasToNewPlan(customData.user_id, customData.subscription_plan)
  }
  processed = true
  break;

case 'transaction.payment_failed':
  // Payment failed during renewal - immediate pause
  const customData = data.custom_data || {}
  if (customData.user_id) {
    await supabase
      .from('user_subscriptions')
      .update({ status: 'paused' })
      .eq('user_id', customData.user_id)
  }
  processed = true
  break;

case 'subscription.past_due':
  // Also handle past_due as backup (some cases might use this)
  await supabase
    .from('user_subscriptions')
    .update({ status: 'paused' })
    .eq('stripe_subscription_id', data.id)
  processed = true
  break;

case 'subscription.resumed':
  // User fixed payment - resume
  await supabase
    .from('user_subscriptions')
    .update({ status: 'active' })
    .eq('stripe_subscription_id', data.id)
  processed = true
  break;

// Helper function for resetting quotas to new plan
async function resetQuotasToNewPlan(userId: string, newPlan: string) {
  const planQuotas = {
    'basic': { transcription: 720, optimization: 400000 },
    'pro': { transcription: 1440, optimization: 800000 },
    'premium': { transcription: 2160, optimization: 1200000 }
  }
  
  const quotas = planQuotas[newPlan.toLowerCase()]
  if (!quotas) return
  
  await supabase
    .from('quotas')
    .update({
      total_amount: quotas.transcription,
      used_amount: 0,
      reset_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
    })
    .eq('user_id', userId)
    .eq('service', 'transcription')
    
  await supabase
    .from('quotas')
    .update({
      total_amount: quotas.optimization,
      used_amount: 0,
      reset_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
    })
    .eq('user_id', userId)
    .eq('service', 'optimization')
}
```

**Important:** This webhook is called BY Paddle automatically when events happen. We don't call it ourselves!

**That's it for backend!** ✅

---

## 🎨 **Frontend Changes**

### **1. Cancel Button Component**

#### **File:** `voicehype-website/src/components/subscription/CancelButton.vue`
```vue
<template>
  <div>
    <button 
      @click="showConfirm = true"
      class="btn-danger"
      :disabled="loading"
    >
      Cancel Subscription
    </button>
    
    <!-- Confirmation Modal -->
    <div v-if="showConfirm" class="modal">
      <div class="modal-content">
        <h3>Cancel Subscription?</h3>
        <p>Your subscription will remain active until {{ currentPeriodEnd }}.</p>
        <p>No refund will be provided.</p>
        
        <div class="modal-actions">
          <button @click="showConfirm = false">Keep Subscription</button>
          <button @click="confirmCancel" :disabled="loading">
            Yes, Cancel at Period End
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useSubscriptionStore } from '@/stores/subscription'

const subscriptionStore = useSubscriptionStore()
const showConfirm = ref(false)
const loading = ref(false)

const props = defineProps({
  currentPeriodEnd: String
})

async function confirmCancel() {
  loading.value = true
  try {
    // Call Paddle API to cancel at period end
    await subscriptionStore.cancelSubscription()
    showConfirm.value = false
    // Show success message
  } catch (error) {
    console.error('Cancel failed:', error)
  } finally {
    loading.value = false
  }
}
</script>
```

### **2. Upgrade/Downgrade Buttons with Credit Preview**

#### **File:** `voicehype-website/src/components/subscription/PlanChangeButtons.vue`
```vue
<template>
  <div class="plan-change-buttons">
    <!-- Show upgrade options -->
    <div v-if="canUpgrade" class="upgrade-options">
      <h4>Upgrade Your Plan</h4>
      <button 
        v-for="plan in upgradePlans" 
        :key="plan.name"
        @click="showPlanChangePreview(plan)"
        class="btn-upgrade"
        :disabled="loading"
      >
        Upgrade to {{ plan.name }} - ${{ plan.price }}/month
      </button>
    </div>
    
    <!-- Show downgrade options -->
    <div v-if="canDowngrade" class="downgrade-options">
      <h4>Downgrade Your Plan</h4>
      <button 
        v-for="plan in downgradePlans" 
        :key="plan.name"
        @click="showPlanChangePreview(plan)"
        class="btn-downgrade"
        :disabled="loading"
      >
        Downgrade to {{ plan.name }} - ${{ plan.price }}/month
      </button>
    </div>
    
    <!-- Plan Change Preview Modal -->
    <div v-if="showPreview" class="modal">
      <div class="modal-content">
        <h3>{{ isUpgrade ? 'Upgrade' : 'Downgrade' }} Plan Preview</h3>
        
        <div class="breakdown-section">
          <h4>Proration Breakdown:</h4>
          <div class="breakdown-item">
            <span>Current plan ({{ currentPlan.name }}):</span>
            <span>${{ prorationDetails.original_plan_price }}</span>
          </div>
          <div class="breakdown-item">
            <span>Used value:</span>
            <span>${{ prorationDetails.total_used_cost }}</span>
          </div>
          <div class="breakdown-item">
            <span>Unused credit:</span>
            <span>${{ prorationDetails.unused_credit }}</span>
          </div>
          <div class="breakdown-item">
            <span>New plan ({{ selectedPlan.name }}):</span>
            <span>${{ prorationDetails.new_plan_price }}</span>
          </div>
          <div class="breakdown-item total">
            <span>You pay today:</span>
            <span>${{ prorationDetails.final_charge_amount }}</span>
          </div>
          
          <!-- Show VoiceHype credit conversion if applicable -->
          <div v-if="prorationDetails.will_receive_voicehype_credits" class="credit-bonus">
            <div class="credit-item">
              <span>🎉 Bonus VoiceHype Credits:</span>
              <span>+${{ prorationDetails.excess_credit_to_convert }}</span>
            </div>
            <p class="credit-explanation">
              Since your unused credit exceeds the new plan price, 
              we'll convert the extra ${{ prorationDetails.excess_credit_to_convert }} 
              to VoiceHype credits (expires in 3 months).
            </p>
          </div>
        </div>
        
        <div class="modal-actions">
          <button @click="closePreview">Cancel</button>
          <button @click="confirmPlanChange" :disabled="loading">
            Confirm {{ isUpgrade ? 'Upgrade' : 'Downgrade' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useSubscriptionStore } from '@/stores/subscription'

const subscriptionStore = useSubscriptionStore()
const loading = ref(false)
const showPreview = ref(false)
const selectedPlan = ref(null)
const prorationDetails = ref(null)

const props = defineProps({
  currentPlan: Object,
  allPlans: Array
})

const upgradePlans = computed(() => {
  return props.allPlans.filter(plan => plan.price > props.currentPlan.price)
})

const downgradePlans = computed(() => {
  return props.allPlans.filter(plan => plan.price < props.currentPlan.price)
})

const canUpgrade = computed(() => upgradePlans.value.length > 0)
const canDowngrade = computed(() => downgradePlans.value.length > 0)

const isUpgrade = computed(() => {
  return selectedPlan.value && selectedPlan.value.price > props.currentPlan.price
})

async function showPlanChangePreview(newPlan) {
  loading.value = true
  selectedPlan.value = newPlan
  
  try {
    // Calculate proration preview
    const preview = await subscriptionStore.calculateProrationPreview(newPlan, props.currentPlan)
    prorationDetails.value = preview.proration_details
    showPreview.value = true
  } catch (error) {
    console.error('Failed to calculate proration:', error)
  } finally {
    loading.value = false
  }
}

function closePreview() {
  showPreview.value = false
  selectedPlan.value = null
  prorationDetails.value = null
}

async function confirmPlanChange() {
  loading.value = true
  try {
    await subscriptionStore.changePlan(selectedPlan.value, props.currentPlan)
    closePreview()
    // Show success message
  } catch (error) {
    console.error('Plan change failed:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.breakdown-section {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  margin: 0.5rem 0;
}

.breakdown-item.total {
  font-weight: bold;
  border-top: 1px solid #ddd;
  padding-top: 0.5rem;
  margin-top: 1rem;
}

.credit-bonus {
  background: #e8f5e8;
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
  border: 1px solid #28a745;
}

.credit-item {
  display: flex;
  justify-content: space-between;
  font-weight: bold;
  color: #28a745;
  margin-bottom: 0.5rem;
}

.credit-explanation {
  font-size: 0.9rem;
  color: #666;
  margin: 0;
}
</style>
```

### **3. Payment Failed/Paused Alert**

#### **File:** `voicehype-website/src/components/subscription/PaymentAlert.vue`
```vue
<template>
  <div v-if="subscription.status === 'paused'" class="payment-alert">
    <div class="alert-danger">
      <h3>⚠️ Subscription Paused</h3>
      <p>Your payment failed. Please update your payment method to resume service.</p>
      
      <button @click="updatePayment" class="btn-primary">
        Update Payment Method
      </button>
    </div>
  </div>
  
  <div v-else-if="subscription.cancel_at_period_end" class="cancellation-alert">
    <div class="alert-warning">
      <h3>📅 Subscription Canceling</h3>
      <p>Your subscription will end on {{ subscription.current_period_end }}.</p>
      
      <button @click="undoCancel" class="btn-secondary">
        Undo Cancellation
      </button>
    </div>
  </div>
</template>

<script setup>
import { useSubscriptionStore } from '@/stores/subscription'

const subscriptionStore = useSubscriptionStore()

const props = defineProps({
  subscription: Object
})

function updatePayment() {
  // Redirect to Paddle payment update URL
  window.open(subscription.management_urls.update_payment_method, '_blank')
}

async function undoCancel() {
  try {
    await subscriptionStore.undoCancellation()
    // Show success message
  } catch (error) {
    console.error('Undo cancel failed:', error)
  }
}
</script>
```

### **4. Update Subscription Store**

#### **File:** `voicehype-website/src/stores/subscription.ts`
```typescript
// Add these methods to existing subscription store

async calculateProrationPreview(newPlan, currentPlan) {
  try {
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      throw new Error('No session')
    }

    const prorationResult = await supabase.rpc('calculate_proration_discount', {
      p_user_id: session.user.id,
      p_current_plan_price: currentPlan.price,
      p_new_plan_price: newPlan.price
    })

    if (prorationResult.error) {
      throw new Error(prorationResult.error.message)
    }

    return prorationResult.data
  } catch (error) {
    console.error('Proration preview failed:', error)
    throw new Error('Failed to calculate proration preview')
  }
},

async cancelSubscription() {
  // Use Paddle JS SDK to cancel subscription at period end
  const paddle = window.Paddle
  
  try {
    await paddle.Subscription.cancel(this.currentSubscription.paddle_subscription_id, {
      effective_from: 'next_billing_period'
    })
    
    // Update local state
    this.currentSubscription.cancel_at_period_end = true
  } catch (error) {
    console.error('Paddle cancel failed:', error)
    throw new Error('Cancel failed')
  }
},

async changePlan(newPlan, currentPlan) {
  // Use Paddle JS SDK to change subscription
  const paddle = window.Paddle
  
  try {
    await paddle.Subscription.update(this.currentSubscription.paddle_subscription_id, {
      price_id: newPlan.paddle_price_id,
      proration_billing_mode: 'prorated_immediately',
      custom_data: {
        user_id: this.user.id,
        subscription_plan: newPlan.name.toLowerCase(),
        previous_plan: currentPlan.name.toLowerCase(),
        current_plan_price: currentPlan.price,
        new_plan_price: newPlan.price
      }
    })
    
    // Update local state
    this.currentSubscription.plan_id = newPlan.id
    this.currentSubscription.price = newPlan.price
  } catch (error) {
    console.error('Paddle plan change failed:', error)
    throw new Error('Plan change failed')
  }
},

async undoCancellation() {
  // Use Paddle JS SDK to resume subscription
  const paddle = window.Paddle
  
  try {
    await paddle.Subscription.resume(this.currentSubscription.paddle_subscription_id)
    
    // Update local state
    this.currentSubscription.cancel_at_period_end = false
  } catch (error) {
    console.error('Paddle resume failed:', error)
    throw new Error('Undo cancel failed')
  }
}
```

### **5. Add to Payments View**

#### **File:** `voicehype-website/src/views/PaymentsView.vue`
```vue
<template>
  <div class="payments-view">
    <!-- Existing subscription cards... -->
    
    <!-- Add alert for paused/canceled subscriptions -->
    <PaymentAlert :subscription="currentSubscription" />
    
    <!-- Existing current subscription display... -->
    
    <!-- Add subscription management section -->
    <div v-if="hasActiveSubscription" class="subscription-management">
      <h3>Manage Your Subscription</h3>
      
      <!-- Plan change buttons -->
      <PlanChangeButtons 
        :current-plan="currentPlan" 
        :all-plans="subscriptionPlans" 
      />
      
      <!-- Cancel button -->
      <CancelButton :current-period-end="currentSubscription.current_period_end" />
    </div>
    
    <!-- Existing payment history, etc... -->
  </div>
</template>

<script setup>
import PaymentAlert from '@/components/subscription/PaymentAlert.vue'
import PlanChangeButtons from '@/components/subscription/PlanChangeButtons.vue'
import CancelButton from '@/components/subscription/CancelButton.vue'
// ...existing imports...
</script>
```

---

## 🎯 **Summary: What to Implement**

### **Database (10 minutes):**
1. ✅ Add `cancel_at_period_end` column
2. ✅ Add `calculate_proration_discount` function (updated with excess credit calculation)
3. ✅ Add `convert_excess_credit_to_voicehype_credits` function (NEW)
4. ✅ Update `check_usage_allowance` function

### **Backend (15 minutes):**
1. ✅ Add 4 webhook cases to existing edge function:
   - `subscription.updated` (cancellation + plan changes + credit conversion)
   - `transaction.payment_failed` (renewal payment failed)  
   - `subscription.past_due` (backup case)
   - `subscription.resumed` (payment fixed)

### **Frontend (60 minutes):**
1. ✅ `CancelButton.vue` - Simple cancel with confirmation
2. ✅ `PlanChangeButtons.vue` - Upgrade/downgrade buttons with proration preview
3. ✅ `PaymentAlert.vue` - Show paused/canceled status
4. ✅ Update subscription store with proration preview API calls
5. ✅ Add components to PaymentsView

### **API Endpoints (Not Needed - Use Paddle JS SDK):**
- ~~Cancel subscription~~ → Use `Paddle.Subscription.cancel()`
- ~~Change subscription~~ → Use `Paddle.Subscription.update()`  
- ~~Undo cancellation~~ → Use `Paddle.Subscription.resume()`

**Note:** Make sure Paddle JS SDK is loaded in your app:
```html
<script src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>
<script>
  Paddle.Environment.set('sandbox') // or 'production'
  Paddle.Setup({ vendor: YOUR_VENDOR_ID })
</script>
```

**Total Time: ~1.5 hours** ⚡

**VoiceHype Pricing:**
- Basic: $9/month (720 min + 400K tokens)
- Pro: $18/month (1440 min + 800K tokens)  
- Premium: $27/month (2160 min + 1.2M tokens)

---

## 🎨 **User Experience Flow**

### **Happy User:**
1. User sees current plan in PaymentsView
2. Sees upgrade/downgrade buttons
3. Clicks upgrade → pays with proration discount → quotas reset to new plan

### **Canceling User:**
1. User clicks "Cancel Subscription"
2. Sees confirmation modal with end date
3. Confirms → subscription cancels at period end
4. Can undo before period ends

### **Payment Failed User:**
1. Payment fails → immediate pause
2. User sees red alert in app
3. Clicks "Update Payment Method" → goes to Paddle
4. Fixes payment → subscription resumes automatically

**Simple and clean!** ✅

---

**This is the bare minimum needed. No complications, no extra tables, no complex logic!** 🚀

---

## 📊 **Simple Insights Plan**

### **What We'll Track:**
1. **Subscription Changes** - Who upgrades/downgrade when
2. **Cancellation Patterns** - Why users cancel and when
3. **Payment Issues** - How often payments fail and recovery rate
4. **Quota Usage** - How much users actually use vs. what they pay for

### **Key Metrics to Monitor:**
- **Churn Rate**: % of users who cancel each month
- **Upgrade Rate**: % of users who upgrade within 30/90 days
- **Payment Recovery**: % of failed payments that get fixed
- **Quota Utilization**: Average % of quotas used by plan tier

### **Simple Tracking Implementation:**
```sql
-- Log subscription events in existing transaction_logs table
INSERT INTO transaction_logs (user_id, type, metadata) VALUES
(user_id, 'subscription_cancelled', '{"reason": "user_initiated", "plan": "pro"}'),
(user_id, 'subscription_upgraded', '{"from": "basic", "to": "pro", "proration": 15.50}'),
(user_id, 'payment_failed', '{"attempt": 1, "amount": 18.00}'),
(user_id, 'payment_recovered', '{"downtime_hours": 24}');
```

### **Weekly Insights (5 min review):**
- Check cancellation reasons in transaction logs
- Monitor which plans have highest upgrade rates
- Review payment failure patterns by plan/user segment
- Track quota usage trends to optimize pricing

### **Monthly Actions:**
- Email survey to cancelled users (optional)
- Adjust pricing if quota utilization is consistently low/high
- Review proration algorithm effectiveness
- Plan feature updates based on upgrade patterns

**Goal: Data-driven decisions with minimal overhead!** 📈

---

## 🔄 **Key Change: Pure Proration (No Quota Merging)**

**Previous Complex Approach:**
- Add remaining quotas from old plan to new plan quotas
- User gets extra credits from unused time

**New Simple Approach:**
- Calculate proration discount based on unused value
- Reset quotas to new plan amounts (fresh start)
- User pays less, gets clean new allowances

**Why This is Better:**
- ✅ **Business-Safe**: Uses real cost data ($0.005709/min, $0.00000403/token)
- ✅ **Accurate Weighting**: Transcription costs 1,417x more per unit than optimization
- ✅ **Fair to Users**: Still get proration discount for unused value
- ✅ **Profit Protection**: Weighted by actual costs = minimal loss risk
- ✅ **Simpler Logic**: No quota merging, clean resets
- ✅ **Industry Standard**: Standard proration model used by most SaaS

**SIMPLE Step-by-Step Example (Your Method):**

**User on Basic Plan ($9/month):**
- Package: 720 minutes + 400,000 tokens
- Used so far: 360 minutes (50%) + 120,000 tokens (30%)

**Step 1: Apply service weights to plan price**
- Transcription portion: $9 × 71.8% = $6.46
- Optimization portion: $9 × 28.2% = $2.54

**Step 2: Calculate used cost for each service**
- Transcription used cost: $6.46 × (360/720) = $6.46 × 0.5 = $3.23
- Optimization used cost: $2.54 × (120,000/400,000) = $2.54 × 0.3 = $0.76
- Total used cost: $3.23 + $0.76 = $3.99

**Step 3: Calculate unused credit**
- Unused credit: $9 - $3.99 = $5.01

**Step 4: Apply to upgrade**
- Upgrading to Pro ($18): Pay $18 - $5.01 = $12.99
- Get fresh: 1440 min + 800K tokens (clean slate)

**Step 4: Apply to downgrade (NEW with VoiceHype Credits)**
- Downgrading to Basic ($9): Pay $0 (because unused credit $5.01 < new plan $9)
- Get fresh: 720 min + 400K tokens (clean slate)
- **No excess credit in this case**

**Step 4: Apply to big downgrade (NEW Example - Premium to Basic)**
- User on Premium ($27) used very little, has $26.49 unused credit
- Downgrading to Basic ($9): Pay $0
- Get fresh: 720 min + 400K tokens
- **Excess credit: $26.49 - $9 = $17.49 converted to VoiceHype credits!** 🎉

**MashaAllah! Your method is much clearer and simpler to calculate!** 🎯

---

## 🎉 **VoiceHype Credits Conversion System**

### **The Smart Solution for Excess Credits**

When users downgrade and their unused credit exceeds the new plan price, instead of capping the discount, we convert the excess to **VoiceHype Credits**! This makes users happy and keeps business fair.

### **Real-World Example: Premium to Basic Downgrade**

**User on Premium Plan ($27/month):**
- Package: 2160 minutes + 1,200,000 tokens
- Used: Only 100 minutes (4.6%) + 50,000 tokens (4.2%) - barely used!

**Proration Calculation:**
1. **Used costs:** $27 × 0.718 × 0.046 + $27 × 0.282 × 0.042 = $0.89 + $0.32 = $1.21
2. **Unused credit:** $27 - $1.21 = $25.79
3. **New plan cost:** $9 (Basic)
4. **User pays:** $0 (because $25.79 > $9)
5. **Excess credit:** $25.79 - $9 = $16.79

**✨ Magic Happens:**
- User gets Basic plan ($9 worth of service) for FREE this month
- User receives $16.79 in VoiceHype Credits (expires in 3 months)
- User can use credits for any VoiceHype services (transcription, optimization, etc.)

### **User Experience Flow:**

**1. User clicks "Downgrade to Basic"**
```
┌─────────────────────────────────────┐
│ 📋 Downgrade Preview                │
├─────────────────────────────────────┤
│ Current: Premium Plan      $27.00   │
│ Used value:                $1.21    │
│ Unused credit:             $25.79   │
│ New: Basic Plan            $9.00    │
│ ─────────────────────────────────   │
│ You pay today:             $0.00    │
│                                     │
│ 🎉 Bonus VoiceHype Credits: +$16.79 │
│ ↳ Expires in 3 months               │
└─────────────────────────────────────┘
```

**2. User confirms, gets:**
- ✅ Fresh Basic plan quotas (720 min + 400K tokens)  
- ✅ $16.79 VoiceHype credits for flexible usage
- ✅ No immediate charge
- ✅ Happy customer experience!

### **Business Benefits:**

**💰 Revenue Protection:**
- Already collected $27, only giving $9 worth of service
- Net profit: $27 - $9 - $16.79 = $1.21 (the actual used value)
- Credits expire in 3 months, encouraging continued usage

**😊 Customer Satisfaction:**
- Users feel they got great value
- No "lost money" feeling
- Credits encourage continued engagement
- Builds loyalty and trust

**🔄 Usage Encouragement:**
- Credits have expiration (3 months) creating urgency
- Users more likely to stay engaged with platform
- Higher chance of future plan upgrades

### **Database Implementation:**

The system automatically:
1. **Calculates excess:** `$25.79 - $9.00 = $16.79`
2. **Creates credit record:**
   ```sql
   INSERT INTO credits (
     user_id, balance, currency, expires_at, status,
     metadata: {
       "source": "subscription_proration",
       "reason": "Plan change from premium to basic",
       "created_from": "plan_change"
     }
   )
   ```
3. **Shows in user dashboard:** Credits section automatically updates
4. **Available for use:** Credits can be used for any VoiceHype services

### **Credit Usage Examples:**

**$16.79 in VoiceHype Credits can buy:**
- ~2,939 minutes of transcription (at $0.005709/min)
- ~4,166,667 optimization tokens (at $0.00000403/token)  
- Mix of both services as needed
- **Much more flexible than rigid plan quotas!**

**Perfect for users who:**
- Have variable usage patterns
- Want to try premium features occasionally  
- Prefer pay-as-you-go flexibility
- Don't want to feel locked into plans

### **The Win-Win Result:**

**✅ User Wins:**
- Gets fair value for money already paid
- Receives flexible credits instead of rigid quotas
- No feeling of losing money on downgrade
- Encouraged to continue using VoiceHype

**✅ Business Wins:**  
- Maintains revenue (keeps the profit from actual usage)
- Increases customer satisfaction and loyalty
- Credits expire, creating engagement incentive
- Simple, transparent system

**This is much better than the "cap at plan price" approach!** Instead of users feeling ripped off getting only $9 value from $25.79 unused credit, they get the full value in a more flexible form. Brilliant! 🎯

---

## 📚 **Developer Onboarding - Files to Read**

**For a new developer to fully understand this subscription management system, read these files in order:**

### **1. Core Planning & Architecture**
```
📄 docs/simple-subscription-management.md (THIS FILE)
   └── Complete system overview, proration formula, implementation plan
```

### **2. Database Schema & Functions**
```
📄 supabase/migrations/[latest]_add_subscription_management.sql
   └── Database changes: cancel_at_period_end column, proration function

📄 supabase/migrations/[existing]_quotas_table.sql
   └── Understanding quota structure (transcription/optimization services)

📄 supabase/migrations/[existing]_user_subscriptions_table.sql
   └── Base subscription table structure
```

### **3. Backend Webhook Integration**
```
📄 supabase/functions/paddle-webhook/index.ts
   └── Paddle event listener (subscription.updated, payment_failed, etc.)
   └── Handles cancellation, plan changes, payment failures automatically
```

### **4. Frontend Components**
```
📄 voicehype-website/src/stores/subscription.ts
   └── Subscription state management, Paddle JS SDK integration

📄 voicehype-website/src/components/subscription/CancelButton.vue
   └── Cancel subscription UI with confirmation modal

📄 voicehype-website/src/components/subscription/PlanChangeButtons.vue
   └── Upgrade/downgrade buttons with proration

📄 voicehype-website/src/components/subscription/PaymentAlert.vue
   └── Payment failed/paused subscription alerts

📄 voicehype-website/src/views/PaymentsView.vue
   └── Main subscription management interface
```

### **5. Cost Analysis & Pricing Data**
```
📄 analytics/all_tiers_profit_analysis.py
   └── Contains real service costs: $0.005709/min, $0.00000403/token
   └── Source of 71.8%/28.2% weights used in proration formula
```

### **6. Key Configuration Files**
```
📄 voicehype-website/package.json
   └── Check for Paddle JS SDK dependency

📄 voicehype-website/src/main.js (or equivalent)
   └── Paddle SDK initialization code
```

### **Reading Order for New Developer:**
1. **Start here** → `simple-subscription-management.md` (overview)
2. **Understand costs** → `analytics/all_tiers_profit_analysis.py` 
3. **Database structure** → Migration files in `supabase/migrations/`
4. **Backend logic** → `supabase/functions/paddle-webhook/index.ts`
5. **Frontend flow** → Components in `voicehype-website/src/components/subscription/`
6. **Integration** → `stores/subscription.ts` and `views/PaymentsView.vue`

### **Key Concepts to Understand:**
- **Proration Formula**: Plan price × service weight × (used/total) 
- **Webhook Pattern**: Paddle calls us, we don't call Paddle webhooks
- **Paddle JS SDK**: Frontend uses SDK, backend listens to events
- **Weight Calculation**: 71.8% transcription, 28.2% optimization (from real costs)
- **No Quota Merging**: Clean reset to new plan quotas on upgrade/downgrade

**🎯 After reading these files, developer should understand the complete subscription flow!**

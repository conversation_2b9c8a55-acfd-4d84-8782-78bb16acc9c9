# VoiceHype PAYG + Credits Complete Setup Guide

This guide covers the complete setup for both **Credits** and **Pay-As-You-Go (PAYG)** billing systems using Paddle integration.

## Overview

VoiceHype now supports two payment models:

1. **Credits**: Users purchase credits upfront ($5-$95) and use them for services
2. **PAYG**: Users add a payment method and get billed monthly for usage

## Prerequisites

- DigitalOcean droplet with self-hosted Supabase
- Paddle account (sandbox for testing, production for live)
- Domain setup (voicehype.ai with supabase.voicehype.ai subdomain)

## Step 1: Database Setup

### 1.1 Apply Paddle Integration Migration

```bash
# SSH into your DigitalOcean droplet
ssh root@***************

# Navigate to your Supabase project
cd /path/to/your/supabase/project

# Apply the migration
psql -h localhost -U postgres -d postgres -f supabase/migrations/20250122_001_paddle_integration.sql
```

### 1.2 Verify Database Schema

```sql
-- Check if paddle schema was created
\dn paddle

-- Check paddle tables
\dt paddle.*

-- Verify PAYG usage table has payment_status column
\d public.payg_usage
```

## Step 2: Paddle Account Configuration

### 2.1 Create Products in Paddle

1. **Credits Product**:
   - Name: VoiceHype Credits
   - Product ID: `prod_voicehype_credits`
   - Type: One-time purchase
   - Pricing: Variable pricing enabled

2. **PAYG Product**:
   - Name: VoiceHype Pay-as-you-go
   - Product ID: `prod_voicehype_payg`
   - Type: One-time purchase (for invoices)
   - Pricing: Variable pricing enabled

### 2.2 Configure Webhooks

Add webhook endpoint: `https://supabase.voicehype.ai/functions/v1/paddle-webhook`

Select events:
- `transaction.completed`
- `transaction.updated`
- `customer.created`
- `customer.updated`

## Step 3: Environment Variables

Add these to your Supabase environment:

```bash
# Paddle Configuration
PADDLE_API_KEY=your_paddle_api_key_here
PADDLE_ENVIRONMENT=sandbox  # or 'production'
PADDLE_WEBHOOK_SECRET=your_webhook_secret_here
PADDLE_PAYG_PRODUCT_ID=prod_voicehype_payg

# Frontend URL
FRONTEND_URL=https://voicehype.netlify.app

# Cron job security
CRON_SECRET=your-secure-random-string-here

# Environment flag
ENVIRONMENT=development  # or 'production'
```

## Step 4: Deploy Edge Functions

Deploy these Supabase Edge Functions:

```bash
# Deploy all functions
supabase functions deploy paddle-webhook
supabase functions deploy create-paddle-checkout
supabase functions deploy get-paddle-transactions
supabase functions deploy validate-payg-access
supabase functions deploy process-monthly-payg
```

## Step 5: Set Up Monthly Billing Cron Job

### 5.1 Install Cron Script

```bash
# Create scripts directory
mkdir -p /opt/voicehype/scripts

# Create the script
cat > /opt/voicehype/scripts/monthly-payg-billing.sh << 'EOF'
#!/bin/bash

# Monthly PAYG Billing Cron Job Script
SUPABASE_URL="https://supabase.voicehype.ai"
CRON_SECRET="your-secure-cron-secret-here"
LOG_FILE="/var/log/voicehype/monthly-billing.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# Create log directory if it doesn't exist
mkdir -p "$(dirname "$LOG_FILE")"

# Function to log messages
log_message() {
    echo "[$DATE] $1" | tee -a "$LOG_FILE"
}

log_message "Starting monthly PAYG billing process..."

# Call the Supabase Edge Function
response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
    -X POST \
    -H "Content-Type: application/json" \
    -H "x-cron-secret: $CRON_SECRET" \
    "$SUPABASE_URL/functions/v1/process-monthly-payg" \
    -d '{}')

# Extract HTTP status and body
http_status=$(echo "$response" | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
body=$(echo "$response" | sed -e 's/HTTPSTATUS:.*//g')

log_message "HTTP Status: $http_status"
log_message "Response: $body"

# Check if the request was successful
if [ "$http_status" -eq 200 ]; then
    log_message "Monthly PAYG billing completed successfully"
    exit 0
else
    log_message "ERROR: Monthly PAYG billing failed with HTTP status $http_status"
    exit 1
fi
EOF

# Make it executable
chmod +x /opt/voicehype/scripts/monthly-payg-billing.sh

# Create log directory
mkdir -p /var/log/voicehype
```

### 5.2 Set Up Cron Job

```bash
# Edit crontab
crontab -e

# Add this line to run on the 1st of every month at 2 AM
0 2 1 * * /opt/voicehype/scripts/monthly-payg-billing.sh
```

### 5.3 Test the Cron Job

```bash
# Test the script manually
/opt/voicehype/scripts/monthly-payg-billing.sh

# Check logs
tail -f /var/log/voicehype/monthly-billing.log
```

## Step 6: Frontend Integration

The frontend is already updated with:

- **Credits Store**: Enhanced with PAYG functions
- **Payments View**: Updated UI for both credits and PAYG
- **PAYG Access Validation**: Check if users can use PAYG

### 6.1 PAYG User Flow

1. User visits payments page
2. Chooses PAYG option
3. Redirected to Paddle to add payment method
4. Returns to VoiceHype with PAYG access enabled
5. Can now use services with monthly billing

### 6.2 Credits User Flow

1. User visits payments page
2. Enters credit amount ($5-$95)
3. Redirected to Paddle checkout
4. Returns to VoiceHype with credits added

## Step 7: Testing

### 7.1 Test Credits Purchase

1. Go to payments page
2. Enter amount between $5-$95
3. Complete Paddle checkout
4. Verify credits are added to account

### 7.2 Test PAYG Setup

1. Go to payments page
2. Click "Set up Pay-as-you-go"
3. Add payment method in Paddle
4. Verify PAYG access is enabled

### 7.3 Test Monthly Billing

```bash
# Manually trigger monthly billing
/opt/voicehype/scripts/monthly-payg-billing.sh

# Check database for invoices
psql -h localhost -U postgres -d postgres -c "
SELECT 
  pt.user_id,
  pt.amount,
  pt.status,
  pt.transaction_type,
  pt.created_at
FROM paddle.transactions pt
WHERE pt.transaction_type = 'payg_invoice'
ORDER BY pt.created_at DESC;
"
```

## Step 8: Monitoring

### 8.1 Database Monitoring

```sql
-- Check PAYG usage
SELECT 
  user_id,
  month,
  total_amount,
  payment_status,
  created_at
FROM public.payg_usage
WHERE payment_status = 'pending'
ORDER BY month DESC;

-- Check Paddle transactions
SELECT 
  transaction_type,
  status,
  COUNT(*),
  SUM(amount)
FROM paddle.transactions
GROUP BY transaction_type, status;
```

### 8.2 Log Monitoring

```bash
# Monitor cron job logs
tail -f /var/log/voicehype/monthly-billing.log

# Monitor Supabase function logs
# (Check your Supabase dashboard for function logs)
```

## Step 9: Production Deployment

### 9.1 Switch to Production

1. Update environment variables:
   ```bash
   PADDLE_ENVIRONMENT=production
   ENVIRONMENT=production
   ```

2. Use production Paddle API keys
3. Update webhook URLs to production domain
4. Test with real payment methods

### 9.2 Security Checklist

- [ ] Webhook signature verification enabled
- [ ] Cron secret is secure and random
- [ ] Database RLS policies are active
- [ ] API keys are not exposed in frontend
- [ ] HTTPS is enforced everywhere

## Troubleshooting

### Common Issues

1. **Cron job not running**: Check `systemctl status cron`
2. **Webhook not received**: Verify URL and Paddle configuration
3. **Payment not processed**: Check webhook logs and database
4. **PAYG access denied**: Verify payment method setup

### Debug Commands

```bash
# Check cron service
systemctl status cron

# Test webhook manually
curl -X POST https://supabase.voicehype.ai/functions/v1/paddle-webhook \
  -H "Content-Type: application/json" \
  -d '{"test": true}'

# Check database connections
psql -h localhost -U postgres -d postgres -c "SELECT NOW();"
```

## Support

For issues with this setup:
1. Check the logs first
2. Verify environment variables
3. Test individual components
4. Contact Paddle support for payment issues

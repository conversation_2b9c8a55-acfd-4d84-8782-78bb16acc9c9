# Technical Issue Report for Requesty Team

**Subject:** Connection Termination Issues with DeepInfra Models via Requesty Router

---

## Issue Summary

We are experiencing consistent connection termination errors when using DeepInfra models through the Requesty router API, while Claude models work perfectly. This affects our production Supabase Edge Functions running on Deno.

## Environment Details

- **Platform**: Supabase Edge Functions (Deno runtime)
- **Deno Version**: 2.3.5
- **API Endpoint**: `https://router.requesty.ai/v1/chat/completions`
- **Client**: VoiceHype application
- **Issue Frequency**: 100% reproducible with DeepInfra models

## Affected Models

**✅ Working Models (Claude)**:
- `anthropic/claude-3-haiku-20240307`
- `anthropic/claude-3-5-sonnet-latest`
- All other Claude models

**❌ Failing Models (DeepInfra)**:
- `deepinfra/meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo`
- `deepinfra/meta-llama/Meta-Llama-3.1-70B-Instruct`
- `deepinfra/deepseek-ai/DeepSeek-V3`

## Specific Error Patterns

### 1. HTTP/2 Protocol Violations with `response_format`
When including `response_format: {"type": "json_object"}` parameter:

**cURL Test Result**:
```bash
curl -X POST https://router.requesty.ai/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer [API_KEY]" \
  -d '{"model": "deepinfra/meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo", "response_format": {"type": "json_object"}, ...}'

# Result: 
curl: (92) [HTTP2] [1] received invalid frame: FRAME[HEADERS, len=147, hend=1, eos=1], error -532: Violation in HTTP messaging rule
```

### 2. Connection Termination During Response Reading
Without `response_format` parameter:

**cURL Test Result**:
```bash
# Returns 200 OK with proper headers but connection terminates
curl: (92) HTTP/2 stream 1 was not closed cleanly: INTERNAL_ERROR (err 2)
```

**Node.js Test Result**:
```javascript
// Response status: 200
// Content-Type: application/json
// Content-Length: 461-670 bytes
// Error: terminated (during response.json() or response.arrayBuffer())
```

**Deno Test Result**:
```
TypeError: error decoding response body
TypeError: Body already consumed
```

### 3. Successful Response Headers but Failed Body Reading

**Successful Response Headers from DeepInfra**:
```
Status: 200 OK
Content-Type: application/json
Content-Length: 461
Connection: keep-alive
Server: Fly/903dea10a (2025-06-24)
```

But the response body reading fails across all JavaScript runtimes (Node.js, Deno).

## Working vs Failing Comparison

### Claude Models (Working)
```javascript
// Request
{
  "model": "anthropic/claude-3-haiku-20240307",
  "messages": [...],
  "response_format": {"type": "json_object"}  // ✅ Works fine
}

// Response: Perfect, no issues
{
  "choices": [{"message": {"content": "..."}}],
  "usage": {...}
}
```

### DeepInfra Models (Failing)
```javascript
// Request 1 - With response_format
{
  "model": "deepinfra/meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo",
  "messages": [...],
  "response_format": {"type": "json_object"}  // ❌ HTTP/2 violation
}
// Result: HTTP/2 protocol error -532

// Request 2 - Without response_format  
{
  "model": "deepinfra/meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo",
  "messages": [...]  // ❌ Connection terminated during body read
}
// Result: 200 OK but "terminated" error when reading response
```

## Testing Methodology

We performed comprehensive testing across multiple environments:

1. **cURL Tests**: Direct HTTP/1.1 and HTTP/2 requests
2. **Node.js Tests**: Using native fetch API
3. **Deno Tests**: Using Deno's fetch implementation
4. **Local Proxy**: HTTP/1.1 proxy server attempts

All tests consistently show the same pattern: DeepInfra models fail while Claude models work perfectly.

## Reproduction Steps

1. Make a request to any DeepInfra model via Requesty
2. Use either approach:
   - With `response_format`: Get HTTP/2 protocol violation
   - Without `response_format`: Get connection termination during response reading
3. Compare with identical request to Claude model (works perfectly)

## Request for Assistance

Could the Requesty team please investigate:

1. **HTTP/2 Handling**: Why DeepInfra models trigger HTTP/2 protocol violations with `response_format`?
2. **Connection Stability**: Why do DeepInfra model responses terminate during body reading?
3. **Proxy Configuration**: Are there different proxy configurations for Claude vs DeepInfra backends?
4. **Recommended Workarounds**: Any client-side configurations that might resolve this?

## Impact on Production

This issue affects our production application serving thousands of users. We currently:
- Cannot use DeepInfra models reliably
- Must implement complex retry logic
- Experience failed optimizations for cost-effective models

## Contact Information

- **Application**: VoiceHype
- **Technical Contact**: [Your email]
- **API Usage**: Production environment with significant volume

We appreciate any insights or fixes the Requesty team can provide. This appears to be a backend routing or proxy configuration issue specific to DeepInfra model handling.

Thank you for your support.

---

*Generated on June 30, 2025*
*Investigation conducted with comprehensive multi-platform testing*

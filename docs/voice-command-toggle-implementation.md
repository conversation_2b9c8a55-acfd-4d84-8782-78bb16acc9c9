# Voice Command Toggle Implementation Plan

## Overview
Implement a feature toggle for voice commands in VoiceHype, allowing users to enable/disable the voice command functionality while maintaining the core text optimization features.

## Components

### 1. Configuration Management
- Add `voicehype.voiceCommandsEnabled` setting to VSCode configuration
- Default value: `true` (backward compatible)
- Update `ConfigurationService` to include:
  ```typescript
  public getVoiceCommandsEnabled(): boolean;
  public setVoiceCommandsEnabled(enabled: boolean): Promise<void>;
  ```
- Add to configuration change event handlers in `VoiceHypePanelService`

### 2. PromptFormatter Modifications
- Modify `createUnifiedUserMessage` to accept `voiceCommandsEnabled` parameter:
  ```typescript
  private createUnifiedUserMessage(transcript: string, customPrompt: string, voiceCommandsEnabled: boolean): string
  ```
- Split instruction sections:
  ```typescript
  private createOptimizationInstructions(customPrompt: string): string
  private createVoiceCommandInstructions(): string
  ```
- Update `createOptimizationMessages` method:
  ```typescript
  public createOptimizationMessages(transcript: string, customPrompt?: string, voiceCommandsEnabled?: boolean): Message[]
  ```
- Add conditional logic to include voice command instructions only when enabled
- Keep backward compatibility by defaulting voiceCommandsEnabled to true

### 3. WebView UI Updates
#### Toggle Switch Component
- Location: Settings panel in WebView, alongside other toggles
- Implement in `TranscriptionSettings.tsx`:
  ```typescript
  interface TranscriptionSettingsProps {
    // ... existing props ...
    voiceCommandsEnabled: boolean;
    onVoiceCommandsToggle: (enabled: boolean) => void;
  }
  ```
- Use existing `Switch` component with consistent styling
- Add state management in App.tsx:
  ```typescript
  const [voiceCommandsEnabled, setVoiceCommandsEnabled] = useState<boolean>(true);
  const handleVoiceCommandsToggle = useCallback((checked: boolean) => {
    setVoiceCommandsEnabled(checked);
    vscode.postMessage({
      command: 'updateOptions',
      options: { voiceCommandsEnabled: checked }
    });
  }, []);
  ```

#### Information Display
- Add `InfoIcon` from Heroicons next to toggle:
  ```typescript
  import { InformationCircleIcon } from '@heroicons/react/24/outline';
  ```
- Add tooltip using VS Code's native tooltip styling:
  ```html
  <div className="flex items-center">
    <span>Voice Commands</span>
    <InformationCircleIcon 
      className="w-4 h-4 ml-1 text-gray-500" 
      title="Enable to use voice commands by saying 'Voice Hype' followed by your command. Disable to treat all input as text to optimize."
    />
  </div>
  ```
- Include "Learn more" link to documentation

## Technical Implementation Details

### Configuration Storage
```typescript
interface VoiceHypeConfiguration {
    // Existing settings
    voiceCommandsEnabled: boolean;
}
```

### PromptFormatter Changes
- Split current instruction set into:
  - Base optimization instructions
  - Voice command specific instructions
- Implement conditional instruction inclusion based on configuration

### UI Component Properties
```typescript
interface VoiceCommandToggleProps {
    enabled: boolean;
    onToggle: (enabled: boolean) => void;
    className?: string;
}
```

## User Experience Considerations

### Toggle States
1. **Enabled (Default)**
   - Full voice command functionality
   - Process "Voice Hype" prefixed commands
   - Regular optimization features

2. **Disabled**
   - Pure optimization mode
   - Treat all input as text to be optimized
   - Ignore "Voice Hype" prefixes

### User Feedback
- Visual feedback on toggle state change
- Clear indication of current mode
- Tooltips explaining each state
- Status bar indicator (optional)

## Implementation Phases

### Phase 1: Core Implementation
1. Add configuration management:
   - Update package.json with new configuration
   - Implement ConfigurationService methods
   - Add configuration handlers to VoiceHypePanelService

2. Modify PromptFormatter:
   - Split instruction logic
   - Add voice command toggle support
   - Update tests

3. Create basic toggle UI:
   - Add voice command toggle to TranscriptionSettings
   - Implement state management in App.tsx
   - Add message handling in VoiceHypePanelService

### Phase 2: UI Enhancement
1. Add information displays
2. Implement tooltips
3. Add accessibility features

### Phase 3: Testing & Validation
1. Test configuration persistence
2. Verify prompt generation in both modes
3. Validate UI components
4. Test accessibility

## Testing Requirements

### Unit Tests
- Configuration management
- Prompt generation with/without voice commands
- Toggle component behavior

### Integration Tests
- Configuration persistence
- WebView-extension communication
- State synchronization

### User Acceptance Testing
- Toggle functionality
- Prompt behavior in both modes
- UI clarity and useability

## Documentation Updates

### User Documentation
- Add voice command toggle section
- Update usage instructions
- Add screenshots of new UI elements

### Developer Documentation
- Document configuration schema
- Update architectural documentation
- Add UI component documentation

## Future Considerations
- Analytics for feature usage
- Custom voice command sets
- Keyboard shortcuts for toggle
- Command history specific to voice commands

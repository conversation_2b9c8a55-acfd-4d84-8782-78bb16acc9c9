# VoiceHype Web Extension Port Plan

**<PERSON><PERSON><PERSON><PERSON> rah<PERSON>r raheem**

## Overview
This document outlines the comprehensive plan to port VoiceHype from a VS Code extension to a cross-browser web extension that works on Chrome, Firefox, Edge, and other Chromium-based browsers.

## Current Architecture Analysis

### VS Code Extension Structure
- **Main Entry**: `extension/src/extension.ts` - VS Code specific activation
- **Core Services**: Recording, Transcription, Authentication, Configuration
- **Audio Recording**: Uses `@voicehype/audify-plus` (native Node.js audio capture)
- **UI**: WebView panels with React/Vue components
- **Storage**: VS Code's secret storage and workspace configuration
- **Real-time**: WebSocket connection to Assembly AI

### Key Components to Port
1. **Audio Recording Service** - Currently uses native audio libraries
2. **Transcription Service** - API calls to Supabase functions
3. **Authentication Service** - OAuth flow with Supabase
4. **Configuration Management** - Settings and preferences
5. **History Service** - Local storage of transcriptions
6. **UI Components** - Recording controls and settings

## Web Extension Architecture

### 1. Manifest Structure (Manifest V3)
```json
{
  "manifest_version": 3,
  "name": "VoiceHype",
  "version": "1.0.0",
  "description": "Voice-to-prompt productivity tool for web browsers",
  "permissions": [
    "activeTab",
    "storage",
    "scripting"
  ],
  "host_permissions": [
    "https://supabase.voicehype.ai/*",
    "https://voicehype.netlify.app/*"
  ],
  "background": {
    "service_worker": "background.js"
  },
  "content_scripts": [{
    "matches": ["<all_urls>"],
    "js": ["content.js"],
    "css": ["content.css"]
  }],
  "action": {
    "default_popup": "popup.html",
    "default_title": "VoiceHype"
  },
  "web_accessible_resources": [{
    "resources": ["widget.html", "assets/*"],
    "matches": ["<all_urls>"]
  }]
}
```

### 2. Core File Structure
```
web-extension/
├── manifest.json
├── background.js              # Service worker (replaces VS Code extension host)
├── content.js                 # Content script for text field detection
├── popup/
│   ├── popup.html            # Extension popup UI
│   ├── popup.js              # Popup logic
│   └── popup.css             # Popup styles
├── widget/
│   ├── widget.html           # Injected widget for text fields
│   ├── widget.js             # Widget functionality
│   └── widget.css            # Widget styles
├── shared/
│   ├── audio-recorder.js     # Browser audio recording (MediaRecorder)
│   ├── transcription.js      # API calls to transcription services
│   ├── auth.js               # Authentication management
│   ├── storage.js            # Browser storage management
│   ├── config.js             # Configuration management
│   └── utils.js              # Shared utilities
├── assets/
│   ├── icons/                # Extension icons
│   └── sounds/               # Audio feedback
└── options/
    ├── options.html          # Settings page
    ├── options.js            # Settings logic
    └── options.css           # Settings styles
```

## Technical Implementation Plan

### Phase 1: Core Infrastructure (Week 1-2)

#### 1.1 Audio Recording System
**Challenge**: Replace native audio libraries with Web APIs
**Solution**: Use MediaRecorder API with getUserMedia

```javascript
// Browser-compatible audio recording
class BrowserAudioRecorder {
  async startRecording() {
    const stream = await navigator.mediaDevices.getUserMedia({ 
      audio: {
        sampleRate: 16000,
        channelCount: 1,
        echoCancellation: true,
        noiseSuppression: true
      }
    });
    
    this.mediaRecorder = new MediaRecorder(stream, {
      mimeType: 'audio/webm;codecs=opus'
    });
    
    this.chunks = [];
    this.mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        this.chunks.push(event.data);
        // For real-time: convert and send chunk
        if (this.realtimeEnabled) {
          this.sendRealtimeChunk(event.data);
        }
      }
    };
    
    this.mediaRecorder.start(100); // 100ms chunks for real-time
  }
}
```

#### 1.2 Storage System
**Challenge**: Replace VS Code storage with browser storage
**Solution**: Use chrome.storage.sync and chrome.storage.local

```javascript
// Browser storage wrapper
class BrowserStorage {
  async setConfig(key, value) {
    await chrome.storage.sync.set({ [key]: value });
  }
  
  async getConfig(key) {
    const result = await chrome.storage.sync.get(key);
    return result[key];
  }
  
  async setHistory(transcriptions) {
    await chrome.storage.local.set({ history: transcriptions });
  }
}
```

#### 1.3 Authentication System
**Challenge**: OAuth flow in browser extension context
**Solution**: Use chrome.identity API or popup-based OAuth

```javascript
// Browser OAuth implementation
class BrowserAuth {
  async signIn() {
    const authUrl = this.buildAuthUrl();
    const popup = window.open(authUrl, 'auth', 'width=500,height=600');
    
    return new Promise((resolve, reject) => {
      const checkClosed = setInterval(() => {
        if (popup.closed) {
          clearInterval(checkClosed);
          // Check for auth result in storage
          this.checkAuthResult().then(resolve).catch(reject);
        }
      }, 1000);
    });
  }
}
```

### Phase 2: Text Field Integration (Week 2-3)

#### 2.1 Text Field Detection
**Solution**: Use Intersection Observer for efficient detection

```javascript
// Content script for text field detection
class TextFieldDetector {
  constructor() {
    this.observer = new IntersectionObserver(this.handleIntersection.bind(this));
    this.widgets = new Map();
  }
  
  init() {
    // Observe all text inputs, textareas, and contenteditable elements
    const textFields = document.querySelectorAll(
      'input[type="text"], input[type="email"], textarea, [contenteditable="true"]'
    );
    
    textFields.forEach(field => this.observer.observe(field));
    
    // Watch for dynamically added fields
    this.setupMutationObserver();
  }
  
  handleIntersection(entries) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        this.addWidget(entry.target);
      } else {
        this.removeWidget(entry.target);
      }
    });
  }
}
```

#### 2.2 Widget System
**Solution**: Inject floating widget near text fields

```javascript
// Widget injection and management
class VoiceWidget {
  create(textField) {
    const widget = document.createElement('div');
    widget.className = 'voicehype-widget';
    widget.innerHTML = `
      <button class="vh-record-btn" title="Start Voice Recording">
        <svg><!-- Microphone icon --></svg>
      </button>
      <div class="vh-controls" style="display: none;">
        <button class="vh-stop">Stop</button>
        <button class="vh-pause">Pause</button>
        <div class="vh-status">Ready</div>
      </div>
    `;
    
    this.positionWidget(widget, textField);
    document.body.appendChild(widget);
    
    this.attachEventListeners(widget, textField);
    return widget;
  }
  
  positionWidget(widget, textField) {
    const rect = textField.getBoundingClientRect();
    widget.style.position = 'fixed';
    widget.style.top = `${rect.top + rect.height - 30}px`;
    widget.style.right = `${window.innerWidth - rect.right + 5}px`;
    widget.style.zIndex = '10000';
  }
}
```

### Phase 3: Real-time Features (Week 3-4)

#### 3.1 Real-time Transcription
**Challenge**: Maintain WebSocket connection across extension components
**Solution**: Background script manages connection, content script receives updates

```javascript
// Background script WebSocket management
class RealtimeManager {
  constructor() {
    this.connection = null;
    this.activeTab = null;
  }
  
  async connect() {
    this.connection = new WebSocket('wss://supabase.voicehype.ai/realtime');
    
    this.connection.onmessage = (event) => {
      const data = JSON.parse(event.data);
      if (data.type === 'transcription') {
        // Send to active content script
        chrome.tabs.sendMessage(this.activeTab, {
          type: 'realtimeUpdate',
          text: data.text,
          isFinal: data.isFinal
        });
      }
    };
  }
  
  sendAudioChunk(chunk) {
    if (this.connection?.readyState === WebSocket.OPEN) {
      this.connection.send(chunk);
    }
  }
}
```

#### 3.2 Audio Slicing
**Solution**: Implement client-side audio chunking for large files

```javascript
// Audio file slicing for large uploads
class AudioSlicer {
  async sliceAudio(audioBlob, maxSizeBytes = 25 * 1024 * 1024) {
    if (audioBlob.size <= maxSizeBytes) {
      return [audioBlob];
    }
    
    const chunks = [];
    const chunkSize = maxSizeBytes;
    
    for (let start = 0; start < audioBlob.size; start += chunkSize) {
      const end = Math.min(start + chunkSize, audioBlob.size);
      const chunk = audioBlob.slice(start, end);
      chunks.push(chunk);
    }
    
    return chunks;
  }
}
```

### Phase 4: File Upload & History (Week 4-5)

#### 4.1 File Upload System
**Solution**: Drag & drop and file picker in popup

```javascript
// File upload handling
class FileUploader {
  setupDropZone(element) {
    element.addEventListener('dragover', (e) => {
      e.preventDefault();
      element.classList.add('drag-over');
    });
    
    element.addEventListener('drop', async (e) => {
      e.preventDefault();
      const files = Array.from(e.dataTransfer.files);
      const audioFiles = files.filter(f => f.type.startsWith('audio/'));
      
      for (const file of audioFiles) {
        await this.processAudioFile(file);
      }
    });
  }
  
  async processAudioFile(file) {
    // Validate file type and size
    if (!this.isValidAudioFile(file)) {
      throw new Error('Invalid audio file');
    }
    
    // Slice if too large
    const chunks = await this.audioSlicer.sliceAudio(file);
    
    // Transcribe each chunk
    const transcriptions = await Promise.all(
      chunks.map(chunk => this.transcriptionService.transcribe(chunk))
    );
    
    // Combine results
    return this.combineTranscriptions(transcriptions);
  }
}
```

#### 4.2 History Management
**Solution**: Local storage with sync capabilities

```javascript
// History management
class HistoryManager {
  async saveTranscription(transcription) {
    const history = await this.getHistory();
    history.unshift({
      id: Date.now(),
      timestamp: new Date().toISOString(),
      originalText: transcription.original,
      optimizedText: transcription.optimized,
      model: transcription.model,
      language: transcription.language
    });
    
    // Keep only last 100 entries
    if (history.length > 100) {
      history.splice(100);
    }
    
    await chrome.storage.local.set({ history });
  }
  
  async getHistory() {
    const result = await chrome.storage.local.get('history');
    return result.history || [];
  }
}
```

## Browser Compatibility Strategy

### Chrome/Chromium (Primary Target)
- Full Manifest V3 support
- Complete MediaRecorder API
- Chrome extension APIs

### Firefox (Secondary)
- Manifest V2 compatibility layer
- WebExtensions API differences
- Different audio format support

### Edge (Tertiary)
- Chromium-based, similar to Chrome
- Microsoft Store distribution

### Cross-Browser Abstraction Layer
```javascript
// Browser API abstraction
class BrowserAPI {
  static get storage() {
    return typeof chrome !== 'undefined' ? chrome.storage : browser.storage;
  }
  
  static get tabs() {
    return typeof chrome !== 'undefined' ? chrome.tabs : browser.tabs;
  }
  
  static async getUserMedia(constraints) {
    return navigator.mediaDevices.getUserMedia(constraints);
  }
}
```

## Shortcuts & Keyboard Integration

### Global Shortcuts
- **Challenge**: Limited global shortcut support in web extensions
- **Solution**: Use chrome.commands API with fallback to page-level shortcuts

```javascript
// Keyboard shortcuts
chrome.commands.onCommand.addListener((command) => {
  switch (command) {
    case 'start-recording':
      // Send message to active tab
      chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
        chrome.tabs.sendMessage(tabs[0].id, {action: 'startRecording'});
      });
      break;
  }
});
```

### Customizable Shortcuts
- Settings page for shortcut customization
- Per-site shortcut preferences
- Conflict detection with existing page shortcuts

## Data Storage Strategy

### Configuration Storage
- **chrome.storage.sync**: User preferences, settings (synced across devices)
- **chrome.storage.local**: Large data, history, temporary files

### Audio File Storage
- **Temporary**: In-memory Blob objects during recording
- **History**: Base64 encoded small clips for playback
- **Large Files**: Direct upload, no local storage

### Privacy Considerations
- No persistent audio storage
- Configurable history retention
- Clear data functionality

## Integration with Existing Services

### Supabase Integration
- Same API endpoints as VS Code extension
- Authentication flow adaptation
- Real-time WebSocket connections

### Assembly AI Real-time
- WebSocket connection from background script
- Audio streaming via MediaRecorder chunks
- Status updates to content scripts

## Development Phases Timeline

### Week 1-2: Foundation
- [ ] Basic manifest and file structure
- [ ] Audio recording with MediaRecorder
- [ ] Storage abstraction layer
- [ ] Authentication system

### Week 2-3: UI Integration
- [ ] Text field detection system
- [ ] Widget injection and positioning
- [ ] Content script communication
- [ ] Basic recording controls

### Week 3-4: Advanced Features
- [ ] Real-time transcription
- [ ] Audio file slicing
- [ ] Background script coordination
- [ ] Error handling and recovery

### Week 4-5: Polish & Testing
- [ ] File upload system
- [ ] History management
- [ ] Cross-browser testing
- [ ] Performance optimization

### Week 5-6: App-Specific Integration
- [ ] WhatsApp integration
- [ ] Gmail integration
- [ ] Slack integration
- [ ] Generic text field optimization

## Authentication & API Key Management

### Same Supabase Client as Website
The web extension will use **exactly the same Supabase client configuration** as the VoiceHype website:

```javascript
// Same as voicehype-website/src/lib/supabase.ts
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://nffixzoqnqxpcqpcxpps.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' // Your anon key

export const supabase = createClient(supabaseUrl, supabaseAnonKey)
```

### VoiceHype API Key - The Most Important Thing
The **VoiceHype API key** (format: `vhkey_xxxxx`) is what makes everything work. This key:
- Authenticates all requests to your Supabase edge functions
- Must be stored **SECURELY** in browser extension storage
- Is used in the `Authorization: Bearer` header for all API calls

### Secure API Key Storage
```javascript
// Secure storage using browser extension APIs
class SecureStorage {
  async storeApiKey(apiKey) {
    // Hash the API key for additional security
    const hashedKey = await this.hashApiKey(apiKey);

    // Store in browser's secure storage
    await this.browser.setStorage('vh_api_key_hash', hashedKey);
    await this.browser.setStorage('vh_api_key', apiKey); // Encrypted by browser
  }

  async getApiKey() {
    const apiKey = await this.browser.getStorage('vh_api_key');
    const storedHash = await this.browser.getStorage('vh_api_key_hash');

    // Verify integrity
    if (apiKey && storedHash) {
      const currentHash = await this.hashApiKey(apiKey);
      if (currentHash === storedHash) {
        return apiKey;
      }
    }
    return null;
  }

  async hashApiKey(apiKey) {
    const encoder = new TextEncoder();
    const data = encoder.encode(apiKey);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    return Array.from(new Uint8Array(hashBuffer))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
  }
}
```

### User Data Display (Same as Website)
The extension will show the same user information as the website:
- **Usage History** - Transcription and optimization usage
- **Subscription Quotas** - Current plan limits and usage
- **Remaining Credits** - Available credits for pay-as-you-go
- **User Profile** - Avatar from OAuth provider (Google/GitHub)

## Detailed File Structure & Implementation

### Background Script (The Brain) - Universal Backend

The background script serves as the universal backend that works across all browsers. It handles all the core logic that doesn't depend on browser-specific APIs.

#### Core Backend Files

```
web-extension/
├── background/
│   ├── index.js                    # Main background script entry point
│   ├── core/
│   │   ├── audio-manager.js        # Audio recording coordination
│   │   ├── supabase-service.js     # API calls to VoiceHype Supabase edge functions
│   │   ├── auth-service.js         # Authentication management (same as website)
│   │   ├── config-manager.js       # Configuration management
│   │   ├── history-service.js      # History management
│   │   ├── realtime-manager.js     # WebSocket connections
│   │   └── file-processor.js       # Audio file processing & slicing
│   ├── utils/
│   │   ├── api-client.js           # HTTP client for Supabase
│   │   ├── error-handler.js        # Centralized error handling
│   │   ├── logger.js               # Logging utility
│   │   └── constants.js            # App constants
│   └── adapters/
│       ├── browser-adapter.js      # Browser API abstraction
│       ├── storage-adapter.js      # Storage API abstraction
│       └── messaging-adapter.js    # Messaging API abstraction
```

#### Browser-Specific Frontend Files

```
web-extension/
├── frontend/
│   ├── shared/                     # Shared Vue.js components
│   │   ├── components/
│   │   │   ├── RecordingControls.vue
│   │   │   ├── ServiceSelector.vue
│   │   │   ├── ModelSelector.vue
│   │   │   ├── LanguageSelector.vue
│   │   │   ├── OptimizationSelector.vue
│   │   │   ├── AudioSettings.vue
│   │   │   ├── HistoryList.vue
│   │   │   └── AuthStatus.vue
│   │   ├── composables/
│   │   │   ├── useRecording.js
│   │   │   ├── useAuth.js
│   │   │   ├── useConfig.js
│   │   │   └── useHistory.js
│   │   └── utils/
│   │       ├── browser-utils.js
│   │       ├── audio-utils.js
│   │       └── ui-utils.js
│   ├── popup/                      # Extension popup
│   │   ├── popup.html
│   │   ├── popup.js               # Vue app initialization
│   │   ├── PopupApp.vue           # Main popup component
│   │   └── popup.css
│   ├── options/                    # Settings page
│   │   ├── options.html
│   │   ├── options.js
│   │   ├── OptionsApp.vue
│   │   └── options.css
│   ├── widget/                     # Injected widget
│   │   ├── widget.html
│   │   ├── widget.js
│   │   ├── WidgetApp.vue
│   │   └── widget.css
│   └── content/
│       ├── content.js              # Content script
│       ├── text-field-detector.js
│       ├── widget-injector.js
│       └── content.css
├── browser-specific/
│   ├── chrome/
│   │   ├── manifest.json           # Chrome Manifest V3
│   │   └── chrome-specific.js
│   ├── firefox/
│   │   ├── manifest.json           # Firefox Manifest V2
│   │   └── firefox-specific.js
│   └── edge/
│       ├── manifest.json           # Edge Manifest V3
│       └── edge-specific.js
```

### Background Script Implementation Details

#### 1. Main Background Script (`background/index.js`)
```javascript
// Universal entry point that works across all browsers
import { BrowserAdapter } from './adapters/browser-adapter.js';
import { AudioManager } from './core/audio-manager.js';
import { SupabaseService } from './core/supabase-service.js';
import { AuthService } from './core/auth-service.js';
import { ConfigManager } from './core/config-manager.js';
import { HistoryService } from './core/history-service.js';
import { RealtimeManager } from './core/realtime-manager.js';
import { Logger } from './utils/logger.js';

class VoiceHypeBackground {
  constructor() {
    this.browser = new BrowserAdapter();
    this.logger = new Logger();
    this.configManager = new ConfigManager(this.browser);
    this.authService = new AuthService(this.browser, this.configManager);
    this.historyService = new HistoryService(this.browser);
    this.supabaseService = new SupabaseService(this.configManager);
    this.realtimeManager = new RealtimeManager(this.configManager);
    this.audioManager = new AudioManager(this.browser, this.configManager);

    this.init();
  }

  async init() {
    // Set up message listeners
    this.browser.onMessage(this.handleMessage.bind(this));

    // Initialize services
    await this.configManager.init();
    await this.authService.init();

    this.logger.info('VoiceHype background script initialized');
  }

  async handleMessage(message, sender, sendResponse) {
    try {
      switch (message.action) {
        case 'startRecording':
          return await this.audioManager.startRecording(message.options);
        case 'stopRecording':
          return await this.audioManager.stopRecording();
        case 'getConfig':
          return await this.configManager.getAll();
        case 'setConfig':
          return await this.configManager.set(message.key, message.value);
        case 'getHistory':
          return await this.historyService.getHistory();
        case 'authenticate':
          return await this.authService.authenticate();
        case 'getUserData':
          return await this.authService.getUserData();
        case 'signOut':
          return await this.authService.signOut();
        default:
          throw new Error(`Unknown action: ${message.action}`);
      }
    } catch (error) {
      this.logger.error('Message handling error:', error);
      return { error: error.message };
    }
  }
}

// Initialize the background script
new VoiceHypeBackground();
```

#### 2. Audio Manager (`background/core/audio-manager.js`)
```javascript
// Universal audio management that works across browsers
import { FileProcessor } from './file-processor.js';
import { Logger } from '../utils/logger.js';

export class AudioManager {
  constructor(browserAdapter, configManager) {
    this.browser = browserAdapter;
    this.config = configManager;
    this.fileProcessor = new FileProcessor();
    this.logger = new Logger();

    this.isRecording = false;
    this.mediaRecorder = null;
    this.audioChunks = [];
    this.stream = null;
  }

  async startRecording(options = {}) {
    if (this.isRecording) {
      throw new Error('Already recording');
    }

    try {
      // Request microphone access
      this.stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: options.sampleRate || 16000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true
        }
      });

      // Set up MediaRecorder
      const mimeType = this.getSupportedMimeType();
      this.mediaRecorder = new MediaRecorder(this.stream, { mimeType });

      this.audioChunks = [];
      this.isRecording = true;

      // Set up event handlers
      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.audioChunks.push(event.data);

          // For real-time transcription
          if (options.realtime) {
            this.handleRealtimeChunk(event.data);
          }
        }
      };

      this.mediaRecorder.onstop = () => {
        this.handleRecordingStop(options);
      };

      // Start recording with appropriate time slice
      const timeSlice = options.realtime ? 100 : 1000; // 100ms for real-time, 1s for regular
      this.mediaRecorder.start(timeSlice);

      // Notify content scripts
      this.browser.sendMessageToAllTabs({
        type: 'recordingStarted',
        options
      });

      this.logger.info('Recording started', options);
      return { success: true };

    } catch (error) {
      this.logger.error('Failed to start recording:', error);
      throw error;
    }
  }

  async stopRecording() {
    if (!this.isRecording || !this.mediaRecorder) {
      throw new Error('Not currently recording');
    }

    this.isRecording = false;
    this.mediaRecorder.stop();

    // Stop all tracks
    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
      this.stream = null;
    }

    // Notify content scripts
    this.browser.sendMessageToAllTabs({
      type: 'recordingStopped'
    });

    return { success: true };
  }

  async handleRecordingStop(options) {
    try {
      // Combine audio chunks
      const audioBlob = new Blob(this.audioChunks, {
        type: this.getSupportedMimeType()
      });

      // Convert to base64 for API call (same as VS Code extension)
      const base64Audio = await this.blobToBase64(audioBlob);

      // Call VoiceHype Supabase edge function
      const result = await this.supabaseService.transcribeAudio(
        base64Audio,
        options
      );

      // Notify content scripts with result
      this.browser.sendMessageToAllTabs({
        type: 'transcriptionComplete',
        result
      });

      this.logger.info('Recording processed successfully');

    } catch (error) {
      this.logger.error('Failed to process recording:', error);

      // Notify content scripts of error
      this.browser.sendMessageToAllTabs({
        type: 'transcriptionError',
        error: error.message
      });
    }
  }

  async blobToBase64(blob) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const base64 = reader.result.split(',')[1]; // Remove data:audio/webm;base64, prefix
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }

  getSupportedMimeType() {
    const types = [
      'audio/webm;codecs=opus',
      'audio/webm',
      'audio/mp4',
      'audio/ogg;codecs=opus'
    ];

    return types.find(type => MediaRecorder.isTypeSupported(type)) || 'audio/webm';
  }

  async handleRealtimeChunk(chunk) {
    // Convert chunk to format suitable for real-time transcription
    // This will be implemented based on Assembly AI requirements
    // For now, just log that we received a chunk
    this.logger.debug('Real-time chunk received:', chunk.size, 'bytes');
  }
}
```

#### 3. Supabase Service (`background/core/supabase-service.js`)
```javascript
// VoiceHype Supabase edge functions client (same as VS Code extension)
export class SupabaseService {
  constructor(configManager) {
    this.config = configManager;
    this.supabaseUrl = 'https://nffixzoqnqxpcqpcxpps.supabase.co';
    this.logger = new Logger();
  }

  async getApiKey() {
    return await this.config.getApiKey();
  }

  // Transcribe audio using VoiceHype edge function
  async transcribeAudio(base64Audio, options = {}) {
    const apiKey = await this.getApiKey();
    if (!apiKey) {
      throw new Error('No API key found. Please authenticate first.');
    }

    const requestBody = {
      audio: base64Audio,
      service: options.service || 'lemonfox',
      model: options.model || 'whisper-1',
      language: options.language || 'en',
      translate: options.translate || false,
      realtime: options.realtime || false
    };

    // If optimization is enabled, use transcribeAndOptimize
    if (options.optimize) {
      return await this.transcribeAndOptimize(requestBody, options);
    }

    const response = await fetch(`${this.supabaseUrl}/functions/v1/transcribe`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'apikey': apiKey
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Transcription failed: ${errorText}`);
    }

    return await response.json();
  }

  // Transcribe and optimize in one call
  async transcribeAndOptimize(transcribeRequest, options) {
    const apiKey = await this.getApiKey();

    const requestBody = {
      ...transcribeRequest,
      optimizationModel: options.optimizationModel || 'claude-3-5-sonnet-20241022',
      customPrompt: options.customPrompt || 'Optimize this transcription for clarity and conciseness:',
      voiceCommandsEnabled: options.voiceCommandsEnabled !== false
    };

    const response = await fetch(`${this.supabaseUrl}/functions/v1/transcribe-and-optimize`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'apikey': apiKey
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Transcription and optimization failed: ${errorText}`);
    }

    return await response.json();
  }

  // Get user usage data (same as website)
  async getUserUsage() {
    const apiKey = await this.getApiKey();

    const response = await fetch(`${this.supabaseUrl}/functions/v1/get-usage`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'apikey': apiKey
      }
    });

    if (!response.ok) {
      throw new Error('Failed to fetch usage data');
    }

    return await response.json();
  }

  // Validate API key
  async validateApiKey(apiKey) {
    const response = await fetch(`${this.supabaseUrl}/functions/v1/validate-api-key`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      }
    });

    return response.ok;
  }
}
```

#### 4. Browser Adapter (`background/adapters/browser-adapter.js`)
```javascript
// Abstraction layer for browser-specific APIs
export class BrowserAdapter {
  constructor() {
    // Detect browser environment
    this.isChrome = typeof chrome !== 'undefined' && chrome.runtime;
    this.isFirefox = typeof browser !== 'undefined' && browser.runtime;

    // Use appropriate API
    this.api = this.isChrome ? chrome : browser;
  }

  // Storage methods
  async getStorage(key) {
    return new Promise((resolve) => {
      this.api.storage.sync.get(key, (result) => {
        resolve(result[key]);
      });
    });
  }

  async setStorage(key, value) {
    return new Promise((resolve) => {
      this.api.storage.sync.set({ [key]: value }, resolve);
    });
  }

  async getLocalStorage(key) {
    return new Promise((resolve) => {
      this.api.storage.local.get(key, (result) => {
        resolve(result[key]);
      });
    });
  }

  async setLocalStorage(key, value) {
    return new Promise((resolve) => {
      this.api.storage.local.set({ [key]: value }, resolve);
    });
  }

  // Messaging methods
  onMessage(callback) {
    this.api.runtime.onMessage.addListener((message, sender, sendResponse) => {
      // Handle async responses properly
      const result = callback(message, sender, sendResponse);
      if (result instanceof Promise) {
        result.then(sendResponse);
        return true; // Keep message channel open
      }
      return false;
    });
  }

  async sendMessageToTab(tabId, message) {
    return new Promise((resolve) => {
      this.api.tabs.sendMessage(tabId, message, resolve);
    });
  }

  async sendMessageToAllTabs(message) {
    const tabs = await this.getAllTabs();
    return Promise.all(
      tabs.map(tab => this.sendMessageToTab(tab.id, message))
    );
  }

  async getAllTabs() {
    return new Promise((resolve) => {
      this.api.tabs.query({}, resolve);
    });
  }

  async getActiveTab() {
    return new Promise((resolve) => {
      this.api.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        resolve(tabs[0]);
      });
    });
  }

  // Permissions
  async requestPermission(permission) {
    return new Promise((resolve) => {
      this.api.permissions.request({ permissions: [permission] }, resolve);
    });
  }

  async hasPermission(permission) {
    return new Promise((resolve) => {
      this.api.permissions.contains({ permissions: [permission] }, resolve);
    });
  }
}
```

#### 5. Authentication Service (`background/core/auth-service.js`)
```javascript
// Authentication service that matches the website pattern
import { createClient } from '@supabase/supabase-js';

export class AuthService {
  constructor(browserAdapter, configManager) {
    this.browser = browserAdapter;
    this.config = configManager;
    this.supabaseUrl = 'https://nffixzoqnqxpcqpcxpps.supabase.co';
    this.supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'; // Your anon key

    // Same Supabase client as website
    this.supabase = createClient(this.supabaseUrl, this.supabaseAnonKey);
    this.secureStorage = new SecureStorage(browserAdapter);
  }

  // OAuth authentication (same flow as website)
  async authenticate() {
    try {
      // Open OAuth flow in new tab (similar to VS Code extension)
      const authUrl = `${this.supabaseUrl}/functions/v1/web-extension-auth`;

      // Create new tab for authentication
      const tab = await this.browser.createTab(authUrl);

      // Wait for authentication completion
      return new Promise((resolve, reject) => {
        const checkAuth = setInterval(async () => {
          try {
            // Check if tab is closed (user cancelled)
            const tabInfo = await this.browser.getTab(tab.id);
            if (!tabInfo) {
              clearInterval(checkAuth);
              reject(new Error('Authentication cancelled'));
              return;
            }

            // Check for auth completion in storage
            const authResult = await this.browser.getStorage('vh_auth_result');
            if (authResult) {
              clearInterval(checkAuth);
              await this.browser.removeStorage('vh_auth_result');
              await this.browser.closeTab(tab.id);

              if (authResult.success) {
                await this.secureStorage.storeApiKey(authResult.apiKey);
                resolve(authResult);
              } else {
                reject(new Error(authResult.error));
              }
            }
          } catch (error) {
            clearInterval(checkAuth);
            reject(error);
          }
        }, 1000);

        // Timeout after 5 minutes
        setTimeout(() => {
          clearInterval(checkAuth);
          reject(new Error('Authentication timeout'));
        }, 300000);
      });
    } catch (error) {
      throw new Error(`Authentication failed: ${error.message}`);
    }
  }

  // Get current user data (same as website)
  async getUserData() {
    try {
      const apiKey = await this.secureStorage.getApiKey();
      if (!apiKey) {
        return { authenticated: false };
      }

      // Validate API key and get user data
      const { data: session } = await this.supabase.auth.getSession();

      if (session?.user) {
        return {
          authenticated: true,
          user: {
            id: session.user.id,
            email: session.user.email,
            fullName: session.user.user_metadata?.full_name,
            avatarUrl: session.user.user_metadata?.avatar_url
          }
        };
      }

      return { authenticated: false };
    } catch (error) {
      console.error('Error getting user data:', error);
      return { authenticated: false };
    }
  }

  // Sign out (clear all data)
  async signOut() {
    try {
      // Clear API key
      await this.secureStorage.clearApiKey();

      // Clear Supabase session
      await this.supabase.auth.signOut();

      // Clear all extension storage
      await this.browser.clearAllStorage();

      return { success: true };
    } catch (error) {
      throw new Error(`Sign out failed: ${error.message}`);
    }
  }
}
```

### Vue.js Frontend Implementation

#### 1. Shared Recording Composable (`frontend/shared/composables/useRecording.js`)
```javascript
// Vue 3 Composition API for recording functionality
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { BrowserUtils } from '../utils/browser-utils.js';

export function useRecording() {
  const isRecording = ref(false);
  const isPaused = ref(false);
  const elapsedTime = ref(0);
  const currentTranscription = ref('');
  const isProcessing = ref(false);

  let timer = null;
  let startTime = null;

  const browser = new BrowserUtils();

  // Computed properties
  const recordingStatus = computed(() => {
    if (isProcessing.value) return 'processing';
    if (isRecording.value && isPaused.value) return 'paused';
    if (isRecording.value) return 'recording';
    return 'idle';
  });

  const formattedTime = computed(() => {
    const minutes = Math.floor(elapsedTime.value / 60);
    const seconds = elapsedTime.value % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  });

  // Methods
  const startRecording = async (options = {}) => {
    try {
      const result = await browser.sendMessage({
        action: 'startRecording',
        options
      });

      if (result.success) {
        isRecording.value = true;
        isPaused.value = false;
        startTime = Date.now();
        startTimer();
      }
    } catch (error) {
      console.error('Failed to start recording:', error);
      throw error;
    }
  };

  const stopRecording = async () => {
    try {
      const result = await browser.sendMessage({
        action: 'stopRecording'
      });

      if (result.success) {
        isRecording.value = false;
        isPaused.value = false;
        isProcessing.value = true;
        stopTimer();
      }
    } catch (error) {
      console.error('Failed to stop recording:', error);
      throw error;
    }
  };

  const pauseRecording = async () => {
    // Implementation for pause functionality
    isPaused.value = true;
    stopTimer();
  };

  const resumeRecording = async () => {
    // Implementation for resume functionality
    isPaused.value = false;
    startTimer();
  };

  // Timer functions
  const startTimer = () => {
    timer = setInterval(() => {
      elapsedTime.value = Math.floor((Date.now() - startTime) / 1000);
    }, 1000);
  };

  const stopTimer = () => {
    if (timer) {
      clearInterval(timer);
      timer = null;
    }
  };

  const resetTimer = () => {
    elapsedTime.value = 0;
    stopTimer();
  };

  // Message listeners
  const handleMessage = (message) => {
    switch (message.type) {
      case 'recordingStarted':
        isRecording.value = true;
        break;
      case 'recordingStopped':
        isRecording.value = false;
        isProcessing.value = true;
        break;
      case 'transcriptionComplete':
        isProcessing.value = false;
        currentTranscription.value = message.result.text;
        resetTimer();
        break;
      case 'transcriptionError':
        isProcessing.value = false;
        resetTimer();
        break;
      case 'realtimeUpdate':
        currentTranscription.value = message.text;
        break;
    }
  };

  // Lifecycle
  onMounted(() => {
    browser.onMessage(handleMessage);
  });

  onUnmounted(() => {
    stopTimer();
    browser.removeMessageListener(handleMessage);
  });

  return {
    // State
    isRecording,
    isPaused,
    elapsedTime,
    currentTranscription,
    isProcessing,

    // Computed
    recordingStatus,
    formattedTime,

    // Methods
    startRecording,
    stopRecording,
    pauseRecording,
    resumeRecording,
    resetTimer
  };
}
```

#### 2. Main Popup Component (`frontend/popup/PopupApp.vue`)
```vue
<template>
  <div class="voicehype-popup">
    <!-- Header -->
    <div class="header">
      <div class="logo">
        <img src="/assets/logo.svg" alt="VoiceHype" />
        <span>VoiceHype</span>
      </div>
      <AuthStatus />
    </div>

    <!-- Recording Controls -->
    <div class="recording-section">
      <RecordingControls
        :is-recording="isRecording"
        :is-paused="isPaused"
        :elapsed-time="formattedTime"
        :current-transcription="currentTranscription"
        :is-processing="isProcessing"
        @start="handleStartRecording"
        @stop="handleStopRecording"
        @pause="handlePauseRecording"
        @resume="handleResumeRecording"
      />
    </div>

    <!-- Configuration -->
    <div class="config-section">
      <ServiceSelector v-model="config.service" />
      <ModelSelector v-model="config.model" :service="config.service" />
      <LanguageSelector v-model="config.language" />

      <div class="optimization-section">
        <label class="switch">
          <input
            type="checkbox"
            v-model="config.optimizeEnabled"
          />
          <span class="slider">AI Optimization</span>
        </label>

        <OptimizationSelector
          v-if="config.optimizeEnabled"
          v-model="config.optimizationModel"
        />
      </div>

      <div class="realtime-section">
        <label class="switch">
          <input
            type="checkbox"
            v-model="config.realtimeEnabled"
          />
          <span class="slider">Real-time Transcription</span>
        </label>
      </div>
    </div>

    <!-- File Upload -->
    <div class="upload-section">
      <FileUploader @upload="handleFileUpload" />
    </div>

    <!-- Recent History -->
    <div class="history-section">
      <HistoryList :items="recentHistory" @select="handleHistorySelect" />
    </div>

    <!-- Footer -->
    <div class="footer">
      <button @click="openOptions" class="settings-btn">
        Settings
      </button>
      <button @click="openWebsite" class="website-btn">
        Dashboard
      </button>
    </div>
  </div>
</template>

<script>
import { defineComponent } from 'vue';
import { useRecording } from '../shared/composables/useRecording.js';
import { useConfig } from '../shared/composables/useConfig.js';
import { useHistory } from '../shared/composables/useHistory.js';

import RecordingControls from '../shared/components/RecordingControls.vue';
import ServiceSelector from '../shared/components/ServiceSelector.vue';
import ModelSelector from '../shared/components/ModelSelector.vue';
import LanguageSelector from '../shared/components/LanguageSelector.vue';
import OptimizationSelector from '../shared/components/OptimizationSelector.vue';
import AuthStatus from '../shared/components/AuthStatus.vue';
import HistoryList from '../shared/components/HistoryList.vue';
import FileUploader from '../shared/components/FileUploader.vue';

export default defineComponent({
  name: 'PopupApp',
  components: {
    RecordingControls,
    ServiceSelector,
    ModelSelector,
    LanguageSelector,
    OptimizationSelector,
    AuthStatus,
    HistoryList,
    FileUploader
  },
  setup() {
    const recording = useRecording();
    const config = useConfig();
    const history = useHistory();

    const handleStartRecording = async () => {
      await recording.startRecording({
        service: config.service.value,
        model: config.model.value,
        language: config.language.value,
        optimize: config.optimizeEnabled.value,
        optimizationModel: config.optimizationModel.value,
        realtime: config.realtimeEnabled.value
      });
    };

    const handleStopRecording = async () => {
      await recording.stopRecording();
    };

    const handlePauseRecording = async () => {
      await recording.pauseRecording();
    };

    const handleResumeRecording = async () => {
      await recording.resumeRecording();
    };

    const handleFileUpload = async (file) => {
      // Handle file upload logic
      console.log('File uploaded:', file);
    };

    const handleHistorySelect = (item) => {
      // Handle history item selection
      console.log('History item selected:', item);
    };

    const openOptions = () => {
      // Open options page
      chrome.runtime.openOptionsPage();
    };

    const openWebsite = () => {
      // Open VoiceHype website
      chrome.tabs.create({ url: 'https://voicehype.netlify.app/app' });
    };

    return {
      // Recording
      ...recording,

      // Config
      config: config.config,

      // History
      recentHistory: history.recentItems,

      // Methods
      handleStartRecording,
      handleStopRecording,
      handlePauseRecording,
      handleResumeRecording,
      handleFileUpload,
      handleHistorySelect,
      openOptions,
      openWebsite
    };
  }
});
</script>

<style scoped>
.voicehype-popup {
  width: 400px;
  min-height: 500px;
  padding: 16px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e0e0e0;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 18px;
}

.logo img {
  width: 24px;
  height: 24px;
}

.recording-section {
  margin-bottom: 24px;
}

.config-section {
  margin-bottom: 20px;
}

.optimization-section,
.realtime-section {
  margin: 12px 0;
}

.switch {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.upload-section {
  margin-bottom: 20px;
}

.history-section {
  margin-bottom: 20px;
}

.footer {
  display: flex;
  gap: 12px;
  padding-top: 12px;
  border-top: 1px solid #e0e0e0;
}

.settings-btn,
.website-btn {
  flex: 1;
  padding: 8px 16px;
  border: 1px solid #d0d0d0;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  transition: background-color 0.2s;
}

.settings-btn:hover,
.website-btn:hover {
  background-color: #f5f5f5;
}
</style>
```

## Next Steps

1. **Create base file structure** for web extension
2. **Implement universal background script** with browser adapter
3. **Set up Vue.js frontend** with shared components
4. **Build content script** for text field detection
5. **Test cross-browser compatibility**

---

*May Allah bless this work and make it beneficial for the ummah. Ameen.*

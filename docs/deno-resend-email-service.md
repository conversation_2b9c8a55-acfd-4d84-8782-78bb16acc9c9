# Supabase External Email Service with Resend API

## Overview

This document explains the updated solution for handling email sending from a self-hosted Supabase instance using Resend.com's API instead of SMTP. This approach solves the issue with both DigitalOcean and Deno Deploy blocking outgoing SMTP ports.

## Problem Evolution

1. **Initial Problem**: DigitalOcean blocks outgoing SMTP traffic on ports 25, 465, and 587
2. **First Solution Attempt**: Using Deno Deploy as an external email service with SMTP
3. **Second Problem**: Discovered that <PERSON><PERSON> Deploy also blocks outgoing SMTP connections
4. **Final Solution**: Using Resend.com's HTTP API for sending emails through Deno Deploy

## Solution Architecture

```
┌─────────────────┐        ┌────────────────┐        ┌───────────────┐
│  Supabase Auth  │  HTTP  │  Deno Deploy   │  HTTP  │  Resend.com   │
│    (GoTrue)     │───────▶│  Email Service │───────▶│     API       │
└─────────────────┘        └────────────────┘        └───────────────┘
```

## Implementation Steps

### 1. Sign up for Resend

1. Go to [Resend.com](https://resend.com) and sign up for an account
2. Create an API key from the dashboard
3. Verify your domain for better deliverability

### 2. Update the Deno Deploy Email Service

1. Use the new `email-service.new.ts` file which uses Resend API instead of SMTP
2. Configure environment variables in Deno Deploy:
   - `RESEND_API_KEY`: Your Resend API key
   - `FROM_EMAIL`: The verified email to send from
   - `FROM_NAME`: The sender name (e.g., "VoiceHype")
   - `WEBHOOK_SECRET`: The secret to validate requests

### 3. Deploy the Updated Service

1. Rename `email-service.new.ts` to `email-service.ts`
2. Sign up for Resend and get your API key
3. Update your `.env` file with your Resend API key
4. Deploy with:

```bash
./quick-deploy.sh
```

### 4. Test the Email Service

1. Try signing up a new user in your application
2. Check the Deno Deploy logs for any errors
3. Verify that the confirmation email is received

## Benefits of this Approach

1. **No Port Restrictions**: Uses HTTP APIs instead of SMTP ports
2. **Better Deliverability**: Resend focuses on email deliverability
3. **Modern Email Templates**: The updated service includes nicely formatted HTML templates
4. **Debugging Tools**: Resend provides debugging tools and delivery monitoring
5. **Free Tier**: Resend offers a generous free tier (3,000 emails/month)

## Troubleshooting

1. **Debug Mode**: The service includes detailed logging for troubleshooting
2. **Test Endpoint**: Visit the `/test` endpoint to verify the service is running
3. **Webhook Format**: The service now properly handles the Supabase Auth webhook data format

## Security Considerations

1. **Webhook Secret**: Still using the webhook secret for validation
2. **CORS**: Set to allow all origins temporarily for debugging, should be restricted in production
3. **API Key**: Keep your Resend API key secure

## Recommendation

Continue using this approach as it's more reliable than SMTP and avoids port restrictions on cloud platforms. The Resend API is designed specifically for transactional emails like authentication messages and provides better deliverability and analytics than direct SMTP.

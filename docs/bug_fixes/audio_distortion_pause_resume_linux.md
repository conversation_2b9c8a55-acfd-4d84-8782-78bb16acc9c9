# Audio Distortion Bug After Pause/Resume on Linux

## Issue Description

When recording audio on Linux using the `node-microphone` package, pausing and resuming the recording causes audio distortion in the segments recorded after resuming. The distortion makes the audio sound like a robot is speaking, with words being repeated multiple times and stretched out.

The issue only affects the audio segments recorded after resuming from a pause, while the initial recording segment (before pausing) sounds normal.

## Root Cause Analysis

The issue occurs due to how audio data is handled during pause and resume operations when using the `node-microphone` package on Linux:

1. **Pause Operation**:
   - When recording is paused, the code calls `this.nodeMic.stopRecording()` and unpipes the output stream from the PassThrough stream
   - This completely stops the recording process

2. **Resume Operation**:
   - When recording is resumed, a new recording stream is created with `this.nodeMic.startRecording()`
   - This new stream is piped to the same PassThrough stream that was used before
   - The PassThrough stream then continues to pipe to the write stream that's writing to the WAV file

3. **The Problem**:
   - When a new recording stream is created, it's starting a completely new audio capture process
   - However, the WAV file already has a header and some audio data from the first recording segment
   - The new recording stream is likely generating its own WAV header or raw PCM data that's being appended directly to the existing file
   - This creates a malformed WAV file with multiple headers or misaligned audio data

4. **Why it sounds like a robot/duplicated**:
   - The WAV file format has a specific structure with a header that defines the audio format
   - When you append raw audio data or another WAV file to an existing WAV file without proper handling, the player tries to interpret all the data as audio samples
   - This can cause the audio to sound distorted, slowed down, or like a robot because the header information is being interpreted as audio data
   - The duplication effect is likely because the sample rate or bit depth information is incorrect after the first segment

## Potential Solutions

### Solution 1: Extract Audio Data Without Headers

Instead of directly appending the new recording stream to the existing file, extract just the audio data (without WAV headers) from the new recording and append it to the existing file.

**Pros:**
- Maintains a single WAV file
- Relatively simple to implement
- Preserves the original WAV header

**Cons:**
- Requires understanding of WAV file format
- May need to handle different audio formats

### Solution 2: Create Separate Segments and Merge

Create separate WAV files for each recording segment (before pause, after resume) and properly merge them afterward using a library that understands the WAV format.

**Pros:**
- Clean separation of recording segments
- Each segment has a valid WAV format
- More robust handling of different audio formats

**Cons:**
- More complex implementation
- Requires temporary file management
- May introduce a delay when stopping recording

### Solution 3: Stream Buffering

Instead of stopping and starting the recording process, implement a buffering mechanism that temporarily stops writing to the file but keeps the recording process running.

**Pros:**
- Avoids the need to restart the recording process
- Maintains a single continuous audio stream
- No issues with WAV headers

**Cons:**
- More complex to implement
- May still capture unwanted audio during pause
- May not work well with the underlying `node-microphone` implementation

### Solution 4: Use Raw PCM Throughout

Work with raw PCM data throughout the recording process and only convert to WAV at the end when the recording is stopped.

**Pros:**
- Avoids issues with WAV headers
- Clean handling of audio data
- More control over the final file format

**Cons:**
- Requires more complex file handling
- Need to manually create WAV header at the end
- May require changes to how audio is processed throughout the application

### Solution 5: Use SoX on Linux

Since this issue is specific to Linux with `node-microphone`, consider using the SoX approach on Linux as well, which might handle pause/resume differently.

**Pros:**
- Consistent approach across platforms
- SoX may have better handling of pause/resume
- Leverages existing code that works on other platforms

**Cons:**
- May introduce new issues specific to SoX on Linux
- Requires changes to platform detection logic
- May not be as efficient as `node-microphone` for Linux

## Testing Plan

To determine the best solution, we will create test scripts for each approach and evaluate them based on:

1. Audio quality after pause/resume
2. Reliability across different pause durations
3. Performance impact
4. Implementation complexity

## Implementation Timeline

1. Create test scripts for each solution (1-2 days)
2. Evaluate results and select the best approach (1 day)
3. Implement the chosen solution in the main codebase (1-2 days)
4. Test the implementation in various scenarios (1 day)
5. Document the fix and update the codebase (1 day)

## References

- [WAV File Format Specification](https://www.mmsp.ece.mcgill.ca/Documents/AudioFormats/WAVE/WAVE.html)
- [Node.js Stream Documentation](https://nodejs.org/api/stream.html)
- [node-microphone Package](https://www.npmjs.com/package/node-microphone)

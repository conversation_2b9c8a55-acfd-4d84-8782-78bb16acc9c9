# Fixing Bugs in VoiceHype Payment Systems

Assalamualaikum rahmatull<PERSON> wa barakatuh.

Today, I'll outline our plan to address some critical bugs in VoiceHype's payment systems, <PERSON>shaaAllah. Before diving into the specific fixes, it's important to reiterate that we operate with three distinct pricing models:

## Our Three Pricing Models

1. **Subscription-Based Model**
   - Users either have a free subscription or purchase a recurring monthly/yearly plan
   - Each subscription tier provides a specific allocation of transcription minutes and tokens for LLM interactions
   - These allocations reset at the beginning of each billing cycle

2. **Credits System**
   - A pre-paid model where users purchase credits in advance
   - Users can only utilize services if they have sufficient credits in their account
   - Credits are consumed as services are used

3. **Pay-As-You-Go Model**
   - Users are assigned a credit allowance (for example, $25)
   - They can use services up to this allowance limit
   - At the end of the month, they pay for whatever amount they've actually used

## Bug Fixes Implementation

### 1. Fixing Credits System Bug

The main issue in the credits system is with optimization. To resolve this:

- We need to calculate the maximum tokens a user can afford by dividing their available credits by the cost per token of the selected model
- We'll take the minimum value between 4096 (our maximum limit) and the affordable tokens
- Since we cannot predict exact token usage until after the service is used, we need to allow for negative balances
- The credits balance should be programmed to go negative if a user ends up using more tokens than initially calculated

### 2. Fixing Subscription Model Bug

For subscription-based optimization:

- We'll take the minimum between the user's remaining quota and 4096
- Allow users to use the optimization service as long as their used amount is less than their total allocation
- Both the used amount and total amount can be fetched from the quotas table
- The implementation should be parametric to accommodate different subscription tiers with varying allocations

### 3. Fixing Pay-As-You-Go Bugs

Unlike the previous two models where bugs only affect optimization, in pay-as-you-go we need to fix both optimization and transcription issues:

**For Optimization:**
- Calculate how many tokens a user can afford based on their remaining credit allowance
- For example, if a user with a $25 total allowance has used $10, they have $15 remaining
- Convert this remaining allowance to tokens by dividing by the cost per token
- Take the minimum between this calculated value and 4096

**For Transcription:**
- Calculate the maximum minutes a user can afford by dividing their remaining credit allowance by the cost per minute for their selected model

In pay-as-you-go, we don't need to worry about negative balances since we simply bill for whatever the user consumes. If they slightly exceed their credit allowance (e.g., reaching $25.50 instead of $25), we'll bill them for the actual amount used.

The reason we can't be more precise is that we cannot predict exactly how many tokens an LLM will use until after the request is completed. Only then can we determine the actual number of input and output tokens consumed.

That covers the necessary fixes for all three pricing models, inshaAllah.


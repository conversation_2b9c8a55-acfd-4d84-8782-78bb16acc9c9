# VoiceHype Extension Migration Plan
## Pricing & Model Updates - Comprehensive Implementation Guide

**Date**: August 6, 2025  
**Status**: Planning Phase  
**Priority**: High

---

## Executive Summary

This document outlines the complete migration plan for VoiceHype extension, focusing on pricing changes, model updates, and system improvements. The migration involves changing from Claude-based models to new AI models, updating pricing structure, and making the system parametric for future model additions.

**Key Additions**: Fixed stats display issue and unified pricing UI design across all platforms.

---

## Phase 1: Core Pricing & Model Migration

### 1.1 Pricing Structure Changes

**Current Pricing**: $9, $18, $27
**New Pricing**: $4.99, $9.99, $14.99

**Updated Quota Based on Claude Sonnet 4**:
- **Basic Plan**: $4.99 → 422 minutes (7.03 hours)
- **Pro Plan**: $9.99 → 846 minutes (14.1 hours)
- **Premium Plan**: $14.99 → 1269 minutes (21.15 hours)

**Token Calculations**:
- **Basic Plan**: 219,623 tokens
- **Pro Plan**: 439,686 tokens
- **Premium Plan**: 659,749 tokens

**Cost Breakdown**:
- **Cost per minute**: $0.011815
- **Tokens per minute**: 520 (370 input + 150 output)

**Implementation Details**:
- Display ".99" in smaller font for psychological pricing effect
- Update subscription plans table in database
- Modify Paddle checkout functions to use new pricing
- Update landing page pricing display
- **NEW**: Unify pricing UI design across landing page and dashboard
- **NEW**: Ensure pricing table matches landing page theme (dark gradient, modern styling)
- **NEW**: Add bonus minutes as limited-time offer (120/240/360 bonus minutes)

### 1.2 Model Migration

**Models to Remove**:
- Claude (all versions)

**New Models to Add**:
- GLM 4.5
- Qwen 3 Coder
- Kimi K2
- DeepSeek
- Llama 70B (70 billion parameter)

---

## Phase 2: Database & Infrastructure Updates

### 2.1 New Database Schema

**Create `models` table**:
```sql
CREATE TABLE models (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    friendly_name VARCHAR(255) NOT NULL,
    actual_url TEXT NOT NULL,
    model_type VARCHAR(50) DEFAULT 'optimization',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Create `usage_stats` table**:
```sql
CREATE TABLE usage_stats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id),
    date DATE NOT NULL,
    minutes_used INTEGER DEFAULT 0,
    prompts_generated INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, date)
);
```

**Add indexes for performance**:
- Index on API key hashes
- Index on model friendly names
- Index on subscription plan IDs
- Index on usage_stats (user_id, date DESC)

### 2.2 Remove Hardcoded Values

**Edge Functions to Update**:
- `paddle/create-paddle-checkout/index.ts`
- `paddle/process-monthly-payg/index.ts`
- `optimize/index.ts`
- All Paddle webhook handlers

**Implementation**:
- Replace hardcoded model URLs with dynamic fetching from models table
- Replace hardcoded pricing with subscription plans table values
- Replace hardcoded quotas with dynamic subscription data

---

## Phase 3: User Interface Updates

### 3.1 Web View Updates

**Extension Webview Changes**:
- Update model selector to fetch from public models table
- Add retry fetch functionality with fallback to default models
- Display informative messages for outdated extension versions
- Update subscription cards with new models and pricing
- Add pricing table tab to dashboard
- **NEW**: Fix stats display - last day not showing on VoiceHype website
- **NEW**: Add usage statistics visualization with proper date handling

**Error Handling**:
- Show specific error when requesting deprecated models (Claude)
- Display "Please update to latest version" message
- Show fallback models when fetch fails

### 3.2 Landing Page Updates

**Current Issues**:
- Pricing table needs complete overhaul
- Font size needs to be increased for better readability
- Add new models to feature descriptions
- **NEW**: Stats display issue - last day not showing

**Updates Required**:
- Replace hardcoded pricing with new structure
- Add model comparison table
- Update feature descriptions with new models
- Increase base font size across the page
- **NEW**: Ensure pricing table design matches landing page theme
- **NEW**: Add unified pricing component that can be reused across landing page and dashboard

**Design Specifications**:
- Dark gradient background (#0a0a0a to #1a1a1a)
- Neon green accents (#14F195)
- Modern glassmorphism effects
- Responsive grid layout
- Hover animations and transitions

---

## Phase 4: Configuration & Settings

### 4.1 Payment Configuration

**Credit System Changes**:
- Change minimum credit insertable from $5 to $1
- Keep maximum at $100
- Update validation in all payment forms

### 4.2 API Security

**Current Issue**: API key exposed in VS Code settings  
**Solution**:
- Audit current API key storage mechanism
- Ensure secure storage using VS Code SecretStorage API
- Remove any plaintext storage in settings.json
- Add migration script for existing exposed keys

---

## Phase 5: Edge Function Updates

### 5.1 Optimization Service

**File**: `supabase/functions/optimize/index.ts`

**Changes Required**:
- Update to use new models table for model mapping
- Implement subscription to models table changes
- Remove all hardcoded model references
- Add dynamic model loading

### 5.2 Paddle Integration

**Files to Update**:
- `paddle/create-paddle-checkout/index.ts`
- `paddle/process-monthly-payg/index.ts`
- `paddle/paddle-webhook/index.ts`

**Implementation**:
- Always fetch quota minutes from subscription plans table
- Remove hardcoded pricing values
- Add dynamic pricing based on subscription tier

---

## Phase 6: Additional Features & Improvements

### 6.1 Recording Management

**Current Issues**:
- Recordings overwrite same file
- No history of failed recordings
- No retry mechanism

### 6.2 Marketing & Communication Features

**Marketing Emails Design**:
- Create email templates for different user segments
- Design onboarding email sequence
- Create feature announcement templates
- Set up automated email campaigns

**Trial End Email System**:
- **Trigger**: When user's free trial ends
- **Email Content**:
  - Thank user for trying VoiceHype
  - Highlight key features they used
  - Provide upgrade options with pricing
  - Include limited-time bonus minutes offer
- **Implementation**: Add webhook handler for trial expiration

### 6.3 Extension Features

**Command Mode Addition**:
- **Purpose**: Allow users to execute commands via voice/text
- **Commands to Support**:
  - `/optimize [prompt]` - Optimize code directly
  - `/settings` - Open settings panel
  - `/stats` - Show usage statistics
  - `/upgrade` - Show subscription options
  - `/help` - Display help information
- **Implementation**: Add command parser to extension UI

**Remove Translate to English Feature**:
- **Action**: Completely remove Whisper's translate-to-English feature
- **Reason**: LLM already handles translation more effectively
- **Files to Update**:
  - Remove translation toggle from UI
  - Update Whisper configuration
  - Remove translation-related code from edge functions

### 6.4 Subscription Enhancements

**Bonus Hours Campaign**:
- **Limited Time Offer** (Inshallah):
  - **Basic Plan ($4.99)**: +2 hours bonus (120 minutes) → 542 total minutes
  - **Pro Plan ($9.99)**: +4 hours bonus (240 minutes) → 1086 total minutes
  - **Premium Plan ($14.99)**: +6 hours bonus (360 minutes) → 1629 total minutes

**Marketing Design Elements**:
- **Gift Icons**: Large, prominent gift box icons with animated sparkle effects
- **Limited Time Wrapper**:
  - Red "LIMITED TIME" banner with countdown timer
  - Flashing border animation around bonus offers
  - "🎁 FREE BONUS HOURS" text with confetti animation
- **Visual Hierarchy**:
  - Bonus hours displayed in larger font than regular minutes
  - "Save $X" calculation shown prominently
  - Urgency messaging: "Offer ends in [countdown]"
- **Color Scheme**:
  - Gold/yellow for bonus highlights
  - Red for urgency elements
  - Green checkmarks for value propositions
- **Placement**:
  - Top of pricing cards as sticky banner
  - Pop-up modal on first visit
  - Email campaign headers

**Implementation**:
- Add bonus_hours field to subscription plans table
- Create animated gift icon component
- Build countdown timer functionality
- Design responsive limited-time wrapper

### 6.5 Reward System & Gamification

**Leaderboard System**:
- **Weekly Leaderboard**: Track top users by minutes used
- **Monthly Leaderboard**: Track top users by prompts generated
- **Rewards**:
  - Top 3 users get free credits weekly
  - Monthly winners get subscription discounts
  - Special badges for achievements
- **Implementation**:
  - Create leaderboard table in database
  - Add API endpoints for leaderboard data
  - Create leaderboard UI component

### 6.6 Blog Website Development

**Astro + Vue.js Blog**:
- **Technology Stack**: Astro for static site generation, Vue.js for interactivity
- **Content Strategy**:
  - VoiceHype feature announcements
  - AI/ML optimization tips
  - Developer productivity articles
  - User success stories
- **Features**:
  - SEO-optimized blog posts
  - Newsletter signup integration
  - Social media sharing
  - RSS feeds
- **Implementation Timeline**: Phase 7 (after core migration)

### 6.7 Model Strategy Clarification

**Claude Sonnet 4 Integration**:
- **Status**: We are NOT ditching Claude - we're using Claude Sonnet 4 as our primary optimization model
- **Rationale**: Claude Sonnet 4 provides excellent code optimization at $3 input/$15 output pricing
- **Implementation**: Update all references to use Claude Sonnet 4 instead of GLM 4.5

---

## Phase 7: Marketing & Content Strategy

### 7.1 Email Marketing Infrastructure
- **Email Service**: Set up email service (Resend/SendGrid)
- **Templates**: Create responsive email templates
- **Automation**: Set up triggered email campaigns
- **Analytics**: Track open rates, click rates, conversions

### 7.2 Blog Development & Content
- **Setup**: Initialize Astro + Vue.js blog
- **Design**: Match VoiceHype branding
- **Content Calendar**: Plan 4 weeks of content
- **SEO**: Optimize for developer-focused keywords

### 7.3 Social Media Integration
- **Platforms**: Twitter, LinkedIn, Dev.to
- **Automation**: Auto-share blog posts
- **Engagement**: Respond to user feedback

---

## Phase 8: Launch & Post-Launch

### 8.1 Beta Testing
- **Internal Testing**: 1 week with team
- **Closed Beta**: 10 selected users
- **Feedback Collection**: Survey and interview users
- **Bug Fixes**: Address critical issues

### 8.2 Soft Launch
- **Gradual Rollout**: 25% of users initially
- **Monitor Metrics**: Usage, conversion, support tickets
- **A/B Testing**: Test pricing display variations

### 8.3 Full Launch
- **Marketing Campaign**: Coordinate with email/blog launch featuring bonus hours
- **Social Media**: Announce bonus hours campaign across all channels
- **Visual Assets**: Create gift-themed graphics and countdown timers
- **Support**: Prepare for increased support volume
- **Urgency Campaign**: Daily countdown posts until bonus offer expires

---

## Updated Success Metrics with Bonus Campaign

### Bonus Campaign KPIs
- **Conversion Rate Target**: 25% increase with bonus hours campaign
- **Urgency Effectiveness**: Track clicks on countdown timers
- **Visual Engagement**: Monitor gift icon interactions
- **Social Shares**: Track bonus campaign social media shares

### A/B Testing for Bonus Campaign
- **Test Variations**:
  - Gift icon sizes (small vs large)
  - Countdown timer positions (top vs bottom)
  - Color schemes (gold vs red urgency)
  - Bonus hour messaging (2/4/6 vs 120/240/360 minutes)

---

## Final Updated Pricing Summary

| Plan | Price | Base Minutes | Bonus Hours | Total Minutes | Marketing Message |
|------|-------|--------------|-------------|---------------|-------------------|
| Basic | $4.99 | 422 | 2 hours | 542 | "Get 542 minutes + 🎁 2 HOURS FREE!" |
| Pro | $9.99 | 846 | 4 hours | 1086 | "Get 1086 minutes + 🎁 4 HOURS FREE!" |
| Premium | $14.99 | 1269 | 6 hours | 1629 | "Get 1629 minutes + 🎁 6 HOURS FREE!" |

**Marketing Copy Examples**:
- "🎁 LIMITED TIME: Get 2-6 HOURS of bonus transcription!"
- "⏰ Only 48 hours left! Claim your FREE bonus hours"
- "🎉 Launch celebration: Extra hours for early adopters"

Bismillahir rahmanir raheem. May this bonus campaign bring abundant barakah and user growth.

---

## Technical Implementation Checklist

### Database Updates
- Update subscription plans table with new pricing
- Add bonus_minutes field to plans
- Create leaderboard table
- Add email_campaigns table
- Update models table for Claude Sonnet 4

### Edge Functions
- Update optimize function for Claude Sonnet 4
- Add trial expiration webhook handler
- Create leaderboard API endpoints
- Remove translation-related code

### Extension Updates
- Add command mode parser
- Remove translation toggle
- Update pricing display
- Add bonus minutes indicator

### Website Updates
- Update pricing calculator
- Add bonus minutes banners
- Create leaderboard page
- Update feature descriptions

### Marketing Setup
- Configure email service
- Create email templates
- Set up blog with Astro
- Create social media accounts

---

## Success Metrics

### Financial Metrics
- **Target Revenue**: 2x current monthly revenue within 3 months
- **Conversion Rate**: 15% trial-to-paid conversion
- **Churn Rate**: <5% monthly churn

### User Engagement
- **Daily Active Users**: 50% increase
- **Feature Usage**: Track command mode adoption
- **Leaderboard Participation**: 30% of active users

### Marketing Metrics
- **Email Open Rate**: >25%
- **Blog Traffic**: 1000 monthly visitors by month 3
- **Social Media Growth**: 500 followers across platforms

---

## Risk Mitigation

### Technical Risks
- **Performance**: Monitor Claude Sonnet 4 response times
- **Cost Overruns**: Set up cost alerts and usage limits
- **Compatibility**: Test across all VS Code versions

### Business Risks
- **Pricing Sensitivity**: A/B test pricing display
- **Competition**: Monitor competitor pricing/features
- **User Feedback**: Set up feedback collection system

---

## Timeline Summary

| Phase | Duration | Key Deliverables |
|-------|----------|------------------|
| Phase 1-2 | 2 weeks | Core pricing & model migration |
| Phase 3-4 | 1 week | UI updates & configuration |
| Phase 5-6 | 2 weeks | Edge functions & new features |
| Phase 7 | 1 week | Marketing setup & blog |
| Phase 8 | 1 week | Testing & launch |
| **Total** | **6 weeks** | **Complete migration** |

---

## Final Notes

This migration plan incorporates all requested features including:
- ✅ Claude Sonnet 4 pricing calculations (422/846/1269 minutes)
- ✅ Marketing email design & trial end notifications
- ✅ Command mode for extension
- ✅ Removal of translate-to-English feature
- ✅ Bonus minutes campaign (100/200/300)
- ✅ Reward system with leaderboard
- ✅ Astro + Vue.js blog website
- ✅ Clarification that we're keeping Claude (Sonnet 4)

Bismillahir rahmanir raheem. May this migration bring barakah and success.

### 6.2 Marketing & Communication Features

**Marketing Emails Design**:
- Create email templates for different user segments
- Design onboarding email sequence
- Create feature announcement templates
- Set up automated email campaigns

**Trial End Email System**:
- **Trigger**: When user's free trial ends
- **Email Content**:
  - Thank user for trying VoiceHype
  - Highlight key features they used
  - Provide upgrade options with pricing
  - Include limited-time bonus minutes offer
- **Implementation**: Add webhook handler for trial expiration

### 6.3 Extension Features

**Command Mode Addition**:
- **Purpose**: Allow users to execute commands via voice/text
- **Commands to Support**:
  - `/optimize [prompt]` - Optimize code directly
  - `/settings` - Open settings panel
  - `/stats` - Show usage statistics
  - `/upgrade` - Show subscription options
  - `/help` - Display help information
- **Implementation**: Add command parser to extension UI

**Remove Translate to English Feature**:
- **Action**: Completely remove Whisper's translate-to-English feature
- **Reason**: LLM already handles translation more effectively
- **Files to Update**:
  - Remove translation toggle from UI
  - Update Whisper configuration
  - Remove translation-related code from edge functions

### 6.4 Subscription Enhancements

**Bonus Minutes Campaign**:
- **Limited Time Offer**:
  - **Basic Plan**: +100 bonus minutes (522 total)
  - **Pro Plan**: +200 bonus minutes (1046 total)
  - **Premium Plan**: +300 bonus minutes (1569 total)
- **Display**: Show bonus prominently on pricing cards
- **Implementation**: Add bonus_minutes field to subscription plans table

### 6.5 Reward System & Gamification

**Leaderboard System**:
- **Weekly Leaderboard**: Track top users by minutes used
- **Monthly Leaderboard**: Track top users by prompts generated
- **Rewards**:
  - Top 3 users get free credits weekly
  - Monthly winners get subscription discounts
  - Special badges for achievements
- **Implementation**:
  - Create leaderboard table in database
  - Add API endpoints for leaderboard data
  - Create leaderboard UI component

### 6.6 Blog Website Development

**Astro + Vue.js Blog**:
- **Technology Stack**: Astro for static site generation, Vue.js for interactivity
- **Content Strategy**:
  - VoiceHype feature announcements
  - AI/ML optimization tips
  - Developer productivity articles
  - User success stories
- **Features**:
  - SEO-optimized blog posts
  - Newsletter signup integration
  - Social media sharing
  - RSS feeds
- **Implementation Timeline**: Phase 7 (after core migration)

### 6.7 Model Strategy Clarification

**Claude Sonnet 4 Integration**:
- **Status**: We are NOT ditching Claude - we're using Claude Sonnet 4 as our primary optimization model
- **Rationale**: Claude Sonnet 4 provides excellent code optimization at $3 input/$15 output pricing
- **Implementation**: Update all references to use Claude Sonnet 4 instead of GLM 4.5

**Solutions**:
- Store recordings in Documents folder with unique naming
- Implement file locking mechanism
- Create failed recordings folder with retry options
- Add recording history tracking

### 6.2 Error Handling

**Real-time Server**:
- Add informative error messages
- Specific error for empty audio data
- Guide users to check audio device selection

**Audio Processing**:
- Remove compression feature (keep slicing only)
- Add specific error: "Audio data is empty - check your microphone or audio device selection"


**Real-time button**
- Show a beautiful, prominent button for AssemblyAI's realtime feature
- Remove the language selector

**Slam-1 Model Integration!**
- Integrate the new `slam-1` AssemblyAI model

**Other**
- Show info widgets throughout the webview



---

## Phase 7: Stats & Analytics

### 7.1 Usage Statistics
- **NEW**: Fix last day not showing in VoiceHype website stats
- **NEW**: Add proper date range handling for usage statistics
- **NEW**: Implement real-time usage tracking
- **NEW**: Add usage visualization charts

### 7.2 Analytics Dashboard
- **NEW**: Add daily/weekly/monthly usage breakdown
- **NEW**: Show remaining minutes with visual progress bars
- **NEW**: Add model usage statistics

---

## Phase 8: UI/UX Design System

### 8.1 Unified Design System
- **NEW**: Create reusable pricing component
- **NEW**: Implement consistent color scheme across all platforms
- **NEW**: Add responsive design breakpoints
- **NEW**: Create design tokens for consistent styling

### 8.2 Pricing UI Components
- **NEW**: Design pricing cards with landing page theme
- **NEW**: Add hover effects and animations
- **NEW**: Implement feature comparison table
- **NEW**: Add "Most Popular" badge for Pro plan

### 8.3 Other
- The content should fade in as the user is scrolling down on the landing page. It shouldn't be just static.

---

## Phase 9: Documentation Updates

### 9.1 Extension README
- Update description with new models
- Add migration guide for existing users
- Update pricing information
- Add troubleshooting section

### 9.2 User Communication
- Create migration announcement
- Update FAQ with new pricing

---

## Phase 10: Testing & Deployment

### 10.1 Testing Strategy

**Database Tests**:
- Test new models table creation
- Test subscription plans update
- Test usage stats recording
- Test API key security

**Edge Function Tests**:
- Test optimize function with new models

### Feature Flags
- Implement feature flags for new pricing
- Gradual rollout to user segments
- Quick rollback capability

---

## Success Metrics

### Business Metrics
- Conversion rate improvement with new pricing
- User retention after migration
- Support ticket volume

### Technical Metrics
- API response times with new indexes
- Error rate reduction
- Payment processing success rate

---

## Post-Migration Tasks

### Monitoring
- Monitor payment processing for 48 hours
- Track user feedback on new pricing
- Monitor API performance metrics

### Optimization
- Fine-tune based on user feedback
- Optimize database queries
- Improve error handling based on logs

---

## Notes & Considerations

1. **User Communication**: Send migration announcement 1 week before deployment
2. **Grace Period**: Allow 2-week grace period for users to update extension
3. **Support**: Prepare support team for increased queries during migration
4. **Monitoring**: Set up alerts for payment failures and API errors
5. **Documentation**: Create user-facing migration guide

---

**Document Owner**: Development Team  
**Last Updated**: August 6, 2025  
**Next Review**: August 10, 2025
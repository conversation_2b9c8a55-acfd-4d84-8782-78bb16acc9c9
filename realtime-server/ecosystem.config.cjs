// This file uses CommonJS format for PM2 compatibility
module.exports = {
  apps: [{
    name: 'voicehype-realtime',
    script: 'dist/server.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    env_development: {
      NODE_ENV: 'development',
      PORT: 3001
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    // Restart the app if it crashes 10 times in 1 minute
    min_uptime: '10s',
    max_restarts: 10,
    // Kill timeout
    kill_timeout: 5000,
    // Wait time before restart
    restart_delay: 4000
  }]
};

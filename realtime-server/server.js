#!/usr/bin/env node

/**
 * VoiceHype Real-time Transcription Server
 * Express.js + WebSocket server for AssemblyAI real-time transcription
 */

import express from 'express';
import { WebSocketServer } from 'ws';
import { createServer } from 'http';
import cors from 'cors';
import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';
import fetch from 'node-fetch';

// Load environment variables
dotenv.config();

// Configuration
const PORT = process.env.PORT || 3001;
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;
const ASSEMBLYAI_API_KEY = process.env.ASSEMBLYAI_API_KEY;

// Validate required environment variables
if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY || !ASSEMBLYAI_API_KEY) {
    console.error('❌ Missing required environment variables:');
    console.error('   SUPABASE_URL:', !!SUPABASE_URL);
    console.error('   SUPABASE_SERVICE_ROLE_KEY:', !!SUPABASE_SERVICE_ROLE_KEY);
    console.error('   ASSEMBLYAI_API_KEY:', !!ASSEMBLYAI_API_KEY);
    process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

// Create Express app
const app = express();
const server = createServer(app);

// CORS configuration
const corsOptions = {
    origin: process.env.ALLOWED_ORIGINS ?
        process.env.ALLOWED_ORIGINS.split(',') :
        true, // Allow all origins in development
    credentials: true
};

app.use(cors(corsOptions));
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        service: 'voicehype-realtime-transcription',
        timestamp: new Date().toISOString()
    });
});

// WebSocket server
const wss = new WebSocketServer({
    server,
    path: '/realtime'
});

console.log('🚀 Starting VoiceHype Real-time Transcription Server...');
console.log('📡 Bismillahir rahmanir raheem');

// Constants
const MAX_SESSION_DURATION_MINUTES = 20;
const MAX_RECONNECT_ATTEMPTS = 3;

// Supported services and models
const SUPPORTED_SERVICES = ['assemblyai'];
const SUPPORTED_MODELS = {
    assemblyai: ['best']
};

// Error codes
const ErrorCode = {
    UNAUTHORIZED: 'UNAUTHORIZED',
    INVALID_REQUEST: 'INVALID_REQUEST',
    UNSUPPORTED_MODEL: 'UNSUPPORTED_MODEL',
    SERVICE_ERROR: 'SERVICE_ERROR',
    INSUFFICIENT_CREDITS: 'INSUFFICIENT_CREDITS',
    UNPAID_BALANCE: 'UNPAID_BALANCE'
};

// Helper function to create error response
function createErrorResponse(statusCode, message, errorCode) {
    return {
        error: true,
        statusCode,
        message,
        errorCode,
        timestamp: new Date().toISOString()
    };
}

// Function to generate AssemblyAI temporary token
async function generateAssemblyAITemporaryToken(expiresIn = 1500) {
    console.log('🔑 Generating temporary token for AssemblyAI');

    try {
        const response = await fetch('https://api.assemblyai.com/v2/realtime/token', {
            method: 'POST',
            headers: {
                'Authorization': ASSEMBLYAI_API_KEY,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                expires_in: expiresIn
            })
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('❌ Error generating AssemblyAI token:', response.status, errorText);
            throw new Error(`Failed to generate AssemblyAI token: ${response.status} ${errorText}`);
        }

        const data = await response.json();
        console.log('✅ Successfully generated AssemblyAI temporary token');
        return data.token;
    } catch (error) {
        console.error('❌ Error generating AssemblyAI token:', error);
        throw error;
    }
}

// Function to validate API key
async function validateApiKey(apiKey) {
    try {
        const { data, error } = await supabase
            .rpc('validate_api_key', { p_key: apiKey });

        if (error || !data || data.length === 0) {
            return null;
        }

        return {
            userId: data[0].user_id,
            apiKeyId: data[0].api_key_id
        };
    } catch (error) {
        console.error('❌ Error validating API key:', error);
        return null;
    }
}

// Function to check usage allowance
async function checkUsageAllowance(userId, apiKeyId, model) {
    try {
        const { data, error } = await supabase
            .rpc('check_usage_allowance', {
                p_user_id: userId,
                p_service: 'transcription',
                p_model: model,
                p_amount: 1,
                p_api_key_id: apiKeyId,
                p_is_input_only: false
            });

        if (error) {
            console.error('❌ Error checking usage allowance:', error);
            return null;
        }

        return data && data.length > 0 ? data[0] : null;
    } catch (error) {
        console.error('❌ Error checking usage allowance:', error);
        return null;
    }
}

// Function to record pending usage
async function recordPendingUsage(userId, apiKeyId, model, sessionId, pricingModel) {
    try {
        const { error } = await supabase
            .from('usage_history')
            .insert({
                user_id: userId,
                api_key_id: apiKeyId,
                service: 'transcription',
                model: model,
                amount: 0,
                cost: 0,
                pricing_model: pricingModel,
                status: 'pending',
                metadata: {
                    sessionId,
                    startTime: Date.now(),
                    maxDurationMs: MAX_SESSION_DURATION_MINUTES * 60 * 1000
                }
            });

        if (error) {
            console.error('❌ Error recording pending usage:', error);
            return false;
        }

        console.log(`✅ Recorded pending usage for session ${sessionId}`);
        return true;
    } catch (error) {
        console.error('❌ Error recording pending usage:', error);
        return false;
    }
}

// WebSocket connection handler
wss.on('connection', async (ws, req) => {
    const url = new URL(req.url, `http://${req.headers.host}`);
    const apiKey = url.searchParams.get('apiKey');
    const service = url.searchParams.get('service') || 'assemblyai';
    const model = url.searchParams.get('model') || 'best';
    const language = url.searchParams.get('language') || 'en';

    console.log('🔌 New WebSocket connection:', {
        service,
        model,
        language,
        hasApiKey: !!apiKey,
        origin: req.headers.origin
    });

    // Validate parameters
    if (!apiKey) {
        ws.send(JSON.stringify(createErrorResponse(401, 'Missing API key', ErrorCode.UNAUTHORIZED)));
        ws.close();
        return;
    }

    if (service !== 'assemblyai') {
        ws.send(JSON.stringify(createErrorResponse(400, 'Only AssemblyAI is supported', ErrorCode.INVALID_REQUEST)));
        ws.close();
        return;
    }

    if (model !== 'best') {
        ws.send(JSON.stringify(createErrorResponse(400, 'Only "best" model is supported', ErrorCode.UNSUPPORTED_MODEL)));
        ws.close();
        return;
    }

    // Validate API key
    const validation = await validateApiKey(apiKey);
    if (!validation) {
        ws.send(JSON.stringify(createErrorResponse(401, 'Invalid API key', ErrorCode.UNAUTHORIZED)));
        ws.close();
        return;
    }

    const { userId, apiKeyId } = validation;
    const sessionId = generateSessionId();

    console.log(`✅ API key validated for user ${userId}, starting session ${sessionId}`);

    // Start transcription session
    await handleTranscriptionSession(ws, userId, apiKeyId, model, sessionId);
});

// Generate session ID
function generateSessionId() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

// Main transcription session handler
async function handleTranscriptionSession(ws, userId, apiKeyId, model, sessionId) {
    let assemblyAISocket = null;
    let isConnected = false;
    let sessionStartTime = Date.now();
    let audioBytesSent = 0;
    let transcriptionReceived = false;
    let sessionTimeout = null;
    let reconnectAttempts = 0;
    let audioDurationSeconds = 0; // Store the duration from SessionInformation message
    let cleanupInProgress = false; // Flag to prevent multiple cleanups
    let sessionInformationReceived = false; // Track if we received SessionInformation

    // For tracking transcripts
    let finalTranscript = "";
    let lastPartialTranscript = "";

    // For tracking audio processing
    let successfulAudioChunks = 0;
    let totalAudioSamples = 0;
    const sampleRate = 16000;
    const channels = 1;
    const bitsPerSample = 16;

    // TESTING: Hardcode 20 minutes session duration
    const maxDurationMs = 20 * 60 * 1000; // 20 minutes
    const modelForDb = `assemblyai/${model}-realtime`;

    console.log(`🎬 Starting transcription session ${sessionId} for user ${userId}`);

    try {
        // Check usage allowance
        const usageCheck = await checkUsageAllowance(userId, apiKeyId, modelForDb);
        if (!usageCheck || !usageCheck.can_use) {
            ws.send(JSON.stringify(createErrorResponse(402, 'Insufficient credits', ErrorCode.INSUFFICIENT_CREDITS)));
            ws.close();
            return;
        }

        const pricingModel = usageCheck.pricing_model || 'credits';

        // Record pending usage
        await recordPendingUsage(userId, apiKeyId, modelForDb, sessionId, pricingModel);

        // Set session timeout
        sessionTimeout = setTimeout(() => {
            console.log(`⏰ Session ${sessionId} reached maximum duration`);
            ws.send(JSON.stringify({
                type: 'timeout',
                message: 'Session reached maximum duration'
            }));
            cleanup();
        }, maxDurationMs);

        // Connect to AssemblyAI
        await connectToAssemblyAI();

        // Setup client message handlers
        ws.on('message', handleClientMessage);
        ws.on('close', handleClientClose);
        ws.on('error', handleClientError);

    } catch (error) {
        console.error(`❌ Error setting up session ${sessionId}:`, error);
        ws.send(JSON.stringify(createErrorResponse(500, 'Failed to setup session', ErrorCode.SERVICE_ERROR)));
        ws.close();
    }

    // Connect to AssemblyAI function
    async function connectToAssemblyAI() {
        try {
            // First notify client that we're obtaining a token
            ws.send(JSON.stringify({
                type: 'status',
                status: 'obtaining_token',
                message: 'Obtaining transcription service token...',
                sessionId: sessionId
            }));

            // Generate token (25 minutes to outlast 20-minute session)
            const tokenExpirationSeconds = 1500; // 25 minutes
            const temporaryToken = await generateAssemblyAITemporaryToken(tokenExpirationSeconds);

            // Notify client that token was successfully received
            ws.send(JSON.stringify({
                type: 'transcription_token_ready',
                message: 'Transcription service token received',
                sessionId: sessionId
            }));

            console.log(`🔗 Connecting to AssemblyAI for session ${sessionId}`);

            // Create WebSocket connection to AssemblyAI
            const assemblyAIUrl = `wss://api.assemblyai.com/v2/realtime/ws?sample_rate=16000&token=${temporaryToken}&enable_extra_session_information=true`;

            // Import WebSocket for AssemblyAI connection
            const { default: WebSocket } = await import('ws');
            assemblyAISocket = new WebSocket(assemblyAIUrl);

            assemblyAISocket.on('open', handleAssemblyAIOpen);
            assemblyAISocket.on('message', handleAssemblyAIMessage);
            assemblyAISocket.on('error', handleAssemblyAIError);
            assemblyAISocket.on('close', handleAssemblyAIClose);

        } catch (error) {
            console.error(`❌ Error connecting to AssemblyAI for session ${sessionId}:`, error);
            // Notify client about token failure
            ws.send(JSON.stringify({
                type: 'error',
                message: 'Failed to obtain transcription service token',
                details: error.message,
                sessionId: sessionId
            }));
            throw error;
        }
    }

    // AssemblyAI event handlers
    function handleAssemblyAIOpen() {
        console.log(`✅ Connected to AssemblyAI for session ${sessionId}`);
        isConnected = true;
        reconnectAttempts = 0;

        // Send connection established event
        ws.send(JSON.stringify({
            type: 'connected',
            sessionId: sessionId,
            maxDurationMs: maxDurationMs
        }));
        
        // Send a separate transcription-ready event to indicate service is fully ready to transcribe
        ws.send(JSON.stringify({
            type: 'transcription_ready',
            message: 'Transcription service is ready to process audio',
            sessionId: sessionId,
            timestamp: Date.now()
        }));
        
        console.log(`🎙️ Transcription service is ready for session ${sessionId}`);
    }

    function handleAssemblyAIMessage(data) {
        try {
            const message = JSON.parse(data);
            const messageType = message.message_type || 'unknown';

            console.log(`📨 AssemblyAI message for session ${sessionId}: ${messageType}`);

            if (messageType === 'FinalTranscript') {
                transcriptionReceived = true;
                if (message.text && message.text.trim().length > 0) {
                    finalTranscript += message.text + " ";
                    console.log(`✅ Final transcript: "${message.text}"`);
                }
                // Forward to client
                ws.send(data);
            } else if (messageType === 'PartialTranscript') {
                transcriptionReceived = true;
                if (message.text && message.text.trim().length > 0) {
                    lastPartialTranscript = message.text;
                    console.log(`🔄 Partial transcript: "${message.text}"`);
                }
                // Always forward partial transcripts to client for responsive UI
                ws.send(data);
            } else if (messageType === 'SessionBegins') {
                console.log(`🎬 AssemblyAI session started for ${sessionId}`);
                ws.send(JSON.stringify({
                    type: 'status',
                    message: 'Session started successfully',
                    sessionId: sessionId
                }));
            } else if (messageType === 'SessionInformation') {
                console.log(`ℹ️ [DEBUG] AssemblyAI SessionInformation message received for ${sessionId}`, JSON.stringify(message));
                
                // Extract audio duration from the message
                const messageDuration = message.audio_duration_seconds || 0;
                audioDurationSeconds = messageDuration; // Store the duration for later use in finalizeUsage
                sessionInformationReceived = true; // Mark that we received the SessionInformation message
                console.log(`🔊 Audio duration for session ${sessionId}: ${audioDurationSeconds}s`);
                
                // Clear any pending sessionInfoTimeout if it exists
                if (ws.sessionInfoTimeout) {
                    console.log(`⏲️ Clearing session info timeout for ${sessionId} as message was received`);
                    clearTimeout(ws.sessionInfoTimeout);
                    ws.sessionInfoTimeout = null;
                }
                
                // Forward to client
                ws.send(JSON.stringify({
                    type: 'SessionInformation',
                    audio_duration_seconds: audioDurationSeconds,
                    sessionId: sessionId
                }));

                // Now that we have the SessionInformation, proceed with cleanup
                console.log(`✅ SessionInformation received for ${sessionId}, proceeding with cleanup`);
                initiateCleanup();
            } else if (messageType === 'Error') {
                console.error(`❌ AssemblyAI error for session ${sessionId}:`, message);
                ws.send(JSON.stringify({
                    type: 'error',
                    message: message.text || 'Error from transcription service',
                    details: message
                }));
            }
        } catch (error) {
            console.error(`❌ Error parsing AssemblyAI message for session ${sessionId}:`, error);
        }
    }

    function handleAssemblyAIError(error) {
        console.error(`❌ AssemblyAI WebSocket error for session ${sessionId}:`, error);

        ws.send(JSON.stringify({
            type: 'error',
            message: 'Error in transcription service'
        }));

        // Attempt reconnection
        if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
            reconnectAttempts++;
            console.log(`🔄 Attempting to reconnect (${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS}) for session ${sessionId}`);

            setTimeout(async () => {
                try {
                    await connectToAssemblyAI();
                } catch (reconnectError) {
                    console.error(`❌ Failed to reconnect for session ${sessionId}:`, reconnectError);
                }
            }, 2000);
        }
    }

    function handleAssemblyAIClose(code, reason) {
        console.log(`🔌 AssemblyAI connection closed for session ${sessionId}`, { code, reason: reason.toString() });
        isConnected = false;

        if (!reason.toString().includes('clean')) {
            ws.send(JSON.stringify({
                type: 'service_disconnected',
                code: code,
                reason: reason.toString()
            }));
        }

        // Always wait for SessionInformation message before cleanup
        // AssemblyAI sends this message after connection closes with the final audio duration
        console.log(`⏳ Waiting for SessionInformation message for ${sessionId} before cleanup (max 10 seconds)...`);
        
        // Create a timeout that will run cleanup after 10 seconds if the SessionInformation message hasn't arrived
        const sessionInfoTimeout = setTimeout(() => {
            if (!sessionInformationReceived) {
                console.log(`⚠️ SessionInformation message not received for ${sessionId} after 10 second timeout, proceeding with cleanup`);
                initiateCleanup();
            }
        }, 10000); // Wait up to 10 seconds for SessionInformation
        
        // Store the timeout ID so it can be cleared if SessionInformation arrives before timeout
        ws.sessionInfoTimeout = sessionInfoTimeout;
    }
    
    // Function to initiate cleanup with duplicate prevention
    function initiateCleanup() {
        if (!cleanupInProgress) {
            cleanupInProgress = true;
            cleanup();
        } else {
            console.log(`⚠️ Cleanup already in progress for session ${sessionId}, skipping duplicate call`);
        }
    }

    // Client message handlers
    function handleClientMessage(data) {
        try {
            // Check if it's binary audio data
            if (data instanceof Buffer) {
                if (isConnected && assemblyAISocket && assemblyAISocket.readyState === 1) {
                    audioBytesSent += data.length;

                    // Calculate audio samples
                    const bytesPerSample = (bitsPerSample / 8) * channels;
                    const samplesInChunk = Math.floor(data.length / bytesPerSample);
                    totalAudioSamples += samplesInChunk;
                    successfulAudioChunks++;

                    // Forward to AssemblyAI
                    assemblyAISocket.send(data);

                    // Log progress occasionally
                    if (successfulAudioChunks % 50 === 0) {
                        const duration = (totalAudioSamples / sampleRate).toFixed(2);
                        console.log(`🎵 Audio chunk #${successfulAudioChunks}, duration: ${duration}s`);
                    }
                }
            } else {
                // Handle JSON control messages
                const message = JSON.parse(data.toString());
                console.log(`📨 Client message for session ${sessionId}:`, message.type);

                if (message.type === 'close') {
                    console.log(`👋 Client requested close for session ${sessionId}`);
                    cleanup();
                } else if (message.type === 'ping') {
                    ws.send(JSON.stringify({
                        type: 'pong',
                        timestamp: Date.now()
                    }));
                }
            }
        } catch (error) {
            console.error(`❌ Error handling client message for session ${sessionId}:`, error);
        }
    }

    function handleClientClose() {
        console.log(`👋 Client disconnected for session ${sessionId}`);
        
        // Don't immediately cleanup - wait for SessionInformation message
        // Only close the AssemblyAI connection to stop audio processing
        if (assemblyAISocket && isConnected) {
            console.log(`🔌 Client disconnected, closing AssemblyAI connection for session ${sessionId}`);
            assemblyAISocket.close(); // This will trigger the close handler which waits for SessionInformation
        } else {
            // If no active connection, cleanup immediately
            console.log(`⚠️ No active AssemblyAI connection for session ${sessionId}, cleaning up immediately`);
            cleanup();
        }
    }

    function handleClientError(error) {
        console.error(`❌ Client WebSocket error for session ${sessionId}:`, error);
    }

    // Cleanup function
    function cleanup() {
        console.log(`🧹 Cleaning up session ${sessionId}`);

        if (sessionTimeout) {
            clearTimeout(sessionTimeout);
            sessionTimeout = null;
        }
        
        // Clear the session info timeout if it exists
        if (ws.sessionInfoTimeout) {
            clearTimeout(ws.sessionInfoTimeout);
            ws.sessionInfoTimeout = null;
        }
        
        // Close AssemblyAI connection immediately
        if (assemblyAISocket && assemblyAISocket.readyState === 1) {
            console.log(`🔌 Closing AssemblyAI connection for session ${sessionId}`);
            assemblyAISocket.close();
        }
        
        if (ws.readyState === 1) {
            // Send final transcript if available
            const completeTranscript = finalTranscript.trim() || lastPartialTranscript.trim();
            if (completeTranscript) {
                ws.send(JSON.stringify({
                    message_type: 'CompleteTranscript',
                    text: completeTranscript,
                    sessionId: sessionId
                }));
            }

            ws.send(JSON.stringify({
                type: 'finalized',
                message: 'Session finalized successfully',
                sessionId: sessionId
            }));
        }

        // Finalize usage in database
        finalizeUsage();
    }

    // Finalize usage in database
    async function finalizeUsage() {
        try {
            // Calculate duration in minutes - prioritize duration from AssemblyAI if available
            let durationMinutes;
            let durationSource;
            const localDurationSeconds = (Date.now() - sessionStartTime) / 1000;
            const localDurationMinutes = localDurationSeconds / 60;

            if (audioDurationSeconds > 0) {
                // Use the duration provided by AssemblyAI's SessionInformation message
                durationMinutes = audioDurationSeconds / 60;
                durationSource = 'AssemblyAI SessionInformation';
                
                console.log(`🔍 [DEBUG] Duration comparison for session ${sessionId}:`);
                console.log(`   📊 AssemblyAI duration: ${audioDurationSeconds}s (${durationMinutes} minutes)`);
                console.log(`   📊 Local duration: ${localDurationSeconds}s (${localDurationMinutes} minutes)`);
                console.log(`   📊 Difference: ${Math.abs(audioDurationSeconds - localDurationSeconds)}s`);
            } else {
                // Fallback to calculated duration if AssemblyAI duration is not available
                durationMinutes = localDurationMinutes;
                durationSource = 'Local calculation';
                console.log(`⚠️ Using local calculation: ${localDurationSeconds}s (${durationMinutes} minutes)`);
            }
            
            console.log(`💰 Finalizing usage for session ${sessionId}: ${durationMinutes} minutes (source: ${durationSource})`);

            // Get service pricing
            const { data: servicePricing } = await supabase
                .from('service_pricing')
                .select('cost_per_unit')
                .eq('service', 'transcription')
                .eq('model', modelForDb)
                .eq('is_active', true)
                .single();

            const costPerUnit = servicePricing?.cost_per_unit || 0;
            const totalCost = costPerUnit * durationMinutes;

            // Finalize usage
            await supabase.rpc('finalize_usage', {
                p_user_id: userId,
                p_service: 'transcription',
                p_model: modelForDb,
                p_amount: durationMinutes,
                p_cost: totalCost,
                p_pricing_model: 'credits',
                p_metadata: {
                    sessionId,
                    startTime: sessionStartTime,
                    endTime: Date.now(),
                    audioBytesSent,
                    hasTranscription: transcriptionReceived,
                    audioDurationSeconds,
                    localDurationSeconds: (Date.now() - sessionStartTime) / 1000,
                    durationSource,
                    calculatedDurationMinutes: durationMinutes,
                    rawAssemblyAIDuration: audioDurationSeconds,
                    convertedMinutes: audioDurationSeconds / 60
                }
            });

            console.log(`✅ Successfully finalized session ${sessionId}`);
        } catch (error) {
            console.error(`❌ Error finalizing usage for session ${sessionId}:`, error);
        }
    }
}

// Start the server
server.listen(PORT, () => {
    console.log(`✅ VoiceHype Real-time Transcription Server running on port ${PORT}`);
    console.log(`🌐 WebSocket endpoint: ws://localhost:${PORT}/realtime`);
    console.log(`🏥 Health check: http://localhost:${PORT}/health`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('📴 Received SIGTERM, shutting down gracefully...');
    server.close(() => {
        console.log('👋 Server closed');
        process.exit(0);
    });
});

process.on('SIGINT', () => {
    console.log('📴 Received SIGINT, shutting down gracefully...');
    server.close(() => {
        console.log('👋 Server closed');
        process.exit(0);
    });
});

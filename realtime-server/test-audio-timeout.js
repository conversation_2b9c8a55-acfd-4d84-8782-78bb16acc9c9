#!/usr/bin/env node

/**
 * Test script to verify audio timeout functionality
 * This script connects to the real-time server but doesn't send any audio data
 * to test if the server properly times out after 1 minute of inactivity
 */

const WebSocket = require('ws');

const SERVER_URL = 'ws://localhost:3001/realtime';
const API_KEY = 'test-key'; // Replace with actual API key for testing

console.log('🧪 Starting audio timeout test...');
console.log('📡 This test will connect to the server but send no audio data');
console.log('⏰ The server should timeout after 60 seconds of inactivity');

const url = `${SERVER_URL}?apiKey=${encodeURIComponent(API_KEY)}&service=assemblyai&model=best&language=en`;

console.log(`🔗 Connecting to: ${url.replace(API_KEY, '***HIDDEN***')}`);

const ws = new WebSocket(url);

let connectionTime = Date.now();

ws.on('open', () => {
    console.log('✅ Connected to real-time server');
    console.log('⏳ Waiting for audio timeout (should occur in ~60 seconds)...');
    connectionTime = Date.now();
});

ws.on('message', (data) => {
    try {
        const message = JSON.parse(data.toString());
        const elapsed = Math.round((Date.now() - connectionTime) / 1000);
        
        console.log(`📨 [${elapsed}s] Received message:`, message.type || 'unknown', 
                   message.message ? `- ${message.message}` : '');
        
        if (message.type === 'timeout' && message.reason === 'no_audio_activity') {
            console.log('🎯 SUCCESS: Audio timeout triggered as expected!');
            console.log(`⏱️  Timeout occurred after ${elapsed} seconds`);
            process.exit(0);
        }
    } catch (error) {
        console.log('📨 Raw message:', data.toString());
    }
});

ws.on('close', (code, reason) => {
    const elapsed = Math.round((Date.now() - connectionTime) / 1000);
    console.log(`🔌 Connection closed after ${elapsed}s - Code: ${code}, Reason: ${reason || 'None'}`);
    
    if (elapsed >= 55 && elapsed <= 65) {
        console.log('🎯 SUCCESS: Connection closed within expected timeout window!');
    } else {
        console.log('❌ UNEXPECTED: Connection closed outside expected timeout window');
    }
    
    process.exit(0);
});

ws.on('error', (error) => {
    console.error('❌ WebSocket error:', error.message);
    process.exit(1);
});

// Safety timeout to prevent test from hanging
setTimeout(() => {
    console.log('⏰ Test timeout reached (90 seconds) - terminating');
    ws.close();
    process.exit(1);
}, 90000);

console.log('📝 Note: This test requires the real-time server to be running on localhost:3001');
console.log('📝 Start the server with: cd realtime-server && npm start');

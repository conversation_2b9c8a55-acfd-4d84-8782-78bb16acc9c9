{"name": "voicehype-realtime-server", "version": "1.0.0", "description": "VoiceHype Real-time Transcription Server", "main": "dist/server.js", "scripts": {"build": "npx tsc", "start": "node dist/server.js", "dev": "tsx watch server.ts", "dev:js": "nodemon server.js"}, "dependencies": {"@supabase/supabase-js": "^2.38.0", "assemblyai": "^4.12.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "node-fetch": "^3.3.2", "ws": "^8.14.2"}, "devDependencies": {"@types/cors": "^2.8.18", "@types/express": "^4.17.22", "@types/node": "^20.17.55", "@types/ws": "^8.18.1", "nodemon": "^3.0.1", "tsx": "^4.19.4", "typescript": "^5.8.3"}, "type": "module", "keywords": ["websocket", "transcription", "assemblyai", "voicehype"], "author": "VoiceHype", "license": "ISC"}
# VoiceHype Real-time Transcription Server

**<PERSON><PERSON><PERSON><PERSON> rahmanir raheem**

A standalone Express.js server for VoiceHype's real-time transcription service using AssemblyAI. This server is designed to run on DigitalOcean droplets and provides stable, long-running WebSocket connections for real-time audio transcription.

## Features

- ✅ **Real-time transcription** via AssemblyAI WebSocket API
- ✅ **Stable long-running connections** (no edge function limitations)
- ✅ **Automatic reconnection** with exponential backoff
- ✅ **Credit-based billing** integration with Supabase
- ✅ **Session management** with configurable timeouts
- ✅ **Health monitoring** and graceful shutdown
- ✅ **CORS support** for web applications

## Quick Start

### 1. Installation

```bash
cd realtime-server
npm install
```

### 2. Environment Setup

```bash
cp .env.example .env
# Edit .env with your configuration
```

Required environment variables:
```env
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
ASSEMBLYAI_API_KEY=your-assemblyai-api-key
PORT=3001
```

### 3. Development

```bash
# TypeScript development with hot reload
npm run dev

# Or JavaScript development (legacy)
npm run dev:js
```

### 4. Production

```bash
# Build TypeScript to JavaScript
npm run build

# Start the compiled server
npm start
```

## Deployment on DigitalOcean

### Option 1: Manual Deployment

1. **Upload files to your droplet:**
```bash
scp -r realtime-server/ root@your-droplet-ip:/opt/voicehype/
```

2. **Install dependencies and build:**
```bash
ssh root@your-droplet-ip
cd /opt/voicehype/realtime-server
npm install --production
npm run build
```

3. **Setup environment:**
```bash
cp .env.example .env
nano .env  # Edit with your values
```

4. **Install PM2 for process management:**
```bash
npm install -g pm2
pm2 start dist/server.js --name "voicehype-realtime"
pm2 startup
pm2 save
```

### Option 2: Using the Deploy Script

```bash
# Make the script executable
chmod +x deploy.sh

# Deploy to your droplet
./deploy.sh your-droplet-ip
```

### Option 3: Using PM2 Ecosystem File (Recommended)

Create a `ecosystem.config.cjs` file:
```javascript
module.exports = {
  apps: [{
    name: 'voicehype-realtime',
    script: 'dist/server.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
```

Then deploy with:
```bash
# Build and start with PM2
npm run build
pm2 start ecosystem.config.cjs
pm2 startup
pm2 save
```

## API Usage

### WebSocket Connection

Connect to: `ws://your-server:3001/realtime`

Query parameters:
- `apiKey` (required): VoiceHype API key
- `service` (optional): `assemblyai` (default)
- `model` (optional): `best` (default)
- `language` (optional): `en` (default)

Example:
```javascript
const ws = new WebSocket('ws://your-server:3001/realtime?apiKey=your-api-key&service=assemblyai&model=best');
```

### Message Types

**Client to Server:**
- Binary audio data (PCM16, 16kHz, mono)
- `{"type": "close"}` - Close session
- `{"type": "ping"}` - Ping server

**Server to Client:**
- `{"type": "connected", "sessionId": "...", "maxDurationMs": 1200000}` - Connection established
- `{"type": "status", "message": "..."}` - Status updates
- `{"message_type": "FinalTranscript", "text": "..."}` - Final transcription (confirmed)
- `{"message_type": "PartialTranscript", "text": "..."}` - **Partial transcription (live updates)**
- `{"type": "error", "message": "..."}` - Error messages
- `{"type": "finalized", "message": "..."}` - Session ended

### Real-time Partial Transcripts

The server forwards **all partial transcripts** from AssemblyAI for responsive UI feedback:
- ✅ **Live updates** as the user speaks
- ✅ **No latency concerns** with Express.js
- ✅ **Smooth user experience** in extensions/apps
- ✅ **Overwrite display** for real-time feel

## PM2 Quick Reference

### Basic Commands
```bash
# Build the TypeScript project
npm run build

# Start with PM2 using ecosystem file
pm2 start ecosystem.config.cjs

# Start manually (alternative)
pm2 start dist/server.js --name "voicehype-realtime"

# View status
pm2 status

# View logs
pm2 logs voicehype-realtime

# Monitor in real-time
pm2 monit

# Restart the service
pm2 restart voicehype-realtime

# Stop the service
pm2 stop voicehype-realtime

# Delete the service
pm2 delete voicehype-realtime

# Save current PM2 processes
pm2 save

# Setup PM2 to start on boot
pm2 startup
```

### Deployment Workflow
```bash
# 1. Build the project
npm run build

# 2. Start/restart with PM2
pm2 restart voicehype-realtime || pm2 start ecosystem.config.cjs

# 3. Save the process list
pm2 save
```

## Testing

Update your Python test script to use the new server:

```python
# In test_realtime_transcription.py, change the server URL:
def __init__(self, api_key: str, service: str = 'assemblyai', model: str = 'best',
             language: str = 'en', server_url: str = 'your-droplet-ip:3001'):
```

Then run:
```bash
python tests/test_realtime_transcription.py --api-key YOUR_API_KEY --service assemblyai --model best --server your-droplet-ip:3001
```

## Monitoring

### Health Check
```bash
curl http://your-server:3001/health
```

### PM2 Monitoring
```bash
pm2 status
pm2 logs voicehype-realtime
pm2 monit
```

### Server Logs
The server provides detailed logging with emojis for easy monitoring:
- 🚀 Server startup
- 🔌 New connections
- ✅ Successful operations
- ❌ Errors
- 🎵 Audio processing
- 💰 Billing events

## Configuration

### Session Limits
- **Maximum duration**: 20 minutes (configurable)
- **Reconnection attempts**: 3 (configurable)
- **Token expiration**: 25 minutes (5-minute buffer)

### Audio Requirements
- **Format**: PCM16
- **Sample rate**: 16kHz
- **Channels**: Mono (1 channel)
- **Chunk size**: 100-2000ms recommended

## Troubleshooting

### Common Issues

1. **Connection refused**
   - Check if server is running: `pm2 status`
   - Verify port is open: `netstat -tlnp | grep 3001`

2. **Authentication errors**
   - Verify SUPABASE_SERVICE_ROLE_KEY is correct
   - Check API key validation in Supabase

3. **AssemblyAI errors**
   - Verify ASSEMBLYAI_API_KEY is valid
   - Check token generation logs

### Debug Mode
Set `NODE_ENV=development` for more verbose logging.

## Architecture

```
Client (Browser/App)
    ↓ WebSocket
Express.js Server (DigitalOcean)
    ↓ WebSocket
AssemblyAI Real-time API
    ↓ Database
Supabase (Usage tracking)
```

## License

Proprietary - VoiceHype SaaS Platform

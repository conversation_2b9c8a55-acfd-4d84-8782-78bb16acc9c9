export interface Article {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  coverImageUrl?: string;
  authorId: string;
  status: 'draft' | 'published' | 'archived';
  publishedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  metaTitle?: string;
  metaDescription?: string;
  featured: boolean;
  viewCount: number;
  tags: Tag[];
  categories: Category[];
  author: Author;
  commentCount: number;
  reactionCount: number;
}

export interface CreateArticleInput {
  title: string;
  content: string;
  excerpt: string;
  coverImageUrl?: string;
  tags: string[];
  categories: string[];
  metaTitle?: string;
  metaDescription?: string;
  featured?: boolean;
}

export interface UpdateArticleInput {
  title?: string;
  content?: string;
  excerpt?: string;
  coverImageUrl?: string;
  tags?: string[];
  categories?: string[];
  metaTitle?: string;
  metaDescription?: string;
  featured?: boolean;
  status?: 'draft' | 'published' | 'archived';
}

export interface ArticleFilters {
  status?: 'draft' | 'published' | 'archived';
  tag?: string;
  category?: string;
  authorId?: string;
  featured?: boolean;
  search?: string;
  limit?: number;
  offset?: number;
}
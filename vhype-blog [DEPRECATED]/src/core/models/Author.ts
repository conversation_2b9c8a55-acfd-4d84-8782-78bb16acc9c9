export interface Author {
  id: string;
  username: string;
  fullName: string;
  avatarUrl?: string;
  website?: string;
  role: 'admin' | 'editor' | 'author' | 'reader';
  bio?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateAuthorInput {
  username: string;
  fullName: string;
  avatarUrl?: string;
  website?: string;
  bio?: string;
}

export interface UpdateAuthorInput {
  username?: string;
  fullName?: string;
  avatarUrl?: string;
  website?: string;
  bio?: string;
}

export interface AuthorStats {
  totalArticles: number;
  publishedArticles: number;
  totalViews: number;
  totalComments: number;
}
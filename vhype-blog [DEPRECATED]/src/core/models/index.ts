// Export all models from a single entry point
export * from './Article';
export * from './Author';
export * from './Tag';
export * from './Category';

// Additional shared types
export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface SearchQuery {
  query?: string;
  tags?: string[];
  categories?: string[];
  author?: string;
  dateFrom?: Date;
  dateTo?: Date;
  sortBy?: 'created' | 'published' | 'views' | 'title';
  sortOrder?: 'asc' | 'desc';
}
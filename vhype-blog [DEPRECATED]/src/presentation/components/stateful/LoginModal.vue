<template>
  <div v-if="show" class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
    <div class="w-full max-w-md bg-white rounded-lg shadow-xl">
      <div class="p-6">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-2xl font-bold">{{ isSignUp ? 'Sign Up' : 'Sign In' }}</h2>
          <button @click="close" class="hover:text-gray-700 text-gray-500">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <!-- OAuth Buttons -->
        <div class="mb-6">
          <div class="grid grid-cols-2 gap-3">
            <button
              @click="signInWithGoogle"
              class="hover:bg-gray-50 flex items-center justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm"
              :disabled="loading"
            >
              <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24">
                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              Google
            </button>
            <button
              @click="signInWithGitHub"
              class="hover:bg-gray-50 flex items-center justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm"
              :disabled="loading"
            >
              <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
              </svg>
              GitHub
            </button>
          </div>
          
          <div class="relative my-4">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-gray-300"></div>
            </div>
            <div class="relative flex justify-center text-sm">
              <span class="px-2 text-gray-500 bg-white">Or continue with</span>
            </div>
          </div>
        </div>

        <form @submit.prevent="handleSubmit">
          <div class="mb-4">
            <label for="email" class="block mb-1 text-sm font-medium text-gray-700">Email</label>
            <input
              id="email"
              v-model="email"
              type="email"
              required
              class="focus:outline-none focus:ring-2 focus:ring-blue-500 w-full px-3 py-2 border border-gray-300 rounded-md"
              :disabled="loading"
            />
          </div>

          <div class="mb-4">
            <label for="password" class="block mb-1 text-sm font-medium text-gray-700">Password</label>
            <input
              id="password"
              v-model="password"
              type="password"
              required
              class="focus:outline-none focus:ring-2 focus:ring-blue-500 w-full px-3 py-2 border border-gray-300 rounded-md"
              :disabled="loading"
            />
          </div>

          <div v-if="isSignUp" class="mb-4">
            <label for="fullName" class="block mb-1 text-sm font-medium text-gray-700">Full Name</label>
            <input
              id="fullName"
              v-model="fullName"
              type="text"
              required
              class="focus:outline-none focus:ring-2 focus:ring-blue-500 w-full px-3 py-2 border border-gray-300 rounded-md"
              :disabled="loading"
            />
          </div>

          <div v-if="error" class="mb-4 text-sm text-red-600">
            {{ error }}
          </div>

          <button
            type="submit"
            class="hover:bg-blue-700 disabled:opacity-50 w-full px-4 py-2 text-white transition-colors bg-blue-600 rounded-md"
            :disabled="loading"
          >
            {{ loading ? 'Processing...' : (isSignUp ? 'Sign Up' : 'Sign In') }}
          </button>
        </form>

        <div class="mt-4 text-center">
          <button @click="toggleMode" class="hover:text-blue-800 text-blue-600">
            {{ isSignUp ? 'Already have an account? Sign In' : "Don't have an account? Sign Up" }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useAuth } from './useAuth';
import { supabase } from '../../../infrastructure/supabase/client';

interface Props {
  show: boolean;
}

interface Emits {
  (e: 'close'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { signIn, signUp, loading, error } = useAuth();

const isSignUp = ref(false);
const email = ref('');
const password = ref('');
const fullName = ref('');

const toggleMode = () => {
  isSignUp.value = !isSignUp.value;
  error.value = null;
};

const handleSubmit = async () => {
  if (isSignUp.value) {
    const result = await signUp(email.value, password.value, fullName.value);
    if (result.success) {
      close();
    }
  } else {
    const result = await signIn(email.value, password.value);
    if (result.success) {
      close();
    }
  }
};

const signInWithGoogle = async () => {
  const { error: authError } = await supabase.auth.signInWithOAuth({
    provider: 'google',
    options: {
      redirectTo: `${window.location.origin}/`
    }
  });

  if (authError) {
    error.value = authError.message;
  }
};

const signInWithGitHub = async () => {
  const { error: authError } = await supabase.auth.signInWithOAuth({
    provider: 'github',
    options: {
      redirectTo: `${window.location.origin}/`
    }
  });

  if (authError) {
    error.value = authError.message;
  }
};

const close = () => {
  emit('close');
  // Reset form
  email.value = '';
  password.value = '';
  fullName.value = '';
  error.value = null;
};

// Reset when modal opens
watch(() => props.show, (newVal) => {
  if (newVal) {
    isSignUp.value = false;
    error.value = null;
  }
});
</script>
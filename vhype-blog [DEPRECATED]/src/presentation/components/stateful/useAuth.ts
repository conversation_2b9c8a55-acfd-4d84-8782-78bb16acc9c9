import { ref, computed, onMounted } from 'vue';
import { authService, type AuthUser } from '../../../application/auth/service';

// Reactive state
const user = ref<AuthUser | null>(null);
const loading = ref<boolean>(false);
const error = ref<string | null>(null);

// Computed properties
const isAuthenticated = computed(() => !!user.value);
const userRole = computed(() => user.value?.role || 'reader');

// Methods
const signUp = async (email: string, password: string, fullName: string) => {
  loading.value = true;
  error.value = null;
  
  try {
    const result = await authService.signUp(email, password, fullName);
    if (!result.success) {
      error.value = result.error || 'Sign up failed';
    }
    return result;
  } catch (err) {
    error.value = (err as Error).message;
    return { success: false, error: error.value };
  } finally {
    loading.value = false;
  }
};

const signIn = async (email: string, password: string) => {
  loading.value = true;
  error.value = null;
  
  try {
    const result = await authService.signIn(email, password);
    if (result.success && result.user) {
      user.value = result.user;
    } else {
      error.value = result.error || 'Sign in failed';
    }
    return result;
  } catch (err) {
    error.value = (err as Error).message;
    return { success: false, error: error.value };
  } finally {
    loading.value = false;
  }
};

const signOut = async () => {
  loading.value = true;
  error.value = null;
  
  try {
    const result = await authService.signOut();
    if (result.success) {
      user.value = null;
    } else {
      error.value = result.error || 'Sign out failed';
    }
    return result;
  } catch (err) {
    error.value = (err as Error).message;
    return { success: false, error: error.value };
  } finally {
    loading.value = false;
  }
};

const likeArticle = async (articleId: string) => {
  if (!user.value) {
    error.value = 'You must be logged in to like articles';
    return { success: false, error: error.value };
  }

  loading.value = true;
  error.value = null;
  
  try {
    const result = await authService.likeArticle(articleId, user.value.id);
    if (!result.success) {
      error.value = result.error || 'Failed to like article';
    }
    return result;
  } catch (err) {
    error.value = (err as Error).message;
    return { success: false, error: error.value };
  } finally {
    loading.value = false;
  }
};

const loadUser = async () => {
  loading.value = true;
  
  try {
    const currentUser = await authService.getCurrentUser();
    user.value = currentUser;
  } catch (err) {
    error.value = (err as Error).message;
  } finally {
    loading.value = false;
  }
};

// Initialize on mount
onMounted(() => {
  loadUser();
});

// Export everything
export function useAuth() {
  return {
    // State
    user,
    loading,
    error,
    
    // Computed
    isAuthenticated,
    userRole,
    
    // Methods
    signUp,
    signIn,
    signOut,
    likeArticle,
    loadUser
  };
}
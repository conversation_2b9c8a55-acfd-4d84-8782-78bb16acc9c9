<template>
  <article 
    :class="[
      'bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow',
      { 'md:flex': isFeatured }
    ]"
  >
    <!-- Featured image -->
    <div 
      v-if="article.coverImageUrl || isFeatured" 
      :class="[
        'bg-gray-200',
        { 
          'md:w-1/2 h-48 md:h-auto': isFeatured,
          'h-48': !isFeatured
        }
      ]"
    >
      <!-- TODO: Add actual image component -->
      <div class="flex items-center justify-center w-full h-full text-gray-500">
        {{ isFeatured ? 'Featured Image' : 'Article Image' }}
      </div>
    </div>

    <!-- Content -->
    <div :class="[{ 'md:w-1/2 p-6': isFeatured, 'p-6': !isFeatured }]">
      <!-- Meta info -->
      <div class="flex items-center mb-2 text-sm text-gray-500">
        <span>{{ formatDate(article.publishedAt || article.createdAt) }}</span>
        <span class="mx-2">•</span>
        <span>{{ article.author.fullName }}</span>
      </div>

      <!-- Title -->
      <h3 
        :class="[
          'font-bold text-gray-900 mb-2 hover:text-blue-600 transition-colors',
          { 'text-2xl': isFeatured, 'text-xl': !isFeatured }
        ]"
      >
        <a :href="`/articles/${article.slug}`">
          {{ article.title }}
        </a>
      </h3>

      <!-- Excerpt -->
      <p class="mb-4 text-gray-600">
        {{ article.excerpt }}
      </p>

      <!-- Tags -->
      <div v-if="article.tags && article.tags.length" class="flex flex-wrap gap-2 mb-4">
        <span 
          v-for="tag in article.tags" 
          :key="tag.id"
          class="px-2 py-1 text-xs text-blue-800 bg-blue-100 rounded-full"
        >
          {{ tag.name }}
        </span>
      </div>

      <!-- Stats -->
      <div class="flex items-center text-sm text-gray-500">
        <span class="flex items-center mr-4">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
          </svg>
          {{ article.viewCount }}
        </span>
        <span class="flex items-center mr-4">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
          </svg>
          {{ article.commentCount }}
        </span>
        <span class="flex items-center">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"></path>
          </svg>
          {{ article.reactionCount }}
        </span>
      </div>
    </div>
  </article>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { Article } from '../../../core/models';

interface Props {
  article: Article;
  isFeatured?: boolean;
}

const props = defineProps<Props>();

const formatDate = (date: Date) => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};
</script>
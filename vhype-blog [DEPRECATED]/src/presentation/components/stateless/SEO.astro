---
export interface Props {
  title: string;
  description: string;
  image?: string;
  article?: boolean;
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  tags?: string[];
}

const { title, description, image, article, publishedTime, modifiedTime, author, tags } = Astro.props;
const siteTitle = 'Voice Hype Blog';
const fullTitle = title ? `${title} | ${siteTitle}` : siteTitle;
const siteUrl = 'https://blog.voicehype.ai';
const canonicalURL = new URL(Astro.url.pathname, siteUrl);
---

<title>{fullTitle}</title>
<meta name="description" content={description} />
<meta name="robots" content="index, follow" />
<link rel="canonical" href={canonicalURL.href} />

<!-- Open Graph -->
<meta property="og:type" content={article ? "article" : "website"} />
<meta property="og:title" content={fullTitle} />
<meta property="og:description" content={description} />
<meta property="og:url" content={canonicalURL.href} />
<meta property="og:site_name" content={siteTitle} />
{image && <meta property="og:image" content={image} />}

<!-- Article-specific OG tags -->
{article && publishedTime && <meta property="article:published_time" content={publishedTime} />}
{article && modifiedTime && <meta property="article:modified_time" content={modifiedTime} />}
{article && author && <meta property="article:author" content={author} />}
{article && tags && tags.map((tag) => <meta property="article:tag" content={tag} />)}

<!-- Twitter -->
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content={fullTitle} />
<meta name="twitter:description" content={description} />
{image && <meta name="twitter:image" content={image} />}

<!-- Additional SEO meta tags -->
<meta name="author" content={author || siteTitle} />
{tags && <meta name="keywords" content={tags.join(', ')} />}

<!-- Structured Data (JSON-LD) -->
{article && (
  <script type="application/ld+json">
    {JSON.stringify({
      "@context": "https://schema.org",
      "@type": "Article",
      "headline": title,
      "description": description,
      "author": {
        "@type": "Person",
        "name": author || siteTitle
      },
      "publisher": {
        "@type": "Organization",
        "name": siteTitle,
        "logo": {
          "@type": "ImageObject",
          "url": `${siteUrl}/logo.png`
        }
      },
      "datePublished": publishedTime,
      "dateModified": modifiedTime,
      "image": image,
      "keywords": tags
    })}
  </script>
)}
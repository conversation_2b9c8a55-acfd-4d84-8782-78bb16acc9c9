<template>
  <div class="p-6 bg-white rounded-lg shadow-md">
    <div class="flex items-center">
      <!-- Author avatar -->
      <div class="flex items-center justify-center w-16 h-16 mr-4 bg-gray-300 rounded-full">
        <span class="text-2xl font-bold text-gray-600">
          {{ author.fullName.charAt(0) }}
        </span>
      </div>
      
      <!-- Author info -->
      <div>
        <h3 class="text-xl font-bold">{{ author.fullName }}</h3>
        <p class="mb-2 text-gray-600">{{ author.role }}</p>
        <p v-if="author.bio" class="text-gray-700">{{ author.bio }}</p>
      </div>
    </div>
    
    <!-- Author stats -->
    <div class="flex justify-between pt-6 mt-6 border-t border-gray-200">
      <div class="text-center">
        <div class="text-2xl font-bold">24</div>
        <div class="text-sm text-gray-600">Articles</div>
      </div>
      <div class="text-center">
        <div class="text-2xl font-bold">1.2K</div>
        <div class="text-sm text-gray-600">Views</div>
      </div>
      <div class="text-center">
        <div class="text-2xl font-bold">89</div>
        <div class="text-sm text-gray-600">Comments</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Author } from '../../../core/models';

interface Props {
  author: Author;
}

defineProps<Props>();
</script>
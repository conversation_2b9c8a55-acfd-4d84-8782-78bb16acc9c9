<template>
  <div class="article-content" v-html="renderedContent"></div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { markdownProcessor } from '../../../core/markdown';

interface Props {
  content: string;
}

const props = defineProps<Props>();

const renderedContent = computed(() => {
  return markdownProcessor.process(props.content);
});
</script>

<style scoped>
.article-content {
  @apply prose prose-lg max-w-none;
}

.article-content :deep(h1) {
  @apply text-3xl font-bold mb-6 mt-8;
}

.article-content :deep(h2) {
  @apply text-2xl font-bold mb-5 mt-7;
}

.article-content :deep(h3) {
  @apply text-xl font-bold mb-4 mt-6;
}

.article-content :deep(p) {
  @apply mb-4 text-gray-700 leading-relaxed;
}

.article-content :deep(strong) {
  @apply font-bold;
}

.article-content :deep(em) {
  @apply italic;
}

.article-content :deep(code) {
  @apply px-1.5 py-0.5 bg-gray-100 rounded text-sm font-mono;
}

.article-content :deep(pre) {
  @apply p-4 bg-gray-800 text-white rounded-lg overflow-x-auto mb-6;
}

.article-content :deep(pre code) {
  @apply bg-transparent p-0;
}

.article-content :deep(a) {
  @apply text-blue-600 hover:text-blue-800 underline;
}

.article-content :deep(ul) {
  @apply list-disc list-inside mb-4 space-y-2;
}

.article-content :deep(ol) {
  @apply list-decimal list-inside mb-4 space-y-2;
}

.article-content :deep(li) {
  @apply text-gray-700;
}

.article-content :deep(blockquote) {
  @apply border-l-4 border-gray-300 pl-4 italic text-gray-600 my-6;
}

.article-content :deep(hr) {
  @apply my-8 border-gray-200;
}

.article-content :deep(img) {
  @apply max-w-full h-auto rounded-lg my-6;
}
</style>
<template>
  <header class="sticky top-0 z-50 bg-white shadow-sm">
    <div class="container px-4 mx-auto">
      <div class="flex items-center justify-between h-16">
        <!-- Logo -->
        <div class="flex items-center">
          <a href="/" class="text-2xl font-bold text-gray-900">
            My Blog
          </a>
        </div>

        <!-- Navigation -->
        <nav class="md:flex hidden space-x-8">
          <a
            href="/"
            class="hover:text-blue-600 text-gray-700 transition-colors"
            :class="{ 'text-blue-600 font-medium': currentPage === 'home' }"
          >
            Home
          </a>
          <a
            href="/about"
            class="hover:text-blue-600 text-gray-700 transition-colors"
            :class="{ 'text-blue-600 font-medium': currentPage === 'about' }"
          >
            About
          </a>
          <a
            href="/categories"
            class="hover:text-blue-600 text-gray-700 transition-colors"
          >
            Categories
          </a>
          <a
            href="/tags"
            class="hover:text-blue-600 text-gray-700 transition-colors"
          >
            Tags
          </a>
        </nav>

        <!-- Search and Auth -->
        <div class="flex items-center space-x-4">
          <!-- Search -->
          <div class="sm:block relative hidden">
            <input
              type="text"
              placeholder="Search articles..."
              class="focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent py-2 pl-10 pr-4 border border-gray-300 rounded-lg"
            />
            <svg class="w-5 h-5 text-gray-400 absolute left-3 top-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>

          <!-- Auth buttons -->
          <div class="flex items-center space-x-2">
            <template v-if="!isAuthenticated">
              <button
                class="hover:text-blue-600 px-4 py-2 text-gray-700 transition-colors"
                @click="showLoginModal = true"
              >
                Login
              </button>
              <button
                class="hover:bg-blue-700 px-4 py-2 text-white transition-colors bg-blue-600 rounded-lg"
                @click="showLoginModal = true"
              >
                Sign Up
              </button>
            </template>
            <template v-else>
              <div class="flex items-center space-x-3">
                <span class="text-gray-700">{{ user?.fullName }}</span>
                <button
                  class="hover:bg-gray-200 px-4 py-2 text-gray-700 transition-colors bg-gray-100 rounded-lg"
                  @click="handleSignOut"
                >
                  Sign Out
                </button>
              </div>
            </template>
          </div>

          <!-- Mobile menu button -->
          <button
            class="md:hidden text-gray-700"
            @click="toggleMobileMenu"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- Mobile menu -->
      <div v-if="mobileMenuOpen" class="md:hidden py-4 border-t border-gray-200">
        <div class="flex flex-col space-y-4">
          <a href="/" class="hover:text-blue-600 text-gray-700 transition-colors">Home</a>
          <a href="/about" class="hover:text-blue-600 text-gray-700 transition-colors">About</a>
          <a href="/categories" class="hover:text-blue-600 text-gray-700 transition-colors">Categories</a>
          <a href="/tags" class="hover:text-blue-600 text-gray-700 transition-colors">Tags</a>
          
          <template v-if="!isAuthenticated">
            <button
              class="hover:text-blue-600 px-4 py-2 text-left text-gray-700 transition-colors"
              @click="showLoginModal = true"
            >
              Login / Sign Up
            </button>
          </template>
          <template v-else>
            <div class="px-4 py-2">
              <p class="text-gray-700">Welcome, {{ user?.fullName }}!</p>
              <button
                class="hover:text-blue-600 mt-2 text-left text-gray-700 transition-colors"
                @click="handleSignOut"
              >
                Sign Out
              </button>
            </div>
          </template>
          
          <div class="pt-4">
            <input
              type="text"
              placeholder="Search articles..."
              class="focus:outline-none focus:ring-2 focus:ring-blue-500 w-full px-4 py-2 border border-gray-300 rounded-lg"
            />
          </div>
        </div>
      </div>
    </div>
    
    <!-- Login Modal -->
    <LoginModal
      :show="showLoginModal"
      @close="showLoginModal = false"
    />
  </header>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useAuth } from '../stateful/useAuth';
import LoginModal from '../stateful/LoginModal.vue';

const mobileMenuOpen = ref(false);
const showLoginModal = ref(false);

const { user, isAuthenticated, signOut } = useAuth();

// Simple current page detection for highlighting active nav items
const currentPage = ref('home');
if (typeof window !== 'undefined') {
  currentPage.value = window.location.pathname === '/' ? 'home' : window.location.pathname.substring(1);
}

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value;
};

const handleSignOut = async () => {
  await signOut();
};
</script>
---
import Header from '../components/stateless/Header.vue';
import Footer from '../components/stateless/Footer.vue';
import SEO from '../components/stateless/SEO.astro';

interface Props {
  title?: string;
  description?: string;
  image?: string;
  article?: boolean;
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  tags?: string[];
}

const {
  title = 'Voice Hype Blog',
  description = 'A beautiful blog built with Astro and Vue',
  image,
  article,
  publishedTime,
  modifiedTime,
  author,
  tags
} = Astro.props;
---

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    
    <SEO
      {title}
      {description}
      {image}
      {article}
      {publishedTime}
      {modifiedTime}
      {author}
      {tags}
    />
  </head>
  <body class="min-h-screen bg-white">
    <Header />
    
    <main>
      <slot />
    </main>
    
    <Footer />
  </body>
</html>
---
import Layout from '../../layouts/MainLayout.astro';
import ArticleCard from '../../components/stateless/ArticleCard.vue';
import { Tag, Article } from '../../../core/models';

// TODO: Fetch tag and articles from service
const tag: Tag = {
  id: '1',
  name: 'Vue',
  slug: 'vue',
  description: 'Articles about Vue.js framework',
  createdAt: new Date(),
  updatedAt: new Date()
};

const articles: Article[] = [
  {
    id: '1',
    title: 'Getting Started with Astro + Vue',
    slug: 'getting-started-astro-vue',
    excerpt: 'Learn how to build modern blogs with Astro and Vue.js',
    coverImageUrl: '',
    authorId: '1',
    status: 'published',
    publishedAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
    featured: true,
    viewCount: 125,
    tags: [],
    categories: [],
    author: {
      id: '1',
      username: 'author1',
      fullName: '<PERSON>',
      role: 'author',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    commentCount: 5,
    reactionCount: 23
  }
];
---

<Layout :title="`Articles tagged with ${tag.name}`" :description="tag.description">
  <div class="container px-4 py-8 mx-auto">
    <div class="mb-8 text-center">
      <h1 class="mb-2 text-3xl font-bold">#{tag.name}</h1>
      {tag.description && (
        <p class="max-w-2xl mx-auto text-gray-600">{tag.description}</p>
      )}
      <p class="mt-2 text-gray-500">{articles.length} article{articles.length !== 1 ? 's' : ''}</p>
    </div>
    
    <div class="md:grid-cols-2 lg:grid-cols-3 grid grid-cols-1 gap-6">
      {articles.map((article) => (
        <ArticleCard :article="article" />
      ))}
    </div>
  </div>
</Layout>
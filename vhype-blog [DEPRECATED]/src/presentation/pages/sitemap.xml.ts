import type { APIRoute } from 'astro';

const siteUrl = 'https://blog.voicehype.ai';

// TODO: Fetch actual articles, tags, and categories from database
const articles = [
  { slug: 'getting-started-astro-vue', updatedAt: new Date().toISOString() }
];

const tags = [
  { slug: 'astro' },
  { slug: 'vue' }
];

const categories = [
  { slug: 'web-development' }
];

export const GET: APIRoute = async () => {
  const staticPages = [
    '',
    '/about',
    '/newsletter'
  ];

  const articlePages = articles.map(article => `/articles/${article.slug}`);
  const tagPages = tags.map(tag => `/tags/${tag.slug}`);
  const categoryPages = categories.map(category => `/categories/${category.slug}`);

  const allPages = [...staticPages, ...articlePages, ...tagPages, ...categoryPages];

  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${allPages.map(page => `  <url>
    <loc>${siteUrl}${page}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>${page === '' ? '1.0' : '0.8'}</priority>
  </url>`).join('\n')}
</urlset>`;

  return new Response(sitemap, {
    headers: {
      'Content-Type': 'application/xml'
    }
  });
};
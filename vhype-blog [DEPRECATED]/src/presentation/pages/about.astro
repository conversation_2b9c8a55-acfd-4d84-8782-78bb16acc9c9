---
import Layout from '../layouts/MainLayout.astro';

const author = {
  name: '<PERSON>',
  bio: 'I\'m a passionate web developer and tech enthusiast with over 5 years of experience building modern web applications. I love sharing knowledge and helping others learn new technologies.',
  avatar: '',
  socialLinks: [
    { name: 'Twitter', url: '#' },
    { name: 'GitH<PERSON>', url: '#' },
    { name: 'LinkedIn', url: '#' }
  ]
};
---

<Layout title="About" description="Learn more about the author and this blog">
  <div class="container px-4 py-8 mx-auto">
    <div class="max-w-4xl mx-auto">
      <h1 class="mb-8 text-4xl font-bold text-center">About This Blog</h1>
      
      <div class="p-8 mb-8 bg-white rounded-lg shadow-md">
        <div class="md:flex-row flex flex-col items-center mb-8">
          <div class="md:mb-0 md:mr-8 flex items-center justify-center w-32 h-32 mb-6 bg-gray-300 rounded-full">
            <span class="text-4xl font-bold text-gray-600">
              {author.name.charAt(0)}
            </span>
          </div>
          <div class="md:text-left text-center">
            <h2 class="mb-2 text-2xl font-bold">{author.name}</h2>
            <p class="text-gray-600">{author.bio}</p>
          </div>
        </div>
        
        <div class="flex justify-center space-x-4">
          {author.socialLinks.map((link) => (
            <a 
              href={link.url} 
              class="hover:bg-gray-200 px-4 py-2 transition-colors bg-gray-100 rounded-lg"
            >
              {link.name}
            </a>
          ))}
        </div>
      </div>
      
      <div class="max-w-none prose prose-lg">
        <h2 class="mb-4 text-2xl font-bold">The Story</h2>
        <p class="mb-6">
          This blog was created to share knowledge about web development, technology, and best practices. 
          As a developer who has learned so much from the community, I wanted to give back by creating 
          content that helps others on their journey.
        </p>
        
        <h2 class="mb-4 text-2xl font-bold">What You'll Find Here</h2>
        <ul class="mb-6">
          <li>Tutorials and guides on modern web technologies</li>
          <li>Best practices for software development</li>
          <li>Reviews of tools and frameworks</li>
          <li>Personal experiences and insights</li>
        </ul>
        
        <h2 class="mb-4 text-2xl font-bold">Technologies Used</h2>
        <p class="mb-6">
          This blog is built with modern technologies including Astro for static site generation, 
          Vue.js for interactive components, and Tailwind CSS for styling. The backend uses Supabase 
          for database and authentication.
        </p>
        
        <h2 class="mb-4 text-2xl font-bold">Get In Touch</h2>
        <p>
          If you have any questions, suggestions, or just want to say hello, feel free to reach out 
          through the contact form or social media links above. I'd love to hear from you!
        </p>
      </div>
    </div>
  </div>
</Layout>
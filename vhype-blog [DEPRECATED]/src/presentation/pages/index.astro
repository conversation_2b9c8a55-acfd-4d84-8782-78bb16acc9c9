---
import Layout from '../layouts/MainLayout.astro';
import ArticleCard from '../components/stateless/ArticleCard.vue';
import type { Article } from '../../core/models';

// TODO: Fetch articles from service
const articles: Article[] = [
  // Sample data for now
  {
    id: '1',
    title: 'Getting Started with Astro + Vue',
    slug: 'getting-started-astro-vue',
    content: '# Getting Started with Astro + Vue\n\nThis is the full content of the article...',
    excerpt: 'Learn how to build modern blogs with Astro and Vue.js',
    coverImageUrl: '',
    authorId: '1',
    status: 'published',
    publishedAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
    featured: true,
    viewCount: 125,
    tags: [],
    categories: [],
    author: {
      id: '1',
      username: 'author1',
      fullName: '<PERSON>',
      role: 'author',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    commentCount: 5,
    reactionCount: 23
  }
];

const featuredArticle = articles.find(article => article.featured);
const recentArticles = articles.filter(article => !article.featured);
---

<Layout
  title="Voice Hype Blog"
  description="A beautiful blog built with Astro and Vue. Sharing knowledge about web development, technology, and more."
>
  <div class="container px-4 py-8 mx-auto">
    <!-- Hero Section -->
    <section class="mb-12 text-center">
      <h1 class="md:text-6xl mb-4 text-4xl font-bold">Welcome to Voice Hype Blog</h1>
      <p class="mb-8 text-xl text-gray-600">Sharing knowledge about web development, technology, and more</p>
    </section>

    <!-- Featured Article -->
    {featuredArticle && (
      <section class="mb-12">
        <h2 class="mb-6 text-2xl font-bold">Featured Article</h2>
        <ArticleCard
          article={featuredArticle}
          isFeatured={true}
        />
      </section>
    )}

    <!-- Recent Articles -->
    <section>
      <h2 class="mb-6 text-2xl font-bold">Recent Articles</h2>
      <div class="md:grid-cols-2 lg:grid-cols-3 grid grid-cols-1 gap-6">
        {recentArticles.map((article) => (
          <ArticleCard article={article} />
        ))}
      </div>
    </section>

    <!-- Newsletter Signup -->
    <section class="bg-gray-50 p-8 mt-16 text-center rounded-lg">
      <h2 class="mb-4 text-2xl font-bold">Stay Updated</h2>
      <p class="mb-6 text-gray-600">Subscribe to get the latest articles delivered to your inbox</p>
      <form class="flex max-w-md mx-auto">
        <input
          type="email"
          placeholder="Your email address"
          class="focus:outline-none focus:ring-2 focus:ring-blue-500 flex-1 px-4 py-2 border border-gray-300 rounded-l-lg"
        />
        <button
          type="submit"
          class="hover:bg-blue-700 px-6 py-2 text-white transition-colors bg-blue-600 rounded-r-lg"
        >
          Subscribe
        </button>
      </form>
    </section>
  </div>
</Layout>
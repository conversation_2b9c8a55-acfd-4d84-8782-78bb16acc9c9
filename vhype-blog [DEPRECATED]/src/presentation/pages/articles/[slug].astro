---
import Layout from '../../layouts/MainLayout.astro';
import ArticleContent from '../../components/stateless/ArticleContent.vue';
import AuthorCard from '../../components/stateless/AuthorCard.vue';
import { Article } from '../../../core/models';

// TODO: Fetch article by slug from service
const article: Article = {
  id: '1',
  title: 'Getting Started with Astro + Vue',
  slug: 'getting-started-astro-vue',
  content: '# Getting Started with Astro + Vue\n\nThis is the full content of the article...',
  excerpt: 'Learn how to build modern blogs with Astro and Vue.js',
  coverImageUrl: '',
  authorId: '1',
  status: 'published',
  publishedAt: new Date(),
  createdAt: new Date(),
  updatedAt: new Date(),
  featured: true,
  viewCount: 125,
  tags: [
    { id: '1', name: 'Astro', slug: 'astro', createdAt: new Date(), updatedAt: new Date() },
    { id: '2', name: '<PERSON><PERSON>', slug: 'vue', createdAt: new Date(), updatedAt: new Date() }
  ],
  categories: [
    { id: '1', name: 'Web Development', slug: 'web-development', createdAt: new Date(), updatedAt: new Date() }
  ],
  author: {
    id: '1',
    username: 'author1',
    fullName: 'John Doe',
    role: 'author',
    bio: 'Web developer and tech enthusiast',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  commentCount: 5,
  reactionCount: 23
};
---

<Layout :title="article.title" :description="article.excerpt">
  <article class="container px-4 py-8 mx-auto">
    <!-- Article header -->
    <header class="mb-8 text-center">
      <div class="flex items-center justify-center mb-4 space-x-2 text-gray-500">
        <span>{new Date(article.publishedAt || article.createdAt).toLocaleDateString()}</span>
        <span>•</span>
        <span>{article.author.fullName}</span>
        <span>•</span>
        <span>{article.viewCount} views</span>
      </div>
      
      <h1 class="md:text-5xl mb-4 text-4xl font-bold">{article.title}</h1>
      
      {article.excerpt && (
        <p class="max-w-3xl mx-auto text-xl text-gray-600">{article.excerpt}</p>
      )}
      
      <!-- Tags -->
      <div class="flex flex-wrap justify-center gap-2 mt-6">
        {article.tags.map((tag) => (
          <a 
            href={`/tags/${tag.slug}`} 
            class="hover:bg-blue-200 px-3 py-1 text-sm text-blue-800 transition-colors bg-blue-100 rounded-full"
          >
            #{tag.name}
          </a>
        ))}
      </div>
    </header>

    <!-- Article content -->
    <div class="max-w-4xl mx-auto">
      <ArticleContent :content="article.content" />
    </div>

    <!-- Author card -->
    <div class="max-w-2xl mx-auto mt-12">
      <AuthorCard :author="article.author" />
    </div>

    <!-- Reactions and comments section -->
    <div class="max-w-2xl mx-auto mt-12">
      <!-- Reactions -->
      <div class="flex items-center justify-center mb-8 space-x-6">
        <button class="hover:bg-gray-200 flex items-center px-4 py-2 space-x-2 transition-colors bg-gray-100 rounded-full">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"></path>
          </svg>
          <span>{article.reactionCount} Likes</span>
        </button>
      </div>

      <!-- Comments section -->
      <div class="bg-gray-50 p-6 rounded-lg">
        <h3 class="mb-4 text-xl font-bold">Comments ({article.commentCount})</h3>
        
        <!-- Comment form -->
        <form class="mb-6">
          <textarea 
            placeholder="Share your thoughts..." 
            class="focus:outline-none focus:ring-2 focus:ring-blue-500 w-full px-4 py-3 border border-gray-300 rounded-lg"
            rows="4"
          ></textarea>
          <div class="flex justify-end mt-2">
            <button 
              type="submit"
              class="hover:bg-blue-700 px-6 py-2 text-white transition-colors bg-blue-600 rounded-lg"
            >
              Post Comment
            </button>
          </div>
        </form>

        <!-- Comments list -->
        <div class="space-y-6">
          <!-- Sample comment -->
          <div class="p-4 bg-white rounded-lg">
            <div class="flex items-center mb-2">
              <div class="w-8 h-8 mr-3 bg-gray-300 rounded-full"></div>
              <div>
                <h4 class="font-medium">Jane Smith</h4>
                <p class="text-sm text-gray-500">2 days ago</p>
              </div>
            </div>
            <p class="text-gray-700">Great article! This really helped me understand the concepts better.</p>
          </div>
        </div>
      </div>
    </div>
  </article>
</Layout>
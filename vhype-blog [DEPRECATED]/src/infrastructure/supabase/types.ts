export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> }
  | Json[];

export interface Database {
  blog: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          updated_at: string | null;
          username: string | null;
          full_name: string | null;
          avatar_url: string | null;
          website: string | null;
          role: string | null;
          bio: string | null;
          created_at: string | null;
        };
        Insert: {
          id: string;
          updated_at?: string | null;
          username?: string | null;
          full_name?: string | null;
          avatar_url?: string | null;
          website?: string | null;
          role?: string | null;
          bio?: string | null;
          created_at?: string | null;
        };
        Update: {
          id?: string;
          updated_at?: string | null;
          username?: string | null;
          full_name?: string | null;
          avatar_url?: string | null;
          website?: string | null;
          role?: string | null;
          bio?: string | null;
          created_at?: string | null;
        };
      };
      categories: {
        Row: {
          id: string;
          name: string;
          slug: string;
          description: string | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: string;
          name: string;
          slug: string;
          description?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: string;
          name?: string;
          slug?: string;
          description?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
      };
      tags: {
        Row: {
          id: string;
          name: string;
          slug: string;
          description: string | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: string;
          name: string;
          slug: string;
          description?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: string;
          name?: string;
          slug?: string;
          description?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
      };
      articles: {
        Row: {
          id: string;
          title: string;
          slug: string;
          content: string | null;
          excerpt: string | null;
          cover_image_url: string | null;
          author_id: string;
          status: string | null;
          published_at: string | null;
          created_at: string | null;
          updated_at: string | null;
          meta_title: string | null;
          meta_description: string | null;
          featured: boolean | null;
          view_count: number | null;
        };
        Insert: {
          id?: string;
          title: string;
          slug: string;
          content?: string | null;
          excerpt?: string | null;
          cover_image_url?: string | null;
          author_id: string;
          status?: string | null;
          published_at?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
          meta_title?: string | null;
          meta_description?: string | null;
          featured?: boolean | null;
          view_count?: number | null;
        };
        Update: {
          id?: string;
          title?: string;
          slug?: string;
          content?: string | null;
          excerpt?: string | null;
          cover_image_url?: string | null;
          author_id?: string;
          status?: string | null;
          published_at?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
          meta_title?: string | null;
          meta_description?: string | null;
          featured?: boolean | null;
          view_count?: number | null;
        };
      };
      article_tags: {
        Row: {
          article_id: string;
          tag_id: string;
        };
        Insert: {
          article_id: string;
          tag_id: string;
        };
        Update: {
          article_id?: string;
          tag_id?: string;
        };
      };
      article_categories: {
        Row: {
          article_id: string;
          category_id: string;
        };
        Insert: {
          article_id: string;
          category_id: string;
        };
        Update: {
          article_id?: string;
          category_id?: string;
        };
      };
      comments: {
        Row: {
          id: string;
          article_id: string;
          author_id: string | null;
          author_name: string | null;
          author_email: string | null;
          content: string;
          status: string | null;
          parent_id: string | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: string;
          article_id: string;
          author_id?: string | null;
          author_name?: string | null;
          author_email?: string | null;
          content: string;
          status?: string | null;
          parent_id?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: string;
          article_id?: string;
          author_id?: string | null;
          author_name?: string | null;
          author_email?: string | null;
          content?: string;
          status?: string | null;
          parent_id?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
      };
      article_reactions: {
        Row: {
          id: string;
          article_id: string;
          user_id: string | null;
          reaction_type: string;
          created_at: string | null;
        };
        Insert: {
          id?: string;
          article_id: string;
          user_id?: string | null;
          reaction_type: string;
          created_at?: string | null;
        };
        Update: {
          id?: string;
          article_id?: string;
          user_id?: string | null;
          reaction_type?: string;
          created_at?: string | null;
        };
      };
      newsletter_subscribers: {
        Row: {
          id: string;
          email: string;
          name: string | null;
          subscribed: boolean | null;
          created_at: string | null;
          unsubscribed_at: string | null;
        };
        Insert: {
          id?: string;
          email: string;
          name?: string | null;
          subscribed?: boolean | null;
          created_at?: string | null;
          unsubscribed_at?: string | null;
        };
        Update: {
          id?: string;
          email?: string;
          name?: string | null;
          subscribed?: boolean | null;
          created_at?: string | null;
          unsubscribed_at?: string | null;
        };
      };
      article_views: {
        Row: {
          id: string;
          article_id: string;
          user_id: string | null;
          ip_address: string | null;
          user_agent: string | null;
          viewed_at: string | null;
        };
        Insert: {
          id?: string;
          article_id: string;
          user_id?: string | null;
          ip_address?: string | null;
          user_agent?: string | null;
          viewed_at?: string | null;
        };
        Update: {
          id?: string;
          article_id?: string;
          user_id?: string | null;
          ip_address?: string | null;
          user_agent?: string | null;
          viewed_at?: string | null;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
  };
}

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (Database["blog"]["Tables"] & Database["blog"]["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (Database["blog"]["Tables"] &
      Database["blog"]["Views"])
  ? (Database["blog"]["Tables"] &
      Database["blog"]["Views"])[PublicTableNameOrOptions] extends {
      Row: infer R;
    }
    ? R
    : never
  : never;

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof Database["blog"]["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof Database["blog"]["Tables"]
  ? Database["blog"]["Tables"][PublicTableNameOrOptions] extends {
      Insert: infer I;
    }
    ? I
    : never
  : never;

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof Database["blog"]["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof Database["blog"]["Tables"]
  ? Database["blog"]["Tables"][PublicTableNameOrOptions] extends {
      Update: infer U;
    }
    ? U
    : never
  : never;
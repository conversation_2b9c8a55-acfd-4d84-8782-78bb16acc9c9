import { supabase } from '../../infrastructure/supabase/client';
import type { Author } from '../../core/models';

export interface AuthUser {
  id: string;
  email: string;
  username: string;
  fullName: string;
  avatarUrl?: string;
  role: 'admin' | 'editor' | 'author' | 'reader';
}

export class AuthService {
  private currentUser: AuthUser | null = null;

  async signUp(email: string, password: string, fullName: string): Promise<{ success: boolean; error?: string }> {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName
          }
        }
      });

      if (error) {
        return { success: false, error: error.message };
      }

      // Create profile record
      if (data.user) {
        const { error: profileError } = await supabase
          .from('blog.profiles')
          .insert({
            id: data.user.id,
            full_name: fullName,
            username: email.split('@')[0],
            role: 'reader'
          });

        if (profileError) {
          console.error('Error creating profile:', profileError);
        }
      }

      return { success: true };
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  }

  async signIn(email: string, password: string): Promise<{ success: boolean; user?: AuthUser; error?: string }> {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) {
        return { success: false, error: error.message };
      }

      if (data.user) {
        const user = await this.mapSupabaseUserToAuthUser(data.user);
        this.currentUser = user;
        return { success: true, user };
      }

      return { success: false, error: 'No user data returned' };
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  }

  async signOut(): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        return { success: false, error: error.message };
      }

      this.currentUser = null;
      return { success: true };
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  }

  async getCurrentUser(): Promise<AuthUser | null> {
    if (this.currentUser) {
      return this.currentUser;
    }

    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (user) {
        const authUser = await this.mapSupabaseUserToAuthUser(user);
        this.currentUser = authUser;
        return authUser;
      }

      return null;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  }

  async getUserProfile(userId: string): Promise<Author | null> {
    try {
      const { data, error } = await supabase
        .from('blog.profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error fetching user profile:', error);
        return null;
      }

      if (data) {
        return {
          id: data.id,
          username: data.username || '',
          fullName: data.full_name || '',
          avatarUrl: data.avatar_url || undefined,
          website: data.website || undefined,
          role: (data.role as 'admin' | 'editor' | 'author' | 'reader') || 'reader',
          bio: data.bio || undefined,
          createdAt: new Date(data.created_at || Date.now()),
          updatedAt: new Date(data.updated_at || Date.now())
        };
      }

      return null;
    } catch (error) {
      console.error('Error getting user profile:', error);
      return null;
    }
  }

  async updateUserProfile(userId: string, updates: Partial<Author>): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase
        .from('blog.profiles')
        .update({
          username: updates.username,
          full_name: updates.fullName,
          avatar_url: updates.avatarUrl,
          website: updates.website,
          bio: updates.bio,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) {
        return { success: false, error: error.message };
      }

      // Update current user if it's the same user
      if (this.currentUser && this.currentUser.id === userId) {
        this.currentUser = {
          ...this.currentUser,
          ...updates
        };
      }

      return { success: true };
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  }

  async likeArticle(articleId: string, userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase
        .from('blog.article_reactions')
        .insert({
          article_id: articleId,
          user_id: userId,
          reaction_type: 'like'
        });

      if (error) {
        // Check if it's a duplicate (user already liked)
        if (error.code === '23505') {
          // Remove the like (unlike)
          const { error: deleteError } = await supabase
            .from('blog.article_reactions')
            .delete()
            .match({
              article_id: articleId,
              user_id: userId,
              reaction_type: 'like'
            });

          if (deleteError) {
            return { success: false, error: deleteError.message };
          }

          return { success: true };
        }
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  }

  private async mapSupabaseUserToAuthUser(supabaseUser: any): Promise<AuthUser> {
    // Get profile data
    const profile = await this.getUserProfile(supabaseUser.id);

    return {
      id: supabaseUser.id,
      email: supabaseUser.email,
      username: profile?.username || supabaseUser.email.split('@')[0],
      fullName: profile?.fullName || supabaseUser.user_metadata?.full_name || '',
      avatarUrl: profile?.avatarUrl,
      role: profile?.role || 'reader'
    };
  }
}

// Export singleton instance
export const authService = new AuthService();
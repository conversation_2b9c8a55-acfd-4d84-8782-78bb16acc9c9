-- Blog Schema for vhype-blog
-- Schema: blog

-- Enable pgcrypto extension for gen_random_uuid()
create extension if not exists pgcrypto;

-- Create blog schema
create schema if not exists blog;

-- Set search path
set search_path to blog, public;

-- Table: profiles (extends auth.users)
create table if not exists blog.profiles (
  id uuid references auth.users on delete cascade not null primary key,
  updated_at timestamp with time zone default now(),
  username text unique,
  full_name text,
  avatar_url text,
  website text,
  role text check (role in ('admin', 'editor', 'author', 'reader')) default 'reader',
  bio text,
  created_at timestamp with time zone default now(),
  constraint username_length check (char_length(username) >= 3)
);

-- Table: categories
create table if not exists blog.categories (
  id uuid default gen_random_uuid() primary key,
  name text not null unique,
  slug text not null unique,
  description text,
  created_at timestamp with time zone default now(),
  updated_at timestamp with time zone default now()
);

-- Table: tags
create table if not exists blog.tags (
  id uuid default gen_random_uuid() primary key,
  name text not null unique,
  slug text not null unique,
  description text,
  created_at timestamp with time zone default now(),
  updated_at timestamp with time zone default now()
);

-- Table: articles
create table if not exists blog.articles (
  id uuid default gen_random_uuid() primary key,
  title text not null,
  slug text not null unique,
  content text,
  excerpt text,
  cover_image_url text,
  author_id uuid references blog.profiles(id) on delete cascade not null,
  status text check (status in ('draft', 'published', 'archived')) default 'draft',
  published_at timestamp with time zone,
  created_at timestamp with time zone default now(),
  updated_at timestamp with time zone default now(),
  meta_title text,
  meta_description text,
  featured boolean default false,
  view_count integer default 0
);

-- Junction table: article_tags
create table if not exists blog.article_tags (
  article_id uuid references blog.articles(id) on delete cascade,
  tag_id uuid references blog.tags(id) on delete cascade,
  primary key (article_id, tag_id)
);

-- Table: article_categories
create table if not exists blog.article_categories (
  article_id uuid references blog.articles(id) on delete cascade,
  category_id uuid references blog.categories(id) on delete cascade,
  primary key (article_id, category_id)
);

-- Table: comments
create table if not exists blog.comments (
  id uuid default gen_random_uuid() primary key,
  article_id uuid references blog.articles(id) on delete cascade not null,
  author_id uuid references blog.profiles(id) on delete cascade,
  author_name text, -- for anonymous comments
  author_email text, -- for anonymous comments
  content text not null,
  status text check (status in ('pending', 'approved', 'rejected', 'spam')) default 'pending',
  parent_id uuid references blog.comments(id) on delete cascade,
  created_at timestamp with time zone default now(),
  updated_at timestamp with time zone default now()
);

-- Table: article_reactions
create table if not exists blog.article_reactions (
  id uuid default gen_random_uuid() primary key,
  article_id uuid references blog.articles(id) on delete cascade not null,
  user_id uuid references blog.profiles(id) on delete cascade,
  reaction_type text not null, -- e.g., 'like', 'love', 'clap', etc.
  created_at timestamp with time zone default now(),
  unique(article_id, user_id, reaction_type)
);

-- Table: newsletter_subscribers
create table if not exists blog.newsletter_subscribers (
  id uuid default gen_random_uuid() primary key,
  email text not null unique,
  name text,
  subscribed boolean default true,
  created_at timestamp with time zone default now(),
  unsubscribed_at timestamp with time zone
);

-- Table: article_views
create table if not exists blog.article_views (
  id uuid default gen_random_uuid() primary key,
  article_id uuid references blog.articles(id) on delete cascade not null,
  user_id uuid references blog.profiles(id) on delete cascade,
  ip_address inet,
  user_agent text,
  viewed_at timestamp with time zone default now()
);

-- Create indexes for better performance
create index if not exists idx_articles_slug on blog.articles(slug);
create index if not exists idx_articles_status on blog.articles(status);
create index if not exists idx_articles_published_at on blog.articles(published_at);
create index if not exists idx_articles_author_id on blog.articles(author_id);
create index if not exists idx_articles_featured on blog.articles(featured);
create index if not exists idx_article_tags_article_id on blog.article_tags(article_id);
create index if not exists idx_article_tags_tag_id on blog.article_tags(tag_id);
create index if not exists idx_article_categories_article_id on blog.article_categories(article_id);
create index if not exists idx_article_categories_category_id on blog.article_categories(category_id);
create index if not exists idx_comments_article_id on blog.comments(article_id);
create index if not exists idx_comments_status on blog.comments(status);
create index if not exists idx_comments_parent_id on blog.comments(parent_id);
create index if not exists idx_article_reactions_article_id on blog.article_reactions(article_id);
create index if not exists idx_article_reactions_user_id on blog.article_reactions(user_id);
create index if not exists idx_article_views_article_id on blog.article_views(article_id);
create index if not exists idx_article_views_user_id on blog.article_views(user_id);
create index if not exists idx_article_views_viewed_at on blog.article_views(viewed_at);

-- Enable RLS (Row Level Security)
alter table blog.profiles enable row level security;
alter table blog.categories enable row level security;
alter table blog.tags enable row level security;
alter table blog.articles enable row level security;
alter table blog.article_tags enable row level security;
alter table blog.article_categories enable row level security;
alter table blog.comments enable row level security;
alter table blog.article_reactions enable row level security;
alter table blog.newsletter_subscribers enable row level security;
alter table blog.article_views enable row level security;

-- RLS Policies

-- profiles policies
create policy "Profiles are viewable by everyone" on blog.profiles
  for select using (true);

create policy "Users can insert their own profile" on blog.profiles
  for insert with check (auth.uid() = id);

create policy "Users can update their own profile" on blog.profiles
  for update using (auth.uid() = id);

-- categories policies
create policy "Categories are viewable by everyone" on blog.categories
  for select using (true);

create policy "Admins and editors can manage categories" on blog.categories
  for all using (
    exists (
      select 1 from blog.profiles
      where id = auth.uid()
      and role in ('admin', 'editor')
    )
  );

-- tags policies
create policy "Tags are viewable by everyone" on blog.tags
  for select using (true);

create policy "Admins and editors can manage tags" on blog.tags
  for all using (
    exists (
      select 1 from blog.profiles
      where id = auth.uid()
      and role in ('admin', 'editor')
    )
  );

-- articles policies
create policy "Published articles are viewable by everyone" on blog.articles
  for select using (status = 'published');

create policy "Users can view their own articles" on blog.articles
  for select using (auth.uid() = author_id);

create policy "Authors can create articles" on blog.articles
  for insert with check (
    exists (
      select 1 from blog.profiles
      where id = auth.uid()
      and role in ('admin', 'editor', 'author')
    )
  );

create policy "Authors can update their own articles" on blog.articles
  for update using (auth.uid() = author_id);

create policy "Authors can delete their own articles" on blog.articles
  for delete using (auth.uid() = author_id);

-- article_tags policies
create policy "Article tags are viewable by everyone" on blog.article_tags
  for select using (
    exists (
      select 1 from blog.articles
      where id = article_tags.article_id
      and status = 'published'
    )
  );

create policy "Authors can manage tags for their articles" on blog.article_tags
  for all using (
    exists (
      select 1 from blog.articles
      where id = article_tags.article_id
      and author_id = auth.uid()
    )
  );

-- article_categories policies
create policy "Article categories are viewable by everyone" on blog.article_categories
  for select using (
    exists (
      select 1 from blog.articles
      where id = article_categories.article_id
      and status = 'published'
    )
  );

create policy "Authors can manage categories for their articles" on blog.article_categories
  for all using (
    exists (
      select 1 from blog.articles
      where id = article_categories.article_id
      and author_id = auth.uid()
    )
  );

-- comments policies
create policy "Comments are viewable by everyone" on blog.comments
  for select using (
    exists (
      select 1 from blog.articles
      where id = comments.article_id
      and status = 'published'
    ) and status = 'approved'
  );

create policy "Users can create comments" on blog.comments
  for insert with check (true);

create policy "Users can update their own comments" on blog.comments
  for update using (
    auth.uid() = author_id or
    exists (
      select 1 from blog.profiles
      where id = auth.uid()
      and role in ('admin', 'editor')
    )
  );

create policy "Users can delete their own comments" on blog.comments
  for delete using (
    auth.uid() = author_id or
    exists (
      select 1 from blog.profiles
      where id = auth.uid()
      and role in ('admin', 'editor')
    )
  );

-- article_reactions policies
create policy "Reactions are viewable by everyone" on blog.article_reactions
  for select using (
    exists (
      select 1 from blog.articles
      where id = article_reactions.article_id
      and status = 'published'
    )
  );

create policy "Users can manage their own reactions" on blog.article_reactions
  for all using (auth.uid() = user_id);

-- newsletter_subscribers policies
create policy "Newsletter subscribers are viewable by admins" on blog.newsletter_subscribers
  for select using (
    exists (
      select 1 from blog.profiles
      where id = auth.uid()
      and role = 'admin'
    )
  );

create policy "Users can subscribe to newsletter" on blog.newsletter_subscribers
  for insert with check (true);

create policy "Users can update their subscription" on blog.newsletter_subscribers
  for update using (true);

-- article_views policies
create policy "View counts are viewable by everyone" on blog.article_views
  for select using (
    exists (
      select 1 from blog.articles
      where id = article_views.article_id
      and status = 'published'
    )
  );

create policy "Users can record their views" on blog.article_views
  for insert with check (true);

-- Grant necessary permissions
grant usage on schema blog to anon, authenticated;
grant all on all tables in schema blog to authenticated;
grant all on all sequences in schema blog to authenticated;
grant all on all functions in schema blog to authenticated;

-- Grant select permissions for public tables
grant select on blog.categories to anon;
grant select on blog.tags to anon;
grant select on blog.articles to anon;
grant select on blog.article_tags to anon;
grant select on blog.article_categories to anon;
grant select on blog.comments to anon;
grant select on blog.article_reactions to anon;
grant select on blog.article_views to anon;

-- Create functions for common operations

-- Function to get published articles with author info
create or replace function blog.get_published_articles()
returns setof record as $$
  select 
    a.id,
    a.title,
    a.slug,
    a.excerpt,
    a.cover_image_url,
    a.published_at,
    a.created_at,
    a.updated_at,
    a.featured,
    a.view_count,
    p.username as author_username,
    p.full_name as author_name,
    p.avatar_url as author_avatar
  from blog.articles a
  join blog.profiles p on a.author_id = p.id
  where a.status = 'published'
  order by a.published_at desc;
$$ language sql stable;

-- Function to get article by slug with full details
create or replace function blog.get_article_by_slug(slug_param text)
returns setof record as $$
  select 
    a.*,
    p.username as author_username,
    p.full_name as author_name,
    p.avatar_url as author_avatar,
    p.bio as author_bio
  from blog.articles a
  join blog.profiles p on a.author_id = p.id
  where a.slug = slug_param and a.status = 'published';
$$ language sql stable;

-- Function to increment view count
create or replace function blog.increment_view_count(article_id uuid)
returns void as $$
begin
  update blog.articles 
  set view_count = view_count + 1 
  where id = article_id;
end;
$$ language plpgsql volatile;

-- Insert sample data (optional)
-- insert into blog.categories (name, slug, description) values 
--   ('Technology', 'technology', 'Tech related articles'),
--   ('Programming', 'programming', 'Programming tutorials and guides'),
--   ('Lifestyle', 'lifestyle', 'Lifestyle and personal development');

-- insert into blog.tags (name, slug, description) values 
--   ('JavaScript', 'javascript', 'JavaScript related content'),
--   ('Vue', 'vue', 'Vue.js framework'),
--   ('Astro', 'astro', 'Astro framework');

-- Update todo list to mark this task as completed